{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\auth\\\\UnauthorizedAccess.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport './UnauthorizedAccess.css';\n\n/**\n * UnauthorizedAccess Component\n * Displays when user doesn't have permission to access a route\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UnauthorizedAccess = ({\n  userRole,\n  requiredRoles = [],\n  isAdminArea = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    logout\n  } = useAuth();\n  const handleGoHome = () => {\n    navigate('/');\n  };\n  const handleGoBack = () => {\n    navigate(-1);\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const getRoleDisplayName = role => {\n    switch (role) {\n      case 'Admin':\n        return 'Administrator';\n      case 'Employee':\n        return 'Employee';\n      case 'Customer':\n        return 'Customer';\n      default:\n        return role;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"unauthorized-access\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"unauthorized-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"unauthorized-icon\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"80\",\n          height: \"80\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\",\n            fill: \"#EF4444\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M15 9H9v6h6V9z\",\n            fill: \"white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"unauthorized-title\",\n        children: \"Access Denied\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"unauthorized-message\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"You don't have permission to access this \", isAdminArea ? 'admin' : '', \" area.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"role-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Your Role:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 28\n            }, this), \" \", getRoleDisplayName(userRole)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Required Role(s):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 28\n            }, this), \" \", requiredRoles.map(getRoleDisplayName).join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"unauthorized-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleGoHome,\n          children: \"Go to Homepage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: handleGoBack,\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-outline\",\n          onClick: handleLogout,\n          children: \"Switch Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this), isAdminArea && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-contact\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Need admin access? Contact your system administrator.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 9\n  }, this);\n};\n_s(UnauthorizedAccess, \"/54Abd91nPctMpu6a3zTzvOoP4s=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = UnauthorizedAccess;\nexport default UnauthorizedAccess;\nvar _c;\n$RefreshReg$(_c, \"UnauthorizedAccess\");", "map": {"version": 3, "names": ["React", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "UnauthorizedAccess", "userRole", "requiredRoles", "isAdminArea", "_s", "navigate", "logout", "handleGoHome", "handleGoBack", "handleLogout", "getRoleDisplayName", "role", "className", "children", "width", "height", "viewBox", "fill", "xmlns", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "join", "onClick", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/auth/UnauthorizedAccess.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport './UnauthorizedAccess.css';\n\n/**\n * UnauthorizedAccess Component\n * Displays when user doesn't have permission to access a route\n */\nconst UnauthorizedAccess = ({ \n    userRole, \n    requiredRoles = [], \n    isAdminArea = false \n}) => {\n    const navigate = useNavigate();\n    const { logout } = useAuth();\n\n    const handleGoHome = () => {\n        navigate('/');\n    };\n\n    const handleGoBack = () => {\n        navigate(-1);\n    };\n\n    const handleLogout = () => {\n        logout();\n        navigate('/login');\n    };\n\n    const getRoleDisplayName = (role) => {\n        switch (role) {\n            case 'Admin': return 'Administrator';\n            case 'Employee': return 'Employee';\n            case 'Customer': return 'Customer';\n            default: return role;\n        }\n    };\n\n    return (\n        <div className=\"unauthorized-access\">\n            <div className=\"unauthorized-container\">\n                <div className=\"unauthorized-icon\">\n                    <svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path \n                            d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\" \n                            fill=\"#EF4444\"\n                        />\n                        <path \n                            d=\"M15 9H9v6h6V9z\" \n                            fill=\"white\"\n                        />\n                    </svg>\n                </div>\n                \n                <h1 className=\"unauthorized-title\">Access Denied</h1>\n                \n                <div className=\"unauthorized-message\">\n                    <p>\n                        You don't have permission to access this {isAdminArea ? 'admin' : ''} area.\n                    </p>\n                    <div className=\"role-info\">\n                        <p><strong>Your Role:</strong> {getRoleDisplayName(userRole)}</p>\n                        <p><strong>Required Role(s):</strong> {requiredRoles.map(getRoleDisplayName).join(', ')}</p>\n                    </div>\n                </div>\n\n                <div className=\"unauthorized-actions\">\n                    <button \n                        className=\"btn btn-primary\" \n                        onClick={handleGoHome}\n                    >\n                        Go to Homepage\n                    </button>\n                    <button \n                        className=\"btn btn-secondary\" \n                        onClick={handleGoBack}\n                    >\n                        Go Back\n                    </button>\n                    <button \n                        className=\"btn btn-outline\" \n                        onClick={handleLogout}\n                    >\n                        Switch Account\n                    </button>\n                </div>\n\n                {isAdminArea && (\n                    <div className=\"admin-contact\">\n                        <p>\n                            Need admin access? Contact your system administrator.\n                        </p>\n                    </div>\n                )}\n            </div>\n        </div>\n    );\n};\n\nexport default UnauthorizedAccess;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAO,0BAA0B;;AAEjC;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,kBAAkB,GAAGA,CAAC;EACxBC,QAAQ;EACRC,aAAa,GAAG,EAAE;EAClBC,WAAW,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACF,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAE5B,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACvBF,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACvBH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACvBH,MAAM,CAAC,CAAC;IACRD,QAAQ,CAAC,QAAQ,CAAC;EACtB,CAAC;EAED,MAAMK,kBAAkB,GAAIC,IAAI,IAAK;IACjC,QAAQA,IAAI;MACR,KAAK,OAAO;QAAE,OAAO,eAAe;MACpC,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC;QAAS,OAAOA,IAAI;IACxB;EACJ,CAAC;EAED,oBACIZ,OAAA;IAAKa,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAChCd,OAAA;MAAKa,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnCd,OAAA;QAAKa,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAC9Bd,OAAA;UAAKe,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAAAL,QAAA,gBAC1Fd,OAAA;YACIoB,CAAC,EAAC,uHAAuH;YACzHF,IAAI,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFxB,OAAA;YACIoB,CAAC,EAAC,gBAAgB;YAClBF,IAAI,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENxB,OAAA;QAAIa,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAa;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAErDxB,OAAA;QAAKa,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCd,OAAA;UAAAc,QAAA,GAAG,2CAC0C,EAACV,WAAW,GAAG,OAAO,GAAG,EAAE,EAAC,QACzE;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxB,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBd,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAU;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACb,kBAAkB,CAACT,QAAQ,CAAC;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjExB,OAAA;YAAAc,QAAA,gBAAGd,OAAA;cAAAc,QAAA,EAAQ;YAAiB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrB,aAAa,CAACsB,GAAG,CAACd,kBAAkB,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENxB,OAAA;QAAKa,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjCd,OAAA;UACIa,SAAS,EAAC,iBAAiB;UAC3Bc,OAAO,EAAEnB,YAAa;UAAAM,QAAA,EACzB;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA;UACIa,SAAS,EAAC,mBAAmB;UAC7Bc,OAAO,EAAElB,YAAa;UAAAK,QAAA,EACzB;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA;UACIa,SAAS,EAAC,iBAAiB;UAC3Bc,OAAO,EAAEjB,YAAa;UAAAI,QAAA,EACzB;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAELpB,WAAW,iBACRJ,OAAA;QAAKa,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1Bd,OAAA;UAAAc,QAAA,EAAG;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnB,EAAA,CAzFIJ,kBAAkB;EAAA,QAKHJ,WAAW,EACTC,OAAO;AAAA;AAAA8B,EAAA,GANxB3B,kBAAkB;AA2FxB,eAAeA,kBAAkB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}