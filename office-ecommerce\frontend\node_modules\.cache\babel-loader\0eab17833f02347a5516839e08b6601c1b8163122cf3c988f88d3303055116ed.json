{"ast": null, "code": "import { Vector3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Loader, LoaderUtils, Matrix4, Bone, BufferGeometry, Float32BufferAttribute, Uint16BufferAttribute, MeshPhongMaterial, FrontSide, Vector2, SkinnedMesh, Mesh, Skeleton, AnimationClip, AnimationMixer, Loader, TextureLoader } from \"three\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nvar XLoader = /* @__PURE__ */function () {\n  var classCallCheck = function (instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  };\n  var createClass = function () {\n    function defineProperties(target, props) {\n      for (let i2 = 0; i2 < props.length; i2++) {\n        var descriptor = props[i2];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n      }\n    }\n    return function (Constructor, protoProps, staticProps) {\n      if (protoProps) defineProperties(Constructor.prototype, protoProps);\n      if (staticProps) defineProperties(Constructor, staticProps);\n      return Constructor;\n    };\n  }();\n  var XboneInf = function XboneInf2() {\n    classCallCheck(this, XboneInf2);\n    this.boneName = \"\";\n    this.BoneIndex = 0;\n    this.Indeces = [];\n    this.Weights = [];\n    this.initMatrix = null;\n    this.OffsetMatrix = null;\n  };\n  var XAnimationInfo = function XAnimationInfo2() {\n    classCallCheck(this, XAnimationInfo2);\n    this.animeName = \"\";\n    this.boneName = \"\";\n    this.targetBone = null;\n    this.keyType = 4;\n    this.frameStartLv = 0;\n    this.keyFrames = [];\n    this.InverseMx = null;\n  };\n  var XAnimationObj = function () {\n    function XAnimationObj2(_flags) {\n      classCallCheck(this, XAnimationObj2);\n      this.fps = 30;\n      this.name = \"xanimation\";\n      this.length = 0;\n      this.hierarchy = [];\n      this.putFlags = _flags;\n      if (this.putFlags.putPos === void 0) {\n        this.putFlags.putPos = true;\n      }\n      if (this.putFlags.putRot === void 0) {\n        this.putFlags.putRot = true;\n      }\n      if (this.putFlags.putScl === void 0) {\n        this.putFlags.putScl = true;\n      }\n    }\n    createClass(XAnimationObj2, [{\n      key: \"make\",\n      value: function make(XAnimationInfoArray) {\n        for (let i2 = 0; i2 < XAnimationInfoArray.length; i2++) {\n          this.hierarchy.push(this.makeBonekeys(XAnimationInfoArray[i2]));\n        }\n        this.length = this.hierarchy[0].keys[this.hierarchy[0].keys.length - 1].time;\n      }\n    }, {\n      key: \"clone\",\n      value: function clone() {\n        return Object.assign({}, this);\n      }\n    }, {\n      key: \"makeBonekeys\",\n      value: function makeBonekeys(XAnimationInfo2) {\n        var refObj = {};\n        refObj.name = XAnimationInfo2.boneName;\n        refObj.parent = \"\";\n        refObj.keys = this.keyFrameRefactor(XAnimationInfo2);\n        refObj.copy = function () {\n          return Object.assign({}, this);\n        };\n        return refObj;\n      }\n    }, {\n      key: \"keyFrameRefactor\",\n      value: function keyFrameRefactor(XAnimationInfo2) {\n        var keys = [];\n        for (let i2 = 0; i2 < XAnimationInfo2.keyFrames.length; i2++) {\n          var keyframe = {};\n          keyframe.time = XAnimationInfo2.keyFrames[i2].time * this.fps;\n          if (XAnimationInfo2.keyFrames[i2].pos && this.putFlags.putPos) {\n            keyframe.pos = XAnimationInfo2.keyFrames[i2].pos;\n          }\n          if (XAnimationInfo2.keyFrames[i2].rot && this.putFlags.putRot) {\n            keyframe.rot = XAnimationInfo2.keyFrames[i2].rot;\n          }\n          if (XAnimationInfo2.keyFrames[i2].scl && this.putFlags.putScl) {\n            keyframe.scl = XAnimationInfo2.keyFrames[i2].scl;\n          }\n          if (XAnimationInfo2.keyFrames[i2].matrix) {\n            keyframe.matrix = XAnimationInfo2.keyFrames[i2].matrix;\n            if (this.putFlags.putPos) {\n              keyframe.pos = new Vector3().setFromMatrixPosition(keyframe.matrix);\n            }\n            if (this.putFlags.putRot) {\n              keyframe.rot = new Quaternion().setFromRotationMatrix(keyframe.matrix);\n            }\n            if (this.putFlags.putScl) {\n              keyframe.scl = new Vector3().setFromMatrixScale(keyframe.matrix);\n            }\n          }\n          keys.push(keyframe);\n        }\n        return keys;\n      }\n    }]);\n    return XAnimationObj2;\n  }();\n  var XKeyFrameInfo = function XKeyFrameInfo2() {\n    classCallCheck(this, XKeyFrameInfo2);\n    this.index = 0;\n    this.Frame = 0;\n    this.time = 0;\n    this.matrix = null;\n  };\n  var XLoader2 = function () {\n    function XLoader3(manager) {\n      Loader.call(this, manager);\n      classCallCheck(this, XLoader3);\n      this.debug = false;\n      this.texloader = new TextureLoader(this.manager);\n      this.url = \"\";\n      this._putMatLength = 0;\n      this._nowMat = null;\n      this._nowFrameName = \"\";\n      this.frameHierarchie = [];\n      this.Hierarchies = {};\n      this.HieStack = [];\n      this._currentObject = {};\n      this._currentFrame = {};\n      this._data = null;\n      this.onLoad = null;\n      this.IsUvYReverse = true;\n      this.Meshes = [];\n      this.animations = [];\n      this.animTicksPerSecond = 30;\n      this._currentGeo = null;\n      this._currentAnime = null;\n      this._currentAnimeFrames = null;\n    }\n    createClass(XLoader3, [{\n      key: \"_setArgOption\",\n      value: function _setArgOption(_arg) {\n        var _start = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (!_arg) {\n          return;\n        }\n        for (let i2 = _start; i2 < _arg.length; i2++) {\n          switch (i2) {\n            case 0:\n              this.url = _arg[i2];\n              break;\n            case 1:\n              this.options = _arg[i2];\n              break;\n          }\n        }\n        if (this.options === void 0) {\n          this.options = {};\n        }\n      }\n    }, {\n      key: \"load\",\n      value: function load(_arg, onLoad, onProgress, onError) {\n        var _this = this;\n        this._setArgOption(_arg);\n        var loader = new FileLoader(this.manager);\n        loader.setPath(this.path);\n        loader.setResponseType(\"arraybuffer\");\n        loader.setRequestHeader(this.requestHeader);\n        loader.setWithCredentials(this.withCredentials);\n        loader.load(this.url, function (response) {\n          try {\n            _this.parse(response, onLoad);\n          } catch (e) {\n            if (onError) {\n              onError(e);\n            } else {\n              console.error(e);\n            }\n            _this.manager.itemError(_this.url);\n          }\n        }, onProgress, onError);\n      }\n    }, {\n      key: \"_readLine\",\n      value: function _readLine(line) {\n        var readed = 0;\n        while (true) {\n          var find = -1;\n          find = line.indexOf(\"//\", readed);\n          if (find === -1) {\n            find = line.indexOf(\"#\", readed);\n          }\n          if (find > -1 && find < 2) {\n            var foundNewLine = -1;\n            foundNewLine = line.indexOf(\"\\r\\n\", readed);\n            if (foundNewLine > 0) {\n              readed = foundNewLine + 2;\n            } else {\n              foundNewLine = line.indexOf(\"\\r\", readed);\n              if (foundNewLine > 0) {\n                readed = foundNewLine + 1;\n              } else {\n                readed = line.indexOf(\"\\n\", readed) + 1;\n              }\n            }\n          } else {\n            break;\n          }\n        }\n        return line.substr(readed);\n      }\n    }, {\n      key: \"_readLine\",\n      value: function _readLine(line) {\n        var readed = 0;\n        while (true) {\n          var find = -1;\n          find = line.indexOf(\"//\", readed);\n          if (find === -1) {\n            find = line.indexOf(\"#\", readed);\n          }\n          if (find > -1 && find < 2) {\n            var foundNewLine = -1;\n            foundNewLine = line.indexOf(\"\\r\\n\", readed);\n            if (foundNewLine > 0) {\n              readed = foundNewLine + 2;\n            } else {\n              foundNewLine = line.indexOf(\"\\r\", readed);\n              if (foundNewLine > 0) {\n                readed = foundNewLine + 1;\n              } else {\n                readed = line.indexOf(\"\\n\", readed) + 1;\n              }\n            }\n          } else {\n            break;\n          }\n        }\n        return line.substr(readed);\n      }\n    }, {\n      key: \"_isBinary\",\n      value: function _isBinary(binData) {\n        var reader = new DataView(binData);\n        var face_size = 32 / 8 * 3 + 32 / 8 * 3 * 3 + 16 / 8;\n        var n_faces = reader.getUint32(80, true);\n        var expect = 80 + 32 / 8 + n_faces * face_size;\n        if (expect === reader.byteLength) {\n          return true;\n        }\n        var fileLength = reader.byteLength;\n        for (let index = 0; index < fileLength; index++) {\n          if (reader.getUint8(index, false) > 127) {\n            return true;\n          }\n        }\n        return false;\n      }\n    }, {\n      key: \"_ensureBinary\",\n      value: function _ensureBinary(buf) {\n        if (typeof buf === \"string\") {\n          var array_buffer = new Uint8Array(buf.length);\n          for (let i2 = 0; i2 < buf.length; i2++) {\n            array_buffer[i2] = buf.charCodeAt(i2) & 255;\n          }\n          return array_buffer.buffer || array_buffer;\n        } else {\n          return buf;\n        }\n      }\n    }, {\n      key: \"_ensureString\",\n      value: function _ensureString(buf) {\n        if (typeof buf !== \"string\") {\n          return decodeText(new Uint8Array(buf));\n        } else {\n          return buf;\n        }\n      }\n    }, {\n      key: \"parse\",\n      value: function _parse(data, onLoad) {\n        var binData = this._ensureBinary(data);\n        this._data = this._ensureString(data);\n        this.onLoad = onLoad;\n        return this._isBinary(binData) ? this._parseBinary(binData) : this._parseASCII();\n      }\n    }, {\n      key: \"_parseBinary\",\n      value: function _parseBinary(data) {\n        return this._parseASCII(decodeText(new Uint8Array(data)));\n      }\n    }, {\n      key: \"_parseASCII\",\n      value: function _parseASCII() {\n        var path;\n        if (this.resourcePath !== \"\") {\n          path = this.resourcePath;\n        } else if (this.path !== \"\") {\n          path = this.path;\n        } else {\n          path = LoaderUtils.extractUrlBase(this.url);\n        }\n        this.texloader.setPath(path).setCrossOrigin(this.crossOrigin);\n        var endRead = 16;\n        this.Hierarchies.children = [];\n        this._hierarchieParse(this.Hierarchies, endRead);\n        this._changeRoot();\n        this._currentObject = this.Hierarchies.children.shift();\n        this._mainloop();\n      }\n    }, {\n      key: \"_hierarchieParse\",\n      value: function _hierarchieParse(_parent, _end) {\n        var endRead = _end;\n        while (true) {\n          var find1 = this._data.indexOf(\"{\", endRead) + 1;\n          var findEnd = this._data.indexOf(\"}\", endRead);\n          var findNext = this._data.indexOf(\"{\", find1) + 1;\n          if (find1 > 0 && findEnd > find1) {\n            var _currentObject = {};\n            _currentObject.children = [];\n            var nameData = this._readLine(this._data.substr(endRead, find1 - endRead - 1)).trim();\n            var word = nameData.split(/ /g);\n            if (word.length > 0) {\n              _currentObject.type = word[0];\n              if (word.length >= 2) {\n                _currentObject.name = word[1];\n              } else {\n                _currentObject.name = word[0] + this.Hierarchies.children.length;\n              }\n            } else {\n              _currentObject.name = nameData;\n              _currentObject.type = \"\";\n            }\n            if (_currentObject.type === \"Animation\") {\n              _currentObject.data = this._data.substr(findNext, findEnd - findNext).trim();\n              var refs = this._hierarchieParse(_currentObject, findEnd + 1);\n              endRead = refs.end;\n              _currentObject.children = refs.parent.children;\n            } else {\n              var DataEnder = this._data.lastIndexOf(\";\", findNext > 0 ? Math.min(findNext, findEnd) : findEnd);\n              _currentObject.data = this._data.substr(find1, DataEnder - find1).trim();\n              if (findNext <= 0 || findEnd < findNext) {\n                endRead = findEnd + 1;\n              } else {\n                var nextStart = Math.max(DataEnder + 1, find1);\n                var _refs = this._hierarchieParse(_currentObject, nextStart);\n                endRead = _refs.end;\n                _currentObject.children = _refs.parent.children;\n              }\n            }\n            _currentObject.parent = _parent;\n            if (_currentObject.type != \"template\") {\n              _parent.children.push(_currentObject);\n            }\n          } else {\n            endRead = find1 === -1 ? this._data.length : findEnd + 1;\n            break;\n          }\n        }\n        return {\n          parent: _parent,\n          end: endRead\n        };\n      }\n    }, {\n      key: \"_mainloop\",\n      value: function _mainloop() {\n        var _this2 = this;\n        this._mainProc();\n        if (this._currentObject.parent || this._currentObject.children.length > 0 || !this._currentObject.worked) {\n          setTimeout(function () {\n            _this2._mainloop();\n          }, 1);\n        } else {\n          setTimeout(function () {\n            _this2.onLoad({\n              models: _this2.Meshes,\n              animations: _this2.animations\n            });\n          }, 1);\n        }\n      }\n    }, {\n      key: \"_mainProc\",\n      value: function _mainProc() {\n        var breakFlag = false;\n        while (true) {\n          if (!this._currentObject.worked) {\n            switch (this._currentObject.type) {\n              case \"template\":\n                break;\n              case \"AnimTicksPerSecond\":\n                this.animTicksPerSecond = parseInt(this._currentObject.data);\n                break;\n              case \"Frame\":\n                this._setFrame();\n                break;\n              case \"FrameTransformMatrix\":\n                this._setFrameTransformMatrix();\n                break;\n              case \"Mesh\":\n                this._changeRoot();\n                this._currentGeo = {};\n                this._currentGeo.name = this._currentObject.name.trim();\n                this._currentGeo.parentName = this._getParentName(this._currentObject).trim();\n                this._currentGeo.VertexSetedBoneCount = [];\n                this._currentGeo.GeometryData = {\n                  vertices: [],\n                  normals: [],\n                  uvs: [],\n                  skinIndices: [],\n                  skinWeights: [],\n                  indices: [],\n                  materialIndices: []\n                };\n                this._currentGeo.Materials = [];\n                this._currentGeo.normalVectors = [];\n                this._currentGeo.BoneInfs = [];\n                this._currentGeo.baseFrame = this._currentFrame;\n                this._makeBoneFrom_CurrentFrame();\n                this._readVertexDatas();\n                breakFlag = true;\n                break;\n              case \"MeshNormals\":\n                this._readVertexDatas();\n                break;\n              case \"MeshTextureCoords\":\n                this._setMeshTextureCoords();\n                break;\n              case \"VertexDuplicationIndices\":\n                break;\n              case \"MeshMaterialList\":\n                this._setMeshMaterialList();\n                break;\n              case \"Material\":\n                this._setMaterial();\n                break;\n              case \"SkinWeights\":\n                this._setSkinWeights();\n                break;\n              case \"AnimationSet\":\n                this._changeRoot();\n                this._currentAnime = {};\n                this._currentAnime.name = this._currentObject.name.trim();\n                this._currentAnime.AnimeFrames = [];\n                break;\n              case \"Animation\":\n                if (this._currentAnimeFrames) {\n                  this._currentAnime.AnimeFrames.push(this._currentAnimeFrames);\n                }\n                this._currentAnimeFrames = new XAnimationInfo();\n                this._currentAnimeFrames.boneName = this._currentObject.data.trim();\n                break;\n              case \"AnimationKey\":\n                this._readAnimationKey();\n                breakFlag = true;\n                break;\n            }\n            this._currentObject.worked = true;\n          }\n          if (this._currentObject.children.length > 0) {\n            this._currentObject = this._currentObject.children.shift();\n            if (this.debug) {\n              console.log(\"processing \" + this._currentObject.name);\n            }\n            if (breakFlag) break;\n          } else {\n            if (this._currentObject.worked) {\n              if (this._currentObject.parent && !this._currentObject.parent.parent) {\n                this._changeRoot();\n              }\n            }\n            if (this._currentObject.parent) {\n              this._currentObject = this._currentObject.parent;\n            } else {\n              breakFlag = true;\n            }\n            if (breakFlag) break;\n          }\n        }\n        return;\n      }\n    }, {\n      key: \"_changeRoot\",\n      value: function _changeRoot() {\n        if (this._currentGeo != null && this._currentGeo.name) {\n          this._makeOutputGeometry();\n        }\n        this._currentGeo = {};\n        if (this._currentAnime != null && this._currentAnime.name) {\n          if (this._currentAnimeFrames) {\n            this._currentAnime.AnimeFrames.push(this._currentAnimeFrames);\n            this._currentAnimeFrames = null;\n          }\n          this._makeOutputAnimation();\n        }\n        this._currentAnime = {};\n      }\n    }, {\n      key: \"_getParentName\",\n      value: function _getParentName(_obj) {\n        if (_obj.parent) {\n          if (_obj.parent.name) {\n            return _obj.parent.name;\n          } else {\n            return this._getParentName(_obj.parent);\n          }\n        } else {\n          return \"\";\n        }\n      }\n    }, {\n      key: \"_setFrame\",\n      value: function _setFrame() {\n        this._nowFrameName = this._currentObject.name.trim();\n        this._currentFrame = {};\n        this._currentFrame.name = this._nowFrameName;\n        this._currentFrame.children = [];\n        if (this._currentObject.parent && this._currentObject.parent.name) {\n          this._currentFrame.parentName = this._currentObject.parent.name;\n        }\n        this.frameHierarchie.push(this._nowFrameName);\n        this.HieStack[this._nowFrameName] = this._currentFrame;\n      }\n    }, {\n      key: \"_setFrameTransformMatrix\",\n      value: function _setFrameTransformMatrix() {\n        this._currentFrame.FrameTransformMatrix = new Matrix4();\n        var data = this._currentObject.data.split(\",\");\n        this._ParseMatrixData(this._currentFrame.FrameTransformMatrix, data);\n        this._makeBoneFrom_CurrentFrame();\n      }\n    }, {\n      key: \"_makeBoneFrom_CurrentFrame\",\n      value: function _makeBoneFrom_CurrentFrame() {\n        if (!this._currentFrame.FrameTransformMatrix) {\n          return;\n        }\n        var b = new Bone();\n        b.name = this._currentFrame.name;\n        b.applyMatrix4(this._currentFrame.FrameTransformMatrix);\n        b.matrixWorld = b.matrix;\n        b.FrameTransformMatrix = this._currentFrame.FrameTransformMatrix;\n        this._currentFrame.putBone = b;\n        if (this._currentFrame.parentName) {\n          for (let frame in this.HieStack) {\n            if (this.HieStack[frame].name === this._currentFrame.parentName) {\n              this.HieStack[frame].putBone.add(this._currentFrame.putBone);\n            }\n          }\n        }\n      }\n    }, {\n      key: \"_readVertexDatas\",\n      value: function _readVertexDatas() {\n        var endRead = 0;\n        var mode = 0;\n        var mode_local = 0;\n        var maxLength = 0;\n        while (true) {\n          var changeMode = false;\n          if (mode_local === 0) {\n            var refO = this._readInt1(endRead);\n            endRead = refO.endRead;\n            mode_local = 1;\n            maxLength = this._currentObject.data.indexOf(\";;\", endRead) + 1;\n            if (maxLength <= 0) {\n              maxLength = this._currentObject.data.length;\n            }\n          } else {\n            var find = 0;\n            switch (mode) {\n              case 0:\n                find = this._currentObject.data.indexOf(\",\", endRead) + 1;\n                break;\n              case 1:\n                find = this._currentObject.data.indexOf(\";,\", endRead) + 1;\n                break;\n            }\n            if (find === 0 || find > maxLength) {\n              find = maxLength;\n              mode_local = 0;\n              changeMode = true;\n            }\n            switch (this._currentObject.type) {\n              case \"Mesh\":\n                switch (mode) {\n                  case 0:\n                    this._readVertex1(this._currentObject.data.substr(endRead, find - endRead));\n                    break;\n                  case 1:\n                    this._readFace1(this._currentObject.data.substr(endRead, find - endRead));\n                    break;\n                }\n                break;\n              case \"MeshNormals\":\n                switch (mode) {\n                  case 0:\n                    this._readNormalVector1(this._currentObject.data.substr(endRead, find - endRead));\n                    break;\n                }\n                break;\n            }\n            endRead = find + 1;\n            if (changeMode) {\n              mode++;\n            }\n          }\n          if (endRead >= this._currentObject.data.length) {\n            break;\n          }\n        }\n      }\n    }, {\n      key: \"_readInt1\",\n      value: function _readInt1(start) {\n        var find = this._currentObject.data.indexOf(\";\", start);\n        return {\n          refI: parseInt(this._currentObject.data.substr(start, find - start)),\n          endRead: find + 1\n        };\n      }\n    }, {\n      key: \"_readVertex1\",\n      value: function _readVertex1(line) {\n        var data = this._readLine(line.trim()).substr(0, line.length - 2).split(\";\");\n        this._currentGeo.GeometryData.vertices.push(parseFloat(data[0]), parseFloat(data[1]), parseFloat(data[2]));\n        this._currentGeo.GeometryData.skinIndices.push(0, 0, 0, 0);\n        this._currentGeo.GeometryData.skinWeights.push(1, 0, 0, 0);\n        this._currentGeo.VertexSetedBoneCount.push(0);\n      }\n    }, {\n      key: \"_readFace1\",\n      value: function _readFace1(line) {\n        var data = this._readLine(line.trim()).substr(2, line.length - 4).split(\",\");\n        this._currentGeo.GeometryData.indices.push(parseInt(data[0], 10), parseInt(data[1], 10), parseInt(data[2], 10));\n      }\n    }, {\n      key: \"_readNormalVector1\",\n      value: function _readNormalVector1(line) {\n        var data = this._readLine(line.trim()).substr(0, line.length - 2).split(\";\");\n        this._currentGeo.GeometryData.normals.push(parseFloat(data[0]), parseFloat(data[1]), parseFloat(data[2]));\n      }\n    }, {\n      key: \"_buildGeometry\",\n      value: function _buildGeometry() {\n        var bufferGeometry = new BufferGeometry();\n        var position = [];\n        var normals = [];\n        var uvs = [];\n        var skinIndices = [];\n        var skinWeights = [];\n        var data = this._currentGeo.GeometryData;\n        for (let i2 = 0, l = data.indices.length; i2 < l; i2++) {\n          var stride2 = data.indices[i2] * 2;\n          var stride3 = data.indices[i2] * 3;\n          var stride4 = data.indices[i2] * 4;\n          position.push(data.vertices[stride3], data.vertices[stride3 + 1], data.vertices[stride3 + 2]);\n          normals.push(data.normals[stride3], data.normals[stride3 + 1], data.normals[stride3 + 2]);\n          skinIndices.push(data.skinIndices[stride4], data.skinIndices[stride4 + 1], data.skinIndices[stride4 + 2], data.skinIndices[stride4 + 3]);\n          skinWeights.push(data.skinWeights[stride4], data.skinWeights[stride4 + 1], data.skinWeights[stride4 + 2], data.skinWeights[stride4 + 3]);\n          uvs.push(data.uvs[stride2], data.uvs[stride2 + 1]);\n        }\n        bufferGeometry.setAttribute(\"position\", new Float32BufferAttribute(position, 3));\n        bufferGeometry.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n        bufferGeometry.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n        bufferGeometry.setAttribute(\"skinIndex\", new Uint16BufferAttribute(skinIndices, 4));\n        bufferGeometry.setAttribute(\"skinWeight\", new Float32BufferAttribute(skinWeights, 4));\n        this._computeGroups(bufferGeometry, data.materialIndices);\n        return bufferGeometry;\n      }\n    }, {\n      key: \"_computeGroups\",\n      value: function _computeGroups(bufferGeometry, materialIndices) {\n        var group;\n        var groups = [];\n        var materialIndex = void 0;\n        for (let i2 = 0; i2 < materialIndices.length; i2++) {\n          var currentMaterialIndex = materialIndices[i2];\n          if (currentMaterialIndex !== materialIndex) {\n            materialIndex = currentMaterialIndex;\n            if (group !== void 0) {\n              group.count = i2 * 3 - group.start;\n              groups.push(group);\n            }\n            group = {\n              start: i2 * 3,\n              materialIndex\n            };\n          }\n        }\n        if (group !== void 0) {\n          group.count = i * 3 - group.start;\n          groups.push(group);\n        }\n        bufferGeometry.groups = groups;\n      }\n    }, {\n      key: \"_setMeshTextureCoords\",\n      value: function _setMeshTextureCoords() {\n        var endRead = 0;\n        var mode = 0;\n        var mode_local = 0;\n        while (true) {\n          switch (mode) {\n            case 0:\n              if (mode_local === 0) {\n                var refO = this._readInt1(0);\n                endRead = refO.endRead;\n                mode_local = 1;\n              } else {\n                var find = this._currentObject.data.indexOf(\",\", endRead) + 1;\n                if (find === 0) {\n                  find = this._currentObject.data.length;\n                  mode = 2;\n                  mode_local = 0;\n                }\n                var line = this._currentObject.data.substr(endRead, find - endRead);\n                var data = this._readLine(line.trim()).split(\";\");\n                if (this.IsUvYReverse) {\n                  this._currentGeo.GeometryData.uvs.push(parseFloat(data[0]), 1 - parseFloat(data[1]));\n                } else {\n                  this._currentGeo.GeometryData.uvs.push(parseFloat(data[0]), parseFloat(data[1]));\n                }\n                endRead = find + 1;\n              }\n              break;\n          }\n          if (endRead >= this._currentObject.data.length) {\n            break;\n          }\n        }\n      }\n    }, {\n      key: \"_setMeshMaterialList\",\n      value: function _setMeshMaterialList() {\n        var endRead = 0;\n        var mode = 0;\n        var mode_local = 0;\n        while (true) {\n          if (mode_local < 2) {\n            var refO = this._readInt1(endRead);\n            endRead = refO.endRead;\n            mode_local++;\n          } else {\n            var find = this._currentObject.data.indexOf(\";\", endRead);\n            if (find === -1) {\n              find = this._currentObject.data.length;\n              mode = 3;\n              mode_local = 0;\n            }\n            var line = this._currentObject.data.substr(endRead, find - endRead);\n            var data = this._readLine(line.trim()).split(\",\");\n            for (let i2 = 0; i2 < data.length; i2++) {\n              this._currentGeo.GeometryData.materialIndices[i2] = parseInt(data[i2]);\n            }\n            endRead = this._currentObject.data.length;\n          }\n          if (endRead >= this._currentObject.data.length || mode >= 3) {\n            break;\n          }\n        }\n      }\n    }, {\n      key: \"_setMaterial\",\n      value: function _setMaterial() {\n        var _nowMat = new MeshPhongMaterial({\n          color: Math.random() * 16777215\n        });\n        _nowMat.side = FrontSide;\n        _nowMat.name = this._currentObject.name;\n        var endRead = 0;\n        var find = this._currentObject.data.indexOf(\";;\", endRead);\n        var line = this._currentObject.data.substr(endRead, find - endRead);\n        var data = this._readLine(line.trim()).split(\";\");\n        _nowMat.color.r = parseFloat(data[0]);\n        _nowMat.color.g = parseFloat(data[1]);\n        _nowMat.color.b = parseFloat(data[2]);\n        endRead = find + 2;\n        find = this._currentObject.data.indexOf(\";\", endRead);\n        line = this._currentObject.data.substr(endRead, find - endRead);\n        _nowMat.shininess = parseFloat(this._readLine(line));\n        endRead = find + 1;\n        find = this._currentObject.data.indexOf(\";;\", endRead);\n        line = this._currentObject.data.substr(endRead, find - endRead);\n        var data2 = this._readLine(line.trim()).split(\";\");\n        _nowMat.specular.r = parseFloat(data2[0]);\n        _nowMat.specular.g = parseFloat(data2[1]);\n        _nowMat.specular.b = parseFloat(data2[2]);\n        endRead = find + 2;\n        find = this._currentObject.data.indexOf(\";;\", endRead);\n        if (find === -1) {\n          find = this._currentObject.data.length;\n        }\n        line = this._currentObject.data.substr(endRead, find - endRead);\n        var data3 = this._readLine(line.trim()).split(\";\");\n        _nowMat.emissive.r = parseFloat(data3[0]);\n        _nowMat.emissive.g = parseFloat(data3[1]);\n        _nowMat.emissive.b = parseFloat(data3[2]);\n        var localObject = null;\n        while (true) {\n          if (this._currentObject.children.length > 0) {\n            localObject = this._currentObject.children.shift();\n            if (this.debug) {\n              console.log(\"processing \" + localObject.name);\n            }\n            var fileName = localObject.data.substr(1, localObject.data.length - 2);\n            switch (localObject.type) {\n              case \"TextureFilename\":\n                _nowMat.map = this.texloader.load(fileName);\n                break;\n              case \"BumpMapFilename\":\n                _nowMat.bumpMap = this.texloader.load(fileName);\n                _nowMat.bumpScale = 0.05;\n                break;\n              case \"NormalMapFilename\":\n                _nowMat.normalMap = this.texloader.load(fileName);\n                _nowMat.normalScale = new Vector2(2, 2);\n                break;\n              case \"EmissiveMapFilename\":\n                _nowMat.emissiveMap = this.texloader.load(fileName);\n                break;\n              case \"LightMapFilename\":\n                _nowMat.lightMap = this.texloader.load(fileName);\n                break;\n            }\n          } else {\n            break;\n          }\n        }\n        this._currentGeo.Materials.push(_nowMat);\n      }\n    }, {\n      key: \"_setSkinWeights\",\n      value: function _setSkinWeights() {\n        var boneInf = new XboneInf();\n        var endRead = 0;\n        var find = this._currentObject.data.indexOf(\";\", endRead);\n        var line = this._currentObject.data.substr(endRead, find - endRead);\n        endRead = find + 1;\n        boneInf.boneName = line.substr(1, line.length - 2);\n        boneInf.BoneIndex = this._currentGeo.BoneInfs.length;\n        find = this._currentObject.data.indexOf(\";\", endRead);\n        endRead = find + 1;\n        find = this._currentObject.data.indexOf(\";\", endRead);\n        line = this._currentObject.data.substr(endRead, find - endRead);\n        var data = this._readLine(line.trim()).split(\",\");\n        for (let i2 = 0; i2 < data.length; i2++) {\n          boneInf.Indeces.push(parseInt(data[i2]));\n        }\n        endRead = find + 1;\n        find = this._currentObject.data.indexOf(\";\", endRead);\n        line = this._currentObject.data.substr(endRead, find - endRead);\n        var data2 = this._readLine(line.trim()).split(\",\");\n        for (let _i = 0; _i < data2.length; _i++) {\n          boneInf.Weights.push(parseFloat(data2[_i]));\n        }\n        endRead = find + 1;\n        find = this._currentObject.data.indexOf(\";\", endRead);\n        if (find <= 0) {\n          find = this._currentObject.data.length;\n        }\n        line = this._currentObject.data.substr(endRead, find - endRead);\n        var data3 = this._readLine(line.trim()).split(\",\");\n        boneInf.OffsetMatrix = new Matrix4();\n        this._ParseMatrixData(boneInf.OffsetMatrix, data3);\n        this._currentGeo.BoneInfs.push(boneInf);\n      }\n    }, {\n      key: \"_makePutBoneList\",\n      value: function _makePutBoneList(_RootName, _bones) {\n        var putting = false;\n        for (let frame in this.HieStack) {\n          if (this.HieStack[frame].name === _RootName || putting) {\n            putting = true;\n            var b = new Bone();\n            b.name = this.HieStack[frame].name;\n            b.applyMatrix4(this.HieStack[frame].FrameTransformMatrix);\n            b.matrixWorld = b.matrix;\n            b.FrameTransformMatrix = this.HieStack[frame].FrameTransformMatrix;\n            b.pos = new Vector3().setFromMatrixPosition(b.FrameTransformMatrix).toArray();\n            b.rotq = new Quaternion().setFromRotationMatrix(b.FrameTransformMatrix).toArray();\n            b.scl = new Vector3().setFromMatrixScale(b.FrameTransformMatrix).toArray();\n            if (this.HieStack[frame].parentName && this.HieStack[frame].parentName.length > 0) {\n              for (let i2 = 0; i2 < _bones.length; i2++) {\n                if (this.HieStack[frame].parentName === _bones[i2].name) {\n                  _bones[i2].add(b);\n                  b.parent = i2;\n                  break;\n                }\n              }\n            }\n            _bones.push(b);\n          }\n        }\n      }\n    }, {\n      key: \"_makeOutputGeometry\",\n      value: function _makeOutputGeometry() {\n        var mesh = null;\n        if (this._currentGeo.BoneInfs.length > 0) {\n          var putBones = [];\n          this._makePutBoneList(this._currentGeo.baseFrame.parentName, putBones);\n          for (let bi = 0; bi < this._currentGeo.BoneInfs.length; bi++) {\n            var boneIndex = 0;\n            for (let bb = 0; bb < putBones.length; bb++) {\n              if (putBones[bb].name === this._currentGeo.BoneInfs[bi].boneName) {\n                boneIndex = bb;\n                putBones[bb].OffsetMatrix = new Matrix4();\n                putBones[bb].OffsetMatrix.copy(this._currentGeo.BoneInfs[bi].OffsetMatrix);\n                break;\n              }\n            }\n            for (let vi = 0; vi < this._currentGeo.BoneInfs[bi].Indeces.length; vi++) {\n              var nowVertexID = this._currentGeo.BoneInfs[bi].Indeces[vi];\n              var nowVal = this._currentGeo.BoneInfs[bi].Weights[vi];\n              var stride = nowVertexID * 4;\n              switch (this._currentGeo.VertexSetedBoneCount[nowVertexID]) {\n                case 0:\n                  this._currentGeo.GeometryData.skinIndices[stride] = boneIndex;\n                  this._currentGeo.GeometryData.skinWeights[stride] = nowVal;\n                  break;\n                case 1:\n                  this._currentGeo.GeometryData.skinIndices[stride + 1] = boneIndex;\n                  this._currentGeo.GeometryData.skinWeights[stride + 1] = nowVal;\n                  break;\n                case 2:\n                  this._currentGeo.GeometryData.skinIndices[stride + 2] = boneIndex;\n                  this._currentGeo.GeometryData.skinWeights[stride + 2] = nowVal;\n                  break;\n                case 3:\n                  this._currentGeo.GeometryData.skinIndices[stride + 3] = boneIndex;\n                  this._currentGeo.GeometryData.skinWeights[stride + 3] = nowVal;\n                  break;\n              }\n              this._currentGeo.VertexSetedBoneCount[nowVertexID]++;\n              if (this._currentGeo.VertexSetedBoneCount[nowVertexID] > 4) {\n                console.log(\"warn! over 4 bone weight! :\" + nowVertexID);\n              }\n            }\n          }\n          for (let sk = 0; sk < this._currentGeo.Materials.length; sk++) {\n            this._currentGeo.Materials[sk].skinning = true;\n          }\n          var offsetList = [];\n          for (let _bi = 0; _bi < putBones.length; _bi++) {\n            if (putBones[_bi].OffsetMatrix) {\n              offsetList.push(putBones[_bi].OffsetMatrix);\n            } else {\n              offsetList.push(new Matrix4());\n            }\n          }\n          var bufferGeometry = this._buildGeometry();\n          mesh = new SkinnedMesh(bufferGeometry, this._currentGeo.Materials.length === 1 ? this._currentGeo.Materials[0] : this._currentGeo.Materials);\n          this._initSkeleton(mesh, putBones, offsetList);\n        } else {\n          var _bufferGeometry = this._buildGeometry();\n          mesh = new Mesh(_bufferGeometry, this._currentGeo.Materials.length === 1 ? this._currentGeo.Materials[0] : this._currentGeo.Materials);\n        }\n        mesh.name = this._currentGeo.name;\n        var worldBaseMx = new Matrix4();\n        var currentMxFrame = this._currentGeo.baseFrame.putBone;\n        if (currentMxFrame && currentMxFrame.parent) {\n          while (true) {\n            currentMxFrame = currentMxFrame.parent;\n            if (currentMxFrame) {\n              worldBaseMx.multiply(currentMxFrame.FrameTransformMatrix);\n            } else {\n              break;\n            }\n          }\n          mesh.applyMatrix4(worldBaseMx);\n        }\n        this.Meshes.push(mesh);\n      }\n    }, {\n      key: \"_initSkeleton\",\n      value: function _initSkeleton(mesh, boneList, boneInverses) {\n        var bones = [],\n          bone,\n          gbone;\n        var i2, il;\n        for (i2 = 0, il = boneList.length; i2 < il; i2++) {\n          gbone = boneList[i2];\n          bone = new Bone();\n          bones.push(bone);\n          bone.name = gbone.name;\n          bone.position.fromArray(gbone.pos);\n          bone.quaternion.fromArray(gbone.rotq);\n          if (gbone.scl !== void 0) bone.scale.fromArray(gbone.scl);\n        }\n        for (i2 = 0, il = boneList.length; i2 < il; i2++) {\n          gbone = boneList[i2];\n          if (gbone.parent !== -1 && gbone.parent !== null && bones[gbone.parent] !== void 0) {\n            bones[gbone.parent].add(bones[i2]);\n          } else {\n            mesh.add(bones[i2]);\n          }\n        }\n        mesh.updateMatrixWorld(true);\n        var skeleton = new Skeleton(bones, boneInverses);\n        mesh.bind(skeleton, mesh.matrixWorld);\n      }\n    }, {\n      key: \"_readAnimationKey\",\n      value: function _readAnimationKey() {\n        var endRead = 0;\n        var find = this._currentObject.data.indexOf(\";\", endRead);\n        var line = this._currentObject.data.substr(endRead, find - endRead);\n        endRead = find + 1;\n        var nowKeyType = parseInt(this._readLine(line));\n        find = this._currentObject.data.indexOf(\";\", endRead);\n        endRead = find + 1;\n        line = this._currentObject.data.substr(endRead);\n        var data = this._readLine(line.trim()).split(\";;,\");\n        for (let i2 = 0; i2 < data.length; i2++) {\n          var data2 = data[i2].split(\";\");\n          var keyInfo = new XKeyFrameInfo();\n          keyInfo.type = nowKeyType;\n          keyInfo.Frame = parseInt(data2[0]);\n          keyInfo.index = this._currentAnimeFrames.keyFrames.length;\n          keyInfo.time = keyInfo.Frame;\n          if (nowKeyType != 4) {\n            var frameFound = false;\n            for (let mm = 0; mm < this._currentAnimeFrames.keyFrames.length; mm++) {\n              if (this._currentAnimeFrames.keyFrames[mm].Frame === keyInfo.Frame) {\n                keyInfo = this._currentAnimeFrames.keyFrames[mm];\n                frameFound = true;\n                break;\n              }\n            }\n            var frameValue = data2[2].split(\",\");\n            switch (nowKeyType) {\n              case 0:\n                keyInfo.rot = new Quaternion(parseFloat(frameValue[1]), parseFloat(frameValue[2]), parseFloat(frameValue[3]), parseFloat(frameValue[0]) * -1);\n                break;\n              case 1:\n                keyInfo.scl = new Vector3(parseFloat(frameValue[0]), parseFloat(frameValue[1]), parseFloat(frameValue[2]));\n                break;\n              case 2:\n                keyInfo.pos = new Vector3(parseFloat(frameValue[0]), parseFloat(frameValue[1]), parseFloat(frameValue[2]));\n                break;\n            }\n            if (!frameFound) {\n              this._currentAnimeFrames.keyFrames.push(keyInfo);\n            }\n          } else {\n            keyInfo.matrix = new Matrix4();\n            this._ParseMatrixData(keyInfo.matrix, data2[2].split(\",\"));\n            this._currentAnimeFrames.keyFrames.push(keyInfo);\n          }\n        }\n      }\n    }, {\n      key: \"_makeOutputAnimation\",\n      value: function _makeOutputAnimation() {\n        var animationObj = new XAnimationObj(this.options);\n        animationObj.fps = this.animTicksPerSecond;\n        animationObj.name = this._currentAnime.name;\n        animationObj.make(this._currentAnime.AnimeFrames);\n        this.animations.push(animationObj);\n      }\n    }, {\n      key: \"assignAnimation\",\n      value: function assignAnimation(_model, _animation) {\n        var model = _model;\n        var animation = _animation;\n        if (!model) {\n          model = this.Meshes[0];\n        }\n        if (!animation) {\n          animation = this.animations[0];\n        }\n        if (!model || !animation) {\n          return null;\n        }\n        var put = {};\n        put.fps = animation.fps;\n        put.name = animation.name;\n        put.length = animation.length;\n        put.hierarchy = [];\n        for (let b = 0; b < model.skeleton.bones.length; b++) {\n          var findAnimation = false;\n          for (let i2 = 0; i2 < animation.hierarchy.length; i2++) {\n            if (model.skeleton.bones[b].name === animation.hierarchy[i2].name) {\n              findAnimation = true;\n              var c_key = animation.hierarchy[i2].copy();\n              c_key.parent = -1;\n              if (model.skeleton.bones[b].parent && model.skeleton.bones[b].parent.type === \"Bone\") {\n                for (let bb = 0; bb < put.hierarchy.length; bb++) {\n                  if (put.hierarchy[bb].name === model.skeleton.bones[b].parent.name) {\n                    c_key.parent = bb;\n                    c_key.parentName = model.skeleton.bones[b].parent.name;\n                  }\n                }\n              }\n              put.hierarchy.push(c_key);\n              break;\n            }\n          }\n          if (!findAnimation) {\n            var _c_key = animation.hierarchy[0].copy();\n            _c_key.name = model.skeleton.bones[b].name;\n            _c_key.parent = -1;\n            for (let k = 0; k < _c_key.keys.length; k++) {\n              if (_c_key.keys[k].pos) {\n                _c_key.keys[k].pos.set(0, 0, 0);\n              }\n              if (_c_key.keys[k].scl) {\n                _c_key.keys[k].scl.set(1, 1, 1);\n              }\n              if (_c_key.keys[k].rot) {\n                _c_key.keys[k].rot.set(0, 0, 0, 1);\n              }\n            }\n            put.hierarchy.push(_c_key);\n          }\n        }\n        if (!model.geometry.animations) {\n          model.geometry.animations = [];\n        }\n        model.geometry.animations.push(AnimationClip.parseAnimation(put, model.skeleton.bones));\n        if (!model.animationMixer) {\n          model.animationMixer = new AnimationMixer(model);\n        }\n        return put;\n      }\n    }, {\n      key: \"_ParseMatrixData\",\n      value: function _ParseMatrixData(targetMatrix, data) {\n        targetMatrix.set(parseFloat(data[0]), parseFloat(data[4]), parseFloat(data[8]), parseFloat(data[12]), parseFloat(data[1]), parseFloat(data[5]), parseFloat(data[9]), parseFloat(data[13]), parseFloat(data[2]), parseFloat(data[6]), parseFloat(data[10]), parseFloat(data[14]), parseFloat(data[3]), parseFloat(data[7]), parseFloat(data[11]), parseFloat(data[15]));\n      }\n    }]);\n    return XLoader3;\n  }();\n  return XLoader2;\n}();\nexport { XLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "createClass", "defineProperties", "target", "props", "i2", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "protoProps", "staticProps", "prototype", "XboneInf", "XboneInf2", "boneName", "BoneIndex", "Indeces", "Weights", "initMatrix", "OffsetMatrix", "XAnimationInfo", "XAnimationInfo2", "animeName", "targetBone", "keyType", "frameStartLv", "keyFrames", "InverseMx", "XAnimationObj", "XAnimationObj2", "_flags", "fps", "name", "hierarchy", "put<PERSON><PERSON>s", "putPos", "putRot", "putScl", "value", "make", "XAnimationInfoArray", "push", "makeBonekeys", "keys", "time", "clone", "assign", "refObj", "parent", "keyFrameRefactor", "copy", "keyframe", "pos", "rot", "scl", "matrix", "Vector3", "setFromMatrixPosition", "Quaternion", "setFromRotationMatrix", "setFromMatrixScale", "XKeyFrameInfo", "XKeyFrameInfo2", "index", "<PERSON>ame", "XLoader2", "XLoader3", "manager", "Loader", "call", "debug", "texloader", "TextureLoader", "url", "_putMatLength", "_nowMat", "_nowFrameName", "frameHierarchie", "Hierarchies", "HieStack", "_currentObject", "_currentFrame", "_data", "onLoad", "IsUvYReverse", "Meshes", "animations", "animTicksPerSecond", "_currentGeo", "_currentAnime", "_currentAnimeFrames", "_setArgOption", "_arg", "_start", "arguments", "options", "load", "onProgress", "onError", "_this", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "response", "parse", "e", "console", "error", "itemError", "_readLine", "line", "readed", "find", "indexOf", "foundNewLine", "substr", "_isBinary", "binData", "reader", "DataView", "face_size", "n_faces", "getUint32", "expect", "byteLength", "fileLength", "getUint8", "_ensureBinary", "buf", "array_buffer", "Uint8Array", "charCodeAt", "buffer", "_ensureString", "decodeText", "_parse", "data", "_parseBinary", "_parseASCII", "resourcePath", "LoaderUtils", "extractUrlBase", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "endRead", "children", "_hierarchieParse", "_changeRoot", "shift", "_mainloop", "_parent", "_end", "find1", "findEnd", "findNext", "nameData", "trim", "word", "split", "type", "refs", "end", "DataEnder", "lastIndexOf", "Math", "min", "nextStart", "max", "_refs", "_this2", "_mainProc", "worked", "setTimeout", "models", "breakFlag", "parseInt", "_setFrame", "_setFrameTransformMatrix", "parentName", "_getParentName", "VertexSetedBoneCount", "GeometryData", "vertices", "normals", "uvs", "skinIndices", "skinWeights", "indices", "materialIndices", "Materials", "normalVectors", "BoneInfs", "baseFrame", "_makeBoneFrom_CurrentFrame", "_readVertexDatas", "_setMeshTextureCoords", "_setMeshMaterialList", "_setMaterial", "_setSkinWeights", "Anime<PERSON>rames", "_readAnimationKey", "log", "_makeOutputGeometry", "_makeOutputAnimation", "_obj", "FrameTransformMatrix", "Matrix4", "_ParseMatrixData", "b", "Bone", "applyMatrix4", "matrixWorld", "putBone", "frame", "add", "mode", "mode_local", "max<PERSON><PERSON><PERSON>", "changeMode", "refO", "_readInt1", "_readVertex1", "_readFace1", "_readNormalVector1", "start", "refI", "parseFloat", "_buildGeometry", "bufferGeometry", "BufferGeometry", "position", "l", "stride2", "stride3", "stride4", "setAttribute", "Float32BufferAttribute", "Uint16BufferAttribute", "_computeGroups", "group", "groups", "materialIndex", "currentMaterialIndex", "count", "i", "MeshPhongMaterial", "color", "random", "side", "FrontSide", "r", "g", "shininess", "data2", "specular", "data3", "emissive", "localObject", "fileName", "map", "bumpMap", "bumpScale", "normalMap", "normalScale", "Vector2", "emissiveMap", "lightMap", "boneInf", "_i", "_makePutBoneList", "_RootName", "_bones", "putting", "toArray", "rotq", "mesh", "putBones", "bi", "boneIndex", "bb", "vi", "nowVertexID", "nowVal", "stride", "sk", "skinning", "offsetList", "_bi", "<PERSON><PERSON><PERSON><PERSON>", "_initSkeleton", "_bufferGeometry", "<PERSON><PERSON>", "worldBaseMx", "currentMxFrame", "multiply", "boneList", "boneInverses", "bones", "bone", "gbone", "il", "fromArray", "quaternion", "scale", "updateMatrixWorld", "skeleton", "Skeleton", "bind", "nowKeyType", "keyInfo", "frameFound", "mm", "frameValue", "animationObj", "assignAnimation", "_model", "_animation", "model", "animation", "put", "findAnimation", "c_key", "_c_key", "k", "set", "geometry", "AnimationClip", "parseAnimation", "animationMixer", "AnimationMixer", "targetMatrix"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\XLoader.js"], "sourcesContent": ["import {\n  AnimationClip,\n  AnimationMixer,\n  Bone,\n  BufferGeometry,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshPhongMaterial,\n  Quaternion,\n  Skeleton,\n  SkinnedMesh,\n  TextureLoader,\n  Uint16BufferAttribute,\n  Vector2,\n  Vector3,\n} from 'three'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nvar XLoader = /* @__PURE__ */ (function () {\n  var classCallCheck = function (instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError('Cannot call a class as a function')\n    }\n  }\n\n  var createClass = (function () {\n    function defineProperties(target, props) {\n      for (let i = 0; i < props.length; i++) {\n        var descriptor = props[i]\n        descriptor.enumerable = descriptor.enumerable || false\n        descriptor.configurable = true\n        if ('value' in descriptor) descriptor.writable = true\n        Object.defineProperty(target, descriptor.key, descriptor)\n      }\n    }\n\n    return function (Constructor, protoProps, staticProps) {\n      if (protoProps) defineProperties(Constructor.prototype, protoProps)\n      if (staticProps) defineProperties(Constructor, staticProps)\n      return Constructor\n    }\n  })()\n\n  var XboneInf = function XboneInf() {\n    classCallCheck(this, XboneInf)\n\n    this.boneName = ''\n    this.BoneIndex = 0\n    this.Indeces = []\n    this.Weights = []\n    this.initMatrix = null\n    this.OffsetMatrix = null\n  }\n\n  var XAnimationInfo = function XAnimationInfo() {\n    classCallCheck(this, XAnimationInfo)\n\n    this.animeName = ''\n    this.boneName = ''\n    this.targetBone = null\n    this.keyType = 4\n    this.frameStartLv = 0\n    this.keyFrames = []\n    this.InverseMx = null\n  }\n\n  var XAnimationObj = (function () {\n    function XAnimationObj(_flags) {\n      classCallCheck(this, XAnimationObj)\n\n      this.fps = 30\n      this.name = 'xanimation'\n      this.length = 0\n      this.hierarchy = []\n      this.putFlags = _flags\n      if (this.putFlags.putPos === undefined) {\n        this.putFlags.putPos = true\n      }\n\n      if (this.putFlags.putRot === undefined) {\n        this.putFlags.putRot = true\n      }\n\n      if (this.putFlags.putScl === undefined) {\n        this.putFlags.putScl = true\n      }\n    }\n\n    createClass(XAnimationObj, [\n      {\n        key: 'make',\n        value: function make(XAnimationInfoArray) {\n          for (let i = 0; i < XAnimationInfoArray.length; i++) {\n            this.hierarchy.push(this.makeBonekeys(XAnimationInfoArray[i]))\n          }\n\n          this.length = this.hierarchy[0].keys[this.hierarchy[0].keys.length - 1].time\n        },\n      },\n      {\n        key: 'clone',\n        value: function clone() {\n          return Object.assign({}, this)\n        },\n      },\n      {\n        key: 'makeBonekeys',\n        value: function makeBonekeys(XAnimationInfo) {\n          var refObj = {}\n          refObj.name = XAnimationInfo.boneName\n          refObj.parent = ''\n          refObj.keys = this.keyFrameRefactor(XAnimationInfo)\n          refObj.copy = function () {\n            return Object.assign({}, this)\n          }\n\n          return refObj\n        },\n      },\n      {\n        key: 'keyFrameRefactor',\n        value: function keyFrameRefactor(XAnimationInfo) {\n          var keys = []\n          for (let i = 0; i < XAnimationInfo.keyFrames.length; i++) {\n            var keyframe = {}\n            keyframe.time = XAnimationInfo.keyFrames[i].time * this.fps\n            if (XAnimationInfo.keyFrames[i].pos && this.putFlags.putPos) {\n              keyframe.pos = XAnimationInfo.keyFrames[i].pos\n            }\n\n            if (XAnimationInfo.keyFrames[i].rot && this.putFlags.putRot) {\n              keyframe.rot = XAnimationInfo.keyFrames[i].rot\n            }\n\n            if (XAnimationInfo.keyFrames[i].scl && this.putFlags.putScl) {\n              keyframe.scl = XAnimationInfo.keyFrames[i].scl\n            }\n\n            if (XAnimationInfo.keyFrames[i].matrix) {\n              keyframe.matrix = XAnimationInfo.keyFrames[i].matrix\n              if (this.putFlags.putPos) {\n                keyframe.pos = new Vector3().setFromMatrixPosition(keyframe.matrix)\n              }\n\n              if (this.putFlags.putRot) {\n                keyframe.rot = new Quaternion().setFromRotationMatrix(keyframe.matrix)\n              }\n\n              if (this.putFlags.putScl) {\n                keyframe.scl = new Vector3().setFromMatrixScale(keyframe.matrix)\n              }\n            }\n\n            keys.push(keyframe)\n          }\n\n          return keys\n        },\n      },\n    ])\n    return XAnimationObj\n  })()\n\n  var XKeyFrameInfo = function XKeyFrameInfo() {\n    classCallCheck(this, XKeyFrameInfo)\n\n    this.index = 0\n    this.Frame = 0\n    this.time = 0.0\n    this.matrix = null\n  }\n\n  var XLoader = (function () {\n    function XLoader(manager) {\n      Loader.call(this, manager)\n\n      classCallCheck(this, XLoader)\n\n      this.debug = false\n      this.texloader = new TextureLoader(this.manager)\n      this.url = ''\n      this._putMatLength = 0\n      this._nowMat = null\n      this._nowFrameName = ''\n      this.frameHierarchie = []\n      this.Hierarchies = {}\n      this.HieStack = []\n      this._currentObject = {}\n      this._currentFrame = {}\n      this._data = null\n      this.onLoad = null\n      this.IsUvYReverse = true\n      this.Meshes = []\n      this.animations = []\n      this.animTicksPerSecond = 30\n      this._currentGeo = null\n      this._currentAnime = null\n      this._currentAnimeFrames = null\n    }\n\n    createClass(XLoader, [\n      {\n        key: '_setArgOption',\n        value: function _setArgOption(_arg) {\n          var _start = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0\n\n          if (!_arg) {\n            return\n          }\n\n          for (let i = _start; i < _arg.length; i++) {\n            switch (i) {\n              case 0:\n                this.url = _arg[i]\n                break\n              case 1:\n                this.options = _arg[i]\n                break\n            }\n          }\n\n          if (this.options === undefined) {\n            this.options = {}\n          }\n        },\n      },\n      {\n        key: 'load',\n        value: function load(_arg, onLoad, onProgress, onError) {\n          var _this = this\n\n          this._setArgOption(_arg)\n          var loader = new FileLoader(this.manager)\n          loader.setPath(this.path)\n          loader.setResponseType('arraybuffer')\n          loader.setRequestHeader(this.requestHeader)\n          loader.setWithCredentials(this.withCredentials)\n          loader.load(\n            this.url,\n            function (response) {\n              try {\n                _this.parse(response, onLoad)\n              } catch (e) {\n                if (onError) {\n                  onError(e)\n                } else {\n                  console.error(e)\n                }\n\n                _this.manager.itemError(_this.url)\n              }\n            },\n            onProgress,\n            onError,\n          )\n        },\n      },\n      {\n        key: '_readLine',\n        value: function _readLine(line) {\n          var readed = 0\n          while (true) {\n            var find = -1\n            find = line.indexOf('//', readed)\n            if (find === -1) {\n              find = line.indexOf('#', readed)\n            }\n\n            if (find > -1 && find < 2) {\n              var foundNewLine = -1\n              foundNewLine = line.indexOf('\\r\\n', readed)\n              if (foundNewLine > 0) {\n                readed = foundNewLine + 2\n              } else {\n                foundNewLine = line.indexOf('\\r', readed)\n                if (foundNewLine > 0) {\n                  readed = foundNewLine + 1\n                } else {\n                  readed = line.indexOf('\\n', readed) + 1\n                }\n              }\n            } else {\n              break\n            }\n          }\n\n          return line.substr(readed)\n        },\n      },\n      {\n        key: '_readLine',\n        value: function _readLine(line) {\n          var readed = 0\n          while (true) {\n            var find = -1\n            find = line.indexOf('//', readed)\n            if (find === -1) {\n              find = line.indexOf('#', readed)\n            }\n\n            if (find > -1 && find < 2) {\n              var foundNewLine = -1\n              foundNewLine = line.indexOf('\\r\\n', readed)\n              if (foundNewLine > 0) {\n                readed = foundNewLine + 2\n              } else {\n                foundNewLine = line.indexOf('\\r', readed)\n                if (foundNewLine > 0) {\n                  readed = foundNewLine + 1\n                } else {\n                  readed = line.indexOf('\\n', readed) + 1\n                }\n              }\n            } else {\n              break\n            }\n          }\n\n          return line.substr(readed)\n        },\n      },\n      {\n        key: '_isBinary',\n        value: function _isBinary(binData) {\n          var reader = new DataView(binData)\n          var face_size = (32 / 8) * 3 + (32 / 8) * 3 * 3 + 16 / 8\n          var n_faces = reader.getUint32(80, true)\n          var expect = 80 + 32 / 8 + n_faces * face_size\n          if (expect === reader.byteLength) {\n            return true\n          }\n\n          var fileLength = reader.byteLength\n          for (let index = 0; index < fileLength; index++) {\n            if (reader.getUint8(index, false) > 127) {\n              return true\n            }\n          }\n\n          return false\n        },\n      },\n      {\n        key: '_ensureBinary',\n        value: function _ensureBinary(buf) {\n          if (typeof buf === 'string') {\n            var array_buffer = new Uint8Array(buf.length)\n            for (let i = 0; i < buf.length; i++) {\n              array_buffer[i] = buf.charCodeAt(i) & 0xff\n            }\n\n            return array_buffer.buffer || array_buffer\n          } else {\n            return buf\n          }\n        },\n      },\n      {\n        key: '_ensureString',\n        value: function _ensureString(buf) {\n          if (typeof buf !== 'string') {\n            return decodeText(new Uint8Array(buf))\n          } else {\n            return buf\n          }\n        },\n      },\n      {\n        key: 'parse',\n        value: function _parse(data, onLoad) {\n          var binData = this._ensureBinary(data)\n          this._data = this._ensureString(data)\n          this.onLoad = onLoad\n          return this._isBinary(binData) ? this._parseBinary(binData) : this._parseASCII()\n        },\n      },\n      {\n        key: '_parseBinary',\n        value: function _parseBinary(data) {\n          return this._parseASCII(decodeText(new Uint8Array(data)))\n        },\n      },\n      {\n        key: '_parseASCII',\n        value: function _parseASCII() {\n          var path\n\n          if (this.resourcePath !== '') {\n            path = this.resourcePath\n          } else if (this.path !== '') {\n            path = this.path\n          } else {\n            path = LoaderUtils.extractUrlBase(this.url)\n          }\n\n          this.texloader.setPath(path).setCrossOrigin(this.crossOrigin)\n\n          var endRead = 16\n          this.Hierarchies.children = []\n          this._hierarchieParse(this.Hierarchies, endRead)\n          this._changeRoot()\n          this._currentObject = this.Hierarchies.children.shift()\n          this._mainloop()\n        },\n      },\n      {\n        key: '_hierarchieParse',\n        value: function _hierarchieParse(_parent, _end) {\n          var endRead = _end\n          while (true) {\n            var find1 = this._data.indexOf('{', endRead) + 1\n            var findEnd = this._data.indexOf('}', endRead)\n            var findNext = this._data.indexOf('{', find1) + 1\n            if (find1 > 0 && findEnd > find1) {\n              var _currentObject = {}\n              _currentObject.children = []\n              var nameData = this._readLine(this._data.substr(endRead, find1 - endRead - 1)).trim()\n              var word = nameData.split(/ /g)\n              if (word.length > 0) {\n                _currentObject.type = word[0]\n                if (word.length >= 2) {\n                  _currentObject.name = word[1]\n                } else {\n                  _currentObject.name = word[0] + this.Hierarchies.children.length\n                }\n              } else {\n                _currentObject.name = nameData\n                _currentObject.type = ''\n              }\n\n              if (_currentObject.type === 'Animation') {\n                _currentObject.data = this._data.substr(findNext, findEnd - findNext).trim()\n                var refs = this._hierarchieParse(_currentObject, findEnd + 1)\n                endRead = refs.end\n                _currentObject.children = refs.parent.children\n              } else {\n                var DataEnder = this._data.lastIndexOf(';', findNext > 0 ? Math.min(findNext, findEnd) : findEnd)\n                _currentObject.data = this._data.substr(find1, DataEnder - find1).trim()\n                if (findNext <= 0 || findEnd < findNext) {\n                  endRead = findEnd + 1\n                } else {\n                  var nextStart = Math.max(DataEnder + 1, find1)\n                  var _refs = this._hierarchieParse(_currentObject, nextStart)\n                  endRead = _refs.end\n                  _currentObject.children = _refs.parent.children\n                }\n              }\n\n              _currentObject.parent = _parent\n              if (_currentObject.type != 'template') {\n                _parent.children.push(_currentObject)\n              }\n            } else {\n              endRead = find1 === -1 ? this._data.length : findEnd + 1\n              break\n            }\n          }\n\n          return {\n            parent: _parent,\n            end: endRead,\n          }\n        },\n      },\n      {\n        key: '_mainloop',\n        value: function _mainloop() {\n          var _this2 = this\n\n          this._mainProc()\n          if (this._currentObject.parent || this._currentObject.children.length > 0 || !this._currentObject.worked) {\n            setTimeout(function () {\n              _this2._mainloop()\n            }, 1)\n          } else {\n            setTimeout(function () {\n              _this2.onLoad({\n                models: _this2.Meshes,\n                animations: _this2.animations,\n              })\n            }, 1)\n          }\n        },\n      },\n      {\n        key: '_mainProc',\n        value: function _mainProc() {\n          var breakFlag = false\n          while (true) {\n            if (!this._currentObject.worked) {\n              switch (this._currentObject.type) {\n                case 'template':\n                  break\n                case 'AnimTicksPerSecond':\n                  this.animTicksPerSecond = parseInt(this._currentObject.data)\n                  break\n                case 'Frame':\n                  this._setFrame()\n                  break\n                case 'FrameTransformMatrix':\n                  this._setFrameTransformMatrix()\n                  break\n                case 'Mesh':\n                  this._changeRoot()\n                  this._currentGeo = {}\n                  this._currentGeo.name = this._currentObject.name.trim()\n                  this._currentGeo.parentName = this._getParentName(this._currentObject).trim()\n                  this._currentGeo.VertexSetedBoneCount = []\n                  this._currentGeo.GeometryData = {\n                    vertices: [],\n                    normals: [],\n                    uvs: [],\n                    skinIndices: [],\n                    skinWeights: [],\n                    indices: [],\n                    materialIndices: [],\n                  }\n                  this._currentGeo.Materials = []\n                  this._currentGeo.normalVectors = []\n                  this._currentGeo.BoneInfs = []\n                  this._currentGeo.baseFrame = this._currentFrame\n                  this._makeBoneFrom_CurrentFrame()\n                  this._readVertexDatas()\n                  breakFlag = true\n                  break\n                case 'MeshNormals':\n                  this._readVertexDatas()\n                  break\n                case 'MeshTextureCoords':\n                  this._setMeshTextureCoords()\n                  break\n                case 'VertexDuplicationIndices':\n                  break\n                case 'MeshMaterialList':\n                  this._setMeshMaterialList()\n                  break\n                case 'Material':\n                  this._setMaterial()\n                  break\n                case 'SkinWeights':\n                  this._setSkinWeights()\n                  break\n                case 'AnimationSet':\n                  this._changeRoot()\n                  this._currentAnime = {}\n                  this._currentAnime.name = this._currentObject.name.trim()\n                  this._currentAnime.AnimeFrames = []\n                  break\n                case 'Animation':\n                  if (this._currentAnimeFrames) {\n                    this._currentAnime.AnimeFrames.push(this._currentAnimeFrames)\n                  }\n\n                  this._currentAnimeFrames = new XAnimationInfo()\n                  this._currentAnimeFrames.boneName = this._currentObject.data.trim()\n                  break\n                case 'AnimationKey':\n                  this._readAnimationKey()\n                  breakFlag = true\n                  break\n              }\n\n              this._currentObject.worked = true\n            }\n\n            if (this._currentObject.children.length > 0) {\n              this._currentObject = this._currentObject.children.shift()\n              if (this.debug) {\n                console.log('processing ' + this._currentObject.name)\n              }\n\n              if (breakFlag) break\n            } else {\n              if (this._currentObject.worked) {\n                if (this._currentObject.parent && !this._currentObject.parent.parent) {\n                  this._changeRoot()\n                }\n              }\n\n              if (this._currentObject.parent) {\n                this._currentObject = this._currentObject.parent\n              } else {\n                breakFlag = true\n              }\n\n              if (breakFlag) break\n            }\n          }\n\n          return\n        },\n      },\n      {\n        key: '_changeRoot',\n        value: function _changeRoot() {\n          if (this._currentGeo != null && this._currentGeo.name) {\n            this._makeOutputGeometry()\n          }\n\n          this._currentGeo = {}\n          if (this._currentAnime != null && this._currentAnime.name) {\n            if (this._currentAnimeFrames) {\n              this._currentAnime.AnimeFrames.push(this._currentAnimeFrames)\n              this._currentAnimeFrames = null\n            }\n\n            this._makeOutputAnimation()\n          }\n\n          this._currentAnime = {}\n        },\n      },\n      {\n        key: '_getParentName',\n        value: function _getParentName(_obj) {\n          if (_obj.parent) {\n            if (_obj.parent.name) {\n              return _obj.parent.name\n            } else {\n              return this._getParentName(_obj.parent)\n            }\n          } else {\n            return ''\n          }\n        },\n      },\n      {\n        key: '_setFrame',\n        value: function _setFrame() {\n          this._nowFrameName = this._currentObject.name.trim()\n          this._currentFrame = {}\n          this._currentFrame.name = this._nowFrameName\n          this._currentFrame.children = []\n          if (this._currentObject.parent && this._currentObject.parent.name) {\n            this._currentFrame.parentName = this._currentObject.parent.name\n          }\n\n          this.frameHierarchie.push(this._nowFrameName)\n          this.HieStack[this._nowFrameName] = this._currentFrame\n        },\n      },\n      {\n        key: '_setFrameTransformMatrix',\n        value: function _setFrameTransformMatrix() {\n          this._currentFrame.FrameTransformMatrix = new Matrix4()\n          var data = this._currentObject.data.split(',')\n          this._ParseMatrixData(this._currentFrame.FrameTransformMatrix, data)\n          this._makeBoneFrom_CurrentFrame()\n        },\n      },\n      {\n        key: '_makeBoneFrom_CurrentFrame',\n        value: function _makeBoneFrom_CurrentFrame() {\n          if (!this._currentFrame.FrameTransformMatrix) {\n            return\n          }\n\n          var b = new Bone()\n          b.name = this._currentFrame.name\n          b.applyMatrix4(this._currentFrame.FrameTransformMatrix)\n          b.matrixWorld = b.matrix\n          b.FrameTransformMatrix = this._currentFrame.FrameTransformMatrix\n          this._currentFrame.putBone = b\n          if (this._currentFrame.parentName) {\n            for (let frame in this.HieStack) {\n              if (this.HieStack[frame].name === this._currentFrame.parentName) {\n                this.HieStack[frame].putBone.add(this._currentFrame.putBone)\n              }\n            }\n          }\n        },\n      },\n      {\n        key: '_readVertexDatas',\n        value: function _readVertexDatas() {\n          var endRead = 0\n          var mode = 0\n          var mode_local = 0\n          var maxLength = 0\n          while (true) {\n            var changeMode = false\n            if (mode_local === 0) {\n              var refO = this._readInt1(endRead)\n              endRead = refO.endRead\n              mode_local = 1\n              maxLength = this._currentObject.data.indexOf(';;', endRead) + 1\n              if (maxLength <= 0) {\n                maxLength = this._currentObject.data.length\n              }\n            } else {\n              var find = 0\n              switch (mode) {\n                case 0:\n                  find = this._currentObject.data.indexOf(',', endRead) + 1\n                  break\n                case 1:\n                  find = this._currentObject.data.indexOf(';,', endRead) + 1\n                  break\n              }\n\n              if (find === 0 || find > maxLength) {\n                find = maxLength\n                mode_local = 0\n                changeMode = true\n              }\n\n              switch (this._currentObject.type) {\n                case 'Mesh':\n                  switch (mode) {\n                    case 0:\n                      this._readVertex1(this._currentObject.data.substr(endRead, find - endRead))\n                      break\n                    case 1:\n                      this._readFace1(this._currentObject.data.substr(endRead, find - endRead))\n                      break\n                  }\n\n                  break\n                case 'MeshNormals':\n                  switch (mode) {\n                    case 0:\n                      this._readNormalVector1(this._currentObject.data.substr(endRead, find - endRead))\n                      break\n                  }\n\n                  break\n              }\n\n              endRead = find + 1\n              if (changeMode) {\n                mode++\n              }\n            }\n\n            if (endRead >= this._currentObject.data.length) {\n              break\n            }\n          }\n        },\n      },\n      {\n        key: '_readInt1',\n        value: function _readInt1(start) {\n          var find = this._currentObject.data.indexOf(';', start)\n          return {\n            refI: parseInt(this._currentObject.data.substr(start, find - start)),\n            endRead: find + 1,\n          }\n        },\n      },\n      {\n        key: '_readVertex1',\n        value: function _readVertex1(line) {\n          var data = this._readLine(line.trim())\n            .substr(0, line.length - 2)\n            .split(';')\n          this._currentGeo.GeometryData.vertices.push(parseFloat(data[0]), parseFloat(data[1]), parseFloat(data[2]))\n          this._currentGeo.GeometryData.skinIndices.push(0, 0, 0, 0)\n          this._currentGeo.GeometryData.skinWeights.push(1, 0, 0, 0)\n          this._currentGeo.VertexSetedBoneCount.push(0)\n        },\n      },\n      {\n        key: '_readFace1',\n        value: function _readFace1(line) {\n          var data = this._readLine(line.trim())\n            .substr(2, line.length - 4)\n            .split(',')\n          this._currentGeo.GeometryData.indices.push(\n            parseInt(data[0], 10),\n            parseInt(data[1], 10),\n            parseInt(data[2], 10),\n          )\n        },\n      },\n      {\n        key: '_readNormalVector1',\n        value: function _readNormalVector1(line) {\n          var data = this._readLine(line.trim())\n            .substr(0, line.length - 2)\n            .split(';')\n          this._currentGeo.GeometryData.normals.push(parseFloat(data[0]), parseFloat(data[1]), parseFloat(data[2]))\n        },\n      },\n      {\n        key: '_buildGeometry',\n        value: function _buildGeometry() {\n          var bufferGeometry = new BufferGeometry()\n          var position = []\n          var normals = []\n          var uvs = []\n          var skinIndices = []\n          var skinWeights = []\n\n          //\n\n          var data = this._currentGeo.GeometryData\n\n          for (let i = 0, l = data.indices.length; i < l; i++) {\n            var stride2 = data.indices[i] * 2\n            var stride3 = data.indices[i] * 3\n            var stride4 = data.indices[i] * 4\n\n            position.push(data.vertices[stride3], data.vertices[stride3 + 1], data.vertices[stride3 + 2])\n            normals.push(data.normals[stride3], data.normals[stride3 + 1], data.normals[stride3 + 2])\n            skinIndices.push(\n              data.skinIndices[stride4],\n              data.skinIndices[stride4 + 1],\n              data.skinIndices[stride4 + 2],\n              data.skinIndices[stride4 + 3],\n            )\n            skinWeights.push(\n              data.skinWeights[stride4],\n              data.skinWeights[stride4 + 1],\n              data.skinWeights[stride4 + 2],\n              data.skinWeights[stride4 + 3],\n            )\n            uvs.push(data.uvs[stride2], data.uvs[stride2 + 1])\n          }\n\n          //\n\n          bufferGeometry.setAttribute('position', new Float32BufferAttribute(position, 3))\n          bufferGeometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n          bufferGeometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n          bufferGeometry.setAttribute('skinIndex', new Uint16BufferAttribute(skinIndices, 4))\n          bufferGeometry.setAttribute('skinWeight', new Float32BufferAttribute(skinWeights, 4))\n\n          this._computeGroups(bufferGeometry, data.materialIndices)\n\n          return bufferGeometry\n        },\n      },\n      {\n        key: '_computeGroups',\n        value: function _computeGroups(bufferGeometry, materialIndices) {\n          var group\n          var groups = []\n          var materialIndex = undefined\n\n          for (let i = 0; i < materialIndices.length; i++) {\n            var currentMaterialIndex = materialIndices[i]\n\n            if (currentMaterialIndex !== materialIndex) {\n              materialIndex = currentMaterialIndex\n\n              if (group !== undefined) {\n                group.count = i * 3 - group.start\n                groups.push(group)\n              }\n\n              group = {\n                start: i * 3,\n                materialIndex: materialIndex,\n              }\n            }\n          }\n\n          if (group !== undefined) {\n            group.count = i * 3 - group.start\n            groups.push(group)\n          }\n\n          bufferGeometry.groups = groups\n        },\n      },\n      {\n        key: '_setMeshTextureCoords',\n        value: function _setMeshTextureCoords() {\n          var endRead = 0\n          var mode = 0\n          var mode_local = 0\n          while (true) {\n            switch (mode) {\n              case 0:\n                if (mode_local === 0) {\n                  var refO = this._readInt1(0)\n                  endRead = refO.endRead\n                  mode_local = 1\n                } else {\n                  var find = this._currentObject.data.indexOf(',', endRead) + 1\n                  if (find === 0) {\n                    find = this._currentObject.data.length\n                    mode = 2\n                    mode_local = 0\n                  }\n\n                  var line = this._currentObject.data.substr(endRead, find - endRead)\n                  var data = this._readLine(line.trim()).split(';')\n                  if (this.IsUvYReverse) {\n                    this._currentGeo.GeometryData.uvs.push(parseFloat(data[0]), 1 - parseFloat(data[1]))\n                  } else {\n                    this._currentGeo.GeometryData.uvs.push(parseFloat(data[0]), parseFloat(data[1]))\n                  }\n\n                  endRead = find + 1\n                }\n\n                break\n            }\n\n            if (endRead >= this._currentObject.data.length) {\n              break\n            }\n          }\n        },\n      },\n      {\n        key: '_setMeshMaterialList',\n        value: function _setMeshMaterialList() {\n          var endRead = 0\n          var mode = 0\n          var mode_local = 0\n          while (true) {\n            if (mode_local < 2) {\n              var refO = this._readInt1(endRead)\n              endRead = refO.endRead\n              mode_local++\n            } else {\n              var find = this._currentObject.data.indexOf(';', endRead)\n              if (find === -1) {\n                find = this._currentObject.data.length\n                mode = 3\n                mode_local = 0\n              }\n\n              var line = this._currentObject.data.substr(endRead, find - endRead)\n              var data = this._readLine(line.trim()).split(',')\n              for (let i = 0; i < data.length; i++) {\n                this._currentGeo.GeometryData.materialIndices[i] = parseInt(data[i])\n              }\n\n              endRead = this._currentObject.data.length\n            }\n\n            if (endRead >= this._currentObject.data.length || mode >= 3) {\n              break\n            }\n          }\n        },\n      },\n      {\n        key: '_setMaterial',\n        value: function _setMaterial() {\n          var _nowMat = new MeshPhongMaterial({\n            color: Math.random() * 0xffffff,\n          })\n          _nowMat.side = FrontSide\n          _nowMat.name = this._currentObject.name\n          var endRead = 0\n          var find = this._currentObject.data.indexOf(';;', endRead)\n          var line = this._currentObject.data.substr(endRead, find - endRead)\n          var data = this._readLine(line.trim()).split(';')\n          _nowMat.color.r = parseFloat(data[0])\n          _nowMat.color.g = parseFloat(data[1])\n          _nowMat.color.b = parseFloat(data[2])\n          endRead = find + 2\n          find = this._currentObject.data.indexOf(';', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          _nowMat.shininess = parseFloat(this._readLine(line))\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';;', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data2 = this._readLine(line.trim()).split(';')\n          _nowMat.specular.r = parseFloat(data2[0])\n          _nowMat.specular.g = parseFloat(data2[1])\n          _nowMat.specular.b = parseFloat(data2[2])\n          endRead = find + 2\n          find = this._currentObject.data.indexOf(';;', endRead)\n          if (find === -1) {\n            find = this._currentObject.data.length\n          }\n\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data3 = this._readLine(line.trim()).split(';')\n          _nowMat.emissive.r = parseFloat(data3[0])\n          _nowMat.emissive.g = parseFloat(data3[1])\n          _nowMat.emissive.b = parseFloat(data3[2])\n          var localObject = null\n          while (true) {\n            if (this._currentObject.children.length > 0) {\n              localObject = this._currentObject.children.shift()\n              if (this.debug) {\n                console.log('processing ' + localObject.name)\n              }\n\n              var fileName = localObject.data.substr(1, localObject.data.length - 2)\n              switch (localObject.type) {\n                case 'TextureFilename':\n                  _nowMat.map = this.texloader.load(fileName)\n                  break\n                case 'BumpMapFilename':\n                  _nowMat.bumpMap = this.texloader.load(fileName)\n                  _nowMat.bumpScale = 0.05\n                  break\n                case 'NormalMapFilename':\n                  _nowMat.normalMap = this.texloader.load(fileName)\n                  _nowMat.normalScale = new Vector2(2, 2)\n                  break\n                case 'EmissiveMapFilename':\n                  _nowMat.emissiveMap = this.texloader.load(fileName)\n                  break\n                case 'LightMapFilename':\n                  _nowMat.lightMap = this.texloader.load(fileName)\n                  break\n              }\n            } else {\n              break\n            }\n          }\n\n          this._currentGeo.Materials.push(_nowMat)\n        },\n      },\n      {\n        key: '_setSkinWeights',\n        value: function _setSkinWeights() {\n          var boneInf = new XboneInf()\n          var endRead = 0\n          var find = this._currentObject.data.indexOf(';', endRead)\n          var line = this._currentObject.data.substr(endRead, find - endRead)\n          endRead = find + 1\n          boneInf.boneName = line.substr(1, line.length - 2)\n          boneInf.BoneIndex = this._currentGeo.BoneInfs.length\n          find = this._currentObject.data.indexOf(';', endRead)\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data = this._readLine(line.trim()).split(',')\n          for (let i = 0; i < data.length; i++) {\n            boneInf.Indeces.push(parseInt(data[i]))\n          }\n\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';', endRead)\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data2 = this._readLine(line.trim()).split(',')\n          for (let _i = 0; _i < data2.length; _i++) {\n            boneInf.Weights.push(parseFloat(data2[_i]))\n          }\n\n          endRead = find + 1\n          find = this._currentObject.data.indexOf(';', endRead)\n          if (find <= 0) {\n            find = this._currentObject.data.length\n          }\n\n          line = this._currentObject.data.substr(endRead, find - endRead)\n          var data3 = this._readLine(line.trim()).split(',')\n          boneInf.OffsetMatrix = new Matrix4()\n          this._ParseMatrixData(boneInf.OffsetMatrix, data3)\n          this._currentGeo.BoneInfs.push(boneInf)\n        },\n      },\n      {\n        key: '_makePutBoneList',\n        value: function _makePutBoneList(_RootName, _bones) {\n          var putting = false\n          for (let frame in this.HieStack) {\n            if (this.HieStack[frame].name === _RootName || putting) {\n              putting = true\n              var b = new Bone()\n              b.name = this.HieStack[frame].name\n              b.applyMatrix4(this.HieStack[frame].FrameTransformMatrix)\n              b.matrixWorld = b.matrix\n              b.FrameTransformMatrix = this.HieStack[frame].FrameTransformMatrix\n              b.pos = new Vector3().setFromMatrixPosition(b.FrameTransformMatrix).toArray()\n              b.rotq = new Quaternion().setFromRotationMatrix(b.FrameTransformMatrix).toArray()\n              b.scl = new Vector3().setFromMatrixScale(b.FrameTransformMatrix).toArray()\n              if (this.HieStack[frame].parentName && this.HieStack[frame].parentName.length > 0) {\n                for (let i = 0; i < _bones.length; i++) {\n                  if (this.HieStack[frame].parentName === _bones[i].name) {\n                    _bones[i].add(b)\n                    b.parent = i\n                    break\n                  }\n                }\n              }\n\n              _bones.push(b)\n            }\n          }\n        },\n      },\n      {\n        key: '_makeOutputGeometry',\n        value: function _makeOutputGeometry() {\n          var mesh = null\n          if (this._currentGeo.BoneInfs.length > 0) {\n            var putBones = []\n            this._makePutBoneList(this._currentGeo.baseFrame.parentName, putBones)\n            for (let bi = 0; bi < this._currentGeo.BoneInfs.length; bi++) {\n              var boneIndex = 0\n              for (let bb = 0; bb < putBones.length; bb++) {\n                if (putBones[bb].name === this._currentGeo.BoneInfs[bi].boneName) {\n                  boneIndex = bb\n                  putBones[bb].OffsetMatrix = new Matrix4()\n                  putBones[bb].OffsetMatrix.copy(this._currentGeo.BoneInfs[bi].OffsetMatrix)\n                  break\n                }\n              }\n\n              for (let vi = 0; vi < this._currentGeo.BoneInfs[bi].Indeces.length; vi++) {\n                var nowVertexID = this._currentGeo.BoneInfs[bi].Indeces[vi]\n                var nowVal = this._currentGeo.BoneInfs[bi].Weights[vi]\n\n                var stride = nowVertexID * 4\n\n                switch (this._currentGeo.VertexSetedBoneCount[nowVertexID]) {\n                  case 0:\n                    this._currentGeo.GeometryData.skinIndices[stride] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride] = nowVal\n                    break\n                  case 1:\n                    this._currentGeo.GeometryData.skinIndices[stride + 1] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride + 1] = nowVal\n                    break\n                  case 2:\n                    this._currentGeo.GeometryData.skinIndices[stride + 2] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride + 2] = nowVal\n                    break\n                  case 3:\n                    this._currentGeo.GeometryData.skinIndices[stride + 3] = boneIndex\n                    this._currentGeo.GeometryData.skinWeights[stride + 3] = nowVal\n                    break\n                }\n\n                this._currentGeo.VertexSetedBoneCount[nowVertexID]++\n                if (this._currentGeo.VertexSetedBoneCount[nowVertexID] > 4) {\n                  console.log('warn! over 4 bone weight! :' + nowVertexID)\n                }\n              }\n            }\n\n            for (let sk = 0; sk < this._currentGeo.Materials.length; sk++) {\n              this._currentGeo.Materials[sk].skinning = true\n            }\n\n            var offsetList = []\n            for (let _bi = 0; _bi < putBones.length; _bi++) {\n              if (putBones[_bi].OffsetMatrix) {\n                offsetList.push(putBones[_bi].OffsetMatrix)\n              } else {\n                offsetList.push(new Matrix4())\n              }\n            }\n\n            var bufferGeometry = this._buildGeometry()\n            mesh = new SkinnedMesh(\n              bufferGeometry,\n              this._currentGeo.Materials.length === 1 ? this._currentGeo.Materials[0] : this._currentGeo.Materials,\n            )\n\n            this._initSkeleton(mesh, putBones, offsetList)\n          } else {\n            var _bufferGeometry = this._buildGeometry()\n            mesh = new Mesh(\n              _bufferGeometry,\n              this._currentGeo.Materials.length === 1 ? this._currentGeo.Materials[0] : this._currentGeo.Materials,\n            )\n          }\n\n          mesh.name = this._currentGeo.name\n          var worldBaseMx = new Matrix4()\n          var currentMxFrame = this._currentGeo.baseFrame.putBone\n          if (currentMxFrame && currentMxFrame.parent) {\n            while (true) {\n              currentMxFrame = currentMxFrame.parent\n              if (currentMxFrame) {\n                worldBaseMx.multiply(currentMxFrame.FrameTransformMatrix)\n              } else {\n                break\n              }\n            }\n\n            mesh.applyMatrix4(worldBaseMx)\n          }\n\n          this.Meshes.push(mesh)\n        },\n      },\n      {\n        key: '_initSkeleton',\n        value: function _initSkeleton(mesh, boneList, boneInverses) {\n          var bones = [],\n            bone,\n            gbone\n          var i, il\n\n          for (i = 0, il = boneList.length; i < il; i++) {\n            gbone = boneList[i]\n\n            bone = new Bone()\n            bones.push(bone)\n\n            bone.name = gbone.name\n            bone.position.fromArray(gbone.pos)\n            bone.quaternion.fromArray(gbone.rotq)\n            if (gbone.scl !== undefined) bone.scale.fromArray(gbone.scl)\n          }\n\n          for (i = 0, il = boneList.length; i < il; i++) {\n            gbone = boneList[i]\n\n            if (gbone.parent !== -1 && gbone.parent !== null && bones[gbone.parent] !== undefined) {\n              bones[gbone.parent].add(bones[i])\n            } else {\n              mesh.add(bones[i])\n            }\n          }\n\n          mesh.updateMatrixWorld(true)\n\n          var skeleton = new Skeleton(bones, boneInverses)\n          mesh.bind(skeleton, mesh.matrixWorld)\n        },\n      },\n      {\n        key: '_readAnimationKey',\n        value: function _readAnimationKey() {\n          var endRead = 0\n          var find = this._currentObject.data.indexOf(';', endRead)\n          var line = this._currentObject.data.substr(endRead, find - endRead)\n          endRead = find + 1\n          var nowKeyType = parseInt(this._readLine(line))\n          find = this._currentObject.data.indexOf(';', endRead)\n          endRead = find + 1\n          line = this._currentObject.data.substr(endRead)\n          var data = this._readLine(line.trim()).split(';;,')\n          for (let i = 0; i < data.length; i++) {\n            var data2 = data[i].split(';')\n            var keyInfo = new XKeyFrameInfo()\n            keyInfo.type = nowKeyType\n            keyInfo.Frame = parseInt(data2[0])\n            keyInfo.index = this._currentAnimeFrames.keyFrames.length\n            keyInfo.time = keyInfo.Frame\n            if (nowKeyType != 4) {\n              var frameFound = false\n              for (let mm = 0; mm < this._currentAnimeFrames.keyFrames.length; mm++) {\n                if (this._currentAnimeFrames.keyFrames[mm].Frame === keyInfo.Frame) {\n                  keyInfo = this._currentAnimeFrames.keyFrames[mm]\n                  frameFound = true\n                  break\n                }\n              }\n\n              var frameValue = data2[2].split(',')\n              switch (nowKeyType) {\n                case 0:\n                  keyInfo.rot = new Quaternion(\n                    parseFloat(frameValue[1]),\n                    parseFloat(frameValue[2]),\n                    parseFloat(frameValue[3]),\n                    parseFloat(frameValue[0]) * -1,\n                  )\n                  break\n                case 1:\n                  keyInfo.scl = new Vector3(\n                    parseFloat(frameValue[0]),\n                    parseFloat(frameValue[1]),\n                    parseFloat(frameValue[2]),\n                  )\n                  break\n                case 2:\n                  keyInfo.pos = new Vector3(\n                    parseFloat(frameValue[0]),\n                    parseFloat(frameValue[1]),\n                    parseFloat(frameValue[2]),\n                  )\n                  break\n              }\n\n              if (!frameFound) {\n                this._currentAnimeFrames.keyFrames.push(keyInfo)\n              }\n            } else {\n              keyInfo.matrix = new Matrix4()\n              this._ParseMatrixData(keyInfo.matrix, data2[2].split(','))\n              this._currentAnimeFrames.keyFrames.push(keyInfo)\n            }\n          }\n        },\n      },\n      {\n        key: '_makeOutputAnimation',\n        value: function _makeOutputAnimation() {\n          var animationObj = new XAnimationObj(this.options)\n          animationObj.fps = this.animTicksPerSecond\n          animationObj.name = this._currentAnime.name\n          animationObj.make(this._currentAnime.AnimeFrames)\n          this.animations.push(animationObj)\n        },\n      },\n      {\n        key: 'assignAnimation',\n        value: function assignAnimation(_model, _animation) {\n          var model = _model\n          var animation = _animation\n          if (!model) {\n            model = this.Meshes[0]\n          }\n\n          if (!animation) {\n            animation = this.animations[0]\n          }\n\n          if (!model || !animation) {\n            return null\n          }\n\n          var put = {}\n          put.fps = animation.fps\n          put.name = animation.name\n          put.length = animation.length\n          put.hierarchy = []\n          for (let b = 0; b < model.skeleton.bones.length; b++) {\n            var findAnimation = false\n            for (let i = 0; i < animation.hierarchy.length; i++) {\n              if (model.skeleton.bones[b].name === animation.hierarchy[i].name) {\n                findAnimation = true\n                var c_key = animation.hierarchy[i].copy()\n                c_key.parent = -1\n                if (model.skeleton.bones[b].parent && model.skeleton.bones[b].parent.type === 'Bone') {\n                  for (let bb = 0; bb < put.hierarchy.length; bb++) {\n                    if (put.hierarchy[bb].name === model.skeleton.bones[b].parent.name) {\n                      c_key.parent = bb\n                      c_key.parentName = model.skeleton.bones[b].parent.name\n                    }\n                  }\n                }\n\n                put.hierarchy.push(c_key)\n                break\n              }\n            }\n\n            if (!findAnimation) {\n              var _c_key = animation.hierarchy[0].copy()\n              _c_key.name = model.skeleton.bones[b].name\n              _c_key.parent = -1\n              for (let k = 0; k < _c_key.keys.length; k++) {\n                if (_c_key.keys[k].pos) {\n                  _c_key.keys[k].pos.set(0, 0, 0)\n                }\n\n                if (_c_key.keys[k].scl) {\n                  _c_key.keys[k].scl.set(1, 1, 1)\n                }\n\n                if (_c_key.keys[k].rot) {\n                  _c_key.keys[k].rot.set(0, 0, 0, 1)\n                }\n              }\n\n              put.hierarchy.push(_c_key)\n            }\n          }\n\n          if (!model.geometry.animations) {\n            model.geometry.animations = []\n          }\n\n          model.geometry.animations.push(AnimationClip.parseAnimation(put, model.skeleton.bones))\n          if (!model.animationMixer) {\n            model.animationMixer = new AnimationMixer(model)\n          }\n\n          return put\n        },\n      },\n      {\n        key: '_ParseMatrixData',\n        value: function _ParseMatrixData(targetMatrix, data) {\n          targetMatrix.set(\n            parseFloat(data[0]),\n            parseFloat(data[4]),\n            parseFloat(data[8]),\n            parseFloat(data[12]),\n            parseFloat(data[1]),\n            parseFloat(data[5]),\n            parseFloat(data[9]),\n            parseFloat(data[13]),\n            parseFloat(data[2]),\n            parseFloat(data[6]),\n            parseFloat(data[10]),\n            parseFloat(data[14]),\n            parseFloat(data[3]),\n            parseFloat(data[7]),\n            parseFloat(data[11]),\n            parseFloat(data[15]),\n          )\n        },\n      },\n    ])\n    return XLoader\n  })()\n\n  return XLoader\n})()\n\nexport { XLoader }\n"], "mappings": ";;AAuBG,IAACA,OAAA,GAA2B,2BAAY;EACzC,IAAIC,cAAA,GAAiB,SAAAA,CAAUC,QAAA,EAAUC,WAAA,EAAa;IACpD,IAAI,EAAED,QAAA,YAAoBC,WAAA,GAAc;MACtC,MAAM,IAAIC,SAAA,CAAU,mCAAmC;IACxD;EACF;EAED,IAAIC,WAAA,GAAe,YAAY;IAC7B,SAASC,iBAAiBC,MAAA,EAAQC,KAAA,EAAO;MACvC,SAASC,EAAA,GAAI,GAAGA,EAAA,GAAID,KAAA,CAAME,MAAA,EAAQD,EAAA,IAAK;QACrC,IAAIE,UAAA,GAAaH,KAAA,CAAMC,EAAC;QACxBE,UAAA,CAAWC,UAAA,GAAaD,UAAA,CAAWC,UAAA,IAAc;QACjDD,UAAA,CAAWE,YAAA,GAAe;QAC1B,IAAI,WAAWF,UAAA,EAAYA,UAAA,CAAWG,QAAA,GAAW;QACjDC,MAAA,CAAOC,cAAA,CAAeT,MAAA,EAAQI,UAAA,CAAWM,GAAA,EAAKN,UAAU;MACzD;IACF;IAED,OAAO,UAAUR,WAAA,EAAae,UAAA,EAAYC,WAAA,EAAa;MACrD,IAAID,UAAA,EAAYZ,gBAAA,CAAiBH,WAAA,CAAYiB,SAAA,EAAWF,UAAU;MAClE,IAAIC,WAAA,EAAab,gBAAA,CAAiBH,WAAA,EAAagB,WAAW;MAC1D,OAAOhB,WAAA;IACR;EACL,EAAM;EAEJ,IAAIkB,QAAA,GAAW,SAASC,UAAA,EAAW;IACjCrB,cAAA,CAAe,MAAMqB,SAAQ;IAE7B,KAAKC,QAAA,GAAW;IAChB,KAAKC,SAAA,GAAY;IACjB,KAAKC,OAAA,GAAU,EAAE;IACjB,KAAKC,OAAA,GAAU,EAAE;IACjB,KAAKC,UAAA,GAAa;IAClB,KAAKC,YAAA,GAAe;EACrB;EAED,IAAIC,cAAA,GAAiB,SAASC,gBAAA,EAAiB;IAC7C7B,cAAA,CAAe,MAAM6B,eAAc;IAEnC,KAAKC,SAAA,GAAY;IACjB,KAAKR,QAAA,GAAW;IAChB,KAAKS,UAAA,GAAa;IAClB,KAAKC,OAAA,GAAU;IACf,KAAKC,YAAA,GAAe;IACpB,KAAKC,SAAA,GAAY,EAAE;IACnB,KAAKC,SAAA,GAAY;EAClB;EAED,IAAIC,aAAA,GAAiB,YAAY;IAC/B,SAASC,eAAcC,MAAA,EAAQ;MAC7BtC,cAAA,CAAe,MAAMqC,cAAa;MAElC,KAAKE,GAAA,GAAM;MACX,KAAKC,IAAA,GAAO;MACZ,KAAK/B,MAAA,GAAS;MACd,KAAKgC,SAAA,GAAY,EAAE;MACnB,KAAKC,QAAA,GAAWJ,MAAA;MAChB,IAAI,KAAKI,QAAA,CAASC,MAAA,KAAW,QAAW;QACtC,KAAKD,QAAA,CAASC,MAAA,GAAS;MACxB;MAED,IAAI,KAAKD,QAAA,CAASE,MAAA,KAAW,QAAW;QACtC,KAAKF,QAAA,CAASE,MAAA,GAAS;MACxB;MAED,IAAI,KAAKF,QAAA,CAASG,MAAA,KAAW,QAAW;QACtC,KAAKH,QAAA,CAASG,MAAA,GAAS;MACxB;IACF;IAEDzC,WAAA,CAAYiC,cAAA,EAAe,CACzB;MACErB,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASC,KAAKC,mBAAA,EAAqB;QACxC,SAASxC,EAAA,GAAI,GAAGA,EAAA,GAAIwC,mBAAA,CAAoBvC,MAAA,EAAQD,EAAA,IAAK;UACnD,KAAKiC,SAAA,CAAUQ,IAAA,CAAK,KAAKC,YAAA,CAAaF,mBAAA,CAAoBxC,EAAC,CAAC,CAAC;QAC9D;QAED,KAAKC,MAAA,GAAS,KAAKgC,SAAA,CAAU,CAAC,EAAEU,IAAA,CAAK,KAAKV,SAAA,CAAU,CAAC,EAAEU,IAAA,CAAK1C,MAAA,GAAS,CAAC,EAAE2C,IAAA;MACzE;IACF,GACD;MACEpC,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASO,MAAA,EAAQ;QACtB,OAAOvC,MAAA,CAAOwC,MAAA,CAAO,CAAE,GAAE,IAAI;MAC9B;IACF,GACD;MACEtC,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASI,aAAarB,eAAA,EAAgB;QAC3C,IAAI0B,MAAA,GAAS,CAAE;QACfA,MAAA,CAAOf,IAAA,GAAOX,eAAA,CAAeP,QAAA;QAC7BiC,MAAA,CAAOC,MAAA,GAAS;QAChBD,MAAA,CAAOJ,IAAA,GAAO,KAAKM,gBAAA,CAAiB5B,eAAc;QAClD0B,MAAA,CAAOG,IAAA,GAAO,YAAY;UACxB,OAAO5C,MAAA,CAAOwC,MAAA,CAAO,CAAE,GAAE,IAAI;QAC9B;QAED,OAAOC,MAAA;MACR;IACF,GACD;MACEvC,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASW,iBAAiB5B,eAAA,EAAgB;QAC/C,IAAIsB,IAAA,GAAO,EAAE;QACb,SAAS3C,EAAA,GAAI,GAAGA,EAAA,GAAIqB,eAAA,CAAeK,SAAA,CAAUzB,MAAA,EAAQD,EAAA,IAAK;UACxD,IAAImD,QAAA,GAAW,CAAE;UACjBA,QAAA,CAASP,IAAA,GAAOvB,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAE4C,IAAA,GAAO,KAAKb,GAAA;UACxD,IAAIV,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEoD,GAAA,IAAO,KAAKlB,QAAA,CAASC,MAAA,EAAQ;YAC3DgB,QAAA,CAASC,GAAA,GAAM/B,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEoD,GAAA;UAC5C;UAED,IAAI/B,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEqD,GAAA,IAAO,KAAKnB,QAAA,CAASE,MAAA,EAAQ;YAC3De,QAAA,CAASE,GAAA,GAAMhC,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEqD,GAAA;UAC5C;UAED,IAAIhC,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEsD,GAAA,IAAO,KAAKpB,QAAA,CAASG,MAAA,EAAQ;YAC3Dc,QAAA,CAASG,GAAA,GAAMjC,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEsD,GAAA;UAC5C;UAED,IAAIjC,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEuD,MAAA,EAAQ;YACtCJ,QAAA,CAASI,MAAA,GAASlC,eAAA,CAAeK,SAAA,CAAU1B,EAAC,EAAEuD,MAAA;YAC9C,IAAI,KAAKrB,QAAA,CAASC,MAAA,EAAQ;cACxBgB,QAAA,CAASC,GAAA,GAAM,IAAII,OAAA,CAAS,EAACC,qBAAA,CAAsBN,QAAA,CAASI,MAAM;YACnE;YAED,IAAI,KAAKrB,QAAA,CAASE,MAAA,EAAQ;cACxBe,QAAA,CAASE,GAAA,GAAM,IAAIK,UAAA,CAAY,EAACC,qBAAA,CAAsBR,QAAA,CAASI,MAAM;YACtE;YAED,IAAI,KAAKrB,QAAA,CAASG,MAAA,EAAQ;cACxBc,QAAA,CAASG,GAAA,GAAM,IAAIE,OAAA,CAAS,EAACI,kBAAA,CAAmBT,QAAA,CAASI,MAAM;YAChE;UACF;UAEDZ,IAAA,CAAKF,IAAA,CAAKU,QAAQ;QACnB;QAED,OAAOR,IAAA;MACR;IACF,EACF;IACD,OAAOd,cAAA;EACX,EAAM;EAEJ,IAAIgC,aAAA,GAAgB,SAASC,eAAA,EAAgB;IAC3CtE,cAAA,CAAe,MAAMsE,cAAa;IAElC,KAAKC,KAAA,GAAQ;IACb,KAAKC,KAAA,GAAQ;IACb,KAAKpB,IAAA,GAAO;IACZ,KAAKW,MAAA,GAAS;EACf;EAED,IAAIU,QAAA,GAAW,YAAY;IACzB,SAASC,SAAQC,OAAA,EAAS;MACxBC,MAAA,CAAOC,IAAA,CAAK,MAAMF,OAAO;MAEzB3E,cAAA,CAAe,MAAM0E,QAAO;MAE5B,KAAKI,KAAA,GAAQ;MACb,KAAKC,SAAA,GAAY,IAAIC,aAAA,CAAc,KAAKL,OAAO;MAC/C,KAAKM,GAAA,GAAM;MACX,KAAKC,aAAA,GAAgB;MACrB,KAAKC,OAAA,GAAU;MACf,KAAKC,aAAA,GAAgB;MACrB,KAAKC,eAAA,GAAkB,EAAE;MACzB,KAAKC,WAAA,GAAc,CAAE;MACrB,KAAKC,QAAA,GAAW,EAAE;MAClB,KAAKC,cAAA,GAAiB,CAAE;MACxB,KAAKC,aAAA,GAAgB,CAAE;MACvB,KAAKC,KAAA,GAAQ;MACb,KAAKC,MAAA,GAAS;MACd,KAAKC,YAAA,GAAe;MACpB,KAAKC,MAAA,GAAS,EAAE;MAChB,KAAKC,UAAA,GAAa,EAAE;MACpB,KAAKC,kBAAA,GAAqB;MAC1B,KAAKC,WAAA,GAAc;MACnB,KAAKC,aAAA,GAAgB;MACrB,KAAKC,mBAAA,GAAsB;IAC5B;IAED9F,WAAA,CAAYsE,QAAA,EAAS,CACnB;MACE1D,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASqD,cAAcC,IAAA,EAAM;QAClC,IAAIC,MAAA,GAASC,SAAA,CAAU7F,MAAA,GAAS,KAAK6F,SAAA,CAAU,CAAC,MAAM,SAAYA,SAAA,CAAU,CAAC,IAAI;QAEjF,IAAI,CAACF,IAAA,EAAM;UACT;QACD;QAED,SAAS5F,EAAA,GAAI6F,MAAA,EAAQ7F,EAAA,GAAI4F,IAAA,CAAK3F,MAAA,EAAQD,EAAA,IAAK;UACzC,QAAQA,EAAA;YACN,KAAK;cACH,KAAKyE,GAAA,GAAMmB,IAAA,CAAK5F,EAAC;cACjB;YACF,KAAK;cACH,KAAK+F,OAAA,GAAUH,IAAA,CAAK5F,EAAC;cACrB;UACH;QACF;QAED,IAAI,KAAK+F,OAAA,KAAY,QAAW;UAC9B,KAAKA,OAAA,GAAU,CAAE;QAClB;MACF;IACF,GACD;MACEvF,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS0D,KAAKJ,IAAA,EAAMT,MAAA,EAAQc,UAAA,EAAYC,OAAA,EAAS;QACtD,IAAIC,KAAA,GAAQ;QAEZ,KAAKR,aAAA,CAAcC,IAAI;QACvB,IAAIQ,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKlC,OAAO;QACxCiC,MAAA,CAAOE,OAAA,CAAQ,KAAKC,IAAI;QACxBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;QACpCJ,MAAA,CAAOK,gBAAA,CAAiB,KAAKC,aAAa;QAC1CN,MAAA,CAAOO,kBAAA,CAAmB,KAAKC,eAAe;QAC9CR,MAAA,CAAOJ,IAAA,CACL,KAAKvB,GAAA,EACL,UAAUoC,QAAA,EAAU;UAClB,IAAI;YACFV,KAAA,CAAMW,KAAA,CAAMD,QAAA,EAAU1B,MAAM;UAC7B,SAAQ4B,CAAA,EAAP;YACA,IAAIb,OAAA,EAAS;cACXA,OAAA,CAAQa,CAAC;YAC3B,OAAuB;cACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;YAChB;YAEDZ,KAAA,CAAMhC,OAAA,CAAQ+C,SAAA,CAAUf,KAAA,CAAM1B,GAAG;UAClC;QACF,GACDwB,UAAA,EACAC,OACD;MACF;IACF,GACD;MACE1F,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS6E,UAAUC,IAAA,EAAM;QAC9B,IAAIC,MAAA,GAAS;QACb,OAAO,MAAM;UACX,IAAIC,IAAA,GAAO;UACXA,IAAA,GAAOF,IAAA,CAAKG,OAAA,CAAQ,MAAMF,MAAM;UAChC,IAAIC,IAAA,KAAS,IAAI;YACfA,IAAA,GAAOF,IAAA,CAAKG,OAAA,CAAQ,KAAKF,MAAM;UAChC;UAED,IAAIC,IAAA,GAAO,MAAMA,IAAA,GAAO,GAAG;YACzB,IAAIE,YAAA,GAAe;YACnBA,YAAA,GAAeJ,IAAA,CAAKG,OAAA,CAAQ,QAAQF,MAAM;YAC1C,IAAIG,YAAA,GAAe,GAAG;cACpBH,MAAA,GAASG,YAAA,GAAe;YACxC,OAAqB;cACLA,YAAA,GAAeJ,IAAA,CAAKG,OAAA,CAAQ,MAAMF,MAAM;cACxC,IAAIG,YAAA,GAAe,GAAG;gBACpBH,MAAA,GAASG,YAAA,GAAe;cAC1C,OAAuB;gBACLH,MAAA,GAASD,IAAA,CAAKG,OAAA,CAAQ,MAAMF,MAAM,IAAI;cACvC;YACF;UACf,OAAmB;YACL;UACD;QACF;QAED,OAAOD,IAAA,CAAKK,MAAA,CAAOJ,MAAM;MAC1B;IACF,GACD;MACE7G,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS6E,UAAUC,IAAA,EAAM;QAC9B,IAAIC,MAAA,GAAS;QACb,OAAO,MAAM;UACX,IAAIC,IAAA,GAAO;UACXA,IAAA,GAAOF,IAAA,CAAKG,OAAA,CAAQ,MAAMF,MAAM;UAChC,IAAIC,IAAA,KAAS,IAAI;YACfA,IAAA,GAAOF,IAAA,CAAKG,OAAA,CAAQ,KAAKF,MAAM;UAChC;UAED,IAAIC,IAAA,GAAO,MAAMA,IAAA,GAAO,GAAG;YACzB,IAAIE,YAAA,GAAe;YACnBA,YAAA,GAAeJ,IAAA,CAAKG,OAAA,CAAQ,QAAQF,MAAM;YAC1C,IAAIG,YAAA,GAAe,GAAG;cACpBH,MAAA,GAASG,YAAA,GAAe;YACxC,OAAqB;cACLA,YAAA,GAAeJ,IAAA,CAAKG,OAAA,CAAQ,MAAMF,MAAM;cACxC,IAAIG,YAAA,GAAe,GAAG;gBACpBH,MAAA,GAASG,YAAA,GAAe;cAC1C,OAAuB;gBACLH,MAAA,GAASD,IAAA,CAAKG,OAAA,CAAQ,MAAMF,MAAM,IAAI;cACvC;YACF;UACf,OAAmB;YACL;UACD;QACF;QAED,OAAOD,IAAA,CAAKK,MAAA,CAAOJ,MAAM;MAC1B;IACF,GACD;MACE7G,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASoF,UAAUC,OAAA,EAAS;QACjC,IAAIC,MAAA,GAAS,IAAIC,QAAA,CAASF,OAAO;QACjC,IAAIG,SAAA,GAAa,KAAK,IAAK,IAAK,KAAK,IAAK,IAAI,IAAI,KAAK;QACvD,IAAIC,OAAA,GAAUH,MAAA,CAAOI,SAAA,CAAU,IAAI,IAAI;QACvC,IAAIC,MAAA,GAAS,KAAK,KAAK,IAAIF,OAAA,GAAUD,SAAA;QACrC,IAAIG,MAAA,KAAWL,MAAA,CAAOM,UAAA,EAAY;UAChC,OAAO;QACR;QAED,IAAIC,UAAA,GAAaP,MAAA,CAAOM,UAAA;QACxB,SAASnE,KAAA,GAAQ,GAAGA,KAAA,GAAQoE,UAAA,EAAYpE,KAAA,IAAS;UAC/C,IAAI6D,MAAA,CAAOQ,QAAA,CAASrE,KAAA,EAAO,KAAK,IAAI,KAAK;YACvC,OAAO;UACR;QACF;QAED,OAAO;MACR;IACF,GACD;MACEvD,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS+F,cAAcC,GAAA,EAAK;QACjC,IAAI,OAAOA,GAAA,KAAQ,UAAU;UAC3B,IAAIC,YAAA,GAAe,IAAIC,UAAA,CAAWF,GAAA,CAAIrI,MAAM;UAC5C,SAASD,EAAA,GAAI,GAAGA,EAAA,GAAIsI,GAAA,CAAIrI,MAAA,EAAQD,EAAA,IAAK;YACnCuI,YAAA,CAAavI,EAAC,IAAIsI,GAAA,CAAIG,UAAA,CAAWzI,EAAC,IAAI;UACvC;UAED,OAAOuI,YAAA,CAAaG,MAAA,IAAUH,YAAA;QAC1C,OAAiB;UACL,OAAOD,GAAA;QACR;MACF;IACF,GACD;MACE9H,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASqG,cAAcL,GAAA,EAAK;QACjC,IAAI,OAAOA,GAAA,KAAQ,UAAU;UAC3B,OAAOM,UAAA,CAAW,IAAIJ,UAAA,CAAWF,GAAG,CAAC;QACjD,OAAiB;UACL,OAAOA,GAAA;QACR;MACF;IACF,GACD;MACE9H,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASuG,OAAOC,IAAA,EAAM3D,MAAA,EAAQ;QACnC,IAAIwC,OAAA,GAAU,KAAKU,aAAA,CAAcS,IAAI;QACrC,KAAK5D,KAAA,GAAQ,KAAKyD,aAAA,CAAcG,IAAI;QACpC,KAAK3D,MAAA,GAASA,MAAA;QACd,OAAO,KAAKuC,SAAA,CAAUC,OAAO,IAAI,KAAKoB,YAAA,CAAapB,OAAO,IAAI,KAAKqB,WAAA,CAAa;MACjF;IACF,GACD;MACExI,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASyG,aAAaD,IAAA,EAAM;QACjC,OAAO,KAAKE,WAAA,CAAYJ,UAAA,CAAW,IAAIJ,UAAA,CAAWM,IAAI,CAAC,CAAC;MACzD;IACF,GACD;MACEtI,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS0G,YAAA,EAAc;QAC5B,IAAIzC,IAAA;QAEJ,IAAI,KAAK0C,YAAA,KAAiB,IAAI;UAC5B1C,IAAA,GAAO,KAAK0C,YAAA;QACxB,WAAqB,KAAK1C,IAAA,KAAS,IAAI;UAC3BA,IAAA,GAAO,KAAKA,IAAA;QACxB,OAAiB;UACLA,IAAA,GAAO2C,WAAA,CAAYC,cAAA,CAAe,KAAK1E,GAAG;QAC3C;QAED,KAAKF,SAAA,CAAU+B,OAAA,CAAQC,IAAI,EAAE6C,cAAA,CAAe,KAAKC,WAAW;QAE5D,IAAIC,OAAA,GAAU;QACd,KAAKxE,WAAA,CAAYyE,QAAA,GAAW,EAAE;QAC9B,KAAKC,gBAAA,CAAiB,KAAK1E,WAAA,EAAawE,OAAO;QAC/C,KAAKG,WAAA,CAAa;QAClB,KAAKzE,cAAA,GAAiB,KAAKF,WAAA,CAAYyE,QAAA,CAASG,KAAA,CAAO;QACvD,KAAKC,SAAA,CAAW;MACjB;IACF,GACD;MACEnJ,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASkH,iBAAiBI,OAAA,EAASC,IAAA,EAAM;QAC9C,IAAIP,OAAA,GAAUO,IAAA;QACd,OAAO,MAAM;UACX,IAAIC,KAAA,GAAQ,KAAK5E,KAAA,CAAMqC,OAAA,CAAQ,KAAK+B,OAAO,IAAI;UAC/C,IAAIS,OAAA,GAAU,KAAK7E,KAAA,CAAMqC,OAAA,CAAQ,KAAK+B,OAAO;UAC7C,IAAIU,QAAA,GAAW,KAAK9E,KAAA,CAAMqC,OAAA,CAAQ,KAAKuC,KAAK,IAAI;UAChD,IAAIA,KAAA,GAAQ,KAAKC,OAAA,GAAUD,KAAA,EAAO;YAChC,IAAI9E,cAAA,GAAiB,CAAE;YACvBA,cAAA,CAAeuE,QAAA,GAAW,EAAE;YAC5B,IAAIU,QAAA,GAAW,KAAK9C,SAAA,CAAU,KAAKjC,KAAA,CAAMuC,MAAA,CAAO6B,OAAA,EAASQ,KAAA,GAAQR,OAAA,GAAU,CAAC,CAAC,EAAEY,IAAA,CAAM;YACrF,IAAIC,IAAA,GAAOF,QAAA,CAASG,KAAA,CAAM,IAAI;YAC9B,IAAID,IAAA,CAAKlK,MAAA,GAAS,GAAG;cACnB+E,cAAA,CAAeqF,IAAA,GAAOF,IAAA,CAAK,CAAC;cAC5B,IAAIA,IAAA,CAAKlK,MAAA,IAAU,GAAG;gBACpB+E,cAAA,CAAehD,IAAA,GAAOmI,IAAA,CAAK,CAAC;cAC9C,OAAuB;gBACLnF,cAAA,CAAehD,IAAA,GAAOmI,IAAA,CAAK,CAAC,IAAI,KAAKrF,WAAA,CAAYyE,QAAA,CAAStJ,MAAA;cAC3D;YACjB,OAAqB;cACL+E,cAAA,CAAehD,IAAA,GAAOiI,QAAA;cACtBjF,cAAA,CAAeqF,IAAA,GAAO;YACvB;YAED,IAAIrF,cAAA,CAAeqF,IAAA,KAAS,aAAa;cACvCrF,cAAA,CAAe8D,IAAA,GAAO,KAAK5D,KAAA,CAAMuC,MAAA,CAAOuC,QAAA,EAAUD,OAAA,GAAUC,QAAQ,EAAEE,IAAA,CAAM;cAC5E,IAAII,IAAA,GAAO,KAAKd,gBAAA,CAAiBxE,cAAA,EAAgB+E,OAAA,GAAU,CAAC;cAC5DT,OAAA,GAAUgB,IAAA,CAAKC,GAAA;cACfvF,cAAA,CAAeuE,QAAA,GAAWe,IAAA,CAAKtH,MAAA,CAAOuG,QAAA;YACtD,OAAqB;cACL,IAAIiB,SAAA,GAAY,KAAKtF,KAAA,CAAMuF,WAAA,CAAY,KAAKT,QAAA,GAAW,IAAIU,IAAA,CAAKC,GAAA,CAAIX,QAAA,EAAUD,OAAO,IAAIA,OAAO;cAChG/E,cAAA,CAAe8D,IAAA,GAAO,KAAK5D,KAAA,CAAMuC,MAAA,CAAOqC,KAAA,EAAOU,SAAA,GAAYV,KAAK,EAAEI,IAAA,CAAM;cACxE,IAAIF,QAAA,IAAY,KAAKD,OAAA,GAAUC,QAAA,EAAU;gBACvCV,OAAA,GAAUS,OAAA,GAAU;cACtC,OAAuB;gBACL,IAAIa,SAAA,GAAYF,IAAA,CAAKG,GAAA,CAAIL,SAAA,GAAY,GAAGV,KAAK;gBAC7C,IAAIgB,KAAA,GAAQ,KAAKtB,gBAAA,CAAiBxE,cAAA,EAAgB4F,SAAS;gBAC3DtB,OAAA,GAAUwB,KAAA,CAAMP,GAAA;gBAChBvF,cAAA,CAAeuE,QAAA,GAAWuB,KAAA,CAAM9H,MAAA,CAAOuG,QAAA;cACxC;YACF;YAEDvE,cAAA,CAAehC,MAAA,GAAS4G,OAAA;YACxB,IAAI5E,cAAA,CAAeqF,IAAA,IAAQ,YAAY;cACrCT,OAAA,CAAQL,QAAA,CAAS9G,IAAA,CAAKuC,cAAc;YACrC;UACf,OAAmB;YACLsE,OAAA,GAAUQ,KAAA,KAAU,KAAK,KAAK5E,KAAA,CAAMjF,MAAA,GAAS8J,OAAA,GAAU;YACvD;UACD;QACF;QAED,OAAO;UACL/G,MAAA,EAAQ4G,OAAA;UACRW,GAAA,EAAKjB;QACN;MACF;IACF,GACD;MACE9I,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASqH,UAAA,EAAY;QAC1B,IAAIoB,MAAA,GAAS;QAEb,KAAKC,SAAA,CAAW;QAChB,IAAI,KAAKhG,cAAA,CAAehC,MAAA,IAAU,KAAKgC,cAAA,CAAeuE,QAAA,CAAStJ,MAAA,GAAS,KAAK,CAAC,KAAK+E,cAAA,CAAeiG,MAAA,EAAQ;UACxGC,UAAA,CAAW,YAAY;YACrBH,MAAA,CAAOpB,SAAA,CAAW;UACnB,GAAE,CAAC;QAChB,OAAiB;UACLuB,UAAA,CAAW,YAAY;YACrBH,MAAA,CAAO5F,MAAA,CAAO;cACZgG,MAAA,EAAQJ,MAAA,CAAO1F,MAAA;cACfC,UAAA,EAAYyF,MAAA,CAAOzF;YACnC,CAAe;UACF,GAAE,CAAC;QACL;MACF;IACF,GACD;MACE9E,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS0I,UAAA,EAAY;QAC1B,IAAII,SAAA,GAAY;QAChB,OAAO,MAAM;UACX,IAAI,CAAC,KAAKpG,cAAA,CAAeiG,MAAA,EAAQ;YAC/B,QAAQ,KAAKjG,cAAA,CAAeqF,IAAA;cAC1B,KAAK;gBACH;cACF,KAAK;gBACH,KAAK9E,kBAAA,GAAqB8F,QAAA,CAAS,KAAKrG,cAAA,CAAe8D,IAAI;gBAC3D;cACF,KAAK;gBACH,KAAKwC,SAAA,CAAW;gBAChB;cACF,KAAK;gBACH,KAAKC,wBAAA,CAA0B;gBAC/B;cACF,KAAK;gBACH,KAAK9B,WAAA,CAAa;gBAClB,KAAKjE,WAAA,GAAc,CAAE;gBACrB,KAAKA,WAAA,CAAYxD,IAAA,GAAO,KAAKgD,cAAA,CAAehD,IAAA,CAAKkI,IAAA,CAAM;gBACvD,KAAK1E,WAAA,CAAYgG,UAAA,GAAa,KAAKC,cAAA,CAAe,KAAKzG,cAAc,EAAEkF,IAAA,CAAM;gBAC7E,KAAK1E,WAAA,CAAYkG,oBAAA,GAAuB,EAAE;gBAC1C,KAAKlG,WAAA,CAAYmG,YAAA,GAAe;kBAC9BC,QAAA,EAAU,EAAE;kBACZC,OAAA,EAAS,EAAE;kBACXC,GAAA,EAAK,EAAE;kBACPC,WAAA,EAAa,EAAE;kBACfC,WAAA,EAAa,EAAE;kBACfC,OAAA,EAAS,EAAE;kBACXC,eAAA,EAAiB;gBAClB;gBACD,KAAK1G,WAAA,CAAY2G,SAAA,GAAY,EAAE;gBAC/B,KAAK3G,WAAA,CAAY4G,aAAA,GAAgB,EAAE;gBACnC,KAAK5G,WAAA,CAAY6G,QAAA,GAAW,EAAE;gBAC9B,KAAK7G,WAAA,CAAY8G,SAAA,GAAY,KAAKrH,aAAA;gBAClC,KAAKsH,0BAAA,CAA4B;gBACjC,KAAKC,gBAAA,CAAkB;gBACvBpB,SAAA,GAAY;gBACZ;cACF,KAAK;gBACH,KAAKoB,gBAAA,CAAkB;gBACvB;cACF,KAAK;gBACH,KAAKC,qBAAA,CAAuB;gBAC5B;cACF,KAAK;gBACH;cACF,KAAK;gBACH,KAAKC,oBAAA,CAAsB;gBAC3B;cACF,KAAK;gBACH,KAAKC,YAAA,CAAc;gBACnB;cACF,KAAK;gBACH,KAAKC,eAAA,CAAiB;gBACtB;cACF,KAAK;gBACH,KAAKnD,WAAA,CAAa;gBAClB,KAAKhE,aAAA,GAAgB,CAAE;gBACvB,KAAKA,aAAA,CAAczD,IAAA,GAAO,KAAKgD,cAAA,CAAehD,IAAA,CAAKkI,IAAA,CAAM;gBACzD,KAAKzE,aAAA,CAAcoH,WAAA,GAAc,EAAE;gBACnC;cACF,KAAK;gBACH,IAAI,KAAKnH,mBAAA,EAAqB;kBAC5B,KAAKD,aAAA,CAAcoH,WAAA,CAAYpK,IAAA,CAAK,KAAKiD,mBAAmB;gBAC7D;gBAED,KAAKA,mBAAA,GAAsB,IAAItE,cAAA,CAAgB;gBAC/C,KAAKsE,mBAAA,CAAoB5E,QAAA,GAAW,KAAKkE,cAAA,CAAe8D,IAAA,CAAKoB,IAAA,CAAM;gBACnE;cACF,KAAK;gBACH,KAAK4C,iBAAA,CAAmB;gBACxB1B,SAAA,GAAY;gBACZ;YACH;YAED,KAAKpG,cAAA,CAAeiG,MAAA,GAAS;UAC9B;UAED,IAAI,KAAKjG,cAAA,CAAeuE,QAAA,CAAStJ,MAAA,GAAS,GAAG;YAC3C,KAAK+E,cAAA,GAAiB,KAAKA,cAAA,CAAeuE,QAAA,CAASG,KAAA,CAAO;YAC1D,IAAI,KAAKpF,KAAA,EAAO;cACd0C,OAAA,CAAQ+F,GAAA,CAAI,gBAAgB,KAAK/H,cAAA,CAAehD,IAAI;YACrD;YAED,IAAIoJ,SAAA,EAAW;UAC7B,OAAmB;YACL,IAAI,KAAKpG,cAAA,CAAeiG,MAAA,EAAQ;cAC9B,IAAI,KAAKjG,cAAA,CAAehC,MAAA,IAAU,CAAC,KAAKgC,cAAA,CAAehC,MAAA,CAAOA,MAAA,EAAQ;gBACpE,KAAKyG,WAAA,CAAa;cACnB;YACF;YAED,IAAI,KAAKzE,cAAA,CAAehC,MAAA,EAAQ;cAC9B,KAAKgC,cAAA,GAAiB,KAAKA,cAAA,CAAehC,MAAA;YAC1D,OAAqB;cACLoI,SAAA,GAAY;YACb;YAED,IAAIA,SAAA,EAAW;UAChB;QACF;QAED;MACD;IACF,GACD;MACE5K,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASmH,YAAA,EAAc;QAC5B,IAAI,KAAKjE,WAAA,IAAe,QAAQ,KAAKA,WAAA,CAAYxD,IAAA,EAAM;UACrD,KAAKgL,mBAAA,CAAqB;QAC3B;QAED,KAAKxH,WAAA,GAAc,CAAE;QACrB,IAAI,KAAKC,aAAA,IAAiB,QAAQ,KAAKA,aAAA,CAAczD,IAAA,EAAM;UACzD,IAAI,KAAK0D,mBAAA,EAAqB;YAC5B,KAAKD,aAAA,CAAcoH,WAAA,CAAYpK,IAAA,CAAK,KAAKiD,mBAAmB;YAC5D,KAAKA,mBAAA,GAAsB;UAC5B;UAED,KAAKuH,oBAAA,CAAsB;QAC5B;QAED,KAAKxH,aAAA,GAAgB,CAAE;MACxB;IACF,GACD;MACEjF,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASmJ,eAAeyB,IAAA,EAAM;QACnC,IAAIA,IAAA,CAAKlK,MAAA,EAAQ;UACf,IAAIkK,IAAA,CAAKlK,MAAA,CAAOhB,IAAA,EAAM;YACpB,OAAOkL,IAAA,CAAKlK,MAAA,CAAOhB,IAAA;UACjC,OAAmB;YACL,OAAO,KAAKyJ,cAAA,CAAeyB,IAAA,CAAKlK,MAAM;UACvC;QACb,OAAiB;UACL,OAAO;QACR;MACF;IACF,GACD;MACExC,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASgJ,UAAA,EAAY;QAC1B,KAAK1G,aAAA,GAAgB,KAAKI,cAAA,CAAehD,IAAA,CAAKkI,IAAA,CAAM;QACpD,KAAKjF,aAAA,GAAgB,CAAE;QACvB,KAAKA,aAAA,CAAcjD,IAAA,GAAO,KAAK4C,aAAA;QAC/B,KAAKK,aAAA,CAAcsE,QAAA,GAAW,EAAE;QAChC,IAAI,KAAKvE,cAAA,CAAehC,MAAA,IAAU,KAAKgC,cAAA,CAAehC,MAAA,CAAOhB,IAAA,EAAM;UACjE,KAAKiD,aAAA,CAAcuG,UAAA,GAAa,KAAKxG,cAAA,CAAehC,MAAA,CAAOhB,IAAA;QAC5D;QAED,KAAK6C,eAAA,CAAgBpC,IAAA,CAAK,KAAKmC,aAAa;QAC5C,KAAKG,QAAA,CAAS,KAAKH,aAAa,IAAI,KAAKK,aAAA;MAC1C;IACF,GACD;MACEzE,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASiJ,yBAAA,EAA2B;QACzC,KAAKtG,aAAA,CAAckI,oBAAA,GAAuB,IAAIC,OAAA,CAAS;QACvD,IAAItE,IAAA,GAAO,KAAK9D,cAAA,CAAe8D,IAAA,CAAKsB,KAAA,CAAM,GAAG;QAC7C,KAAKiD,gBAAA,CAAiB,KAAKpI,aAAA,CAAckI,oBAAA,EAAsBrE,IAAI;QACnE,KAAKyD,0BAAA,CAA4B;MAClC;IACF,GACD;MACE/L,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASiK,2BAAA,EAA6B;QAC3C,IAAI,CAAC,KAAKtH,aAAA,CAAckI,oBAAA,EAAsB;UAC5C;QACD;QAED,IAAIG,CAAA,GAAI,IAAIC,IAAA,CAAM;QAClBD,CAAA,CAAEtL,IAAA,GAAO,KAAKiD,aAAA,CAAcjD,IAAA;QAC5BsL,CAAA,CAAEE,YAAA,CAAa,KAAKvI,aAAA,CAAckI,oBAAoB;QACtDG,CAAA,CAAEG,WAAA,GAAcH,CAAA,CAAE/J,MAAA;QAClB+J,CAAA,CAAEH,oBAAA,GAAuB,KAAKlI,aAAA,CAAckI,oBAAA;QAC5C,KAAKlI,aAAA,CAAcyI,OAAA,GAAUJ,CAAA;QAC7B,IAAI,KAAKrI,aAAA,CAAcuG,UAAA,EAAY;UACjC,SAASmC,KAAA,IAAS,KAAK5I,QAAA,EAAU;YAC/B,IAAI,KAAKA,QAAA,CAAS4I,KAAK,EAAE3L,IAAA,KAAS,KAAKiD,aAAA,CAAcuG,UAAA,EAAY;cAC/D,KAAKzG,QAAA,CAAS4I,KAAK,EAAED,OAAA,CAAQE,GAAA,CAAI,KAAK3I,aAAA,CAAcyI,OAAO;YAC5D;UACF;QACF;MACF;IACF,GACD;MACElN,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASkK,iBAAA,EAAmB;QACjC,IAAIlD,OAAA,GAAU;QACd,IAAIuE,IAAA,GAAO;QACX,IAAIC,UAAA,GAAa;QACjB,IAAIC,SAAA,GAAY;QAChB,OAAO,MAAM;UACX,IAAIC,UAAA,GAAa;UACjB,IAAIF,UAAA,KAAe,GAAG;YACpB,IAAIG,IAAA,GAAO,KAAKC,SAAA,CAAU5E,OAAO;YACjCA,OAAA,GAAU2E,IAAA,CAAK3E,OAAA;YACfwE,UAAA,GAAa;YACbC,SAAA,GAAY,KAAK/I,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,MAAM+B,OAAO,IAAI;YAC9D,IAAIyE,SAAA,IAAa,GAAG;cAClBA,SAAA,GAAY,KAAK/I,cAAA,CAAe8D,IAAA,CAAK7I,MAAA;YACtC;UACf,OAAmB;YACL,IAAIqH,IAAA,GAAO;YACX,QAAQuG,IAAA;cACN,KAAK;gBACHvG,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO,IAAI;gBACxD;cACF,KAAK;gBACHhC,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,MAAM+B,OAAO,IAAI;gBACzD;YACH;YAED,IAAIhC,IAAA,KAAS,KAAKA,IAAA,GAAOyG,SAAA,EAAW;cAClCzG,IAAA,GAAOyG,SAAA;cACPD,UAAA,GAAa;cACbE,UAAA,GAAa;YACd;YAED,QAAQ,KAAKhJ,cAAA,CAAeqF,IAAA;cAC1B,KAAK;gBACH,QAAQwD,IAAA;kBACN,KAAK;oBACH,KAAKM,YAAA,CAAa,KAAKnJ,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO,CAAC;oBAC1E;kBACF,KAAK;oBACH,KAAK8E,UAAA,CAAW,KAAKpJ,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO,CAAC;oBACxE;gBACH;gBAED;cACF,KAAK;gBACH,QAAQuE,IAAA;kBACN,KAAK;oBACH,KAAKQ,kBAAA,CAAmB,KAAKrJ,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO,CAAC;oBAChF;gBACH;gBAED;YACH;YAEDA,OAAA,GAAUhC,IAAA,GAAO;YACjB,IAAI0G,UAAA,EAAY;cACdH,IAAA;YACD;UACF;UAED,IAAIvE,OAAA,IAAW,KAAKtE,cAAA,CAAe8D,IAAA,CAAK7I,MAAA,EAAQ;YAC9C;UACD;QACF;MACF;IACF,GACD;MACEO,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS4L,UAAUI,KAAA,EAAO;QAC/B,IAAIhH,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+G,KAAK;QACtD,OAAO;UACLC,IAAA,EAAMlD,QAAA,CAAS,KAAKrG,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6G,KAAA,EAAOhH,IAAA,GAAOgH,KAAK,CAAC;UACnEhF,OAAA,EAAShC,IAAA,GAAO;QACjB;MACF;IACF,GACD;MACE9G,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS6L,aAAa/G,IAAA,EAAM;QACjC,IAAI0B,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,CAAI,CAAE,EAClCzC,MAAA,CAAO,GAAGL,IAAA,CAAKnH,MAAA,GAAS,CAAC,EACzBmK,KAAA,CAAM,GAAG;QACZ,KAAK5E,WAAA,CAAYmG,YAAA,CAAaC,QAAA,CAASnJ,IAAA,CAAK+L,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAAG0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAAG0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,CAAC;QACzG,KAAKtD,WAAA,CAAYmG,YAAA,CAAaI,WAAA,CAAYtJ,IAAA,CAAK,GAAG,GAAG,GAAG,CAAC;QACzD,KAAK+C,WAAA,CAAYmG,YAAA,CAAaK,WAAA,CAAYvJ,IAAA,CAAK,GAAG,GAAG,GAAG,CAAC;QACzD,KAAK+C,WAAA,CAAYkG,oBAAA,CAAqBjJ,IAAA,CAAK,CAAC;MAC7C;IACF,GACD;MACEjC,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS8L,WAAWhH,IAAA,EAAM;QAC/B,IAAI0B,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,CAAI,CAAE,EAClCzC,MAAA,CAAO,GAAGL,IAAA,CAAKnH,MAAA,GAAS,CAAC,EACzBmK,KAAA,CAAM,GAAG;QACZ,KAAK5E,WAAA,CAAYmG,YAAA,CAAaM,OAAA,CAAQxJ,IAAA,CACpC4I,QAAA,CAASvC,IAAA,CAAK,CAAC,GAAG,EAAE,GACpBuC,QAAA,CAASvC,IAAA,CAAK,CAAC,GAAG,EAAE,GACpBuC,QAAA,CAASvC,IAAA,CAAK,CAAC,GAAG,EAAE,CACrB;MACF;IACF,GACD;MACEtI,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS+L,mBAAmBjH,IAAA,EAAM;QACvC,IAAI0B,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,CAAI,CAAE,EAClCzC,MAAA,CAAO,GAAGL,IAAA,CAAKnH,MAAA,GAAS,CAAC,EACzBmK,KAAA,CAAM,GAAG;QACZ,KAAK5E,WAAA,CAAYmG,YAAA,CAAaE,OAAA,CAAQpJ,IAAA,CAAK+L,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAAG0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAAG0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,CAAC;MACzG;IACF,GACD;MACEtI,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASmM,eAAA,EAAiB;QAC/B,IAAIC,cAAA,GAAiB,IAAIC,cAAA,CAAgB;QACzC,IAAIC,QAAA,GAAW,EAAE;QACjB,IAAI/C,OAAA,GAAU,EAAE;QAChB,IAAIC,GAAA,GAAM,EAAE;QACZ,IAAIC,WAAA,GAAc,EAAE;QACpB,IAAIC,WAAA,GAAc,EAAE;QAIpB,IAAIlD,IAAA,GAAO,KAAKtD,WAAA,CAAYmG,YAAA;QAE5B,SAAS3L,EAAA,GAAI,GAAG6O,CAAA,GAAI/F,IAAA,CAAKmD,OAAA,CAAQhM,MAAA,EAAQD,EAAA,GAAI6O,CAAA,EAAG7O,EAAA,IAAK;UACnD,IAAI8O,OAAA,GAAUhG,IAAA,CAAKmD,OAAA,CAAQjM,EAAC,IAAI;UAChC,IAAI+O,OAAA,GAAUjG,IAAA,CAAKmD,OAAA,CAAQjM,EAAC,IAAI;UAChC,IAAIgP,OAAA,GAAUlG,IAAA,CAAKmD,OAAA,CAAQjM,EAAC,IAAI;UAEhC4O,QAAA,CAASnM,IAAA,CAAKqG,IAAA,CAAK8C,QAAA,CAASmD,OAAO,GAAGjG,IAAA,CAAK8C,QAAA,CAASmD,OAAA,GAAU,CAAC,GAAGjG,IAAA,CAAK8C,QAAA,CAASmD,OAAA,GAAU,CAAC,CAAC;UAC5FlD,OAAA,CAAQpJ,IAAA,CAAKqG,IAAA,CAAK+C,OAAA,CAAQkD,OAAO,GAAGjG,IAAA,CAAK+C,OAAA,CAAQkD,OAAA,GAAU,CAAC,GAAGjG,IAAA,CAAK+C,OAAA,CAAQkD,OAAA,GAAU,CAAC,CAAC;UACxFhD,WAAA,CAAYtJ,IAAA,CACVqG,IAAA,CAAKiD,WAAA,CAAYiD,OAAO,GACxBlG,IAAA,CAAKiD,WAAA,CAAYiD,OAAA,GAAU,CAAC,GAC5BlG,IAAA,CAAKiD,WAAA,CAAYiD,OAAA,GAAU,CAAC,GAC5BlG,IAAA,CAAKiD,WAAA,CAAYiD,OAAA,GAAU,CAAC,CAC7B;UACDhD,WAAA,CAAYvJ,IAAA,CACVqG,IAAA,CAAKkD,WAAA,CAAYgD,OAAO,GACxBlG,IAAA,CAAKkD,WAAA,CAAYgD,OAAA,GAAU,CAAC,GAC5BlG,IAAA,CAAKkD,WAAA,CAAYgD,OAAA,GAAU,CAAC,GAC5BlG,IAAA,CAAKkD,WAAA,CAAYgD,OAAA,GAAU,CAAC,CAC7B;UACDlD,GAAA,CAAIrJ,IAAA,CAAKqG,IAAA,CAAKgD,GAAA,CAAIgD,OAAO,GAAGhG,IAAA,CAAKgD,GAAA,CAAIgD,OAAA,GAAU,CAAC,CAAC;QAClD;QAIDJ,cAAA,CAAeO,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBN,QAAA,EAAU,CAAC,CAAC;QAC/EF,cAAA,CAAeO,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBrD,OAAA,EAAS,CAAC,CAAC;QAC5E6C,cAAA,CAAeO,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBpD,GAAA,EAAK,CAAC,CAAC;QACpE4C,cAAA,CAAeO,YAAA,CAAa,aAAa,IAAIE,qBAAA,CAAsBpD,WAAA,EAAa,CAAC,CAAC;QAClF2C,cAAA,CAAeO,YAAA,CAAa,cAAc,IAAIC,sBAAA,CAAuBlD,WAAA,EAAa,CAAC,CAAC;QAEpF,KAAKoD,cAAA,CAAeV,cAAA,EAAgB5F,IAAA,CAAKoD,eAAe;QAExD,OAAOwC,cAAA;MACR;IACF,GACD;MACElO,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS8M,eAAeV,cAAA,EAAgBxC,eAAA,EAAiB;QAC9D,IAAImD,KAAA;QACJ,IAAIC,MAAA,GAAS,EAAE;QACf,IAAIC,aAAA,GAAgB;QAEpB,SAASvP,EAAA,GAAI,GAAGA,EAAA,GAAIkM,eAAA,CAAgBjM,MAAA,EAAQD,EAAA,IAAK;UAC/C,IAAIwP,oBAAA,GAAuBtD,eAAA,CAAgBlM,EAAC;UAE5C,IAAIwP,oBAAA,KAAyBD,aAAA,EAAe;YAC1CA,aAAA,GAAgBC,oBAAA;YAEhB,IAAIH,KAAA,KAAU,QAAW;cACvBA,KAAA,CAAMI,KAAA,GAAQzP,EAAA,GAAI,IAAIqP,KAAA,CAAMf,KAAA;cAC5BgB,MAAA,CAAO7M,IAAA,CAAK4M,KAAK;YAClB;YAEDA,KAAA,GAAQ;cACNf,KAAA,EAAOtO,EAAA,GAAI;cACXuP;YACD;UACF;QACF;QAED,IAAIF,KAAA,KAAU,QAAW;UACvBA,KAAA,CAAMI,KAAA,GAAQC,CAAA,GAAI,IAAIL,KAAA,CAAMf,KAAA;UAC5BgB,MAAA,CAAO7M,IAAA,CAAK4M,KAAK;QAClB;QAEDX,cAAA,CAAeY,MAAA,GAASA,MAAA;MACzB;IACF,GACD;MACE9O,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASmK,sBAAA,EAAwB;QACtC,IAAInD,OAAA,GAAU;QACd,IAAIuE,IAAA,GAAO;QACX,IAAIC,UAAA,GAAa;QACjB,OAAO,MAAM;UACX,QAAQD,IAAA;YACN,KAAK;cACH,IAAIC,UAAA,KAAe,GAAG;gBACpB,IAAIG,IAAA,GAAO,KAAKC,SAAA,CAAU,CAAC;gBAC3B5E,OAAA,GAAU2E,IAAA,CAAK3E,OAAA;gBACfwE,UAAA,GAAa;cAC/B,OAAuB;gBACL,IAAIxG,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO,IAAI;gBAC5D,IAAIhC,IAAA,KAAS,GAAG;kBACdA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAK7I,MAAA;kBAChC4N,IAAA,GAAO;kBACPC,UAAA,GAAa;gBACd;gBAED,IAAI1G,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;gBAClE,IAAIR,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;gBAChD,IAAI,KAAKhF,YAAA,EAAc;kBACrB,KAAKI,WAAA,CAAYmG,YAAA,CAAaG,GAAA,CAAIrJ,IAAA,CAAK+L,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAAG,IAAI0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,CAAC;gBACvG,OAAyB;kBACL,KAAKtD,WAAA,CAAYmG,YAAA,CAAaG,GAAA,CAAIrJ,IAAA,CAAK+L,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAAG0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,CAAC;gBAChF;gBAEDQ,OAAA,GAAUhC,IAAA,GAAO;cAClB;cAED;UACH;UAED,IAAIgC,OAAA,IAAW,KAAKtE,cAAA,CAAe8D,IAAA,CAAK7I,MAAA,EAAQ;YAC9C;UACD;QACF;MACF;IACF,GACD;MACEO,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASoK,qBAAA,EAAuB;QACrC,IAAIpD,OAAA,GAAU;QACd,IAAIuE,IAAA,GAAO;QACX,IAAIC,UAAA,GAAa;QACjB,OAAO,MAAM;UACX,IAAIA,UAAA,GAAa,GAAG;YAClB,IAAIG,IAAA,GAAO,KAAKC,SAAA,CAAU5E,OAAO;YACjCA,OAAA,GAAU2E,IAAA,CAAK3E,OAAA;YACfwE,UAAA;UACd,OAAmB;YACL,IAAIxG,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;YACxD,IAAIhC,IAAA,KAAS,IAAI;cACfA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAK7I,MAAA;cAChC4N,IAAA,GAAO;cACPC,UAAA,GAAa;YACd;YAED,IAAI1G,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;YAClE,IAAIR,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;YAChD,SAASpK,EAAA,GAAI,GAAGA,EAAA,GAAI8I,IAAA,CAAK7I,MAAA,EAAQD,EAAA,IAAK;cACpC,KAAKwF,WAAA,CAAYmG,YAAA,CAAaO,eAAA,CAAgBlM,EAAC,IAAIqL,QAAA,CAASvC,IAAA,CAAK9I,EAAC,CAAC;YACpE;YAEDsJ,OAAA,GAAU,KAAKtE,cAAA,CAAe8D,IAAA,CAAK7I,MAAA;UACpC;UAED,IAAIqJ,OAAA,IAAW,KAAKtE,cAAA,CAAe8D,IAAA,CAAK7I,MAAA,IAAU4N,IAAA,IAAQ,GAAG;YAC3D;UACD;QACF;MACF;IACF,GACD;MACErN,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASqK,aAAA,EAAe;QAC7B,IAAIhI,OAAA,GAAU,IAAIgL,iBAAA,CAAkB;UAClCC,KAAA,EAAOlF,IAAA,CAAKmF,MAAA,CAAM,IAAK;QACnC,CAAW;QACDlL,OAAA,CAAQmL,IAAA,GAAOC,SAAA;QACfpL,OAAA,CAAQ3C,IAAA,GAAO,KAAKgD,cAAA,CAAehD,IAAA;QACnC,IAAIsH,OAAA,GAAU;QACd,IAAIhC,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,MAAM+B,OAAO;QACzD,IAAIlC,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAClE,IAAIR,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;QAChDzF,OAAA,CAAQiL,KAAA,CAAMI,CAAA,GAAIxB,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC;QACpCnE,OAAA,CAAQiL,KAAA,CAAMK,CAAA,GAAIzB,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC;QACpCnE,OAAA,CAAQiL,KAAA,CAAMtC,CAAA,GAAIkB,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC;QACpCQ,OAAA,GAAUhC,IAAA,GAAO;QACjBA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACpDlC,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAC9D3E,OAAA,CAAQuL,SAAA,GAAY1B,UAAA,CAAW,KAAKrH,SAAA,CAAUC,IAAI,CAAC;QACnDkC,OAAA,GAAUhC,IAAA,GAAO;QACjBA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,MAAM+B,OAAO;QACrDlC,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAC9D,IAAI6G,KAAA,GAAQ,KAAKhJ,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;QACjDzF,OAAA,CAAQyL,QAAA,CAASJ,CAAA,GAAIxB,UAAA,CAAW2B,KAAA,CAAM,CAAC,CAAC;QACxCxL,OAAA,CAAQyL,QAAA,CAASH,CAAA,GAAIzB,UAAA,CAAW2B,KAAA,CAAM,CAAC,CAAC;QACxCxL,OAAA,CAAQyL,QAAA,CAAS9C,CAAA,GAAIkB,UAAA,CAAW2B,KAAA,CAAM,CAAC,CAAC;QACxC7G,OAAA,GAAUhC,IAAA,GAAO;QACjBA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,MAAM+B,OAAO;QACrD,IAAIhC,IAAA,KAAS,IAAI;UACfA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAK7I,MAAA;QACjC;QAEDmH,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAC9D,IAAI+G,KAAA,GAAQ,KAAKlJ,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;QACjDzF,OAAA,CAAQ2L,QAAA,CAASN,CAAA,GAAIxB,UAAA,CAAW6B,KAAA,CAAM,CAAC,CAAC;QACxC1L,OAAA,CAAQ2L,QAAA,CAASL,CAAA,GAAIzB,UAAA,CAAW6B,KAAA,CAAM,CAAC,CAAC;QACxC1L,OAAA,CAAQ2L,QAAA,CAAShD,CAAA,GAAIkB,UAAA,CAAW6B,KAAA,CAAM,CAAC,CAAC;QACxC,IAAIE,WAAA,GAAc;QAClB,OAAO,MAAM;UACX,IAAI,KAAKvL,cAAA,CAAeuE,QAAA,CAAStJ,MAAA,GAAS,GAAG;YAC3CsQ,WAAA,GAAc,KAAKvL,cAAA,CAAeuE,QAAA,CAASG,KAAA,CAAO;YAClD,IAAI,KAAKpF,KAAA,EAAO;cACd0C,OAAA,CAAQ+F,GAAA,CAAI,gBAAgBwD,WAAA,CAAYvO,IAAI;YAC7C;YAED,IAAIwO,QAAA,GAAWD,WAAA,CAAYzH,IAAA,CAAKrB,MAAA,CAAO,GAAG8I,WAAA,CAAYzH,IAAA,CAAK7I,MAAA,GAAS,CAAC;YACrE,QAAQsQ,WAAA,CAAYlG,IAAA;cAClB,KAAK;gBACH1F,OAAA,CAAQ8L,GAAA,GAAM,KAAKlM,SAAA,CAAUyB,IAAA,CAAKwK,QAAQ;gBAC1C;cACF,KAAK;gBACH7L,OAAA,CAAQ+L,OAAA,GAAU,KAAKnM,SAAA,CAAUyB,IAAA,CAAKwK,QAAQ;gBAC9C7L,OAAA,CAAQgM,SAAA,GAAY;gBACpB;cACF,KAAK;gBACHhM,OAAA,CAAQiM,SAAA,GAAY,KAAKrM,SAAA,CAAUyB,IAAA,CAAKwK,QAAQ;gBAChD7L,OAAA,CAAQkM,WAAA,GAAc,IAAIC,OAAA,CAAQ,GAAG,CAAC;gBACtC;cACF,KAAK;gBACHnM,OAAA,CAAQoM,WAAA,GAAc,KAAKxM,SAAA,CAAUyB,IAAA,CAAKwK,QAAQ;gBAClD;cACF,KAAK;gBACH7L,OAAA,CAAQqM,QAAA,GAAW,KAAKzM,SAAA,CAAUyB,IAAA,CAAKwK,QAAQ;gBAC/C;YACH;UACf,OAAmB;YACL;UACD;QACF;QAED,KAAKhL,WAAA,CAAY2G,SAAA,CAAU1J,IAAA,CAAKkC,OAAO;MACxC;IACF,GACD;MACEnE,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASsK,gBAAA,EAAkB;QAChC,IAAIqE,OAAA,GAAU,IAAIrQ,QAAA,CAAU;QAC5B,IAAI0I,OAAA,GAAU;QACd,IAAIhC,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACxD,IAAIlC,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAClEA,OAAA,GAAUhC,IAAA,GAAO;QACjB2J,OAAA,CAAQnQ,QAAA,GAAWsG,IAAA,CAAKK,MAAA,CAAO,GAAGL,IAAA,CAAKnH,MAAA,GAAS,CAAC;QACjDgR,OAAA,CAAQlQ,SAAA,GAAY,KAAKyE,WAAA,CAAY6G,QAAA,CAASpM,MAAA;QAC9CqH,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACpDA,OAAA,GAAUhC,IAAA,GAAO;QACjBA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACpDlC,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAC9D,IAAIR,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;QAChD,SAASpK,EAAA,GAAI,GAAGA,EAAA,GAAI8I,IAAA,CAAK7I,MAAA,EAAQD,EAAA,IAAK;UACpCiR,OAAA,CAAQjQ,OAAA,CAAQyB,IAAA,CAAK4I,QAAA,CAASvC,IAAA,CAAK9I,EAAC,CAAC,CAAC;QACvC;QAEDsJ,OAAA,GAAUhC,IAAA,GAAO;QACjBA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACpDlC,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAC9D,IAAI6G,KAAA,GAAQ,KAAKhJ,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;QACjD,SAAS8G,EAAA,GAAK,GAAGA,EAAA,GAAKf,KAAA,CAAMlQ,MAAA,EAAQiR,EAAA,IAAM;UACxCD,OAAA,CAAQhQ,OAAA,CAAQwB,IAAA,CAAK+L,UAAA,CAAW2B,KAAA,CAAMe,EAAE,CAAC,CAAC;QAC3C;QAED5H,OAAA,GAAUhC,IAAA,GAAO;QACjBA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACpD,IAAIhC,IAAA,IAAQ,GAAG;UACbA,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAK7I,MAAA;QACjC;QAEDmH,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAC9D,IAAI+G,KAAA,GAAQ,KAAKlJ,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,GAAG;QACjD6G,OAAA,CAAQ9P,YAAA,GAAe,IAAIiM,OAAA,CAAS;QACpC,KAAKC,gBAAA,CAAiB4D,OAAA,CAAQ9P,YAAA,EAAckP,KAAK;QACjD,KAAK7K,WAAA,CAAY6G,QAAA,CAAS5J,IAAA,CAAKwO,OAAO;MACvC;IACF,GACD;MACEzQ,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS6O,iBAAiBC,SAAA,EAAWC,MAAA,EAAQ;QAClD,IAAIC,OAAA,GAAU;QACd,SAAS3D,KAAA,IAAS,KAAK5I,QAAA,EAAU;UAC/B,IAAI,KAAKA,QAAA,CAAS4I,KAAK,EAAE3L,IAAA,KAASoP,SAAA,IAAaE,OAAA,EAAS;YACtDA,OAAA,GAAU;YACV,IAAIhE,CAAA,GAAI,IAAIC,IAAA,CAAM;YAClBD,CAAA,CAAEtL,IAAA,GAAO,KAAK+C,QAAA,CAAS4I,KAAK,EAAE3L,IAAA;YAC9BsL,CAAA,CAAEE,YAAA,CAAa,KAAKzI,QAAA,CAAS4I,KAAK,EAAER,oBAAoB;YACxDG,CAAA,CAAEG,WAAA,GAAcH,CAAA,CAAE/J,MAAA;YAClB+J,CAAA,CAAEH,oBAAA,GAAuB,KAAKpI,QAAA,CAAS4I,KAAK,EAAER,oBAAA;YAC9CG,CAAA,CAAElK,GAAA,GAAM,IAAII,OAAA,CAAS,EAACC,qBAAA,CAAsB6J,CAAA,CAAEH,oBAAoB,EAAEoE,OAAA,CAAS;YAC7EjE,CAAA,CAAEkE,IAAA,GAAO,IAAI9N,UAAA,CAAY,EAACC,qBAAA,CAAsB2J,CAAA,CAAEH,oBAAoB,EAAEoE,OAAA,CAAS;YACjFjE,CAAA,CAAEhK,GAAA,GAAM,IAAIE,OAAA,CAAS,EAACI,kBAAA,CAAmB0J,CAAA,CAAEH,oBAAoB,EAAEoE,OAAA,CAAS;YAC1E,IAAI,KAAKxM,QAAA,CAAS4I,KAAK,EAAEnC,UAAA,IAAc,KAAKzG,QAAA,CAAS4I,KAAK,EAAEnC,UAAA,CAAWvL,MAAA,GAAS,GAAG;cACjF,SAASD,EAAA,GAAI,GAAGA,EAAA,GAAIqR,MAAA,CAAOpR,MAAA,EAAQD,EAAA,IAAK;gBACtC,IAAI,KAAK+E,QAAA,CAAS4I,KAAK,EAAEnC,UAAA,KAAe6F,MAAA,CAAOrR,EAAC,EAAEgC,IAAA,EAAM;kBACtDqP,MAAA,CAAOrR,EAAC,EAAE4N,GAAA,CAAIN,CAAC;kBACfA,CAAA,CAAEtK,MAAA,GAAShD,EAAA;kBACX;gBACD;cACF;YACF;YAEDqR,MAAA,CAAO5O,IAAA,CAAK6K,CAAC;UACd;QACF;MACF;IACF,GACD;MACE9M,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS0K,oBAAA,EAAsB;QACpC,IAAIyE,IAAA,GAAO;QACX,IAAI,KAAKjM,WAAA,CAAY6G,QAAA,CAASpM,MAAA,GAAS,GAAG;UACxC,IAAIyR,QAAA,GAAW,EAAE;UACjB,KAAKP,gBAAA,CAAiB,KAAK3L,WAAA,CAAY8G,SAAA,CAAUd,UAAA,EAAYkG,QAAQ;UACrE,SAASC,EAAA,GAAK,GAAGA,EAAA,GAAK,KAAKnM,WAAA,CAAY6G,QAAA,CAASpM,MAAA,EAAQ0R,EAAA,IAAM;YAC5D,IAAIC,SAAA,GAAY;YAChB,SAASC,EAAA,GAAK,GAAGA,EAAA,GAAKH,QAAA,CAASzR,MAAA,EAAQ4R,EAAA,IAAM;cAC3C,IAAIH,QAAA,CAASG,EAAE,EAAE7P,IAAA,KAAS,KAAKwD,WAAA,CAAY6G,QAAA,CAASsF,EAAE,EAAE7Q,QAAA,EAAU;gBAChE8Q,SAAA,GAAYC,EAAA;gBACZH,QAAA,CAASG,EAAE,EAAE1Q,YAAA,GAAe,IAAIiM,OAAA,CAAS;gBACzCsE,QAAA,CAASG,EAAE,EAAE1Q,YAAA,CAAa+B,IAAA,CAAK,KAAKsC,WAAA,CAAY6G,QAAA,CAASsF,EAAE,EAAExQ,YAAY;gBACzE;cACD;YACF;YAED,SAAS2Q,EAAA,GAAK,GAAGA,EAAA,GAAK,KAAKtM,WAAA,CAAY6G,QAAA,CAASsF,EAAE,EAAE3Q,OAAA,CAAQf,MAAA,EAAQ6R,EAAA,IAAM;cACxE,IAAIC,WAAA,GAAc,KAAKvM,WAAA,CAAY6G,QAAA,CAASsF,EAAE,EAAE3Q,OAAA,CAAQ8Q,EAAE;cAC1D,IAAIE,MAAA,GAAS,KAAKxM,WAAA,CAAY6G,QAAA,CAASsF,EAAE,EAAE1Q,OAAA,CAAQ6Q,EAAE;cAErD,IAAIG,MAAA,GAASF,WAAA,GAAc;cAE3B,QAAQ,KAAKvM,WAAA,CAAYkG,oBAAA,CAAqBqG,WAAW;gBACvD,KAAK;kBACH,KAAKvM,WAAA,CAAYmG,YAAA,CAAaI,WAAA,CAAYkG,MAAM,IAAIL,SAAA;kBACpD,KAAKpM,WAAA,CAAYmG,YAAA,CAAaK,WAAA,CAAYiG,MAAM,IAAID,MAAA;kBACpD;gBACF,KAAK;kBACH,KAAKxM,WAAA,CAAYmG,YAAA,CAAaI,WAAA,CAAYkG,MAAA,GAAS,CAAC,IAAIL,SAAA;kBACxD,KAAKpM,WAAA,CAAYmG,YAAA,CAAaK,WAAA,CAAYiG,MAAA,GAAS,CAAC,IAAID,MAAA;kBACxD;gBACF,KAAK;kBACH,KAAKxM,WAAA,CAAYmG,YAAA,CAAaI,WAAA,CAAYkG,MAAA,GAAS,CAAC,IAAIL,SAAA;kBACxD,KAAKpM,WAAA,CAAYmG,YAAA,CAAaK,WAAA,CAAYiG,MAAA,GAAS,CAAC,IAAID,MAAA;kBACxD;gBACF,KAAK;kBACH,KAAKxM,WAAA,CAAYmG,YAAA,CAAaI,WAAA,CAAYkG,MAAA,GAAS,CAAC,IAAIL,SAAA;kBACxD,KAAKpM,WAAA,CAAYmG,YAAA,CAAaK,WAAA,CAAYiG,MAAA,GAAS,CAAC,IAAID,MAAA;kBACxD;cACH;cAED,KAAKxM,WAAA,CAAYkG,oBAAA,CAAqBqG,WAAW;cACjD,IAAI,KAAKvM,WAAA,CAAYkG,oBAAA,CAAqBqG,WAAW,IAAI,GAAG;gBAC1D/K,OAAA,CAAQ+F,GAAA,CAAI,gCAAgCgF,WAAW;cACxD;YACF;UACF;UAED,SAASG,EAAA,GAAK,GAAGA,EAAA,GAAK,KAAK1M,WAAA,CAAY2G,SAAA,CAAUlM,MAAA,EAAQiS,EAAA,IAAM;YAC7D,KAAK1M,WAAA,CAAY2G,SAAA,CAAU+F,EAAE,EAAEC,QAAA,GAAW;UAC3C;UAED,IAAIC,UAAA,GAAa,EAAE;UACnB,SAASC,GAAA,GAAM,GAAGA,GAAA,GAAMX,QAAA,CAASzR,MAAA,EAAQoS,GAAA,IAAO;YAC9C,IAAIX,QAAA,CAASW,GAAG,EAAElR,YAAA,EAAc;cAC9BiR,UAAA,CAAW3P,IAAA,CAAKiP,QAAA,CAASW,GAAG,EAAElR,YAAY;YAC1D,OAAqB;cACLiR,UAAA,CAAW3P,IAAA,CAAK,IAAI2K,OAAA,EAAS;YAC9B;UACF;UAED,IAAIsB,cAAA,GAAiB,KAAKD,cAAA,CAAgB;UAC1CgD,IAAA,GAAO,IAAIa,WAAA,CACT5D,cAAA,EACA,KAAKlJ,WAAA,CAAY2G,SAAA,CAAUlM,MAAA,KAAW,IAAI,KAAKuF,WAAA,CAAY2G,SAAA,CAAU,CAAC,IAAI,KAAK3G,WAAA,CAAY2G,SAC5F;UAED,KAAKoG,aAAA,CAAcd,IAAA,EAAMC,QAAA,EAAUU,UAAU;QACzD,OAAiB;UACL,IAAII,eAAA,GAAkB,KAAK/D,cAAA,CAAgB;UAC3CgD,IAAA,GAAO,IAAIgB,IAAA,CACTD,eAAA,EACA,KAAKhN,WAAA,CAAY2G,SAAA,CAAUlM,MAAA,KAAW,IAAI,KAAKuF,WAAA,CAAY2G,SAAA,CAAU,CAAC,IAAI,KAAK3G,WAAA,CAAY2G,SAC5F;QACF;QAEDsF,IAAA,CAAKzP,IAAA,GAAO,KAAKwD,WAAA,CAAYxD,IAAA;QAC7B,IAAI0Q,WAAA,GAAc,IAAItF,OAAA,CAAS;QAC/B,IAAIuF,cAAA,GAAiB,KAAKnN,WAAA,CAAY8G,SAAA,CAAUoB,OAAA;QAChD,IAAIiF,cAAA,IAAkBA,cAAA,CAAe3P,MAAA,EAAQ;UAC3C,OAAO,MAAM;YACX2P,cAAA,GAAiBA,cAAA,CAAe3P,MAAA;YAChC,IAAI2P,cAAA,EAAgB;cAClBD,WAAA,CAAYE,QAAA,CAASD,cAAA,CAAexF,oBAAoB;YACxE,OAAqB;cACL;YACD;UACF;UAEDsE,IAAA,CAAKjE,YAAA,CAAakF,WAAW;QAC9B;QAED,KAAKrN,MAAA,CAAO5C,IAAA,CAAKgP,IAAI;MACtB;IACF,GACD;MACEjR,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASiQ,cAAcd,IAAA,EAAMoB,QAAA,EAAUC,YAAA,EAAc;QAC1D,IAAIC,KAAA,GAAQ,EAAE;UACZC,IAAA;UACAC,KAAA;QACF,IAAIjT,EAAA,EAAGkT,EAAA;QAEP,KAAKlT,EAAA,GAAI,GAAGkT,EAAA,GAAKL,QAAA,CAAS5S,MAAA,EAAQD,EAAA,GAAIkT,EAAA,EAAIlT,EAAA,IAAK;UAC7CiT,KAAA,GAAQJ,QAAA,CAAS7S,EAAC;UAElBgT,IAAA,GAAO,IAAIzF,IAAA,CAAM;UACjBwF,KAAA,CAAMtQ,IAAA,CAAKuQ,IAAI;UAEfA,IAAA,CAAKhR,IAAA,GAAOiR,KAAA,CAAMjR,IAAA;UAClBgR,IAAA,CAAKpE,QAAA,CAASuE,SAAA,CAAUF,KAAA,CAAM7P,GAAG;UACjC4P,IAAA,CAAKI,UAAA,CAAWD,SAAA,CAAUF,KAAA,CAAMzB,IAAI;UACpC,IAAIyB,KAAA,CAAM3P,GAAA,KAAQ,QAAW0P,IAAA,CAAKK,KAAA,CAAMF,SAAA,CAAUF,KAAA,CAAM3P,GAAG;QAC5D;QAED,KAAKtD,EAAA,GAAI,GAAGkT,EAAA,GAAKL,QAAA,CAAS5S,MAAA,EAAQD,EAAA,GAAIkT,EAAA,EAAIlT,EAAA,IAAK;UAC7CiT,KAAA,GAAQJ,QAAA,CAAS7S,EAAC;UAElB,IAAIiT,KAAA,CAAMjQ,MAAA,KAAW,MAAMiQ,KAAA,CAAMjQ,MAAA,KAAW,QAAQ+P,KAAA,CAAME,KAAA,CAAMjQ,MAAM,MAAM,QAAW;YACrF+P,KAAA,CAAME,KAAA,CAAMjQ,MAAM,EAAE4K,GAAA,CAAImF,KAAA,CAAM/S,EAAC,CAAC;UAC9C,OAAmB;YACLyR,IAAA,CAAK7D,GAAA,CAAImF,KAAA,CAAM/S,EAAC,CAAC;UAClB;QACF;QAEDyR,IAAA,CAAK6B,iBAAA,CAAkB,IAAI;QAE3B,IAAIC,QAAA,GAAW,IAAIC,QAAA,CAAST,KAAA,EAAOD,YAAY;QAC/CrB,IAAA,CAAKgC,IAAA,CAAKF,QAAA,EAAU9B,IAAA,CAAKhE,WAAW;MACrC;IACF,GACD;MACEjN,GAAA,EAAK;MACL8B,KAAA,EAAO,SAASwK,kBAAA,EAAoB;QAClC,IAAIxD,OAAA,GAAU;QACd,IAAIhC,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACxD,IAAIlC,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAA,EAAShC,IAAA,GAAOgC,OAAO;QAClEA,OAAA,GAAUhC,IAAA,GAAO;QACjB,IAAIoM,UAAA,GAAarI,QAAA,CAAS,KAAKlE,SAAA,CAAUC,IAAI,CAAC;QAC9CE,IAAA,GAAO,KAAKtC,cAAA,CAAe8D,IAAA,CAAKvB,OAAA,CAAQ,KAAK+B,OAAO;QACpDA,OAAA,GAAUhC,IAAA,GAAO;QACjBF,IAAA,GAAO,KAAKpC,cAAA,CAAe8D,IAAA,CAAKrB,MAAA,CAAO6B,OAAO;QAC9C,IAAIR,IAAA,GAAO,KAAK3B,SAAA,CAAUC,IAAA,CAAK8C,IAAA,EAAM,EAAEE,KAAA,CAAM,KAAK;QAClD,SAASpK,EAAA,GAAI,GAAGA,EAAA,GAAI8I,IAAA,CAAK7I,MAAA,EAAQD,EAAA,IAAK;UACpC,IAAImQ,KAAA,GAAQrH,IAAA,CAAK9I,EAAC,EAAEoK,KAAA,CAAM,GAAG;UAC7B,IAAIuJ,OAAA,GAAU,IAAI9P,aAAA,CAAe;UACjC8P,OAAA,CAAQtJ,IAAA,GAAOqJ,UAAA;UACfC,OAAA,CAAQ3P,KAAA,GAAQqH,QAAA,CAAS8E,KAAA,CAAM,CAAC,CAAC;UACjCwD,OAAA,CAAQ5P,KAAA,GAAQ,KAAK2B,mBAAA,CAAoBhE,SAAA,CAAUzB,MAAA;UACnD0T,OAAA,CAAQ/Q,IAAA,GAAO+Q,OAAA,CAAQ3P,KAAA;UACvB,IAAI0P,UAAA,IAAc,GAAG;YACnB,IAAIE,UAAA,GAAa;YACjB,SAASC,EAAA,GAAK,GAAGA,EAAA,GAAK,KAAKnO,mBAAA,CAAoBhE,SAAA,CAAUzB,MAAA,EAAQ4T,EAAA,IAAM;cACrE,IAAI,KAAKnO,mBAAA,CAAoBhE,SAAA,CAAUmS,EAAE,EAAE7P,KAAA,KAAU2P,OAAA,CAAQ3P,KAAA,EAAO;gBAClE2P,OAAA,GAAU,KAAKjO,mBAAA,CAAoBhE,SAAA,CAAUmS,EAAE;gBAC/CD,UAAA,GAAa;gBACb;cACD;YACF;YAED,IAAIE,UAAA,GAAa3D,KAAA,CAAM,CAAC,EAAE/F,KAAA,CAAM,GAAG;YACnC,QAAQsJ,UAAA;cACN,KAAK;gBACHC,OAAA,CAAQtQ,GAAA,GAAM,IAAIK,UAAA,CAChB8K,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,GACxBtF,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,GACxBtF,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,GACxBtF,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,IAAI,EAC7B;gBACD;cACF,KAAK;gBACHH,OAAA,CAAQrQ,GAAA,GAAM,IAAIE,OAAA,CAChBgL,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,GACxBtF,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,GACxBtF,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,CACzB;gBACD;cACF,KAAK;gBACHH,OAAA,CAAQvQ,GAAA,GAAM,IAAII,OAAA,CAChBgL,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,GACxBtF,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,GACxBtF,UAAA,CAAWsF,UAAA,CAAW,CAAC,CAAC,CACzB;gBACD;YACH;YAED,IAAI,CAACF,UAAA,EAAY;cACf,KAAKlO,mBAAA,CAAoBhE,SAAA,CAAUe,IAAA,CAAKkR,OAAO;YAChD;UACf,OAAmB;YACLA,OAAA,CAAQpQ,MAAA,GAAS,IAAI6J,OAAA,CAAS;YAC9B,KAAKC,gBAAA,CAAiBsG,OAAA,CAAQpQ,MAAA,EAAQ4M,KAAA,CAAM,CAAC,EAAE/F,KAAA,CAAM,GAAG,CAAC;YACzD,KAAK1E,mBAAA,CAAoBhE,SAAA,CAAUe,IAAA,CAAKkR,OAAO;UAChD;QACF;MACF;IACF,GACD;MACEnT,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS2K,qBAAA,EAAuB;QACrC,IAAI8G,YAAA,GAAe,IAAInS,aAAA,CAAc,KAAKmE,OAAO;QACjDgO,YAAA,CAAahS,GAAA,GAAM,KAAKwD,kBAAA;QACxBwO,YAAA,CAAa/R,IAAA,GAAO,KAAKyD,aAAA,CAAczD,IAAA;QACvC+R,YAAA,CAAaxR,IAAA,CAAK,KAAKkD,aAAA,CAAcoH,WAAW;QAChD,KAAKvH,UAAA,CAAW7C,IAAA,CAAKsR,YAAY;MAClC;IACF,GACD;MACEvT,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS0R,gBAAgBC,MAAA,EAAQC,UAAA,EAAY;QAClD,IAAIC,KAAA,GAAQF,MAAA;QACZ,IAAIG,SAAA,GAAYF,UAAA;QAChB,IAAI,CAACC,KAAA,EAAO;UACVA,KAAA,GAAQ,KAAK9O,MAAA,CAAO,CAAC;QACtB;QAED,IAAI,CAAC+O,SAAA,EAAW;UACdA,SAAA,GAAY,KAAK9O,UAAA,CAAW,CAAC;QAC9B;QAED,IAAI,CAAC6O,KAAA,IAAS,CAACC,SAAA,EAAW;UACxB,OAAO;QACR;QAED,IAAIC,GAAA,GAAM,CAAE;QACZA,GAAA,CAAItS,GAAA,GAAMqS,SAAA,CAAUrS,GAAA;QACpBsS,GAAA,CAAIrS,IAAA,GAAOoS,SAAA,CAAUpS,IAAA;QACrBqS,GAAA,CAAIpU,MAAA,GAASmU,SAAA,CAAUnU,MAAA;QACvBoU,GAAA,CAAIpS,SAAA,GAAY,EAAE;QAClB,SAASqL,CAAA,GAAI,GAAGA,CAAA,GAAI6G,KAAA,CAAMZ,QAAA,CAASR,KAAA,CAAM9S,MAAA,EAAQqN,CAAA,IAAK;UACpD,IAAIgH,aAAA,GAAgB;UACpB,SAAStU,EAAA,GAAI,GAAGA,EAAA,GAAIoU,SAAA,CAAUnS,SAAA,CAAUhC,MAAA,EAAQD,EAAA,IAAK;YACnD,IAAImU,KAAA,CAAMZ,QAAA,CAASR,KAAA,CAAMzF,CAAC,EAAEtL,IAAA,KAASoS,SAAA,CAAUnS,SAAA,CAAUjC,EAAC,EAAEgC,IAAA,EAAM;cAChEsS,aAAA,GAAgB;cAChB,IAAIC,KAAA,GAAQH,SAAA,CAAUnS,SAAA,CAAUjC,EAAC,EAAEkD,IAAA,CAAM;cACzCqR,KAAA,CAAMvR,MAAA,GAAS;cACf,IAAImR,KAAA,CAAMZ,QAAA,CAASR,KAAA,CAAMzF,CAAC,EAAEtK,MAAA,IAAUmR,KAAA,CAAMZ,QAAA,CAASR,KAAA,CAAMzF,CAAC,EAAEtK,MAAA,CAAOqH,IAAA,KAAS,QAAQ;gBACpF,SAASwH,EAAA,GAAK,GAAGA,EAAA,GAAKwC,GAAA,CAAIpS,SAAA,CAAUhC,MAAA,EAAQ4R,EAAA,IAAM;kBAChD,IAAIwC,GAAA,CAAIpS,SAAA,CAAU4P,EAAE,EAAE7P,IAAA,KAASmS,KAAA,CAAMZ,QAAA,CAASR,KAAA,CAAMzF,CAAC,EAAEtK,MAAA,CAAOhB,IAAA,EAAM;oBAClEuS,KAAA,CAAMvR,MAAA,GAAS6O,EAAA;oBACf0C,KAAA,CAAM/I,UAAA,GAAa2I,KAAA,CAAMZ,QAAA,CAASR,KAAA,CAAMzF,CAAC,EAAEtK,MAAA,CAAOhB,IAAA;kBACnD;gBACF;cACF;cAEDqS,GAAA,CAAIpS,SAAA,CAAUQ,IAAA,CAAK8R,KAAK;cACxB;YACD;UACF;UAED,IAAI,CAACD,aAAA,EAAe;YAClB,IAAIE,MAAA,GAASJ,SAAA,CAAUnS,SAAA,CAAU,CAAC,EAAEiB,IAAA,CAAM;YAC1CsR,MAAA,CAAOxS,IAAA,GAAOmS,KAAA,CAAMZ,QAAA,CAASR,KAAA,CAAMzF,CAAC,EAAEtL,IAAA;YACtCwS,MAAA,CAAOxR,MAAA,GAAS;YAChB,SAASyR,CAAA,GAAI,GAAGA,CAAA,GAAID,MAAA,CAAO7R,IAAA,CAAK1C,MAAA,EAAQwU,CAAA,IAAK;cAC3C,IAAID,MAAA,CAAO7R,IAAA,CAAK8R,CAAC,EAAErR,GAAA,EAAK;gBACtBoR,MAAA,CAAO7R,IAAA,CAAK8R,CAAC,EAAErR,GAAA,CAAIsR,GAAA,CAAI,GAAG,GAAG,CAAC;cAC/B;cAED,IAAIF,MAAA,CAAO7R,IAAA,CAAK8R,CAAC,EAAEnR,GAAA,EAAK;gBACtBkR,MAAA,CAAO7R,IAAA,CAAK8R,CAAC,EAAEnR,GAAA,CAAIoR,GAAA,CAAI,GAAG,GAAG,CAAC;cAC/B;cAED,IAAIF,MAAA,CAAO7R,IAAA,CAAK8R,CAAC,EAAEpR,GAAA,EAAK;gBACtBmR,MAAA,CAAO7R,IAAA,CAAK8R,CAAC,EAAEpR,GAAA,CAAIqR,GAAA,CAAI,GAAG,GAAG,GAAG,CAAC;cAClC;YACF;YAEDL,GAAA,CAAIpS,SAAA,CAAUQ,IAAA,CAAK+R,MAAM;UAC1B;QACF;QAED,IAAI,CAACL,KAAA,CAAMQ,QAAA,CAASrP,UAAA,EAAY;UAC9B6O,KAAA,CAAMQ,QAAA,CAASrP,UAAA,GAAa,EAAE;QAC/B;QAED6O,KAAA,CAAMQ,QAAA,CAASrP,UAAA,CAAW7C,IAAA,CAAKmS,aAAA,CAAcC,cAAA,CAAeR,GAAA,EAAKF,KAAA,CAAMZ,QAAA,CAASR,KAAK,CAAC;QACtF,IAAI,CAACoB,KAAA,CAAMW,cAAA,EAAgB;UACzBX,KAAA,CAAMW,cAAA,GAAiB,IAAIC,cAAA,CAAeZ,KAAK;QAChD;QAED,OAAOE,GAAA;MACR;IACF,GACD;MACE7T,GAAA,EAAK;MACL8B,KAAA,EAAO,SAAS+K,iBAAiB2H,YAAA,EAAclM,IAAA,EAAM;QACnDkM,YAAA,CAAaN,GAAA,CACXlG,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,EAAE,CAAC,GACnB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,EAAE,CAAC,GACnB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,EAAE,CAAC,GACnB0F,UAAA,CAAW1F,IAAA,CAAK,EAAE,CAAC,GACnB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,CAAC,CAAC,GAClB0F,UAAA,CAAW1F,IAAA,CAAK,EAAE,CAAC,GACnB0F,UAAA,CAAW1F,IAAA,CAAK,EAAE,CAAC,CACpB;MACF;IACF,EACF;IACD,OAAO5E,QAAA;EACX,EAAM;EAEJ,OAAOD,QAAA;AACT,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}