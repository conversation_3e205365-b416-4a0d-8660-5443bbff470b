{"ast": null, "code": "import { Loader, LoaderUtils, FileLoader, Color, SpotLight, PointLight, DirectionalLight, MeshBasicMaterial, MeshPhysicalMaterial, Vector2, Matrix4, Vector3, Quaternion, InstancedMesh, InstancedBufferAttribute, Object3D, TextureLoader, ImageBitmapLoader, BufferAttribute, InterleavedBuffer, InterleavedBufferAttribute, LinearFilter, LinearMipmapLinearFilter, RepeatWrapping, PointsMaterial, Material, LineBasicMaterial, MeshStandardMaterial, DoubleSide, PropertyBinding, BufferGeometry, SkinnedMesh, Mesh, TriangleStripDrawMode, TriangleFanDrawMode, LineSegments, Line, LineLoop, Points, Group, PerspectiveCamera, MathUtils, OrthographicCamera, Skeleton, AnimationClip, Bone, InterpolateLinear, NearestFilter, NearestMipmapNearestFilter, LinearMipmapNearestFilter, NearestMipmapLinearFilter, ClampToEdgeWrapping, MirroredRepeatWrapping, InterpolateDiscrete, FrontSide, Texture, VectorKeyframeTrack, NumberKeyframeTrack, QuaternionKeyframeTrack, Box3, Sphere, Interpolant } from \"three\";\nimport { toTrianglesDrawMode } from \"../utils/BufferGeometryUtils.js\";\nimport { version } from \"../_polyfill/constants.js\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nconst SRGBColorSpace = \"srgb\";\nconst LinearSRGBColorSpace = \"srgb-linear\";\nconst sRGBEncoding = 3001;\nconst LinearEncoding = 3e3;\nclass GLTFLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.dracoLoader = null;\n    this.ktx2Loader = null;\n    this.meshoptDecoder = null;\n    this.pluginCallbacks = [];\n    this.register(function (parser) {\n      return new GLTFMaterialsClearcoatExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsDispersionExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureBasisUExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureWebPExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureAVIFExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSheenExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsTransmissionExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsVolumeExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIorExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsEmissiveStrengthExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSpecularExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIridescenceExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsAnisotropyExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsBumpExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFLightsExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshoptCompression(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshGpuInstancing(parser);\n    });\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    let resourcePath;\n    if (this.resourcePath !== \"\") {\n      resourcePath = this.resourcePath;\n    } else if (this.path !== \"\") {\n      const relativeUrl = LoaderUtils.extractUrlBase(url);\n      resourcePath = LoaderUtils.resolveURL(relativeUrl, this.path);\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url);\n    }\n    this.manager.itemStart(url);\n    const _onError = function (e) {\n      if (onError) {\n        onError(e);\n      } else {\n        console.error(e);\n      }\n      scope.manager.itemError(url);\n      scope.manager.itemEnd(url);\n    };\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (data) {\n      try {\n        scope.parse(data, resourcePath, function (gltf) {\n          onLoad(gltf);\n          scope.manager.itemEnd(url);\n        }, _onError);\n      } catch (e) {\n        _onError(e);\n      }\n    }, onProgress, _onError);\n  }\n  setDRACOLoader(dracoLoader) {\n    this.dracoLoader = dracoLoader;\n    return this;\n  }\n  setDDSLoader() {\n    throw new Error('THREE.GLTFLoader: \"MSFT_texture_dds\" no longer supported. Please update to \"KHR_texture_basisu\".');\n  }\n  setKTX2Loader(ktx2Loader) {\n    this.ktx2Loader = ktx2Loader;\n    return this;\n  }\n  setMeshoptDecoder(meshoptDecoder) {\n    this.meshoptDecoder = meshoptDecoder;\n    return this;\n  }\n  register(callback) {\n    if (this.pluginCallbacks.indexOf(callback) === -1) {\n      this.pluginCallbacks.push(callback);\n    }\n    return this;\n  }\n  unregister(callback) {\n    if (this.pluginCallbacks.indexOf(callback) !== -1) {\n      this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1);\n    }\n    return this;\n  }\n  parse(data, path, onLoad, onError) {\n    let json;\n    const extensions = {};\n    const plugins = {};\n    if (typeof data === \"string\") {\n      json = JSON.parse(data);\n    } else if (data instanceof ArrayBuffer) {\n      const magic = decodeText(new Uint8Array(data.slice(0, 4)));\n      if (magic === BINARY_EXTENSION_HEADER_MAGIC) {\n        try {\n          extensions[EXTENSIONS.KHR_BINARY_GLTF] = new GLTFBinaryExtension(data);\n        } catch (error) {\n          if (onError) onError(error);\n          return;\n        }\n        json = JSON.parse(extensions[EXTENSIONS.KHR_BINARY_GLTF].content);\n      } else {\n        json = JSON.parse(decodeText(new Uint8Array(data)));\n      }\n    } else {\n      json = data;\n    }\n    if (json.asset === void 0 || json.asset.version[0] < 2) {\n      if (onError) onError(new Error(\"THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.\"));\n      return;\n    }\n    const parser = new GLTFParser(json, {\n      path: path || this.resourcePath || \"\",\n      crossOrigin: this.crossOrigin,\n      requestHeader: this.requestHeader,\n      manager: this.manager,\n      ktx2Loader: this.ktx2Loader,\n      meshoptDecoder: this.meshoptDecoder\n    });\n    parser.fileLoader.setRequestHeader(this.requestHeader);\n    for (let i = 0; i < this.pluginCallbacks.length; i++) {\n      const plugin = this.pluginCallbacks[i](parser);\n      if (!plugin.name) console.error(\"THREE.GLTFLoader: Invalid plugin found: missing name\");\n      plugins[plugin.name] = plugin;\n      extensions[plugin.name] = true;\n    }\n    if (json.extensionsUsed) {\n      for (let i = 0; i < json.extensionsUsed.length; ++i) {\n        const extensionName = json.extensionsUsed[i];\n        const extensionsRequired = json.extensionsRequired || [];\n        switch (extensionName) {\n          case EXTENSIONS.KHR_MATERIALS_UNLIT:\n            extensions[extensionName] = new GLTFMaterialsUnlitExtension();\n            break;\n          case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n            extensions[extensionName] = new GLTFDracoMeshCompressionExtension(json, this.dracoLoader);\n            break;\n          case EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n            extensions[extensionName] = new GLTFTextureTransformExtension();\n            break;\n          case EXTENSIONS.KHR_MESH_QUANTIZATION:\n            extensions[extensionName] = new GLTFMeshQuantizationExtension();\n            break;\n          default:\n            if (extensionsRequired.indexOf(extensionName) >= 0 && plugins[extensionName] === void 0) {\n              console.warn('THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".');\n            }\n        }\n      }\n    }\n    parser.setExtensions(extensions);\n    parser.setPlugins(plugins);\n    parser.parse(onLoad, onError);\n  }\n  parseAsync(data, path) {\n    const scope = this;\n    return new Promise(function (resolve, reject) {\n      scope.parse(data, path, resolve, reject);\n    });\n  }\n}\nfunction GLTFRegistry() {\n  let objects = {};\n  return {\n    get: function (key) {\n      return objects[key];\n    },\n    add: function (key, object) {\n      objects[key] = object;\n    },\n    remove: function (key) {\n      delete objects[key];\n    },\n    removeAll: function () {\n      objects = {};\n    }\n  };\n}\nconst EXTENSIONS = {\n  KHR_BINARY_GLTF: \"KHR_binary_glTF\",\n  KHR_DRACO_MESH_COMPRESSION: \"KHR_draco_mesh_compression\",\n  KHR_LIGHTS_PUNCTUAL: \"KHR_lights_punctual\",\n  KHR_MATERIALS_CLEARCOAT: \"KHR_materials_clearcoat\",\n  KHR_MATERIALS_DISPERSION: \"KHR_materials_dispersion\",\n  KHR_MATERIALS_IOR: \"KHR_materials_ior\",\n  KHR_MATERIALS_SHEEN: \"KHR_materials_sheen\",\n  KHR_MATERIALS_SPECULAR: \"KHR_materials_specular\",\n  KHR_MATERIALS_TRANSMISSION: \"KHR_materials_transmission\",\n  KHR_MATERIALS_IRIDESCENCE: \"KHR_materials_iridescence\",\n  KHR_MATERIALS_ANISOTROPY: \"KHR_materials_anisotropy\",\n  KHR_MATERIALS_UNLIT: \"KHR_materials_unlit\",\n  KHR_MATERIALS_VOLUME: \"KHR_materials_volume\",\n  KHR_TEXTURE_BASISU: \"KHR_texture_basisu\",\n  KHR_TEXTURE_TRANSFORM: \"KHR_texture_transform\",\n  KHR_MESH_QUANTIZATION: \"KHR_mesh_quantization\",\n  KHR_MATERIALS_EMISSIVE_STRENGTH: \"KHR_materials_emissive_strength\",\n  EXT_MATERIALS_BUMP: \"EXT_materials_bump\",\n  EXT_TEXTURE_WEBP: \"EXT_texture_webp\",\n  EXT_TEXTURE_AVIF: \"EXT_texture_avif\",\n  EXT_MESHOPT_COMPRESSION: \"EXT_meshopt_compression\",\n  EXT_MESH_GPU_INSTANCING: \"EXT_mesh_gpu_instancing\"\n};\nclass GLTFLightsExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;\n    this.cache = {\n      refs: {},\n      uses: {}\n    };\n  }\n  _markDefs() {\n    const parser = this.parser;\n    const nodeDefs = this.parser.json.nodes || [];\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.extensions && nodeDef.extensions[this.name] && nodeDef.extensions[this.name].light !== void 0) {\n        parser._addNodeRef(this.cache, nodeDef.extensions[this.name].light);\n      }\n    }\n  }\n  _loadLight(lightIndex) {\n    const parser = this.parser;\n    const cacheKey = \"light:\" + lightIndex;\n    let dependency = parser.cache.get(cacheKey);\n    if (dependency) return dependency;\n    const json = parser.json;\n    const extensions = json.extensions && json.extensions[this.name] || {};\n    const lightDefs = extensions.lights || [];\n    const lightDef = lightDefs[lightIndex];\n    let lightNode;\n    const color = new Color(16777215);\n    if (lightDef.color !== void 0) color.setRGB(lightDef.color[0], lightDef.color[1], lightDef.color[2], LinearSRGBColorSpace);\n    const range = lightDef.range !== void 0 ? lightDef.range : 0;\n    switch (lightDef.type) {\n      case \"directional\":\n        lightNode = new DirectionalLight(color);\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      case \"point\":\n        lightNode = new PointLight(color);\n        lightNode.distance = range;\n        break;\n      case \"spot\":\n        lightNode = new SpotLight(color);\n        lightNode.distance = range;\n        lightDef.spot = lightDef.spot || {};\n        lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== void 0 ? lightDef.spot.innerConeAngle : 0;\n        lightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== void 0 ? lightDef.spot.outerConeAngle : Math.PI / 4;\n        lightNode.angle = lightDef.spot.outerConeAngle;\n        lightNode.penumbra = 1 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      default:\n        throw new Error(\"THREE.GLTFLoader: Unexpected light type: \" + lightDef.type);\n    }\n    lightNode.position.set(0, 0, 0);\n    lightNode.decay = 2;\n    assignExtrasToUserData(lightNode, lightDef);\n    if (lightDef.intensity !== void 0) lightNode.intensity = lightDef.intensity;\n    lightNode.name = parser.createUniqueName(lightDef.name || \"light_\" + lightIndex);\n    dependency = Promise.resolve(lightNode);\n    parser.cache.add(cacheKey, dependency);\n    return dependency;\n  }\n  getDependency(type, index) {\n    if (type !== \"light\") return;\n    return this._loadLight(index);\n  }\n  createNodeAttachment(nodeIndex) {\n    const self2 = this;\n    const parser = this.parser;\n    const json = parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    const lightDef = nodeDef.extensions && nodeDef.extensions[this.name] || {};\n    const lightIndex = lightDef.light;\n    if (lightIndex === void 0) return null;\n    return this._loadLight(lightIndex).then(function (light) {\n      return parser._getNodeRef(self2.cache, lightIndex, light);\n    });\n  }\n}\nclass GLTFMaterialsUnlitExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MATERIALS_UNLIT;\n  }\n  getMaterialType() {\n    return MeshBasicMaterial;\n  }\n  extendParams(materialParams, materialDef, parser) {\n    const pending = [];\n    materialParams.color = new Color(1, 1, 1);\n    materialParams.opacity = 1;\n    const metallicRoughness = materialDef.pbrMetallicRoughness;\n    if (metallicRoughness) {\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== void 0) {\n        pending.push(parser.assignTexture(materialParams, \"map\", metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const emissiveStrength = materialDef.extensions[this.name].emissiveStrength;\n    if (emissiveStrength !== void 0) {\n      materialParams.emissiveIntensity = emissiveStrength;\n    }\n    return Promise.resolve();\n  }\n}\nclass GLTFMaterialsClearcoatExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.clearcoatFactor !== void 0) {\n      materialParams.clearcoat = extension.clearcoatFactor;\n    }\n    if (extension.clearcoatTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"clearcoatMap\", extension.clearcoatTexture));\n    }\n    if (extension.clearcoatRoughnessFactor !== void 0) {\n      materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;\n    }\n    if (extension.clearcoatRoughnessTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"clearcoatRoughnessMap\", extension.clearcoatRoughnessTexture));\n    }\n    if (extension.clearcoatNormalTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"clearcoatNormalMap\", extension.clearcoatNormalTexture));\n      if (extension.clearcoatNormalTexture.scale !== void 0) {\n        const scale = extension.clearcoatNormalTexture.scale;\n        materialParams.clearcoatNormalScale = new Vector2(scale, scale);\n      }\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsDispersionExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_DISPERSION;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const extension = materialDef.extensions[this.name];\n    materialParams.dispersion = extension.dispersion !== void 0 ? extension.dispersion : 0;\n    return Promise.resolve();\n  }\n}\nclass GLTFMaterialsIridescenceExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.iridescenceFactor !== void 0) {\n      materialParams.iridescence = extension.iridescenceFactor;\n    }\n    if (extension.iridescenceTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"iridescenceMap\", extension.iridescenceTexture));\n    }\n    if (extension.iridescenceIor !== void 0) {\n      materialParams.iridescenceIOR = extension.iridescenceIor;\n    }\n    if (materialParams.iridescenceThicknessRange === void 0) {\n      materialParams.iridescenceThicknessRange = [100, 400];\n    }\n    if (extension.iridescenceThicknessMinimum !== void 0) {\n      materialParams.iridescenceThicknessRange[0] = extension.iridescenceThicknessMinimum;\n    }\n    if (extension.iridescenceThicknessMaximum !== void 0) {\n      materialParams.iridescenceThicknessRange[1] = extension.iridescenceThicknessMaximum;\n    }\n    if (extension.iridescenceThicknessTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"iridescenceThicknessMap\", extension.iridescenceThicknessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsSheenExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SHEEN;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    materialParams.sheenColor = new Color(0, 0, 0);\n    materialParams.sheenRoughness = 0;\n    materialParams.sheen = 1;\n    const extension = materialDef.extensions[this.name];\n    if (extension.sheenColorFactor !== void 0) {\n      const colorFactor = extension.sheenColorFactor;\n      materialParams.sheenColor.setRGB(colorFactor[0], colorFactor[1], colorFactor[2], LinearSRGBColorSpace);\n    }\n    if (extension.sheenRoughnessFactor !== void 0) {\n      materialParams.sheenRoughness = extension.sheenRoughnessFactor;\n    }\n    if (extension.sheenColorTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"sheenColorMap\", extension.sheenColorTexture, SRGBColorSpace));\n    }\n    if (extension.sheenRoughnessTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"sheenRoughnessMap\", extension.sheenRoughnessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsTransmissionExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.transmissionFactor !== void 0) {\n      materialParams.transmission = extension.transmissionFactor;\n    }\n    if (extension.transmissionTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"transmissionMap\", extension.transmissionTexture));\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsVolumeExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_VOLUME;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.thickness = extension.thicknessFactor !== void 0 ? extension.thicknessFactor : 0;\n    if (extension.thicknessTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"thicknessMap\", extension.thicknessTexture));\n    }\n    materialParams.attenuationDistance = extension.attenuationDistance || Infinity;\n    const colorArray = extension.attenuationColor || [1, 1, 1];\n    materialParams.attenuationColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace);\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsIorExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IOR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const extension = materialDef.extensions[this.name];\n    materialParams.ior = extension.ior !== void 0 ? extension.ior : 1.5;\n    return Promise.resolve();\n  }\n}\nclass GLTFMaterialsSpecularExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.specularIntensity = extension.specularFactor !== void 0 ? extension.specularFactor : 1;\n    if (extension.specularTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"specularIntensityMap\", extension.specularTexture));\n    }\n    const colorArray = extension.specularColorFactor || [1, 1, 1];\n    materialParams.specularColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace);\n    if (extension.specularColorTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"specularColorMap\", extension.specularColorTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsBumpExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_MATERIALS_BUMP;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.bumpScale = extension.bumpFactor !== void 0 ? extension.bumpFactor : 1;\n    if (extension.bumpTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"bumpMap\", extension.bumpTexture));\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.anisotropyStrength !== void 0) {\n      materialParams.anisotropy = extension.anisotropyStrength;\n    }\n    if (extension.anisotropyRotation !== void 0) {\n      materialParams.anisotropyRotation = extension.anisotropyRotation;\n    }\n    if (extension.anisotropyTexture !== void 0) {\n      pending.push(parser.assignTexture(materialParams, \"anisotropyMap\", extension.anisotropyTexture));\n    }\n    return Promise.all(pending);\n  }\n}\nclass GLTFTextureBasisUExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_TEXTURE_BASISU;\n  }\n  loadTexture(textureIndex) {\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[this.name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[this.name];\n    const loader = parser.options.ktx2Loader;\n    if (!loader) {\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n        throw new Error(\"THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures\");\n      } else {\n        return null;\n      }\n    }\n    return parser.loadTextureImage(textureIndex, extension.source, loader);\n  }\n}\nclass GLTFTextureWebPExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_WEBP;\n    this.isSupported = null;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader);\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error(\"THREE.GLTFLoader: WebP required by asset but unsupported.\");\n      }\n      return parser.loadTexture(textureIndex);\n    });\n  }\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image();\n        image.src = \"data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA\";\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1);\n        };\n      });\n    }\n    return this.isSupported;\n  }\n}\nclass GLTFTextureAVIFExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_AVIF;\n    this.isSupported = null;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader);\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error(\"THREE.GLTFLoader: AVIF required by asset but unsupported.\");\n      }\n      return parser.loadTexture(textureIndex);\n    });\n  }\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image();\n        image.src = \"data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI=\";\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1);\n        };\n      });\n    }\n    return this.isSupported;\n  }\n}\nclass GLTFMeshoptCompression {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;\n    this.parser = parser;\n  }\n  loadBufferView(index) {\n    const json = this.parser.json;\n    const bufferView = json.bufferViews[index];\n    if (bufferView.extensions && bufferView.extensions[this.name]) {\n      const extensionDef = bufferView.extensions[this.name];\n      const buffer = this.parser.getDependency(\"buffer\", extensionDef.buffer);\n      const decoder = this.parser.options.meshoptDecoder;\n      if (!decoder || !decoder.supported) {\n        if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n          throw new Error(\"THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files\");\n        } else {\n          return null;\n        }\n      }\n      return buffer.then(function (res) {\n        const byteOffset = extensionDef.byteOffset || 0;\n        const byteLength = extensionDef.byteLength || 0;\n        const count = extensionDef.count;\n        const stride = extensionDef.byteStride;\n        const source = new Uint8Array(res, byteOffset, byteLength);\n        if (decoder.decodeGltfBufferAsync) {\n          return decoder.decodeGltfBufferAsync(count, stride, source, extensionDef.mode, extensionDef.filter).then(function (res2) {\n            return res2.buffer;\n          });\n        } else {\n          return decoder.ready.then(function () {\n            const result = new ArrayBuffer(count * stride);\n            decoder.decodeGltfBuffer(new Uint8Array(result), count, stride, source, extensionDef.mode, extensionDef.filter);\n            return result;\n          });\n        }\n      });\n    } else {\n      return null;\n    }\n  }\n}\nclass GLTFMeshGpuInstancing {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING;\n    this.parser = parser;\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    if (!nodeDef.extensions || !nodeDef.extensions[this.name] || nodeDef.mesh === void 0) {\n      return null;\n    }\n    const meshDef = json.meshes[nodeDef.mesh];\n    for (const primitive of meshDef.primitives) {\n      if (primitive.mode !== WEBGL_CONSTANTS.TRIANGLES && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN && primitive.mode !== void 0) {\n        return null;\n      }\n    }\n    const extensionDef = nodeDef.extensions[this.name];\n    const attributesDef = extensionDef.attributes;\n    const pending = [];\n    const attributes = {};\n    for (const key in attributesDef) {\n      pending.push(this.parser.getDependency(\"accessor\", attributesDef[key]).then(accessor => {\n        attributes[key] = accessor;\n        return attributes[key];\n      }));\n    }\n    if (pending.length < 1) {\n      return null;\n    }\n    pending.push(this.parser.createNodeMesh(nodeIndex));\n    return Promise.all(pending).then(results => {\n      const nodeObject = results.pop();\n      const meshes = nodeObject.isGroup ? nodeObject.children : [nodeObject];\n      const count = results[0].count;\n      const instancedMeshes = [];\n      for (const mesh of meshes) {\n        const m = new Matrix4();\n        const p = new Vector3();\n        const q = new Quaternion();\n        const s = new Vector3(1, 1, 1);\n        const instancedMesh = new InstancedMesh(mesh.geometry, mesh.material, count);\n        for (let i = 0; i < count; i++) {\n          if (attributes.TRANSLATION) {\n            p.fromBufferAttribute(attributes.TRANSLATION, i);\n          }\n          if (attributes.ROTATION) {\n            q.fromBufferAttribute(attributes.ROTATION, i);\n          }\n          if (attributes.SCALE) {\n            s.fromBufferAttribute(attributes.SCALE, i);\n          }\n          instancedMesh.setMatrixAt(i, m.compose(p, q, s));\n        }\n        for (const attributeName in attributes) {\n          if (attributeName === \"_COLOR_0\") {\n            const attr = attributes[attributeName];\n            instancedMesh.instanceColor = new InstancedBufferAttribute(attr.array, attr.itemSize, attr.normalized);\n          } else if (attributeName !== \"TRANSLATION\" && attributeName !== \"ROTATION\" && attributeName !== \"SCALE\") {\n            mesh.geometry.setAttribute(attributeName, attributes[attributeName]);\n          }\n        }\n        Object3D.prototype.copy.call(instancedMesh, mesh);\n        this.parser.assignFinalMaterial(instancedMesh);\n        instancedMeshes.push(instancedMesh);\n      }\n      if (nodeObject.isGroup) {\n        nodeObject.clear();\n        nodeObject.add(...instancedMeshes);\n        return nodeObject;\n      }\n      return instancedMeshes[0];\n    });\n  }\n}\nconst BINARY_EXTENSION_HEADER_MAGIC = \"glTF\";\nconst BINARY_EXTENSION_HEADER_LENGTH = 12;\nconst BINARY_EXTENSION_CHUNK_TYPES = {\n  JSON: 1313821514,\n  BIN: 5130562\n};\nclass GLTFBinaryExtension {\n  constructor(data) {\n    this.name = EXTENSIONS.KHR_BINARY_GLTF;\n    this.content = null;\n    this.body = null;\n    const headerView = new DataView(data, 0, BINARY_EXTENSION_HEADER_LENGTH);\n    this.header = {\n      magic: decodeText(new Uint8Array(data.slice(0, 4))),\n      version: headerView.getUint32(4, true),\n      length: headerView.getUint32(8, true)\n    };\n    if (this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC) {\n      throw new Error(\"THREE.GLTFLoader: Unsupported glTF-Binary header.\");\n    } else if (this.header.version < 2) {\n      throw new Error(\"THREE.GLTFLoader: Legacy binary file detected.\");\n    }\n    const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;\n    const chunkView = new DataView(data, BINARY_EXTENSION_HEADER_LENGTH);\n    let chunkIndex = 0;\n    while (chunkIndex < chunkContentsLength) {\n      const chunkLength = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      const chunkType = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON) {\n        const contentArray = new Uint8Array(data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength);\n        this.content = decodeText(contentArray);\n      } else if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN) {\n        const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;\n        this.body = data.slice(byteOffset, byteOffset + chunkLength);\n      }\n      chunkIndex += chunkLength;\n    }\n    if (this.content === null) {\n      throw new Error(\"THREE.GLTFLoader: JSON content not found.\");\n    }\n  }\n}\nclass GLTFDracoMeshCompressionExtension {\n  constructor(json, dracoLoader) {\n    if (!dracoLoader) {\n      throw new Error(\"THREE.GLTFLoader: No DRACOLoader instance provided.\");\n    }\n    this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;\n    this.json = json;\n    this.dracoLoader = dracoLoader;\n    this.dracoLoader.preload();\n  }\n  decodePrimitive(primitive, parser) {\n    const json = this.json;\n    const dracoLoader = this.dracoLoader;\n    const bufferViewIndex = primitive.extensions[this.name].bufferView;\n    const gltfAttributeMap = primitive.extensions[this.name].attributes;\n    const threeAttributeMap = {};\n    const attributeNormalizedMap = {};\n    const attributeTypeMap = {};\n    for (const attributeName in gltfAttributeMap) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      threeAttributeMap[threeAttributeName] = gltfAttributeMap[attributeName];\n    }\n    for (const attributeName in primitive.attributes) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      if (gltfAttributeMap[attributeName] !== void 0) {\n        const accessorDef = json.accessors[primitive.attributes[attributeName]];\n        const componentType = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n        attributeTypeMap[threeAttributeName] = componentType.name;\n        attributeNormalizedMap[threeAttributeName] = accessorDef.normalized === true;\n      }\n    }\n    return parser.getDependency(\"bufferView\", bufferViewIndex).then(function (bufferView) {\n      return new Promise(function (resolve, reject) {\n        dracoLoader.decodeDracoFile(bufferView, function (geometry) {\n          for (const attributeName in geometry.attributes) {\n            const attribute = geometry.attributes[attributeName];\n            const normalized = attributeNormalizedMap[attributeName];\n            if (normalized !== void 0) attribute.normalized = normalized;\n          }\n          resolve(geometry);\n        }, threeAttributeMap, attributeTypeMap, LinearSRGBColorSpace, reject);\n      });\n    });\n  }\n}\nclass GLTFTextureTransformExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;\n  }\n  extendTexture(texture, transform) {\n    if ((transform.texCoord === void 0 || transform.texCoord === texture.channel) && transform.offset === void 0 && transform.rotation === void 0 && transform.scale === void 0) {\n      return texture;\n    }\n    texture = texture.clone();\n    if (transform.texCoord !== void 0) {\n      texture.channel = transform.texCoord;\n    }\n    if (transform.offset !== void 0) {\n      texture.offset.fromArray(transform.offset);\n    }\n    if (transform.rotation !== void 0) {\n      texture.rotation = transform.rotation;\n    }\n    if (transform.scale !== void 0) {\n      texture.repeat.fromArray(transform.scale);\n    }\n    texture.needsUpdate = true;\n    return texture;\n  }\n}\nclass GLTFMeshQuantizationExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MESH_QUANTIZATION;\n  }\n}\nclass GLTFCubicSplineInterpolant extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer);\n  }\n  copySampleValue_(index) {\n    const result = this.resultBuffer,\n      values = this.sampleValues,\n      valueSize = this.valueSize,\n      offset = index * valueSize * 3 + valueSize;\n    for (let i = 0; i !== valueSize; i++) {\n      result[i] = values[offset + i];\n    }\n    return result;\n  }\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer;\n    const values = this.sampleValues;\n    const stride = this.valueSize;\n    const stride2 = stride * 2;\n    const stride3 = stride * 3;\n    const td = t1 - t0;\n    const p = (t - t0) / td;\n    const pp = p * p;\n    const ppp = pp * p;\n    const offset1 = i1 * stride3;\n    const offset0 = offset1 - stride3;\n    const s2 = -2 * ppp + 3 * pp;\n    const s3 = ppp - pp;\n    const s0 = 1 - s2;\n    const s1 = s3 - pp + p;\n    for (let i = 0; i !== stride; i++) {\n      const p0 = values[offset0 + i + stride];\n      const m0 = values[offset0 + i + stride2] * td;\n      const p1 = values[offset1 + i + stride];\n      const m1 = values[offset1 + i] * td;\n      result[i] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;\n    }\n    return result;\n  }\n}\nconst _q = /* @__PURE__ */new Quaternion();\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n  interpolate_(i1, t0, t, t1) {\n    const result = super.interpolate_(i1, t0, t, t1);\n    _q.fromArray(result).normalize().toArray(result);\n    return result;\n  }\n}\nconst WEBGL_CONSTANTS = {\n  FLOAT: 5126,\n  //FLOAT_MAT2: 35674,\n  FLOAT_MAT3: 35675,\n  FLOAT_MAT4: 35676,\n  FLOAT_VEC2: 35664,\n  FLOAT_VEC3: 35665,\n  FLOAT_VEC4: 35666,\n  LINEAR: 9729,\n  REPEAT: 10497,\n  SAMPLER_2D: 35678,\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  UNSIGNED_BYTE: 5121,\n  UNSIGNED_SHORT: 5123\n};\nconst WEBGL_COMPONENT_TYPES = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array\n};\nconst WEBGL_FILTERS = {\n  9728: NearestFilter,\n  9729: LinearFilter,\n  9984: NearestMipmapNearestFilter,\n  9985: LinearMipmapNearestFilter,\n  9986: NearestMipmapLinearFilter,\n  9987: LinearMipmapLinearFilter\n};\nconst WEBGL_WRAPPINGS = {\n  33071: ClampToEdgeWrapping,\n  33648: MirroredRepeatWrapping,\n  10497: RepeatWrapping\n};\nconst WEBGL_TYPE_SIZES = {\n  SCALAR: 1,\n  VEC2: 2,\n  VEC3: 3,\n  VEC4: 4,\n  MAT2: 4,\n  MAT3: 9,\n  MAT4: 16\n};\nconst ATTRIBUTES = {\n  POSITION: \"position\",\n  NORMAL: \"normal\",\n  TANGENT: \"tangent\",\n  // uv => uv1, 4 uv channels\n  // https://github.com/mrdoob/three.js/pull/25943\n  // https://github.com/mrdoob/three.js/pull/25788\n  ...(version >= 152 ? {\n    TEXCOORD_0: \"uv\",\n    TEXCOORD_1: \"uv1\",\n    TEXCOORD_2: \"uv2\",\n    TEXCOORD_3: \"uv3\"\n  } : {\n    TEXCOORD_0: \"uv\",\n    TEXCOORD_1: \"uv2\"\n  }),\n  COLOR_0: \"color\",\n  WEIGHTS_0: \"skinWeight\",\n  JOINTS_0: \"skinIndex\"\n};\nconst PATH_PROPERTIES = {\n  scale: \"scale\",\n  translation: \"position\",\n  rotation: \"quaternion\",\n  weights: \"morphTargetInfluences\"\n};\nconst INTERPOLATION = {\n  CUBICSPLINE: void 0,\n  // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n  // keyframe track will be initialized with a default interpolation type, then modified.\n  LINEAR: InterpolateLinear,\n  STEP: InterpolateDiscrete\n};\nconst ALPHA_MODES = {\n  OPAQUE: \"OPAQUE\",\n  MASK: \"MASK\",\n  BLEND: \"BLEND\"\n};\nfunction createDefaultMaterial(cache) {\n  if (cache[\"DefaultMaterial\"] === void 0) {\n    cache[\"DefaultMaterial\"] = new MeshStandardMaterial({\n      color: 16777215,\n      emissive: 0,\n      metalness: 1,\n      roughness: 1,\n      transparent: false,\n      depthTest: true,\n      side: FrontSide\n    });\n  }\n  return cache[\"DefaultMaterial\"];\n}\nfunction addUnknownExtensionsToUserData(knownExtensions, object, objectDef) {\n  for (const name in objectDef.extensions) {\n    if (knownExtensions[name] === void 0) {\n      object.userData.gltfExtensions = object.userData.gltfExtensions || {};\n      object.userData.gltfExtensions[name] = objectDef.extensions[name];\n    }\n  }\n}\nfunction assignExtrasToUserData(object, gltfDef) {\n  if (gltfDef.extras !== void 0) {\n    if (typeof gltfDef.extras === \"object\") {\n      Object.assign(object.userData, gltfDef.extras);\n    } else {\n      console.warn(\"THREE.GLTFLoader: Ignoring primitive type .extras, \" + gltfDef.extras);\n    }\n  }\n}\nfunction addMorphTargets(geometry, targets, parser) {\n  let hasMorphPosition = false;\n  let hasMorphNormal = false;\n  let hasMorphColor = false;\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (target.POSITION !== void 0) hasMorphPosition = true;\n    if (target.NORMAL !== void 0) hasMorphNormal = true;\n    if (target.COLOR_0 !== void 0) hasMorphColor = true;\n    if (hasMorphPosition && hasMorphNormal && hasMorphColor) break;\n  }\n  if (!hasMorphPosition && !hasMorphNormal && !hasMorphColor) return Promise.resolve(geometry);\n  const pendingPositionAccessors = [];\n  const pendingNormalAccessors = [];\n  const pendingColorAccessors = [];\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (hasMorphPosition) {\n      const pendingAccessor = target.POSITION !== void 0 ? parser.getDependency(\"accessor\", target.POSITION) : geometry.attributes.position;\n      pendingPositionAccessors.push(pendingAccessor);\n    }\n    if (hasMorphNormal) {\n      const pendingAccessor = target.NORMAL !== void 0 ? parser.getDependency(\"accessor\", target.NORMAL) : geometry.attributes.normal;\n      pendingNormalAccessors.push(pendingAccessor);\n    }\n    if (hasMorphColor) {\n      const pendingAccessor = target.COLOR_0 !== void 0 ? parser.getDependency(\"accessor\", target.COLOR_0) : geometry.attributes.color;\n      pendingColorAccessors.push(pendingAccessor);\n    }\n  }\n  return Promise.all([Promise.all(pendingPositionAccessors), Promise.all(pendingNormalAccessors), Promise.all(pendingColorAccessors)]).then(function (accessors) {\n    const morphPositions = accessors[0];\n    const morphNormals = accessors[1];\n    const morphColors = accessors[2];\n    if (hasMorphPosition) geometry.morphAttributes.position = morphPositions;\n    if (hasMorphNormal) geometry.morphAttributes.normal = morphNormals;\n    if (hasMorphColor) geometry.morphAttributes.color = morphColors;\n    geometry.morphTargetsRelative = true;\n    return geometry;\n  });\n}\nfunction updateMorphTargets(mesh, meshDef) {\n  mesh.updateMorphTargets();\n  if (meshDef.weights !== void 0) {\n    for (let i = 0, il = meshDef.weights.length; i < il; i++) {\n      mesh.morphTargetInfluences[i] = meshDef.weights[i];\n    }\n  }\n  if (meshDef.extras && Array.isArray(meshDef.extras.targetNames)) {\n    const targetNames = meshDef.extras.targetNames;\n    if (mesh.morphTargetInfluences.length === targetNames.length) {\n      mesh.morphTargetDictionary = {};\n      for (let i = 0, il = targetNames.length; i < il; i++) {\n        mesh.morphTargetDictionary[targetNames[i]] = i;\n      }\n    } else {\n      console.warn(\"THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.\");\n    }\n  }\n}\nfunction createPrimitiveKey(primitiveDef) {\n  let geometryKey;\n  const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION];\n  if (dracoExtension) {\n    geometryKey = \"draco:\" + dracoExtension.bufferView + \":\" + dracoExtension.indices + \":\" + createAttributesKey(dracoExtension.attributes);\n  } else {\n    geometryKey = primitiveDef.indices + \":\" + createAttributesKey(primitiveDef.attributes) + \":\" + primitiveDef.mode;\n  }\n  if (primitiveDef.targets !== void 0) {\n    for (let i = 0, il = primitiveDef.targets.length; i < il; i++) {\n      geometryKey += \":\" + createAttributesKey(primitiveDef.targets[i]);\n    }\n  }\n  return geometryKey;\n}\nfunction createAttributesKey(attributes) {\n  let attributesKey = \"\";\n  const keys = Object.keys(attributes).sort();\n  for (let i = 0, il = keys.length; i < il; i++) {\n    attributesKey += keys[i] + \":\" + attributes[keys[i]] + \";\";\n  }\n  return attributesKey;\n}\nfunction getNormalizedComponentScale(constructor) {\n  switch (constructor) {\n    case Int8Array:\n      return 1 / 127;\n    case Uint8Array:\n      return 1 / 255;\n    case Int16Array:\n      return 1 / 32767;\n    case Uint16Array:\n      return 1 / 65535;\n    default:\n      throw new Error(\"THREE.GLTFLoader: Unsupported normalized accessor component type.\");\n  }\n}\nfunction getImageURIMimeType(uri) {\n  if (uri.search(/\\.jpe?g($|\\?)/i) > 0 || uri.search(/^data\\:image\\/jpeg/) === 0) return \"image/jpeg\";\n  if (uri.search(/\\.webp($|\\?)/i) > 0 || uri.search(/^data\\:image\\/webp/) === 0) return \"image/webp\";\n  return \"image/png\";\n}\nconst _identityMatrix = /* @__PURE__ */new Matrix4();\nclass GLTFParser {\n  constructor(json = {}, options = {}) {\n    this.json = json;\n    this.extensions = {};\n    this.plugins = {};\n    this.options = options;\n    this.cache = new GLTFRegistry();\n    this.associations = /* @__PURE__ */new Map();\n    this.primitiveCache = {};\n    this.nodeCache = {};\n    this.meshCache = {\n      refs: {},\n      uses: {}\n    };\n    this.cameraCache = {\n      refs: {},\n      uses: {}\n    };\n    this.lightCache = {\n      refs: {},\n      uses: {}\n    };\n    this.sourceCache = {};\n    this.textureCache = {};\n    this.nodeNamesUsed = {};\n    let isSafari = false;\n    let isFirefox = false;\n    let firefoxVersion = -1;\n    if (typeof navigator !== \"undefined\" && typeof navigator.userAgent !== \"undefined\") {\n      isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) === true;\n      isFirefox = navigator.userAgent.indexOf(\"Firefox\") > -1;\n      firefoxVersion = isFirefox ? navigator.userAgent.match(/Firefox\\/([0-9]+)\\./)[1] : -1;\n    }\n    if (typeof createImageBitmap === \"undefined\" || isSafari || isFirefox && firefoxVersion < 98) {\n      this.textureLoader = new TextureLoader(this.options.manager);\n    } else {\n      this.textureLoader = new ImageBitmapLoader(this.options.manager);\n    }\n    this.textureLoader.setCrossOrigin(this.options.crossOrigin);\n    this.textureLoader.setRequestHeader(this.options.requestHeader);\n    this.fileLoader = new FileLoader(this.options.manager);\n    this.fileLoader.setResponseType(\"arraybuffer\");\n    if (this.options.crossOrigin === \"use-credentials\") {\n      this.fileLoader.setWithCredentials(true);\n    }\n  }\n  setExtensions(extensions) {\n    this.extensions = extensions;\n  }\n  setPlugins(plugins) {\n    this.plugins = plugins;\n  }\n  parse(onLoad, onError) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    this.cache.removeAll();\n    this.nodeCache = {};\n    this._invokeAll(function (ext) {\n      return ext._markDefs && ext._markDefs();\n    });\n    Promise.all(this._invokeAll(function (ext) {\n      return ext.beforeRoot && ext.beforeRoot();\n    })).then(function () {\n      return Promise.all([parser.getDependencies(\"scene\"), parser.getDependencies(\"animation\"), parser.getDependencies(\"camera\")]);\n    }).then(function (dependencies) {\n      const result = {\n        scene: dependencies[0][json.scene || 0],\n        scenes: dependencies[0],\n        animations: dependencies[1],\n        cameras: dependencies[2],\n        asset: json.asset,\n        parser,\n        userData: {}\n      };\n      addUnknownExtensionsToUserData(extensions, result, json);\n      assignExtrasToUserData(result, json);\n      return Promise.all(parser._invokeAll(function (ext) {\n        return ext.afterRoot && ext.afterRoot(result);\n      })).then(function () {\n        for (const scene of result.scenes) {\n          scene.updateMatrixWorld();\n        }\n        onLoad(result);\n      });\n    }).catch(onError);\n  }\n  /**\n   * Marks the special nodes/meshes in json for efficient parse.\n   */\n  _markDefs() {\n    const nodeDefs = this.json.nodes || [];\n    const skinDefs = this.json.skins || [];\n    const meshDefs = this.json.meshes || [];\n    for (let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex++) {\n      const joints = skinDefs[skinIndex].joints;\n      for (let i = 0, il = joints.length; i < il; i++) {\n        nodeDefs[joints[i]].isBone = true;\n      }\n    }\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.mesh !== void 0) {\n        this._addNodeRef(this.meshCache, nodeDef.mesh);\n        if (nodeDef.skin !== void 0) {\n          meshDefs[nodeDef.mesh].isSkinnedMesh = true;\n        }\n      }\n      if (nodeDef.camera !== void 0) {\n        this._addNodeRef(this.cameraCache, nodeDef.camera);\n      }\n    }\n  }\n  /**\n   * Counts references to shared node / Object3D resources. These resources\n   * can be reused, or \"instantiated\", at multiple nodes in the scene\n   * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n   * be marked. Non-scenegraph resources (like Materials, Geometries, and\n   * Textures) can be reused directly and are not marked here.\n   *\n   * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n   */\n  _addNodeRef(cache, index) {\n    if (index === void 0) return;\n    if (cache.refs[index] === void 0) {\n      cache.refs[index] = cache.uses[index] = 0;\n    }\n    cache.refs[index]++;\n  }\n  /** Returns a reference to a shared resource, cloning it if necessary. */\n  _getNodeRef(cache, index, object) {\n    if (cache.refs[index] <= 1) return object;\n    const ref = object.clone();\n    const updateMappings = (original, clone) => {\n      const mappings = this.associations.get(original);\n      if (mappings != null) {\n        this.associations.set(clone, mappings);\n      }\n      for (const [i, child] of original.children.entries()) {\n        updateMappings(child, clone.children[i]);\n      }\n    };\n    updateMappings(object, ref);\n    ref.name += \"_instance_\" + cache.uses[index]++;\n    return ref;\n  }\n  _invokeOne(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.push(this);\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) return result;\n    }\n    return null;\n  }\n  _invokeAll(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.unshift(this);\n    const pending = [];\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) pending.push(result);\n    }\n    return pending;\n  }\n  /**\n   * Requests the specified dependency asynchronously, with caching.\n   * @param {string} type\n   * @param {number} index\n   * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n   */\n  getDependency(type, index) {\n    const cacheKey = type + \":\" + index;\n    let dependency = this.cache.get(cacheKey);\n    if (!dependency) {\n      switch (type) {\n        case \"scene\":\n          dependency = this.loadScene(index);\n          break;\n        case \"node\":\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadNode && ext.loadNode(index);\n          });\n          break;\n        case \"mesh\":\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMesh && ext.loadMesh(index);\n          });\n          break;\n        case \"accessor\":\n          dependency = this.loadAccessor(index);\n          break;\n        case \"bufferView\":\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadBufferView && ext.loadBufferView(index);\n          });\n          break;\n        case \"buffer\":\n          dependency = this.loadBuffer(index);\n          break;\n        case \"material\":\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMaterial && ext.loadMaterial(index);\n          });\n          break;\n        case \"texture\":\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadTexture && ext.loadTexture(index);\n          });\n          break;\n        case \"skin\":\n          dependency = this.loadSkin(index);\n          break;\n        case \"animation\":\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadAnimation && ext.loadAnimation(index);\n          });\n          break;\n        case \"camera\":\n          dependency = this.loadCamera(index);\n          break;\n        default:\n          dependency = this._invokeOne(function (ext) {\n            return ext != this && ext.getDependency && ext.getDependency(type, index);\n          });\n          if (!dependency) {\n            throw new Error(\"Unknown type: \" + type);\n          }\n          break;\n      }\n      this.cache.add(cacheKey, dependency);\n    }\n    return dependency;\n  }\n  /**\n   * Requests all dependencies of the specified type asynchronously, with caching.\n   * @param {string} type\n   * @return {Promise<Array<Object>>}\n   */\n  getDependencies(type) {\n    let dependencies = this.cache.get(type);\n    if (!dependencies) {\n      const parser = this;\n      const defs = this.json[type + (type === \"mesh\" ? \"es\" : \"s\")] || [];\n      dependencies = Promise.all(defs.map(function (def, index) {\n        return parser.getDependency(type, index);\n      }));\n      this.cache.add(type, dependencies);\n    }\n    return dependencies;\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBuffer(bufferIndex) {\n    const bufferDef = this.json.buffers[bufferIndex];\n    const loader = this.fileLoader;\n    if (bufferDef.type && bufferDef.type !== \"arraybuffer\") {\n      throw new Error(\"THREE.GLTFLoader: \" + bufferDef.type + \" buffer type is not supported.\");\n    }\n    if (bufferDef.uri === void 0 && bufferIndex === 0) {\n      return Promise.resolve(this.extensions[EXTENSIONS.KHR_BINARY_GLTF].body);\n    }\n    const options = this.options;\n    return new Promise(function (resolve, reject) {\n      loader.load(LoaderUtils.resolveURL(bufferDef.uri, options.path), resolve, void 0, function () {\n        reject(new Error('THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".'));\n      });\n    });\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferViewIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBufferView(bufferViewIndex) {\n    const bufferViewDef = this.json.bufferViews[bufferViewIndex];\n    return this.getDependency(\"buffer\", bufferViewDef.buffer).then(function (buffer) {\n      const byteLength = bufferViewDef.byteLength || 0;\n      const byteOffset = bufferViewDef.byteOffset || 0;\n      return buffer.slice(byteOffset, byteOffset + byteLength);\n    });\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n   * @param {number} accessorIndex\n   * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n   */\n  loadAccessor(accessorIndex) {\n    const parser = this;\n    const json = this.json;\n    const accessorDef = this.json.accessors[accessorIndex];\n    if (accessorDef.bufferView === void 0 && accessorDef.sparse === void 0) {\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n      const normalized = accessorDef.normalized === true;\n      const array = new TypedArray(accessorDef.count * itemSize);\n      return Promise.resolve(new BufferAttribute(array, itemSize, normalized));\n    }\n    const pendingBufferViews = [];\n    if (accessorDef.bufferView !== void 0) {\n      pendingBufferViews.push(this.getDependency(\"bufferView\", accessorDef.bufferView));\n    } else {\n      pendingBufferViews.push(null);\n    }\n    if (accessorDef.sparse !== void 0) {\n      pendingBufferViews.push(this.getDependency(\"bufferView\", accessorDef.sparse.indices.bufferView));\n      pendingBufferViews.push(this.getDependency(\"bufferView\", accessorDef.sparse.values.bufferView));\n    }\n    return Promise.all(pendingBufferViews).then(function (bufferViews) {\n      const bufferView = bufferViews[0];\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n      const elementBytes = TypedArray.BYTES_PER_ELEMENT;\n      const itemBytes = elementBytes * itemSize;\n      const byteOffset = accessorDef.byteOffset || 0;\n      const byteStride = accessorDef.bufferView !== void 0 ? json.bufferViews[accessorDef.bufferView].byteStride : void 0;\n      const normalized = accessorDef.normalized === true;\n      let array, bufferAttribute;\n      if (byteStride && byteStride !== itemBytes) {\n        const ibSlice = Math.floor(byteOffset / byteStride);\n        const ibCacheKey = \"InterleavedBuffer:\" + accessorDef.bufferView + \":\" + accessorDef.componentType + \":\" + ibSlice + \":\" + accessorDef.count;\n        let ib = parser.cache.get(ibCacheKey);\n        if (!ib) {\n          array = new TypedArray(bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes);\n          ib = new InterleavedBuffer(array, byteStride / elementBytes);\n          parser.cache.add(ibCacheKey, ib);\n        }\n        bufferAttribute = new InterleavedBufferAttribute(ib, itemSize, byteOffset % byteStride / elementBytes, normalized);\n      } else {\n        if (bufferView === null) {\n          array = new TypedArray(accessorDef.count * itemSize);\n        } else {\n          array = new TypedArray(bufferView, byteOffset, accessorDef.count * itemSize);\n        }\n        bufferAttribute = new BufferAttribute(array, itemSize, normalized);\n      }\n      if (accessorDef.sparse !== void 0) {\n        const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;\n        const TypedArrayIndices = WEBGL_COMPONENT_TYPES[accessorDef.sparse.indices.componentType];\n        const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;\n        const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;\n        const sparseIndices = new TypedArrayIndices(bufferViews[1], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices);\n        const sparseValues = new TypedArray(bufferViews[2], byteOffsetValues, accessorDef.sparse.count * itemSize);\n        if (bufferView !== null) {\n          bufferAttribute = new BufferAttribute(bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized);\n        }\n        for (let i = 0, il = sparseIndices.length; i < il; i++) {\n          const index = sparseIndices[i];\n          bufferAttribute.setX(index, sparseValues[i * itemSize]);\n          if (itemSize >= 2) bufferAttribute.setY(index, sparseValues[i * itemSize + 1]);\n          if (itemSize >= 3) bufferAttribute.setZ(index, sparseValues[i * itemSize + 2]);\n          if (itemSize >= 4) bufferAttribute.setW(index, sparseValues[i * itemSize + 3]);\n          if (itemSize >= 5) throw new Error(\"THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.\");\n        }\n      }\n      return bufferAttribute;\n    });\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n   * @param {number} textureIndex\n   * @return {Promise<THREE.Texture|null>}\n   */\n  loadTexture(textureIndex) {\n    const json = this.json;\n    const options = this.options;\n    const textureDef = json.textures[textureIndex];\n    const sourceIndex = textureDef.source;\n    const sourceDef = json.images[sourceIndex];\n    let loader = this.textureLoader;\n    if (sourceDef.uri) {\n      const handler = options.manager.getHandler(sourceDef.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.loadTextureImage(textureIndex, sourceIndex, loader);\n  }\n  loadTextureImage(textureIndex, sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const textureDef = json.textures[textureIndex];\n    const sourceDef = json.images[sourceIndex];\n    const cacheKey = (sourceDef.uri || sourceDef.bufferView) + \":\" + textureDef.sampler;\n    if (this.textureCache[cacheKey]) {\n      return this.textureCache[cacheKey];\n    }\n    const promise = this.loadImageSource(sourceIndex, loader).then(function (texture) {\n      texture.flipY = false;\n      texture.name = textureDef.name || sourceDef.name || \"\";\n      if (texture.name === \"\" && typeof sourceDef.uri === \"string\" && sourceDef.uri.startsWith(\"data:image/\") === false) {\n        texture.name = sourceDef.uri;\n      }\n      const samplers = json.samplers || {};\n      const sampler = samplers[textureDef.sampler] || {};\n      texture.magFilter = WEBGL_FILTERS[sampler.magFilter] || LinearFilter;\n      texture.minFilter = WEBGL_FILTERS[sampler.minFilter] || LinearMipmapLinearFilter;\n      texture.wrapS = WEBGL_WRAPPINGS[sampler.wrapS] || RepeatWrapping;\n      texture.wrapT = WEBGL_WRAPPINGS[sampler.wrapT] || RepeatWrapping;\n      parser.associations.set(texture, {\n        textures: textureIndex\n      });\n      return texture;\n    }).catch(function () {\n      return null;\n    });\n    this.textureCache[cacheKey] = promise;\n    return promise;\n  }\n  loadImageSource(sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const options = this.options;\n    if (this.sourceCache[sourceIndex] !== void 0) {\n      return this.sourceCache[sourceIndex].then(texture => texture.clone());\n    }\n    const sourceDef = json.images[sourceIndex];\n    const URL = self.URL || self.webkitURL;\n    let sourceURI = sourceDef.uri || \"\";\n    let isObjectURL = false;\n    if (sourceDef.bufferView !== void 0) {\n      sourceURI = parser.getDependency(\"bufferView\", sourceDef.bufferView).then(function (bufferView) {\n        isObjectURL = true;\n        const blob = new Blob([bufferView], {\n          type: sourceDef.mimeType\n        });\n        sourceURI = URL.createObjectURL(blob);\n        return sourceURI;\n      });\n    } else if (sourceDef.uri === void 0) {\n      throw new Error(\"THREE.GLTFLoader: Image \" + sourceIndex + \" is missing URI and bufferView\");\n    }\n    const promise = Promise.resolve(sourceURI).then(function (sourceURI2) {\n      return new Promise(function (resolve, reject) {\n        let onLoad = resolve;\n        if (loader.isImageBitmapLoader === true) {\n          onLoad = function (imageBitmap) {\n            const texture = new Texture(imageBitmap);\n            texture.needsUpdate = true;\n            resolve(texture);\n          };\n        }\n        loader.load(LoaderUtils.resolveURL(sourceURI2, options.path), onLoad, void 0, reject);\n      });\n    }).then(function (texture) {\n      if (isObjectURL === true) {\n        URL.revokeObjectURL(sourceURI);\n      }\n      assignExtrasToUserData(texture, sourceDef);\n      texture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType(sourceDef.uri);\n      return texture;\n    }).catch(function (error) {\n      console.error(\"THREE.GLTFLoader: Couldn't load texture\", sourceURI);\n      throw error;\n    });\n    this.sourceCache[sourceIndex] = promise;\n    return promise;\n  }\n  /**\n   * Asynchronously assigns a texture to the given material parameters.\n   * @param {Object} materialParams\n   * @param {string} mapName\n   * @param {Object} mapDef\n   * @return {Promise<Texture>}\n   */\n  assignTexture(materialParams, mapName, mapDef, colorSpace) {\n    const parser = this;\n    return this.getDependency(\"texture\", mapDef.index).then(function (texture) {\n      if (!texture) return null;\n      if (mapDef.texCoord !== void 0 && mapDef.texCoord > 0) {\n        texture = texture.clone();\n        texture.channel = mapDef.texCoord;\n      }\n      if (parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM]) {\n        const transform = mapDef.extensions !== void 0 ? mapDef.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM] : void 0;\n        if (transform) {\n          const gltfReference = parser.associations.get(texture);\n          texture = parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM].extendTexture(texture, transform);\n          parser.associations.set(texture, gltfReference);\n        }\n      }\n      if (colorSpace !== void 0) {\n        if (typeof colorSpace === \"number\") colorSpace = colorSpace === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n        if (\"colorSpace\" in texture) texture.colorSpace = colorSpace;else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding;\n      }\n      materialParams[mapName] = texture;\n      return texture;\n    });\n  }\n  /**\n   * Assigns final material to a Mesh, Line, or Points instance. The instance\n   * already has a material (generated from the glTF material options alone)\n   * but reuse of the same glTF material may require multiple threejs materials\n   * to accommodate different primitive types, defines, etc. New materials will\n   * be created if necessary, and reused from a cache.\n   * @param  {Object3D} mesh Mesh, Line, or Points instance.\n   */\n  assignFinalMaterial(mesh) {\n    const geometry = mesh.geometry;\n    let material = mesh.material;\n    const useDerivativeTangents = geometry.attributes.tangent === void 0;\n    const useVertexColors = geometry.attributes.color !== void 0;\n    const useFlatShading = geometry.attributes.normal === void 0;\n    if (mesh.isPoints) {\n      const cacheKey = \"PointsMaterial:\" + material.uuid;\n      let pointsMaterial = this.cache.get(cacheKey);\n      if (!pointsMaterial) {\n        pointsMaterial = new PointsMaterial();\n        Material.prototype.copy.call(pointsMaterial, material);\n        pointsMaterial.color.copy(material.color);\n        pointsMaterial.map = material.map;\n        pointsMaterial.sizeAttenuation = false;\n        this.cache.add(cacheKey, pointsMaterial);\n      }\n      material = pointsMaterial;\n    } else if (mesh.isLine) {\n      const cacheKey = \"LineBasicMaterial:\" + material.uuid;\n      let lineMaterial = this.cache.get(cacheKey);\n      if (!lineMaterial) {\n        lineMaterial = new LineBasicMaterial();\n        Material.prototype.copy.call(lineMaterial, material);\n        lineMaterial.color.copy(material.color);\n        lineMaterial.map = material.map;\n        this.cache.add(cacheKey, lineMaterial);\n      }\n      material = lineMaterial;\n    }\n    if (useDerivativeTangents || useVertexColors || useFlatShading) {\n      let cacheKey = \"ClonedMaterial:\" + material.uuid + \":\";\n      if (useDerivativeTangents) cacheKey += \"derivative-tangents:\";\n      if (useVertexColors) cacheKey += \"vertex-colors:\";\n      if (useFlatShading) cacheKey += \"flat-shading:\";\n      let cachedMaterial = this.cache.get(cacheKey);\n      if (!cachedMaterial) {\n        cachedMaterial = material.clone();\n        if (useVertexColors) cachedMaterial.vertexColors = true;\n        if (useFlatShading) cachedMaterial.flatShading = true;\n        if (useDerivativeTangents) {\n          if (cachedMaterial.normalScale) cachedMaterial.normalScale.y *= -1;\n          if (cachedMaterial.clearcoatNormalScale) cachedMaterial.clearcoatNormalScale.y *= -1;\n        }\n        this.cache.add(cacheKey, cachedMaterial);\n        this.associations.set(cachedMaterial, this.associations.get(material));\n      }\n      material = cachedMaterial;\n    }\n    mesh.material = material;\n  }\n  getMaterialType() {\n    return MeshStandardMaterial;\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n   * @param {number} materialIndex\n   * @return {Promise<Material>}\n   */\n  loadMaterial(materialIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const materialDef = json.materials[materialIndex];\n    let materialType;\n    const materialParams = {};\n    const materialExtensions = materialDef.extensions || {};\n    const pending = [];\n    if (materialExtensions[EXTENSIONS.KHR_MATERIALS_UNLIT]) {\n      const kmuExtension = extensions[EXTENSIONS.KHR_MATERIALS_UNLIT];\n      materialType = kmuExtension.getMaterialType();\n      pending.push(kmuExtension.extendParams(materialParams, materialDef, parser));\n    } else {\n      const metallicRoughness = materialDef.pbrMetallicRoughness || {};\n      materialParams.color = new Color(1, 1, 1);\n      materialParams.opacity = 1;\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== void 0) {\n        pending.push(parser.assignTexture(materialParams, \"map\", metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n      materialParams.metalness = metallicRoughness.metallicFactor !== void 0 ? metallicRoughness.metallicFactor : 1;\n      materialParams.roughness = metallicRoughness.roughnessFactor !== void 0 ? metallicRoughness.roughnessFactor : 1;\n      if (metallicRoughness.metallicRoughnessTexture !== void 0) {\n        pending.push(parser.assignTexture(materialParams, \"metalnessMap\", metallicRoughness.metallicRoughnessTexture));\n        pending.push(parser.assignTexture(materialParams, \"roughnessMap\", metallicRoughness.metallicRoughnessTexture));\n      }\n      materialType = this._invokeOne(function (ext) {\n        return ext.getMaterialType && ext.getMaterialType(materialIndex);\n      });\n      pending.push(Promise.all(this._invokeAll(function (ext) {\n        return ext.extendMaterialParams && ext.extendMaterialParams(materialIndex, materialParams);\n      })));\n    }\n    if (materialDef.doubleSided === true) {\n      materialParams.side = DoubleSide;\n    }\n    const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;\n    if (alphaMode === ALPHA_MODES.BLEND) {\n      materialParams.transparent = true;\n      materialParams.depthWrite = false;\n    } else {\n      materialParams.transparent = false;\n      if (alphaMode === ALPHA_MODES.MASK) {\n        materialParams.alphaTest = materialDef.alphaCutoff !== void 0 ? materialDef.alphaCutoff : 0.5;\n      }\n    }\n    if (materialDef.normalTexture !== void 0 && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, \"normalMap\", materialDef.normalTexture));\n      materialParams.normalScale = new Vector2(1, 1);\n      if (materialDef.normalTexture.scale !== void 0) {\n        const scale = materialDef.normalTexture.scale;\n        materialParams.normalScale.set(scale, scale);\n      }\n    }\n    if (materialDef.occlusionTexture !== void 0 && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, \"aoMap\", materialDef.occlusionTexture));\n      if (materialDef.occlusionTexture.strength !== void 0) {\n        materialParams.aoMapIntensity = materialDef.occlusionTexture.strength;\n      }\n    }\n    if (materialDef.emissiveFactor !== void 0 && materialType !== MeshBasicMaterial) {\n      const emissiveFactor = materialDef.emissiveFactor;\n      materialParams.emissive = new Color().setRGB(emissiveFactor[0], emissiveFactor[1], emissiveFactor[2], LinearSRGBColorSpace);\n    }\n    if (materialDef.emissiveTexture !== void 0 && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, \"emissiveMap\", materialDef.emissiveTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending).then(function () {\n      const material = new materialType(materialParams);\n      if (materialDef.name) material.name = materialDef.name;\n      assignExtrasToUserData(material, materialDef);\n      parser.associations.set(material, {\n        materials: materialIndex\n      });\n      if (materialDef.extensions) addUnknownExtensionsToUserData(extensions, material, materialDef);\n      return material;\n    });\n  }\n  /** When Object3D instances are targeted by animation, they need unique names. */\n  createUniqueName(originalName) {\n    const sanitizedName = PropertyBinding.sanitizeNodeName(originalName || \"\");\n    if (sanitizedName in this.nodeNamesUsed) {\n      return sanitizedName + \"_\" + ++this.nodeNamesUsed[sanitizedName];\n    } else {\n      this.nodeNamesUsed[sanitizedName] = 0;\n      return sanitizedName;\n    }\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n   *\n   * Creates BufferGeometries from primitives.\n   *\n   * @param {Array<GLTF.Primitive>} primitives\n   * @return {Promise<Array<BufferGeometry>>}\n   */\n  loadGeometries(primitives) {\n    const parser = this;\n    const extensions = this.extensions;\n    const cache = this.primitiveCache;\n    function createDracoPrimitive(primitive) {\n      return extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(primitive, parser).then(function (geometry) {\n        return addPrimitiveAttributes(geometry, primitive, parser);\n      });\n    }\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const primitive = primitives[i];\n      const cacheKey = createPrimitiveKey(primitive);\n      const cached = cache[cacheKey];\n      if (cached) {\n        pending.push(cached.promise);\n      } else {\n        let geometryPromise;\n        if (primitive.extensions && primitive.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]) {\n          geometryPromise = createDracoPrimitive(primitive);\n        } else {\n          geometryPromise = addPrimitiveAttributes(new BufferGeometry(), primitive, parser);\n        }\n        cache[cacheKey] = {\n          primitive,\n          promise: geometryPromise\n        };\n        pending.push(geometryPromise);\n      }\n    }\n    return Promise.all(pending);\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n   * @param {number} meshIndex\n   * @return {Promise<Group|Mesh|SkinnedMesh>}\n   */\n  loadMesh(meshIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const meshDef = json.meshes[meshIndex];\n    const primitives = meshDef.primitives;\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const material = primitives[i].material === void 0 ? createDefaultMaterial(this.cache) : this.getDependency(\"material\", primitives[i].material);\n      pending.push(material);\n    }\n    pending.push(parser.loadGeometries(primitives));\n    return Promise.all(pending).then(function (results) {\n      const materials = results.slice(0, results.length - 1);\n      const geometries = results[results.length - 1];\n      const meshes = [];\n      for (let i = 0, il = geometries.length; i < il; i++) {\n        const geometry = geometries[i];\n        const primitive = primitives[i];\n        let mesh;\n        const material = materials[i];\n        if (primitive.mode === WEBGL_CONSTANTS.TRIANGLES || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN || primitive.mode === void 0) {\n          mesh = meshDef.isSkinnedMesh === true ? new SkinnedMesh(geometry, material) : new Mesh(geometry, material);\n          if (mesh.isSkinnedMesh === true) {\n            mesh.normalizeSkinWeights();\n          }\n          if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleStripDrawMode);\n          } else if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleFanDrawMode);\n          }\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINES) {\n          mesh = new LineSegments(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_STRIP) {\n          mesh = new Line(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_LOOP) {\n          mesh = new LineLoop(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.POINTS) {\n          mesh = new Points(geometry, material);\n        } else {\n          throw new Error(\"THREE.GLTFLoader: Primitive mode unsupported: \" + primitive.mode);\n        }\n        if (Object.keys(mesh.geometry.morphAttributes).length > 0) {\n          updateMorphTargets(mesh, meshDef);\n        }\n        mesh.name = parser.createUniqueName(meshDef.name || \"mesh_\" + meshIndex);\n        assignExtrasToUserData(mesh, meshDef);\n        if (primitive.extensions) addUnknownExtensionsToUserData(extensions, mesh, primitive);\n        parser.assignFinalMaterial(mesh);\n        meshes.push(mesh);\n      }\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        parser.associations.set(meshes[i], {\n          meshes: meshIndex,\n          primitives: i\n        });\n      }\n      if (meshes.length === 1) {\n        if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, meshes[0], meshDef);\n        return meshes[0];\n      }\n      const group = new Group();\n      if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, group, meshDef);\n      parser.associations.set(group, {\n        meshes: meshIndex\n      });\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        group.add(meshes[i]);\n      }\n      return group;\n    });\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n   * @param {number} cameraIndex\n   * @return {Promise<THREE.Camera>}\n   */\n  loadCamera(cameraIndex) {\n    let camera;\n    const cameraDef = this.json.cameras[cameraIndex];\n    const params = cameraDef[cameraDef.type];\n    if (!params) {\n      console.warn(\"THREE.GLTFLoader: Missing camera parameters.\");\n      return;\n    }\n    if (cameraDef.type === \"perspective\") {\n      camera = new PerspectiveCamera(MathUtils.radToDeg(params.yfov), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6);\n    } else if (cameraDef.type === \"orthographic\") {\n      camera = new OrthographicCamera(-params.xmag, params.xmag, params.ymag, -params.ymag, params.znear, params.zfar);\n    }\n    if (cameraDef.name) camera.name = this.createUniqueName(cameraDef.name);\n    assignExtrasToUserData(camera, cameraDef);\n    return Promise.resolve(camera);\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n   * @param {number} skinIndex\n   * @return {Promise<Skeleton>}\n   */\n  loadSkin(skinIndex) {\n    const skinDef = this.json.skins[skinIndex];\n    const pending = [];\n    for (let i = 0, il = skinDef.joints.length; i < il; i++) {\n      pending.push(this._loadNodeShallow(skinDef.joints[i]));\n    }\n    if (skinDef.inverseBindMatrices !== void 0) {\n      pending.push(this.getDependency(\"accessor\", skinDef.inverseBindMatrices));\n    } else {\n      pending.push(null);\n    }\n    return Promise.all(pending).then(function (results) {\n      const inverseBindMatrices = results.pop();\n      const jointNodes = results;\n      const bones = [];\n      const boneInverses = [];\n      for (let i = 0, il = jointNodes.length; i < il; i++) {\n        const jointNode = jointNodes[i];\n        if (jointNode) {\n          bones.push(jointNode);\n          const mat = new Matrix4();\n          if (inverseBindMatrices !== null) {\n            mat.fromArray(inverseBindMatrices.array, i * 16);\n          }\n          boneInverses.push(mat);\n        } else {\n          console.warn('THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[i]);\n        }\n      }\n      return new Skeleton(bones, boneInverses);\n    });\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n   * @param {number} animationIndex\n   * @return {Promise<AnimationClip>}\n   */\n  loadAnimation(animationIndex) {\n    const json = this.json;\n    const parser = this;\n    const animationDef = json.animations[animationIndex];\n    const animationName = animationDef.name ? animationDef.name : \"animation_\" + animationIndex;\n    const pendingNodes = [];\n    const pendingInputAccessors = [];\n    const pendingOutputAccessors = [];\n    const pendingSamplers = [];\n    const pendingTargets = [];\n    for (let i = 0, il = animationDef.channels.length; i < il; i++) {\n      const channel = animationDef.channels[i];\n      const sampler = animationDef.samplers[channel.sampler];\n      const target = channel.target;\n      const name = target.node;\n      const input = animationDef.parameters !== void 0 ? animationDef.parameters[sampler.input] : sampler.input;\n      const output = animationDef.parameters !== void 0 ? animationDef.parameters[sampler.output] : sampler.output;\n      if (target.node === void 0) continue;\n      pendingNodes.push(this.getDependency(\"node\", name));\n      pendingInputAccessors.push(this.getDependency(\"accessor\", input));\n      pendingOutputAccessors.push(this.getDependency(\"accessor\", output));\n      pendingSamplers.push(sampler);\n      pendingTargets.push(target);\n    }\n    return Promise.all([Promise.all(pendingNodes), Promise.all(pendingInputAccessors), Promise.all(pendingOutputAccessors), Promise.all(pendingSamplers), Promise.all(pendingTargets)]).then(function (dependencies) {\n      const nodes = dependencies[0];\n      const inputAccessors = dependencies[1];\n      const outputAccessors = dependencies[2];\n      const samplers = dependencies[3];\n      const targets = dependencies[4];\n      const tracks = [];\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        const node = nodes[i];\n        const inputAccessor = inputAccessors[i];\n        const outputAccessor = outputAccessors[i];\n        const sampler = samplers[i];\n        const target = targets[i];\n        if (node === void 0) continue;\n        if (node.updateMatrix) {\n          node.updateMatrix();\n        }\n        const createdTracks = parser._createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target);\n        if (createdTracks) {\n          for (let k = 0; k < createdTracks.length; k++) {\n            tracks.push(createdTracks[k]);\n          }\n        }\n      }\n      return new AnimationClip(animationName, void 0, tracks);\n    });\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    if (nodeDef.mesh === void 0) return null;\n    return parser.getDependency(\"mesh\", nodeDef.mesh).then(function (mesh) {\n      const node = parser._getNodeRef(parser.meshCache, nodeDef.mesh, mesh);\n      if (nodeDef.weights !== void 0) {\n        node.traverse(function (o) {\n          if (!o.isMesh) return;\n          for (let i = 0, il = nodeDef.weights.length; i < il; i++) {\n            o.morphTargetInfluences[i] = nodeDef.weights[i];\n          }\n        });\n      }\n      return node;\n    });\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n   * @param {number} nodeIndex\n   * @return {Promise<Object3D>}\n   */\n  loadNode(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    const nodePending = parser._loadNodeShallow(nodeIndex);\n    const childPending = [];\n    const childrenDef = nodeDef.children || [];\n    for (let i = 0, il = childrenDef.length; i < il; i++) {\n      childPending.push(parser.getDependency(\"node\", childrenDef[i]));\n    }\n    const skeletonPending = nodeDef.skin === void 0 ? Promise.resolve(null) : parser.getDependency(\"skin\", nodeDef.skin);\n    return Promise.all([nodePending, Promise.all(childPending), skeletonPending]).then(function (results) {\n      const node = results[0];\n      const children = results[1];\n      const skeleton = results[2];\n      if (skeleton !== null) {\n        node.traverse(function (mesh) {\n          if (!mesh.isSkinnedMesh) return;\n          mesh.bind(skeleton, _identityMatrix);\n        });\n      }\n      for (let i = 0, il = children.length; i < il; i++) {\n        node.add(children[i]);\n      }\n      return node;\n    });\n  }\n  // ._loadNodeShallow() parses a single node.\n  // skin and child nodes are created and added in .loadNode() (no '_' prefix).\n  _loadNodeShallow(nodeIndex) {\n    const json = this.json;\n    const extensions = this.extensions;\n    const parser = this;\n    if (this.nodeCache[nodeIndex] !== void 0) {\n      return this.nodeCache[nodeIndex];\n    }\n    const nodeDef = json.nodes[nodeIndex];\n    const nodeName = nodeDef.name ? parser.createUniqueName(nodeDef.name) : \"\";\n    const pending = [];\n    const meshPromise = parser._invokeOne(function (ext) {\n      return ext.createNodeMesh && ext.createNodeMesh(nodeIndex);\n    });\n    if (meshPromise) {\n      pending.push(meshPromise);\n    }\n    if (nodeDef.camera !== void 0) {\n      pending.push(parser.getDependency(\"camera\", nodeDef.camera).then(function (camera) {\n        return parser._getNodeRef(parser.cameraCache, nodeDef.camera, camera);\n      }));\n    }\n    parser._invokeAll(function (ext) {\n      return ext.createNodeAttachment && ext.createNodeAttachment(nodeIndex);\n    }).forEach(function (promise) {\n      pending.push(promise);\n    });\n    this.nodeCache[nodeIndex] = Promise.all(pending).then(function (objects) {\n      let node;\n      if (nodeDef.isBone === true) {\n        node = new Bone();\n      } else if (objects.length > 1) {\n        node = new Group();\n      } else if (objects.length === 1) {\n        node = objects[0];\n      } else {\n        node = new Object3D();\n      }\n      if (node !== objects[0]) {\n        for (let i = 0, il = objects.length; i < il; i++) {\n          node.add(objects[i]);\n        }\n      }\n      if (nodeDef.name) {\n        node.userData.name = nodeDef.name;\n        node.name = nodeName;\n      }\n      assignExtrasToUserData(node, nodeDef);\n      if (nodeDef.extensions) addUnknownExtensionsToUserData(extensions, node, nodeDef);\n      if (nodeDef.matrix !== void 0) {\n        const matrix = new Matrix4();\n        matrix.fromArray(nodeDef.matrix);\n        node.applyMatrix4(matrix);\n      } else {\n        if (nodeDef.translation !== void 0) {\n          node.position.fromArray(nodeDef.translation);\n        }\n        if (nodeDef.rotation !== void 0) {\n          node.quaternion.fromArray(nodeDef.rotation);\n        }\n        if (nodeDef.scale !== void 0) {\n          node.scale.fromArray(nodeDef.scale);\n        }\n      }\n      if (!parser.associations.has(node)) {\n        parser.associations.set(node, {});\n      }\n      parser.associations.get(node).nodes = nodeIndex;\n      return node;\n    });\n    return this.nodeCache[nodeIndex];\n  }\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n   * @param {number} sceneIndex\n   * @return {Promise<Group>}\n   */\n  loadScene(sceneIndex) {\n    const extensions = this.extensions;\n    const sceneDef = this.json.scenes[sceneIndex];\n    const parser = this;\n    const scene = new Group();\n    if (sceneDef.name) scene.name = parser.createUniqueName(sceneDef.name);\n    assignExtrasToUserData(scene, sceneDef);\n    if (sceneDef.extensions) addUnknownExtensionsToUserData(extensions, scene, sceneDef);\n    const nodeIds = sceneDef.nodes || [];\n    const pending = [];\n    for (let i = 0, il = nodeIds.length; i < il; i++) {\n      pending.push(parser.getDependency(\"node\", nodeIds[i]));\n    }\n    return Promise.all(pending).then(function (nodes) {\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        scene.add(nodes[i]);\n      }\n      const reduceAssociations = node => {\n        const reducedAssociations = /* @__PURE__ */new Map();\n        for (const [key, value] of parser.associations) {\n          if (key instanceof Material || key instanceof Texture) {\n            reducedAssociations.set(key, value);\n          }\n        }\n        node.traverse(node2 => {\n          const mappings = parser.associations.get(node2);\n          if (mappings != null) {\n            reducedAssociations.set(node2, mappings);\n          }\n        });\n        return reducedAssociations;\n      };\n      parser.associations = reduceAssociations(scene);\n      return scene;\n    });\n  }\n  _createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target) {\n    const tracks = [];\n    const targetName = node.name ? node.name : node.uuid;\n    const targetNames = [];\n    if (PATH_PROPERTIES[target.path] === PATH_PROPERTIES.weights) {\n      node.traverse(function (object) {\n        if (object.morphTargetInfluences) {\n          targetNames.push(object.name ? object.name : object.uuid);\n        }\n      });\n    } else {\n      targetNames.push(targetName);\n    }\n    let TypedKeyframeTrack;\n    switch (PATH_PROPERTIES[target.path]) {\n      case PATH_PROPERTIES.weights:\n        TypedKeyframeTrack = NumberKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.rotation:\n        TypedKeyframeTrack = QuaternionKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.position:\n      case PATH_PROPERTIES.scale:\n        TypedKeyframeTrack = VectorKeyframeTrack;\n        break;\n      default:\n        switch (outputAccessor.itemSize) {\n          case 1:\n            TypedKeyframeTrack = NumberKeyframeTrack;\n            break;\n          case 2:\n          case 3:\n          default:\n            TypedKeyframeTrack = VectorKeyframeTrack;\n            break;\n        }\n        break;\n    }\n    const interpolation = sampler.interpolation !== void 0 ? INTERPOLATION[sampler.interpolation] : InterpolateLinear;\n    const outputArray = this._getArrayFromAccessor(outputAccessor);\n    for (let j = 0, jl = targetNames.length; j < jl; j++) {\n      const track = new TypedKeyframeTrack(targetNames[j] + \".\" + PATH_PROPERTIES[target.path], inputAccessor.array, outputArray, interpolation);\n      if (sampler.interpolation === \"CUBICSPLINE\") {\n        this._createCubicSplineTrackInterpolant(track);\n      }\n      tracks.push(track);\n    }\n    return tracks;\n  }\n  _getArrayFromAccessor(accessor) {\n    let outputArray = accessor.array;\n    if (accessor.normalized) {\n      const scale = getNormalizedComponentScale(outputArray.constructor);\n      const scaled = new Float32Array(outputArray.length);\n      for (let j = 0, jl = outputArray.length; j < jl; j++) {\n        scaled[j] = outputArray[j] * scale;\n      }\n      outputArray = scaled;\n    }\n    return outputArray;\n  }\n  _createCubicSplineTrackInterpolant(track) {\n    track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline(result) {\n      const interpolantType = this instanceof QuaternionKeyframeTrack ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;\n      return new interpolantType(this.times, this.values, this.getValueSize() / 3, result);\n    };\n    track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;\n  }\n}\nfunction computeBounds(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const box = new Box3();\n  if (attributes.POSITION !== void 0) {\n    const accessor = parser.json.accessors[attributes.POSITION];\n    const min = accessor.min;\n    const max = accessor.max;\n    if (min !== void 0 && max !== void 0) {\n      box.set(new Vector3(min[0], min[1], min[2]), new Vector3(max[0], max[1], max[2]));\n      if (accessor.normalized) {\n        const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n        box.min.multiplyScalar(boxScale);\n        box.max.multiplyScalar(boxScale);\n      }\n    } else {\n      console.warn(\"THREE.GLTFLoader: Missing min/max properties for accessor POSITION.\");\n      return;\n    }\n  } else {\n    return;\n  }\n  const targets = primitiveDef.targets;\n  if (targets !== void 0) {\n    const maxDisplacement = new Vector3();\n    const vector = new Vector3();\n    for (let i = 0, il = targets.length; i < il; i++) {\n      const target = targets[i];\n      if (target.POSITION !== void 0) {\n        const accessor = parser.json.accessors[target.POSITION];\n        const min = accessor.min;\n        const max = accessor.max;\n        if (min !== void 0 && max !== void 0) {\n          vector.setX(Math.max(Math.abs(min[0]), Math.abs(max[0])));\n          vector.setY(Math.max(Math.abs(min[1]), Math.abs(max[1])));\n          vector.setZ(Math.max(Math.abs(min[2]), Math.abs(max[2])));\n          if (accessor.normalized) {\n            const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n            vector.multiplyScalar(boxScale);\n          }\n          maxDisplacement.max(vector);\n        } else {\n          console.warn(\"THREE.GLTFLoader: Missing min/max properties for accessor POSITION.\");\n        }\n      }\n    }\n    box.expandByVector(maxDisplacement);\n  }\n  geometry.boundingBox = box;\n  const sphere = new Sphere();\n  box.getCenter(sphere.center);\n  sphere.radius = box.min.distanceTo(box.max) / 2;\n  geometry.boundingSphere = sphere;\n}\nfunction addPrimitiveAttributes(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const pending = [];\n  function assignAttributeAccessor(accessorIndex, attributeName) {\n    return parser.getDependency(\"accessor\", accessorIndex).then(function (accessor) {\n      geometry.setAttribute(attributeName, accessor);\n    });\n  }\n  for (const gltfAttributeName in attributes) {\n    const threeAttributeName = ATTRIBUTES[gltfAttributeName] || gltfAttributeName.toLowerCase();\n    if (threeAttributeName in geometry.attributes) continue;\n    pending.push(assignAttributeAccessor(attributes[gltfAttributeName], threeAttributeName));\n  }\n  if (primitiveDef.indices !== void 0 && !geometry.index) {\n    const accessor = parser.getDependency(\"accessor\", primitiveDef.indices).then(function (accessor2) {\n      geometry.setIndex(accessor2);\n    });\n    pending.push(accessor);\n  }\n  assignExtrasToUserData(geometry, primitiveDef);\n  computeBounds(geometry, primitiveDef, parser);\n  return Promise.all(pending).then(function () {\n    return primitiveDef.targets !== void 0 ? addMorphTargets(geometry, primitiveDef.targets, parser) : geometry;\n  });\n}\nexport { GLTFLoader };", "map": {"version": 3, "names": ["SRGBColorSpace", "LinearSRGBColorSpace", "sRGBEncoding", "LinearEncoding", "GLTFLoader", "Loader", "constructor", "manager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ktx2Loader", "meshoptDecoder", "pluginCallbacks", "register", "parser", "GLTFMaterialsClearcoatExtension", "GLTFMaterialsDispersionExtension", "GLTFTextureBasisUExtension", "GLTFTextureWebPExtension", "GLTFTextureAVIFExtension", "GLTFMaterialsSheenExtension", "GLTFMaterialsTransmissionExtension", "GLTFMaterialsVolumeExtension", "GLTFMaterialsIorExtension", "GLTFMaterialsEmissiveStrengthExtension", "GLTFMaterialsSpecularExtension", "GLTFMaterialsIridescenceExtension", "GLTFMaterialsAnisotropyExtension", "GLTFMaterialsBumpExtension", "GLTFLightsExtension", "GLTFMeshoptCompression", "GLTFMeshGpuInstancing", "load", "url", "onLoad", "onProgress", "onError", "scope", "resourcePath", "path", "relativeUrl", "LoaderUtils", "extractUrlBase", "resolveURL", "itemStart", "_onError", "e", "console", "error", "itemError", "itemEnd", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "data", "parse", "gltf", "setDRACOLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "setKTX2Loader", "setMeshoptDecoder", "callback", "indexOf", "push", "unregister", "splice", "json", "extensions", "plugins", "JSON", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magic", "decodeText", "Uint8Array", "slice", "BINARY_EXTENSION_HEADER_MAGIC", "EXTENSIONS", "KHR_BINARY_GLTF", "GLTFBinaryExtension", "content", "asset", "version", "GLTFParser", "crossOrigin", "fileLoader", "i", "length", "plugin", "name", "extensionsUsed", "extensionName", "extensionsRequired", "KHR_MATERIALS_UNLIT", "GLTFMaterialsUnlitExtension", "KHR_DRACO_MESH_COMPRESSION", "GLTFDracoMeshCompressionExtension", "KHR_TEXTURE_TRANSFORM", "GLTFTextureTransformExtension", "KHR_MESH_QUANTIZATION", "GLTFMeshQuantizationExtension", "warn", "setExtensions", "setPlugins", "parseAsync", "Promise", "resolve", "reject", "GLTFRegistry", "objects", "get", "key", "add", "object", "remove", "removeAll", "KHR_LIGHTS_PUNCTUAL", "KHR_MATERIALS_CLEARCOAT", "KHR_MATERIALS_DISPERSION", "KHR_MATERIALS_IOR", "KHR_MATERIALS_SHEEN", "KHR_MATERIALS_SPECULAR", "KHR_MATERIALS_TRANSMISSION", "KHR_MATERIALS_IRIDESCENCE", "KHR_MATERIALS_ANISOTROPY", "KHR_MATERIALS_VOLUME", "KHR_TEXTURE_BASISU", "KHR_MATERIALS_EMISSIVE_STRENGTH", "EXT_MATERIALS_BUMP", "EXT_TEXTURE_WEBP", "EXT_TEXTURE_AVIF", "EXT_MESHOPT_COMPRESSION", "EXT_MESH_GPU_INSTANCING", "cache", "refs", "uses", "_markDefs", "nodeDefs", "nodes", "nodeIndex", "node<PERSON><PERSON><PERSON>", "nodeDef", "light", "_addNodeRef", "_loadLight", "lightIndex", "cache<PERSON>ey", "dependency", "lightDefs", "lights", "lightDef", "lightNode", "color", "Color", "setRGB", "range", "type", "DirectionalLight", "target", "position", "set", "PointLight", "distance", "SpotLight", "spot", "innerConeAngle", "outerConeAngle", "Math", "PI", "angle", "penumbra", "decay", "assignExtrasToUserData", "intensity", "createUniqueName", "getDependency", "index", "createNodeAttachment", "self2", "then", "_getNodeRef", "getMaterialType", "MeshBasicMaterial", "extendParams", "materialParams", "materialDef", "pending", "opacity", "metallicRoughness", "pbrMetallicRoughness", "Array", "isArray", "baseColorFactor", "array", "baseColorTexture", "assignTexture", "all", "extendMaterialParams", "materialIndex", "materials", "emissiveStrength", "emissiveIntensity", "MeshPhysicalMaterial", "extension", "clearcoatFactor", "clearcoat", "clearcoatTexture", "clearcoatRoughnessFactor", "clearcoatRoughness", "clearcoatRoughnessTexture", "clearcoatNormalTexture", "scale", "clearcoatNormalScale", "Vector2", "dispersion", "iridescenceFactor", "iridescence", "iridescenceTexture", "iridescenceIor", "iridescenceIOR", "iridescenceThicknessRange", "iridescenceThicknessMinimum", "iridescenceThicknessMaximum", "iridescenceThicknessTexture", "sheenColor", "sheenRoughness", "sheen", "sheenColorFactor", "colorFactor", "sheenRoughnessFactor", "sheenColorTexture", "sheenRoughnessTexture", "transmissionFactor", "transmission", "transmissionTexture", "thickness", "thicknessFactor", "thicknessTexture", "attenuationDistance", "Infinity", "colorArray", "attenuationColor", "ior", "specularIntensity", "specularFactor", "specularTexture", "specularColorFactor", "specularColor", "specularColorTexture", "bumpScale", "bumpFactor", "bumpTexture", "anisotropyStrength", "anisotropy", "anisotropyRotation", "anisotropyTexture", "loadTexture", "textureIndex", "textureDef", "textures", "options", "loadTextureImage", "source", "isSupported", "images", "textureLoader", "uri", "handler", "<PERSON><PERSON><PERSON><PERSON>", "detectSupport", "image", "Image", "src", "onload", "onerror", "height", "loadBufferView", "bufferView", "bufferViews", "extensionDef", "buffer", "decoder", "supported", "res", "byteOffset", "byteLength", "count", "stride", "byteStride", "decodeGltfBufferAsync", "mode", "filter", "res2", "ready", "result", "decodeGltfBuffer", "createNodeMesh", "mesh", "meshDef", "meshes", "primitive", "primitives", "WEBGL_CONSTANTS", "TRIANGLES", "TRIANGLE_STRIP", "TRIANGLE_FAN", "attributesDef", "attributes", "accessor", "results", "nodeObject", "pop", "isGroup", "children", "instanced<PERSON><PERSON><PERSON>", "m", "Matrix4", "p", "Vector3", "q", "Quaternion", "s", "instanced<PERSON><PERSON>", "In<PERSON>d<PERSON>esh", "geometry", "material", "TRANSLATION", "fromBufferAttribute", "ROTATION", "SCALE", "setMatrixAt", "compose", "attributeName", "attr", "instanceColor", "InstancedBufferAttribute", "itemSize", "normalized", "setAttribute", "Object3D", "prototype", "copy", "call", "assignFinalMaterial", "clear", "BINARY_EXTENSION_HEADER_LENGTH", "BINARY_EXTENSION_CHUNK_TYPES", "BIN", "body", "headerView", "DataView", "header", "getUint32", "chunkContentsLength", "chunkView", "chunkIndex", "chunkLength", "chunkType", "contentArray", "preload", "decodePrimitive", "bufferViewIndex", "gltfAttributeMap", "threeAttributeMap", "attributeNormalizedMap", "attributeTypeMap", "threeAttributeName", "ATTRIBUTES", "toLowerCase", "accessorDef", "accessors", "componentType", "WEBGL_COMPONENT_TYPES", "decodeDracoFile", "attribute", "extendTexture", "texture", "transform", "texCoord", "channel", "offset", "rotation", "clone", "fromArray", "repeat", "needsUpdate", "GLTFCubicSplineInterpolant", "Interpolant", "parameterPositions", "sampleValues", "sampleSize", "result<PERSON><PERSON><PERSON>", "copySampleValue_", "values", "valueSize", "interpolate_", "i1", "t0", "t", "t1", "stride2", "stride3", "td", "pp", "ppp", "offset1", "offset0", "s2", "s3", "s0", "s1", "p0", "m0", "p1", "m1", "_q", "GLTFCubicSplineQuaternionInterpolant", "normalize", "toArray", "FLOAT", "FLOAT_MAT3", "FLOAT_MAT4", "FLOAT_VEC2", "FLOAT_VEC3", "FLOAT_VEC4", "LINEAR", "REPEAT", "SAMPLER_2D", "POINTS", "LINES", "LINE_LOOP", "LINE_STRIP", "UNSIGNED_BYTE", "UNSIGNED_SHORT", "Int8Array", "Int16Array", "Uint16Array", "Uint32Array", "Float32Array", "WEBGL_FILTERS", "NearestFilter", "LinearFilter", "NearestMipmapNearestFilter", "LinearMipmapNearestFilter", "NearestMipmapLinearFilter", "LinearMipmapLinearFilter", "WEBGL_WRAPPINGS", "ClampToEdgeWrapping", "MirroredRepeatWrapping", "RepeatWrapping", "WEBGL_TYPE_SIZES", "SCALAR", "VEC2", "VEC3", "VEC4", "MAT2", "MAT3", "MAT4", "POSITION", "NORMAL", "TANGENT", "TEXCOORD_0", "TEXCOORD_1", "TEXCOORD_2", "TEXCOORD_3", "COLOR_0", "WEIGHTS_0", "JOINTS_0", "PATH_PROPERTIES", "translation", "weights", "INTERPOLATION", "CUBICSPLINE", "InterpolateLinear", "STEP", "InterpolateDiscrete", "ALPHA_MODES", "OPAQUE", "MASK", "BLEND", "createDefaultMaterial", "MeshStandardMaterial", "emissive", "metalness", "roughness", "transparent", "depthTest", "side", "FrontSide", "addUnknownExtensionsToUserData", "knownExtensions", "objectDef", "userData", "gltfExtensions", "gltfDef", "extras", "Object", "assign", "addMorphTargets", "targets", "hasMorphPosition", "hasMorphNormal", "hasMorphColor", "il", "pendingPositionAccessors", "pendingNormalAccessors", "pendingColorAccessors", "pendingAccessor", "normal", "morphPositions", "morphNormals", "morphColors", "morphAttributes", "morphTargetsRelative", "updateMorphTargets", "morphTargetInfluences", "targetNames", "morphTargetDictionary", "createPrimitiveKey", "primitiveDef", "geometry<PERSON>ey", "dracoExtension", "indices", "createAttributesKey", "<PERSON><PERSON><PERSON>", "keys", "sort", "getNormalizedComponentScale", "getImageURIMimeType", "search", "_identityMatrix", "associations", "Map", "primitiveCache", "nodeCache", "meshCache", "cameraCache", "lightCache", "sourceCache", "textureCache", "nodeNamesUsed", "<PERSON><PERSON><PERSON><PERSON>", "isFirefox", "firefoxVersion", "navigator", "userAgent", "test", "match", "createImageBitmap", "TextureLoader", "ImageBitmapLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_invokeAll", "ext", "beforeRoot", "getDependencies", "dependencies", "scene", "scenes", "animations", "cameras", "afterRoot", "updateMatrixWorld", "catch", "skinDefs", "skins", "meshDefs", "skinIndex", "<PERSON><PERSON><PERSON><PERSON>", "joints", "isBone", "skin", "isSkinnedMesh", "camera", "ref", "updateMappings", "original", "mappings", "child", "entries", "_invokeOne", "func", "unshift", "loadScene", "loadNode", "loadMesh", "loadAccessor", "loadBuffer", "loadMaterial", "loadSkin", "loadAnimation", "loadCamera", "defs", "map", "def", "bufferIndex", "bufferDef", "buffers", "bufferViewDef", "accessorIndex", "sparse", "TypedArray", "BufferAttribute", "pendingBufferViews", "elementBytes", "BYTES_PER_ELEMENT", "itemBytes", "bufferAttribute", "ibSlice", "floor", "ib<PERSON><PERSON><PERSON><PERSON>", "ib", "InterleavedBuffer", "InterleavedBufferAttribute", "itemSizeIndices", "TypedArrayIndices", "byteOffsetIndices", "byteOffsetValues", "sparseIndices", "sparseValues", "setX", "setY", "setZ", "setW", "sourceIndex", "sourceDef", "sampler", "promise", "loadImageSource", "flipY", "startsWith", "samplers", "magFilter", "minFilter", "wrapS", "wrapT", "URL", "self", "webkitURL", "sourceURI", "isObjectURL", "blob", "Blob", "mimeType", "createObjectURL", "sourceURI2", "isImageBitmapLoader", "imageBitmap", "Texture", "revokeObjectURL", "mapName", "mapDef", "colorSpace", "gltfReference", "encoding", "useDerivativeTangents", "tangent", "useVertexColors", "useFlatShading", "isPoints", "uuid", "pointsMaterial", "PointsMaterial", "Material", "sizeAttenuation", "isLine", "lineMaterial", "LineBasicMaterial", "cachedMaterial", "vertexColors", "flatShading", "normalScale", "y", "materialType", "materialExtensions", "kmuExtension", "metallicFactor", "roughnessFactor", "metallicRoughnessTexture", "doubleSided", "DoubleSide", "alphaMode", "depthWrite", "alphaTest", "<PERSON><PERSON><PERSON><PERSON>", "normalTexture", "occlusionTexture", "strength", "aoMapIntensity", "emissiveFactor", "emissiveTexture", "originalName", "sanitizedName", "PropertyBinding", "sanitizeNodeName", "loadGeometries", "createDracoPrimitive", "addPrimitiveAttributes", "cached", "geometryPromise", "BufferGeometry", "meshIndex", "geometries", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "normalizeSkinWeights", "toTrianglesDrawMode", "TriangleStripDrawMode", "TriangleFanDrawMode", "LineSegments", "Line", "LineLoop", "Points", "group", "Group", "cameraIndex", "cameraDef", "params", "PerspectiveCamera", "MathUtils", "radToDeg", "yfov", "aspectRatio", "znear", "zfar", "OrthographicCamera", "xmag", "ymag", "skinDef", "_loadNodeShallow", "inverseBindMatrices", "jointNodes", "bones", "boneInverses", "jointNode", "mat", "Skeleton", "animationIndex", "animationDef", "animationName", "pendingNodes", "pendingInputAccessors", "pendingOutputAccessors", "pendingSamplers", "pendingTargets", "channels", "node", "input", "parameters", "output", "inputAccessors", "outputAccessors", "tracks", "inputAccessor", "outputAccessor", "updateMatrix", "createdTracks", "_createAnimationTracks", "k", "AnimationClip", "traverse", "o", "<PERSON><PERSON><PERSON>", "nodePending", "childPending", "childrenDef", "skeletonPending", "skeleton", "bind", "nodeName", "meshPromise", "for<PERSON>ach", "Bone", "matrix", "applyMatrix4", "quaternion", "has", "sceneIndex", "sceneDef", "nodeIds", "reduceAssociations", "reducedAssociations", "value", "node2", "targetName", "TypedKeyframeTrack", "NumberKeyframeTrack", "QuaternionKeyframeTrack", "VectorKeyframeTrack", "interpolation", "outputArray", "_getArrayFromAccessor", "j", "jl", "track", "_createCubicSplineTrackInterpolant", "scaled", "createInterpolant", "InterpolantFactoryMethodGLTFCubicSpline", "interpolantType", "times", "getValueSize", "isInterpolantFactoryMethodGLTFCubicSpline", "computeBounds", "box", "Box3", "min", "max", "boxScale", "multiplyScalar", "maxDisplacement", "vector", "abs", "expandByVector", "boundingBox", "sphere", "Sphere", "getCenter", "center", "radius", "distanceTo", "boundingSphere", "assignAttributeAccessor", "gltfAttributeName", "accessor2", "setIndex"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\GLTFLoader.js"], "sourcesContent": ["import {\n  AnimationClip,\n  Bone,\n  Box3,\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  DirectionalLight,\n  DoubleSide,\n  FileLoader,\n  FrontSide,\n  Group,\n  ImageBitmapLoader,\n  InstancedMesh,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  Interpolant,\n  InterpolateDiscrete,\n  InterpolateLinear,\n  Line,\n  LineBasicMaterial,\n  LineLoop,\n  LineSegments,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  LinearMipmapNearestFilter,\n  Loader,\n  LoaderUtils,\n  Material,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  MeshPhysicalMaterial,\n  MeshStandardMaterial,\n  MirroredRepeatWrapping,\n  NearestFilter,\n  NearestMipmapLinearFilter,\n  NearestMipmapNearestFilter,\n  NumberKeyframeTrack,\n  Object3D,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PointLight,\n  Points,\n  PointsMaterial,\n  PropertyBinding,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  RepeatWrapping,\n  Skeleton,\n  SkinnedMesh,\n  Sphere,\n  SpotLight,\n  Texture,\n  TextureLoader,\n  TriangleFanDrawMode,\n  TriangleStripDrawMode,\n  Vector2,\n  Vector3,\n  VectorKeyframeTrack,\n  InstancedBufferAttribute,\n} from 'three'\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils'\nimport { version } from '../_polyfill/constants'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nconst SRGBColorSpace = 'srgb'\nconst LinearSRGBColorSpace = 'srgb-linear'\nconst sRGBEncoding = 3001\nconst LinearEncoding = 3000\n\nclass GLTFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.dracoLoader = null\n    this.ktx2Loader = null\n    this.meshoptDecoder = null\n\n    this.pluginCallbacks = []\n\n    this.register(function (parser) {\n      return new GLTFMaterialsClearcoatExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsDispersionExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureBasisUExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureWebPExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFTextureAVIFExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsSheenExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsTransmissionExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsVolumeExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsIorExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsEmissiveStrengthExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsSpecularExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsIridescenceExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsAnisotropyExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMaterialsBumpExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFLightsExtension(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMeshoptCompression(parser)\n    })\n\n    this.register(function (parser) {\n      return new GLTFMeshGpuInstancing(parser)\n    })\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    let resourcePath\n\n    if (this.resourcePath !== '') {\n      resourcePath = this.resourcePath\n    } else if (this.path !== '') {\n      // If a base path is set, resources will be relative paths from that plus the relative path of the gltf file\n      // Example  path = 'https://my-cnd-server.com/', url = 'assets/models/model.gltf'\n      // resourcePath = 'https://my-cnd-server.com/assets/models/'\n      // referenced resource 'model.bin' will be loaded from 'https://my-cnd-server.com/assets/models/model.bin'\n      // referenced resource '../textures/texture.png' will be loaded from 'https://my-cnd-server.com/assets/textures/texture.png'\n      const relativeUrl = LoaderUtils.extractUrlBase(url)\n      resourcePath = LoaderUtils.resolveURL(relativeUrl, this.path)\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url)\n    }\n\n    // Tells the LoadingManager to track an extra item, which resolves after\n    // the model is fully loaded. This means the count of items loaded will\n    // be incorrect, but ensures manager.onLoad() does not fire early.\n    this.manager.itemStart(url)\n\n    const _onError = function (e) {\n      if (onError) {\n        onError(e)\n      } else {\n        console.error(e)\n      }\n\n      scope.manager.itemError(url)\n      scope.manager.itemEnd(url)\n    }\n\n    const loader = new FileLoader(this.manager)\n\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (data) {\n        try {\n          scope.parse(\n            data,\n            resourcePath,\n            function (gltf) {\n              onLoad(gltf)\n\n              scope.manager.itemEnd(url)\n            },\n            _onError,\n          )\n        } catch (e) {\n          _onError(e)\n        }\n      },\n      onProgress,\n      _onError,\n    )\n  }\n\n  setDRACOLoader(dracoLoader) {\n    this.dracoLoader = dracoLoader\n    return this\n  }\n\n  setDDSLoader() {\n    throw new Error('THREE.GLTFLoader: \"MSFT_texture_dds\" no longer supported. Please update to \"KHR_texture_basisu\".')\n  }\n\n  setKTX2Loader(ktx2Loader) {\n    this.ktx2Loader = ktx2Loader\n    return this\n  }\n\n  setMeshoptDecoder(meshoptDecoder) {\n    this.meshoptDecoder = meshoptDecoder\n    return this\n  }\n\n  register(callback) {\n    if (this.pluginCallbacks.indexOf(callback) === -1) {\n      this.pluginCallbacks.push(callback)\n    }\n\n    return this\n  }\n\n  unregister(callback) {\n    if (this.pluginCallbacks.indexOf(callback) !== -1) {\n      this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1)\n    }\n\n    return this\n  }\n\n  parse(data, path, onLoad, onError) {\n    let json\n    const extensions = {}\n    const plugins = {}\n\n    if (typeof data === 'string') {\n      json = JSON.parse(data)\n    } else if (data instanceof ArrayBuffer) {\n      const magic = decodeText(new Uint8Array(data.slice(0, 4)))\n\n      if (magic === BINARY_EXTENSION_HEADER_MAGIC) {\n        try {\n          extensions[EXTENSIONS.KHR_BINARY_GLTF] = new GLTFBinaryExtension(data)\n        } catch (error) {\n          if (onError) onError(error)\n          return\n        }\n\n        json = JSON.parse(extensions[EXTENSIONS.KHR_BINARY_GLTF].content)\n      } else {\n        json = JSON.parse(decodeText(new Uint8Array(data)))\n      }\n    } else {\n      json = data\n    }\n\n    if (json.asset === undefined || json.asset.version[0] < 2) {\n      if (onError) onError(new Error('THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.'))\n      return\n    }\n\n    const parser = new GLTFParser(json, {\n      path: path || this.resourcePath || '',\n      crossOrigin: this.crossOrigin,\n      requestHeader: this.requestHeader,\n      manager: this.manager,\n      ktx2Loader: this.ktx2Loader,\n      meshoptDecoder: this.meshoptDecoder,\n    })\n\n    parser.fileLoader.setRequestHeader(this.requestHeader)\n\n    for (let i = 0; i < this.pluginCallbacks.length; i++) {\n      const plugin = this.pluginCallbacks[i](parser)\n\n      if (!plugin.name) console.error('THREE.GLTFLoader: Invalid plugin found: missing name')\n\n      plugins[plugin.name] = plugin\n\n      // Workaround to avoid determining as unknown extension\n      // in addUnknownExtensionsToUserData().\n      // Remove this workaround if we move all the existing\n      // extension handlers to plugin system\n      extensions[plugin.name] = true\n    }\n\n    if (json.extensionsUsed) {\n      for (let i = 0; i < json.extensionsUsed.length; ++i) {\n        const extensionName = json.extensionsUsed[i]\n        const extensionsRequired = json.extensionsRequired || []\n\n        switch (extensionName) {\n          case EXTENSIONS.KHR_MATERIALS_UNLIT:\n            extensions[extensionName] = new GLTFMaterialsUnlitExtension()\n            break\n\n          case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n            extensions[extensionName] = new GLTFDracoMeshCompressionExtension(json, this.dracoLoader)\n            break\n\n          case EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n            extensions[extensionName] = new GLTFTextureTransformExtension()\n            break\n\n          case EXTENSIONS.KHR_MESH_QUANTIZATION:\n            extensions[extensionName] = new GLTFMeshQuantizationExtension()\n            break\n\n          default:\n            if (extensionsRequired.indexOf(extensionName) >= 0 && plugins[extensionName] === undefined) {\n              console.warn('THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".')\n            }\n        }\n      }\n    }\n\n    parser.setExtensions(extensions)\n    parser.setPlugins(plugins)\n    parser.parse(onLoad, onError)\n  }\n\n  parseAsync(data, path) {\n    const scope = this\n\n    return new Promise(function (resolve, reject) {\n      scope.parse(data, path, resolve, reject)\n    })\n  }\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n  let objects = {}\n\n  return {\n    get: function (key) {\n      return objects[key]\n    },\n\n    add: function (key, object) {\n      objects[key] = object\n    },\n\n    remove: function (key) {\n      delete objects[key]\n    },\n\n    removeAll: function () {\n      objects = {}\n    },\n  }\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n  KHR_BINARY_GLTF: 'KHR_binary_glTF',\n  KHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n  KHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n  KHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n  KHR_MATERIALS_DISPERSION: 'KHR_materials_dispersion',\n  KHR_MATERIALS_IOR: 'KHR_materials_ior',\n  KHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n  KHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n  KHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n  KHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n  KHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n  KHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n  KHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n  KHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n  KHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n  KHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n  KHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n  EXT_MATERIALS_BUMP: 'EXT_materials_bump',\n  EXT_TEXTURE_WEBP: 'EXT_texture_webp',\n  EXT_TEXTURE_AVIF: 'EXT_texture_avif',\n  EXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n  EXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing',\n}\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightsExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL\n\n    // Object3D instance caches\n    this.cache = { refs: {}, uses: {} }\n  }\n\n  _markDefs() {\n    const parser = this.parser\n    const nodeDefs = this.parser.json.nodes || []\n\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex]\n\n      if (nodeDef.extensions && nodeDef.extensions[this.name] && nodeDef.extensions[this.name].light !== undefined) {\n        parser._addNodeRef(this.cache, nodeDef.extensions[this.name].light)\n      }\n    }\n  }\n\n  _loadLight(lightIndex) {\n    const parser = this.parser\n    const cacheKey = 'light:' + lightIndex\n    let dependency = parser.cache.get(cacheKey)\n\n    if (dependency) return dependency\n\n    const json = parser.json\n    const extensions = (json.extensions && json.extensions[this.name]) || {}\n    const lightDefs = extensions.lights || []\n    const lightDef = lightDefs[lightIndex]\n    let lightNode\n\n    const color = new Color(0xffffff)\n\n    if (lightDef.color !== undefined)\n      color.setRGB(lightDef.color[0], lightDef.color[1], lightDef.color[2], LinearSRGBColorSpace)\n\n    const range = lightDef.range !== undefined ? lightDef.range : 0\n\n    switch (lightDef.type) {\n      case 'directional':\n        lightNode = new DirectionalLight(color)\n        lightNode.target.position.set(0, 0, -1)\n        lightNode.add(lightNode.target)\n        break\n\n      case 'point':\n        lightNode = new PointLight(color)\n        lightNode.distance = range\n        break\n\n      case 'spot':\n        lightNode = new SpotLight(color)\n        lightNode.distance = range\n        // Handle spotlight properties.\n        lightDef.spot = lightDef.spot || {}\n        lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0\n        lightDef.spot.outerConeAngle =\n          lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0\n        lightNode.angle = lightDef.spot.outerConeAngle\n        lightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle\n        lightNode.target.position.set(0, 0, -1)\n        lightNode.add(lightNode.target)\n        break\n\n      default:\n        throw new Error('THREE.GLTFLoader: Unexpected light type: ' + lightDef.type)\n    }\n\n    // Some lights (e.g. spot) default to a position other than the origin. Reset the position\n    // here, because node-level parsing will only override position if explicitly specified.\n    lightNode.position.set(0, 0, 0)\n\n    lightNode.decay = 2\n\n    assignExtrasToUserData(lightNode, lightDef)\n\n    if (lightDef.intensity !== undefined) lightNode.intensity = lightDef.intensity\n\n    lightNode.name = parser.createUniqueName(lightDef.name || 'light_' + lightIndex)\n\n    dependency = Promise.resolve(lightNode)\n\n    parser.cache.add(cacheKey, dependency)\n\n    return dependency\n  }\n\n  getDependency(type, index) {\n    if (type !== 'light') return\n\n    return this._loadLight(index)\n  }\n\n  createNodeAttachment(nodeIndex) {\n    const self = this\n    const parser = this.parser\n    const json = parser.json\n    const nodeDef = json.nodes[nodeIndex]\n    const lightDef = (nodeDef.extensions && nodeDef.extensions[this.name]) || {}\n    const lightIndex = lightDef.light\n\n    if (lightIndex === undefined) return null\n\n    return this._loadLight(lightIndex).then(function (light) {\n      return parser._getNodeRef(self.cache, lightIndex, light)\n    })\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MATERIALS_UNLIT\n  }\n\n  getMaterialType() {\n    return MeshBasicMaterial\n  }\n\n  extendParams(materialParams, materialDef, parser) {\n    const pending = []\n\n    materialParams.color = new Color(1.0, 1.0, 1.0)\n    materialParams.opacity = 1.0\n\n    const metallicRoughness = materialDef.pbrMetallicRoughness\n\n    if (metallicRoughness) {\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor\n\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace)\n        materialParams.opacity = array[3]\n      }\n\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace))\n      }\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const emissiveStrength = materialDef.extensions[this.name].emissiveStrength\n\n    if (emissiveStrength !== undefined) {\n      materialParams.emissiveIntensity = emissiveStrength\n    }\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.clearcoatFactor !== undefined) {\n      materialParams.clearcoat = extension.clearcoatFactor\n    }\n\n    if (extension.clearcoatTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatMap', extension.clearcoatTexture))\n    }\n\n    if (extension.clearcoatRoughnessFactor !== undefined) {\n      materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor\n    }\n\n    if (extension.clearcoatRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture))\n    }\n\n    if (extension.clearcoatNormalTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture))\n\n      if (extension.clearcoatNormalTexture.scale !== undefined) {\n        const scale = extension.clearcoatNormalTexture.scale\n\n        materialParams.clearcoatNormalScale = new Vector2(scale, scale)\n      }\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials dispersion Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_dispersion\n */\nclass GLTFMaterialsDispersionExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_DISPERSION\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.dispersion = extension.dispersion !== undefined ? extension.dispersion : 0\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.iridescenceFactor !== undefined) {\n      materialParams.iridescence = extension.iridescenceFactor\n    }\n\n    if (extension.iridescenceTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceMap', extension.iridescenceTexture))\n    }\n\n    if (extension.iridescenceIor !== undefined) {\n      materialParams.iridescenceIOR = extension.iridescenceIor\n    }\n\n    if (materialParams.iridescenceThicknessRange === undefined) {\n      materialParams.iridescenceThicknessRange = [100, 400]\n    }\n\n    if (extension.iridescenceThicknessMinimum !== undefined) {\n      materialParams.iridescenceThicknessRange[0] = extension.iridescenceThicknessMinimum\n    }\n\n    if (extension.iridescenceThicknessMaximum !== undefined) {\n      materialParams.iridescenceThicknessRange[1] = extension.iridescenceThicknessMaximum\n    }\n\n    if (extension.iridescenceThicknessTexture !== undefined) {\n      pending.push(\n        parser.assignTexture(materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture),\n      )\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_SHEEN\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    materialParams.sheenColor = new Color(0, 0, 0)\n    materialParams.sheenRoughness = 0\n    materialParams.sheen = 1\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.sheenColorFactor !== undefined) {\n      const colorFactor = extension.sheenColorFactor\n      materialParams.sheenColor.setRGB(colorFactor[0], colorFactor[1], colorFactor[2], LinearSRGBColorSpace)\n    }\n\n    if (extension.sheenRoughnessFactor !== undefined) {\n      materialParams.sheenRoughness = extension.sheenRoughnessFactor\n    }\n\n    if (extension.sheenColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace))\n    }\n\n    if (extension.sheenRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.transmissionFactor !== undefined) {\n      materialParams.transmission = extension.transmissionFactor\n    }\n\n    if (extension.transmissionTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'transmissionMap', extension.transmissionTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_VOLUME\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0\n\n    if (extension.thicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'thicknessMap', extension.thicknessTexture))\n    }\n\n    materialParams.attenuationDistance = extension.attenuationDistance || Infinity\n\n    const colorArray = extension.attenuationColor || [1, 1, 1]\n    materialParams.attenuationColor = new Color().setRGB(\n      colorArray[0],\n      colorArray[1],\n      colorArray[2],\n      LinearSRGBColorSpace,\n    )\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_IOR\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.ior = extension.ior !== undefined ? extension.ior : 1.5\n\n    return Promise.resolve()\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0\n\n    if (extension.specularTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularIntensityMap', extension.specularTexture))\n    }\n\n    const colorArray = extension.specularColorFactor || [1, 1, 1]\n    materialParams.specularColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace)\n\n    if (extension.specularColorTexture !== undefined) {\n      pending.push(\n        parser.assignTexture(materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace),\n      )\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials bump Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_materials_bump\n */\nclass GLTFMaterialsBumpExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_MATERIALS_BUMP\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    materialParams.bumpScale = extension.bumpFactor !== undefined ? extension.bumpFactor : 1.0\n\n    if (extension.bumpTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'bumpMap', extension.bumpTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY\n  }\n\n  getMaterialType(materialIndex) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null\n\n    return MeshPhysicalMaterial\n  }\n\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser\n    const materialDef = parser.json.materials[materialIndex]\n\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve()\n    }\n\n    const pending = []\n\n    const extension = materialDef.extensions[this.name]\n\n    if (extension.anisotropyStrength !== undefined) {\n      materialParams.anisotropy = extension.anisotropyStrength\n    }\n\n    if (extension.anisotropyRotation !== undefined) {\n      materialParams.anisotropyRotation = extension.anisotropyRotation\n    }\n\n    if (extension.anisotropyTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'anisotropyMap', extension.anisotropyTexture))\n    }\n\n    return Promise.all(pending)\n  }\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n */\nclass GLTFTextureBasisUExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.KHR_TEXTURE_BASISU\n  }\n\n  loadTexture(textureIndex) {\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[this.name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[this.name]\n    const loader = parser.options.ktx2Loader\n\n    if (!loader) {\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n        throw new Error('THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures')\n      } else {\n        // Assumes that the extension is optional and that a fallback texture is present\n        return null\n      }\n    }\n\n    return parser.loadTextureImage(textureIndex, extension.source, loader)\n  }\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n */\nclass GLTFTextureWebPExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_TEXTURE_WEBP\n    this.isSupported = null\n  }\n\n  loadTexture(textureIndex) {\n    const name = this.name\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[name]\n    const source = json.images[extension.source]\n\n    let loader = parser.textureLoader\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader)\n\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: WebP required by asset but unsupported.')\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex)\n    })\n  }\n\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image()\n\n        // Lossy test image. Support for lossy images doesn't guarantee support for all\n        // WebP images, unfortunately.\n        image.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA'\n\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1)\n        }\n      })\n    }\n\n    return this.isSupported\n  }\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n */\nclass GLTFTextureAVIFExtension {\n  constructor(parser) {\n    this.parser = parser\n    this.name = EXTENSIONS.EXT_TEXTURE_AVIF\n    this.isSupported = null\n  }\n\n  loadTexture(textureIndex) {\n    const name = this.name\n    const parser = this.parser\n    const json = parser.json\n\n    const textureDef = json.textures[textureIndex]\n\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null\n    }\n\n    const extension = textureDef.extensions[name]\n    const source = json.images[extension.source]\n\n    let loader = parser.textureLoader\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader)\n\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: AVIF required by asset but unsupported.')\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex)\n    })\n  }\n\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image()\n\n        // Lossy test image.\n        image.src =\n          'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI='\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1)\n        }\n      })\n    }\n\n    return this.isSupported\n  }\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n */\nclass GLTFMeshoptCompression {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION\n    this.parser = parser\n  }\n\n  loadBufferView(index) {\n    const json = this.parser.json\n    const bufferView = json.bufferViews[index]\n\n    if (bufferView.extensions && bufferView.extensions[this.name]) {\n      const extensionDef = bufferView.extensions[this.name]\n\n      const buffer = this.parser.getDependency('buffer', extensionDef.buffer)\n      const decoder = this.parser.options.meshoptDecoder\n\n      if (!decoder || !decoder.supported) {\n        if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n          throw new Error('THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files')\n        } else {\n          // Assumes that the extension is optional and that fallback buffer data is present\n          return null\n        }\n      }\n\n      return buffer.then(function (res) {\n        const byteOffset = extensionDef.byteOffset || 0\n        const byteLength = extensionDef.byteLength || 0\n\n        const count = extensionDef.count\n        const stride = extensionDef.byteStride\n\n        const source = new Uint8Array(res, byteOffset, byteLength)\n\n        if (decoder.decodeGltfBufferAsync) {\n          return decoder\n            .decodeGltfBufferAsync(count, stride, source, extensionDef.mode, extensionDef.filter)\n            .then(function (res) {\n              return res.buffer\n            })\n        } else {\n          // Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n          return decoder.ready.then(function () {\n            const result = new ArrayBuffer(count * stride)\n            decoder.decodeGltfBuffer(\n              new Uint8Array(result),\n              count,\n              stride,\n              source,\n              extensionDef.mode,\n              extensionDef.filter,\n            )\n            return result\n          })\n        }\n      })\n    } else {\n      return null\n    }\n  }\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n */\nclass GLTFMeshGpuInstancing {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING\n    this.parser = parser\n  }\n\n  createNodeMesh(nodeIndex) {\n    const json = this.parser.json\n    const nodeDef = json.nodes[nodeIndex]\n\n    if (!nodeDef.extensions || !nodeDef.extensions[this.name] || nodeDef.mesh === undefined) {\n      return null\n    }\n\n    const meshDef = json.meshes[nodeDef.mesh]\n\n    // No Points or Lines + Instancing support yet\n\n    for (const primitive of meshDef.primitives) {\n      if (\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLES &&\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP &&\n        primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN &&\n        primitive.mode !== undefined\n      ) {\n        return null\n      }\n    }\n\n    const extensionDef = nodeDef.extensions[this.name]\n    const attributesDef = extensionDef.attributes\n\n    // @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n    const pending = []\n    const attributes = {}\n\n    for (const key in attributesDef) {\n      pending.push(\n        this.parser.getDependency('accessor', attributesDef[key]).then((accessor) => {\n          attributes[key] = accessor\n          return attributes[key]\n        }),\n      )\n    }\n\n    if (pending.length < 1) {\n      return null\n    }\n\n    pending.push(this.parser.createNodeMesh(nodeIndex))\n\n    return Promise.all(pending).then((results) => {\n      const nodeObject = results.pop()\n      const meshes = nodeObject.isGroup ? nodeObject.children : [nodeObject]\n      const count = results[0].count // All attribute counts should be same\n      const instancedMeshes = []\n\n      for (const mesh of meshes) {\n        // Temporal variables\n        const m = new Matrix4()\n        const p = new Vector3()\n        const q = new Quaternion()\n        const s = new Vector3(1, 1, 1)\n\n        const instancedMesh = new InstancedMesh(mesh.geometry, mesh.material, count)\n\n        for (let i = 0; i < count; i++) {\n          if (attributes.TRANSLATION) {\n            p.fromBufferAttribute(attributes.TRANSLATION, i)\n          }\n\n          if (attributes.ROTATION) {\n            q.fromBufferAttribute(attributes.ROTATION, i)\n          }\n\n          if (attributes.SCALE) {\n            s.fromBufferAttribute(attributes.SCALE, i)\n          }\n\n          instancedMesh.setMatrixAt(i, m.compose(p, q, s))\n        }\n\n        // Add instance attributes to the geometry, excluding TRS.\n        for (const attributeName in attributes) {\n          if (attributeName === '_COLOR_0') {\n            const attr = attributes[attributeName]\n            instancedMesh.instanceColor = new InstancedBufferAttribute(attr.array, attr.itemSize, attr.normalized)\n          } else if (attributeName !== 'TRANSLATION' && attributeName !== 'ROTATION' && attributeName !== 'SCALE') {\n            mesh.geometry.setAttribute(attributeName, attributes[attributeName])\n          }\n        }\n\n        // Just in case\n        Object3D.prototype.copy.call(instancedMesh, mesh)\n\n        this.parser.assignFinalMaterial(instancedMesh)\n\n        instancedMeshes.push(instancedMesh)\n      }\n\n      if (nodeObject.isGroup) {\n        nodeObject.clear()\n\n        nodeObject.add(...instancedMeshes)\n\n        return nodeObject\n      }\n\n      return instancedMeshes[0]\n    })\n  }\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF'\nconst BINARY_EXTENSION_HEADER_LENGTH = 12\nconst BINARY_EXTENSION_CHUNK_TYPES = { JSON: 0x4e4f534a, BIN: 0x004e4942 }\n\nclass GLTFBinaryExtension {\n  constructor(data) {\n    this.name = EXTENSIONS.KHR_BINARY_GLTF\n    this.content = null\n    this.body = null\n\n    const headerView = new DataView(data, 0, BINARY_EXTENSION_HEADER_LENGTH)\n\n    this.header = {\n      magic: decodeText(new Uint8Array(data.slice(0, 4))),\n      version: headerView.getUint32(4, true),\n      length: headerView.getUint32(8, true),\n    }\n\n    if (this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC) {\n      throw new Error('THREE.GLTFLoader: Unsupported glTF-Binary header.')\n    } else if (this.header.version < 2.0) {\n      throw new Error('THREE.GLTFLoader: Legacy binary file detected.')\n    }\n\n    const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH\n    const chunkView = new DataView(data, BINARY_EXTENSION_HEADER_LENGTH)\n    let chunkIndex = 0\n\n    while (chunkIndex < chunkContentsLength) {\n      const chunkLength = chunkView.getUint32(chunkIndex, true)\n      chunkIndex += 4\n\n      const chunkType = chunkView.getUint32(chunkIndex, true)\n      chunkIndex += 4\n\n      if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON) {\n        const contentArray = new Uint8Array(data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength)\n        this.content = decodeText(contentArray)\n      } else if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN) {\n        const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex\n        this.body = data.slice(byteOffset, byteOffset + chunkLength)\n      }\n\n      // Clients must ignore chunks with unknown types.\n\n      chunkIndex += chunkLength\n    }\n\n    if (this.content === null) {\n      throw new Error('THREE.GLTFLoader: JSON content not found.')\n    }\n  }\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n */\nclass GLTFDracoMeshCompressionExtension {\n  constructor(json, dracoLoader) {\n    if (!dracoLoader) {\n      throw new Error('THREE.GLTFLoader: No DRACOLoader instance provided.')\n    }\n\n    this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION\n    this.json = json\n    this.dracoLoader = dracoLoader\n    this.dracoLoader.preload()\n  }\n\n  decodePrimitive(primitive, parser) {\n    const json = this.json\n    const dracoLoader = this.dracoLoader\n    const bufferViewIndex = primitive.extensions[this.name].bufferView\n    const gltfAttributeMap = primitive.extensions[this.name].attributes\n    const threeAttributeMap = {}\n    const attributeNormalizedMap = {}\n    const attributeTypeMap = {}\n\n    for (const attributeName in gltfAttributeMap) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase()\n\n      threeAttributeMap[threeAttributeName] = gltfAttributeMap[attributeName]\n    }\n\n    for (const attributeName in primitive.attributes) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase()\n\n      if (gltfAttributeMap[attributeName] !== undefined) {\n        const accessorDef = json.accessors[primitive.attributes[attributeName]]\n        const componentType = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n\n        attributeTypeMap[threeAttributeName] = componentType.name\n        attributeNormalizedMap[threeAttributeName] = accessorDef.normalized === true\n      }\n    }\n\n    return parser.getDependency('bufferView', bufferViewIndex).then(function (bufferView) {\n      return new Promise(function (resolve, reject) {\n        dracoLoader.decodeDracoFile(\n          bufferView,\n          function (geometry) {\n            for (const attributeName in geometry.attributes) {\n              const attribute = geometry.attributes[attributeName]\n              const normalized = attributeNormalizedMap[attributeName]\n\n              if (normalized !== undefined) attribute.normalized = normalized\n            }\n\n            resolve(geometry)\n          },\n          threeAttributeMap,\n          attributeTypeMap,\n          LinearSRGBColorSpace,\n          reject,\n        )\n      })\n    })\n  }\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n */\nclass GLTFTextureTransformExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM\n  }\n\n  extendTexture(texture, transform) {\n    if (\n      (transform.texCoord === undefined || transform.texCoord === texture.channel) &&\n      transform.offset === undefined &&\n      transform.rotation === undefined &&\n      transform.scale === undefined\n    ) {\n      // See https://github.com/mrdoob/three.js/issues/21819.\n      return texture\n    }\n\n    texture = texture.clone()\n\n    if (transform.texCoord !== undefined) {\n      texture.channel = transform.texCoord\n    }\n\n    if (transform.offset !== undefined) {\n      texture.offset.fromArray(transform.offset)\n    }\n\n    if (transform.rotation !== undefined) {\n      texture.rotation = transform.rotation\n    }\n\n    if (transform.scale !== undefined) {\n      texture.repeat.fromArray(transform.scale)\n    }\n\n    texture.needsUpdate = true\n\n    return texture\n  }\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n */\nclass GLTFMeshQuantizationExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MESH_QUANTIZATION\n  }\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer)\n  }\n\n  copySampleValue_(index) {\n    // Copies a sample value to the result buffer. See description of glTF\n    // CUBICSPLINE values layout in interpolate_() function below.\n\n    const result = this.resultBuffer,\n      values = this.sampleValues,\n      valueSize = this.valueSize,\n      offset = index * valueSize * 3 + valueSize\n\n    for (let i = 0; i !== valueSize; i++) {\n      result[i] = values[offset + i]\n    }\n\n    return result\n  }\n\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer\n    const values = this.sampleValues\n    const stride = this.valueSize\n\n    const stride2 = stride * 2\n    const stride3 = stride * 3\n\n    const td = t1 - t0\n\n    const p = (t - t0) / td\n    const pp = p * p\n    const ppp = pp * p\n\n    const offset1 = i1 * stride3\n    const offset0 = offset1 - stride3\n\n    const s2 = -2 * ppp + 3 * pp\n    const s3 = ppp - pp\n    const s0 = 1 - s2\n    const s1 = s3 - pp + p\n\n    // Layout of keyframe output values for CUBICSPLINE animations:\n    //   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n    for (let i = 0; i !== stride; i++) {\n      const p0 = values[offset0 + i + stride] // splineVertex_k\n      const m0 = values[offset0 + i + stride2] * td // outTangent_k * (t_k+1 - t_k)\n      const p1 = values[offset1 + i + stride] // splineVertex_k+1\n      const m1 = values[offset1 + i] * td // inTangent_k+1 * (t_k+1 - t_k)\n\n      result[i] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1\n    }\n\n    return result\n  }\n}\n\nconst _q = /* @__PURE__ */ new Quaternion()\n\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n  interpolate_(i1, t0, t, t1) {\n    const result = super.interpolate_(i1, t0, t, t1)\n\n    _q.fromArray(result).normalize().toArray(result)\n\n    return result\n  }\n}\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n  FLOAT: 5126,\n  //FLOAT_MAT2: 35674,\n  FLOAT_MAT3: 35675,\n  FLOAT_MAT4: 35676,\n  FLOAT_VEC2: 35664,\n  FLOAT_VEC3: 35665,\n  FLOAT_VEC4: 35666,\n  LINEAR: 9729,\n  REPEAT: 10497,\n  SAMPLER_2D: 35678,\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  UNSIGNED_BYTE: 5121,\n  UNSIGNED_SHORT: 5123,\n}\n\nconst WEBGL_COMPONENT_TYPES = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array,\n}\n\nconst WEBGL_FILTERS = {\n  9728: NearestFilter,\n  9729: LinearFilter,\n  9984: NearestMipmapNearestFilter,\n  9985: LinearMipmapNearestFilter,\n  9986: NearestMipmapLinearFilter,\n  9987: LinearMipmapLinearFilter,\n}\n\nconst WEBGL_WRAPPINGS = {\n  33071: ClampToEdgeWrapping,\n  33648: MirroredRepeatWrapping,\n  10497: RepeatWrapping,\n}\n\nconst WEBGL_TYPE_SIZES = {\n  SCALAR: 1,\n  VEC2: 2,\n  VEC3: 3,\n  VEC4: 4,\n  MAT2: 4,\n  MAT3: 9,\n  MAT4: 16,\n}\n\nconst ATTRIBUTES = {\n  POSITION: 'position',\n  NORMAL: 'normal',\n  TANGENT: 'tangent',\n  // uv => uv1, 4 uv channels\n  // https://github.com/mrdoob/three.js/pull/25943\n  // https://github.com/mrdoob/three.js/pull/25788\n  ...(version >= 152\n    ? {\n        TEXCOORD_0: 'uv',\n        TEXCOORD_1: 'uv1',\n        TEXCOORD_2: 'uv2',\n        TEXCOORD_3: 'uv3',\n      }\n    : {\n        TEXCOORD_0: 'uv',\n        TEXCOORD_1: 'uv2',\n      }),\n\n  COLOR_0: 'color',\n  WEIGHTS_0: 'skinWeight',\n  JOINTS_0: 'skinIndex',\n}\n\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  translation: 'position',\n  rotation: 'quaternion',\n  weights: 'morphTargetInfluences',\n}\n\nconst INTERPOLATION = {\n  CUBICSPLINE: undefined, // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n  // keyframe track will be initialized with a default interpolation type, then modified.\n  LINEAR: InterpolateLinear,\n  STEP: InterpolateDiscrete,\n}\n\nconst ALPHA_MODES = {\n  OPAQUE: 'OPAQUE',\n  MASK: 'MASK',\n  BLEND: 'BLEND',\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n */\nfunction createDefaultMaterial(cache) {\n  if (cache['DefaultMaterial'] === undefined) {\n    cache['DefaultMaterial'] = new MeshStandardMaterial({\n      color: 0xffffff,\n      emissive: 0x000000,\n      metalness: 1,\n      roughness: 1,\n      transparent: false,\n      depthTest: true,\n      side: FrontSide,\n    })\n  }\n\n  return cache['DefaultMaterial']\n}\n\nfunction addUnknownExtensionsToUserData(knownExtensions, object, objectDef) {\n  // Add unknown glTF extensions to an object's userData.\n\n  for (const name in objectDef.extensions) {\n    if (knownExtensions[name] === undefined) {\n      object.userData.gltfExtensions = object.userData.gltfExtensions || {}\n      object.userData.gltfExtensions[name] = objectDef.extensions[name]\n    }\n  }\n}\n\n/**\n * @param {Object3D|Material|BufferGeometry} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData(object, gltfDef) {\n  if (gltfDef.extras !== undefined) {\n    if (typeof gltfDef.extras === 'object') {\n      Object.assign(object.userData, gltfDef.extras)\n    } else {\n      console.warn('THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras)\n    }\n  }\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets(geometry, targets, parser) {\n  let hasMorphPosition = false\n  let hasMorphNormal = false\n  let hasMorphColor = false\n\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i]\n\n    if (target.POSITION !== undefined) hasMorphPosition = true\n    if (target.NORMAL !== undefined) hasMorphNormal = true\n    if (target.COLOR_0 !== undefined) hasMorphColor = true\n\n    if (hasMorphPosition && hasMorphNormal && hasMorphColor) break\n  }\n\n  if (!hasMorphPosition && !hasMorphNormal && !hasMorphColor) return Promise.resolve(geometry)\n\n  const pendingPositionAccessors = []\n  const pendingNormalAccessors = []\n  const pendingColorAccessors = []\n\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i]\n\n    if (hasMorphPosition) {\n      const pendingAccessor =\n        target.POSITION !== undefined ? parser.getDependency('accessor', target.POSITION) : geometry.attributes.position\n\n      pendingPositionAccessors.push(pendingAccessor)\n    }\n\n    if (hasMorphNormal) {\n      const pendingAccessor =\n        target.NORMAL !== undefined ? parser.getDependency('accessor', target.NORMAL) : geometry.attributes.normal\n\n      pendingNormalAccessors.push(pendingAccessor)\n    }\n\n    if (hasMorphColor) {\n      const pendingAccessor =\n        target.COLOR_0 !== undefined ? parser.getDependency('accessor', target.COLOR_0) : geometry.attributes.color\n\n      pendingColorAccessors.push(pendingAccessor)\n    }\n  }\n\n  return Promise.all([\n    Promise.all(pendingPositionAccessors),\n    Promise.all(pendingNormalAccessors),\n    Promise.all(pendingColorAccessors),\n  ]).then(function (accessors) {\n    const morphPositions = accessors[0]\n    const morphNormals = accessors[1]\n    const morphColors = accessors[2]\n\n    if (hasMorphPosition) geometry.morphAttributes.position = morphPositions\n    if (hasMorphNormal) geometry.morphAttributes.normal = morphNormals\n    if (hasMorphColor) geometry.morphAttributes.color = morphColors\n    geometry.morphTargetsRelative = true\n\n    return geometry\n  })\n}\n\n/**\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets(mesh, meshDef) {\n  mesh.updateMorphTargets()\n\n  if (meshDef.weights !== undefined) {\n    for (let i = 0, il = meshDef.weights.length; i < il; i++) {\n      mesh.morphTargetInfluences[i] = meshDef.weights[i]\n    }\n  }\n\n  // .extras has user-defined data, so check that .extras.targetNames is an array.\n  if (meshDef.extras && Array.isArray(meshDef.extras.targetNames)) {\n    const targetNames = meshDef.extras.targetNames\n\n    if (mesh.morphTargetInfluences.length === targetNames.length) {\n      mesh.morphTargetDictionary = {}\n\n      for (let i = 0, il = targetNames.length; i < il; i++) {\n        mesh.morphTargetDictionary[targetNames[i]] = i\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.')\n    }\n  }\n}\n\nfunction createPrimitiveKey(primitiveDef) {\n  let geometryKey\n\n  const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]\n\n  if (dracoExtension) {\n    geometryKey =\n      'draco:' +\n      dracoExtension.bufferView +\n      ':' +\n      dracoExtension.indices +\n      ':' +\n      createAttributesKey(dracoExtension.attributes)\n  } else {\n    geometryKey = primitiveDef.indices + ':' + createAttributesKey(primitiveDef.attributes) + ':' + primitiveDef.mode\n  }\n\n  if (primitiveDef.targets !== undefined) {\n    for (let i = 0, il = primitiveDef.targets.length; i < il; i++) {\n      geometryKey += ':' + createAttributesKey(primitiveDef.targets[i])\n    }\n  }\n\n  return geometryKey\n}\n\nfunction createAttributesKey(attributes) {\n  let attributesKey = ''\n\n  const keys = Object.keys(attributes).sort()\n\n  for (let i = 0, il = keys.length; i < il; i++) {\n    attributesKey += keys[i] + ':' + attributes[keys[i]] + ';'\n  }\n\n  return attributesKey\n}\n\nfunction getNormalizedComponentScale(constructor) {\n  // Reference:\n  // https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n  switch (constructor) {\n    case Int8Array:\n      return 1 / 127\n\n    case Uint8Array:\n      return 1 / 255\n\n    case Int16Array:\n      return 1 / 32767\n\n    case Uint16Array:\n      return 1 / 65535\n\n    default:\n      throw new Error('THREE.GLTFLoader: Unsupported normalized accessor component type.')\n  }\n}\n\nfunction getImageURIMimeType(uri) {\n  if (uri.search(/\\.jpe?g($|\\?)/i) > 0 || uri.search(/^data\\:image\\/jpeg/) === 0) return 'image/jpeg'\n  if (uri.search(/\\.webp($|\\?)/i) > 0 || uri.search(/^data\\:image\\/webp/) === 0) return 'image/webp'\n\n  return 'image/png'\n}\n\nconst _identityMatrix = /* @__PURE__ */ new Matrix4()\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n  constructor(json = {}, options = {}) {\n    this.json = json\n    this.extensions = {}\n    this.plugins = {}\n    this.options = options\n\n    // loader object cache\n    this.cache = new GLTFRegistry()\n\n    // associations between Three.js objects and glTF elements\n    this.associations = new Map()\n\n    // BufferGeometry caching\n    this.primitiveCache = {}\n\n    // Node cache\n    this.nodeCache = {}\n\n    // Object3D instance caches\n    this.meshCache = { refs: {}, uses: {} }\n    this.cameraCache = { refs: {}, uses: {} }\n    this.lightCache = { refs: {}, uses: {} }\n\n    this.sourceCache = {}\n    this.textureCache = {}\n\n    // Track node names, to ensure no duplicates\n    this.nodeNamesUsed = {}\n\n    // Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n    // expensive work of uploading a texture to the GPU off the main thread.\n\n    let isSafari = false\n    let isFirefox = false\n    let firefoxVersion = -1\n\n    if (typeof navigator !== 'undefined' && typeof navigator.userAgent !== 'undefined') {\n      isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) === true\n      isFirefox = navigator.userAgent.indexOf('Firefox') > -1\n      firefoxVersion = isFirefox ? navigator.userAgent.match(/Firefox\\/([0-9]+)\\./)[1] : -1\n    }\n\n    if (typeof createImageBitmap === 'undefined' || isSafari || (isFirefox && firefoxVersion < 98)) {\n      this.textureLoader = new TextureLoader(this.options.manager)\n    } else {\n      this.textureLoader = new ImageBitmapLoader(this.options.manager)\n    }\n\n    this.textureLoader.setCrossOrigin(this.options.crossOrigin)\n    this.textureLoader.setRequestHeader(this.options.requestHeader)\n\n    this.fileLoader = new FileLoader(this.options.manager)\n    this.fileLoader.setResponseType('arraybuffer')\n\n    if (this.options.crossOrigin === 'use-credentials') {\n      this.fileLoader.setWithCredentials(true)\n    }\n  }\n\n  setExtensions(extensions) {\n    this.extensions = extensions\n  }\n\n  setPlugins(plugins) {\n    this.plugins = plugins\n  }\n\n  parse(onLoad, onError) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n\n    // Clear the loader cache\n    this.cache.removeAll()\n    this.nodeCache = {}\n\n    // Mark the special nodes/meshes in json for efficient parse\n    this._invokeAll(function (ext) {\n      return ext._markDefs && ext._markDefs()\n    })\n\n    Promise.all(\n      this._invokeAll(function (ext) {\n        return ext.beforeRoot && ext.beforeRoot()\n      }),\n    )\n      .then(function () {\n        return Promise.all([\n          parser.getDependencies('scene'),\n          parser.getDependencies('animation'),\n          parser.getDependencies('camera'),\n        ])\n      })\n      .then(function (dependencies) {\n        const result = {\n          scene: dependencies[0][json.scene || 0],\n          scenes: dependencies[0],\n          animations: dependencies[1],\n          cameras: dependencies[2],\n          asset: json.asset,\n          parser: parser,\n          userData: {},\n        }\n\n        addUnknownExtensionsToUserData(extensions, result, json)\n\n        assignExtrasToUserData(result, json)\n\n        return Promise.all(\n          parser._invokeAll(function (ext) {\n            return ext.afterRoot && ext.afterRoot(result)\n          }),\n        ).then(function () {\n          for (const scene of result.scenes) {\n            scene.updateMatrixWorld()\n          }\n\n          onLoad(result)\n        })\n      })\n      .catch(onError)\n  }\n\n  /**\n   * Marks the special nodes/meshes in json for efficient parse.\n   */\n  _markDefs() {\n    const nodeDefs = this.json.nodes || []\n    const skinDefs = this.json.skins || []\n    const meshDefs = this.json.meshes || []\n\n    // Nothing in the node definition indicates whether it is a Bone or an\n    // Object3D. Use the skins' joint references to mark bones.\n    for (let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex++) {\n      const joints = skinDefs[skinIndex].joints\n\n      for (let i = 0, il = joints.length; i < il; i++) {\n        nodeDefs[joints[i]].isBone = true\n      }\n    }\n\n    // Iterate over all nodes, marking references to shared resources,\n    // as well as skeleton joints.\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex]\n\n      if (nodeDef.mesh !== undefined) {\n        this._addNodeRef(this.meshCache, nodeDef.mesh)\n\n        // Nothing in the mesh definition indicates whether it is\n        // a SkinnedMesh or Mesh. Use the node's mesh reference\n        // to mark SkinnedMesh if node has skin.\n        if (nodeDef.skin !== undefined) {\n          meshDefs[nodeDef.mesh].isSkinnedMesh = true\n        }\n      }\n\n      if (nodeDef.camera !== undefined) {\n        this._addNodeRef(this.cameraCache, nodeDef.camera)\n      }\n    }\n  }\n\n  /**\n   * Counts references to shared node / Object3D resources. These resources\n   * can be reused, or \"instantiated\", at multiple nodes in the scene\n   * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n   * be marked. Non-scenegraph resources (like Materials, Geometries, and\n   * Textures) can be reused directly and are not marked here.\n   *\n   * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n   */\n  _addNodeRef(cache, index) {\n    if (index === undefined) return\n\n    if (cache.refs[index] === undefined) {\n      cache.refs[index] = cache.uses[index] = 0\n    }\n\n    cache.refs[index]++\n  }\n\n  /** Returns a reference to a shared resource, cloning it if necessary. */\n  _getNodeRef(cache, index, object) {\n    if (cache.refs[index] <= 1) return object\n\n    const ref = object.clone()\n\n    // Propagates mappings to the cloned object, prevents mappings on the\n    // original object from being lost.\n    const updateMappings = (original, clone) => {\n      const mappings = this.associations.get(original)\n      if (mappings != null) {\n        this.associations.set(clone, mappings)\n      }\n\n      for (const [i, child] of original.children.entries()) {\n        updateMappings(child, clone.children[i])\n      }\n    }\n\n    updateMappings(object, ref)\n\n    ref.name += '_instance_' + cache.uses[index]++\n\n    return ref\n  }\n\n  _invokeOne(func) {\n    const extensions = Object.values(this.plugins)\n    extensions.push(this)\n\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i])\n\n      if (result) return result\n    }\n\n    return null\n  }\n\n  _invokeAll(func) {\n    const extensions = Object.values(this.plugins)\n    extensions.unshift(this)\n\n    const pending = []\n\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i])\n\n      if (result) pending.push(result)\n    }\n\n    return pending\n  }\n\n  /**\n   * Requests the specified dependency asynchronously, with caching.\n   * @param {string} type\n   * @param {number} index\n   * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n   */\n  getDependency(type, index) {\n    const cacheKey = type + ':' + index\n    let dependency = this.cache.get(cacheKey)\n\n    if (!dependency) {\n      switch (type) {\n        case 'scene':\n          dependency = this.loadScene(index)\n          break\n\n        case 'node':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadNode && ext.loadNode(index)\n          })\n          break\n\n        case 'mesh':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMesh && ext.loadMesh(index)\n          })\n          break\n\n        case 'accessor':\n          dependency = this.loadAccessor(index)\n          break\n\n        case 'bufferView':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadBufferView && ext.loadBufferView(index)\n          })\n          break\n\n        case 'buffer':\n          dependency = this.loadBuffer(index)\n          break\n\n        case 'material':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMaterial && ext.loadMaterial(index)\n          })\n          break\n\n        case 'texture':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadTexture && ext.loadTexture(index)\n          })\n          break\n\n        case 'skin':\n          dependency = this.loadSkin(index)\n          break\n\n        case 'animation':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadAnimation && ext.loadAnimation(index)\n          })\n          break\n\n        case 'camera':\n          dependency = this.loadCamera(index)\n          break\n\n        default:\n          dependency = this._invokeOne(function (ext) {\n            return ext != this && ext.getDependency && ext.getDependency(type, index)\n          })\n\n          if (!dependency) {\n            throw new Error('Unknown type: ' + type)\n          }\n\n          break\n      }\n\n      this.cache.add(cacheKey, dependency)\n    }\n\n    return dependency\n  }\n\n  /**\n   * Requests all dependencies of the specified type asynchronously, with caching.\n   * @param {string} type\n   * @return {Promise<Array<Object>>}\n   */\n  getDependencies(type) {\n    let dependencies = this.cache.get(type)\n\n    if (!dependencies) {\n      const parser = this\n      const defs = this.json[type + (type === 'mesh' ? 'es' : 's')] || []\n\n      dependencies = Promise.all(\n        defs.map(function (def, index) {\n          return parser.getDependency(type, index)\n        }),\n      )\n\n      this.cache.add(type, dependencies)\n    }\n\n    return dependencies\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBuffer(bufferIndex) {\n    const bufferDef = this.json.buffers[bufferIndex]\n    const loader = this.fileLoader\n\n    if (bufferDef.type && bufferDef.type !== 'arraybuffer') {\n      throw new Error('THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.')\n    }\n\n    // If present, GLB container is required to be the first buffer.\n    if (bufferDef.uri === undefined && bufferIndex === 0) {\n      return Promise.resolve(this.extensions[EXTENSIONS.KHR_BINARY_GLTF].body)\n    }\n\n    const options = this.options\n\n    return new Promise(function (resolve, reject) {\n      loader.load(LoaderUtils.resolveURL(bufferDef.uri, options.path), resolve, undefined, function () {\n        reject(new Error('THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".'))\n      })\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferViewIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBufferView(bufferViewIndex) {\n    const bufferViewDef = this.json.bufferViews[bufferViewIndex]\n\n    return this.getDependency('buffer', bufferViewDef.buffer).then(function (buffer) {\n      const byteLength = bufferViewDef.byteLength || 0\n      const byteOffset = bufferViewDef.byteOffset || 0\n      return buffer.slice(byteOffset, byteOffset + byteLength)\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n   * @param {number} accessorIndex\n   * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n   */\n  loadAccessor(accessorIndex) {\n    const parser = this\n    const json = this.json\n\n    const accessorDef = this.json.accessors[accessorIndex]\n\n    if (accessorDef.bufferView === undefined && accessorDef.sparse === undefined) {\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type]\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n      const normalized = accessorDef.normalized === true\n\n      const array = new TypedArray(accessorDef.count * itemSize)\n      return Promise.resolve(new BufferAttribute(array, itemSize, normalized))\n    }\n\n    const pendingBufferViews = []\n\n    if (accessorDef.bufferView !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.bufferView))\n    } else {\n      pendingBufferViews.push(null)\n    }\n\n    if (accessorDef.sparse !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.indices.bufferView))\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.values.bufferView))\n    }\n\n    return Promise.all(pendingBufferViews).then(function (bufferViews) {\n      const bufferView = bufferViews[0]\n\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type]\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType]\n\n      // For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n      const elementBytes = TypedArray.BYTES_PER_ELEMENT\n      const itemBytes = elementBytes * itemSize\n      const byteOffset = accessorDef.byteOffset || 0\n      const byteStride =\n        accessorDef.bufferView !== undefined ? json.bufferViews[accessorDef.bufferView].byteStride : undefined\n      const normalized = accessorDef.normalized === true\n      let array, bufferAttribute\n\n      // The buffer is not interleaved if the stride is the item size in bytes.\n      if (byteStride && byteStride !== itemBytes) {\n        // Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n        // This makes sure that IBA.count reflects accessor.count properly\n        const ibSlice = Math.floor(byteOffset / byteStride)\n        const ibCacheKey =\n          'InterleavedBuffer:' +\n          accessorDef.bufferView +\n          ':' +\n          accessorDef.componentType +\n          ':' +\n          ibSlice +\n          ':' +\n          accessorDef.count\n        let ib = parser.cache.get(ibCacheKey)\n\n        if (!ib) {\n          array = new TypedArray(bufferView, ibSlice * byteStride, (accessorDef.count * byteStride) / elementBytes)\n\n          // Integer parameters to IB/IBA are in array elements, not bytes.\n          ib = new InterleavedBuffer(array, byteStride / elementBytes)\n\n          parser.cache.add(ibCacheKey, ib)\n        }\n\n        bufferAttribute = new InterleavedBufferAttribute(\n          ib,\n          itemSize,\n          (byteOffset % byteStride) / elementBytes,\n          normalized,\n        )\n      } else {\n        if (bufferView === null) {\n          array = new TypedArray(accessorDef.count * itemSize)\n        } else {\n          array = new TypedArray(bufferView, byteOffset, accessorDef.count * itemSize)\n        }\n\n        bufferAttribute = new BufferAttribute(array, itemSize, normalized)\n      }\n\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n      if (accessorDef.sparse !== undefined) {\n        const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR\n        const TypedArrayIndices = WEBGL_COMPONENT_TYPES[accessorDef.sparse.indices.componentType]\n\n        const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0\n        const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0\n\n        const sparseIndices = new TypedArrayIndices(\n          bufferViews[1],\n          byteOffsetIndices,\n          accessorDef.sparse.count * itemSizeIndices,\n        )\n        const sparseValues = new TypedArray(bufferViews[2], byteOffsetValues, accessorDef.sparse.count * itemSize)\n\n        if (bufferView !== null) {\n          // Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n          bufferAttribute = new BufferAttribute(\n            bufferAttribute.array.slice(),\n            bufferAttribute.itemSize,\n            bufferAttribute.normalized,\n          )\n        }\n\n        for (let i = 0, il = sparseIndices.length; i < il; i++) {\n          const index = sparseIndices[i]\n\n          bufferAttribute.setX(index, sparseValues[i * itemSize])\n          if (itemSize >= 2) bufferAttribute.setY(index, sparseValues[i * itemSize + 1])\n          if (itemSize >= 3) bufferAttribute.setZ(index, sparseValues[i * itemSize + 2])\n          if (itemSize >= 4) bufferAttribute.setW(index, sparseValues[i * itemSize + 3])\n          if (itemSize >= 5) throw new Error('THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.')\n        }\n      }\n\n      return bufferAttribute\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n   * @param {number} textureIndex\n   * @return {Promise<THREE.Texture|null>}\n   */\n  loadTexture(textureIndex) {\n    const json = this.json\n    const options = this.options\n    const textureDef = json.textures[textureIndex]\n    const sourceIndex = textureDef.source\n    const sourceDef = json.images[sourceIndex]\n\n    let loader = this.textureLoader\n\n    if (sourceDef.uri) {\n      const handler = options.manager.getHandler(sourceDef.uri)\n      if (handler !== null) loader = handler\n    }\n\n    return this.loadTextureImage(textureIndex, sourceIndex, loader)\n  }\n\n  loadTextureImage(textureIndex, sourceIndex, loader) {\n    const parser = this\n    const json = this.json\n\n    const textureDef = json.textures[textureIndex]\n    const sourceDef = json.images[sourceIndex]\n\n    const cacheKey = (sourceDef.uri || sourceDef.bufferView) + ':' + textureDef.sampler\n\n    if (this.textureCache[cacheKey]) {\n      // See https://github.com/mrdoob/three.js/issues/21559.\n      return this.textureCache[cacheKey]\n    }\n\n    const promise = this.loadImageSource(sourceIndex, loader)\n      .then(function (texture) {\n        texture.flipY = false\n\n        texture.name = textureDef.name || sourceDef.name || ''\n\n        if (\n          texture.name === '' &&\n          typeof sourceDef.uri === 'string' &&\n          sourceDef.uri.startsWith('data:image/') === false\n        ) {\n          texture.name = sourceDef.uri\n        }\n\n        const samplers = json.samplers || {}\n        const sampler = samplers[textureDef.sampler] || {}\n\n        texture.magFilter = WEBGL_FILTERS[sampler.magFilter] || LinearFilter\n        texture.minFilter = WEBGL_FILTERS[sampler.minFilter] || LinearMipmapLinearFilter\n        texture.wrapS = WEBGL_WRAPPINGS[sampler.wrapS] || RepeatWrapping\n        texture.wrapT = WEBGL_WRAPPINGS[sampler.wrapT] || RepeatWrapping\n\n        parser.associations.set(texture, { textures: textureIndex })\n\n        return texture\n      })\n      .catch(function () {\n        return null\n      })\n\n    this.textureCache[cacheKey] = promise\n\n    return promise\n  }\n\n  loadImageSource(sourceIndex, loader) {\n    const parser = this\n    const json = this.json\n    const options = this.options\n\n    if (this.sourceCache[sourceIndex] !== undefined) {\n      return this.sourceCache[sourceIndex].then((texture) => texture.clone())\n    }\n\n    const sourceDef = json.images[sourceIndex]\n\n    const URL = self.URL || self.webkitURL\n\n    let sourceURI = sourceDef.uri || ''\n    let isObjectURL = false\n\n    if (sourceDef.bufferView !== undefined) {\n      // Load binary image data from bufferView, if provided.\n\n      sourceURI = parser.getDependency('bufferView', sourceDef.bufferView).then(function (bufferView) {\n        isObjectURL = true\n        const blob = new Blob([bufferView], { type: sourceDef.mimeType })\n        sourceURI = URL.createObjectURL(blob)\n        return sourceURI\n      })\n    } else if (sourceDef.uri === undefined) {\n      throw new Error('THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView')\n    }\n\n    const promise = Promise.resolve(sourceURI)\n      .then(function (sourceURI) {\n        return new Promise(function (resolve, reject) {\n          let onLoad = resolve\n\n          if (loader.isImageBitmapLoader === true) {\n            onLoad = function (imageBitmap) {\n              const texture = new Texture(imageBitmap)\n              texture.needsUpdate = true\n\n              resolve(texture)\n            }\n          }\n\n          loader.load(LoaderUtils.resolveURL(sourceURI, options.path), onLoad, undefined, reject)\n        })\n      })\n      .then(function (texture) {\n        // Clean up resources and configure Texture.\n\n        if (isObjectURL === true) {\n          URL.revokeObjectURL(sourceURI)\n        }\n\n        assignExtrasToUserData(texture, sourceDef)\n\n        texture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType(sourceDef.uri)\n\n        return texture\n      })\n      .catch(function (error) {\n        console.error(\"THREE.GLTFLoader: Couldn't load texture\", sourceURI)\n        throw error\n      })\n\n    this.sourceCache[sourceIndex] = promise\n    return promise\n  }\n\n  /**\n   * Asynchronously assigns a texture to the given material parameters.\n   * @param {Object} materialParams\n   * @param {string} mapName\n   * @param {Object} mapDef\n   * @return {Promise<Texture>}\n   */\n  assignTexture(materialParams, mapName, mapDef, colorSpace) {\n    const parser = this\n\n    return this.getDependency('texture', mapDef.index).then(function (texture) {\n      if (!texture) return null\n\n      if (mapDef.texCoord !== undefined && mapDef.texCoord > 0) {\n        texture = texture.clone()\n        texture.channel = mapDef.texCoord\n      }\n\n      if (parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM]) {\n        const transform =\n          mapDef.extensions !== undefined ? mapDef.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM] : undefined\n\n        if (transform) {\n          const gltfReference = parser.associations.get(texture)\n          texture = parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM].extendTexture(texture, transform)\n          parser.associations.set(texture, gltfReference)\n        }\n      }\n\n      if (colorSpace !== undefined) {\n        // Convert from legacy encoding to colorSpace\n        if (typeof colorSpace === 'number')\n          colorSpace = colorSpace === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace\n\n        // Set colorSpace if able, fallback to legacy encoding\n        if ('colorSpace' in texture) texture.colorSpace = colorSpace\n        else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding\n      }\n\n      materialParams[mapName] = texture\n\n      return texture\n    })\n  }\n\n  /**\n   * Assigns final material to a Mesh, Line, or Points instance. The instance\n   * already has a material (generated from the glTF material options alone)\n   * but reuse of the same glTF material may require multiple threejs materials\n   * to accommodate different primitive types, defines, etc. New materials will\n   * be created if necessary, and reused from a cache.\n   * @param  {Object3D} mesh Mesh, Line, or Points instance.\n   */\n  assignFinalMaterial(mesh) {\n    const geometry = mesh.geometry\n    let material = mesh.material\n\n    const useDerivativeTangents = geometry.attributes.tangent === undefined\n    const useVertexColors = geometry.attributes.color !== undefined\n    const useFlatShading = geometry.attributes.normal === undefined\n\n    if (mesh.isPoints) {\n      const cacheKey = 'PointsMaterial:' + material.uuid\n\n      let pointsMaterial = this.cache.get(cacheKey)\n\n      if (!pointsMaterial) {\n        pointsMaterial = new PointsMaterial()\n        Material.prototype.copy.call(pointsMaterial, material)\n        pointsMaterial.color.copy(material.color)\n        pointsMaterial.map = material.map\n        pointsMaterial.sizeAttenuation = false // glTF spec says points should be 1px\n\n        this.cache.add(cacheKey, pointsMaterial)\n      }\n\n      material = pointsMaterial\n    } else if (mesh.isLine) {\n      const cacheKey = 'LineBasicMaterial:' + material.uuid\n\n      let lineMaterial = this.cache.get(cacheKey)\n\n      if (!lineMaterial) {\n        lineMaterial = new LineBasicMaterial()\n        Material.prototype.copy.call(lineMaterial, material)\n        lineMaterial.color.copy(material.color)\n        lineMaterial.map = material.map\n\n        this.cache.add(cacheKey, lineMaterial)\n      }\n\n      material = lineMaterial\n    }\n\n    // Clone the material if it will be modified\n    if (useDerivativeTangents || useVertexColors || useFlatShading) {\n      let cacheKey = 'ClonedMaterial:' + material.uuid + ':'\n\n      if (useDerivativeTangents) cacheKey += 'derivative-tangents:'\n      if (useVertexColors) cacheKey += 'vertex-colors:'\n      if (useFlatShading) cacheKey += 'flat-shading:'\n\n      let cachedMaterial = this.cache.get(cacheKey)\n\n      if (!cachedMaterial) {\n        cachedMaterial = material.clone()\n\n        if (useVertexColors) cachedMaterial.vertexColors = true\n        if (useFlatShading) cachedMaterial.flatShading = true\n\n        if (useDerivativeTangents) {\n          // https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n          if (cachedMaterial.normalScale) cachedMaterial.normalScale.y *= -1\n          if (cachedMaterial.clearcoatNormalScale) cachedMaterial.clearcoatNormalScale.y *= -1\n        }\n\n        this.cache.add(cacheKey, cachedMaterial)\n\n        this.associations.set(cachedMaterial, this.associations.get(material))\n      }\n\n      material = cachedMaterial\n    }\n\n    mesh.material = material\n  }\n\n  getMaterialType(/* materialIndex */) {\n    return MeshStandardMaterial\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n   * @param {number} materialIndex\n   * @return {Promise<Material>}\n   */\n  loadMaterial(materialIndex) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n    const materialDef = json.materials[materialIndex]\n\n    let materialType\n    const materialParams = {}\n    const materialExtensions = materialDef.extensions || {}\n\n    const pending = []\n\n    if (materialExtensions[EXTENSIONS.KHR_MATERIALS_UNLIT]) {\n      const kmuExtension = extensions[EXTENSIONS.KHR_MATERIALS_UNLIT]\n      materialType = kmuExtension.getMaterialType()\n      pending.push(kmuExtension.extendParams(materialParams, materialDef, parser))\n    } else {\n      // Specification:\n      // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n      const metallicRoughness = materialDef.pbrMetallicRoughness || {}\n\n      materialParams.color = new Color(1.0, 1.0, 1.0)\n      materialParams.opacity = 1.0\n\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor\n\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace)\n        materialParams.opacity = array[3]\n      }\n\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace))\n      }\n\n      materialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0\n      materialParams.roughness =\n        metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0\n\n      if (metallicRoughness.metallicRoughnessTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture))\n        pending.push(parser.assignTexture(materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture))\n      }\n\n      materialType = this._invokeOne(function (ext) {\n        return ext.getMaterialType && ext.getMaterialType(materialIndex)\n      })\n\n      pending.push(\n        Promise.all(\n          this._invokeAll(function (ext) {\n            return ext.extendMaterialParams && ext.extendMaterialParams(materialIndex, materialParams)\n          }),\n        ),\n      )\n    }\n\n    if (materialDef.doubleSided === true) {\n      materialParams.side = DoubleSide\n    }\n\n    const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE\n\n    if (alphaMode === ALPHA_MODES.BLEND) {\n      materialParams.transparent = true\n\n      // See: https://github.com/mrdoob/three.js/issues/17706\n      materialParams.depthWrite = false\n    } else {\n      materialParams.transparent = false\n\n      if (alphaMode === ALPHA_MODES.MASK) {\n        materialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5\n      }\n    }\n\n    if (materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'normalMap', materialDef.normalTexture))\n\n      materialParams.normalScale = new Vector2(1, 1)\n\n      if (materialDef.normalTexture.scale !== undefined) {\n        const scale = materialDef.normalTexture.scale\n\n        materialParams.normalScale.set(scale, scale)\n      }\n    }\n\n    if (materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'aoMap', materialDef.occlusionTexture))\n\n      if (materialDef.occlusionTexture.strength !== undefined) {\n        materialParams.aoMapIntensity = materialDef.occlusionTexture.strength\n      }\n    }\n\n    if (materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial) {\n      const emissiveFactor = materialDef.emissiveFactor\n      materialParams.emissive = new Color().setRGB(\n        emissiveFactor[0],\n        emissiveFactor[1],\n        emissiveFactor[2],\n        LinearSRGBColorSpace,\n      )\n    }\n\n    if (materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace))\n    }\n\n    return Promise.all(pending).then(function () {\n      const material = new materialType(materialParams)\n\n      if (materialDef.name) material.name = materialDef.name\n\n      assignExtrasToUserData(material, materialDef)\n\n      parser.associations.set(material, { materials: materialIndex })\n\n      if (materialDef.extensions) addUnknownExtensionsToUserData(extensions, material, materialDef)\n\n      return material\n    })\n  }\n\n  /** When Object3D instances are targeted by animation, they need unique names. */\n  createUniqueName(originalName) {\n    const sanitizedName = PropertyBinding.sanitizeNodeName(originalName || '')\n\n    if (sanitizedName in this.nodeNamesUsed) {\n      return sanitizedName + '_' + ++this.nodeNamesUsed[sanitizedName]\n    } else {\n      this.nodeNamesUsed[sanitizedName] = 0\n\n      return sanitizedName\n    }\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n   *\n   * Creates BufferGeometries from primitives.\n   *\n   * @param {Array<GLTF.Primitive>} primitives\n   * @return {Promise<Array<BufferGeometry>>}\n   */\n  loadGeometries(primitives) {\n    const parser = this\n    const extensions = this.extensions\n    const cache = this.primitiveCache\n\n    function createDracoPrimitive(primitive) {\n      return extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]\n        .decodePrimitive(primitive, parser)\n        .then(function (geometry) {\n          return addPrimitiveAttributes(geometry, primitive, parser)\n        })\n    }\n\n    const pending = []\n\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const primitive = primitives[i]\n      const cacheKey = createPrimitiveKey(primitive)\n\n      // See if we've already created this geometry\n      const cached = cache[cacheKey]\n\n      if (cached) {\n        // Use the cached geometry if it exists\n        pending.push(cached.promise)\n      } else {\n        let geometryPromise\n\n        if (primitive.extensions && primitive.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]) {\n          // Use DRACO geometry if available\n          geometryPromise = createDracoPrimitive(primitive)\n        } else {\n          // Otherwise create a new geometry\n          geometryPromise = addPrimitiveAttributes(new BufferGeometry(), primitive, parser)\n        }\n\n        // Cache this geometry\n        cache[cacheKey] = { primitive: primitive, promise: geometryPromise }\n\n        pending.push(geometryPromise)\n      }\n    }\n\n    return Promise.all(pending)\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n   * @param {number} meshIndex\n   * @return {Promise<Group|Mesh|SkinnedMesh>}\n   */\n  loadMesh(meshIndex) {\n    const parser = this\n    const json = this.json\n    const extensions = this.extensions\n\n    const meshDef = json.meshes[meshIndex]\n    const primitives = meshDef.primitives\n\n    const pending = []\n\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const material =\n        primitives[i].material === undefined\n          ? createDefaultMaterial(this.cache)\n          : this.getDependency('material', primitives[i].material)\n\n      pending.push(material)\n    }\n\n    pending.push(parser.loadGeometries(primitives))\n\n    return Promise.all(pending).then(function (results) {\n      const materials = results.slice(0, results.length - 1)\n      const geometries = results[results.length - 1]\n\n      const meshes = []\n\n      for (let i = 0, il = geometries.length; i < il; i++) {\n        const geometry = geometries[i]\n        const primitive = primitives[i]\n\n        // 1. create Mesh\n\n        let mesh\n\n        const material = materials[i]\n\n        if (\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLES ||\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ||\n          primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ||\n          primitive.mode === undefined\n        ) {\n          // .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n          mesh = meshDef.isSkinnedMesh === true ? new SkinnedMesh(geometry, material) : new Mesh(geometry, material)\n\n          if (mesh.isSkinnedMesh === true) {\n            // normalize skin weights to fix malformed assets (see #15319)\n            mesh.normalizeSkinWeights()\n          }\n\n          if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleStripDrawMode)\n          } else if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleFanDrawMode)\n          }\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINES) {\n          mesh = new LineSegments(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_STRIP) {\n          mesh = new Line(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_LOOP) {\n          mesh = new LineLoop(geometry, material)\n        } else if (primitive.mode === WEBGL_CONSTANTS.POINTS) {\n          mesh = new Points(geometry, material)\n        } else {\n          throw new Error('THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode)\n        }\n\n        if (Object.keys(mesh.geometry.morphAttributes).length > 0) {\n          updateMorphTargets(mesh, meshDef)\n        }\n\n        mesh.name = parser.createUniqueName(meshDef.name || 'mesh_' + meshIndex)\n\n        assignExtrasToUserData(mesh, meshDef)\n\n        if (primitive.extensions) addUnknownExtensionsToUserData(extensions, mesh, primitive)\n\n        parser.assignFinalMaterial(mesh)\n\n        meshes.push(mesh)\n      }\n\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        parser.associations.set(meshes[i], {\n          meshes: meshIndex,\n          primitives: i,\n        })\n      }\n\n      if (meshes.length === 1) {\n        if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, meshes[0], meshDef)\n\n        return meshes[0]\n      }\n\n      const group = new Group()\n\n      if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, group, meshDef)\n\n      parser.associations.set(group, { meshes: meshIndex })\n\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        group.add(meshes[i])\n      }\n\n      return group\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n   * @param {number} cameraIndex\n   * @return {Promise<THREE.Camera>}\n   */\n  loadCamera(cameraIndex) {\n    let camera\n    const cameraDef = this.json.cameras[cameraIndex]\n    const params = cameraDef[cameraDef.type]\n\n    if (!params) {\n      console.warn('THREE.GLTFLoader: Missing camera parameters.')\n      return\n    }\n\n    if (cameraDef.type === 'perspective') {\n      camera = new PerspectiveCamera(\n        MathUtils.radToDeg(params.yfov),\n        params.aspectRatio || 1,\n        params.znear || 1,\n        params.zfar || 2e6,\n      )\n    } else if (cameraDef.type === 'orthographic') {\n      camera = new OrthographicCamera(-params.xmag, params.xmag, params.ymag, -params.ymag, params.znear, params.zfar)\n    }\n\n    if (cameraDef.name) camera.name = this.createUniqueName(cameraDef.name)\n\n    assignExtrasToUserData(camera, cameraDef)\n\n    return Promise.resolve(camera)\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n   * @param {number} skinIndex\n   * @return {Promise<Skeleton>}\n   */\n  loadSkin(skinIndex) {\n    const skinDef = this.json.skins[skinIndex]\n\n    const pending = []\n\n    for (let i = 0, il = skinDef.joints.length; i < il; i++) {\n      pending.push(this._loadNodeShallow(skinDef.joints[i]))\n    }\n\n    if (skinDef.inverseBindMatrices !== undefined) {\n      pending.push(this.getDependency('accessor', skinDef.inverseBindMatrices))\n    } else {\n      pending.push(null)\n    }\n\n    return Promise.all(pending).then(function (results) {\n      const inverseBindMatrices = results.pop()\n      const jointNodes = results\n\n      // Note that bones (joint nodes) may or may not be in the\n      // scene graph at this time.\n\n      const bones = []\n      const boneInverses = []\n\n      for (let i = 0, il = jointNodes.length; i < il; i++) {\n        const jointNode = jointNodes[i]\n\n        if (jointNode) {\n          bones.push(jointNode)\n\n          const mat = new Matrix4()\n\n          if (inverseBindMatrices !== null) {\n            mat.fromArray(inverseBindMatrices.array, i * 16)\n          }\n\n          boneInverses.push(mat)\n        } else {\n          console.warn('THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[i])\n        }\n      }\n\n      return new Skeleton(bones, boneInverses)\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n   * @param {number} animationIndex\n   * @return {Promise<AnimationClip>}\n   */\n  loadAnimation(animationIndex) {\n    const json = this.json\n    const parser = this\n\n    const animationDef = json.animations[animationIndex]\n    const animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex\n\n    const pendingNodes = []\n    const pendingInputAccessors = []\n    const pendingOutputAccessors = []\n    const pendingSamplers = []\n    const pendingTargets = []\n\n    for (let i = 0, il = animationDef.channels.length; i < il; i++) {\n      const channel = animationDef.channels[i]\n      const sampler = animationDef.samplers[channel.sampler]\n      const target = channel.target\n      const name = target.node\n      const input = animationDef.parameters !== undefined ? animationDef.parameters[sampler.input] : sampler.input\n      const output = animationDef.parameters !== undefined ? animationDef.parameters[sampler.output] : sampler.output\n\n      if (target.node === undefined) continue\n\n      pendingNodes.push(this.getDependency('node', name))\n      pendingInputAccessors.push(this.getDependency('accessor', input))\n      pendingOutputAccessors.push(this.getDependency('accessor', output))\n      pendingSamplers.push(sampler)\n      pendingTargets.push(target)\n    }\n\n    return Promise.all([\n      Promise.all(pendingNodes),\n      Promise.all(pendingInputAccessors),\n      Promise.all(pendingOutputAccessors),\n      Promise.all(pendingSamplers),\n      Promise.all(pendingTargets),\n    ]).then(function (dependencies) {\n      const nodes = dependencies[0]\n      const inputAccessors = dependencies[1]\n      const outputAccessors = dependencies[2]\n      const samplers = dependencies[3]\n      const targets = dependencies[4]\n\n      const tracks = []\n\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        const node = nodes[i]\n        const inputAccessor = inputAccessors[i]\n        const outputAccessor = outputAccessors[i]\n        const sampler = samplers[i]\n        const target = targets[i]\n\n        if (node === undefined) continue\n\n        if (node.updateMatrix) {\n          node.updateMatrix()\n        }\n\n        const createdTracks = parser._createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target)\n\n        if (createdTracks) {\n          for (let k = 0; k < createdTracks.length; k++) {\n            tracks.push(createdTracks[k])\n          }\n        }\n      }\n\n      return new AnimationClip(animationName, undefined, tracks)\n    })\n  }\n\n  createNodeMesh(nodeIndex) {\n    const json = this.json\n    const parser = this\n    const nodeDef = json.nodes[nodeIndex]\n\n    if (nodeDef.mesh === undefined) return null\n\n    return parser.getDependency('mesh', nodeDef.mesh).then(function (mesh) {\n      const node = parser._getNodeRef(parser.meshCache, nodeDef.mesh, mesh)\n\n      // if weights are provided on the node, override weights on the mesh.\n      if (nodeDef.weights !== undefined) {\n        node.traverse(function (o) {\n          if (!o.isMesh) return\n\n          for (let i = 0, il = nodeDef.weights.length; i < il; i++) {\n            o.morphTargetInfluences[i] = nodeDef.weights[i]\n          }\n        })\n      }\n\n      return node\n    })\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n   * @param {number} nodeIndex\n   * @return {Promise<Object3D>}\n   */\n  loadNode(nodeIndex) {\n    const json = this.json\n    const parser = this\n\n    const nodeDef = json.nodes[nodeIndex]\n\n    const nodePending = parser._loadNodeShallow(nodeIndex)\n\n    const childPending = []\n    const childrenDef = nodeDef.children || []\n\n    for (let i = 0, il = childrenDef.length; i < il; i++) {\n      childPending.push(parser.getDependency('node', childrenDef[i]))\n    }\n\n    const skeletonPending =\n      nodeDef.skin === undefined ? Promise.resolve(null) : parser.getDependency('skin', nodeDef.skin)\n\n    return Promise.all([nodePending, Promise.all(childPending), skeletonPending]).then(function (results) {\n      const node = results[0]\n      const children = results[1]\n      const skeleton = results[2]\n\n      if (skeleton !== null) {\n        // This full traverse should be fine because\n        // child glTF nodes have not been added to this node yet.\n        node.traverse(function (mesh) {\n          if (!mesh.isSkinnedMesh) return\n\n          mesh.bind(skeleton, _identityMatrix)\n        })\n      }\n\n      for (let i = 0, il = children.length; i < il; i++) {\n        node.add(children[i])\n      }\n\n      return node\n    })\n  }\n\n  // ._loadNodeShallow() parses a single node.\n  // skin and child nodes are created and added in .loadNode() (no '_' prefix).\n  _loadNodeShallow(nodeIndex) {\n    const json = this.json\n    const extensions = this.extensions\n    const parser = this\n\n    // This method is called from .loadNode() and .loadSkin().\n    // Cache a node to avoid duplication.\n\n    if (this.nodeCache[nodeIndex] !== undefined) {\n      return this.nodeCache[nodeIndex]\n    }\n\n    const nodeDef = json.nodes[nodeIndex]\n\n    // reserve node's name before its dependencies, so the root has the intended name.\n    const nodeName = nodeDef.name ? parser.createUniqueName(nodeDef.name) : ''\n\n    const pending = []\n\n    const meshPromise = parser._invokeOne(function (ext) {\n      return ext.createNodeMesh && ext.createNodeMesh(nodeIndex)\n    })\n\n    if (meshPromise) {\n      pending.push(meshPromise)\n    }\n\n    if (nodeDef.camera !== undefined) {\n      pending.push(\n        parser.getDependency('camera', nodeDef.camera).then(function (camera) {\n          return parser._getNodeRef(parser.cameraCache, nodeDef.camera, camera)\n        }),\n      )\n    }\n\n    parser\n      ._invokeAll(function (ext) {\n        return ext.createNodeAttachment && ext.createNodeAttachment(nodeIndex)\n      })\n      .forEach(function (promise) {\n        pending.push(promise)\n      })\n\n    this.nodeCache[nodeIndex] = Promise.all(pending).then(function (objects) {\n      let node\n\n      // .isBone isn't in glTF spec. See ._markDefs\n      if (nodeDef.isBone === true) {\n        node = new Bone()\n      } else if (objects.length > 1) {\n        node = new Group()\n      } else if (objects.length === 1) {\n        node = objects[0]\n      } else {\n        node = new Object3D()\n      }\n\n      if (node !== objects[0]) {\n        for (let i = 0, il = objects.length; i < il; i++) {\n          node.add(objects[i])\n        }\n      }\n\n      if (nodeDef.name) {\n        node.userData.name = nodeDef.name\n        node.name = nodeName\n      }\n\n      assignExtrasToUserData(node, nodeDef)\n\n      if (nodeDef.extensions) addUnknownExtensionsToUserData(extensions, node, nodeDef)\n\n      if (nodeDef.matrix !== undefined) {\n        const matrix = new Matrix4()\n        matrix.fromArray(nodeDef.matrix)\n        node.applyMatrix4(matrix)\n      } else {\n        if (nodeDef.translation !== undefined) {\n          node.position.fromArray(nodeDef.translation)\n        }\n\n        if (nodeDef.rotation !== undefined) {\n          node.quaternion.fromArray(nodeDef.rotation)\n        }\n\n        if (nodeDef.scale !== undefined) {\n          node.scale.fromArray(nodeDef.scale)\n        }\n      }\n\n      if (!parser.associations.has(node)) {\n        parser.associations.set(node, {})\n      }\n\n      parser.associations.get(node).nodes = nodeIndex\n\n      return node\n    })\n\n    return this.nodeCache[nodeIndex]\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n   * @param {number} sceneIndex\n   * @return {Promise<Group>}\n   */\n  loadScene(sceneIndex) {\n    const extensions = this.extensions\n    const sceneDef = this.json.scenes[sceneIndex]\n    const parser = this\n\n    // Loader returns Group, not Scene.\n    // See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n    const scene = new Group()\n    if (sceneDef.name) scene.name = parser.createUniqueName(sceneDef.name)\n\n    assignExtrasToUserData(scene, sceneDef)\n\n    if (sceneDef.extensions) addUnknownExtensionsToUserData(extensions, scene, sceneDef)\n\n    const nodeIds = sceneDef.nodes || []\n\n    const pending = []\n\n    for (let i = 0, il = nodeIds.length; i < il; i++) {\n      pending.push(parser.getDependency('node', nodeIds[i]))\n    }\n\n    return Promise.all(pending).then(function (nodes) {\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        scene.add(nodes[i])\n      }\n\n      // Removes dangling associations, associations that reference a node that\n      // didn't make it into the scene.\n      const reduceAssociations = (node) => {\n        const reducedAssociations = new Map()\n\n        for (const [key, value] of parser.associations) {\n          if (key instanceof Material || key instanceof Texture) {\n            reducedAssociations.set(key, value)\n          }\n        }\n\n        node.traverse((node) => {\n          const mappings = parser.associations.get(node)\n\n          if (mappings != null) {\n            reducedAssociations.set(node, mappings)\n          }\n        })\n\n        return reducedAssociations\n      }\n\n      parser.associations = reduceAssociations(scene)\n\n      return scene\n    })\n  }\n\n  _createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target) {\n    const tracks = []\n\n    const targetName = node.name ? node.name : node.uuid\n    const targetNames = []\n\n    if (PATH_PROPERTIES[target.path] === PATH_PROPERTIES.weights) {\n      node.traverse(function (object) {\n        if (object.morphTargetInfluences) {\n          targetNames.push(object.name ? object.name : object.uuid)\n        }\n      })\n    } else {\n      targetNames.push(targetName)\n    }\n\n    let TypedKeyframeTrack\n\n    switch (PATH_PROPERTIES[target.path]) {\n      case PATH_PROPERTIES.weights:\n        TypedKeyframeTrack = NumberKeyframeTrack\n        break\n\n      case PATH_PROPERTIES.rotation:\n        TypedKeyframeTrack = QuaternionKeyframeTrack\n        break\n\n      case PATH_PROPERTIES.position:\n      case PATH_PROPERTIES.scale:\n        TypedKeyframeTrack = VectorKeyframeTrack\n        break\n\n      default:\n        switch (outputAccessor.itemSize) {\n          case 1:\n            TypedKeyframeTrack = NumberKeyframeTrack\n            break\n          case 2:\n          case 3:\n          default:\n            TypedKeyframeTrack = VectorKeyframeTrack\n            break\n        }\n\n        break\n    }\n\n    const interpolation = sampler.interpolation !== undefined ? INTERPOLATION[sampler.interpolation] : InterpolateLinear\n\n    const outputArray = this._getArrayFromAccessor(outputAccessor)\n\n    for (let j = 0, jl = targetNames.length; j < jl; j++) {\n      const track = new TypedKeyframeTrack(\n        targetNames[j] + '.' + PATH_PROPERTIES[target.path],\n        inputAccessor.array,\n        outputArray,\n        interpolation,\n      )\n\n      // Override interpolation with custom factory method.\n      if (sampler.interpolation === 'CUBICSPLINE') {\n        this._createCubicSplineTrackInterpolant(track)\n      }\n\n      tracks.push(track)\n    }\n\n    return tracks\n  }\n\n  _getArrayFromAccessor(accessor) {\n    let outputArray = accessor.array\n\n    if (accessor.normalized) {\n      const scale = getNormalizedComponentScale(outputArray.constructor)\n      const scaled = new Float32Array(outputArray.length)\n\n      for (let j = 0, jl = outputArray.length; j < jl; j++) {\n        scaled[j] = outputArray[j] * scale\n      }\n\n      outputArray = scaled\n    }\n\n    return outputArray\n  }\n\n  _createCubicSplineTrackInterpolant(track) {\n    track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline(result) {\n      // A CUBICSPLINE keyframe in glTF has three output values for each input value,\n      // representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n      // must be divided by three to get the interpolant's sampleSize argument.\n\n      const interpolantType =\n        this instanceof QuaternionKeyframeTrack ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant\n\n      return new interpolantType(this.times, this.values, this.getValueSize() / 3, result)\n    }\n\n    // Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n    track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true\n  }\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes\n\n  const box = new Box3()\n\n  if (attributes.POSITION !== undefined) {\n    const accessor = parser.json.accessors[attributes.POSITION]\n\n    const min = accessor.min\n    const max = accessor.max\n\n    // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n    if (min !== undefined && max !== undefined) {\n      box.set(new Vector3(min[0], min[1], min[2]), new Vector3(max[0], max[1], max[2]))\n\n      if (accessor.normalized) {\n        const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType])\n        box.min.multiplyScalar(boxScale)\n        box.max.multiplyScalar(boxScale)\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.')\n\n      return\n    }\n  } else {\n    return\n  }\n\n  const targets = primitiveDef.targets\n\n  if (targets !== undefined) {\n    const maxDisplacement = new Vector3()\n    const vector = new Vector3()\n\n    for (let i = 0, il = targets.length; i < il; i++) {\n      const target = targets[i]\n\n      if (target.POSITION !== undefined) {\n        const accessor = parser.json.accessors[target.POSITION]\n        const min = accessor.min\n        const max = accessor.max\n\n        // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n        if (min !== undefined && max !== undefined) {\n          // we need to get max of absolute components because target weight is [-1,1]\n          vector.setX(Math.max(Math.abs(min[0]), Math.abs(max[0])))\n          vector.setY(Math.max(Math.abs(min[1]), Math.abs(max[1])))\n          vector.setZ(Math.max(Math.abs(min[2]), Math.abs(max[2])))\n\n          if (accessor.normalized) {\n            const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType])\n            vector.multiplyScalar(boxScale)\n          }\n\n          // Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n          // to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n          // are used to implement key-frame animations and as such only two are active at a time - this results in very large\n          // boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n          maxDisplacement.max(vector)\n        } else {\n          console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.')\n        }\n      }\n    }\n\n    // As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n    box.expandByVector(maxDisplacement)\n  }\n\n  geometry.boundingBox = box\n\n  const sphere = new Sphere()\n\n  box.getCenter(sphere.center)\n  sphere.radius = box.min.distanceTo(box.max) / 2\n\n  geometry.boundingSphere = sphere\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes\n\n  const pending = []\n\n  function assignAttributeAccessor(accessorIndex, attributeName) {\n    return parser.getDependency('accessor', accessorIndex).then(function (accessor) {\n      geometry.setAttribute(attributeName, accessor)\n    })\n  }\n\n  for (const gltfAttributeName in attributes) {\n    const threeAttributeName = ATTRIBUTES[gltfAttributeName] || gltfAttributeName.toLowerCase()\n\n    // Skip attributes already provided by e.g. Draco extension.\n    if (threeAttributeName in geometry.attributes) continue\n\n    pending.push(assignAttributeAccessor(attributes[gltfAttributeName], threeAttributeName))\n  }\n\n  if (primitiveDef.indices !== undefined && !geometry.index) {\n    const accessor = parser.getDependency('accessor', primitiveDef.indices).then(function (accessor) {\n      geometry.setIndex(accessor)\n    })\n\n    pending.push(accessor)\n  }\n\n  assignExtrasToUserData(geometry, primitiveDef)\n\n  computeBounds(geometry, primitiveDef, parser)\n\n  return Promise.all(pending).then(function () {\n    return primitiveDef.targets !== undefined ? addMorphTargets(geometry, primitiveDef.targets, parser) : geometry\n  })\n}\n\nexport { GLTFLoader }\n"], "mappings": ";;;;AAoEA,MAAMA,cAAA,GAAiB;AACvB,MAAMC,oBAAA,GAAuB;AAC7B,MAAMC,YAAA,GAAe;AACrB,MAAMC,cAAA,GAAiB;AAEvB,MAAMC,UAAA,SAAmBC,MAAA,CAAO;EAC9BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,WAAA,GAAc;IACnB,KAAKC,UAAA,GAAa;IAClB,KAAKC,cAAA,GAAiB;IAEtB,KAAKC,eAAA,GAAkB,EAAE;IAEzB,KAAKC,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIC,+BAAA,CAAgCD,MAAM;IACvD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIE,gCAAA,CAAiCF,MAAM;IACxD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIG,0BAAA,CAA2BH,MAAM;IAClD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAII,wBAAA,CAAyBJ,MAAM;IAChD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIK,wBAAA,CAAyBL,MAAM;IAChD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIM,2BAAA,CAA4BN,MAAM;IACnD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIO,kCAAA,CAAmCP,MAAM;IAC1D,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIQ,4BAAA,CAA6BR,MAAM;IACpD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIS,yBAAA,CAA0BT,MAAM;IACjD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIU,sCAAA,CAAuCV,MAAM;IAC9D,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIW,8BAAA,CAA+BX,MAAM;IACtD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIY,iCAAA,CAAkCZ,MAAM;IACzD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIa,gCAAA,CAAiCb,MAAM;IACxD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIc,0BAAA,CAA2Bd,MAAM;IAClD,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIe,mBAAA,CAAoBf,MAAM;IAC3C,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIgB,sBAAA,CAAuBhB,MAAM;IAC9C,CAAK;IAED,KAAKD,QAAA,CAAS,UAAUC,MAAA,EAAQ;MAC9B,OAAO,IAAIiB,qBAAA,CAAsBjB,MAAM;IAC7C,CAAK;EACF;EAEDkB,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,IAAIC,YAAA;IAEJ,IAAI,KAAKA,YAAA,KAAiB,IAAI;MAC5BA,YAAA,GAAe,KAAKA,YAAA;IAC1B,WAAe,KAAKC,IAAA,KAAS,IAAI;MAM3B,MAAMC,WAAA,GAAcC,WAAA,CAAYC,cAAA,CAAeT,GAAG;MAClDK,YAAA,GAAeG,WAAA,CAAYE,UAAA,CAAWH,WAAA,EAAa,KAAKD,IAAI;IAClE,OAAW;MACLD,YAAA,GAAeG,WAAA,CAAYC,cAAA,CAAeT,GAAG;IAC9C;IAKD,KAAKzB,OAAA,CAAQoC,SAAA,CAAUX,GAAG;IAE1B,MAAMY,QAAA,GAAW,SAAAA,CAAUC,CAAA,EAAG;MAC5B,IAAIV,OAAA,EAAS;QACXA,OAAA,CAAQU,CAAC;MACjB,OAAa;QACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;MAChB;MAEDT,KAAA,CAAM7B,OAAA,CAAQyC,SAAA,CAAUhB,GAAG;MAC3BI,KAAA,CAAM7B,OAAA,CAAQ0C,OAAA,CAAQjB,GAAG;IAC1B;IAED,MAAMkB,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAK5C,OAAO;IAE1C2C,MAAA,CAAOE,OAAA,CAAQ,KAAKd,IAAI;IACxBY,MAAA,CAAOG,eAAA,CAAgB,aAAa;IACpCH,MAAA,CAAOI,gBAAA,CAAiB,KAAKC,aAAa;IAC1CL,MAAA,CAAOM,kBAAA,CAAmB,KAAKC,eAAe;IAE9CP,MAAA,CAAOnB,IAAA,CACLC,GAAA,EACA,UAAU0B,IAAA,EAAM;MACd,IAAI;QACFtB,KAAA,CAAMuB,KAAA,CACJD,IAAA,EACArB,YAAA,EACA,UAAUuB,IAAA,EAAM;UACd3B,MAAA,CAAO2B,IAAI;UAEXxB,KAAA,CAAM7B,OAAA,CAAQ0C,OAAA,CAAQjB,GAAG;QAC1B,GACDY,QACD;MACF,SAAQC,CAAA,EAAP;QACAD,QAAA,CAASC,CAAC;MACX;IACF,GACDX,UAAA,EACAU,QACD;EACF;EAEDiB,eAAerD,WAAA,EAAa;IAC1B,KAAKA,WAAA,GAAcA,WAAA;IACnB,OAAO;EACR;EAEDsD,aAAA,EAAe;IACb,MAAM,IAAIC,KAAA,CAAM,kGAAkG;EACnH;EAEDC,cAAcvD,UAAA,EAAY;IACxB,KAAKA,UAAA,GAAaA,UAAA;IAClB,OAAO;EACR;EAEDwD,kBAAkBvD,cAAA,EAAgB;IAChC,KAAKA,cAAA,GAAiBA,cAAA;IACtB,OAAO;EACR;EAEDE,SAASsD,QAAA,EAAU;IACjB,IAAI,KAAKvD,eAAA,CAAgBwD,OAAA,CAAQD,QAAQ,MAAM,IAAI;MACjD,KAAKvD,eAAA,CAAgByD,IAAA,CAAKF,QAAQ;IACnC;IAED,OAAO;EACR;EAEDG,WAAWH,QAAA,EAAU;IACnB,IAAI,KAAKvD,eAAA,CAAgBwD,OAAA,CAAQD,QAAQ,MAAM,IAAI;MACjD,KAAKvD,eAAA,CAAgB2D,MAAA,CAAO,KAAK3D,eAAA,CAAgBwD,OAAA,CAAQD,QAAQ,GAAG,CAAC;IACtE;IAED,OAAO;EACR;EAEDP,MAAMD,IAAA,EAAMpB,IAAA,EAAML,MAAA,EAAQE,OAAA,EAAS;IACjC,IAAIoC,IAAA;IACJ,MAAMC,UAAA,GAAa,CAAE;IACrB,MAAMC,OAAA,GAAU,CAAE;IAElB,IAAI,OAAOf,IAAA,KAAS,UAAU;MAC5Ba,IAAA,GAAOG,IAAA,CAAKf,KAAA,CAAMD,IAAI;IAC5B,WAAeA,IAAA,YAAgBiB,WAAA,EAAa;MACtC,MAAMC,KAAA,GAAQC,UAAA,CAAW,IAAIC,UAAA,CAAWpB,IAAA,CAAKqB,KAAA,CAAM,GAAG,CAAC,CAAC,CAAC;MAEzD,IAAIH,KAAA,KAAUI,6BAAA,EAA+B;QAC3C,IAAI;UACFR,UAAA,CAAWS,UAAA,CAAWC,eAAe,IAAI,IAAIC,mBAAA,CAAoBzB,IAAI;QACtE,SAAQX,KAAA,EAAP;UACA,IAAIZ,OAAA,EAASA,OAAA,CAAQY,KAAK;UAC1B;QACD;QAEDwB,IAAA,GAAOG,IAAA,CAAKf,KAAA,CAAMa,UAAA,CAAWS,UAAA,CAAWC,eAAe,EAAEE,OAAO;MACxE,OAAa;QACLb,IAAA,GAAOG,IAAA,CAAKf,KAAA,CAAMkB,UAAA,CAAW,IAAIC,UAAA,CAAWpB,IAAI,CAAC,CAAC;MACnD;IACP,OAAW;MACLa,IAAA,GAAOb,IAAA;IACR;IAED,IAAIa,IAAA,CAAKc,KAAA,KAAU,UAAad,IAAA,CAAKc,KAAA,CAAMC,OAAA,CAAQ,CAAC,IAAI,GAAG;MACzD,IAAInD,OAAA,EAASA,OAAA,CAAQ,IAAI4B,KAAA,CAAM,yEAAyE,CAAC;MACzG;IACD;IAED,MAAMlD,MAAA,GAAS,IAAI0E,UAAA,CAAWhB,IAAA,EAAM;MAClCjC,IAAA,EAAMA,IAAA,IAAQ,KAAKD,YAAA,IAAgB;MACnCmD,WAAA,EAAa,KAAKA,WAAA;MAClBjC,aAAA,EAAe,KAAKA,aAAA;MACpBhD,OAAA,EAAS,KAAKA,OAAA;MACdE,UAAA,EAAY,KAAKA,UAAA;MACjBC,cAAA,EAAgB,KAAKA;IAC3B,CAAK;IAEDG,MAAA,CAAO4E,UAAA,CAAWnC,gBAAA,CAAiB,KAAKC,aAAa;IAErD,SAASmC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK/E,eAAA,CAAgBgF,MAAA,EAAQD,CAAA,IAAK;MACpD,MAAME,MAAA,GAAS,KAAKjF,eAAA,CAAgB+E,CAAC,EAAE7E,MAAM;MAE7C,IAAI,CAAC+E,MAAA,CAAOC,IAAA,EAAM/C,OAAA,CAAQC,KAAA,CAAM,sDAAsD;MAEtF0B,OAAA,CAAQmB,MAAA,CAAOC,IAAI,IAAID,MAAA;MAMvBpB,UAAA,CAAWoB,MAAA,CAAOC,IAAI,IAAI;IAC3B;IAED,IAAItB,IAAA,CAAKuB,cAAA,EAAgB;MACvB,SAASJ,CAAA,GAAI,GAAGA,CAAA,GAAInB,IAAA,CAAKuB,cAAA,CAAeH,MAAA,EAAQ,EAAED,CAAA,EAAG;QACnD,MAAMK,aAAA,GAAgBxB,IAAA,CAAKuB,cAAA,CAAeJ,CAAC;QAC3C,MAAMM,kBAAA,GAAqBzB,IAAA,CAAKyB,kBAAA,IAAsB,EAAE;QAExD,QAAQD,aAAA;UACN,KAAKd,UAAA,CAAWgB,mBAAA;YACdzB,UAAA,CAAWuB,aAAa,IAAI,IAAIG,2BAAA,CAA6B;YAC7D;UAEF,KAAKjB,UAAA,CAAWkB,0BAAA;YACd3B,UAAA,CAAWuB,aAAa,IAAI,IAAIK,iCAAA,CAAkC7B,IAAA,EAAM,KAAK/D,WAAW;YACxF;UAEF,KAAKyE,UAAA,CAAWoB,qBAAA;YACd7B,UAAA,CAAWuB,aAAa,IAAI,IAAIO,6BAAA,CAA+B;YAC/D;UAEF,KAAKrB,UAAA,CAAWsB,qBAAA;YACd/B,UAAA,CAAWuB,aAAa,IAAI,IAAIS,6BAAA,CAA+B;YAC/D;UAEF;YACE,IAAIR,kBAAA,CAAmB7B,OAAA,CAAQ4B,aAAa,KAAK,KAAKtB,OAAA,CAAQsB,aAAa,MAAM,QAAW;cAC1FjD,OAAA,CAAQ2D,IAAA,CAAK,0CAA0CV,aAAA,GAAgB,IAAI;YAC5E;QACJ;MACF;IACF;IAEDlF,MAAA,CAAO6F,aAAA,CAAclC,UAAU;IAC/B3D,MAAA,CAAO8F,UAAA,CAAWlC,OAAO;IACzB5D,MAAA,CAAO8C,KAAA,CAAM1B,MAAA,EAAQE,OAAO;EAC7B;EAEDyE,WAAWlD,IAAA,EAAMpB,IAAA,EAAM;IACrB,MAAMF,KAAA,GAAQ;IAEd,OAAO,IAAIyE,OAAA,CAAQ,UAAUC,OAAA,EAASC,MAAA,EAAQ;MAC5C3E,KAAA,CAAMuB,KAAA,CAAMD,IAAA,EAAMpB,IAAA,EAAMwE,OAAA,EAASC,MAAM;IAC7C,CAAK;EACF;AACH;AAIA,SAASC,aAAA,EAAe;EACtB,IAAIC,OAAA,GAAU,CAAE;EAEhB,OAAO;IACLC,GAAA,EAAK,SAAAA,CAAUC,GAAA,EAAK;MAClB,OAAOF,OAAA,CAAQE,GAAG;IACnB;IAEDC,GAAA,EAAK,SAAAA,CAAUD,GAAA,EAAKE,MAAA,EAAQ;MAC1BJ,OAAA,CAAQE,GAAG,IAAIE,MAAA;IAChB;IAEDC,MAAA,EAAQ,SAAAA,CAAUH,GAAA,EAAK;MACrB,OAAOF,OAAA,CAAQE,GAAG;IACnB;IAEDI,SAAA,EAAW,SAAAA,CAAA,EAAY;MACrBN,OAAA,GAAU,CAAE;IACb;EACF;AACH;AAMA,MAAMhC,UAAA,GAAa;EACjBC,eAAA,EAAiB;EACjBiB,0BAAA,EAA4B;EAC5BqB,mBAAA,EAAqB;EACrBC,uBAAA,EAAyB;EACzBC,wBAAA,EAA0B;EAC1BC,iBAAA,EAAmB;EACnBC,mBAAA,EAAqB;EACrBC,sBAAA,EAAwB;EACxBC,0BAAA,EAA4B;EAC5BC,yBAAA,EAA2B;EAC3BC,wBAAA,EAA0B;EAC1B/B,mBAAA,EAAqB;EACrBgC,oBAAA,EAAsB;EACtBC,kBAAA,EAAoB;EACpB7B,qBAAA,EAAuB;EACvBE,qBAAA,EAAuB;EACvB4B,+BAAA,EAAiC;EACjCC,kBAAA,EAAoB;EACpBC,gBAAA,EAAkB;EAClBC,gBAAA,EAAkB;EAClBC,uBAAA,EAAyB;EACzBC,uBAAA,EAAyB;AAC3B;AAOA,MAAM5G,mBAAA,CAAoB;EACxBtB,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWuC,mBAAA;IAGvB,KAAKiB,KAAA,GAAQ;MAAEC,IAAA,EAAM;MAAIC,IAAA,EAAM;IAAI;EACpC;EAEDC,UAAA,EAAY;IACV,MAAM/H,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgI,QAAA,GAAW,KAAKhI,MAAA,CAAO0D,IAAA,CAAKuE,KAAA,IAAS,EAAE;IAE7C,SAASC,SAAA,GAAY,GAAGC,UAAA,GAAaH,QAAA,CAASlD,MAAA,EAAQoD,SAAA,GAAYC,UAAA,EAAYD,SAAA,IAAa;MACzF,MAAME,OAAA,GAAUJ,QAAA,CAASE,SAAS;MAElC,IAAIE,OAAA,CAAQzE,UAAA,IAAcyE,OAAA,CAAQzE,UAAA,CAAW,KAAKqB,IAAI,KAAKoD,OAAA,CAAQzE,UAAA,CAAW,KAAKqB,IAAI,EAAEqD,KAAA,KAAU,QAAW;QAC5GrI,MAAA,CAAOsI,WAAA,CAAY,KAAKV,KAAA,EAAOQ,OAAA,CAAQzE,UAAA,CAAW,KAAKqB,IAAI,EAAEqD,KAAK;MACnE;IACF;EACF;EAEDE,WAAWC,UAAA,EAAY;IACrB,MAAMxI,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMyI,QAAA,GAAW,WAAWD,UAAA;IAC5B,IAAIE,UAAA,GAAa1I,MAAA,CAAO4H,KAAA,CAAMvB,GAAA,CAAIoC,QAAQ;IAE1C,IAAIC,UAAA,EAAY,OAAOA,UAAA;IAEvB,MAAMhF,IAAA,GAAO1D,MAAA,CAAO0D,IAAA;IACpB,MAAMC,UAAA,GAAcD,IAAA,CAAKC,UAAA,IAAcD,IAAA,CAAKC,UAAA,CAAW,KAAKqB,IAAI,KAAM,CAAE;IACxE,MAAM2D,SAAA,GAAYhF,UAAA,CAAWiF,MAAA,IAAU,EAAE;IACzC,MAAMC,QAAA,GAAWF,SAAA,CAAUH,UAAU;IACrC,IAAIM,SAAA;IAEJ,MAAMC,KAAA,GAAQ,IAAIC,KAAA,CAAM,QAAQ;IAEhC,IAAIH,QAAA,CAASE,KAAA,KAAU,QACrBA,KAAA,CAAME,MAAA,CAAOJ,QAAA,CAASE,KAAA,CAAM,CAAC,GAAGF,QAAA,CAASE,KAAA,CAAM,CAAC,GAAGF,QAAA,CAASE,KAAA,CAAM,CAAC,GAAG3J,oBAAoB;IAE5F,MAAM8J,KAAA,GAAQL,QAAA,CAASK,KAAA,KAAU,SAAYL,QAAA,CAASK,KAAA,GAAQ;IAE9D,QAAQL,QAAA,CAASM,IAAA;MACf,KAAK;QACHL,SAAA,GAAY,IAAIM,gBAAA,CAAiBL,KAAK;QACtCD,SAAA,CAAUO,MAAA,CAAOC,QAAA,CAASC,GAAA,CAAI,GAAG,GAAG,EAAE;QACtCT,SAAA,CAAUvC,GAAA,CAAIuC,SAAA,CAAUO,MAAM;QAC9B;MAEF,KAAK;QACHP,SAAA,GAAY,IAAIU,UAAA,CAAWT,KAAK;QAChCD,SAAA,CAAUW,QAAA,GAAWP,KAAA;QACrB;MAEF,KAAK;QACHJ,SAAA,GAAY,IAAIY,SAAA,CAAUX,KAAK;QAC/BD,SAAA,CAAUW,QAAA,GAAWP,KAAA;QAErBL,QAAA,CAASc,IAAA,GAAOd,QAAA,CAASc,IAAA,IAAQ,CAAE;QACnCd,QAAA,CAASc,IAAA,CAAKC,cAAA,GAAiBf,QAAA,CAASc,IAAA,CAAKC,cAAA,KAAmB,SAAYf,QAAA,CAASc,IAAA,CAAKC,cAAA,GAAiB;QAC3Gf,QAAA,CAASc,IAAA,CAAKE,cAAA,GACZhB,QAAA,CAASc,IAAA,CAAKE,cAAA,KAAmB,SAAYhB,QAAA,CAASc,IAAA,CAAKE,cAAA,GAAiBC,IAAA,CAAKC,EAAA,GAAK;QACxFjB,SAAA,CAAUkB,KAAA,GAAQnB,QAAA,CAASc,IAAA,CAAKE,cAAA;QAChCf,SAAA,CAAUmB,QAAA,GAAW,IAAMpB,QAAA,CAASc,IAAA,CAAKC,cAAA,GAAiBf,QAAA,CAASc,IAAA,CAAKE,cAAA;QACxEf,SAAA,CAAUO,MAAA,CAAOC,QAAA,CAASC,GAAA,CAAI,GAAG,GAAG,EAAE;QACtCT,SAAA,CAAUvC,GAAA,CAAIuC,SAAA,CAAUO,MAAM;QAC9B;MAEF;QACE,MAAM,IAAInG,KAAA,CAAM,8CAA8C2F,QAAA,CAASM,IAAI;IAC9E;IAIDL,SAAA,CAAUQ,QAAA,CAASC,GAAA,CAAI,GAAG,GAAG,CAAC;IAE9BT,SAAA,CAAUoB,KAAA,GAAQ;IAElBC,sBAAA,CAAuBrB,SAAA,EAAWD,QAAQ;IAE1C,IAAIA,QAAA,CAASuB,SAAA,KAAc,QAAWtB,SAAA,CAAUsB,SAAA,GAAYvB,QAAA,CAASuB,SAAA;IAErEtB,SAAA,CAAU9D,IAAA,GAAOhF,MAAA,CAAOqK,gBAAA,CAAiBxB,QAAA,CAAS7D,IAAA,IAAQ,WAAWwD,UAAU;IAE/EE,UAAA,GAAa1C,OAAA,CAAQC,OAAA,CAAQ6C,SAAS;IAEtC9I,MAAA,CAAO4H,KAAA,CAAMrB,GAAA,CAAIkC,QAAA,EAAUC,UAAU;IAErC,OAAOA,UAAA;EACR;EAED4B,cAAcnB,IAAA,EAAMoB,KAAA,EAAO;IACzB,IAAIpB,IAAA,KAAS,SAAS;IAEtB,OAAO,KAAKZ,UAAA,CAAWgC,KAAK;EAC7B;EAEDC,qBAAqBtC,SAAA,EAAW;IAC9B,MAAMuC,KAAA,GAAO;IACb,MAAMzK,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM0D,IAAA,GAAO1D,MAAA,CAAO0D,IAAA;IACpB,MAAM0E,OAAA,GAAU1E,IAAA,CAAKuE,KAAA,CAAMC,SAAS;IACpC,MAAMW,QAAA,GAAYT,OAAA,CAAQzE,UAAA,IAAcyE,OAAA,CAAQzE,UAAA,CAAW,KAAKqB,IAAI,KAAM,CAAE;IAC5E,MAAMwD,UAAA,GAAaK,QAAA,CAASR,KAAA;IAE5B,IAAIG,UAAA,KAAe,QAAW,OAAO;IAErC,OAAO,KAAKD,UAAA,CAAWC,UAAU,EAAEkC,IAAA,CAAK,UAAUrC,KAAA,EAAO;MACvD,OAAOrI,MAAA,CAAO2K,WAAA,CAAYF,KAAA,CAAK7C,KAAA,EAAOY,UAAA,EAAYH,KAAK;IAC7D,CAAK;EACF;AACH;AAOA,MAAMhD,2BAAA,CAA4B;EAChC5F,YAAA,EAAc;IACZ,KAAKuF,IAAA,GAAOZ,UAAA,CAAWgB,mBAAA;EACxB;EAEDwF,gBAAA,EAAkB;IAChB,OAAOC,iBAAA;EACR;EAEDC,aAAaC,cAAA,EAAgBC,WAAA,EAAahL,MAAA,EAAQ;IAChD,MAAMiL,OAAA,GAAU,EAAE;IAElBF,cAAA,CAAehC,KAAA,GAAQ,IAAIC,KAAA,CAAM,GAAK,GAAK,CAAG;IAC9C+B,cAAA,CAAeG,OAAA,GAAU;IAEzB,MAAMC,iBAAA,GAAoBH,WAAA,CAAYI,oBAAA;IAEtC,IAAID,iBAAA,EAAmB;MACrB,IAAIE,KAAA,CAAMC,OAAA,CAAQH,iBAAA,CAAkBI,eAAe,GAAG;QACpD,MAAMC,KAAA,GAAQL,iBAAA,CAAkBI,eAAA;QAEhCR,cAAA,CAAehC,KAAA,CAAME,MAAA,CAAOuC,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGpM,oBAAoB;QAC9E2L,cAAA,CAAeG,OAAA,GAAUM,KAAA,CAAM,CAAC;MACjC;MAED,IAAIL,iBAAA,CAAkBM,gBAAA,KAAqB,QAAW;QACpDR,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,OAAOI,iBAAA,CAAkBM,gBAAA,EAAkBtM,cAAc,CAAC;MAC7G;IACF;IAED,OAAO6G,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAMvK,sCAAA,CAAuC;EAC3CjB,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWkD,+BAAA;EACxB;EAEDsE,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAM8F,gBAAA,GAAmBf,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,EAAE+G,gBAAA;IAE3D,IAAIA,gBAAA,KAAqB,QAAW;MAClChB,cAAA,CAAeiB,iBAAA,GAAoBD,gBAAA;IACpC;IAED,OAAO/F,OAAA,CAAQC,OAAA,CAAS;EACzB;AACH;AAOA,MAAMhG,+BAAA,CAAgC;EACpCR,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWwC,uBAAA;EACxB;EAEDgE,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElB,MAAMiB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD,IAAIkH,SAAA,CAAUC,eAAA,KAAoB,QAAW;MAC3CpB,cAAA,CAAeqB,SAAA,GAAYF,SAAA,CAAUC,eAAA;IACtC;IAED,IAAID,SAAA,CAAUG,gBAAA,KAAqB,QAAW;MAC5CpB,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,gBAAgBmB,SAAA,CAAUG,gBAAgB,CAAC;IAC9F;IAED,IAAIH,SAAA,CAAUI,wBAAA,KAA6B,QAAW;MACpDvB,cAAA,CAAewB,kBAAA,GAAqBL,SAAA,CAAUI,wBAAA;IAC/C;IAED,IAAIJ,SAAA,CAAUM,yBAAA,KAA8B,QAAW;MACrDvB,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,yBAAyBmB,SAAA,CAAUM,yBAAyB,CAAC;IAChH;IAED,IAAIN,SAAA,CAAUO,sBAAA,KAA2B,QAAW;MAClDxB,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,sBAAsBmB,SAAA,CAAUO,sBAAsB,CAAC;MAEzG,IAAIP,SAAA,CAAUO,sBAAA,CAAuBC,KAAA,KAAU,QAAW;QACxD,MAAMA,KAAA,GAAQR,SAAA,CAAUO,sBAAA,CAAuBC,KAAA;QAE/C3B,cAAA,CAAe4B,oBAAA,GAAuB,IAAIC,OAAA,CAAQF,KAAA,EAAOA,KAAK;MAC/D;IACF;IAED,OAAO1G,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAM/K,gCAAA,CAAiC;EACrCT,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWyC,wBAAA;EACxB;EAED+D,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMiG,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD+F,cAAA,CAAe8B,UAAA,GAAaX,SAAA,CAAUW,UAAA,KAAe,SAAYX,SAAA,CAAUW,UAAA,GAAa;IAExF,OAAO7G,OAAA,CAAQC,OAAA,CAAS;EACzB;AACH;AAOA,MAAMrF,iCAAA,CAAkC;EACtCnB,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAW8C,yBAAA;EACxB;EAED0D,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElB,MAAMiB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD,IAAIkH,SAAA,CAAUY,iBAAA,KAAsB,QAAW;MAC7C/B,cAAA,CAAegC,WAAA,GAAcb,SAAA,CAAUY,iBAAA;IACxC;IAED,IAAIZ,SAAA,CAAUc,kBAAA,KAAuB,QAAW;MAC9C/B,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,kBAAkBmB,SAAA,CAAUc,kBAAkB,CAAC;IAClG;IAED,IAAId,SAAA,CAAUe,cAAA,KAAmB,QAAW;MAC1ClC,cAAA,CAAemC,cAAA,GAAiBhB,SAAA,CAAUe,cAAA;IAC3C;IAED,IAAIlC,cAAA,CAAeoC,yBAAA,KAA8B,QAAW;MAC1DpC,cAAA,CAAeoC,yBAAA,GAA4B,CAAC,KAAK,GAAG;IACrD;IAED,IAAIjB,SAAA,CAAUkB,2BAAA,KAAgC,QAAW;MACvDrC,cAAA,CAAeoC,yBAAA,CAA0B,CAAC,IAAIjB,SAAA,CAAUkB,2BAAA;IACzD;IAED,IAAIlB,SAAA,CAAUmB,2BAAA,KAAgC,QAAW;MACvDtC,cAAA,CAAeoC,yBAAA,CAA0B,CAAC,IAAIjB,SAAA,CAAUmB,2BAAA;IACzD;IAED,IAAInB,SAAA,CAAUoB,2BAAA,KAAgC,QAAW;MACvDrC,OAAA,CAAQ1H,IAAA,CACNvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,2BAA2BmB,SAAA,CAAUoB,2BAA2B,CACtG;IACF;IAED,OAAOtH,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAM3K,2BAAA,CAA4B;EAChCb,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAW2C,mBAAA;EACxB;EAED6D,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElBF,cAAA,CAAewC,UAAA,GAAa,IAAIvE,KAAA,CAAM,GAAG,GAAG,CAAC;IAC7C+B,cAAA,CAAeyC,cAAA,GAAiB;IAChCzC,cAAA,CAAe0C,KAAA,GAAQ;IAEvB,MAAMvB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD,IAAIkH,SAAA,CAAUwB,gBAAA,KAAqB,QAAW;MAC5C,MAAMC,WAAA,GAAczB,SAAA,CAAUwB,gBAAA;MAC9B3C,cAAA,CAAewC,UAAA,CAAWtE,MAAA,CAAO0E,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGvO,oBAAoB;IACtG;IAED,IAAI8M,SAAA,CAAU0B,oBAAA,KAAyB,QAAW;MAChD7C,cAAA,CAAeyC,cAAA,GAAiBtB,SAAA,CAAU0B,oBAAA;IAC3C;IAED,IAAI1B,SAAA,CAAU2B,iBAAA,KAAsB,QAAW;MAC7C5C,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,iBAAiBmB,SAAA,CAAU2B,iBAAA,EAAmB1O,cAAc,CAAC;IAChH;IAED,IAAI+M,SAAA,CAAU4B,qBAAA,KAA0B,QAAW;MACjD7C,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,qBAAqBmB,SAAA,CAAU4B,qBAAqB,CAAC;IACxG;IAED,OAAO9H,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAQA,MAAM1K,kCAAA,CAAmC;EACvCd,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAW6C,0BAAA;EACxB;EAED2D,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElB,MAAMiB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD,IAAIkH,SAAA,CAAU6B,kBAAA,KAAuB,QAAW;MAC9ChD,cAAA,CAAeiD,YAAA,GAAe9B,SAAA,CAAU6B,kBAAA;IACzC;IAED,IAAI7B,SAAA,CAAU+B,mBAAA,KAAwB,QAAW;MAC/ChD,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,mBAAmBmB,SAAA,CAAU+B,mBAAmB,CAAC;IACpG;IAED,OAAOjI,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAMzK,4BAAA,CAA6B;EACjCf,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWgD,oBAAA;EACxB;EAEDwD,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElB,MAAMiB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD+F,cAAA,CAAemD,SAAA,GAAYhC,SAAA,CAAUiC,eAAA,KAAoB,SAAYjC,SAAA,CAAUiC,eAAA,GAAkB;IAEjG,IAAIjC,SAAA,CAAUkC,gBAAA,KAAqB,QAAW;MAC5CnD,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,gBAAgBmB,SAAA,CAAUkC,gBAAgB,CAAC;IAC9F;IAEDrD,cAAA,CAAesD,mBAAA,GAAsBnC,SAAA,CAAUmC,mBAAA,IAAuBC,QAAA;IAEtE,MAAMC,UAAA,GAAarC,SAAA,CAAUsC,gBAAA,IAAoB,CAAC,GAAG,GAAG,CAAC;IACzDzD,cAAA,CAAeyD,gBAAA,GAAmB,IAAIxF,KAAA,CAAK,EAAGC,MAAA,CAC5CsF,UAAA,CAAW,CAAC,GACZA,UAAA,CAAW,CAAC,GACZA,UAAA,CAAW,CAAC,GACZnP,oBACD;IAED,OAAO4G,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAMxK,yBAAA,CAA0B;EAC9BhB,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAW0C,iBAAA;EACxB;EAED8D,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMiG,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD+F,cAAA,CAAe0D,GAAA,GAAMvC,SAAA,CAAUuC,GAAA,KAAQ,SAAYvC,SAAA,CAAUuC,GAAA,GAAM;IAEnE,OAAOzI,OAAA,CAAQC,OAAA,CAAS;EACzB;AACH;AAOA,MAAMtF,8BAAA,CAA+B;EACnClB,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAW4C,sBAAA;EACxB;EAED4D,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElB,MAAMiB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD+F,cAAA,CAAe2D,iBAAA,GAAoBxC,SAAA,CAAUyC,cAAA,KAAmB,SAAYzC,SAAA,CAAUyC,cAAA,GAAiB;IAEvG,IAAIzC,SAAA,CAAU0C,eAAA,KAAoB,QAAW;MAC3C3D,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,wBAAwBmB,SAAA,CAAU0C,eAAe,CAAC;IACrG;IAED,MAAML,UAAA,GAAarC,SAAA,CAAU2C,mBAAA,IAAuB,CAAC,GAAG,GAAG,CAAC;IAC5D9D,cAAA,CAAe+D,aAAA,GAAgB,IAAI9F,KAAA,CAAK,EAAGC,MAAA,CAAOsF,UAAA,CAAW,CAAC,GAAGA,UAAA,CAAW,CAAC,GAAGA,UAAA,CAAW,CAAC,GAAGnP,oBAAoB;IAEnH,IAAI8M,SAAA,CAAU6C,oBAAA,KAAyB,QAAW;MAChD9D,OAAA,CAAQ1H,IAAA,CACNvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,oBAAoBmB,SAAA,CAAU6C,oBAAA,EAAsB5P,cAAc,CACxG;IACF;IAED,OAAO6G,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAMnK,0BAAA,CAA2B;EAC/BrB,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWmD,kBAAA;EACxB;EAEDqD,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElB,MAAMiB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD+F,cAAA,CAAeiE,SAAA,GAAY9C,SAAA,CAAU+C,UAAA,KAAe,SAAY/C,SAAA,CAAU+C,UAAA,GAAa;IAEvF,IAAI/C,SAAA,CAAUgD,WAAA,KAAgB,QAAW;MACvCjE,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,WAAWmB,SAAA,CAAUgD,WAAW,CAAC;IACpF;IAED,OAAOlJ,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAMpK,gCAAA,CAAiC;EACrCpB,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAW+C,wBAAA;EACxB;EAEDyD,gBAAgBiB,aAAA,EAAe;IAC7B,MAAM7L,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG,OAAO;IAE1E,OAAOiH,oBAAA;EACR;EAEDL,qBAAqBC,aAAA,EAAed,cAAA,EAAgB;IAClD,MAAM/K,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAMgL,WAAA,GAAchL,MAAA,CAAO0D,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEvD,IAAI,CAACb,WAAA,CAAYrH,UAAA,IAAc,CAACqH,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI,GAAG;MACjE,OAAOgB,OAAA,CAAQC,OAAA,CAAS;IACzB;IAED,MAAMgF,OAAA,GAAU,EAAE;IAElB,MAAMiB,SAAA,GAAYlB,WAAA,CAAYrH,UAAA,CAAW,KAAKqB,IAAI;IAElD,IAAIkH,SAAA,CAAUiD,kBAAA,KAAuB,QAAW;MAC9CpE,cAAA,CAAeqE,UAAA,GAAalD,SAAA,CAAUiD,kBAAA;IACvC;IAED,IAAIjD,SAAA,CAAUmD,kBAAA,KAAuB,QAAW;MAC9CtE,cAAA,CAAesE,kBAAA,GAAqBnD,SAAA,CAAUmD,kBAAA;IAC/C;IAED,IAAInD,SAAA,CAAUoD,iBAAA,KAAsB,QAAW;MAC7CrE,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,iBAAiBmB,SAAA,CAAUoD,iBAAiB,CAAC;IAChG;IAED,OAAOtJ,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;AACH;AAOA,MAAM9K,0BAAA,CAA2B;EAC/BV,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWiD,kBAAA;EACxB;EAEDkI,YAAYC,YAAA,EAAc;IACxB,MAAMxP,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM0D,IAAA,GAAO1D,MAAA,CAAO0D,IAAA;IAEpB,MAAM+L,UAAA,GAAa/L,IAAA,CAAKgM,QAAA,CAASF,YAAY;IAE7C,IAAI,CAACC,UAAA,CAAW9L,UAAA,IAAc,CAAC8L,UAAA,CAAW9L,UAAA,CAAW,KAAKqB,IAAI,GAAG;MAC/D,OAAO;IACR;IAED,MAAMkH,SAAA,GAAYuD,UAAA,CAAW9L,UAAA,CAAW,KAAKqB,IAAI;IACjD,MAAM3C,MAAA,GAASrC,MAAA,CAAO2P,OAAA,CAAQ/P,UAAA;IAE9B,IAAI,CAACyC,MAAA,EAAQ;MACX,IAAIqB,IAAA,CAAKyB,kBAAA,IAAsBzB,IAAA,CAAKyB,kBAAA,CAAmB7B,OAAA,CAAQ,KAAK0B,IAAI,KAAK,GAAG;QAC9E,MAAM,IAAI9B,KAAA,CAAM,6EAA6E;MACrG,OAAa;QAEL,OAAO;MACR;IACF;IAED,OAAOlD,MAAA,CAAO4P,gBAAA,CAAiBJ,YAAA,EAActD,SAAA,CAAU2D,MAAA,EAAQxN,MAAM;EACtE;AACH;AAOA,MAAMjC,wBAAA,CAAyB;EAC7BX,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWoD,gBAAA;IACvB,KAAKsI,WAAA,GAAc;EACpB;EAEDP,YAAYC,YAAA,EAAc;IACxB,MAAMxK,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMhF,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM0D,IAAA,GAAO1D,MAAA,CAAO0D,IAAA;IAEpB,MAAM+L,UAAA,GAAa/L,IAAA,CAAKgM,QAAA,CAASF,YAAY;IAE7C,IAAI,CAACC,UAAA,CAAW9L,UAAA,IAAc,CAAC8L,UAAA,CAAW9L,UAAA,CAAWqB,IAAI,GAAG;MAC1D,OAAO;IACR;IAED,MAAMkH,SAAA,GAAYuD,UAAA,CAAW9L,UAAA,CAAWqB,IAAI;IAC5C,MAAM6K,MAAA,GAASnM,IAAA,CAAKqM,MAAA,CAAO7D,SAAA,CAAU2D,MAAM;IAE3C,IAAIxN,MAAA,GAASrC,MAAA,CAAOgQ,aAAA;IACpB,IAAIH,MAAA,CAAOI,GAAA,EAAK;MACd,MAAMC,OAAA,GAAUlQ,MAAA,CAAO2P,OAAA,CAAQjQ,OAAA,CAAQyQ,UAAA,CAAWN,MAAA,CAAOI,GAAG;MAC5D,IAAIC,OAAA,KAAY,MAAM7N,MAAA,GAAS6N,OAAA;IAChC;IAED,OAAO,KAAKE,aAAA,CAAa,EAAG1F,IAAA,CAAK,UAAUoF,WAAA,EAAa;MACtD,IAAIA,WAAA,EAAa,OAAO9P,MAAA,CAAO4P,gBAAA,CAAiBJ,YAAA,EAActD,SAAA,CAAU2D,MAAA,EAAQxN,MAAM;MAEtF,IAAIqB,IAAA,CAAKyB,kBAAA,IAAsBzB,IAAA,CAAKyB,kBAAA,CAAmB7B,OAAA,CAAQ0B,IAAI,KAAK,GAAG;QACzE,MAAM,IAAI9B,KAAA,CAAM,2DAA2D;MAC5E;MAGD,OAAOlD,MAAA,CAAOuP,WAAA,CAAYC,YAAY;IAC5C,CAAK;EACF;EAEDY,cAAA,EAAgB;IACd,IAAI,CAAC,KAAKN,WAAA,EAAa;MACrB,KAAKA,WAAA,GAAc,IAAI9J,OAAA,CAAQ,UAAUC,OAAA,EAAS;QAChD,MAAMoK,KAAA,GAAQ,IAAIC,KAAA,CAAO;QAIzBD,KAAA,CAAME,GAAA,GAAM;QAEZF,KAAA,CAAMG,MAAA,GAASH,KAAA,CAAMI,OAAA,GAAU,YAAY;UACzCxK,OAAA,CAAQoK,KAAA,CAAMK,MAAA,KAAW,CAAC;QAC3B;MACT,CAAO;IACF;IAED,OAAO,KAAKZ,WAAA;EACb;AACH;AAOA,MAAMzP,wBAAA,CAAyB;EAC7BZ,YAAYO,MAAA,EAAQ;IAClB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKgF,IAAA,GAAOZ,UAAA,CAAWqD,gBAAA;IACvB,KAAKqI,WAAA,GAAc;EACpB;EAEDP,YAAYC,YAAA,EAAc;IACxB,MAAMxK,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMhF,MAAA,GAAS,KAAKA,MAAA;IACpB,MAAM0D,IAAA,GAAO1D,MAAA,CAAO0D,IAAA;IAEpB,MAAM+L,UAAA,GAAa/L,IAAA,CAAKgM,QAAA,CAASF,YAAY;IAE7C,IAAI,CAACC,UAAA,CAAW9L,UAAA,IAAc,CAAC8L,UAAA,CAAW9L,UAAA,CAAWqB,IAAI,GAAG;MAC1D,OAAO;IACR;IAED,MAAMkH,SAAA,GAAYuD,UAAA,CAAW9L,UAAA,CAAWqB,IAAI;IAC5C,MAAM6K,MAAA,GAASnM,IAAA,CAAKqM,MAAA,CAAO7D,SAAA,CAAU2D,MAAM;IAE3C,IAAIxN,MAAA,GAASrC,MAAA,CAAOgQ,aAAA;IACpB,IAAIH,MAAA,CAAOI,GAAA,EAAK;MACd,MAAMC,OAAA,GAAUlQ,MAAA,CAAO2P,OAAA,CAAQjQ,OAAA,CAAQyQ,UAAA,CAAWN,MAAA,CAAOI,GAAG;MAC5D,IAAIC,OAAA,KAAY,MAAM7N,MAAA,GAAS6N,OAAA;IAChC;IAED,OAAO,KAAKE,aAAA,CAAa,EAAG1F,IAAA,CAAK,UAAUoF,WAAA,EAAa;MACtD,IAAIA,WAAA,EAAa,OAAO9P,MAAA,CAAO4P,gBAAA,CAAiBJ,YAAA,EAActD,SAAA,CAAU2D,MAAA,EAAQxN,MAAM;MAEtF,IAAIqB,IAAA,CAAKyB,kBAAA,IAAsBzB,IAAA,CAAKyB,kBAAA,CAAmB7B,OAAA,CAAQ0B,IAAI,KAAK,GAAG;QACzE,MAAM,IAAI9B,KAAA,CAAM,2DAA2D;MAC5E;MAGD,OAAOlD,MAAA,CAAOuP,WAAA,CAAYC,YAAY;IAC5C,CAAK;EACF;EAEDY,cAAA,EAAgB;IACd,IAAI,CAAC,KAAKN,WAAA,EAAa;MACrB,KAAKA,WAAA,GAAc,IAAI9J,OAAA,CAAQ,UAAUC,OAAA,EAAS;QAChD,MAAMoK,KAAA,GAAQ,IAAIC,KAAA,CAAO;QAGzBD,KAAA,CAAME,GAAA,GACJ;QACFF,KAAA,CAAMG,MAAA,GAASH,KAAA,CAAMI,OAAA,GAAU,YAAY;UACzCxK,OAAA,CAAQoK,KAAA,CAAMK,MAAA,KAAW,CAAC;QAC3B;MACT,CAAO;IACF;IAED,OAAO,KAAKZ,WAAA;EACb;AACH;AAOA,MAAM9O,sBAAA,CAAuB;EAC3BvB,YAAYO,MAAA,EAAQ;IAClB,KAAKgF,IAAA,GAAOZ,UAAA,CAAWsD,uBAAA;IACvB,KAAK1H,MAAA,GAASA,MAAA;EACf;EAED2Q,eAAepG,KAAA,EAAO;IACpB,MAAM7G,IAAA,GAAO,KAAK1D,MAAA,CAAO0D,IAAA;IACzB,MAAMkN,UAAA,GAAalN,IAAA,CAAKmN,WAAA,CAAYtG,KAAK;IAEzC,IAAIqG,UAAA,CAAWjN,UAAA,IAAciN,UAAA,CAAWjN,UAAA,CAAW,KAAKqB,IAAI,GAAG;MAC7D,MAAM8L,YAAA,GAAeF,UAAA,CAAWjN,UAAA,CAAW,KAAKqB,IAAI;MAEpD,MAAM+L,MAAA,GAAS,KAAK/Q,MAAA,CAAOsK,aAAA,CAAc,UAAUwG,YAAA,CAAaC,MAAM;MACtE,MAAMC,OAAA,GAAU,KAAKhR,MAAA,CAAO2P,OAAA,CAAQ9P,cAAA;MAEpC,IAAI,CAACmR,OAAA,IAAW,CAACA,OAAA,CAAQC,SAAA,EAAW;QAClC,IAAIvN,IAAA,CAAKyB,kBAAA,IAAsBzB,IAAA,CAAKyB,kBAAA,CAAmB7B,OAAA,CAAQ,KAAK0B,IAAI,KAAK,GAAG;UAC9E,MAAM,IAAI9B,KAAA,CAAM,oFAAoF;QAC9G,OAAe;UAEL,OAAO;QACR;MACF;MAED,OAAO6N,MAAA,CAAOrG,IAAA,CAAK,UAAUwG,GAAA,EAAK;QAChC,MAAMC,UAAA,GAAaL,YAAA,CAAaK,UAAA,IAAc;QAC9C,MAAMC,UAAA,GAAaN,YAAA,CAAaM,UAAA,IAAc;QAE9C,MAAMC,KAAA,GAAQP,YAAA,CAAaO,KAAA;QAC3B,MAAMC,MAAA,GAASR,YAAA,CAAaS,UAAA;QAE5B,MAAM1B,MAAA,GAAS,IAAI5L,UAAA,CAAWiN,GAAA,EAAKC,UAAA,EAAYC,UAAU;QAEzD,IAAIJ,OAAA,CAAQQ,qBAAA,EAAuB;UACjC,OAAOR,OAAA,CACJQ,qBAAA,CAAsBH,KAAA,EAAOC,MAAA,EAAQzB,MAAA,EAAQiB,YAAA,CAAaW,IAAA,EAAMX,YAAA,CAAaY,MAAM,EACnFhH,IAAA,CAAK,UAAUiH,IAAA,EAAK;YACnB,OAAOA,IAAA,CAAIZ,MAAA;UACzB,CAAa;QACb,OAAe;UAEL,OAAOC,OAAA,CAAQY,KAAA,CAAMlH,IAAA,CAAK,YAAY;YACpC,MAAMmH,MAAA,GAAS,IAAI/N,WAAA,CAAYuN,KAAA,GAAQC,MAAM;YAC7CN,OAAA,CAAQc,gBAAA,CACN,IAAI7N,UAAA,CAAW4N,MAAM,GACrBR,KAAA,EACAC,MAAA,EACAzB,MAAA,EACAiB,YAAA,CAAaW,IAAA,EACbX,YAAA,CAAaY,MACd;YACD,OAAOG,MAAA;UACnB,CAAW;QACF;MACT,CAAO;IACP,OAAW;MACL,OAAO;IACR;EACF;AACH;AAQA,MAAM5Q,qBAAA,CAAsB;EAC1BxB,YAAYO,MAAA,EAAQ;IAClB,KAAKgF,IAAA,GAAOZ,UAAA,CAAWuD,uBAAA;IACvB,KAAK3H,MAAA,GAASA,MAAA;EACf;EAED+R,eAAe7J,SAAA,EAAW;IACxB,MAAMxE,IAAA,GAAO,KAAK1D,MAAA,CAAO0D,IAAA;IACzB,MAAM0E,OAAA,GAAU1E,IAAA,CAAKuE,KAAA,CAAMC,SAAS;IAEpC,IAAI,CAACE,OAAA,CAAQzE,UAAA,IAAc,CAACyE,OAAA,CAAQzE,UAAA,CAAW,KAAKqB,IAAI,KAAKoD,OAAA,CAAQ4J,IAAA,KAAS,QAAW;MACvF,OAAO;IACR;IAED,MAAMC,OAAA,GAAUvO,IAAA,CAAKwO,MAAA,CAAO9J,OAAA,CAAQ4J,IAAI;IAIxC,WAAWG,SAAA,IAAaF,OAAA,CAAQG,UAAA,EAAY;MAC1C,IACED,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBC,SAAA,IACnCH,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBE,cAAA,IACnCJ,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBG,YAAA,IACnCL,SAAA,CAAUV,IAAA,KAAS,QACnB;QACA,OAAO;MACR;IACF;IAED,MAAMX,YAAA,GAAe1I,OAAA,CAAQzE,UAAA,CAAW,KAAKqB,IAAI;IACjD,MAAMyN,aAAA,GAAgB3B,YAAA,CAAa4B,UAAA;IAInC,MAAMzH,OAAA,GAAU,EAAE;IAClB,MAAMyH,UAAA,GAAa,CAAE;IAErB,WAAWpM,GAAA,IAAOmM,aAAA,EAAe;MAC/BxH,OAAA,CAAQ1H,IAAA,CACN,KAAKvD,MAAA,CAAOsK,aAAA,CAAc,YAAYmI,aAAA,CAAcnM,GAAG,CAAC,EAAEoE,IAAA,CAAMiI,QAAA,IAAa;QAC3ED,UAAA,CAAWpM,GAAG,IAAIqM,QAAA;QAClB,OAAOD,UAAA,CAAWpM,GAAG;MAC/B,CAAS,CACF;IACF;IAED,IAAI2E,OAAA,CAAQnG,MAAA,GAAS,GAAG;MACtB,OAAO;IACR;IAEDmG,OAAA,CAAQ1H,IAAA,CAAK,KAAKvD,MAAA,CAAO+R,cAAA,CAAe7J,SAAS,CAAC;IAElD,OAAOlC,OAAA,CAAQ2F,GAAA,CAAIV,OAAO,EAAEP,IAAA,CAAMkI,OAAA,IAAY;MAC5C,MAAMC,UAAA,GAAaD,OAAA,CAAQE,GAAA,CAAK;MAChC,MAAMZ,MAAA,GAASW,UAAA,CAAWE,OAAA,GAAUF,UAAA,CAAWG,QAAA,GAAW,CAACH,UAAU;MACrE,MAAMxB,KAAA,GAAQuB,OAAA,CAAQ,CAAC,EAAEvB,KAAA;MACzB,MAAM4B,eAAA,GAAkB,EAAE;MAE1B,WAAWjB,IAAA,IAAQE,MAAA,EAAQ;QAEzB,MAAMgB,CAAA,GAAI,IAAIC,OAAA,CAAS;QACvB,MAAMC,CAAA,GAAI,IAAIC,OAAA,CAAS;QACvB,MAAMC,CAAA,GAAI,IAAIC,UAAA,CAAY;QAC1B,MAAMC,CAAA,GAAI,IAAIH,OAAA,CAAQ,GAAG,GAAG,CAAC;QAE7B,MAAMI,aAAA,GAAgB,IAAIC,aAAA,CAAc1B,IAAA,CAAK2B,QAAA,EAAU3B,IAAA,CAAK4B,QAAA,EAAUvC,KAAK;QAE3E,SAASxM,CAAA,GAAI,GAAGA,CAAA,GAAIwM,KAAA,EAAOxM,CAAA,IAAK;UAC9B,IAAI6N,UAAA,CAAWmB,WAAA,EAAa;YAC1BT,CAAA,CAAEU,mBAAA,CAAoBpB,UAAA,CAAWmB,WAAA,EAAahP,CAAC;UAChD;UAED,IAAI6N,UAAA,CAAWqB,QAAA,EAAU;YACvBT,CAAA,CAAEQ,mBAAA,CAAoBpB,UAAA,CAAWqB,QAAA,EAAUlP,CAAC;UAC7C;UAED,IAAI6N,UAAA,CAAWsB,KAAA,EAAO;YACpBR,CAAA,CAAEM,mBAAA,CAAoBpB,UAAA,CAAWsB,KAAA,EAAOnP,CAAC;UAC1C;UAED4O,aAAA,CAAcQ,WAAA,CAAYpP,CAAA,EAAGqO,CAAA,CAAEgB,OAAA,CAAQd,CAAA,EAAGE,CAAA,EAAGE,CAAC,CAAC;QAChD;QAGD,WAAWW,aAAA,IAAiBzB,UAAA,EAAY;UACtC,IAAIyB,aAAA,KAAkB,YAAY;YAChC,MAAMC,IAAA,GAAO1B,UAAA,CAAWyB,aAAa;YACrCV,aAAA,CAAcY,aAAA,GAAgB,IAAIC,wBAAA,CAAyBF,IAAA,CAAK5I,KAAA,EAAO4I,IAAA,CAAKG,QAAA,EAAUH,IAAA,CAAKI,UAAU;UACjH,WAAqBL,aAAA,KAAkB,iBAAiBA,aAAA,KAAkB,cAAcA,aAAA,KAAkB,SAAS;YACvGnC,IAAA,CAAK2B,QAAA,CAASc,YAAA,CAAaN,aAAA,EAAezB,UAAA,CAAWyB,aAAa,CAAC;UACpE;QACF;QAGDO,QAAA,CAASC,SAAA,CAAUC,IAAA,CAAKC,IAAA,CAAKpB,aAAA,EAAezB,IAAI;QAEhD,KAAKhS,MAAA,CAAO8U,mBAAA,CAAoBrB,aAAa;QAE7CR,eAAA,CAAgB1P,IAAA,CAAKkQ,aAAa;MACnC;MAED,IAAIZ,UAAA,CAAWE,OAAA,EAAS;QACtBF,UAAA,CAAWkC,KAAA,CAAO;QAElBlC,UAAA,CAAWtM,GAAA,CAAI,GAAG0M,eAAe;QAEjC,OAAOJ,UAAA;MACR;MAED,OAAOI,eAAA,CAAgB,CAAC;IAC9B,CAAK;EACF;AACH;AAGA,MAAM9O,6BAAA,GAAgC;AACtC,MAAM6Q,8BAAA,GAAiC;AACvC,MAAMC,4BAAA,GAA+B;EAAEpR,IAAA,EAAM;EAAYqR,GAAA,EAAK;AAAY;AAE1E,MAAM5Q,mBAAA,CAAoB;EACxB7E,YAAYoD,IAAA,EAAM;IAChB,KAAKmC,IAAA,GAAOZ,UAAA,CAAWC,eAAA;IACvB,KAAKE,OAAA,GAAU;IACf,KAAK4Q,IAAA,GAAO;IAEZ,MAAMC,UAAA,GAAa,IAAIC,QAAA,CAASxS,IAAA,EAAM,GAAGmS,8BAA8B;IAEvE,KAAKM,MAAA,GAAS;MACZvR,KAAA,EAAOC,UAAA,CAAW,IAAIC,UAAA,CAAWpB,IAAA,CAAKqB,KAAA,CAAM,GAAG,CAAC,CAAC,CAAC;MAClDO,OAAA,EAAS2Q,UAAA,CAAWG,SAAA,CAAU,GAAG,IAAI;MACrCzQ,MAAA,EAAQsQ,UAAA,CAAWG,SAAA,CAAU,GAAG,IAAI;IACrC;IAED,IAAI,KAAKD,MAAA,CAAOvR,KAAA,KAAUI,6BAAA,EAA+B;MACvD,MAAM,IAAIjB,KAAA,CAAM,mDAAmD;IACpE,WAAU,KAAKoS,MAAA,CAAO7Q,OAAA,GAAU,GAAK;MACpC,MAAM,IAAIvB,KAAA,CAAM,gDAAgD;IACjE;IAED,MAAMsS,mBAAA,GAAsB,KAAKF,MAAA,CAAOxQ,MAAA,GAASkQ,8BAAA;IACjD,MAAMS,SAAA,GAAY,IAAIJ,QAAA,CAASxS,IAAA,EAAMmS,8BAA8B;IACnE,IAAIU,UAAA,GAAa;IAEjB,OAAOA,UAAA,GAAaF,mBAAA,EAAqB;MACvC,MAAMG,WAAA,GAAcF,SAAA,CAAUF,SAAA,CAAUG,UAAA,EAAY,IAAI;MACxDA,UAAA,IAAc;MAEd,MAAME,SAAA,GAAYH,SAAA,CAAUF,SAAA,CAAUG,UAAA,EAAY,IAAI;MACtDA,UAAA,IAAc;MAEd,IAAIE,SAAA,KAAcX,4BAAA,CAA6BpR,IAAA,EAAM;QACnD,MAAMgS,YAAA,GAAe,IAAI5R,UAAA,CAAWpB,IAAA,EAAMmS,8BAAA,GAAiCU,UAAA,EAAYC,WAAW;QAClG,KAAKpR,OAAA,GAAUP,UAAA,CAAW6R,YAAY;MAC9C,WAAiBD,SAAA,KAAcX,4BAAA,CAA6BC,GAAA,EAAK;QACzD,MAAM/D,UAAA,GAAa6D,8BAAA,GAAiCU,UAAA;QACpD,KAAKP,IAAA,GAAOtS,IAAA,CAAKqB,KAAA,CAAMiN,UAAA,EAAYA,UAAA,GAAawE,WAAW;MAC5D;MAIDD,UAAA,IAAcC,WAAA;IACf;IAED,IAAI,KAAKpR,OAAA,KAAY,MAAM;MACzB,MAAM,IAAIrB,KAAA,CAAM,2CAA2C;IAC5D;EACF;AACH;AAOA,MAAMqC,iCAAA,CAAkC;EACtC9F,YAAYiE,IAAA,EAAM/D,WAAA,EAAa;IAC7B,IAAI,CAACA,WAAA,EAAa;MAChB,MAAM,IAAIuD,KAAA,CAAM,qDAAqD;IACtE;IAED,KAAK8B,IAAA,GAAOZ,UAAA,CAAWkB,0BAAA;IACvB,KAAK5B,IAAA,GAAOA,IAAA;IACZ,KAAK/D,WAAA,GAAcA,WAAA;IACnB,KAAKA,WAAA,CAAYmW,OAAA,CAAS;EAC3B;EAEDC,gBAAgB5D,SAAA,EAAWnS,MAAA,EAAQ;IACjC,MAAM0D,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAM/D,WAAA,GAAc,KAAKA,WAAA;IACzB,MAAMqW,eAAA,GAAkB7D,SAAA,CAAUxO,UAAA,CAAW,KAAKqB,IAAI,EAAE4L,UAAA;IACxD,MAAMqF,gBAAA,GAAmB9D,SAAA,CAAUxO,UAAA,CAAW,KAAKqB,IAAI,EAAE0N,UAAA;IACzD,MAAMwD,iBAAA,GAAoB,CAAE;IAC5B,MAAMC,sBAAA,GAAyB,CAAE;IACjC,MAAMC,gBAAA,GAAmB,CAAE;IAE3B,WAAWjC,aAAA,IAAiB8B,gBAAA,EAAkB;MAC5C,MAAMI,kBAAA,GAAqBC,UAAA,CAAWnC,aAAa,KAAKA,aAAA,CAAcoC,WAAA,CAAa;MAEnFL,iBAAA,CAAkBG,kBAAkB,IAAIJ,gBAAA,CAAiB9B,aAAa;IACvE;IAED,WAAWA,aAAA,IAAiBhC,SAAA,CAAUO,UAAA,EAAY;MAChD,MAAM2D,kBAAA,GAAqBC,UAAA,CAAWnC,aAAa,KAAKA,aAAA,CAAcoC,WAAA,CAAa;MAEnF,IAAIN,gBAAA,CAAiB9B,aAAa,MAAM,QAAW;QACjD,MAAMqC,WAAA,GAAc9S,IAAA,CAAK+S,SAAA,CAAUtE,SAAA,CAAUO,UAAA,CAAWyB,aAAa,CAAC;QACtE,MAAMuC,aAAA,GAAgBC,qBAAA,CAAsBH,WAAA,CAAYE,aAAa;QAErEN,gBAAA,CAAiBC,kBAAkB,IAAIK,aAAA,CAAc1R,IAAA;QACrDmR,sBAAA,CAAuBE,kBAAkB,IAAIG,WAAA,CAAYhC,UAAA,KAAe;MACzE;IACF;IAED,OAAOxU,MAAA,CAAOsK,aAAA,CAAc,cAAc0L,eAAe,EAAEtL,IAAA,CAAK,UAAUkG,UAAA,EAAY;MACpF,OAAO,IAAI5K,OAAA,CAAQ,UAAUC,OAAA,EAASC,MAAA,EAAQ;QAC5CvG,WAAA,CAAYiX,eAAA,CACVhG,UAAA,EACA,UAAU+C,QAAA,EAAU;UAClB,WAAWQ,aAAA,IAAiBR,QAAA,CAASjB,UAAA,EAAY;YAC/C,MAAMmE,SAAA,GAAYlD,QAAA,CAASjB,UAAA,CAAWyB,aAAa;YACnD,MAAMK,UAAA,GAAa2B,sBAAA,CAAuBhC,aAAa;YAEvD,IAAIK,UAAA,KAAe,QAAWqC,SAAA,CAAUrC,UAAA,GAAaA,UAAA;UACtD;UAEDvO,OAAA,CAAQ0N,QAAQ;QACjB,GACDuC,iBAAA,EACAE,gBAAA,EACAhX,oBAAA,EACA8G,MACD;MACT,CAAO;IACP,CAAK;EACF;AACH;AAOA,MAAMT,6BAAA,CAA8B;EAClChG,YAAA,EAAc;IACZ,KAAKuF,IAAA,GAAOZ,UAAA,CAAWoB,qBAAA;EACxB;EAEDsR,cAAcC,OAAA,EAASC,SAAA,EAAW;IAChC,KACGA,SAAA,CAAUC,QAAA,KAAa,UAAaD,SAAA,CAAUC,QAAA,KAAaF,OAAA,CAAQG,OAAA,KACpEF,SAAA,CAAUG,MAAA,KAAW,UACrBH,SAAA,CAAUI,QAAA,KAAa,UACvBJ,SAAA,CAAUtK,KAAA,KAAU,QACpB;MAEA,OAAOqK,OAAA;IACR;IAEDA,OAAA,GAAUA,OAAA,CAAQM,KAAA,CAAO;IAEzB,IAAIL,SAAA,CAAUC,QAAA,KAAa,QAAW;MACpCF,OAAA,CAAQG,OAAA,GAAUF,SAAA,CAAUC,QAAA;IAC7B;IAED,IAAID,SAAA,CAAUG,MAAA,KAAW,QAAW;MAClCJ,OAAA,CAAQI,MAAA,CAAOG,SAAA,CAAUN,SAAA,CAAUG,MAAM;IAC1C;IAED,IAAIH,SAAA,CAAUI,QAAA,KAAa,QAAW;MACpCL,OAAA,CAAQK,QAAA,GAAWJ,SAAA,CAAUI,QAAA;IAC9B;IAED,IAAIJ,SAAA,CAAUtK,KAAA,KAAU,QAAW;MACjCqK,OAAA,CAAQQ,MAAA,CAAOD,SAAA,CAAUN,SAAA,CAAUtK,KAAK;IACzC;IAEDqK,OAAA,CAAQS,WAAA,GAAc;IAEtB,OAAOT,OAAA;EACR;AACH;AAOA,MAAMpR,6BAAA,CAA8B;EAClClG,YAAA,EAAc;IACZ,KAAKuF,IAAA,GAAOZ,UAAA,CAAWsB,qBAAA;EACxB;AACH;AAQA,MAAM+R,0BAAA,SAAmCC,WAAA,CAAY;EACnDjY,YAAYkY,kBAAA,EAAoBC,YAAA,EAAcC,UAAA,EAAYC,YAAA,EAAc;IACtE,MAAMH,kBAAA,EAAoBC,YAAA,EAAcC,UAAA,EAAYC,YAAY;EACjE;EAEDC,iBAAiBxN,KAAA,EAAO;IAItB,MAAMsH,MAAA,GAAS,KAAKiG,YAAA;MAClBE,MAAA,GAAS,KAAKJ,YAAA;MACdK,SAAA,GAAY,KAAKA,SAAA;MACjBd,MAAA,GAAS5M,KAAA,GAAQ0N,SAAA,GAAY,IAAIA,SAAA;IAEnC,SAASpT,CAAA,GAAI,GAAGA,CAAA,KAAMoT,SAAA,EAAWpT,CAAA,IAAK;MACpCgN,MAAA,CAAOhN,CAAC,IAAImT,MAAA,CAAOb,MAAA,GAAStS,CAAC;IAC9B;IAED,OAAOgN,MAAA;EACR;EAEDqG,aAAaC,EAAA,EAAIC,EAAA,EAAIC,CAAA,EAAGC,EAAA,EAAI;IAC1B,MAAMzG,MAAA,GAAS,KAAKiG,YAAA;IACpB,MAAME,MAAA,GAAS,KAAKJ,YAAA;IACpB,MAAMtG,MAAA,GAAS,KAAK2G,SAAA;IAEpB,MAAMM,OAAA,GAAUjH,MAAA,GAAS;IACzB,MAAMkH,OAAA,GAAUlH,MAAA,GAAS;IAEzB,MAAMmH,EAAA,GAAKH,EAAA,GAAKF,EAAA;IAEhB,MAAMhF,CAAA,IAAKiF,CAAA,GAAID,EAAA,IAAMK,EAAA;IACrB,MAAMC,EAAA,GAAKtF,CAAA,GAAIA,CAAA;IACf,MAAMuF,GAAA,GAAMD,EAAA,GAAKtF,CAAA;IAEjB,MAAMwF,OAAA,GAAUT,EAAA,GAAKK,OAAA;IACrB,MAAMK,OAAA,GAAUD,OAAA,GAAUJ,OAAA;IAE1B,MAAMM,EAAA,GAAK,KAAKH,GAAA,GAAM,IAAID,EAAA;IAC1B,MAAMK,EAAA,GAAKJ,GAAA,GAAMD,EAAA;IACjB,MAAMM,EAAA,GAAK,IAAIF,EAAA;IACf,MAAMG,EAAA,GAAKF,EAAA,GAAKL,EAAA,GAAKtF,CAAA;IAIrB,SAASvO,CAAA,GAAI,GAAGA,CAAA,KAAMyM,MAAA,EAAQzM,CAAA,IAAK;MACjC,MAAMqU,EAAA,GAAKlB,MAAA,CAAOa,OAAA,GAAUhU,CAAA,GAAIyM,MAAM;MACtC,MAAM6H,EAAA,GAAKnB,MAAA,CAAOa,OAAA,GAAUhU,CAAA,GAAI0T,OAAO,IAAIE,EAAA;MAC3C,MAAMW,EAAA,GAAKpB,MAAA,CAAOY,OAAA,GAAU/T,CAAA,GAAIyM,MAAM;MACtC,MAAM+H,EAAA,GAAKrB,MAAA,CAAOY,OAAA,GAAU/T,CAAC,IAAI4T,EAAA;MAEjC5G,MAAA,CAAOhN,CAAC,IAAImU,EAAA,GAAKE,EAAA,GAAKD,EAAA,GAAKE,EAAA,GAAKL,EAAA,GAAKM,EAAA,GAAKL,EAAA,GAAKM,EAAA;IAChD;IAED,OAAOxH,MAAA;EACR;AACH;AAEA,MAAMyH,EAAA,GAAqB,mBAAI/F,UAAA,CAAY;AAE3C,MAAMgG,oCAAA,SAA6C9B,0BAAA,CAA2B;EAC5ES,aAAaC,EAAA,EAAIC,EAAA,EAAIC,CAAA,EAAGC,EAAA,EAAI;IAC1B,MAAMzG,MAAA,GAAS,MAAMqG,YAAA,CAAaC,EAAA,EAAIC,EAAA,EAAIC,CAAA,EAAGC,EAAE;IAE/CgB,EAAA,CAAGhC,SAAA,CAAUzF,MAAM,EAAE2H,SAAA,CAAW,EAACC,OAAA,CAAQ5H,MAAM;IAE/C,OAAOA,MAAA;EACR;AACH;AAQA,MAAMQ,eAAA,GAAkB;EACtBqH,KAAA,EAAO;EAAA;EAEPC,UAAA,EAAY;EACZC,UAAA,EAAY;EACZC,UAAA,EAAY;EACZC,UAAA,EAAY;EACZC,UAAA,EAAY;EACZC,MAAA,EAAQ;EACRC,MAAA,EAAQ;EACRC,UAAA,EAAY;EACZC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,SAAA,EAAW;EACXC,UAAA,EAAY;EACZhI,SAAA,EAAW;EACXC,cAAA,EAAgB;EAChBC,YAAA,EAAc;EACd+H,aAAA,EAAe;EACfC,cAAA,EAAgB;AAClB;AAEA,MAAM7D,qBAAA,GAAwB;EAC5B,MAAM8D,SAAA;EACN,MAAMxW,UAAA;EACN,MAAMyW,UAAA;EACN,MAAMC,WAAA;EACN,MAAMC,WAAA;EACN,MAAMC;AACR;AAEA,MAAMC,aAAA,GAAgB;EACpB,MAAMC,aAAA;EACN,MAAMC,YAAA;EACN,MAAMC,0BAAA;EACN,MAAMC,yBAAA;EACN,MAAMC,yBAAA;EACN,MAAMC;AACR;AAEA,MAAMC,eAAA,GAAkB;EACtB,OAAOC,mBAAA;EACP,OAAOC,sBAAA;EACP,OAAOC;AACT;AAEA,MAAMC,gBAAA,GAAmB;EACvBC,MAAA,EAAQ;EACRC,IAAA,EAAM;EACNC,IAAA,EAAM;EACNC,IAAA,EAAM;EACNC,IAAA,EAAM;EACNC,IAAA,EAAM;EACNC,IAAA,EAAM;AACR;AAEA,MAAM1F,UAAA,GAAa;EACjB2F,QAAA,EAAU;EACVC,MAAA,EAAQ;EACRC,OAAA,EAAS;EAAA;EAAA;EAAA;EAIT,IAAI1X,OAAA,IAAW,MACX;IACE2X,UAAA,EAAY;IACZC,UAAA,EAAY;IACZC,UAAA,EAAY;IACZC,UAAA,EAAY;EACb,IACD;IACEH,UAAA,EAAY;IACZC,UAAA,EAAY;EACpB;EAEEG,OAAA,EAAS;EACTC,SAAA,EAAW;EACXC,QAAA,EAAU;AACZ;AAEA,MAAMC,eAAA,GAAkB;EACtBjQ,KAAA,EAAO;EACPkQ,WAAA,EAAa;EACbxF,QAAA,EAAU;EACVyF,OAAA,EAAS;AACX;AAEA,MAAMC,aAAA,GAAgB;EACpBC,WAAA,EAAa;EAAA;EAAA;EAEb/C,MAAA,EAAQgD,iBAAA;EACRC,IAAA,EAAMC;AACR;AAEA,MAAMC,WAAA,GAAc;EAClBC,MAAA,EAAQ;EACRC,IAAA,EAAM;EACNC,KAAA,EAAO;AACT;AAKA,SAASC,sBAAsB3V,KAAA,EAAO;EACpC,IAAIA,KAAA,CAAM,iBAAiB,MAAM,QAAW;IAC1CA,KAAA,CAAM,iBAAiB,IAAI,IAAI4V,oBAAA,CAAqB;MAClDzU,KAAA,EAAO;MACP0U,QAAA,EAAU;MACVC,SAAA,EAAW;MACXC,SAAA,EAAW;MACXC,WAAA,EAAa;MACbC,SAAA,EAAW;MACXC,IAAA,EAAMC;IACZ,CAAK;EACF;EAED,OAAOnW,KAAA,CAAM,iBAAiB;AAChC;AAEA,SAASoW,+BAA+BC,eAAA,EAAiBzX,MAAA,EAAQ0X,SAAA,EAAW;EAG1E,WAAWlZ,IAAA,IAAQkZ,SAAA,CAAUva,UAAA,EAAY;IACvC,IAAIsa,eAAA,CAAgBjZ,IAAI,MAAM,QAAW;MACvCwB,MAAA,CAAO2X,QAAA,CAASC,cAAA,GAAiB5X,MAAA,CAAO2X,QAAA,CAASC,cAAA,IAAkB,CAAE;MACrE5X,MAAA,CAAO2X,QAAA,CAASC,cAAA,CAAepZ,IAAI,IAAIkZ,SAAA,CAAUva,UAAA,CAAWqB,IAAI;IACjE;EACF;AACH;AAMA,SAASmF,uBAAuB3D,MAAA,EAAQ6X,OAAA,EAAS;EAC/C,IAAIA,OAAA,CAAQC,MAAA,KAAW,QAAW;IAChC,IAAI,OAAOD,OAAA,CAAQC,MAAA,KAAW,UAAU;MACtCC,MAAA,CAAOC,MAAA,CAAOhY,MAAA,CAAO2X,QAAA,EAAUE,OAAA,CAAQC,MAAM;IACnD,OAAW;MACLrc,OAAA,CAAQ2D,IAAA,CAAK,wDAAwDyY,OAAA,CAAQC,MAAM;IACpF;EACF;AACH;AAUA,SAASG,gBAAgB9K,QAAA,EAAU+K,OAAA,EAAS1e,MAAA,EAAQ;EAClD,IAAI2e,gBAAA,GAAmB;EACvB,IAAIC,cAAA,GAAiB;EACrB,IAAIC,aAAA,GAAgB;EAEpB,SAASha,CAAA,GAAI,GAAGia,EAAA,GAAKJ,OAAA,CAAQ5Z,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;IAChD,MAAMwE,MAAA,GAASqV,OAAA,CAAQ7Z,CAAC;IAExB,IAAIwE,MAAA,CAAO4S,QAAA,KAAa,QAAW0C,gBAAA,GAAmB;IACtD,IAAItV,MAAA,CAAO6S,MAAA,KAAW,QAAW0C,cAAA,GAAiB;IAClD,IAAIvV,MAAA,CAAOmT,OAAA,KAAY,QAAWqC,aAAA,GAAgB;IAElD,IAAIF,gBAAA,IAAoBC,cAAA,IAAkBC,aAAA,EAAe;EAC1D;EAED,IAAI,CAACF,gBAAA,IAAoB,CAACC,cAAA,IAAkB,CAACC,aAAA,EAAe,OAAO7Y,OAAA,CAAQC,OAAA,CAAQ0N,QAAQ;EAE3F,MAAMoL,wBAAA,GAA2B,EAAE;EACnC,MAAMC,sBAAA,GAAyB,EAAE;EACjC,MAAMC,qBAAA,GAAwB,EAAE;EAEhC,SAASpa,CAAA,GAAI,GAAGia,EAAA,GAAKJ,OAAA,CAAQ5Z,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;IAChD,MAAMwE,MAAA,GAASqV,OAAA,CAAQ7Z,CAAC;IAExB,IAAI8Z,gBAAA,EAAkB;MACpB,MAAMO,eAAA,GACJ7V,MAAA,CAAO4S,QAAA,KAAa,SAAYjc,MAAA,CAAOsK,aAAA,CAAc,YAAYjB,MAAA,CAAO4S,QAAQ,IAAItI,QAAA,CAASjB,UAAA,CAAWpJ,QAAA;MAE1GyV,wBAAA,CAAyBxb,IAAA,CAAK2b,eAAe;IAC9C;IAED,IAAIN,cAAA,EAAgB;MAClB,MAAMM,eAAA,GACJ7V,MAAA,CAAO6S,MAAA,KAAW,SAAYlc,MAAA,CAAOsK,aAAA,CAAc,YAAYjB,MAAA,CAAO6S,MAAM,IAAIvI,QAAA,CAASjB,UAAA,CAAWyM,MAAA;MAEtGH,sBAAA,CAAuBzb,IAAA,CAAK2b,eAAe;IAC5C;IAED,IAAIL,aAAA,EAAe;MACjB,MAAMK,eAAA,GACJ7V,MAAA,CAAOmT,OAAA,KAAY,SAAYxc,MAAA,CAAOsK,aAAA,CAAc,YAAYjB,MAAA,CAAOmT,OAAO,IAAI7I,QAAA,CAASjB,UAAA,CAAW3J,KAAA;MAExGkW,qBAAA,CAAsB1b,IAAA,CAAK2b,eAAe;IAC3C;EACF;EAED,OAAOlZ,OAAA,CAAQ2F,GAAA,CAAI,CACjB3F,OAAA,CAAQ2F,GAAA,CAAIoT,wBAAwB,GACpC/Y,OAAA,CAAQ2F,GAAA,CAAIqT,sBAAsB,GAClChZ,OAAA,CAAQ2F,GAAA,CAAIsT,qBAAqB,EAClC,EAAEvU,IAAA,CAAK,UAAU+L,SAAA,EAAW;IAC3B,MAAM2I,cAAA,GAAiB3I,SAAA,CAAU,CAAC;IAClC,MAAM4I,YAAA,GAAe5I,SAAA,CAAU,CAAC;IAChC,MAAM6I,WAAA,GAAc7I,SAAA,CAAU,CAAC;IAE/B,IAAIkI,gBAAA,EAAkBhL,QAAA,CAAS4L,eAAA,CAAgBjW,QAAA,GAAW8V,cAAA;IAC1D,IAAIR,cAAA,EAAgBjL,QAAA,CAAS4L,eAAA,CAAgBJ,MAAA,GAASE,YAAA;IACtD,IAAIR,aAAA,EAAelL,QAAA,CAAS4L,eAAA,CAAgBxW,KAAA,GAAQuW,WAAA;IACpD3L,QAAA,CAAS6L,oBAAA,GAAuB;IAEhC,OAAO7L,QAAA;EACX,CAAG;AACH;AAMA,SAAS8L,mBAAmBzN,IAAA,EAAMC,OAAA,EAAS;EACzCD,IAAA,CAAKyN,kBAAA,CAAoB;EAEzB,IAAIxN,OAAA,CAAQ4K,OAAA,KAAY,QAAW;IACjC,SAAShY,CAAA,GAAI,GAAGia,EAAA,GAAK7M,OAAA,CAAQ4K,OAAA,CAAQ/X,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MACxDmN,IAAA,CAAK0N,qBAAA,CAAsB7a,CAAC,IAAIoN,OAAA,CAAQ4K,OAAA,CAAQhY,CAAC;IAClD;EACF;EAGD,IAAIoN,OAAA,CAAQqM,MAAA,IAAUjT,KAAA,CAAMC,OAAA,CAAQ2G,OAAA,CAAQqM,MAAA,CAAOqB,WAAW,GAAG;IAC/D,MAAMA,WAAA,GAAc1N,OAAA,CAAQqM,MAAA,CAAOqB,WAAA;IAEnC,IAAI3N,IAAA,CAAK0N,qBAAA,CAAsB5a,MAAA,KAAW6a,WAAA,CAAY7a,MAAA,EAAQ;MAC5DkN,IAAA,CAAK4N,qBAAA,GAAwB,CAAE;MAE/B,SAAS/a,CAAA,GAAI,GAAGia,EAAA,GAAKa,WAAA,CAAY7a,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QACpDmN,IAAA,CAAK4N,qBAAA,CAAsBD,WAAA,CAAY9a,CAAC,CAAC,IAAIA,CAAA;MAC9C;IACP,OAAW;MACL5C,OAAA,CAAQ2D,IAAA,CAAK,sEAAsE;IACpF;EACF;AACH;AAEA,SAASia,mBAAmBC,YAAA,EAAc;EACxC,IAAIC,WAAA;EAEJ,MAAMC,cAAA,GAAiBF,YAAA,CAAanc,UAAA,IAAcmc,YAAA,CAAanc,UAAA,CAAWS,UAAA,CAAWkB,0BAA0B;EAE/G,IAAI0a,cAAA,EAAgB;IAClBD,WAAA,GACE,WACAC,cAAA,CAAepP,UAAA,GACf,MACAoP,cAAA,CAAeC,OAAA,GACf,MACAC,mBAAA,CAAoBF,cAAA,CAAetN,UAAU;EACnD,OAAS;IACLqN,WAAA,GAAcD,YAAA,CAAaG,OAAA,GAAU,MAAMC,mBAAA,CAAoBJ,YAAA,CAAapN,UAAU,IAAI,MAAMoN,YAAA,CAAarO,IAAA;EAC9G;EAED,IAAIqO,YAAA,CAAapB,OAAA,KAAY,QAAW;IACtC,SAAS7Z,CAAA,GAAI,GAAGia,EAAA,GAAKgB,YAAA,CAAapB,OAAA,CAAQ5Z,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MAC7Dkb,WAAA,IAAe,MAAMG,mBAAA,CAAoBJ,YAAA,CAAapB,OAAA,CAAQ7Z,CAAC,CAAC;IACjE;EACF;EAED,OAAOkb,WAAA;AACT;AAEA,SAASG,oBAAoBxN,UAAA,EAAY;EACvC,IAAIyN,aAAA,GAAgB;EAEpB,MAAMC,IAAA,GAAO7B,MAAA,CAAO6B,IAAA,CAAK1N,UAAU,EAAE2N,IAAA,CAAM;EAE3C,SAASxb,CAAA,GAAI,GAAGia,EAAA,GAAKsB,IAAA,CAAKtb,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;IAC7Csb,aAAA,IAAiBC,IAAA,CAAKvb,CAAC,IAAI,MAAM6N,UAAA,CAAW0N,IAAA,CAAKvb,CAAC,CAAC,IAAI;EACxD;EAED,OAAOsb,aAAA;AACT;AAEA,SAASG,4BAA4B7gB,WAAA,EAAa;EAIhD,QAAQA,WAAA;IACN,KAAKgb,SAAA;MACH,OAAO,IAAI;IAEb,KAAKxW,UAAA;MACH,OAAO,IAAI;IAEb,KAAKyW,UAAA;MACH,OAAO,IAAI;IAEb,KAAKC,WAAA;MACH,OAAO,IAAI;IAEb;MACE,MAAM,IAAIzX,KAAA,CAAM,mEAAmE;EACtF;AACH;AAEA,SAASqd,oBAAoBtQ,GAAA,EAAK;EAChC,IAAIA,GAAA,CAAIuQ,MAAA,CAAO,gBAAgB,IAAI,KAAKvQ,GAAA,CAAIuQ,MAAA,CAAO,oBAAoB,MAAM,GAAG,OAAO;EACvF,IAAIvQ,GAAA,CAAIuQ,MAAA,CAAO,eAAe,IAAI,KAAKvQ,GAAA,CAAIuQ,MAAA,CAAO,oBAAoB,MAAM,GAAG,OAAO;EAEtF,OAAO;AACT;AAEA,MAAMC,eAAA,GAAkC,mBAAItN,OAAA,CAAS;AAIrD,MAAMzO,UAAA,CAAW;EACfjF,YAAYiE,IAAA,GAAO,IAAIiM,OAAA,GAAU,IAAI;IACnC,KAAKjM,IAAA,GAAOA,IAAA;IACZ,KAAKC,UAAA,GAAa,CAAE;IACpB,KAAKC,OAAA,GAAU,CAAE;IACjB,KAAK+L,OAAA,GAAUA,OAAA;IAGf,KAAK/H,KAAA,GAAQ,IAAIzB,YAAA,CAAc;IAG/B,KAAKua,YAAA,GAAe,mBAAIC,GAAA,CAAK;IAG7B,KAAKC,cAAA,GAAiB,CAAE;IAGxB,KAAKC,SAAA,GAAY,CAAE;IAGnB,KAAKC,SAAA,GAAY;MAAEjZ,IAAA,EAAM;MAAIC,IAAA,EAAM;IAAI;IACvC,KAAKiZ,WAAA,GAAc;MAAElZ,IAAA,EAAM;MAAIC,IAAA,EAAM;IAAI;IACzC,KAAKkZ,UAAA,GAAa;MAAEnZ,IAAA,EAAM;MAAIC,IAAA,EAAM;IAAI;IAExC,KAAKmZ,WAAA,GAAc,CAAE;IACrB,KAAKC,YAAA,GAAe,CAAE;IAGtB,KAAKC,aAAA,GAAgB,CAAE;IAKvB,IAAIC,QAAA,GAAW;IACf,IAAIC,SAAA,GAAY;IAChB,IAAIC,cAAA,GAAiB;IAErB,IAAI,OAAOC,SAAA,KAAc,eAAe,OAAOA,SAAA,CAAUC,SAAA,KAAc,aAAa;MAClFJ,QAAA,GAAW,iCAAiCK,IAAA,CAAKF,SAAA,CAAUC,SAAS,MAAM;MAC1EH,SAAA,GAAYE,SAAA,CAAUC,SAAA,CAAUle,OAAA,CAAQ,SAAS,IAAI;MACrDge,cAAA,GAAiBD,SAAA,GAAYE,SAAA,CAAUC,SAAA,CAAUE,KAAA,CAAM,qBAAqB,EAAE,CAAC,IAAI;IACpF;IAED,IAAI,OAAOC,iBAAA,KAAsB,eAAeP,QAAA,IAAaC,SAAA,IAAaC,cAAA,GAAiB,IAAK;MAC9F,KAAKtR,aAAA,GAAgB,IAAI4R,aAAA,CAAc,KAAKjS,OAAA,CAAQjQ,OAAO;IACjE,OAAW;MACL,KAAKsQ,aAAA,GAAgB,IAAI6R,iBAAA,CAAkB,KAAKlS,OAAA,CAAQjQ,OAAO;IAChE;IAED,KAAKsQ,aAAA,CAAc8R,cAAA,CAAe,KAAKnS,OAAA,CAAQhL,WAAW;IAC1D,KAAKqL,aAAA,CAAcvN,gBAAA,CAAiB,KAAKkN,OAAA,CAAQjN,aAAa;IAE9D,KAAKkC,UAAA,GAAa,IAAItC,UAAA,CAAW,KAAKqN,OAAA,CAAQjQ,OAAO;IACrD,KAAKkF,UAAA,CAAWpC,eAAA,CAAgB,aAAa;IAE7C,IAAI,KAAKmN,OAAA,CAAQhL,WAAA,KAAgB,mBAAmB;MAClD,KAAKC,UAAA,CAAWjC,kBAAA,CAAmB,IAAI;IACxC;EACF;EAEDkD,cAAclC,UAAA,EAAY;IACxB,KAAKA,UAAA,GAAaA,UAAA;EACnB;EAEDmC,WAAWlC,OAAA,EAAS;IAClB,KAAKA,OAAA,GAAUA,OAAA;EAChB;EAEDd,MAAM1B,MAAA,EAAQE,OAAA,EAAS;IACrB,MAAMtB,MAAA,GAAS;IACf,MAAM0D,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMC,UAAA,GAAa,KAAKA,UAAA;IAGxB,KAAKiE,KAAA,CAAMlB,SAAA,CAAW;IACtB,KAAKma,SAAA,GAAY,CAAE;IAGnB,KAAKkB,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7B,OAAOA,GAAA,CAAIja,SAAA,IAAaia,GAAA,CAAIja,SAAA,CAAW;IAC7C,CAAK;IAED/B,OAAA,CAAQ2F,GAAA,CACN,KAAKoW,UAAA,CAAW,UAAUC,GAAA,EAAK;MAC7B,OAAOA,GAAA,CAAIC,UAAA,IAAcD,GAAA,CAAIC,UAAA,CAAY;IACjD,CAAO,CACF,EACEvX,IAAA,CAAK,YAAY;MAChB,OAAO1E,OAAA,CAAQ2F,GAAA,CAAI,CACjB3L,MAAA,CAAOkiB,eAAA,CAAgB,OAAO,GAC9BliB,MAAA,CAAOkiB,eAAA,CAAgB,WAAW,GAClCliB,MAAA,CAAOkiB,eAAA,CAAgB,QAAQ,EAChC;IACT,CAAO,EACAxX,IAAA,CAAK,UAAUyX,YAAA,EAAc;MAC5B,MAAMtQ,MAAA,GAAS;QACbuQ,KAAA,EAAOD,YAAA,CAAa,CAAC,EAAEze,IAAA,CAAK0e,KAAA,IAAS,CAAC;QACtCC,MAAA,EAAQF,YAAA,CAAa,CAAC;QACtBG,UAAA,EAAYH,YAAA,CAAa,CAAC;QAC1BI,OAAA,EAASJ,YAAA,CAAa,CAAC;QACvB3d,KAAA,EAAOd,IAAA,CAAKc,KAAA;QACZxE,MAAA;QACAme,QAAA,EAAU,CAAE;MACb;MAEDH,8BAAA,CAA+Bra,UAAA,EAAYkO,MAAA,EAAQnO,IAAI;MAEvDyG,sBAAA,CAAuB0H,MAAA,EAAQnO,IAAI;MAEnC,OAAOsC,OAAA,CAAQ2F,GAAA,CACb3L,MAAA,CAAO+hB,UAAA,CAAW,UAAUC,GAAA,EAAK;QAC/B,OAAOA,GAAA,CAAIQ,SAAA,IAAaR,GAAA,CAAIQ,SAAA,CAAU3Q,MAAM;MACxD,CAAW,CACF,EAACnH,IAAA,CAAK,YAAY;QACjB,WAAW0X,KAAA,IAASvQ,MAAA,CAAOwQ,MAAA,EAAQ;UACjCD,KAAA,CAAMK,iBAAA,CAAmB;QAC1B;QAEDrhB,MAAA,CAAOyQ,MAAM;MACvB,CAAS;IACT,CAAO,EACA6Q,KAAA,CAAMphB,OAAO;EACjB;EAAA;AAAA;AAAA;EAKDyG,UAAA,EAAY;IACV,MAAMC,QAAA,GAAW,KAAKtE,IAAA,CAAKuE,KAAA,IAAS,EAAE;IACtC,MAAM0a,QAAA,GAAW,KAAKjf,IAAA,CAAKkf,KAAA,IAAS,EAAE;IACtC,MAAMC,QAAA,GAAW,KAAKnf,IAAA,CAAKwO,MAAA,IAAU,EAAE;IAIvC,SAAS4Q,SAAA,GAAY,GAAGC,UAAA,GAAaJ,QAAA,CAAS7d,MAAA,EAAQge,SAAA,GAAYC,UAAA,EAAYD,SAAA,IAAa;MACzF,MAAME,MAAA,GAASL,QAAA,CAASG,SAAS,EAAEE,MAAA;MAEnC,SAASne,CAAA,GAAI,GAAGia,EAAA,GAAKkE,MAAA,CAAOle,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QAC/CmD,QAAA,CAASgb,MAAA,CAAOne,CAAC,CAAC,EAAEoe,MAAA,GAAS;MAC9B;IACF;IAID,SAAS/a,SAAA,GAAY,GAAGC,UAAA,GAAaH,QAAA,CAASlD,MAAA,EAAQoD,SAAA,GAAYC,UAAA,EAAYD,SAAA,IAAa;MACzF,MAAME,OAAA,GAAUJ,QAAA,CAASE,SAAS;MAElC,IAAIE,OAAA,CAAQ4J,IAAA,KAAS,QAAW;QAC9B,KAAK1J,WAAA,CAAY,KAAKwY,SAAA,EAAW1Y,OAAA,CAAQ4J,IAAI;QAK7C,IAAI5J,OAAA,CAAQ8a,IAAA,KAAS,QAAW;UAC9BL,QAAA,CAASza,OAAA,CAAQ4J,IAAI,EAAEmR,aAAA,GAAgB;QACxC;MACF;MAED,IAAI/a,OAAA,CAAQgb,MAAA,KAAW,QAAW;QAChC,KAAK9a,WAAA,CAAY,KAAKyY,WAAA,EAAa3Y,OAAA,CAAQgb,MAAM;MAClD;IACF;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWD9a,YAAYV,KAAA,EAAO2C,KAAA,EAAO;IACxB,IAAIA,KAAA,KAAU,QAAW;IAEzB,IAAI3C,KAAA,CAAMC,IAAA,CAAK0C,KAAK,MAAM,QAAW;MACnC3C,KAAA,CAAMC,IAAA,CAAK0C,KAAK,IAAI3C,KAAA,CAAME,IAAA,CAAKyC,KAAK,IAAI;IACzC;IAED3C,KAAA,CAAMC,IAAA,CAAK0C,KAAK;EACjB;EAAA;EAGDI,YAAY/C,KAAA,EAAO2C,KAAA,EAAO/D,MAAA,EAAQ;IAChC,IAAIoB,KAAA,CAAMC,IAAA,CAAK0C,KAAK,KAAK,GAAG,OAAO/D,MAAA;IAEnC,MAAM6c,GAAA,GAAM7c,MAAA,CAAO6Q,KAAA,CAAO;IAI1B,MAAMiM,cAAA,GAAiBA,CAACC,QAAA,EAAUlM,KAAA,KAAU;MAC1C,MAAMmM,QAAA,GAAW,KAAK9C,YAAA,CAAara,GAAA,CAAIkd,QAAQ;MAC/C,IAAIC,QAAA,IAAY,MAAM;QACpB,KAAK9C,YAAA,CAAanX,GAAA,CAAI8N,KAAA,EAAOmM,QAAQ;MACtC;MAED,WAAW,CAAC3e,CAAA,EAAG4e,KAAK,KAAKF,QAAA,CAASvQ,QAAA,CAAS0Q,OAAA,IAAW;QACpDJ,cAAA,CAAeG,KAAA,EAAOpM,KAAA,CAAMrE,QAAA,CAASnO,CAAC,CAAC;MACxC;IACF;IAEDye,cAAA,CAAe9c,MAAA,EAAQ6c,GAAG;IAE1BA,GAAA,CAAIre,IAAA,IAAQ,eAAe4C,KAAA,CAAME,IAAA,CAAKyC,KAAK;IAE3C,OAAO8Y,GAAA;EACR;EAEDM,WAAWC,IAAA,EAAM;IACf,MAAMjgB,UAAA,GAAa4a,MAAA,CAAOvG,MAAA,CAAO,KAAKpU,OAAO;IAC7CD,UAAA,CAAWJ,IAAA,CAAK,IAAI;IAEpB,SAASsB,CAAA,GAAI,GAAGA,CAAA,GAAIlB,UAAA,CAAWmB,MAAA,EAAQD,CAAA,IAAK;MAC1C,MAAMgN,MAAA,GAAS+R,IAAA,CAAKjgB,UAAA,CAAWkB,CAAC,CAAC;MAEjC,IAAIgN,MAAA,EAAQ,OAAOA,MAAA;IACpB;IAED,OAAO;EACR;EAEDkQ,WAAW6B,IAAA,EAAM;IACf,MAAMjgB,UAAA,GAAa4a,MAAA,CAAOvG,MAAA,CAAO,KAAKpU,OAAO;IAC7CD,UAAA,CAAWkgB,OAAA,CAAQ,IAAI;IAEvB,MAAM5Y,OAAA,GAAU,EAAE;IAElB,SAASpG,CAAA,GAAI,GAAGA,CAAA,GAAIlB,UAAA,CAAWmB,MAAA,EAAQD,CAAA,IAAK;MAC1C,MAAMgN,MAAA,GAAS+R,IAAA,CAAKjgB,UAAA,CAAWkB,CAAC,CAAC;MAEjC,IAAIgN,MAAA,EAAQ5G,OAAA,CAAQ1H,IAAA,CAAKsO,MAAM;IAChC;IAED,OAAO5G,OAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDX,cAAcnB,IAAA,EAAMoB,KAAA,EAAO;IACzB,MAAM9B,QAAA,GAAWU,IAAA,GAAO,MAAMoB,KAAA;IAC9B,IAAI7B,UAAA,GAAa,KAAKd,KAAA,CAAMvB,GAAA,CAAIoC,QAAQ;IAExC,IAAI,CAACC,UAAA,EAAY;MACf,QAAQS,IAAA;QACN,KAAK;UACHT,UAAA,GAAa,KAAKob,SAAA,CAAUvZ,KAAK;UACjC;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKib,UAAA,CAAW,UAAU3B,GAAA,EAAK;YAC1C,OAAOA,GAAA,CAAI+B,QAAA,IAAY/B,GAAA,CAAI+B,QAAA,CAASxZ,KAAK;UACrD,CAAW;UACD;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKib,UAAA,CAAW,UAAU3B,GAAA,EAAK;YAC1C,OAAOA,GAAA,CAAIgC,QAAA,IAAYhC,GAAA,CAAIgC,QAAA,CAASzZ,KAAK;UACrD,CAAW;UACD;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKub,YAAA,CAAa1Z,KAAK;UACpC;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKib,UAAA,CAAW,UAAU3B,GAAA,EAAK;YAC1C,OAAOA,GAAA,CAAIrR,cAAA,IAAkBqR,GAAA,CAAIrR,cAAA,CAAepG,KAAK;UACjE,CAAW;UACD;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKwb,UAAA,CAAW3Z,KAAK;UAClC;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKib,UAAA,CAAW,UAAU3B,GAAA,EAAK;YAC1C,OAAOA,GAAA,CAAImC,YAAA,IAAgBnC,GAAA,CAAImC,YAAA,CAAa5Z,KAAK;UAC7D,CAAW;UACD;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKib,UAAA,CAAW,UAAU3B,GAAA,EAAK;YAC1C,OAAOA,GAAA,CAAIzS,WAAA,IAAeyS,GAAA,CAAIzS,WAAA,CAAYhF,KAAK;UAC3D,CAAW;UACD;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAK0b,QAAA,CAAS7Z,KAAK;UAChC;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAKib,UAAA,CAAW,UAAU3B,GAAA,EAAK;YAC1C,OAAOA,GAAA,CAAIqC,aAAA,IAAiBrC,GAAA,CAAIqC,aAAA,CAAc9Z,KAAK;UAC/D,CAAW;UACD;QAEF,KAAK;UACH7B,UAAA,GAAa,KAAK4b,UAAA,CAAW/Z,KAAK;UAClC;QAEF;UACE7B,UAAA,GAAa,KAAKib,UAAA,CAAW,UAAU3B,GAAA,EAAK;YAC1C,OAAOA,GAAA,IAAO,QAAQA,GAAA,CAAI1X,aAAA,IAAiB0X,GAAA,CAAI1X,aAAA,CAAcnB,IAAA,EAAMoB,KAAK;UACpF,CAAW;UAED,IAAI,CAAC7B,UAAA,EAAY;YACf,MAAM,IAAIxF,KAAA,CAAM,mBAAmBiG,IAAI;UACxC;UAED;MACH;MAED,KAAKvB,KAAA,CAAMrB,GAAA,CAAIkC,QAAA,EAAUC,UAAU;IACpC;IAED,OAAOA,UAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAODwZ,gBAAgB/Y,IAAA,EAAM;IACpB,IAAIgZ,YAAA,GAAe,KAAKva,KAAA,CAAMvB,GAAA,CAAI8C,IAAI;IAEtC,IAAI,CAACgZ,YAAA,EAAc;MACjB,MAAMniB,MAAA,GAAS;MACf,MAAMukB,IAAA,GAAO,KAAK7gB,IAAA,CAAKyF,IAAA,IAAQA,IAAA,KAAS,SAAS,OAAO,IAAI,KAAK,EAAE;MAEnEgZ,YAAA,GAAenc,OAAA,CAAQ2F,GAAA,CACrB4Y,IAAA,CAAKC,GAAA,CAAI,UAAUC,GAAA,EAAKla,KAAA,EAAO;QAC7B,OAAOvK,MAAA,CAAOsK,aAAA,CAAcnB,IAAA,EAAMoB,KAAK;MACjD,CAAS,CACF;MAED,KAAK3C,KAAA,CAAMrB,GAAA,CAAI4C,IAAA,EAAMgZ,YAAY;IAClC;IAED,OAAOA,YAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD+B,WAAWQ,WAAA,EAAa;IACtB,MAAMC,SAAA,GAAY,KAAKjhB,IAAA,CAAKkhB,OAAA,CAAQF,WAAW;IAC/C,MAAMriB,MAAA,GAAS,KAAKuC,UAAA;IAEpB,IAAI+f,SAAA,CAAUxb,IAAA,IAAQwb,SAAA,CAAUxb,IAAA,KAAS,eAAe;MACtD,MAAM,IAAIjG,KAAA,CAAM,uBAAuByhB,SAAA,CAAUxb,IAAA,GAAO,gCAAgC;IACzF;IAGD,IAAIwb,SAAA,CAAU1U,GAAA,KAAQ,UAAayU,WAAA,KAAgB,GAAG;MACpD,OAAO1e,OAAA,CAAQC,OAAA,CAAQ,KAAKtC,UAAA,CAAWS,UAAA,CAAWC,eAAe,EAAE8Q,IAAI;IACxE;IAED,MAAMxF,OAAA,GAAU,KAAKA,OAAA;IAErB,OAAO,IAAI3J,OAAA,CAAQ,UAAUC,OAAA,EAASC,MAAA,EAAQ;MAC5C7D,MAAA,CAAOnB,IAAA,CAAKS,WAAA,CAAYE,UAAA,CAAW8iB,SAAA,CAAU1U,GAAA,EAAKN,OAAA,CAAQlO,IAAI,GAAGwE,OAAA,EAAS,QAAW,YAAY;QAC/FC,MAAA,CAAO,IAAIhD,KAAA,CAAM,8CAA8CyhB,SAAA,CAAU1U,GAAA,GAAM,IAAI,CAAC;MAC5F,CAAO;IACP,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAODU,eAAeqF,eAAA,EAAiB;IAC9B,MAAM6O,aAAA,GAAgB,KAAKnhB,IAAA,CAAKmN,WAAA,CAAYmF,eAAe;IAE3D,OAAO,KAAK1L,aAAA,CAAc,UAAUua,aAAA,CAAc9T,MAAM,EAAErG,IAAA,CAAK,UAAUqG,MAAA,EAAQ;MAC/E,MAAMK,UAAA,GAAayT,aAAA,CAAczT,UAAA,IAAc;MAC/C,MAAMD,UAAA,GAAa0T,aAAA,CAAc1T,UAAA,IAAc;MAC/C,OAAOJ,MAAA,CAAO7M,KAAA,CAAMiN,UAAA,EAAYA,UAAA,GAAaC,UAAU;IAC7D,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD6S,aAAaa,aAAA,EAAe;IAC1B,MAAM9kB,MAAA,GAAS;IACf,MAAM0D,IAAA,GAAO,KAAKA,IAAA;IAElB,MAAM8S,WAAA,GAAc,KAAK9S,IAAA,CAAK+S,SAAA,CAAUqO,aAAa;IAErD,IAAItO,WAAA,CAAY5F,UAAA,KAAe,UAAa4F,WAAA,CAAYuO,MAAA,KAAW,QAAW;MAC5E,MAAMxQ,QAAA,GAAWkH,gBAAA,CAAiBjF,WAAA,CAAYrN,IAAI;MAClD,MAAM6b,UAAA,GAAarO,qBAAA,CAAsBH,WAAA,CAAYE,aAAa;MAClE,MAAMlC,UAAA,GAAagC,WAAA,CAAYhC,UAAA,KAAe;MAE9C,MAAMhJ,KAAA,GAAQ,IAAIwZ,UAAA,CAAWxO,WAAA,CAAYnF,KAAA,GAAQkD,QAAQ;MACzD,OAAOvO,OAAA,CAAQC,OAAA,CAAQ,IAAIgf,eAAA,CAAgBzZ,KAAA,EAAO+I,QAAA,EAAUC,UAAU,CAAC;IACxE;IAED,MAAM0Q,kBAAA,GAAqB,EAAE;IAE7B,IAAI1O,WAAA,CAAY5F,UAAA,KAAe,QAAW;MACxCsU,kBAAA,CAAmB3hB,IAAA,CAAK,KAAK+G,aAAA,CAAc,cAAckM,WAAA,CAAY5F,UAAU,CAAC;IACtF,OAAW;MACLsU,kBAAA,CAAmB3hB,IAAA,CAAK,IAAI;IAC7B;IAED,IAAIiT,WAAA,CAAYuO,MAAA,KAAW,QAAW;MACpCG,kBAAA,CAAmB3hB,IAAA,CAAK,KAAK+G,aAAA,CAAc,cAAckM,WAAA,CAAYuO,MAAA,CAAO9E,OAAA,CAAQrP,UAAU,CAAC;MAC/FsU,kBAAA,CAAmB3hB,IAAA,CAAK,KAAK+G,aAAA,CAAc,cAAckM,WAAA,CAAYuO,MAAA,CAAO/M,MAAA,CAAOpH,UAAU,CAAC;IAC/F;IAED,OAAO5K,OAAA,CAAQ2F,GAAA,CAAIuZ,kBAAkB,EAAExa,IAAA,CAAK,UAAUmG,WAAA,EAAa;MACjE,MAAMD,UAAA,GAAaC,WAAA,CAAY,CAAC;MAEhC,MAAM0D,QAAA,GAAWkH,gBAAA,CAAiBjF,WAAA,CAAYrN,IAAI;MAClD,MAAM6b,UAAA,GAAarO,qBAAA,CAAsBH,WAAA,CAAYE,aAAa;MAGlE,MAAMyO,YAAA,GAAeH,UAAA,CAAWI,iBAAA;MAChC,MAAMC,SAAA,GAAYF,YAAA,GAAe5Q,QAAA;MACjC,MAAMpD,UAAA,GAAaqF,WAAA,CAAYrF,UAAA,IAAc;MAC7C,MAAMI,UAAA,GACJiF,WAAA,CAAY5F,UAAA,KAAe,SAAYlN,IAAA,CAAKmN,WAAA,CAAY2F,WAAA,CAAY5F,UAAU,EAAEW,UAAA,GAAa;MAC/F,MAAMiD,UAAA,GAAagC,WAAA,CAAYhC,UAAA,KAAe;MAC9C,IAAIhJ,KAAA,EAAO8Z,eAAA;MAGX,IAAI/T,UAAA,IAAcA,UAAA,KAAe8T,SAAA,EAAW;QAG1C,MAAME,OAAA,GAAUzb,IAAA,CAAK0b,KAAA,CAAMrU,UAAA,GAAaI,UAAU;QAClD,MAAMkU,UAAA,GACJ,uBACAjP,WAAA,CAAY5F,UAAA,GACZ,MACA4F,WAAA,CAAYE,aAAA,GACZ,MACA6O,OAAA,GACA,MACA/O,WAAA,CAAYnF,KAAA;QACd,IAAIqU,EAAA,GAAK1lB,MAAA,CAAO4H,KAAA,CAAMvB,GAAA,CAAIof,UAAU;QAEpC,IAAI,CAACC,EAAA,EAAI;UACPla,KAAA,GAAQ,IAAIwZ,UAAA,CAAWpU,UAAA,EAAY2U,OAAA,GAAUhU,UAAA,EAAaiF,WAAA,CAAYnF,KAAA,GAAQE,UAAA,GAAc4T,YAAY;UAGxGO,EAAA,GAAK,IAAIC,iBAAA,CAAkBna,KAAA,EAAO+F,UAAA,GAAa4T,YAAY;UAE3DnlB,MAAA,CAAO4H,KAAA,CAAMrB,GAAA,CAAIkf,UAAA,EAAYC,EAAE;QAChC;QAEDJ,eAAA,GAAkB,IAAIM,0BAAA,CACpBF,EAAA,EACAnR,QAAA,EACCpD,UAAA,GAAaI,UAAA,GAAc4T,YAAA,EAC5B3Q,UACD;MACT,OAAa;QACL,IAAI5D,UAAA,KAAe,MAAM;UACvBpF,KAAA,GAAQ,IAAIwZ,UAAA,CAAWxO,WAAA,CAAYnF,KAAA,GAAQkD,QAAQ;QAC7D,OAAe;UACL/I,KAAA,GAAQ,IAAIwZ,UAAA,CAAWpU,UAAA,EAAYO,UAAA,EAAYqF,WAAA,CAAYnF,KAAA,GAAQkD,QAAQ;QAC5E;QAED+Q,eAAA,GAAkB,IAAIL,eAAA,CAAgBzZ,KAAA,EAAO+I,QAAA,EAAUC,UAAU;MAClE;MAGD,IAAIgC,WAAA,CAAYuO,MAAA,KAAW,QAAW;QACpC,MAAMc,eAAA,GAAkBpK,gBAAA,CAAiBC,MAAA;QACzC,MAAMoK,iBAAA,GAAoBnP,qBAAA,CAAsBH,WAAA,CAAYuO,MAAA,CAAO9E,OAAA,CAAQvJ,aAAa;QAExF,MAAMqP,iBAAA,GAAoBvP,WAAA,CAAYuO,MAAA,CAAO9E,OAAA,CAAQ9O,UAAA,IAAc;QACnE,MAAM6U,gBAAA,GAAmBxP,WAAA,CAAYuO,MAAA,CAAO/M,MAAA,CAAO7G,UAAA,IAAc;QAEjE,MAAM8U,aAAA,GAAgB,IAAIH,iBAAA,CACxBjV,WAAA,CAAY,CAAC,GACbkV,iBAAA,EACAvP,WAAA,CAAYuO,MAAA,CAAO1T,KAAA,GAAQwU,eAC5B;QACD,MAAMK,YAAA,GAAe,IAAIlB,UAAA,CAAWnU,WAAA,CAAY,CAAC,GAAGmV,gBAAA,EAAkBxP,WAAA,CAAYuO,MAAA,CAAO1T,KAAA,GAAQkD,QAAQ;QAEzG,IAAI3D,UAAA,KAAe,MAAM;UAEvB0U,eAAA,GAAkB,IAAIL,eAAA,CACpBK,eAAA,CAAgB9Z,KAAA,CAAMtH,KAAA,CAAO,GAC7BohB,eAAA,CAAgB/Q,QAAA,EAChB+Q,eAAA,CAAgB9Q,UACjB;QACF;QAED,SAAS3P,CAAA,GAAI,GAAGia,EAAA,GAAKmH,aAAA,CAAcnhB,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;UACtD,MAAM0F,KAAA,GAAQ0b,aAAA,CAAcphB,CAAC;UAE7BygB,eAAA,CAAgBa,IAAA,CAAK5b,KAAA,EAAO2b,YAAA,CAAarhB,CAAA,GAAI0P,QAAQ,CAAC;UACtD,IAAIA,QAAA,IAAY,GAAG+Q,eAAA,CAAgBc,IAAA,CAAK7b,KAAA,EAAO2b,YAAA,CAAarhB,CAAA,GAAI0P,QAAA,GAAW,CAAC,CAAC;UAC7E,IAAIA,QAAA,IAAY,GAAG+Q,eAAA,CAAgBe,IAAA,CAAK9b,KAAA,EAAO2b,YAAA,CAAarhB,CAAA,GAAI0P,QAAA,GAAW,CAAC,CAAC;UAC7E,IAAIA,QAAA,IAAY,GAAG+Q,eAAA,CAAgBgB,IAAA,CAAK/b,KAAA,EAAO2b,YAAA,CAAarhB,CAAA,GAAI0P,QAAA,GAAW,CAAC,CAAC;UAC7E,IAAIA,QAAA,IAAY,GAAG,MAAM,IAAIrR,KAAA,CAAM,mEAAmE;QACvG;MACF;MAED,OAAOoiB,eAAA;IACb,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD/V,YAAYC,YAAA,EAAc;IACxB,MAAM9L,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMiM,OAAA,GAAU,KAAKA,OAAA;IACrB,MAAMF,UAAA,GAAa/L,IAAA,CAAKgM,QAAA,CAASF,YAAY;IAC7C,MAAM+W,WAAA,GAAc9W,UAAA,CAAWI,MAAA;IAC/B,MAAM2W,SAAA,GAAY9iB,IAAA,CAAKqM,MAAA,CAAOwW,WAAW;IAEzC,IAAIlkB,MAAA,GAAS,KAAK2N,aAAA;IAElB,IAAIwW,SAAA,CAAUvW,GAAA,EAAK;MACjB,MAAMC,OAAA,GAAUP,OAAA,CAAQjQ,OAAA,CAAQyQ,UAAA,CAAWqW,SAAA,CAAUvW,GAAG;MACxD,IAAIC,OAAA,KAAY,MAAM7N,MAAA,GAAS6N,OAAA;IAChC;IAED,OAAO,KAAKN,gBAAA,CAAiBJ,YAAA,EAAc+W,WAAA,EAAalkB,MAAM;EAC/D;EAEDuN,iBAAiBJ,YAAA,EAAc+W,WAAA,EAAalkB,MAAA,EAAQ;IAClD,MAAMrC,MAAA,GAAS;IACf,MAAM0D,IAAA,GAAO,KAAKA,IAAA;IAElB,MAAM+L,UAAA,GAAa/L,IAAA,CAAKgM,QAAA,CAASF,YAAY;IAC7C,MAAMgX,SAAA,GAAY9iB,IAAA,CAAKqM,MAAA,CAAOwW,WAAW;IAEzC,MAAM9d,QAAA,IAAY+d,SAAA,CAAUvW,GAAA,IAAOuW,SAAA,CAAU5V,UAAA,IAAc,MAAMnB,UAAA,CAAWgX,OAAA;IAE5E,IAAI,KAAKvF,YAAA,CAAazY,QAAQ,GAAG;MAE/B,OAAO,KAAKyY,YAAA,CAAazY,QAAQ;IAClC;IAED,MAAMie,OAAA,GAAU,KAAKC,eAAA,CAAgBJ,WAAA,EAAalkB,MAAM,EACrDqI,IAAA,CAAK,UAAUqM,OAAA,EAAS;MACvBA,OAAA,CAAQ6P,KAAA,GAAQ;MAEhB7P,OAAA,CAAQ/R,IAAA,GAAOyK,UAAA,CAAWzK,IAAA,IAAQwhB,SAAA,CAAUxhB,IAAA,IAAQ;MAEpD,IACE+R,OAAA,CAAQ/R,IAAA,KAAS,MACjB,OAAOwhB,SAAA,CAAUvW,GAAA,KAAQ,YACzBuW,SAAA,CAAUvW,GAAA,CAAI4W,UAAA,CAAW,aAAa,MAAM,OAC5C;QACA9P,OAAA,CAAQ/R,IAAA,GAAOwhB,SAAA,CAAUvW,GAAA;MAC1B;MAED,MAAM6W,QAAA,GAAWpjB,IAAA,CAAKojB,QAAA,IAAY,CAAE;MACpC,MAAML,OAAA,GAAUK,QAAA,CAASrX,UAAA,CAAWgX,OAAO,KAAK,CAAE;MAElD1P,OAAA,CAAQgQ,SAAA,GAAYjM,aAAA,CAAc2L,OAAA,CAAQM,SAAS,KAAK/L,YAAA;MACxDjE,OAAA,CAAQiQ,SAAA,GAAYlM,aAAA,CAAc2L,OAAA,CAAQO,SAAS,KAAK5L,wBAAA;MACxDrE,OAAA,CAAQkQ,KAAA,GAAQ5L,eAAA,CAAgBoL,OAAA,CAAQQ,KAAK,KAAKzL,cAAA;MAClDzE,OAAA,CAAQmQ,KAAA,GAAQ7L,eAAA,CAAgBoL,OAAA,CAAQS,KAAK,KAAK1L,cAAA;MAElDxb,MAAA,CAAO0gB,YAAA,CAAanX,GAAA,CAAIwN,OAAA,EAAS;QAAErH,QAAA,EAAUF;MAAA,CAAc;MAE3D,OAAOuH,OAAA;IACf,CAAO,EACA2L,KAAA,CAAM,YAAY;MACjB,OAAO;IACf,CAAO;IAEH,KAAKxB,YAAA,CAAazY,QAAQ,IAAIie,OAAA;IAE9B,OAAOA,OAAA;EACR;EAEDC,gBAAgBJ,WAAA,EAAalkB,MAAA,EAAQ;IACnC,MAAMrC,MAAA,GAAS;IACf,MAAM0D,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMiM,OAAA,GAAU,KAAKA,OAAA;IAErB,IAAI,KAAKsR,WAAA,CAAYsF,WAAW,MAAM,QAAW;MAC/C,OAAO,KAAKtF,WAAA,CAAYsF,WAAW,EAAE7b,IAAA,CAAMqM,OAAA,IAAYA,OAAA,CAAQM,KAAA,EAAO;IACvE;IAED,MAAMmP,SAAA,GAAY9iB,IAAA,CAAKqM,MAAA,CAAOwW,WAAW;IAEzC,MAAMY,GAAA,GAAMC,IAAA,CAAKD,GAAA,IAAOC,IAAA,CAAKC,SAAA;IAE7B,IAAIC,SAAA,GAAYd,SAAA,CAAUvW,GAAA,IAAO;IACjC,IAAIsX,WAAA,GAAc;IAElB,IAAIf,SAAA,CAAU5V,UAAA,KAAe,QAAW;MAGtC0W,SAAA,GAAYtnB,MAAA,CAAOsK,aAAA,CAAc,cAAckc,SAAA,CAAU5V,UAAU,EAAElG,IAAA,CAAK,UAAUkG,UAAA,EAAY;QAC9F2W,WAAA,GAAc;QACd,MAAMC,IAAA,GAAO,IAAIC,IAAA,CAAK,CAAC7W,UAAU,GAAG;UAAEzH,IAAA,EAAMqd,SAAA,CAAUkB;QAAA,CAAU;QAChEJ,SAAA,GAAYH,GAAA,CAAIQ,eAAA,CAAgBH,IAAI;QACpC,OAAOF,SAAA;MACf,CAAO;IACP,WAAed,SAAA,CAAUvW,GAAA,KAAQ,QAAW;MACtC,MAAM,IAAI/M,KAAA,CAAM,6BAA6BqjB,WAAA,GAAc,gCAAgC;IAC5F;IAED,MAAMG,OAAA,GAAU1gB,OAAA,CAAQC,OAAA,CAAQqhB,SAAS,EACtC5c,IAAA,CAAK,UAAUkd,UAAA,EAAW;MACzB,OAAO,IAAI5hB,OAAA,CAAQ,UAAUC,OAAA,EAASC,MAAA,EAAQ;QAC5C,IAAI9E,MAAA,GAAS6E,OAAA;QAEb,IAAI5D,MAAA,CAAOwlB,mBAAA,KAAwB,MAAM;UACvCzmB,MAAA,GAAS,SAAAA,CAAU0mB,WAAA,EAAa;YAC9B,MAAM/Q,OAAA,GAAU,IAAIgR,OAAA,CAAQD,WAAW;YACvC/Q,OAAA,CAAQS,WAAA,GAAc;YAEtBvR,OAAA,CAAQ8Q,OAAO;UAChB;QACF;QAED1U,MAAA,CAAOnB,IAAA,CAAKS,WAAA,CAAYE,UAAA,CAAW+lB,UAAA,EAAWjY,OAAA,CAAQlO,IAAI,GAAGL,MAAA,EAAQ,QAAW8E,MAAM;MAChG,CAAS;IACT,CAAO,EACAwE,IAAA,CAAK,UAAUqM,OAAA,EAAS;MAGvB,IAAIwQ,WAAA,KAAgB,MAAM;QACxBJ,GAAA,CAAIa,eAAA,CAAgBV,SAAS;MAC9B;MAEDnd,sBAAA,CAAuB4M,OAAA,EAASyP,SAAS;MAEzCzP,OAAA,CAAQoH,QAAA,CAASuJ,QAAA,GAAWlB,SAAA,CAAUkB,QAAA,IAAYnH,mBAAA,CAAoBiG,SAAA,CAAUvW,GAAG;MAEnF,OAAO8G,OAAA;IACf,CAAO,EACA2L,KAAA,CAAM,UAAUxgB,KAAA,EAAO;MACtBD,OAAA,CAAQC,KAAA,CAAM,2CAA2ColB,SAAS;MAClE,MAAMplB,KAAA;IACd,CAAO;IAEH,KAAK+e,WAAA,CAAYsF,WAAW,IAAIG,OAAA;IAChC,OAAOA,OAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDhb,cAAcX,cAAA,EAAgBkd,OAAA,EAASC,MAAA,EAAQC,UAAA,EAAY;IACzD,MAAMnoB,MAAA,GAAS;IAEf,OAAO,KAAKsK,aAAA,CAAc,WAAW4d,MAAA,CAAO3d,KAAK,EAAEG,IAAA,CAAK,UAAUqM,OAAA,EAAS;MACzE,IAAI,CAACA,OAAA,EAAS,OAAO;MAErB,IAAImR,MAAA,CAAOjR,QAAA,KAAa,UAAaiR,MAAA,CAAOjR,QAAA,GAAW,GAAG;QACxDF,OAAA,GAAUA,OAAA,CAAQM,KAAA,CAAO;QACzBN,OAAA,CAAQG,OAAA,GAAUgR,MAAA,CAAOjR,QAAA;MAC1B;MAED,IAAIjX,MAAA,CAAO2D,UAAA,CAAWS,UAAA,CAAWoB,qBAAqB,GAAG;QACvD,MAAMwR,SAAA,GACJkR,MAAA,CAAOvkB,UAAA,KAAe,SAAYukB,MAAA,CAAOvkB,UAAA,CAAWS,UAAA,CAAWoB,qBAAqB,IAAI;QAE1F,IAAIwR,SAAA,EAAW;UACb,MAAMoR,aAAA,GAAgBpoB,MAAA,CAAO0gB,YAAA,CAAara,GAAA,CAAI0Q,OAAO;UACrDA,OAAA,GAAU/W,MAAA,CAAO2D,UAAA,CAAWS,UAAA,CAAWoB,qBAAqB,EAAEsR,aAAA,CAAcC,OAAA,EAASC,SAAS;UAC9FhX,MAAA,CAAO0gB,YAAA,CAAanX,GAAA,CAAIwN,OAAA,EAASqR,aAAa;QAC/C;MACF;MAED,IAAID,UAAA,KAAe,QAAW;QAE5B,IAAI,OAAOA,UAAA,KAAe,UACxBA,UAAA,GAAaA,UAAA,KAAe9oB,YAAA,GAAeF,cAAA,GAAiBC,oBAAA;QAG9D,IAAI,gBAAgB2X,OAAA,EAASA,OAAA,CAAQoR,UAAA,GAAaA,UAAA,MAC7CpR,OAAA,CAAQsR,QAAA,GAAWF,UAAA,KAAehpB,cAAA,GAAiBE,YAAA,GAAeC,cAAA;MACxE;MAEDyL,cAAA,CAAekd,OAAO,IAAIlR,OAAA;MAE1B,OAAOA,OAAA;IACb,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDjC,oBAAoB9C,IAAA,EAAM;IACxB,MAAM2B,QAAA,GAAW3B,IAAA,CAAK2B,QAAA;IACtB,IAAIC,QAAA,GAAW5B,IAAA,CAAK4B,QAAA;IAEpB,MAAM0U,qBAAA,GAAwB3U,QAAA,CAASjB,UAAA,CAAW6V,OAAA,KAAY;IAC9D,MAAMC,eAAA,GAAkB7U,QAAA,CAASjB,UAAA,CAAW3J,KAAA,KAAU;IACtD,MAAM0f,cAAA,GAAiB9U,QAAA,CAASjB,UAAA,CAAWyM,MAAA,KAAW;IAEtD,IAAInN,IAAA,CAAK0W,QAAA,EAAU;MACjB,MAAMjgB,QAAA,GAAW,oBAAoBmL,QAAA,CAAS+U,IAAA;MAE9C,IAAIC,cAAA,GAAiB,KAAKhhB,KAAA,CAAMvB,GAAA,CAAIoC,QAAQ;MAE5C,IAAI,CAACmgB,cAAA,EAAgB;QACnBA,cAAA,GAAiB,IAAIC,cAAA,CAAgB;QACrCC,QAAA,CAASnU,SAAA,CAAUC,IAAA,CAAKC,IAAA,CAAK+T,cAAA,EAAgBhV,QAAQ;QACrDgV,cAAA,CAAe7f,KAAA,CAAM6L,IAAA,CAAKhB,QAAA,CAAS7K,KAAK;QACxC6f,cAAA,CAAepE,GAAA,GAAM5Q,QAAA,CAAS4Q,GAAA;QAC9BoE,cAAA,CAAeG,eAAA,GAAkB;QAEjC,KAAKnhB,KAAA,CAAMrB,GAAA,CAAIkC,QAAA,EAAUmgB,cAAc;MACxC;MAEDhV,QAAA,GAAWgV,cAAA;IACjB,WAAe5W,IAAA,CAAKgX,MAAA,EAAQ;MACtB,MAAMvgB,QAAA,GAAW,uBAAuBmL,QAAA,CAAS+U,IAAA;MAEjD,IAAIM,YAAA,GAAe,KAAKrhB,KAAA,CAAMvB,GAAA,CAAIoC,QAAQ;MAE1C,IAAI,CAACwgB,YAAA,EAAc;QACjBA,YAAA,GAAe,IAAIC,iBAAA,CAAmB;QACtCJ,QAAA,CAASnU,SAAA,CAAUC,IAAA,CAAKC,IAAA,CAAKoU,YAAA,EAAcrV,QAAQ;QACnDqV,YAAA,CAAalgB,KAAA,CAAM6L,IAAA,CAAKhB,QAAA,CAAS7K,KAAK;QACtCkgB,YAAA,CAAazE,GAAA,GAAM5Q,QAAA,CAAS4Q,GAAA;QAE5B,KAAK5c,KAAA,CAAMrB,GAAA,CAAIkC,QAAA,EAAUwgB,YAAY;MACtC;MAEDrV,QAAA,GAAWqV,YAAA;IACZ;IAGD,IAAIX,qBAAA,IAAyBE,eAAA,IAAmBC,cAAA,EAAgB;MAC9D,IAAIhgB,QAAA,GAAW,oBAAoBmL,QAAA,CAAS+U,IAAA,GAAO;MAEnD,IAAIL,qBAAA,EAAuB7f,QAAA,IAAY;MACvC,IAAI+f,eAAA,EAAiB/f,QAAA,IAAY;MACjC,IAAIggB,cAAA,EAAgBhgB,QAAA,IAAY;MAEhC,IAAI0gB,cAAA,GAAiB,KAAKvhB,KAAA,CAAMvB,GAAA,CAAIoC,QAAQ;MAE5C,IAAI,CAAC0gB,cAAA,EAAgB;QACnBA,cAAA,GAAiBvV,QAAA,CAASyD,KAAA,CAAO;QAEjC,IAAImR,eAAA,EAAiBW,cAAA,CAAeC,YAAA,GAAe;QACnD,IAAIX,cAAA,EAAgBU,cAAA,CAAeE,WAAA,GAAc;QAEjD,IAAIf,qBAAA,EAAuB;UAEzB,IAAIa,cAAA,CAAeG,WAAA,EAAaH,cAAA,CAAeG,WAAA,CAAYC,CAAA,IAAK;UAChE,IAAIJ,cAAA,CAAexc,oBAAA,EAAsBwc,cAAA,CAAexc,oBAAA,CAAqB4c,CAAA,IAAK;QACnF;QAED,KAAK3hB,KAAA,CAAMrB,GAAA,CAAIkC,QAAA,EAAU0gB,cAAc;QAEvC,KAAKzI,YAAA,CAAanX,GAAA,CAAI4f,cAAA,EAAgB,KAAKzI,YAAA,CAAara,GAAA,CAAIuN,QAAQ,CAAC;MACtE;MAEDA,QAAA,GAAWuV,cAAA;IACZ;IAEDnX,IAAA,CAAK4B,QAAA,GAAWA,QAAA;EACjB;EAEDhJ,gBAAA,EAAqC;IACnC,OAAO4S,oBAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD2G,aAAatY,aAAA,EAAe;IAC1B,MAAM7L,MAAA,GAAS;IACf,MAAM0D,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMC,UAAA,GAAa,KAAKA,UAAA;IACxB,MAAMqH,WAAA,GAActH,IAAA,CAAKoI,SAAA,CAAUD,aAAa;IAEhD,IAAI2d,YAAA;IACJ,MAAMze,cAAA,GAAiB,CAAE;IACzB,MAAM0e,kBAAA,GAAqBze,WAAA,CAAYrH,UAAA,IAAc,CAAE;IAEvD,MAAMsH,OAAA,GAAU,EAAE;IAElB,IAAIwe,kBAAA,CAAmBrlB,UAAA,CAAWgB,mBAAmB,GAAG;MACtD,MAAMskB,YAAA,GAAe/lB,UAAA,CAAWS,UAAA,CAAWgB,mBAAmB;MAC9DokB,YAAA,GAAeE,YAAA,CAAa9e,eAAA,CAAiB;MAC7CK,OAAA,CAAQ1H,IAAA,CAAKmmB,YAAA,CAAa5e,YAAA,CAAaC,cAAA,EAAgBC,WAAA,EAAahL,MAAM,CAAC;IACjF,OAAW;MAIL,MAAMmL,iBAAA,GAAoBH,WAAA,CAAYI,oBAAA,IAAwB,CAAE;MAEhEL,cAAA,CAAehC,KAAA,GAAQ,IAAIC,KAAA,CAAM,GAAK,GAAK,CAAG;MAC9C+B,cAAA,CAAeG,OAAA,GAAU;MAEzB,IAAIG,KAAA,CAAMC,OAAA,CAAQH,iBAAA,CAAkBI,eAAe,GAAG;QACpD,MAAMC,KAAA,GAAQL,iBAAA,CAAkBI,eAAA;QAEhCR,cAAA,CAAehC,KAAA,CAAME,MAAA,CAAOuC,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGpM,oBAAoB;QAC9E2L,cAAA,CAAeG,OAAA,GAAUM,KAAA,CAAM,CAAC;MACjC;MAED,IAAIL,iBAAA,CAAkBM,gBAAA,KAAqB,QAAW;QACpDR,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,OAAOI,iBAAA,CAAkBM,gBAAA,EAAkBtM,cAAc,CAAC;MAC7G;MAED4L,cAAA,CAAe2S,SAAA,GAAYvS,iBAAA,CAAkBwe,cAAA,KAAmB,SAAYxe,iBAAA,CAAkBwe,cAAA,GAAiB;MAC/G5e,cAAA,CAAe4S,SAAA,GACbxS,iBAAA,CAAkBye,eAAA,KAAoB,SAAYze,iBAAA,CAAkBye,eAAA,GAAkB;MAExF,IAAIze,iBAAA,CAAkB0e,wBAAA,KAA6B,QAAW;QAC5D5e,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,gBAAgBI,iBAAA,CAAkB0e,wBAAwB,CAAC;QAC7G5e,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,gBAAgBI,iBAAA,CAAkB0e,wBAAwB,CAAC;MAC9G;MAEDL,YAAA,GAAe,KAAK7F,UAAA,CAAW,UAAU3B,GAAA,EAAK;QAC5C,OAAOA,GAAA,CAAIpX,eAAA,IAAmBoX,GAAA,CAAIpX,eAAA,CAAgBiB,aAAa;MACvE,CAAO;MAEDZ,OAAA,CAAQ1H,IAAA,CACNyC,OAAA,CAAQ2F,GAAA,CACN,KAAKoW,UAAA,CAAW,UAAUC,GAAA,EAAK;QAC7B,OAAOA,GAAA,CAAIpW,oBAAA,IAAwBoW,GAAA,CAAIpW,oBAAA,CAAqBC,aAAA,EAAed,cAAc;MACrG,CAAW,CACF,CACF;IACF;IAED,IAAIC,WAAA,CAAY8e,WAAA,KAAgB,MAAM;MACpC/e,cAAA,CAAe+S,IAAA,GAAOiM,UAAA;IACvB;IAED,MAAMC,SAAA,GAAYhf,WAAA,CAAYgf,SAAA,IAAa7M,WAAA,CAAYC,MAAA;IAEvD,IAAI4M,SAAA,KAAc7M,WAAA,CAAYG,KAAA,EAAO;MACnCvS,cAAA,CAAe6S,WAAA,GAAc;MAG7B7S,cAAA,CAAekf,UAAA,GAAa;IAClC,OAAW;MACLlf,cAAA,CAAe6S,WAAA,GAAc;MAE7B,IAAIoM,SAAA,KAAc7M,WAAA,CAAYE,IAAA,EAAM;QAClCtS,cAAA,CAAemf,SAAA,GAAYlf,WAAA,CAAYmf,WAAA,KAAgB,SAAYnf,WAAA,CAAYmf,WAAA,GAAc;MAC9F;IACF;IAED,IAAInf,WAAA,CAAYof,aAAA,KAAkB,UAAaZ,YAAA,KAAiB3e,iBAAA,EAAmB;MACjFI,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,aAAaC,WAAA,CAAYof,aAAa,CAAC;MAEzFrf,cAAA,CAAeue,WAAA,GAAc,IAAI1c,OAAA,CAAQ,GAAG,CAAC;MAE7C,IAAI5B,WAAA,CAAYof,aAAA,CAAc1d,KAAA,KAAU,QAAW;QACjD,MAAMA,KAAA,GAAQ1B,WAAA,CAAYof,aAAA,CAAc1d,KAAA;QAExC3B,cAAA,CAAeue,WAAA,CAAY/f,GAAA,CAAImD,KAAA,EAAOA,KAAK;MAC5C;IACF;IAED,IAAI1B,WAAA,CAAYqf,gBAAA,KAAqB,UAAab,YAAA,KAAiB3e,iBAAA,EAAmB;MACpFI,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,SAASC,WAAA,CAAYqf,gBAAgB,CAAC;MAExF,IAAIrf,WAAA,CAAYqf,gBAAA,CAAiBC,QAAA,KAAa,QAAW;QACvDvf,cAAA,CAAewf,cAAA,GAAiBvf,WAAA,CAAYqf,gBAAA,CAAiBC,QAAA;MAC9D;IACF;IAED,IAAItf,WAAA,CAAYwf,cAAA,KAAmB,UAAahB,YAAA,KAAiB3e,iBAAA,EAAmB;MAClF,MAAM2f,cAAA,GAAiBxf,WAAA,CAAYwf,cAAA;MACnCzf,cAAA,CAAe0S,QAAA,GAAW,IAAIzU,KAAA,CAAK,EAAGC,MAAA,CACpCuhB,cAAA,CAAe,CAAC,GAChBA,cAAA,CAAe,CAAC,GAChBA,cAAA,CAAe,CAAC,GAChBprB,oBACD;IACF;IAED,IAAI4L,WAAA,CAAYyf,eAAA,KAAoB,UAAajB,YAAA,KAAiB3e,iBAAA,EAAmB;MACnFI,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO0L,aAAA,CAAcX,cAAA,EAAgB,eAAeC,WAAA,CAAYyf,eAAA,EAAiBtrB,cAAc,CAAC;IAC9G;IAED,OAAO6G,OAAA,CAAQ2F,GAAA,CAAIV,OAAO,EAAEP,IAAA,CAAK,YAAY;MAC3C,MAAMkJ,QAAA,GAAW,IAAI4V,YAAA,CAAaze,cAAc;MAEhD,IAAIC,WAAA,CAAYhG,IAAA,EAAM4O,QAAA,CAAS5O,IAAA,GAAOgG,WAAA,CAAYhG,IAAA;MAElDmF,sBAAA,CAAuByJ,QAAA,EAAU5I,WAAW;MAE5ChL,MAAA,CAAO0gB,YAAA,CAAanX,GAAA,CAAIqK,QAAA,EAAU;QAAE9H,SAAA,EAAWD;MAAA,CAAe;MAE9D,IAAIb,WAAA,CAAYrH,UAAA,EAAYqa,8BAAA,CAA+Bra,UAAA,EAAYiQ,QAAA,EAAU5I,WAAW;MAE5F,OAAO4I,QAAA;IACb,CAAK;EACF;EAAA;EAGDvJ,iBAAiBqgB,YAAA,EAAc;IAC7B,MAAMC,aAAA,GAAgBC,eAAA,CAAgBC,gBAAA,CAAiBH,YAAA,IAAgB,EAAE;IAEzE,IAAIC,aAAA,IAAiB,KAAKxJ,aAAA,EAAe;MACvC,OAAOwJ,aAAA,GAAgB,MAAM,EAAE,KAAKxJ,aAAA,CAAcwJ,aAAa;IACrE,OAAW;MACL,KAAKxJ,aAAA,CAAcwJ,aAAa,IAAI;MAEpC,OAAOA,aAAA;IACR;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDG,eAAe1Y,UAAA,EAAY;IACzB,MAAMpS,MAAA,GAAS;IACf,MAAM2D,UAAA,GAAa,KAAKA,UAAA;IACxB,MAAMiE,KAAA,GAAQ,KAAKgZ,cAAA;IAEnB,SAASmK,qBAAqB5Y,SAAA,EAAW;MACvC,OAAOxO,UAAA,CAAWS,UAAA,CAAWkB,0BAA0B,EACpDyQ,eAAA,CAAgB5D,SAAA,EAAWnS,MAAM,EACjC0K,IAAA,CAAK,UAAUiJ,QAAA,EAAU;QACxB,OAAOqX,sBAAA,CAAuBrX,QAAA,EAAUxB,SAAA,EAAWnS,MAAM;MACnE,CAAS;IACJ;IAED,MAAMiL,OAAA,GAAU,EAAE;IAElB,SAASpG,CAAA,GAAI,GAAGia,EAAA,GAAK1M,UAAA,CAAWtN,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MACnD,MAAMsN,SAAA,GAAYC,UAAA,CAAWvN,CAAC;MAC9B,MAAM4D,QAAA,GAAWoX,kBAAA,CAAmB1N,SAAS;MAG7C,MAAM8Y,MAAA,GAASrjB,KAAA,CAAMa,QAAQ;MAE7B,IAAIwiB,MAAA,EAAQ;QAEVhgB,OAAA,CAAQ1H,IAAA,CAAK0nB,MAAA,CAAOvE,OAAO;MACnC,OAAa;QACL,IAAIwE,eAAA;QAEJ,IAAI/Y,SAAA,CAAUxO,UAAA,IAAcwO,SAAA,CAAUxO,UAAA,CAAWS,UAAA,CAAWkB,0BAA0B,GAAG;UAEvF4lB,eAAA,GAAkBH,oBAAA,CAAqB5Y,SAAS;QAC1D,OAAe;UAEL+Y,eAAA,GAAkBF,sBAAA,CAAuB,IAAIG,cAAA,CAAc,GAAIhZ,SAAA,EAAWnS,MAAM;QACjF;QAGD4H,KAAA,CAAMa,QAAQ,IAAI;UAAE0J,SAAA;UAAsBuU,OAAA,EAASwE;QAAiB;QAEpEjgB,OAAA,CAAQ1H,IAAA,CAAK2nB,eAAe;MAC7B;IACF;IAED,OAAOllB,OAAA,CAAQ2F,GAAA,CAAIV,OAAO;EAC3B;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD+Y,SAASoH,SAAA,EAAW;IAClB,MAAMprB,MAAA,GAAS;IACf,MAAM0D,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMC,UAAA,GAAa,KAAKA,UAAA;IAExB,MAAMsO,OAAA,GAAUvO,IAAA,CAAKwO,MAAA,CAAOkZ,SAAS;IACrC,MAAMhZ,UAAA,GAAaH,OAAA,CAAQG,UAAA;IAE3B,MAAMnH,OAAA,GAAU,EAAE;IAElB,SAASpG,CAAA,GAAI,GAAGia,EAAA,GAAK1M,UAAA,CAAWtN,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MACnD,MAAM+O,QAAA,GACJxB,UAAA,CAAWvN,CAAC,EAAE+O,QAAA,KAAa,SACvB2J,qBAAA,CAAsB,KAAK3V,KAAK,IAChC,KAAK0C,aAAA,CAAc,YAAY8H,UAAA,CAAWvN,CAAC,EAAE+O,QAAQ;MAE3D3I,OAAA,CAAQ1H,IAAA,CAAKqQ,QAAQ;IACtB;IAED3I,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAO8qB,cAAA,CAAe1Y,UAAU,CAAC;IAE9C,OAAOpM,OAAA,CAAQ2F,GAAA,CAAIV,OAAO,EAAEP,IAAA,CAAK,UAAUkI,OAAA,EAAS;MAClD,MAAM9G,SAAA,GAAY8G,OAAA,CAAQ1O,KAAA,CAAM,GAAG0O,OAAA,CAAQ9N,MAAA,GAAS,CAAC;MACrD,MAAMumB,UAAA,GAAazY,OAAA,CAAQA,OAAA,CAAQ9N,MAAA,GAAS,CAAC;MAE7C,MAAMoN,MAAA,GAAS,EAAE;MAEjB,SAASrN,CAAA,GAAI,GAAGia,EAAA,GAAKuM,UAAA,CAAWvmB,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QACnD,MAAM8O,QAAA,GAAW0X,UAAA,CAAWxmB,CAAC;QAC7B,MAAMsN,SAAA,GAAYC,UAAA,CAAWvN,CAAC;QAI9B,IAAImN,IAAA;QAEJ,MAAM4B,QAAA,GAAW9H,SAAA,CAAUjH,CAAC;QAE5B,IACEsN,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBC,SAAA,IACnCH,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBE,cAAA,IACnCJ,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBG,YAAA,IACnCL,SAAA,CAAUV,IAAA,KAAS,QACnB;UAEAO,IAAA,GAAOC,OAAA,CAAQkR,aAAA,KAAkB,OAAO,IAAImI,WAAA,CAAY3X,QAAA,EAAUC,QAAQ,IAAI,IAAI2X,IAAA,CAAK5X,QAAA,EAAUC,QAAQ;UAEzG,IAAI5B,IAAA,CAAKmR,aAAA,KAAkB,MAAM;YAE/BnR,IAAA,CAAKwZ,oBAAA,CAAsB;UAC5B;UAED,IAAIrZ,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBE,cAAA,EAAgB;YACrDP,IAAA,CAAK2B,QAAA,GAAW8X,mBAAA,CAAoBzZ,IAAA,CAAK2B,QAAA,EAAU+X,qBAAqB;UACzE,WAAUvZ,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBG,YAAA,EAAc;YAC1DR,IAAA,CAAK2B,QAAA,GAAW8X,mBAAA,CAAoBzZ,IAAA,CAAK2B,QAAA,EAAUgY,mBAAmB;UACvE;QACF,WAAUxZ,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgB+H,KAAA,EAAO;UACnDpI,IAAA,GAAO,IAAI4Z,YAAA,CAAajY,QAAA,EAAUC,QAAQ;QAC3C,WAAUzB,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBiI,UAAA,EAAY;UACxDtI,IAAA,GAAO,IAAI6Z,IAAA,CAAKlY,QAAA,EAAUC,QAAQ;QACnC,WAAUzB,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgBgI,SAAA,EAAW;UACvDrI,IAAA,GAAO,IAAI8Z,QAAA,CAASnY,QAAA,EAAUC,QAAQ;QACvC,WAAUzB,SAAA,CAAUV,IAAA,KAASY,eAAA,CAAgB8H,MAAA,EAAQ;UACpDnI,IAAA,GAAO,IAAI+Z,MAAA,CAAOpY,QAAA,EAAUC,QAAQ;QAC9C,OAAe;UACL,MAAM,IAAI1Q,KAAA,CAAM,mDAAmDiP,SAAA,CAAUV,IAAI;QAClF;QAED,IAAI8M,MAAA,CAAO6B,IAAA,CAAKpO,IAAA,CAAK2B,QAAA,CAAS4L,eAAe,EAAEza,MAAA,GAAS,GAAG;UACzD2a,kBAAA,CAAmBzN,IAAA,EAAMC,OAAO;QACjC;QAEDD,IAAA,CAAKhN,IAAA,GAAOhF,MAAA,CAAOqK,gBAAA,CAAiB4H,OAAA,CAAQjN,IAAA,IAAQ,UAAUomB,SAAS;QAEvEjhB,sBAAA,CAAuB6H,IAAA,EAAMC,OAAO;QAEpC,IAAIE,SAAA,CAAUxO,UAAA,EAAYqa,8BAAA,CAA+Bra,UAAA,EAAYqO,IAAA,EAAMG,SAAS;QAEpFnS,MAAA,CAAO8U,mBAAA,CAAoB9C,IAAI;QAE/BE,MAAA,CAAO3O,IAAA,CAAKyO,IAAI;MACjB;MAED,SAASnN,CAAA,GAAI,GAAGia,EAAA,GAAK5M,MAAA,CAAOpN,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QAC/C7E,MAAA,CAAO0gB,YAAA,CAAanX,GAAA,CAAI2I,MAAA,CAAOrN,CAAC,GAAG;UACjCqN,MAAA,EAAQkZ,SAAA;UACRhZ,UAAA,EAAYvN;QACtB,CAAS;MACF;MAED,IAAIqN,MAAA,CAAOpN,MAAA,KAAW,GAAG;QACvB,IAAImN,OAAA,CAAQtO,UAAA,EAAYqa,8BAAA,CAA+Bra,UAAA,EAAYuO,MAAA,CAAO,CAAC,GAAGD,OAAO;QAErF,OAAOC,MAAA,CAAO,CAAC;MAChB;MAED,MAAM8Z,KAAA,GAAQ,IAAIC,KAAA,CAAO;MAEzB,IAAIha,OAAA,CAAQtO,UAAA,EAAYqa,8BAAA,CAA+Bra,UAAA,EAAYqoB,KAAA,EAAO/Z,OAAO;MAEjFjS,MAAA,CAAO0gB,YAAA,CAAanX,GAAA,CAAIyiB,KAAA,EAAO;QAAE9Z,MAAA,EAAQkZ;MAAA,CAAW;MAEpD,SAASvmB,CAAA,GAAI,GAAGia,EAAA,GAAK5M,MAAA,CAAOpN,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QAC/CmnB,KAAA,CAAMzlB,GAAA,CAAI2L,MAAA,CAAOrN,CAAC,CAAC;MACpB;MAED,OAAOmnB,KAAA;IACb,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD1H,WAAW4H,WAAA,EAAa;IACtB,IAAI9I,MAAA;IACJ,MAAM+I,SAAA,GAAY,KAAKzoB,IAAA,CAAK6e,OAAA,CAAQ2J,WAAW;IAC/C,MAAME,MAAA,GAASD,SAAA,CAAUA,SAAA,CAAUhjB,IAAI;IAEvC,IAAI,CAACijB,MAAA,EAAQ;MACXnqB,OAAA,CAAQ2D,IAAA,CAAK,8CAA8C;MAC3D;IACD;IAED,IAAIumB,SAAA,CAAUhjB,IAAA,KAAS,eAAe;MACpCia,MAAA,GAAS,IAAIiJ,iBAAA,CACXC,SAAA,CAAUC,QAAA,CAASH,MAAA,CAAOI,IAAI,GAC9BJ,MAAA,CAAOK,WAAA,IAAe,GACtBL,MAAA,CAAOM,KAAA,IAAS,GAChBN,MAAA,CAAOO,IAAA,IAAQ,GAChB;IACP,WAAeR,SAAA,CAAUhjB,IAAA,KAAS,gBAAgB;MAC5Cia,MAAA,GAAS,IAAIwJ,kBAAA,CAAmB,CAACR,MAAA,CAAOS,IAAA,EAAMT,MAAA,CAAOS,IAAA,EAAMT,MAAA,CAAOU,IAAA,EAAM,CAACV,MAAA,CAAOU,IAAA,EAAMV,MAAA,CAAOM,KAAA,EAAON,MAAA,CAAOO,IAAI;IAChH;IAED,IAAIR,SAAA,CAAUnnB,IAAA,EAAMoe,MAAA,CAAOpe,IAAA,GAAO,KAAKqF,gBAAA,CAAiB8hB,SAAA,CAAUnnB,IAAI;IAEtEmF,sBAAA,CAAuBiZ,MAAA,EAAQ+I,SAAS;IAExC,OAAOnmB,OAAA,CAAQC,OAAA,CAAQmd,MAAM;EAC9B;EAAA;AAAA;AAAA;AAAA;AAAA;EAODgB,SAAStB,SAAA,EAAW;IAClB,MAAMiK,OAAA,GAAU,KAAKrpB,IAAA,CAAKkf,KAAA,CAAME,SAAS;IAEzC,MAAM7X,OAAA,GAAU,EAAE;IAElB,SAASpG,CAAA,GAAI,GAAGia,EAAA,GAAKiO,OAAA,CAAQ/J,MAAA,CAAOle,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MACvDoG,OAAA,CAAQ1H,IAAA,CAAK,KAAKypB,gBAAA,CAAiBD,OAAA,CAAQ/J,MAAA,CAAOne,CAAC,CAAC,CAAC;IACtD;IAED,IAAIkoB,OAAA,CAAQE,mBAAA,KAAwB,QAAW;MAC7ChiB,OAAA,CAAQ1H,IAAA,CAAK,KAAK+G,aAAA,CAAc,YAAYyiB,OAAA,CAAQE,mBAAmB,CAAC;IAC9E,OAAW;MACLhiB,OAAA,CAAQ1H,IAAA,CAAK,IAAI;IAClB;IAED,OAAOyC,OAAA,CAAQ2F,GAAA,CAAIV,OAAO,EAAEP,IAAA,CAAK,UAAUkI,OAAA,EAAS;MAClD,MAAMqa,mBAAA,GAAsBra,OAAA,CAAQE,GAAA,CAAK;MACzC,MAAMoa,UAAA,GAAata,OAAA;MAKnB,MAAMua,KAAA,GAAQ,EAAE;MAChB,MAAMC,YAAA,GAAe,EAAE;MAEvB,SAASvoB,CAAA,GAAI,GAAGia,EAAA,GAAKoO,UAAA,CAAWpoB,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QACnD,MAAMwoB,SAAA,GAAYH,UAAA,CAAWroB,CAAC;QAE9B,IAAIwoB,SAAA,EAAW;UACbF,KAAA,CAAM5pB,IAAA,CAAK8pB,SAAS;UAEpB,MAAMC,GAAA,GAAM,IAAIna,OAAA,CAAS;UAEzB,IAAI8Z,mBAAA,KAAwB,MAAM;YAChCK,GAAA,CAAIhW,SAAA,CAAU2V,mBAAA,CAAoBzhB,KAAA,EAAO3G,CAAA,GAAI,EAAE;UAChD;UAEDuoB,YAAA,CAAa7pB,IAAA,CAAK+pB,GAAG;QAC/B,OAAe;UACLrrB,OAAA,CAAQ2D,IAAA,CAAK,oDAAoDmnB,OAAA,CAAQ/J,MAAA,CAAOne,CAAC,CAAC;QACnF;MACF;MAED,OAAO,IAAI0oB,QAAA,CAASJ,KAAA,EAAOC,YAAY;IAC7C,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD/I,cAAcmJ,cAAA,EAAgB;IAC5B,MAAM9pB,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAM1D,MAAA,GAAS;IAEf,MAAMytB,YAAA,GAAe/pB,IAAA,CAAK4e,UAAA,CAAWkL,cAAc;IACnD,MAAME,aAAA,GAAgBD,YAAA,CAAazoB,IAAA,GAAOyoB,YAAA,CAAazoB,IAAA,GAAO,eAAewoB,cAAA;IAE7E,MAAMG,YAAA,GAAe,EAAE;IACvB,MAAMC,qBAAA,GAAwB,EAAE;IAChC,MAAMC,sBAAA,GAAyB,EAAE;IACjC,MAAMC,eAAA,GAAkB,EAAE;IAC1B,MAAMC,cAAA,GAAiB,EAAE;IAEzB,SAASlpB,CAAA,GAAI,GAAGia,EAAA,GAAK2O,YAAA,CAAaO,QAAA,CAASlpB,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MAC9D,MAAMqS,OAAA,GAAUuW,YAAA,CAAaO,QAAA,CAASnpB,CAAC;MACvC,MAAM4hB,OAAA,GAAUgH,YAAA,CAAa3G,QAAA,CAAS5P,OAAA,CAAQuP,OAAO;MACrD,MAAMpd,MAAA,GAAS6N,OAAA,CAAQ7N,MAAA;MACvB,MAAMrE,IAAA,GAAOqE,MAAA,CAAO4kB,IAAA;MACpB,MAAMC,KAAA,GAAQT,YAAA,CAAaU,UAAA,KAAe,SAAYV,YAAA,CAAaU,UAAA,CAAW1H,OAAA,CAAQyH,KAAK,IAAIzH,OAAA,CAAQyH,KAAA;MACvG,MAAME,MAAA,GAASX,YAAA,CAAaU,UAAA,KAAe,SAAYV,YAAA,CAAaU,UAAA,CAAW1H,OAAA,CAAQ2H,MAAM,IAAI3H,OAAA,CAAQ2H,MAAA;MAEzG,IAAI/kB,MAAA,CAAO4kB,IAAA,KAAS,QAAW;MAE/BN,YAAA,CAAapqB,IAAA,CAAK,KAAK+G,aAAA,CAAc,QAAQtF,IAAI,CAAC;MAClD4oB,qBAAA,CAAsBrqB,IAAA,CAAK,KAAK+G,aAAA,CAAc,YAAY4jB,KAAK,CAAC;MAChEL,sBAAA,CAAuBtqB,IAAA,CAAK,KAAK+G,aAAA,CAAc,YAAY8jB,MAAM,CAAC;MAClEN,eAAA,CAAgBvqB,IAAA,CAAKkjB,OAAO;MAC5BsH,cAAA,CAAexqB,IAAA,CAAK8F,MAAM;IAC3B;IAED,OAAOrD,OAAA,CAAQ2F,GAAA,CAAI,CACjB3F,OAAA,CAAQ2F,GAAA,CAAIgiB,YAAY,GACxB3nB,OAAA,CAAQ2F,GAAA,CAAIiiB,qBAAqB,GACjC5nB,OAAA,CAAQ2F,GAAA,CAAIkiB,sBAAsB,GAClC7nB,OAAA,CAAQ2F,GAAA,CAAImiB,eAAe,GAC3B9nB,OAAA,CAAQ2F,GAAA,CAAIoiB,cAAc,EAC3B,EAAErjB,IAAA,CAAK,UAAUyX,YAAA,EAAc;MAC9B,MAAMla,KAAA,GAAQka,YAAA,CAAa,CAAC;MAC5B,MAAMkM,cAAA,GAAiBlM,YAAA,CAAa,CAAC;MACrC,MAAMmM,eAAA,GAAkBnM,YAAA,CAAa,CAAC;MACtC,MAAM2E,QAAA,GAAW3E,YAAA,CAAa,CAAC;MAC/B,MAAMzD,OAAA,GAAUyD,YAAA,CAAa,CAAC;MAE9B,MAAMoM,MAAA,GAAS,EAAE;MAEjB,SAAS1pB,CAAA,GAAI,GAAGia,EAAA,GAAK7W,KAAA,CAAMnD,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QAC9C,MAAMopB,IAAA,GAAOhmB,KAAA,CAAMpD,CAAC;QACpB,MAAM2pB,aAAA,GAAgBH,cAAA,CAAexpB,CAAC;QACtC,MAAM4pB,cAAA,GAAiBH,eAAA,CAAgBzpB,CAAC;QACxC,MAAM4hB,OAAA,GAAUK,QAAA,CAASjiB,CAAC;QAC1B,MAAMwE,MAAA,GAASqV,OAAA,CAAQ7Z,CAAC;QAExB,IAAIopB,IAAA,KAAS,QAAW;QAExB,IAAIA,IAAA,CAAKS,YAAA,EAAc;UACrBT,IAAA,CAAKS,YAAA,CAAc;QACpB;QAED,MAAMC,aAAA,GAAgB3uB,MAAA,CAAO4uB,sBAAA,CAAuBX,IAAA,EAAMO,aAAA,EAAeC,cAAA,EAAgBhI,OAAA,EAASpd,MAAM;QAExG,IAAIslB,aAAA,EAAe;UACjB,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIF,aAAA,CAAc7pB,MAAA,EAAQ+pB,CAAA,IAAK;YAC7CN,MAAA,CAAOhrB,IAAA,CAAKorB,aAAA,CAAcE,CAAC,CAAC;UAC7B;QACF;MACF;MAED,OAAO,IAAIC,aAAA,CAAcpB,aAAA,EAAe,QAAWa,MAAM;IAC/D,CAAK;EACF;EAEDxc,eAAe7J,SAAA,EAAW;IACxB,MAAMxE,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAM1D,MAAA,GAAS;IACf,MAAMoI,OAAA,GAAU1E,IAAA,CAAKuE,KAAA,CAAMC,SAAS;IAEpC,IAAIE,OAAA,CAAQ4J,IAAA,KAAS,QAAW,OAAO;IAEvC,OAAOhS,MAAA,CAAOsK,aAAA,CAAc,QAAQlC,OAAA,CAAQ4J,IAAI,EAAEtH,IAAA,CAAK,UAAUsH,IAAA,EAAM;MACrE,MAAMic,IAAA,GAAOjuB,MAAA,CAAO2K,WAAA,CAAY3K,MAAA,CAAO8gB,SAAA,EAAW1Y,OAAA,CAAQ4J,IAAA,EAAMA,IAAI;MAGpE,IAAI5J,OAAA,CAAQyU,OAAA,KAAY,QAAW;QACjCoR,IAAA,CAAKc,QAAA,CAAS,UAAUC,CAAA,EAAG;UACzB,IAAI,CAACA,CAAA,CAAEC,MAAA,EAAQ;UAEf,SAASpqB,CAAA,GAAI,GAAGia,EAAA,GAAK1W,OAAA,CAAQyU,OAAA,CAAQ/X,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;YACxDmqB,CAAA,CAAEtP,qBAAA,CAAsB7a,CAAC,IAAIuD,OAAA,CAAQyU,OAAA,CAAQhY,CAAC;UAC/C;QACX,CAAS;MACF;MAED,OAAOopB,IAAA;IACb,CAAK;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAODlK,SAAS7b,SAAA,EAAW;IAClB,MAAMxE,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAM1D,MAAA,GAAS;IAEf,MAAMoI,OAAA,GAAU1E,IAAA,CAAKuE,KAAA,CAAMC,SAAS;IAEpC,MAAMgnB,WAAA,GAAclvB,MAAA,CAAOgtB,gBAAA,CAAiB9kB,SAAS;IAErD,MAAMinB,YAAA,GAAe,EAAE;IACvB,MAAMC,WAAA,GAAchnB,OAAA,CAAQ4K,QAAA,IAAY,EAAE;IAE1C,SAASnO,CAAA,GAAI,GAAGia,EAAA,GAAKsQ,WAAA,CAAYtqB,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MACpDsqB,YAAA,CAAa5rB,IAAA,CAAKvD,MAAA,CAAOsK,aAAA,CAAc,QAAQ8kB,WAAA,CAAYvqB,CAAC,CAAC,CAAC;IAC/D;IAED,MAAMwqB,eAAA,GACJjnB,OAAA,CAAQ8a,IAAA,KAAS,SAAYld,OAAA,CAAQC,OAAA,CAAQ,IAAI,IAAIjG,MAAA,CAAOsK,aAAA,CAAc,QAAQlC,OAAA,CAAQ8a,IAAI;IAEhG,OAAOld,OAAA,CAAQ2F,GAAA,CAAI,CAACujB,WAAA,EAAalpB,OAAA,CAAQ2F,GAAA,CAAIwjB,YAAY,GAAGE,eAAe,CAAC,EAAE3kB,IAAA,CAAK,UAAUkI,OAAA,EAAS;MACpG,MAAMqb,IAAA,GAAOrb,OAAA,CAAQ,CAAC;MACtB,MAAMI,QAAA,GAAWJ,OAAA,CAAQ,CAAC;MAC1B,MAAM0c,QAAA,GAAW1c,OAAA,CAAQ,CAAC;MAE1B,IAAI0c,QAAA,KAAa,MAAM;QAGrBrB,IAAA,CAAKc,QAAA,CAAS,UAAU/c,IAAA,EAAM;UAC5B,IAAI,CAACA,IAAA,CAAKmR,aAAA,EAAe;UAEzBnR,IAAA,CAAKud,IAAA,CAAKD,QAAA,EAAU7O,eAAe;QAC7C,CAAS;MACF;MAED,SAAS5b,CAAA,GAAI,GAAGia,EAAA,GAAK9L,QAAA,CAASlO,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QACjDopB,IAAA,CAAK1nB,GAAA,CAAIyM,QAAA,CAASnO,CAAC,CAAC;MACrB;MAED,OAAOopB,IAAA;IACb,CAAK;EACF;EAAA;EAAA;EAIDjB,iBAAiB9kB,SAAA,EAAW;IAC1B,MAAMxE,IAAA,GAAO,KAAKA,IAAA;IAClB,MAAMC,UAAA,GAAa,KAAKA,UAAA;IACxB,MAAM3D,MAAA,GAAS;IAKf,IAAI,KAAK6gB,SAAA,CAAU3Y,SAAS,MAAM,QAAW;MAC3C,OAAO,KAAK2Y,SAAA,CAAU3Y,SAAS;IAChC;IAED,MAAME,OAAA,GAAU1E,IAAA,CAAKuE,KAAA,CAAMC,SAAS;IAGpC,MAAMsnB,QAAA,GAAWpnB,OAAA,CAAQpD,IAAA,GAAOhF,MAAA,CAAOqK,gBAAA,CAAiBjC,OAAA,CAAQpD,IAAI,IAAI;IAExE,MAAMiG,OAAA,GAAU,EAAE;IAElB,MAAMwkB,WAAA,GAAczvB,MAAA,CAAO2jB,UAAA,CAAW,UAAU3B,GAAA,EAAK;MACnD,OAAOA,GAAA,CAAIjQ,cAAA,IAAkBiQ,GAAA,CAAIjQ,cAAA,CAAe7J,SAAS;IAC/D,CAAK;IAED,IAAIunB,WAAA,EAAa;MACfxkB,OAAA,CAAQ1H,IAAA,CAAKksB,WAAW;IACzB;IAED,IAAIrnB,OAAA,CAAQgb,MAAA,KAAW,QAAW;MAChCnY,OAAA,CAAQ1H,IAAA,CACNvD,MAAA,CAAOsK,aAAA,CAAc,UAAUlC,OAAA,CAAQgb,MAAM,EAAE1Y,IAAA,CAAK,UAAU0Y,MAAA,EAAQ;QACpE,OAAOpjB,MAAA,CAAO2K,WAAA,CAAY3K,MAAA,CAAO+gB,WAAA,EAAa3Y,OAAA,CAAQgb,MAAA,EAAQA,MAAM;MAC9E,CAAS,CACF;IACF;IAEDpjB,MAAA,CACG+hB,UAAA,CAAW,UAAUC,GAAA,EAAK;MACzB,OAAOA,GAAA,CAAIxX,oBAAA,IAAwBwX,GAAA,CAAIxX,oBAAA,CAAqBtC,SAAS;IAC7E,CAAO,EACAwnB,OAAA,CAAQ,UAAUhJ,OAAA,EAAS;MAC1Bzb,OAAA,CAAQ1H,IAAA,CAAKmjB,OAAO;IAC5B,CAAO;IAEH,KAAK7F,SAAA,CAAU3Y,SAAS,IAAIlC,OAAA,CAAQ2F,GAAA,CAAIV,OAAO,EAAEP,IAAA,CAAK,UAAUtE,OAAA,EAAS;MACvE,IAAI6nB,IAAA;MAGJ,IAAI7lB,OAAA,CAAQ6a,MAAA,KAAW,MAAM;QAC3BgL,IAAA,GAAO,IAAI0B,IAAA,CAAM;MACzB,WAAiBvpB,OAAA,CAAQtB,MAAA,GAAS,GAAG;QAC7BmpB,IAAA,GAAO,IAAIhC,KAAA,CAAO;MAC1B,WAAiB7lB,OAAA,CAAQtB,MAAA,KAAW,GAAG;QAC/BmpB,IAAA,GAAO7nB,OAAA,CAAQ,CAAC;MACxB,OAAa;QACL6nB,IAAA,GAAO,IAAIvZ,QAAA,CAAU;MACtB;MAED,IAAIuZ,IAAA,KAAS7nB,OAAA,CAAQ,CAAC,GAAG;QACvB,SAASvB,CAAA,GAAI,GAAGia,EAAA,GAAK1Y,OAAA,CAAQtB,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;UAChDopB,IAAA,CAAK1nB,GAAA,CAAIH,OAAA,CAAQvB,CAAC,CAAC;QACpB;MACF;MAED,IAAIuD,OAAA,CAAQpD,IAAA,EAAM;QAChBipB,IAAA,CAAK9P,QAAA,CAASnZ,IAAA,GAAOoD,OAAA,CAAQpD,IAAA;QAC7BipB,IAAA,CAAKjpB,IAAA,GAAOwqB,QAAA;MACb;MAEDrlB,sBAAA,CAAuB8jB,IAAA,EAAM7lB,OAAO;MAEpC,IAAIA,OAAA,CAAQzE,UAAA,EAAYqa,8BAAA,CAA+Bra,UAAA,EAAYsqB,IAAA,EAAM7lB,OAAO;MAEhF,IAAIA,OAAA,CAAQwnB,MAAA,KAAW,QAAW;QAChC,MAAMA,MAAA,GAAS,IAAIzc,OAAA,CAAS;QAC5Byc,MAAA,CAAOtY,SAAA,CAAUlP,OAAA,CAAQwnB,MAAM;QAC/B3B,IAAA,CAAK4B,YAAA,CAAaD,MAAM;MAChC,OAAa;QACL,IAAIxnB,OAAA,CAAQwU,WAAA,KAAgB,QAAW;UACrCqR,IAAA,CAAK3kB,QAAA,CAASgO,SAAA,CAAUlP,OAAA,CAAQwU,WAAW;QAC5C;QAED,IAAIxU,OAAA,CAAQgP,QAAA,KAAa,QAAW;UAClC6W,IAAA,CAAK6B,UAAA,CAAWxY,SAAA,CAAUlP,OAAA,CAAQgP,QAAQ;QAC3C;QAED,IAAIhP,OAAA,CAAQsE,KAAA,KAAU,QAAW;UAC/BuhB,IAAA,CAAKvhB,KAAA,CAAM4K,SAAA,CAAUlP,OAAA,CAAQsE,KAAK;QACnC;MACF;MAED,IAAI,CAAC1M,MAAA,CAAO0gB,YAAA,CAAaqP,GAAA,CAAI9B,IAAI,GAAG;QAClCjuB,MAAA,CAAO0gB,YAAA,CAAanX,GAAA,CAAI0kB,IAAA,EAAM,EAAE;MACjC;MAEDjuB,MAAA,CAAO0gB,YAAA,CAAara,GAAA,CAAI4nB,IAAI,EAAEhmB,KAAA,GAAQC,SAAA;MAEtC,OAAO+lB,IAAA;IACb,CAAK;IAED,OAAO,KAAKpN,SAAA,CAAU3Y,SAAS;EAChC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD4b,UAAUkM,UAAA,EAAY;IACpB,MAAMrsB,UAAA,GAAa,KAAKA,UAAA;IACxB,MAAMssB,QAAA,GAAW,KAAKvsB,IAAA,CAAK2e,MAAA,CAAO2N,UAAU;IAC5C,MAAMhwB,MAAA,GAAS;IAIf,MAAMoiB,KAAA,GAAQ,IAAI6J,KAAA,CAAO;IACzB,IAAIgE,QAAA,CAASjrB,IAAA,EAAMod,KAAA,CAAMpd,IAAA,GAAOhF,MAAA,CAAOqK,gBAAA,CAAiB4lB,QAAA,CAASjrB,IAAI;IAErEmF,sBAAA,CAAuBiY,KAAA,EAAO6N,QAAQ;IAEtC,IAAIA,QAAA,CAAStsB,UAAA,EAAYqa,8BAAA,CAA+Bra,UAAA,EAAYye,KAAA,EAAO6N,QAAQ;IAEnF,MAAMC,OAAA,GAAUD,QAAA,CAAShoB,KAAA,IAAS,EAAE;IAEpC,MAAMgD,OAAA,GAAU,EAAE;IAElB,SAASpG,CAAA,GAAI,GAAGia,EAAA,GAAKoR,OAAA,CAAQprB,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MAChDoG,OAAA,CAAQ1H,IAAA,CAAKvD,MAAA,CAAOsK,aAAA,CAAc,QAAQ4lB,OAAA,CAAQrrB,CAAC,CAAC,CAAC;IACtD;IAED,OAAOmB,OAAA,CAAQ2F,GAAA,CAAIV,OAAO,EAAEP,IAAA,CAAK,UAAUzC,KAAA,EAAO;MAChD,SAASpD,CAAA,GAAI,GAAGia,EAAA,GAAK7W,KAAA,CAAMnD,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;QAC9Cud,KAAA,CAAM7b,GAAA,CAAI0B,KAAA,CAAMpD,CAAC,CAAC;MACnB;MAID,MAAMsrB,kBAAA,GAAsBlC,IAAA,IAAS;QACnC,MAAMmC,mBAAA,GAAsB,mBAAIzP,GAAA,CAAK;QAErC,WAAW,CAACra,GAAA,EAAK+pB,KAAK,KAAKrwB,MAAA,CAAO0gB,YAAA,EAAc;UAC9C,IAAIpa,GAAA,YAAewiB,QAAA,IAAYxiB,GAAA,YAAeyhB,OAAA,EAAS;YACrDqI,mBAAA,CAAoB7mB,GAAA,CAAIjD,GAAA,EAAK+pB,KAAK;UACnC;QACF;QAEDpC,IAAA,CAAKc,QAAA,CAAUuB,KAAA,IAAS;UACtB,MAAM9M,QAAA,GAAWxjB,MAAA,CAAO0gB,YAAA,CAAara,GAAA,CAAIiqB,KAAI;UAE7C,IAAI9M,QAAA,IAAY,MAAM;YACpB4M,mBAAA,CAAoB7mB,GAAA,CAAI+mB,KAAA,EAAM9M,QAAQ;UACvC;QACX,CAAS;QAED,OAAO4M,mBAAA;MACR;MAEDpwB,MAAA,CAAO0gB,YAAA,GAAeyP,kBAAA,CAAmB/N,KAAK;MAE9C,OAAOA,KAAA;IACb,CAAK;EACF;EAEDwM,uBAAuBX,IAAA,EAAMO,aAAA,EAAeC,cAAA,EAAgBhI,OAAA,EAASpd,MAAA,EAAQ;IAC3E,MAAMklB,MAAA,GAAS,EAAE;IAEjB,MAAMgC,UAAA,GAAatC,IAAA,CAAKjpB,IAAA,GAAOipB,IAAA,CAAKjpB,IAAA,GAAOipB,IAAA,CAAKtF,IAAA;IAChD,MAAMhJ,WAAA,GAAc,EAAE;IAEtB,IAAIhD,eAAA,CAAgBtT,MAAA,CAAO5H,IAAI,MAAMkb,eAAA,CAAgBE,OAAA,EAAS;MAC5DoR,IAAA,CAAKc,QAAA,CAAS,UAAUvoB,MAAA,EAAQ;QAC9B,IAAIA,MAAA,CAAOkZ,qBAAA,EAAuB;UAChCC,WAAA,CAAYpc,IAAA,CAAKiD,MAAA,CAAOxB,IAAA,GAAOwB,MAAA,CAAOxB,IAAA,GAAOwB,MAAA,CAAOmiB,IAAI;QACzD;MACT,CAAO;IACP,OAAW;MACLhJ,WAAA,CAAYpc,IAAA,CAAKgtB,UAAU;IAC5B;IAED,IAAIC,kBAAA;IAEJ,QAAQ7T,eAAA,CAAgBtT,MAAA,CAAO5H,IAAI;MACjC,KAAKkb,eAAA,CAAgBE,OAAA;QACnB2T,kBAAA,GAAqBC,mBAAA;QACrB;MAEF,KAAK9T,eAAA,CAAgBvF,QAAA;QACnBoZ,kBAAA,GAAqBE,uBAAA;QACrB;MAEF,KAAK/T,eAAA,CAAgBrT,QAAA;MACrB,KAAKqT,eAAA,CAAgBjQ,KAAA;QACnB8jB,kBAAA,GAAqBG,mBAAA;QACrB;MAEF;QACE,QAAQlC,cAAA,CAAela,QAAA;UACrB,KAAK;YACHic,kBAAA,GAAqBC,mBAAA;YACrB;UACF,KAAK;UACL,KAAK;UACL;YACED,kBAAA,GAAqBG,mBAAA;YACrB;QACH;QAED;IACH;IAED,MAAMC,aAAA,GAAgBnK,OAAA,CAAQmK,aAAA,KAAkB,SAAY9T,aAAA,CAAc2J,OAAA,CAAQmK,aAAa,IAAI5T,iBAAA;IAEnG,MAAM6T,WAAA,GAAc,KAAKC,qBAAA,CAAsBrC,cAAc;IAE7D,SAASsC,CAAA,GAAI,GAAGC,EAAA,GAAKrR,WAAA,CAAY7a,MAAA,EAAQisB,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACpD,MAAME,KAAA,GAAQ,IAAIT,kBAAA,CAChB7Q,WAAA,CAAYoR,CAAC,IAAI,MAAMpU,eAAA,CAAgBtT,MAAA,CAAO5H,IAAI,GAClD+sB,aAAA,CAAchjB,KAAA,EACdqlB,WAAA,EACAD,aACD;MAGD,IAAInK,OAAA,CAAQmK,aAAA,KAAkB,eAAe;QAC3C,KAAKM,kCAAA,CAAmCD,KAAK;MAC9C;MAED1C,MAAA,CAAOhrB,IAAA,CAAK0tB,KAAK;IAClB;IAED,OAAO1C,MAAA;EACR;EAEDuC,sBAAsBne,QAAA,EAAU;IAC9B,IAAIke,WAAA,GAAcle,QAAA,CAASnH,KAAA;IAE3B,IAAImH,QAAA,CAAS6B,UAAA,EAAY;MACvB,MAAM9H,KAAA,GAAQ4T,2BAAA,CAA4BuQ,WAAA,CAAYpxB,WAAW;MACjE,MAAM0xB,MAAA,GAAS,IAAItW,YAAA,CAAagW,WAAA,CAAY/rB,MAAM;MAElD,SAASisB,CAAA,GAAI,GAAGC,EAAA,GAAKH,WAAA,CAAY/rB,MAAA,EAAQisB,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACpDI,MAAA,CAAOJ,CAAC,IAAIF,WAAA,CAAYE,CAAC,IAAIrkB,KAAA;MAC9B;MAEDmkB,WAAA,GAAcM,MAAA;IACf;IAED,OAAON,WAAA;EACR;EAEDK,mCAAmCD,KAAA,EAAO;IACxCA,KAAA,CAAMG,iBAAA,GAAoB,SAASC,wCAAwCxf,MAAA,EAAQ;MAKjF,MAAMyf,eAAA,GACJ,gBAAgBZ,uBAAA,GAA0BnX,oCAAA,GAAuC9B,0BAAA;MAEnF,OAAO,IAAI6Z,eAAA,CAAgB,KAAKC,KAAA,EAAO,KAAKvZ,MAAA,EAAQ,KAAKwZ,YAAA,CAAY,IAAK,GAAG3f,MAAM;IACpF;IAGDof,KAAA,CAAMG,iBAAA,CAAkBK,yCAAA,GAA4C;EACrE;AACH;AAOA,SAASC,cAAc/d,QAAA,EAAUmM,YAAA,EAAc9f,MAAA,EAAQ;EACrD,MAAM0S,UAAA,GAAaoN,YAAA,CAAapN,UAAA;EAEhC,MAAMif,GAAA,GAAM,IAAIC,IAAA,CAAM;EAEtB,IAAIlf,UAAA,CAAWuJ,QAAA,KAAa,QAAW;IACrC,MAAMtJ,QAAA,GAAW3S,MAAA,CAAO0D,IAAA,CAAK+S,SAAA,CAAU/D,UAAA,CAAWuJ,QAAQ;IAE1D,MAAM4V,GAAA,GAAMlf,QAAA,CAASkf,GAAA;IACrB,MAAMC,GAAA,GAAMnf,QAAA,CAASmf,GAAA;IAIrB,IAAID,GAAA,KAAQ,UAAaC,GAAA,KAAQ,QAAW;MAC1CH,GAAA,CAAIpoB,GAAA,CAAI,IAAI8J,OAAA,CAAQwe,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,CAAC,GAAG,IAAIxe,OAAA,CAAQye,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,CAAC,CAAC;MAEhF,IAAInf,QAAA,CAAS6B,UAAA,EAAY;QACvB,MAAMud,QAAA,GAAWzR,2BAAA,CAA4B3J,qBAAA,CAAsBhE,QAAA,CAAS+D,aAAa,CAAC;QAC1Fib,GAAA,CAAIE,GAAA,CAAIG,cAAA,CAAeD,QAAQ;QAC/BJ,GAAA,CAAIG,GAAA,CAAIE,cAAA,CAAeD,QAAQ;MAChC;IACP,OAAW;MACL9vB,OAAA,CAAQ2D,IAAA,CAAK,qEAAqE;MAElF;IACD;EACL,OAAS;IACL;EACD;EAED,MAAM8Y,OAAA,GAAUoB,YAAA,CAAapB,OAAA;EAE7B,IAAIA,OAAA,KAAY,QAAW;IACzB,MAAMuT,eAAA,GAAkB,IAAI5e,OAAA,CAAS;IACrC,MAAM6e,MAAA,GAAS,IAAI7e,OAAA,CAAS;IAE5B,SAASxO,CAAA,GAAI,GAAGia,EAAA,GAAKJ,OAAA,CAAQ5Z,MAAA,EAAQD,CAAA,GAAIia,EAAA,EAAIja,CAAA,IAAK;MAChD,MAAMwE,MAAA,GAASqV,OAAA,CAAQ7Z,CAAC;MAExB,IAAIwE,MAAA,CAAO4S,QAAA,KAAa,QAAW;QACjC,MAAMtJ,QAAA,GAAW3S,MAAA,CAAO0D,IAAA,CAAK+S,SAAA,CAAUpN,MAAA,CAAO4S,QAAQ;QACtD,MAAM4V,GAAA,GAAMlf,QAAA,CAASkf,GAAA;QACrB,MAAMC,GAAA,GAAMnf,QAAA,CAASmf,GAAA;QAIrB,IAAID,GAAA,KAAQ,UAAaC,GAAA,KAAQ,QAAW;UAE1CI,MAAA,CAAO/L,IAAA,CAAKrc,IAAA,CAAKgoB,GAAA,CAAIhoB,IAAA,CAAKqoB,GAAA,CAAIN,GAAA,CAAI,CAAC,CAAC,GAAG/nB,IAAA,CAAKqoB,GAAA,CAAIL,GAAA,CAAI,CAAC,CAAC,CAAC,CAAC;UACxDI,MAAA,CAAO9L,IAAA,CAAKtc,IAAA,CAAKgoB,GAAA,CAAIhoB,IAAA,CAAKqoB,GAAA,CAAIN,GAAA,CAAI,CAAC,CAAC,GAAG/nB,IAAA,CAAKqoB,GAAA,CAAIL,GAAA,CAAI,CAAC,CAAC,CAAC,CAAC;UACxDI,MAAA,CAAO7L,IAAA,CAAKvc,IAAA,CAAKgoB,GAAA,CAAIhoB,IAAA,CAAKqoB,GAAA,CAAIN,GAAA,CAAI,CAAC,CAAC,GAAG/nB,IAAA,CAAKqoB,GAAA,CAAIL,GAAA,CAAI,CAAC,CAAC,CAAC,CAAC;UAExD,IAAInf,QAAA,CAAS6B,UAAA,EAAY;YACvB,MAAMud,QAAA,GAAWzR,2BAAA,CAA4B3J,qBAAA,CAAsBhE,QAAA,CAAS+D,aAAa,CAAC;YAC1Fwb,MAAA,CAAOF,cAAA,CAAeD,QAAQ;UAC/B;UAMDE,eAAA,CAAgBH,GAAA,CAAII,MAAM;QACpC,OAAe;UACLjwB,OAAA,CAAQ2D,IAAA,CAAK,qEAAqE;QACnF;MACF;IACF;IAGD+rB,GAAA,CAAIS,cAAA,CAAeH,eAAe;EACnC;EAEDte,QAAA,CAAS0e,WAAA,GAAcV,GAAA;EAEvB,MAAMW,MAAA,GAAS,IAAIC,MAAA,CAAQ;EAE3BZ,GAAA,CAAIa,SAAA,CAAUF,MAAA,CAAOG,MAAM;EAC3BH,MAAA,CAAOI,MAAA,GAASf,GAAA,CAAIE,GAAA,CAAIc,UAAA,CAAWhB,GAAA,CAAIG,GAAG,IAAI;EAE9Cne,QAAA,CAASif,cAAA,GAAiBN,MAAA;AAC5B;AAQA,SAAStH,uBAAuBrX,QAAA,EAAUmM,YAAA,EAAc9f,MAAA,EAAQ;EAC9D,MAAM0S,UAAA,GAAaoN,YAAA,CAAapN,UAAA;EAEhC,MAAMzH,OAAA,GAAU,EAAE;EAElB,SAAS4nB,wBAAwB/N,aAAA,EAAe3Q,aAAA,EAAe;IAC7D,OAAOnU,MAAA,CAAOsK,aAAA,CAAc,YAAYwa,aAAa,EAAEpa,IAAA,CAAK,UAAUiI,QAAA,EAAU;MAC9EgB,QAAA,CAASc,YAAA,CAAaN,aAAA,EAAexB,QAAQ;IACnD,CAAK;EACF;EAED,WAAWmgB,iBAAA,IAAqBpgB,UAAA,EAAY;IAC1C,MAAM2D,kBAAA,GAAqBC,UAAA,CAAWwc,iBAAiB,KAAKA,iBAAA,CAAkBvc,WAAA,CAAa;IAG3F,IAAIF,kBAAA,IAAsB1C,QAAA,CAASjB,UAAA,EAAY;IAE/CzH,OAAA,CAAQ1H,IAAA,CAAKsvB,uBAAA,CAAwBngB,UAAA,CAAWogB,iBAAiB,GAAGzc,kBAAkB,CAAC;EACxF;EAED,IAAIyJ,YAAA,CAAaG,OAAA,KAAY,UAAa,CAACtM,QAAA,CAASpJ,KAAA,EAAO;IACzD,MAAMoI,QAAA,GAAW3S,MAAA,CAAOsK,aAAA,CAAc,YAAYwV,YAAA,CAAaG,OAAO,EAAEvV,IAAA,CAAK,UAAUqoB,SAAA,EAAU;MAC/Fpf,QAAA,CAASqf,QAAA,CAASD,SAAQ;IAChC,CAAK;IAED9nB,OAAA,CAAQ1H,IAAA,CAAKoP,QAAQ;EACtB;EAEDxI,sBAAA,CAAuBwJ,QAAA,EAAUmM,YAAY;EAE7C4R,aAAA,CAAc/d,QAAA,EAAUmM,YAAA,EAAc9f,MAAM;EAE5C,OAAOgG,OAAA,CAAQ2F,GAAA,CAAIV,OAAO,EAAEP,IAAA,CAAK,YAAY;IAC3C,OAAOoV,YAAA,CAAapB,OAAA,KAAY,SAAYD,eAAA,CAAgB9K,QAAA,EAAUmM,YAAA,CAAapB,OAAA,EAAS1e,MAAM,IAAI2T,QAAA;EAC1G,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}