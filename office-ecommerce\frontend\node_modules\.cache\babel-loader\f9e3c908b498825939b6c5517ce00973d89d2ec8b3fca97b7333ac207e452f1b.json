{"ast": null, "code": "import { <PERSON>ufferGeometry, Vector3, Matrix4, Float32BufferAttribute } from \"three\";\nclass DecalGeometry extends BufferGeometry {\n  constructor(mesh, position, orientation, size) {\n    super();\n    const vertices = [];\n    const normals = [];\n    const uvs = [];\n    const plane = new Vector3();\n    const projectorMatrix = new Matrix4();\n    projectorMatrix.makeRotationFromEuler(orientation);\n    projectorMatrix.setPosition(position);\n    const projectorMatrixInverse = new Matrix4();\n    projectorMatrixInverse.copy(projectorMatrix).invert();\n    generate();\n    this.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n    this.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n    this.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n    function generate() {\n      let i;\n      let decalVertices = [];\n      const vertex = new Vector3();\n      const normal = new Vector3();\n      if (mesh.geometry.isGeometry === true) {\n        console.error(\"THREE.DecalGeometry no longer supports THREE.Geometry. Use BufferGeometry instead.\");\n        return;\n      }\n      const geometry = mesh.geometry;\n      const positionAttribute = geometry.attributes.position;\n      const normalAttribute = geometry.attributes.normal;\n      if (geometry.index !== null) {\n        const index = geometry.index;\n        for (i = 0; i < index.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, index.getX(i));\n          normal.fromBufferAttribute(normalAttribute, index.getX(i));\n          pushDecalVertex(decalVertices, vertex, normal);\n        }\n      } else {\n        for (i = 0; i < positionAttribute.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, i);\n          normal.fromBufferAttribute(normalAttribute, i);\n          pushDecalVertex(decalVertices, vertex, normal);\n        }\n      }\n      decalVertices = clipGeometry(decalVertices, plane.set(1, 0, 0));\n      decalVertices = clipGeometry(decalVertices, plane.set(-1, 0, 0));\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 1, 0));\n      decalVertices = clipGeometry(decalVertices, plane.set(0, -1, 0));\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, 1));\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, -1));\n      for (i = 0; i < decalVertices.length; i++) {\n        const decalVertex = decalVertices[i];\n        uvs.push(0.5 + decalVertex.position.x / size.x, 0.5 + decalVertex.position.y / size.y);\n        decalVertex.position.applyMatrix4(projectorMatrix);\n        vertices.push(decalVertex.position.x, decalVertex.position.y, decalVertex.position.z);\n        normals.push(decalVertex.normal.x, decalVertex.normal.y, decalVertex.normal.z);\n      }\n    }\n    function pushDecalVertex(decalVertices, vertex, normal) {\n      vertex.applyMatrix4(mesh.matrixWorld);\n      vertex.applyMatrix4(projectorMatrixInverse);\n      normal.transformDirection(mesh.matrixWorld);\n      decalVertices.push(new DecalVertex(vertex.clone(), normal.clone()));\n    }\n    function clipGeometry(inVertices, plane2) {\n      const outVertices = [];\n      const s = 0.5 * Math.abs(size.dot(plane2));\n      for (let i = 0; i < inVertices.length; i += 3) {\n        let v1Out,\n          v2Out,\n          v3Out,\n          total = 0;\n        let nV1, nV2, nV3, nV4;\n        const d1 = inVertices[i + 0].position.dot(plane2) - s;\n        const d2 = inVertices[i + 1].position.dot(plane2) - s;\n        const d3 = inVertices[i + 2].position.dot(plane2) - s;\n        v1Out = d1 > 0;\n        v2Out = d2 > 0;\n        v3Out = d3 > 0;\n        total = (v1Out ? 1 : 0) + (v2Out ? 1 : 0) + (v3Out ? 1 : 0);\n        switch (total) {\n          case 0:\n            {\n              outVertices.push(inVertices[i]);\n              outVertices.push(inVertices[i + 1]);\n              outVertices.push(inVertices[i + 2]);\n              break;\n            }\n          case 1:\n            {\n              if (v1Out) {\n                nV1 = inVertices[i + 1];\n                nV2 = inVertices[i + 2];\n                nV3 = clip(inVertices[i], nV1, plane2, s);\n                nV4 = clip(inVertices[i], nV2, plane2, s);\n              }\n              if (v2Out) {\n                nV1 = inVertices[i];\n                nV2 = inVertices[i + 2];\n                nV3 = clip(inVertices[i + 1], nV1, plane2, s);\n                nV4 = clip(inVertices[i + 1], nV2, plane2, s);\n                outVertices.push(nV3);\n                outVertices.push(nV2.clone());\n                outVertices.push(nV1.clone());\n                outVertices.push(nV2.clone());\n                outVertices.push(nV3.clone());\n                outVertices.push(nV4);\n                break;\n              }\n              if (v3Out) {\n                nV1 = inVertices[i];\n                nV2 = inVertices[i + 1];\n                nV3 = clip(inVertices[i + 2], nV1, plane2, s);\n                nV4 = clip(inVertices[i + 2], nV2, plane2, s);\n              }\n              outVertices.push(nV1.clone());\n              outVertices.push(nV2.clone());\n              outVertices.push(nV3);\n              outVertices.push(nV4);\n              outVertices.push(nV3.clone());\n              outVertices.push(nV2.clone());\n              break;\n            }\n          case 2:\n            {\n              if (!v1Out) {\n                nV1 = inVertices[i].clone();\n                nV2 = clip(nV1, inVertices[i + 1], plane2, s);\n                nV3 = clip(nV1, inVertices[i + 2], plane2, s);\n                outVertices.push(nV1);\n                outVertices.push(nV2);\n                outVertices.push(nV3);\n              }\n              if (!v2Out) {\n                nV1 = inVertices[i + 1].clone();\n                nV2 = clip(nV1, inVertices[i + 2], plane2, s);\n                nV3 = clip(nV1, inVertices[i], plane2, s);\n                outVertices.push(nV1);\n                outVertices.push(nV2);\n                outVertices.push(nV3);\n              }\n              if (!v3Out) {\n                nV1 = inVertices[i + 2].clone();\n                nV2 = clip(nV1, inVertices[i], plane2, s);\n                nV3 = clip(nV1, inVertices[i + 1], plane2, s);\n                outVertices.push(nV1);\n                outVertices.push(nV2);\n                outVertices.push(nV3);\n              }\n              break;\n            }\n        }\n      }\n      return outVertices;\n    }\n    function clip(v0, v1, p, s) {\n      const d0 = v0.position.dot(p) - s;\n      const d1 = v1.position.dot(p) - s;\n      const s0 = d0 / (d0 - d1);\n      const v = new DecalVertex(new Vector3(v0.position.x + s0 * (v1.position.x - v0.position.x), v0.position.y + s0 * (v1.position.y - v0.position.y), v0.position.z + s0 * (v1.position.z - v0.position.z)), new Vector3(v0.normal.x + s0 * (v1.normal.x - v0.normal.x), v0.normal.y + s0 * (v1.normal.y - v0.normal.y), v0.normal.z + s0 * (v1.normal.z - v0.normal.z)));\n      return v;\n    }\n  }\n}\nclass DecalVertex {\n  constructor(position, normal) {\n    this.position = position;\n    this.normal = normal;\n  }\n  clone() {\n    return new this.constructor(this.position.clone(), this.normal.clone());\n  }\n}\nexport { DecalGeometry, DecalVertex };", "map": {"version": 3, "names": ["DecalGeometry", "BufferGeometry", "constructor", "mesh", "position", "orientation", "size", "vertices", "normals", "uvs", "plane", "Vector3", "projectorMatrix", "Matrix4", "makeRotationFromEuler", "setPosition", "projectorMatrixInverse", "copy", "invert", "generate", "setAttribute", "Float32BufferAttribute", "i", "decalVertices", "vertex", "normal", "geometry", "isGeometry", "console", "error", "positionAttribute", "attributes", "normalAttribute", "index", "count", "fromBufferAttribute", "getX", "pushDecalVertex", "clipGeometry", "set", "length", "decalVertex", "push", "x", "y", "applyMatrix4", "z", "matrixWorld", "transformDirection", "DecalVertex", "clone", "inVertices", "plane2", "outVertices", "s", "Math", "abs", "dot", "v1Out", "v2Out", "v3Out", "total", "nV1", "nV2", "nV3", "nV4", "d1", "d2", "d3", "clip", "v0", "v1", "p", "d0", "s0", "v"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\geometries\\DecalGeometry.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute, Matrix4, Vector3 } from 'three'\n\n/**\n * You can use this geometry to create a decal mesh, that serves different kinds of purposes.\n * e.g. adding unique details to models, performing dynamic visual environmental changes or covering seams.\n *\n * Constructor parameter:\n *\n * mesh — Any mesh object\n * position — Position of the decal projector\n * orientation — Orientation of the decal projector\n * size — Size of the decal projector\n *\n * reference: http://blog.wolfire.com/2009/06/how-to-project-decals/\n *\n */\n\nclass DecalGeometry extends BufferGeometry {\n  constructor(mesh, position, orientation, size) {\n    super()\n\n    // buffers\n\n    const vertices = []\n    const normals = []\n    const uvs = []\n\n    // helpers\n\n    const plane = new Vector3()\n\n    // this matrix represents the transformation of the decal projector\n\n    const projectorMatrix = new Matrix4()\n    projectorMatrix.makeRotationFromEuler(orientation)\n    projectorMatrix.setPosition(position)\n\n    const projectorMatrixInverse = new Matrix4()\n    projectorMatrixInverse.copy(projectorMatrix).invert()\n\n    // generate buffers\n\n    generate()\n\n    // build geometry\n\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n\n    function generate() {\n      let i\n\n      let decalVertices = []\n\n      const vertex = new Vector3()\n      const normal = new Vector3()\n\n      // handle different geometry types\n\n      if (mesh.geometry.isGeometry === true) {\n        console.error('THREE.DecalGeometry no longer supports THREE.Geometry. Use BufferGeometry instead.')\n        return\n      }\n\n      const geometry = mesh.geometry\n\n      const positionAttribute = geometry.attributes.position\n      const normalAttribute = geometry.attributes.normal\n\n      // first, create an array of 'DecalVertex' objects\n      // three consecutive 'DecalVertex' objects represent a single face\n      //\n      // this data structure will be later used to perform the clipping\n\n      if (geometry.index !== null) {\n        // indexed BufferGeometry\n\n        const index = geometry.index\n\n        for (i = 0; i < index.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, index.getX(i))\n          normal.fromBufferAttribute(normalAttribute, index.getX(i))\n\n          pushDecalVertex(decalVertices, vertex, normal)\n        }\n      } else {\n        // non-indexed BufferGeometry\n\n        for (i = 0; i < positionAttribute.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, i)\n          normal.fromBufferAttribute(normalAttribute, i)\n\n          pushDecalVertex(decalVertices, vertex, normal)\n        }\n      }\n\n      // second, clip the geometry so that it doesn't extend out from the projector\n\n      decalVertices = clipGeometry(decalVertices, plane.set(1, 0, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(-1, 0, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 1, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, -1, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, 1))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, -1))\n\n      // third, generate final vertices, normals and uvs\n\n      for (i = 0; i < decalVertices.length; i++) {\n        const decalVertex = decalVertices[i]\n\n        // create texture coordinates (we are still in projector space)\n\n        uvs.push(0.5 + decalVertex.position.x / size.x, 0.5 + decalVertex.position.y / size.y)\n\n        // transform the vertex back to world space\n\n        decalVertex.position.applyMatrix4(projectorMatrix)\n\n        // now create vertex and normal buffer data\n\n        vertices.push(decalVertex.position.x, decalVertex.position.y, decalVertex.position.z)\n        normals.push(decalVertex.normal.x, decalVertex.normal.y, decalVertex.normal.z)\n      }\n    }\n\n    function pushDecalVertex(decalVertices, vertex, normal) {\n      // transform the vertex to world space, then to projector space\n\n      vertex.applyMatrix4(mesh.matrixWorld)\n      vertex.applyMatrix4(projectorMatrixInverse)\n\n      normal.transformDirection(mesh.matrixWorld)\n\n      decalVertices.push(new DecalVertex(vertex.clone(), normal.clone()))\n    }\n\n    function clipGeometry(inVertices, plane) {\n      const outVertices = []\n\n      const s = 0.5 * Math.abs(size.dot(plane))\n\n      // a single iteration clips one face,\n      // which consists of three consecutive 'DecalVertex' objects\n\n      for (let i = 0; i < inVertices.length; i += 3) {\n        let v1Out,\n          v2Out,\n          v3Out,\n          total = 0\n        let nV1, nV2, nV3, nV4\n\n        const d1 = inVertices[i + 0].position.dot(plane) - s\n        const d2 = inVertices[i + 1].position.dot(plane) - s\n        const d3 = inVertices[i + 2].position.dot(plane) - s\n\n        v1Out = d1 > 0\n        v2Out = d2 > 0\n        v3Out = d3 > 0\n\n        // calculate, how many vertices of the face lie outside of the clipping plane\n\n        total = (v1Out ? 1 : 0) + (v2Out ? 1 : 0) + (v3Out ? 1 : 0)\n\n        switch (total) {\n          case 0: {\n            // the entire face lies inside of the plane, no clipping needed\n\n            outVertices.push(inVertices[i])\n            outVertices.push(inVertices[i + 1])\n            outVertices.push(inVertices[i + 2])\n            break\n          }\n\n          case 1: {\n            // one vertex lies outside of the plane, perform clipping\n\n            if (v1Out) {\n              nV1 = inVertices[i + 1]\n              nV2 = inVertices[i + 2]\n              nV3 = clip(inVertices[i], nV1, plane, s)\n              nV4 = clip(inVertices[i], nV2, plane, s)\n            }\n\n            if (v2Out) {\n              nV1 = inVertices[i]\n              nV2 = inVertices[i + 2]\n              nV3 = clip(inVertices[i + 1], nV1, plane, s)\n              nV4 = clip(inVertices[i + 1], nV2, plane, s)\n\n              outVertices.push(nV3)\n              outVertices.push(nV2.clone())\n              outVertices.push(nV1.clone())\n\n              outVertices.push(nV2.clone())\n              outVertices.push(nV3.clone())\n              outVertices.push(nV4)\n              break\n            }\n\n            if (v3Out) {\n              nV1 = inVertices[i]\n              nV2 = inVertices[i + 1]\n              nV3 = clip(inVertices[i + 2], nV1, plane, s)\n              nV4 = clip(inVertices[i + 2], nV2, plane, s)\n            }\n\n            outVertices.push(nV1.clone())\n            outVertices.push(nV2.clone())\n            outVertices.push(nV3)\n\n            outVertices.push(nV4)\n            outVertices.push(nV3.clone())\n            outVertices.push(nV2.clone())\n\n            break\n          }\n\n          case 2: {\n            // two vertices lies outside of the plane, perform clipping\n\n            if (!v1Out) {\n              nV1 = inVertices[i].clone()\n              nV2 = clip(nV1, inVertices[i + 1], plane, s)\n              nV3 = clip(nV1, inVertices[i + 2], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            if (!v2Out) {\n              nV1 = inVertices[i + 1].clone()\n              nV2 = clip(nV1, inVertices[i + 2], plane, s)\n              nV3 = clip(nV1, inVertices[i], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            if (!v3Out) {\n              nV1 = inVertices[i + 2].clone()\n              nV2 = clip(nV1, inVertices[i], plane, s)\n              nV3 = clip(nV1, inVertices[i + 1], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            break\n          }\n\n          case 3: {\n            // the entire face lies outside of the plane, so let's discard the corresponding vertices\n\n            break\n          }\n        }\n      }\n\n      return outVertices\n    }\n\n    function clip(v0, v1, p, s) {\n      const d0 = v0.position.dot(p) - s\n      const d1 = v1.position.dot(p) - s\n\n      const s0 = d0 / (d0 - d1)\n\n      const v = new DecalVertex(\n        new Vector3(\n          v0.position.x + s0 * (v1.position.x - v0.position.x),\n          v0.position.y + s0 * (v1.position.y - v0.position.y),\n          v0.position.z + s0 * (v1.position.z - v0.position.z),\n        ),\n        new Vector3(\n          v0.normal.x + s0 * (v1.normal.x - v0.normal.x),\n          v0.normal.y + s0 * (v1.normal.y - v0.normal.y),\n          v0.normal.z + s0 * (v1.normal.z - v0.normal.z),\n        ),\n      )\n\n      // need to clip more values (texture coordinates)? do it this way:\n      // intersectpoint.value = a.value + s * ( b.value - a.value );\n\n      return v\n    }\n  }\n}\n\n// helper\n\nclass DecalVertex {\n  constructor(position, normal) {\n    this.position = position\n    this.normal = normal\n  }\n\n  clone() {\n    return new this.constructor(this.position.clone(), this.normal.clone())\n  }\n}\n\nexport { DecalGeometry, DecalVertex }\n"], "mappings": ";AAiBA,MAAMA,aAAA,SAAsBC,cAAA,CAAe;EACzCC,YAAYC,IAAA,EAAMC,QAAA,EAAUC,WAAA,EAAaC,IAAA,EAAM;IAC7C,MAAO;IAIP,MAAMC,QAAA,GAAW,EAAE;IACnB,MAAMC,OAAA,GAAU,EAAE;IAClB,MAAMC,GAAA,GAAM,EAAE;IAId,MAAMC,KAAA,GAAQ,IAAIC,OAAA,CAAS;IAI3B,MAAMC,eAAA,GAAkB,IAAIC,OAAA,CAAS;IACrCD,eAAA,CAAgBE,qBAAA,CAAsBT,WAAW;IACjDO,eAAA,CAAgBG,WAAA,CAAYX,QAAQ;IAEpC,MAAMY,sBAAA,GAAyB,IAAIH,OAAA,CAAS;IAC5CG,sBAAA,CAAuBC,IAAA,CAAKL,eAAe,EAAEM,MAAA,CAAQ;IAIrDC,QAAA,CAAU;IAIV,KAAKC,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBd,QAAA,EAAU,CAAC,CAAC;IACrE,KAAKa,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBb,OAAA,EAAS,CAAC,CAAC;IAClE,KAAKY,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBZ,GAAA,EAAK,CAAC,CAAC;IAE1D,SAASU,SAAA,EAAW;MAClB,IAAIG,CAAA;MAEJ,IAAIC,aAAA,GAAgB,EAAE;MAEtB,MAAMC,MAAA,GAAS,IAAIb,OAAA,CAAS;MAC5B,MAAMc,MAAA,GAAS,IAAId,OAAA,CAAS;MAI5B,IAAIR,IAAA,CAAKuB,QAAA,CAASC,UAAA,KAAe,MAAM;QACrCC,OAAA,CAAQC,KAAA,CAAM,oFAAoF;QAClG;MACD;MAED,MAAMH,QAAA,GAAWvB,IAAA,CAAKuB,QAAA;MAEtB,MAAMI,iBAAA,GAAoBJ,QAAA,CAASK,UAAA,CAAW3B,QAAA;MAC9C,MAAM4B,eAAA,GAAkBN,QAAA,CAASK,UAAA,CAAWN,MAAA;MAO5C,IAAIC,QAAA,CAASO,KAAA,KAAU,MAAM;QAG3B,MAAMA,KAAA,GAAQP,QAAA,CAASO,KAAA;QAEvB,KAAKX,CAAA,GAAI,GAAGA,CAAA,GAAIW,KAAA,CAAMC,KAAA,EAAOZ,CAAA,IAAK;UAChCE,MAAA,CAAOW,mBAAA,CAAoBL,iBAAA,EAAmBG,KAAA,CAAMG,IAAA,CAAKd,CAAC,CAAC;UAC3DG,MAAA,CAAOU,mBAAA,CAAoBH,eAAA,EAAiBC,KAAA,CAAMG,IAAA,CAAKd,CAAC,CAAC;UAEzDe,eAAA,CAAgBd,aAAA,EAAeC,MAAA,EAAQC,MAAM;QAC9C;MACT,OAAa;QAGL,KAAKH,CAAA,GAAI,GAAGA,CAAA,GAAIQ,iBAAA,CAAkBI,KAAA,EAAOZ,CAAA,IAAK;UAC5CE,MAAA,CAAOW,mBAAA,CAAoBL,iBAAA,EAAmBR,CAAC;UAC/CG,MAAA,CAAOU,mBAAA,CAAoBH,eAAA,EAAiBV,CAAC;UAE7Ce,eAAA,CAAgBd,aAAA,EAAeC,MAAA,EAAQC,MAAM;QAC9C;MACF;MAIDF,aAAA,GAAgBe,YAAA,CAAaf,aAAA,EAAeb,KAAA,CAAM6B,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;MAC9DhB,aAAA,GAAgBe,YAAA,CAAaf,aAAA,EAAeb,KAAA,CAAM6B,GAAA,CAAI,IAAI,GAAG,CAAC,CAAC;MAC/DhB,aAAA,GAAgBe,YAAA,CAAaf,aAAA,EAAeb,KAAA,CAAM6B,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;MAC9DhB,aAAA,GAAgBe,YAAA,CAAaf,aAAA,EAAeb,KAAA,CAAM6B,GAAA,CAAI,GAAG,IAAI,CAAC,CAAC;MAC/DhB,aAAA,GAAgBe,YAAA,CAAaf,aAAA,EAAeb,KAAA,CAAM6B,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;MAC9DhB,aAAA,GAAgBe,YAAA,CAAaf,aAAA,EAAeb,KAAA,CAAM6B,GAAA,CAAI,GAAG,GAAG,EAAE,CAAC;MAI/D,KAAKjB,CAAA,GAAI,GAAGA,CAAA,GAAIC,aAAA,CAAciB,MAAA,EAAQlB,CAAA,IAAK;QACzC,MAAMmB,WAAA,GAAclB,aAAA,CAAcD,CAAC;QAInCb,GAAA,CAAIiC,IAAA,CAAK,MAAMD,WAAA,CAAYrC,QAAA,CAASuC,CAAA,GAAIrC,IAAA,CAAKqC,CAAA,EAAG,MAAMF,WAAA,CAAYrC,QAAA,CAASwC,CAAA,GAAItC,IAAA,CAAKsC,CAAC;QAIrFH,WAAA,CAAYrC,QAAA,CAASyC,YAAA,CAAajC,eAAe;QAIjDL,QAAA,CAASmC,IAAA,CAAKD,WAAA,CAAYrC,QAAA,CAASuC,CAAA,EAAGF,WAAA,CAAYrC,QAAA,CAASwC,CAAA,EAAGH,WAAA,CAAYrC,QAAA,CAAS0C,CAAC;QACpFtC,OAAA,CAAQkC,IAAA,CAAKD,WAAA,CAAYhB,MAAA,CAAOkB,CAAA,EAAGF,WAAA,CAAYhB,MAAA,CAAOmB,CAAA,EAAGH,WAAA,CAAYhB,MAAA,CAAOqB,CAAC;MAC9E;IACF;IAED,SAAST,gBAAgBd,aAAA,EAAeC,MAAA,EAAQC,MAAA,EAAQ;MAGtDD,MAAA,CAAOqB,YAAA,CAAa1C,IAAA,CAAK4C,WAAW;MACpCvB,MAAA,CAAOqB,YAAA,CAAa7B,sBAAsB;MAE1CS,MAAA,CAAOuB,kBAAA,CAAmB7C,IAAA,CAAK4C,WAAW;MAE1CxB,aAAA,CAAcmB,IAAA,CAAK,IAAIO,WAAA,CAAYzB,MAAA,CAAO0B,KAAA,IAASzB,MAAA,CAAOyB,KAAA,CAAK,CAAE,CAAC;IACnE;IAED,SAASZ,aAAaa,UAAA,EAAYC,MAAA,EAAO;MACvC,MAAMC,WAAA,GAAc,EAAE;MAEtB,MAAMC,CAAA,GAAI,MAAMC,IAAA,CAAKC,GAAA,CAAIlD,IAAA,CAAKmD,GAAA,CAAIL,MAAK,CAAC;MAKxC,SAAS9B,CAAA,GAAI,GAAGA,CAAA,GAAI6B,UAAA,CAAWX,MAAA,EAAQlB,CAAA,IAAK,GAAG;QAC7C,IAAIoC,KAAA;UACFC,KAAA;UACAC,KAAA;UACAC,KAAA,GAAQ;QACV,IAAIC,GAAA,EAAKC,GAAA,EAAKC,GAAA,EAAKC,GAAA;QAEnB,MAAMC,EAAA,GAAKf,UAAA,CAAW7B,CAAA,GAAI,CAAC,EAAElB,QAAA,CAASqD,GAAA,CAAIL,MAAK,IAAIE,CAAA;QACnD,MAAMa,EAAA,GAAKhB,UAAA,CAAW7B,CAAA,GAAI,CAAC,EAAElB,QAAA,CAASqD,GAAA,CAAIL,MAAK,IAAIE,CAAA;QACnD,MAAMc,EAAA,GAAKjB,UAAA,CAAW7B,CAAA,GAAI,CAAC,EAAElB,QAAA,CAASqD,GAAA,CAAIL,MAAK,IAAIE,CAAA;QAEnDI,KAAA,GAAQQ,EAAA,GAAK;QACbP,KAAA,GAAQQ,EAAA,GAAK;QACbP,KAAA,GAAQQ,EAAA,GAAK;QAIbP,KAAA,IAASH,KAAA,GAAQ,IAAI,MAAMC,KAAA,GAAQ,IAAI,MAAMC,KAAA,GAAQ,IAAI;QAEzD,QAAQC,KAAA;UACN,KAAK;YAAG;cAGNR,WAAA,CAAYX,IAAA,CAAKS,UAAA,CAAW7B,CAAC,CAAC;cAC9B+B,WAAA,CAAYX,IAAA,CAAKS,UAAA,CAAW7B,CAAA,GAAI,CAAC,CAAC;cAClC+B,WAAA,CAAYX,IAAA,CAAKS,UAAA,CAAW7B,CAAA,GAAI,CAAC,CAAC;cAClC;YACD;UAED,KAAK;YAAG;cAGN,IAAIoC,KAAA,EAAO;gBACTI,GAAA,GAAMX,UAAA,CAAW7B,CAAA,GAAI,CAAC;gBACtByC,GAAA,GAAMZ,UAAA,CAAW7B,CAAA,GAAI,CAAC;gBACtB0C,GAAA,GAAMK,IAAA,CAAKlB,UAAA,CAAW7B,CAAC,GAAGwC,GAAA,EAAKV,MAAA,EAAOE,CAAC;gBACvCW,GAAA,GAAMI,IAAA,CAAKlB,UAAA,CAAW7B,CAAC,GAAGyC,GAAA,EAAKX,MAAA,EAAOE,CAAC;cACxC;cAED,IAAIK,KAAA,EAAO;gBACTG,GAAA,GAAMX,UAAA,CAAW7B,CAAC;gBAClByC,GAAA,GAAMZ,UAAA,CAAW7B,CAAA,GAAI,CAAC;gBACtB0C,GAAA,GAAMK,IAAA,CAAKlB,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAGwC,GAAA,EAAKV,MAAA,EAAOE,CAAC;gBAC3CW,GAAA,GAAMI,IAAA,CAAKlB,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAGyC,GAAA,EAAKX,MAAA,EAAOE,CAAC;gBAE3CD,WAAA,CAAYX,IAAA,CAAKsB,GAAG;gBACpBX,WAAA,CAAYX,IAAA,CAAKqB,GAAA,CAAIb,KAAA,EAAO;gBAC5BG,WAAA,CAAYX,IAAA,CAAKoB,GAAA,CAAIZ,KAAA,EAAO;gBAE5BG,WAAA,CAAYX,IAAA,CAAKqB,GAAA,CAAIb,KAAA,EAAO;gBAC5BG,WAAA,CAAYX,IAAA,CAAKsB,GAAA,CAAId,KAAA,EAAO;gBAC5BG,WAAA,CAAYX,IAAA,CAAKuB,GAAG;gBACpB;cACD;cAED,IAAIL,KAAA,EAAO;gBACTE,GAAA,GAAMX,UAAA,CAAW7B,CAAC;gBAClByC,GAAA,GAAMZ,UAAA,CAAW7B,CAAA,GAAI,CAAC;gBACtB0C,GAAA,GAAMK,IAAA,CAAKlB,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAGwC,GAAA,EAAKV,MAAA,EAAOE,CAAC;gBAC3CW,GAAA,GAAMI,IAAA,CAAKlB,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAGyC,GAAA,EAAKX,MAAA,EAAOE,CAAC;cAC5C;cAEDD,WAAA,CAAYX,IAAA,CAAKoB,GAAA,CAAIZ,KAAA,EAAO;cAC5BG,WAAA,CAAYX,IAAA,CAAKqB,GAAA,CAAIb,KAAA,EAAO;cAC5BG,WAAA,CAAYX,IAAA,CAAKsB,GAAG;cAEpBX,WAAA,CAAYX,IAAA,CAAKuB,GAAG;cACpBZ,WAAA,CAAYX,IAAA,CAAKsB,GAAA,CAAId,KAAA,EAAO;cAC5BG,WAAA,CAAYX,IAAA,CAAKqB,GAAA,CAAIb,KAAA,EAAO;cAE5B;YACD;UAED,KAAK;YAAG;cAGN,IAAI,CAACQ,KAAA,EAAO;gBACVI,GAAA,GAAMX,UAAA,CAAW7B,CAAC,EAAE4B,KAAA,CAAO;gBAC3Ba,GAAA,GAAMM,IAAA,CAAKP,GAAA,EAAKX,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAG8B,MAAA,EAAOE,CAAC;gBAC3CU,GAAA,GAAMK,IAAA,CAAKP,GAAA,EAAKX,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAG8B,MAAA,EAAOE,CAAC;gBAC3CD,WAAA,CAAYX,IAAA,CAAKoB,GAAG;gBACpBT,WAAA,CAAYX,IAAA,CAAKqB,GAAG;gBACpBV,WAAA,CAAYX,IAAA,CAAKsB,GAAG;cACrB;cAED,IAAI,CAACL,KAAA,EAAO;gBACVG,GAAA,GAAMX,UAAA,CAAW7B,CAAA,GAAI,CAAC,EAAE4B,KAAA,CAAO;gBAC/Ba,GAAA,GAAMM,IAAA,CAAKP,GAAA,EAAKX,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAG8B,MAAA,EAAOE,CAAC;gBAC3CU,GAAA,GAAMK,IAAA,CAAKP,GAAA,EAAKX,UAAA,CAAW7B,CAAC,GAAG8B,MAAA,EAAOE,CAAC;gBACvCD,WAAA,CAAYX,IAAA,CAAKoB,GAAG;gBACpBT,WAAA,CAAYX,IAAA,CAAKqB,GAAG;gBACpBV,WAAA,CAAYX,IAAA,CAAKsB,GAAG;cACrB;cAED,IAAI,CAACJ,KAAA,EAAO;gBACVE,GAAA,GAAMX,UAAA,CAAW7B,CAAA,GAAI,CAAC,EAAE4B,KAAA,CAAO;gBAC/Ba,GAAA,GAAMM,IAAA,CAAKP,GAAA,EAAKX,UAAA,CAAW7B,CAAC,GAAG8B,MAAA,EAAOE,CAAC;gBACvCU,GAAA,GAAMK,IAAA,CAAKP,GAAA,EAAKX,UAAA,CAAW7B,CAAA,GAAI,CAAC,GAAG8B,MAAA,EAAOE,CAAC;gBAC3CD,WAAA,CAAYX,IAAA,CAAKoB,GAAG;gBACpBT,WAAA,CAAYX,IAAA,CAAKqB,GAAG;gBACpBV,WAAA,CAAYX,IAAA,CAAKsB,GAAG;cACrB;cAED;YACD;QAOF;MACF;MAED,OAAOX,WAAA;IACR;IAED,SAASgB,KAAKC,EAAA,EAAIC,EAAA,EAAIC,CAAA,EAAGlB,CAAA,EAAG;MAC1B,MAAMmB,EAAA,GAAKH,EAAA,CAAGlE,QAAA,CAASqD,GAAA,CAAIe,CAAC,IAAIlB,CAAA;MAChC,MAAMY,EAAA,GAAKK,EAAA,CAAGnE,QAAA,CAASqD,GAAA,CAAIe,CAAC,IAAIlB,CAAA;MAEhC,MAAMoB,EAAA,GAAKD,EAAA,IAAMA,EAAA,GAAKP,EAAA;MAEtB,MAAMS,CAAA,GAAI,IAAI1B,WAAA,CACZ,IAAItC,OAAA,CACF2D,EAAA,CAAGlE,QAAA,CAASuC,CAAA,GAAI+B,EAAA,IAAMH,EAAA,CAAGnE,QAAA,CAASuC,CAAA,GAAI2B,EAAA,CAAGlE,QAAA,CAASuC,CAAA,GAClD2B,EAAA,CAAGlE,QAAA,CAASwC,CAAA,GAAI8B,EAAA,IAAMH,EAAA,CAAGnE,QAAA,CAASwC,CAAA,GAAI0B,EAAA,CAAGlE,QAAA,CAASwC,CAAA,GAClD0B,EAAA,CAAGlE,QAAA,CAAS0C,CAAA,GAAI4B,EAAA,IAAMH,EAAA,CAAGnE,QAAA,CAAS0C,CAAA,GAAIwB,EAAA,CAAGlE,QAAA,CAAS0C,CAAA,CACnD,GACD,IAAInC,OAAA,CACF2D,EAAA,CAAG7C,MAAA,CAAOkB,CAAA,GAAI+B,EAAA,IAAMH,EAAA,CAAG9C,MAAA,CAAOkB,CAAA,GAAI2B,EAAA,CAAG7C,MAAA,CAAOkB,CAAA,GAC5C2B,EAAA,CAAG7C,MAAA,CAAOmB,CAAA,GAAI8B,EAAA,IAAMH,EAAA,CAAG9C,MAAA,CAAOmB,CAAA,GAAI0B,EAAA,CAAG7C,MAAA,CAAOmB,CAAA,GAC5C0B,EAAA,CAAG7C,MAAA,CAAOqB,CAAA,GAAI4B,EAAA,IAAMH,EAAA,CAAG9C,MAAA,CAAOqB,CAAA,GAAIwB,EAAA,CAAG7C,MAAA,CAAOqB,CAAA,CAC7C,CACF;MAKD,OAAO6B,CAAA;IACR;EACF;AACH;AAIA,MAAM1B,WAAA,CAAY;EAChB/C,YAAYE,QAAA,EAAUqB,MAAA,EAAQ;IAC5B,KAAKrB,QAAA,GAAWA,QAAA;IAChB,KAAKqB,MAAA,GAASA,MAAA;EACf;EAEDyB,MAAA,EAAQ;IACN,OAAO,IAAI,KAAKhD,WAAA,CAAY,KAAKE,QAAA,CAAS8C,KAAA,CAAK,GAAI,KAAKzB,MAAA,CAAOyB,KAAA,EAAO;EACvE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}