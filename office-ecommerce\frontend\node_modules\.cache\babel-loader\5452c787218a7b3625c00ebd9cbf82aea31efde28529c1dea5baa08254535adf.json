{"ast": null, "code": "const SepiaShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    amount: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float amount;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 color = texture2D( tDiffuse, vUv );\n    \tvec3 c = color.rgb;\n\n    \tcolor.r = dot( c, vec3( 1.0 - 0.607 * amount, 0.769 * amount, 0.189 * amount ) );\n    \tcolor.g = dot( c, vec3( 0.349 * amount, 1.0 - 0.314 * amount, 0.168 * amount ) );\n    \tcolor.b = dot( c, vec3( 0.272 * amount, 0.534 * amount, 1.0 - 0.869 * amount ) );\n\n    \tgl_FragColor = vec4( min( vec3( 1.0 ), color.rgb ), color.a );\n\n    }\n  `)\n};\nexport { SepiaShader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "uniforms", "tDiffuse", "value", "amount", "vertexShader", "fragmentShader"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\shaders\\SepiaShader.ts"], "sourcesContent": ["/**\n * Sepia tone shader\n * based on glfx.js sepia shader\n * https://github.com/evanw/glfx.js\n */\n\nexport const SepiaShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    amount: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float amount;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 color = texture2D( tDiffuse, vUv );\n    \tvec3 c = color.rgb;\n\n    \tcolor.r = dot( c, vec3( 1.0 - 0.607 * amount, 0.769 * amount, 0.189 * amount ) );\n    \tcolor.g = dot( c, vec3( 0.349 * amount, 1.0 - 0.314 * amount, 0.168 * amount ) );\n    \tcolor.b = dot( c, vec3( 0.272 * amount, 0.534 * amount, 1.0 - 0.869 * amount ) );\n\n    \tgl_FragColor = vec4( min( vec3( 1.0 ), color.rgb ), color.a );\n\n    }\n  `,\n}\n"], "mappings": "AAMO,MAAMA,WAAA,GAAc;EACzBC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,MAAA,EAAQ;MAAED,KAAA,EAAO;IAAI;EACvB;EAEAE,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}