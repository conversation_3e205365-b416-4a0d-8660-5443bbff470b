const ActivityLogService = require('../services/activityLogService');
const logger = require('../utils/logger');

/**
 * Middleware to automatically log activities based on routes and actions
 */
class ActivityLoggerMiddleware {
  /**
   * Generic activity logging middleware
   * @param {string} action - Action being performed (CREATE, UPDATE, DELETE, etc.)
   * @param {string} entityType - Type of entity (User, Product, Order, etc.)
   * @param {Function} getEntityData - Function to extract entity data from request/response
   */
  static logActivity(action, entityType, getEntityData = null) {
    return async (req, res, next) => {
      const startTime = Date.now();

      // Store original res.json to intercept response
      const originalJson = res.json;
      
      res.json = function(data) {
        const duration = Date.now() - startTime;
        
        // Log the activity asynchronously
        setImmediate(async () => {
          try {
            let entityData = {};
            
            if (getEntityData && typeof getEntityData === 'function') {
              entityData = getEntityData(req, res, data);
            }

            const logData = ActivityLogService.createLogFromRequest(req, action, entityType, entityData);
            logData.statusCode = res.statusCode;
            logData.duration = duration;

            // Set severity based on status code
            if (res.statusCode >= 500) {
              logData.severity = 'ERROR';
            } else if (res.statusCode >= 400) {
              logData.severity = 'WARNING';
            } else {
              logData.severity = 'INFO';
            }

            await ActivityLogService.logActivity(logData);
          } catch (error) {
            logger.error('Failed to log activity in middleware:', error);
          }
        });

        // Call original json method
        return originalJson.call(this, data);
      };

      next();
    };
  }

  /**
   * Login activity logger
   */
  static logLogin() {
    return this.logActivity('LOGIN', 'Authentication', (req, res, data) => ({
      description: `User logged in successfully`,
      name: req.body.email
    }));
  }

  /**
   * Logout activity logger
   */
  static logLogout() {
    return this.logActivity('LOGOUT', 'Authentication', (req, res, data) => ({
      description: `User logged out`
    }));
  }

  /**
   * Product creation logger
   */
  static logProductCreate() {
    return this.logActivity('CREATE', 'Product', (req, res, data) => ({
      id: data.data?.productId,
      name: req.body.productName,
      description: `Created product: ${req.body.productName}`
    }));
  }

  /**
   * Product update logger
   */
  static logProductUpdate() {
    return this.logActivity('UPDATE', 'Product', (req, res, data) => ({
      id: req.params.id,
      name: req.body.productName,
      description: `Updated product: ${req.body.productName || req.params.id}`,
      oldValues: req.originalProduct, // This would need to be set by the route handler
      newValues: req.body
    }));
  }

  /**
   * Product deletion logger
   */
  static logProductDelete() {
    return this.logActivity('DELETE', 'Product', (req, res, data) => ({
      id: req.params.id,
      description: `Deleted product: ${req.params.id}`
    }));
  }

  /**
   * Order creation logger
   */
  static logOrderCreate() {
    return this.logActivity('CREATE', 'Order', (req, res, data) => ({
      id: data.data?.orderId,
      name: data.data?.orderNumber,
      description: `Created order: ${data.data?.orderNumber}`,
      metadata: {
        total: data.data?.total,
        itemCount: data.data?.items?.length
      }
    }));
  }

  /**
   * Order update logger
   */
  static logOrderUpdate() {
    return this.logActivity('UPDATE', 'Order', (req, res, data) => ({
      id: req.params.id,
      description: `Updated order: ${req.params.id}`,
      oldValues: req.originalOrder,
      newValues: req.body
    }));
  }

  /**
   * User creation logger
   */
  static logUserCreate() {
    return this.logActivity('CREATE', 'User', (req, res, data) => ({
      id: data.data?.userId,
      name: `${req.body.firstName} ${req.body.lastName}`,
      description: `Created user: ${req.body.email}`,
      metadata: {
        role: req.body.role
      }
    }));
  }

  /**
   * User update logger
   */
  static logUserUpdate() {
    return this.logActivity('UPDATE', 'User', (req, res, data) => ({
      id: req.params.id,
      description: `Updated user: ${req.params.id}`,
      oldValues: req.originalUser,
      newValues: req.body
    }));
  }

  /**
   * Inventory adjustment logger
   */
  static logInventoryAdjustment() {
    return this.logActivity('UPDATE', 'Inventory', (req, res, data) => ({
      id: req.params.id,
      description: `Inventory adjustment for: ${req.params.id}`,
      metadata: {
        adjustment: req.body.adjustment,
        reason: req.body.reason
      }
    }));
  }

  /**
   * Admin dashboard access logger
   */
  static logDashboardAccess() {
    return this.logActivity('VIEW', 'Dashboard', (req, res, data) => ({
      description: `Accessed admin dashboard`
    }));
  }

  /**
   * Settings update logger
   */
  static logSettingsUpdate() {
    return this.logActivity('UPDATE', 'Settings', (req, res, data) => ({
      description: `Updated system settings`,
      oldValues: req.originalSettings,
      newValues: req.body
    }));
  }

  /**
   * File upload logger
   */
  static logFileUpload() {
    return this.logActivity('UPLOAD', 'File', (req, res, data) => ({
      description: `Uploaded file: ${req.file?.originalname}`,
      metadata: {
        filename: req.file?.filename,
        size: req.file?.size,
        mimetype: req.file?.mimetype
      }
    }));
  }

  /**
   * Bulk operation logger
   */
  static logBulkOperation(operationType, entityType) {
    return this.logActivity(`BULK_${operationType}`, entityType, (req, res, data) => ({
      description: `Bulk ${operationType.toLowerCase()} operation on ${entityType}`,
      metadata: {
        itemCount: Array.isArray(req.body) ? req.body.length : req.body.items?.length,
        operation: operationType
      }
    }));
  }

  /**
   * Error logger for failed operations
   */
  static logError(action, entityType) {
    return async (error, req, res, next) => {
      try {
        const logData = ActivityLogService.createLogFromRequest(req, action, entityType, {
          description: `Failed to ${action.toLowerCase()} ${entityType}: ${error.message}`
        });
        
        logData.severity = 'ERROR';
        logData.statusCode = res.statusCode || 500;
        logData.metadata = {
          ...logData.metadata,
          error: {
            message: error.message,
            stack: error.stack
          }
        };

        await ActivityLogService.logActivity(logData);
      } catch (logError) {
        logger.error('Failed to log error activity:', logError);
      }

      next(error);
    };
  }
}

module.exports = ActivityLoggerMiddleware;
