{"ast": null, "code": "import { Vector2 } from \"three\";\nfunction UVsDebug(geometry, size = 1024) {\n  const abc = \"abc\";\n  const a = new Vector2();\n  const b = new Vector2();\n  const uvs = [new Vector2(), new Vector2(), new Vector2()];\n  const face = [];\n  const canvas = document.createElement(\"canvas\");\n  const width = size;\n  const height = size;\n  canvas.width = width;\n  canvas.height = height;\n  const ctx = canvas.getContext(\"2d\");\n  ctx.lineWidth = 1;\n  ctx.strokeStyle = \"rgb( 63, 63, 63 )\";\n  ctx.textAlign = \"center\";\n  ctx.fillStyle = \"rgb( 255, 255, 255 )\";\n  ctx.fillRect(0, 0, width, height);\n  const index = geometry.index;\n  const uvAttribute = geometry.attributes.uv;\n  if (index) {\n    for (let i = 0, il = index.count; i < il; i += 3) {\n      face[0] = index.getX(i);\n      face[1] = index.getX(i + 1);\n      face[2] = index.getX(i + 2);\n      uvs[0].fromBufferAttribute(uvAttribute, face[0]);\n      uvs[1].fromBufferAttribute(uvAttribute, face[1]);\n      uvs[2].fromBufferAttribute(uvAttribute, face[2]);\n      processFace(face, uvs, i / 3);\n    }\n  } else {\n    for (let i = 0, il = uvAttribute.count; i < il; i += 3) {\n      face[0] = i;\n      face[1] = i + 1;\n      face[2] = i + 2;\n      uvs[0].fromBufferAttribute(uvAttribute, face[0]);\n      uvs[1].fromBufferAttribute(uvAttribute, face[1]);\n      uvs[2].fromBufferAttribute(uvAttribute, face[2]);\n      processFace(face, uvs, i / 3);\n    }\n  }\n  return canvas;\n  function processFace(face2, uvs2, index2) {\n    ctx.beginPath();\n    a.set(0, 0);\n    for (let j = 0, jl = uvs2.length; j < jl; j++) {\n      const uv = uvs2[j];\n      a.x += uv.x;\n      a.y += uv.y;\n      if (j === 0) {\n        ctx.moveTo(uv.x * (width - 2) + 0.5, (1 - uv.y) * (height - 2) + 0.5);\n      } else {\n        ctx.lineTo(uv.x * (width - 2) + 0.5, (1 - uv.y) * (height - 2) + 0.5);\n      }\n    }\n    ctx.closePath();\n    ctx.stroke();\n    a.divideScalar(uvs2.length);\n    ctx.font = \"18px Arial\";\n    ctx.fillStyle = \"rgb( 63, 63, 63 )\";\n    ctx.fillText(index2, a.x * width, (1 - a.y) * height);\n    if (a.x > 0.95) {\n      ctx.fillText(index2, a.x % 1 * width, (1 - a.y) * height);\n    }\n    ctx.font = \"12px Arial\";\n    ctx.fillStyle = \"rgb( 191, 191, 191 )\";\n    for (let j = 0, jl = uvs2.length; j < jl; j++) {\n      const uv = uvs2[j];\n      b.addVectors(a, uv).divideScalar(2);\n      const vnum = face2[j];\n      ctx.fillText(abc[j] + vnum, b.x * width, (1 - b.y) * height);\n      if (b.x > 0.95) {\n        ctx.fillText(abc[j] + vnum, b.x % 1 * width, (1 - b.y) * height);\n      }\n    }\n  }\n}\nexport { UVsDebug };", "map": {"version": 3, "names": ["UVsDebug", "geometry", "size", "abc", "a", "Vector2", "b", "uvs", "face", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "lineWidth", "strokeStyle", "textAlign", "fillStyle", "fillRect", "index", "uvAttribute", "attributes", "uv", "i", "il", "count", "getX", "fromBufferAttribute", "processFace", "face2", "uvs2", "index2", "beginPath", "set", "j", "jl", "length", "x", "y", "moveTo", "lineTo", "closePath", "stroke", "divideScalar", "font", "fillText", "addVectors", "vnum"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\utils\\UVsDebug.js"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * tool for \"unwrapping\" and debugging three.js geometries UV mapping\n *\n * Sample usage:\n *\tdocument.body.appendChild( UVsDebug( new THREE.SphereGeometry( 10, 10, 10, 10 ) );\n *\n */\n\nfunction UVsDebug(geometry, size = 1024) {\n  // handles wrapping of uv.x > 1 only\n\n  const abc = 'abc'\n  const a = new Vector2()\n  const b = new Vector2()\n\n  const uvs = [new Vector2(), new Vector2(), new Vector2()]\n\n  const face = []\n\n  const canvas = document.createElement('canvas')\n  const width = size // power of 2 required for wrapping\n  const height = size\n  canvas.width = width\n  canvas.height = height\n\n  const ctx = canvas.getContext('2d')\n  ctx.lineWidth = 1\n  ctx.strokeStyle = 'rgb( 63, 63, 63 )'\n  ctx.textAlign = 'center'\n\n  // paint background white\n\n  ctx.fillStyle = 'rgb( 255, 255, 255 )'\n  ctx.fillRect(0, 0, width, height)\n\n  const index = geometry.index\n  const uvAttribute = geometry.attributes.uv\n\n  if (index) {\n    // indexed geometry\n\n    for (let i = 0, il = index.count; i < il; i += 3) {\n      face[0] = index.getX(i)\n      face[1] = index.getX(i + 1)\n      face[2] = index.getX(i + 2)\n\n      uvs[0].fromBufferAttribute(uvAttribute, face[0])\n      uvs[1].fromBufferAttribute(uvAttribute, face[1])\n      uvs[2].fromBufferAttribute(uvAttribute, face[2])\n\n      processFace(face, uvs, i / 3)\n    }\n  } else {\n    // non-indexed geometry\n\n    for (let i = 0, il = uvAttribute.count; i < il; i += 3) {\n      face[0] = i\n      face[1] = i + 1\n      face[2] = i + 2\n\n      uvs[0].fromBufferAttribute(uvAttribute, face[0])\n      uvs[1].fromBufferAttribute(uvAttribute, face[1])\n      uvs[2].fromBufferAttribute(uvAttribute, face[2])\n\n      processFace(face, uvs, i / 3)\n    }\n  }\n\n  return canvas\n\n  function processFace(face, uvs, index) {\n    // draw contour of face\n\n    ctx.beginPath()\n\n    a.set(0, 0)\n\n    for (let j = 0, jl = uvs.length; j < jl; j++) {\n      const uv = uvs[j]\n\n      a.x += uv.x\n      a.y += uv.y\n\n      if (j === 0) {\n        ctx.moveTo(uv.x * (width - 2) + 0.5, (1 - uv.y) * (height - 2) + 0.5)\n      } else {\n        ctx.lineTo(uv.x * (width - 2) + 0.5, (1 - uv.y) * (height - 2) + 0.5)\n      }\n    }\n\n    ctx.closePath()\n    ctx.stroke()\n\n    // calculate center of face\n\n    a.divideScalar(uvs.length)\n\n    // label the face number\n\n    ctx.font = '18px Arial'\n    ctx.fillStyle = 'rgb( 63, 63, 63 )'\n    ctx.fillText(index, a.x * width, (1 - a.y) * height)\n\n    if (a.x > 0.95) {\n      // wrap x // 0.95 is arbitrary\n\n      ctx.fillText(index, (a.x % 1) * width, (1 - a.y) * height)\n    }\n\n    //\n\n    ctx.font = '12px Arial'\n    ctx.fillStyle = 'rgb( 191, 191, 191 )'\n\n    // label uv edge orders\n\n    for (let j = 0, jl = uvs.length; j < jl; j++) {\n      const uv = uvs[j]\n      b.addVectors(a, uv).divideScalar(2)\n\n      const vnum = face[j]\n      ctx.fillText(abc[j] + vnum, b.x * width, (1 - b.y) * height)\n\n      if (b.x > 0.95) {\n        // wrap x\n\n        ctx.fillText(abc[j] + vnum, (b.x % 1) * width, (1 - b.y) * height)\n      }\n    }\n  }\n}\n\nexport { UVsDebug }\n"], "mappings": ";AAUA,SAASA,SAASC,QAAA,EAAUC,IAAA,GAAO,MAAM;EAGvC,MAAMC,GAAA,GAAM;EACZ,MAAMC,CAAA,GAAI,IAAIC,OAAA,CAAS;EACvB,MAAMC,CAAA,GAAI,IAAID,OAAA,CAAS;EAEvB,MAAME,GAAA,GAAM,CAAC,IAAIF,OAAA,CAAO,GAAI,IAAIA,OAAA,CAAS,GAAE,IAAIA,OAAA,EAAS;EAExD,MAAMG,IAAA,GAAO,EAAE;EAEf,MAAMC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;EAC9C,MAAMC,KAAA,GAAQV,IAAA;EACd,MAAMW,MAAA,GAASX,IAAA;EACfO,MAAA,CAAOG,KAAA,GAAQA,KAAA;EACfH,MAAA,CAAOI,MAAA,GAASA,MAAA;EAEhB,MAAMC,GAAA,GAAML,MAAA,CAAOM,UAAA,CAAW,IAAI;EAClCD,GAAA,CAAIE,SAAA,GAAY;EAChBF,GAAA,CAAIG,WAAA,GAAc;EAClBH,GAAA,CAAII,SAAA,GAAY;EAIhBJ,GAAA,CAAIK,SAAA,GAAY;EAChBL,GAAA,CAAIM,QAAA,CAAS,GAAG,GAAGR,KAAA,EAAOC,MAAM;EAEhC,MAAMQ,KAAA,GAAQpB,QAAA,CAASoB,KAAA;EACvB,MAAMC,WAAA,GAAcrB,QAAA,CAASsB,UAAA,CAAWC,EAAA;EAExC,IAAIH,KAAA,EAAO;IAGT,SAASI,CAAA,GAAI,GAAGC,EAAA,GAAKL,KAAA,CAAMM,KAAA,EAAOF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;MAChDjB,IAAA,CAAK,CAAC,IAAIa,KAAA,CAAMO,IAAA,CAAKH,CAAC;MACtBjB,IAAA,CAAK,CAAC,IAAIa,KAAA,CAAMO,IAAA,CAAKH,CAAA,GAAI,CAAC;MAC1BjB,IAAA,CAAK,CAAC,IAAIa,KAAA,CAAMO,IAAA,CAAKH,CAAA,GAAI,CAAC;MAE1BlB,GAAA,CAAI,CAAC,EAAEsB,mBAAA,CAAoBP,WAAA,EAAad,IAAA,CAAK,CAAC,CAAC;MAC/CD,GAAA,CAAI,CAAC,EAAEsB,mBAAA,CAAoBP,WAAA,EAAad,IAAA,CAAK,CAAC,CAAC;MAC/CD,GAAA,CAAI,CAAC,EAAEsB,mBAAA,CAAoBP,WAAA,EAAad,IAAA,CAAK,CAAC,CAAC;MAE/CsB,WAAA,CAAYtB,IAAA,EAAMD,GAAA,EAAKkB,CAAA,GAAI,CAAC;IAC7B;EACL,OAAS;IAGL,SAASA,CAAA,GAAI,GAAGC,EAAA,GAAKJ,WAAA,CAAYK,KAAA,EAAOF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;MACtDjB,IAAA,CAAK,CAAC,IAAIiB,CAAA;MACVjB,IAAA,CAAK,CAAC,IAAIiB,CAAA,GAAI;MACdjB,IAAA,CAAK,CAAC,IAAIiB,CAAA,GAAI;MAEdlB,GAAA,CAAI,CAAC,EAAEsB,mBAAA,CAAoBP,WAAA,EAAad,IAAA,CAAK,CAAC,CAAC;MAC/CD,GAAA,CAAI,CAAC,EAAEsB,mBAAA,CAAoBP,WAAA,EAAad,IAAA,CAAK,CAAC,CAAC;MAC/CD,GAAA,CAAI,CAAC,EAAEsB,mBAAA,CAAoBP,WAAA,EAAad,IAAA,CAAK,CAAC,CAAC;MAE/CsB,WAAA,CAAYtB,IAAA,EAAMD,GAAA,EAAKkB,CAAA,GAAI,CAAC;IAC7B;EACF;EAED,OAAOhB,MAAA;EAEP,SAASqB,YAAYC,KAAA,EAAMC,IAAA,EAAKC,MAAA,EAAO;IAGrCnB,GAAA,CAAIoB,SAAA,CAAW;IAEf9B,CAAA,CAAE+B,GAAA,CAAI,GAAG,CAAC;IAEV,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKL,IAAA,CAAIM,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC5C,MAAMZ,EAAA,GAAKQ,IAAA,CAAII,CAAC;MAEhBhC,CAAA,CAAEmC,CAAA,IAAKf,EAAA,CAAGe,CAAA;MACVnC,CAAA,CAAEoC,CAAA,IAAKhB,EAAA,CAAGgB,CAAA;MAEV,IAAIJ,CAAA,KAAM,GAAG;QACXtB,GAAA,CAAI2B,MAAA,CAAOjB,EAAA,CAAGe,CAAA,IAAK3B,KAAA,GAAQ,KAAK,MAAM,IAAIY,EAAA,CAAGgB,CAAA,KAAM3B,MAAA,GAAS,KAAK,GAAG;MAC5E,OAAa;QACLC,GAAA,CAAI4B,MAAA,CAAOlB,EAAA,CAAGe,CAAA,IAAK3B,KAAA,GAAQ,KAAK,MAAM,IAAIY,EAAA,CAAGgB,CAAA,KAAM3B,MAAA,GAAS,KAAK,GAAG;MACrE;IACF;IAEDC,GAAA,CAAI6B,SAAA,CAAW;IACf7B,GAAA,CAAI8B,MAAA,CAAQ;IAIZxC,CAAA,CAAEyC,YAAA,CAAab,IAAA,CAAIM,MAAM;IAIzBxB,GAAA,CAAIgC,IAAA,GAAO;IACXhC,GAAA,CAAIK,SAAA,GAAY;IAChBL,GAAA,CAAIiC,QAAA,CAASd,MAAA,EAAO7B,CAAA,CAAEmC,CAAA,GAAI3B,KAAA,GAAQ,IAAIR,CAAA,CAAEoC,CAAA,IAAK3B,MAAM;IAEnD,IAAIT,CAAA,CAAEmC,CAAA,GAAI,MAAM;MAGdzB,GAAA,CAAIiC,QAAA,CAASd,MAAA,EAAQ7B,CAAA,CAAEmC,CAAA,GAAI,IAAK3B,KAAA,GAAQ,IAAIR,CAAA,CAAEoC,CAAA,IAAK3B,MAAM;IAC1D;IAIDC,GAAA,CAAIgC,IAAA,GAAO;IACXhC,GAAA,CAAIK,SAAA,GAAY;IAIhB,SAASiB,CAAA,GAAI,GAAGC,EAAA,GAAKL,IAAA,CAAIM,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC5C,MAAMZ,EAAA,GAAKQ,IAAA,CAAII,CAAC;MAChB9B,CAAA,CAAE0C,UAAA,CAAW5C,CAAA,EAAGoB,EAAE,EAAEqB,YAAA,CAAa,CAAC;MAElC,MAAMI,IAAA,GAAOlB,KAAA,CAAKK,CAAC;MACnBtB,GAAA,CAAIiC,QAAA,CAAS5C,GAAA,CAAIiC,CAAC,IAAIa,IAAA,EAAM3C,CAAA,CAAEiC,CAAA,GAAI3B,KAAA,GAAQ,IAAIN,CAAA,CAAEkC,CAAA,IAAK3B,MAAM;MAE3D,IAAIP,CAAA,CAAEiC,CAAA,GAAI,MAAM;QAGdzB,GAAA,CAAIiC,QAAA,CAAS5C,GAAA,CAAIiC,CAAC,IAAIa,IAAA,EAAO3C,CAAA,CAAEiC,CAAA,GAAI,IAAK3B,KAAA,GAAQ,IAAIN,CAAA,CAAEkC,CAAA,IAAK3B,MAAM;MAClE;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}