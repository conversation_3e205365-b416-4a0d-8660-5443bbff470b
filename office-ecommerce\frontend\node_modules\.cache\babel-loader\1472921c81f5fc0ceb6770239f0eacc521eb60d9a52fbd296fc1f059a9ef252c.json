{"ast": null, "code": "const ToneMapShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    averageLuminance: {\n      value: 1\n    },\n    luminanceMap: {\n      value: null\n    },\n    maxLuminance: {\n      value: 16\n    },\n    minLuminance: {\n      value: 0.01\n    },\n    middleGrey: {\n      value: 0.6\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    #include <common>\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    uniform float middleGrey;\n    uniform float minLuminance;\n    uniform float maxLuminance;\n    #ifdef ADAPTED_LUMINANCE\n    \tuniform sampler2D luminanceMap;\n    #else\n    \tuniform float averageLuminance;\n    #endif\n\n    vec3 ToneMap( vec3 vColor ) {\n    \t#ifdef ADAPTED_LUMINANCE\n    // Get the calculated average luminance\n    \t\tfloat fLumAvg = texture2D(luminanceMap, vec2(0.5, 0.5)).r;\n    \t#else\n    \t\tfloat fLumAvg = averageLuminance;\n    \t#endif\n\n    // Calculate the luminance of the current pixel\n    \tfloat fLumPixel = linearToRelativeLuminance( vColor );\n\n    // Apply the modified operator (Eq. 4)\n    \tfloat fLumScaled = (fLumPixel * middleGrey) / max( minLuminance, fLumAvg );\n\n    \tfloat fLumCompressed = (fLumScaled * (1.0 + (fLumScaled / (maxLuminance * maxLuminance)))) / (1.0 + fLumScaled);\n    \treturn fLumCompressed * vColor;\n    }\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tgl_FragColor = vec4( ToneMap( texel.xyz ), texel.w );\n\n    }\n  `)\n};\nexport { ToneMapShader };", "map": {"version": 3, "names": ["ToneMap<PERSON><PERSON>er", "uniforms", "tDiffuse", "value", "averageLuminance", "luminanceMap", "maxLuminance", "minLuminance", "<PERSON><PERSON><PERSON>", "vertexShader", "fragmentShader"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\shaders\\ToneMapShader.ts"], "sourcesContent": ["/**\n * Full-screen tone-mapping shader based on http://www.cis.rit.edu/people/faculty/ferwerda/publications/sig02_paper.pdf\n */\n\nexport const ToneMapShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    averageLuminance: { value: 1.0 },\n    luminanceMap: { value: null },\n    maxLuminance: { value: 16.0 },\n    minLuminance: { value: 0.01 },\n    middleGrey: { value: 0.6 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    uniform float middleGrey;\n    uniform float minLuminance;\n    uniform float maxLuminance;\n    #ifdef ADAPTED_LUMINANCE\n    \tuniform sampler2D luminanceMap;\n    #else\n    \tuniform float averageLuminance;\n    #endif\n\n    vec3 ToneMap( vec3 vColor ) {\n    \t#ifdef ADAPTED_LUMINANCE\n    // Get the calculated average luminance\n    \t\tfloat fLumAvg = texture2D(luminanceMap, vec2(0.5, 0.5)).r;\n    \t#else\n    \t\tfloat fLumAvg = averageLuminance;\n    \t#endif\n\n    // Calculate the luminance of the current pixel\n    \tfloat fLumPixel = linearToRelativeLuminance( vColor );\n\n    // Apply the modified operator (Eq. 4)\n    \tfloat fLumScaled = (fLumPixel * middleGrey) / max( minLuminance, fLumAvg );\n\n    \tfloat fLumCompressed = (fLumScaled * (1.0 + (fLumScaled / (maxLuminance * maxLuminance)))) / (1.0 + fLumScaled);\n    \treturn fLumCompressed * vColor;\n    }\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tgl_FragColor = vec4( ToneMap( texel.xyz ), texel.w );\n\n    }\n  `,\n}\n"], "mappings": "AAIO,MAAMA,aAAA,GAAgB;EAC3BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,gBAAA,EAAkB;MAAED,KAAA,EAAO;IAAI;IAC/BE,YAAA,EAAc;MAAEF,KAAA,EAAO;IAAK;IAC5BG,YAAA,EAAc;MAAEH,KAAA,EAAO;IAAK;IAC5BI,YAAA,EAAc;MAAEJ,KAAA,EAAO;IAAK;IAC5BK,UAAA,EAAY;MAAEL,KAAA,EAAO;IAAI;EAC3B;EAEAM,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0C7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}