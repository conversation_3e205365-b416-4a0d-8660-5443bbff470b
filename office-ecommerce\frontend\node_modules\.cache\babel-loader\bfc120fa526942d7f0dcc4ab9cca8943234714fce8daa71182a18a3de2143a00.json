{"ast": null, "code": "import { useThree, useLoader } from '@react-three/fiber';\nimport { useEffect } from 'react';\nimport { KTX2Loader } from 'three-stdlib';\nimport { IsObject } from './useTexture.js';\nconst cdn = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master';\nfunction useKTX2(input, basisPath = `${cdn}/basis/`) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(KTX2Loader, IsObject(input) ? Object.values(input) : input, loader => {\n    loader.detectSupport(gl);\n    loader.setTranscoderPath(basisPath);\n  }); // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n\n  useEffect(() => {\n    const array = Array.isArray(textures) ? textures : [textures];\n    array.forEach(gl.initTexture);\n  }, [gl, textures]);\n  if (IsObject(input)) {\n    const keys = Object.keys(input);\n    const keyed = {};\n    keys.forEach(key => Object.assign(keyed, {\n      [key]: textures[keys.indexOf(key)]\n    }));\n    return keyed;\n  } else {\n    return textures;\n  }\n}\nuseKTX2.preload = (url, basisPath = `${cdn}/basis/`) => useLoader.preload(KTX2Loader, url, loader => {\n  loader.setTranscoderPath(basisPath);\n});\nuseKTX2.clear = input => useLoader.clear(KTX2Loader, input);\nexport { useKTX2 };", "map": {"version": 3, "names": ["useThree", "useLoader", "useEffect", "KTX2Loader", "IsObject", "cdn", "useKTX2", "input", "basisPath", "gl", "state", "textures", "Object", "values", "loader", "detectSupport", "setTranscoderPath", "array", "Array", "isArray", "for<PERSON>ach", "initTexture", "keys", "keyed", "key", "assign", "indexOf", "preload", "url", "clear"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useKTX2.js"], "sourcesContent": ["import { useThree, useLoader } from '@react-three/fiber';\nimport { useEffect } from 'react';\nimport { KTX2Loader } from 'three-stdlib';\nimport { IsObject } from './useTexture.js';\n\nconst cdn = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master';\nfunction useKTX2(input, basisPath = `${cdn}/basis/`) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(KTX2Loader, IsObject(input) ? Object.values(input) : input, loader => {\n    loader.detectSupport(gl);\n    loader.setTranscoderPath(basisPath);\n  }); // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n\n  useEffect(() => {\n    const array = Array.isArray(textures) ? textures : [textures];\n    array.forEach(gl.initTexture);\n  }, [gl, textures]);\n\n  if (IsObject(input)) {\n    const keys = Object.keys(input);\n    const keyed = {};\n    keys.forEach(key => Object.assign(keyed, {\n      [key]: textures[keys.indexOf(key)]\n    }));\n    return keyed;\n  } else {\n    return textures;\n  }\n}\n\nuseKTX2.preload = (url, basisPath = `${cdn}/basis/`) => useLoader.preload(KTX2Loader, url, loader => {\n  loader.setTranscoderPath(basisPath);\n});\n\nuseKTX2.clear = input => useLoader.clear(KTX2Loader, input);\n\nexport { useKTX2 };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AACxD,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,QAAQ,QAAQ,iBAAiB;AAE1C,MAAMC,GAAG,GAAG,uDAAuD;AACnE,SAASC,OAAOA,CAACC,KAAK,EAAEC,SAAS,GAAG,GAAGH,GAAG,SAAS,EAAE;EACnD,MAAMI,EAAE,GAAGT,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,QAAQ,GAAGV,SAAS,CAACE,UAAU,EAAEC,QAAQ,CAACG,KAAK,CAAC,GAAGK,MAAM,CAACC,MAAM,CAACN,KAAK,CAAC,GAAGA,KAAK,EAAEO,MAAM,IAAI;IAC/FA,MAAM,CAACC,aAAa,CAACN,EAAE,CAAC;IACxBK,MAAM,CAACE,iBAAiB,CAACR,SAAS,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC;EACJ;;EAEAN,SAAS,CAAC,MAAM;IACd,MAAMe,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC7DM,KAAK,CAACG,OAAO,CAACX,EAAE,CAACY,WAAW,CAAC;EAC/B,CAAC,EAAE,CAACZ,EAAE,EAAEE,QAAQ,CAAC,CAAC;EAElB,IAAIP,QAAQ,CAACG,KAAK,CAAC,EAAE;IACnB,MAAMe,IAAI,GAAGV,MAAM,CAACU,IAAI,CAACf,KAAK,CAAC;IAC/B,MAAMgB,KAAK,GAAG,CAAC,CAAC;IAChBD,IAAI,CAACF,OAAO,CAACI,GAAG,IAAIZ,MAAM,CAACa,MAAM,CAACF,KAAK,EAAE;MACvC,CAACC,GAAG,GAAGb,QAAQ,CAACW,IAAI,CAACI,OAAO,CAACF,GAAG,CAAC;IACnC,CAAC,CAAC,CAAC;IACH,OAAOD,KAAK;EACd,CAAC,MAAM;IACL,OAAOZ,QAAQ;EACjB;AACF;AAEAL,OAAO,CAACqB,OAAO,GAAG,CAACC,GAAG,EAAEpB,SAAS,GAAG,GAAGH,GAAG,SAAS,KAAKJ,SAAS,CAAC0B,OAAO,CAACxB,UAAU,EAAEyB,GAAG,EAAEd,MAAM,IAAI;EACnGA,MAAM,CAACE,iBAAiB,CAACR,SAAS,CAAC;AACrC,CAAC,CAAC;AAEFF,OAAO,CAACuB,KAAK,GAAGtB,KAAK,IAAIN,SAAS,CAAC4B,KAAK,CAAC1B,UAAU,EAAEI,KAAK,CAAC;AAE3D,SAASD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}