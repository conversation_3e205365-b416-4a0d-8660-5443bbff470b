{"ast": null, "code": "import { Loader, LoaderUtils, FileLoader, FrontSide, RepeatWrapping, Color, MeshPhongMaterial, Vector2, DefaultLoadingManager, TextureLoader } from \"three\";\nclass MTLLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  /**\n   * Loads and parses a MTL asset from a URL.\n   *\n   * @param {String} url - URL to the MTL file.\n   * @param {Function} [onLoad] - Callback invoked with the loaded object.\n   * @param {Function} [onProgress] - Callback for download progress.\n   * @param {Function} [onError] - Callback for download errors.\n   *\n   * @see setPath setResourcePath\n   *\n   * @note In order for relative texture references to resolve correctly\n   * you must call setResourcePath() explicitly prior to load.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const path = this.path === \"\" ? LoaderUtils.extractUrlBase(url) : this.path;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text, path));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  setMaterialOptions(value) {\n    this.materialOptions = value;\n    return this;\n  }\n  /**\n   * Parses a MTL file.\n   *\n   * @param {String} text - Content of MTL file\n   * @return {MaterialCreator}\n   *\n   * @see setPath setResourcePath\n   *\n   * @note In order for relative texture references to resolve correctly\n   * you must call setResourcePath() explicitly prior to parse.\n   */\n  parse(text, path) {\n    const lines = text.split(\"\\n\");\n    let info = {};\n    const delimiter_pattern = /\\s+/;\n    const materialsInfo = {};\n    for (let i = 0; i < lines.length; i++) {\n      let line = lines[i];\n      line = line.trim();\n      if (line.length === 0 || line.charAt(0) === \"#\") {\n        continue;\n      }\n      const pos = line.indexOf(\" \");\n      let key = pos >= 0 ? line.substring(0, pos) : line;\n      key = key.toLowerCase();\n      let value = pos >= 0 ? line.substring(pos + 1) : \"\";\n      value = value.trim();\n      if (key === \"newmtl\") {\n        info = {\n          name: value\n        };\n        materialsInfo[value] = info;\n      } else {\n        if (key === \"ka\" || key === \"kd\" || key === \"ks\" || key === \"ke\") {\n          const ss = value.split(delimiter_pattern, 3);\n          info[key] = [parseFloat(ss[0]), parseFloat(ss[1]), parseFloat(ss[2])];\n        } else {\n          info[key] = value;\n        }\n      }\n    }\n    const materialCreator = new MaterialCreator(this.resourcePath || path, this.materialOptions);\n    materialCreator.setCrossOrigin(this.crossOrigin);\n    materialCreator.setManager(this.manager);\n    materialCreator.setMaterials(materialsInfo);\n    return materialCreator;\n  }\n}\nclass MaterialCreator {\n  constructor(baseUrl = \"\", options = {}) {\n    this.baseUrl = baseUrl;\n    this.options = options;\n    this.materialsInfo = {};\n    this.materials = {};\n    this.materialsArray = [];\n    this.nameLookup = {};\n    this.crossOrigin = \"anonymous\";\n    this.side = this.options.side !== void 0 ? this.options.side : FrontSide;\n    this.wrap = this.options.wrap !== void 0 ? this.options.wrap : RepeatWrapping;\n  }\n  setCrossOrigin(value) {\n    this.crossOrigin = value;\n    return this;\n  }\n  setManager(value) {\n    this.manager = value;\n  }\n  setMaterials(materialsInfo) {\n    this.materialsInfo = this.convert(materialsInfo);\n    this.materials = {};\n    this.materialsArray = [];\n    this.nameLookup = {};\n  }\n  convert(materialsInfo) {\n    if (!this.options) return materialsInfo;\n    const converted = {};\n    for (const mn in materialsInfo) {\n      const mat = materialsInfo[mn];\n      const covmat = {};\n      converted[mn] = covmat;\n      for (const prop in mat) {\n        let save = true;\n        let value = mat[prop];\n        const lprop = prop.toLowerCase();\n        switch (lprop) {\n          case \"kd\":\n          case \"ka\":\n          case \"ks\":\n            if (this.options && this.options.normalizeRGB) {\n              value = [value[0] / 255, value[1] / 255, value[2] / 255];\n            }\n            if (this.options && this.options.ignoreZeroRGBs) {\n              if (value[0] === 0 && value[1] === 0 && value[2] === 0) {\n                save = false;\n              }\n            }\n            break;\n        }\n        if (save) {\n          covmat[lprop] = value;\n        }\n      }\n    }\n    return converted;\n  }\n  preload() {\n    for (const mn in this.materialsInfo) {\n      this.create(mn);\n    }\n  }\n  getIndex(materialName) {\n    return this.nameLookup[materialName];\n  }\n  getAsArray() {\n    let index = 0;\n    for (const mn in this.materialsInfo) {\n      this.materialsArray[index] = this.create(mn);\n      this.nameLookup[mn] = index;\n      index++;\n    }\n    return this.materialsArray;\n  }\n  create(materialName) {\n    if (this.materials[materialName] === void 0) {\n      this.createMaterial_(materialName);\n    }\n    return this.materials[materialName];\n  }\n  createMaterial_(materialName) {\n    const scope = this;\n    const mat = this.materialsInfo[materialName];\n    const params = {\n      name: materialName,\n      side: this.side\n    };\n    function resolveURL(baseUrl, url) {\n      if (typeof url !== \"string\" || url === \"\") return \"\";\n      if (/^https?:\\/\\//i.test(url)) return url;\n      return baseUrl + url;\n    }\n    function setMapForType(mapType, value) {\n      if (params[mapType]) return;\n      const texParams = scope.getTextureParams(value, params);\n      const map = scope.loadTexture(resolveURL(scope.baseUrl, texParams.url));\n      map.repeat.copy(texParams.scale);\n      map.offset.copy(texParams.offset);\n      map.wrapS = scope.wrap;\n      map.wrapT = scope.wrap;\n      params[mapType] = map;\n    }\n    for (const prop in mat) {\n      const value = mat[prop];\n      let n;\n      if (value === \"\") continue;\n      switch (prop.toLowerCase()) {\n        case \"kd\":\n          params.color = new Color().fromArray(value);\n          break;\n        case \"ks\":\n          params.specular = new Color().fromArray(value);\n          break;\n        case \"ke\":\n          params.emissive = new Color().fromArray(value);\n          break;\n        case \"map_kd\":\n          setMapForType(\"map\", value);\n          break;\n        case \"map_ks\":\n          setMapForType(\"specularMap\", value);\n          break;\n        case \"map_ke\":\n          setMapForType(\"emissiveMap\", value);\n          break;\n        case \"norm\":\n          setMapForType(\"normalMap\", value);\n          break;\n        case \"map_bump\":\n        case \"bump\":\n          setMapForType(\"bumpMap\", value);\n          break;\n        case \"map_d\":\n          setMapForType(\"alphaMap\", value);\n          params.transparent = true;\n          break;\n        case \"ns\":\n          params.shininess = parseFloat(value);\n          break;\n        case \"d\":\n          n = parseFloat(value);\n          if (n < 1) {\n            params.opacity = n;\n            params.transparent = true;\n          }\n          break;\n        case \"tr\":\n          n = parseFloat(value);\n          if (this.options && this.options.invertTrProperty) n = 1 - n;\n          if (n > 0) {\n            params.opacity = 1 - n;\n            params.transparent = true;\n          }\n          break;\n      }\n    }\n    this.materials[materialName] = new MeshPhongMaterial(params);\n    return this.materials[materialName];\n  }\n  getTextureParams(value, matParams) {\n    const texParams = {\n      scale: new Vector2(1, 1),\n      offset: new Vector2(0, 0)\n    };\n    const items = value.split(/\\s+/);\n    let pos;\n    pos = items.indexOf(\"-bm\");\n    if (pos >= 0) {\n      matParams.bumpScale = parseFloat(items[pos + 1]);\n      items.splice(pos, 2);\n    }\n    pos = items.indexOf(\"-s\");\n    if (pos >= 0) {\n      texParams.scale.set(parseFloat(items[pos + 1]), parseFloat(items[pos + 2]));\n      items.splice(pos, 4);\n    }\n    pos = items.indexOf(\"-o\");\n    if (pos >= 0) {\n      texParams.offset.set(parseFloat(items[pos + 1]), parseFloat(items[pos + 2]));\n      items.splice(pos, 4);\n    }\n    texParams.url = items.join(\" \").trim();\n    return texParams;\n  }\n  loadTexture(url, mapping, onLoad, onProgress, onError) {\n    const manager = this.manager !== void 0 ? this.manager : DefaultLoadingManager;\n    let loader = manager.getHandler(url);\n    if (loader === null) {\n      loader = new TextureLoader(manager);\n    }\n    if (loader.setCrossOrigin) loader.setCrossOrigin(this.crossOrigin);\n    const texture = loader.load(url, onLoad, onProgress, onError);\n    if (mapping !== void 0) texture.mapping = mapping;\n    return texture;\n  }\n}\nexport { MTLLoader };", "map": {"version": 3, "names": ["MTLLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "path", "LoaderUtils", "extractUrlBase", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "setMaterialOptions", "value", "materialOptions", "lines", "split", "info", "delimiter_pattern", "materialsInfo", "i", "length", "line", "trim", "char<PERSON>t", "pos", "indexOf", "key", "substring", "toLowerCase", "name", "ss", "parseFloat", "materialCreator", "MaterialCreator", "resourcePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "setManager", "setMaterials", "baseUrl", "options", "materials", "materialsArray", "nameLookup", "side", "FrontSide", "wrap", "RepeatWrapping", "convert", "converted", "mn", "mat", "covmat", "prop", "save", "lprop", "normalizeRGB", "ignoreZeroRGBs", "preload", "create", "getIndex", "materialName", "getAsArray", "index", "createMaterial_", "params", "resolveURL", "test", "setMapForType", "mapType", "texParams", "getTextureParams", "map", "loadTexture", "repeat", "copy", "scale", "offset", "wrapS", "wrapT", "n", "color", "Color", "fromArray", "specular", "emissive", "transparent", "shininess", "opacity", "invertTrProperty", "MeshPhongMaterial", "mat<PERSON><PERSON><PERSON>", "Vector2", "items", "bumpScale", "splice", "set", "join", "mapping", "DefaultLoadingManager", "<PERSON><PERSON><PERSON><PERSON>", "TextureLoader", "texture"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\MTLLoader.js"], "sourcesContent": ["import {\n  Color,\n  DefaultLoadingManager,\n  FileLoader,\n  FrontSide,\n  Loader,\n  LoaderUtils,\n  MeshPhongMaterial,\n  RepeatWrapping,\n  TextureLoader,\n  Vector2,\n} from 'three'\n\n/**\n * Loads a Wavefront .mtl file specifying materials\n */\n\nclass MTLLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  /**\n   * Loads and parses a MTL asset from a URL.\n   *\n   * @param {String} url - URL to the MTL file.\n   * @param {Function} [onLoad] - Callback invoked with the loaded object.\n   * @param {Function} [onProgress] - Callback for download progress.\n   * @param {Function} [onError] - Callback for download errors.\n   *\n   * @see setPath setResourcePath\n   *\n   * @note In order for relative texture references to resolve correctly\n   * you must call setResourcePath() explicitly prior to load.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = this.path === '' ? LoaderUtils.extractUrlBase(url) : this.path\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  setMaterialOptions(value) {\n    this.materialOptions = value\n    return this\n  }\n\n  /**\n   * Parses a MTL file.\n   *\n   * @param {String} text - Content of MTL file\n   * @return {MaterialCreator}\n   *\n   * @see setPath setResourcePath\n   *\n   * @note In order for relative texture references to resolve correctly\n   * you must call setResourcePath() explicitly prior to parse.\n   */\n  parse(text, path) {\n    const lines = text.split('\\n')\n    let info = {}\n    const delimiter_pattern = /\\s+/\n    const materialsInfo = {}\n\n    for (let i = 0; i < lines.length; i++) {\n      let line = lines[i]\n      line = line.trim()\n\n      if (line.length === 0 || line.charAt(0) === '#') {\n        // Blank line or comment ignore\n        continue\n      }\n\n      const pos = line.indexOf(' ')\n\n      let key = pos >= 0 ? line.substring(0, pos) : line\n      key = key.toLowerCase()\n\n      let value = pos >= 0 ? line.substring(pos + 1) : ''\n      value = value.trim()\n\n      if (key === 'newmtl') {\n        // New material\n\n        info = { name: value }\n        materialsInfo[value] = info\n      } else {\n        if (key === 'ka' || key === 'kd' || key === 'ks' || key === 'ke') {\n          const ss = value.split(delimiter_pattern, 3)\n          info[key] = [parseFloat(ss[0]), parseFloat(ss[1]), parseFloat(ss[2])]\n        } else {\n          info[key] = value\n        }\n      }\n    }\n\n    const materialCreator = new MaterialCreator(this.resourcePath || path, this.materialOptions)\n    materialCreator.setCrossOrigin(this.crossOrigin)\n    materialCreator.setManager(this.manager)\n    materialCreator.setMaterials(materialsInfo)\n    return materialCreator\n  }\n}\n\n/**\n * Create a new MTLLoader.MaterialCreator\n * @param baseUrl - Url relative to which textures are loaded\n * @param options - Set of options on how to construct the materials\n *                  side: Which side to apply the material\n *                        FrontSide (default), THREE.BackSide, THREE.DoubleSide\n *                  wrap: What type of wrapping to apply for textures\n *                        RepeatWrapping (default), THREE.ClampToEdgeWrapping, THREE.MirroredRepeatWrapping\n *                  normalizeRGB: RGBs need to be normalized to 0-1 from 0-255\n *                                Default: false, assumed to be already normalized\n *                  ignoreZeroRGBs: Ignore values of RGBs (Ka,Kd,Ks) that are all 0's\n *                                  Default: false\n * @constructor\n */\n\nclass MaterialCreator {\n  constructor(baseUrl = '', options = {}) {\n    this.baseUrl = baseUrl\n    this.options = options\n    this.materialsInfo = {}\n    this.materials = {}\n    this.materialsArray = []\n    this.nameLookup = {}\n\n    this.crossOrigin = 'anonymous'\n\n    this.side = this.options.side !== undefined ? this.options.side : FrontSide\n    this.wrap = this.options.wrap !== undefined ? this.options.wrap : RepeatWrapping\n  }\n\n  setCrossOrigin(value) {\n    this.crossOrigin = value\n    return this\n  }\n\n  setManager(value) {\n    this.manager = value\n  }\n\n  setMaterials(materialsInfo) {\n    this.materialsInfo = this.convert(materialsInfo)\n    this.materials = {}\n    this.materialsArray = []\n    this.nameLookup = {}\n  }\n\n  convert(materialsInfo) {\n    if (!this.options) return materialsInfo\n\n    const converted = {}\n\n    for (const mn in materialsInfo) {\n      // Convert materials info into normalized form based on options\n\n      const mat = materialsInfo[mn]\n\n      const covmat = {}\n\n      converted[mn] = covmat\n\n      for (const prop in mat) {\n        let save = true\n        let value = mat[prop]\n        const lprop = prop.toLowerCase()\n\n        switch (lprop) {\n          case 'kd':\n          case 'ka':\n          case 'ks':\n            // Diffuse color (color under white light) using RGB values\n\n            if (this.options && this.options.normalizeRGB) {\n              value = [value[0] / 255, value[1] / 255, value[2] / 255]\n            }\n\n            if (this.options && this.options.ignoreZeroRGBs) {\n              if (value[0] === 0 && value[1] === 0 && value[2] === 0) {\n                // ignore\n\n                save = false\n              }\n            }\n\n            break\n\n          default:\n            break\n        }\n\n        if (save) {\n          covmat[lprop] = value\n        }\n      }\n    }\n\n    return converted\n  }\n\n  preload() {\n    for (const mn in this.materialsInfo) {\n      this.create(mn)\n    }\n  }\n\n  getIndex(materialName) {\n    return this.nameLookup[materialName]\n  }\n\n  getAsArray() {\n    let index = 0\n\n    for (const mn in this.materialsInfo) {\n      this.materialsArray[index] = this.create(mn)\n      this.nameLookup[mn] = index\n      index++\n    }\n\n    return this.materialsArray\n  }\n\n  create(materialName) {\n    if (this.materials[materialName] === undefined) {\n      this.createMaterial_(materialName)\n    }\n\n    return this.materials[materialName]\n  }\n\n  createMaterial_(materialName) {\n    // Create material\n\n    const scope = this\n    const mat = this.materialsInfo[materialName]\n    const params = {\n      name: materialName,\n      side: this.side,\n    }\n\n    function resolveURL(baseUrl, url) {\n      if (typeof url !== 'string' || url === '') return ''\n\n      // Absolute URL\n      if (/^https?:\\/\\//i.test(url)) return url\n\n      return baseUrl + url\n    }\n\n    function setMapForType(mapType, value) {\n      if (params[mapType]) return // Keep the first encountered texture\n\n      const texParams = scope.getTextureParams(value, params)\n      const map = scope.loadTexture(resolveURL(scope.baseUrl, texParams.url))\n\n      map.repeat.copy(texParams.scale)\n      map.offset.copy(texParams.offset)\n\n      map.wrapS = scope.wrap\n      map.wrapT = scope.wrap\n\n      params[mapType] = map\n    }\n\n    for (const prop in mat) {\n      const value = mat[prop]\n      let n\n\n      if (value === '') continue\n\n      switch (prop.toLowerCase()) {\n        // Ns is material specular exponent\n\n        case 'kd':\n          // Diffuse color (color under white light) using RGB values\n\n          params.color = new Color().fromArray(value)\n\n          break\n\n        case 'ks':\n          // Specular color (color when light is reflected from shiny surface) using RGB values\n          params.specular = new Color().fromArray(value)\n\n          break\n\n        case 'ke':\n          // Emissive using RGB values\n          params.emissive = new Color().fromArray(value)\n\n          break\n\n        case 'map_kd':\n          // Diffuse texture map\n\n          setMapForType('map', value)\n\n          break\n\n        case 'map_ks':\n          // Specular map\n\n          setMapForType('specularMap', value)\n\n          break\n\n        case 'map_ke':\n          // Emissive map\n\n          setMapForType('emissiveMap', value)\n\n          break\n\n        case 'norm':\n          setMapForType('normalMap', value)\n\n          break\n\n        case 'map_bump':\n        case 'bump':\n          // Bump texture map\n\n          setMapForType('bumpMap', value)\n\n          break\n\n        case 'map_d':\n          // Alpha map\n\n          setMapForType('alphaMap', value)\n          params.transparent = true\n\n          break\n\n        case 'ns':\n          // The specular exponent (defines the focus of the specular highlight)\n          // A high exponent results in a tight, concentrated highlight. Ns values normally range from 0 to 1000.\n\n          params.shininess = parseFloat(value)\n\n          break\n\n        case 'd':\n          n = parseFloat(value)\n\n          if (n < 1) {\n            params.opacity = n\n            params.transparent = true\n          }\n\n          break\n\n        case 'tr':\n          n = parseFloat(value)\n\n          if (this.options && this.options.invertTrProperty) n = 1 - n\n\n          if (n > 0) {\n            params.opacity = 1 - n\n            params.transparent = true\n          }\n\n          break\n\n        default:\n          break\n      }\n    }\n\n    this.materials[materialName] = new MeshPhongMaterial(params)\n    return this.materials[materialName]\n  }\n\n  getTextureParams(value, matParams) {\n    const texParams = {\n      scale: new Vector2(1, 1),\n      offset: new Vector2(0, 0),\n    }\n\n    const items = value.split(/\\s+/)\n    let pos\n\n    pos = items.indexOf('-bm')\n\n    if (pos >= 0) {\n      matParams.bumpScale = parseFloat(items[pos + 1])\n      items.splice(pos, 2)\n    }\n\n    pos = items.indexOf('-s')\n\n    if (pos >= 0) {\n      texParams.scale.set(parseFloat(items[pos + 1]), parseFloat(items[pos + 2]))\n      items.splice(pos, 4) // we expect 3 parameters here!\n    }\n\n    pos = items.indexOf('-o')\n\n    if (pos >= 0) {\n      texParams.offset.set(parseFloat(items[pos + 1]), parseFloat(items[pos + 2]))\n      items.splice(pos, 4) // we expect 3 parameters here!\n    }\n\n    texParams.url = items.join(' ').trim()\n    return texParams\n  }\n\n  loadTexture(url, mapping, onLoad, onProgress, onError) {\n    const manager = this.manager !== undefined ? this.manager : DefaultLoadingManager\n    let loader = manager.getHandler(url)\n\n    if (loader === null) {\n      loader = new TextureLoader(manager)\n    }\n\n    if (loader.setCrossOrigin) loader.setCrossOrigin(this.crossOrigin)\n\n    const texture = loader.load(url, onLoad, onProgress, onError)\n\n    if (mapping !== undefined) texture.mapping = mapping\n\n    return texture\n  }\n}\n\nexport { MTLLoader }\n"], "mappings": ";AAiBA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAeDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,IAAA,GAAO,KAAKA,IAAA,KAAS,KAAKC,WAAA,CAAYC,cAAA,CAAeP,GAAG,IAAI,KAAKK,IAAA;IAEvE,MAAMG,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKX,OAAO;IAC1CU,MAAA,CAAOE,OAAA,CAAQ,KAAKL,IAAI;IACxBG,MAAA,CAAOG,gBAAA,CAAiB,KAAKC,aAAa;IAC1CJ,MAAA,CAAOK,kBAAA,CAAmB,KAAKC,eAAe;IAC9CN,MAAA,CAAOT,IAAA,CACLC,GAAA,EACA,UAAUe,IAAA,EAAM;MACd,IAAI;QACFd,MAAA,CAAOG,KAAA,CAAMY,KAAA,CAAMD,IAAA,EAAMV,IAAI,CAAC;MAC/B,SAAQY,CAAA,EAAP;QACA,IAAId,OAAA,EAAS;UACXA,OAAA,CAAQc,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDb,KAAA,CAAMN,OAAA,CAAQsB,SAAA,CAAUpB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDkB,mBAAmBC,KAAA,EAAO;IACxB,KAAKC,eAAA,GAAkBD,KAAA;IACvB,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaDN,MAAMD,IAAA,EAAMV,IAAA,EAAM;IAChB,MAAMmB,KAAA,GAAQT,IAAA,CAAKU,KAAA,CAAM,IAAI;IAC7B,IAAIC,IAAA,GAAO,CAAE;IACb,MAAMC,iBAAA,GAAoB;IAC1B,MAAMC,aAAA,GAAgB,CAAE;IAExB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIL,KAAA,CAAMM,MAAA,EAAQD,CAAA,IAAK;MACrC,IAAIE,IAAA,GAAOP,KAAA,CAAMK,CAAC;MAClBE,IAAA,GAAOA,IAAA,CAAKC,IAAA,CAAM;MAElB,IAAID,IAAA,CAAKD,MAAA,KAAW,KAAKC,IAAA,CAAKE,MAAA,CAAO,CAAC,MAAM,KAAK;QAE/C;MACD;MAED,MAAMC,GAAA,GAAMH,IAAA,CAAKI,OAAA,CAAQ,GAAG;MAE5B,IAAIC,GAAA,GAAMF,GAAA,IAAO,IAAIH,IAAA,CAAKM,SAAA,CAAU,GAAGH,GAAG,IAAIH,IAAA;MAC9CK,GAAA,GAAMA,GAAA,CAAIE,WAAA,CAAa;MAEvB,IAAIhB,KAAA,GAAQY,GAAA,IAAO,IAAIH,IAAA,CAAKM,SAAA,CAAUH,GAAA,GAAM,CAAC,IAAI;MACjDZ,KAAA,GAAQA,KAAA,CAAMU,IAAA,CAAM;MAEpB,IAAII,GAAA,KAAQ,UAAU;QAGpBV,IAAA,GAAO;UAAEa,IAAA,EAAMjB;QAAO;QACtBM,aAAA,CAAcN,KAAK,IAAII,IAAA;MAC/B,OAAa;QACL,IAAIU,GAAA,KAAQ,QAAQA,GAAA,KAAQ,QAAQA,GAAA,KAAQ,QAAQA,GAAA,KAAQ,MAAM;UAChE,MAAMI,EAAA,GAAKlB,KAAA,CAAMG,KAAA,CAAME,iBAAA,EAAmB,CAAC;UAC3CD,IAAA,CAAKU,GAAG,IAAI,CAACK,UAAA,CAAWD,EAAA,CAAG,CAAC,CAAC,GAAGC,UAAA,CAAWD,EAAA,CAAG,CAAC,CAAC,GAAGC,UAAA,CAAWD,EAAA,CAAG,CAAC,CAAC,CAAC;QAC9E,OAAe;UACLd,IAAA,CAAKU,GAAG,IAAId,KAAA;QACb;MACF;IACF;IAED,MAAMoB,eAAA,GAAkB,IAAIC,eAAA,CAAgB,KAAKC,YAAA,IAAgBvC,IAAA,EAAM,KAAKkB,eAAe;IAC3FmB,eAAA,CAAgBG,cAAA,CAAe,KAAKC,WAAW;IAC/CJ,eAAA,CAAgBK,UAAA,CAAW,KAAKjD,OAAO;IACvC4C,eAAA,CAAgBM,YAAA,CAAapB,aAAa;IAC1C,OAAOc,eAAA;EACR;AACH;AAiBA,MAAMC,eAAA,CAAgB;EACpB9C,YAAYoD,OAAA,GAAU,IAAIC,OAAA,GAAU,IAAI;IACtC,KAAKD,OAAA,GAAUA,OAAA;IACf,KAAKC,OAAA,GAAUA,OAAA;IACf,KAAKtB,aAAA,GAAgB,CAAE;IACvB,KAAKuB,SAAA,GAAY,CAAE;IACnB,KAAKC,cAAA,GAAiB,EAAE;IACxB,KAAKC,UAAA,GAAa,CAAE;IAEpB,KAAKP,WAAA,GAAc;IAEnB,KAAKQ,IAAA,GAAO,KAAKJ,OAAA,CAAQI,IAAA,KAAS,SAAY,KAAKJ,OAAA,CAAQI,IAAA,GAAOC,SAAA;IAClE,KAAKC,IAAA,GAAO,KAAKN,OAAA,CAAQM,IAAA,KAAS,SAAY,KAAKN,OAAA,CAAQM,IAAA,GAAOC,cAAA;EACnE;EAEDZ,eAAevB,KAAA,EAAO;IACpB,KAAKwB,WAAA,GAAcxB,KAAA;IACnB,OAAO;EACR;EAEDyB,WAAWzB,KAAA,EAAO;IAChB,KAAKxB,OAAA,GAAUwB,KAAA;EAChB;EAED0B,aAAapB,aAAA,EAAe;IAC1B,KAAKA,aAAA,GAAgB,KAAK8B,OAAA,CAAQ9B,aAAa;IAC/C,KAAKuB,SAAA,GAAY,CAAE;IACnB,KAAKC,cAAA,GAAiB,EAAE;IACxB,KAAKC,UAAA,GAAa,CAAE;EACrB;EAEDK,QAAQ9B,aAAA,EAAe;IACrB,IAAI,CAAC,KAAKsB,OAAA,EAAS,OAAOtB,aAAA;IAE1B,MAAM+B,SAAA,GAAY,CAAE;IAEpB,WAAWC,EAAA,IAAMhC,aAAA,EAAe;MAG9B,MAAMiC,GAAA,GAAMjC,aAAA,CAAcgC,EAAE;MAE5B,MAAME,MAAA,GAAS,CAAE;MAEjBH,SAAA,CAAUC,EAAE,IAAIE,MAAA;MAEhB,WAAWC,IAAA,IAAQF,GAAA,EAAK;QACtB,IAAIG,IAAA,GAAO;QACX,IAAI1C,KAAA,GAAQuC,GAAA,CAAIE,IAAI;QACpB,MAAME,KAAA,GAAQF,IAAA,CAAKzB,WAAA,CAAa;QAEhC,QAAQ2B,KAAA;UACN,KAAK;UACL,KAAK;UACL,KAAK;YAGH,IAAI,KAAKf,OAAA,IAAW,KAAKA,OAAA,CAAQgB,YAAA,EAAc;cAC7C5C,KAAA,GAAQ,CAACA,KAAA,CAAM,CAAC,IAAI,KAAKA,KAAA,CAAM,CAAC,IAAI,KAAKA,KAAA,CAAM,CAAC,IAAI,GAAG;YACxD;YAED,IAAI,KAAK4B,OAAA,IAAW,KAAKA,OAAA,CAAQiB,cAAA,EAAgB;cAC/C,IAAI7C,KAAA,CAAM,CAAC,MAAM,KAAKA,KAAA,CAAM,CAAC,MAAM,KAAKA,KAAA,CAAM,CAAC,MAAM,GAAG;gBAGtD0C,IAAA,GAAO;cACR;YACF;YAED;QAIH;QAED,IAAIA,IAAA,EAAM;UACRF,MAAA,CAAOG,KAAK,IAAI3C,KAAA;QACjB;MACF;IACF;IAED,OAAOqC,SAAA;EACR;EAEDS,QAAA,EAAU;IACR,WAAWR,EAAA,IAAM,KAAKhC,aAAA,EAAe;MACnC,KAAKyC,MAAA,CAAOT,EAAE;IACf;EACF;EAEDU,SAASC,YAAA,EAAc;IACrB,OAAO,KAAKlB,UAAA,CAAWkB,YAAY;EACpC;EAEDC,WAAA,EAAa;IACX,IAAIC,KAAA,GAAQ;IAEZ,WAAWb,EAAA,IAAM,KAAKhC,aAAA,EAAe;MACnC,KAAKwB,cAAA,CAAeqB,KAAK,IAAI,KAAKJ,MAAA,CAAOT,EAAE;MAC3C,KAAKP,UAAA,CAAWO,EAAE,IAAIa,KAAA;MACtBA,KAAA;IACD;IAED,OAAO,KAAKrB,cAAA;EACb;EAEDiB,OAAOE,YAAA,EAAc;IACnB,IAAI,KAAKpB,SAAA,CAAUoB,YAAY,MAAM,QAAW;MAC9C,KAAKG,eAAA,CAAgBH,YAAY;IAClC;IAED,OAAO,KAAKpB,SAAA,CAAUoB,YAAY;EACnC;EAEDG,gBAAgBH,YAAA,EAAc;IAG5B,MAAMnE,KAAA,GAAQ;IACd,MAAMyD,GAAA,GAAM,KAAKjC,aAAA,CAAc2C,YAAY;IAC3C,MAAMI,MAAA,GAAS;MACbpC,IAAA,EAAMgC,YAAA;MACNjB,IAAA,EAAM,KAAKA;IACZ;IAED,SAASsB,WAAW3B,OAAA,EAASjD,GAAA,EAAK;MAChC,IAAI,OAAOA,GAAA,KAAQ,YAAYA,GAAA,KAAQ,IAAI,OAAO;MAGlD,IAAI,gBAAgB6E,IAAA,CAAK7E,GAAG,GAAG,OAAOA,GAAA;MAEtC,OAAOiD,OAAA,GAAUjD,GAAA;IAClB;IAED,SAAS8E,cAAcC,OAAA,EAASzD,KAAA,EAAO;MACrC,IAAIqD,MAAA,CAAOI,OAAO,GAAG;MAErB,MAAMC,SAAA,GAAY5E,KAAA,CAAM6E,gBAAA,CAAiB3D,KAAA,EAAOqD,MAAM;MACtD,MAAMO,GAAA,GAAM9E,KAAA,CAAM+E,WAAA,CAAYP,UAAA,CAAWxE,KAAA,CAAM6C,OAAA,EAAS+B,SAAA,CAAUhF,GAAG,CAAC;MAEtEkF,GAAA,CAAIE,MAAA,CAAOC,IAAA,CAAKL,SAAA,CAAUM,KAAK;MAC/BJ,GAAA,CAAIK,MAAA,CAAOF,IAAA,CAAKL,SAAA,CAAUO,MAAM;MAEhCL,GAAA,CAAIM,KAAA,GAAQpF,KAAA,CAAMoD,IAAA;MAClB0B,GAAA,CAAIO,KAAA,GAAQrF,KAAA,CAAMoD,IAAA;MAElBmB,MAAA,CAAOI,OAAO,IAAIG,GAAA;IACnB;IAED,WAAWnB,IAAA,IAAQF,GAAA,EAAK;MACtB,MAAMvC,KAAA,GAAQuC,GAAA,CAAIE,IAAI;MACtB,IAAI2B,CAAA;MAEJ,IAAIpE,KAAA,KAAU,IAAI;MAElB,QAAQyC,IAAA,CAAKzB,WAAA,CAAa;QAGxB,KAAK;UAGHqC,MAAA,CAAOgB,KAAA,GAAQ,IAAIC,KAAA,CAAK,EAAGC,SAAA,CAAUvE,KAAK;UAE1C;QAEF,KAAK;UAEHqD,MAAA,CAAOmB,QAAA,GAAW,IAAIF,KAAA,CAAK,EAAGC,SAAA,CAAUvE,KAAK;UAE7C;QAEF,KAAK;UAEHqD,MAAA,CAAOoB,QAAA,GAAW,IAAIH,KAAA,CAAK,EAAGC,SAAA,CAAUvE,KAAK;UAE7C;QAEF,KAAK;UAGHwD,aAAA,CAAc,OAAOxD,KAAK;UAE1B;QAEF,KAAK;UAGHwD,aAAA,CAAc,eAAexD,KAAK;UAElC;QAEF,KAAK;UAGHwD,aAAA,CAAc,eAAexD,KAAK;UAElC;QAEF,KAAK;UACHwD,aAAA,CAAc,aAAaxD,KAAK;UAEhC;QAEF,KAAK;QACL,KAAK;UAGHwD,aAAA,CAAc,WAAWxD,KAAK;UAE9B;QAEF,KAAK;UAGHwD,aAAA,CAAc,YAAYxD,KAAK;UAC/BqD,MAAA,CAAOqB,WAAA,GAAc;UAErB;QAEF,KAAK;UAIHrB,MAAA,CAAOsB,SAAA,GAAYxD,UAAA,CAAWnB,KAAK;UAEnC;QAEF,KAAK;UACHoE,CAAA,GAAIjD,UAAA,CAAWnB,KAAK;UAEpB,IAAIoE,CAAA,GAAI,GAAG;YACTf,MAAA,CAAOuB,OAAA,GAAUR,CAAA;YACjBf,MAAA,CAAOqB,WAAA,GAAc;UACtB;UAED;QAEF,KAAK;UACHN,CAAA,GAAIjD,UAAA,CAAWnB,KAAK;UAEpB,IAAI,KAAK4B,OAAA,IAAW,KAAKA,OAAA,CAAQiD,gBAAA,EAAkBT,CAAA,GAAI,IAAIA,CAAA;UAE3D,IAAIA,CAAA,GAAI,GAAG;YACTf,MAAA,CAAOuB,OAAA,GAAU,IAAIR,CAAA;YACrBf,MAAA,CAAOqB,WAAA,GAAc;UACtB;UAED;MAIH;IACF;IAED,KAAK7C,SAAA,CAAUoB,YAAY,IAAI,IAAI6B,iBAAA,CAAkBzB,MAAM;IAC3D,OAAO,KAAKxB,SAAA,CAAUoB,YAAY;EACnC;EAEDU,iBAAiB3D,KAAA,EAAO+E,SAAA,EAAW;IACjC,MAAMrB,SAAA,GAAY;MAChBM,KAAA,EAAO,IAAIgB,OAAA,CAAQ,GAAG,CAAC;MACvBf,MAAA,EAAQ,IAAIe,OAAA,CAAQ,GAAG,CAAC;IACzB;IAED,MAAMC,KAAA,GAAQjF,KAAA,CAAMG,KAAA,CAAM,KAAK;IAC/B,IAAIS,GAAA;IAEJA,GAAA,GAAMqE,KAAA,CAAMpE,OAAA,CAAQ,KAAK;IAEzB,IAAID,GAAA,IAAO,GAAG;MACZmE,SAAA,CAAUG,SAAA,GAAY/D,UAAA,CAAW8D,KAAA,CAAMrE,GAAA,GAAM,CAAC,CAAC;MAC/CqE,KAAA,CAAME,MAAA,CAAOvE,GAAA,EAAK,CAAC;IACpB;IAEDA,GAAA,GAAMqE,KAAA,CAAMpE,OAAA,CAAQ,IAAI;IAExB,IAAID,GAAA,IAAO,GAAG;MACZ8C,SAAA,CAAUM,KAAA,CAAMoB,GAAA,CAAIjE,UAAA,CAAW8D,KAAA,CAAMrE,GAAA,GAAM,CAAC,CAAC,GAAGO,UAAA,CAAW8D,KAAA,CAAMrE,GAAA,GAAM,CAAC,CAAC,CAAC;MAC1EqE,KAAA,CAAME,MAAA,CAAOvE,GAAA,EAAK,CAAC;IACpB;IAEDA,GAAA,GAAMqE,KAAA,CAAMpE,OAAA,CAAQ,IAAI;IAExB,IAAID,GAAA,IAAO,GAAG;MACZ8C,SAAA,CAAUO,MAAA,CAAOmB,GAAA,CAAIjE,UAAA,CAAW8D,KAAA,CAAMrE,GAAA,GAAM,CAAC,CAAC,GAAGO,UAAA,CAAW8D,KAAA,CAAMrE,GAAA,GAAM,CAAC,CAAC,CAAC;MAC3EqE,KAAA,CAAME,MAAA,CAAOvE,GAAA,EAAK,CAAC;IACpB;IAED8C,SAAA,CAAUhF,GAAA,GAAMuG,KAAA,CAAMI,IAAA,CAAK,GAAG,EAAE3E,IAAA,CAAM;IACtC,OAAOgD,SAAA;EACR;EAEDG,YAAYnF,GAAA,EAAK4G,OAAA,EAAS3G,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrD,MAAML,OAAA,GAAU,KAAKA,OAAA,KAAY,SAAY,KAAKA,OAAA,GAAU+G,qBAAA;IAC5D,IAAIrG,MAAA,GAASV,OAAA,CAAQgH,UAAA,CAAW9G,GAAG;IAEnC,IAAIQ,MAAA,KAAW,MAAM;MACnBA,MAAA,GAAS,IAAIuG,aAAA,CAAcjH,OAAO;IACnC;IAED,IAAIU,MAAA,CAAOqC,cAAA,EAAgBrC,MAAA,CAAOqC,cAAA,CAAe,KAAKC,WAAW;IAEjE,MAAMkE,OAAA,GAAUxG,MAAA,CAAOT,IAAA,CAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAO;IAE5D,IAAIyG,OAAA,KAAY,QAAWI,OAAA,CAAQJ,OAAA,GAAUA,OAAA;IAE7C,OAAOI,OAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}