{"ast": null, "code": "import { WebGLRenderTarget, LinearFilter, HalfFloatType, Vector2, Scene, Camera, BufferGeometry, BufferAttribute, Mesh } from 'three';\nimport { ConvolutionMaterial } from './ConvolutionMaterial.js';\nclass BlurPass {\n  constructor({\n    gl,\n    resolution,\n    width = 500,\n    height = 500,\n    minDepthThreshold = 0,\n    maxDepthThreshold = 1,\n    depthScale = 0,\n    depthToBlurRatioBias = 0.25\n  }) {\n    this.renderToScreen = false;\n    this.renderTargetA = new WebGLRenderTarget(resolution, resolution, {\n      minFilter: LinearFilter,\n      magFilter: LinearFilter,\n      stencilBuffer: false,\n      depthBuffer: false,\n      type: HalfFloatType\n    });\n    this.renderTargetB = this.renderTargetA.clone();\n    this.convolutionMaterial = new ConvolutionMaterial();\n    this.convolutionMaterial.setTexelSize(1.0 / width, 1.0 / height);\n    this.convolutionMaterial.setResolution(new Vector2(width, height));\n    this.scene = new Scene();\n    this.camera = new Camera();\n    this.convolutionMaterial.uniforms.minDepthThreshold.value = minDepthThreshold;\n    this.convolutionMaterial.uniforms.maxDepthThreshold.value = maxDepthThreshold;\n    this.convolutionMaterial.uniforms.depthScale.value = depthScale;\n    this.convolutionMaterial.uniforms.depthToBlurRatioBias.value = depthToBlurRatioBias;\n    this.convolutionMaterial.defines.USE_DEPTH = depthScale > 0;\n    const vertices = new Float32Array([-1, -1, 0, 3, -1, 0, -1, 3, 0]);\n    const uvs = new Float32Array([0, 0, 2, 0, 0, 2]);\n    const geometry = new BufferGeometry();\n    geometry.setAttribute('position', new BufferAttribute(vertices, 3));\n    geometry.setAttribute('uv', new BufferAttribute(uvs, 2));\n    this.screen = new Mesh(geometry, this.convolutionMaterial);\n    this.screen.frustumCulled = false;\n    this.scene.add(this.screen);\n  }\n  render(renderer, inputBuffer, outputBuffer) {\n    const scene = this.scene;\n    const camera = this.camera;\n    const renderTargetA = this.renderTargetA;\n    const renderTargetB = this.renderTargetB;\n    let material = this.convolutionMaterial;\n    let uniforms = material.uniforms;\n    uniforms.depthBuffer.value = inputBuffer.depthTexture;\n    const kernel = material.kernel;\n    let lastRT = inputBuffer;\n    let destRT;\n    let i, l; // Apply the multi-pass blur.\n\n    for (i = 0, l = kernel.length - 1; i < l; ++i) {\n      // Alternate between targets.\n      destRT = (i & 1) === 0 ? renderTargetA : renderTargetB;\n      uniforms.kernel.value = kernel[i];\n      uniforms.inputBuffer.value = lastRT.texture;\n      renderer.setRenderTarget(destRT);\n      renderer.render(scene, camera);\n      lastRT = destRT;\n    }\n    uniforms.kernel.value = kernel[i];\n    uniforms.inputBuffer.value = lastRT.texture;\n    renderer.setRenderTarget(this.renderToScreen ? null : outputBuffer);\n    renderer.render(scene, camera);\n  }\n}\nexport { BlurPass };", "map": {"version": 3, "names": ["WebGLRenderTarget", "LinearFilter", "HalfFloatType", "Vector2", "Scene", "Camera", "BufferGeometry", "BufferAttribute", "<PERSON><PERSON>", "ConvolutionMaterial", "BlurPass", "constructor", "gl", "resolution", "width", "height", "minDepthThr<PERSON>old", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depthScale", "depthToBlurRatioBias", "renderToScreen", "renderTargetA", "minFilter", "magFilter", "stencil<PERSON>uffer", "depthBuffer", "type", "renderTargetB", "clone", "convolutionMaterial", "setTexelSize", "setResolution", "scene", "camera", "uniforms", "value", "defines", "USE_DEPTH", "vertices", "Float32Array", "uvs", "geometry", "setAttribute", "screen", "frustumCulled", "add", "render", "renderer", "inputBuffer", "outputBuffer", "material", "depthTexture", "kernel", "lastRT", "destRT", "i", "l", "length", "texture", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/materials/BlurPass.js"], "sourcesContent": ["import { WebGLRenderTarget, LinearFilter, HalfFloatType, Vector2, Scene, Camera, BufferGeometry, BufferAttribute, Mesh } from 'three';\nimport { ConvolutionMaterial } from './ConvolutionMaterial.js';\n\nclass BlurPass {\n  constructor({\n    gl,\n    resolution,\n    width = 500,\n    height = 500,\n    minDepthThreshold = 0,\n    maxDepthThreshold = 1,\n    depthScale = 0,\n    depthToBlurRatioBias = 0.25\n  }) {\n    this.renderToScreen = false;\n    this.renderTargetA = new WebGLRenderTarget(resolution, resolution, {\n      minFilter: LinearFilter,\n      magFilter: LinearFilter,\n      stencilBuffer: false,\n      depthBuffer: false,\n      type: HalfFloatType\n    });\n    this.renderTargetB = this.renderTargetA.clone();\n    this.convolutionMaterial = new ConvolutionMaterial();\n    this.convolutionMaterial.setTexelSize(1.0 / width, 1.0 / height);\n    this.convolutionMaterial.setResolution(new Vector2(width, height));\n    this.scene = new Scene();\n    this.camera = new Camera();\n    this.convolutionMaterial.uniforms.minDepthThreshold.value = minDepthThreshold;\n    this.convolutionMaterial.uniforms.maxDepthThreshold.value = maxDepthThreshold;\n    this.convolutionMaterial.uniforms.depthScale.value = depthScale;\n    this.convolutionMaterial.uniforms.depthToBlurRatioBias.value = depthToBlurRatioBias;\n    this.convolutionMaterial.defines.USE_DEPTH = depthScale > 0;\n    const vertices = new Float32Array([-1, -1, 0, 3, -1, 0, -1, 3, 0]);\n    const uvs = new Float32Array([0, 0, 2, 0, 0, 2]);\n    const geometry = new BufferGeometry();\n    geometry.setAttribute('position', new BufferAttribute(vertices, 3));\n    geometry.setAttribute('uv', new BufferAttribute(uvs, 2));\n    this.screen = new Mesh(geometry, this.convolutionMaterial);\n    this.screen.frustumCulled = false;\n    this.scene.add(this.screen);\n  }\n\n  render(renderer, inputBuffer, outputBuffer) {\n    const scene = this.scene;\n    const camera = this.camera;\n    const renderTargetA = this.renderTargetA;\n    const renderTargetB = this.renderTargetB;\n    let material = this.convolutionMaterial;\n    let uniforms = material.uniforms;\n    uniforms.depthBuffer.value = inputBuffer.depthTexture;\n    const kernel = material.kernel;\n    let lastRT = inputBuffer;\n    let destRT;\n    let i, l; // Apply the multi-pass blur.\n\n    for (i = 0, l = kernel.length - 1; i < l; ++i) {\n      // Alternate between targets.\n      destRT = (i & 1) === 0 ? renderTargetA : renderTargetB;\n      uniforms.kernel.value = kernel[i];\n      uniforms.inputBuffer.value = lastRT.texture;\n      renderer.setRenderTarget(destRT);\n      renderer.render(scene, camera);\n      lastRT = destRT;\n    }\n\n    uniforms.kernel.value = kernel[i];\n    uniforms.inputBuffer.value = lastRT.texture;\n    renderer.setRenderTarget(this.renderToScreen ? null : outputBuffer);\n    renderer.render(scene, camera);\n  }\n\n}\n\nexport { BlurPass };\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,cAAc,EAAEC,eAAe,EAAEC,IAAI,QAAQ,OAAO;AACrI,SAASC,mBAAmB,QAAQ,0BAA0B;AAE9D,MAAMC,QAAQ,CAAC;EACbC,WAAWA,CAAC;IACVC,EAAE;IACFC,UAAU;IACVC,KAAK,GAAG,GAAG;IACXC,MAAM,GAAG,GAAG;IACZC,iBAAiB,GAAG,CAAC;IACrBC,iBAAiB,GAAG,CAAC;IACrBC,UAAU,GAAG,CAAC;IACdC,oBAAoB,GAAG;EACzB,CAAC,EAAE;IACD,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,aAAa,GAAG,IAAIrB,iBAAiB,CAACa,UAAU,EAAEA,UAAU,EAAE;MACjES,SAAS,EAAErB,YAAY;MACvBsB,SAAS,EAAEtB,YAAY;MACvBuB,aAAa,EAAE,KAAK;MACpBC,WAAW,EAAE,KAAK;MAClBC,IAAI,EAAExB;IACR,CAAC,CAAC;IACF,IAAI,CAACyB,aAAa,GAAG,IAAI,CAACN,aAAa,CAACO,KAAK,CAAC,CAAC;IAC/C,IAAI,CAACC,mBAAmB,GAAG,IAAIpB,mBAAmB,CAAC,CAAC;IACpD,IAAI,CAACoB,mBAAmB,CAACC,YAAY,CAAC,GAAG,GAAGhB,KAAK,EAAE,GAAG,GAAGC,MAAM,CAAC;IAChE,IAAI,CAACc,mBAAmB,CAACE,aAAa,CAAC,IAAI5B,OAAO,CAACW,KAAK,EAAEC,MAAM,CAAC,CAAC;IAClE,IAAI,CAACiB,KAAK,GAAG,IAAI5B,KAAK,CAAC,CAAC;IACxB,IAAI,CAAC6B,MAAM,GAAG,IAAI5B,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACwB,mBAAmB,CAACK,QAAQ,CAAClB,iBAAiB,CAACmB,KAAK,GAAGnB,iBAAiB;IAC7E,IAAI,CAACa,mBAAmB,CAACK,QAAQ,CAACjB,iBAAiB,CAACkB,KAAK,GAAGlB,iBAAiB;IAC7E,IAAI,CAACY,mBAAmB,CAACK,QAAQ,CAAChB,UAAU,CAACiB,KAAK,GAAGjB,UAAU;IAC/D,IAAI,CAACW,mBAAmB,CAACK,QAAQ,CAACf,oBAAoB,CAACgB,KAAK,GAAGhB,oBAAoB;IACnF,IAAI,CAACU,mBAAmB,CAACO,OAAO,CAACC,SAAS,GAAGnB,UAAU,GAAG,CAAC;IAC3D,MAAMoB,QAAQ,GAAG,IAAIC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClE,MAAMC,GAAG,GAAG,IAAID,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAChD,MAAME,QAAQ,GAAG,IAAInC,cAAc,CAAC,CAAC;IACrCmC,QAAQ,CAACC,YAAY,CAAC,UAAU,EAAE,IAAInC,eAAe,CAAC+B,QAAQ,EAAE,CAAC,CAAC,CAAC;IACnEG,QAAQ,CAACC,YAAY,CAAC,IAAI,EAAE,IAAInC,eAAe,CAACiC,GAAG,EAAE,CAAC,CAAC,CAAC;IACxD,IAAI,CAACG,MAAM,GAAG,IAAInC,IAAI,CAACiC,QAAQ,EAAE,IAAI,CAACZ,mBAAmB,CAAC;IAC1D,IAAI,CAACc,MAAM,CAACC,aAAa,GAAG,KAAK;IACjC,IAAI,CAACZ,KAAK,CAACa,GAAG,CAAC,IAAI,CAACF,MAAM,CAAC;EAC7B;EAEAG,MAAMA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAE;IAC1C,MAAMjB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMC,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMZ,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,MAAMM,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,IAAIuB,QAAQ,GAAG,IAAI,CAACrB,mBAAmB;IACvC,IAAIK,QAAQ,GAAGgB,QAAQ,CAAChB,QAAQ;IAChCA,QAAQ,CAACT,WAAW,CAACU,KAAK,GAAGa,WAAW,CAACG,YAAY;IACrD,MAAMC,MAAM,GAAGF,QAAQ,CAACE,MAAM;IAC9B,IAAIC,MAAM,GAAGL,WAAW;IACxB,IAAIM,MAAM;IACV,IAAIC,CAAC,EAAEC,CAAC,CAAC,CAAC;;IAEV,KAAKD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC7C;MACAD,MAAM,GAAG,CAACC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAGlC,aAAa,GAAGM,aAAa;MACtDO,QAAQ,CAACkB,MAAM,CAACjB,KAAK,GAAGiB,MAAM,CAACG,CAAC,CAAC;MACjCrB,QAAQ,CAACc,WAAW,CAACb,KAAK,GAAGkB,MAAM,CAACK,OAAO;MAC3CX,QAAQ,CAACY,eAAe,CAACL,MAAM,CAAC;MAChCP,QAAQ,CAACD,MAAM,CAACd,KAAK,EAAEC,MAAM,CAAC;MAC9BoB,MAAM,GAAGC,MAAM;IACjB;IAEApB,QAAQ,CAACkB,MAAM,CAACjB,KAAK,GAAGiB,MAAM,CAACG,CAAC,CAAC;IACjCrB,QAAQ,CAACc,WAAW,CAACb,KAAK,GAAGkB,MAAM,CAACK,OAAO;IAC3CX,QAAQ,CAACY,eAAe,CAAC,IAAI,CAACvC,cAAc,GAAG,IAAI,GAAG6B,YAAY,CAAC;IACnEF,QAAQ,CAACD,MAAM,CAACd,KAAK,EAAEC,MAAM,CAAC;EAChC;AAEF;AAEA,SAASvB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}