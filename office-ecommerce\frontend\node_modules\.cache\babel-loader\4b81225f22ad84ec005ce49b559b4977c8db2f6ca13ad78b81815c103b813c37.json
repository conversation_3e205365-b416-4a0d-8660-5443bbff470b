{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport { Mesh } from 'three';\nimport { SAH, acceleratedRaycast, computeBoundsTree, disposeBoundsTree } from 'three-mesh-bvh';\nconst isMesh = child => child.isMesh;\n/**\n * @deprecated Use the Bvh component instead\n */\n\nfunction useBVH(mesh, options) {\n  options = {\n    strategy: SAH,\n    verbose: false,\n    setBoundingBox: true,\n    maxDepth: 40,\n    maxLeafTris: 10,\n    ...options\n  };\n  React.useEffect(() => {\n    if (mesh.current) {\n      mesh.current.raycast = acceleratedRaycast;\n      const geometry = mesh.current.geometry;\n      geometry.computeBoundsTree = computeBoundsTree;\n      geometry.disposeBoundsTree = disposeBoundsTree;\n      geometry.computeBoundsTree(options);\n      return () => {\n        if (geometry.boundsTree) {\n          geometry.disposeBoundsTree();\n        }\n      };\n    }\n  }, [mesh, JSON.stringify(options)]);\n}\nconst Bvh = /*#__PURE__*/React.forwardRef(({\n  enabled = true,\n  firstHitOnly = false,\n  children,\n  strategy = SAH,\n  verbose = false,\n  setBoundingBox = true,\n  maxDepth = 40,\n  maxLeafTris = 10,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const raycaster = useThree(state => state.raycaster);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useEffect(() => {\n    if (enabled) {\n      const options = {\n        strategy,\n        verbose,\n        setBoundingBox,\n        maxDepth,\n        maxLeafTris\n      };\n      const group = ref.current; // This can only safely work if the component is used once, but there is no alternative.\n      // Hijacking the raycast method to do it for individual meshes is not an option as it would\n      // cost too much memory ...\n\n      raycaster.firstHitOnly = firstHitOnly;\n      group.traverse(child => {\n        // Only include meshes that do not yet have a boundsTree and whose raycast is standard issue\n        if (isMesh(child) && !child.geometry.boundsTree && child.raycast === Mesh.prototype.raycast) {\n          child.raycast = acceleratedRaycast;\n          child.geometry.computeBoundsTree = computeBoundsTree;\n          child.geometry.disposeBoundsTree = disposeBoundsTree;\n          child.geometry.computeBoundsTree(options);\n        }\n      });\n      return () => {\n        delete raycaster.firstHitOnly;\n        group.traverse(child => {\n          if (isMesh(child) && child.geometry.boundsTree) {\n            child.geometry.disposeBoundsTree();\n            child.raycast = Mesh.prototype.raycast;\n          }\n        });\n      };\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), children);\n});\nexport { Bvh, useBVH };", "map": {"version": 3, "names": ["_extends", "useThree", "React", "<PERSON><PERSON>", "SAH", "acceleratedRaycast", "computeBoundsTree", "disposeBoundsTree", "<PERSON><PERSON><PERSON>", "child", "useBVH", "mesh", "options", "strategy", "verbose", "setBoundingBox", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "current", "raycast", "geometry", "boundsTree", "JSON", "stringify", "Bvh", "forwardRef", "enabled", "firstHitOnly", "children", "props", "fref", "ref", "useRef", "raycaster", "state", "useImperativeHandle", "group", "traverse", "prototype", "createElement"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useBVH.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport * as React from 'react';\nimport { Mesh } from 'three';\nimport { SAH, acceleratedRaycast, computeBoundsTree, disposeBoundsTree } from 'three-mesh-bvh';\n\nconst isMesh = child => child.isMesh;\n/**\n * @deprecated Use the Bvh component instead\n */\n\n\nfunction useBVH(mesh, options) {\n  options = {\n    strategy: SAH,\n    verbose: false,\n    setBoundingBox: true,\n    maxDepth: 40,\n    maxLeafTris: 10,\n    ...options\n  };\n  React.useEffect(() => {\n    if (mesh.current) {\n      mesh.current.raycast = acceleratedRaycast;\n      const geometry = mesh.current.geometry;\n      geometry.computeBoundsTree = computeBoundsTree;\n      geometry.disposeBoundsTree = disposeBoundsTree;\n      geometry.computeBoundsTree(options);\n      return () => {\n        if (geometry.boundsTree) {\n          geometry.disposeBoundsTree();\n        }\n      };\n    }\n  }, [mesh, JSON.stringify(options)]);\n}\nconst Bvh = /*#__PURE__*/React.forwardRef(({\n  enabled = true,\n  firstHitOnly = false,\n  children,\n  strategy = SAH,\n  verbose = false,\n  setBoundingBox = true,\n  maxDepth = 40,\n  maxLeafTris = 10,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const raycaster = useThree(state => state.raycaster);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useEffect(() => {\n    if (enabled) {\n      const options = {\n        strategy,\n        verbose,\n        setBoundingBox,\n        maxDepth,\n        maxLeafTris\n      };\n      const group = ref.current; // This can only safely work if the component is used once, but there is no alternative.\n      // Hijacking the raycast method to do it for individual meshes is not an option as it would\n      // cost too much memory ...\n\n      raycaster.firstHitOnly = firstHitOnly;\n      group.traverse(child => {\n        // Only include meshes that do not yet have a boundsTree and whose raycast is standard issue\n        if (isMesh(child) && !child.geometry.boundsTree && child.raycast === Mesh.prototype.raycast) {\n          child.raycast = acceleratedRaycast;\n          child.geometry.computeBoundsTree = computeBoundsTree;\n          child.geometry.disposeBoundsTree = disposeBoundsTree;\n          child.geometry.computeBoundsTree(options);\n        }\n      });\n      return () => {\n        delete raycaster.firstHitOnly;\n        group.traverse(child => {\n          if (isMesh(child) && child.geometry.boundsTree) {\n            child.geometry.disposeBoundsTree();\n            child.raycast = Mesh.prototype.raycast;\n          }\n        });\n      };\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), children);\n});\n\nexport { Bvh, useBVH };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,OAAO;AAC5B,SAASC,GAAG,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,gBAAgB;AAE9F,MAAMC,MAAM,GAAGC,KAAK,IAAIA,KAAK,CAACD,MAAM;AACpC;AACA;AACA;;AAGA,SAASE,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC7BA,OAAO,GAAG;IACRC,QAAQ,EAAET,GAAG;IACbU,OAAO,EAAE,KAAK;IACdC,cAAc,EAAE,IAAI;IACpBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACf,GAAGL;EACL,CAAC;EACDV,KAAK,CAACgB,SAAS,CAAC,MAAM;IACpB,IAAIP,IAAI,CAACQ,OAAO,EAAE;MAChBR,IAAI,CAACQ,OAAO,CAACC,OAAO,GAAGf,kBAAkB;MACzC,MAAMgB,QAAQ,GAAGV,IAAI,CAACQ,OAAO,CAACE,QAAQ;MACtCA,QAAQ,CAACf,iBAAiB,GAAGA,iBAAiB;MAC9Ce,QAAQ,CAACd,iBAAiB,GAAGA,iBAAiB;MAC9Cc,QAAQ,CAACf,iBAAiB,CAACM,OAAO,CAAC;MACnC,OAAO,MAAM;QACX,IAAIS,QAAQ,CAACC,UAAU,EAAE;UACvBD,QAAQ,CAACd,iBAAiB,CAAC,CAAC;QAC9B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACI,IAAI,EAAEY,IAAI,CAACC,SAAS,CAACZ,OAAO,CAAC,CAAC,CAAC;AACrC;AACA,MAAMa,GAAG,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,CAAC;EACzCC,OAAO,GAAG,IAAI;EACdC,YAAY,GAAG,KAAK;EACpBC,QAAQ;EACRhB,QAAQ,GAAGT,GAAG;EACdU,OAAO,GAAG,KAAK;EACfC,cAAc,GAAG,IAAI;EACrBC,QAAQ,GAAG,EAAE;EACbC,WAAW,GAAG,EAAE;EAChB,GAAGa;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAG9B,KAAK,CAAC+B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,SAAS,GAAGjC,QAAQ,CAACkC,KAAK,IAAIA,KAAK,CAACD,SAAS,CAAC;EACpDhC,KAAK,CAACkC,mBAAmB,CAACL,IAAI,EAAE,MAAMC,GAAG,CAACb,OAAO,EAAE,EAAE,CAAC;EACtDjB,KAAK,CAACgB,SAAS,CAAC,MAAM;IACpB,IAAIS,OAAO,EAAE;MACX,MAAMf,OAAO,GAAG;QACdC,QAAQ;QACRC,OAAO;QACPC,cAAc;QACdC,QAAQ;QACRC;MACF,CAAC;MACD,MAAMoB,KAAK,GAAGL,GAAG,CAACb,OAAO,CAAC,CAAC;MAC3B;MACA;;MAEAe,SAAS,CAACN,YAAY,GAAGA,YAAY;MACrCS,KAAK,CAACC,QAAQ,CAAC7B,KAAK,IAAI;QACtB;QACA,IAAID,MAAM,CAACC,KAAK,CAAC,IAAI,CAACA,KAAK,CAACY,QAAQ,CAACC,UAAU,IAAIb,KAAK,CAACW,OAAO,KAAKjB,IAAI,CAACoC,SAAS,CAACnB,OAAO,EAAE;UAC3FX,KAAK,CAACW,OAAO,GAAGf,kBAAkB;UAClCI,KAAK,CAACY,QAAQ,CAACf,iBAAiB,GAAGA,iBAAiB;UACpDG,KAAK,CAACY,QAAQ,CAACd,iBAAiB,GAAGA,iBAAiB;UACpDE,KAAK,CAACY,QAAQ,CAACf,iBAAiB,CAACM,OAAO,CAAC;QAC3C;MACF,CAAC,CAAC;MACF,OAAO,MAAM;QACX,OAAOsB,SAAS,CAACN,YAAY;QAC7BS,KAAK,CAACC,QAAQ,CAAC7B,KAAK,IAAI;UACtB,IAAID,MAAM,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACY,QAAQ,CAACC,UAAU,EAAE;YAC9Cb,KAAK,CAACY,QAAQ,CAACd,iBAAiB,CAAC,CAAC;YAClCE,KAAK,CAACW,OAAO,GAAGjB,IAAI,CAACoC,SAAS,CAACnB,OAAO;UACxC;QACF,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO,aAAalB,KAAK,CAACsC,aAAa,CAAC,OAAO,EAAExC,QAAQ,CAAC;IACxDgC,GAAG,EAAEA;EACP,CAAC,EAAEF,KAAK,CAAC,EAAED,QAAQ,CAAC;AACtB,CAAC,CAAC;AAEF,SAASJ,GAAG,EAAEf,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}