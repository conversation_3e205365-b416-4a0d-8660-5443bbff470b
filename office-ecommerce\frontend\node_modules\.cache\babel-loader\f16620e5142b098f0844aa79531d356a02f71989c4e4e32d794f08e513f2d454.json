{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { OrbitControls as OrbitControls$1 } from 'three-stdlib';\nconst OrbitControls = /*#__PURE__*/React.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new OrbitControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\nexport { OrbitControls };", "map": {"version": 3, "names": ["_extends", "useThree", "useFrame", "React", "OrbitControls", "OrbitControls$1", "forwardRef", "makeDefault", "camera", "regress", "dom<PERSON>lement", "enableDamping", "keyEvents", "onChange", "onStart", "onEnd", "restProps", "ref", "invalidate", "state", "defaultCamera", "gl", "events", "setEvents", "set", "get", "performance", "explCamera", "explDomElement", "connected", "controls", "useMemo", "enabled", "update", "useEffect", "connect", "dispose", "callback", "e", "onStartCb", "onEndCb", "addEventListener", "removeEventListener", "old", "createElement", "object"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/OrbitControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { OrbitControls as OrbitControls$1 } from 'three-stdlib';\n\nconst OrbitControls = /*#__PURE__*/React.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new OrbitControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\nexport { OrbitControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,IAAIC,eAAe,QAAQ,cAAc;AAE/D,MAAMD,aAAa,GAAG,aAAaD,KAAK,CAACG,UAAU,CAAC,CAAC;EACnDC,WAAW;EACXC,MAAM;EACNC,OAAO;EACPC,UAAU;EACVC,aAAa,GAAG,IAAI;EACpBC,SAAS,GAAG,KAAK;EACjBC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,UAAU,GAAGjB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACD,UAAU,CAAC;EACtD,MAAME,aAAa,GAAGnB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACX,MAAM,CAAC;EACrD,MAAMa,EAAE,GAAGpB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACE,EAAE,CAAC;EACtC,MAAMC,MAAM,GAAGrB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,SAAS,GAAGtB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACI,SAAS,CAAC;EACpD,MAAMC,GAAG,GAAGvB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGxB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACM,GAAG,CAAC;EACxC,MAAMC,WAAW,GAAGzB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACO,WAAW,CAAC;EACxD,MAAMC,UAAU,GAAGnB,MAAM,IAAIY,aAAa;EAC1C,MAAMQ,cAAc,GAAGlB,UAAU,IAAIY,MAAM,CAACO,SAAS,IAAIR,EAAE,CAACX,UAAU;EACtE,MAAMoB,QAAQ,GAAG3B,KAAK,CAAC4B,OAAO,CAAC,MAAM,IAAI1B,eAAe,CAACsB,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACnFzB,QAAQ,CAAC,MAAM;IACb,IAAI4B,QAAQ,CAACE,OAAO,EAAEF,QAAQ,CAACG,MAAM,CAAC,CAAC;EACzC,CAAC,EAAE,CAAC,CAAC,CAAC;EACN9B,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpB,IAAItB,SAAS,EAAE;MACbkB,QAAQ,CAACK,OAAO,CAACvB,SAAS,KAAK,IAAI,GAAGgB,cAAc,GAAGhB,SAAS,CAAC;IACnE;IAEAkB,QAAQ,CAACK,OAAO,CAACP,cAAc,CAAC;IAChC,OAAO,MAAM,KAAKE,QAAQ,CAACM,OAAO,CAAC,CAAC;EACtC,CAAC,EAAE,CAACxB,SAAS,EAAEgB,cAAc,EAAEnB,OAAO,EAAEqB,QAAQ,EAAEZ,UAAU,CAAC,CAAC;EAC9Df,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpB,MAAMG,QAAQ,GAAGC,CAAC,IAAI;MACpBpB,UAAU,CAAC,CAAC;MACZ,IAAIT,OAAO,EAAEiB,WAAW,CAACjB,OAAO,CAAC,CAAC;MAClC,IAAII,QAAQ,EAAEA,QAAQ,CAACyB,CAAC,CAAC;IAC3B,CAAC;IAED,MAAMC,SAAS,GAAGD,CAAC,IAAI;MACrB,IAAIxB,OAAO,EAAEA,OAAO,CAACwB,CAAC,CAAC;IACzB,CAAC;IAED,MAAME,OAAO,GAAGF,CAAC,IAAI;MACnB,IAAIvB,KAAK,EAAEA,KAAK,CAACuB,CAAC,CAAC;IACrB,CAAC;IAEDR,QAAQ,CAACW,gBAAgB,CAAC,QAAQ,EAAEJ,QAAQ,CAAC;IAC7CP,QAAQ,CAACW,gBAAgB,CAAC,OAAO,EAAEF,SAAS,CAAC;IAC7CT,QAAQ,CAACW,gBAAgB,CAAC,KAAK,EAAED,OAAO,CAAC;IACzC,OAAO,MAAM;MACXV,QAAQ,CAACY,mBAAmB,CAAC,OAAO,EAAEH,SAAS,CAAC;MAChDT,QAAQ,CAACY,mBAAmB,CAAC,KAAK,EAAEF,OAAO,CAAC;MAC5CV,QAAQ,CAACY,mBAAmB,CAAC,QAAQ,EAAEL,QAAQ,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,CAACxB,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAEe,QAAQ,EAAEZ,UAAU,EAAEK,SAAS,CAAC,CAAC;EAC/DpB,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpB,IAAI3B,WAAW,EAAE;MACf,MAAMoC,GAAG,GAAGlB,GAAG,CAAC,CAAC,CAACK,QAAQ;MAC1BN,GAAG,CAAC;QACFM;MACF,CAAC,CAAC;MACF,OAAO,MAAMN,GAAG,CAAC;QACfM,QAAQ,EAAEa;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpC,WAAW,EAAEuB,QAAQ,CAAC,CAAC;EAC3B,OAAO,aAAa3B,KAAK,CAACyC,aAAa,CAAC,WAAW,EAAE5C,QAAQ,CAAC;IAC5DiB,GAAG,EAAEA,GAAG;IACR4B,MAAM,EAAEf,QAAQ;IAChBnB,aAAa,EAAEA;EACjB,CAAC,EAAEK,SAAS,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,SAASZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}