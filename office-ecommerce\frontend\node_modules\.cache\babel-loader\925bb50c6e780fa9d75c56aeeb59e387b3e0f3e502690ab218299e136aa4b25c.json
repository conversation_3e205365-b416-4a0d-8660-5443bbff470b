{"ast": null, "code": "import { WebGLRenderTarget, HalfFloatType } from \"three\";\nimport { SSAARenderPass } from \"./SSAARenderPass.js\";\nclass TAARenderPass extends SSAARenderPass {\n  constructor(scene, camera, clearColor, clearAlpha) {\n    super(scene, camera, clearColor, clearAlpha);\n    this.sampleLevel = 0;\n    this.accumulate = false;\n    this.accumulateIndex = -1;\n  }\n  render(renderer, writeBuffer, readBuffer, deltaTime) {\n    if (this.accumulate === false) {\n      super.render(renderer, writeBuffer, readBuffer, deltaTime);\n      this.accumulateIndex = -1;\n      return;\n    }\n    const jitterOffsets = _JitterVectors[5];\n    if (this.sampleRenderTarget === void 0) {\n      this.sampleRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, {\n        type: HalfFloatType\n      });\n      this.sampleRenderTarget.texture.name = \"TAARenderPass.sample\";\n    }\n    if (this.holdRenderTarget === void 0) {\n      this.holdRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, {\n        type: HalfFloatType\n      });\n      this.holdRenderTarget.texture.name = \"TAARenderPass.hold\";\n    }\n    if (this.accumulateIndex === -1) {\n      super.render(renderer, this.holdRenderTarget, readBuffer, deltaTime);\n      this.accumulateIndex = 0;\n    }\n    const autoClear = renderer.autoClear;\n    renderer.autoClear = false;\n    renderer.getClearColor(this._oldClearColor);\n    const oldClearAlpha = renderer.getClearAlpha();\n    const sampleWeight = 1 / jitterOffsets.length;\n    if (this.accumulateIndex >= 0 && this.accumulateIndex < jitterOffsets.length) {\n      this.copyUniforms[\"opacity\"].value = sampleWeight;\n      this.copyUniforms[\"tDiffuse\"].value = writeBuffer.texture;\n      const numSamplesPerFrame = Math.pow(2, this.sampleLevel);\n      for (let i = 0; i < numSamplesPerFrame; i++) {\n        const j = this.accumulateIndex;\n        const jitterOffset = jitterOffsets[j];\n        if (this.camera.setViewOffset) {\n          this.camera.setViewOffset(readBuffer.width, readBuffer.height, jitterOffset[0] * 0.0625, jitterOffset[1] * 0.0625,\n          // 0.0625 = 1 / 16\n          readBuffer.width, readBuffer.height);\n        }\n        renderer.setRenderTarget(writeBuffer);\n        renderer.setClearColor(this.clearColor, this.clearAlpha);\n        renderer.clear();\n        renderer.render(this.scene, this.camera);\n        renderer.setRenderTarget(this.sampleRenderTarget);\n        if (this.accumulateIndex === 0) {\n          renderer.setClearColor(0, 0);\n          renderer.clear();\n        }\n        this.fsQuad.render(renderer);\n        this.accumulateIndex++;\n        if (this.accumulateIndex >= jitterOffsets.length) break;\n      }\n      if (this.camera.clearViewOffset) this.camera.clearViewOffset();\n    }\n    renderer.setClearColor(this.clearColor, this.clearAlpha);\n    const accumulationWeight = this.accumulateIndex * sampleWeight;\n    if (accumulationWeight > 0) {\n      this.copyUniforms[\"opacity\"].value = 1;\n      this.copyUniforms[\"tDiffuse\"].value = this.sampleRenderTarget.texture;\n      renderer.setRenderTarget(writeBuffer);\n      renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n    if (accumulationWeight < 1) {\n      this.copyUniforms[\"opacity\"].value = 1 - accumulationWeight;\n      this.copyUniforms[\"tDiffuse\"].value = this.holdRenderTarget.texture;\n      renderer.setRenderTarget(writeBuffer);\n      this.fsQuad.render(renderer);\n    }\n    renderer.autoClear = autoClear;\n    renderer.setClearColor(this._oldClearColor, oldClearAlpha);\n  }\n  dispose() {\n    super.dispose();\n    if (this.sampleRenderTarget !== void 0) this.sampleRenderTarget.dispose();\n    if (this.holdRenderTarget !== void 0) this.holdRenderTarget.dispose();\n  }\n}\nconst _JitterVectors = [[[0, 0]], [[4, 4], [-4, -4]], [[-2, -6], [6, -2], [-6, 2], [2, 6]], [[1, -3], [-1, 3], [5, 1], [-3, -5], [-5, 5], [-7, -1], [3, 7], [7, -7]], [[1, 1], [-1, -3], [-3, 2], [4, -1], [-5, -2], [2, 5], [5, 3], [3, -5], [-2, 6], [0, -7], [-4, -6], [-6, 4], [-8, 0], [7, -4], [6, 7], [-7, -8]], [[-4, -7], [-7, -5], [-3, -5], [-5, -4], [-1, -4], [-2, -2], [-6, -1], [-4, 0], [-7, 1], [-1, 2], [-6, 3], [-3, 3], [-7, 6], [-3, 6], [-5, 7], [-1, 7], [5, -7], [1, -6], [6, -5], [4, -4], [2, -3], [7, -2], [1, -1], [4, -1], [2, 1], [6, 2], [0, 4], [4, 4], [2, 5], [7, 5], [5, 6], [3, 7]]];\nexport { TAARenderPass };", "map": {"version": 3, "names": ["TAARenderPass", "SSAARenderPass", "constructor", "scene", "camera", "clearColor", "clearAlpha", "sampleLevel", "accumulate", "accumulateIndex", "render", "renderer", "writeBuffer", "readBuffer", "deltaTime", "jitterOffsets", "_JitterVectors", "sampleRender<PERSON>arget", "WebGLRenderTarget", "width", "height", "type", "HalfFloatType", "texture", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autoClear", "getClearColor", "_oldClearColor", "oldClearAlpha", "getClearAlpha", "sampleWeight", "length", "copyUniforms", "value", "numSamplesPerFrame", "Math", "pow", "i", "j", "jitterOffset", "setViewOffset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setClearColor", "clear", "fsQuad", "clearViewOffset", "accumulationWeight", "dispose"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\TAARenderPass.js"], "sourcesContent": ["import { HalfFloatType, WebGLRenderTarget } from 'three'\nimport { SSAARenderPass } from './SSAARenderPass'\n\n/**\n *\n * Temporal Anti-Aliasing Render Pass\n *\n * When there is no motion in the scene, the TAA render pass accumulates jittered camera samples across frames to create a high quality anti-aliased result.\n *\n * References:\n *\n * TODO: Add support for motion vector pas so that accumulation of samples across frames can occur on dynamics scenes.\n *\n */\n\nclass TAARenderPass extends SSAARenderPass {\n  constructor(scene, camera, clearColor, clearAlpha) {\n    super(scene, camera, clearColor, clearAlpha)\n\n    this.sampleLevel = 0\n    this.accumulate = false\n    this.accumulateIndex = -1\n  }\n\n  render(renderer, writeBuffer, readBuffer, deltaTime) {\n    if (this.accumulate === false) {\n      super.render(renderer, writeBuffer, readBuffer, deltaTime)\n\n      this.accumulateIndex = -1\n      return\n    }\n\n    const jitterOffsets = _JitterVectors[5]\n\n    if (this.sampleRenderTarget === undefined) {\n      this.sampleRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, { type: HalfFloatType })\n      this.sampleRenderTarget.texture.name = 'TAARenderPass.sample'\n    }\n\n    if (this.holdRenderTarget === undefined) {\n      this.holdRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, { type: HalfFloatType })\n      this.holdRenderTarget.texture.name = 'TAARenderPass.hold'\n    }\n\n    if (this.accumulateIndex === -1) {\n      super.render(renderer, this.holdRenderTarget, readBuffer, deltaTime)\n\n      this.accumulateIndex = 0\n    }\n\n    const autoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    renderer.getClearColor(this._oldClearColor)\n    const oldClearAlpha = renderer.getClearAlpha()\n\n    const sampleWeight = 1.0 / jitterOffsets.length\n\n    if (this.accumulateIndex >= 0 && this.accumulateIndex < jitterOffsets.length) {\n      this.copyUniforms['opacity'].value = sampleWeight\n      this.copyUniforms['tDiffuse'].value = writeBuffer.texture\n\n      // render the scene multiple times, each slightly jitter offset from the last and accumulate the results.\n      const numSamplesPerFrame = Math.pow(2, this.sampleLevel)\n      for (let i = 0; i < numSamplesPerFrame; i++) {\n        const j = this.accumulateIndex\n        const jitterOffset = jitterOffsets[j]\n\n        if (this.camera.setViewOffset) {\n          this.camera.setViewOffset(\n            readBuffer.width,\n            readBuffer.height,\n            jitterOffset[0] * 0.0625,\n            jitterOffset[1] * 0.0625, // 0.0625 = 1 / 16\n            readBuffer.width,\n            readBuffer.height,\n          )\n        }\n\n        renderer.setRenderTarget(writeBuffer)\n        renderer.setClearColor(this.clearColor, this.clearAlpha)\n        renderer.clear()\n        renderer.render(this.scene, this.camera)\n\n        renderer.setRenderTarget(this.sampleRenderTarget)\n        if (this.accumulateIndex === 0) {\n          renderer.setClearColor(0x000000, 0.0)\n          renderer.clear()\n        }\n\n        this.fsQuad.render(renderer)\n\n        this.accumulateIndex++\n\n        if (this.accumulateIndex >= jitterOffsets.length) break\n      }\n\n      if (this.camera.clearViewOffset) this.camera.clearViewOffset()\n    }\n\n    renderer.setClearColor(this.clearColor, this.clearAlpha)\n    const accumulationWeight = this.accumulateIndex * sampleWeight\n\n    if (accumulationWeight > 0) {\n      this.copyUniforms['opacity'].value = 1.0\n      this.copyUniforms['tDiffuse'].value = this.sampleRenderTarget.texture\n      renderer.setRenderTarget(writeBuffer)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n\n    if (accumulationWeight < 1.0) {\n      this.copyUniforms['opacity'].value = 1.0 - accumulationWeight\n      this.copyUniforms['tDiffuse'].value = this.holdRenderTarget.texture\n      renderer.setRenderTarget(writeBuffer)\n      this.fsQuad.render(renderer)\n    }\n\n    renderer.autoClear = autoClear\n    renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n  }\n\n  dispose() {\n    super.dispose()\n\n    if (this.sampleRenderTarget !== undefined) this.sampleRenderTarget.dispose()\n    if (this.holdRenderTarget !== undefined) this.holdRenderTarget.dispose()\n  }\n}\n\n// prettier-ignore\nconst _JitterVectors = [\n\t[\n\t\t[ 0, 0 ]\n\t],\n\t[\n\t\t[ 4, 4 ], [ - 4, - 4 ]\n\t],\n\t[\n\t\t[ - 2, - 6 ], [ 6, - 2 ], [ - 6, 2 ], [ 2, 6 ]\n\t],\n\t[\n\t\t[ 1, - 3 ], [ - 1, 3 ], [ 5, 1 ], [ - 3, - 5 ],\n\t\t[ - 5, 5 ], [ - 7, - 1 ], [ 3, 7 ], [ 7, - 7 ]\n\t],\n\t[\n\t\t[ 1, 1 ], [ - 1, - 3 ], [ - 3, 2 ], [ 4, - 1 ],\n\t\t[ - 5, - 2 ], [ 2, 5 ], [ 5, 3 ], [ 3, - 5 ],\n\t\t[ - 2, 6 ], [ 0, - 7 ], [ - 4, - 6 ], [ - 6, 4 ],\n\t\t[ - 8, 0 ], [ 7, - 4 ], [ 6, 7 ], [ - 7, - 8 ]\n\t],\n\t[\n\t\t[ - 4, - 7 ], [ - 7, - 5 ], [ - 3, - 5 ], [ - 5, - 4 ],\n\t\t[ - 1, - 4 ], [ - 2, - 2 ], [ - 6, - 1 ], [ - 4, 0 ],\n\t\t[ - 7, 1 ], [ - 1, 2 ], [ - 6, 3 ], [ - 3, 3 ],\n\t\t[ - 7, 6 ], [ - 3, 6 ], [ - 5, 7 ], [ - 1, 7 ],\n\t\t[ 5, - 7 ], [ 1, - 6 ], [ 6, - 5 ], [ 4, - 4 ],\n\t\t[ 2, - 3 ], [ 7, - 2 ], [ 1, - 1 ], [ 4, - 1 ],\n\t\t[ 2, 1 ], [ 6, 2 ], [ 0, 4 ], [ 4, 4 ],\n\t\t[ 2, 5 ], [ 7, 5 ], [ 5, 6 ], [ 3, 7 ]\n\t]\n];\n\nexport { TAARenderPass }\n"], "mappings": ";;AAeA,MAAMA,aAAA,SAAsBC,cAAA,CAAe;EACzCC,YAAYC,KAAA,EAAOC,MAAA,EAAQC,UAAA,EAAYC,UAAA,EAAY;IACjD,MAAMH,KAAA,EAAOC,MAAA,EAAQC,UAAA,EAAYC,UAAU;IAE3C,KAAKC,WAAA,GAAc;IACnB,KAAKC,UAAA,GAAa;IAClB,KAAKC,eAAA,GAAkB;EACxB;EAEDC,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAYC,SAAA,EAAW;IACnD,IAAI,KAAKN,UAAA,KAAe,OAAO;MAC7B,MAAME,MAAA,CAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAYC,SAAS;MAEzD,KAAKL,eAAA,GAAkB;MACvB;IACD;IAED,MAAMM,aAAA,GAAgBC,cAAA,CAAe,CAAC;IAEtC,IAAI,KAAKC,kBAAA,KAAuB,QAAW;MACzC,KAAKA,kBAAA,GAAqB,IAAIC,iBAAA,CAAkBL,UAAA,CAAWM,KAAA,EAAON,UAAA,CAAWO,MAAA,EAAQ;QAAEC,IAAA,EAAMC;MAAA,CAAe;MAC5G,KAAKL,kBAAA,CAAmBM,OAAA,CAAQC,IAAA,GAAO;IACxC;IAED,IAAI,KAAKC,gBAAA,KAAqB,QAAW;MACvC,KAAKA,gBAAA,GAAmB,IAAIP,iBAAA,CAAkBL,UAAA,CAAWM,KAAA,EAAON,UAAA,CAAWO,MAAA,EAAQ;QAAEC,IAAA,EAAMC;MAAA,CAAe;MAC1G,KAAKG,gBAAA,CAAiBF,OAAA,CAAQC,IAAA,GAAO;IACtC;IAED,IAAI,KAAKf,eAAA,KAAoB,IAAI;MAC/B,MAAMC,MAAA,CAAOC,QAAA,EAAU,KAAKc,gBAAA,EAAkBZ,UAAA,EAAYC,SAAS;MAEnE,KAAKL,eAAA,GAAkB;IACxB;IAED,MAAMiB,SAAA,GAAYf,QAAA,CAASe,SAAA;IAC3Bf,QAAA,CAASe,SAAA,GAAY;IAErBf,QAAA,CAASgB,aAAA,CAAc,KAAKC,cAAc;IAC1C,MAAMC,aAAA,GAAgBlB,QAAA,CAASmB,aAAA,CAAe;IAE9C,MAAMC,YAAA,GAAe,IAAMhB,aAAA,CAAciB,MAAA;IAEzC,IAAI,KAAKvB,eAAA,IAAmB,KAAK,KAAKA,eAAA,GAAkBM,aAAA,CAAciB,MAAA,EAAQ;MAC5E,KAAKC,YAAA,CAAa,SAAS,EAAEC,KAAA,GAAQH,YAAA;MACrC,KAAKE,YAAA,CAAa,UAAU,EAAEC,KAAA,GAAQtB,WAAA,CAAYW,OAAA;MAGlD,MAAMY,kBAAA,GAAqBC,IAAA,CAAKC,GAAA,CAAI,GAAG,KAAK9B,WAAW;MACvD,SAAS+B,CAAA,GAAI,GAAGA,CAAA,GAAIH,kBAAA,EAAoBG,CAAA,IAAK;QAC3C,MAAMC,CAAA,GAAI,KAAK9B,eAAA;QACf,MAAM+B,YAAA,GAAezB,aAAA,CAAcwB,CAAC;QAEpC,IAAI,KAAKnC,MAAA,CAAOqC,aAAA,EAAe;UAC7B,KAAKrC,MAAA,CAAOqC,aAAA,CACV5B,UAAA,CAAWM,KAAA,EACXN,UAAA,CAAWO,MAAA,EACXoB,YAAA,CAAa,CAAC,IAAI,QAClBA,YAAA,CAAa,CAAC,IAAI;UAAA;UAClB3B,UAAA,CAAWM,KAAA,EACXN,UAAA,CAAWO,MACZ;QACF;QAEDT,QAAA,CAAS+B,eAAA,CAAgB9B,WAAW;QACpCD,QAAA,CAASgC,aAAA,CAAc,KAAKtC,UAAA,EAAY,KAAKC,UAAU;QACvDK,QAAA,CAASiC,KAAA,CAAO;QAChBjC,QAAA,CAASD,MAAA,CAAO,KAAKP,KAAA,EAAO,KAAKC,MAAM;QAEvCO,QAAA,CAAS+B,eAAA,CAAgB,KAAKzB,kBAAkB;QAChD,IAAI,KAAKR,eAAA,KAAoB,GAAG;UAC9BE,QAAA,CAASgC,aAAA,CAAc,GAAU,CAAG;UACpChC,QAAA,CAASiC,KAAA,CAAO;QACjB;QAED,KAAKC,MAAA,CAAOnC,MAAA,CAAOC,QAAQ;QAE3B,KAAKF,eAAA;QAEL,IAAI,KAAKA,eAAA,IAAmBM,aAAA,CAAciB,MAAA,EAAQ;MACnD;MAED,IAAI,KAAK5B,MAAA,CAAO0C,eAAA,EAAiB,KAAK1C,MAAA,CAAO0C,eAAA,CAAiB;IAC/D;IAEDnC,QAAA,CAASgC,aAAA,CAAc,KAAKtC,UAAA,EAAY,KAAKC,UAAU;IACvD,MAAMyC,kBAAA,GAAqB,KAAKtC,eAAA,GAAkBsB,YAAA;IAElD,IAAIgB,kBAAA,GAAqB,GAAG;MAC1B,KAAKd,YAAA,CAAa,SAAS,EAAEC,KAAA,GAAQ;MACrC,KAAKD,YAAA,CAAa,UAAU,EAAEC,KAAA,GAAQ,KAAKjB,kBAAA,CAAmBM,OAAA;MAC9DZ,QAAA,CAAS+B,eAAA,CAAgB9B,WAAW;MACpCD,QAAA,CAASiC,KAAA,CAAO;MAChB,KAAKC,MAAA,CAAOnC,MAAA,CAAOC,QAAQ;IAC5B;IAED,IAAIoC,kBAAA,GAAqB,GAAK;MAC5B,KAAKd,YAAA,CAAa,SAAS,EAAEC,KAAA,GAAQ,IAAMa,kBAAA;MAC3C,KAAKd,YAAA,CAAa,UAAU,EAAEC,KAAA,GAAQ,KAAKT,gBAAA,CAAiBF,OAAA;MAC5DZ,QAAA,CAAS+B,eAAA,CAAgB9B,WAAW;MACpC,KAAKiC,MAAA,CAAOnC,MAAA,CAAOC,QAAQ;IAC5B;IAEDA,QAAA,CAASe,SAAA,GAAYA,SAAA;IACrBf,QAAA,CAASgC,aAAA,CAAc,KAAKf,cAAA,EAAgBC,aAAa;EAC1D;EAEDmB,QAAA,EAAU;IACR,MAAMA,OAAA,CAAS;IAEf,IAAI,KAAK/B,kBAAA,KAAuB,QAAW,KAAKA,kBAAA,CAAmB+B,OAAA,CAAS;IAC5E,IAAI,KAAKvB,gBAAA,KAAqB,QAAW,KAAKA,gBAAA,CAAiBuB,OAAA,CAAS;EACzE;AACH;AAGA,MAAMhC,cAAA,GAAiB,CACtB,CACC,CAAE,GAAG,CAAG,EACR,EACD,CACC,CAAE,GAAG,CAAG,GAAE,CAAE,IAAK,EAAK,EACtB,EACD,CACC,CAAE,IAAK,EAAG,GAAI,CAAE,GAAG,KAAO,CAAE,IAAK,CAAG,GAAE,CAAE,GAAG,CAAG,EAC9C,EACD,CACC,CAAE,GAAG,EAAK,GAAE,CAAE,IAAK,CAAC,GAAI,CAAE,GAAG,CAAG,GAAE,CAAE,IAAK,EAAK,GAC9C,CAAE,IAAK,CAAG,GAAE,CAAE,IAAK,KAAO,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,EAAK,EAC9C,EACD,CACC,CAAE,GAAG,CAAC,GAAI,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,CAAC,GAAI,CAAE,GAAG,EAAK,GAC9C,CAAE,IAAK,EAAK,GAAE,CAAE,GAAG,IAAK,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,EAAK,GAC5C,CAAE,IAAK,CAAG,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,CAAG,GAChD,CAAE,IAAK,CAAG,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,CAAG,GAAE,CAAE,IAAK,EAAK,EAC9C,EACD,CACC,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,EAAK,GACtD,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,CAAG,GACpD,CAAE,IAAK,CAAG,GAAE,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAG,GAC9C,CAAE,IAAK,CAAG,GAAE,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAG,GAC9C,CAAE,GAAG,EAAK,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAK,GAC9C,CAAE,GAAG,EAAK,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAK,GAC9C,CAAE,GAAG,IAAK,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAG,GACtC,CAAE,GAAG,IAAK,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAG,EACtC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}