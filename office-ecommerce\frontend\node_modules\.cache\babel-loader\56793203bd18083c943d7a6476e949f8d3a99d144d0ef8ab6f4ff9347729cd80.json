{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\ProductManagementNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport ProductCard from './components/ProductCard';\nimport ProductForm from './components/ProductForm';\nimport SearchFilters from './components/SearchFilters';\nimport './ProductManagementNew.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductManagementNew = () => {\n  _s();\n  // State management\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(0);\n  const [totalCount, setTotalCount] = useState(0);\n  const [pageSize] = useState(12); // Grid layout works better with 12 items\n\n  // Filter states\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('name');\n  const [sortDirection, setSortDirection] = useState('ASC');\n\n  // UI states\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n      const response = await productsApi.getProducts(params);\n      if (response.success) {\n        var _response$data$pagina, _response$data$pagina2;\n        setProducts(response.data.products || []);\n        setTotalCount(((_response$data$pagina = response.data.pagination) === null || _response$data$pagina === void 0 ? void 0 : _response$data$pagina.totalItems) || 0);\n        setTotalPages(((_response$data$pagina2 = response.data.pagination) === null || _response$data$pagina2 === void 0 ? void 0 : _response$data$pagina2.totalPages) || 0);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // WebSocket event handlers\n  useEffect(() => {\n    const handleProductCreated = data => {\n      toast.success(`New product created: ${data.name}`);\n      fetchProducts();\n    };\n    const handleProductUpdated = data => {\n      toast.info(`Product updated: ${data.name}`);\n      fetchProducts();\n    };\n    const handleProductDeleted = data => {\n      toast.info(`Product deleted: ${data.name}`);\n      fetchProducts();\n    };\n\n    // Register event listeners\n    websocketService.on('productCreated', handleProductCreated);\n    websocketService.on('productUpdated', handleProductUpdated);\n    websocketService.on('productDeleted', handleProductDeleted);\n\n    // Cleanup function\n    return () => {\n      websocketService.off('productCreated', handleProductCreated);\n      websocketService.off('productUpdated', handleProductUpdated);\n      websocketService.off('productDeleted', handleProductDeleted);\n    };\n  }, [fetchProducts]);\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowAddForm(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowAddForm(true);\n  };\n  const handleDeleteProduct = async productId => {\n    if (!window.confirm('Are you sure you want to delete this product?')) {\n      return;\n    }\n    try {\n      const response = await productsApi.deleteProduct(productId);\n      if (response.success) {\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    }\n  };\n  const handleProductSaved = productData => {\n    setShowAddForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n    if (editingProduct) {\n      toast.success('Product updated successfully');\n    } else {\n      toast.success('Product created successfully');\n    }\n  };\n  const handleFormCancel = () => {\n    setShowAddForm(false);\n    setEditingProduct(null);\n  };\n\n  // Handle search and filters\n  const handleSearch = value => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  };\n  const handleCategoryFilter = category => {\n    setSelectedCategory(category);\n    setCurrentPage(1);\n  };\n  const handleStatusFilter = status => {\n    setSelectedStatus(status);\n    setCurrentPage(1);\n  };\n  const handleSort = field => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Pagination handlers\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const handlePrevPage = () => {\n    if (currentPage > 1) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n  const handleNextPage = () => {\n    if (currentPage < totalPages) {\n      setCurrentPage(currentPage + 1);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-management-new\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pm-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pm-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"pm-title\",\n          children: \"Product Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pm-header-stats\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"pm-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pm-stat-number\",\n              children: totalCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pm-stat-label\",\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pm-header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pm-view-toggle\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `pm-view-btn ${viewMode === 'grid' ? 'active' : ''}`,\n            onClick: () => setViewMode('grid'),\n            title: \"Grid View\",\n            children: \"\\u229E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `pm-view-btn ${viewMode === 'list' ? 'active' : ''}`,\n            onClick: () => setViewMode('list'),\n            title: \"List View\",\n            children: \"\\u2630\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"pm-btn pm-btn-primary\",\n          onClick: handleAddProduct,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"pm-btn-icon\",\n            children: \"+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), \"Add Product\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SearchFilters, {\n      searchTerm: searchTerm,\n      selectedCategory: selectedCategory,\n      selectedStatus: selectedStatus,\n      categories: categories,\n      onSearch: handleSearch,\n      onCategoryFilter: handleCategoryFilter,\n      onStatusFilter: handleStatusFilter,\n      onSort: handleSort,\n      sortBy: sortBy,\n      sortDirection: sortDirection\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pm-form-container\",\n      children: /*#__PURE__*/_jsxDEV(ProductForm, {\n        product: editingProduct,\n        categories: categories,\n        onSave: handleProductSaved,\n        onCancel: handleFormCancel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pm-content\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pm-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pm-loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading products...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this) : products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pm-empty\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pm-empty-icon\",\n          children: \"\\uD83D\\uDCE6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Products Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Start by adding your first product to the inventory.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"pm-btn pm-btn-primary\",\n          onClick: handleAddProduct,\n          children: \"Add First Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `pm-products ${viewMode === 'grid' ? 'pm-grid' : 'pm-list'}`,\n          children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product,\n            viewMode: viewMode,\n            onEdit: handleEditProduct,\n            onDelete: handleDeleteProduct\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pm-pagination\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pm-pagination-info\",\n            children: [\"Showing \", (currentPage - 1) * pageSize + 1, \" to \", Math.min(currentPage * pageSize, totalCount), \" of \", totalCount, \" products\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pm-pagination-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pm-btn pm-btn-secondary\",\n              onClick: handlePrevPage,\n              disabled: currentPage === 1,\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this), Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              const page = i + Math.max(1, currentPage - 2);\n              if (page > totalPages) return null;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `pm-btn ${currentPage === page ? 'pm-btn-primary' : 'pm-btn-secondary'}`,\n                onClick: () => handlePageChange(page),\n                children: page\n              }, page, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 23\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pm-btn pm-btn-secondary\",\n              onClick: handleNextPage,\n              disabled: currentPage === totalPages,\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductManagementNew, \"sZDcfi9XwQHVfmw1CoSG4paLMlk=\");\n_c = ProductManagementNew;\nexport default ProductManagementNew;\nvar _c;\n$RefreshReg$(_c, \"ProductManagementNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "toast", "productsApi", "websocketService", "ProductCard", "ProductForm", "SearchFilters", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductManagementNew", "_s", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalCount", "setTotalCount", "pageSize", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedStatus", "setSelectedStatus", "sortBy", "setSortBy", "sortDirection", "setSortDirection", "showAddForm", "setShowAddForm", "editingProduct", "setEditingProduct", "viewMode", "setViewMode", "fetchProducts", "params", "page", "limit", "search", "undefined", "category", "status", "response", "getProducts", "success", "_response$data$pagina", "_response$data$pagina2", "data", "pagination", "totalItems", "error", "console", "fetchCategories", "getCategories", "handleProductCreated", "name", "handleProductUpdated", "info", "handleProductDeleted", "on", "off", "handleAddProduct", "handleEditProduct", "product", "handleDeleteProduct", "productId", "window", "confirm", "deleteProduct", "handleProductSaved", "productData", "handleFormCancel", "handleSearch", "value", "handleCategoryFilter", "handleStatusFilter", "handleSort", "field", "handlePageChange", "handlePrevPage", "handleNextPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "onSearch", "onCategory<PERSON><PERSON>er", "onStatusFilter", "onSort", "onSave", "onCancel", "length", "map", "onEdit", "onDelete", "id", "Math", "min", "disabled", "Array", "from", "_", "i", "max", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/ProductManagementNew.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport ProductCard from './components/ProductCard';\nimport ProductForm from './components/ProductForm';\nimport SearchFilters from './components/SearchFilters';\nimport './ProductManagementNew.css';\n\nconst ProductManagementNew = () => {\n  // State management\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(0);\n  const [totalCount, setTotalCount] = useState(0);\n  const [pageSize] = useState(12); // Grid layout works better with 12 items\n\n  // Filter states\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('name');\n  const [sortDirection, setSortDirection] = useState('ASC');\n\n  // UI states\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n\n      const response = await productsApi.getProducts(params);\n\n      if (response.success) {\n        setProducts(response.data.products || []);\n        setTotalCount(response.data.pagination?.totalItems || 0);\n        setTotalPages(response.data.pagination?.totalPages || 0);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // WebSocket event handlers\n  useEffect(() => {\n    const handleProductCreated = (data) => {\n      toast.success(`New product created: ${data.name}`);\n      fetchProducts();\n    };\n\n    const handleProductUpdated = (data) => {\n      toast.info(`Product updated: ${data.name}`);\n      fetchProducts();\n    };\n\n    const handleProductDeleted = (data) => {\n      toast.info(`Product deleted: ${data.name}`);\n      fetchProducts();\n    };\n\n    // Register event listeners\n    websocketService.on('productCreated', handleProductCreated);\n    websocketService.on('productUpdated', handleProductUpdated);\n    websocketService.on('productDeleted', handleProductDeleted);\n\n    // Cleanup function\n    return () => {\n      websocketService.off('productCreated', handleProductCreated);\n      websocketService.off('productUpdated', handleProductUpdated);\n      websocketService.off('productDeleted', handleProductDeleted);\n    };\n  }, [fetchProducts]);\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowAddForm(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setShowAddForm(true);\n  };\n\n  const handleDeleteProduct = async (productId) => {\n    if (!window.confirm('Are you sure you want to delete this product?')) {\n      return;\n    }\n\n    try {\n      const response = await productsApi.deleteProduct(productId);\n      if (response.success) {\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    }\n  };\n\n  const handleProductSaved = (productData) => {\n    setShowAddForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n\n    if (editingProduct) {\n      toast.success('Product updated successfully');\n    } else {\n      toast.success('Product created successfully');\n    }\n  };\n\n  const handleFormCancel = () => {\n    setShowAddForm(false);\n    setEditingProduct(null);\n  };\n\n  // Handle search and filters\n  const handleSearch = (value) => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  };\n\n  const handleCategoryFilter = (category) => {\n    setSelectedCategory(category);\n    setCurrentPage(1);\n  };\n\n  const handleStatusFilter = (status) => {\n    setSelectedStatus(status);\n    setCurrentPage(1);\n  };\n\n  const handleSort = (field) => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Pagination handlers\n  const handlePageChange = (page) => {\n    setCurrentPage(page);\n  };\n\n  const handlePrevPage = () => {\n    if (currentPage > 1) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n\n  const handleNextPage = () => {\n    if (currentPage < totalPages) {\n      setCurrentPage(currentPage + 1);\n    }\n  };\n\n  return (\n    <div className=\"product-management-new\">\n      {/* Header */}\n      <div className=\"pm-header\">\n        <div className=\"pm-header-content\">\n          <h1 className=\"pm-title\">Product Management</h1>\n          <div className=\"pm-header-stats\">\n            <span className=\"pm-stat\">\n              <span className=\"pm-stat-number\">{totalCount}</span>\n              <span className=\"pm-stat-label\">Total Products</span>\n            </span>\n          </div>\n        </div>\n        <div className=\"pm-header-actions\">\n          <div className=\"pm-view-toggle\">\n            <button\n              className={`pm-view-btn ${viewMode === 'grid' ? 'active' : ''}`}\n              onClick={() => setViewMode('grid')}\n              title=\"Grid View\"\n            >\n              ⊞\n            </button>\n            <button\n              className={`pm-view-btn ${viewMode === 'list' ? 'active' : ''}`}\n              onClick={() => setViewMode('list')}\n              title=\"List View\"\n            >\n              ☰\n            </button>\n          </div>\n          <button\n            className=\"pm-btn pm-btn-primary\"\n            onClick={handleAddProduct}\n          >\n            <span className=\"pm-btn-icon\">+</span>\n            Add Product\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <SearchFilters\n        searchTerm={searchTerm}\n        selectedCategory={selectedCategory}\n        selectedStatus={selectedStatus}\n        categories={categories}\n        onSearch={handleSearch}\n        onCategoryFilter={handleCategoryFilter}\n        onStatusFilter={handleStatusFilter}\n        onSort={handleSort}\n        sortBy={sortBy}\n        sortDirection={sortDirection}\n      />\n\n      {/* Add/Edit Form */}\n      {showAddForm && (\n        <div className=\"pm-form-container\">\n          <ProductForm\n            product={editingProduct}\n            categories={categories}\n            onSave={handleProductSaved}\n            onCancel={handleFormCancel}\n          />\n        </div>\n      )}\n\n      {/* Products Grid/List */}\n      <div className=\"pm-content\">\n        {loading ? (\n          <div className=\"pm-loading\">\n            <div className=\"pm-loading-spinner\"></div>\n            <p>Loading products...</p>\n          </div>\n        ) : products.length === 0 ? (\n          <div className=\"pm-empty\">\n            <div className=\"pm-empty-icon\">📦</div>\n            <h3>No Products Found</h3>\n            <p>Start by adding your first product to the inventory.</p>\n            <button\n              className=\"pm-btn pm-btn-primary\"\n              onClick={handleAddProduct}\n            >\n              Add First Product\n            </button>\n          </div>\n        ) : (\n          <>\n            <div className={`pm-products ${viewMode === 'grid' ? 'pm-grid' : 'pm-list'}`}>\n              {products.map(product => (\n                <ProductCard\n                  key={product.id}\n                  product={product}\n                  viewMode={viewMode}\n                  onEdit={handleEditProduct}\n                  onDelete={handleDeleteProduct}\n                />\n              ))}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n              <div className=\"pm-pagination\">\n                <div className=\"pm-pagination-info\">\n                  Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} products\n                </div>\n                <div className=\"pm-pagination-controls\">\n                  <button\n                    className=\"pm-btn pm-btn-secondary\"\n                    onClick={handlePrevPage}\n                    disabled={currentPage === 1}\n                  >\n                    Previous\n                  </button>\n                  \n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    const page = i + Math.max(1, currentPage - 2);\n                    if (page > totalPages) return null;\n                    \n                    return (\n                      <button\n                        key={page}\n                        className={`pm-btn ${currentPage === page ? 'pm-btn-primary' : 'pm-btn-secondary'}`}\n                        onClick={() => handlePageChange(page)}\n                      >\n                        {page}\n                      </button>\n                    );\n                  })}\n                  \n                  <button\n                    className=\"pm-btn pm-btn-secondary\"\n                    onClick={handleNextPage}\n                    disabled={currentPage === totalPages}\n                  >\n                    Next\n                  </button>\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ProductManagementNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC2B,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;;EAElD;EACA,MAAM4C,aAAa,GAAG1C,WAAW,CAAC,YAAY;IAC5C,IAAI;MACFkB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,MAAM,GAAG;QACbC,IAAI,EAAEzB,WAAW;QACjB0B,KAAK,EAAEpB,QAAQ;QACfqB,MAAM,EAAEpB,UAAU,IAAIqB,SAAS;QAC/BC,QAAQ,EAAEpB,gBAAgB,IAAImB,SAAS;QACvCE,MAAM,EAAEnB,cAAc,IAAIiB,SAAS;QACnCf,MAAM;QACNE;MACF,CAAC;MAED,MAAMgB,QAAQ,GAAG,MAAMhD,WAAW,CAACiD,WAAW,CAACR,MAAM,CAAC;MAEtD,IAAIO,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QACpBxC,WAAW,CAACoC,QAAQ,CAACK,IAAI,CAAC1C,QAAQ,IAAI,EAAE,CAAC;QACzCW,aAAa,CAAC,EAAA6B,qBAAA,GAAAH,QAAQ,CAACK,IAAI,CAACC,UAAU,cAAAH,qBAAA,uBAAxBA,qBAAA,CAA0BI,UAAU,KAAI,CAAC,CAAC;QACxDnC,aAAa,CAAC,EAAAgC,sBAAA,GAAAJ,QAAQ,CAACK,IAAI,CAACC,UAAU,cAAAF,sBAAA,uBAAxBA,sBAAA,CAA0BjC,UAAU,KAAI,CAAC,CAAC;MAC1D,CAAC,MAAM;QACLpB,KAAK,CAACyD,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDzD,KAAK,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACC,WAAW,EAAEM,QAAQ,EAAEC,UAAU,EAAEE,gBAAgB,EAAEE,cAAc,EAAEE,MAAM,EAAEE,aAAa,CAAC,CAAC;;EAEhG;EACA,MAAM0B,eAAe,GAAG5D,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,MAAMkD,QAAQ,GAAG,MAAMhD,WAAW,CAAC2D,aAAa,CAAC,CAAC;MAClD,IAAIX,QAAQ,CAACE,OAAO,EAAE;QACpBpC,aAAa,CAACkC,QAAQ,CAACK,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACd2C,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB3C,SAAS,CAAC,MAAM;IACd6D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA7D,SAAS,CAAC,MAAM;IACd,MAAM+D,oBAAoB,GAAIP,IAAI,IAAK;MACrCtD,KAAK,CAACmD,OAAO,CAAC,wBAAwBG,IAAI,CAACQ,IAAI,EAAE,CAAC;MAClDrB,aAAa,CAAC,CAAC;IACjB,CAAC;IAED,MAAMsB,oBAAoB,GAAIT,IAAI,IAAK;MACrCtD,KAAK,CAACgE,IAAI,CAAC,oBAAoBV,IAAI,CAACQ,IAAI,EAAE,CAAC;MAC3CrB,aAAa,CAAC,CAAC;IACjB,CAAC;IAED,MAAMwB,oBAAoB,GAAIX,IAAI,IAAK;MACrCtD,KAAK,CAACgE,IAAI,CAAC,oBAAoBV,IAAI,CAACQ,IAAI,EAAE,CAAC;MAC3CrB,aAAa,CAAC,CAAC;IACjB,CAAC;;IAED;IACAvC,gBAAgB,CAACgE,EAAE,CAAC,gBAAgB,EAAEL,oBAAoB,CAAC;IAC3D3D,gBAAgB,CAACgE,EAAE,CAAC,gBAAgB,EAAEH,oBAAoB,CAAC;IAC3D7D,gBAAgB,CAACgE,EAAE,CAAC,gBAAgB,EAAED,oBAAoB,CAAC;;IAE3D;IACA,OAAO,MAAM;MACX/D,gBAAgB,CAACiE,GAAG,CAAC,gBAAgB,EAAEN,oBAAoB,CAAC;MAC5D3D,gBAAgB,CAACiE,GAAG,CAAC,gBAAgB,EAAEJ,oBAAoB,CAAC;MAC5D7D,gBAAgB,CAACiE,GAAG,CAAC,gBAAgB,EAAEF,oBAAoB,CAAC;IAC9D,CAAC;EACH,CAAC,EAAE,CAACxB,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM2B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9B,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMiC,iBAAiB,GAAIC,OAAO,IAAK;IACrChC,iBAAiB,CAACgC,OAAO,CAAC;IAC1BlC,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMmC,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMhD,WAAW,CAAC0E,aAAa,CAACH,SAAS,CAAC;MAC3D,IAAIvB,QAAQ,CAACE,OAAO,EAAE;QACpBnD,KAAK,CAACmD,OAAO,CAAC,8BAA8B,CAAC;QAC7CV,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLzC,KAAK,CAACyD,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CzD,KAAK,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IACvC;EACF,CAAC;EAED,MAAMmB,kBAAkB,GAAIC,WAAW,IAAK;IAC1CzC,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBG,aAAa,CAAC,CAAC;IAEf,IAAIJ,cAAc,EAAE;MAClBrC,KAAK,CAACmD,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,MAAM;MACLnD,KAAK,CAACmD,OAAO,CAAC,8BAA8B,CAAC;IAC/C;EACF,CAAC;EAED,MAAM2B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1C,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMyC,YAAY,GAAIC,KAAK,IAAK;IAC9BtD,aAAa,CAACsD,KAAK,CAAC;IACpB7D,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAM8D,oBAAoB,GAAIlC,QAAQ,IAAK;IACzCnB,mBAAmB,CAACmB,QAAQ,CAAC;IAC7B5B,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAM+D,kBAAkB,GAAIlC,MAAM,IAAK;IACrClB,iBAAiB,CAACkB,MAAM,CAAC;IACzB7B,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMgE,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAIrD,MAAM,KAAKqD,KAAK,EAAE;MACpBlD,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACLD,SAAS,CAACoD,KAAK,CAAC;MAChBlD,gBAAgB,CAAC,KAAK,CAAC;IACzB;IACAf,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMkE,gBAAgB,GAAI1C,IAAI,IAAK;IACjCxB,cAAc,CAACwB,IAAI,CAAC;EACtB,CAAC;EAED,MAAM2C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIpE,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMqE,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrE,WAAW,GAAGE,UAAU,EAAE;MAC5BD,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,oBACEX,OAAA;IAAKiF,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAErClF,OAAA;MAAKiF,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlF,OAAA;QAAKiF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClF,OAAA;UAAIiF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDtF,OAAA;UAAKiF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BlF,OAAA;YAAMiF,SAAS,EAAC,SAAS;YAAAC,QAAA,gBACvBlF,OAAA;cAAMiF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEnE;YAAU;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpDtF,OAAA;cAAMiF,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtF,OAAA;QAAKiF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClF,OAAA;UAAKiF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlF,OAAA;YACEiF,SAAS,EAAE,eAAejD,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEuD,OAAO,EAAEA,CAAA,KAAMtD,WAAW,CAAC,MAAM,CAAE;YACnCuD,KAAK,EAAC,WAAW;YAAAN,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtF,OAAA;YACEiF,SAAS,EAAE,eAAejD,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEuD,OAAO,EAAEA,CAAA,KAAMtD,WAAW,CAAC,MAAM,CAAE;YACnCuD,KAAK,EAAC,WAAW;YAAAN,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNtF,OAAA;UACEiF,SAAS,EAAC,uBAAuB;UACjCM,OAAO,EAAE1B,gBAAiB;UAAAqB,QAAA,gBAE1BlF,OAAA;YAAMiF,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA,CAACF,aAAa;MACZoB,UAAU,EAAEA,UAAW;MACvBE,gBAAgB,EAAEA,gBAAiB;MACnCE,cAAc,EAAEA,cAAe;MAC/Bf,UAAU,EAAEA,UAAW;MACvBkF,QAAQ,EAAEjB,YAAa;MACvBkB,gBAAgB,EAAEhB,oBAAqB;MACvCiB,cAAc,EAAEhB,kBAAmB;MACnCiB,MAAM,EAAEhB,UAAW;MACnBpD,MAAM,EAAEA,MAAO;MACfE,aAAa,EAAEA;IAAc;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,EAGD1D,WAAW,iBACV5B,OAAA;MAAKiF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChClF,OAAA,CAACH,WAAW;QACVkE,OAAO,EAAEjC,cAAe;QACxBvB,UAAU,EAAEA,UAAW;QACvBsF,MAAM,EAAExB,kBAAmB;QAC3ByB,QAAQ,EAAEvB;MAAiB;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDtF,OAAA;MAAKiF,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxBzE,OAAO,gBACNT,OAAA;QAAKiF,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlF,OAAA;UAAKiF,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CtF,OAAA;UAAAkF,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,GACJjF,QAAQ,CAAC0F,MAAM,KAAK,CAAC,gBACvB/F,OAAA;QAAKiF,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBlF,OAAA;UAAKiF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCtF,OAAA;UAAAkF,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BtF,OAAA;UAAAkF,QAAA,EAAG;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3DtF,OAAA;UACEiF,SAAS,EAAC,uBAAuB;UACjCM,OAAO,EAAE1B,gBAAiB;UAAAqB,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENtF,OAAA,CAAAE,SAAA;QAAAgF,QAAA,gBACElF,OAAA;UAAKiF,SAAS,EAAE,eAAejD,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,EAAG;UAAAkD,QAAA,EAC1E7E,QAAQ,CAAC2F,GAAG,CAACjC,OAAO,iBACnB/D,OAAA,CAACJ,WAAW;YAEVmE,OAAO,EAAEA,OAAQ;YACjB/B,QAAQ,EAAEA,QAAS;YACnBiE,MAAM,EAAEnC,iBAAkB;YAC1BoC,QAAQ,EAAElC;UAAoB,GAJzBD,OAAO,CAACoC,EAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGLzE,UAAU,GAAG,CAAC,iBACbb,OAAA;UAAKiF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BlF,OAAA;YAAKiF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,UAC1B,EAAE,CAACvE,WAAW,GAAG,CAAC,IAAIM,QAAQ,GAAI,CAAC,EAAC,MAAI,EAACmF,IAAI,CAACC,GAAG,CAAC1F,WAAW,GAAGM,QAAQ,EAAEF,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,WAChH;UAAA;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrClF,OAAA;cACEiF,SAAS,EAAC,yBAAyB;cACnCM,OAAO,EAAER,cAAe;cACxBuB,QAAQ,EAAE3F,WAAW,KAAK,CAAE;cAAAuE,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERiB,KAAK,CAACC,IAAI,CAAC;cAAET,MAAM,EAAEK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAExF,UAAU;YAAE,CAAC,EAAE,CAAC4F,CAAC,EAAEC,CAAC,KAAK;cACzD,MAAMtE,IAAI,GAAGsE,CAAC,GAAGN,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEhG,WAAW,GAAG,CAAC,CAAC;cAC7C,IAAIyB,IAAI,GAAGvB,UAAU,EAAE,OAAO,IAAI;cAElC,oBACEb,OAAA;gBAEEiF,SAAS,EAAE,UAAUtE,WAAW,KAAKyB,IAAI,GAAG,gBAAgB,GAAG,kBAAkB,EAAG;gBACpFmD,OAAO,EAAEA,CAAA,KAAMT,gBAAgB,CAAC1C,IAAI,CAAE;gBAAA8C,QAAA,EAErC9C;cAAI,GAJAA,IAAI;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKH,CAAC;YAEb,CAAC,CAAC,eAEFtF,OAAA;cACEiF,SAAS,EAAC,yBAAyB;cACnCM,OAAO,EAAEP,cAAe;cACxBsB,QAAQ,EAAE3F,WAAW,KAAKE,UAAW;cAAAqE,QAAA,EACtC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,eACD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAjVID,oBAAoB;AAAAyG,EAAA,GAApBzG,oBAAoB;AAmV1B,eAAeA,oBAAoB;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}