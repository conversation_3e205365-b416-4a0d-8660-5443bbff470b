{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useCurrency } from '../contexts/CurrencyContext';\n\n// Custom hook for price formatting and conversion\nexport const usePrice = () => {\n  _s();\n  const {\n    formatPrice,\n    convertPrice,\n    getCurrentCurrency\n  } = useCurrency();\n\n  // Format a single price\n  const formatSinglePrice = priceInUSD => {\n    if (!priceInUSD || isNaN(priceInUSD)) return formatPrice(0);\n    return formatPrice(priceInUSD);\n  };\n\n  // Format price with discount\n  const formatPriceWithDiscount = (originalPrice, discountPrice) => {\n    const formatted = {\n      original: formatSinglePrice(originalPrice),\n      discount: discountPrice ? formatSinglePrice(discountPrice) : null,\n      hasDiscount: !!discountPrice && discountPrice < originalPrice\n    };\n    return formatted;\n  };\n\n  // Calculate savings amount\n  const calculateSavings = (originalPrice, discountPrice) => {\n    if (!discountPrice || discountPrice >= originalPrice) return null;\n    const savings = originalPrice - discountPrice;\n    return formatSinglePrice(savings);\n  };\n\n  // Calculate savings percentage\n  const calculateSavingsPercentage = (originalPrice, discountPrice) => {\n    if (!discountPrice || discountPrice >= originalPrice) return null;\n    const percentage = (originalPrice - discountPrice) / originalPrice * 100;\n    return Math.round(percentage);\n  };\n\n  // Format cart total\n  const formatCartTotal = items => {\n    const total = items.reduce((sum, item) => {\n      const price = item.discountPrice || item.price;\n      return sum + price * item.quantity;\n    }, 0);\n    return formatSinglePrice(total);\n  };\n\n  // Format cart subtotal (before tax/shipping)\n  const formatCartSubtotal = items => {\n    return formatCartTotal(items);\n  };\n\n  // Format tax amount\n  const formatTax = (subtotal, taxRate = 0.08) => {\n    const taxAmount = subtotal * taxRate;\n    return formatSinglePrice(taxAmount);\n  };\n\n  // Format shipping cost\n  const formatShipping = shippingCost => {\n    return formatSinglePrice(shippingCost);\n  };\n\n  // Get current currency symbol\n  const getCurrencySymbol = () => {\n    return getCurrentCurrency().symbol;\n  };\n\n  // Get current currency code\n  const getCurrencyCode = () => {\n    return getCurrentCurrency().code;\n  };\n  return {\n    formatSinglePrice,\n    formatPriceWithDiscount,\n    calculateSavings,\n    calculateSavingsPercentage,\n    formatCartTotal,\n    formatCartSubtotal,\n    formatTax,\n    formatShipping,\n    getCurrencySymbol,\n    getCurrencyCode,\n    convertPrice\n  };\n};\n_s(usePrice, \"GNQi4HjU2rzMKyEPOyg65NI7Ihc=\", false, function () {\n  return [useCurrency];\n});\nexport default usePrice;", "map": {"version": 3, "names": ["useCurrency", "usePrice", "_s", "formatPrice", "convertPrice", "getCurrentCurrency", "formatSinglePrice", "priceInUSD", "isNaN", "formatPriceWithDiscount", "originalPrice", "discountPrice", "formatted", "original", "discount", "hasDiscount", "calculateSavings", "savings", "calculateSavingsPercentage", "percentage", "Math", "round", "formatCartTotal", "items", "total", "reduce", "sum", "item", "price", "quantity", "formatCartSubtotal", "formatTax", "subtotal", "taxRate", "taxAmount", "formatShipping", "shippingCost", "getCurrencySymbol", "symbol", "getCurrencyCode", "code"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/hooks/usePrice.js"], "sourcesContent": ["import { useCurrency } from '../contexts/CurrencyContext';\n\n// Custom hook for price formatting and conversion\nexport const usePrice = () => {\n    const { formatPrice, convertPrice, getCurrentCurrency } = useCurrency();\n\n    // Format a single price\n    const formatSinglePrice = (priceInUSD) => {\n        if (!priceInUSD || isNaN(priceInUSD)) return formatPrice(0);\n        return formatPrice(priceInUSD);\n    };\n\n    // Format price with discount\n    const formatPriceWithDiscount = (originalPrice, discountPrice) => {\n        const formatted = {\n            original: formatSinglePrice(originalPrice),\n            discount: discountPrice ? formatSinglePrice(discountPrice) : null,\n            hasDiscount: !!discountPrice && discountPrice < originalPrice\n        };\n\n        return formatted;\n    };\n\n    // Calculate savings amount\n    const calculateSavings = (originalPrice, discountPrice) => {\n        if (!discountPrice || discountPrice >= originalPrice) return null;\n        const savings = originalPrice - discountPrice;\n        return formatSinglePrice(savings);\n    };\n\n    // Calculate savings percentage\n    const calculateSavingsPercentage = (originalPrice, discountPrice) => {\n        if (!discountPrice || discountPrice >= originalPrice) return null;\n        const percentage = ((originalPrice - discountPrice) / originalPrice) * 100;\n        return Math.round(percentage);\n    };\n\n    // Format cart total\n    const formatCartTotal = (items) => {\n        const total = items.reduce((sum, item) => {\n            const price = item.discountPrice || item.price;\n            return sum + (price * item.quantity);\n        }, 0);\n        return formatSinglePrice(total);\n    };\n\n    // Format cart subtotal (before tax/shipping)\n    const formatCartSubtotal = (items) => {\n        return formatCartTotal(items);\n    };\n\n    // Format tax amount\n    const formatTax = (subtotal, taxRate = 0.08) => {\n        const taxAmount = subtotal * taxRate;\n        return formatSinglePrice(taxAmount);\n    };\n\n    // Format shipping cost\n    const formatShipping = (shippingCost) => {\n        return formatSinglePrice(shippingCost);\n    };\n\n    // Get current currency symbol\n    const getCurrencySymbol = () => {\n        return getCurrentCurrency().symbol;\n    };\n\n    // Get current currency code\n    const getCurrencyCode = () => {\n        return getCurrentCurrency().code;\n    };\n\n    return {\n        formatSinglePrice,\n        formatPriceWithDiscount,\n        calculateSavings,\n        calculateSavingsPercentage,\n        formatCartTotal,\n        formatCartSubtotal,\n        formatTax,\n        formatShipping,\n        getCurrencySymbol,\n        getCurrencyCode,\n        convertPrice\n    };\n};\n\nexport default usePrice;\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,6BAA6B;;AAEzD;AACA,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAmB,CAAC,GAAGL,WAAW,CAAC,CAAC;;EAEvE;EACA,MAAMM,iBAAiB,GAAIC,UAAU,IAAK;IACtC,IAAI,CAACA,UAAU,IAAIC,KAAK,CAACD,UAAU,CAAC,EAAE,OAAOJ,WAAW,CAAC,CAAC,CAAC;IAC3D,OAAOA,WAAW,CAACI,UAAU,CAAC;EAClC,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAACC,aAAa,EAAEC,aAAa,KAAK;IAC9D,MAAMC,SAAS,GAAG;MACdC,QAAQ,EAAEP,iBAAiB,CAACI,aAAa,CAAC;MAC1CI,QAAQ,EAAEH,aAAa,GAAGL,iBAAiB,CAACK,aAAa,CAAC,GAAG,IAAI;MACjEI,WAAW,EAAE,CAAC,CAACJ,aAAa,IAAIA,aAAa,GAAGD;IACpD,CAAC;IAED,OAAOE,SAAS;EACpB,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAACN,aAAa,EAAEC,aAAa,KAAK;IACvD,IAAI,CAACA,aAAa,IAAIA,aAAa,IAAID,aAAa,EAAE,OAAO,IAAI;IACjE,MAAMO,OAAO,GAAGP,aAAa,GAAGC,aAAa;IAC7C,OAAOL,iBAAiB,CAACW,OAAO,CAAC;EACrC,CAAC;;EAED;EACA,MAAMC,0BAA0B,GAAGA,CAACR,aAAa,EAAEC,aAAa,KAAK;IACjE,IAAI,CAACA,aAAa,IAAIA,aAAa,IAAID,aAAa,EAAE,OAAO,IAAI;IACjE,MAAMS,UAAU,GAAI,CAACT,aAAa,GAAGC,aAAa,IAAID,aAAa,GAAI,GAAG;IAC1E,OAAOU,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;EACjC,CAAC;;EAED;EACA,MAAMG,eAAe,GAAIC,KAAK,IAAK;IAC/B,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACtC,MAAMC,KAAK,GAAGD,IAAI,CAAChB,aAAa,IAAIgB,IAAI,CAACC,KAAK;MAC9C,OAAOF,GAAG,GAAIE,KAAK,GAAGD,IAAI,CAACE,QAAS;IACxC,CAAC,EAAE,CAAC,CAAC;IACL,OAAOvB,iBAAiB,CAACkB,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAIP,KAAK,IAAK;IAClC,OAAOD,eAAe,CAACC,KAAK,CAAC;EACjC,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAGA,CAACC,QAAQ,EAAEC,OAAO,GAAG,IAAI,KAAK;IAC5C,MAAMC,SAAS,GAAGF,QAAQ,GAAGC,OAAO;IACpC,OAAO3B,iBAAiB,CAAC4B,SAAS,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,YAAY,IAAK;IACrC,OAAO9B,iBAAiB,CAAC8B,YAAY,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,OAAOhC,kBAAkB,CAAC,CAAC,CAACiC,MAAM;EACtC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B,OAAOlC,kBAAkB,CAAC,CAAC,CAACmC,IAAI;EACpC,CAAC;EAED,OAAO;IACHlC,iBAAiB;IACjBG,uBAAuB;IACvBO,gBAAgB;IAChBE,0BAA0B;IAC1BI,eAAe;IACfQ,kBAAkB;IAClBC,SAAS;IACTI,cAAc;IACdE,iBAAiB;IACjBE,eAAe;IACfnC;EACJ,CAAC;AACL,CAAC;AAACF,EAAA,CAlFWD,QAAQ;EAAA,QACyCD,WAAW;AAAA;AAmFzE,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}