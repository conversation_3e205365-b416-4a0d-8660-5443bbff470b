{"ast": null, "code": "import { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { _ as _setPrototypeOf, a as _isNativeReflectConstruct } from './isNativeReflectConstruct-5594d075.esm.js';\nimport * as THREE from 'three';\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nvar RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {\n  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);\n  var _super = _createSuper(RoundedPlaneGeometry);\n  function RoundedPlaneGeometry() {\n    var _this;\n    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;\n    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;\n    _classCallCheck(this, RoundedPlaneGeometry);\n    _this = _super.call(this); // helper const's\n\n    var wi = width / 2 - radius; // inner width\n\n    var hi = height / 2 - radius; // inner height\n\n    var ul = radius / width; // u left\n\n    var ur = (width - radius) / width; // u right\n\n    var vl = radius / height; // v low\n\n    var vh = (height - radius) / height; // v high\n\n    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];\n    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];\n    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];\n    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];\n    var phi, cos, sin, xc, yc, uc, vc, idx;\n    for (var i = 0; i < 4; i++) {\n      xc = i < 1 || i > 2 ? wi : -wi;\n      yc = i < 2 ? hi : -hi;\n      uc = i < 1 || i > 2 ? ur : ul;\n      vc = i < 2 ? vh : vl;\n      for (var j = 0; j <= segments; j++) {\n        phi = Math.PI / 2 * (i + j / segments);\n        cos = Math.cos(phi);\n        sin = Math.sin(phi);\n        positions.push(xc + radius * cos, yc + radius * sin, 0);\n        uvs.push(uc + ul * cos, vc + vl * sin);\n        if (j < segments) {\n          idx = (segments + 1) * i + j + 4;\n          indices.push(i, idx, idx + 1);\n        }\n      }\n    }\n    _this.setIndex(new THREE.BufferAttribute(new Uint32Array(indices), 1));\n    _this.setAttribute(\"position\", new THREE.BufferAttribute(new Float32Array(positions), 3));\n    _this.setAttribute(\"uv\", new THREE.BufferAttribute(new Float32Array(uvs), 2));\n    return _this;\n  }\n  return RoundedPlaneGeometry;\n}(THREE.BufferGeometry);\nvar geometry = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  RoundedPlaneGeometry: RoundedPlaneGeometry\n});\nexport { RoundedPlaneGeometry as R, geometry as g };", "map": {"version": 3, "names": ["_", "_classCallCheck", "_setPrototypeOf", "a", "_isNativeReflectConstruct", "THREE", "_inherits", "subClass", "superClass", "TypeError", "prototype", "Object", "create", "constructor", "value", "writable", "configurable", "_getPrototypeOf", "o", "setPrototypeOf", "getPrototypeOf", "__proto__", "_assertThisInitialized", "self", "ReferenceError", "_possibleConstructorReturn", "call", "_createSuper", "Derived", "hasNativeReflectConstruct", "_createSuperInternal", "Super", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "arguments", "apply", "RoundedPlaneGeometry", "_THREE$BufferGeometry", "_super", "_this", "width", "length", "undefined", "height", "radius", "segments", "wi", "hi", "ul", "ur", "vl", "vh", "positions", "uvs", "n", "indices", "phi", "cos", "sin", "xc", "yc", "uc", "vc", "idx", "i", "j", "Math", "PI", "push", "setIndex", "BufferAttribute", "Uint32Array", "setAttribute", "Float32Array", "BufferGeometry", "geometry", "freeze", "R", "g"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/maath/dist/geometry-217d0c0b.esm.js"], "sourcesContent": ["import { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { _ as _setPrototypeOf, a as _isNativeReflectConstruct } from './isNativeReflectConstruct-5594d075.esm.js';\nimport * as THREE from 'three';\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nvar RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {\n  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);\n\n  var _super = _createSuper(RoundedPlaneGeometry);\n\n  function RoundedPlaneGeometry() {\n    var _this;\n\n    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;\n    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;\n\n    _classCallCheck(this, RoundedPlaneGeometry);\n\n    _this = _super.call(this); // helper const's\n\n    var wi = width / 2 - radius; // inner width\n\n    var hi = height / 2 - radius; // inner height\n\n    var ul = radius / width; // u left\n\n    var ur = (width - radius) / width; // u right\n\n    var vl = radius / height; // v low\n\n    var vh = (height - radius) / height; // v high\n\n    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];\n    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];\n    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];\n    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];\n    var phi, cos, sin, xc, yc, uc, vc, idx;\n\n    for (var i = 0; i < 4; i++) {\n      xc = i < 1 || i > 2 ? wi : -wi;\n      yc = i < 2 ? hi : -hi;\n      uc = i < 1 || i > 2 ? ur : ul;\n      vc = i < 2 ? vh : vl;\n\n      for (var j = 0; j <= segments; j++) {\n        phi = Math.PI / 2 * (i + j / segments);\n        cos = Math.cos(phi);\n        sin = Math.sin(phi);\n        positions.push(xc + radius * cos, yc + radius * sin, 0);\n        uvs.push(uc + ul * cos, vc + vl * sin);\n\n        if (j < segments) {\n          idx = (segments + 1) * i + j + 4;\n          indices.push(i, idx, idx + 1);\n        }\n      }\n    }\n\n    _this.setIndex(new THREE.BufferAttribute(new Uint32Array(indices), 1));\n\n    _this.setAttribute(\"position\", new THREE.BufferAttribute(new Float32Array(positions), 3));\n\n    _this.setAttribute(\"uv\", new THREE.BufferAttribute(new Float32Array(uvs), 2));\n\n    return _this;\n  }\n\n  return RoundedPlaneGeometry;\n}(THREE.BufferGeometry);\n\nvar geometry = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  RoundedPlaneGeometry: RoundedPlaneGeometry\n});\n\nexport { RoundedPlaneGeometry as R, geometry as g };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,QAAQ,kCAAkC;AACvE,SAASD,CAAC,IAAIE,eAAe,EAAEC,CAAC,IAAIC,yBAAyB,QAAQ,4CAA4C;AACjH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAIC,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEAF,QAAQ,CAACG,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,UAAU,IAAIA,UAAU,CAACE,SAAS,EAAE;IACrEG,WAAW,EAAE;MACXC,KAAK,EAAEP,QAAQ;MACfQ,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAIR,UAAU,EAAEN,eAAe,CAACK,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAASS,eAAeA,CAACC,CAAC,EAAE;EAC1BD,eAAe,GAAGN,MAAM,CAACQ,cAAc,GAAGR,MAAM,CAACS,cAAc,GAAG,SAASH,eAAeA,CAACC,CAAC,EAAE;IAC5F,OAAOA,CAAC,CAACG,SAAS,IAAIV,MAAM,CAACS,cAAc,CAACF,CAAC,CAAC;EAChD,CAAC;EACD,OAAOD,eAAe,CAACC,CAAC,CAAC;AAC3B;AAEA,SAASI,sBAAsBA,CAACC,IAAI,EAAE;EACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,0BAA0BA,CAACF,IAAI,EAAEG,IAAI,EAAE;EAC9C,IAAIA,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IACpE,OAAOA,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIjB,SAAS,CAAC,0DAA0D,CAAC;EACjF;EAEA,OAAOa,sBAAsB,CAACC,IAAI,CAAC;AACrC;AAEA,SAASI,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAIC,yBAAyB,GAAGzB,yBAAyB,CAAC,CAAC;EAC3D,OAAO,SAAS0B,oBAAoBA,CAAA,EAAG;IACrC,IAAIC,KAAK,GAAGd,eAAe,CAACW,OAAO,CAAC;MAChCI,MAAM;IAEV,IAAIH,yBAAyB,EAAE;MAC7B,IAAII,SAAS,GAAGhB,eAAe,CAAC,IAAI,CAAC,CAACJ,WAAW;MACjDmB,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACJ,KAAK,EAAEK,SAAS,EAAEH,SAAS,CAAC;IACzD,CAAC,MAAM;MACLD,MAAM,GAAGD,KAAK,CAACM,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IACvC;IAEA,OAAOX,0BAA0B,CAAC,IAAI,EAAEO,MAAM,CAAC;EACjD,CAAC;AACH;AAEA,IAAIM,oBAAoB,GAAG,aAAa,UAAUC,qBAAqB,EAAE;EACvEjC,SAAS,CAACgC,oBAAoB,EAAEC,qBAAqB,CAAC;EAEtD,IAAIC,MAAM,GAAGb,YAAY,CAACW,oBAAoB,CAAC;EAE/C,SAASA,oBAAoBA,CAAA,EAAG;IAC9B,IAAIG,KAAK;IAET,IAAIC,KAAK,GAAGN,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACjF,IAAIS,MAAM,GAAGT,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,IAAIU,MAAM,GAAGV,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;IACpF,IAAIW,QAAQ,GAAGX,SAAS,CAACO,MAAM,GAAG,CAAC,IAAIP,SAAS,CAAC,CAAC,CAAC,KAAKQ,SAAS,GAAGR,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IAErFnC,eAAe,CAAC,IAAI,EAAEqC,oBAAoB,CAAC;IAE3CG,KAAK,GAAGD,MAAM,CAACd,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE3B,IAAIsB,EAAE,GAAGN,KAAK,GAAG,CAAC,GAAGI,MAAM,CAAC,CAAC;;IAE7B,IAAIG,EAAE,GAAGJ,MAAM,GAAG,CAAC,GAAGC,MAAM,CAAC,CAAC;;IAE9B,IAAII,EAAE,GAAGJ,MAAM,GAAGJ,KAAK,CAAC,CAAC;;IAEzB,IAAIS,EAAE,GAAG,CAACT,KAAK,GAAGI,MAAM,IAAIJ,KAAK,CAAC,CAAC;;IAEnC,IAAIU,EAAE,GAAGN,MAAM,GAAGD,MAAM,CAAC,CAAC;;IAE1B,IAAIQ,EAAE,GAAG,CAACR,MAAM,GAAGC,MAAM,IAAID,MAAM,CAAC,CAAC;;IAErC,IAAIS,SAAS,GAAG,CAACN,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAE,CAACD,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAE,CAACD,EAAE,EAAE,CAACC,EAAE,EAAE,CAAC,EAAED,EAAE,EAAE,CAACC,EAAE,EAAE,CAAC,CAAC;IAChE,IAAIM,GAAG,GAAG,CAACJ,EAAE,EAAEE,EAAE,EAAEH,EAAE,EAAEG,EAAE,EAAEH,EAAE,EAAEE,EAAE,EAAED,EAAE,EAAEC,EAAE,CAAC;IAC1C,IAAII,CAAC,GAAG,CAAC,CAAC,IAAIT,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3K,IAAIU,OAAO,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7H,IAAIE,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG;IAEtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BL,EAAE,GAAGK,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGlB,EAAE,GAAG,CAACA,EAAE;MAC9Bc,EAAE,GAAGI,CAAC,GAAG,CAAC,GAAGjB,EAAE,GAAG,CAACA,EAAE;MACrBc,EAAE,GAAGG,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGf,EAAE,GAAGD,EAAE;MAC7Bc,EAAE,GAAGE,CAAC,GAAG,CAAC,GAAGb,EAAE,GAAGD,EAAE;MAEpB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIpB,QAAQ,EAAEoB,CAAC,EAAE,EAAE;QAClCT,GAAG,GAAGU,IAAI,CAACC,EAAE,GAAG,CAAC,IAAIH,CAAC,GAAGC,CAAC,GAAGpB,QAAQ,CAAC;QACtCY,GAAG,GAAGS,IAAI,CAACT,GAAG,CAACD,GAAG,CAAC;QACnBE,GAAG,GAAGQ,IAAI,CAACR,GAAG,CAACF,GAAG,CAAC;QACnBJ,SAAS,CAACgB,IAAI,CAACT,EAAE,GAAGf,MAAM,GAAGa,GAAG,EAAEG,EAAE,GAAGhB,MAAM,GAAGc,GAAG,EAAE,CAAC,CAAC;QACvDL,GAAG,CAACe,IAAI,CAACP,EAAE,GAAGb,EAAE,GAAGS,GAAG,EAAEK,EAAE,GAAGZ,EAAE,GAAGQ,GAAG,CAAC;QAEtC,IAAIO,CAAC,GAAGpB,QAAQ,EAAE;UAChBkB,GAAG,GAAG,CAAClB,QAAQ,GAAG,CAAC,IAAImB,CAAC,GAAGC,CAAC,GAAG,CAAC;UAChCV,OAAO,CAACa,IAAI,CAACJ,CAAC,EAAED,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAC/B;MACF;IACF;IAEAxB,KAAK,CAAC8B,QAAQ,CAAC,IAAIlE,KAAK,CAACmE,eAAe,CAAC,IAAIC,WAAW,CAAChB,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IAEtEhB,KAAK,CAACiC,YAAY,CAAC,UAAU,EAAE,IAAIrE,KAAK,CAACmE,eAAe,CAAC,IAAIG,YAAY,CAACrB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzFb,KAAK,CAACiC,YAAY,CAAC,IAAI,EAAE,IAAIrE,KAAK,CAACmE,eAAe,CAAC,IAAIG,YAAY,CAACpB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7E,OAAOd,KAAK;EACd;EAEA,OAAOH,oBAAoB;AAC7B,CAAC,CAACjC,KAAK,CAACuE,cAAc,CAAC;AAEvB,IAAIC,QAAQ,GAAG,aAAalE,MAAM,CAACmE,MAAM,CAAC;EACxCzD,SAAS,EAAE,IAAI;EACfiB,oBAAoB,EAAEA;AACxB,CAAC,CAAC;AAEF,SAASA,oBAAoB,IAAIyC,CAAC,EAAEF,QAAQ,IAAIG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}