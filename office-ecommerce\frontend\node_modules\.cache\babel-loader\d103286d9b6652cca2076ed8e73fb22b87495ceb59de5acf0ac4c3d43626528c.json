{"ast": null, "code": "import { Vector3, Matrix4 } from \"three\";\nconst inverseProjectionMatrix = /* @__PURE__ */new Matrix4();\nclass CSMFrustum {\n  constructor(data) {\n    data = data || {};\n    this.vertices = {\n      near: [new Vector3(), new Vector3(), new Vector3(), new Vector3()],\n      far: [new Vector3(), new Vector3(), new Vector3(), new Vector3()]\n    };\n    if (data.projectionMatrix !== void 0) {\n      this.setFromProjectionMatrix(data.projectionMatrix, data.maxFar || 1e4);\n    }\n  }\n  setFromProjectionMatrix(projectionMatrix, maxFar) {\n    const isOrthographic = projectionMatrix.elements[2 * 4 + 3] === 0;\n    inverseProjectionMatrix.copy(projectionMatrix).invert();\n    this.vertices.near[0].set(1, 1, -1);\n    this.vertices.near[1].set(1, -1, -1);\n    this.vertices.near[2].set(-1, -1, -1);\n    this.vertices.near[3].set(-1, 1, -1);\n    this.vertices.near.forEach(function (v) {\n      v.applyMatrix4(inverseProjectionMatrix);\n    });\n    this.vertices.far[0].set(1, 1, 1);\n    this.vertices.far[1].set(1, -1, 1);\n    this.vertices.far[2].set(-1, -1, 1);\n    this.vertices.far[3].set(-1, 1, 1);\n    this.vertices.far.forEach(function (v) {\n      v.applyMatrix4(inverseProjectionMatrix);\n      const absZ = Math.abs(v.z);\n      if (isOrthographic) {\n        v.z *= Math.min(maxFar / absZ, 1);\n      } else {\n        v.multiplyScalar(Math.min(maxFar / absZ, 1));\n      }\n    });\n    return this.vertices;\n  }\n  split(breaks, target) {\n    while (breaks.length > target.length) {\n      target.push(new CSMFrustum());\n    }\n    target.length = breaks.length;\n    for (let i = 0; i < breaks.length; i++) {\n      const cascade = target[i];\n      if (i === 0) {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.near[j].copy(this.vertices.near[j]);\n        }\n      } else {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.near[j].lerpVectors(this.vertices.near[j], this.vertices.far[j], breaks[i - 1]);\n        }\n      }\n      if (i === breaks.length - 1) {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.far[j].copy(this.vertices.far[j]);\n        }\n      } else {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.far[j].lerpVectors(this.vertices.near[j], this.vertices.far[j], breaks[i]);\n        }\n      }\n    }\n  }\n  toSpace(cameraMatrix, target) {\n    for (let i = 0; i < 4; i++) {\n      target.vertices.near[i].copy(this.vertices.near[i]).applyMatrix4(cameraMatrix);\n      target.vertices.far[i].copy(this.vertices.far[i]).applyMatrix4(cameraMatrix);\n    }\n  }\n}\nexport { CSMFrustum };", "map": {"version": 3, "names": ["inverseProjectionMatrix", "Matrix4", "CSMFrustum", "constructor", "data", "vertices", "near", "Vector3", "far", "projectionMatrix", "setFromProjectionMatrix", "maxFar", "isOrthographic", "elements", "copy", "invert", "set", "for<PERSON>ach", "v", "applyMatrix4", "absZ", "Math", "abs", "z", "min", "multiplyScalar", "split", "breaks", "target", "length", "push", "i", "cascade", "j", "lerpVectors", "toSpace", "cameraMatrix"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\csm\\CSMFrustum.js"], "sourcesContent": ["import { Vector3, Matrix4 } from 'three'\n\nconst inverseProjectionMatrix = /* @__PURE__ */ new Matrix4()\n\nclass CSMFrustum {\n  constructor(data) {\n    data = data || {}\n\n    this.vertices = {\n      near: [new Vector3(), new Vector3(), new Vector3(), new Vector3()],\n      far: [new Vector3(), new Vector3(), new Vector3(), new Vector3()],\n    }\n\n    if (data.projectionMatrix !== undefined) {\n      this.setFromProjectionMatrix(data.projectionMatrix, data.maxFar || 10000)\n    }\n  }\n\n  setFromProjectionMatrix(projectionMatrix, maxFar) {\n    const isOrthographic = projectionMatrix.elements[2 * 4 + 3] === 0\n\n    inverseProjectionMatrix.copy(projectionMatrix).invert()\n\n    // 3 --- 0  vertices.near/far order\n    // |     |\n    // 2 --- 1\n    // clip space spans from [-1, 1]\n\n    this.vertices.near[0].set(1, 1, -1)\n    this.vertices.near[1].set(1, -1, -1)\n    this.vertices.near[2].set(-1, -1, -1)\n    this.vertices.near[3].set(-1, 1, -1)\n    this.vertices.near.forEach(function (v) {\n      v.applyMatrix4(inverseProjectionMatrix)\n    })\n\n    this.vertices.far[0].set(1, 1, 1)\n    this.vertices.far[1].set(1, -1, 1)\n    this.vertices.far[2].set(-1, -1, 1)\n    this.vertices.far[3].set(-1, 1, 1)\n    this.vertices.far.forEach(function (v) {\n      v.applyMatrix4(inverseProjectionMatrix)\n\n      const absZ = Math.abs(v.z)\n      if (isOrthographic) {\n        v.z *= Math.min(maxFar / absZ, 1.0)\n      } else {\n        v.multiplyScalar(Math.min(maxFar / absZ, 1.0))\n      }\n    })\n\n    return this.vertices\n  }\n\n  split(breaks, target) {\n    while (breaks.length > target.length) {\n      target.push(new CSMFrustum())\n    }\n\n    target.length = breaks.length\n\n    for (let i = 0; i < breaks.length; i++) {\n      const cascade = target[i]\n\n      if (i === 0) {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.near[j].copy(this.vertices.near[j])\n        }\n      } else {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.near[j].lerpVectors(this.vertices.near[j], this.vertices.far[j], breaks[i - 1])\n        }\n      }\n\n      if (i === breaks.length - 1) {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.far[j].copy(this.vertices.far[j])\n        }\n      } else {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.far[j].lerpVectors(this.vertices.near[j], this.vertices.far[j], breaks[i])\n        }\n      }\n    }\n  }\n\n  toSpace(cameraMatrix, target) {\n    for (let i = 0; i < 4; i++) {\n      target.vertices.near[i].copy(this.vertices.near[i]).applyMatrix4(cameraMatrix)\n\n      target.vertices.far[i].copy(this.vertices.far[i]).applyMatrix4(cameraMatrix)\n    }\n  }\n}\n\nexport { CSMFrustum }\n"], "mappings": ";AAEA,MAAMA,uBAAA,GAA0C,mBAAIC,OAAA,CAAS;AAE7D,MAAMC,UAAA,CAAW;EACfC,YAAYC,IAAA,EAAM;IAChBA,IAAA,GAAOA,IAAA,IAAQ,CAAE;IAEjB,KAAKC,QAAA,GAAW;MACdC,IAAA,EAAM,CAAC,IAAIC,OAAA,CAAO,GAAI,IAAIA,OAAA,CAAS,GAAE,IAAIA,OAAA,CAAO,GAAI,IAAIA,OAAA,EAAS;MACjEC,GAAA,EAAK,CAAC,IAAID,OAAA,CAAO,GAAI,IAAIA,OAAA,CAAS,GAAE,IAAIA,OAAA,CAAO,GAAI,IAAIA,OAAA,EAAS;IACjE;IAED,IAAIH,IAAA,CAAKK,gBAAA,KAAqB,QAAW;MACvC,KAAKC,uBAAA,CAAwBN,IAAA,CAAKK,gBAAA,EAAkBL,IAAA,CAAKO,MAAA,IAAU,GAAK;IACzE;EACF;EAEDD,wBAAwBD,gBAAA,EAAkBE,MAAA,EAAQ;IAChD,MAAMC,cAAA,GAAiBH,gBAAA,CAAiBI,QAAA,CAAS,IAAI,IAAI,CAAC,MAAM;IAEhEb,uBAAA,CAAwBc,IAAA,CAAKL,gBAAgB,EAAEM,MAAA,CAAQ;IAOvD,KAAKV,QAAA,CAASC,IAAA,CAAK,CAAC,EAAEU,GAAA,CAAI,GAAG,GAAG,EAAE;IAClC,KAAKX,QAAA,CAASC,IAAA,CAAK,CAAC,EAAEU,GAAA,CAAI,GAAG,IAAI,EAAE;IACnC,KAAKX,QAAA,CAASC,IAAA,CAAK,CAAC,EAAEU,GAAA,CAAI,IAAI,IAAI,EAAE;IACpC,KAAKX,QAAA,CAASC,IAAA,CAAK,CAAC,EAAEU,GAAA,CAAI,IAAI,GAAG,EAAE;IACnC,KAAKX,QAAA,CAASC,IAAA,CAAKW,OAAA,CAAQ,UAAUC,CAAA,EAAG;MACtCA,CAAA,CAAEC,YAAA,CAAanB,uBAAuB;IAC5C,CAAK;IAED,KAAKK,QAAA,CAASG,GAAA,CAAI,CAAC,EAAEQ,GAAA,CAAI,GAAG,GAAG,CAAC;IAChC,KAAKX,QAAA,CAASG,GAAA,CAAI,CAAC,EAAEQ,GAAA,CAAI,GAAG,IAAI,CAAC;IACjC,KAAKX,QAAA,CAASG,GAAA,CAAI,CAAC,EAAEQ,GAAA,CAAI,IAAI,IAAI,CAAC;IAClC,KAAKX,QAAA,CAASG,GAAA,CAAI,CAAC,EAAEQ,GAAA,CAAI,IAAI,GAAG,CAAC;IACjC,KAAKX,QAAA,CAASG,GAAA,CAAIS,OAAA,CAAQ,UAAUC,CAAA,EAAG;MACrCA,CAAA,CAAEC,YAAA,CAAanB,uBAAuB;MAEtC,MAAMoB,IAAA,GAAOC,IAAA,CAAKC,GAAA,CAAIJ,CAAA,CAAEK,CAAC;MACzB,IAAIX,cAAA,EAAgB;QAClBM,CAAA,CAAEK,CAAA,IAAKF,IAAA,CAAKG,GAAA,CAAIb,MAAA,GAASS,IAAA,EAAM,CAAG;MAC1C,OAAa;QACLF,CAAA,CAAEO,cAAA,CAAeJ,IAAA,CAAKG,GAAA,CAAIb,MAAA,GAASS,IAAA,EAAM,CAAG,CAAC;MAC9C;IACP,CAAK;IAED,OAAO,KAAKf,QAAA;EACb;EAEDqB,MAAMC,MAAA,EAAQC,MAAA,EAAQ;IACpB,OAAOD,MAAA,CAAOE,MAAA,GAASD,MAAA,CAAOC,MAAA,EAAQ;MACpCD,MAAA,CAAOE,IAAA,CAAK,IAAI5B,UAAA,EAAY;IAC7B;IAED0B,MAAA,CAAOC,MAAA,GAASF,MAAA,CAAOE,MAAA;IAEvB,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIJ,MAAA,CAAOE,MAAA,EAAQE,CAAA,IAAK;MACtC,MAAMC,OAAA,GAAUJ,MAAA,CAAOG,CAAC;MAExB,IAAIA,CAAA,KAAM,GAAG;QACX,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1BD,OAAA,CAAQ3B,QAAA,CAASC,IAAA,CAAK2B,CAAC,EAAEnB,IAAA,CAAK,KAAKT,QAAA,CAASC,IAAA,CAAK2B,CAAC,CAAC;QACpD;MACT,OAAa;QACL,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1BD,OAAA,CAAQ3B,QAAA,CAASC,IAAA,CAAK2B,CAAC,EAAEC,WAAA,CAAY,KAAK7B,QAAA,CAASC,IAAA,CAAK2B,CAAC,GAAG,KAAK5B,QAAA,CAASG,GAAA,CAAIyB,CAAC,GAAGN,MAAA,CAAOI,CAAA,GAAI,CAAC,CAAC;QAChG;MACF;MAED,IAAIA,CAAA,KAAMJ,MAAA,CAAOE,MAAA,GAAS,GAAG;QAC3B,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1BD,OAAA,CAAQ3B,QAAA,CAASG,GAAA,CAAIyB,CAAC,EAAEnB,IAAA,CAAK,KAAKT,QAAA,CAASG,GAAA,CAAIyB,CAAC,CAAC;QAClD;MACT,OAAa;QACL,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1BD,OAAA,CAAQ3B,QAAA,CAASG,GAAA,CAAIyB,CAAC,EAAEC,WAAA,CAAY,KAAK7B,QAAA,CAASC,IAAA,CAAK2B,CAAC,GAAG,KAAK5B,QAAA,CAASG,GAAA,CAAIyB,CAAC,GAAGN,MAAA,CAAOI,CAAC,CAAC;QAC3F;MACF;IACF;EACF;EAEDI,QAAQC,YAAA,EAAcR,MAAA,EAAQ;IAC5B,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1BH,MAAA,CAAOvB,QAAA,CAASC,IAAA,CAAKyB,CAAC,EAAEjB,IAAA,CAAK,KAAKT,QAAA,CAASC,IAAA,CAAKyB,CAAC,CAAC,EAAEZ,YAAA,CAAaiB,YAAY;MAE7ER,MAAA,CAAOvB,QAAA,CAASG,GAAA,CAAIuB,CAAC,EAAEjB,IAAA,CAAK,KAAKT,QAAA,CAASG,GAAA,CAAIuB,CAAC,CAAC,EAAEZ,YAAA,CAAaiB,YAAY;IAC5E;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}