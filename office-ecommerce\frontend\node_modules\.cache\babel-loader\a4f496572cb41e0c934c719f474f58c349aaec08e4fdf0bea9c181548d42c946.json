{"ast": null, "code": "import * as THREE from 'three';\nimport * as React from 'react';\nimport { DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport create from 'zustand';\nimport { suspend, preload, clear } from 'suspend-react';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\nconst catalogue = {};\nconst extend = objects => void Object.assign(catalogue, objects);\nfunction createRenderer(_roots, _getEventPriority) {\n  function createInstance(type, {\n    args = [],\n    attach,\n    ...props\n  }, root) {\n    let name = `${type[0].toUpperCase()}${type.slice(1)}`;\n    let instance;\n    if (type === 'primitive') {\n      if (props.object === undefined) throw new Error(\"R3F: Primitives without 'object' are invalid!\");\n      const object = props.object;\n      instance = prepare(object, {\n        type,\n        root,\n        attach,\n        primitive: true\n      });\n    } else {\n      const target = catalogue[name];\n      if (!target) {\n        throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n      }\n\n      // Throw if an object or literal was passed for args\n      if (!Array.isArray(args)) throw new Error('R3F: The args prop must be an array!');\n\n      // Instanciate new object, link it to the root\n      // Append memoized props with args so it's not forgotten\n      instance = prepare(new target(...args), {\n        type,\n        root,\n        attach,\n        // Save args in case we need to reconstruct later for HMR\n        memoizedProps: {\n          args\n        }\n      });\n    }\n\n    // Auto-attach geometries and materials\n    if (instance.__r3f.attach === undefined) {\n      if (instance.isBufferGeometry) instance.__r3f.attach = 'geometry';else if (instance.isMaterial) instance.__r3f.attach = 'material';\n    }\n\n    // It should NOT call onUpdate on object instanciation, because it hasn't been added to the\n    // view yet. If the callback relies on references for instance, they won't be ready yet, this is\n    // why it passes \"true\" here\n    // There is no reason to apply props to injects\n    if (name !== 'inject') applyProps$1(instance, props);\n    return instance;\n  }\n  function appendChild(parentInstance, child) {\n    let added = false;\n    if (child) {\n      var _child$__r3f, _parentInstance$__r3f;\n      // The attach attribute implies that the object attaches itself on the parent\n      if ((_child$__r3f = child.__r3f) != null && _child$__r3f.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        // add in the usual parent-child way\n        parentInstance.add(child);\n        added = true;\n      }\n      // This is for anything that used attach, and for non-Object3Ds that don't get attached to props;\n      // that is, anything that's a child in React but not a child in the scenegraph.\n      if (!added) (_parentInstance$__r3f = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function insertBefore(parentInstance, child, beforeChild) {\n    let added = false;\n    if (child) {\n      var _child$__r3f2, _parentInstance$__r3f2;\n      if ((_child$__r3f2 = child.__r3f) != null && _child$__r3f2.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        child.parent = parentInstance;\n        child.dispatchEvent({\n          type: 'added'\n        });\n        parentInstance.dispatchEvent({\n          type: 'childadded',\n          child\n        });\n        const restSiblings = parentInstance.children.filter(sibling => sibling !== child);\n        const index = restSiblings.indexOf(beforeChild);\n        parentInstance.children = [...restSiblings.slice(0, index), child, ...restSiblings.slice(index)];\n        added = true;\n      }\n      if (!added) (_parentInstance$__r3f2 = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f2.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function removeRecursive(array, parent, dispose = false) {\n    if (array) [...array].forEach(child => removeChild(parent, child, dispose));\n  }\n  function removeChild(parentInstance, child, dispose) {\n    if (child) {\n      var _parentInstance$__r3f3, _child$__r3f3, _child$__r3f5;\n      // Clear the parent reference\n      if (child.__r3f) child.__r3f.parent = null;\n      // Remove child from the parents objects\n      if ((_parentInstance$__r3f3 = parentInstance.__r3f) != null && _parentInstance$__r3f3.objects) parentInstance.__r3f.objects = parentInstance.__r3f.objects.filter(x => x !== child);\n      // Remove attachment\n      if ((_child$__r3f3 = child.__r3f) != null && _child$__r3f3.attach) {\n        detach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        var _child$__r3f4;\n        parentInstance.remove(child);\n        // @ts-expect-error\n        // Remove interactivity on the initial root\n        if ((_child$__r3f4 = child.__r3f) != null && _child$__r3f4.root) {\n          removeInteractivity(findInitialRoot(child), child);\n        }\n      }\n\n      // Allow objects to bail out of recursive dispose altogether by passing dispose={null}\n      // Never dispose of primitives because their state may be kept outside of React!\n      // In order for an object to be able to dispose it has to have\n      //   - a dispose method,\n      //   - it cannot be a <primitive object={...} />\n      //   - it cannot be a THREE.Scene, because three has broken it's own api\n      //\n      // Since disposal is recursive, we can check the optional dispose arg, which will be undefined\n      // when the reconciler calls it, but then carry our own check recursively\n      const isPrimitive = (_child$__r3f5 = child.__r3f) == null ? void 0 : _child$__r3f5.primitive;\n      const shouldDispose = !isPrimitive && (dispose === undefined ? child.dispose !== null : dispose);\n\n      // Remove nested child objects. Primitives should not have objects and children that are\n      // attached to them declaratively ...\n      if (!isPrimitive) {\n        var _child$__r3f6;\n        removeRecursive((_child$__r3f6 = child.__r3f) == null ? void 0 : _child$__r3f6.objects, child, shouldDispose);\n        removeRecursive(child.children, child, shouldDispose);\n      }\n\n      // Remove references\n      delete child.__r3f;\n\n      // Dispose item whenever the reconciler feels like it\n      if (shouldDispose && child.dispose && child.type !== 'Scene') {\n        const callback = () => {\n          try {\n            child.dispose();\n          } catch (e) {\n            /* ... */\n          }\n        };\n\n        // Schedule async at runtime, flush sync in testing\n        if (typeof IS_REACT_ACT_ENVIRONMENT === 'undefined') {\n          unstable_scheduleCallback(unstable_IdlePriority, callback);\n        } else {\n          callback();\n        }\n      }\n      invalidateInstance(parentInstance);\n    }\n  }\n  function switchInstance(instance, type, newProps, fiber) {\n    var _instance$__r3f;\n    const parent = (_instance$__r3f = instance.__r3f) == null ? void 0 : _instance$__r3f.parent;\n    if (!parent) return;\n    const newInstance = createInstance(type, newProps, instance.__r3f.root);\n\n    // https://github.com/pmndrs/react-three-fiber/issues/1348\n    // When args change the instance has to be re-constructed, which then\n    // forces r3f to re-parent the children and non-scene objects\n    if (instance.children) {\n      for (const child of instance.children) {\n        if (child.__r3f) appendChild(newInstance, child);\n      }\n      instance.children = instance.children.filter(child => !child.__r3f);\n    }\n    instance.__r3f.objects.forEach(child => appendChild(newInstance, child));\n    instance.__r3f.objects = [];\n    if (!instance.__r3f.autoRemovedBeforeAppend) {\n      removeChild(parent, instance);\n    }\n    if (newInstance.parent) {\n      newInstance.__r3f.autoRemovedBeforeAppend = true;\n    }\n    appendChild(parent, newInstance);\n\n    // Re-bind event handlers on the initial root\n    if (newInstance.raycast && newInstance.__r3f.eventCount) {\n      const rootState = findInitialRoot(newInstance).getState();\n      rootState.internal.interaction.push(newInstance);\n    }\n    [fiber, fiber.alternate].forEach(fiber => {\n      if (fiber !== null) {\n        fiber.stateNode = newInstance;\n        if (fiber.ref) {\n          if (typeof fiber.ref === 'function') fiber.ref(newInstance);else fiber.ref.current = newInstance;\n        }\n      }\n    });\n  }\n\n  // Don't handle text instances, make it no-op\n  const handleTextInstance = () => {};\n  const reconciler = Reconciler({\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    supportsMutation: true,\n    isPrimaryRenderer: false,\n    supportsPersistence: false,\n    supportsHydration: false,\n    noTimeout: -1,\n    appendChildToContainer: (container, child) => {\n      if (!child) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n\n      // Link current root to the default scene\n      scene.__r3f.root = container;\n      appendChild(scene, child);\n    },\n    removeChildFromContainer: (container, child) => {\n      if (!child) return;\n      removeChild(container.getState().scene, child);\n    },\n    insertInContainerBefore: (container, child, beforeChild) => {\n      if (!child || !beforeChild) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n      insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: () => null,\n    getChildHostContext: parentHostContext => parentHostContext,\n    finalizeInitialChildren(instance) {\n      var _instance$__r3f2;\n      const localState = (_instance$__r3f2 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f2 : {};\n      // https://github.com/facebook/react/issues/20271\n      // Returning true will trigger commitMount\n      return Boolean(localState.handlers);\n    },\n    prepareUpdate(instance, _type, oldProps, newProps) {\n      var _instance$__r3f3;\n      const localState = (_instance$__r3f3 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f3 : {};\n\n      // Create diff-sets\n      if (localState.primitive && newProps.object && newProps.object !== instance) {\n        return [true];\n      } else {\n        // This is a data object, let's extract critical information about it\n        const {\n          args: argsNew = [],\n          children: cN,\n          ...restNew\n        } = newProps;\n        const {\n          args: argsOld = [],\n          children: cO,\n          ...restOld\n        } = oldProps;\n\n        // Throw if an object or literal was passed for args\n        if (!Array.isArray(argsNew)) throw new Error('R3F: the args prop must be an array!');\n\n        // If it has new props or arguments, then it needs to be re-instantiated\n        if (argsNew.some((value, index) => value !== argsOld[index])) return [true];\n        // Create a diff-set, flag if there are any changes\n        const diff = diffProps(instance, restNew, restOld, true);\n        if (diff.changes.length) return [false, diff];\n\n        // Otherwise do not touch the instance\n        return null;\n      }\n    },\n    commitUpdate(instance, [reconstruct, diff], type, _oldProps, newProps, fiber) {\n      // Reconstruct when args or <primitive object={...} have changes\n      if (reconstruct) switchInstance(instance, type, newProps, fiber);\n      // Otherwise just overwrite props\n      else applyProps$1(instance, diff);\n    },\n    commitMount(instance, _type, _props, _int) {\n      var _instance$__r3f4;\n      // https://github.com/facebook/react/issues/20271\n      // This will make sure events are only added once to the central container on the initial root\n      const localState = (_instance$__r3f4 = instance.__r3f) != null ? _instance$__r3f4 : {};\n      if (instance.raycast && localState.handlers && localState.eventCount) {\n        findInitialRoot(instance).getState().internal.interaction.push(instance);\n      }\n    },\n    getPublicInstance: instance => instance,\n    prepareForCommit: () => null,\n    preparePortalMount: container => prepare(container.getState().scene),\n    resetAfterCommit: () => {},\n    shouldSetTextContent: () => false,\n    clearContainer: () => false,\n    hideInstance(instance) {\n      var _instance$__r3f5;\n      // Detach while the instance is hidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f5 = instance.__r3f) != null ? _instance$__r3f5 : {};\n      if (type && parent) detach(parent, instance, type);\n      if (instance.isObject3D) instance.visible = false;\n      invalidateInstance(instance);\n    },\n    unhideInstance(instance, props) {\n      var _instance$__r3f6;\n      // Re-attach when the instance is unhidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f6 = instance.__r3f) != null ? _instance$__r3f6 : {};\n      if (type && parent) attach(parent, instance, type);\n      if (instance.isObject3D && props.visible == null || props.visible) instance.visible = true;\n      invalidateInstance(instance);\n    },\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n    // @ts-expect-error\n    getCurrentEventPriority: () => _getEventPriority ? _getEventPriority() : DefaultEventPriority,\n    beforeActiveInstanceBlur: () => {},\n    afterActiveInstanceBlur: () => {},\n    detachDeletedInstance: () => {},\n    now: typeof performance !== 'undefined' && is.fun(performance.now) ? performance.now : is.fun(Date.now) ? Date.now : () => 0,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r920883503\n    scheduleTimeout: is.fun(setTimeout) ? setTimeout : undefined,\n    cancelTimeout: is.fun(clearTimeout) ? clearTimeout : undefined\n  });\n  return {\n    reconciler,\n    applyProps: applyProps$1\n  };\n}\nvar _window$document, _window$navigator;\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nconst DEFAULT = '__default';\nconst DEFAULTS = new Map();\nconst isDiffSet = def => def && !!def.memoized && !!def.changes;\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nconst getRootState = obj => {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n};\n\n/**\r\n * Returns the instances initial (outmost) root\r\n */\nfunction findInitialRoot(child) {\n  let root = child.__r3f.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n/**\r\n * Collects nodes and materials from a THREE.Object3D.\r\n */\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.dispose && obj.type !== 'Scene') obj.dispose();\n  for (const p in obj) {\n    p.dispose == null ? void 0 : p.dispose();\n    delete obj[p];\n  }\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(object, state) {\n  const instance = object;\n  instance.__r3f = {\n    type: '',\n    root: null,\n    previousAttach: null,\n    memoizedProps: {},\n    eventCount: 0,\n    handlers: {},\n    objects: [],\n    parent: null,\n    ...state\n  };\n  return object;\n}\nfunction resolve(instance, key) {\n  let target = instance;\n  if (key.includes('-')) {\n    const entries = key.split('-');\n    const last = entries.pop();\n    target = entries.reduce((acc, key) => acc[key], instance);\n    return {\n      target,\n      key: last\n    };\n  } else return {\n    target,\n    key\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child, type) {\n  if (is.str(type)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(type)) {\n      const root = type.replace(INDEX_REGEX, '');\n      const {\n        target,\n        key\n      } = resolve(parent, root);\n      if (!Array.isArray(target[key])) target[key] = [];\n    }\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    child.__r3f.previousAttach = target[key];\n    target[key] = child;\n  } else child.__r3f.previousAttach = type(parent, child);\n}\nfunction detach(parent, child, type) {\n  var _child$__r3f, _child$__r3f2;\n  if (is.str(type)) {\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    const previous = child.__r3f.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete target[key];\n    // Otherwise set the previous value\n    else target[key] = previous;\n  } else (_child$__r3f = child.__r3f) == null ? void 0 : _child$__r3f.previousAttach == null ? void 0 : _child$__r3f.previousAttach(parent, child);\n  (_child$__r3f2 = child.__r3f) == null ? true : delete _child$__r3f2.previousAttach;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, {\n  children: cN,\n  key: kN,\n  ref: rN,\n  ...props\n}, {\n  children: cP,\n  key: kP,\n  ref: rP,\n  ...previous\n} = {}, remove = false) {\n  const localState = instance.__r3f;\n  const entries = Object.entries(props);\n  const changes = [];\n\n  // Catch removed props, prepend them so they can be reset or removed\n  if (remove) {\n    const previousKeys = Object.keys(previous);\n    for (let i = 0; i < previousKeys.length; i++) {\n      if (!props.hasOwnProperty(previousKeys[i])) entries.unshift([previousKeys[i], DEFAULT + 'remove']);\n    }\n  }\n  entries.forEach(([key, value]) => {\n    var _instance$__r3f;\n    // Bail out on primitive object\n    if ((_instance$__r3f = instance.__r3f) != null && _instance$__r3f.primitive && key === 'object') return;\n    // When props match bail out\n    if (is.equ(value, previous[key])) return;\n    // Collect handlers and bail out\n    if (/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(key)) return changes.push([key, value, true, []]);\n    // Split dashed props\n    let entries = [];\n    if (key.includes('-')) entries = key.split('-');\n    changes.push([key, value, false, entries]);\n\n    // Reset pierced props\n    for (const prop in props) {\n      const value = props[prop];\n      if (prop.startsWith(`${key}-`)) changes.push([prop, value, false, prop.split('-')]);\n    }\n  });\n  const memoized = {\n    ...props\n  };\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.args) memoized.args = localState.memoizedProps.args;\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.attach) memoized.attach = localState.memoizedProps.attach;\n  return {\n    memoized,\n    changes\n  };\n}\nconst __DEV__ = typeof process !== 'undefined' && process.env.NODE_ENV !== 'production';\n\n// This function applies a set of changes to the instance\nfunction applyProps$1(instance, data) {\n  var _instance$__r3f2;\n  // Filter equals, events and reserved props\n  const localState = instance.__r3f;\n  const root = localState == null ? void 0 : localState.root;\n  const rootState = root == null ? void 0 : root.getState == null ? void 0 : root.getState();\n  const {\n    memoized,\n    changes\n  } = isDiffSet(data) ? data : diffProps(instance, data);\n  const prevHandlers = localState == null ? void 0 : localState.eventCount;\n\n  // Prepare memoized props\n  if (instance.__r3f) instance.__r3f.memoizedProps = memoized;\n  for (let i = 0; i < changes.length; i++) {\n    let [key, value, isEvent, keys] = changes[i];\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(instance)) {\n      const sRGBEncoding = 3001;\n      const SRGBColorSpace = 'srgb';\n      const LinearSRGBColorSpace = 'srgb-linear';\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n    let currentInstance = instance;\n    let targetProp = currentInstance[key];\n\n    // Revolve dashed props\n    if (keys.length) {\n      targetProp = keys.reduce((acc, key) => acc[key], instance);\n      // If the target is atomic, it forces us to switch the root\n      if (!(targetProp && targetProp.set)) {\n        const [name, ...reverseEntries] = keys.reverse();\n        currentInstance = reverseEntries.reverse().reduce((acc, key) => acc[key], instance);\n        key = name;\n      }\n    }\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (value === DEFAULT + 'remove') {\n      if (currentInstance.constructor) {\n        // create a blank slate of the instance and copy the particular parameter.\n        let ctor = DEFAULTS.get(currentInstance.constructor);\n        if (!ctor) {\n          // @ts-expect-error\n          ctor = new currentInstance.constructor();\n          DEFAULTS.set(currentInstance.constructor, ctor);\n        }\n        value = ctor[key];\n      } else {\n        // instance does not have constructor, just set it to 0\n        value = 0;\n      }\n    }\n\n    // Deal with pointer events ...\n    if (isEvent && localState) {\n      if (value) localState.handlers[key] = value;else delete localState.handlers[key];\n      localState.eventCount = Object.keys(localState.handlers).length;\n    }\n    // Special treatment for objects with support for set/copy, and layers\n    else if (targetProp && targetProp.set && (targetProp.copy || targetProp instanceof THREE.Layers)) {\n      // If value is an array\n      if (Array.isArray(value)) {\n        if (targetProp.fromArray) targetProp.fromArray(value);else targetProp.set(...value);\n      }\n      // Test again target.copy(class) next ...\n      else if (targetProp.copy && value && value.constructor && (\n      // Some environments may break strict identity checks by duplicating versions of three.js.\n      // Loosen to unminified names, ignoring descendents.\n      // https://github.com/pmndrs/react-three-fiber/issues/2856\n      // TODO: fix upstream and remove in v9\n      __DEV__ ? targetProp.constructor.name === value.constructor.name : targetProp.constructor === value.constructor)) {\n        targetProp.copy(value);\n      }\n      // If nothing else fits, just set the single value, ignore undefined\n      // https://github.com/pmndrs/react-three-fiber/issues/274\n      else if (value !== undefined) {\n        var _targetProp;\n        const isColor = (_targetProp = targetProp) == null ? void 0 : _targetProp.isColor;\n        // Allow setting array scalars\n        if (!isColor && targetProp.setScalar) targetProp.setScalar(value);\n        // Layers have no copy function, we must therefore copy the mask property\n        else if (targetProp instanceof THREE.Layers && value instanceof THREE.Layers) targetProp.mask = value.mask;\n        // Otherwise just set ...\n        else targetProp.set(value);\n        // For versions of three which don't support THREE.ColorManagement,\n        // Auto-convert sRGB colors\n        // https://github.com/pmndrs/react-three-fiber/issues/344\n        if (!getColorManagement() && rootState && !rootState.linear && isColor) targetProp.convertSRGBToLinear();\n      }\n      // Else, just overwrite the value\n    } else {\n      var _currentInstance$key;\n      currentInstance[key] = value;\n\n      // Auto-convert sRGB textures, for now ...\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if ((_currentInstance$key = currentInstance[key]) != null && _currentInstance$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      currentInstance[key].format === THREE.RGBAFormat && currentInstance[key].type === THREE.UnsignedByteType && rootState) {\n        const texture = currentInstance[key];\n        if (hasColorSpace(texture) && hasColorSpace(rootState.gl)) texture.colorSpace = rootState.gl.outputColorSpace;else texture.encoding = rootState.gl.outputEncoding;\n      }\n    }\n    invalidateInstance(instance);\n  }\n  if (localState && localState.parent && instance.raycast && prevHandlers !== localState.eventCount) {\n    // Get the initial root state's internals\n    const internal = findInitialRoot(instance).getState().internal;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = internal.interaction.indexOf(instance);\n    if (index > -1) internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (localState.eventCount) internal.interaction.push(instance);\n  }\n\n  // Call the update lifecycle when it is being updated, but only when it is part of the scene.\n  // Skip updates to the `onUpdate` prop itself\n  const isCircular = changes.length === 1 && changes[0][0] === 'onUpdate';\n  if (!isCircular && changes.length && (_instance$__r3f2 = instance.__r3f) != null && _instance$__r3f2.parent) updateInstance(instance);\n  return instance;\n}\nfunction invalidateInstance(instance) {\n  var _instance$__r3f3, _instance$__r3f3$root;\n  const state = (_instance$__r3f3 = instance.__r3f) == null ? void 0 : (_instance$__r3f3$root = _instance$__r3f3.root) == null ? void 0 : _instance$__r3f3$root.getState == null ? void 0 : _instance$__r3f3$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateInstance(instance) {\n  instance.onUpdate == null ? void 0 : instance.onUpdate(instance);\n}\nfunction updateCamera(camera, size) {\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  // Do not mess with the camera if it belongs to the user\n  if (!camera.manual) {\n    if (isOrthographicCamera(camera)) {\n      camera.left = size.width / -2;\n      camera.right = size.width / 2;\n      camera.top = size.height / 2;\n      camera.bottom = size.height / -2;\n    } else {\n      camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n    // https://github.com/pmndrs/react-three-fiber/issues/178\n    // Update matrix world since the renderer is a frame late\n    camera.updateMatrixWorld();\n  }\n}\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n// https://github.com/facebook/react/tree/main/packages/react-reconciler#getcurrenteventpriority\n// Gives React a clue as to how import the current interaction is\nfunction getEventPriority() {\n  var _globalScope$event;\n  // Get a handle to the current global scope in window and worker contexts if able\n  // https://github.com/pmndrs/react-three-fiber/pull/2493\n  const globalScope = typeof self !== 'undefined' && self || typeof window !== 'undefined' && window;\n  if (!globalScope) return DefaultEventPriority;\n  const name = (_globalScope$event = globalScope.event) == null ? void 0 : _globalScope$event.type;\n  switch (name) {\n    case 'click':\n    case 'contextmenu':\n    case 'dblclick':\n    case 'pointercancel':\n    case 'pointerdown':\n    case 'pointerup':\n      return DiscreteEventPriority;\n    case 'pointermove':\n    case 'pointerout':\n    case 'pointerover':\n    case 'pointerenter':\n    case 'pointerleave':\n    case 'wheel':\n      return ContinuousEventPriority;\n    default:\n      return DefaultEventPriority;\n  }\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    const rootState = store.getState();\n\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object) || rootState;\n        const {\n          raycaster,\n          pointer,\n          camera,\n          internal\n        } = state;\n        const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n        const hasPointerCapture = id => {\n          var _internal$capturedMap, _internal$capturedMap2;\n          return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n        };\n        const setPointerCapture = id => {\n          const captureData = {\n            intersection: hit,\n            target: event.target\n          };\n          if (internal.capturedMap.has(id)) {\n            // if the pointerId was previously captured, we add the hit to the\n            // event capturedMap.\n            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n          } else {\n            // if the pointerId was not previously captured, we create a map\n            // containing the hitObject, and the hit. hitObject is used for\n            // faster access.\n            internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n          }\n          event.target.setPointerCapture(id);\n        };\n        const releasePointerCapture = id => {\n          const captures = internal.capturedMap.get(id);\n          if (captures) {\n            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n          }\n        };\n\n        // Add native event props\n        let extractEventProps = {};\n        // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n        for (let prop in event) {\n          let property = event[prop];\n          // Only copy over atomics, leave functions alone as these should be\n          // called as event.nativeEvent.fn()\n          if (typeof property !== 'function') extractEventProps[prop] = property;\n        }\n        let raycastEvent = {\n          ...hit,\n          ...extractEventProps,\n          pointer,\n          intersections,\n          stopped: localState.stopped,\n          delta,\n          unprojectedPoint,\n          ray: raycaster.ray,\n          camera: camera,\n          // Hijack stopPropagation, which just sets a flag\n          stopPropagation() {\n            // https://github.com/pmndrs/react-three-fiber/issues/596\n            // Events are not allowed to stop propagation if the pointer has been captured\n            const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n            // We only authorize stopPropagation...\n            if (\n            // ...if this pointer hasn't been captured\n            !capturesForPointer ||\n            // ... or if the hit object is capturing the pointer\n            capturesForPointer.has(hit.eventObject)) {\n              raycastEvent.stopped = localState.stopped = true;\n              // Propagation is stopped, remove all other hover records\n              // An event handler is only allowed to flush other handlers if it is hovered itself\n              if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                // Objects cannot flush out higher up objects that have already caught the event\n                const higher = intersections.slice(0, intersections.indexOf(hit));\n                cancelPointer([...higher, hit]);\n              }\n            }\n          },\n          // there should be a distinction between target and currentTarget\n          target: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          currentTarget: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          nativeEvent: event\n        };\n\n        // Call subscribers\n        callback(raycastEvent);\n        // Event bubbling may be interrupted by stopPropagation\n        if (localState.stopped === true) break;\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\n// Keys that shouldn't be copied between R3F stores\nconst privateKeys = ['set', 'get', 'setSize', 'setFrameloop', 'setDpr', 'events', 'invalidate', 'advance', 'size', 'viewport'];\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootState = create((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      xr: null,\n      scene: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        updateStyle: false\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, updateStyle, top, left) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top: top || 0,\n          left: left || 0,\n          updateStyle\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        active: false,\n        priority: 0,\n        frames: 0,\n        lastEvent: /*#__PURE__*/React.createRef(),\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootState.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootState.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootState.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      var _size$updateStyle;\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = (_size$updateStyle = size.updateStyle) != null ? _size$updateStyle : typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootState.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootState;\n};\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nlet i;\nlet globalEffects = new Set();\nlet globalAfterEffects = new Set();\nlet globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction render$1(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nfunction createLoop(roots) {\n  let running = false;\n  let useFrameInProgress = false;\n  let repeat;\n  let frame;\n  let state;\n  function loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n\n    // Run effects\n    flushGlobalEffects('before', timestamp);\n\n    // Render all roots\n    useFrameInProgress = true;\n    for (const root of roots.values()) {\n      var _state$gl$xr;\n      state = root.store.getState();\n      // If the frameloop is invalidated, do not run another frame\n      if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n        repeat += render$1(timestamp, state);\n      }\n    }\n    useFrameInProgress = false;\n\n    // Run after-effects\n    flushGlobalEffects('after', timestamp);\n\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n      // Tail call effects, they are called when rendering stops\n      flushGlobalEffects('tail', timestamp);\n\n      // Flag end of operation\n      running = false;\n      return cancelAnimationFrame(frame);\n    }\n  }\n  function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return roots.forEach(root => invalidate(root.store.getState(), frames));\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n    if (frames > 1) {\n      // legacy support for people using frames parameters\n      // Increase frames, do not go higher than 60\n      state.internal.frames = Math.min(60, state.internal.frames + frames);\n    } else {\n      if (useFrameInProgress) {\n        //called from within a useFrame, it means the user wants an additional frame\n        state.internal.frames = 2;\n      } else {\n        //the user need a new frame, no need to increment further than 1\n        state.internal.frames = 1;\n      }\n    }\n\n    // If the render-loop isn't active, start it\n    if (!running) {\n      running = true;\n      requestAnimationFrame(loop);\n    }\n  }\n  function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n    if (!state) for (const root of roots.values()) render$1(timestamp, root.store.getState());else render$1(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n  }\n  return {\n    loop,\n    invalidate,\n    advance\n  };\n}\n\n/**\r\n * Exposes an object's {@link LocalState}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  useIsomorphicLayoutEffect(() => void (instance.current = ref.current.__r3f), [ref]);\n  return instance;\n}\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    // Construct new loader and run extensions\n    let loader = memoizedLoaders.get(Proto);\n    if (!loader) {\n      loader = new Proto();\n      memoizedLoaders.set(Proto, loader);\n    }\n    if (extensions) extensions(loader);\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (data.scene) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(Proto, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [Proto, ...keys], {\n    equal: is.equ\n  });\n  // Return the object/s\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (Proto, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [Proto, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (Proto, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([Proto, ...keys]);\n};\nconst roots = new Map();\nconst {\n  invalidate,\n  advance\n} = createLoop(roots);\nconst {\n  reconciler,\n  applyProps\n} = createRenderer(roots, getEventPriority);\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;else return new THREE.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, defaultSize) {\n  const defaultStyle = typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement;\n  if (defaultSize) {\n    const {\n      width,\n      height,\n      top,\n      left,\n      updateStyle = defaultStyle\n    } = defaultSize;\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle\n    };\n  } else if (typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle: defaultStyle\n    };\n  } else if (typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0,\n      updateStyle: defaultStyle\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store, ConcurrentRoot, null, false, null, '', logRecoverableError, null);\n  // Map it\n  if (!prevRoot) roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof THREE.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n              camera.manual = true;\n              camera.updateProjectionMatrix();\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n        } else {\n          scene = new THREE.Scene();\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene: prepare(scene)\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n      if (!configured) {\n        // Set color space and tonemapping preferences, once\n        const LinearEncoding = 3000;\n        const sRGBEncoding = 3001;\n        applyProps(gl, {\n          outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n          toneMapping: flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping\n        });\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.updateStyle, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer(/*#__PURE__*/jsx(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state);\n            roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  }, container.uuid);\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = React.useCallback((rootState, injectState) => {\n    const intersect = {\n      ...rootState\n    }; // all prev state props\n\n    // Only the fields of \"rootState\" that do not differ from injectState\n    // Some props should be off-limits\n    // Otherwise filter out the props that are different and let the inject layer take precedence\n    Object.keys(rootState).forEach(key => {\n      if (\n      // Some props should be off-limits\n      privateKeys.includes(key) ||\n      // Otherwise filter out the props that are different and let the inject layer take precedence\n      // Unless the inject layer props is undefined, then we keep the root layer\n      rootState[key] !== injectState[key] && injectState[key]) {\n        delete intersect[key];\n      }\n    });\n    let viewport = undefined;\n    if (injectState && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...intersect,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...(injectState == null ? void 0 : injectState.events),\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      ...rest\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [state]);\n  const [usePortalStore] = React.useState(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const previousState = previousRoot.getState();\n    const store = create((set, get) => ({\n      ...previousState,\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      previousRoot,\n      events: {\n        ...previousState.events,\n        ...events\n      },\n      size: {\n        ...previousState.size,\n        ...size\n      },\n      ...rest,\n      // Set and get refer to this root-state\n      set,\n      get,\n      // Layers are allowed to override events\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    }));\n    return store;\n  });\n  React.useEffect(() => {\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const unsub = previousRoot.subscribe(prev => usePortalStore.setState(state => inject(prev, state)));\n    return () => {\n      unsub();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  React.useEffect(() => {\n    usePortalStore.setState(injectState => inject(previousRoot.getState(), injectState));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  React.useEffect(() => {\n    return () => {\n      usePortalStore.destroy();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(Fragment, {\n    children: reconciler.createPortal(/*#__PURE__*/jsx(context.Provider, {\n      value: usePortalStore,\n      children: children\n    }), usePortalStore, null)\n  });\n}\n\n/**\r\n * Force React to flush any updates inside the provided callback synchronously and immediately.\r\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\r\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\r\n * having to revert to a non-React solution.\r\n */\nfunction flushSync(fn) {\n  // `flushSync` implementation only takes one argument. I don't know what's up with the type declaration for it.\n  return reconciler.flushSync(fn, undefined);\n}\nreconciler.injectIntoDevTools({\n  bundleType: process.env.NODE_ENV === 'production' ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: React.version\n});\nconst act = React.unstable_act;\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      var _events$handlers;\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      Object.entries((_events$handlers = events.handlers) != null ? _events$handlers : []).forEach(([name, event]) => {\n        const [eventName, passive] = DOM_EVENTS[name];\n        target.addEventListener(eventName, event, {\n          passive\n        });\n      });\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        var _events$handlers2;\n        Object.entries((_events$handlers2 = events.handlers) != null ? _events$handlers2 : []).forEach(([name, event]) => {\n          if (events && events.connected instanceof HTMLElement) {\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        });\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\nexport { useInstanceHandle as A, Block as B, useStore as C, useThree as D, ErrorBoundary as E, useFrame as F, useGraph as G, useLoader as H, useIsomorphicLayoutEffect as a, createRoot as b, createPointerEvents as c, unmountComponentAtNode as d, extend as e, createEvents as f, context as g, createPortal as h, isRef as i, reconciler as j, applyProps as k, dispose as l, invalidate as m, advance as n, addEffect as o, addAfterEffect as p, addTail as q, render as r, flushGlobalEffects as s, threeTypes as t, useMutableCallback as u, flushSync as v, getRootState as w, act as x, buildGraph as y, roots as z };", "map": {"version": 3, "names": ["THREE", "React", "DefaultEventPriority", "ContinuousEventPriority", "DiscreteEventPriority", "ConcurrentRoot", "create", "suspend", "preload", "clear", "jsx", "Fragment", "<PERSON><PERSON><PERSON><PERSON>", "unstable_scheduleCallback", "unstable_IdlePriority", "threeTypes", "Object", "freeze", "__proto__", "catalogue", "extend", "objects", "assign", "<PERSON><PERSON><PERSON><PERSON>", "_roots", "_getEventPriority", "createInstance", "type", "args", "attach", "props", "root", "name", "toUpperCase", "slice", "instance", "object", "undefined", "Error", "prepare", "primitive", "target", "Array", "isArray", "memoizedProps", "__r3f", "isBufferGeometry", "isMaterial", "applyProps$1", "append<PERSON><PERSON><PERSON>", "parentInstance", "child", "added", "_child$__r3f", "_parentInstance$__r3f", "isObject3D", "add", "push", "parent", "updateInstance", "invalidateInstance", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "_child$__r3f2", "_parentInstance$__r3f2", "dispatchEvent", "restSiblings", "children", "filter", "sibling", "index", "indexOf", "removeRecursive", "array", "dispose", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "_parentInstance$__r3f3", "_child$__r3f3", "_child$__r3f5", "x", "detach", "_child$__r3f4", "remove", "removeInteractivity", "findInitialRoot", "isPrimitive", "shouldDispose", "_child$__r3f6", "callback", "e", "IS_REACT_ACT_ENVIRONMENT", "switchInstance", "newProps", "fiber", "_instance$__r3f", "newInstance", "autoRemovedBeforeAppend", "raycast", "eventCount", "rootState", "getState", "internal", "interaction", "alternate", "stateNode", "ref", "current", "handleTextInstance", "reconciler", "appendInitialChild", "supportsMutation", "isPrimary<PERSON><PERSON><PERSON>", "supportsPersistence", "supportsHydration", "noTimeout", "append<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container", "scene", "<PERSON><PERSON><PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON>", "insertInContainerBefore", "getRootHostContext", "getChildHostContext", "parentHostContext", "finalizeInitialChildren", "_instance$__r3f2", "localState", "Boolean", "handlers", "prepareUpdate", "_type", "oldProps", "_instance$__r3f3", "args<PERSON>ew", "cN", "restNew", "argsOld", "cO", "restOld", "some", "value", "diff", "diffProps", "changes", "length", "commitUpdate", "reconstruct", "_oldProps", "commitMount", "_props", "_int", "_instance$__r3f4", "getPublicInstance", "prepareForCommit", "preparePortalMount", "resetAfterCommit", "shouldSetTextContent", "clearContainer", "hideInstance", "_instance$__r3f5", "visible", "unhideInstance", "_instance$__r3f6", "createTextInstance", "hideTextInstance", "unhideTextInstance", "getCurrentEventPriority", "beforeActiveInstanceBlur", "afterActiveInstanceBlur", "detachDeletedInstance", "now", "performance", "is", "fun", "Date", "scheduleTimeout", "setTimeout", "cancelTimeout", "clearTimeout", "applyProps", "_window$document", "_window$navigator", "hasColorSpace", "getColorManagement", "_ColorManagement", "ColorManagement", "isOrthographicCamera", "def", "isRef", "obj", "hasOwnProperty", "useIsomorphicLayoutEffect", "window", "document", "createElement", "navigator", "product", "useLayoutEffect", "useEffect", "useMutableCallback", "fn", "useRef", "Block", "set", "Promise", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "state", "error", "componentDidCatch", "err", "render", "getDerivedStateFromError", "DEFAULT", "DEFAULTS", "Map", "isDiffSet", "memoized", "calculateDpr", "dpr", "_window$devicePixelRa", "devicePixelRatio", "Math", "min", "max", "getRootState", "_r3f", "previousRoot", "a", "arr", "str", "num", "boo", "und", "equ", "b", "arrays", "strict", "isObj", "isArr", "i", "keys", "buildGraph", "data", "nodes", "materials", "traverse", "material", "p", "previousAttach", "resolve", "key", "includes", "entries", "split", "last", "pop", "reduce", "acc", "INDEX_REGEX", "test", "replace", "previous", "kN", "rN", "cP", "kP", "rP", "previousKeys", "unshift", "prop", "startsWith", "__DEV__", "process", "env", "NODE_ENV", "prevHandlers", "isEvent", "sRGBEncoding", "SRGBColorSpace", "LinearSRGBColorSpace", "currentInstance", "targetProp", "reverseEntries", "reverse", "ctor", "get", "copy", "Layers", "fromArray", "_targetProp", "isColor", "setScalar", "mask", "linear", "convertSRGBToLinear", "_currentInstance$key", "isTexture", "format", "RGBAFormat", "UnsignedByteType", "texture", "gl", "colorSpace", "outputColorSpace", "encoding", "outputEncoding", "splice", "isCircular", "_instance$__r3f3$root", "frames", "invalidate", "onUpdate", "updateCamera", "camera", "size", "manual", "left", "width", "right", "top", "height", "bottom", "aspect", "updateProjectionMatrix", "updateMatrixWorld", "makeId", "event", "eventObject", "uuid", "instanceId", "getEventPriority", "_globalScope$event", "globalScope", "self", "releaseInternalPointerCapture", "capturedMap", "captures", "pointerId", "captureData", "delete", "releasePointerCapture", "store", "o", "initialHits", "hovered", "createEvents", "calculateDistance", "dx", "offsetX", "initialClick", "dy", "offsetY", "round", "sqrt", "filterPointerEvents", "intersect", "duplicates", "Set", "intersections", "eventsObjects", "raycaster", "events", "compute", "handleRaycast", "enabled", "_state$previousRoot", "intersectObject", "hits", "flatMap", "sort", "aState", "bState", "distance", "priority", "item", "id", "has", "hit", "_r3f2", "values", "intersection", "handleIntersects", "delta", "stopped", "pointer", "unprojectedPoint", "Vector3", "y", "unproject", "hasPointerCapture", "_internal$capturedMap", "_internal$capturedMap2", "setPointerCapture", "extractEventProps", "property", "raycastEvent", "ray", "stopPropagation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "find", "higher", "cancelPointer", "currentTarget", "nativeEvent", "hovered<PERSON>bj", "onPointerOut", "onPointerLeave", "pointerMissed", "onPointerMissed", "handlePointer", "requestAnimationFrame", "handleEvent", "lastEvent", "isPointerMove", "isClickEvent", "map", "onIntersect", "onPointerOver", "onPointerEnter", "hoveredItem", "onPointerMove", "handler", "privateKeys", "<PERSON><PERSON><PERSON><PERSON>", "context", "createContext", "createStore", "advance", "position", "defaultTarget", "tempTarget", "getCurrentViewport", "isVector3", "getWorldPosition", "distanceTo", "zoom", "factor", "fov", "PI", "h", "tan", "w", "performanceTimeout", "setPerformanceCurrent", "Vector2", "connected", "xr", "timestamp", "runGlobalEffects", "legacy", "flat", "controls", "clock", "Clock", "mouse", "frameloop", "debounce", "regress", "updateStyle", "viewport", "initialDpr", "setEvents", "setSize", "setDpr", "resolved", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stop", "elapsedTime", "start", "active", "createRef", "subscribers", "subscribe", "s", "oldSize", "oldDpr", "oldCamera", "_size$updateStyle", "setPixelRatio", "HTMLCanvasElement", "dom<PERSON>lement", "createSubs", "subs", "sub", "globalEffects", "globalAfterEffects", "globalTailEffects", "addEffect", "addAfterEffect", "addTail", "run", "effects", "flushGlobalEffects", "subscription", "render$1", "frame", "<PERSON><PERSON><PERSON><PERSON>", "oldTime", "createLoop", "roots", "running", "useFrameInProgress", "repeat", "loop", "_state$gl$xr", "isPresenting", "cancelAnimationFrame", "_state$gl$xr2", "useInstanceHandle", "useStore", "useContext", "useThree", "selector", "equalityFn", "useFrame", "renderPriority", "useGraph", "useMemo", "memoizedLoaders", "WeakMap", "loadingFn", "extensions", "onProgress", "Proto", "input", "loader", "all", "res", "reject", "load", "message", "useLoader", "results", "equal", "shallowLoose", "createRendererInstance", "canvas", "customRenderer", "WebGLRenderer", "powerPreference", "antialias", "alpha", "computeInitialSize", "defaultSize", "defaultStyle", "parentElement", "getBoundingClientRect", "OffscreenCanvas", "createRoot", "prevRoot", "prevFiber", "prevStore", "console", "warn", "logRecoverableError", "reportError", "createContainer", "onCreated", "configured", "lastCamera", "configure", "glConfig", "propsSize", "sceneOptions", "onCreatedCallback", "shadows", "orthographic", "raycastOptions", "cameraOptions", "Raycaster", "params", "options", "isCamera", "Camera", "OrthographicCamera", "PerspectiveCamera", "z", "rotation", "lookAt", "isScene", "Scene", "_gl$xr", "handleXRFrame", "handleSessionChange", "setAnimationLoop", "connect", "addEventListener", "disconnect", "removeEventListener", "shadowMap", "oldEnabled", "oldType", "PCFSoftShadowMap", "_types$shadows", "types", "basic", "BasicShadowMap", "percentage", "PCFShadowMap", "soft", "variance", "VSMShadowMap", "needsUpdate", "legacyMode", "LinearEncoding", "toneMapping", "NoToneMapping", "ACESFilmicToneMapping", "updateContainer", "Provider", "rootElement", "unmount", "unmountComponentAtNode", "config", "_state$gl", "_state$gl$renderLists", "_state$gl2", "_state$gl3", "renderLists", "forceContextLoss", "createPortal", "Portal", "rest", "useState", "inject", "useCallback", "injectState", "usePortalStore", "previousState", "unsub", "prev", "setState", "destroy", "flushSync", "injectIntoDevTools", "bundleType", "rendererPackageName", "version", "act", "unstable_act", "DOM_EVENTS", "onClick", "onContextMenu", "onDoubleClick", "onWheel", "onPointerDown", "onPointerUp", "onPointerCancel", "onLostPointerCapture", "createPointerEvents", "setFromCamera", "update", "_internal$lastEvent", "_events$handlers", "eventName", "passive", "_events$handlers2", "HTMLElement", "A", "B", "C", "D", "E", "F", "G", "H", "c", "d", "f", "g", "j", "k", "l", "m", "n", "q", "r", "t", "u", "v"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/fiber/dist/events-776716bd.esm.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport create from 'zustand';\nimport { suspend, preload, clear } from 'suspend-react';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nconst catalogue = {};\nconst extend = objects => void Object.assign(catalogue, objects);\nfunction createRenderer(_roots, _getEventPriority) {\n  function createInstance(type, {\n    args = [],\n    attach,\n    ...props\n  }, root) {\n    let name = `${type[0].toUpperCase()}${type.slice(1)}`;\n    let instance;\n    if (type === 'primitive') {\n      if (props.object === undefined) throw new Error(\"R3F: Primitives without 'object' are invalid!\");\n      const object = props.object;\n      instance = prepare(object, {\n        type,\n        root,\n        attach,\n        primitive: true\n      });\n    } else {\n      const target = catalogue[name];\n      if (!target) {\n        throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n      }\n\n      // Throw if an object or literal was passed for args\n      if (!Array.isArray(args)) throw new Error('R3F: The args prop must be an array!');\n\n      // Instanciate new object, link it to the root\n      // Append memoized props with args so it's not forgotten\n      instance = prepare(new target(...args), {\n        type,\n        root,\n        attach,\n        // Save args in case we need to reconstruct later for HMR\n        memoizedProps: {\n          args\n        }\n      });\n    }\n\n    // Auto-attach geometries and materials\n    if (instance.__r3f.attach === undefined) {\n      if (instance.isBufferGeometry) instance.__r3f.attach = 'geometry';else if (instance.isMaterial) instance.__r3f.attach = 'material';\n    }\n\n    // It should NOT call onUpdate on object instanciation, because it hasn't been added to the\n    // view yet. If the callback relies on references for instance, they won't be ready yet, this is\n    // why it passes \"true\" here\n    // There is no reason to apply props to injects\n    if (name !== 'inject') applyProps$1(instance, props);\n    return instance;\n  }\n  function appendChild(parentInstance, child) {\n    let added = false;\n    if (child) {\n      var _child$__r3f, _parentInstance$__r3f;\n      // The attach attribute implies that the object attaches itself on the parent\n      if ((_child$__r3f = child.__r3f) != null && _child$__r3f.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        // add in the usual parent-child way\n        parentInstance.add(child);\n        added = true;\n      }\n      // This is for anything that used attach, and for non-Object3Ds that don't get attached to props;\n      // that is, anything that's a child in React but not a child in the scenegraph.\n      if (!added) (_parentInstance$__r3f = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function insertBefore(parentInstance, child, beforeChild) {\n    let added = false;\n    if (child) {\n      var _child$__r3f2, _parentInstance$__r3f2;\n      if ((_child$__r3f2 = child.__r3f) != null && _child$__r3f2.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        child.parent = parentInstance;\n        child.dispatchEvent({\n          type: 'added'\n        });\n        parentInstance.dispatchEvent({\n          type: 'childadded',\n          child\n        });\n        const restSiblings = parentInstance.children.filter(sibling => sibling !== child);\n        const index = restSiblings.indexOf(beforeChild);\n        parentInstance.children = [...restSiblings.slice(0, index), child, ...restSiblings.slice(index)];\n        added = true;\n      }\n      if (!added) (_parentInstance$__r3f2 = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f2.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function removeRecursive(array, parent, dispose = false) {\n    if (array) [...array].forEach(child => removeChild(parent, child, dispose));\n  }\n  function removeChild(parentInstance, child, dispose) {\n    if (child) {\n      var _parentInstance$__r3f3, _child$__r3f3, _child$__r3f5;\n      // Clear the parent reference\n      if (child.__r3f) child.__r3f.parent = null;\n      // Remove child from the parents objects\n      if ((_parentInstance$__r3f3 = parentInstance.__r3f) != null && _parentInstance$__r3f3.objects) parentInstance.__r3f.objects = parentInstance.__r3f.objects.filter(x => x !== child);\n      // Remove attachment\n      if ((_child$__r3f3 = child.__r3f) != null && _child$__r3f3.attach) {\n        detach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        var _child$__r3f4;\n        parentInstance.remove(child);\n        // @ts-expect-error\n        // Remove interactivity on the initial root\n        if ((_child$__r3f4 = child.__r3f) != null && _child$__r3f4.root) {\n          removeInteractivity(findInitialRoot(child), child);\n        }\n      }\n\n      // Allow objects to bail out of recursive dispose altogether by passing dispose={null}\n      // Never dispose of primitives because their state may be kept outside of React!\n      // In order for an object to be able to dispose it has to have\n      //   - a dispose method,\n      //   - it cannot be a <primitive object={...} />\n      //   - it cannot be a THREE.Scene, because three has broken it's own api\n      //\n      // Since disposal is recursive, we can check the optional dispose arg, which will be undefined\n      // when the reconciler calls it, but then carry our own check recursively\n      const isPrimitive = (_child$__r3f5 = child.__r3f) == null ? void 0 : _child$__r3f5.primitive;\n      const shouldDispose = !isPrimitive && (dispose === undefined ? child.dispose !== null : dispose);\n\n      // Remove nested child objects. Primitives should not have objects and children that are\n      // attached to them declaratively ...\n      if (!isPrimitive) {\n        var _child$__r3f6;\n        removeRecursive((_child$__r3f6 = child.__r3f) == null ? void 0 : _child$__r3f6.objects, child, shouldDispose);\n        removeRecursive(child.children, child, shouldDispose);\n      }\n\n      // Remove references\n      delete child.__r3f;\n\n      // Dispose item whenever the reconciler feels like it\n      if (shouldDispose && child.dispose && child.type !== 'Scene') {\n        const callback = () => {\n          try {\n            child.dispose();\n          } catch (e) {\n            /* ... */\n          }\n        };\n\n        // Schedule async at runtime, flush sync in testing\n        if (typeof IS_REACT_ACT_ENVIRONMENT === 'undefined') {\n          unstable_scheduleCallback(unstable_IdlePriority, callback);\n        } else {\n          callback();\n        }\n      }\n      invalidateInstance(parentInstance);\n    }\n  }\n  function switchInstance(instance, type, newProps, fiber) {\n    var _instance$__r3f;\n    const parent = (_instance$__r3f = instance.__r3f) == null ? void 0 : _instance$__r3f.parent;\n    if (!parent) return;\n    const newInstance = createInstance(type, newProps, instance.__r3f.root);\n\n    // https://github.com/pmndrs/react-three-fiber/issues/1348\n    // When args change the instance has to be re-constructed, which then\n    // forces r3f to re-parent the children and non-scene objects\n    if (instance.children) {\n      for (const child of instance.children) {\n        if (child.__r3f) appendChild(newInstance, child);\n      }\n      instance.children = instance.children.filter(child => !child.__r3f);\n    }\n    instance.__r3f.objects.forEach(child => appendChild(newInstance, child));\n    instance.__r3f.objects = [];\n    if (!instance.__r3f.autoRemovedBeforeAppend) {\n      removeChild(parent, instance);\n    }\n    if (newInstance.parent) {\n      newInstance.__r3f.autoRemovedBeforeAppend = true;\n    }\n    appendChild(parent, newInstance);\n\n    // Re-bind event handlers on the initial root\n    if (newInstance.raycast && newInstance.__r3f.eventCount) {\n      const rootState = findInitialRoot(newInstance).getState();\n      rootState.internal.interaction.push(newInstance);\n    }\n    [fiber, fiber.alternate].forEach(fiber => {\n      if (fiber !== null) {\n        fiber.stateNode = newInstance;\n        if (fiber.ref) {\n          if (typeof fiber.ref === 'function') fiber.ref(newInstance);else fiber.ref.current = newInstance;\n        }\n      }\n    });\n  }\n\n  // Don't handle text instances, make it no-op\n  const handleTextInstance = () => {};\n  const reconciler = Reconciler({\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    supportsMutation: true,\n    isPrimaryRenderer: false,\n    supportsPersistence: false,\n    supportsHydration: false,\n    noTimeout: -1,\n    appendChildToContainer: (container, child) => {\n      if (!child) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n\n      // Link current root to the default scene\n      scene.__r3f.root = container;\n      appendChild(scene, child);\n    },\n    removeChildFromContainer: (container, child) => {\n      if (!child) return;\n      removeChild(container.getState().scene, child);\n    },\n    insertInContainerBefore: (container, child, beforeChild) => {\n      if (!child || !beforeChild) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n      insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: () => null,\n    getChildHostContext: parentHostContext => parentHostContext,\n    finalizeInitialChildren(instance) {\n      var _instance$__r3f2;\n      const localState = (_instance$__r3f2 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f2 : {};\n      // https://github.com/facebook/react/issues/20271\n      // Returning true will trigger commitMount\n      return Boolean(localState.handlers);\n    },\n    prepareUpdate(instance, _type, oldProps, newProps) {\n      var _instance$__r3f3;\n      const localState = (_instance$__r3f3 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f3 : {};\n\n      // Create diff-sets\n      if (localState.primitive && newProps.object && newProps.object !== instance) {\n        return [true];\n      } else {\n        // This is a data object, let's extract critical information about it\n        const {\n          args: argsNew = [],\n          children: cN,\n          ...restNew\n        } = newProps;\n        const {\n          args: argsOld = [],\n          children: cO,\n          ...restOld\n        } = oldProps;\n\n        // Throw if an object or literal was passed for args\n        if (!Array.isArray(argsNew)) throw new Error('R3F: the args prop must be an array!');\n\n        // If it has new props or arguments, then it needs to be re-instantiated\n        if (argsNew.some((value, index) => value !== argsOld[index])) return [true];\n        // Create a diff-set, flag if there are any changes\n        const diff = diffProps(instance, restNew, restOld, true);\n        if (diff.changes.length) return [false, diff];\n\n        // Otherwise do not touch the instance\n        return null;\n      }\n    },\n    commitUpdate(instance, [reconstruct, diff], type, _oldProps, newProps, fiber) {\n      // Reconstruct when args or <primitive object={...} have changes\n      if (reconstruct) switchInstance(instance, type, newProps, fiber);\n      // Otherwise just overwrite props\n      else applyProps$1(instance, diff);\n    },\n    commitMount(instance, _type, _props, _int) {\n      var _instance$__r3f4;\n      // https://github.com/facebook/react/issues/20271\n      // This will make sure events are only added once to the central container on the initial root\n      const localState = (_instance$__r3f4 = instance.__r3f) != null ? _instance$__r3f4 : {};\n      if (instance.raycast && localState.handlers && localState.eventCount) {\n        findInitialRoot(instance).getState().internal.interaction.push(instance);\n      }\n    },\n    getPublicInstance: instance => instance,\n    prepareForCommit: () => null,\n    preparePortalMount: container => prepare(container.getState().scene),\n    resetAfterCommit: () => {},\n    shouldSetTextContent: () => false,\n    clearContainer: () => false,\n    hideInstance(instance) {\n      var _instance$__r3f5;\n      // Detach while the instance is hidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f5 = instance.__r3f) != null ? _instance$__r3f5 : {};\n      if (type && parent) detach(parent, instance, type);\n      if (instance.isObject3D) instance.visible = false;\n      invalidateInstance(instance);\n    },\n    unhideInstance(instance, props) {\n      var _instance$__r3f6;\n      // Re-attach when the instance is unhidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f6 = instance.__r3f) != null ? _instance$__r3f6 : {};\n      if (type && parent) attach(parent, instance, type);\n      if (instance.isObject3D && props.visible == null || props.visible) instance.visible = true;\n      invalidateInstance(instance);\n    },\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n    // @ts-expect-error\n    getCurrentEventPriority: () => _getEventPriority ? _getEventPriority() : DefaultEventPriority,\n    beforeActiveInstanceBlur: () => {},\n    afterActiveInstanceBlur: () => {},\n    detachDeletedInstance: () => {},\n    now: typeof performance !== 'undefined' && is.fun(performance.now) ? performance.now : is.fun(Date.now) ? Date.now : () => 0,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r920883503\n    scheduleTimeout: is.fun(setTimeout) ? setTimeout : undefined,\n    cancelTimeout: is.fun(clearTimeout) ? clearTimeout : undefined\n  });\n  return {\n    reconciler,\n    applyProps: applyProps$1\n  };\n}\n\nvar _window$document, _window$navigator;\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nconst DEFAULT = '__default';\nconst DEFAULTS = new Map();\nconst isDiffSet = def => def && !!def.memoized && !!def.changes;\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nconst getRootState = obj => {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n};\n\n/**\r\n * Returns the instances initial (outmost) root\r\n */\nfunction findInitialRoot(child) {\n  let root = child.__r3f.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n/**\r\n * Collects nodes and materials from a THREE.Object3D.\r\n */\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.dispose && obj.type !== 'Scene') obj.dispose();\n  for (const p in obj) {\n    p.dispose == null ? void 0 : p.dispose();\n    delete obj[p];\n  }\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(object, state) {\n  const instance = object;\n  instance.__r3f = {\n    type: '',\n    root: null,\n    previousAttach: null,\n    memoizedProps: {},\n    eventCount: 0,\n    handlers: {},\n    objects: [],\n    parent: null,\n    ...state\n  };\n  return object;\n}\nfunction resolve(instance, key) {\n  let target = instance;\n  if (key.includes('-')) {\n    const entries = key.split('-');\n    const last = entries.pop();\n    target = entries.reduce((acc, key) => acc[key], instance);\n    return {\n      target,\n      key: last\n    };\n  } else return {\n    target,\n    key\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child, type) {\n  if (is.str(type)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(type)) {\n      const root = type.replace(INDEX_REGEX, '');\n      const {\n        target,\n        key\n      } = resolve(parent, root);\n      if (!Array.isArray(target[key])) target[key] = [];\n    }\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    child.__r3f.previousAttach = target[key];\n    target[key] = child;\n  } else child.__r3f.previousAttach = type(parent, child);\n}\nfunction detach(parent, child, type) {\n  var _child$__r3f, _child$__r3f2;\n  if (is.str(type)) {\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    const previous = child.__r3f.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete target[key];\n    // Otherwise set the previous value\n    else target[key] = previous;\n  } else (_child$__r3f = child.__r3f) == null ? void 0 : _child$__r3f.previousAttach == null ? void 0 : _child$__r3f.previousAttach(parent, child);\n  (_child$__r3f2 = child.__r3f) == null ? true : delete _child$__r3f2.previousAttach;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, {\n  children: cN,\n  key: kN,\n  ref: rN,\n  ...props\n}, {\n  children: cP,\n  key: kP,\n  ref: rP,\n  ...previous\n} = {}, remove = false) {\n  const localState = instance.__r3f;\n  const entries = Object.entries(props);\n  const changes = [];\n\n  // Catch removed props, prepend them so they can be reset or removed\n  if (remove) {\n    const previousKeys = Object.keys(previous);\n    for (let i = 0; i < previousKeys.length; i++) {\n      if (!props.hasOwnProperty(previousKeys[i])) entries.unshift([previousKeys[i], DEFAULT + 'remove']);\n    }\n  }\n  entries.forEach(([key, value]) => {\n    var _instance$__r3f;\n    // Bail out on primitive object\n    if ((_instance$__r3f = instance.__r3f) != null && _instance$__r3f.primitive && key === 'object') return;\n    // When props match bail out\n    if (is.equ(value, previous[key])) return;\n    // Collect handlers and bail out\n    if (/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(key)) return changes.push([key, value, true, []]);\n    // Split dashed props\n    let entries = [];\n    if (key.includes('-')) entries = key.split('-');\n    changes.push([key, value, false, entries]);\n\n    // Reset pierced props\n    for (const prop in props) {\n      const value = props[prop];\n      if (prop.startsWith(`${key}-`)) changes.push([prop, value, false, prop.split('-')]);\n    }\n  });\n  const memoized = {\n    ...props\n  };\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.args) memoized.args = localState.memoizedProps.args;\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.attach) memoized.attach = localState.memoizedProps.attach;\n  return {\n    memoized,\n    changes\n  };\n}\nconst __DEV__ = typeof process !== 'undefined' && process.env.NODE_ENV !== 'production';\n\n// This function applies a set of changes to the instance\nfunction applyProps$1(instance, data) {\n  var _instance$__r3f2;\n  // Filter equals, events and reserved props\n  const localState = instance.__r3f;\n  const root = localState == null ? void 0 : localState.root;\n  const rootState = root == null ? void 0 : root.getState == null ? void 0 : root.getState();\n  const {\n    memoized,\n    changes\n  } = isDiffSet(data) ? data : diffProps(instance, data);\n  const prevHandlers = localState == null ? void 0 : localState.eventCount;\n\n  // Prepare memoized props\n  if (instance.__r3f) instance.__r3f.memoizedProps = memoized;\n  for (let i = 0; i < changes.length; i++) {\n    let [key, value, isEvent, keys] = changes[i];\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(instance)) {\n      const sRGBEncoding = 3001;\n      const SRGBColorSpace = 'srgb';\n      const LinearSRGBColorSpace = 'srgb-linear';\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n    let currentInstance = instance;\n    let targetProp = currentInstance[key];\n\n    // Revolve dashed props\n    if (keys.length) {\n      targetProp = keys.reduce((acc, key) => acc[key], instance);\n      // If the target is atomic, it forces us to switch the root\n      if (!(targetProp && targetProp.set)) {\n        const [name, ...reverseEntries] = keys.reverse();\n        currentInstance = reverseEntries.reverse().reduce((acc, key) => acc[key], instance);\n        key = name;\n      }\n    }\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (value === DEFAULT + 'remove') {\n      if (currentInstance.constructor) {\n        // create a blank slate of the instance and copy the particular parameter.\n        let ctor = DEFAULTS.get(currentInstance.constructor);\n        if (!ctor) {\n          // @ts-expect-error\n          ctor = new currentInstance.constructor();\n          DEFAULTS.set(currentInstance.constructor, ctor);\n        }\n        value = ctor[key];\n      } else {\n        // instance does not have constructor, just set it to 0\n        value = 0;\n      }\n    }\n\n    // Deal with pointer events ...\n    if (isEvent && localState) {\n      if (value) localState.handlers[key] = value;else delete localState.handlers[key];\n      localState.eventCount = Object.keys(localState.handlers).length;\n    }\n    // Special treatment for objects with support for set/copy, and layers\n    else if (targetProp && targetProp.set && (targetProp.copy || targetProp instanceof THREE.Layers)) {\n      // If value is an array\n      if (Array.isArray(value)) {\n        if (targetProp.fromArray) targetProp.fromArray(value);else targetProp.set(...value);\n      }\n      // Test again target.copy(class) next ...\n      else if (targetProp.copy && value && value.constructor && (\n      // Some environments may break strict identity checks by duplicating versions of three.js.\n      // Loosen to unminified names, ignoring descendents.\n      // https://github.com/pmndrs/react-three-fiber/issues/2856\n      // TODO: fix upstream and remove in v9\n      __DEV__ ? targetProp.constructor.name === value.constructor.name : targetProp.constructor === value.constructor)) {\n        targetProp.copy(value);\n      }\n      // If nothing else fits, just set the single value, ignore undefined\n      // https://github.com/pmndrs/react-three-fiber/issues/274\n      else if (value !== undefined) {\n        var _targetProp;\n        const isColor = (_targetProp = targetProp) == null ? void 0 : _targetProp.isColor;\n        // Allow setting array scalars\n        if (!isColor && targetProp.setScalar) targetProp.setScalar(value);\n        // Layers have no copy function, we must therefore copy the mask property\n        else if (targetProp instanceof THREE.Layers && value instanceof THREE.Layers) targetProp.mask = value.mask;\n        // Otherwise just set ...\n        else targetProp.set(value);\n        // For versions of three which don't support THREE.ColorManagement,\n        // Auto-convert sRGB colors\n        // https://github.com/pmndrs/react-three-fiber/issues/344\n        if (!getColorManagement() && rootState && !rootState.linear && isColor) targetProp.convertSRGBToLinear();\n      }\n      // Else, just overwrite the value\n    } else {\n      var _currentInstance$key;\n      currentInstance[key] = value;\n\n      // Auto-convert sRGB textures, for now ...\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if ((_currentInstance$key = currentInstance[key]) != null && _currentInstance$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      currentInstance[key].format === THREE.RGBAFormat && currentInstance[key].type === THREE.UnsignedByteType && rootState) {\n        const texture = currentInstance[key];\n        if (hasColorSpace(texture) && hasColorSpace(rootState.gl)) texture.colorSpace = rootState.gl.outputColorSpace;else texture.encoding = rootState.gl.outputEncoding;\n      }\n    }\n    invalidateInstance(instance);\n  }\n  if (localState && localState.parent && instance.raycast && prevHandlers !== localState.eventCount) {\n    // Get the initial root state's internals\n    const internal = findInitialRoot(instance).getState().internal;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = internal.interaction.indexOf(instance);\n    if (index > -1) internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (localState.eventCount) internal.interaction.push(instance);\n  }\n\n  // Call the update lifecycle when it is being updated, but only when it is part of the scene.\n  // Skip updates to the `onUpdate` prop itself\n  const isCircular = changes.length === 1 && changes[0][0] === 'onUpdate';\n  if (!isCircular && changes.length && (_instance$__r3f2 = instance.__r3f) != null && _instance$__r3f2.parent) updateInstance(instance);\n  return instance;\n}\nfunction invalidateInstance(instance) {\n  var _instance$__r3f3, _instance$__r3f3$root;\n  const state = (_instance$__r3f3 = instance.__r3f) == null ? void 0 : (_instance$__r3f3$root = _instance$__r3f3.root) == null ? void 0 : _instance$__r3f3$root.getState == null ? void 0 : _instance$__r3f3$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateInstance(instance) {\n  instance.onUpdate == null ? void 0 : instance.onUpdate(instance);\n}\nfunction updateCamera(camera, size) {\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  // Do not mess with the camera if it belongs to the user\n  if (!camera.manual) {\n    if (isOrthographicCamera(camera)) {\n      camera.left = size.width / -2;\n      camera.right = size.width / 2;\n      camera.top = size.height / 2;\n      camera.bottom = size.height / -2;\n    } else {\n      camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n    // https://github.com/pmndrs/react-three-fiber/issues/178\n    // Update matrix world since the renderer is a frame late\n    camera.updateMatrixWorld();\n  }\n}\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n// https://github.com/facebook/react/tree/main/packages/react-reconciler#getcurrenteventpriority\n// Gives React a clue as to how import the current interaction is\nfunction getEventPriority() {\n  var _globalScope$event;\n  // Get a handle to the current global scope in window and worker contexts if able\n  // https://github.com/pmndrs/react-three-fiber/pull/2493\n  const globalScope = typeof self !== 'undefined' && self || typeof window !== 'undefined' && window;\n  if (!globalScope) return DefaultEventPriority;\n  const name = (_globalScope$event = globalScope.event) == null ? void 0 : _globalScope$event.type;\n  switch (name) {\n    case 'click':\n    case 'contextmenu':\n    case 'dblclick':\n    case 'pointercancel':\n    case 'pointerdown':\n    case 'pointerup':\n      return DiscreteEventPriority;\n    case 'pointermove':\n    case 'pointerout':\n    case 'pointerover':\n    case 'pointerenter':\n    case 'pointerleave':\n    case 'wheel':\n      return ContinuousEventPriority;\n    default:\n      return DefaultEventPriority;\n  }\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    const rootState = store.getState();\n\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object) || rootState;\n        const {\n          raycaster,\n          pointer,\n          camera,\n          internal\n        } = state;\n        const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n        const hasPointerCapture = id => {\n          var _internal$capturedMap, _internal$capturedMap2;\n          return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n        };\n        const setPointerCapture = id => {\n          const captureData = {\n            intersection: hit,\n            target: event.target\n          };\n          if (internal.capturedMap.has(id)) {\n            // if the pointerId was previously captured, we add the hit to the\n            // event capturedMap.\n            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n          } else {\n            // if the pointerId was not previously captured, we create a map\n            // containing the hitObject, and the hit. hitObject is used for\n            // faster access.\n            internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n          }\n          event.target.setPointerCapture(id);\n        };\n        const releasePointerCapture = id => {\n          const captures = internal.capturedMap.get(id);\n          if (captures) {\n            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n          }\n        };\n\n        // Add native event props\n        let extractEventProps = {};\n        // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n        for (let prop in event) {\n          let property = event[prop];\n          // Only copy over atomics, leave functions alone as these should be\n          // called as event.nativeEvent.fn()\n          if (typeof property !== 'function') extractEventProps[prop] = property;\n        }\n        let raycastEvent = {\n          ...hit,\n          ...extractEventProps,\n          pointer,\n          intersections,\n          stopped: localState.stopped,\n          delta,\n          unprojectedPoint,\n          ray: raycaster.ray,\n          camera: camera,\n          // Hijack stopPropagation, which just sets a flag\n          stopPropagation() {\n            // https://github.com/pmndrs/react-three-fiber/issues/596\n            // Events are not allowed to stop propagation if the pointer has been captured\n            const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n            // We only authorize stopPropagation...\n            if (\n            // ...if this pointer hasn't been captured\n            !capturesForPointer ||\n            // ... or if the hit object is capturing the pointer\n            capturesForPointer.has(hit.eventObject)) {\n              raycastEvent.stopped = localState.stopped = true;\n              // Propagation is stopped, remove all other hover records\n              // An event handler is only allowed to flush other handlers if it is hovered itself\n              if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                // Objects cannot flush out higher up objects that have already caught the event\n                const higher = intersections.slice(0, intersections.indexOf(hit));\n                cancelPointer([...higher, hit]);\n              }\n            }\n          },\n          // there should be a distinction between target and currentTarget\n          target: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          currentTarget: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          nativeEvent: event\n        };\n\n        // Call subscribers\n        callback(raycastEvent);\n        // Event bubbling may be interrupted by stopPropagation\n        if (localState.stopped === true) break;\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\n// Keys that shouldn't be copied between R3F stores\nconst privateKeys = ['set', 'get', 'setSize', 'setFrameloop', 'setDpr', 'events', 'invalidate', 'advance', 'size', 'viewport'];\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootState = create((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      xr: null,\n      scene: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        updateStyle: false\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, updateStyle, top, left) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top: top || 0,\n          left: left || 0,\n          updateStyle\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        active: false,\n        priority: 0,\n        frames: 0,\n        lastEvent: /*#__PURE__*/React.createRef(),\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootState.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootState.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootState.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      var _size$updateStyle;\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = (_size$updateStyle = size.updateStyle) != null ? _size$updateStyle : typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootState.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootState;\n};\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nlet i;\nlet globalEffects = new Set();\nlet globalAfterEffects = new Set();\nlet globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction render$1(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nfunction createLoop(roots) {\n  let running = false;\n  let useFrameInProgress = false;\n  let repeat;\n  let frame;\n  let state;\n  function loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n\n    // Run effects\n    flushGlobalEffects('before', timestamp);\n\n    // Render all roots\n    useFrameInProgress = true;\n    for (const root of roots.values()) {\n      var _state$gl$xr;\n      state = root.store.getState();\n      // If the frameloop is invalidated, do not run another frame\n      if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n        repeat += render$1(timestamp, state);\n      }\n    }\n    useFrameInProgress = false;\n\n    // Run after-effects\n    flushGlobalEffects('after', timestamp);\n\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n      // Tail call effects, they are called when rendering stops\n      flushGlobalEffects('tail', timestamp);\n\n      // Flag end of operation\n      running = false;\n      return cancelAnimationFrame(frame);\n    }\n  }\n  function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return roots.forEach(root => invalidate(root.store.getState(), frames));\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n    if (frames > 1) {\n      // legacy support for people using frames parameters\n      // Increase frames, do not go higher than 60\n      state.internal.frames = Math.min(60, state.internal.frames + frames);\n    } else {\n      if (useFrameInProgress) {\n        //called from within a useFrame, it means the user wants an additional frame\n        state.internal.frames = 2;\n      } else {\n        //the user need a new frame, no need to increment further than 1\n        state.internal.frames = 1;\n      }\n    }\n\n    // If the render-loop isn't active, start it\n    if (!running) {\n      running = true;\n      requestAnimationFrame(loop);\n    }\n  }\n  function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n    if (!state) for (const root of roots.values()) render$1(timestamp, root.store.getState());else render$1(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n  }\n  return {\n    loop,\n    invalidate,\n    advance\n  };\n}\n\n/**\r\n * Exposes an object's {@link LocalState}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  useIsomorphicLayoutEffect(() => void (instance.current = ref.current.__r3f), [ref]);\n  return instance;\n}\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    // Construct new loader and run extensions\n    let loader = memoizedLoaders.get(Proto);\n    if (!loader) {\n      loader = new Proto();\n      memoizedLoaders.set(Proto, loader);\n    }\n    if (extensions) extensions(loader);\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (data.scene) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(Proto, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [Proto, ...keys], {\n    equal: is.equ\n  });\n  // Return the object/s\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (Proto, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [Proto, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (Proto, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([Proto, ...keys]);\n};\n\nconst roots = new Map();\nconst {\n  invalidate,\n  advance\n} = createLoop(roots);\nconst {\n  reconciler,\n  applyProps\n} = createRenderer(roots, getEventPriority);\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;else return new THREE.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, defaultSize) {\n  const defaultStyle = typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement;\n  if (defaultSize) {\n    const {\n      width,\n      height,\n      top,\n      left,\n      updateStyle = defaultStyle\n    } = defaultSize;\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle\n    };\n  } else if (typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle: defaultStyle\n    };\n  } else if (typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0,\n      updateStyle: defaultStyle\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store, ConcurrentRoot, null, false, null, '', logRecoverableError, null);\n  // Map it\n  if (!prevRoot) roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof THREE.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n              camera.manual = true;\n              camera.updateProjectionMatrix();\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n        } else {\n          scene = new THREE.Scene();\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene: prepare(scene)\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n      if (!configured) {\n        // Set color space and tonemapping preferences, once\n        const LinearEncoding = 3000;\n        const sRGBEncoding = 3001;\n        applyProps(gl, {\n          outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n          toneMapping: flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping\n        });\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.updateStyle, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer( /*#__PURE__*/jsx(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state);\n            roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  }, container.uuid);\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = React.useCallback((rootState, injectState) => {\n    const intersect = {\n      ...rootState\n    }; // all prev state props\n\n    // Only the fields of \"rootState\" that do not differ from injectState\n    // Some props should be off-limits\n    // Otherwise filter out the props that are different and let the inject layer take precedence\n    Object.keys(rootState).forEach(key => {\n      if (\n      // Some props should be off-limits\n      privateKeys.includes(key) ||\n      // Otherwise filter out the props that are different and let the inject layer take precedence\n      // Unless the inject layer props is undefined, then we keep the root layer\n      rootState[key] !== injectState[key] && injectState[key]) {\n        delete intersect[key];\n      }\n    });\n    let viewport = undefined;\n    if (injectState && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...intersect,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...(injectState == null ? void 0 : injectState.events),\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      ...rest\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [state]);\n  const [usePortalStore] = React.useState(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const previousState = previousRoot.getState();\n    const store = create((set, get) => ({\n      ...previousState,\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      previousRoot,\n      events: {\n        ...previousState.events,\n        ...events\n      },\n      size: {\n        ...previousState.size,\n        ...size\n      },\n      ...rest,\n      // Set and get refer to this root-state\n      set,\n      get,\n      // Layers are allowed to override events\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    }));\n    return store;\n  });\n  React.useEffect(() => {\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const unsub = previousRoot.subscribe(prev => usePortalStore.setState(state => inject(prev, state)));\n    return () => {\n      unsub();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  React.useEffect(() => {\n    usePortalStore.setState(injectState => inject(previousRoot.getState(), injectState));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  React.useEffect(() => {\n    return () => {\n      usePortalStore.destroy();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(Fragment, {\n    children: reconciler.createPortal( /*#__PURE__*/jsx(context.Provider, {\n      value: usePortalStore,\n      children: children\n    }), usePortalStore, null)\n  });\n}\n\n/**\r\n * Force React to flush any updates inside the provided callback synchronously and immediately.\r\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\r\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\r\n * having to revert to a non-React solution.\r\n */\nfunction flushSync(fn) {\n  // `flushSync` implementation only takes one argument. I don't know what's up with the type declaration for it.\n  return reconciler.flushSync(fn, undefined);\n}\nreconciler.injectIntoDevTools({\n  bundleType: process.env.NODE_ENV === 'production' ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: React.version\n});\nconst act = React.unstable_act;\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      var _events$handlers;\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      Object.entries((_events$handlers = events.handlers) != null ? _events$handlers : []).forEach(([name, event]) => {\n        const [eventName, passive] = DOM_EVENTS[name];\n        target.addEventListener(eventName, event, {\n          passive\n        });\n      });\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        var _events$handlers2;\n        Object.entries((_events$handlers2 = events.handlers) != null ? _events$handlers2 : []).forEach(([name, event]) => {\n          if (events && events.connected instanceof HTMLElement) {\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        });\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\nexport { useInstanceHandle as A, Block as B, useStore as C, useThree as D, ErrorBoundary as E, useFrame as F, useGraph as G, useLoader as H, useIsomorphicLayoutEffect as a, createRoot as b, createPointerEvents as c, unmountComponentAtNode as d, extend as e, createEvents as f, context as g, createPortal as h, isRef as i, reconciler as j, applyProps as k, dispose as l, invalidate as m, advance as n, addEffect as o, addAfterEffect as p, addTail as q, render as r, flushGlobalEffects as s, threeTypes as t, useMutableCallback as u, flushSync as v, getRootState as w, act as x, buildGraph as y, roots as z };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,cAAc,QAAQ,4BAA4B;AACjI,OAAOC,MAAM,MAAM,SAAS;AAC5B,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,eAAe;AACvD,SAASC,GAAG,EAAEC,QAAQ,QAAQ,mBAAmB;AACjD,OAAOC,UAAU,MAAM,kBAAkB;AACzC,SAASC,yBAAyB,EAAEC,qBAAqB,QAAQ,WAAW;AAE5E,IAAIC,UAAU,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EAC1CC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,MAAMC,SAAS,GAAG,CAAC,CAAC;AACpB,MAAMC,MAAM,GAAGC,OAAO,IAAI,KAAKL,MAAM,CAACM,MAAM,CAACH,SAAS,EAAEE,OAAO,CAAC;AAChE,SAASE,cAAcA,CAACC,MAAM,EAAEC,iBAAiB,EAAE;EACjD,SAASC,cAAcA,CAACC,IAAI,EAAE;IAC5BC,IAAI,GAAG,EAAE;IACTC,MAAM;IACN,GAAGC;EACL,CAAC,EAAEC,IAAI,EAAE;IACP,IAAIC,IAAI,GAAG,GAAGL,IAAI,CAAC,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC,GAAGN,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC,EAAE;IACrD,IAAIC,QAAQ;IACZ,IAAIR,IAAI,KAAK,WAAW,EAAE;MACxB,IAAIG,KAAK,CAACM,MAAM,KAAKC,SAAS,EAAE,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;MAChG,MAAMF,MAAM,GAAGN,KAAK,CAACM,MAAM;MAC3BD,QAAQ,GAAGI,OAAO,CAACH,MAAM,EAAE;QACzBT,IAAI;QACJI,IAAI;QACJF,MAAM;QACNW,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMC,MAAM,GAAGtB,SAAS,CAACa,IAAI,CAAC;MAC9B,IAAI,CAACS,MAAM,EAAE;QACX,MAAM,IAAIH,KAAK,CAAC,QAAQN,IAAI,8JAA8J,CAAC;MAC7L;;MAEA;MACA,IAAI,CAACU,KAAK,CAACC,OAAO,CAACf,IAAI,CAAC,EAAE,MAAM,IAAIU,KAAK,CAAC,sCAAsC,CAAC;;MAEjF;MACA;MACAH,QAAQ,GAAGI,OAAO,CAAC,IAAIE,MAAM,CAAC,GAAGb,IAAI,CAAC,EAAE;QACtCD,IAAI;QACJI,IAAI;QACJF,MAAM;QACN;QACAe,aAAa,EAAE;UACbhB;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIO,QAAQ,CAACU,KAAK,CAAChB,MAAM,KAAKQ,SAAS,EAAE;MACvC,IAAIF,QAAQ,CAACW,gBAAgB,EAAEX,QAAQ,CAACU,KAAK,CAAChB,MAAM,GAAG,UAAU,CAAC,KAAK,IAAIM,QAAQ,CAACY,UAAU,EAAEZ,QAAQ,CAACU,KAAK,CAAChB,MAAM,GAAG,UAAU;IACpI;;IAEA;IACA;IACA;IACA;IACA,IAAIG,IAAI,KAAK,QAAQ,EAAEgB,YAAY,CAACb,QAAQ,EAAEL,KAAK,CAAC;IACpD,OAAOK,QAAQ;EACjB;EACA,SAASc,WAAWA,CAACC,cAAc,EAAEC,KAAK,EAAE;IAC1C,IAAIC,KAAK,GAAG,KAAK;IACjB,IAAID,KAAK,EAAE;MACT,IAAIE,YAAY,EAAEC,qBAAqB;MACvC;MACA,IAAI,CAACD,YAAY,GAAGF,KAAK,CAACN,KAAK,KAAK,IAAI,IAAIQ,YAAY,CAACxB,MAAM,EAAE;QAC/DA,MAAM,CAACqB,cAAc,EAAEC,KAAK,EAAEA,KAAK,CAACN,KAAK,CAAChB,MAAM,CAAC;MACnD,CAAC,MAAM,IAAIsB,KAAK,CAACI,UAAU,IAAIL,cAAc,CAACK,UAAU,EAAE;QACxD;QACAL,cAAc,CAACM,GAAG,CAACL,KAAK,CAAC;QACzBC,KAAK,GAAG,IAAI;MACd;MACA;MACA;MACA,IAAI,CAACA,KAAK,EAAE,CAACE,qBAAqB,GAAGJ,cAAc,CAACL,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGS,qBAAqB,CAACjC,OAAO,CAACoC,IAAI,CAACN,KAAK,CAAC;MACvH,IAAI,CAACA,KAAK,CAACN,KAAK,EAAEN,OAAO,CAACY,KAAK,EAAE,CAAC,CAAC,CAAC;MACpCA,KAAK,CAACN,KAAK,CAACa,MAAM,GAAGR,cAAc;MACnCS,cAAc,CAACR,KAAK,CAAC;MACrBS,kBAAkB,CAACT,KAAK,CAAC;IAC3B;EACF;EACA,SAASU,YAAYA,CAACX,cAAc,EAAEC,KAAK,EAAEW,WAAW,EAAE;IACxD,IAAIV,KAAK,GAAG,KAAK;IACjB,IAAID,KAAK,EAAE;MACT,IAAIY,aAAa,EAAEC,sBAAsB;MACzC,IAAI,CAACD,aAAa,GAAGZ,KAAK,CAACN,KAAK,KAAK,IAAI,IAAIkB,aAAa,CAAClC,MAAM,EAAE;QACjEA,MAAM,CAACqB,cAAc,EAAEC,KAAK,EAAEA,KAAK,CAACN,KAAK,CAAChB,MAAM,CAAC;MACnD,CAAC,MAAM,IAAIsB,KAAK,CAACI,UAAU,IAAIL,cAAc,CAACK,UAAU,EAAE;QACxDJ,KAAK,CAACO,MAAM,GAAGR,cAAc;QAC7BC,KAAK,CAACc,aAAa,CAAC;UAClBtC,IAAI,EAAE;QACR,CAAC,CAAC;QACFuB,cAAc,CAACe,aAAa,CAAC;UAC3BtC,IAAI,EAAE,YAAY;UAClBwB;QACF,CAAC,CAAC;QACF,MAAMe,YAAY,GAAGhB,cAAc,CAACiB,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAKlB,KAAK,CAAC;QACjF,MAAMmB,KAAK,GAAGJ,YAAY,CAACK,OAAO,CAACT,WAAW,CAAC;QAC/CZ,cAAc,CAACiB,QAAQ,GAAG,CAAC,GAAGD,YAAY,CAAChC,KAAK,CAAC,CAAC,EAAEoC,KAAK,CAAC,EAAEnB,KAAK,EAAE,GAAGe,YAAY,CAAChC,KAAK,CAACoC,KAAK,CAAC,CAAC;QAChGlB,KAAK,GAAG,IAAI;MACd;MACA,IAAI,CAACA,KAAK,EAAE,CAACY,sBAAsB,GAAGd,cAAc,CAACL,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,sBAAsB,CAAC3C,OAAO,CAACoC,IAAI,CAACN,KAAK,CAAC;MACzH,IAAI,CAACA,KAAK,CAACN,KAAK,EAAEN,OAAO,CAACY,KAAK,EAAE,CAAC,CAAC,CAAC;MACpCA,KAAK,CAACN,KAAK,CAACa,MAAM,GAAGR,cAAc;MACnCS,cAAc,CAACR,KAAK,CAAC;MACrBS,kBAAkB,CAACT,KAAK,CAAC;IAC3B;EACF;EACA,SAASqB,eAAeA,CAACC,KAAK,EAAEf,MAAM,EAAEgB,OAAO,GAAG,KAAK,EAAE;IACvD,IAAID,KAAK,EAAE,CAAC,GAAGA,KAAK,CAAC,CAACE,OAAO,CAACxB,KAAK,IAAIyB,WAAW,CAAClB,MAAM,EAAEP,KAAK,EAAEuB,OAAO,CAAC,CAAC;EAC7E;EACA,SAASE,WAAWA,CAAC1B,cAAc,EAAEC,KAAK,EAAEuB,OAAO,EAAE;IACnD,IAAIvB,KAAK,EAAE;MACT,IAAI0B,sBAAsB,EAAEC,aAAa,EAAEC,aAAa;MACxD;MACA,IAAI5B,KAAK,CAACN,KAAK,EAAEM,KAAK,CAACN,KAAK,CAACa,MAAM,GAAG,IAAI;MAC1C;MACA,IAAI,CAACmB,sBAAsB,GAAG3B,cAAc,CAACL,KAAK,KAAK,IAAI,IAAIgC,sBAAsB,CAACxD,OAAO,EAAE6B,cAAc,CAACL,KAAK,CAACxB,OAAO,GAAG6B,cAAc,CAACL,KAAK,CAACxB,OAAO,CAAC+C,MAAM,CAACY,CAAC,IAAIA,CAAC,KAAK7B,KAAK,CAAC;MACnL;MACA,IAAI,CAAC2B,aAAa,GAAG3B,KAAK,CAACN,KAAK,KAAK,IAAI,IAAIiC,aAAa,CAACjD,MAAM,EAAE;QACjEoD,MAAM,CAAC/B,cAAc,EAAEC,KAAK,EAAEA,KAAK,CAACN,KAAK,CAAChB,MAAM,CAAC;MACnD,CAAC,MAAM,IAAIsB,KAAK,CAACI,UAAU,IAAIL,cAAc,CAACK,UAAU,EAAE;QACxD,IAAI2B,aAAa;QACjBhC,cAAc,CAACiC,MAAM,CAAChC,KAAK,CAAC;QAC5B;QACA;QACA,IAAI,CAAC+B,aAAa,GAAG/B,KAAK,CAACN,KAAK,KAAK,IAAI,IAAIqC,aAAa,CAACnD,IAAI,EAAE;UAC/DqD,mBAAmB,CAACC,eAAe,CAAClC,KAAK,CAAC,EAAEA,KAAK,CAAC;QACpD;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMmC,WAAW,GAAG,CAACP,aAAa,GAAG5B,KAAK,CAACN,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkC,aAAa,CAACvC,SAAS;MAC5F,MAAM+C,aAAa,GAAG,CAACD,WAAW,KAAKZ,OAAO,KAAKrC,SAAS,GAAGc,KAAK,CAACuB,OAAO,KAAK,IAAI,GAAGA,OAAO,CAAC;;MAEhG;MACA;MACA,IAAI,CAACY,WAAW,EAAE;QAChB,IAAIE,aAAa;QACjBhB,eAAe,CAAC,CAACgB,aAAa,GAAGrC,KAAK,CAACN,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2C,aAAa,CAACnE,OAAO,EAAE8B,KAAK,EAAEoC,aAAa,CAAC;QAC7Gf,eAAe,CAACrB,KAAK,CAACgB,QAAQ,EAAEhB,KAAK,EAAEoC,aAAa,CAAC;MACvD;;MAEA;MACA,OAAOpC,KAAK,CAACN,KAAK;;MAElB;MACA,IAAI0C,aAAa,IAAIpC,KAAK,CAACuB,OAAO,IAAIvB,KAAK,CAACxB,IAAI,KAAK,OAAO,EAAE;QAC5D,MAAM8D,QAAQ,GAAGA,CAAA,KAAM;UACrB,IAAI;YACFtC,KAAK,CAACuB,OAAO,CAAC,CAAC;UACjB,CAAC,CAAC,OAAOgB,CAAC,EAAE;YACV;UAAA;QAEJ,CAAC;;QAED;QACA,IAAI,OAAOC,wBAAwB,KAAK,WAAW,EAAE;UACnD9E,yBAAyB,CAACC,qBAAqB,EAAE2E,QAAQ,CAAC;QAC5D,CAAC,MAAM;UACLA,QAAQ,CAAC,CAAC;QACZ;MACF;MACA7B,kBAAkB,CAACV,cAAc,CAAC;IACpC;EACF;EACA,SAAS0C,cAAcA,CAACzD,QAAQ,EAAER,IAAI,EAAEkE,QAAQ,EAAEC,KAAK,EAAE;IACvD,IAAIC,eAAe;IACnB,MAAMrC,MAAM,GAAG,CAACqC,eAAe,GAAG5D,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkD,eAAe,CAACrC,MAAM;IAC3F,IAAI,CAACA,MAAM,EAAE;IACb,MAAMsC,WAAW,GAAGtE,cAAc,CAACC,IAAI,EAAEkE,QAAQ,EAAE1D,QAAQ,CAACU,KAAK,CAACd,IAAI,CAAC;;IAEvE;IACA;IACA;IACA,IAAII,QAAQ,CAACgC,QAAQ,EAAE;MACrB,KAAK,MAAMhB,KAAK,IAAIhB,QAAQ,CAACgC,QAAQ,EAAE;QACrC,IAAIhB,KAAK,CAACN,KAAK,EAAEI,WAAW,CAAC+C,WAAW,EAAE7C,KAAK,CAAC;MAClD;MACAhB,QAAQ,CAACgC,QAAQ,GAAGhC,QAAQ,CAACgC,QAAQ,CAACC,MAAM,CAACjB,KAAK,IAAI,CAACA,KAAK,CAACN,KAAK,CAAC;IACrE;IACAV,QAAQ,CAACU,KAAK,CAACxB,OAAO,CAACsD,OAAO,CAACxB,KAAK,IAAIF,WAAW,CAAC+C,WAAW,EAAE7C,KAAK,CAAC,CAAC;IACxEhB,QAAQ,CAACU,KAAK,CAACxB,OAAO,GAAG,EAAE;IAC3B,IAAI,CAACc,QAAQ,CAACU,KAAK,CAACoD,uBAAuB,EAAE;MAC3CrB,WAAW,CAAClB,MAAM,EAAEvB,QAAQ,CAAC;IAC/B;IACA,IAAI6D,WAAW,CAACtC,MAAM,EAAE;MACtBsC,WAAW,CAACnD,KAAK,CAACoD,uBAAuB,GAAG,IAAI;IAClD;IACAhD,WAAW,CAACS,MAAM,EAAEsC,WAAW,CAAC;;IAEhC;IACA,IAAIA,WAAW,CAACE,OAAO,IAAIF,WAAW,CAACnD,KAAK,CAACsD,UAAU,EAAE;MACvD,MAAMC,SAAS,GAAGf,eAAe,CAACW,WAAW,CAAC,CAACK,QAAQ,CAAC,CAAC;MACzDD,SAAS,CAACE,QAAQ,CAACC,WAAW,CAAC9C,IAAI,CAACuC,WAAW,CAAC;IAClD;IACA,CAACF,KAAK,EAAEA,KAAK,CAACU,SAAS,CAAC,CAAC7B,OAAO,CAACmB,KAAK,IAAI;MACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClBA,KAAK,CAACW,SAAS,GAAGT,WAAW;QAC7B,IAAIF,KAAK,CAACY,GAAG,EAAE;UACb,IAAI,OAAOZ,KAAK,CAACY,GAAG,KAAK,UAAU,EAAEZ,KAAK,CAACY,GAAG,CAACV,WAAW,CAAC,CAAC,KAAKF,KAAK,CAACY,GAAG,CAACC,OAAO,GAAGX,WAAW;QAClG;MACF;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMY,kBAAkB,GAAGA,CAAA,KAAM,CAAC,CAAC;EACnC,MAAMC,UAAU,GAAGjG,UAAU,CAAC;IAC5Bc,cAAc;IACdkD,WAAW;IACX3B,WAAW;IACX6D,kBAAkB,EAAE7D,WAAW;IAC/BY,YAAY;IACZkD,gBAAgB,EAAE,IAAI;IACtBC,iBAAiB,EAAE,KAAK;IACxBC,mBAAmB,EAAE,KAAK;IAC1BC,iBAAiB,EAAE,KAAK;IACxBC,SAAS,EAAE,CAAC,CAAC;IACbC,sBAAsB,EAAEA,CAACC,SAAS,EAAElE,KAAK,KAAK;MAC5C,IAAI,CAACA,KAAK,EAAE;;MAEZ;MACA,MAAMmE,KAAK,GAAGD,SAAS,CAAChB,QAAQ,CAAC,CAAC,CAACiB,KAAK;MACxC,IAAI,CAACA,KAAK,CAACzE,KAAK,EAAE;;MAElB;MACAyE,KAAK,CAACzE,KAAK,CAACd,IAAI,GAAGsF,SAAS;MAC5BpE,WAAW,CAACqE,KAAK,EAAEnE,KAAK,CAAC;IAC3B,CAAC;IACDoE,wBAAwB,EAAEA,CAACF,SAAS,EAAElE,KAAK,KAAK;MAC9C,IAAI,CAACA,KAAK,EAAE;MACZyB,WAAW,CAACyC,SAAS,CAAChB,QAAQ,CAAC,CAAC,CAACiB,KAAK,EAAEnE,KAAK,CAAC;IAChD,CAAC;IACDqE,uBAAuB,EAAEA,CAACH,SAAS,EAAElE,KAAK,EAAEW,WAAW,KAAK;MAC1D,IAAI,CAACX,KAAK,IAAI,CAACW,WAAW,EAAE;;MAE5B;MACA,MAAMwD,KAAK,GAAGD,SAAS,CAAChB,QAAQ,CAAC,CAAC,CAACiB,KAAK;MACxC,IAAI,CAACA,KAAK,CAACzE,KAAK,EAAE;MAClBgB,YAAY,CAACyD,KAAK,EAAEnE,KAAK,EAAEW,WAAW,CAAC;IACzC,CAAC;IACD2D,kBAAkB,EAAEA,CAAA,KAAM,IAAI;IAC9BC,mBAAmB,EAAEC,iBAAiB,IAAIA,iBAAiB;IAC3DC,uBAAuBA,CAACzF,QAAQ,EAAE;MAChC,IAAI0F,gBAAgB;MACpB,MAAMC,UAAU,GAAG,CAACD,gBAAgB,GAAG1F,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAGgF,gBAAgB,GAAG,CAAC,CAAC;MAClH;MACA;MACA,OAAOE,OAAO,CAACD,UAAU,CAACE,QAAQ,CAAC;IACrC,CAAC;IACDC,aAAaA,CAAC9F,QAAQ,EAAE+F,KAAK,EAAEC,QAAQ,EAAEtC,QAAQ,EAAE;MACjD,IAAIuC,gBAAgB;MACpB,MAAMN,UAAU,GAAG,CAACM,gBAAgB,GAAGjG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAGuF,gBAAgB,GAAG,CAAC,CAAC;;MAElH;MACA,IAAIN,UAAU,CAACtF,SAAS,IAAIqD,QAAQ,CAACzD,MAAM,IAAIyD,QAAQ,CAACzD,MAAM,KAAKD,QAAQ,EAAE;QAC3E,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,MAAM;QACL;QACA,MAAM;UACJP,IAAI,EAAEyG,OAAO,GAAG,EAAE;UAClBlE,QAAQ,EAAEmE,EAAE;UACZ,GAAGC;QACL,CAAC,GAAG1C,QAAQ;QACZ,MAAM;UACJjE,IAAI,EAAE4G,OAAO,GAAG,EAAE;UAClBrE,QAAQ,EAAEsE,EAAE;UACZ,GAAGC;QACL,CAAC,GAAGP,QAAQ;;QAEZ;QACA,IAAI,CAACzF,KAAK,CAACC,OAAO,CAAC0F,OAAO,CAAC,EAAE,MAAM,IAAI/F,KAAK,CAAC,sCAAsC,CAAC;;QAEpF;QACA,IAAI+F,OAAO,CAACM,IAAI,CAAC,CAACC,KAAK,EAAEtE,KAAK,KAAKsE,KAAK,KAAKJ,OAAO,CAAClE,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC;QAC3E;QACA,MAAMuE,IAAI,GAAGC,SAAS,CAAC3G,QAAQ,EAAEoG,OAAO,EAAEG,OAAO,EAAE,IAAI,CAAC;QACxD,IAAIG,IAAI,CAACE,OAAO,CAACC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAEH,IAAI,CAAC;;QAE7C;QACA,OAAO,IAAI;MACb;IACF,CAAC;IACDI,YAAYA,CAAC9G,QAAQ,EAAE,CAAC+G,WAAW,EAAEL,IAAI,CAAC,EAAElH,IAAI,EAAEwH,SAAS,EAAEtD,QAAQ,EAAEC,KAAK,EAAE;MAC5E;MACA,IAAIoD,WAAW,EAAEtD,cAAc,CAACzD,QAAQ,EAAER,IAAI,EAAEkE,QAAQ,EAAEC,KAAK,CAAC;MAChE;MAAA,KACK9C,YAAY,CAACb,QAAQ,EAAE0G,IAAI,CAAC;IACnC,CAAC;IACDO,WAAWA,CAACjH,QAAQ,EAAE+F,KAAK,EAAEmB,MAAM,EAAEC,IAAI,EAAE;MACzC,IAAIC,gBAAgB;MACpB;MACA;MACA,MAAMzB,UAAU,GAAG,CAACyB,gBAAgB,GAAGpH,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAG0G,gBAAgB,GAAG,CAAC,CAAC;MACtF,IAAIpH,QAAQ,CAAC+D,OAAO,IAAI4B,UAAU,CAACE,QAAQ,IAAIF,UAAU,CAAC3B,UAAU,EAAE;QACpEd,eAAe,CAAClD,QAAQ,CAAC,CAACkE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACC,WAAW,CAAC9C,IAAI,CAACtB,QAAQ,CAAC;MAC1E;IACF,CAAC;IACDqH,iBAAiB,EAAErH,QAAQ,IAAIA,QAAQ;IACvCsH,gBAAgB,EAAEA,CAAA,KAAM,IAAI;IAC5BC,kBAAkB,EAAErC,SAAS,IAAI9E,OAAO,CAAC8E,SAAS,CAAChB,QAAQ,CAAC,CAAC,CAACiB,KAAK,CAAC;IACpEqC,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC1BC,oBAAoB,EAAEA,CAAA,KAAM,KAAK;IACjCC,cAAc,EAAEA,CAAA,KAAM,KAAK;IAC3BC,YAAYA,CAAC3H,QAAQ,EAAE;MACrB,IAAI4H,gBAAgB;MACpB;MACA,MAAM;QACJlI,MAAM,EAAEF,IAAI;QACZ+B;MACF,CAAC,GAAG,CAACqG,gBAAgB,GAAG5H,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAGkH,gBAAgB,GAAG,CAAC,CAAC;MACvE,IAAIpI,IAAI,IAAI+B,MAAM,EAAEuB,MAAM,CAACvB,MAAM,EAAEvB,QAAQ,EAAER,IAAI,CAAC;MAClD,IAAIQ,QAAQ,CAACoB,UAAU,EAAEpB,QAAQ,CAAC6H,OAAO,GAAG,KAAK;MACjDpG,kBAAkB,CAACzB,QAAQ,CAAC;IAC9B,CAAC;IACD8H,cAAcA,CAAC9H,QAAQ,EAAEL,KAAK,EAAE;MAC9B,IAAIoI,gBAAgB;MACpB;MACA,MAAM;QACJrI,MAAM,EAAEF,IAAI;QACZ+B;MACF,CAAC,GAAG,CAACwG,gBAAgB,GAAG/H,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAGqH,gBAAgB,GAAG,CAAC,CAAC;MACvE,IAAIvI,IAAI,IAAI+B,MAAM,EAAE7B,MAAM,CAAC6B,MAAM,EAAEvB,QAAQ,EAAER,IAAI,CAAC;MAClD,IAAIQ,QAAQ,CAACoB,UAAU,IAAIzB,KAAK,CAACkI,OAAO,IAAI,IAAI,IAAIlI,KAAK,CAACkI,OAAO,EAAE7H,QAAQ,CAAC6H,OAAO,GAAG,IAAI;MAC1FpG,kBAAkB,CAACzB,QAAQ,CAAC;IAC9B,CAAC;IACDgI,kBAAkB,EAAEvD,kBAAkB;IACtCwD,gBAAgB,EAAExD,kBAAkB;IACpCyD,kBAAkB,EAAEzD,kBAAkB;IACtC;IACA;IACA0D,uBAAuB,EAAEA,CAAA,KAAM7I,iBAAiB,GAAGA,iBAAiB,CAAC,CAAC,GAAGvB,oBAAoB;IAC7FqK,wBAAwB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAClCC,uBAAuB,EAAEA,CAAA,KAAM,CAAC,CAAC;IACjCC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,GAAG,EAAE,OAAOC,WAAW,KAAK,WAAW,IAAIC,EAAE,CAACC,GAAG,CAACF,WAAW,CAACD,GAAG,CAAC,GAAGC,WAAW,CAACD,GAAG,GAAGE,EAAE,CAACC,GAAG,CAACC,IAAI,CAACJ,GAAG,CAAC,GAAGI,IAAI,CAACJ,GAAG,GAAG,MAAM,CAAC;IAC5H;IACAK,eAAe,EAAEH,EAAE,CAACC,GAAG,CAACG,UAAU,CAAC,GAAGA,UAAU,GAAG3I,SAAS;IAC5D4I,aAAa,EAAEL,EAAE,CAACC,GAAG,CAACK,YAAY,CAAC,GAAGA,YAAY,GAAG7I;EACvD,CAAC,CAAC;EACF,OAAO;IACLwE,UAAU;IACVsE,UAAU,EAAEnI;EACd,CAAC;AACH;AAEA,IAAIoI,gBAAgB,EAAEC,iBAAiB;AACvC;AACA;AACA;AACA,MAAMC,aAAa,GAAGlJ,MAAM,IAAI,YAAY,IAAIA,MAAM,IAAI,kBAAkB,IAAIA,MAAM;AACtF;AACA;AACA;AACA,MAAMmJ,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,IAAIC,gBAAgB;EACpB,OAAO,CAACA,gBAAgB,GAAGrK,SAAS,CAACsK,eAAe,KAAK,IAAI,GAAGD,gBAAgB,GAAG,IAAI;AACzF,CAAC;AACD,MAAME,oBAAoB,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACD,oBAAoB;AACnE,MAAME,KAAK,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,cAAc,CAAC,SAAS,CAAC;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,OAAOC,MAAM,KAAK,WAAW,KAAK,CAACZ,gBAAgB,GAAGY,MAAM,CAACC,QAAQ,KAAK,IAAI,IAAIb,gBAAgB,CAACc,aAAa,IAAI,CAAC,CAACb,iBAAiB,GAAGW,MAAM,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGd,iBAAiB,CAACe,OAAO,MAAM,aAAa,CAAC,GAAGnM,KAAK,CAACoM,eAAe,GAAGpM,KAAK,CAACqM,SAAS;AACxS,SAASC,kBAAkBA,CAACC,EAAE,EAAE;EAC9B,MAAM9F,GAAG,GAAGzG,KAAK,CAACwM,MAAM,CAACD,EAAE,CAAC;EAC5BT,yBAAyB,CAAC,MAAM,MAAMrF,GAAG,CAACC,OAAO,GAAG6F,EAAE,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAC9D,OAAO9F,GAAG;AACZ;AACA,SAASgG,KAAKA,CAAC;EACbC;AACF,CAAC,EAAE;EACDZ,yBAAyB,CAAC,MAAM;IAC9BY,GAAG,CAAC,IAAIC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;IAC5B,OAAO,MAAMD,GAAG,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EACT,OAAO,IAAI;AACb;AACA,MAAME,aAAa,SAAS5M,KAAK,CAAC6M,SAAS,CAAC;EAC1CC,WAAWA,CAAC,GAAGnL,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd,IAAI,CAACoL,KAAK,GAAG;MACXC,KAAK,EAAE;IACT,CAAC;EACH;EACAC,iBAAiBA,CAACC,GAAG,EAAE;IACrB,IAAI,CAACrL,KAAK,CAAC6K,GAAG,CAACQ,GAAG,CAAC;EACrB;EACAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACJ,KAAK,CAACC,KAAK,GAAG,IAAI,GAAG,IAAI,CAACnL,KAAK,CAACqC,QAAQ;EACtD;AACF;AACA0I,aAAa,CAACQ,wBAAwB,GAAG,OAAO;EAC9CJ,KAAK,EAAE;AACT,CAAC,CAAC;AACF,MAAMK,OAAO,GAAG,WAAW;AAC3B,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC1B,MAAMC,SAAS,GAAG9B,GAAG,IAAIA,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC+B,QAAQ,IAAI,CAAC,CAAC/B,GAAG,CAAC5C,OAAO;AAC/D,SAAS4E,YAAYA,CAACC,GAAG,EAAE;EACzB,IAAIC,qBAAqB;EACzB;EACA;EACA,MAAMpL,MAAM,GAAG,OAAOuJ,MAAM,KAAK,WAAW,GAAG,CAAC6B,qBAAqB,GAAG7B,MAAM,CAAC8B,gBAAgB,KAAK,IAAI,GAAGD,qBAAqB,GAAG,CAAC,GAAG,CAAC;EACxI,OAAOnL,KAAK,CAACC,OAAO,CAACiL,GAAG,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC,CAAC,EAAEnL,MAAM,CAAC,EAAEmL,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG;AAC9E;;AAEA;AACA;AACA;AACA,MAAMM,YAAY,GAAGrC,GAAG,IAAI;EAC1B,IAAIsC,IAAI;EACR,OAAO,CAACA,IAAI,GAAGtC,GAAG,CAAChJ,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsL,IAAI,CAACpM,IAAI,CAACsE,QAAQ,CAAC,CAAC;AACnE,CAAC;;AAED;AACA;AACA;AACA,SAAShB,eAAeA,CAAClC,KAAK,EAAE;EAC9B,IAAIpB,IAAI,GAAGoB,KAAK,CAACN,KAAK,CAACd,IAAI;EAC3B,OAAOA,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAAC+H,YAAY,EAAErM,IAAI,GAAGA,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAAC+H,YAAY;EACxE,OAAOrM,IAAI;AACb;AACA;AACA,MAAM6I,EAAE,GAAG;EACTiB,GAAG,EAAEwC,CAAC,IAAIA,CAAC,KAAKrN,MAAM,CAACqN,CAAC,CAAC,IAAI,CAACzD,EAAE,CAAC0D,GAAG,CAACD,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU;EAClExD,GAAG,EAAEwD,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU;EACjCE,GAAG,EAAEF,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ;EAC/BG,GAAG,EAAEH,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ;EAC/BI,GAAG,EAAEJ,CAAC,IAAI,OAAOA,CAAC,KAAK,SAAS;EAChCK,GAAG,EAAEL,CAAC,IAAIA,CAAC,KAAK,KAAK,CAAC;EACtBC,GAAG,EAAED,CAAC,IAAI3L,KAAK,CAACC,OAAO,CAAC0L,CAAC,CAAC;EAC1BM,GAAGA,CAACN,CAAC,EAAEO,CAAC,EAAE;IACRC,MAAM,GAAG,SAAS;IAClBxN,OAAO,GAAG,WAAW;IACrByN,MAAM,GAAG;EACX,CAAC,GAAG,CAAC,CAAC,EAAE;IACN;IACA,IAAI,OAAOT,CAAC,KAAK,OAAOO,CAAC,IAAI,CAAC,CAACP,CAAC,KAAK,CAAC,CAACO,CAAC,EAAE,OAAO,KAAK;IACtD;IACA,IAAIhE,EAAE,CAAC2D,GAAG,CAACF,CAAC,CAAC,IAAIzD,EAAE,CAAC4D,GAAG,CAACH,CAAC,CAAC,IAAIzD,EAAE,CAAC6D,GAAG,CAACJ,CAAC,CAAC,EAAE,OAAOA,CAAC,KAAKO,CAAC;IACvD,MAAMG,KAAK,GAAGnE,EAAE,CAACiB,GAAG,CAACwC,CAAC,CAAC;IACvB,IAAIU,KAAK,IAAI1N,OAAO,KAAK,WAAW,EAAE,OAAOgN,CAAC,KAAKO,CAAC;IACpD,MAAMI,KAAK,GAAGpE,EAAE,CAAC0D,GAAG,CAACD,CAAC,CAAC;IACvB,IAAIW,KAAK,IAAIH,MAAM,KAAK,WAAW,EAAE,OAAOR,CAAC,KAAKO,CAAC;IACnD;IACA,IAAI,CAACI,KAAK,IAAID,KAAK,KAAKV,CAAC,KAAKO,CAAC,EAAE,OAAO,IAAI;IAC5C;IACA,IAAIK,CAAC;IACL;IACA,KAAKA,CAAC,IAAIZ,CAAC,EAAE,IAAI,EAAEY,CAAC,IAAIL,CAAC,CAAC,EAAE,OAAO,KAAK;IACxC;IACA,IAAIG,KAAK,IAAIF,MAAM,KAAK,SAAS,IAAIxN,OAAO,KAAK,SAAS,EAAE;MAC1D,KAAK4N,CAAC,IAAIH,MAAM,GAAGF,CAAC,GAAGP,CAAC,EAAE,IAAI,CAACzD,EAAE,CAAC+D,GAAG,CAACN,CAAC,CAACY,CAAC,CAAC,EAAEL,CAAC,CAACK,CAAC,CAAC,EAAE;QAChDH,MAAM;QACNzN,OAAO,EAAE;MACX,CAAC,CAAC,EAAE,OAAO,KAAK;IAClB,CAAC,MAAM;MACL,KAAK4N,CAAC,IAAIH,MAAM,GAAGF,CAAC,GAAGP,CAAC,EAAE,IAAIA,CAAC,CAACY,CAAC,CAAC,KAAKL,CAAC,CAACK,CAAC,CAAC,EAAE,OAAO,KAAK;IAC3D;IACA;IACA,IAAIrE,EAAE,CAAC8D,GAAG,CAACO,CAAC,CAAC,EAAE;MACb;MACA,IAAID,KAAK,IAAIX,CAAC,CAACrF,MAAM,KAAK,CAAC,IAAI4F,CAAC,CAAC5F,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC1D;MACA,IAAI+F,KAAK,IAAI/N,MAAM,CAACkO,IAAI,CAACb,CAAC,CAAC,CAACrF,MAAM,KAAK,CAAC,IAAIhI,MAAM,CAACkO,IAAI,CAACN,CAAC,CAAC,CAAC5F,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACpF;MACA,IAAIqF,CAAC,KAAKO,CAAC,EAAE,OAAO,KAAK;IAC3B;IACA,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,SAASO,UAAUA,CAAC/M,MAAM,EAAE;EAC1B,MAAMgN,IAAI,GAAG;IACXC,KAAK,EAAE,CAAC,CAAC;IACTC,SAAS,EAAE,CAAC;EACd,CAAC;EACD,IAAIlN,MAAM,EAAE;IACVA,MAAM,CAACmN,QAAQ,CAAC1D,GAAG,IAAI;MACrB,IAAIA,GAAG,CAAC7J,IAAI,EAAEoN,IAAI,CAACC,KAAK,CAACxD,GAAG,CAAC7J,IAAI,CAAC,GAAG6J,GAAG;MACxC,IAAIA,GAAG,CAAC2D,QAAQ,IAAI,CAACJ,IAAI,CAACE,SAAS,CAACzD,GAAG,CAAC2D,QAAQ,CAACxN,IAAI,CAAC,EAAEoN,IAAI,CAACE,SAAS,CAACzD,GAAG,CAAC2D,QAAQ,CAACxN,IAAI,CAAC,GAAG6J,GAAG,CAAC2D,QAAQ;IAC1G,CAAC,CAAC;EACJ;EACA,OAAOJ,IAAI;AACb;;AAEA;AACA,SAAS1K,OAAOA,CAACmH,GAAG,EAAE;EACpB,IAAIA,GAAG,CAACnH,OAAO,IAAImH,GAAG,CAAClK,IAAI,KAAK,OAAO,EAAEkK,GAAG,CAACnH,OAAO,CAAC,CAAC;EACtD,KAAK,MAAM+K,CAAC,IAAI5D,GAAG,EAAE;IACnB4D,CAAC,CAAC/K,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG+K,CAAC,CAAC/K,OAAO,CAAC,CAAC;IACxC,OAAOmH,GAAG,CAAC4D,CAAC,CAAC;EACf;AACF;;AAEA;AACA,SAASlN,OAAOA,CAACH,MAAM,EAAE4K,KAAK,EAAE;EAC9B,MAAM7K,QAAQ,GAAGC,MAAM;EACvBD,QAAQ,CAACU,KAAK,GAAG;IACflB,IAAI,EAAE,EAAE;IACRI,IAAI,EAAE,IAAI;IACV2N,cAAc,EAAE,IAAI;IACpB9M,aAAa,EAAE,CAAC,CAAC;IACjBuD,UAAU,EAAE,CAAC;IACb6B,QAAQ,EAAE,CAAC,CAAC;IACZ3G,OAAO,EAAE,EAAE;IACXqC,MAAM,EAAE,IAAI;IACZ,GAAGsJ;EACL,CAAC;EACD,OAAO5K,MAAM;AACf;AACA,SAASuN,OAAOA,CAACxN,QAAQ,EAAEyN,GAAG,EAAE;EAC9B,IAAInN,MAAM,GAAGN,QAAQ;EACrB,IAAIyN,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,OAAO,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;IAC9B,MAAMC,IAAI,GAAGF,OAAO,CAACG,GAAG,CAAC,CAAC;IAC1BxN,MAAM,GAAGqN,OAAO,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEP,GAAG,KAAKO,GAAG,CAACP,GAAG,CAAC,EAAEzN,QAAQ,CAAC;IACzD,OAAO;MACLM,MAAM;MACNmN,GAAG,EAAEI;IACP,CAAC;EACH,CAAC,MAAM,OAAO;IACZvN,MAAM;IACNmN;EACF,CAAC;AACH;;AAEA;AACA,MAAMQ,WAAW,GAAG,OAAO;AAC3B,SAASvO,MAAMA,CAAC6B,MAAM,EAAEP,KAAK,EAAExB,IAAI,EAAE;EACnC,IAAIiJ,EAAE,CAAC2D,GAAG,CAAC5M,IAAI,CAAC,EAAE;IAChB;IACA,IAAIyO,WAAW,CAACC,IAAI,CAAC1O,IAAI,CAAC,EAAE;MAC1B,MAAMI,IAAI,GAAGJ,IAAI,CAAC2O,OAAO,CAACF,WAAW,EAAE,EAAE,CAAC;MAC1C,MAAM;QACJ3N,MAAM;QACNmN;MACF,CAAC,GAAGD,OAAO,CAACjM,MAAM,EAAE3B,IAAI,CAAC;MACzB,IAAI,CAACW,KAAK,CAACC,OAAO,CAACF,MAAM,CAACmN,GAAG,CAAC,CAAC,EAAEnN,MAAM,CAACmN,GAAG,CAAC,GAAG,EAAE;IACnD;IACA,MAAM;MACJnN,MAAM;MACNmN;IACF,CAAC,GAAGD,OAAO,CAACjM,MAAM,EAAE/B,IAAI,CAAC;IACzBwB,KAAK,CAACN,KAAK,CAAC6M,cAAc,GAAGjN,MAAM,CAACmN,GAAG,CAAC;IACxCnN,MAAM,CAACmN,GAAG,CAAC,GAAGzM,KAAK;EACrB,CAAC,MAAMA,KAAK,CAACN,KAAK,CAAC6M,cAAc,GAAG/N,IAAI,CAAC+B,MAAM,EAAEP,KAAK,CAAC;AACzD;AACA,SAAS8B,MAAMA,CAACvB,MAAM,EAAEP,KAAK,EAAExB,IAAI,EAAE;EACnC,IAAI0B,YAAY,EAAEU,aAAa;EAC/B,IAAI6G,EAAE,CAAC2D,GAAG,CAAC5M,IAAI,CAAC,EAAE;IAChB,MAAM;MACJc,MAAM;MACNmN;IACF,CAAC,GAAGD,OAAO,CAACjM,MAAM,EAAE/B,IAAI,CAAC;IACzB,MAAM4O,QAAQ,GAAGpN,KAAK,CAACN,KAAK,CAAC6M,cAAc;IAC3C;IACA,IAAIa,QAAQ,KAAKlO,SAAS,EAAE,OAAOI,MAAM,CAACmN,GAAG,CAAC;IAC9C;IAAA,KACKnN,MAAM,CAACmN,GAAG,CAAC,GAAGW,QAAQ;EAC7B,CAAC,MAAM,CAAClN,YAAY,GAAGF,KAAK,CAACN,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGQ,YAAY,CAACqM,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrM,YAAY,CAACqM,cAAc,CAAChM,MAAM,EAAEP,KAAK,CAAC;EAChJ,CAACY,aAAa,GAAGZ,KAAK,CAACN,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG,OAAOkB,aAAa,CAAC2L,cAAc;AACpF;AACA;AACA,SAAS5G,SAASA,CAAC3G,QAAQ,EAAE;EAC3BgC,QAAQ,EAAEmE,EAAE;EACZsH,GAAG,EAAEY,EAAE;EACP9J,GAAG,EAAE+J,EAAE;EACP,GAAG3O;AACL,CAAC,EAAE;EACDqC,QAAQ,EAAEuM,EAAE;EACZd,GAAG,EAAEe,EAAE;EACPjK,GAAG,EAAEkK,EAAE;EACP,GAAGL;AACL,CAAC,GAAG,CAAC,CAAC,EAAEpL,MAAM,GAAG,KAAK,EAAE;EACtB,MAAM2C,UAAU,GAAG3F,QAAQ,CAACU,KAAK;EACjC,MAAMiN,OAAO,GAAG9O,MAAM,CAAC8O,OAAO,CAAChO,KAAK,CAAC;EACrC,MAAMiH,OAAO,GAAG,EAAE;;EAElB;EACA,IAAI5D,MAAM,EAAE;IACV,MAAM0L,YAAY,GAAG7P,MAAM,CAACkO,IAAI,CAACqB,QAAQ,CAAC;IAC1C,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,YAAY,CAAC7H,MAAM,EAAEiG,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACnN,KAAK,CAACgK,cAAc,CAAC+E,YAAY,CAAC5B,CAAC,CAAC,CAAC,EAAEa,OAAO,CAACgB,OAAO,CAAC,CAACD,YAAY,CAAC5B,CAAC,CAAC,EAAE3B,OAAO,GAAG,QAAQ,CAAC,CAAC;IACpG;EACF;EACAwC,OAAO,CAACnL,OAAO,CAAC,CAAC,CAACiL,GAAG,EAAEhH,KAAK,CAAC,KAAK;IAChC,IAAI7C,eAAe;IACnB;IACA,IAAI,CAACA,eAAe,GAAG5D,QAAQ,CAACU,KAAK,KAAK,IAAI,IAAIkD,eAAe,CAACvD,SAAS,IAAIoN,GAAG,KAAK,QAAQ,EAAE;IACjG;IACA,IAAIhF,EAAE,CAAC+D,GAAG,CAAC/F,KAAK,EAAE2H,QAAQ,CAACX,GAAG,CAAC,CAAC,EAAE;IAClC;IACA,IAAI,kDAAkD,CAACS,IAAI,CAACT,GAAG,CAAC,EAAE,OAAO7G,OAAO,CAACtF,IAAI,CAAC,CAACmM,GAAG,EAAEhH,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC7G;IACA,IAAIkH,OAAO,GAAG,EAAE;IAChB,IAAIF,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAEC,OAAO,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;IAC/ChH,OAAO,CAACtF,IAAI,CAAC,CAACmM,GAAG,EAAEhH,KAAK,EAAE,KAAK,EAAEkH,OAAO,CAAC,CAAC;;IAE1C;IACA,KAAK,MAAMiB,IAAI,IAAIjP,KAAK,EAAE;MACxB,MAAM8G,KAAK,GAAG9G,KAAK,CAACiP,IAAI,CAAC;MACzB,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAGpB,GAAG,GAAG,CAAC,EAAE7G,OAAO,CAACtF,IAAI,CAAC,CAACsN,IAAI,EAAEnI,KAAK,EAAE,KAAK,EAAEmI,IAAI,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACrF;EACF,CAAC,CAAC;EACF,MAAMrC,QAAQ,GAAG;IACf,GAAG5L;EACL,CAAC;EACD,IAAIgG,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAClF,aAAa,IAAIkF,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAClF,aAAa,CAAChB,IAAI,EAAE8L,QAAQ,CAAC9L,IAAI,GAAGkG,UAAU,CAAClF,aAAa,CAAChB,IAAI;EACxJ,IAAIkG,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAClF,aAAa,IAAIkF,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAClF,aAAa,CAACf,MAAM,EAAE6L,QAAQ,CAAC7L,MAAM,GAAGiG,UAAU,CAAClF,aAAa,CAACf,MAAM;EAC9J,OAAO;IACL6L,QAAQ;IACR3E;EACF,CAAC;AACH;AACA,MAAMkI,OAAO,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;;AAEvF;AACA,SAASpO,YAAYA,CAACb,QAAQ,EAAEiN,IAAI,EAAE;EACpC,IAAIvH,gBAAgB;EACpB;EACA,MAAMC,UAAU,GAAG3F,QAAQ,CAACU,KAAK;EACjC,MAAMd,IAAI,GAAG+F,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC/F,IAAI;EAC1D,MAAMqE,SAAS,GAAGrE,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACsE,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGtE,IAAI,CAACsE,QAAQ,CAAC,CAAC;EAC1F,MAAM;IACJqH,QAAQ;IACR3E;EACF,CAAC,GAAG0E,SAAS,CAAC2B,IAAI,CAAC,GAAGA,IAAI,GAAGtG,SAAS,CAAC3G,QAAQ,EAAEiN,IAAI,CAAC;EACtD,MAAMiC,YAAY,GAAGvJ,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC3B,UAAU;;EAExE;EACA,IAAIhE,QAAQ,CAACU,KAAK,EAAEV,QAAQ,CAACU,KAAK,CAACD,aAAa,GAAG8K,QAAQ;EAC3D,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlG,OAAO,CAACC,MAAM,EAAEiG,CAAC,EAAE,EAAE;IACvC,IAAI,CAACW,GAAG,EAAEhH,KAAK,EAAE0I,OAAO,EAAEpC,IAAI,CAAC,GAAGnG,OAAO,CAACkG,CAAC,CAAC;;IAE5C;IACA;IACA,IAAI3D,aAAa,CAACnJ,QAAQ,CAAC,EAAE;MAC3B,MAAMoP,YAAY,GAAG,IAAI;MACzB,MAAMC,cAAc,GAAG,MAAM;MAC7B,MAAMC,oBAAoB,GAAG,aAAa;MAC1C,IAAI7B,GAAG,KAAK,UAAU,EAAE;QACtBA,GAAG,GAAG,YAAY;QAClBhH,KAAK,GAAGA,KAAK,KAAK2I,YAAY,GAAGC,cAAc,GAAGC,oBAAoB;MACxE,CAAC,MAAM,IAAI7B,GAAG,KAAK,gBAAgB,EAAE;QACnCA,GAAG,GAAG,kBAAkB;QACxBhH,KAAK,GAAGA,KAAK,KAAK2I,YAAY,GAAGC,cAAc,GAAGC,oBAAoB;MACxE;IACF;IACA,IAAIC,eAAe,GAAGvP,QAAQ;IAC9B,IAAIwP,UAAU,GAAGD,eAAe,CAAC9B,GAAG,CAAC;;IAErC;IACA,IAAIV,IAAI,CAAClG,MAAM,EAAE;MACf2I,UAAU,GAAGzC,IAAI,CAACgB,MAAM,CAAC,CAACC,GAAG,EAAEP,GAAG,KAAKO,GAAG,CAACP,GAAG,CAAC,EAAEzN,QAAQ,CAAC;MAC1D;MACA,IAAI,EAAEwP,UAAU,IAAIA,UAAU,CAAChF,GAAG,CAAC,EAAE;QACnC,MAAM,CAAC3K,IAAI,EAAE,GAAG4P,cAAc,CAAC,GAAG1C,IAAI,CAAC2C,OAAO,CAAC,CAAC;QAChDH,eAAe,GAAGE,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC3B,MAAM,CAAC,CAACC,GAAG,EAAEP,GAAG,KAAKO,GAAG,CAACP,GAAG,CAAC,EAAEzN,QAAQ,CAAC;QACnFyN,GAAG,GAAG5N,IAAI;MACZ;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAI4G,KAAK,KAAK0E,OAAO,GAAG,QAAQ,EAAE;MAChC,IAAIoE,eAAe,CAAC3E,WAAW,EAAE;QAC/B;QACA,IAAI+E,IAAI,GAAGvE,QAAQ,CAACwE,GAAG,CAACL,eAAe,CAAC3E,WAAW,CAAC;QACpD,IAAI,CAAC+E,IAAI,EAAE;UACT;UACAA,IAAI,GAAG,IAAIJ,eAAe,CAAC3E,WAAW,CAAC,CAAC;UACxCQ,QAAQ,CAACZ,GAAG,CAAC+E,eAAe,CAAC3E,WAAW,EAAE+E,IAAI,CAAC;QACjD;QACAlJ,KAAK,GAAGkJ,IAAI,CAAClC,GAAG,CAAC;MACnB,CAAC,MAAM;QACL;QACAhH,KAAK,GAAG,CAAC;MACX;IACF;;IAEA;IACA,IAAI0I,OAAO,IAAIxJ,UAAU,EAAE;MACzB,IAAIc,KAAK,EAAEd,UAAU,CAACE,QAAQ,CAAC4H,GAAG,CAAC,GAAGhH,KAAK,CAAC,KAAK,OAAOd,UAAU,CAACE,QAAQ,CAAC4H,GAAG,CAAC;MAChF9H,UAAU,CAAC3B,UAAU,GAAGnF,MAAM,CAACkO,IAAI,CAACpH,UAAU,CAACE,QAAQ,CAAC,CAACgB,MAAM;IACjE;IACA;IAAA,KACK,IAAI2I,UAAU,IAAIA,UAAU,CAAChF,GAAG,KAAKgF,UAAU,CAACK,IAAI,IAAIL,UAAU,YAAY3R,KAAK,CAACiS,MAAM,CAAC,EAAE;MAChG;MACA,IAAIvP,KAAK,CAACC,OAAO,CAACiG,KAAK,CAAC,EAAE;QACxB,IAAI+I,UAAU,CAACO,SAAS,EAAEP,UAAU,CAACO,SAAS,CAACtJ,KAAK,CAAC,CAAC,KAAK+I,UAAU,CAAChF,GAAG,CAAC,GAAG/D,KAAK,CAAC;MACrF;MACA;MAAA,KACK,IAAI+I,UAAU,CAACK,IAAI,IAAIpJ,KAAK,IAAIA,KAAK,CAACmE,WAAW;MACtD;MACA;MACA;MACA;MACAkE,OAAO,GAAGU,UAAU,CAAC5E,WAAW,CAAC/K,IAAI,KAAK4G,KAAK,CAACmE,WAAW,CAAC/K,IAAI,GAAG2P,UAAU,CAAC5E,WAAW,KAAKnE,KAAK,CAACmE,WAAW,CAAC,EAAE;QAChH4E,UAAU,CAACK,IAAI,CAACpJ,KAAK,CAAC;MACxB;MACA;MACA;MAAA,KACK,IAAIA,KAAK,KAAKvG,SAAS,EAAE;QAC5B,IAAI8P,WAAW;QACf,MAAMC,OAAO,GAAG,CAACD,WAAW,GAAGR,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGQ,WAAW,CAACC,OAAO;QACjF;QACA,IAAI,CAACA,OAAO,IAAIT,UAAU,CAACU,SAAS,EAAEV,UAAU,CAACU,SAAS,CAACzJ,KAAK,CAAC;QACjE;QAAA,KACK,IAAI+I,UAAU,YAAY3R,KAAK,CAACiS,MAAM,IAAIrJ,KAAK,YAAY5I,KAAK,CAACiS,MAAM,EAAEN,UAAU,CAACW,IAAI,GAAG1J,KAAK,CAAC0J,IAAI;QAC1G;QAAA,KACKX,UAAU,CAAChF,GAAG,CAAC/D,KAAK,CAAC;QAC1B;QACA;QACA;QACA,IAAI,CAAC2C,kBAAkB,CAAC,CAAC,IAAInF,SAAS,IAAI,CAACA,SAAS,CAACmM,MAAM,IAAIH,OAAO,EAAET,UAAU,CAACa,mBAAmB,CAAC,CAAC;MAC1G;MACA;IACF,CAAC,MAAM;MACL,IAAIC,oBAAoB;MACxBf,eAAe,CAAC9B,GAAG,CAAC,GAAGhH,KAAK;;MAE5B;MACA;MACA,IAAI,CAAC6J,oBAAoB,GAAGf,eAAe,CAAC9B,GAAG,CAAC,KAAK,IAAI,IAAI6C,oBAAoB,CAACC,SAAS;MAC3F;MACAhB,eAAe,CAAC9B,GAAG,CAAC,CAAC+C,MAAM,KAAK3S,KAAK,CAAC4S,UAAU,IAAIlB,eAAe,CAAC9B,GAAG,CAAC,CAACjO,IAAI,KAAK3B,KAAK,CAAC6S,gBAAgB,IAAIzM,SAAS,EAAE;QACrH,MAAM0M,OAAO,GAAGpB,eAAe,CAAC9B,GAAG,CAAC;QACpC,IAAItE,aAAa,CAACwH,OAAO,CAAC,IAAIxH,aAAa,CAAClF,SAAS,CAAC2M,EAAE,CAAC,EAAED,OAAO,CAACE,UAAU,GAAG5M,SAAS,CAAC2M,EAAE,CAACE,gBAAgB,CAAC,KAAKH,OAAO,CAACI,QAAQ,GAAG9M,SAAS,CAAC2M,EAAE,CAACI,cAAc;MACnK;IACF;IACAvP,kBAAkB,CAACzB,QAAQ,CAAC;EAC9B;EACA,IAAI2F,UAAU,IAAIA,UAAU,CAACpE,MAAM,IAAIvB,QAAQ,CAAC+D,OAAO,IAAImL,YAAY,KAAKvJ,UAAU,CAAC3B,UAAU,EAAE;IACjG;IACA,MAAMG,QAAQ,GAAGjB,eAAe,CAAClD,QAAQ,CAAC,CAACkE,QAAQ,CAAC,CAAC,CAACC,QAAQ;IAC9D;IACA,MAAMhC,KAAK,GAAGgC,QAAQ,CAACC,WAAW,CAAChC,OAAO,CAACpC,QAAQ,CAAC;IACpD,IAAImC,KAAK,GAAG,CAAC,CAAC,EAAEgC,QAAQ,CAACC,WAAW,CAAC6M,MAAM,CAAC9O,KAAK,EAAE,CAAC,CAAC;IACrD;IACA,IAAIwD,UAAU,CAAC3B,UAAU,EAAEG,QAAQ,CAACC,WAAW,CAAC9C,IAAI,CAACtB,QAAQ,CAAC;EAChE;;EAEA;EACA;EACA,MAAMkR,UAAU,GAAGtK,OAAO,CAACC,MAAM,KAAK,CAAC,IAAID,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,UAAU;EACvE,IAAI,CAACsK,UAAU,IAAItK,OAAO,CAACC,MAAM,IAAI,CAACnB,gBAAgB,GAAG1F,QAAQ,CAACU,KAAK,KAAK,IAAI,IAAIgF,gBAAgB,CAACnE,MAAM,EAAEC,cAAc,CAACxB,QAAQ,CAAC;EACrI,OAAOA,QAAQ;AACjB;AACA,SAASyB,kBAAkBA,CAACzB,QAAQ,EAAE;EACpC,IAAIiG,gBAAgB,EAAEkL,qBAAqB;EAC3C,MAAMtG,KAAK,GAAG,CAAC5E,gBAAgB,GAAGjG,QAAQ,CAACU,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACyQ,qBAAqB,GAAGlL,gBAAgB,CAACrG,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuR,qBAAqB,CAACjN,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGiN,qBAAqB,CAACjN,QAAQ,CAAC,CAAC;EAC1N,IAAI2G,KAAK,IAAIA,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,KAAK,CAAC,EAAEvG,KAAK,CAACwG,UAAU,CAAC,CAAC;AAC9D;AACA,SAAS7P,cAAcA,CAACxB,QAAQ,EAAE;EAChCA,QAAQ,CAACsR,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGtR,QAAQ,CAACsR,QAAQ,CAACtR,QAAQ,CAAC;AAClE;AACA,SAASuR,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAClC;EACA;EACA,IAAI,CAACD,MAAM,CAACE,MAAM,EAAE;IAClB,IAAInI,oBAAoB,CAACiI,MAAM,CAAC,EAAE;MAChCA,MAAM,CAACG,IAAI,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;MAC7BJ,MAAM,CAACK,KAAK,GAAGJ,IAAI,CAACG,KAAK,GAAG,CAAC;MAC7BJ,MAAM,CAACM,GAAG,GAAGL,IAAI,CAACM,MAAM,GAAG,CAAC;MAC5BP,MAAM,CAACQ,MAAM,GAAGP,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;IAClC,CAAC,MAAM;MACLP,MAAM,CAACS,MAAM,GAAGR,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACM,MAAM;IAC1C;IACAP,MAAM,CAACU,sBAAsB,CAAC,CAAC;IAC/B;IACA;IACAV,MAAM,CAACW,iBAAiB,CAAC,CAAC;EAC5B;AACF;AAEA,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAO,CAACA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACpS,MAAM,EAAEsS,IAAI,GAAG,GAAG,GAAGF,KAAK,CAAClQ,KAAK,GAAGkQ,KAAK,CAACG,UAAU;AACxF;;AAEA;AACA;AACA,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,IAAIC,kBAAkB;EACtB;EACA;EACA,MAAMC,WAAW,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,IAAI,OAAO/I,MAAM,KAAK,WAAW,IAAIA,MAAM;EAClG,IAAI,CAAC8I,WAAW,EAAE,OAAO5U,oBAAoB;EAC7C,MAAM8B,IAAI,GAAG,CAAC6S,kBAAkB,GAAGC,WAAW,CAACN,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,kBAAkB,CAAClT,IAAI;EAChG,QAAQK,IAAI;IACV,KAAK,OAAO;IACZ,KAAK,aAAa;IAClB,KAAK,UAAU;IACf,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,WAAW;MACd,OAAO5B,qBAAqB;IAC9B,KAAK,aAAa;IAClB,KAAK,YAAY;IACjB,KAAK,aAAa;IAClB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,OAAO;MACV,OAAOD,uBAAuB;IAChC;MACE,OAAOD,oBAAoB;EAC/B;AACF;;AAEA;AACA;AACA;AACA;AACA,SAAS8U,6BAA6BA,CAACC,WAAW,EAAEpJ,GAAG,EAAEqJ,QAAQ,EAAEC,SAAS,EAAE;EAC5E,MAAMC,WAAW,GAAGF,QAAQ,CAACnD,GAAG,CAAClG,GAAG,CAAC;EACrC,IAAIuJ,WAAW,EAAE;IACfF,QAAQ,CAACG,MAAM,CAACxJ,GAAG,CAAC;IACpB;IACA,IAAIqJ,QAAQ,CAACtB,IAAI,KAAK,CAAC,EAAE;MACvBqB,WAAW,CAACI,MAAM,CAACF,SAAS,CAAC;MAC7BC,WAAW,CAAC3S,MAAM,CAAC6S,qBAAqB,CAACH,SAAS,CAAC;IACrD;EACF;AACF;AACA,SAAS/P,mBAAmBA,CAACmQ,KAAK,EAAEnT,MAAM,EAAE;EAC1C,MAAM;IACJkE;EACF,CAAC,GAAGiP,KAAK,CAAClP,QAAQ,CAAC,CAAC;EACpB;EACAC,QAAQ,CAACC,WAAW,GAAGD,QAAQ,CAACC,WAAW,CAACnC,MAAM,CAACoR,CAAC,IAAIA,CAAC,KAAKpT,MAAM,CAAC;EACrEkE,QAAQ,CAACmP,WAAW,GAAGnP,QAAQ,CAACmP,WAAW,CAACrR,MAAM,CAACoR,CAAC,IAAIA,CAAC,KAAKpT,MAAM,CAAC;EACrEkE,QAAQ,CAACoP,OAAO,CAAC/Q,OAAO,CAAC,CAACiE,KAAK,EAAEgH,GAAG,KAAK;IACvC,IAAIhH,KAAK,CAAC6L,WAAW,KAAKrS,MAAM,IAAIwG,KAAK,CAACxG,MAAM,KAAKA,MAAM,EAAE;MAC3D;MACAkE,QAAQ,CAACoP,OAAO,CAACL,MAAM,CAACzF,GAAG,CAAC;IAC9B;EACF,CAAC,CAAC;EACFtJ,QAAQ,CAAC2O,WAAW,CAACtQ,OAAO,CAAC,CAACuQ,QAAQ,EAAEC,SAAS,KAAK;IACpDH,6BAA6B,CAAC1O,QAAQ,CAAC2O,WAAW,EAAE7S,MAAM,EAAE8S,QAAQ,EAAEC,SAAS,CAAC;EAClF,CAAC,CAAC;AACJ;AACA,SAASQ,YAAYA,CAACJ,KAAK,EAAE;EAC3B;EACA,SAASK,iBAAiBA,CAACpB,KAAK,EAAE;IAChC,MAAM;MACJlO;IACF,CAAC,GAAGiP,KAAK,CAAClP,QAAQ,CAAC,CAAC;IACpB,MAAMwP,EAAE,GAAGrB,KAAK,CAACsB,OAAO,GAAGxP,QAAQ,CAACyP,YAAY,CAAC,CAAC,CAAC;IACnD,MAAMC,EAAE,GAAGxB,KAAK,CAACyB,OAAO,GAAG3P,QAAQ,CAACyP,YAAY,CAAC,CAAC,CAAC;IACnD,OAAOhI,IAAI,CAACmI,KAAK,CAACnI,IAAI,CAACoI,IAAI,CAACN,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE,CAAC,CAAC;EACjD;;EAEA;EACA,SAASI,mBAAmBA,CAAC/U,OAAO,EAAE;IACpC,OAAOA,OAAO,CAAC+C,MAAM,CAACyH,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAClD,IAAI,CAAC3G,IAAI,IAAI;MAClF,IAAImM,IAAI;MACR,OAAO,CAACA,IAAI,GAAGtC,GAAG,CAAChJ,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsL,IAAI,CAACnG,QAAQ,CAAC,WAAW,GAAGhG,IAAI,CAAC;IAChF,CAAC,CAAC,CAAC;EACL;EACA,SAASqU,SAASA,CAAC7B,KAAK,EAAEpQ,MAAM,EAAE;IAChC,MAAM4I,KAAK,GAAGuI,KAAK,CAAClP,QAAQ,CAAC,CAAC;IAC9B,MAAMiQ,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,MAAMC,aAAa,GAAG,EAAE;IACxB;IACA,MAAMC,aAAa,GAAGrS,MAAM,GAAGA,MAAM,CAAC4I,KAAK,CAAC1G,QAAQ,CAACC,WAAW,CAAC,GAAGyG,KAAK,CAAC1G,QAAQ,CAACC,WAAW;IAC9F;IACA,KAAK,IAAI0I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,aAAa,CAACzN,MAAM,EAAEiG,CAAC,EAAE,EAAE;MAC7C,MAAMjC,KAAK,GAAGkB,YAAY,CAACuI,aAAa,CAACxH,CAAC,CAAC,CAAC;MAC5C,IAAIjC,KAAK,EAAE;QACTA,KAAK,CAAC0J,SAAS,CAAC/C,MAAM,GAAGtR,SAAS;MACpC;IACF;IACA,IAAI,CAAC2K,KAAK,CAACoB,YAAY,EAAE;MACvB;MACApB,KAAK,CAAC2J,MAAM,CAACC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG5J,KAAK,CAAC2J,MAAM,CAACC,OAAO,CAACpC,KAAK,EAAExH,KAAK,CAAC;IAC5E;IACA,SAAS6J,aAAaA,CAAChL,GAAG,EAAE;MAC1B,MAAMmB,KAAK,GAAGkB,YAAY,CAACrC,GAAG,CAAC;MAC/B;MACA,IAAI,CAACmB,KAAK,IAAI,CAACA,KAAK,CAAC2J,MAAM,CAACG,OAAO,IAAI9J,KAAK,CAAC0J,SAAS,CAAC/C,MAAM,KAAK,IAAI,EAAE,OAAO,EAAE;;MAEjF;MACA,IAAI3G,KAAK,CAAC0J,SAAS,CAAC/C,MAAM,KAAKtR,SAAS,EAAE;QACxC,IAAI0U,mBAAmB;QACvB/J,KAAK,CAAC2J,MAAM,CAACC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG5J,KAAK,CAAC2J,MAAM,CAACC,OAAO,CAACpC,KAAK,EAAExH,KAAK,EAAE,CAAC+J,mBAAmB,GAAG/J,KAAK,CAACoB,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2I,mBAAmB,CAAC1Q,QAAQ,CAAC,CAAC,CAAC;QACxK;QACA,IAAI2G,KAAK,CAAC0J,SAAS,CAAC/C,MAAM,KAAKtR,SAAS,EAAE2K,KAAK,CAAC0J,SAAS,CAAC/C,MAAM,GAAG,IAAI;MACzE;;MAEA;MACA,OAAO3G,KAAK,CAAC0J,SAAS,CAAC/C,MAAM,GAAG3G,KAAK,CAAC0J,SAAS,CAACM,eAAe,CAACnL,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE;IACjF;;IAEA;IACA,IAAIoL,IAAI,GAAGR;IACX;IAAA,CACCS,OAAO,CAACL,aAAa;IACtB;IAAA,CACCM,IAAI,CAAC,CAAC9I,CAAC,EAAEO,CAAC,KAAK;MACd,MAAMwI,MAAM,GAAGlJ,YAAY,CAACG,CAAC,CAACjM,MAAM,CAAC;MACrC,MAAMiV,MAAM,GAAGnJ,YAAY,CAACU,CAAC,CAACxM,MAAM,CAAC;MACrC,IAAI,CAACgV,MAAM,IAAI,CAACC,MAAM,EAAE,OAAOhJ,CAAC,CAACiJ,QAAQ,GAAG1I,CAAC,CAAC0I,QAAQ;MACtD,OAAOD,MAAM,CAACV,MAAM,CAACY,QAAQ,GAAGH,MAAM,CAACT,MAAM,CAACY,QAAQ,IAAIlJ,CAAC,CAACiJ,QAAQ,GAAG1I,CAAC,CAAC0I,QAAQ;IACnF,CAAC;IACD;IAAA,CACClT,MAAM,CAACoT,IAAI,IAAI;MACd,MAAMC,EAAE,GAAGlD,MAAM,CAACiD,IAAI,CAAC;MACvB,IAAIlB,UAAU,CAACoB,GAAG,CAACD,EAAE,CAAC,EAAE,OAAO,KAAK;MACpCnB,UAAU,CAAC9S,GAAG,CAACiU,EAAE,CAAC;MAClB,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA;IACA,IAAIzK,KAAK,CAAC2J,MAAM,CAACvS,MAAM,EAAE6S,IAAI,GAAGjK,KAAK,CAAC2J,MAAM,CAACvS,MAAM,CAAC6S,IAAI,EAAEjK,KAAK,CAAC;;IAEhE;IACA,KAAK,MAAM2K,GAAG,IAAIV,IAAI,EAAE;MACtB,IAAIxC,WAAW,GAAGkD,GAAG,CAACvV,MAAM;MAC5B;MACA,OAAOqS,WAAW,EAAE;QAClB,IAAImD,KAAK;QACT,IAAI,CAACA,KAAK,GAAGnD,WAAW,CAAC5R,KAAK,KAAK,IAAI,IAAI+U,KAAK,CAACzR,UAAU,EAAEqQ,aAAa,CAAC/S,IAAI,CAAC;UAC9E,GAAGkU,GAAG;UACNlD;QACF,CAAC,CAAC;QACFA,WAAW,GAAGA,WAAW,CAAC/Q,MAAM;MAClC;IACF;;IAEA;IACA,IAAI,WAAW,IAAI8Q,KAAK,IAAIxH,KAAK,CAAC1G,QAAQ,CAAC2O,WAAW,CAACyC,GAAG,CAAClD,KAAK,CAACW,SAAS,CAAC,EAAE;MAC3E,KAAK,IAAIC,WAAW,IAAIpI,KAAK,CAAC1G,QAAQ,CAAC2O,WAAW,CAAClD,GAAG,CAACyC,KAAK,CAACW,SAAS,CAAC,CAAC0C,MAAM,CAAC,CAAC,EAAE;QAChF,IAAI,CAACvB,UAAU,CAACoB,GAAG,CAACnD,MAAM,CAACa,WAAW,CAAC0C,YAAY,CAAC,CAAC,EAAEtB,aAAa,CAAC/S,IAAI,CAAC2R,WAAW,CAAC0C,YAAY,CAAC;MACrG;IACF;IACA,OAAOtB,aAAa;EACtB;;EAEA;EACA,SAASuB,gBAAgBA,CAACvB,aAAa,EAAEhC,KAAK,EAAEwD,KAAK,EAAEvS,QAAQ,EAAE;IAC/D,MAAMW,SAAS,GAAGmP,KAAK,CAAClP,QAAQ,CAAC,CAAC;;IAElC;IACA,IAAImQ,aAAa,CAACxN,MAAM,EAAE;MACxB,MAAMlB,UAAU,GAAG;QACjBmQ,OAAO,EAAE;MACX,CAAC;MACD,KAAK,MAAMN,GAAG,IAAInB,aAAa,EAAE;QAC/B,MAAMxJ,KAAK,GAAGkB,YAAY,CAACyJ,GAAG,CAACvV,MAAM,CAAC,IAAIgE,SAAS;QACnD,MAAM;UACJsQ,SAAS;UACTwB,OAAO;UACPvE,MAAM;UACNrN;QACF,CAAC,GAAG0G,KAAK;QACT,MAAMmL,gBAAgB,GAAG,IAAInY,KAAK,CAACoY,OAAO,CAACF,OAAO,CAAClT,CAAC,EAAEkT,OAAO,CAACG,CAAC,EAAE,CAAC,CAAC,CAACC,SAAS,CAAC3E,MAAM,CAAC;QACrF,MAAM4E,iBAAiB,GAAGd,EAAE,IAAI;UAC9B,IAAIe,qBAAqB,EAAEC,sBAAsB;UACjD,OAAO,CAACD,qBAAqB,GAAG,CAACC,sBAAsB,GAAGnS,QAAQ,CAAC2O,WAAW,CAAClD,GAAG,CAAC0F,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,sBAAsB,CAACf,GAAG,CAACC,GAAG,CAAClD,WAAW,CAAC,KAAK,IAAI,GAAG+D,qBAAqB,GAAG,KAAK;QACjM,CAAC;QACD,MAAME,iBAAiB,GAAGjB,EAAE,IAAI;UAC9B,MAAMrC,WAAW,GAAG;YAClB0C,YAAY,EAAEH,GAAG;YACjBlV,MAAM,EAAE+R,KAAK,CAAC/R;UAChB,CAAC;UACD,IAAI6D,QAAQ,CAAC2O,WAAW,CAACyC,GAAG,CAACD,EAAE,CAAC,EAAE;YAChC;YACA;YACAnR,QAAQ,CAAC2O,WAAW,CAAClD,GAAG,CAAC0F,EAAE,CAAC,CAAC9K,GAAG,CAACgL,GAAG,CAAClD,WAAW,EAAEW,WAAW,CAAC;UAChE,CAAC,MAAM;YACL;YACA;YACA;YACA9O,QAAQ,CAAC2O,WAAW,CAACtI,GAAG,CAAC8K,EAAE,EAAE,IAAIjK,GAAG,CAAC,CAAC,CAACmK,GAAG,CAAClD,WAAW,EAAEW,WAAW,CAAC,CAAC,CAAC,CAAC;UACzE;UACAZ,KAAK,CAAC/R,MAAM,CAACiW,iBAAiB,CAACjB,EAAE,CAAC;QACpC,CAAC;QACD,MAAMnC,qBAAqB,GAAGmC,EAAE,IAAI;UAClC,MAAMvC,QAAQ,GAAG5O,QAAQ,CAAC2O,WAAW,CAAClD,GAAG,CAAC0F,EAAE,CAAC;UAC7C,IAAIvC,QAAQ,EAAE;YACZF,6BAA6B,CAAC1O,QAAQ,CAAC2O,WAAW,EAAE0C,GAAG,CAAClD,WAAW,EAAES,QAAQ,EAAEuC,EAAE,CAAC;UACpF;QACF,CAAC;;QAED;QACA,IAAIkB,iBAAiB,GAAG,CAAC,CAAC;QAC1B;QACA,KAAK,IAAI5H,IAAI,IAAIyD,KAAK,EAAE;UACtB,IAAIoE,QAAQ,GAAGpE,KAAK,CAACzD,IAAI,CAAC;UAC1B;UACA;UACA,IAAI,OAAO6H,QAAQ,KAAK,UAAU,EAAED,iBAAiB,CAAC5H,IAAI,CAAC,GAAG6H,QAAQ;QACxE;QACA,IAAIC,YAAY,GAAG;UACjB,GAAGlB,GAAG;UACN,GAAGgB,iBAAiB;UACpBT,OAAO;UACP1B,aAAa;UACbyB,OAAO,EAAEnQ,UAAU,CAACmQ,OAAO;UAC3BD,KAAK;UACLG,gBAAgB;UAChBW,GAAG,EAAEpC,SAAS,CAACoC,GAAG;UAClBnF,MAAM,EAAEA,MAAM;UACd;UACAoF,eAAeA,CAAA,EAAG;YAChB;YACA;YACA,MAAMC,kBAAkB,GAAG,WAAW,IAAIxE,KAAK,IAAIlO,QAAQ,CAAC2O,WAAW,CAAClD,GAAG,CAACyC,KAAK,CAACW,SAAS,CAAC;;YAE5F;YACA;YACA;YACA,CAAC6D,kBAAkB;YACnB;YACAA,kBAAkB,CAACtB,GAAG,CAACC,GAAG,CAAClD,WAAW,CAAC,EAAE;cACvCoE,YAAY,CAACZ,OAAO,GAAGnQ,UAAU,CAACmQ,OAAO,GAAG,IAAI;cAChD;cACA;cACA,IAAI3R,QAAQ,CAACoP,OAAO,CAAC9B,IAAI,IAAIlR,KAAK,CAACuW,IAAI,CAAC3S,QAAQ,CAACoP,OAAO,CAACmC,MAAM,CAAC,CAAC,CAAC,CAACqB,IAAI,CAACjK,CAAC,IAAIA,CAAC,CAACwF,WAAW,KAAKkD,GAAG,CAAClD,WAAW,CAAC,EAAE;gBAC/G;gBACA,MAAM0E,MAAM,GAAG3C,aAAa,CAACtU,KAAK,CAAC,CAAC,EAAEsU,aAAa,CAACjS,OAAO,CAACoT,GAAG,CAAC,CAAC;gBACjEyB,aAAa,CAAC,CAAC,GAAGD,MAAM,EAAExB,GAAG,CAAC,CAAC;cACjC;YACF;UACF,CAAC;UACD;UACAlV,MAAM,EAAE;YACN8V,iBAAiB;YACjBG,iBAAiB;YACjBpD;UACF,CAAC;UACD+D,aAAa,EAAE;YACbd,iBAAiB;YACjBG,iBAAiB;YACjBpD;UACF,CAAC;UACDgE,WAAW,EAAE9E;QACf,CAAC;;QAED;QACA/O,QAAQ,CAACoT,YAAY,CAAC;QACtB;QACA,IAAI/Q,UAAU,CAACmQ,OAAO,KAAK,IAAI,EAAE;MACnC;IACF;IACA,OAAOzB,aAAa;EACtB;EACA,SAAS4C,aAAaA,CAAC5C,aAAa,EAAE;IACpC,MAAM;MACJlQ;IACF,CAAC,GAAGiP,KAAK,CAAClP,QAAQ,CAAC,CAAC;IACpB,KAAK,MAAMkT,UAAU,IAAIjT,QAAQ,CAACoP,OAAO,CAACmC,MAAM,CAAC,CAAC,EAAE;MAClD;MACA;MACA,IAAI,CAACrB,aAAa,CAACxN,MAAM,IAAI,CAACwN,aAAa,CAAC0C,IAAI,CAACvB,GAAG,IAAIA,GAAG,CAACvV,MAAM,KAAKmX,UAAU,CAACnX,MAAM,IAAIuV,GAAG,CAACrT,KAAK,KAAKiV,UAAU,CAACjV,KAAK,IAAIqT,GAAG,CAAChD,UAAU,KAAK4E,UAAU,CAAC5E,UAAU,CAAC,EAAE;QACvK,MAAMF,WAAW,GAAG8E,UAAU,CAAC9E,WAAW;QAC1C,MAAMtS,QAAQ,GAAGsS,WAAW,CAAC5R,KAAK;QAClC,MAAMmF,QAAQ,GAAG7F,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC6F,QAAQ;QAC9D1B,QAAQ,CAACoP,OAAO,CAACL,MAAM,CAACd,MAAM,CAACgF,UAAU,CAAC,CAAC;QAC3C,IAAIpX,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACgE,UAAU,EAAE;UAC3C;UACA,MAAMiJ,IAAI,GAAG;YACX,GAAGmK,UAAU;YACb/C;UACF,CAAC;UACDxO,QAAQ,CAACwR,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGxR,QAAQ,CAACwR,YAAY,CAACpK,IAAI,CAAC;UACpEpH,QAAQ,CAACyR,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGzR,QAAQ,CAACyR,cAAc,CAACrK,IAAI,CAAC;QAC1E;MACF;IACF;EACF;EACA,SAASsK,aAAaA,CAAClF,KAAK,EAAEnT,OAAO,EAAE;IACrC,KAAK,IAAI4N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5N,OAAO,CAAC2H,MAAM,EAAEiG,CAAC,EAAE,EAAE;MACvC,MAAM9M,QAAQ,GAAGd,OAAO,CAAC4N,CAAC,CAAC,CAACpM,KAAK;MACjCV,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC6F,QAAQ,CAAC2R,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGxX,QAAQ,CAAC6F,QAAQ,CAAC2R,eAAe,CAACnF,KAAK,CAAC;IAC3H;EACF;EACA,SAASoF,aAAaA,CAAC5X,IAAI,EAAE;IAC3B;IACA,QAAQA,IAAI;MACV,KAAK,gBAAgB;MACrB,KAAK,iBAAiB;QACpB,OAAO,MAAMoX,aAAa,CAAC,EAAE,CAAC;MAChC,KAAK,sBAAsB;QACzB,OAAO5E,KAAK,IAAI;UACd,MAAM;YACJlO;UACF,CAAC,GAAGiP,KAAK,CAAClP,QAAQ,CAAC,CAAC;UACpB,IAAI,WAAW,IAAImO,KAAK,IAAIlO,QAAQ,CAAC2O,WAAW,CAACyC,GAAG,CAAClD,KAAK,CAACW,SAAS,CAAC,EAAE;YACrE;YACA;YACA;YACA;YACA0E,qBAAqB,CAAC,MAAM;cAC1B;cACA,IAAIvT,QAAQ,CAAC2O,WAAW,CAACyC,GAAG,CAAClD,KAAK,CAACW,SAAS,CAAC,EAAE;gBAC7C7O,QAAQ,CAAC2O,WAAW,CAACI,MAAM,CAACb,KAAK,CAACW,SAAS,CAAC;gBAC5CiE,aAAa,CAAC,EAAE,CAAC;cACnB;YACF,CAAC,CAAC;UACJ;QACF,CAAC;IACL;;IAEA;IACA,OAAO,SAASU,WAAWA,CAACtF,KAAK,EAAE;MACjC,MAAM;QACJmF,eAAe;QACfrT;MACF,CAAC,GAAGiP,KAAK,CAAClP,QAAQ,CAAC,CAAC;;MAEpB;MACAC,QAAQ,CAACyT,SAAS,CAACpT,OAAO,GAAG6N,KAAK;;MAElC;MACA,MAAMwF,aAAa,GAAGhY,IAAI,KAAK,eAAe;MAC9C,MAAMiY,YAAY,GAAGjY,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,eAAe;MAC/F,MAAMoC,MAAM,GAAG4V,aAAa,GAAG5D,mBAAmB,GAAG/T,SAAS;MAC9D,MAAM4U,IAAI,GAAGZ,SAAS,CAAC7B,KAAK,EAAEpQ,MAAM,CAAC;MACrC,MAAM4T,KAAK,GAAGiC,YAAY,GAAGrE,iBAAiB,CAACpB,KAAK,CAAC,GAAG,CAAC;;MAEzD;MACA,IAAIxS,IAAI,KAAK,eAAe,EAAE;QAC5BsE,QAAQ,CAACyP,YAAY,GAAG,CAACvB,KAAK,CAACsB,OAAO,EAAEtB,KAAK,CAACyB,OAAO,CAAC;QACtD3P,QAAQ,CAACmP,WAAW,GAAGwB,IAAI,CAACiD,GAAG,CAACvC,GAAG,IAAIA,GAAG,CAAClD,WAAW,CAAC;MACzD;;MAEA;MACA;MACA,IAAIwF,YAAY,IAAI,CAAChD,IAAI,CAACjO,MAAM,EAAE;QAChC,IAAIgP,KAAK,IAAI,CAAC,EAAE;UACd0B,aAAa,CAAClF,KAAK,EAAElO,QAAQ,CAACC,WAAW,CAAC;UAC1C,IAAIoT,eAAe,EAAEA,eAAe,CAACnF,KAAK,CAAC;QAC7C;MACF;MACA;MACA,IAAIwF,aAAa,EAAEZ,aAAa,CAACnC,IAAI,CAAC;MACtC,SAASkD,WAAWA,CAAC/K,IAAI,EAAE;QACzB,MAAMqF,WAAW,GAAGrF,IAAI,CAACqF,WAAW;QACpC,MAAMtS,QAAQ,GAAGsS,WAAW,CAAC5R,KAAK;QAClC,MAAMmF,QAAQ,GAAG7F,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC6F,QAAQ;;QAE9D;QACA,IAAI,EAAE7F,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACgE,UAAU,CAAC,EAAE;;QAEhD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;QAEQ,IAAI6T,aAAa,EAAE;UACjB;UACA,IAAIhS,QAAQ,CAACoS,aAAa,IAAIpS,QAAQ,CAACqS,cAAc,IAAIrS,QAAQ,CAACwR,YAAY,IAAIxR,QAAQ,CAACyR,cAAc,EAAE;YACzG;YACA,MAAMhC,EAAE,GAAGlD,MAAM,CAACnF,IAAI,CAAC;YACvB,MAAMkL,WAAW,GAAGhU,QAAQ,CAACoP,OAAO,CAAC3D,GAAG,CAAC0F,EAAE,CAAC;YAC5C,IAAI,CAAC6C,WAAW,EAAE;cAChB;cACAhU,QAAQ,CAACoP,OAAO,CAAC/I,GAAG,CAAC8K,EAAE,EAAErI,IAAI,CAAC;cAC9BpH,QAAQ,CAACoS,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGpS,QAAQ,CAACoS,aAAa,CAAChL,IAAI,CAAC;cACtEpH,QAAQ,CAACqS,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrS,QAAQ,CAACqS,cAAc,CAACjL,IAAI,CAAC;YAC1E,CAAC,MAAM,IAAIkL,WAAW,CAACrC,OAAO,EAAE;cAC9B;cACA7I,IAAI,CAAC2J,eAAe,CAAC,CAAC;YACxB;UACF;UACA;UACA/Q,QAAQ,CAACuS,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvS,QAAQ,CAACuS,aAAa,CAACnL,IAAI,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAMoL,OAAO,GAAGxS,QAAQ,CAAChG,IAAI,CAAC;UAC9B,IAAIwY,OAAO,EAAE;YACX;YACA;YACA,IAAI,CAACP,YAAY,IAAI3T,QAAQ,CAACmP,WAAW,CAAC5F,QAAQ,CAAC4E,WAAW,CAAC,EAAE;cAC/D;cACAiF,aAAa,CAAClF,KAAK,EAAElO,QAAQ,CAACC,WAAW,CAACnC,MAAM,CAAChC,MAAM,IAAI,CAACkE,QAAQ,CAACmP,WAAW,CAAC5F,QAAQ,CAACzN,MAAM,CAAC,CAAC,CAAC;cACnG;cACAoY,OAAO,CAACpL,IAAI,CAAC;YACf;UACF,CAAC,MAAM;YACL;YACA,IAAI6K,YAAY,IAAI3T,QAAQ,CAACmP,WAAW,CAAC5F,QAAQ,CAAC4E,WAAW,CAAC,EAAE;cAC9DiF,aAAa,CAAClF,KAAK,EAAElO,QAAQ,CAACC,WAAW,CAACnC,MAAM,CAAChC,MAAM,IAAI,CAACkE,QAAQ,CAACmP,WAAW,CAAC5F,QAAQ,CAACzN,MAAM,CAAC,CAAC,CAAC;YACrG;UACF;QACF;MACF;MACA2V,gBAAgB,CAACd,IAAI,EAAEzC,KAAK,EAAEwD,KAAK,EAAEmC,WAAW,CAAC;IACnD,CAAC;EACH;EACA,OAAO;IACLP;EACF,CAAC;AACH;;AAEA;AACA,MAAMa,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC;AAC9H,MAAMC,UAAU,GAAG/O,GAAG,IAAI,CAAC,EAAEA,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACyB,MAAM,CAAC;AACvD,MAAMuN,OAAO,GAAG,aAAa1a,KAAK,CAAC2a,aAAa,CAAC,IAAI,CAAC;AACtD,MAAMC,WAAW,GAAGA,CAACrH,UAAU,EAAEsH,OAAO,KAAK;EAC3C,MAAM1U,SAAS,GAAG9F,MAAM,CAAC,CAACqM,GAAG,EAAEoF,GAAG,KAAK;IACrC,MAAMgJ,QAAQ,GAAG,IAAI/a,KAAK,CAACoY,OAAO,CAAC,CAAC;IACpC,MAAM4C,aAAa,GAAG,IAAIhb,KAAK,CAACoY,OAAO,CAAC,CAAC;IACzC,MAAM6C,UAAU,GAAG,IAAIjb,KAAK,CAACoY,OAAO,CAAC,CAAC;IACtC,SAAS8C,kBAAkBA,CAACvH,MAAM,GAAG5B,GAAG,CAAC,CAAC,CAAC4B,MAAM,EAAElR,MAAM,GAAGuY,aAAa,EAAEpH,IAAI,GAAG7B,GAAG,CAAC,CAAC,CAAC6B,IAAI,EAAE;MAC5F,MAAM;QACJG,KAAK;QACLG,MAAM;QACND,GAAG;QACHH;MACF,CAAC,GAAGF,IAAI;MACR,MAAMQ,MAAM,GAAGL,KAAK,GAAGG,MAAM;MAC7B,IAAIzR,MAAM,CAAC0Y,SAAS,EAAEF,UAAU,CAACjJ,IAAI,CAACvP,MAAM,CAAC,CAAC,KAAKwY,UAAU,CAACtO,GAAG,CAAC,GAAGlK,MAAM,CAAC;MAC5E,MAAM6U,QAAQ,GAAG3D,MAAM,CAACyH,gBAAgB,CAACL,QAAQ,CAAC,CAACM,UAAU,CAACJ,UAAU,CAAC;MACzE,IAAIvP,oBAAoB,CAACiI,MAAM,CAAC,EAAE;QAChC,OAAO;UACLI,KAAK,EAAEA,KAAK,GAAGJ,MAAM,CAAC2H,IAAI;UAC1BpH,MAAM,EAAEA,MAAM,GAAGP,MAAM,CAAC2H,IAAI;UAC5BrH,GAAG;UACHH,IAAI;UACJyH,MAAM,EAAE,CAAC;UACTjE,QAAQ;UACRlD;QACF,CAAC;MACH,CAAC,MAAM;QACL,MAAMoH,GAAG,GAAG7H,MAAM,CAAC6H,GAAG,GAAGzN,IAAI,CAAC0N,EAAE,GAAG,GAAG,CAAC,CAAC;QACxC,MAAMC,CAAC,GAAG,CAAC,GAAG3N,IAAI,CAAC4N,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGlE,QAAQ,CAAC,CAAC;QAC5C,MAAMsE,CAAC,GAAGF,CAAC,IAAI3H,KAAK,GAAGG,MAAM,CAAC;QAC9B,OAAO;UACLH,KAAK,EAAE6H,CAAC;UACR1H,MAAM,EAAEwH,CAAC;UACTzH,GAAG;UACHH,IAAI;UACJyH,MAAM,EAAExH,KAAK,GAAG6H,CAAC;UACjBtE,QAAQ;UACRlD;QACF,CAAC;MACH;IACF;IACA,IAAIyH,kBAAkB,GAAGxZ,SAAS;IAClC,MAAMyZ,qBAAqB,GAAGnV,OAAO,IAAIgG,GAAG,CAACK,KAAK,KAAK;MACrDrC,WAAW,EAAE;QACX,GAAGqC,KAAK,CAACrC,WAAW;QACpBhE;MACF;IACF,CAAC,CAAC,CAAC;IACH,MAAMuR,OAAO,GAAG,IAAIlY,KAAK,CAAC+b,OAAO,CAAC,CAAC;IACnC,MAAM3V,SAAS,GAAG;MAChBuG,GAAG;MACHoF,GAAG;MACH;MACAgB,EAAE,EAAE,IAAI;MACRY,MAAM,EAAE,IAAI;MACZ+C,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE;QACNY,QAAQ,EAAE,CAAC;QACXT,OAAO,EAAE,IAAI;QACbkF,SAAS,EAAE;MACb,CAAC;MACDC,EAAE,EAAE,IAAI;MACR3U,KAAK,EAAE,IAAI;MACXkM,UAAU,EAAEA,CAACD,MAAM,GAAG,CAAC,KAAKC,UAAU,CAACzB,GAAG,CAAC,CAAC,EAAEwB,MAAM,CAAC;MACrDuH,OAAO,EAAEA,CAACoB,SAAS,EAAEC,gBAAgB,KAAKrB,OAAO,CAACoB,SAAS,EAAEC,gBAAgB,EAAEpK,GAAG,CAAC,CAAC,CAAC;MACrFqK,MAAM,EAAE,KAAK;MACb7J,MAAM,EAAE,KAAK;MACb8J,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAIvc,KAAK,CAACwc,KAAK,CAAC,CAAC;MACxBtE,OAAO;MACPuE,KAAK,EAAEvE,OAAO;MACdwE,SAAS,EAAE,QAAQ;MACnB/C,eAAe,EAAEtX,SAAS;MAC1BsI,WAAW,EAAE;QACXhE,OAAO,EAAE,CAAC;QACVqH,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,CAAC;QACN0O,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAEA,CAAA,KAAM;UACb,MAAM5P,KAAK,GAAG+E,GAAG,CAAC,CAAC;UACnB;UACA,IAAI8J,kBAAkB,EAAE3Q,YAAY,CAAC2Q,kBAAkB,CAAC;UACxD;UACA,IAAI7O,KAAK,CAACrC,WAAW,CAAChE,OAAO,KAAKqG,KAAK,CAACrC,WAAW,CAACqD,GAAG,EAAE8N,qBAAqB,CAAC9O,KAAK,CAACrC,WAAW,CAACqD,GAAG,CAAC;UACrG;UACA6N,kBAAkB,GAAG7Q,UAAU,CAAC,MAAM8Q,qBAAqB,CAAC/J,GAAG,CAAC,CAAC,CAACpH,WAAW,CAACsD,GAAG,CAAC,EAAEjB,KAAK,CAACrC,WAAW,CAACgS,QAAQ,CAAC;QACjH;MACF,CAAC;MACD/I,IAAI,EAAE;QACJG,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE,CAAC;QACTD,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE,CAAC;QACP+I,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;QACRC,UAAU,EAAE,CAAC;QACbnP,GAAG,EAAE,CAAC;QACNmG,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE,CAAC;QACTD,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE,CAAC;QACPM,MAAM,EAAE,CAAC;QACTkD,QAAQ,EAAE,CAAC;QACXiE,MAAM,EAAE,CAAC;QACTL;MACF,CAAC;MACD8B,SAAS,EAAErG,MAAM,IAAIhK,GAAG,CAACK,KAAK,KAAK;QACjC,GAAGA,KAAK;QACR2J,MAAM,EAAE;UACN,GAAG3J,KAAK,CAAC2J,MAAM;UACf,GAAGA;QACL;MACF,CAAC,CAAC,CAAC;MACHsG,OAAO,EAAEA,CAAClJ,KAAK,EAAEG,MAAM,EAAE2I,WAAW,EAAE5I,GAAG,EAAEH,IAAI,KAAK;QAClD,MAAMH,MAAM,GAAG5B,GAAG,CAAC,CAAC,CAAC4B,MAAM;QAC3B,MAAMC,IAAI,GAAG;UACXG,KAAK;UACLG,MAAM;UACND,GAAG,EAAEA,GAAG,IAAI,CAAC;UACbH,IAAI,EAAEA,IAAI,IAAI,CAAC;UACf+I;QACF,CAAC;QACDlQ,GAAG,CAACK,KAAK,KAAK;UACZ4G,IAAI;UACJkJ,QAAQ,EAAE;YACR,GAAG9P,KAAK,CAAC8P,QAAQ;YACjB,GAAG5B,kBAAkB,CAACvH,MAAM,EAAEqH,aAAa,EAAEpH,IAAI;UACnD;QACF,CAAC,CAAC,CAAC;MACL,CAAC;MACDsJ,MAAM,EAAEtP,GAAG,IAAIjB,GAAG,CAACK,KAAK,IAAI;QAC1B,MAAMmQ,QAAQ,GAAGxP,YAAY,CAACC,GAAG,CAAC;QAClC,OAAO;UACLkP,QAAQ,EAAE;YACR,GAAG9P,KAAK,CAAC8P,QAAQ;YACjBlP,GAAG,EAAEuP,QAAQ;YACbJ,UAAU,EAAE/P,KAAK,CAAC8P,QAAQ,CAACC,UAAU,IAAII;UAC3C;QACF,CAAC;MACH,CAAC,CAAC;MACFC,YAAY,EAAEA,CAACV,SAAS,GAAG,QAAQ,KAAK;QACtC,MAAMH,KAAK,GAAGxK,GAAG,CAAC,CAAC,CAACwK,KAAK;;QAEzB;QACAA,KAAK,CAACc,IAAI,CAAC,CAAC;QACZd,KAAK,CAACe,WAAW,GAAG,CAAC;QACrB,IAAIZ,SAAS,KAAK,OAAO,EAAE;UACzBH,KAAK,CAACgB,KAAK,CAAC,CAAC;UACbhB,KAAK,CAACe,WAAW,GAAG,CAAC;QACvB;QACA3Q,GAAG,CAAC,OAAO;UACT+P;QACF,CAAC,CAAC,CAAC;MACL,CAAC;MACDtO,YAAY,EAAE/L,SAAS;MACvBiE,QAAQ,EAAE;QACRkX,MAAM,EAAE,KAAK;QACbjG,QAAQ,EAAE,CAAC;QACXhE,MAAM,EAAE,CAAC;QACTwG,SAAS,EAAE,aAAa9Z,KAAK,CAACwd,SAAS,CAAC,CAAC;QACzClX,WAAW,EAAE,EAAE;QACfmP,OAAO,EAAE,IAAIlI,GAAG,CAAC,CAAC;QAClBkQ,WAAW,EAAE,EAAE;QACf3H,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACpBN,WAAW,EAAE,EAAE;QACfR,WAAW,EAAE,IAAIzH,GAAG,CAAC,CAAC;QACtBmQ,SAAS,EAAEA,CAACjX,GAAG,EAAE6Q,QAAQ,EAAEhC,KAAK,KAAK;UACnC,MAAMjP,QAAQ,GAAGyL,GAAG,CAAC,CAAC,CAACzL,QAAQ;UAC/B;UACA;UACA;UACA;UACAA,QAAQ,CAACiR,QAAQ,GAAGjR,QAAQ,CAACiR,QAAQ,IAAIA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UAC9DjR,QAAQ,CAACoX,WAAW,CAACja,IAAI,CAAC;YACxBiD,GAAG;YACH6Q,QAAQ;YACRhC;UACF,CAAC,CAAC;UACF;UACA;UACAjP,QAAQ,CAACoX,WAAW,GAAGpX,QAAQ,CAACoX,WAAW,CAACvG,IAAI,CAAC,CAAC9I,CAAC,EAAEO,CAAC,KAAKP,CAAC,CAACkJ,QAAQ,GAAG3I,CAAC,CAAC2I,QAAQ,CAAC;UACnF,OAAO,MAAM;YACX,MAAMjR,QAAQ,GAAGyL,GAAG,CAAC,CAAC,CAACzL,QAAQ;YAC/B,IAAIA,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACoX,WAAW,EAAE;cAC5C;cACApX,QAAQ,CAACiR,QAAQ,GAAGjR,QAAQ,CAACiR,QAAQ,IAAIA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC9D;cACAjR,QAAQ,CAACoX,WAAW,GAAGpX,QAAQ,CAACoX,WAAW,CAACtZ,MAAM,CAACwZ,CAAC,IAAIA,CAAC,CAAClX,GAAG,KAAKA,GAAG,CAAC;YACxE;UACF,CAAC;QACH;MACF;IACF,CAAC;IACD,OAAON,SAAS;EAClB,CAAC,CAAC;EACF,MAAM4G,KAAK,GAAG5G,SAAS,CAACC,QAAQ,CAAC,CAAC;EAClC,IAAIwX,OAAO,GAAG7Q,KAAK,CAAC4G,IAAI;EACxB,IAAIkK,MAAM,GAAG9Q,KAAK,CAAC8P,QAAQ,CAAClP,GAAG;EAC/B,IAAImQ,SAAS,GAAG/Q,KAAK,CAAC2G,MAAM;EAC5BvN,SAAS,CAACuX,SAAS,CAAC,MAAM;IACxB,MAAM;MACJhK,MAAM;MACNC,IAAI;MACJkJ,QAAQ;MACR/J,EAAE;MACFpG;IACF,CAAC,GAAGvG,SAAS,CAACC,QAAQ,CAAC,CAAC;;IAExB;IACA,IAAIuN,IAAI,CAACG,KAAK,KAAK8J,OAAO,CAAC9J,KAAK,IAAIH,IAAI,CAACM,MAAM,KAAK2J,OAAO,CAAC3J,MAAM,IAAI4I,QAAQ,CAAClP,GAAG,KAAKkQ,MAAM,EAAE;MAC7F,IAAIE,iBAAiB;MACrBH,OAAO,GAAGjK,IAAI;MACdkK,MAAM,GAAGhB,QAAQ,CAAClP,GAAG;MACrB;MACA8F,YAAY,CAACC,MAAM,EAAEC,IAAI,CAAC;MAC1Bb,EAAE,CAACkL,aAAa,CAACnB,QAAQ,CAAClP,GAAG,CAAC;MAC9B,MAAMiP,WAAW,GAAG,CAACmB,iBAAiB,GAAGpK,IAAI,CAACiJ,WAAW,KAAK,IAAI,GAAGmB,iBAAiB,GAAG,OAAOE,iBAAiB,KAAK,WAAW,IAAInL,EAAE,CAACoL,UAAU,YAAYD,iBAAiB;MAC/KnL,EAAE,CAACkK,OAAO,CAACrJ,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACM,MAAM,EAAE2I,WAAW,CAAC;IAClD;;IAEA;IACA,IAAIlJ,MAAM,KAAKoK,SAAS,EAAE;MACxBA,SAAS,GAAGpK,MAAM;MAClB;MACAhH,GAAG,CAACK,KAAK,KAAK;QACZ8P,QAAQ,EAAE;UACR,GAAG9P,KAAK,CAAC8P,QAAQ;UACjB,GAAG9P,KAAK,CAAC8P,QAAQ,CAAC5B,kBAAkB,CAACvH,MAAM;QAC7C;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;;EAEF;EACAvN,SAAS,CAACuX,SAAS,CAAC3Q,KAAK,IAAIwG,UAAU,CAACxG,KAAK,CAAC,CAAC;;EAE/C;EACA,OAAO5G,SAAS;AAClB,CAAC;AAED,SAASgY,UAAUA,CAAC3Y,QAAQ,EAAE4Y,IAAI,EAAE;EAClC,MAAMC,GAAG,GAAG;IACV7Y;EACF,CAAC;EACD4Y,IAAI,CAAC7a,GAAG,CAAC8a,GAAG,CAAC;EACb,OAAO,MAAM,KAAKD,IAAI,CAAChJ,MAAM,CAACiJ,GAAG,CAAC;AACpC;AACA,IAAIrP,CAAC;AACL,IAAIsP,aAAa,GAAG,IAAIhI,GAAG,CAAC,CAAC;AAC7B,IAAIiI,kBAAkB,GAAG,IAAIjI,GAAG,CAAC,CAAC;AAClC,IAAIkI,iBAAiB,GAAG,IAAIlI,GAAG,CAAC,CAAC;;AAEjC;AACA;AACA;AACA;AACA,MAAMmI,SAAS,GAAGjZ,QAAQ,IAAI2Y,UAAU,CAAC3Y,QAAQ,EAAE8Y,aAAa,CAAC;;AAEjE;AACA;AACA;AACA;AACA,MAAMI,cAAc,GAAGlZ,QAAQ,IAAI2Y,UAAU,CAAC3Y,QAAQ,EAAE+Y,kBAAkB,CAAC;;AAE3E;AACA;AACA;AACA;AACA,MAAMI,OAAO,GAAGnZ,QAAQ,IAAI2Y,UAAU,CAAC3Y,QAAQ,EAAEgZ,iBAAiB,CAAC;AACnE,SAASI,GAAGA,CAACC,OAAO,EAAE5C,SAAS,EAAE;EAC/B,IAAI,CAAC4C,OAAO,CAAClL,IAAI,EAAE;EACnB,KAAK,MAAM;IACTnO;EACF,CAAC,IAAIqZ,OAAO,CAACjH,MAAM,CAAC,CAAC,EAAE;IACrBpS,QAAQ,CAACyW,SAAS,CAAC;EACrB;AACF;AACA,SAAS6C,kBAAkBA,CAACpd,IAAI,EAAEua,SAAS,EAAE;EAC3C,QAAQva,IAAI;IACV,KAAK,QAAQ;MACX,OAAOkd,GAAG,CAACN,aAAa,EAAErC,SAAS,CAAC;IACtC,KAAK,OAAO;MACV,OAAO2C,GAAG,CAACL,kBAAkB,EAAEtC,SAAS,CAAC;IAC3C,KAAK,MAAM;MACT,OAAO2C,GAAG,CAACJ,iBAAiB,EAAEvC,SAAS,CAAC;EAC5C;AACF;AACA,IAAIwB,WAAW;AACf,IAAIsB,YAAY;AAChB,SAASC,QAAQA,CAAC/C,SAAS,EAAElP,KAAK,EAAEkS,KAAK,EAAE;EACzC;EACA,IAAIlH,KAAK,GAAGhL,KAAK,CAACuP,KAAK,CAAC4C,QAAQ,CAAC,CAAC;EAClC;EACA,IAAInS,KAAK,CAAC0P,SAAS,KAAK,OAAO,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;IAChElE,KAAK,GAAGkE,SAAS,GAAGlP,KAAK,CAACuP,KAAK,CAACe,WAAW;IAC3CtQ,KAAK,CAACuP,KAAK,CAAC6C,OAAO,GAAGpS,KAAK,CAACuP,KAAK,CAACe,WAAW;IAC7CtQ,KAAK,CAACuP,KAAK,CAACe,WAAW,GAAGpB,SAAS;EACrC;EACA;EACAwB,WAAW,GAAG1Q,KAAK,CAAC1G,QAAQ,CAACoX,WAAW;EACxC,KAAKzO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,WAAW,CAAC1U,MAAM,EAAEiG,CAAC,EAAE,EAAE;IACvC+P,YAAY,GAAGtB,WAAW,CAACzO,CAAC,CAAC;IAC7B+P,YAAY,CAACtY,GAAG,CAACC,OAAO,CAACqY,YAAY,CAACzJ,KAAK,CAAClP,QAAQ,CAAC,CAAC,EAAE2R,KAAK,EAAEkH,KAAK,CAAC;EACvE;EACA;EACA,IAAI,CAAClS,KAAK,CAAC1G,QAAQ,CAACiR,QAAQ,IAAIvK,KAAK,CAAC+F,EAAE,CAAC3F,MAAM,EAAEJ,KAAK,CAAC+F,EAAE,CAAC3F,MAAM,CAACJ,KAAK,CAAC1F,KAAK,EAAE0F,KAAK,CAAC2G,MAAM,CAAC;EAC3F;EACA3G,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,GAAGxF,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEjB,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,GAAG,CAAC,CAAC;EAC9D,OAAOvG,KAAK,CAAC0P,SAAS,KAAK,QAAQ,GAAG,CAAC,GAAG1P,KAAK,CAAC1G,QAAQ,CAACiN,MAAM;AACjE;AACA,SAAS8L,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,IAAIC,MAAM;EACV,IAAIP,KAAK;EACT,IAAIlS,KAAK;EACT,SAAS0S,IAAIA,CAACxD,SAAS,EAAE;IACvBgD,KAAK,GAAGrF,qBAAqB,CAAC6F,IAAI,CAAC;IACnCH,OAAO,GAAG,IAAI;IACdE,MAAM,GAAG,CAAC;;IAEV;IACAV,kBAAkB,CAAC,QAAQ,EAAE7C,SAAS,CAAC;;IAEvC;IACAsD,kBAAkB,GAAG,IAAI;IACzB,KAAK,MAAMzd,IAAI,IAAIud,KAAK,CAACzH,MAAM,CAAC,CAAC,EAAE;MACjC,IAAI8H,YAAY;MAChB3S,KAAK,GAAGjL,IAAI,CAACwT,KAAK,CAAClP,QAAQ,CAAC,CAAC;MAC7B;MACA,IAAI2G,KAAK,CAAC1G,QAAQ,CAACkX,MAAM,KAAKxQ,KAAK,CAAC0P,SAAS,KAAK,QAAQ,IAAI1P,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAACoM,YAAY,GAAG3S,KAAK,CAAC+F,EAAE,CAACkJ,EAAE,KAAK,IAAI,IAAI0D,YAAY,CAACC,YAAY,CAAC,EAAE;QAChKH,MAAM,IAAIR,QAAQ,CAAC/C,SAAS,EAAElP,KAAK,CAAC;MACtC;IACF;IACAwS,kBAAkB,GAAG,KAAK;;IAE1B;IACAT,kBAAkB,CAAC,OAAO,EAAE7C,SAAS,CAAC;;IAEtC;IACA,IAAIuD,MAAM,KAAK,CAAC,EAAE;MAChB;MACAV,kBAAkB,CAAC,MAAM,EAAE7C,SAAS,CAAC;;MAErC;MACAqD,OAAO,GAAG,KAAK;MACf,OAAOM,oBAAoB,CAACX,KAAK,CAAC;IACpC;EACF;EACA,SAAS1L,UAAUA,CAACxG,KAAK,EAAEuG,MAAM,GAAG,CAAC,EAAE;IACrC,IAAIuM,aAAa;IACjB,IAAI,CAAC9S,KAAK,EAAE,OAAOsS,KAAK,CAAC3a,OAAO,CAAC5C,IAAI,IAAIyR,UAAU,CAACzR,IAAI,CAACwT,KAAK,CAAClP,QAAQ,CAAC,CAAC,EAAEkN,MAAM,CAAC,CAAC;IACnF,IAAI,CAACuM,aAAa,GAAG9S,KAAK,CAAC+F,EAAE,CAACkJ,EAAE,KAAK,IAAI,IAAI6D,aAAa,CAACF,YAAY,IAAI,CAAC5S,KAAK,CAAC1G,QAAQ,CAACkX,MAAM,IAAIxQ,KAAK,CAAC0P,SAAS,KAAK,OAAO,EAAE;IAClI,IAAInJ,MAAM,GAAG,CAAC,EAAE;MACd;MACA;MACAvG,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,GAAGxF,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEhB,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,GAAGA,MAAM,CAAC;IACtE,CAAC,MAAM;MACL,IAAIiM,kBAAkB,EAAE;QACtB;QACAxS,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,GAAG,CAAC;MAC3B,CAAC,MAAM;QACL;QACAvG,KAAK,CAAC1G,QAAQ,CAACiN,MAAM,GAAG,CAAC;MAC3B;IACF;;IAEA;IACA,IAAI,CAACgM,OAAO,EAAE;MACZA,OAAO,GAAG,IAAI;MACd1F,qBAAqB,CAAC6F,IAAI,CAAC;IAC7B;EACF;EACA,SAAS5E,OAAOA,CAACoB,SAAS,EAAEC,gBAAgB,GAAG,IAAI,EAAEnP,KAAK,EAAEkS,KAAK,EAAE;IACjE,IAAI/C,gBAAgB,EAAE4C,kBAAkB,CAAC,QAAQ,EAAE7C,SAAS,CAAC;IAC7D,IAAI,CAAClP,KAAK,EAAE,KAAK,MAAMjL,IAAI,IAAIud,KAAK,CAACzH,MAAM,CAAC,CAAC,EAAEoH,QAAQ,CAAC/C,SAAS,EAAEna,IAAI,CAACwT,KAAK,CAAClP,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK4Y,QAAQ,CAAC/C,SAAS,EAAElP,KAAK,EAAEkS,KAAK,CAAC;IAChI,IAAI/C,gBAAgB,EAAE4C,kBAAkB,CAAC,OAAO,EAAE7C,SAAS,CAAC;EAC9D;EACA,OAAO;IACLwD,IAAI;IACJlM,UAAU;IACVsH;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiF,iBAAiBA,CAACrZ,GAAG,EAAE;EAC9B,MAAMvE,QAAQ,GAAGlC,KAAK,CAACwM,MAAM,CAAC,IAAI,CAAC;EACnCV,yBAAyB,CAAC,MAAM,MAAM5J,QAAQ,CAACwE,OAAO,GAAGD,GAAG,CAACC,OAAO,CAAC9D,KAAK,CAAC,EAAE,CAAC6D,GAAG,CAAC,CAAC;EACnF,OAAOvE,QAAQ;AACjB;AACA,SAAS6d,QAAQA,CAAA,EAAG;EAClB,MAAMzK,KAAK,GAAGtV,KAAK,CAACggB,UAAU,CAACtF,OAAO,CAAC;EACvC,IAAI,CAACpF,KAAK,EAAE,MAAM,IAAIjT,KAAK,CAAC,0DAA0D,CAAC;EACvF,OAAOiT,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,SAAS2K,QAAQA,CAACC,QAAQ,GAAGnT,KAAK,IAAIA,KAAK,EAAEoT,UAAU,EAAE;EACvD,OAAOJ,QAAQ,CAAC,CAAC,CAACG,QAAQ,EAAEC,UAAU,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC5a,QAAQ,EAAE6a,cAAc,GAAG,CAAC,EAAE;EAC9C,MAAM/K,KAAK,GAAGyK,QAAQ,CAAC,CAAC;EACxB,MAAMrC,SAAS,GAAGpI,KAAK,CAAClP,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACqX,SAAS;EACrD;EACA,MAAMjX,GAAG,GAAG6F,kBAAkB,CAAC9G,QAAQ,CAAC;EACxC;EACAsG,yBAAyB,CAAC,MAAM4R,SAAS,CAACjX,GAAG,EAAE4Z,cAAc,EAAE/K,KAAK,CAAC,EAAE,CAAC+K,cAAc,EAAE3C,SAAS,EAAEpI,KAAK,CAAC,CAAC;EAC1G,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA,SAASgL,QAAQA,CAACne,MAAM,EAAE;EACxB,OAAOnC,KAAK,CAACugB,OAAO,CAAC,MAAMrR,UAAU,CAAC/M,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;AAC1D;AACA,MAAMqe,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,SAASC,SAASA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACzC,OAAO,UAAUC,KAAK,EAAE,GAAGC,KAAK,EAAE;IAChC;IACA,IAAIC,MAAM,GAAGP,eAAe,CAAC1O,GAAG,CAAC+O,KAAK,CAAC;IACvC,IAAI,CAACE,MAAM,EAAE;MACXA,MAAM,GAAG,IAAIF,KAAK,CAAC,CAAC;MACpBL,eAAe,CAAC9T,GAAG,CAACmU,KAAK,EAAEE,MAAM,CAAC;IACpC;IACA,IAAIJ,UAAU,EAAEA,UAAU,CAACI,MAAM,CAAC;IAClC;IACA,OAAOpU,OAAO,CAACqU,GAAG,CAACF,KAAK,CAAC7G,GAAG,CAAC6G,KAAK,IAAI,IAAInU,OAAO,CAAC,CAACsU,GAAG,EAAEC,MAAM,KAAKH,MAAM,CAACI,IAAI,CAACL,KAAK,EAAE3R,IAAI,IAAI;MAC5F,IAAIA,IAAI,CAAC9H,KAAK,EAAEtG,MAAM,CAACM,MAAM,CAAC8N,IAAI,EAAED,UAAU,CAACC,IAAI,CAAC9H,KAAK,CAAC,CAAC;MAC3D4Z,GAAG,CAAC9R,IAAI,CAAC;IACX,CAAC,EAAEyR,UAAU,EAAE5T,KAAK,IAAIkU,MAAM,CAAC,IAAI7e,KAAK,CAAC,kBAAkBye,KAAK,KAAK9T,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACoU,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrH,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACR,KAAK,EAAEC,KAAK,EAAEH,UAAU,EAAEC,UAAU,EAAE;EACvD;EACA,MAAM3R,IAAI,GAAGxM,KAAK,CAACC,OAAO,CAACoe,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,MAAMQ,OAAO,GAAGhhB,OAAO,CAACogB,SAAS,CAACC,UAAU,EAAEC,UAAU,CAAC,EAAE,CAACC,KAAK,EAAE,GAAG5R,IAAI,CAAC,EAAE;IAC3EsS,KAAK,EAAE5W,EAAE,CAAC+D;EACZ,CAAC,CAAC;EACF;EACA,OAAOjM,KAAK,CAACC,OAAO,CAACoe,KAAK,CAAC,GAAGQ,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;AACpD;;AAEA;AACA;AACA;AACAD,SAAS,CAAC9gB,OAAO,GAAG,UAAUsgB,KAAK,EAAEC,KAAK,EAAEH,UAAU,EAAE;EACtD,MAAM1R,IAAI,GAAGxM,KAAK,CAACC,OAAO,CAACoe,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,OAAOvgB,OAAO,CAACmgB,SAAS,CAACC,UAAU,CAAC,EAAE,CAACE,KAAK,EAAE,GAAG5R,IAAI,CAAC,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACAoS,SAAS,CAAC7gB,KAAK,GAAG,UAAUqgB,KAAK,EAAEC,KAAK,EAAE;EACxC,MAAM7R,IAAI,GAAGxM,KAAK,CAACC,OAAO,CAACoe,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,OAAOtgB,KAAK,CAAC,CAACqgB,KAAK,EAAE,GAAG5R,IAAI,CAAC,CAAC;AAChC,CAAC;AAED,MAAMoQ,KAAK,GAAG,IAAI9R,GAAG,CAAC,CAAC;AACvB,MAAM;EACJgG,UAAU;EACVsH;AACF,CAAC,GAAGuE,UAAU,CAACC,KAAK,CAAC;AACrB,MAAM;EACJzY,UAAU;EACVsE;AACF,CAAC,GAAG5J,cAAc,CAAC+d,KAAK,EAAE1K,gBAAgB,CAAC;AAC3C,MAAM6M,YAAY,GAAG;EACnBpgB,OAAO,EAAE,SAAS;EAClByN,MAAM,EAAE;AACV,CAAC;AACD,MAAM4S,sBAAsB,GAAGA,CAAC3O,EAAE,EAAE4O,MAAM,KAAK;EAC7C,MAAMC,cAAc,GAAG,OAAO7O,EAAE,KAAK,UAAU,GAAGA,EAAE,CAAC4O,MAAM,CAAC,GAAG5O,EAAE;EACjE,IAAI2H,UAAU,CAACkH,cAAc,CAAC,EAAE,OAAOA,cAAc,CAAC,KAAK,OAAO,IAAI5hB,KAAK,CAAC6hB,aAAa,CAAC;IACxFC,eAAe,EAAE,kBAAkB;IACnCH,MAAM,EAAEA,MAAM;IACdI,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;IACX,GAAGjP;EACL,CAAC,CAAC;AACJ,CAAC;AACD,SAASkP,kBAAkBA,CAACN,MAAM,EAAEO,WAAW,EAAE;EAC/C,MAAMC,YAAY,GAAG,OAAOjE,iBAAiB,KAAK,WAAW,IAAIyD,MAAM,YAAYzD,iBAAiB;EACpG,IAAIgE,WAAW,EAAE;IACf,MAAM;MACJnO,KAAK;MACLG,MAAM;MACND,GAAG;MACHH,IAAI;MACJ+I,WAAW,GAAGsF;IAChB,CAAC,GAAGD,WAAW;IACf,OAAO;MACLnO,KAAK;MACLG,MAAM;MACND,GAAG;MACHH,IAAI;MACJ+I;IACF,CAAC;EACH,CAAC,MAAM,IAAI,OAAOqB,iBAAiB,KAAK,WAAW,IAAIyD,MAAM,YAAYzD,iBAAiB,IAAIyD,MAAM,CAACS,aAAa,EAAE;IAClH,MAAM;MACJrO,KAAK;MACLG,MAAM;MACND,GAAG;MACHH;IACF,CAAC,GAAG6N,MAAM,CAACS,aAAa,CAACC,qBAAqB,CAAC,CAAC;IAChD,OAAO;MACLtO,KAAK;MACLG,MAAM;MACND,GAAG;MACHH,IAAI;MACJ+I,WAAW,EAAEsF;IACf,CAAC;EACH,CAAC,MAAM,IAAI,OAAOG,eAAe,KAAK,WAAW,IAAIX,MAAM,YAAYW,eAAe,EAAE;IACtF,OAAO;MACLvO,KAAK,EAAE4N,MAAM,CAAC5N,KAAK;MACnBG,MAAM,EAAEyN,MAAM,CAACzN,MAAM;MACrBD,GAAG,EAAE,CAAC;MACNH,IAAI,EAAE,CAAC;MACP+I,WAAW,EAAEsF;IACf,CAAC;EACH;EACA,OAAO;IACLpO,KAAK,EAAE,CAAC;IACRG,MAAM,EAAE,CAAC;IACTD,GAAG,EAAE,CAAC;IACNH,IAAI,EAAE;EACR,CAAC;AACH;AACA,SAASyO,UAAUA,CAACZ,MAAM,EAAE;EAC1B;EACA,MAAMa,QAAQ,GAAGlD,KAAK,CAACvN,GAAG,CAAC4P,MAAM,CAAC;EAClC,MAAMc,SAAS,GAAGD,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC1c,KAAK;EAC5D,MAAM4c,SAAS,GAAGF,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACjN,KAAK;EAC5D,IAAIiN,QAAQ,EAAEG,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;;EAExE;EACA;EACA,MAAMC,mBAAmB,GAAG,OAAOC,WAAW,KAAK,UAAU;EAC7D;EACA;EACAA,WAAW;EACX;EACAH,OAAO,CAAC1V,KAAK;;EAEb;EACA,MAAMsI,KAAK,GAAGmN,SAAS,IAAI7H,WAAW,CAACrH,UAAU,EAAEsH,OAAO,CAAC;EAC3D;EACA,MAAMhV,KAAK,GAAG2c,SAAS,IAAI5b,UAAU,CAACkc,eAAe,CAACxN,KAAK,EAAElV,cAAc,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAEwiB,mBAAmB,EAAE,IAAI,CAAC;EAC9H;EACA,IAAI,CAACL,QAAQ,EAAElD,KAAK,CAAC3S,GAAG,CAACgV,MAAM,EAAE;IAC/B7b,KAAK;IACLyP;EACF,CAAC,CAAC;;EAEF;EACA,IAAIyN,SAAS;EACb,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,UAAU;EACd,OAAO;IACLC,SAASA,CAACrhB,KAAK,GAAG,CAAC,CAAC,EAAE;MACpB,IAAI;QACFiR,EAAE,EAAEqQ,QAAQ;QACZxP,IAAI,EAAEyP,SAAS;QACf/b,KAAK,EAAEgc,YAAY;QACnB3M,MAAM;QACNqM,SAAS,EAAEO,iBAAiB;QAC5BC,OAAO,GAAG,KAAK;QACfjR,MAAM,GAAG,KAAK;QACd8J,IAAI,GAAG,KAAK;QACZD,MAAM,GAAG,KAAK;QACdqH,YAAY,GAAG,KAAK;QACpB/G,SAAS,GAAG,QAAQ;QACpB9O,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACZjD,WAAW;QACX+L,SAAS,EAAEgN,cAAc;QACzB/P,MAAM,EAAEgQ,aAAa;QACrBhK;MACF,CAAC,GAAG7X,KAAK;MACT,IAAIkL,KAAK,GAAGuI,KAAK,CAAClP,QAAQ,CAAC,CAAC;;MAE5B;MACA,IAAI0M,EAAE,GAAG/F,KAAK,CAAC+F,EAAE;MACjB,IAAI,CAAC/F,KAAK,CAAC+F,EAAE,EAAE/F,KAAK,CAACL,GAAG,CAAC;QACvBoG,EAAE,EAAEA,EAAE,GAAG2O,sBAAsB,CAAC0B,QAAQ,EAAEzB,MAAM;MAClD,CAAC,CAAC;;MAEF;MACA,IAAIjL,SAAS,GAAG1J,KAAK,CAAC0J,SAAS;MAC/B,IAAI,CAACA,SAAS,EAAE1J,KAAK,CAACL,GAAG,CAAC;QACxB+J,SAAS,EAAEA,SAAS,GAAG,IAAI1W,KAAK,CAAC4jB,SAAS,CAAC;MAC7C,CAAC,CAAC;;MAEF;MACA,MAAM;QACJC,MAAM;QACN,GAAGC;MACL,CAAC,GAAGJ,cAAc,IAAI,CAAC,CAAC;MACxB,IAAI,CAAC9Y,EAAE,CAAC+D,GAAG,CAACmV,OAAO,EAAEpN,SAAS,EAAE+K,YAAY,CAAC,EAAEtW,UAAU,CAACuL,SAAS,EAAE;QACnE,GAAGoN;MACL,CAAC,CAAC;MACF,IAAI,CAAClZ,EAAE,CAAC+D,GAAG,CAACkV,MAAM,EAAEnN,SAAS,CAACmN,MAAM,EAAEpC,YAAY,CAAC,EAAEtW,UAAU,CAACuL,SAAS,EAAE;QACzEmN,MAAM,EAAE;UACN,GAAGnN,SAAS,CAACmN,MAAM;UACnB,GAAGA;QACL;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAAC7W,KAAK,CAAC2G,MAAM,IAAI3G,KAAK,CAAC2G,MAAM,KAAKuP,UAAU,IAAI,CAACtY,EAAE,CAAC+D,GAAG,CAACuU,UAAU,EAAES,aAAa,EAAElC,YAAY,CAAC,EAAE;QACpGyB,UAAU,GAAGS,aAAa;QAC1B,MAAMI,QAAQ,GAAGJ,aAAa,YAAY3jB,KAAK,CAACgkB,MAAM;QACtD,MAAMrQ,MAAM,GAAGoQ,QAAQ,GAAGJ,aAAa,GAAGF,YAAY,GAAG,IAAIzjB,KAAK,CAACikB,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAIjkB,KAAK,CAACkkB,iBAAiB,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;QAC5J,IAAI,CAACH,QAAQ,EAAE;UACbpQ,MAAM,CAACoH,QAAQ,CAACoJ,CAAC,GAAG,CAAC;UACrB,IAAIR,aAAa,EAAE;YACjBxY,UAAU,CAACwI,MAAM,EAAEgQ,aAAa,CAAC;YACjC;YACA;YACA,IAAI,QAAQ,IAAIA,aAAa,IAAI,MAAM,IAAIA,aAAa,IAAI,OAAO,IAAIA,aAAa,IAAI,QAAQ,IAAIA,aAAa,IAAI,KAAK,IAAIA,aAAa,EAAE;cAC3IhQ,MAAM,CAACE,MAAM,GAAG,IAAI;cACpBF,MAAM,CAACU,sBAAsB,CAAC,CAAC;YACjC;UACF;UACA;UACA,IAAI,CAACrH,KAAK,CAAC2G,MAAM,IAAI,EAAEgQ,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACS,QAAQ,CAAC,EAAEzQ,MAAM,CAAC0Q,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjG;QACArX,KAAK,CAACL,GAAG,CAAC;UACRgH;QACF,CAAC,CAAC;;QAEF;QACA;QACA+C,SAAS,CAAC/C,MAAM,GAAGA,MAAM;MAC3B;;MAEA;MACA,IAAI,CAAC3G,KAAK,CAAC1F,KAAK,EAAE;QAChB,IAAIA,KAAK;QACT,IAAIgc,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACgB,OAAO,EAAE;UAChDhd,KAAK,GAAGgc,YAAY;QACtB,CAAC,MAAM;UACLhc,KAAK,GAAG,IAAItH,KAAK,CAACukB,KAAK,CAAC,CAAC;UACzB,IAAIjB,YAAY,EAAEnY,UAAU,CAAC7D,KAAK,EAAEgc,YAAY,CAAC;QACnD;QACAtW,KAAK,CAACL,GAAG,CAAC;UACRrF,KAAK,EAAE/E,OAAO,CAAC+E,KAAK;QACtB,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI,CAAC0F,KAAK,CAACiP,EAAE,EAAE;QACb,IAAIuI,MAAM;QACV;QACA,MAAMC,aAAa,GAAGA,CAACvI,SAAS,EAAEgD,KAAK,KAAK;UAC1C,MAAMlS,KAAK,GAAGuI,KAAK,CAAClP,QAAQ,CAAC,CAAC;UAC9B,IAAI2G,KAAK,CAAC0P,SAAS,KAAK,OAAO,EAAE;UACjC5B,OAAO,CAACoB,SAAS,EAAE,IAAI,EAAElP,KAAK,EAAEkS,KAAK,CAAC;QACxC,CAAC;;QAED;QACA,MAAMwF,mBAAmB,GAAGA,CAAA,KAAM;UAChC,MAAM1X,KAAK,GAAGuI,KAAK,CAAClP,QAAQ,CAAC,CAAC;UAC9B2G,KAAK,CAAC+F,EAAE,CAACkJ,EAAE,CAACnF,OAAO,GAAG9J,KAAK,CAAC+F,EAAE,CAACkJ,EAAE,CAAC2D,YAAY;UAC9C5S,KAAK,CAAC+F,EAAE,CAACkJ,EAAE,CAAC0I,gBAAgB,CAAC3X,KAAK,CAAC+F,EAAE,CAACkJ,EAAE,CAAC2D,YAAY,GAAG6E,aAAa,GAAG,IAAI,CAAC;UAC7E,IAAI,CAACzX,KAAK,CAAC+F,EAAE,CAACkJ,EAAE,CAAC2D,YAAY,EAAEpM,UAAU,CAACxG,KAAK,CAAC;QAClD,CAAC;;QAED;QACA,MAAMiP,EAAE,GAAG;UACT2I,OAAOA,CAAA,EAAG;YACR,MAAM7R,EAAE,GAAGwC,KAAK,CAAClP,QAAQ,CAAC,CAAC,CAAC0M,EAAE;YAC9BA,EAAE,CAACkJ,EAAE,CAAC4I,gBAAgB,CAAC,cAAc,EAAEH,mBAAmB,CAAC;YAC3D3R,EAAE,CAACkJ,EAAE,CAAC4I,gBAAgB,CAAC,YAAY,EAAEH,mBAAmB,CAAC;UAC3D,CAAC;UACDI,UAAUA,CAAA,EAAG;YACX,MAAM/R,EAAE,GAAGwC,KAAK,CAAClP,QAAQ,CAAC,CAAC,CAAC0M,EAAE;YAC9BA,EAAE,CAACkJ,EAAE,CAAC8I,mBAAmB,CAAC,cAAc,EAAEL,mBAAmB,CAAC;YAC9D3R,EAAE,CAACkJ,EAAE,CAAC8I,mBAAmB,CAAC,YAAY,EAAEL,mBAAmB,CAAC;UAC9D;QACF,CAAC;;QAED;QACA,IAAI,QAAQ,CAACF,MAAM,GAAGzR,EAAE,CAACkJ,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuI,MAAM,CAACK,gBAAgB,CAAC,KAAK,UAAU,EAAE5I,EAAE,CAAC2I,OAAO,CAAC,CAAC;QACrG5X,KAAK,CAACL,GAAG,CAAC;UACRsP;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIlJ,EAAE,CAACiS,SAAS,EAAE;QAChB,MAAMC,UAAU,GAAGlS,EAAE,CAACiS,SAAS,CAAClO,OAAO;QACvC,MAAMoO,OAAO,GAAGnS,EAAE,CAACiS,SAAS,CAACrjB,IAAI;QACjCoR,EAAE,CAACiS,SAAS,CAAClO,OAAO,GAAG,CAAC,CAAC0M,OAAO;QAChC,IAAI5Y,EAAE,CAAC6D,GAAG,CAAC+U,OAAO,CAAC,EAAE;UACnBzQ,EAAE,CAACiS,SAAS,CAACrjB,IAAI,GAAG3B,KAAK,CAACmlB,gBAAgB;QAC5C,CAAC,MAAM,IAAIva,EAAE,CAAC2D,GAAG,CAACiV,OAAO,CAAC,EAAE;UAC1B,IAAI4B,cAAc;UAClB,MAAMC,KAAK,GAAG;YACZC,KAAK,EAAEtlB,KAAK,CAACulB,cAAc;YAC3BC,UAAU,EAAExlB,KAAK,CAACylB,YAAY;YAC9BC,IAAI,EAAE1lB,KAAK,CAACmlB,gBAAgB;YAC5BQ,QAAQ,EAAE3lB,KAAK,CAAC4lB;UAClB,CAAC;UACD7S,EAAE,CAACiS,SAAS,CAACrjB,IAAI,GAAG,CAACyjB,cAAc,GAAGC,KAAK,CAAC7B,OAAO,CAAC,KAAK,IAAI,GAAG4B,cAAc,GAAGplB,KAAK,CAACmlB,gBAAgB;QACzG,CAAC,MAAM,IAAIva,EAAE,CAACiB,GAAG,CAAC2X,OAAO,CAAC,EAAE;UAC1BxiB,MAAM,CAACM,MAAM,CAACyR,EAAE,CAACiS,SAAS,EAAExB,OAAO,CAAC;QACtC;QACA,IAAIyB,UAAU,KAAKlS,EAAE,CAACiS,SAAS,CAAClO,OAAO,IAAIoO,OAAO,KAAKnS,EAAE,CAACiS,SAAS,CAACrjB,IAAI,EAAEoR,EAAE,CAACiS,SAAS,CAACa,WAAW,GAAG,IAAI;MAC3G;;MAEA;MACA;MACA,MAAMpa,eAAe,GAAGF,kBAAkB,CAAC,CAAC;MAC5C,IAAIE,eAAe,EAAE;QACnB,IAAI,SAAS,IAAIA,eAAe,EAAEA,eAAe,CAACqL,OAAO,GAAG,CAACsF,MAAM,CAAC,KAAK,IAAI,YAAY,IAAI3Q,eAAe,EAAEA,eAAe,CAACqa,UAAU,GAAG1J,MAAM;MACnJ;MACA,IAAI,CAAC6G,UAAU,EAAE;QACf;QACA,MAAM8C,cAAc,GAAG,IAAI;QAC3B,MAAMxU,YAAY,GAAG,IAAI;QACzBpG,UAAU,CAAC4H,EAAE,EAAE;UACbI,cAAc,EAAEZ,MAAM,GAAGwT,cAAc,GAAGxU,YAAY;UACtDyU,WAAW,EAAE3J,IAAI,GAAGrc,KAAK,CAACimB,aAAa,GAAGjmB,KAAK,CAACkmB;QAClD,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIlZ,KAAK,CAACoP,MAAM,KAAKA,MAAM,EAAEpP,KAAK,CAACL,GAAG,CAAC,OAAO;QAC5CyP;MACF,CAAC,CAAC,CAAC;MACH,IAAIpP,KAAK,CAACuF,MAAM,KAAKA,MAAM,EAAEvF,KAAK,CAACL,GAAG,CAAC,OAAO;QAC5C4F;MACF,CAAC,CAAC,CAAC;MACH,IAAIvF,KAAK,CAACqP,IAAI,KAAKA,IAAI,EAAErP,KAAK,CAACL,GAAG,CAAC,OAAO;QACxC0P;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,IAAI+G,QAAQ,IAAI,CAACxY,EAAE,CAACC,GAAG,CAACuY,QAAQ,CAAC,IAAI,CAAC1I,UAAU,CAAC0I,QAAQ,CAAC,IAAI,CAACxY,EAAE,CAAC+D,GAAG,CAACyU,QAAQ,EAAErQ,EAAE,EAAE0O,YAAY,CAAC,EAAEtW,UAAU,CAAC4H,EAAE,EAAEqQ,QAAQ,CAAC;MAC3H;MACA,IAAIzM,MAAM,IAAI,CAAC3J,KAAK,CAAC2J,MAAM,CAAC3O,QAAQ,EAAEgF,KAAK,CAACL,GAAG,CAAC;QAC9CgK,MAAM,EAAEA,MAAM,CAACpB,KAAK;MACtB,CAAC,CAAC;MACF;MACA,MAAM3B,IAAI,GAAGqO,kBAAkB,CAACN,MAAM,EAAE0B,SAAS,CAAC;MAClD,IAAI,CAACzY,EAAE,CAAC+D,GAAG,CAACiF,IAAI,EAAE5G,KAAK,CAAC4G,IAAI,EAAE6N,YAAY,CAAC,EAAE;QAC3CzU,KAAK,CAACiQ,OAAO,CAACrJ,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACM,MAAM,EAAEN,IAAI,CAACiJ,WAAW,EAAEjJ,IAAI,CAACK,GAAG,EAAEL,IAAI,CAACE,IAAI,CAAC;MAC/E;MACA;MACA,IAAIlG,GAAG,IAAIZ,KAAK,CAAC8P,QAAQ,CAAClP,GAAG,KAAKD,YAAY,CAACC,GAAG,CAAC,EAAEZ,KAAK,CAACkQ,MAAM,CAACtP,GAAG,CAAC;MACtE;MACA,IAAIZ,KAAK,CAAC0P,SAAS,KAAKA,SAAS,EAAE1P,KAAK,CAACoQ,YAAY,CAACV,SAAS,CAAC;MAChE;MACA,IAAI,CAAC1P,KAAK,CAAC2M,eAAe,EAAE3M,KAAK,CAACL,GAAG,CAAC;QACpCgN;MACF,CAAC,CAAC;MACF;MACA,IAAIhP,WAAW,IAAI,CAACC,EAAE,CAAC+D,GAAG,CAAChE,WAAW,EAAEqC,KAAK,CAACrC,WAAW,EAAE8W,YAAY,CAAC,EAAEzU,KAAK,CAACL,GAAG,CAACK,KAAK,KAAK;QAC5FrC,WAAW,EAAE;UACX,GAAGqC,KAAK,CAACrC,WAAW;UACpB,GAAGA;QACL;MACF,CAAC,CAAC,CAAC;;MAEH;MACAqY,SAAS,GAAGO,iBAAiB;MAC7BN,UAAU,GAAG,IAAI;MACjB,OAAO,IAAI;IACb,CAAC;IACD7V,MAAMA,CAACjJ,QAAQ,EAAE;MACf;MACA,IAAI,CAAC8e,UAAU,EAAE,IAAI,CAACE,SAAS,CAAC,CAAC;MACjCtc,UAAU,CAACsf,eAAe,CAAE,aAAazlB,GAAG,CAAC0lB,QAAQ,EAAE;QACrD7Q,KAAK,EAAEA,KAAK;QACZpR,QAAQ,EAAEA,QAAQ;QAClB6e,SAAS,EAAEA,SAAS;QACpBqD,WAAW,EAAE1E;MACf,CAAC,CAAC,EAAE7b,KAAK,EAAE,IAAI,EAAE,MAAMzD,SAAS,CAAC;MACjC,OAAOkT,KAAK;IACd,CAAC;IACD+Q,OAAOA,CAAA,EAAG;MACRC,sBAAsB,CAAC5E,MAAM,CAAC;IAChC;EACF,CAAC;AACH;AACA,SAASvU,MAAMA,CAACjJ,QAAQ,EAAEwd,MAAM,EAAE6E,MAAM,EAAE;EACxC7D,OAAO,CAACC,IAAI,CAAC,wEAAwE,CAAC;EACtF,MAAM7gB,IAAI,GAAGwgB,UAAU,CAACZ,MAAM,CAAC;EAC/B5f,IAAI,CAACohB,SAAS,CAACqD,MAAM,CAAC;EACtB,OAAOzkB,IAAI,CAACqL,MAAM,CAACjJ,QAAQ,CAAC;AAC9B;AACA,SAASiiB,QAAQA,CAAC;EAChB7Q,KAAK;EACLpR,QAAQ;EACR6e,SAAS;EACTqD;AACF,CAAC,EAAE;EACDta,yBAAyB,CAAC,MAAM;IAC9B,MAAMiB,KAAK,GAAGuI,KAAK,CAAClP,QAAQ,CAAC,CAAC;IAC9B;IACA2G,KAAK,CAACL,GAAG,CAACK,KAAK,KAAK;MAClB1G,QAAQ,EAAE;QACR,GAAG0G,KAAK,CAAC1G,QAAQ;QACjBkX,MAAM,EAAE;MACV;IACF,CAAC,CAAC,CAAC;IACH;IACA,IAAIwF,SAAS,EAAEA,SAAS,CAAChW,KAAK,CAAC;IAC/B;IACA;IACA,IAAI,CAACuI,KAAK,CAAClP,QAAQ,CAAC,CAAC,CAACsQ,MAAM,CAACqF,SAAS,EAAEhP,KAAK,CAAC2J,MAAM,CAACiO,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG5X,KAAK,CAAC2J,MAAM,CAACiO,OAAO,CAACyB,WAAW,CAAC;IACjH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa3lB,GAAG,CAACia,OAAO,CAACyL,QAAQ,EAAE;IACxCxd,KAAK,EAAE2M,KAAK;IACZpR,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACA,SAASoiB,sBAAsBA,CAAC5E,MAAM,EAAElc,QAAQ,EAAE;EAChD,MAAM1D,IAAI,GAAGud,KAAK,CAACvN,GAAG,CAAC4P,MAAM,CAAC;EAC9B,MAAM7b,KAAK,GAAG/D,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC+D,KAAK;EAChD,IAAIA,KAAK,EAAE;IACT,MAAMkH,KAAK,GAAGjL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACwT,KAAK,CAAClP,QAAQ,CAAC,CAAC;IAC3D,IAAI2G,KAAK,EAAEA,KAAK,CAAC1G,QAAQ,CAACkX,MAAM,GAAG,KAAK;IACxC3W,UAAU,CAACsf,eAAe,CAAC,IAAI,EAAErgB,KAAK,EAAE,IAAI,EAAE,MAAM;MAClD,IAAIkH,KAAK,EAAE;QACThC,UAAU,CAAC,MAAM;UACf,IAAI;YACF,IAAIyb,SAAS,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,UAAU;YAC5D5Z,KAAK,CAAC2J,MAAM,CAACmO,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG9X,KAAK,CAAC2J,MAAM,CAACmO,UAAU,CAAC,CAAC;YACpE,CAAC2B,SAAS,GAAGzZ,KAAK,CAAC+F,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC2T,qBAAqB,GAAGD,SAAS,CAACI,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAAChiB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGgiB,qBAAqB,CAAChiB,OAAO,CAAC,CAAC;YAC7L,CAACiiB,UAAU,GAAG3Z,KAAK,CAAC+F,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4T,UAAU,CAACG,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGH,UAAU,CAACG,gBAAgB,CAAC,CAAC;YACvH,IAAI,CAACF,UAAU,GAAG5Z,KAAK,CAAC+F,EAAE,KAAK,IAAI,IAAI6T,UAAU,CAAC3K,EAAE,EAAEjP,KAAK,CAACiP,EAAE,CAAC6I,UAAU,CAAC,CAAC;YAC3EpgB,OAAO,CAACsI,KAAK,CAAC;YACdsS,KAAK,CAACjK,MAAM,CAACsM,MAAM,CAAC;YACpB,IAAIlc,QAAQ,EAAEA,QAAQ,CAACkc,MAAM,CAAC;UAChC,CAAC,CAAC,OAAOjc,CAAC,EAAE;YACV;UAAA;QAEJ,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;EACJ;AACF;AACA,SAASqhB,YAAYA,CAAC5iB,QAAQ,EAAEkD,SAAS,EAAE2F,KAAK,EAAE;EAChD,OAAO,aAAatM,GAAG,CAACsmB,MAAM,EAAE;IAC9B7iB,QAAQ,EAAEA,QAAQ;IAClBkD,SAAS,EAAEA,SAAS;IACpB2F,KAAK,EAAEA;EACT,CAAC,EAAE3F,SAAS,CAACqN,IAAI,CAAC;AACpB;AACA,SAASsS,MAAMA,CAAC;EACdha,KAAK,GAAG,CAAC,CAAC;EACV7I,QAAQ;EACRkD;AACF,CAAC,EAAE;EACD;AACF;AACA;AACA;AACA;EACE,MAAM;IACJsP,MAAM;IACN/C,IAAI;IACJ,GAAGqT;EACL,CAAC,GAAGja,KAAK;EACT,MAAMoB,YAAY,GAAG4R,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACtJ,SAAS,CAAC,GAAGzW,KAAK,CAACinB,QAAQ,CAAC,MAAM,IAAIlnB,KAAK,CAAC4jB,SAAS,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC1L,OAAO,CAAC,GAAGjY,KAAK,CAACinB,QAAQ,CAAC,MAAM,IAAIlnB,KAAK,CAAC+b,OAAO,CAAC,CAAC,CAAC;EAC3D,MAAMoL,MAAM,GAAGlnB,KAAK,CAACmnB,WAAW,CAAC,CAAChhB,SAAS,EAAEihB,WAAW,KAAK;IAC3D,MAAMhR,SAAS,GAAG;MAChB,GAAGjQ;IACL,CAAC,CAAC,CAAC;;IAEH;IACA;IACA;IACApF,MAAM,CAACkO,IAAI,CAAC9I,SAAS,CAAC,CAACzB,OAAO,CAACiL,GAAG,IAAI;MACpC;MACA;MACA6K,WAAW,CAAC5K,QAAQ,CAACD,GAAG,CAAC;MACzB;MACA;MACAxJ,SAAS,CAACwJ,GAAG,CAAC,KAAKyX,WAAW,CAACzX,GAAG,CAAC,IAAIyX,WAAW,CAACzX,GAAG,CAAC,EAAE;QACvD,OAAOyG,SAAS,CAACzG,GAAG,CAAC;MACvB;IACF,CAAC,CAAC;IACF,IAAIkN,QAAQ,GAAGza,SAAS;IACxB,IAAIglB,WAAW,IAAIzT,IAAI,EAAE;MACvB,MAAMD,MAAM,GAAG0T,WAAW,CAAC1T,MAAM;MACjC;MACAmJ,QAAQ,GAAG1W,SAAS,CAAC0W,QAAQ,CAAC5B,kBAAkB,CAACvH,MAAM,EAAE,IAAI3T,KAAK,CAACoY,OAAO,CAAC,CAAC,EAAExE,IAAI,CAAC;MACnF;MACA,IAAID,MAAM,KAAKvN,SAAS,CAACuN,MAAM,EAAED,YAAY,CAACC,MAAM,EAAEC,IAAI,CAAC;IAC7D;IACA,OAAO;MACL;MACA,GAAGyC,SAAS;MACZ;MACA/O,KAAK,EAAED,SAAS;MAChBqP,SAAS;MACTwB,OAAO;MACPuE,KAAK,EAAEvE,OAAO;MACd;MACA9J,YAAY;MACZ;MACAuI,MAAM,EAAE;QACN,GAAGvQ,SAAS,CAACuQ,MAAM;QACnB,IAAI0Q,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC1Q,MAAM,CAAC;QACtD,GAAGA;MACL,CAAC;MACD/C,IAAI,EAAE;QACJ,GAAGxN,SAAS,CAACwN,IAAI;QACjB,GAAGA;MACL,CAAC;MACDkJ,QAAQ,EAAE;QACR,GAAG1W,SAAS,CAAC0W,QAAQ;QACrB,GAAGA;MACL,CAAC;MACD,GAAGmK;IACL,CAAC;EACH,CAAC;EACD;EACA,CAACja,KAAK,CAAC,CAAC;EACR,MAAM,CAACsa,cAAc,CAAC,GAAGrnB,KAAK,CAACinB,QAAQ,CAAC,MAAM;IAC5C;IACA,MAAMK,aAAa,GAAGnZ,YAAY,CAAC/H,QAAQ,CAAC,CAAC;IAC7C,MAAMkP,KAAK,GAAGjV,MAAM,CAAC,CAACqM,GAAG,EAAEoF,GAAG,MAAM;MAClC,GAAGwV,aAAa;MAChBjgB,KAAK,EAAED,SAAS;MAChBqP,SAAS;MACTwB,OAAO;MACPuE,KAAK,EAAEvE,OAAO;MACd9J,YAAY;MACZuI,MAAM,EAAE;QACN,GAAG4Q,aAAa,CAAC5Q,MAAM;QACvB,GAAGA;MACL,CAAC;MACD/C,IAAI,EAAE;QACJ,GAAG2T,aAAa,CAAC3T,IAAI;QACrB,GAAGA;MACL,CAAC;MACD,GAAGqT,IAAI;MACP;MACAta,GAAG;MACHoF,GAAG;MACH;MACAiL,SAAS,EAAErG,MAAM,IAAIhK,GAAG,CAACK,KAAK,KAAK;QACjC,GAAGA,KAAK;QACR2J,MAAM,EAAE;UACN,GAAG3J,KAAK,CAAC2J,MAAM;UACf,GAAGA;QACL;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,OAAOpB,KAAK;EACd,CAAC,CAAC;EACFtV,KAAK,CAACqM,SAAS,CAAC,MAAM;IACpB;IACA,MAAMkb,KAAK,GAAGpZ,YAAY,CAACuP,SAAS,CAAC8J,IAAI,IAAIH,cAAc,CAACI,QAAQ,CAAC1a,KAAK,IAAIma,MAAM,CAACM,IAAI,EAAEza,KAAK,CAAC,CAAC,CAAC;IACnG,OAAO,MAAM;MACXwa,KAAK,CAAC,CAAC;IACT,CAAC;IACD;EACF,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EACZlnB,KAAK,CAACqM,SAAS,CAAC,MAAM;IACpBgb,cAAc,CAACI,QAAQ,CAACL,WAAW,IAAIF,MAAM,CAAC/Y,YAAY,CAAC/H,QAAQ,CAAC,CAAC,EAAEghB,WAAW,CAAC,CAAC;IACpF;EACF,CAAC,EAAE,CAACF,MAAM,CAAC,CAAC;EACZlnB,KAAK,CAACqM,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXgb,cAAc,CAACK,OAAO,CAAC,CAAC;IAC1B,CAAC;IACD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAajnB,GAAG,CAACC,QAAQ,EAAE;IAChCwD,QAAQ,EAAE0C,UAAU,CAACkgB,YAAY,CAAE,aAAarmB,GAAG,CAACia,OAAO,CAACyL,QAAQ,EAAE;MACpExd,KAAK,EAAE0e,cAAc;MACrBnjB,QAAQ,EAAEA;IACZ,CAAC,CAAC,EAAEmjB,cAAc,EAAE,IAAI;EAC1B,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,SAASA,CAACpb,EAAE,EAAE;EACrB;EACA,OAAO3F,UAAU,CAAC+gB,SAAS,CAACpb,EAAE,EAAEnK,SAAS,CAAC;AAC5C;AACAwE,UAAU,CAACghB,kBAAkB,CAAC;EAC5BC,UAAU,EAAE5W,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;EACzD2W,mBAAmB,EAAE,oBAAoB;EACzCC,OAAO,EAAE/nB,KAAK,CAAC+nB;AACjB,CAAC,CAAC;AACF,MAAMC,GAAG,GAAGhoB,KAAK,CAACioB,YAAY;AAE9B,MAAMC,UAAU,GAAG;EACjBC,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;EACzBC,aAAa,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC;EACrCC,aAAa,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;EAClCC,OAAO,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC;EACxBC,aAAa,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC;EACpCC,WAAW,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC;EAChChP,cAAc,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC;EACtCc,aAAa,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC;EACpCmO,eAAe,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC;EACxCC,oBAAoB,EAAE,CAAC,oBAAoB,EAAE,IAAI;AACnD,CAAC;;AAED;AACA,SAASC,mBAAmBA,CAACrT,KAAK,EAAE;EAClC,MAAM;IACJqE;EACF,CAAC,GAAGjE,YAAY,CAACJ,KAAK,CAAC;EACvB,OAAO;IACLgC,QAAQ,EAAE,CAAC;IACXT,OAAO,EAAE,IAAI;IACbF,OAAOA,CAACpC,KAAK,EAAExH,KAAK,EAAEuD,QAAQ,EAAE;MAC9B;MACA;MACAvD,KAAK,CAACkL,OAAO,CAACvL,GAAG,CAAC6H,KAAK,CAACsB,OAAO,GAAG9I,KAAK,CAAC4G,IAAI,CAACG,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAES,KAAK,CAACyB,OAAO,GAAGjJ,KAAK,CAAC4G,IAAI,CAACM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACzGlH,KAAK,CAAC0J,SAAS,CAACmS,aAAa,CAAC7b,KAAK,CAACkL,OAAO,EAAElL,KAAK,CAAC2G,MAAM,CAAC;IAC5D,CAAC;IACDqI,SAAS,EAAE3Z,SAAS;IACpB2F,QAAQ,EAAEhH,MAAM,CAACkO,IAAI,CAACiZ,UAAU,CAAC,CAACjY,MAAM,CAAC,CAACC,GAAG,EAAEP,GAAG,MAAM;MACtD,GAAGO,GAAG;MACN,CAACP,GAAG,GAAGgK,aAAa,CAAChK,GAAG;IAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACPkZ,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIC,mBAAmB;MACvB,MAAM;QACJpS,MAAM;QACNrQ;MACF,CAAC,GAAGiP,KAAK,CAAClP,QAAQ,CAAC,CAAC;MACpB,IAAI,CAAC0iB,mBAAmB,GAAGziB,QAAQ,CAACyT,SAAS,KAAK,IAAI,IAAIgP,mBAAmB,CAACpiB,OAAO,IAAIgQ,MAAM,CAAC3O,QAAQ,EAAE2O,MAAM,CAAC3O,QAAQ,CAACuS,aAAa,CAACjU,QAAQ,CAACyT,SAAS,CAACpT,OAAO,CAAC;IACrK,CAAC;IACDie,OAAO,EAAEniB,MAAM,IAAI;MACjB,IAAIumB,gBAAgB;MACpB,MAAM;QACJrc,GAAG;QACHgK;MACF,CAAC,GAAGpB,KAAK,CAAClP,QAAQ,CAAC,CAAC;MACpBsQ,MAAM,CAACmO,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGnO,MAAM,CAACmO,UAAU,CAAC,CAAC;MACxDnY,GAAG,CAACK,KAAK,KAAK;QACZ2J,MAAM,EAAE;UACN,GAAG3J,KAAK,CAAC2J,MAAM;UACfqF,SAAS,EAAEvZ;QACb;MACF,CAAC,CAAC,CAAC;MACHzB,MAAM,CAAC8O,OAAO,CAAC,CAACkZ,gBAAgB,GAAGrS,MAAM,CAAC3O,QAAQ,KAAK,IAAI,GAAGghB,gBAAgB,GAAG,EAAE,CAAC,CAACrkB,OAAO,CAAC,CAAC,CAAC3C,IAAI,EAAEwS,KAAK,CAAC,KAAK;QAC9G,MAAM,CAACyU,SAAS,EAAEC,OAAO,CAAC,GAAGf,UAAU,CAACnmB,IAAI,CAAC;QAC7CS,MAAM,CAACoiB,gBAAgB,CAACoE,SAAS,EAAEzU,KAAK,EAAE;UACxC0U;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACDpE,UAAU,EAAEA,CAAA,KAAM;MAChB,MAAM;QACJnY,GAAG;QACHgK;MACF,CAAC,GAAGpB,KAAK,CAAClP,QAAQ,CAAC,CAAC;MACpB,IAAIsQ,MAAM,CAACqF,SAAS,EAAE;QACpB,IAAImN,iBAAiB;QACrBnoB,MAAM,CAAC8O,OAAO,CAAC,CAACqZ,iBAAiB,GAAGxS,MAAM,CAAC3O,QAAQ,KAAK,IAAI,GAAGmhB,iBAAiB,GAAG,EAAE,CAAC,CAACxkB,OAAO,CAAC,CAAC,CAAC3C,IAAI,EAAEwS,KAAK,CAAC,KAAK;UAChH,IAAImC,MAAM,IAAIA,MAAM,CAACqF,SAAS,YAAYoN,WAAW,EAAE;YACrD,MAAM,CAACH,SAAS,CAAC,GAAGd,UAAU,CAACnmB,IAAI,CAAC;YACpC2U,MAAM,CAACqF,SAAS,CAAC+I,mBAAmB,CAACkE,SAAS,EAAEzU,KAAK,CAAC;UACxD;QACF,CAAC,CAAC;QACF7H,GAAG,CAACK,KAAK,KAAK;UACZ2J,MAAM,EAAE;YACN,GAAG3J,KAAK,CAAC2J,MAAM;YACfqF,SAAS,EAAE3Z;UACb;QACF,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;AACH;AAEA,SAAS0d,iBAAiB,IAAIsJ,CAAC,EAAE3c,KAAK,IAAI4c,CAAC,EAAEtJ,QAAQ,IAAIuJ,CAAC,EAAErJ,QAAQ,IAAIsJ,CAAC,EAAE3c,aAAa,IAAI4c,CAAC,EAAEpJ,QAAQ,IAAIqJ,CAAC,EAAEnJ,QAAQ,IAAIoJ,CAAC,EAAErI,SAAS,IAAIsI,CAAC,EAAE7d,yBAAyB,IAAIsC,CAAC,EAAEkU,UAAU,IAAI3T,CAAC,EAAEga,mBAAmB,IAAIiB,CAAC,EAAEtD,sBAAsB,IAAIuD,CAAC,EAAE1oB,MAAM,IAAIsE,CAAC,EAAEiQ,YAAY,IAAIoU,CAAC,EAAEpP,OAAO,IAAIqP,CAAC,EAAEjD,YAAY,IAAIrL,CAAC,EAAE9P,KAAK,IAAIqD,CAAC,EAAEpI,UAAU,IAAIojB,CAAC,EAAE9e,UAAU,IAAI+e,CAAC,EAAExlB,OAAO,IAAIylB,CAAC,EAAE3W,UAAU,IAAI4W,CAAC,EAAEtP,OAAO,IAAIuP,CAAC,EAAE3L,SAAS,IAAIlJ,CAAC,EAAEmJ,cAAc,IAAIlP,CAAC,EAAEmP,OAAO,IAAI0L,CAAC,EAAEld,MAAM,IAAImd,CAAC,EAAExL,kBAAkB,IAAInB,CAAC,EAAE7c,UAAU,IAAIypB,CAAC,EAAEje,kBAAkB,IAAIke,CAAC,EAAE7C,SAAS,IAAI8C,CAAC,EAAExc,YAAY,IAAI0N,CAAC,EAAEqM,GAAG,IAAIjjB,CAAC,EAAEmK,UAAU,IAAIkJ,CAAC,EAAEiH,KAAK,IAAI6E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}