{"ast": null, "code": "import { Loader, <PERSON>ader<PERSON>tils, FileLoader, Vector3, Quaternion, Matrix4, MeshBasicMaterial, Scene, TextureLoader, Euler, MathUtils, AnimationClip, VectorKeyframeTrack, QuaternionKeyframeTrack, MeshLambertMaterial, MeshPhongMaterial, Vector2, DoubleSide, FrontSide, PerspectiveCamera, OrthographicCamera, Color, AmbientLight, SpotLight, PointLight, DirectionalLight, BufferGeometry, Float32BufferAttribute, Group, Bone, LineBasicMaterial, SkinnedMesh, Mesh, Line, LineSegments, RepeatWrapping, ClampToEdgeWrapping, Skeleton } from \"three\";\nimport { TGALoader } from \"./TGALoader.js\";\nimport { UV1 } from \"../_polyfill/uv1.js\";\nclass ColladaLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const path = scope.path === \"\" ? LoaderUtils.extractUrlBase(url) : scope.path;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text, path));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(text, path) {\n    function getElementsByTagName(xml2, name) {\n      const array = [];\n      const childNodes = xml2.childNodes;\n      for (let i = 0, l = childNodes.length; i < l; i++) {\n        const child = childNodes[i];\n        if (child.nodeName === name) {\n          array.push(child);\n        }\n      }\n      return array;\n    }\n    function parseStrings(text2) {\n      if (text2.length === 0) return [];\n      const parts = text2.trim().split(/\\s+/);\n      const array = new Array(parts.length);\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parts[i];\n      }\n      return array;\n    }\n    function parseFloats(text2) {\n      if (text2.length === 0) return [];\n      const parts = text2.trim().split(/\\s+/);\n      const array = new Array(parts.length);\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parseFloat(parts[i]);\n      }\n      return array;\n    }\n    function parseInts(text2) {\n      if (text2.length === 0) return [];\n      const parts = text2.trim().split(/\\s+/);\n      const array = new Array(parts.length);\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parseInt(parts[i]);\n      }\n      return array;\n    }\n    function parseId(text2) {\n      return text2.substring(1);\n    }\n    function generateId() {\n      return \"three_default_\" + count++;\n    }\n    function isEmpty(object) {\n      return Object.keys(object).length === 0;\n    }\n    function parseAsset(xml2) {\n      return {\n        unit: parseAssetUnit(getElementsByTagName(xml2, \"unit\")[0]),\n        upAxis: parseAssetUpAxis(getElementsByTagName(xml2, \"up_axis\")[0])\n      };\n    }\n    function parseAssetUnit(xml2) {\n      if (xml2 !== void 0 && xml2.hasAttribute(\"meter\") === true) {\n        return parseFloat(xml2.getAttribute(\"meter\"));\n      } else {\n        return 1;\n      }\n    }\n    function parseAssetUpAxis(xml2) {\n      return xml2 !== void 0 ? xml2.textContent : \"Y_UP\";\n    }\n    function parseLibrary(xml2, libraryName, nodeName, parser) {\n      const library2 = getElementsByTagName(xml2, libraryName)[0];\n      if (library2 !== void 0) {\n        const elements = getElementsByTagName(library2, nodeName);\n        for (let i = 0; i < elements.length; i++) {\n          parser(elements[i]);\n        }\n      }\n    }\n    function buildLibrary(data, builder) {\n      for (const name in data) {\n        const object = data[name];\n        object.build = builder(data[name]);\n      }\n    }\n    function getBuild(data, builder) {\n      if (data.build !== void 0) return data.build;\n      data.build = builder(data);\n      return data.build;\n    }\n    function parseAnimation(xml2) {\n      const data = {\n        sources: {},\n        samplers: {},\n        channels: {}\n      };\n      let hasChildren = false;\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        let id;\n        switch (child.nodeName) {\n          case \"source\":\n            id = child.getAttribute(\"id\");\n            data.sources[id] = parseSource(child);\n            break;\n          case \"sampler\":\n            id = child.getAttribute(\"id\");\n            data.samplers[id] = parseAnimationSampler(child);\n            break;\n          case \"channel\":\n            id = child.getAttribute(\"target\");\n            data.channels[id] = parseAnimationChannel(child);\n            break;\n          case \"animation\":\n            parseAnimation(child);\n            hasChildren = true;\n            break;\n          default:\n            console.log(child);\n        }\n      }\n      if (hasChildren === false) {\n        library.animations[xml2.getAttribute(\"id\") || MathUtils.generateUUID()] = data;\n      }\n    }\n    function parseAnimationSampler(xml2) {\n      const data = {\n        inputs: {}\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"input\":\n            const id = parseId(child.getAttribute(\"source\"));\n            const semantic = child.getAttribute(\"semantic\");\n            data.inputs[semantic] = id;\n            break;\n        }\n      }\n      return data;\n    }\n    function parseAnimationChannel(xml2) {\n      const data = {};\n      const target = xml2.getAttribute(\"target\");\n      let parts = target.split(\"/\");\n      const id = parts.shift();\n      let sid = parts.shift();\n      const arraySyntax = sid.indexOf(\"(\") !== -1;\n      const memberSyntax = sid.indexOf(\".\") !== -1;\n      if (memberSyntax) {\n        parts = sid.split(\".\");\n        sid = parts.shift();\n        data.member = parts.shift();\n      } else if (arraySyntax) {\n        const indices = sid.split(\"(\");\n        sid = indices.shift();\n        for (let i = 0; i < indices.length; i++) {\n          indices[i] = parseInt(indices[i].replace(/\\)/, \"\"));\n        }\n        data.indices = indices;\n      }\n      data.id = id;\n      data.sid = sid;\n      data.arraySyntax = arraySyntax;\n      data.memberSyntax = memberSyntax;\n      data.sampler = parseId(xml2.getAttribute(\"source\"));\n      return data;\n    }\n    function buildAnimation(data) {\n      const tracks = [];\n      const channels = data.channels;\n      const samplers = data.samplers;\n      const sources = data.sources;\n      for (const target in channels) {\n        if (channels.hasOwnProperty(target)) {\n          const channel = channels[target];\n          const sampler = samplers[channel.sampler];\n          const inputId = sampler.inputs.INPUT;\n          const outputId = sampler.inputs.OUTPUT;\n          const inputSource = sources[inputId];\n          const outputSource = sources[outputId];\n          const animation = buildAnimationChannel(channel, inputSource, outputSource);\n          createKeyframeTracks(animation, tracks);\n        }\n      }\n      return tracks;\n    }\n    function getAnimation(id) {\n      return getBuild(library.animations[id], buildAnimation);\n    }\n    function buildAnimationChannel(channel, inputSource, outputSource) {\n      const node = library.nodes[channel.id];\n      const object3D = getNode(node.id);\n      const transform = node.transforms[channel.sid];\n      const defaultMatrix = node.matrix.clone().transpose();\n      let time, stride;\n      let i, il, j, jl;\n      const data = {};\n      switch (transform) {\n        case \"matrix\":\n          for (i = 0, il = inputSource.array.length; i < il; i++) {\n            time = inputSource.array[i];\n            stride = i * outputSource.stride;\n            if (data[time] === void 0) data[time] = {};\n            if (channel.arraySyntax === true) {\n              const value = outputSource.array[stride];\n              const index = channel.indices[0] + 4 * channel.indices[1];\n              data[time][index] = value;\n            } else {\n              for (j = 0, jl = outputSource.stride; j < jl; j++) {\n                data[time][j] = outputSource.array[stride + j];\n              }\n            }\n          }\n          break;\n        case \"translate\":\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform);\n          break;\n        case \"rotate\":\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform);\n          break;\n        case \"scale\":\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform);\n          break;\n      }\n      const keyframes = prepareAnimationData(data, defaultMatrix);\n      const animation = {\n        name: object3D.uuid,\n        keyframes\n      };\n      return animation;\n    }\n    function prepareAnimationData(data, defaultMatrix) {\n      const keyframes = [];\n      for (const time in data) {\n        keyframes.push({\n          time: parseFloat(time),\n          value: data[time]\n        });\n      }\n      keyframes.sort(ascending);\n      for (let i = 0; i < 16; i++) {\n        transformAnimationData(keyframes, i, defaultMatrix.elements[i]);\n      }\n      return keyframes;\n      function ascending(a, b) {\n        return a.time - b.time;\n      }\n    }\n    const position = new Vector3();\n    const scale = new Vector3();\n    const quaternion = new Quaternion();\n    function createKeyframeTracks(animation, tracks) {\n      const keyframes = animation.keyframes;\n      const name = animation.name;\n      const times = [];\n      const positionData = [];\n      const quaternionData = [];\n      const scaleData = [];\n      for (let i = 0, l = keyframes.length; i < l; i++) {\n        const keyframe = keyframes[i];\n        const time = keyframe.time;\n        const value = keyframe.value;\n        matrix.fromArray(value).transpose();\n        matrix.decompose(position, quaternion, scale);\n        times.push(time);\n        positionData.push(position.x, position.y, position.z);\n        quaternionData.push(quaternion.x, quaternion.y, quaternion.z, quaternion.w);\n        scaleData.push(scale.x, scale.y, scale.z);\n      }\n      if (positionData.length > 0) tracks.push(new VectorKeyframeTrack(name + \".position\", times, positionData));\n      if (quaternionData.length > 0) {\n        tracks.push(new QuaternionKeyframeTrack(name + \".quaternion\", times, quaternionData));\n      }\n      if (scaleData.length > 0) tracks.push(new VectorKeyframeTrack(name + \".scale\", times, scaleData));\n      return tracks;\n    }\n    function transformAnimationData(keyframes, property, defaultValue) {\n      let keyframe;\n      let empty = true;\n      let i, l;\n      for (i = 0, l = keyframes.length; i < l; i++) {\n        keyframe = keyframes[i];\n        if (keyframe.value[property] === void 0) {\n          keyframe.value[property] = null;\n        } else {\n          empty = false;\n        }\n      }\n      if (empty === true) {\n        for (i = 0, l = keyframes.length; i < l; i++) {\n          keyframe = keyframes[i];\n          keyframe.value[property] = defaultValue;\n        }\n      } else {\n        createMissingKeyframes(keyframes, property);\n      }\n    }\n    function createMissingKeyframes(keyframes, property) {\n      let prev, next;\n      for (let i = 0, l = keyframes.length; i < l; i++) {\n        const keyframe = keyframes[i];\n        if (keyframe.value[property] === null) {\n          prev = getPrev(keyframes, i, property);\n          next = getNext(keyframes, i, property);\n          if (prev === null) {\n            keyframe.value[property] = next.value[property];\n            continue;\n          }\n          if (next === null) {\n            keyframe.value[property] = prev.value[property];\n            continue;\n          }\n          interpolate(keyframe, prev, next, property);\n        }\n      }\n    }\n    function getPrev(keyframes, i, property) {\n      while (i >= 0) {\n        const keyframe = keyframes[i];\n        if (keyframe.value[property] !== null) return keyframe;\n        i--;\n      }\n      return null;\n    }\n    function getNext(keyframes, i, property) {\n      while (i < keyframes.length) {\n        const keyframe = keyframes[i];\n        if (keyframe.value[property] !== null) return keyframe;\n        i++;\n      }\n      return null;\n    }\n    function interpolate(key, prev, next, property) {\n      if (next.time - prev.time === 0) {\n        key.value[property] = prev.value[property];\n        return;\n      }\n      key.value[property] = (key.time - prev.time) * (next.value[property] - prev.value[property]) / (next.time - prev.time) + prev.value[property];\n    }\n    function parseAnimationClip(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"id\") || \"default\",\n        start: parseFloat(xml2.getAttribute(\"start\") || 0),\n        end: parseFloat(xml2.getAttribute(\"end\") || 0),\n        animations: []\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"instance_animation\":\n            data.animations.push(parseId(child.getAttribute(\"url\")));\n            break;\n        }\n      }\n      library.clips[xml2.getAttribute(\"id\")] = data;\n    }\n    function buildAnimationClip(data) {\n      const tracks = [];\n      const name = data.name;\n      const duration = data.end - data.start || -1;\n      const animations2 = data.animations;\n      for (let i = 0, il = animations2.length; i < il; i++) {\n        const animationTracks = getAnimation(animations2[i]);\n        for (let j = 0, jl = animationTracks.length; j < jl; j++) {\n          tracks.push(animationTracks[j]);\n        }\n      }\n      return new AnimationClip(name, duration, tracks);\n    }\n    function getAnimationClip(id) {\n      return getBuild(library.clips[id], buildAnimationClip);\n    }\n    function parseController(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"skin\":\n            data.id = parseId(child.getAttribute(\"source\"));\n            data.skin = parseSkin(child);\n            break;\n          case \"morph\":\n            data.id = parseId(child.getAttribute(\"source\"));\n            console.warn(\"THREE.ColladaLoader: Morph target animation not supported yet.\");\n            break;\n        }\n      }\n      library.controllers[xml2.getAttribute(\"id\")] = data;\n    }\n    function parseSkin(xml2) {\n      const data = {\n        sources: {}\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"bind_shape_matrix\":\n            data.bindShapeMatrix = parseFloats(child.textContent);\n            break;\n          case \"source\":\n            const id = child.getAttribute(\"id\");\n            data.sources[id] = parseSource(child);\n            break;\n          case \"joints\":\n            data.joints = parseJoints(child);\n            break;\n          case \"vertex_weights\":\n            data.vertexWeights = parseVertexWeights(child);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseJoints(xml2) {\n      const data = {\n        inputs: {}\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"input\":\n            const semantic = child.getAttribute(\"semantic\");\n            const id = parseId(child.getAttribute(\"source\"));\n            data.inputs[semantic] = id;\n            break;\n        }\n      }\n      return data;\n    }\n    function parseVertexWeights(xml2) {\n      const data = {\n        inputs: {}\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"input\":\n            const semantic = child.getAttribute(\"semantic\");\n            const id = parseId(child.getAttribute(\"source\"));\n            const offset = parseInt(child.getAttribute(\"offset\"));\n            data.inputs[semantic] = {\n              id,\n              offset\n            };\n            break;\n          case \"vcount\":\n            data.vcount = parseInts(child.textContent);\n            break;\n          case \"v\":\n            data.v = parseInts(child.textContent);\n            break;\n        }\n      }\n      return data;\n    }\n    function buildController(data) {\n      const build = {\n        id: data.id\n      };\n      const geometry = library.geometries[build.id];\n      if (data.skin !== void 0) {\n        build.skin = buildSkin(data.skin);\n        geometry.sources.skinIndices = build.skin.indices;\n        geometry.sources.skinWeights = build.skin.weights;\n      }\n      return build;\n    }\n    function buildSkin(data) {\n      const BONE_LIMIT = 4;\n      const build = {\n        joints: [],\n        // this must be an array to preserve the joint order\n        indices: {\n          array: [],\n          stride: BONE_LIMIT\n        },\n        weights: {\n          array: [],\n          stride: BONE_LIMIT\n        }\n      };\n      const sources = data.sources;\n      const vertexWeights = data.vertexWeights;\n      const vcount = vertexWeights.vcount;\n      const v = vertexWeights.v;\n      const jointOffset = vertexWeights.inputs.JOINT.offset;\n      const weightOffset = vertexWeights.inputs.WEIGHT.offset;\n      const jointSource = data.sources[data.joints.inputs.JOINT];\n      const inverseSource = data.sources[data.joints.inputs.INV_BIND_MATRIX];\n      const weights = sources[vertexWeights.inputs.WEIGHT.id].array;\n      let stride = 0;\n      let i, j, l;\n      for (i = 0, l = vcount.length; i < l; i++) {\n        const jointCount = vcount[i];\n        const vertexSkinData = [];\n        for (j = 0; j < jointCount; j++) {\n          const skinIndex = v[stride + jointOffset];\n          const weightId = v[stride + weightOffset];\n          const skinWeight = weights[weightId];\n          vertexSkinData.push({\n            index: skinIndex,\n            weight: skinWeight\n          });\n          stride += 2;\n        }\n        vertexSkinData.sort(descending);\n        for (j = 0; j < BONE_LIMIT; j++) {\n          const d = vertexSkinData[j];\n          if (d !== void 0) {\n            build.indices.array.push(d.index);\n            build.weights.array.push(d.weight);\n          } else {\n            build.indices.array.push(0);\n            build.weights.array.push(0);\n          }\n        }\n      }\n      if (data.bindShapeMatrix) {\n        build.bindMatrix = new Matrix4().fromArray(data.bindShapeMatrix).transpose();\n      } else {\n        build.bindMatrix = new Matrix4().identity();\n      }\n      for (i = 0, l = jointSource.array.length; i < l; i++) {\n        const name = jointSource.array[i];\n        const boneInverse = new Matrix4().fromArray(inverseSource.array, i * inverseSource.stride).transpose();\n        build.joints.push({\n          name,\n          boneInverse\n        });\n      }\n      return build;\n      function descending(a, b) {\n        return b.weight - a.weight;\n      }\n    }\n    function getController(id) {\n      return getBuild(library.controllers[id], buildController);\n    }\n    function parseImage(xml2) {\n      const data = {\n        init_from: getElementsByTagName(xml2, \"init_from\")[0].textContent\n      };\n      library.images[xml2.getAttribute(\"id\")] = data;\n    }\n    function buildImage(data) {\n      if (data.build !== void 0) return data.build;\n      return data.init_from;\n    }\n    function getImage(id) {\n      const data = library.images[id];\n      if (data !== void 0) {\n        return getBuild(data, buildImage);\n      }\n      console.warn(\"THREE.ColladaLoader: Couldn't find image with ID:\", id);\n      return null;\n    }\n    function parseEffect(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"profile_COMMON\":\n            data.profile = parseEffectProfileCOMMON(child);\n            break;\n        }\n      }\n      library.effects[xml2.getAttribute(\"id\")] = data;\n    }\n    function parseEffectProfileCOMMON(xml2) {\n      const data = {\n        surfaces: {},\n        samplers: {}\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"newparam\":\n            parseEffectNewparam(child, data);\n            break;\n          case \"technique\":\n            data.technique = parseEffectTechnique(child);\n            break;\n          case \"extra\":\n            data.extra = parseEffectExtra(child);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectNewparam(xml2, data) {\n      const sid = xml2.getAttribute(\"sid\");\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"surface\":\n            data.surfaces[sid] = parseEffectSurface(child);\n            break;\n          case \"sampler2D\":\n            data.samplers[sid] = parseEffectSampler(child);\n            break;\n        }\n      }\n    }\n    function parseEffectSurface(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"init_from\":\n            data.init_from = child.textContent;\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectSampler(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"source\":\n            data.source = child.textContent;\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectTechnique(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"constant\":\n          case \"lambert\":\n          case \"blinn\":\n          case \"phong\":\n            data.type = child.nodeName;\n            data.parameters = parseEffectParameters(child);\n            break;\n          case \"extra\":\n            data.extra = parseEffectExtra(child);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectParameters(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"emission\":\n          case \"diffuse\":\n          case \"specular\":\n          case \"bump\":\n          case \"ambient\":\n          case \"shininess\":\n          case \"transparency\":\n            data[child.nodeName] = parseEffectParameter(child);\n            break;\n          case \"transparent\":\n            data[child.nodeName] = {\n              opaque: child.hasAttribute(\"opaque\") ? child.getAttribute(\"opaque\") : \"A_ONE\",\n              data: parseEffectParameter(child)\n            };\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectParameter(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"color\":\n            data[child.nodeName] = parseFloats(child.textContent);\n            break;\n          case \"float\":\n            data[child.nodeName] = parseFloat(child.textContent);\n            break;\n          case \"texture\":\n            data[child.nodeName] = {\n              id: child.getAttribute(\"texture\"),\n              extra: parseEffectParameterTexture(child)\n            };\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectParameterTexture(xml2) {\n      const data = {\n        technique: {}\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"extra\":\n            parseEffectParameterTextureExtra(child, data);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectParameterTextureExtra(xml2, data) {\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"technique\":\n            parseEffectParameterTextureExtraTechnique(child, data);\n            break;\n        }\n      }\n    }\n    function parseEffectParameterTextureExtraTechnique(xml2, data) {\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"repeatU\":\n          case \"repeatV\":\n          case \"offsetU\":\n          case \"offsetV\":\n            data.technique[child.nodeName] = parseFloat(child.textContent);\n            break;\n          case \"wrapU\":\n          case \"wrapV\":\n            if (child.textContent.toUpperCase() === \"TRUE\") {\n              data.technique[child.nodeName] = 1;\n            } else if (child.textContent.toUpperCase() === \"FALSE\") {\n              data.technique[child.nodeName] = 0;\n            } else {\n              data.technique[child.nodeName] = parseInt(child.textContent);\n            }\n            break;\n          case \"bump\":\n            data[child.nodeName] = parseEffectExtraTechniqueBump(child);\n            break;\n        }\n      }\n    }\n    function parseEffectExtra(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"technique\":\n            data.technique = parseEffectExtraTechnique(child);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectExtraTechnique(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"double_sided\":\n            data[child.nodeName] = parseInt(child.textContent);\n            break;\n          case \"bump\":\n            data[child.nodeName] = parseEffectExtraTechniqueBump(child);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseEffectExtraTechniqueBump(xml2) {\n      var data = {};\n      for (var i = 0, l = xml2.childNodes.length; i < l; i++) {\n        var child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"texture\":\n            data[child.nodeName] = {\n              id: child.getAttribute(\"texture\"),\n              texcoord: child.getAttribute(\"texcoord\"),\n              extra: parseEffectParameterTexture(child)\n            };\n            break;\n        }\n      }\n      return data;\n    }\n    function buildEffect(data) {\n      return data;\n    }\n    function getEffect(id) {\n      return getBuild(library.effects[id], buildEffect);\n    }\n    function parseMaterial(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"name\")\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"instance_effect\":\n            data.url = parseId(child.getAttribute(\"url\"));\n            break;\n        }\n      }\n      library.materials[xml2.getAttribute(\"id\")] = data;\n    }\n    function getTextureLoader(image) {\n      let loader;\n      let extension = image.slice((image.lastIndexOf(\".\") - 1 >>> 0) + 2);\n      extension = extension.toLowerCase();\n      switch (extension) {\n        case \"tga\":\n          loader = tgaLoader;\n          break;\n        default:\n          loader = textureLoader;\n      }\n      return loader;\n    }\n    function buildMaterial(data) {\n      const effect = getEffect(data.url);\n      const technique = effect.profile.technique;\n      let material;\n      switch (technique.type) {\n        case \"phong\":\n        case \"blinn\":\n          material = new MeshPhongMaterial();\n          break;\n        case \"lambert\":\n          material = new MeshLambertMaterial();\n          break;\n        default:\n          material = new MeshBasicMaterial();\n          break;\n      }\n      material.name = data.name || \"\";\n      function getTexture(textureObject) {\n        const sampler = effect.profile.samplers[textureObject.id];\n        let image = null;\n        if (sampler !== void 0) {\n          const surface = effect.profile.surfaces[sampler.source];\n          image = getImage(surface.init_from);\n        } else {\n          console.warn(\"THREE.ColladaLoader: Undefined sampler. Access image directly (see #12530).\");\n          image = getImage(textureObject.id);\n        }\n        if (image !== null) {\n          const loader = getTextureLoader(image);\n          if (loader !== void 0) {\n            const texture = loader.load(image);\n            const extra = textureObject.extra;\n            if (extra !== void 0 && extra.technique !== void 0 && isEmpty(extra.technique) === false) {\n              const technique2 = extra.technique;\n              texture.wrapS = technique2.wrapU ? RepeatWrapping : ClampToEdgeWrapping;\n              texture.wrapT = technique2.wrapV ? RepeatWrapping : ClampToEdgeWrapping;\n              texture.offset.set(technique2.offsetU || 0, technique2.offsetV || 0);\n              texture.repeat.set(technique2.repeatU || 1, technique2.repeatV || 1);\n            } else {\n              texture.wrapS = RepeatWrapping;\n              texture.wrapT = RepeatWrapping;\n            }\n            return texture;\n          } else {\n            console.warn(\"THREE.ColladaLoader: Loader for texture %s not found.\", image);\n            return null;\n          }\n        } else {\n          console.warn(\"THREE.ColladaLoader: Couldn't create texture with ID:\", textureObject.id);\n          return null;\n        }\n      }\n      const parameters = technique.parameters;\n      for (const key in parameters) {\n        const parameter = parameters[key];\n        switch (key) {\n          case \"diffuse\":\n            if (parameter.color) material.color.fromArray(parameter.color);\n            if (parameter.texture) material.map = getTexture(parameter.texture);\n            break;\n          case \"specular\":\n            if (parameter.color && material.specular) material.specular.fromArray(parameter.color);\n            if (parameter.texture) material.specularMap = getTexture(parameter.texture);\n            break;\n          case \"bump\":\n            if (parameter.texture) material.normalMap = getTexture(parameter.texture);\n            break;\n          case \"ambient\":\n            if (parameter.texture) material.lightMap = getTexture(parameter.texture);\n            break;\n          case \"shininess\":\n            if (parameter.float && material.shininess) material.shininess = parameter.float;\n            break;\n          case \"emission\":\n            if (parameter.color && material.emissive) material.emissive.fromArray(parameter.color);\n            if (parameter.texture) material.emissiveMap = getTexture(parameter.texture);\n            break;\n        }\n      }\n      let transparent = parameters[\"transparent\"];\n      let transparency = parameters[\"transparency\"];\n      if (transparency === void 0 && transparent) {\n        transparency = {\n          float: 1\n        };\n      }\n      if (transparent === void 0 && transparency) {\n        transparent = {\n          opaque: \"A_ONE\",\n          data: {\n            color: [1, 1, 1, 1]\n          }\n        };\n      }\n      if (transparent && transparency) {\n        if (transparent.data.texture) {\n          material.transparent = true;\n        } else {\n          const color = transparent.data.color;\n          switch (transparent.opaque) {\n            case \"A_ONE\":\n              material.opacity = color[3] * transparency.float;\n              break;\n            case \"RGB_ZERO\":\n              material.opacity = 1 - color[0] * transparency.float;\n              break;\n            case \"A_ZERO\":\n              material.opacity = 1 - color[3] * transparency.float;\n              break;\n            case \"RGB_ONE\":\n              material.opacity = color[0] * transparency.float;\n              break;\n            default:\n              console.warn('THREE.ColladaLoader: Invalid opaque type \"%s\" of transparent tag.', transparent.opaque);\n          }\n          if (material.opacity < 1) material.transparent = true;\n        }\n      }\n      if (technique.extra !== void 0 && technique.extra.technique !== void 0) {\n        const techniques = technique.extra.technique;\n        for (const k in techniques) {\n          const v = techniques[k];\n          switch (k) {\n            case \"double_sided\":\n              material.side = v === 1 ? DoubleSide : FrontSide;\n              break;\n            case \"bump\":\n              material.normalMap = getTexture(v.texture);\n              material.normalScale = new Vector2(1, 1);\n              break;\n          }\n        }\n      }\n      return material;\n    }\n    function getMaterial(id) {\n      return getBuild(library.materials[id], buildMaterial);\n    }\n    function parseCamera(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"name\")\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"optics\":\n            data.optics = parseCameraOptics(child);\n            break;\n        }\n      }\n      library.cameras[xml2.getAttribute(\"id\")] = data;\n    }\n    function parseCameraOptics(xml2) {\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        switch (child.nodeName) {\n          case \"technique_common\":\n            return parseCameraTechnique(child);\n        }\n      }\n      return {};\n    }\n    function parseCameraTechnique(xml2) {\n      const data = {};\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        switch (child.nodeName) {\n          case \"perspective\":\n          case \"orthographic\":\n            data.technique = child.nodeName;\n            data.parameters = parseCameraParameters(child);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseCameraParameters(xml2) {\n      const data = {};\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        switch (child.nodeName) {\n          case \"xfov\":\n          case \"yfov\":\n          case \"xmag\":\n          case \"ymag\":\n          case \"znear\":\n          case \"zfar\":\n          case \"aspect_ratio\":\n            data[child.nodeName] = parseFloat(child.textContent);\n            break;\n        }\n      }\n      return data;\n    }\n    function buildCamera(data) {\n      let camera;\n      switch (data.optics.technique) {\n        case \"perspective\":\n          camera = new PerspectiveCamera(data.optics.parameters.yfov, data.optics.parameters.aspect_ratio, data.optics.parameters.znear, data.optics.parameters.zfar);\n          break;\n        case \"orthographic\":\n          let ymag = data.optics.parameters.ymag;\n          let xmag = data.optics.parameters.xmag;\n          const aspectRatio = data.optics.parameters.aspect_ratio;\n          xmag = xmag === void 0 ? ymag * aspectRatio : xmag;\n          ymag = ymag === void 0 ? xmag / aspectRatio : ymag;\n          xmag *= 0.5;\n          ymag *= 0.5;\n          camera = new OrthographicCamera(-xmag, xmag, ymag, -ymag,\n          // left, right, top, bottom\n          data.optics.parameters.znear, data.optics.parameters.zfar);\n          break;\n        default:\n          camera = new PerspectiveCamera();\n          break;\n      }\n      camera.name = data.name || \"\";\n      return camera;\n    }\n    function getCamera(id) {\n      const data = library.cameras[id];\n      if (data !== void 0) {\n        return getBuild(data, buildCamera);\n      }\n      console.warn(\"THREE.ColladaLoader: Couldn't find camera with ID:\", id);\n      return null;\n    }\n    function parseLight(xml2) {\n      let data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"technique_common\":\n            data = parseLightTechnique(child);\n            break;\n        }\n      }\n      library.lights[xml2.getAttribute(\"id\")] = data;\n    }\n    function parseLightTechnique(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"directional\":\n          case \"point\":\n          case \"spot\":\n          case \"ambient\":\n            data.technique = child.nodeName;\n            data.parameters = parseLightParameters(child);\n        }\n      }\n      return data;\n    }\n    function parseLightParameters(xml2) {\n      const data = {};\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"color\":\n            const array = parseFloats(child.textContent);\n            data.color = new Color().fromArray(array);\n            break;\n          case \"falloff_angle\":\n            data.falloffAngle = parseFloat(child.textContent);\n            break;\n          case \"quadratic_attenuation\":\n            const f = parseFloat(child.textContent);\n            data.distance = f ? Math.sqrt(1 / f) : 0;\n            break;\n        }\n      }\n      return data;\n    }\n    function buildLight(data) {\n      let light;\n      switch (data.technique) {\n        case \"directional\":\n          light = new DirectionalLight();\n          break;\n        case \"point\":\n          light = new PointLight();\n          break;\n        case \"spot\":\n          light = new SpotLight();\n          break;\n        case \"ambient\":\n          light = new AmbientLight();\n          break;\n      }\n      if (data.parameters.color) light.color.copy(data.parameters.color);\n      if (data.parameters.distance) light.distance = data.parameters.distance;\n      return light;\n    }\n    function getLight(id) {\n      const data = library.lights[id];\n      if (data !== void 0) {\n        return getBuild(data, buildLight);\n      }\n      console.warn(\"THREE.ColladaLoader: Couldn't find light with ID:\", id);\n      return null;\n    }\n    function parseGeometry(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"name\"),\n        sources: {},\n        vertices: {},\n        primitives: []\n      };\n      const mesh = getElementsByTagName(xml2, \"mesh\")[0];\n      if (mesh === void 0) return;\n      for (let i = 0; i < mesh.childNodes.length; i++) {\n        const child = mesh.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        const id = child.getAttribute(\"id\");\n        switch (child.nodeName) {\n          case \"source\":\n            data.sources[id] = parseSource(child);\n            break;\n          case \"vertices\":\n            data.vertices = parseGeometryVertices(child);\n            break;\n          case \"polygons\":\n            console.warn(\"THREE.ColladaLoader: Unsupported primitive type: \", child.nodeName);\n            break;\n          case \"lines\":\n          case \"linestrips\":\n          case \"polylist\":\n          case \"triangles\":\n            data.primitives.push(parseGeometryPrimitive(child));\n            break;\n          default:\n            console.log(child);\n        }\n      }\n      library.geometries[xml2.getAttribute(\"id\")] = data;\n    }\n    function parseSource(xml2) {\n      const data = {\n        array: [],\n        stride: 3\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"float_array\":\n            data.array = parseFloats(child.textContent);\n            break;\n          case \"Name_array\":\n            data.array = parseStrings(child.textContent);\n            break;\n          case \"technique_common\":\n            const accessor = getElementsByTagName(child, \"accessor\")[0];\n            if (accessor !== void 0) {\n              data.stride = parseInt(accessor.getAttribute(\"stride\"));\n            }\n            break;\n        }\n      }\n      return data;\n    }\n    function parseGeometryVertices(xml2) {\n      const data = {};\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        data[child.getAttribute(\"semantic\")] = parseId(child.getAttribute(\"source\"));\n      }\n      return data;\n    }\n    function parseGeometryPrimitive(xml2) {\n      const primitive = {\n        type: xml2.nodeName,\n        material: xml2.getAttribute(\"material\"),\n        count: parseInt(xml2.getAttribute(\"count\")),\n        inputs: {},\n        stride: 0,\n        hasUV: false\n      };\n      for (let i = 0, l = xml2.childNodes.length; i < l; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"input\":\n            const id = parseId(child.getAttribute(\"source\"));\n            const semantic = child.getAttribute(\"semantic\");\n            const offset = parseInt(child.getAttribute(\"offset\"));\n            const set = parseInt(child.getAttribute(\"set\"));\n            const inputname = set > 0 ? semantic + set : semantic;\n            primitive.inputs[inputname] = {\n              id,\n              offset\n            };\n            primitive.stride = Math.max(primitive.stride, offset + 1);\n            if (semantic === \"TEXCOORD\") primitive.hasUV = true;\n            break;\n          case \"vcount\":\n            primitive.vcount = parseInts(child.textContent);\n            break;\n          case \"p\":\n            primitive.p = parseInts(child.textContent);\n            break;\n        }\n      }\n      return primitive;\n    }\n    function groupPrimitives(primitives) {\n      const build = {};\n      for (let i = 0; i < primitives.length; i++) {\n        const primitive = primitives[i];\n        if (build[primitive.type] === void 0) build[primitive.type] = [];\n        build[primitive.type].push(primitive);\n      }\n      return build;\n    }\n    function checkUVCoordinates(primitives) {\n      let count2 = 0;\n      for (let i = 0, l = primitives.length; i < l; i++) {\n        const primitive = primitives[i];\n        if (primitive.hasUV === true) {\n          count2++;\n        }\n      }\n      if (count2 > 0 && count2 < primitives.length) {\n        primitives.uvsNeedsFix = true;\n      }\n    }\n    function buildGeometry(data) {\n      const build = {};\n      const sources = data.sources;\n      const vertices = data.vertices;\n      const primitives = data.primitives;\n      if (primitives.length === 0) return {};\n      const groupedPrimitives = groupPrimitives(primitives);\n      for (const type in groupedPrimitives) {\n        const primitiveType = groupedPrimitives[type];\n        checkUVCoordinates(primitiveType);\n        build[type] = buildGeometryType(primitiveType, sources, vertices);\n      }\n      return build;\n    }\n    function buildGeometryType(primitives, sources, vertices) {\n      const build = {};\n      const position2 = {\n        array: [],\n        stride: 0\n      };\n      const normal = {\n        array: [],\n        stride: 0\n      };\n      const uv = {\n        array: [],\n        stride: 0\n      };\n      const uv1 = {\n        array: [],\n        stride: 0\n      };\n      const color = {\n        array: [],\n        stride: 0\n      };\n      const skinIndex = {\n        array: [],\n        stride: 4\n      };\n      const skinWeight = {\n        array: [],\n        stride: 4\n      };\n      const geometry = new BufferGeometry();\n      const materialKeys = [];\n      let start = 0;\n      for (let p = 0; p < primitives.length; p++) {\n        const primitive = primitives[p];\n        const inputs = primitive.inputs;\n        let count2 = 0;\n        switch (primitive.type) {\n          case \"lines\":\n          case \"linestrips\":\n            count2 = primitive.count * 2;\n            break;\n          case \"triangles\":\n            count2 = primitive.count * 3;\n            break;\n          case \"polylist\":\n            for (let g = 0; g < primitive.count; g++) {\n              const vc = primitive.vcount[g];\n              switch (vc) {\n                case 3:\n                  count2 += 3;\n                  break;\n                case 4:\n                  count2 += 6;\n                  break;\n                default:\n                  count2 += (vc - 2) * 3;\n                  break;\n              }\n            }\n            break;\n          default:\n            console.warn(\"THREE.ColladaLoader: Unknow primitive type:\", primitive.type);\n        }\n        geometry.addGroup(start, count2, p);\n        start += count2;\n        if (primitive.material) {\n          materialKeys.push(primitive.material);\n        }\n        for (const name in inputs) {\n          const input = inputs[name];\n          switch (name) {\n            case \"VERTEX\":\n              for (const key in vertices) {\n                const id = vertices[key];\n                switch (key) {\n                  case \"POSITION\":\n                    const prevLength = position2.array.length;\n                    buildGeometryData(primitive, sources[id], input.offset, position2.array);\n                    position2.stride = sources[id].stride;\n                    if (sources.skinWeights && sources.skinIndices) {\n                      buildGeometryData(primitive, sources.skinIndices, input.offset, skinIndex.array);\n                      buildGeometryData(primitive, sources.skinWeights, input.offset, skinWeight.array);\n                    }\n                    if (primitive.hasUV === false && primitives.uvsNeedsFix === true) {\n                      const count3 = (position2.array.length - prevLength) / position2.stride;\n                      for (let i = 0; i < count3; i++) {\n                        uv.array.push(0, 0);\n                      }\n                    }\n                    break;\n                  case \"NORMAL\":\n                    buildGeometryData(primitive, sources[id], input.offset, normal.array);\n                    normal.stride = sources[id].stride;\n                    break;\n                  case \"COLOR\":\n                    buildGeometryData(primitive, sources[id], input.offset, color.array);\n                    color.stride = sources[id].stride;\n                    break;\n                  case \"TEXCOORD\":\n                    buildGeometryData(primitive, sources[id], input.offset, uv.array);\n                    uv.stride = sources[id].stride;\n                    break;\n                  case \"TEXCOORD1\":\n                    buildGeometryData(primitive, sources[id], input.offset, uv1.array);\n                    uv.stride = sources[id].stride;\n                    break;\n                  default:\n                    console.warn('THREE.ColladaLoader: Semantic \"%s\" not handled in geometry build process.', key);\n                }\n              }\n              break;\n            case \"NORMAL\":\n              buildGeometryData(primitive, sources[input.id], input.offset, normal.array);\n              normal.stride = sources[input.id].stride;\n              break;\n            case \"COLOR\":\n              buildGeometryData(primitive, sources[input.id], input.offset, color.array);\n              color.stride = sources[input.id].stride;\n              break;\n            case \"TEXCOORD\":\n              buildGeometryData(primitive, sources[input.id], input.offset, uv.array);\n              uv.stride = sources[input.id].stride;\n              break;\n            case \"TEXCOORD1\":\n              buildGeometryData(primitive, sources[input.id], input.offset, uv1.array);\n              uv1.stride = sources[input.id].stride;\n              break;\n          }\n        }\n      }\n      if (position2.array.length > 0) {\n        geometry.setAttribute(\"position\", new Float32BufferAttribute(position2.array, position2.stride));\n      }\n      if (normal.array.length > 0) {\n        geometry.setAttribute(\"normal\", new Float32BufferAttribute(normal.array, normal.stride));\n      }\n      if (color.array.length > 0) geometry.setAttribute(\"color\", new Float32BufferAttribute(color.array, color.stride));\n      if (uv.array.length > 0) geometry.setAttribute(\"uv\", new Float32BufferAttribute(uv.array, uv.stride));\n      if (uv1.array.length > 0) geometry.setAttribute(UV1, new Float32BufferAttribute(uv1.array, uv1.stride));\n      if (skinIndex.array.length > 0) {\n        geometry.setAttribute(\"skinIndex\", new Float32BufferAttribute(skinIndex.array, skinIndex.stride));\n      }\n      if (skinWeight.array.length > 0) {\n        geometry.setAttribute(\"skinWeight\", new Float32BufferAttribute(skinWeight.array, skinWeight.stride));\n      }\n      build.data = geometry;\n      build.type = primitives[0].type;\n      build.materialKeys = materialKeys;\n      return build;\n    }\n    function buildGeometryData(primitive, source, offset, array) {\n      const indices = primitive.p;\n      const stride = primitive.stride;\n      const vcount = primitive.vcount;\n      function pushVector(i) {\n        let index = indices[i + offset] * sourceStride;\n        const length = index + sourceStride;\n        for (; index < length; index++) {\n          array.push(sourceArray[index]);\n        }\n      }\n      const sourceArray = source.array;\n      const sourceStride = source.stride;\n      if (primitive.vcount !== void 0) {\n        let index = 0;\n        for (let i = 0, l = vcount.length; i < l; i++) {\n          const count2 = vcount[i];\n          if (count2 === 4) {\n            const a = index + stride * 0;\n            const b = index + stride * 1;\n            const c = index + stride * 2;\n            const d = index + stride * 3;\n            pushVector(a);\n            pushVector(b);\n            pushVector(d);\n            pushVector(b);\n            pushVector(c);\n            pushVector(d);\n          } else if (count2 === 3) {\n            const a = index + stride * 0;\n            const b = index + stride * 1;\n            const c = index + stride * 2;\n            pushVector(a);\n            pushVector(b);\n            pushVector(c);\n          } else if (count2 > 4) {\n            for (let k = 1, kl = count2 - 2; k <= kl; k++) {\n              const a = index + stride * 0;\n              const b = index + stride * k;\n              const c = index + stride * (k + 1);\n              pushVector(a);\n              pushVector(b);\n              pushVector(c);\n            }\n          }\n          index += stride * count2;\n        }\n      } else {\n        for (let i = 0, l = indices.length; i < l; i += stride) {\n          pushVector(i);\n        }\n      }\n    }\n    function getGeometry(id) {\n      return getBuild(library.geometries[id], buildGeometry);\n    }\n    function parseKinematicsModel(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"name\") || \"\",\n        joints: {},\n        links: []\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"technique_common\":\n            parseKinematicsTechniqueCommon(child, data);\n            break;\n        }\n      }\n      library.kinematicsModels[xml2.getAttribute(\"id\")] = data;\n    }\n    function buildKinematicsModel(data) {\n      if (data.build !== void 0) return data.build;\n      return data;\n    }\n    function getKinematicsModel(id) {\n      return getBuild(library.kinematicsModels[id], buildKinematicsModel);\n    }\n    function parseKinematicsTechniqueCommon(xml2, data) {\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"joint\":\n            data.joints[child.getAttribute(\"sid\")] = parseKinematicsJoint(child);\n            break;\n          case \"link\":\n            data.links.push(parseKinematicsLink(child));\n            break;\n        }\n      }\n    }\n    function parseKinematicsJoint(xml2) {\n      let data;\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"prismatic\":\n          case \"revolute\":\n            data = parseKinematicsJointParameter(child);\n            break;\n        }\n      }\n      return data;\n    }\n    function parseKinematicsJointParameter(xml2) {\n      const data = {\n        sid: xml2.getAttribute(\"sid\"),\n        name: xml2.getAttribute(\"name\") || \"\",\n        axis: new Vector3(),\n        limits: {\n          min: 0,\n          max: 0\n        },\n        type: xml2.nodeName,\n        static: false,\n        zeroPosition: 0,\n        middlePosition: 0\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"axis\":\n            const array = parseFloats(child.textContent);\n            data.axis.fromArray(array);\n            break;\n          case \"limits\":\n            const max = child.getElementsByTagName(\"max\")[0];\n            const min = child.getElementsByTagName(\"min\")[0];\n            data.limits.max = parseFloat(max.textContent);\n            data.limits.min = parseFloat(min.textContent);\n            break;\n        }\n      }\n      if (data.limits.min >= data.limits.max) {\n        data.static = true;\n      }\n      data.middlePosition = (data.limits.min + data.limits.max) / 2;\n      return data;\n    }\n    function parseKinematicsLink(xml2) {\n      const data = {\n        sid: xml2.getAttribute(\"sid\"),\n        name: xml2.getAttribute(\"name\") || \"\",\n        attachments: [],\n        transforms: []\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"attachment_full\":\n            data.attachments.push(parseKinematicsAttachment(child));\n            break;\n          case \"matrix\":\n          case \"translate\":\n          case \"rotate\":\n            data.transforms.push(parseKinematicsTransform(child));\n            break;\n        }\n      }\n      return data;\n    }\n    function parseKinematicsAttachment(xml2) {\n      const data = {\n        joint: xml2.getAttribute(\"joint\").split(\"/\").pop(),\n        transforms: [],\n        links: []\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"link\":\n            data.links.push(parseKinematicsLink(child));\n            break;\n          case \"matrix\":\n          case \"translate\":\n          case \"rotate\":\n            data.transforms.push(parseKinematicsTransform(child));\n            break;\n        }\n      }\n      return data;\n    }\n    function parseKinematicsTransform(xml2) {\n      const data = {\n        type: xml2.nodeName\n      };\n      const array = parseFloats(xml2.textContent);\n      switch (data.type) {\n        case \"matrix\":\n          data.obj = new Matrix4();\n          data.obj.fromArray(array).transpose();\n          break;\n        case \"translate\":\n          data.obj = new Vector3();\n          data.obj.fromArray(array);\n          break;\n        case \"rotate\":\n          data.obj = new Vector3();\n          data.obj.fromArray(array);\n          data.angle = MathUtils.degToRad(array[3]);\n          break;\n      }\n      return data;\n    }\n    function parsePhysicsModel(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"name\") || \"\",\n        rigidBodies: {}\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"rigid_body\":\n            data.rigidBodies[child.getAttribute(\"name\")] = {};\n            parsePhysicsRigidBody(child, data.rigidBodies[child.getAttribute(\"name\")]);\n            break;\n        }\n      }\n      library.physicsModels[xml2.getAttribute(\"id\")] = data;\n    }\n    function parsePhysicsRigidBody(xml2, data) {\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"technique_common\":\n            parsePhysicsTechniqueCommon(child, data);\n            break;\n        }\n      }\n    }\n    function parsePhysicsTechniqueCommon(xml2, data) {\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"inertia\":\n            data.inertia = parseFloats(child.textContent);\n            break;\n          case \"mass\":\n            data.mass = parseFloats(child.textContent)[0];\n            break;\n        }\n      }\n    }\n    function parseKinematicsScene(xml2) {\n      const data = {\n        bindJointAxis: []\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"bind_joint_axis\":\n            data.bindJointAxis.push(parseKinematicsBindJointAxis(child));\n            break;\n        }\n      }\n      library.kinematicsScenes[parseId(xml2.getAttribute(\"url\"))] = data;\n    }\n    function parseKinematicsBindJointAxis(xml2) {\n      const data = {\n        target: xml2.getAttribute(\"target\").split(\"/\").pop()\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        switch (child.nodeName) {\n          case \"axis\":\n            const param = child.getElementsByTagName(\"param\")[0];\n            data.axis = param.textContent;\n            const tmpJointIndex = data.axis.split(\"inst_\").pop().split(\"axis\")[0];\n            data.jointIndex = tmpJointIndex.substr(0, tmpJointIndex.length - 1);\n            break;\n        }\n      }\n      return data;\n    }\n    function buildKinematicsScene(data) {\n      if (data.build !== void 0) return data.build;\n      return data;\n    }\n    function getKinematicsScene(id) {\n      return getBuild(library.kinematicsScenes[id], buildKinematicsScene);\n    }\n    function setupKinematics() {\n      const kinematicsModelId = Object.keys(library.kinematicsModels)[0];\n      const kinematicsSceneId = Object.keys(library.kinematicsScenes)[0];\n      const visualSceneId = Object.keys(library.visualScenes)[0];\n      if (kinematicsModelId === void 0 || kinematicsSceneId === void 0) return;\n      const kinematicsModel = getKinematicsModel(kinematicsModelId);\n      const kinematicsScene = getKinematicsScene(kinematicsSceneId);\n      const visualScene = getVisualScene(visualSceneId);\n      const bindJointAxis = kinematicsScene.bindJointAxis;\n      const jointMap = {};\n      for (let i = 0, l = bindJointAxis.length; i < l; i++) {\n        const axis = bindJointAxis[i];\n        const targetElement = collada.querySelector('[sid=\"' + axis.target + '\"]');\n        if (targetElement) {\n          const parentVisualElement = targetElement.parentElement;\n          connect(axis.jointIndex, parentVisualElement);\n        }\n      }\n      function connect(jointIndex, visualElement) {\n        const visualElementName = visualElement.getAttribute(\"name\");\n        const joint = kinematicsModel.joints[jointIndex];\n        visualScene.traverse(function (object) {\n          if (object.name === visualElementName) {\n            jointMap[jointIndex] = {\n              object,\n              transforms: buildTransformList(visualElement),\n              joint,\n              position: joint.zeroPosition\n            };\n          }\n        });\n      }\n      const m0 = new Matrix4();\n      kinematics = {\n        joints: kinematicsModel && kinematicsModel.joints,\n        getJointValue: function (jointIndex) {\n          const jointData = jointMap[jointIndex];\n          if (jointData) {\n            return jointData.position;\n          } else {\n            console.warn(\"THREE.ColladaLoader: Joint \" + jointIndex + \" doesn't exist.\");\n          }\n        },\n        setJointValue: function (jointIndex, value) {\n          const jointData = jointMap[jointIndex];\n          if (jointData) {\n            const joint = jointData.joint;\n            if (value > joint.limits.max || value < joint.limits.min) {\n              console.warn(\"THREE.ColladaLoader: Joint \" + jointIndex + \" value \" + value + \" outside of limits (min: \" + joint.limits.min + \", max: \" + joint.limits.max + \").\");\n            } else if (joint.static) {\n              console.warn(\"THREE.ColladaLoader: Joint \" + jointIndex + \" is static.\");\n            } else {\n              const object = jointData.object;\n              const axis = joint.axis;\n              const transforms = jointData.transforms;\n              matrix.identity();\n              for (let i = 0; i < transforms.length; i++) {\n                const transform = transforms[i];\n                if (transform.sid && transform.sid.indexOf(jointIndex) !== -1) {\n                  switch (joint.type) {\n                    case \"revolute\":\n                      matrix.multiply(m0.makeRotationAxis(axis, MathUtils.degToRad(value)));\n                      break;\n                    case \"prismatic\":\n                      matrix.multiply(m0.makeTranslation(axis.x * value, axis.y * value, axis.z * value));\n                      break;\n                    default:\n                      console.warn(\"THREE.ColladaLoader: Unknown joint type: \" + joint.type);\n                      break;\n                  }\n                } else {\n                  switch (transform.type) {\n                    case \"matrix\":\n                      matrix.multiply(transform.obj);\n                      break;\n                    case \"translate\":\n                      matrix.multiply(m0.makeTranslation(transform.obj.x, transform.obj.y, transform.obj.z));\n                      break;\n                    case \"scale\":\n                      matrix.scale(transform.obj);\n                      break;\n                    case \"rotate\":\n                      matrix.multiply(m0.makeRotationAxis(transform.obj, transform.angle));\n                      break;\n                  }\n                }\n              }\n              object.matrix.copy(matrix);\n              object.matrix.decompose(object.position, object.quaternion, object.scale);\n              jointMap[jointIndex].position = value;\n            }\n          } else {\n            console.log(\"THREE.ColladaLoader: \" + jointIndex + \" does not exist.\");\n          }\n        }\n      };\n    }\n    function buildTransformList(node) {\n      const transforms = [];\n      const xml2 = collada.querySelector('[id=\"' + node.id + '\"]');\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        let array, vector2;\n        switch (child.nodeName) {\n          case \"matrix\":\n            array = parseFloats(child.textContent);\n            const matrix2 = new Matrix4().fromArray(array).transpose();\n            transforms.push({\n              sid: child.getAttribute(\"sid\"),\n              type: child.nodeName,\n              obj: matrix2\n            });\n            break;\n          case \"translate\":\n          case \"scale\":\n            array = parseFloats(child.textContent);\n            vector2 = new Vector3().fromArray(array);\n            transforms.push({\n              sid: child.getAttribute(\"sid\"),\n              type: child.nodeName,\n              obj: vector2\n            });\n            break;\n          case \"rotate\":\n            array = parseFloats(child.textContent);\n            vector2 = new Vector3().fromArray(array);\n            const angle = MathUtils.degToRad(array[3]);\n            transforms.push({\n              sid: child.getAttribute(\"sid\"),\n              type: child.nodeName,\n              obj: vector2,\n              angle\n            });\n            break;\n        }\n      }\n      return transforms;\n    }\n    function prepareNodes(xml2) {\n      const elements = xml2.getElementsByTagName(\"node\");\n      for (let i = 0; i < elements.length; i++) {\n        const element = elements[i];\n        if (element.hasAttribute(\"id\") === false) {\n          element.setAttribute(\"id\", generateId());\n        }\n      }\n    }\n    const matrix = new Matrix4();\n    const vector = new Vector3();\n    function parseNode(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"name\") || \"\",\n        type: xml2.getAttribute(\"type\"),\n        id: xml2.getAttribute(\"id\"),\n        sid: xml2.getAttribute(\"sid\"),\n        matrix: new Matrix4(),\n        nodes: [],\n        instanceCameras: [],\n        instanceControllers: [],\n        instanceLights: [],\n        instanceGeometries: [],\n        instanceNodes: [],\n        transforms: {}\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        if (child.nodeType !== 1) continue;\n        let array;\n        switch (child.nodeName) {\n          case \"node\":\n            data.nodes.push(child.getAttribute(\"id\"));\n            parseNode(child);\n            break;\n          case \"instance_camera\":\n            data.instanceCameras.push(parseId(child.getAttribute(\"url\")));\n            break;\n          case \"instance_controller\":\n            data.instanceControllers.push(parseNodeInstance(child));\n            break;\n          case \"instance_light\":\n            data.instanceLights.push(parseId(child.getAttribute(\"url\")));\n            break;\n          case \"instance_geometry\":\n            data.instanceGeometries.push(parseNodeInstance(child));\n            break;\n          case \"instance_node\":\n            data.instanceNodes.push(parseId(child.getAttribute(\"url\")));\n            break;\n          case \"matrix\":\n            array = parseFloats(child.textContent);\n            data.matrix.multiply(matrix.fromArray(array).transpose());\n            data.transforms[child.getAttribute(\"sid\")] = child.nodeName;\n            break;\n          case \"translate\":\n            array = parseFloats(child.textContent);\n            vector.fromArray(array);\n            data.matrix.multiply(matrix.makeTranslation(vector.x, vector.y, vector.z));\n            data.transforms[child.getAttribute(\"sid\")] = child.nodeName;\n            break;\n          case \"rotate\":\n            array = parseFloats(child.textContent);\n            const angle = MathUtils.degToRad(array[3]);\n            data.matrix.multiply(matrix.makeRotationAxis(vector.fromArray(array), angle));\n            data.transforms[child.getAttribute(\"sid\")] = child.nodeName;\n            break;\n          case \"scale\":\n            array = parseFloats(child.textContent);\n            data.matrix.scale(vector.fromArray(array));\n            data.transforms[child.getAttribute(\"sid\")] = child.nodeName;\n            break;\n          case \"extra\":\n            break;\n          default:\n            console.log(child);\n        }\n      }\n      if (hasNode(data.id)) {\n        console.warn(\"THREE.ColladaLoader: There is already a node with ID %s. Exclude current node from further processing.\", data.id);\n      } else {\n        library.nodes[data.id] = data;\n      }\n      return data;\n    }\n    function parseNodeInstance(xml2) {\n      const data = {\n        id: parseId(xml2.getAttribute(\"url\")),\n        materials: {},\n        skeletons: []\n      };\n      for (let i = 0; i < xml2.childNodes.length; i++) {\n        const child = xml2.childNodes[i];\n        switch (child.nodeName) {\n          case \"bind_material\":\n            const instances = child.getElementsByTagName(\"instance_material\");\n            for (let j = 0; j < instances.length; j++) {\n              const instance = instances[j];\n              const symbol = instance.getAttribute(\"symbol\");\n              const target = instance.getAttribute(\"target\");\n              data.materials[symbol] = parseId(target);\n            }\n            break;\n          case \"skeleton\":\n            data.skeletons.push(parseId(child.textContent));\n            break;\n        }\n      }\n      return data;\n    }\n    function buildSkeleton(skeletons, joints) {\n      const boneData = [];\n      const sortedBoneData = [];\n      let i, j, data;\n      for (i = 0; i < skeletons.length; i++) {\n        const skeleton = skeletons[i];\n        let root;\n        if (hasNode(skeleton)) {\n          root = getNode(skeleton);\n          buildBoneHierarchy(root, joints, boneData);\n        } else if (hasVisualScene(skeleton)) {\n          const visualScene = library.visualScenes[skeleton];\n          const children = visualScene.children;\n          for (let j2 = 0; j2 < children.length; j2++) {\n            const child = children[j2];\n            if (child.type === \"JOINT\") {\n              const root2 = getNode(child.id);\n              buildBoneHierarchy(root2, joints, boneData);\n            }\n          }\n        } else {\n          console.error(\"THREE.ColladaLoader: Unable to find root bone of skeleton with ID:\", skeleton);\n        }\n      }\n      for (i = 0; i < joints.length; i++) {\n        for (j = 0; j < boneData.length; j++) {\n          data = boneData[j];\n          if (data.bone.name === joints[i].name) {\n            sortedBoneData[i] = data;\n            data.processed = true;\n            break;\n          }\n        }\n      }\n      for (i = 0; i < boneData.length; i++) {\n        data = boneData[i];\n        if (data.processed === false) {\n          sortedBoneData.push(data);\n          data.processed = true;\n        }\n      }\n      const bones = [];\n      const boneInverses = [];\n      for (i = 0; i < sortedBoneData.length; i++) {\n        data = sortedBoneData[i];\n        bones.push(data.bone);\n        boneInverses.push(data.boneInverse);\n      }\n      return new Skeleton(bones, boneInverses);\n    }\n    function buildBoneHierarchy(root, joints, boneData) {\n      root.traverse(function (object) {\n        if (object.isBone === true) {\n          let boneInverse;\n          for (let i = 0; i < joints.length; i++) {\n            const joint = joints[i];\n            if (joint.name === object.name) {\n              boneInverse = joint.boneInverse;\n              break;\n            }\n          }\n          if (boneInverse === void 0) {\n            boneInverse = new Matrix4();\n          }\n          boneData.push({\n            bone: object,\n            boneInverse,\n            processed: false\n          });\n        }\n      });\n    }\n    function buildNode(data) {\n      const objects = [];\n      const matrix2 = data.matrix;\n      const nodes = data.nodes;\n      const type = data.type;\n      const instanceCameras = data.instanceCameras;\n      const instanceControllers = data.instanceControllers;\n      const instanceLights = data.instanceLights;\n      const instanceGeometries = data.instanceGeometries;\n      const instanceNodes = data.instanceNodes;\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        objects.push(getNode(nodes[i]));\n      }\n      for (let i = 0, l = instanceCameras.length; i < l; i++) {\n        const instanceCamera = getCamera(instanceCameras[i]);\n        if (instanceCamera !== null) {\n          objects.push(instanceCamera.clone());\n        }\n      }\n      for (let i = 0, l = instanceControllers.length; i < l; i++) {\n        const instance = instanceControllers[i];\n        const controller = getController(instance.id);\n        const geometries = getGeometry(controller.id);\n        const newObjects = buildObjects(geometries, instance.materials);\n        const skeletons = instance.skeletons;\n        const joints = controller.skin.joints;\n        const skeleton = buildSkeleton(skeletons, joints);\n        for (let j = 0, jl = newObjects.length; j < jl; j++) {\n          const object2 = newObjects[j];\n          if (object2.isSkinnedMesh) {\n            object2.bind(skeleton, controller.skin.bindMatrix);\n            object2.normalizeSkinWeights();\n          }\n          objects.push(object2);\n        }\n      }\n      for (let i = 0, l = instanceLights.length; i < l; i++) {\n        const instanceLight = getLight(instanceLights[i]);\n        if (instanceLight !== null) {\n          objects.push(instanceLight.clone());\n        }\n      }\n      for (let i = 0, l = instanceGeometries.length; i < l; i++) {\n        const instance = instanceGeometries[i];\n        const geometries = getGeometry(instance.id);\n        const newObjects = buildObjects(geometries, instance.materials);\n        for (let j = 0, jl = newObjects.length; j < jl; j++) {\n          objects.push(newObjects[j]);\n        }\n      }\n      for (let i = 0, l = instanceNodes.length; i < l; i++) {\n        objects.push(getNode(instanceNodes[i]).clone());\n      }\n      let object;\n      if (nodes.length === 0 && objects.length === 1) {\n        object = objects[0];\n      } else {\n        object = type === \"JOINT\" ? new Bone() : new Group();\n        for (let i = 0; i < objects.length; i++) {\n          object.add(objects[i]);\n        }\n      }\n      object.name = type === \"JOINT\" ? data.sid : data.name;\n      object.matrix.copy(matrix2);\n      object.matrix.decompose(object.position, object.quaternion, object.scale);\n      return object;\n    }\n    const fallbackMaterial = new MeshBasicMaterial({\n      color: 16711935\n    });\n    function resolveMaterialBinding(keys, instanceMaterials) {\n      const materials = [];\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const id = instanceMaterials[keys[i]];\n        if (id === void 0) {\n          console.warn(\"THREE.ColladaLoader: Material with key %s not found. Apply fallback material.\", keys[i]);\n          materials.push(fallbackMaterial);\n        } else {\n          materials.push(getMaterial(id));\n        }\n      }\n      return materials;\n    }\n    function buildObjects(geometries, instanceMaterials) {\n      const objects = [];\n      for (const type in geometries) {\n        const geometry = geometries[type];\n        const materials = resolveMaterialBinding(geometry.materialKeys, instanceMaterials);\n        if (materials.length === 0) {\n          if (type === \"lines\" || type === \"linestrips\") {\n            materials.push(new LineBasicMaterial());\n          } else {\n            materials.push(new MeshPhongMaterial());\n          }\n        }\n        const skinning = geometry.data.attributes.skinIndex !== void 0;\n        const material = materials.length === 1 ? materials[0] : materials;\n        let object;\n        switch (type) {\n          case \"lines\":\n            object = new LineSegments(geometry.data, material);\n            break;\n          case \"linestrips\":\n            object = new Line(geometry.data, material);\n            break;\n          case \"triangles\":\n          case \"polylist\":\n            if (skinning) {\n              object = new SkinnedMesh(geometry.data, material);\n            } else {\n              object = new Mesh(geometry.data, material);\n            }\n            break;\n        }\n        objects.push(object);\n      }\n      return objects;\n    }\n    function hasNode(id) {\n      return library.nodes[id] !== void 0;\n    }\n    function getNode(id) {\n      return getBuild(library.nodes[id], buildNode);\n    }\n    function parseVisualScene(xml2) {\n      const data = {\n        name: xml2.getAttribute(\"name\"),\n        children: []\n      };\n      prepareNodes(xml2);\n      const elements = getElementsByTagName(xml2, \"node\");\n      for (let i = 0; i < elements.length; i++) {\n        data.children.push(parseNode(elements[i]));\n      }\n      library.visualScenes[xml2.getAttribute(\"id\")] = data;\n    }\n    function buildVisualScene(data) {\n      const group = new Group();\n      group.name = data.name;\n      const children = data.children;\n      for (let i = 0; i < children.length; i++) {\n        const child = children[i];\n        group.add(getNode(child.id));\n      }\n      return group;\n    }\n    function hasVisualScene(id) {\n      return library.visualScenes[id] !== void 0;\n    }\n    function getVisualScene(id) {\n      return getBuild(library.visualScenes[id], buildVisualScene);\n    }\n    function parseScene(xml2) {\n      const instance = getElementsByTagName(xml2, \"instance_visual_scene\")[0];\n      return getVisualScene(parseId(instance.getAttribute(\"url\")));\n    }\n    function setupAnimations() {\n      const clips = library.clips;\n      if (isEmpty(clips) === true) {\n        if (isEmpty(library.animations) === false) {\n          const tracks = [];\n          for (const id in library.animations) {\n            const animationTracks = getAnimation(id);\n            for (let i = 0, l = animationTracks.length; i < l; i++) {\n              tracks.push(animationTracks[i]);\n            }\n          }\n          animations.push(new AnimationClip(\"default\", -1, tracks));\n        }\n      } else {\n        for (const id in clips) {\n          animations.push(getAnimationClip(id));\n        }\n      }\n    }\n    function parserErrorToText(parserError2) {\n      let result = \"\";\n      const stack = [parserError2];\n      while (stack.length) {\n        const node = stack.shift();\n        if (node.nodeType === Node.TEXT_NODE) {\n          result += node.textContent;\n        } else {\n          result += \"\\n\";\n          stack.push.apply(stack, node.childNodes);\n        }\n      }\n      return result.trim();\n    }\n    if (text.length === 0) {\n      return {\n        scene: new Scene()\n      };\n    }\n    const xml = new DOMParser().parseFromString(text, \"application/xml\");\n    const collada = getElementsByTagName(xml, \"COLLADA\")[0];\n    const parserError = xml.getElementsByTagName(\"parsererror\")[0];\n    if (parserError !== void 0) {\n      const errorElement = getElementsByTagName(parserError, \"div\")[0];\n      let errorText;\n      if (errorElement) {\n        errorText = errorElement.textContent;\n      } else {\n        errorText = parserErrorToText(parserError);\n      }\n      console.error(\"THREE.ColladaLoader: Failed to parse collada file.\\n\", errorText);\n      return null;\n    }\n    const version = collada.getAttribute(\"version\");\n    console.log(\"THREE.ColladaLoader: File version\", version);\n    const asset = parseAsset(getElementsByTagName(collada, \"asset\")[0]);\n    const textureLoader = new TextureLoader(this.manager);\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin);\n    let tgaLoader;\n    if (TGALoader) {\n      tgaLoader = new TGALoader(this.manager);\n      tgaLoader.setPath(this.resourcePath || path);\n    }\n    const animations = [];\n    let kinematics = {};\n    let count = 0;\n    const library = {\n      animations: {},\n      clips: {},\n      controllers: {},\n      images: {},\n      effects: {},\n      materials: {},\n      cameras: {},\n      lights: {},\n      geometries: {},\n      nodes: {},\n      visualScenes: {},\n      kinematicsModels: {},\n      physicsModels: {},\n      kinematicsScenes: {}\n    };\n    parseLibrary(collada, \"library_animations\", \"animation\", parseAnimation);\n    parseLibrary(collada, \"library_animation_clips\", \"animation_clip\", parseAnimationClip);\n    parseLibrary(collada, \"library_controllers\", \"controller\", parseController);\n    parseLibrary(collada, \"library_images\", \"image\", parseImage);\n    parseLibrary(collada, \"library_effects\", \"effect\", parseEffect);\n    parseLibrary(collada, \"library_materials\", \"material\", parseMaterial);\n    parseLibrary(collada, \"library_cameras\", \"camera\", parseCamera);\n    parseLibrary(collada, \"library_lights\", \"light\", parseLight);\n    parseLibrary(collada, \"library_geometries\", \"geometry\", parseGeometry);\n    parseLibrary(collada, \"library_nodes\", \"node\", parseNode);\n    parseLibrary(collada, \"library_visual_scenes\", \"visual_scene\", parseVisualScene);\n    parseLibrary(collada, \"library_kinematics_models\", \"kinematics_model\", parseKinematicsModel);\n    parseLibrary(collada, \"library_physics_models\", \"physics_model\", parsePhysicsModel);\n    parseLibrary(collada, \"scene\", \"instance_kinematics_scene\", parseKinematicsScene);\n    buildLibrary(library.animations, buildAnimation);\n    buildLibrary(library.clips, buildAnimationClip);\n    buildLibrary(library.controllers, buildController);\n    buildLibrary(library.images, buildImage);\n    buildLibrary(library.effects, buildEffect);\n    buildLibrary(library.materials, buildMaterial);\n    buildLibrary(library.cameras, buildCamera);\n    buildLibrary(library.lights, buildLight);\n    buildLibrary(library.geometries, buildGeometry);\n    buildLibrary(library.visualScenes, buildVisualScene);\n    setupAnimations();\n    setupKinematics();\n    const scene = parseScene(getElementsByTagName(collada, \"scene\")[0]);\n    scene.animations = animations;\n    if (asset.upAxis === \"Z_UP\") {\n      scene.quaternion.setFromEuler(new Euler(-Math.PI / 2, 0, 0));\n    }\n    scene.scale.multiplyScalar(asset.unit);\n    return {\n      get animations() {\n        console.warn(\"THREE.ColladaLoader: Please access animations over scene.animations now.\");\n        return animations;\n      },\n      kinematics,\n      library,\n      scene\n    };\n  }\n}\nexport { ColladaLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "path", "LoaderUtils", "extractUrlBase", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "getElementsByTagName", "xml2", "name", "array", "childNodes", "i", "l", "length", "child", "nodeName", "push", "parseStrings", "text2", "parts", "trim", "split", "Array", "parseFloats", "parseFloat", "parseInts", "parseInt", "parseId", "substring", "generateId", "count", "isEmpty", "object", "Object", "keys", "parseAsset", "unit", "parseAssetUnit", "upAxis", "parseAssetUpAxis", "hasAttribute", "getAttribute", "textContent", "parseLibrary", "libraryName", "parser", "library2", "elements", "buildLibrary", "data", "builder", "build", "getBuild", "parseAnimation", "sources", "samplers", "channels", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "id", "parseSource", "parseAnimationSampler", "parseAnimationChannel", "log", "library", "animations", "MathUtils", "generateUUID", "inputs", "semantic", "target", "shift", "sid", "arraySyntax", "indexOf", "memberSyntax", "member", "indices", "replace", "sampler", "buildAnimation", "tracks", "hasOwnProperty", "channel", "inputId", "INPUT", "outputId", "OUTPUT", "inputSource", "outputSource", "animation", "buildAnimationChannel", "createKeyframeTracks", "getAnimation", "node", "nodes", "object3D", "getNode", "transform", "transforms", "defaultMatrix", "matrix", "clone", "transpose", "time", "stride", "il", "j", "jl", "value", "index", "warn", "keyframes", "prepareAnimationData", "uuid", "sort", "ascending", "transformAnimationData", "a", "b", "position", "Vector3", "scale", "quaternion", "Quaternion", "times", "positionData", "quaternionData", "scaleData", "keyframe", "fromArray", "decompose", "x", "y", "z", "w", "VectorKeyframeTrack", "QuaternionKeyframeTrack", "property", "defaultValue", "empty", "createMissingKeyframes", "prev", "next", "getPrev", "getNext", "interpolate", "key", "parseAnimationClip", "start", "end", "clips", "buildAnimationClip", "duration", "animations2", "animationTracks", "AnimationClip", "getAnimationClip", "parseController", "skin", "parse<PERSON><PERSON>", "controllers", "bindShapeMatrix", "joints", "parseJoints", "vertexWeights", "parseVertexWeights", "offset", "vcount", "v", "buildController", "geometry", "geometries", "buildSkin", "skinIndices", "skinWeights", "weights", "BONE_LIMIT", "jointOffset", "JOINT", "weightOffset", "WEIGHT", "jointSource", "inverseSource", "INV_BIND_MATRIX", "jointCount", "vertexSkinData", "skinIndex", "weightId", "skinWeight", "weight", "descending", "d", "bindMatrix", "Matrix4", "identity", "boneInverse", "getController", "parseImage", "init_from", "images", "buildImage", "getImage", "parseEffect", "profile", "parseEffectProfileCOMMON", "effects", "surfaces", "parseEffectNewparam", "technique", "parseEffectTechnique", "extra", "parseEffectExtra", "parseEffectSurface", "parseEffectSampler", "source", "type", "parameters", "parseEffectParameters", "parseEffectParameter", "opaque", "parseEffectParameterTexture", "parseEffectParameterTextureExtra", "parseEffectParameterTextureExtraTechnique", "toUpperCase", "parseEffectExtraTechniqueBump", "parseEffectExtraTechnique", "texcoord", "buildEffect", "getEffect", "parseMaterial", "materials", "getTextureLoader", "image", "extension", "slice", "lastIndexOf", "toLowerCase", "tga<PERSON><PERSON><PERSON>", "textureLoader", "buildMaterial", "effect", "material", "MeshPhongMaterial", "MeshLambertMaterial", "MeshBasicMaterial", "getTexture", "textureObject", "surface", "texture", "technique2", "wrapS", "wrapU", "RepeatWrapping", "ClampToEdgeWrapping", "wrapT", "wrapV", "set", "offsetU", "offsetV", "repeat", "repeatU", "repeatV", "parameter", "color", "map", "specular", "specularMap", "normalMap", "lightMap", "float", "shininess", "emissive", "emissiveMap", "transparent", "transparency", "opacity", "techniques", "k", "side", "DoubleSide", "FrontSide", "normalScale", "Vector2", "getMaterial", "parseCamera", "optics", "parseCameraOptics", "cameras", "parseCameraTechnique", "parseCameraParameters", "buildCamera", "camera", "PerspectiveCamera", "yfov", "aspect_ratio", "znear", "zfar", "ymag", "xmag", "aspectRatio", "OrthographicCamera", "getCamera", "parseLight", "parseLightTechnique", "lights", "parseLightParameters", "Color", "falloffAngle", "f", "distance", "Math", "sqrt", "buildLight", "light", "DirectionalLight", "PointLight", "SpotLight", "AmbientLight", "copy", "getLight", "parseGeometry", "vertices", "primitives", "mesh", "parseGeometryVertices", "parseGeometryPrimitive", "accessor", "primitive", "hasUV", "inputname", "max", "p", "groupPrimitives", "checkUVCoordinates", "count2", "uvsNeedsFix", "buildGeometry", "groupedPrimitives", "primitiveType", "buildGeometryType", "position2", "normal", "uv", "uv1", "BufferGeometry", "materialKeys", "g", "vc", "addGroup", "input", "prevLength", "buildGeometryData", "count3", "setAttribute", "Float32BufferAttribute", "UV1", "pushVector", "sourceStride", "sourceArray", "c", "kl", "getGeometry", "parseKinematicsModel", "links", "parseKinematicsTechniqueCommon", "kinematicsModels", "buildKinematicsModel", "getKinematicsModel", "parseKinematicsJoint", "parseKinematicsLink", "parseKinematicsJointParameter", "axis", "limits", "min", "static", "zeroPosition", "middlePosition", "attachments", "parseKinematicsAttachment", "parseKinematicsTransform", "joint", "pop", "obj", "angle", "degToRad", "parsePhysicsModel", "rigidBodies", "parsePhysicsRigidBody", "physicsModels", "parsePhysicsTechniqueCommon", "inertia", "mass", "parseKinematicsScene", "bindJointAxis", "parseKinematicsBindJointAxis", "kinematicsScenes", "param", "tmpJointIndex", "jointIndex", "substr", "buildKinematicsScene", "getKinematicsScene", "setupKinematics", "kinematicsModelId", "kinematicsSceneId", "visualSceneId", "visualScenes", "kinematicsModel", "kinematicsScene", "visualScene", "getVisualScene", "jointMap", "targetElement", "collada", "querySelector", "parentVisualElement", "parentElement", "connect", "visualElement", "visualElementName", "traverse", "buildTransformList", "m0", "kinematics", "getJointValue", "jointData", "setJointValue", "multiply", "makeRotationAxis", "makeTranslation", "vector2", "matrix2", "prepareNodes", "element", "vector", "parseNode", "instanceCameras", "instanceControllers", "instanceLights", "instanceGeometries", "instanceNodes", "parseNodeInstance", "hasNode", "skeletons", "instances", "instance", "symbol", "buildSkeleton", "boneData", "sortedBoneData", "skeleton", "root", "buildBoneHierarchy", "hasVisualScene", "children", "j2", "root2", "bone", "processed", "bones", "boneInverses", "Skeleton", "isBone", "buildNode", "objects", "instanceCamera", "controller", "newObjects", "buildObjects", "object2", "isSkinnedMesh", "bind", "normalizeSkinWeights", "instanceLight", "Bone", "Group", "add", "fallbackMaterial", "resolveMaterialBinding", "instanceMaterials", "LineBasicMaterial", "skinning", "attributes", "LineSegments", "Line", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "parseVisualScene", "buildVisualScene", "group", "parseScene", "setupAnimations", "parserErrorToText", "parserError2", "result", "stack", "Node", "TEXT_NODE", "apply", "scene", "Scene", "xml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "parserE<PERSON>r", "errorElement", "errorText", "version", "asset", "TextureLoader", "resourcePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setFromEuler", "<PERSON>uler", "PI", "multiplyScalar"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\ColladaLoader.js"], "sourcesContent": ["import {\n  AmbientLight,\n  AnimationClip,\n  Bone,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  DirectionalLight,\n  DoubleSide,\n  Euler,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  Group,\n  Line,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  LoaderUtils,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  MeshLambertMaterial,\n  MeshPhongMaterial,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PointLight,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  RepeatWrapping,\n  Scene,\n  Skeleton,\n  SkinnedMesh,\n  SpotLight,\n  TextureLoader,\n  Vector2,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\nimport { TGALoader } from '../loaders/TGALoader'\nimport { UV1 } from '../_polyfill/uv1'\n\nclass ColladaLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = scope.path === '' ? LoaderUtils.extractUrlBase(url) : scope.path\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(text, path) {\n    function getElementsByTagName(xml, name) {\n      // Non recursive xml.getElementsByTagName() ...\n\n      const array = []\n      const childNodes = xml.childNodes\n\n      for (let i = 0, l = childNodes.length; i < l; i++) {\n        const child = childNodes[i]\n\n        if (child.nodeName === name) {\n          array.push(child)\n        }\n      }\n\n      return array\n    }\n\n    function parseStrings(text) {\n      if (text.length === 0) return []\n\n      const parts = text.trim().split(/\\s+/)\n      const array = new Array(parts.length)\n\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parts[i]\n      }\n\n      return array\n    }\n\n    function parseFloats(text) {\n      if (text.length === 0) return []\n\n      const parts = text.trim().split(/\\s+/)\n      const array = new Array(parts.length)\n\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parseFloat(parts[i])\n      }\n\n      return array\n    }\n\n    function parseInts(text) {\n      if (text.length === 0) return []\n\n      const parts = text.trim().split(/\\s+/)\n      const array = new Array(parts.length)\n\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parseInt(parts[i])\n      }\n\n      return array\n    }\n\n    function parseId(text) {\n      return text.substring(1)\n    }\n\n    function generateId() {\n      return 'three_default_' + count++\n    }\n\n    function isEmpty(object) {\n      return Object.keys(object).length === 0\n    }\n\n    // asset\n\n    function parseAsset(xml) {\n      return {\n        unit: parseAssetUnit(getElementsByTagName(xml, 'unit')[0]),\n        upAxis: parseAssetUpAxis(getElementsByTagName(xml, 'up_axis')[0]),\n      }\n    }\n\n    function parseAssetUnit(xml) {\n      if (xml !== undefined && xml.hasAttribute('meter') === true) {\n        return parseFloat(xml.getAttribute('meter'))\n      } else {\n        return 1 // default 1 meter\n      }\n    }\n\n    function parseAssetUpAxis(xml) {\n      return xml !== undefined ? xml.textContent : 'Y_UP'\n    }\n\n    // library\n\n    function parseLibrary(xml, libraryName, nodeName, parser) {\n      const library = getElementsByTagName(xml, libraryName)[0]\n\n      if (library !== undefined) {\n        const elements = getElementsByTagName(library, nodeName)\n\n        for (let i = 0; i < elements.length; i++) {\n          parser(elements[i])\n        }\n      }\n    }\n\n    function buildLibrary(data, builder) {\n      for (const name in data) {\n        const object = data[name]\n        object.build = builder(data[name])\n      }\n    }\n\n    // get\n\n    function getBuild(data, builder) {\n      if (data.build !== undefined) return data.build\n\n      data.build = builder(data)\n\n      return data.build\n    }\n\n    // animation\n\n    function parseAnimation(xml) {\n      const data = {\n        sources: {},\n        samplers: {},\n        channels: {},\n      }\n\n      let hasChildren = false\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        let id\n\n        switch (child.nodeName) {\n          case 'source':\n            id = child.getAttribute('id')\n            data.sources[id] = parseSource(child)\n            break\n\n          case 'sampler':\n            id = child.getAttribute('id')\n            data.samplers[id] = parseAnimationSampler(child)\n            break\n\n          case 'channel':\n            id = child.getAttribute('target')\n            data.channels[id] = parseAnimationChannel(child)\n            break\n\n          case 'animation':\n            // hierarchy of related animations\n            parseAnimation(child)\n            hasChildren = true\n            break\n\n          default:\n            console.log(child)\n        }\n      }\n\n      if (hasChildren === false) {\n        // since 'id' attributes can be optional, it's necessary to generate a UUID for unqiue assignment\n\n        library.animations[xml.getAttribute('id') || MathUtils.generateUUID()] = data\n      }\n    }\n\n    function parseAnimationSampler(xml) {\n      const data = {\n        inputs: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const id = parseId(child.getAttribute('source'))\n            const semantic = child.getAttribute('semantic')\n            data.inputs[semantic] = id\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseAnimationChannel(xml) {\n      const data = {}\n\n      const target = xml.getAttribute('target')\n\n      // parsing SID Addressing Syntax\n\n      let parts = target.split('/')\n\n      const id = parts.shift()\n      let sid = parts.shift()\n\n      // check selection syntax\n\n      const arraySyntax = sid.indexOf('(') !== -1\n      const memberSyntax = sid.indexOf('.') !== -1\n\n      if (memberSyntax) {\n        //  member selection access\n\n        parts = sid.split('.')\n        sid = parts.shift()\n        data.member = parts.shift()\n      } else if (arraySyntax) {\n        // array-access syntax. can be used to express fields in one-dimensional vectors or two-dimensional matrices.\n\n        const indices = sid.split('(')\n        sid = indices.shift()\n\n        for (let i = 0; i < indices.length; i++) {\n          indices[i] = parseInt(indices[i].replace(/\\)/, ''))\n        }\n\n        data.indices = indices\n      }\n\n      data.id = id\n      data.sid = sid\n\n      data.arraySyntax = arraySyntax\n      data.memberSyntax = memberSyntax\n\n      data.sampler = parseId(xml.getAttribute('source'))\n\n      return data\n    }\n\n    function buildAnimation(data) {\n      const tracks = []\n\n      const channels = data.channels\n      const samplers = data.samplers\n      const sources = data.sources\n\n      for (const target in channels) {\n        if (channels.hasOwnProperty(target)) {\n          const channel = channels[target]\n          const sampler = samplers[channel.sampler]\n\n          const inputId = sampler.inputs.INPUT\n          const outputId = sampler.inputs.OUTPUT\n\n          const inputSource = sources[inputId]\n          const outputSource = sources[outputId]\n\n          const animation = buildAnimationChannel(channel, inputSource, outputSource)\n\n          createKeyframeTracks(animation, tracks)\n        }\n      }\n\n      return tracks\n    }\n\n    function getAnimation(id) {\n      return getBuild(library.animations[id], buildAnimation)\n    }\n\n    function buildAnimationChannel(channel, inputSource, outputSource) {\n      const node = library.nodes[channel.id]\n      const object3D = getNode(node.id)\n\n      const transform = node.transforms[channel.sid]\n      const defaultMatrix = node.matrix.clone().transpose()\n\n      let time, stride\n      let i, il, j, jl\n\n      const data = {}\n\n      // the collada spec allows the animation of data in various ways.\n      // depending on the transform type (matrix, translate, rotate, scale), we execute different logic\n\n      switch (transform) {\n        case 'matrix':\n          for (i = 0, il = inputSource.array.length; i < il; i++) {\n            time = inputSource.array[i]\n            stride = i * outputSource.stride\n\n            if (data[time] === undefined) data[time] = {}\n\n            if (channel.arraySyntax === true) {\n              const value = outputSource.array[stride]\n              const index = channel.indices[0] + 4 * channel.indices[1]\n\n              data[time][index] = value\n            } else {\n              for (j = 0, jl = outputSource.stride; j < jl; j++) {\n                data[time][j] = outputSource.array[stride + j]\n              }\n            }\n          }\n\n          break\n\n        case 'translate':\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform)\n          break\n\n        case 'rotate':\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform)\n          break\n\n        case 'scale':\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform)\n          break\n      }\n\n      const keyframes = prepareAnimationData(data, defaultMatrix)\n\n      const animation = {\n        name: object3D.uuid,\n        keyframes: keyframes,\n      }\n\n      return animation\n    }\n\n    function prepareAnimationData(data, defaultMatrix) {\n      const keyframes = []\n\n      // transfer data into a sortable array\n\n      for (const time in data) {\n        keyframes.push({ time: parseFloat(time), value: data[time] })\n      }\n\n      // ensure keyframes are sorted by time\n\n      keyframes.sort(ascending)\n\n      // now we clean up all animation data, so we can use them for keyframe tracks\n\n      for (let i = 0; i < 16; i++) {\n        transformAnimationData(keyframes, i, defaultMatrix.elements[i])\n      }\n\n      return keyframes\n\n      // array sort function\n\n      function ascending(a, b) {\n        return a.time - b.time\n      }\n    }\n\n    const position = new Vector3()\n    const scale = new Vector3()\n    const quaternion = new Quaternion()\n\n    function createKeyframeTracks(animation, tracks) {\n      const keyframes = animation.keyframes\n      const name = animation.name\n\n      const times = []\n      const positionData = []\n      const quaternionData = []\n      const scaleData = []\n\n      for (let i = 0, l = keyframes.length; i < l; i++) {\n        const keyframe = keyframes[i]\n\n        const time = keyframe.time\n        const value = keyframe.value\n\n        matrix.fromArray(value).transpose()\n        matrix.decompose(position, quaternion, scale)\n\n        times.push(time)\n        positionData.push(position.x, position.y, position.z)\n        quaternionData.push(quaternion.x, quaternion.y, quaternion.z, quaternion.w)\n        scaleData.push(scale.x, scale.y, scale.z)\n      }\n\n      if (positionData.length > 0) tracks.push(new VectorKeyframeTrack(name + '.position', times, positionData))\n      if (quaternionData.length > 0) {\n        tracks.push(new QuaternionKeyframeTrack(name + '.quaternion', times, quaternionData))\n      }\n      if (scaleData.length > 0) tracks.push(new VectorKeyframeTrack(name + '.scale', times, scaleData))\n\n      return tracks\n    }\n\n    function transformAnimationData(keyframes, property, defaultValue) {\n      let keyframe\n\n      let empty = true\n      let i, l\n\n      // check, if values of a property are missing in our keyframes\n\n      for (i = 0, l = keyframes.length; i < l; i++) {\n        keyframe = keyframes[i]\n\n        if (keyframe.value[property] === undefined) {\n          keyframe.value[property] = null // mark as missing\n        } else {\n          empty = false\n        }\n      }\n\n      if (empty === true) {\n        // no values at all, so we set a default value\n\n        for (i = 0, l = keyframes.length; i < l; i++) {\n          keyframe = keyframes[i]\n\n          keyframe.value[property] = defaultValue\n        }\n      } else {\n        // filling gaps\n\n        createMissingKeyframes(keyframes, property)\n      }\n    }\n\n    function createMissingKeyframes(keyframes, property) {\n      let prev, next\n\n      for (let i = 0, l = keyframes.length; i < l; i++) {\n        const keyframe = keyframes[i]\n\n        if (keyframe.value[property] === null) {\n          prev = getPrev(keyframes, i, property)\n          next = getNext(keyframes, i, property)\n\n          if (prev === null) {\n            keyframe.value[property] = next.value[property]\n            continue\n          }\n\n          if (next === null) {\n            keyframe.value[property] = prev.value[property]\n            continue\n          }\n\n          interpolate(keyframe, prev, next, property)\n        }\n      }\n    }\n\n    function getPrev(keyframes, i, property) {\n      while (i >= 0) {\n        const keyframe = keyframes[i]\n\n        if (keyframe.value[property] !== null) return keyframe\n\n        i--\n      }\n\n      return null\n    }\n\n    function getNext(keyframes, i, property) {\n      while (i < keyframes.length) {\n        const keyframe = keyframes[i]\n\n        if (keyframe.value[property] !== null) return keyframe\n\n        i++\n      }\n\n      return null\n    }\n\n    function interpolate(key, prev, next, property) {\n      if (next.time - prev.time === 0) {\n        key.value[property] = prev.value[property]\n        return\n      }\n\n      key.value[property] =\n        ((key.time - prev.time) * (next.value[property] - prev.value[property])) / (next.time - prev.time) +\n        prev.value[property]\n    }\n\n    // animation clips\n\n    function parseAnimationClip(xml) {\n      const data = {\n        name: xml.getAttribute('id') || 'default',\n        start: parseFloat(xml.getAttribute('start') || 0),\n        end: parseFloat(xml.getAttribute('end') || 0),\n        animations: [],\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'instance_animation':\n            data.animations.push(parseId(child.getAttribute('url')))\n            break\n        }\n      }\n\n      library.clips[xml.getAttribute('id')] = data\n    }\n\n    function buildAnimationClip(data) {\n      const tracks = []\n\n      const name = data.name\n      const duration = data.end - data.start || -1\n      const animations = data.animations\n\n      for (let i = 0, il = animations.length; i < il; i++) {\n        const animationTracks = getAnimation(animations[i])\n\n        for (let j = 0, jl = animationTracks.length; j < jl; j++) {\n          tracks.push(animationTracks[j])\n        }\n      }\n\n      return new AnimationClip(name, duration, tracks)\n    }\n\n    function getAnimationClip(id) {\n      return getBuild(library.clips[id], buildAnimationClip)\n    }\n\n    // controller\n\n    function parseController(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'skin':\n            // there is exactly one skin per controller\n            data.id = parseId(child.getAttribute('source'))\n            data.skin = parseSkin(child)\n            break\n\n          case 'morph':\n            data.id = parseId(child.getAttribute('source'))\n            console.warn('THREE.ColladaLoader: Morph target animation not supported yet.')\n            break\n        }\n      }\n\n      library.controllers[xml.getAttribute('id')] = data\n    }\n\n    function parseSkin(xml) {\n      const data = {\n        sources: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'bind_shape_matrix':\n            data.bindShapeMatrix = parseFloats(child.textContent)\n            break\n\n          case 'source':\n            const id = child.getAttribute('id')\n            data.sources[id] = parseSource(child)\n            break\n\n          case 'joints':\n            data.joints = parseJoints(child)\n            break\n\n          case 'vertex_weights':\n            data.vertexWeights = parseVertexWeights(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseJoints(xml) {\n      const data = {\n        inputs: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const semantic = child.getAttribute('semantic')\n            const id = parseId(child.getAttribute('source'))\n            data.inputs[semantic] = id\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseVertexWeights(xml) {\n      const data = {\n        inputs: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const semantic = child.getAttribute('semantic')\n            const id = parseId(child.getAttribute('source'))\n            const offset = parseInt(child.getAttribute('offset'))\n            data.inputs[semantic] = { id: id, offset: offset }\n            break\n\n          case 'vcount':\n            data.vcount = parseInts(child.textContent)\n            break\n\n          case 'v':\n            data.v = parseInts(child.textContent)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildController(data) {\n      const build = {\n        id: data.id,\n      }\n\n      const geometry = library.geometries[build.id]\n\n      if (data.skin !== undefined) {\n        build.skin = buildSkin(data.skin)\n\n        // we enhance the 'sources' property of the corresponding geometry with our skin data\n\n        geometry.sources.skinIndices = build.skin.indices\n        geometry.sources.skinWeights = build.skin.weights\n      }\n\n      return build\n    }\n\n    function buildSkin(data) {\n      const BONE_LIMIT = 4\n\n      const build = {\n        joints: [], // this must be an array to preserve the joint order\n        indices: {\n          array: [],\n          stride: BONE_LIMIT,\n        },\n        weights: {\n          array: [],\n          stride: BONE_LIMIT,\n        },\n      }\n\n      const sources = data.sources\n      const vertexWeights = data.vertexWeights\n\n      const vcount = vertexWeights.vcount\n      const v = vertexWeights.v\n      const jointOffset = vertexWeights.inputs.JOINT.offset\n      const weightOffset = vertexWeights.inputs.WEIGHT.offset\n\n      const jointSource = data.sources[data.joints.inputs.JOINT]\n      const inverseSource = data.sources[data.joints.inputs.INV_BIND_MATRIX]\n\n      const weights = sources[vertexWeights.inputs.WEIGHT.id].array\n      let stride = 0\n\n      let i, j, l\n\n      // procces skin data for each vertex\n\n      for (i = 0, l = vcount.length; i < l; i++) {\n        const jointCount = vcount[i] // this is the amount of joints that affect a single vertex\n        const vertexSkinData = []\n\n        for (j = 0; j < jointCount; j++) {\n          const skinIndex = v[stride + jointOffset]\n          const weightId = v[stride + weightOffset]\n          const skinWeight = weights[weightId]\n\n          vertexSkinData.push({ index: skinIndex, weight: skinWeight })\n\n          stride += 2\n        }\n\n        // we sort the joints in descending order based on the weights.\n        // this ensures, we only procced the most important joints of the vertex\n\n        vertexSkinData.sort(descending)\n\n        // now we provide for each vertex a set of four index and weight values.\n        // the order of the skin data matches the order of vertices\n\n        for (j = 0; j < BONE_LIMIT; j++) {\n          const d = vertexSkinData[j]\n\n          if (d !== undefined) {\n            build.indices.array.push(d.index)\n            build.weights.array.push(d.weight)\n          } else {\n            build.indices.array.push(0)\n            build.weights.array.push(0)\n          }\n        }\n      }\n\n      // setup bind matrix\n\n      if (data.bindShapeMatrix) {\n        build.bindMatrix = new Matrix4().fromArray(data.bindShapeMatrix).transpose()\n      } else {\n        build.bindMatrix = new Matrix4().identity()\n      }\n\n      // process bones and inverse bind matrix data\n\n      for (i = 0, l = jointSource.array.length; i < l; i++) {\n        const name = jointSource.array[i]\n        const boneInverse = new Matrix4().fromArray(inverseSource.array, i * inverseSource.stride).transpose()\n\n        build.joints.push({ name: name, boneInverse: boneInverse })\n      }\n\n      return build\n\n      // array sort function\n\n      function descending(a, b) {\n        return b.weight - a.weight\n      }\n    }\n\n    function getController(id) {\n      return getBuild(library.controllers[id], buildController)\n    }\n\n    // image\n\n    function parseImage(xml) {\n      const data = {\n        init_from: getElementsByTagName(xml, 'init_from')[0].textContent,\n      }\n\n      library.images[xml.getAttribute('id')] = data\n    }\n\n    function buildImage(data) {\n      if (data.build !== undefined) return data.build\n\n      return data.init_from\n    }\n\n    function getImage(id) {\n      const data = library.images[id]\n\n      if (data !== undefined) {\n        return getBuild(data, buildImage)\n      }\n\n      console.warn(\"THREE.ColladaLoader: Couldn't find image with ID:\", id)\n\n      return null\n    }\n\n    // effect\n\n    function parseEffect(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'profile_COMMON':\n            data.profile = parseEffectProfileCOMMON(child)\n            break\n        }\n      }\n\n      library.effects[xml.getAttribute('id')] = data\n    }\n\n    function parseEffectProfileCOMMON(xml) {\n      const data = {\n        surfaces: {},\n        samplers: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'newparam':\n            parseEffectNewparam(child, data)\n            break\n\n          case 'technique':\n            data.technique = parseEffectTechnique(child)\n            break\n\n          case 'extra':\n            data.extra = parseEffectExtra(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectNewparam(xml, data) {\n      const sid = xml.getAttribute('sid')\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'surface':\n            data.surfaces[sid] = parseEffectSurface(child)\n            break\n\n          case 'sampler2D':\n            data.samplers[sid] = parseEffectSampler(child)\n            break\n        }\n      }\n    }\n\n    function parseEffectSurface(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'init_from':\n            data.init_from = child.textContent\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectSampler(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'source':\n            data.source = child.textContent\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectTechnique(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'constant':\n          case 'lambert':\n          case 'blinn':\n          case 'phong':\n            data.type = child.nodeName\n            data.parameters = parseEffectParameters(child)\n            break\n\n          case 'extra':\n            data.extra = parseEffectExtra(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameters(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'emission':\n          case 'diffuse':\n          case 'specular':\n          case 'bump':\n          case 'ambient':\n          case 'shininess':\n          case 'transparency':\n            data[child.nodeName] = parseEffectParameter(child)\n            break\n          case 'transparent':\n            data[child.nodeName] = {\n              opaque: child.hasAttribute('opaque') ? child.getAttribute('opaque') : 'A_ONE',\n              data: parseEffectParameter(child),\n            }\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameter(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'color':\n            data[child.nodeName] = parseFloats(child.textContent)\n            break\n\n          case 'float':\n            data[child.nodeName] = parseFloat(child.textContent)\n            break\n\n          case 'texture':\n            data[child.nodeName] = { id: child.getAttribute('texture'), extra: parseEffectParameterTexture(child) }\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameterTexture(xml) {\n      const data = {\n        technique: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'extra':\n            parseEffectParameterTextureExtra(child, data)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameterTextureExtra(xml, data) {\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique':\n            parseEffectParameterTextureExtraTechnique(child, data)\n            break\n        }\n      }\n    }\n\n    function parseEffectParameterTextureExtraTechnique(xml, data) {\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'repeatU':\n          case 'repeatV':\n          case 'offsetU':\n          case 'offsetV':\n            data.technique[child.nodeName] = parseFloat(child.textContent)\n            break\n\n          case 'wrapU':\n          case 'wrapV':\n            // some files have values for wrapU/wrapV which become NaN via parseInt\n\n            if (child.textContent.toUpperCase() === 'TRUE') {\n              data.technique[child.nodeName] = 1\n            } else if (child.textContent.toUpperCase() === 'FALSE') {\n              data.technique[child.nodeName] = 0\n            } else {\n              data.technique[child.nodeName] = parseInt(child.textContent)\n            }\n\n            break\n\n          case 'bump':\n            data[child.nodeName] = parseEffectExtraTechniqueBump(child)\n            break\n        }\n      }\n    }\n\n    function parseEffectExtra(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique':\n            data.technique = parseEffectExtraTechnique(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectExtraTechnique(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'double_sided':\n            data[child.nodeName] = parseInt(child.textContent)\n            break\n\n          case 'bump':\n            data[child.nodeName] = parseEffectExtraTechniqueBump(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectExtraTechniqueBump(xml) {\n      var data = {}\n\n      for (var i = 0, l = xml.childNodes.length; i < l; i++) {\n        var child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'texture':\n            data[child.nodeName] = {\n              id: child.getAttribute('texture'),\n              texcoord: child.getAttribute('texcoord'),\n              extra: parseEffectParameterTexture(child),\n            }\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildEffect(data) {\n      return data\n    }\n\n    function getEffect(id) {\n      return getBuild(library.effects[id], buildEffect)\n    }\n\n    // material\n\n    function parseMaterial(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'instance_effect':\n            data.url = parseId(child.getAttribute('url'))\n            break\n        }\n      }\n\n      library.materials[xml.getAttribute('id')] = data\n    }\n\n    function getTextureLoader(image) {\n      let loader\n\n      let extension = image.slice(((image.lastIndexOf('.') - 1) >>> 0) + 2) // http://www.jstips.co/en/javascript/get-file-extension/\n      extension = extension.toLowerCase()\n\n      switch (extension) {\n        case 'tga':\n          loader = tgaLoader\n          break\n\n        default:\n          loader = textureLoader\n      }\n\n      return loader\n    }\n\n    function buildMaterial(data) {\n      const effect = getEffect(data.url)\n      const technique = effect.profile.technique\n\n      let material\n\n      switch (technique.type) {\n        case 'phong':\n        case 'blinn':\n          material = new MeshPhongMaterial()\n          break\n\n        case 'lambert':\n          material = new MeshLambertMaterial()\n          break\n\n        default:\n          material = new MeshBasicMaterial()\n          break\n      }\n\n      material.name = data.name || ''\n\n      function getTexture(textureObject) {\n        const sampler = effect.profile.samplers[textureObject.id]\n        let image = null\n\n        // get image\n\n        if (sampler !== undefined) {\n          const surface = effect.profile.surfaces[sampler.source]\n          image = getImage(surface.init_from)\n        } else {\n          console.warn('THREE.ColladaLoader: Undefined sampler. Access image directly (see #12530).')\n          image = getImage(textureObject.id)\n        }\n\n        // create texture if image is avaiable\n\n        if (image !== null) {\n          const loader = getTextureLoader(image)\n\n          if (loader !== undefined) {\n            const texture = loader.load(image)\n\n            const extra = textureObject.extra\n\n            if (extra !== undefined && extra.technique !== undefined && isEmpty(extra.technique) === false) {\n              const technique = extra.technique\n\n              texture.wrapS = technique.wrapU ? RepeatWrapping : ClampToEdgeWrapping\n              texture.wrapT = technique.wrapV ? RepeatWrapping : ClampToEdgeWrapping\n\n              texture.offset.set(technique.offsetU || 0, technique.offsetV || 0)\n              texture.repeat.set(technique.repeatU || 1, technique.repeatV || 1)\n            } else {\n              texture.wrapS = RepeatWrapping\n              texture.wrapT = RepeatWrapping\n            }\n\n            return texture\n          } else {\n            console.warn('THREE.ColladaLoader: Loader for texture %s not found.', image)\n\n            return null\n          }\n        } else {\n          console.warn(\"THREE.ColladaLoader: Couldn't create texture with ID:\", textureObject.id)\n\n          return null\n        }\n      }\n\n      const parameters = technique.parameters\n\n      for (const key in parameters) {\n        const parameter = parameters[key]\n\n        switch (key) {\n          case 'diffuse':\n            if (parameter.color) material.color.fromArray(parameter.color)\n            if (parameter.texture) material.map = getTexture(parameter.texture)\n            break\n          case 'specular':\n            if (parameter.color && material.specular) material.specular.fromArray(parameter.color)\n            if (parameter.texture) material.specularMap = getTexture(parameter.texture)\n            break\n          case 'bump':\n            if (parameter.texture) material.normalMap = getTexture(parameter.texture)\n            break\n          case 'ambient':\n            if (parameter.texture) material.lightMap = getTexture(parameter.texture)\n            break\n          case 'shininess':\n            if (parameter.float && material.shininess) material.shininess = parameter.float\n            break\n          case 'emission':\n            if (parameter.color && material.emissive) material.emissive.fromArray(parameter.color)\n            if (parameter.texture) material.emissiveMap = getTexture(parameter.texture)\n            break\n        }\n      }\n\n      //\n\n      let transparent = parameters['transparent']\n      let transparency = parameters['transparency']\n\n      // <transparency> does not exist but <transparent>\n\n      if (transparency === undefined && transparent) {\n        transparency = {\n          float: 1,\n        }\n      }\n\n      // <transparent> does not exist but <transparency>\n\n      if (transparent === undefined && transparency) {\n        transparent = {\n          opaque: 'A_ONE',\n          data: {\n            color: [1, 1, 1, 1],\n          },\n        }\n      }\n\n      if (transparent && transparency) {\n        // handle case if a texture exists but no color\n\n        if (transparent.data.texture) {\n          // we do not set an alpha map (see #13792)\n\n          material.transparent = true\n        } else {\n          const color = transparent.data.color\n\n          switch (transparent.opaque) {\n            case 'A_ONE':\n              material.opacity = color[3] * transparency.float\n              break\n            case 'RGB_ZERO':\n              material.opacity = 1 - color[0] * transparency.float\n              break\n            case 'A_ZERO':\n              material.opacity = 1 - color[3] * transparency.float\n              break\n            case 'RGB_ONE':\n              material.opacity = color[0] * transparency.float\n              break\n            default:\n              console.warn('THREE.ColladaLoader: Invalid opaque type \"%s\" of transparent tag.', transparent.opaque)\n          }\n\n          if (material.opacity < 1) material.transparent = true\n        }\n      }\n\n      //\n\n      if (technique.extra !== undefined && technique.extra.technique !== undefined) {\n        const techniques = technique.extra.technique\n\n        for (const k in techniques) {\n          const v = techniques[k]\n\n          switch (k) {\n            case 'double_sided':\n              material.side = v === 1 ? DoubleSide : FrontSide\n              break\n\n            case 'bump':\n              material.normalMap = getTexture(v.texture)\n              material.normalScale = new Vector2(1, 1)\n              break\n          }\n        }\n      }\n\n      return material\n    }\n\n    function getMaterial(id) {\n      return getBuild(library.materials[id], buildMaterial)\n    }\n\n    // camera\n\n    function parseCamera(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'optics':\n            data.optics = parseCameraOptics(child)\n            break\n        }\n      }\n\n      library.cameras[xml.getAttribute('id')] = data\n    }\n\n    function parseCameraOptics(xml) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            return parseCameraTechnique(child)\n        }\n      }\n\n      return {}\n    }\n\n    function parseCameraTechnique(xml) {\n      const data = {}\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'perspective':\n          case 'orthographic':\n            data.technique = child.nodeName\n            data.parameters = parseCameraParameters(child)\n\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseCameraParameters(xml) {\n      const data = {}\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'xfov':\n          case 'yfov':\n          case 'xmag':\n          case 'ymag':\n          case 'znear':\n          case 'zfar':\n          case 'aspect_ratio':\n            data[child.nodeName] = parseFloat(child.textContent)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildCamera(data) {\n      let camera\n\n      switch (data.optics.technique) {\n        case 'perspective':\n          camera = new PerspectiveCamera(\n            data.optics.parameters.yfov,\n            data.optics.parameters.aspect_ratio,\n            data.optics.parameters.znear,\n            data.optics.parameters.zfar,\n          )\n          break\n\n        case 'orthographic':\n          let ymag = data.optics.parameters.ymag\n          let xmag = data.optics.parameters.xmag\n          const aspectRatio = data.optics.parameters.aspect_ratio\n\n          xmag = xmag === undefined ? ymag * aspectRatio : xmag\n          ymag = ymag === undefined ? xmag / aspectRatio : ymag\n\n          xmag *= 0.5\n          ymag *= 0.5\n\n          camera = new OrthographicCamera(\n            -xmag,\n            xmag,\n            ymag,\n            -ymag, // left, right, top, bottom\n            data.optics.parameters.znear,\n            data.optics.parameters.zfar,\n          )\n          break\n\n        default:\n          camera = new PerspectiveCamera()\n          break\n      }\n\n      camera.name = data.name || ''\n\n      return camera\n    }\n\n    function getCamera(id) {\n      const data = library.cameras[id]\n\n      if (data !== undefined) {\n        return getBuild(data, buildCamera)\n      }\n\n      console.warn(\"THREE.ColladaLoader: Couldn't find camera with ID:\", id)\n\n      return null\n    }\n\n    // light\n\n    function parseLight(xml) {\n      let data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            data = parseLightTechnique(child)\n            break\n        }\n      }\n\n      library.lights[xml.getAttribute('id')] = data\n    }\n\n    function parseLightTechnique(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'directional':\n          case 'point':\n          case 'spot':\n          case 'ambient':\n            data.technique = child.nodeName\n            data.parameters = parseLightParameters(child)\n        }\n      }\n\n      return data\n    }\n\n    function parseLightParameters(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'color':\n            const array = parseFloats(child.textContent)\n            data.color = new Color().fromArray(array)\n            break\n\n          case 'falloff_angle':\n            data.falloffAngle = parseFloat(child.textContent)\n            break\n\n          case 'quadratic_attenuation':\n            const f = parseFloat(child.textContent)\n            data.distance = f ? Math.sqrt(1 / f) : 0\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildLight(data) {\n      let light\n\n      switch (data.technique) {\n        case 'directional':\n          light = new DirectionalLight()\n          break\n\n        case 'point':\n          light = new PointLight()\n          break\n\n        case 'spot':\n          light = new SpotLight()\n          break\n\n        case 'ambient':\n          light = new AmbientLight()\n          break\n      }\n\n      if (data.parameters.color) light.color.copy(data.parameters.color)\n      if (data.parameters.distance) light.distance = data.parameters.distance\n\n      return light\n    }\n\n    function getLight(id) {\n      const data = library.lights[id]\n\n      if (data !== undefined) {\n        return getBuild(data, buildLight)\n      }\n\n      console.warn(\"THREE.ColladaLoader: Couldn't find light with ID:\", id)\n\n      return null\n    }\n\n    // geometry\n\n    function parseGeometry(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n        sources: {},\n        vertices: {},\n        primitives: [],\n      }\n\n      const mesh = getElementsByTagName(xml, 'mesh')[0]\n\n      // the following tags inside geometry are not supported yet (see https://github.com/mrdoob/three.js/pull/12606): convex_mesh, spline, brep\n      if (mesh === undefined) return\n\n      for (let i = 0; i < mesh.childNodes.length; i++) {\n        const child = mesh.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        const id = child.getAttribute('id')\n\n        switch (child.nodeName) {\n          case 'source':\n            data.sources[id] = parseSource(child)\n            break\n\n          case 'vertices':\n            // data.sources[ id ] = data.sources[ parseId( getElementsByTagName( child, 'input' )[ 0 ].getAttribute( 'source' ) ) ];\n            data.vertices = parseGeometryVertices(child)\n            break\n\n          case 'polygons':\n            console.warn('THREE.ColladaLoader: Unsupported primitive type: ', child.nodeName)\n            break\n\n          case 'lines':\n          case 'linestrips':\n          case 'polylist':\n          case 'triangles':\n            data.primitives.push(parseGeometryPrimitive(child))\n            break\n\n          default:\n            console.log(child)\n        }\n      }\n\n      library.geometries[xml.getAttribute('id')] = data\n    }\n\n    function parseSource(xml) {\n      const data = {\n        array: [],\n        stride: 3,\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'float_array':\n            data.array = parseFloats(child.textContent)\n            break\n\n          case 'Name_array':\n            data.array = parseStrings(child.textContent)\n            break\n\n          case 'technique_common':\n            const accessor = getElementsByTagName(child, 'accessor')[0]\n\n            if (accessor !== undefined) {\n              data.stride = parseInt(accessor.getAttribute('stride'))\n            }\n\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseGeometryVertices(xml) {\n      const data = {}\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        data[child.getAttribute('semantic')] = parseId(child.getAttribute('source'))\n      }\n\n      return data\n    }\n\n    function parseGeometryPrimitive(xml) {\n      const primitive = {\n        type: xml.nodeName,\n        material: xml.getAttribute('material'),\n        count: parseInt(xml.getAttribute('count')),\n        inputs: {},\n        stride: 0,\n        hasUV: false,\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const id = parseId(child.getAttribute('source'))\n            const semantic = child.getAttribute('semantic')\n            const offset = parseInt(child.getAttribute('offset'))\n            const set = parseInt(child.getAttribute('set'))\n            const inputname = set > 0 ? semantic + set : semantic\n            primitive.inputs[inputname] = { id: id, offset: offset }\n            primitive.stride = Math.max(primitive.stride, offset + 1)\n            if (semantic === 'TEXCOORD') primitive.hasUV = true\n            break\n\n          case 'vcount':\n            primitive.vcount = parseInts(child.textContent)\n            break\n\n          case 'p':\n            primitive.p = parseInts(child.textContent)\n            break\n        }\n      }\n\n      return primitive\n    }\n\n    function groupPrimitives(primitives) {\n      const build = {}\n\n      for (let i = 0; i < primitives.length; i++) {\n        const primitive = primitives[i]\n\n        if (build[primitive.type] === undefined) build[primitive.type] = []\n\n        build[primitive.type].push(primitive)\n      }\n\n      return build\n    }\n\n    function checkUVCoordinates(primitives) {\n      let count = 0\n\n      for (let i = 0, l = primitives.length; i < l; i++) {\n        const primitive = primitives[i]\n\n        if (primitive.hasUV === true) {\n          count++\n        }\n      }\n\n      if (count > 0 && count < primitives.length) {\n        primitives.uvsNeedsFix = true\n      }\n    }\n\n    function buildGeometry(data) {\n      const build = {}\n\n      const sources = data.sources\n      const vertices = data.vertices\n      const primitives = data.primitives\n\n      if (primitives.length === 0) return {}\n\n      // our goal is to create one buffer geometry for a single type of primitives\n      // first, we group all primitives by their type\n\n      const groupedPrimitives = groupPrimitives(primitives)\n\n      for (const type in groupedPrimitives) {\n        const primitiveType = groupedPrimitives[type]\n\n        // second, ensure consistent uv coordinates for each type of primitives (polylist,triangles or lines)\n\n        checkUVCoordinates(primitiveType)\n\n        // third, create a buffer geometry for each type of primitives\n\n        build[type] = buildGeometryType(primitiveType, sources, vertices)\n      }\n\n      return build\n    }\n\n    function buildGeometryType(primitives, sources, vertices) {\n      const build = {}\n\n      const position = { array: [], stride: 0 }\n      const normal = { array: [], stride: 0 }\n      const uv = { array: [], stride: 0 }\n      const uv1 = { array: [], stride: 0 }\n      const color = { array: [], stride: 0 }\n\n      const skinIndex = { array: [], stride: 4 }\n      const skinWeight = { array: [], stride: 4 }\n\n      const geometry = new BufferGeometry()\n\n      const materialKeys = []\n\n      let start = 0\n\n      for (let p = 0; p < primitives.length; p++) {\n        const primitive = primitives[p]\n        const inputs = primitive.inputs\n\n        // groups\n\n        let count = 0\n\n        switch (primitive.type) {\n          case 'lines':\n          case 'linestrips':\n            count = primitive.count * 2\n            break\n\n          case 'triangles':\n            count = primitive.count * 3\n            break\n\n          case 'polylist':\n            for (let g = 0; g < primitive.count; g++) {\n              const vc = primitive.vcount[g]\n\n              switch (vc) {\n                case 3:\n                  count += 3 // single triangle\n                  break\n\n                case 4:\n                  count += 6 // quad, subdivided into two triangles\n                  break\n\n                default:\n                  count += (vc - 2) * 3 // polylist with more than four vertices\n                  break\n              }\n            }\n\n            break\n\n          default:\n            console.warn('THREE.ColladaLoader: Unknow primitive type:', primitive.type)\n        }\n\n        geometry.addGroup(start, count, p)\n        start += count\n\n        // material\n\n        if (primitive.material) {\n          materialKeys.push(primitive.material)\n        }\n\n        // geometry data\n\n        for (const name in inputs) {\n          const input = inputs[name]\n\n          switch (name) {\n            case 'VERTEX':\n              for (const key in vertices) {\n                const id = vertices[key]\n\n                switch (key) {\n                  case 'POSITION':\n                    const prevLength = position.array.length\n                    buildGeometryData(primitive, sources[id], input.offset, position.array)\n                    position.stride = sources[id].stride\n\n                    if (sources.skinWeights && sources.skinIndices) {\n                      buildGeometryData(primitive, sources.skinIndices, input.offset, skinIndex.array)\n                      buildGeometryData(primitive, sources.skinWeights, input.offset, skinWeight.array)\n                    }\n\n                    // see #3803\n\n                    if (primitive.hasUV === false && primitives.uvsNeedsFix === true) {\n                      const count = (position.array.length - prevLength) / position.stride\n\n                      for (let i = 0; i < count; i++) {\n                        // fill missing uv coordinates\n\n                        uv.array.push(0, 0)\n                      }\n                    }\n\n                    break\n\n                  case 'NORMAL':\n                    buildGeometryData(primitive, sources[id], input.offset, normal.array)\n                    normal.stride = sources[id].stride\n                    break\n\n                  case 'COLOR':\n                    buildGeometryData(primitive, sources[id], input.offset, color.array)\n                    color.stride = sources[id].stride\n                    break\n\n                  case 'TEXCOORD':\n                    buildGeometryData(primitive, sources[id], input.offset, uv.array)\n                    uv.stride = sources[id].stride\n                    break\n\n                  case 'TEXCOORD1':\n                    buildGeometryData(primitive, sources[id], input.offset, uv1.array)\n                    uv.stride = sources[id].stride\n                    break\n\n                  default:\n                    console.warn('THREE.ColladaLoader: Semantic \"%s\" not handled in geometry build process.', key)\n                }\n              }\n\n              break\n\n            case 'NORMAL':\n              buildGeometryData(primitive, sources[input.id], input.offset, normal.array)\n              normal.stride = sources[input.id].stride\n              break\n\n            case 'COLOR':\n              buildGeometryData(primitive, sources[input.id], input.offset, color.array)\n              color.stride = sources[input.id].stride\n              break\n\n            case 'TEXCOORD':\n              buildGeometryData(primitive, sources[input.id], input.offset, uv.array)\n              uv.stride = sources[input.id].stride\n              break\n\n            case 'TEXCOORD1':\n              buildGeometryData(primitive, sources[input.id], input.offset, uv1.array)\n              uv1.stride = sources[input.id].stride\n              break\n          }\n        }\n      }\n\n      // build geometry\n\n      if (position.array.length > 0) {\n        geometry.setAttribute('position', new Float32BufferAttribute(position.array, position.stride))\n      }\n      if (normal.array.length > 0) {\n        geometry.setAttribute('normal', new Float32BufferAttribute(normal.array, normal.stride))\n      }\n      if (color.array.length > 0) geometry.setAttribute('color', new Float32BufferAttribute(color.array, color.stride))\n      if (uv.array.length > 0) geometry.setAttribute('uv', new Float32BufferAttribute(uv.array, uv.stride))\n      if (uv1.array.length > 0) geometry.setAttribute(UV1, new Float32BufferAttribute(uv1.array, uv1.stride))\n\n      if (skinIndex.array.length > 0) {\n        geometry.setAttribute('skinIndex', new Float32BufferAttribute(skinIndex.array, skinIndex.stride))\n      }\n      if (skinWeight.array.length > 0) {\n        geometry.setAttribute('skinWeight', new Float32BufferAttribute(skinWeight.array, skinWeight.stride))\n      }\n\n      build.data = geometry\n      build.type = primitives[0].type\n      build.materialKeys = materialKeys\n\n      return build\n    }\n\n    function buildGeometryData(primitive, source, offset, array) {\n      const indices = primitive.p\n      const stride = primitive.stride\n      const vcount = primitive.vcount\n\n      function pushVector(i) {\n        let index = indices[i + offset] * sourceStride\n        const length = index + sourceStride\n\n        for (; index < length; index++) {\n          array.push(sourceArray[index])\n        }\n      }\n\n      const sourceArray = source.array\n      const sourceStride = source.stride\n\n      if (primitive.vcount !== undefined) {\n        let index = 0\n\n        for (let i = 0, l = vcount.length; i < l; i++) {\n          const count = vcount[i]\n\n          if (count === 4) {\n            const a = index + stride * 0\n            const b = index + stride * 1\n            const c = index + stride * 2\n            const d = index + stride * 3\n\n            pushVector(a)\n            pushVector(b)\n            pushVector(d)\n            pushVector(b)\n            pushVector(c)\n            pushVector(d)\n          } else if (count === 3) {\n            const a = index + stride * 0\n            const b = index + stride * 1\n            const c = index + stride * 2\n\n            pushVector(a)\n            pushVector(b)\n            pushVector(c)\n          } else if (count > 4) {\n            for (let k = 1, kl = count - 2; k <= kl; k++) {\n              const a = index + stride * 0\n              const b = index + stride * k\n              const c = index + stride * (k + 1)\n\n              pushVector(a)\n              pushVector(b)\n              pushVector(c)\n            }\n          }\n\n          index += stride * count\n        }\n      } else {\n        for (let i = 0, l = indices.length; i < l; i += stride) {\n          pushVector(i)\n        }\n      }\n    }\n\n    function getGeometry(id) {\n      return getBuild(library.geometries[id], buildGeometry)\n    }\n\n    // kinematics\n\n    function parseKinematicsModel(xml) {\n      const data = {\n        name: xml.getAttribute('name') || '',\n        joints: {},\n        links: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            parseKinematicsTechniqueCommon(child, data)\n            break\n        }\n      }\n\n      library.kinematicsModels[xml.getAttribute('id')] = data\n    }\n\n    function buildKinematicsModel(data) {\n      if (data.build !== undefined) return data.build\n\n      return data\n    }\n\n    function getKinematicsModel(id) {\n      return getBuild(library.kinematicsModels[id], buildKinematicsModel)\n    }\n\n    function parseKinematicsTechniqueCommon(xml, data) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'joint':\n            data.joints[child.getAttribute('sid')] = parseKinematicsJoint(child)\n            break\n\n          case 'link':\n            data.links.push(parseKinematicsLink(child))\n            break\n        }\n      }\n    }\n\n    function parseKinematicsJoint(xml) {\n      let data\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'prismatic':\n          case 'revolute':\n            data = parseKinematicsJointParameter(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseKinematicsJointParameter(xml) {\n      const data = {\n        sid: xml.getAttribute('sid'),\n        name: xml.getAttribute('name') || '',\n        axis: new Vector3(),\n        limits: {\n          min: 0,\n          max: 0,\n        },\n        type: xml.nodeName,\n        static: false,\n        zeroPosition: 0,\n        middlePosition: 0,\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'axis':\n            const array = parseFloats(child.textContent)\n            data.axis.fromArray(array)\n            break\n          case 'limits':\n            const max = child.getElementsByTagName('max')[0]\n            const min = child.getElementsByTagName('min')[0]\n\n            data.limits.max = parseFloat(max.textContent)\n            data.limits.min = parseFloat(min.textContent)\n            break\n        }\n      }\n\n      // if min is equal to or greater than max, consider the joint static\n\n      if (data.limits.min >= data.limits.max) {\n        data.static = true\n      }\n\n      // calculate middle position\n\n      data.middlePosition = (data.limits.min + data.limits.max) / 2.0\n\n      return data\n    }\n\n    function parseKinematicsLink(xml) {\n      const data = {\n        sid: xml.getAttribute('sid'),\n        name: xml.getAttribute('name') || '',\n        attachments: [],\n        transforms: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'attachment_full':\n            data.attachments.push(parseKinematicsAttachment(child))\n            break\n\n          case 'matrix':\n          case 'translate':\n          case 'rotate':\n            data.transforms.push(parseKinematicsTransform(child))\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseKinematicsAttachment(xml) {\n      const data = {\n        joint: xml.getAttribute('joint').split('/').pop(),\n        transforms: [],\n        links: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'link':\n            data.links.push(parseKinematicsLink(child))\n            break\n\n          case 'matrix':\n          case 'translate':\n          case 'rotate':\n            data.transforms.push(parseKinematicsTransform(child))\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseKinematicsTransform(xml) {\n      const data = {\n        type: xml.nodeName,\n      }\n\n      const array = parseFloats(xml.textContent)\n\n      switch (data.type) {\n        case 'matrix':\n          data.obj = new Matrix4()\n          data.obj.fromArray(array).transpose()\n          break\n\n        case 'translate':\n          data.obj = new Vector3()\n          data.obj.fromArray(array)\n          break\n\n        case 'rotate':\n          data.obj = new Vector3()\n          data.obj.fromArray(array)\n          data.angle = MathUtils.degToRad(array[3])\n          break\n      }\n\n      return data\n    }\n\n    // physics\n\n    function parsePhysicsModel(xml) {\n      const data = {\n        name: xml.getAttribute('name') || '',\n        rigidBodies: {},\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'rigid_body':\n            data.rigidBodies[child.getAttribute('name')] = {}\n            parsePhysicsRigidBody(child, data.rigidBodies[child.getAttribute('name')])\n            break\n        }\n      }\n\n      library.physicsModels[xml.getAttribute('id')] = data\n    }\n\n    function parsePhysicsRigidBody(xml, data) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            parsePhysicsTechniqueCommon(child, data)\n            break\n        }\n      }\n    }\n\n    function parsePhysicsTechniqueCommon(xml, data) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'inertia':\n            data.inertia = parseFloats(child.textContent)\n            break\n\n          case 'mass':\n            data.mass = parseFloats(child.textContent)[0]\n            break\n        }\n      }\n    }\n\n    // scene\n\n    function parseKinematicsScene(xml) {\n      const data = {\n        bindJointAxis: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'bind_joint_axis':\n            data.bindJointAxis.push(parseKinematicsBindJointAxis(child))\n            break\n        }\n      }\n\n      library.kinematicsScenes[parseId(xml.getAttribute('url'))] = data\n    }\n\n    function parseKinematicsBindJointAxis(xml) {\n      const data = {\n        target: xml.getAttribute('target').split('/').pop(),\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'axis':\n            const param = child.getElementsByTagName('param')[0]\n            data.axis = param.textContent\n            const tmpJointIndex = data.axis.split('inst_').pop().split('axis')[0]\n            data.jointIndex = tmpJointIndex.substr(0, tmpJointIndex.length - 1)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildKinematicsScene(data) {\n      if (data.build !== undefined) return data.build\n\n      return data\n    }\n\n    function getKinematicsScene(id) {\n      return getBuild(library.kinematicsScenes[id], buildKinematicsScene)\n    }\n\n    function setupKinematics() {\n      const kinematicsModelId = Object.keys(library.kinematicsModels)[0]\n      const kinematicsSceneId = Object.keys(library.kinematicsScenes)[0]\n      const visualSceneId = Object.keys(library.visualScenes)[0]\n\n      if (kinematicsModelId === undefined || kinematicsSceneId === undefined) return\n\n      const kinematicsModel = getKinematicsModel(kinematicsModelId)\n      const kinematicsScene = getKinematicsScene(kinematicsSceneId)\n      const visualScene = getVisualScene(visualSceneId)\n\n      const bindJointAxis = kinematicsScene.bindJointAxis\n      const jointMap = {}\n\n      for (let i = 0, l = bindJointAxis.length; i < l; i++) {\n        const axis = bindJointAxis[i]\n\n        // the result of the following query is an element of type 'translate', 'rotate','scale' or 'matrix'\n\n        const targetElement = collada.querySelector('[sid=\"' + axis.target + '\"]')\n\n        if (targetElement) {\n          // get the parent of the transform element\n\n          const parentVisualElement = targetElement.parentElement\n\n          // connect the joint of the kinematics model with the element in the visual scene\n\n          connect(axis.jointIndex, parentVisualElement)\n        }\n      }\n\n      function connect(jointIndex, visualElement) {\n        const visualElementName = visualElement.getAttribute('name')\n        const joint = kinematicsModel.joints[jointIndex]\n\n        visualScene.traverse(function (object) {\n          if (object.name === visualElementName) {\n            jointMap[jointIndex] = {\n              object: object,\n              transforms: buildTransformList(visualElement),\n              joint: joint,\n              position: joint.zeroPosition,\n            }\n          }\n        })\n      }\n\n      const m0 = new Matrix4()\n\n      kinematics = {\n        joints: kinematicsModel && kinematicsModel.joints,\n\n        getJointValue: function (jointIndex) {\n          const jointData = jointMap[jointIndex]\n\n          if (jointData) {\n            return jointData.position\n          } else {\n            console.warn('THREE.ColladaLoader: Joint ' + jointIndex + \" doesn't exist.\")\n          }\n        },\n\n        setJointValue: function (jointIndex, value) {\n          const jointData = jointMap[jointIndex]\n\n          if (jointData) {\n            const joint = jointData.joint\n\n            if (value > joint.limits.max || value < joint.limits.min) {\n              console.warn(\n                'THREE.ColladaLoader: Joint ' +\n                jointIndex +\n                ' value ' +\n                value +\n                ' outside of limits (min: ' +\n                joint.limits.min +\n                ', max: ' +\n                joint.limits.max +\n                ').',\n              )\n            } else if (joint.static) {\n              console.warn('THREE.ColladaLoader: Joint ' + jointIndex + ' is static.')\n            } else {\n              const object = jointData.object\n              const axis = joint.axis\n              const transforms = jointData.transforms\n\n              matrix.identity()\n\n              // each update, we have to apply all transforms in the correct order\n\n              for (let i = 0; i < transforms.length; i++) {\n                const transform = transforms[i]\n\n                // if there is a connection of the transform node with a joint, apply the joint value\n\n                if (transform.sid && transform.sid.indexOf(jointIndex) !== -1) {\n                  switch (joint.type) {\n                    case 'revolute':\n                      matrix.multiply(m0.makeRotationAxis(axis, MathUtils.degToRad(value)))\n                      break\n\n                    case 'prismatic':\n                      matrix.multiply(m0.makeTranslation(axis.x * value, axis.y * value, axis.z * value))\n                      break\n\n                    default:\n                      console.warn('THREE.ColladaLoader: Unknown joint type: ' + joint.type)\n                      break\n                  }\n                } else {\n                  switch (transform.type) {\n                    case 'matrix':\n                      matrix.multiply(transform.obj)\n                      break\n\n                    case 'translate':\n                      matrix.multiply(m0.makeTranslation(transform.obj.x, transform.obj.y, transform.obj.z))\n                      break\n\n                    case 'scale':\n                      matrix.scale(transform.obj)\n                      break\n\n                    case 'rotate':\n                      matrix.multiply(m0.makeRotationAxis(transform.obj, transform.angle))\n                      break\n                  }\n                }\n              }\n\n              object.matrix.copy(matrix)\n              object.matrix.decompose(object.position, object.quaternion, object.scale)\n\n              jointMap[jointIndex].position = value\n            }\n          } else {\n            console.log('THREE.ColladaLoader: ' + jointIndex + ' does not exist.')\n          }\n        },\n      }\n    }\n\n    function buildTransformList(node) {\n      const transforms = []\n\n      const xml = collada.querySelector('[id=\"' + node.id + '\"]')\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        let array, vector\n\n        switch (child.nodeName) {\n          case 'matrix':\n            array = parseFloats(child.textContent)\n            const matrix = new Matrix4().fromArray(array).transpose()\n            transforms.push({\n              sid: child.getAttribute('sid'),\n              type: child.nodeName,\n              obj: matrix,\n            })\n            break\n\n          case 'translate':\n          case 'scale':\n            array = parseFloats(child.textContent)\n            vector = new Vector3().fromArray(array)\n            transforms.push({\n              sid: child.getAttribute('sid'),\n              type: child.nodeName,\n              obj: vector,\n            })\n            break\n\n          case 'rotate':\n            array = parseFloats(child.textContent)\n            vector = new Vector3().fromArray(array)\n            const angle = MathUtils.degToRad(array[3])\n            transforms.push({\n              sid: child.getAttribute('sid'),\n              type: child.nodeName,\n              obj: vector,\n              angle: angle,\n            })\n            break\n        }\n      }\n\n      return transforms\n    }\n\n    // nodes\n\n    function prepareNodes(xml) {\n      const elements = xml.getElementsByTagName('node')\n\n      // ensure all node elements have id attributes\n\n      for (let i = 0; i < elements.length; i++) {\n        const element = elements[i]\n\n        if (element.hasAttribute('id') === false) {\n          element.setAttribute('id', generateId())\n        }\n      }\n    }\n\n    const matrix = new Matrix4()\n    const vector = new Vector3()\n\n    function parseNode(xml) {\n      const data = {\n        name: xml.getAttribute('name') || '',\n        type: xml.getAttribute('type'),\n        id: xml.getAttribute('id'),\n        sid: xml.getAttribute('sid'),\n        matrix: new Matrix4(),\n        nodes: [],\n        instanceCameras: [],\n        instanceControllers: [],\n        instanceLights: [],\n        instanceGeometries: [],\n        instanceNodes: [],\n        transforms: {},\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        let array\n\n        switch (child.nodeName) {\n          case 'node':\n            data.nodes.push(child.getAttribute('id'))\n            parseNode(child)\n            break\n\n          case 'instance_camera':\n            data.instanceCameras.push(parseId(child.getAttribute('url')))\n            break\n\n          case 'instance_controller':\n            data.instanceControllers.push(parseNodeInstance(child))\n            break\n\n          case 'instance_light':\n            data.instanceLights.push(parseId(child.getAttribute('url')))\n            break\n\n          case 'instance_geometry':\n            data.instanceGeometries.push(parseNodeInstance(child))\n            break\n\n          case 'instance_node':\n            data.instanceNodes.push(parseId(child.getAttribute('url')))\n            break\n\n          case 'matrix':\n            array = parseFloats(child.textContent)\n            data.matrix.multiply(matrix.fromArray(array).transpose())\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'translate':\n            array = parseFloats(child.textContent)\n            vector.fromArray(array)\n            data.matrix.multiply(matrix.makeTranslation(vector.x, vector.y, vector.z))\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'rotate':\n            array = parseFloats(child.textContent)\n            const angle = MathUtils.degToRad(array[3])\n            data.matrix.multiply(matrix.makeRotationAxis(vector.fromArray(array), angle))\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'scale':\n            array = parseFloats(child.textContent)\n            data.matrix.scale(vector.fromArray(array))\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'extra':\n            break\n\n          default:\n            console.log(child)\n        }\n      }\n\n      if (hasNode(data.id)) {\n        console.warn(\n          'THREE.ColladaLoader: There is already a node with ID %s. Exclude current node from further processing.',\n          data.id,\n        )\n      } else {\n        library.nodes[data.id] = data\n      }\n\n      return data\n    }\n\n    function parseNodeInstance(xml) {\n      const data = {\n        id: parseId(xml.getAttribute('url')),\n        materials: {},\n        skeletons: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'bind_material':\n            const instances = child.getElementsByTagName('instance_material')\n\n            for (let j = 0; j < instances.length; j++) {\n              const instance = instances[j]\n              const symbol = instance.getAttribute('symbol')\n              const target = instance.getAttribute('target')\n\n              data.materials[symbol] = parseId(target)\n            }\n\n            break\n\n          case 'skeleton':\n            data.skeletons.push(parseId(child.textContent))\n            break\n\n          default:\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildSkeleton(skeletons, joints) {\n      const boneData = []\n      const sortedBoneData = []\n\n      let i, j, data\n\n      // a skeleton can have multiple root bones. collada expresses this\n      // situtation with multiple \"skeleton\" tags per controller instance\n\n      for (i = 0; i < skeletons.length; i++) {\n        const skeleton = skeletons[i]\n\n        let root\n\n        if (hasNode(skeleton)) {\n          root = getNode(skeleton)\n          buildBoneHierarchy(root, joints, boneData)\n        } else if (hasVisualScene(skeleton)) {\n          // handle case where the skeleton refers to the visual scene (#13335)\n\n          const visualScene = library.visualScenes[skeleton]\n          const children = visualScene.children\n\n          for (let j = 0; j < children.length; j++) {\n            const child = children[j]\n\n            if (child.type === 'JOINT') {\n              const root = getNode(child.id)\n              buildBoneHierarchy(root, joints, boneData)\n            }\n          }\n        } else {\n          console.error('THREE.ColladaLoader: Unable to find root bone of skeleton with ID:', skeleton)\n        }\n      }\n\n      // sort bone data (the order is defined in the corresponding controller)\n\n      for (i = 0; i < joints.length; i++) {\n        for (j = 0; j < boneData.length; j++) {\n          data = boneData[j]\n\n          if (data.bone.name === joints[i].name) {\n            sortedBoneData[i] = data\n            data.processed = true\n            break\n          }\n        }\n      }\n\n      // add unprocessed bone data at the end of the list\n\n      for (i = 0; i < boneData.length; i++) {\n        data = boneData[i]\n\n        if (data.processed === false) {\n          sortedBoneData.push(data)\n          data.processed = true\n        }\n      }\n\n      // setup arrays for skeleton creation\n\n      const bones = []\n      const boneInverses = []\n\n      for (i = 0; i < sortedBoneData.length; i++) {\n        data = sortedBoneData[i]\n\n        bones.push(data.bone)\n        boneInverses.push(data.boneInverse)\n      }\n\n      return new Skeleton(bones, boneInverses)\n    }\n\n    function buildBoneHierarchy(root, joints, boneData) {\n      // setup bone data from visual scene\n\n      root.traverse(function (object) {\n        if (object.isBone === true) {\n          let boneInverse\n\n          // retrieve the boneInverse from the controller data\n\n          for (let i = 0; i < joints.length; i++) {\n            const joint = joints[i]\n\n            if (joint.name === object.name) {\n              boneInverse = joint.boneInverse\n              break\n            }\n          }\n\n          if (boneInverse === undefined) {\n            // Unfortunately, there can be joints in the visual scene that are not part of the\n            // corresponding controller. In this case, we have to create a dummy boneInverse matrix\n            // for the respective bone. This bone won't affect any vertices, because there are no skin indices\n            // and weights defined for it. But we still have to add the bone to the sorted bone list in order to\n            // ensure a correct animation of the model.\n\n            boneInverse = new Matrix4()\n          }\n\n          boneData.push({ bone: object, boneInverse: boneInverse, processed: false })\n        }\n      })\n    }\n\n    function buildNode(data) {\n      const objects = []\n\n      const matrix = data.matrix\n      const nodes = data.nodes\n      const type = data.type\n      const instanceCameras = data.instanceCameras\n      const instanceControllers = data.instanceControllers\n      const instanceLights = data.instanceLights\n      const instanceGeometries = data.instanceGeometries\n      const instanceNodes = data.instanceNodes\n\n      // nodes\n\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        objects.push(getNode(nodes[i]))\n      }\n\n      // instance cameras\n\n      for (let i = 0, l = instanceCameras.length; i < l; i++) {\n        const instanceCamera = getCamera(instanceCameras[i])\n\n        if (instanceCamera !== null) {\n          objects.push(instanceCamera.clone())\n        }\n      }\n\n      // instance controllers\n\n      for (let i = 0, l = instanceControllers.length; i < l; i++) {\n        const instance = instanceControllers[i]\n        const controller = getController(instance.id)\n        const geometries = getGeometry(controller.id)\n        const newObjects = buildObjects(geometries, instance.materials)\n\n        const skeletons = instance.skeletons\n        const joints = controller.skin.joints\n\n        const skeleton = buildSkeleton(skeletons, joints)\n\n        for (let j = 0, jl = newObjects.length; j < jl; j++) {\n          const object = newObjects[j]\n\n          if (object.isSkinnedMesh) {\n            object.bind(skeleton, controller.skin.bindMatrix)\n            object.normalizeSkinWeights()\n          }\n\n          objects.push(object)\n        }\n      }\n\n      // instance lights\n\n      for (let i = 0, l = instanceLights.length; i < l; i++) {\n        const instanceLight = getLight(instanceLights[i])\n\n        if (instanceLight !== null) {\n          objects.push(instanceLight.clone())\n        }\n      }\n\n      // instance geometries\n\n      for (let i = 0, l = instanceGeometries.length; i < l; i++) {\n        const instance = instanceGeometries[i]\n\n        // a single geometry instance in collada can lead to multiple object3Ds.\n        // this is the case when primitives are combined like triangles and lines\n\n        const geometries = getGeometry(instance.id)\n        const newObjects = buildObjects(geometries, instance.materials)\n\n        for (let j = 0, jl = newObjects.length; j < jl; j++) {\n          objects.push(newObjects[j])\n        }\n      }\n\n      // instance nodes\n\n      for (let i = 0, l = instanceNodes.length; i < l; i++) {\n        objects.push(getNode(instanceNodes[i]).clone())\n      }\n\n      let object\n\n      if (nodes.length === 0 && objects.length === 1) {\n        object = objects[0]\n      } else {\n        object = type === 'JOINT' ? new Bone() : new Group()\n\n        for (let i = 0; i < objects.length; i++) {\n          object.add(objects[i])\n        }\n      }\n\n      object.name = type === 'JOINT' ? data.sid : data.name\n      object.matrix.copy(matrix)\n      object.matrix.decompose(object.position, object.quaternion, object.scale)\n\n      return object\n    }\n\n    const fallbackMaterial = new MeshBasicMaterial({ color: 0xff00ff })\n\n    function resolveMaterialBinding(keys, instanceMaterials) {\n      const materials = []\n\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const id = instanceMaterials[keys[i]]\n\n        if (id === undefined) {\n          console.warn('THREE.ColladaLoader: Material with key %s not found. Apply fallback material.', keys[i])\n          materials.push(fallbackMaterial)\n        } else {\n          materials.push(getMaterial(id))\n        }\n      }\n\n      return materials\n    }\n\n    function buildObjects(geometries, instanceMaterials) {\n      const objects = []\n\n      for (const type in geometries) {\n        const geometry = geometries[type]\n\n        const materials = resolveMaterialBinding(geometry.materialKeys, instanceMaterials)\n\n        // handle case if no materials are defined\n\n        if (materials.length === 0) {\n          if (type === 'lines' || type === 'linestrips') {\n            materials.push(new LineBasicMaterial())\n          } else {\n            materials.push(new MeshPhongMaterial())\n          }\n        }\n\n        // regard skinning\n\n        const skinning = geometry.data.attributes.skinIndex !== undefined\n\n        // choose between a single or multi materials (material array)\n\n        const material = materials.length === 1 ? materials[0] : materials\n\n        // now create a specific 3D object\n\n        let object\n\n        switch (type) {\n          case 'lines':\n            object = new LineSegments(geometry.data, material)\n            break\n\n          case 'linestrips':\n            object = new Line(geometry.data, material)\n            break\n\n          case 'triangles':\n          case 'polylist':\n            if (skinning) {\n              object = new SkinnedMesh(geometry.data, material)\n            } else {\n              object = new Mesh(geometry.data, material)\n            }\n\n            break\n        }\n\n        objects.push(object)\n      }\n\n      return objects\n    }\n\n    function hasNode(id) {\n      return library.nodes[id] !== undefined\n    }\n\n    function getNode(id) {\n      return getBuild(library.nodes[id], buildNode)\n    }\n\n    // visual scenes\n\n    function parseVisualScene(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n        children: [],\n      }\n\n      prepareNodes(xml)\n\n      const elements = getElementsByTagName(xml, 'node')\n\n      for (let i = 0; i < elements.length; i++) {\n        data.children.push(parseNode(elements[i]))\n      }\n\n      library.visualScenes[xml.getAttribute('id')] = data\n    }\n\n    function buildVisualScene(data) {\n      const group = new Group()\n      group.name = data.name\n\n      const children = data.children\n\n      for (let i = 0; i < children.length; i++) {\n        const child = children[i]\n\n        group.add(getNode(child.id))\n      }\n\n      return group\n    }\n\n    function hasVisualScene(id) {\n      return library.visualScenes[id] !== undefined\n    }\n\n    function getVisualScene(id) {\n      return getBuild(library.visualScenes[id], buildVisualScene)\n    }\n\n    // scenes\n\n    function parseScene(xml) {\n      const instance = getElementsByTagName(xml, 'instance_visual_scene')[0]\n      return getVisualScene(parseId(instance.getAttribute('url')))\n    }\n\n    function setupAnimations() {\n      const clips = library.clips\n\n      if (isEmpty(clips) === true) {\n        if (isEmpty(library.animations) === false) {\n          // if there are animations but no clips, we create a default clip for playback\n\n          const tracks = []\n\n          for (const id in library.animations) {\n            const animationTracks = getAnimation(id)\n\n            for (let i = 0, l = animationTracks.length; i < l; i++) {\n              tracks.push(animationTracks[i])\n            }\n          }\n\n          animations.push(new AnimationClip('default', -1, tracks))\n        }\n      } else {\n        for (const id in clips) {\n          animations.push(getAnimationClip(id))\n        }\n      }\n    }\n\n    // convert the parser error element into text with each child elements text\n    // separated by new lines.\n\n    function parserErrorToText(parserError) {\n      let result = ''\n      const stack = [parserError]\n\n      while (stack.length) {\n        const node = stack.shift()\n\n        if (node.nodeType === Node.TEXT_NODE) {\n          result += node.textContent\n        } else {\n          result += '\\n'\n          stack.push.apply(stack, node.childNodes)\n        }\n      }\n\n      return result.trim()\n    }\n\n    if (text.length === 0) {\n      return { scene: new Scene() }\n    }\n\n    const xml = new DOMParser().parseFromString(text, 'application/xml')\n\n    const collada = getElementsByTagName(xml, 'COLLADA')[0]\n\n    const parserError = xml.getElementsByTagName('parsererror')[0]\n    if (parserError !== undefined) {\n      // Chrome will return parser error with a div in it\n\n      const errorElement = getElementsByTagName(parserError, 'div')[0]\n      let errorText\n\n      if (errorElement) {\n        errorText = errorElement.textContent\n      } else {\n        errorText = parserErrorToText(parserError)\n      }\n\n      console.error('THREE.ColladaLoader: Failed to parse collada file.\\n', errorText)\n\n      return null\n    }\n\n    // metadata\n\n    const version = collada.getAttribute('version')\n    console.log('THREE.ColladaLoader: File version', version)\n\n    const asset = parseAsset(getElementsByTagName(collada, 'asset')[0])\n    const textureLoader = new TextureLoader(this.manager)\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    let tgaLoader\n\n    if (TGALoader) {\n      tgaLoader = new TGALoader(this.manager)\n      tgaLoader.setPath(this.resourcePath || path)\n    }\n\n    //\n\n    const animations = []\n    let kinematics = {}\n    let count = 0\n\n    //\n\n    const library = {\n      animations: {},\n      clips: {},\n      controllers: {},\n      images: {},\n      effects: {},\n      materials: {},\n      cameras: {},\n      lights: {},\n      geometries: {},\n      nodes: {},\n      visualScenes: {},\n      kinematicsModels: {},\n      physicsModels: {},\n      kinematicsScenes: {},\n    }\n\n    parseLibrary(collada, 'library_animations', 'animation', parseAnimation)\n    parseLibrary(collada, 'library_animation_clips', 'animation_clip', parseAnimationClip)\n    parseLibrary(collada, 'library_controllers', 'controller', parseController)\n    parseLibrary(collada, 'library_images', 'image', parseImage)\n    parseLibrary(collada, 'library_effects', 'effect', parseEffect)\n    parseLibrary(collada, 'library_materials', 'material', parseMaterial)\n    parseLibrary(collada, 'library_cameras', 'camera', parseCamera)\n    parseLibrary(collada, 'library_lights', 'light', parseLight)\n    parseLibrary(collada, 'library_geometries', 'geometry', parseGeometry)\n    parseLibrary(collada, 'library_nodes', 'node', parseNode)\n    parseLibrary(collada, 'library_visual_scenes', 'visual_scene', parseVisualScene)\n    parseLibrary(collada, 'library_kinematics_models', 'kinematics_model', parseKinematicsModel)\n    parseLibrary(collada, 'library_physics_models', 'physics_model', parsePhysicsModel)\n    parseLibrary(collada, 'scene', 'instance_kinematics_scene', parseKinematicsScene)\n\n    buildLibrary(library.animations, buildAnimation)\n    buildLibrary(library.clips, buildAnimationClip)\n    buildLibrary(library.controllers, buildController)\n    buildLibrary(library.images, buildImage)\n    buildLibrary(library.effects, buildEffect)\n    buildLibrary(library.materials, buildMaterial)\n    buildLibrary(library.cameras, buildCamera)\n    buildLibrary(library.lights, buildLight)\n    buildLibrary(library.geometries, buildGeometry)\n    buildLibrary(library.visualScenes, buildVisualScene)\n\n    setupAnimations()\n    setupKinematics()\n\n    const scene = parseScene(getElementsByTagName(collada, 'scene')[0])\n    scene.animations = animations\n\n    if (asset.upAxis === 'Z_UP') {\n      scene.quaternion.setFromEuler(new Euler(-Math.PI / 2, 0, 0))\n    }\n\n    scene.scale.multiplyScalar(asset.unit)\n\n    return {\n      get animations() {\n        console.warn('THREE.ColladaLoader: Please access animations over scene.animations now.')\n        return animations\n      },\n      kinematics: kinematics,\n      library: library,\n      scene: scene,\n    }\n  }\n}\n\nexport { ColladaLoader }\n"], "mappings": ";;;AA2CA,MAAMA,aAAA,SAAsBC,MAAA,CAAO;EACjCC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,IAAA,GAAOD,KAAA,CAAMC,IAAA,KAAS,KAAKC,WAAA,CAAYC,cAAA,CAAeP,GAAG,IAAII,KAAA,CAAMC,IAAA;IAEzE,MAAMG,MAAA,GAAS,IAAIC,UAAA,CAAWL,KAAA,CAAMN,OAAO;IAC3CU,MAAA,CAAOE,OAAA,CAAQN,KAAA,CAAMC,IAAI;IACzBG,MAAA,CAAOG,gBAAA,CAAiBP,KAAA,CAAMQ,aAAa;IAC3CJ,MAAA,CAAOK,kBAAA,CAAmBT,KAAA,CAAMU,eAAe;IAC/CN,MAAA,CAAOT,IAAA,CACLC,GAAA,EACA,UAAUe,IAAA,EAAM;MACd,IAAI;QACFd,MAAA,CAAOG,KAAA,CAAMY,KAAA,CAAMD,IAAA,EAAMV,IAAI,CAAC;MAC/B,SAAQY,CAAA,EAAP;QACA,IAAId,OAAA,EAAS;UACXA,OAAA,CAAQc,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDb,KAAA,CAAMN,OAAA,CAAQsB,SAAA,CAAUpB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDa,MAAMD,IAAA,EAAMV,IAAA,EAAM;IAChB,SAASgB,qBAAqBC,IAAA,EAAKC,IAAA,EAAM;MAGvC,MAAMC,KAAA,GAAQ,EAAE;MAChB,MAAMC,UAAA,GAAaH,IAAA,CAAIG,UAAA;MAEvB,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIF,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACjD,MAAMG,KAAA,GAAQJ,UAAA,CAAWC,CAAC;QAE1B,IAAIG,KAAA,CAAMC,QAAA,KAAaP,IAAA,EAAM;UAC3BC,KAAA,CAAMO,IAAA,CAAKF,KAAK;QACjB;MACF;MAED,OAAOL,KAAA;IACR;IAED,SAASQ,aAAaC,KAAA,EAAM;MAC1B,IAAIA,KAAA,CAAKL,MAAA,KAAW,GAAG,OAAO,EAAE;MAEhC,MAAMM,KAAA,GAAQD,KAAA,CAAKE,IAAA,CAAI,EAAGC,KAAA,CAAM,KAAK;MACrC,MAAMZ,KAAA,GAAQ,IAAIa,KAAA,CAAMH,KAAA,CAAMN,MAAM;MAEpC,SAASF,CAAA,GAAI,GAAGC,CAAA,GAAIO,KAAA,CAAMN,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5CF,KAAA,CAAME,CAAC,IAAIQ,KAAA,CAAMR,CAAC;MACnB;MAED,OAAOF,KAAA;IACR;IAED,SAASc,YAAYL,KAAA,EAAM;MACzB,IAAIA,KAAA,CAAKL,MAAA,KAAW,GAAG,OAAO,EAAE;MAEhC,MAAMM,KAAA,GAAQD,KAAA,CAAKE,IAAA,CAAI,EAAGC,KAAA,CAAM,KAAK;MACrC,MAAMZ,KAAA,GAAQ,IAAIa,KAAA,CAAMH,KAAA,CAAMN,MAAM;MAEpC,SAASF,CAAA,GAAI,GAAGC,CAAA,GAAIO,KAAA,CAAMN,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5CF,KAAA,CAAME,CAAC,IAAIa,UAAA,CAAWL,KAAA,CAAMR,CAAC,CAAC;MAC/B;MAED,OAAOF,KAAA;IACR;IAED,SAASgB,UAAUP,KAAA,EAAM;MACvB,IAAIA,KAAA,CAAKL,MAAA,KAAW,GAAG,OAAO,EAAE;MAEhC,MAAMM,KAAA,GAAQD,KAAA,CAAKE,IAAA,CAAI,EAAGC,KAAA,CAAM,KAAK;MACrC,MAAMZ,KAAA,GAAQ,IAAIa,KAAA,CAAMH,KAAA,CAAMN,MAAM;MAEpC,SAASF,CAAA,GAAI,GAAGC,CAAA,GAAIO,KAAA,CAAMN,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5CF,KAAA,CAAME,CAAC,IAAIe,QAAA,CAASP,KAAA,CAAMR,CAAC,CAAC;MAC7B;MAED,OAAOF,KAAA;IACR;IAED,SAASkB,QAAQT,KAAA,EAAM;MACrB,OAAOA,KAAA,CAAKU,SAAA,CAAU,CAAC;IACxB;IAED,SAASC,WAAA,EAAa;MACpB,OAAO,mBAAmBC,KAAA;IAC3B;IAED,SAASC,QAAQC,MAAA,EAAQ;MACvB,OAAOC,MAAA,CAAOC,IAAA,CAAKF,MAAM,EAAEnB,MAAA,KAAW;IACvC;IAID,SAASsB,WAAW5B,IAAA,EAAK;MACvB,OAAO;QACL6B,IAAA,EAAMC,cAAA,CAAe/B,oBAAA,CAAqBC,IAAA,EAAK,MAAM,EAAE,CAAC,CAAC;QACzD+B,MAAA,EAAQC,gBAAA,CAAiBjC,oBAAA,CAAqBC,IAAA,EAAK,SAAS,EAAE,CAAC,CAAC;MACjE;IACF;IAED,SAAS8B,eAAe9B,IAAA,EAAK;MAC3B,IAAIA,IAAA,KAAQ,UAAaA,IAAA,CAAIiC,YAAA,CAAa,OAAO,MAAM,MAAM;QAC3D,OAAOhB,UAAA,CAAWjB,IAAA,CAAIkC,YAAA,CAAa,OAAO,CAAC;MACnD,OAAa;QACL,OAAO;MACR;IACF;IAED,SAASF,iBAAiBhC,IAAA,EAAK;MAC7B,OAAOA,IAAA,KAAQ,SAAYA,IAAA,CAAImC,WAAA,GAAc;IAC9C;IAID,SAASC,aAAapC,IAAA,EAAKqC,WAAA,EAAa7B,QAAA,EAAU8B,MAAA,EAAQ;MACxD,MAAMC,QAAA,GAAUxC,oBAAA,CAAqBC,IAAA,EAAKqC,WAAW,EAAE,CAAC;MAExD,IAAIE,QAAA,KAAY,QAAW;QACzB,MAAMC,QAAA,GAAWzC,oBAAA,CAAqBwC,QAAA,EAAS/B,QAAQ;QAEvD,SAASJ,CAAA,GAAI,GAAGA,CAAA,GAAIoC,QAAA,CAASlC,MAAA,EAAQF,CAAA,IAAK;UACxCkC,MAAA,CAAOE,QAAA,CAASpC,CAAC,CAAC;QACnB;MACF;IACF;IAED,SAASqC,aAAaC,IAAA,EAAMC,OAAA,EAAS;MACnC,WAAW1C,IAAA,IAAQyC,IAAA,EAAM;QACvB,MAAMjB,MAAA,GAASiB,IAAA,CAAKzC,IAAI;QACxBwB,MAAA,CAAOmB,KAAA,GAAQD,OAAA,CAAQD,IAAA,CAAKzC,IAAI,CAAC;MAClC;IACF;IAID,SAAS4C,SAASH,IAAA,EAAMC,OAAA,EAAS;MAC/B,IAAID,IAAA,CAAKE,KAAA,KAAU,QAAW,OAAOF,IAAA,CAAKE,KAAA;MAE1CF,IAAA,CAAKE,KAAA,GAAQD,OAAA,CAAQD,IAAI;MAEzB,OAAOA,IAAA,CAAKE,KAAA;IACb;IAID,SAASE,eAAe9C,IAAA,EAAK;MAC3B,MAAM0C,IAAA,GAAO;QACXK,OAAA,EAAS,CAAE;QACXC,QAAA,EAAU,CAAE;QACZC,QAAA,EAAU,CAAE;MACb;MAED,IAAIC,WAAA,GAAc;MAElB,SAAS9C,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,IAAIC,EAAA;QAEJ,QAAQ7C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH4C,EAAA,GAAK7C,KAAA,CAAM2B,YAAA,CAAa,IAAI;YAC5BQ,IAAA,CAAKK,OAAA,CAAQK,EAAE,IAAIC,WAAA,CAAY9C,KAAK;YACpC;UAEF,KAAK;YACH6C,EAAA,GAAK7C,KAAA,CAAM2B,YAAA,CAAa,IAAI;YAC5BQ,IAAA,CAAKM,QAAA,CAASI,EAAE,IAAIE,qBAAA,CAAsB/C,KAAK;YAC/C;UAEF,KAAK;YACH6C,EAAA,GAAK7C,KAAA,CAAM2B,YAAA,CAAa,QAAQ;YAChCQ,IAAA,CAAKO,QAAA,CAASG,EAAE,IAAIG,qBAAA,CAAsBhD,KAAK;YAC/C;UAEF,KAAK;YAEHuC,cAAA,CAAevC,KAAK;YACpB2C,WAAA,GAAc;YACd;UAEF;YACEtD,OAAA,CAAQ4D,GAAA,CAAIjD,KAAK;QACpB;MACF;MAED,IAAI2C,WAAA,KAAgB,OAAO;QAGzBO,OAAA,CAAQC,UAAA,CAAW1D,IAAA,CAAIkC,YAAA,CAAa,IAAI,KAAKyB,SAAA,CAAUC,YAAA,CAAc,KAAIlB,IAAA;MAC1E;IACF;IAED,SAASY,sBAAsBtD,IAAA,EAAK;MAClC,MAAM0C,IAAA,GAAO;QACXmB,MAAA,EAAQ,CAAE;MACX;MAED,SAASzD,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAM4C,EAAA,GAAKhC,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YAC/C,MAAM4B,QAAA,GAAWvD,KAAA,CAAM2B,YAAA,CAAa,UAAU;YAC9CQ,IAAA,CAAKmB,MAAA,CAAOC,QAAQ,IAAIV,EAAA;YACxB;QACH;MACF;MAED,OAAOV,IAAA;IACR;IAED,SAASa,sBAAsBvD,IAAA,EAAK;MAClC,MAAM0C,IAAA,GAAO,CAAE;MAEf,MAAMqB,MAAA,GAAS/D,IAAA,CAAIkC,YAAA,CAAa,QAAQ;MAIxC,IAAItB,KAAA,GAAQmD,MAAA,CAAOjD,KAAA,CAAM,GAAG;MAE5B,MAAMsC,EAAA,GAAKxC,KAAA,CAAMoD,KAAA,CAAO;MACxB,IAAIC,GAAA,GAAMrD,KAAA,CAAMoD,KAAA,CAAO;MAIvB,MAAME,WAAA,GAAcD,GAAA,CAAIE,OAAA,CAAQ,GAAG,MAAM;MACzC,MAAMC,YAAA,GAAeH,GAAA,CAAIE,OAAA,CAAQ,GAAG,MAAM;MAE1C,IAAIC,YAAA,EAAc;QAGhBxD,KAAA,GAAQqD,GAAA,CAAInD,KAAA,CAAM,GAAG;QACrBmD,GAAA,GAAMrD,KAAA,CAAMoD,KAAA,CAAO;QACnBtB,IAAA,CAAK2B,MAAA,GAASzD,KAAA,CAAMoD,KAAA,CAAO;MAC5B,WAAUE,WAAA,EAAa;QAGtB,MAAMI,OAAA,GAAUL,GAAA,CAAInD,KAAA,CAAM,GAAG;QAC7BmD,GAAA,GAAMK,OAAA,CAAQN,KAAA,CAAO;QAErB,SAAS5D,CAAA,GAAI,GAAGA,CAAA,GAAIkE,OAAA,CAAQhE,MAAA,EAAQF,CAAA,IAAK;UACvCkE,OAAA,CAAQlE,CAAC,IAAIe,QAAA,CAASmD,OAAA,CAAQlE,CAAC,EAAEmE,OAAA,CAAQ,MAAM,EAAE,CAAC;QACnD;QAED7B,IAAA,CAAK4B,OAAA,GAAUA,OAAA;MAChB;MAED5B,IAAA,CAAKU,EAAA,GAAKA,EAAA;MACVV,IAAA,CAAKuB,GAAA,GAAMA,GAAA;MAEXvB,IAAA,CAAKwB,WAAA,GAAcA,WAAA;MACnBxB,IAAA,CAAK0B,YAAA,GAAeA,YAAA;MAEpB1B,IAAA,CAAK8B,OAAA,GAAUpD,OAAA,CAAQpB,IAAA,CAAIkC,YAAA,CAAa,QAAQ,CAAC;MAEjD,OAAOQ,IAAA;IACR;IAED,SAAS+B,eAAe/B,IAAA,EAAM;MAC5B,MAAMgC,MAAA,GAAS,EAAE;MAEjB,MAAMzB,QAAA,GAAWP,IAAA,CAAKO,QAAA;MACtB,MAAMD,QAAA,GAAWN,IAAA,CAAKM,QAAA;MACtB,MAAMD,OAAA,GAAUL,IAAA,CAAKK,OAAA;MAErB,WAAWgB,MAAA,IAAUd,QAAA,EAAU;QAC7B,IAAIA,QAAA,CAAS0B,cAAA,CAAeZ,MAAM,GAAG;UACnC,MAAMa,OAAA,GAAU3B,QAAA,CAASc,MAAM;UAC/B,MAAMS,OAAA,GAAUxB,QAAA,CAAS4B,OAAA,CAAQJ,OAAO;UAExC,MAAMK,OAAA,GAAUL,OAAA,CAAQX,MAAA,CAAOiB,KAAA;UAC/B,MAAMC,QAAA,GAAWP,OAAA,CAAQX,MAAA,CAAOmB,MAAA;UAEhC,MAAMC,WAAA,GAAclC,OAAA,CAAQ8B,OAAO;UACnC,MAAMK,YAAA,GAAenC,OAAA,CAAQgC,QAAQ;UAErC,MAAMI,SAAA,GAAYC,qBAAA,CAAsBR,OAAA,EAASK,WAAA,EAAaC,YAAY;UAE1EG,oBAAA,CAAqBF,SAAA,EAAWT,MAAM;QACvC;MACF;MAED,OAAOA,MAAA;IACR;IAED,SAASY,aAAalC,EAAA,EAAI;MACxB,OAAOP,QAAA,CAASY,OAAA,CAAQC,UAAA,CAAWN,EAAE,GAAGqB,cAAc;IACvD;IAED,SAASW,sBAAsBR,OAAA,EAASK,WAAA,EAAaC,YAAA,EAAc;MACjE,MAAMK,IAAA,GAAO9B,OAAA,CAAQ+B,KAAA,CAAMZ,OAAA,CAAQxB,EAAE;MACrC,MAAMqC,QAAA,GAAWC,OAAA,CAAQH,IAAA,CAAKnC,EAAE;MAEhC,MAAMuC,SAAA,GAAYJ,IAAA,CAAKK,UAAA,CAAWhB,OAAA,CAAQX,GAAG;MAC7C,MAAM4B,aAAA,GAAgBN,IAAA,CAAKO,MAAA,CAAOC,KAAA,CAAK,EAAGC,SAAA,CAAW;MAErD,IAAIC,IAAA,EAAMC,MAAA;MACV,IAAI9F,CAAA,EAAG+F,EAAA,EAAIC,CAAA,EAAGC,EAAA;MAEd,MAAM3D,IAAA,GAAO,CAAE;MAKf,QAAQiD,SAAA;QACN,KAAK;UACH,KAAKvF,CAAA,GAAI,GAAG+F,EAAA,GAAKlB,WAAA,CAAY/E,KAAA,CAAMI,MAAA,EAAQF,CAAA,GAAI+F,EAAA,EAAI/F,CAAA,IAAK;YACtD6F,IAAA,GAAOhB,WAAA,CAAY/E,KAAA,CAAME,CAAC;YAC1B8F,MAAA,GAAS9F,CAAA,GAAI8E,YAAA,CAAagB,MAAA;YAE1B,IAAIxD,IAAA,CAAKuD,IAAI,MAAM,QAAWvD,IAAA,CAAKuD,IAAI,IAAI,CAAE;YAE7C,IAAIrB,OAAA,CAAQV,WAAA,KAAgB,MAAM;cAChC,MAAMoC,KAAA,GAAQpB,YAAA,CAAahF,KAAA,CAAMgG,MAAM;cACvC,MAAMK,KAAA,GAAQ3B,OAAA,CAAQN,OAAA,CAAQ,CAAC,IAAI,IAAIM,OAAA,CAAQN,OAAA,CAAQ,CAAC;cAExD5B,IAAA,CAAKuD,IAAI,EAAEM,KAAK,IAAID,KAAA;YAClC,OAAmB;cACL,KAAKF,CAAA,GAAI,GAAGC,EAAA,GAAKnB,YAAA,CAAagB,MAAA,EAAQE,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;gBACjD1D,IAAA,CAAKuD,IAAI,EAAEG,CAAC,IAAIlB,YAAA,CAAahF,KAAA,CAAMgG,MAAA,GAASE,CAAC;cAC9C;YACF;UACF;UAED;QAEF,KAAK;UACHxG,OAAA,CAAQ4G,IAAA,CAAK,2EAA2Eb,SAAS;UACjG;QAEF,KAAK;UACH/F,OAAA,CAAQ4G,IAAA,CAAK,2EAA2Eb,SAAS;UACjG;QAEF,KAAK;UACH/F,OAAA,CAAQ4G,IAAA,CAAK,2EAA2Eb,SAAS;UACjG;MACH;MAED,MAAMc,SAAA,GAAYC,oBAAA,CAAqBhE,IAAA,EAAMmD,aAAa;MAE1D,MAAMV,SAAA,GAAY;QAChBlF,IAAA,EAAMwF,QAAA,CAASkB,IAAA;QACfF;MACD;MAED,OAAOtB,SAAA;IACR;IAED,SAASuB,qBAAqBhE,IAAA,EAAMmD,aAAA,EAAe;MACjD,MAAMY,SAAA,GAAY,EAAE;MAIpB,WAAWR,IAAA,IAAQvD,IAAA,EAAM;QACvB+D,SAAA,CAAUhG,IAAA,CAAK;UAAEwF,IAAA,EAAMhF,UAAA,CAAWgF,IAAI;UAAGK,KAAA,EAAO5D,IAAA,CAAKuD,IAAI;QAAA,CAAG;MAC7D;MAIDQ,SAAA,CAAUG,IAAA,CAAKC,SAAS;MAIxB,SAASzG,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAIA,CAAA,IAAK;QAC3B0G,sBAAA,CAAuBL,SAAA,EAAWrG,CAAA,EAAGyF,aAAA,CAAcrD,QAAA,CAASpC,CAAC,CAAC;MAC/D;MAED,OAAOqG,SAAA;MAIP,SAASI,UAAUE,CAAA,EAAGC,CAAA,EAAG;QACvB,OAAOD,CAAA,CAAEd,IAAA,GAAOe,CAAA,CAAEf,IAAA;MACnB;IACF;IAED,MAAMgB,QAAA,GAAW,IAAIC,OAAA,CAAS;IAC9B,MAAMC,KAAA,GAAQ,IAAID,OAAA,CAAS;IAC3B,MAAME,UAAA,GAAa,IAAIC,UAAA,CAAY;IAEnC,SAAShC,qBAAqBF,SAAA,EAAWT,MAAA,EAAQ;MAC/C,MAAM+B,SAAA,GAAYtB,SAAA,CAAUsB,SAAA;MAC5B,MAAMxG,IAAA,GAAOkF,SAAA,CAAUlF,IAAA;MAEvB,MAAMqH,KAAA,GAAQ,EAAE;MAChB,MAAMC,YAAA,GAAe,EAAE;MACvB,MAAMC,cAAA,GAAiB,EAAE;MACzB,MAAMC,SAAA,GAAY,EAAE;MAEpB,SAASrH,CAAA,GAAI,GAAGC,CAAA,GAAIoG,SAAA,CAAUnG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAChD,MAAMsH,QAAA,GAAWjB,SAAA,CAAUrG,CAAC;QAE5B,MAAM6F,IAAA,GAAOyB,QAAA,CAASzB,IAAA;QACtB,MAAMK,KAAA,GAAQoB,QAAA,CAASpB,KAAA;QAEvBR,MAAA,CAAO6B,SAAA,CAAUrB,KAAK,EAAEN,SAAA,CAAW;QACnCF,MAAA,CAAO8B,SAAA,CAAUX,QAAA,EAAUG,UAAA,EAAYD,KAAK;QAE5CG,KAAA,CAAM7G,IAAA,CAAKwF,IAAI;QACfsB,YAAA,CAAa9G,IAAA,CAAKwG,QAAA,CAASY,CAAA,EAAGZ,QAAA,CAASa,CAAA,EAAGb,QAAA,CAASc,CAAC;QACpDP,cAAA,CAAe/G,IAAA,CAAK2G,UAAA,CAAWS,CAAA,EAAGT,UAAA,CAAWU,CAAA,EAAGV,UAAA,CAAWW,CAAA,EAAGX,UAAA,CAAWY,CAAC;QAC1EP,SAAA,CAAUhH,IAAA,CAAK0G,KAAA,CAAMU,CAAA,EAAGV,KAAA,CAAMW,CAAA,EAAGX,KAAA,CAAMY,CAAC;MACzC;MAED,IAAIR,YAAA,CAAajH,MAAA,GAAS,GAAGoE,MAAA,CAAOjE,IAAA,CAAK,IAAIwH,mBAAA,CAAoBhI,IAAA,GAAO,aAAaqH,KAAA,EAAOC,YAAY,CAAC;MACzG,IAAIC,cAAA,CAAelH,MAAA,GAAS,GAAG;QAC7BoE,MAAA,CAAOjE,IAAA,CAAK,IAAIyH,uBAAA,CAAwBjI,IAAA,GAAO,eAAeqH,KAAA,EAAOE,cAAc,CAAC;MACrF;MACD,IAAIC,SAAA,CAAUnH,MAAA,GAAS,GAAGoE,MAAA,CAAOjE,IAAA,CAAK,IAAIwH,mBAAA,CAAoBhI,IAAA,GAAO,UAAUqH,KAAA,EAAOG,SAAS,CAAC;MAEhG,OAAO/C,MAAA;IACR;IAED,SAASoC,uBAAuBL,SAAA,EAAW0B,QAAA,EAAUC,YAAA,EAAc;MACjE,IAAIV,QAAA;MAEJ,IAAIW,KAAA,GAAQ;MACZ,IAAIjI,CAAA,EAAGC,CAAA;MAIP,KAAKD,CAAA,GAAI,GAAGC,CAAA,GAAIoG,SAAA,CAAUnG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5CsH,QAAA,GAAWjB,SAAA,CAAUrG,CAAC;QAEtB,IAAIsH,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,MAAM,QAAW;UAC1CT,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,IAAI;QACrC,OAAe;UACLE,KAAA,GAAQ;QACT;MACF;MAED,IAAIA,KAAA,KAAU,MAAM;QAGlB,KAAKjI,CAAA,GAAI,GAAGC,CAAA,GAAIoG,SAAA,CAAUnG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UAC5CsH,QAAA,GAAWjB,SAAA,CAAUrG,CAAC;UAEtBsH,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,IAAIC,YAAA;QAC5B;MACT,OAAa;QAGLE,sBAAA,CAAuB7B,SAAA,EAAW0B,QAAQ;MAC3C;IACF;IAED,SAASG,uBAAuB7B,SAAA,EAAW0B,QAAA,EAAU;MACnD,IAAII,IAAA,EAAMC,IAAA;MAEV,SAASpI,CAAA,GAAI,GAAGC,CAAA,GAAIoG,SAAA,CAAUnG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAChD,MAAMsH,QAAA,GAAWjB,SAAA,CAAUrG,CAAC;QAE5B,IAAIsH,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,MAAM,MAAM;UACrCI,IAAA,GAAOE,OAAA,CAAQhC,SAAA,EAAWrG,CAAA,EAAG+H,QAAQ;UACrCK,IAAA,GAAOE,OAAA,CAAQjC,SAAA,EAAWrG,CAAA,EAAG+H,QAAQ;UAErC,IAAII,IAAA,KAAS,MAAM;YACjBb,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,IAAIK,IAAA,CAAKlC,KAAA,CAAM6B,QAAQ;YAC9C;UACD;UAED,IAAIK,IAAA,KAAS,MAAM;YACjBd,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,IAAII,IAAA,CAAKjC,KAAA,CAAM6B,QAAQ;YAC9C;UACD;UAEDQ,WAAA,CAAYjB,QAAA,EAAUa,IAAA,EAAMC,IAAA,EAAML,QAAQ;QAC3C;MACF;IACF;IAED,SAASM,QAAQhC,SAAA,EAAWrG,CAAA,EAAG+H,QAAA,EAAU;MACvC,OAAO/H,CAAA,IAAK,GAAG;QACb,MAAMsH,QAAA,GAAWjB,SAAA,CAAUrG,CAAC;QAE5B,IAAIsH,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,MAAM,MAAM,OAAOT,QAAA;QAE9CtH,CAAA;MACD;MAED,OAAO;IACR;IAED,SAASsI,QAAQjC,SAAA,EAAWrG,CAAA,EAAG+H,QAAA,EAAU;MACvC,OAAO/H,CAAA,GAAIqG,SAAA,CAAUnG,MAAA,EAAQ;QAC3B,MAAMoH,QAAA,GAAWjB,SAAA,CAAUrG,CAAC;QAE5B,IAAIsH,QAAA,CAASpB,KAAA,CAAM6B,QAAQ,MAAM,MAAM,OAAOT,QAAA;QAE9CtH,CAAA;MACD;MAED,OAAO;IACR;IAED,SAASuI,YAAYC,GAAA,EAAKL,IAAA,EAAMC,IAAA,EAAML,QAAA,EAAU;MAC9C,IAAIK,IAAA,CAAKvC,IAAA,GAAOsC,IAAA,CAAKtC,IAAA,KAAS,GAAG;QAC/B2C,GAAA,CAAItC,KAAA,CAAM6B,QAAQ,IAAII,IAAA,CAAKjC,KAAA,CAAM6B,QAAQ;QACzC;MACD;MAEDS,GAAA,CAAItC,KAAA,CAAM6B,QAAQ,KACdS,GAAA,CAAI3C,IAAA,GAAOsC,IAAA,CAAKtC,IAAA,KAASuC,IAAA,CAAKlC,KAAA,CAAM6B,QAAQ,IAAII,IAAA,CAAKjC,KAAA,CAAM6B,QAAQ,MAAOK,IAAA,CAAKvC,IAAA,GAAOsC,IAAA,CAAKtC,IAAA,IAC7FsC,IAAA,CAAKjC,KAAA,CAAM6B,QAAQ;IACtB;IAID,SAASU,mBAAmB7I,IAAA,EAAK;MAC/B,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,IAAI,KAAK;QAChC4G,KAAA,EAAO7H,UAAA,CAAWjB,IAAA,CAAIkC,YAAA,CAAa,OAAO,KAAK,CAAC;QAChD6G,GAAA,EAAK9H,UAAA,CAAWjB,IAAA,CAAIkC,YAAA,CAAa,KAAK,KAAK,CAAC;QAC5CwB,UAAA,EAAY;MACb;MAED,SAAStD,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKgB,UAAA,CAAWjD,IAAA,CAAKW,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,CAAC;YACvD;QACH;MACF;MAEDuB,OAAA,CAAQuF,KAAA,CAAMhJ,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IACzC;IAED,SAASuG,mBAAmBvG,IAAA,EAAM;MAChC,MAAMgC,MAAA,GAAS,EAAE;MAEjB,MAAMzE,IAAA,GAAOyC,IAAA,CAAKzC,IAAA;MAClB,MAAMiJ,QAAA,GAAWxG,IAAA,CAAKqG,GAAA,GAAMrG,IAAA,CAAKoG,KAAA,IAAS;MAC1C,MAAMK,WAAA,GAAazG,IAAA,CAAKgB,UAAA;MAExB,SAAStD,CAAA,GAAI,GAAG+F,EAAA,GAAKgD,WAAA,CAAW7I,MAAA,EAAQF,CAAA,GAAI+F,EAAA,EAAI/F,CAAA,IAAK;QACnD,MAAMgJ,eAAA,GAAkB9D,YAAA,CAAa6D,WAAA,CAAW/I,CAAC,CAAC;QAElD,SAASgG,CAAA,GAAI,GAAGC,EAAA,GAAK+C,eAAA,CAAgB9I,MAAA,EAAQ8F,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACxD1B,MAAA,CAAOjE,IAAA,CAAK2I,eAAA,CAAgBhD,CAAC,CAAC;QAC/B;MACF;MAED,OAAO,IAAIiD,aAAA,CAAcpJ,IAAA,EAAMiJ,QAAA,EAAUxE,MAAM;IAChD;IAED,SAAS4E,iBAAiBlG,EAAA,EAAI;MAC5B,OAAOP,QAAA,CAASY,OAAA,CAAQuF,KAAA,CAAM5F,EAAE,GAAG6F,kBAAkB;IACtD;IAID,SAASM,gBAAgBvJ,IAAA,EAAK;MAC5B,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YAEHkC,IAAA,CAAKU,EAAA,GAAKhC,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YAC9CQ,IAAA,CAAK8G,IAAA,GAAOC,SAAA,CAAUlJ,KAAK;YAC3B;UAEF,KAAK;YACHmC,IAAA,CAAKU,EAAA,GAAKhC,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YAC9CtC,OAAA,CAAQ4G,IAAA,CAAK,gEAAgE;YAC7E;QACH;MACF;MAED/C,OAAA,CAAQiG,WAAA,CAAY1J,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAC/C;IAED,SAAS+G,UAAUzJ,IAAA,EAAK;MACtB,MAAM0C,IAAA,GAAO;QACXK,OAAA,EAAS,CAAE;MACZ;MAED,SAAS3C,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKiH,eAAA,GAAkB3I,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACpD;UAEF,KAAK;YACH,MAAMiB,EAAA,GAAK7C,KAAA,CAAM2B,YAAA,CAAa,IAAI;YAClCQ,IAAA,CAAKK,OAAA,CAAQK,EAAE,IAAIC,WAAA,CAAY9C,KAAK;YACpC;UAEF,KAAK;YACHmC,IAAA,CAAKkH,MAAA,GAASC,WAAA,CAAYtJ,KAAK;YAC/B;UAEF,KAAK;YACHmC,IAAA,CAAKoH,aAAA,GAAgBC,kBAAA,CAAmBxJ,KAAK;YAC7C;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASmH,YAAY7J,IAAA,EAAK;MACxB,MAAM0C,IAAA,GAAO;QACXmB,MAAA,EAAQ,CAAE;MACX;MAED,SAASzD,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAMsD,QAAA,GAAWvD,KAAA,CAAM2B,YAAA,CAAa,UAAU;YAC9C,MAAMkB,EAAA,GAAKhC,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YAC/CQ,IAAA,CAAKmB,MAAA,CAAOC,QAAQ,IAAIV,EAAA;YACxB;QACH;MACF;MAED,OAAOV,IAAA;IACR;IAED,SAASqH,mBAAmB/J,IAAA,EAAK;MAC/B,MAAM0C,IAAA,GAAO;QACXmB,MAAA,EAAQ,CAAE;MACX;MAED,SAASzD,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAMsD,QAAA,GAAWvD,KAAA,CAAM2B,YAAA,CAAa,UAAU;YAC9C,MAAMkB,EAAA,GAAKhC,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YAC/C,MAAM8H,MAAA,GAAS7I,QAAA,CAASZ,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YACpDQ,IAAA,CAAKmB,MAAA,CAAOC,QAAQ,IAAI;cAAEV,EAAA;cAAQ4G;YAAgB;YAClD;UAEF,KAAK;YACHtH,IAAA,CAAKuH,MAAA,GAAS/I,SAAA,CAAUX,KAAA,CAAM4B,WAAW;YACzC;UAEF,KAAK;YACHO,IAAA,CAAKwH,CAAA,GAAIhJ,SAAA,CAAUX,KAAA,CAAM4B,WAAW;YACpC;QACH;MACF;MAED,OAAOO,IAAA;IACR;IAED,SAASyH,gBAAgBzH,IAAA,EAAM;MAC7B,MAAME,KAAA,GAAQ;QACZQ,EAAA,EAAIV,IAAA,CAAKU;MACV;MAED,MAAMgH,QAAA,GAAW3G,OAAA,CAAQ4G,UAAA,CAAWzH,KAAA,CAAMQ,EAAE;MAE5C,IAAIV,IAAA,CAAK8G,IAAA,KAAS,QAAW;QAC3B5G,KAAA,CAAM4G,IAAA,GAAOc,SAAA,CAAU5H,IAAA,CAAK8G,IAAI;QAIhCY,QAAA,CAASrH,OAAA,CAAQwH,WAAA,GAAc3H,KAAA,CAAM4G,IAAA,CAAKlF,OAAA;QAC1C8F,QAAA,CAASrH,OAAA,CAAQyH,WAAA,GAAc5H,KAAA,CAAM4G,IAAA,CAAKiB,OAAA;MAC3C;MAED,OAAO7H,KAAA;IACR;IAED,SAAS0H,UAAU5H,IAAA,EAAM;MACvB,MAAMgI,UAAA,GAAa;MAEnB,MAAM9H,KAAA,GAAQ;QACZgH,MAAA,EAAQ,EAAE;QAAA;QACVtF,OAAA,EAAS;UACPpE,KAAA,EAAO,EAAE;UACTgG,MAAA,EAAQwE;QACT;QACDD,OAAA,EAAS;UACPvK,KAAA,EAAO,EAAE;UACTgG,MAAA,EAAQwE;QACT;MACF;MAED,MAAM3H,OAAA,GAAUL,IAAA,CAAKK,OAAA;MACrB,MAAM+G,aAAA,GAAgBpH,IAAA,CAAKoH,aAAA;MAE3B,MAAMG,MAAA,GAASH,aAAA,CAAcG,MAAA;MAC7B,MAAMC,CAAA,GAAIJ,aAAA,CAAcI,CAAA;MACxB,MAAMS,WAAA,GAAcb,aAAA,CAAcjG,MAAA,CAAO+G,KAAA,CAAMZ,MAAA;MAC/C,MAAMa,YAAA,GAAef,aAAA,CAAcjG,MAAA,CAAOiH,MAAA,CAAOd,MAAA;MAEjD,MAAMe,WAAA,GAAcrI,IAAA,CAAKK,OAAA,CAAQL,IAAA,CAAKkH,MAAA,CAAO/F,MAAA,CAAO+G,KAAK;MACzD,MAAMI,aAAA,GAAgBtI,IAAA,CAAKK,OAAA,CAAQL,IAAA,CAAKkH,MAAA,CAAO/F,MAAA,CAAOoH,eAAe;MAErE,MAAMR,OAAA,GAAU1H,OAAA,CAAQ+G,aAAA,CAAcjG,MAAA,CAAOiH,MAAA,CAAO1H,EAAE,EAAElD,KAAA;MACxD,IAAIgG,MAAA,GAAS;MAEb,IAAI9F,CAAA,EAAGgG,CAAA,EAAG/F,CAAA;MAIV,KAAKD,CAAA,GAAI,GAAGC,CAAA,GAAI4J,MAAA,CAAO3J,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACzC,MAAM8K,UAAA,GAAajB,MAAA,CAAO7J,CAAC;QAC3B,MAAM+K,cAAA,GAAiB,EAAE;QAEzB,KAAK/E,CAAA,GAAI,GAAGA,CAAA,GAAI8E,UAAA,EAAY9E,CAAA,IAAK;UAC/B,MAAMgF,SAAA,GAAYlB,CAAA,CAAEhE,MAAA,GAASyE,WAAW;UACxC,MAAMU,QAAA,GAAWnB,CAAA,CAAEhE,MAAA,GAAS2E,YAAY;UACxC,MAAMS,UAAA,GAAab,OAAA,CAAQY,QAAQ;UAEnCF,cAAA,CAAe1K,IAAA,CAAK;YAAE8F,KAAA,EAAO6E,SAAA;YAAWG,MAAA,EAAQD;UAAA,CAAY;UAE5DpF,MAAA,IAAU;QACX;QAKDiF,cAAA,CAAevE,IAAA,CAAK4E,UAAU;QAK9B,KAAKpF,CAAA,GAAI,GAAGA,CAAA,GAAIsE,UAAA,EAAYtE,CAAA,IAAK;UAC/B,MAAMqF,CAAA,GAAIN,cAAA,CAAe/E,CAAC;UAE1B,IAAIqF,CAAA,KAAM,QAAW;YACnB7I,KAAA,CAAM0B,OAAA,CAAQpE,KAAA,CAAMO,IAAA,CAAKgL,CAAA,CAAElF,KAAK;YAChC3D,KAAA,CAAM6H,OAAA,CAAQvK,KAAA,CAAMO,IAAA,CAAKgL,CAAA,CAAEF,MAAM;UAC7C,OAAiB;YACL3I,KAAA,CAAM0B,OAAA,CAAQpE,KAAA,CAAMO,IAAA,CAAK,CAAC;YAC1BmC,KAAA,CAAM6H,OAAA,CAAQvK,KAAA,CAAMO,IAAA,CAAK,CAAC;UAC3B;QACF;MACF;MAID,IAAIiC,IAAA,CAAKiH,eAAA,EAAiB;QACxB/G,KAAA,CAAM8I,UAAA,GAAa,IAAIC,OAAA,CAAS,EAAChE,SAAA,CAAUjF,IAAA,CAAKiH,eAAe,EAAE3D,SAAA,CAAW;MACpF,OAAa;QACLpD,KAAA,CAAM8I,UAAA,GAAa,IAAIC,OAAA,CAAO,EAAGC,QAAA,CAAU;MAC5C;MAID,KAAKxL,CAAA,GAAI,GAAGC,CAAA,GAAI0K,WAAA,CAAY7K,KAAA,CAAMI,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACpD,MAAMH,IAAA,GAAO8K,WAAA,CAAY7K,KAAA,CAAME,CAAC;QAChC,MAAMyL,WAAA,GAAc,IAAIF,OAAA,CAAS,EAAChE,SAAA,CAAUqD,aAAA,CAAc9K,KAAA,EAAOE,CAAA,GAAI4K,aAAA,CAAc9E,MAAM,EAAEF,SAAA,CAAW;QAEtGpD,KAAA,CAAMgH,MAAA,CAAOnJ,IAAA,CAAK;UAAER,IAAA;UAAY4L;QAAA,CAA0B;MAC3D;MAED,OAAOjJ,KAAA;MAIP,SAAS4I,WAAWzE,CAAA,EAAGC,CAAA,EAAG;QACxB,OAAOA,CAAA,CAAEuE,MAAA,GAASxE,CAAA,CAAEwE,MAAA;MACrB;IACF;IAED,SAASO,cAAc1I,EAAA,EAAI;MACzB,OAAOP,QAAA,CAASY,OAAA,CAAQiG,WAAA,CAAYtG,EAAE,GAAG+G,eAAe;IACzD;IAID,SAAS4B,WAAW/L,IAAA,EAAK;MACvB,MAAM0C,IAAA,GAAO;QACXsJ,SAAA,EAAWjM,oBAAA,CAAqBC,IAAA,EAAK,WAAW,EAAE,CAAC,EAAEmC;MACtD;MAEDsB,OAAA,CAAQwI,MAAA,CAAOjM,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAC1C;IAED,SAASwJ,WAAWxJ,IAAA,EAAM;MACxB,IAAIA,IAAA,CAAKE,KAAA,KAAU,QAAW,OAAOF,IAAA,CAAKE,KAAA;MAE1C,OAAOF,IAAA,CAAKsJ,SAAA;IACb;IAED,SAASG,SAAS/I,EAAA,EAAI;MACpB,MAAMV,IAAA,GAAOe,OAAA,CAAQwI,MAAA,CAAO7I,EAAE;MAE9B,IAAIV,IAAA,KAAS,QAAW;QACtB,OAAOG,QAAA,CAASH,IAAA,EAAMwJ,UAAU;MACjC;MAEDtM,OAAA,CAAQ4G,IAAA,CAAK,qDAAqDpD,EAAE;MAEpE,OAAO;IACR;IAID,SAASgJ,YAAYpM,IAAA,EAAK;MACxB,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAK2J,OAAA,GAAUC,wBAAA,CAAyB/L,KAAK;YAC7C;QACH;MACF;MAEDkD,OAAA,CAAQ8I,OAAA,CAAQvM,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAC3C;IAED,SAAS4J,yBAAyBtM,IAAA,EAAK;MACrC,MAAM0C,IAAA,GAAO;QACX8J,QAAA,EAAU,CAAE;QACZxJ,QAAA,EAAU,CAAE;MACb;MAED,SAAS5C,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHiM,mBAAA,CAAoBlM,KAAA,EAAOmC,IAAI;YAC/B;UAEF,KAAK;YACHA,IAAA,CAAKgK,SAAA,GAAYC,oBAAA,CAAqBpM,KAAK;YAC3C;UAEF,KAAK;YACHmC,IAAA,CAAKkK,KAAA,GAAQC,gBAAA,CAAiBtM,KAAK;YACnC;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAAS+J,oBAAoBzM,IAAA,EAAK0C,IAAA,EAAM;MACtC,MAAMuB,GAAA,GAAMjE,IAAA,CAAIkC,YAAA,CAAa,KAAK;MAElC,SAAS9B,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAK8J,QAAA,CAASvI,GAAG,IAAI6I,kBAAA,CAAmBvM,KAAK;YAC7C;UAEF,KAAK;YACHmC,IAAA,CAAKM,QAAA,CAASiB,GAAG,IAAI8I,kBAAA,CAAmBxM,KAAK;YAC7C;QACH;MACF;IACF;IAED,SAASuM,mBAAmB9M,IAAA,EAAK;MAC/B,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKsJ,SAAA,GAAYzL,KAAA,CAAM4B,WAAA;YACvB;QACH;MACF;MAED,OAAOO,IAAA;IACR;IAED,SAASqK,mBAAmB/M,IAAA,EAAK;MAC/B,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKsK,MAAA,GAASzM,KAAA,CAAM4B,WAAA;YACpB;QACH;MACF;MAED,OAAOO,IAAA;IACR;IAED,SAASiK,qBAAqB3M,IAAA,EAAK;MACjC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YACHkC,IAAA,CAAKuK,IAAA,GAAO1M,KAAA,CAAMC,QAAA;YAClBkC,IAAA,CAAKwK,UAAA,GAAaC,qBAAA,CAAsB5M,KAAK;YAC7C;UAEF,KAAK;YACHmC,IAAA,CAAKkK,KAAA,GAAQC,gBAAA,CAAiBtM,KAAK;YACnC;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASyK,sBAAsBnN,IAAA,EAAK;MAClC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YACHkC,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAI4M,oBAAA,CAAqB7M,KAAK;YACjD;UACF,KAAK;YACHmC,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAI;cACrB6M,MAAA,EAAQ9M,KAAA,CAAM0B,YAAA,CAAa,QAAQ,IAAI1B,KAAA,CAAM2B,YAAA,CAAa,QAAQ,IAAI;cACtEQ,IAAA,EAAM0K,oBAAA,CAAqB7M,KAAK;YACjC;YACD;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAAS0K,qBAAqBpN,IAAA,EAAK;MACjC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAIQ,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACpD;UAEF,KAAK;YACHO,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAIS,UAAA,CAAWV,KAAA,CAAM4B,WAAW;YACnD;UAEF,KAAK;YACHO,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAI;cAAE4C,EAAA,EAAI7C,KAAA,CAAM2B,YAAA,CAAa,SAAS;cAAG0K,KAAA,EAAOU,2BAAA,CAA4B/M,KAAK;YAAG;YACvG;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAAS4K,4BAA4BtN,IAAA,EAAK;MACxC,MAAM0C,IAAA,GAAO;QACXgK,SAAA,EAAW,CAAE;MACd;MAED,SAAStM,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH+M,gCAAA,CAAiChN,KAAA,EAAOmC,IAAI;YAC5C;QACH;MACF;MAED,OAAOA,IAAA;IACR;IAED,SAAS6K,iCAAiCvN,IAAA,EAAK0C,IAAA,EAAM;MACnD,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHgN,yCAAA,CAA0CjN,KAAA,EAAOmC,IAAI;YACrD;QACH;MACF;IACF;IAED,SAAS8K,0CAA0CxN,IAAA,EAAK0C,IAAA,EAAM;MAC5D,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YACHkC,IAAA,CAAKgK,SAAA,CAAUnM,KAAA,CAAMC,QAAQ,IAAIS,UAAA,CAAWV,KAAA,CAAM4B,WAAW;YAC7D;UAEF,KAAK;UACL,KAAK;YAGH,IAAI5B,KAAA,CAAM4B,WAAA,CAAYsL,WAAA,CAAW,MAAO,QAAQ;cAC9C/K,IAAA,CAAKgK,SAAA,CAAUnM,KAAA,CAAMC,QAAQ,IAAI;YAClC,WAAUD,KAAA,CAAM4B,WAAA,CAAYsL,WAAA,CAAW,MAAO,SAAS;cACtD/K,IAAA,CAAKgK,SAAA,CAAUnM,KAAA,CAAMC,QAAQ,IAAI;YAC/C,OAAmB;cACLkC,IAAA,CAAKgK,SAAA,CAAUnM,KAAA,CAAMC,QAAQ,IAAIW,QAAA,CAASZ,KAAA,CAAM4B,WAAW;YAC5D;YAED;UAEF,KAAK;YACHO,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAIkN,6BAAA,CAA8BnN,KAAK;YAC1D;QACH;MACF;IACF;IAED,SAASsM,iBAAiB7M,IAAA,EAAK;MAC7B,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKgK,SAAA,GAAYiB,yBAAA,CAA0BpN,KAAK;YAChD;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASiL,0BAA0B3N,IAAA,EAAK;MACtC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAIW,QAAA,CAASZ,KAAA,CAAM4B,WAAW;YACjD;UAEF,KAAK;YACHO,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAIkN,6BAAA,CAA8BnN,KAAK;YAC1D;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASgL,8BAA8B1N,IAAA,EAAK;MAC1C,IAAI0C,IAAA,GAAO,CAAE;MAEb,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,IAAIG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE5B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAI;cACrB4C,EAAA,EAAI7C,KAAA,CAAM2B,YAAA,CAAa,SAAS;cAChC0L,QAAA,EAAUrN,KAAA,CAAM2B,YAAA,CAAa,UAAU;cACvC0K,KAAA,EAAOU,2BAAA,CAA4B/M,KAAK;YACzC;YACD;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASmL,YAAYnL,IAAA,EAAM;MACzB,OAAOA,IAAA;IACR;IAED,SAASoL,UAAU1K,EAAA,EAAI;MACrB,OAAOP,QAAA,CAASY,OAAA,CAAQ8I,OAAA,CAAQnJ,EAAE,GAAGyK,WAAW;IACjD;IAID,SAASE,cAAc/N,IAAA,EAAK;MAC1B,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM;MAC9B;MAED,SAAS9B,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKhE,GAAA,GAAM0C,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC;YAC5C;QACH;MACF;MAEDuB,OAAA,CAAQuK,SAAA,CAAUhO,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAC7C;IAED,SAASuL,iBAAiBC,KAAA,EAAO;MAC/B,IAAIhP,MAAA;MAEJ,IAAIiP,SAAA,GAAYD,KAAA,CAAME,KAAA,EAAQF,KAAA,CAAMG,WAAA,CAAY,GAAG,IAAI,MAAO,KAAK,CAAC;MACpEF,SAAA,GAAYA,SAAA,CAAUG,WAAA,CAAa;MAEnC,QAAQH,SAAA;QACN,KAAK;UACHjP,MAAA,GAASqP,SAAA;UACT;QAEF;UACErP,MAAA,GAASsP,aAAA;MACZ;MAED,OAAOtP,MAAA;IACR;IAED,SAASuP,cAAc/L,IAAA,EAAM;MAC3B,MAAMgM,MAAA,GAASZ,SAAA,CAAUpL,IAAA,CAAKhE,GAAG;MACjC,MAAMgO,SAAA,GAAYgC,MAAA,CAAOrC,OAAA,CAAQK,SAAA;MAEjC,IAAIiC,QAAA;MAEJ,QAAQjC,SAAA,CAAUO,IAAA;QAChB,KAAK;QACL,KAAK;UACH0B,QAAA,GAAW,IAAIC,iBAAA,CAAmB;UAClC;QAEF,KAAK;UACHD,QAAA,GAAW,IAAIE,mBAAA,CAAqB;UACpC;QAEF;UACEF,QAAA,GAAW,IAAIG,iBAAA,CAAmB;UAClC;MACH;MAEDH,QAAA,CAAS1O,IAAA,GAAOyC,IAAA,CAAKzC,IAAA,IAAQ;MAE7B,SAAS8O,WAAWC,aAAA,EAAe;QACjC,MAAMxK,OAAA,GAAUkK,MAAA,CAAOrC,OAAA,CAAQrJ,QAAA,CAASgM,aAAA,CAAc5L,EAAE;QACxD,IAAI8K,KAAA,GAAQ;QAIZ,IAAI1J,OAAA,KAAY,QAAW;UACzB,MAAMyK,OAAA,GAAUP,MAAA,CAAOrC,OAAA,CAAQG,QAAA,CAAShI,OAAA,CAAQwI,MAAM;UACtDkB,KAAA,GAAQ/B,QAAA,CAAS8C,OAAA,CAAQjD,SAAS;QAC5C,OAAe;UACLpM,OAAA,CAAQ4G,IAAA,CAAK,6EAA6E;UAC1F0H,KAAA,GAAQ/B,QAAA,CAAS6C,aAAA,CAAc5L,EAAE;QAClC;QAID,IAAI8K,KAAA,KAAU,MAAM;UAClB,MAAMhP,MAAA,GAAS+O,gBAAA,CAAiBC,KAAK;UAErC,IAAIhP,MAAA,KAAW,QAAW;YACxB,MAAMgQ,OAAA,GAAUhQ,MAAA,CAAOT,IAAA,CAAKyP,KAAK;YAEjC,MAAMtB,KAAA,GAAQoC,aAAA,CAAcpC,KAAA;YAE5B,IAAIA,KAAA,KAAU,UAAaA,KAAA,CAAMF,SAAA,KAAc,UAAalL,OAAA,CAAQoL,KAAA,CAAMF,SAAS,MAAM,OAAO;cAC9F,MAAMyC,UAAA,GAAYvC,KAAA,CAAMF,SAAA;cAExBwC,OAAA,CAAQE,KAAA,GAAQD,UAAA,CAAUE,KAAA,GAAQC,cAAA,GAAiBC,mBAAA;cACnDL,OAAA,CAAQM,KAAA,GAAQL,UAAA,CAAUM,KAAA,GAAQH,cAAA,GAAiBC,mBAAA;cAEnDL,OAAA,CAAQlF,MAAA,CAAO0F,GAAA,CAAIP,UAAA,CAAUQ,OAAA,IAAW,GAAGR,UAAA,CAAUS,OAAA,IAAW,CAAC;cACjEV,OAAA,CAAQW,MAAA,CAAOH,GAAA,CAAIP,UAAA,CAAUW,OAAA,IAAW,GAAGX,UAAA,CAAUY,OAAA,IAAW,CAAC;YAC/E,OAAmB;cACLb,OAAA,CAAQE,KAAA,GAAQE,cAAA;cAChBJ,OAAA,CAAQM,KAAA,GAAQF,cAAA;YACjB;YAED,OAAOJ,OAAA;UACnB,OAAiB;YACLtP,OAAA,CAAQ4G,IAAA,CAAK,yDAAyD0H,KAAK;YAE3E,OAAO;UACR;QACX,OAAe;UACLtO,OAAA,CAAQ4G,IAAA,CAAK,yDAAyDwI,aAAA,CAAc5L,EAAE;UAEtF,OAAO;QACR;MACF;MAED,MAAM8J,UAAA,GAAaR,SAAA,CAAUQ,UAAA;MAE7B,WAAWtE,GAAA,IAAOsE,UAAA,EAAY;QAC5B,MAAM8C,SAAA,GAAY9C,UAAA,CAAWtE,GAAG;QAEhC,QAAQA,GAAA;UACN,KAAK;YACH,IAAIoH,SAAA,CAAUC,KAAA,EAAOtB,QAAA,CAASsB,KAAA,CAAMtI,SAAA,CAAUqI,SAAA,CAAUC,KAAK;YAC7D,IAAID,SAAA,CAAUd,OAAA,EAASP,QAAA,CAASuB,GAAA,GAAMnB,UAAA,CAAWiB,SAAA,CAAUd,OAAO;YAClE;UACF,KAAK;YACH,IAAIc,SAAA,CAAUC,KAAA,IAAStB,QAAA,CAASwB,QAAA,EAAUxB,QAAA,CAASwB,QAAA,CAASxI,SAAA,CAAUqI,SAAA,CAAUC,KAAK;YACrF,IAAID,SAAA,CAAUd,OAAA,EAASP,QAAA,CAASyB,WAAA,GAAcrB,UAAA,CAAWiB,SAAA,CAAUd,OAAO;YAC1E;UACF,KAAK;YACH,IAAIc,SAAA,CAAUd,OAAA,EAASP,QAAA,CAAS0B,SAAA,GAAYtB,UAAA,CAAWiB,SAAA,CAAUd,OAAO;YACxE;UACF,KAAK;YACH,IAAIc,SAAA,CAAUd,OAAA,EAASP,QAAA,CAAS2B,QAAA,GAAWvB,UAAA,CAAWiB,SAAA,CAAUd,OAAO;YACvE;UACF,KAAK;YACH,IAAIc,SAAA,CAAUO,KAAA,IAAS5B,QAAA,CAAS6B,SAAA,EAAW7B,QAAA,CAAS6B,SAAA,GAAYR,SAAA,CAAUO,KAAA;YAC1E;UACF,KAAK;YACH,IAAIP,SAAA,CAAUC,KAAA,IAAStB,QAAA,CAAS8B,QAAA,EAAU9B,QAAA,CAAS8B,QAAA,CAAS9I,SAAA,CAAUqI,SAAA,CAAUC,KAAK;YACrF,IAAID,SAAA,CAAUd,OAAA,EAASP,QAAA,CAAS+B,WAAA,GAAc3B,UAAA,CAAWiB,SAAA,CAAUd,OAAO;YAC1E;QACH;MACF;MAID,IAAIyB,WAAA,GAAczD,UAAA,CAAW,aAAa;MAC1C,IAAI0D,YAAA,GAAe1D,UAAA,CAAW,cAAc;MAI5C,IAAI0D,YAAA,KAAiB,UAAaD,WAAA,EAAa;QAC7CC,YAAA,GAAe;UACbL,KAAA,EAAO;QACR;MACF;MAID,IAAII,WAAA,KAAgB,UAAaC,YAAA,EAAc;QAC7CD,WAAA,GAAc;UACZtD,MAAA,EAAQ;UACR3K,IAAA,EAAM;YACJuN,KAAA,EAAO,CAAC,GAAG,GAAG,GAAG,CAAC;UACnB;QACF;MACF;MAED,IAAIU,WAAA,IAAeC,YAAA,EAAc;QAG/B,IAAID,WAAA,CAAYjO,IAAA,CAAKwM,OAAA,EAAS;UAG5BP,QAAA,CAASgC,WAAA,GAAc;QACjC,OAAe;UACL,MAAMV,KAAA,GAAQU,WAAA,CAAYjO,IAAA,CAAKuN,KAAA;UAE/B,QAAQU,WAAA,CAAYtD,MAAA;YAClB,KAAK;cACHsB,QAAA,CAASkC,OAAA,GAAUZ,KAAA,CAAM,CAAC,IAAIW,YAAA,CAAaL,KAAA;cAC3C;YACF,KAAK;cACH5B,QAAA,CAASkC,OAAA,GAAU,IAAIZ,KAAA,CAAM,CAAC,IAAIW,YAAA,CAAaL,KAAA;cAC/C;YACF,KAAK;cACH5B,QAAA,CAASkC,OAAA,GAAU,IAAIZ,KAAA,CAAM,CAAC,IAAIW,YAAA,CAAaL,KAAA;cAC/C;YACF,KAAK;cACH5B,QAAA,CAASkC,OAAA,GAAUZ,KAAA,CAAM,CAAC,IAAIW,YAAA,CAAaL,KAAA;cAC3C;YACF;cACE3Q,OAAA,CAAQ4G,IAAA,CAAK,qEAAqEmK,WAAA,CAAYtD,MAAM;UACvG;UAED,IAAIsB,QAAA,CAASkC,OAAA,GAAU,GAAGlC,QAAA,CAASgC,WAAA,GAAc;QAClD;MACF;MAID,IAAIjE,SAAA,CAAUE,KAAA,KAAU,UAAaF,SAAA,CAAUE,KAAA,CAAMF,SAAA,KAAc,QAAW;QAC5E,MAAMoE,UAAA,GAAapE,SAAA,CAAUE,KAAA,CAAMF,SAAA;QAEnC,WAAWqE,CAAA,IAAKD,UAAA,EAAY;UAC1B,MAAM5G,CAAA,GAAI4G,UAAA,CAAWC,CAAC;UAEtB,QAAQA,CAAA;YACN,KAAK;cACHpC,QAAA,CAASqC,IAAA,GAAO9G,CAAA,KAAM,IAAI+G,UAAA,GAAaC,SAAA;cACvC;YAEF,KAAK;cACHvC,QAAA,CAAS0B,SAAA,GAAYtB,UAAA,CAAW7E,CAAA,CAAEgF,OAAO;cACzCP,QAAA,CAASwC,WAAA,GAAc,IAAIC,OAAA,CAAQ,GAAG,CAAC;cACvC;UACH;QACF;MACF;MAED,OAAOzC,QAAA;IACR;IAED,SAAS0C,YAAYjO,EAAA,EAAI;MACvB,OAAOP,QAAA,CAASY,OAAA,CAAQuK,SAAA,CAAU5K,EAAE,GAAGqL,aAAa;IACrD;IAID,SAAS6C,YAAYtR,IAAA,EAAK;MACxB,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM;MAC9B;MAED,SAAS9B,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAK6O,MAAA,GAASC,iBAAA,CAAkBjR,KAAK;YACrC;QACH;MACF;MAEDkD,OAAA,CAAQgO,OAAA,CAAQzR,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAC3C;IAED,SAAS8O,kBAAkBxR,IAAA,EAAK;MAC9B,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,QAAQG,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,OAAOkR,oBAAA,CAAqBnR,KAAK;QACpC;MACF;MAED,OAAO,CAAE;IACV;IAED,SAASmR,qBAAqB1R,IAAA,EAAK;MACjC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,QAAQG,KAAA,CAAMC,QAAA;UACZ,KAAK;UACL,KAAK;YACHkC,IAAA,CAAKgK,SAAA,GAAYnM,KAAA,CAAMC,QAAA;YACvBkC,IAAA,CAAKwK,UAAA,GAAayE,qBAAA,CAAsBpR,KAAK;YAE7C;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASiP,sBAAsB3R,IAAA,EAAK;MAClC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,QAAQG,KAAA,CAAMC,QAAA;UACZ,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YACHkC,IAAA,CAAKnC,KAAA,CAAMC,QAAQ,IAAIS,UAAA,CAAWV,KAAA,CAAM4B,WAAW;YACnD;QACH;MACF;MAED,OAAOO,IAAA;IACR;IAED,SAASkP,YAAYlP,IAAA,EAAM;MACzB,IAAImP,MAAA;MAEJ,QAAQnP,IAAA,CAAK6O,MAAA,CAAO7E,SAAA;QAClB,KAAK;UACHmF,MAAA,GAAS,IAAIC,iBAAA,CACXpP,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAW6E,IAAA,EACvBrP,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAW8E,YAAA,EACvBtP,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAW+E,KAAA,EACvBvP,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAWgF,IACxB;UACD;QAEF,KAAK;UACH,IAAIC,IAAA,GAAOzP,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAWiF,IAAA;UAClC,IAAIC,IAAA,GAAO1P,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAWkF,IAAA;UAClC,MAAMC,WAAA,GAAc3P,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAW8E,YAAA;UAE3CI,IAAA,GAAOA,IAAA,KAAS,SAAYD,IAAA,GAAOE,WAAA,GAAcD,IAAA;UACjDD,IAAA,GAAOA,IAAA,KAAS,SAAYC,IAAA,GAAOC,WAAA,GAAcF,IAAA;UAEjDC,IAAA,IAAQ;UACRD,IAAA,IAAQ;UAERN,MAAA,GAAS,IAAIS,kBAAA,CACX,CAACF,IAAA,EACDA,IAAA,EACAD,IAAA,EACA,CAACA,IAAA;UAAA;UACDzP,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAW+E,KAAA,EACvBvP,IAAA,CAAK6O,MAAA,CAAOrE,UAAA,CAAWgF,IACxB;UACD;QAEF;UACEL,MAAA,GAAS,IAAIC,iBAAA,CAAmB;UAChC;MACH;MAEDD,MAAA,CAAO5R,IAAA,GAAOyC,IAAA,CAAKzC,IAAA,IAAQ;MAE3B,OAAO4R,MAAA;IACR;IAED,SAASU,UAAUnP,EAAA,EAAI;MACrB,MAAMV,IAAA,GAAOe,OAAA,CAAQgO,OAAA,CAAQrO,EAAE;MAE/B,IAAIV,IAAA,KAAS,QAAW;QACtB,OAAOG,QAAA,CAASH,IAAA,EAAMkP,WAAW;MAClC;MAEDhS,OAAA,CAAQ4G,IAAA,CAAK,sDAAsDpD,EAAE;MAErE,OAAO;IACR;IAID,SAASoP,WAAWxS,IAAA,EAAK;MACvB,IAAI0C,IAAA,GAAO,CAAE;MAEb,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,GAAO+P,mBAAA,CAAoBlS,KAAK;YAChC;QACH;MACF;MAEDkD,OAAA,CAAQiP,MAAA,CAAO1S,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAC1C;IAED,SAAS+P,oBAAoBzS,IAAA,EAAK;MAChC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YACHkC,IAAA,CAAKgK,SAAA,GAAYnM,KAAA,CAAMC,QAAA;YACvBkC,IAAA,CAAKwK,UAAA,GAAayF,oBAAA,CAAqBpS,KAAK;QAC/C;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASiQ,qBAAqB3S,IAAA,EAAK;MACjC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAMN,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YAC3CO,IAAA,CAAKuN,KAAA,GAAQ,IAAI2C,KAAA,CAAK,EAAGjL,SAAA,CAAUzH,KAAK;YACxC;UAEF,KAAK;YACHwC,IAAA,CAAKmQ,YAAA,GAAe5R,UAAA,CAAWV,KAAA,CAAM4B,WAAW;YAChD;UAEF,KAAK;YACH,MAAM2Q,CAAA,GAAI7R,UAAA,CAAWV,KAAA,CAAM4B,WAAW;YACtCO,IAAA,CAAKqQ,QAAA,GAAWD,CAAA,GAAIE,IAAA,CAAKC,IAAA,CAAK,IAAIH,CAAC,IAAI;YACvC;QACH;MACF;MAED,OAAOpQ,IAAA;IACR;IAED,SAASwQ,WAAWxQ,IAAA,EAAM;MACxB,IAAIyQ,KAAA;MAEJ,QAAQzQ,IAAA,CAAKgK,SAAA;QACX,KAAK;UACHyG,KAAA,GAAQ,IAAIC,gBAAA,CAAkB;UAC9B;QAEF,KAAK;UACHD,KAAA,GAAQ,IAAIE,UAAA,CAAY;UACxB;QAEF,KAAK;UACHF,KAAA,GAAQ,IAAIG,SAAA,CAAW;UACvB;QAEF,KAAK;UACHH,KAAA,GAAQ,IAAII,YAAA,CAAc;UAC1B;MACH;MAED,IAAI7Q,IAAA,CAAKwK,UAAA,CAAW+C,KAAA,EAAOkD,KAAA,CAAMlD,KAAA,CAAMuD,IAAA,CAAK9Q,IAAA,CAAKwK,UAAA,CAAW+C,KAAK;MACjE,IAAIvN,IAAA,CAAKwK,UAAA,CAAW6F,QAAA,EAAUI,KAAA,CAAMJ,QAAA,GAAWrQ,IAAA,CAAKwK,UAAA,CAAW6F,QAAA;MAE/D,OAAOI,KAAA;IACR;IAED,SAASM,SAASrQ,EAAA,EAAI;MACpB,MAAMV,IAAA,GAAOe,OAAA,CAAQiP,MAAA,CAAOtP,EAAE;MAE9B,IAAIV,IAAA,KAAS,QAAW;QACtB,OAAOG,QAAA,CAASH,IAAA,EAAMwQ,UAAU;MACjC;MAEDtT,OAAA,CAAQ4G,IAAA,CAAK,qDAAqDpD,EAAE;MAEpE,OAAO;IACR;IAID,SAASsQ,cAAc1T,IAAA,EAAK;MAC1B,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM;QAC7Ba,OAAA,EAAS,CAAE;QACX4Q,QAAA,EAAU,CAAE;QACZC,UAAA,EAAY;MACb;MAED,MAAMC,IAAA,GAAO9T,oBAAA,CAAqBC,IAAA,EAAK,MAAM,EAAE,CAAC;MAGhD,IAAI6T,IAAA,KAAS,QAAW;MAExB,SAASzT,CAAA,GAAI,GAAGA,CAAA,GAAIyT,IAAA,CAAK1T,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC/C,MAAMG,KAAA,GAAQsT,IAAA,CAAK1T,UAAA,CAAWC,CAAC;QAE/B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,MAAMC,EAAA,GAAK7C,KAAA,CAAM2B,YAAA,CAAa,IAAI;QAElC,QAAQ3B,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKK,OAAA,CAAQK,EAAE,IAAIC,WAAA,CAAY9C,KAAK;YACpC;UAEF,KAAK;YAEHmC,IAAA,CAAKiR,QAAA,GAAWG,qBAAA,CAAsBvT,KAAK;YAC3C;UAEF,KAAK;YACHX,OAAA,CAAQ4G,IAAA,CAAK,qDAAqDjG,KAAA,CAAMC,QAAQ;YAChF;UAEF,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;YACHkC,IAAA,CAAKkR,UAAA,CAAWnT,IAAA,CAAKsT,sBAAA,CAAuBxT,KAAK,CAAC;YAClD;UAEF;YACEX,OAAA,CAAQ4D,GAAA,CAAIjD,KAAK;QACpB;MACF;MAEDkD,OAAA,CAAQ4G,UAAA,CAAWrK,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAC9C;IAED,SAASW,YAAYrD,IAAA,EAAK;MACxB,MAAM0C,IAAA,GAAO;QACXxC,KAAA,EAAO,EAAE;QACTgG,MAAA,EAAQ;MACT;MAED,SAAS9F,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKxC,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YAC1C;UAEF,KAAK;YACHO,IAAA,CAAKxC,KAAA,GAAQQ,YAAA,CAAaH,KAAA,CAAM4B,WAAW;YAC3C;UAEF,KAAK;YACH,MAAM6R,QAAA,GAAWjU,oBAAA,CAAqBQ,KAAA,EAAO,UAAU,EAAE,CAAC;YAE1D,IAAIyT,QAAA,KAAa,QAAW;cAC1BtR,IAAA,CAAKwD,MAAA,GAAS/E,QAAA,CAAS6S,QAAA,CAAS9R,YAAA,CAAa,QAAQ,CAAC;YACvD;YAED;QACH;MACF;MAED,OAAOQ,IAAA;IACR;IAED,SAASoR,sBAAsB9T,IAAA,EAAK;MAClC,MAAM0C,IAAA,GAAO,CAAE;MAEf,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1BT,IAAA,CAAKnC,KAAA,CAAM2B,YAAA,CAAa,UAAU,CAAC,IAAId,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;MAC5E;MAED,OAAOQ,IAAA;IACR;IAED,SAASqR,uBAAuB/T,IAAA,EAAK;MACnC,MAAMiU,SAAA,GAAY;QAChBhH,IAAA,EAAMjN,IAAA,CAAIQ,QAAA;QACVmO,QAAA,EAAU3O,IAAA,CAAIkC,YAAA,CAAa,UAAU;QACrCX,KAAA,EAAOJ,QAAA,CAASnB,IAAA,CAAIkC,YAAA,CAAa,OAAO,CAAC;QACzC2B,MAAA,EAAQ,CAAE;QACVqC,MAAA,EAAQ;QACRgO,KAAA,EAAO;MACR;MAED,SAAS9T,CAAA,GAAI,GAAGC,CAAA,GAAIL,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAM4C,EAAA,GAAKhC,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YAC/C,MAAM4B,QAAA,GAAWvD,KAAA,CAAM2B,YAAA,CAAa,UAAU;YAC9C,MAAM8H,MAAA,GAAS7I,QAAA,CAASZ,KAAA,CAAM2B,YAAA,CAAa,QAAQ,CAAC;YACpD,MAAMwN,GAAA,GAAMvO,QAAA,CAASZ,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC;YAC9C,MAAMiS,SAAA,GAAYzE,GAAA,GAAM,IAAI5L,QAAA,GAAW4L,GAAA,GAAM5L,QAAA;YAC7CmQ,SAAA,CAAUpQ,MAAA,CAAOsQ,SAAS,IAAI;cAAE/Q,EAAA;cAAQ4G;YAAgB;YACxDiK,SAAA,CAAU/N,MAAA,GAAS8M,IAAA,CAAKoB,GAAA,CAAIH,SAAA,CAAU/N,MAAA,EAAQ8D,MAAA,GAAS,CAAC;YACxD,IAAIlG,QAAA,KAAa,YAAYmQ,SAAA,CAAUC,KAAA,GAAQ;YAC/C;UAEF,KAAK;YACHD,SAAA,CAAUhK,MAAA,GAAS/I,SAAA,CAAUX,KAAA,CAAM4B,WAAW;YAC9C;UAEF,KAAK;YACH8R,SAAA,CAAUI,CAAA,GAAInT,SAAA,CAAUX,KAAA,CAAM4B,WAAW;YACzC;QACH;MACF;MAED,OAAO8R,SAAA;IACR;IAED,SAASK,gBAAgBV,UAAA,EAAY;MACnC,MAAMhR,KAAA,GAAQ,CAAE;MAEhB,SAASxC,CAAA,GAAI,GAAGA,CAAA,GAAIwT,UAAA,CAAWtT,MAAA,EAAQF,CAAA,IAAK;QAC1C,MAAM6T,SAAA,GAAYL,UAAA,CAAWxT,CAAC;QAE9B,IAAIwC,KAAA,CAAMqR,SAAA,CAAUhH,IAAI,MAAM,QAAWrK,KAAA,CAAMqR,SAAA,CAAUhH,IAAI,IAAI,EAAE;QAEnErK,KAAA,CAAMqR,SAAA,CAAUhH,IAAI,EAAExM,IAAA,CAAKwT,SAAS;MACrC;MAED,OAAOrR,KAAA;IACR;IAED,SAAS2R,mBAAmBX,UAAA,EAAY;MACtC,IAAIY,MAAA,GAAQ;MAEZ,SAASpU,CAAA,GAAI,GAAGC,CAAA,GAAIuT,UAAA,CAAWtT,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACjD,MAAM6T,SAAA,GAAYL,UAAA,CAAWxT,CAAC;QAE9B,IAAI6T,SAAA,CAAUC,KAAA,KAAU,MAAM;UAC5BM,MAAA;QACD;MACF;MAED,IAAIA,MAAA,GAAQ,KAAKA,MAAA,GAAQZ,UAAA,CAAWtT,MAAA,EAAQ;QAC1CsT,UAAA,CAAWa,WAAA,GAAc;MAC1B;IACF;IAED,SAASC,cAAchS,IAAA,EAAM;MAC3B,MAAME,KAAA,GAAQ,CAAE;MAEhB,MAAMG,OAAA,GAAUL,IAAA,CAAKK,OAAA;MACrB,MAAM4Q,QAAA,GAAWjR,IAAA,CAAKiR,QAAA;MACtB,MAAMC,UAAA,GAAalR,IAAA,CAAKkR,UAAA;MAExB,IAAIA,UAAA,CAAWtT,MAAA,KAAW,GAAG,OAAO,CAAE;MAKtC,MAAMqU,iBAAA,GAAoBL,eAAA,CAAgBV,UAAU;MAEpD,WAAW3G,IAAA,IAAQ0H,iBAAA,EAAmB;QACpC,MAAMC,aAAA,GAAgBD,iBAAA,CAAkB1H,IAAI;QAI5CsH,kBAAA,CAAmBK,aAAa;QAIhChS,KAAA,CAAMqK,IAAI,IAAI4H,iBAAA,CAAkBD,aAAA,EAAe7R,OAAA,EAAS4Q,QAAQ;MACjE;MAED,OAAO/Q,KAAA;IACR;IAED,SAASiS,kBAAkBjB,UAAA,EAAY7Q,OAAA,EAAS4Q,QAAA,EAAU;MACxD,MAAM/Q,KAAA,GAAQ,CAAE;MAEhB,MAAMkS,SAAA,GAAW;QAAE5U,KAAA,EAAO;QAAIgG,MAAA,EAAQ;MAAG;MACzC,MAAM6O,MAAA,GAAS;QAAE7U,KAAA,EAAO;QAAIgG,MAAA,EAAQ;MAAG;MACvC,MAAM8O,EAAA,GAAK;QAAE9U,KAAA,EAAO;QAAIgG,MAAA,EAAQ;MAAG;MACnC,MAAM+O,GAAA,GAAM;QAAE/U,KAAA,EAAO;QAAIgG,MAAA,EAAQ;MAAG;MACpC,MAAM+J,KAAA,GAAQ;QAAE/P,KAAA,EAAO;QAAIgG,MAAA,EAAQ;MAAG;MAEtC,MAAMkF,SAAA,GAAY;QAAElL,KAAA,EAAO;QAAIgG,MAAA,EAAQ;MAAG;MAC1C,MAAMoF,UAAA,GAAa;QAAEpL,KAAA,EAAO;QAAIgG,MAAA,EAAQ;MAAG;MAE3C,MAAMkE,QAAA,GAAW,IAAI8K,cAAA,CAAgB;MAErC,MAAMC,YAAA,GAAe,EAAE;MAEvB,IAAIrM,KAAA,GAAQ;MAEZ,SAASuL,CAAA,GAAI,GAAGA,CAAA,GAAIT,UAAA,CAAWtT,MAAA,EAAQ+T,CAAA,IAAK;QAC1C,MAAMJ,SAAA,GAAYL,UAAA,CAAWS,CAAC;QAC9B,MAAMxQ,MAAA,GAASoQ,SAAA,CAAUpQ,MAAA;QAIzB,IAAI2Q,MAAA,GAAQ;QAEZ,QAAQP,SAAA,CAAUhH,IAAA;UAChB,KAAK;UACL,KAAK;YACHuH,MAAA,GAAQP,SAAA,CAAU1S,KAAA,GAAQ;YAC1B;UAEF,KAAK;YACHiT,MAAA,GAAQP,SAAA,CAAU1S,KAAA,GAAQ;YAC1B;UAEF,KAAK;YACH,SAAS6T,CAAA,GAAI,GAAGA,CAAA,GAAInB,SAAA,CAAU1S,KAAA,EAAO6T,CAAA,IAAK;cACxC,MAAMC,EAAA,GAAKpB,SAAA,CAAUhK,MAAA,CAAOmL,CAAC;cAE7B,QAAQC,EAAA;gBACN,KAAK;kBACHb,MAAA,IAAS;kBACT;gBAEF,KAAK;kBACHA,MAAA,IAAS;kBACT;gBAEF;kBACEA,MAAA,KAAUa,EAAA,GAAK,KAAK;kBACpB;cACH;YACF;YAED;UAEF;YACEzV,OAAA,CAAQ4G,IAAA,CAAK,+CAA+CyN,SAAA,CAAUhH,IAAI;QAC7E;QAED7C,QAAA,CAASkL,QAAA,CAASxM,KAAA,EAAO0L,MAAA,EAAOH,CAAC;QACjCvL,KAAA,IAAS0L,MAAA;QAIT,IAAIP,SAAA,CAAUtF,QAAA,EAAU;UACtBwG,YAAA,CAAa1U,IAAA,CAAKwT,SAAA,CAAUtF,QAAQ;QACrC;QAID,WAAW1O,IAAA,IAAQ4D,MAAA,EAAQ;UACzB,MAAM0R,KAAA,GAAQ1R,MAAA,CAAO5D,IAAI;UAEzB,QAAQA,IAAA;YACN,KAAK;cACH,WAAW2I,GAAA,IAAO+K,QAAA,EAAU;gBAC1B,MAAMvQ,EAAA,GAAKuQ,QAAA,CAAS/K,GAAG;gBAEvB,QAAQA,GAAA;kBACN,KAAK;oBACH,MAAM4M,UAAA,GAAaV,SAAA,CAAS5U,KAAA,CAAMI,MAAA;oBAClCmV,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQK,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQ8K,SAAA,CAAS5U,KAAK;oBACtE4U,SAAA,CAAS5O,MAAA,GAASnD,OAAA,CAAQK,EAAE,EAAE8C,MAAA;oBAE9B,IAAInD,OAAA,CAAQyH,WAAA,IAAezH,OAAA,CAAQwH,WAAA,EAAa;sBAC9CkL,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQwH,WAAA,EAAagL,KAAA,CAAMvL,MAAA,EAAQoB,SAAA,CAAUlL,KAAK;sBAC/EuV,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQyH,WAAA,EAAa+K,KAAA,CAAMvL,MAAA,EAAQsB,UAAA,CAAWpL,KAAK;oBACjF;oBAID,IAAI+T,SAAA,CAAUC,KAAA,KAAU,SAASN,UAAA,CAAWa,WAAA,KAAgB,MAAM;sBAChE,MAAMiB,MAAA,IAASZ,SAAA,CAAS5U,KAAA,CAAMI,MAAA,GAASkV,UAAA,IAAcV,SAAA,CAAS5O,MAAA;sBAE9D,SAAS9F,CAAA,GAAI,GAAGA,CAAA,GAAIsV,MAAA,EAAOtV,CAAA,IAAK;wBAG9B4U,EAAA,CAAG9U,KAAA,CAAMO,IAAA,CAAK,GAAG,CAAC;sBACnB;oBACF;oBAED;kBAEF,KAAK;oBACHgV,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQK,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQ+K,MAAA,CAAO7U,KAAK;oBACpE6U,MAAA,CAAO7O,MAAA,GAASnD,OAAA,CAAQK,EAAE,EAAE8C,MAAA;oBAC5B;kBAEF,KAAK;oBACHuP,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQK,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQiG,KAAA,CAAM/P,KAAK;oBACnE+P,KAAA,CAAM/J,MAAA,GAASnD,OAAA,CAAQK,EAAE,EAAE8C,MAAA;oBAC3B;kBAEF,KAAK;oBACHuP,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQK,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQgL,EAAA,CAAG9U,KAAK;oBAChE8U,EAAA,CAAG9O,MAAA,GAASnD,OAAA,CAAQK,EAAE,EAAE8C,MAAA;oBACxB;kBAEF,KAAK;oBACHuP,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQK,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQiL,GAAA,CAAI/U,KAAK;oBACjE8U,EAAA,CAAG9O,MAAA,GAASnD,OAAA,CAAQK,EAAE,EAAE8C,MAAA;oBACxB;kBAEF;oBACEtG,OAAA,CAAQ4G,IAAA,CAAK,6EAA6EoC,GAAG;gBAChG;cACF;cAED;YAEF,KAAK;cACH6M,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQ+K,MAAA,CAAO7U,KAAK;cAC1E6U,MAAA,CAAO7O,MAAA,GAASnD,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,EAAE8C,MAAA;cAClC;YAEF,KAAK;cACHuP,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQiG,KAAA,CAAM/P,KAAK;cACzE+P,KAAA,CAAM/J,MAAA,GAASnD,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,EAAE8C,MAAA;cACjC;YAEF,KAAK;cACHuP,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQgL,EAAA,CAAG9U,KAAK;cACtE8U,EAAA,CAAG9O,MAAA,GAASnD,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,EAAE8C,MAAA;cAC9B;YAEF,KAAK;cACHuP,iBAAA,CAAkBxB,SAAA,EAAWlR,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,GAAGmS,KAAA,CAAMvL,MAAA,EAAQiL,GAAA,CAAI/U,KAAK;cACvE+U,GAAA,CAAI/O,MAAA,GAASnD,OAAA,CAAQwS,KAAA,CAAMnS,EAAE,EAAE8C,MAAA;cAC/B;UACH;QACF;MACF;MAID,IAAI4O,SAAA,CAAS5U,KAAA,CAAMI,MAAA,GAAS,GAAG;QAC7B8J,QAAA,CAASuL,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBd,SAAA,CAAS5U,KAAA,EAAO4U,SAAA,CAAS5O,MAAM,CAAC;MAC9F;MACD,IAAI6O,MAAA,CAAO7U,KAAA,CAAMI,MAAA,GAAS,GAAG;QAC3B8J,QAAA,CAASuL,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBb,MAAA,CAAO7U,KAAA,EAAO6U,MAAA,CAAO7O,MAAM,CAAC;MACxF;MACD,IAAI+J,KAAA,CAAM/P,KAAA,CAAMI,MAAA,GAAS,GAAG8J,QAAA,CAASuL,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuB3F,KAAA,CAAM/P,KAAA,EAAO+P,KAAA,CAAM/J,MAAM,CAAC;MAChH,IAAI8O,EAAA,CAAG9U,KAAA,CAAMI,MAAA,GAAS,GAAG8J,QAAA,CAASuL,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBZ,EAAA,CAAG9U,KAAA,EAAO8U,EAAA,CAAG9O,MAAM,CAAC;MACpG,IAAI+O,GAAA,CAAI/U,KAAA,CAAMI,MAAA,GAAS,GAAG8J,QAAA,CAASuL,YAAA,CAAaE,GAAA,EAAK,IAAID,sBAAA,CAAuBX,GAAA,CAAI/U,KAAA,EAAO+U,GAAA,CAAI/O,MAAM,CAAC;MAEtG,IAAIkF,SAAA,CAAUlL,KAAA,CAAMI,MAAA,GAAS,GAAG;QAC9B8J,QAAA,CAASuL,YAAA,CAAa,aAAa,IAAIC,sBAAA,CAAuBxK,SAAA,CAAUlL,KAAA,EAAOkL,SAAA,CAAUlF,MAAM,CAAC;MACjG;MACD,IAAIoF,UAAA,CAAWpL,KAAA,CAAMI,MAAA,GAAS,GAAG;QAC/B8J,QAAA,CAASuL,YAAA,CAAa,cAAc,IAAIC,sBAAA,CAAuBtK,UAAA,CAAWpL,KAAA,EAAOoL,UAAA,CAAWpF,MAAM,CAAC;MACpG;MAEDtD,KAAA,CAAMF,IAAA,GAAO0H,QAAA;MACbxH,KAAA,CAAMqK,IAAA,GAAO2G,UAAA,CAAW,CAAC,EAAE3G,IAAA;MAC3BrK,KAAA,CAAMuS,YAAA,GAAeA,YAAA;MAErB,OAAOvS,KAAA;IACR;IAED,SAAS6S,kBAAkBxB,SAAA,EAAWjH,MAAA,EAAQhD,MAAA,EAAQ9J,KAAA,EAAO;MAC3D,MAAMoE,OAAA,GAAU2P,SAAA,CAAUI,CAAA;MAC1B,MAAMnO,MAAA,GAAS+N,SAAA,CAAU/N,MAAA;MACzB,MAAM+D,MAAA,GAASgK,SAAA,CAAUhK,MAAA;MAEzB,SAAS6L,WAAW1V,CAAA,EAAG;QACrB,IAAImG,KAAA,GAAQjC,OAAA,CAAQlE,CAAA,GAAI4J,MAAM,IAAI+L,YAAA;QAClC,MAAMzV,MAAA,GAASiG,KAAA,GAAQwP,YAAA;QAEvB,OAAOxP,KAAA,GAAQjG,MAAA,EAAQiG,KAAA,IAAS;UAC9BrG,KAAA,CAAMO,IAAA,CAAKuV,WAAA,CAAYzP,KAAK,CAAC;QAC9B;MACF;MAED,MAAMyP,WAAA,GAAchJ,MAAA,CAAO9M,KAAA;MAC3B,MAAM6V,YAAA,GAAe/I,MAAA,CAAO9G,MAAA;MAE5B,IAAI+N,SAAA,CAAUhK,MAAA,KAAW,QAAW;QAClC,IAAI1D,KAAA,GAAQ;QAEZ,SAASnG,CAAA,GAAI,GAAGC,CAAA,GAAI4J,MAAA,CAAO3J,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UAC7C,MAAMoU,MAAA,GAAQvK,MAAA,CAAO7J,CAAC;UAEtB,IAAIoU,MAAA,KAAU,GAAG;YACf,MAAMzN,CAAA,GAAIR,KAAA,GAAQL,MAAA,GAAS;YAC3B,MAAMc,CAAA,GAAIT,KAAA,GAAQL,MAAA,GAAS;YAC3B,MAAM+P,CAAA,GAAI1P,KAAA,GAAQL,MAAA,GAAS;YAC3B,MAAMuF,CAAA,GAAIlF,KAAA,GAAQL,MAAA,GAAS;YAE3B4P,UAAA,CAAW/O,CAAC;YACZ+O,UAAA,CAAW9O,CAAC;YACZ8O,UAAA,CAAWrK,CAAC;YACZqK,UAAA,CAAW9O,CAAC;YACZ8O,UAAA,CAAWG,CAAC;YACZH,UAAA,CAAWrK,CAAC;UACxB,WAAqB+I,MAAA,KAAU,GAAG;YACtB,MAAMzN,CAAA,GAAIR,KAAA,GAAQL,MAAA,GAAS;YAC3B,MAAMc,CAAA,GAAIT,KAAA,GAAQL,MAAA,GAAS;YAC3B,MAAM+P,CAAA,GAAI1P,KAAA,GAAQL,MAAA,GAAS;YAE3B4P,UAAA,CAAW/O,CAAC;YACZ+O,UAAA,CAAW9O,CAAC;YACZ8O,UAAA,CAAWG,CAAC;UACxB,WAAqBzB,MAAA,GAAQ,GAAG;YACpB,SAASzD,CAAA,GAAI,GAAGmF,EAAA,GAAK1B,MAAA,GAAQ,GAAGzD,CAAA,IAAKmF,EAAA,EAAInF,CAAA,IAAK;cAC5C,MAAMhK,CAAA,GAAIR,KAAA,GAAQL,MAAA,GAAS;cAC3B,MAAMc,CAAA,GAAIT,KAAA,GAAQL,MAAA,GAAS6K,CAAA;cAC3B,MAAMkF,CAAA,GAAI1P,KAAA,GAAQL,MAAA,IAAU6K,CAAA,GAAI;cAEhC+E,UAAA,CAAW/O,CAAC;cACZ+O,UAAA,CAAW9O,CAAC;cACZ8O,UAAA,CAAWG,CAAC;YACb;UACF;UAED1P,KAAA,IAASL,MAAA,GAASsO,MAAA;QACnB;MACT,OAAa;QACL,SAASpU,CAAA,GAAI,GAAGC,CAAA,GAAIiE,OAAA,CAAQhE,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK8F,MAAA,EAAQ;UACtD4P,UAAA,CAAW1V,CAAC;QACb;MACF;IACF;IAED,SAAS+V,YAAY/S,EAAA,EAAI;MACvB,OAAOP,QAAA,CAASY,OAAA,CAAQ4G,UAAA,CAAWjH,EAAE,GAAGsR,aAAa;IACtD;IAID,SAAS0B,qBAAqBpW,IAAA,EAAK;MACjC,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM,KAAK;QAClC0H,MAAA,EAAQ,CAAE;QACVyM,KAAA,EAAO;MACR;MAED,SAASjW,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH8V,8BAAA,CAA+B/V,KAAA,EAAOmC,IAAI;YAC1C;QACH;MACF;MAEDe,OAAA,CAAQ8S,gBAAA,CAAiBvW,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IACpD;IAED,SAAS8T,qBAAqB9T,IAAA,EAAM;MAClC,IAAIA,IAAA,CAAKE,KAAA,KAAU,QAAW,OAAOF,IAAA,CAAKE,KAAA;MAE1C,OAAOF,IAAA;IACR;IAED,SAAS+T,mBAAmBrT,EAAA,EAAI;MAC9B,OAAOP,QAAA,CAASY,OAAA,CAAQ8S,gBAAA,CAAiBnT,EAAE,GAAGoT,oBAAoB;IACnE;IAED,SAASF,+BAA+BtW,IAAA,EAAK0C,IAAA,EAAM;MACjD,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKkH,MAAA,CAAOrJ,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,IAAIwU,oBAAA,CAAqBnW,KAAK;YACnE;UAEF,KAAK;YACHmC,IAAA,CAAK2T,KAAA,CAAM5V,IAAA,CAAKkW,mBAAA,CAAoBpW,KAAK,CAAC;YAC1C;QACH;MACF;IACF;IAED,SAASmW,qBAAqB1W,IAAA,EAAK;MACjC,IAAI0C,IAAA;MAEJ,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;UACL,KAAK;YACHkC,IAAA,GAAOkU,6BAAA,CAA8BrW,KAAK;YAC1C;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAASkU,8BAA8B5W,IAAA,EAAK;MAC1C,MAAM0C,IAAA,GAAO;QACXuB,GAAA,EAAKjE,IAAA,CAAIkC,YAAA,CAAa,KAAK;QAC3BjC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM,KAAK;QAClC2U,IAAA,EAAM,IAAI3P,OAAA,CAAS;QACnB4P,MAAA,EAAQ;UACNC,GAAA,EAAK;UACL3C,GAAA,EAAK;QACN;QACDnH,IAAA,EAAMjN,IAAA,CAAIQ,QAAA;QACVwW,MAAA,EAAQ;QACRC,YAAA,EAAc;QACdC,cAAA,EAAgB;MACjB;MAED,SAAS9W,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAMN,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YAC3CO,IAAA,CAAKmU,IAAA,CAAKlP,SAAA,CAAUzH,KAAK;YACzB;UACF,KAAK;YACH,MAAMkU,GAAA,GAAM7T,KAAA,CAAMR,oBAAA,CAAqB,KAAK,EAAE,CAAC;YAC/C,MAAMgX,GAAA,GAAMxW,KAAA,CAAMR,oBAAA,CAAqB,KAAK,EAAE,CAAC;YAE/C2C,IAAA,CAAKoU,MAAA,CAAO1C,GAAA,GAAMnT,UAAA,CAAWmT,GAAA,CAAIjS,WAAW;YAC5CO,IAAA,CAAKoU,MAAA,CAAOC,GAAA,GAAM9V,UAAA,CAAW8V,GAAA,CAAI5U,WAAW;YAC5C;QACH;MACF;MAID,IAAIO,IAAA,CAAKoU,MAAA,CAAOC,GAAA,IAAOrU,IAAA,CAAKoU,MAAA,CAAO1C,GAAA,EAAK;QACtC1R,IAAA,CAAKsU,MAAA,GAAS;MACf;MAIDtU,IAAA,CAAKwU,cAAA,IAAkBxU,IAAA,CAAKoU,MAAA,CAAOC,GAAA,GAAMrU,IAAA,CAAKoU,MAAA,CAAO1C,GAAA,IAAO;MAE5D,OAAO1R,IAAA;IACR;IAED,SAASiU,oBAAoB3W,IAAA,EAAK;MAChC,MAAM0C,IAAA,GAAO;QACXuB,GAAA,EAAKjE,IAAA,CAAIkC,YAAA,CAAa,KAAK;QAC3BjC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM,KAAK;QAClCiV,WAAA,EAAa,EAAE;QACfvR,UAAA,EAAY;MACb;MAED,SAASxF,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKyU,WAAA,CAAY1W,IAAA,CAAK2W,yBAAA,CAA0B7W,KAAK,CAAC;YACtD;UAEF,KAAK;UACL,KAAK;UACL,KAAK;YACHmC,IAAA,CAAKkD,UAAA,CAAWnF,IAAA,CAAK4W,wBAAA,CAAyB9W,KAAK,CAAC;YACpD;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAAS0U,0BAA0BpX,IAAA,EAAK;MACtC,MAAM0C,IAAA,GAAO;QACX4U,KAAA,EAAOtX,IAAA,CAAIkC,YAAA,CAAa,OAAO,EAAEpB,KAAA,CAAM,GAAG,EAAEyW,GAAA,CAAK;QACjD3R,UAAA,EAAY,EAAE;QACdyQ,KAAA,EAAO;MACR;MAED,SAASjW,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAK2T,KAAA,CAAM5V,IAAA,CAAKkW,mBAAA,CAAoBpW,KAAK,CAAC;YAC1C;UAEF,KAAK;UACL,KAAK;UACL,KAAK;YACHmC,IAAA,CAAKkD,UAAA,CAAWnF,IAAA,CAAK4W,wBAAA,CAAyB9W,KAAK,CAAC;YACpD;QACH;MACF;MAED,OAAOmC,IAAA;IACR;IAED,SAAS2U,yBAAyBrX,IAAA,EAAK;MACrC,MAAM0C,IAAA,GAAO;QACXuK,IAAA,EAAMjN,IAAA,CAAIQ;MACX;MAED,MAAMN,KAAA,GAAQc,WAAA,CAAYhB,IAAA,CAAImC,WAAW;MAEzC,QAAQO,IAAA,CAAKuK,IAAA;QACX,KAAK;UACHvK,IAAA,CAAK8U,GAAA,GAAM,IAAI7L,OAAA,CAAS;UACxBjJ,IAAA,CAAK8U,GAAA,CAAI7P,SAAA,CAAUzH,KAAK,EAAE8F,SAAA,CAAW;UACrC;QAEF,KAAK;UACHtD,IAAA,CAAK8U,GAAA,GAAM,IAAItQ,OAAA,CAAS;UACxBxE,IAAA,CAAK8U,GAAA,CAAI7P,SAAA,CAAUzH,KAAK;UACxB;QAEF,KAAK;UACHwC,IAAA,CAAK8U,GAAA,GAAM,IAAItQ,OAAA,CAAS;UACxBxE,IAAA,CAAK8U,GAAA,CAAI7P,SAAA,CAAUzH,KAAK;UACxBwC,IAAA,CAAK+U,KAAA,GAAQ9T,SAAA,CAAU+T,QAAA,CAASxX,KAAA,CAAM,CAAC,CAAC;UACxC;MACH;MAED,OAAOwC,IAAA;IACR;IAID,SAASiV,kBAAkB3X,IAAA,EAAK;MAC9B,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM,KAAK;QAClC0V,WAAA,EAAa,CAAE;MAChB;MAED,SAASxX,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKkV,WAAA,CAAYrX,KAAA,CAAM2B,YAAA,CAAa,MAAM,CAAC,IAAI,CAAE;YACjD2V,qBAAA,CAAsBtX,KAAA,EAAOmC,IAAA,CAAKkV,WAAA,CAAYrX,KAAA,CAAM2B,YAAA,CAAa,MAAM,CAAC,CAAC;YACzE;QACH;MACF;MAEDuB,OAAA,CAAQqU,aAAA,CAAc9X,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IACjD;IAED,SAASmV,sBAAsB7X,IAAA,EAAK0C,IAAA,EAAM;MACxC,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHuX,2BAAA,CAA4BxX,KAAA,EAAOmC,IAAI;YACvC;QACH;MACF;IACF;IAED,SAASqV,4BAA4B/X,IAAA,EAAK0C,IAAA,EAAM;MAC9C,SAAStC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKsV,OAAA,GAAUhX,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YAC5C;UAEF,KAAK;YACHO,IAAA,CAAKuV,IAAA,GAAOjX,WAAA,CAAYT,KAAA,CAAM4B,WAAW,EAAE,CAAC;YAC5C;QACH;MACF;IACF;IAID,SAAS+V,qBAAqBlY,IAAA,EAAK;MACjC,MAAM0C,IAAA,GAAO;QACXyV,aAAA,EAAe;MAChB;MAED,SAAS/X,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAKyV,aAAA,CAAc1X,IAAA,CAAK2X,4BAAA,CAA6B7X,KAAK,CAAC;YAC3D;QACH;MACF;MAEDkD,OAAA,CAAQ4U,gBAAA,CAAiBjX,OAAA,CAAQpB,IAAA,CAAIkC,YAAA,CAAa,KAAK,CAAC,CAAC,IAAIQ,IAAA;IAC9D;IAED,SAAS0V,6BAA6BpY,IAAA,EAAK;MACzC,MAAM0C,IAAA,GAAO;QACXqB,MAAA,EAAQ/D,IAAA,CAAIkC,YAAA,CAAa,QAAQ,EAAEpB,KAAA,CAAM,GAAG,EAAEyW,GAAA,CAAK;MACpD;MAED,SAASnX,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,QAAQ5C,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAM8X,KAAA,GAAQ/X,KAAA,CAAMR,oBAAA,CAAqB,OAAO,EAAE,CAAC;YACnD2C,IAAA,CAAKmU,IAAA,GAAOyB,KAAA,CAAMnW,WAAA;YAClB,MAAMoW,aAAA,GAAgB7V,IAAA,CAAKmU,IAAA,CAAK/V,KAAA,CAAM,OAAO,EAAEyW,GAAA,GAAMzW,KAAA,CAAM,MAAM,EAAE,CAAC;YACpE4B,IAAA,CAAK8V,UAAA,GAAaD,aAAA,CAAcE,MAAA,CAAO,GAAGF,aAAA,CAAcjY,MAAA,GAAS,CAAC;YAClE;QACH;MACF;MAED,OAAOoC,IAAA;IACR;IAED,SAASgW,qBAAqBhW,IAAA,EAAM;MAClC,IAAIA,IAAA,CAAKE,KAAA,KAAU,QAAW,OAAOF,IAAA,CAAKE,KAAA;MAE1C,OAAOF,IAAA;IACR;IAED,SAASiW,mBAAmBvV,EAAA,EAAI;MAC9B,OAAOP,QAAA,CAASY,OAAA,CAAQ4U,gBAAA,CAAiBjV,EAAE,GAAGsV,oBAAoB;IACnE;IAED,SAASE,gBAAA,EAAkB;MACzB,MAAMC,iBAAA,GAAoBnX,MAAA,CAAOC,IAAA,CAAK8B,OAAA,CAAQ8S,gBAAgB,EAAE,CAAC;MACjE,MAAMuC,iBAAA,GAAoBpX,MAAA,CAAOC,IAAA,CAAK8B,OAAA,CAAQ4U,gBAAgB,EAAE,CAAC;MACjE,MAAMU,aAAA,GAAgBrX,MAAA,CAAOC,IAAA,CAAK8B,OAAA,CAAQuV,YAAY,EAAE,CAAC;MAEzD,IAAIH,iBAAA,KAAsB,UAAaC,iBAAA,KAAsB,QAAW;MAExE,MAAMG,eAAA,GAAkBxC,kBAAA,CAAmBoC,iBAAiB;MAC5D,MAAMK,eAAA,GAAkBP,kBAAA,CAAmBG,iBAAiB;MAC5D,MAAMK,WAAA,GAAcC,cAAA,CAAeL,aAAa;MAEhD,MAAMZ,aAAA,GAAgBe,eAAA,CAAgBf,aAAA;MACtC,MAAMkB,QAAA,GAAW,CAAE;MAEnB,SAASjZ,CAAA,GAAI,GAAGC,CAAA,GAAI8X,aAAA,CAAc7X,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACpD,MAAMyW,IAAA,GAAOsB,aAAA,CAAc/X,CAAC;QAI5B,MAAMkZ,aAAA,GAAgBC,OAAA,CAAQC,aAAA,CAAc,WAAW3C,IAAA,CAAK9S,MAAA,GAAS,IAAI;QAEzE,IAAIuV,aAAA,EAAe;UAGjB,MAAMG,mBAAA,GAAsBH,aAAA,CAAcI,aAAA;UAI1CC,OAAA,CAAQ9C,IAAA,CAAK2B,UAAA,EAAYiB,mBAAmB;QAC7C;MACF;MAED,SAASE,QAAQnB,UAAA,EAAYoB,aAAA,EAAe;QAC1C,MAAMC,iBAAA,GAAoBD,aAAA,CAAc1X,YAAA,CAAa,MAAM;QAC3D,MAAMoV,KAAA,GAAQ2B,eAAA,CAAgBrP,MAAA,CAAO4O,UAAU;QAE/CW,WAAA,CAAYW,QAAA,CAAS,UAAUrY,MAAA,EAAQ;UACrC,IAAIA,MAAA,CAAOxB,IAAA,KAAS4Z,iBAAA,EAAmB;YACrCR,QAAA,CAASb,UAAU,IAAI;cACrB/W,MAAA;cACAmE,UAAA,EAAYmU,kBAAA,CAAmBH,aAAa;cAC5CtC,KAAA;cACArQ,QAAA,EAAUqQ,KAAA,CAAML;YACjB;UACF;QACX,CAAS;MACF;MAED,MAAM+C,EAAA,GAAK,IAAIrO,OAAA,CAAS;MAExBsO,UAAA,GAAa;QACXrQ,MAAA,EAAQqP,eAAA,IAAmBA,eAAA,CAAgBrP,MAAA;QAE3CsQ,aAAA,EAAe,SAAAA,CAAU1B,UAAA,EAAY;UACnC,MAAM2B,SAAA,GAAYd,QAAA,CAASb,UAAU;UAErC,IAAI2B,SAAA,EAAW;YACb,OAAOA,SAAA,CAAUlT,QAAA;UAC7B,OAAiB;YACLrH,OAAA,CAAQ4G,IAAA,CAAK,gCAAgCgS,UAAA,GAAa,iBAAiB;UAC5E;QACF;QAED4B,aAAA,EAAe,SAAAA,CAAU5B,UAAA,EAAYlS,KAAA,EAAO;UAC1C,MAAM6T,SAAA,GAAYd,QAAA,CAASb,UAAU;UAErC,IAAI2B,SAAA,EAAW;YACb,MAAM7C,KAAA,GAAQ6C,SAAA,CAAU7C,KAAA;YAExB,IAAIhR,KAAA,GAAQgR,KAAA,CAAMR,MAAA,CAAO1C,GAAA,IAAO9N,KAAA,GAAQgR,KAAA,CAAMR,MAAA,CAAOC,GAAA,EAAK;cACxDnX,OAAA,CAAQ4G,IAAA,CACN,gCACAgS,UAAA,GACA,YACAlS,KAAA,GACA,8BACAgR,KAAA,CAAMR,MAAA,CAAOC,GAAA,GACb,YACAO,KAAA,CAAMR,MAAA,CAAO1C,GAAA,GACb,IACD;YACf,WAAuBkD,KAAA,CAAMN,MAAA,EAAQ;cACvBpX,OAAA,CAAQ4G,IAAA,CAAK,gCAAgCgS,UAAA,GAAa,aAAa;YACrF,OAAmB;cACL,MAAM/W,MAAA,GAAS0Y,SAAA,CAAU1Y,MAAA;cACzB,MAAMoV,IAAA,GAAOS,KAAA,CAAMT,IAAA;cACnB,MAAMjR,UAAA,GAAauU,SAAA,CAAUvU,UAAA;cAE7BE,MAAA,CAAO8F,QAAA,CAAU;cAIjB,SAASxL,CAAA,GAAI,GAAGA,CAAA,GAAIwF,UAAA,CAAWtF,MAAA,EAAQF,CAAA,IAAK;gBAC1C,MAAMuF,SAAA,GAAYC,UAAA,CAAWxF,CAAC;gBAI9B,IAAIuF,SAAA,CAAU1B,GAAA,IAAO0B,SAAA,CAAU1B,GAAA,CAAIE,OAAA,CAAQqU,UAAU,MAAM,IAAI;kBAC7D,QAAQlB,KAAA,CAAMrK,IAAA;oBACZ,KAAK;sBACHnH,MAAA,CAAOuU,QAAA,CAASL,EAAA,CAAGM,gBAAA,CAAiBzD,IAAA,EAAMlT,SAAA,CAAU+T,QAAA,CAASpR,KAAK,CAAC,CAAC;sBACpE;oBAEF,KAAK;sBACHR,MAAA,CAAOuU,QAAA,CAASL,EAAA,CAAGO,eAAA,CAAgB1D,IAAA,CAAKhP,CAAA,GAAIvB,KAAA,EAAOuQ,IAAA,CAAK/O,CAAA,GAAIxB,KAAA,EAAOuQ,IAAA,CAAK9O,CAAA,GAAIzB,KAAK,CAAC;sBAClF;oBAEF;sBACE1G,OAAA,CAAQ4G,IAAA,CAAK,8CAA8C8Q,KAAA,CAAMrK,IAAI;sBACrE;kBACH;gBACnB,OAAuB;kBACL,QAAQtH,SAAA,CAAUsH,IAAA;oBAChB,KAAK;sBACHnH,MAAA,CAAOuU,QAAA,CAAS1U,SAAA,CAAU6R,GAAG;sBAC7B;oBAEF,KAAK;sBACH1R,MAAA,CAAOuU,QAAA,CAASL,EAAA,CAAGO,eAAA,CAAgB5U,SAAA,CAAU6R,GAAA,CAAI3P,CAAA,EAAGlC,SAAA,CAAU6R,GAAA,CAAI1P,CAAA,EAAGnC,SAAA,CAAU6R,GAAA,CAAIzP,CAAC,CAAC;sBACrF;oBAEF,KAAK;sBACHjC,MAAA,CAAOqB,KAAA,CAAMxB,SAAA,CAAU6R,GAAG;sBAC1B;oBAEF,KAAK;sBACH1R,MAAA,CAAOuU,QAAA,CAASL,EAAA,CAAGM,gBAAA,CAAiB3U,SAAA,CAAU6R,GAAA,EAAK7R,SAAA,CAAU8R,KAAK,CAAC;sBACnE;kBACH;gBACF;cACF;cAEDhW,MAAA,CAAOqE,MAAA,CAAO0N,IAAA,CAAK1N,MAAM;cACzBrE,MAAA,CAAOqE,MAAA,CAAO8B,SAAA,CAAUnG,MAAA,CAAOwF,QAAA,EAAUxF,MAAA,CAAO2F,UAAA,EAAY3F,MAAA,CAAO0F,KAAK;cAExEkS,QAAA,CAASb,UAAU,EAAEvR,QAAA,GAAWX,KAAA;YACjC;UACb,OAAiB;YACL1G,OAAA,CAAQ4D,GAAA,CAAI,0BAA0BgV,UAAA,GAAa,kBAAkB;UACtE;QACF;MACF;IACF;IAED,SAASuB,mBAAmBxU,IAAA,EAAM;MAChC,MAAMK,UAAA,GAAa,EAAE;MAErB,MAAM5F,IAAA,GAAMuZ,OAAA,CAAQC,aAAA,CAAc,UAAUjU,IAAA,CAAKnC,EAAA,GAAK,IAAI;MAE1D,SAAShD,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,IAAIjD,KAAA,EAAOsa,OAAA;QAEX,QAAQja,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHN,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACrC,MAAMsY,OAAA,GAAS,IAAI9O,OAAA,CAAO,EAAGhE,SAAA,CAAUzH,KAAK,EAAE8F,SAAA,CAAW;YACzDJ,UAAA,CAAWnF,IAAA,CAAK;cACdwD,GAAA,EAAK1D,KAAA,CAAM2B,YAAA,CAAa,KAAK;cAC7B+K,IAAA,EAAM1M,KAAA,CAAMC,QAAA;cACZgX,GAAA,EAAKiD;YACnB,CAAa;YACD;UAEF,KAAK;UACL,KAAK;YACHva,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACrCqY,OAAA,GAAS,IAAItT,OAAA,GAAUS,SAAA,CAAUzH,KAAK;YACtC0F,UAAA,CAAWnF,IAAA,CAAK;cACdwD,GAAA,EAAK1D,KAAA,CAAM2B,YAAA,CAAa,KAAK;cAC7B+K,IAAA,EAAM1M,KAAA,CAAMC,QAAA;cACZgX,GAAA,EAAKgD;YACnB,CAAa;YACD;UAEF,KAAK;YACHta,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACrCqY,OAAA,GAAS,IAAItT,OAAA,GAAUS,SAAA,CAAUzH,KAAK;YACtC,MAAMuX,KAAA,GAAQ9T,SAAA,CAAU+T,QAAA,CAASxX,KAAA,CAAM,CAAC,CAAC;YACzC0F,UAAA,CAAWnF,IAAA,CAAK;cACdwD,GAAA,EAAK1D,KAAA,CAAM2B,YAAA,CAAa,KAAK;cAC7B+K,IAAA,EAAM1M,KAAA,CAAMC,QAAA;cACZgX,GAAA,EAAKgD,OAAA;cACL/C;YACd,CAAa;YACD;QACH;MACF;MAED,OAAO7R,UAAA;IACR;IAID,SAAS8U,aAAa1a,IAAA,EAAK;MACzB,MAAMwC,QAAA,GAAWxC,IAAA,CAAID,oBAAA,CAAqB,MAAM;MAIhD,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIoC,QAAA,CAASlC,MAAA,EAAQF,CAAA,IAAK;QACxC,MAAMua,OAAA,GAAUnY,QAAA,CAASpC,CAAC;QAE1B,IAAIua,OAAA,CAAQ1Y,YAAA,CAAa,IAAI,MAAM,OAAO;UACxC0Y,OAAA,CAAQhF,YAAA,CAAa,MAAMrU,UAAA,EAAY;QACxC;MACF;IACF;IAED,MAAMwE,MAAA,GAAS,IAAI6F,OAAA,CAAS;IAC5B,MAAMiP,MAAA,GAAS,IAAI1T,OAAA,CAAS;IAE5B,SAAS2T,UAAU7a,IAAA,EAAK;MACtB,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM,KAAK;QAClC+K,IAAA,EAAMjN,IAAA,CAAIkC,YAAA,CAAa,MAAM;QAC7BkB,EAAA,EAAIpD,IAAA,CAAIkC,YAAA,CAAa,IAAI;QACzB+B,GAAA,EAAKjE,IAAA,CAAIkC,YAAA,CAAa,KAAK;QAC3B4D,MAAA,EAAQ,IAAI6F,OAAA,CAAS;QACrBnG,KAAA,EAAO,EAAE;QACTsV,eAAA,EAAiB,EAAE;QACnBC,mBAAA,EAAqB,EAAE;QACvBC,cAAA,EAAgB,EAAE;QAClBC,kBAAA,EAAoB,EAAE;QACtBC,aAAA,EAAe,EAAE;QACjBtV,UAAA,EAAY,CAAE;MACf;MAED,SAASxF,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,IAAIG,KAAA,CAAM4C,QAAA,KAAa,GAAG;QAE1B,IAAIjD,KAAA;QAEJ,QAAQK,KAAA,CAAMC,QAAA;UACZ,KAAK;YACHkC,IAAA,CAAK8C,KAAA,CAAM/E,IAAA,CAAKF,KAAA,CAAM2B,YAAA,CAAa,IAAI,CAAC;YACxC2Y,SAAA,CAAUta,KAAK;YACf;UAEF,KAAK;YACHmC,IAAA,CAAKoY,eAAA,CAAgBra,IAAA,CAAKW,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,CAAC;YAC5D;UAEF,KAAK;YACHQ,IAAA,CAAKqY,mBAAA,CAAoBta,IAAA,CAAK0a,iBAAA,CAAkB5a,KAAK,CAAC;YACtD;UAEF,KAAK;YACHmC,IAAA,CAAKsY,cAAA,CAAeva,IAAA,CAAKW,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,CAAC;YAC3D;UAEF,KAAK;YACHQ,IAAA,CAAKuY,kBAAA,CAAmBxa,IAAA,CAAK0a,iBAAA,CAAkB5a,KAAK,CAAC;YACrD;UAEF,KAAK;YACHmC,IAAA,CAAKwY,aAAA,CAAcza,IAAA,CAAKW,OAAA,CAAQb,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,CAAC;YAC1D;UAEF,KAAK;YACHhC,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACrCO,IAAA,CAAKoD,MAAA,CAAOuU,QAAA,CAASvU,MAAA,CAAO6B,SAAA,CAAUzH,KAAK,EAAE8F,SAAA,EAAW;YACxDtD,IAAA,CAAKkD,UAAA,CAAWrF,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,IAAI3B,KAAA,CAAMC,QAAA;YACnD;UAEF,KAAK;YACHN,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACrCyY,MAAA,CAAOjT,SAAA,CAAUzH,KAAK;YACtBwC,IAAA,CAAKoD,MAAA,CAAOuU,QAAA,CAASvU,MAAA,CAAOyU,eAAA,CAAgBK,MAAA,CAAO/S,CAAA,EAAG+S,MAAA,CAAO9S,CAAA,EAAG8S,MAAA,CAAO7S,CAAC,CAAC;YACzErF,IAAA,CAAKkD,UAAA,CAAWrF,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,IAAI3B,KAAA,CAAMC,QAAA;YACnD;UAEF,KAAK;YACHN,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACrC,MAAMsV,KAAA,GAAQ9T,SAAA,CAAU+T,QAAA,CAASxX,KAAA,CAAM,CAAC,CAAC;YACzCwC,IAAA,CAAKoD,MAAA,CAAOuU,QAAA,CAASvU,MAAA,CAAOwU,gBAAA,CAAiBM,MAAA,CAAOjT,SAAA,CAAUzH,KAAK,GAAGuX,KAAK,CAAC;YAC5E/U,IAAA,CAAKkD,UAAA,CAAWrF,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,IAAI3B,KAAA,CAAMC,QAAA;YACnD;UAEF,KAAK;YACHN,KAAA,GAAQc,WAAA,CAAYT,KAAA,CAAM4B,WAAW;YACrCO,IAAA,CAAKoD,MAAA,CAAOqB,KAAA,CAAMyT,MAAA,CAAOjT,SAAA,CAAUzH,KAAK,CAAC;YACzCwC,IAAA,CAAKkD,UAAA,CAAWrF,KAAA,CAAM2B,YAAA,CAAa,KAAK,CAAC,IAAI3B,KAAA,CAAMC,QAAA;YACnD;UAEF,KAAK;YACH;UAEF;YACEZ,OAAA,CAAQ4D,GAAA,CAAIjD,KAAK;QACpB;MACF;MAED,IAAI6a,OAAA,CAAQ1Y,IAAA,CAAKU,EAAE,GAAG;QACpBxD,OAAA,CAAQ4G,IAAA,CACN,0GACA9D,IAAA,CAAKU,EACN;MACT,OAAa;QACLK,OAAA,CAAQ+B,KAAA,CAAM9C,IAAA,CAAKU,EAAE,IAAIV,IAAA;MAC1B;MAED,OAAOA,IAAA;IACR;IAED,SAASyY,kBAAkBnb,IAAA,EAAK;MAC9B,MAAM0C,IAAA,GAAO;QACXU,EAAA,EAAIhC,OAAA,CAAQpB,IAAA,CAAIkC,YAAA,CAAa,KAAK,CAAC;QACnC8L,SAAA,EAAW,CAAE;QACbqN,SAAA,EAAW;MACZ;MAED,SAASjb,CAAA,GAAI,GAAGA,CAAA,GAAIJ,IAAA,CAAIG,UAAA,CAAWG,MAAA,EAAQF,CAAA,IAAK;QAC9C,MAAMG,KAAA,GAAQP,IAAA,CAAIG,UAAA,CAAWC,CAAC;QAE9B,QAAQG,KAAA,CAAMC,QAAA;UACZ,KAAK;YACH,MAAM8a,SAAA,GAAY/a,KAAA,CAAMR,oBAAA,CAAqB,mBAAmB;YAEhE,SAASqG,CAAA,GAAI,GAAGA,CAAA,GAAIkV,SAAA,CAAUhb,MAAA,EAAQ8F,CAAA,IAAK;cACzC,MAAMmV,QAAA,GAAWD,SAAA,CAAUlV,CAAC;cAC5B,MAAMoV,MAAA,GAASD,QAAA,CAASrZ,YAAA,CAAa,QAAQ;cAC7C,MAAM6B,MAAA,GAASwX,QAAA,CAASrZ,YAAA,CAAa,QAAQ;cAE7CQ,IAAA,CAAKsL,SAAA,CAAUwN,MAAM,IAAIpa,OAAA,CAAQ2C,MAAM;YACxC;YAED;UAEF,KAAK;YACHrB,IAAA,CAAK2Y,SAAA,CAAU5a,IAAA,CAAKW,OAAA,CAAQb,KAAA,CAAM4B,WAAW,CAAC;YAC9C;QAIH;MACF;MAED,OAAOO,IAAA;IACR;IAED,SAAS+Y,cAAcJ,SAAA,EAAWzR,MAAA,EAAQ;MACxC,MAAM8R,QAAA,GAAW,EAAE;MACnB,MAAMC,cAAA,GAAiB,EAAE;MAEzB,IAAIvb,CAAA,EAAGgG,CAAA,EAAG1D,IAAA;MAKV,KAAKtC,CAAA,GAAI,GAAGA,CAAA,GAAIib,SAAA,CAAU/a,MAAA,EAAQF,CAAA,IAAK;QACrC,MAAMwb,QAAA,GAAWP,SAAA,CAAUjb,CAAC;QAE5B,IAAIyb,IAAA;QAEJ,IAAIT,OAAA,CAAQQ,QAAQ,GAAG;UACrBC,IAAA,GAAOnW,OAAA,CAAQkW,QAAQ;UACvBE,kBAAA,CAAmBD,IAAA,EAAMjS,MAAA,EAAQ8R,QAAQ;QACnD,WAAmBK,cAAA,CAAeH,QAAQ,GAAG;UAGnC,MAAMzC,WAAA,GAAc1V,OAAA,CAAQuV,YAAA,CAAa4C,QAAQ;UACjD,MAAMI,QAAA,GAAW7C,WAAA,CAAY6C,QAAA;UAE7B,SAASC,EAAA,GAAI,GAAGA,EAAA,GAAID,QAAA,CAAS1b,MAAA,EAAQ2b,EAAA,IAAK;YACxC,MAAM1b,KAAA,GAAQyb,QAAA,CAASC,EAAC;YAExB,IAAI1b,KAAA,CAAM0M,IAAA,KAAS,SAAS;cAC1B,MAAMiP,KAAA,GAAOxW,OAAA,CAAQnF,KAAA,CAAM6C,EAAE;cAC7B0Y,kBAAA,CAAmBI,KAAA,EAAMtS,MAAA,EAAQ8R,QAAQ;YAC1C;UACF;QACX,OAAe;UACL9b,OAAA,CAAQC,KAAA,CAAM,sEAAsE+b,QAAQ;QAC7F;MACF;MAID,KAAKxb,CAAA,GAAI,GAAGA,CAAA,GAAIwJ,MAAA,CAAOtJ,MAAA,EAAQF,CAAA,IAAK;QAClC,KAAKgG,CAAA,GAAI,GAAGA,CAAA,GAAIsV,QAAA,CAASpb,MAAA,EAAQ8F,CAAA,IAAK;UACpC1D,IAAA,GAAOgZ,QAAA,CAAStV,CAAC;UAEjB,IAAI1D,IAAA,CAAKyZ,IAAA,CAAKlc,IAAA,KAAS2J,MAAA,CAAOxJ,CAAC,EAAEH,IAAA,EAAM;YACrC0b,cAAA,CAAevb,CAAC,IAAIsC,IAAA;YACpBA,IAAA,CAAK0Z,SAAA,GAAY;YACjB;UACD;QACF;MACF;MAID,KAAKhc,CAAA,GAAI,GAAGA,CAAA,GAAIsb,QAAA,CAASpb,MAAA,EAAQF,CAAA,IAAK;QACpCsC,IAAA,GAAOgZ,QAAA,CAAStb,CAAC;QAEjB,IAAIsC,IAAA,CAAK0Z,SAAA,KAAc,OAAO;UAC5BT,cAAA,CAAelb,IAAA,CAAKiC,IAAI;UACxBA,IAAA,CAAK0Z,SAAA,GAAY;QAClB;MACF;MAID,MAAMC,KAAA,GAAQ,EAAE;MAChB,MAAMC,YAAA,GAAe,EAAE;MAEvB,KAAKlc,CAAA,GAAI,GAAGA,CAAA,GAAIub,cAAA,CAAerb,MAAA,EAAQF,CAAA,IAAK;QAC1CsC,IAAA,GAAOiZ,cAAA,CAAevb,CAAC;QAEvBic,KAAA,CAAM5b,IAAA,CAAKiC,IAAA,CAAKyZ,IAAI;QACpBG,YAAA,CAAa7b,IAAA,CAAKiC,IAAA,CAAKmJ,WAAW;MACnC;MAED,OAAO,IAAI0Q,QAAA,CAASF,KAAA,EAAOC,YAAY;IACxC;IAED,SAASR,mBAAmBD,IAAA,EAAMjS,MAAA,EAAQ8R,QAAA,EAAU;MAGlDG,IAAA,CAAK/B,QAAA,CAAS,UAAUrY,MAAA,EAAQ;QAC9B,IAAIA,MAAA,CAAO+a,MAAA,KAAW,MAAM;UAC1B,IAAI3Q,WAAA;UAIJ,SAASzL,CAAA,GAAI,GAAGA,CAAA,GAAIwJ,MAAA,CAAOtJ,MAAA,EAAQF,CAAA,IAAK;YACtC,MAAMkX,KAAA,GAAQ1N,MAAA,CAAOxJ,CAAC;YAEtB,IAAIkX,KAAA,CAAMrX,IAAA,KAASwB,MAAA,CAAOxB,IAAA,EAAM;cAC9B4L,WAAA,GAAcyL,KAAA,CAAMzL,WAAA;cACpB;YACD;UACF;UAED,IAAIA,WAAA,KAAgB,QAAW;YAO7BA,WAAA,GAAc,IAAIF,OAAA,CAAS;UAC5B;UAED+P,QAAA,CAASjb,IAAA,CAAK;YAAE0b,IAAA,EAAM1a,MAAA;YAAQoK,WAAA;YAA0BuQ,SAAA,EAAW;UAAA,CAAO;QAC3E;MACT,CAAO;IACF;IAED,SAASK,UAAU/Z,IAAA,EAAM;MACvB,MAAMga,OAAA,GAAU,EAAE;MAElB,MAAMjC,OAAA,GAAS/X,IAAA,CAAKoD,MAAA;MACpB,MAAMN,KAAA,GAAQ9C,IAAA,CAAK8C,KAAA;MACnB,MAAMyH,IAAA,GAAOvK,IAAA,CAAKuK,IAAA;MAClB,MAAM6N,eAAA,GAAkBpY,IAAA,CAAKoY,eAAA;MAC7B,MAAMC,mBAAA,GAAsBrY,IAAA,CAAKqY,mBAAA;MACjC,MAAMC,cAAA,GAAiBtY,IAAA,CAAKsY,cAAA;MAC5B,MAAMC,kBAAA,GAAqBvY,IAAA,CAAKuY,kBAAA;MAChC,MAAMC,aAAA,GAAgBxY,IAAA,CAAKwY,aAAA;MAI3B,SAAS9a,CAAA,GAAI,GAAGC,CAAA,GAAImF,KAAA,CAAMlF,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5Csc,OAAA,CAAQjc,IAAA,CAAKiF,OAAA,CAAQF,KAAA,CAAMpF,CAAC,CAAC,CAAC;MAC/B;MAID,SAASA,CAAA,GAAI,GAAGC,CAAA,GAAIya,eAAA,CAAgBxa,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACtD,MAAMuc,cAAA,GAAiBpK,SAAA,CAAUuI,eAAA,CAAgB1a,CAAC,CAAC;QAEnD,IAAIuc,cAAA,KAAmB,MAAM;UAC3BD,OAAA,CAAQjc,IAAA,CAAKkc,cAAA,CAAe5W,KAAA,EAAO;QACpC;MACF;MAID,SAAS3F,CAAA,GAAI,GAAGC,CAAA,GAAI0a,mBAAA,CAAoBza,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC1D,MAAMmb,QAAA,GAAWR,mBAAA,CAAoB3a,CAAC;QACtC,MAAMwc,UAAA,GAAa9Q,aAAA,CAAcyP,QAAA,CAASnY,EAAE;QAC5C,MAAMiH,UAAA,GAAa8L,WAAA,CAAYyG,UAAA,CAAWxZ,EAAE;QAC5C,MAAMyZ,UAAA,GAAaC,YAAA,CAAazS,UAAA,EAAYkR,QAAA,CAASvN,SAAS;QAE9D,MAAMqN,SAAA,GAAYE,QAAA,CAASF,SAAA;QAC3B,MAAMzR,MAAA,GAASgT,UAAA,CAAWpT,IAAA,CAAKI,MAAA;QAE/B,MAAMgS,QAAA,GAAWH,aAAA,CAAcJ,SAAA,EAAWzR,MAAM;QAEhD,SAASxD,CAAA,GAAI,GAAGC,EAAA,GAAKwW,UAAA,CAAWvc,MAAA,EAAQ8F,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACnD,MAAM2W,OAAA,GAASF,UAAA,CAAWzW,CAAC;UAE3B,IAAI2W,OAAA,CAAOC,aAAA,EAAe;YACxBD,OAAA,CAAOE,IAAA,CAAKrB,QAAA,EAAUgB,UAAA,CAAWpT,IAAA,CAAKkC,UAAU;YAChDqR,OAAA,CAAOG,oBAAA,CAAsB;UAC9B;UAEDR,OAAA,CAAQjc,IAAA,CAAKsc,OAAM;QACpB;MACF;MAID,SAAS3c,CAAA,GAAI,GAAGC,CAAA,GAAI2a,cAAA,CAAe1a,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACrD,MAAM+c,aAAA,GAAgB1J,QAAA,CAASuH,cAAA,CAAe5a,CAAC,CAAC;QAEhD,IAAI+c,aAAA,KAAkB,MAAM;UAC1BT,OAAA,CAAQjc,IAAA,CAAK0c,aAAA,CAAcpX,KAAA,EAAO;QACnC;MACF;MAID,SAAS3F,CAAA,GAAI,GAAGC,CAAA,GAAI4a,kBAAA,CAAmB3a,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACzD,MAAMmb,QAAA,GAAWN,kBAAA,CAAmB7a,CAAC;QAKrC,MAAMiK,UAAA,GAAa8L,WAAA,CAAYoF,QAAA,CAASnY,EAAE;QAC1C,MAAMyZ,UAAA,GAAaC,YAAA,CAAazS,UAAA,EAAYkR,QAAA,CAASvN,SAAS;QAE9D,SAAS5H,CAAA,GAAI,GAAGC,EAAA,GAAKwW,UAAA,CAAWvc,MAAA,EAAQ8F,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACnDsW,OAAA,CAAQjc,IAAA,CAAKoc,UAAA,CAAWzW,CAAC,CAAC;QAC3B;MACF;MAID,SAAShG,CAAA,GAAI,GAAGC,CAAA,GAAI6a,aAAA,CAAc5a,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACpDsc,OAAA,CAAQjc,IAAA,CAAKiF,OAAA,CAAQwV,aAAA,CAAc9a,CAAC,CAAC,EAAE2F,KAAA,EAAO;MAC/C;MAED,IAAItE,MAAA;MAEJ,IAAI+D,KAAA,CAAMlF,MAAA,KAAW,KAAKoc,OAAA,CAAQpc,MAAA,KAAW,GAAG;QAC9CmB,MAAA,GAASib,OAAA,CAAQ,CAAC;MAC1B,OAAa;QACLjb,MAAA,GAASwL,IAAA,KAAS,UAAU,IAAImQ,IAAA,CAAM,IAAG,IAAIC,KAAA,CAAO;QAEpD,SAASjd,CAAA,GAAI,GAAGA,CAAA,GAAIsc,OAAA,CAAQpc,MAAA,EAAQF,CAAA,IAAK;UACvCqB,MAAA,CAAO6b,GAAA,CAAIZ,OAAA,CAAQtc,CAAC,CAAC;QACtB;MACF;MAEDqB,MAAA,CAAOxB,IAAA,GAAOgN,IAAA,KAAS,UAAUvK,IAAA,CAAKuB,GAAA,GAAMvB,IAAA,CAAKzC,IAAA;MACjDwB,MAAA,CAAOqE,MAAA,CAAO0N,IAAA,CAAKiH,OAAM;MACzBhZ,MAAA,CAAOqE,MAAA,CAAO8B,SAAA,CAAUnG,MAAA,CAAOwF,QAAA,EAAUxF,MAAA,CAAO2F,UAAA,EAAY3F,MAAA,CAAO0F,KAAK;MAExE,OAAO1F,MAAA;IACR;IAED,MAAM8b,gBAAA,GAAmB,IAAIzO,iBAAA,CAAkB;MAAEmB,KAAA,EAAO;IAAQ,CAAE;IAElE,SAASuN,uBAAuB7b,IAAA,EAAM8b,iBAAA,EAAmB;MACvD,MAAMzP,SAAA,GAAY,EAAE;MAEpB,SAAS5N,CAAA,GAAI,GAAGC,CAAA,GAAIsB,IAAA,CAAKrB,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC3C,MAAMgD,EAAA,GAAKqa,iBAAA,CAAkB9b,IAAA,CAAKvB,CAAC,CAAC;QAEpC,IAAIgD,EAAA,KAAO,QAAW;UACpBxD,OAAA,CAAQ4G,IAAA,CAAK,iFAAiF7E,IAAA,CAAKvB,CAAC,CAAC;UACrG4N,SAAA,CAAUvN,IAAA,CAAK8c,gBAAgB;QACzC,OAAe;UACLvP,SAAA,CAAUvN,IAAA,CAAK4Q,WAAA,CAAYjO,EAAE,CAAC;QAC/B;MACF;MAED,OAAO4K,SAAA;IACR;IAED,SAAS8O,aAAazS,UAAA,EAAYoT,iBAAA,EAAmB;MACnD,MAAMf,OAAA,GAAU,EAAE;MAElB,WAAWzP,IAAA,IAAQ5C,UAAA,EAAY;QAC7B,MAAMD,QAAA,GAAWC,UAAA,CAAW4C,IAAI;QAEhC,MAAMe,SAAA,GAAYwP,sBAAA,CAAuBpT,QAAA,CAAS+K,YAAA,EAAcsI,iBAAiB;QAIjF,IAAIzP,SAAA,CAAU1N,MAAA,KAAW,GAAG;UAC1B,IAAI2M,IAAA,KAAS,WAAWA,IAAA,KAAS,cAAc;YAC7Ce,SAAA,CAAUvN,IAAA,CAAK,IAAIid,iBAAA,EAAmB;UAClD,OAAiB;YACL1P,SAAA,CAAUvN,IAAA,CAAK,IAAImO,iBAAA,EAAmB;UACvC;QACF;QAID,MAAM+O,QAAA,GAAWvT,QAAA,CAAS1H,IAAA,CAAKkb,UAAA,CAAWxS,SAAA,KAAc;QAIxD,MAAMuD,QAAA,GAAWX,SAAA,CAAU1N,MAAA,KAAW,IAAI0N,SAAA,CAAU,CAAC,IAAIA,SAAA;QAIzD,IAAIvM,MAAA;QAEJ,QAAQwL,IAAA;UACN,KAAK;YACHxL,MAAA,GAAS,IAAIoc,YAAA,CAAazT,QAAA,CAAS1H,IAAA,EAAMiM,QAAQ;YACjD;UAEF,KAAK;YACHlN,MAAA,GAAS,IAAIqc,IAAA,CAAK1T,QAAA,CAAS1H,IAAA,EAAMiM,QAAQ;YACzC;UAEF,KAAK;UACL,KAAK;YACH,IAAIgP,QAAA,EAAU;cACZlc,MAAA,GAAS,IAAIsc,WAAA,CAAY3T,QAAA,CAAS1H,IAAA,EAAMiM,QAAQ;YAC9D,OAAmB;cACLlN,MAAA,GAAS,IAAIuc,IAAA,CAAK5T,QAAA,CAAS1H,IAAA,EAAMiM,QAAQ;YAC1C;YAED;QACH;QAED+N,OAAA,CAAQjc,IAAA,CAAKgB,MAAM;MACpB;MAED,OAAOib,OAAA;IACR;IAED,SAAStB,QAAQhY,EAAA,EAAI;MACnB,OAAOK,OAAA,CAAQ+B,KAAA,CAAMpC,EAAE,MAAM;IAC9B;IAED,SAASsC,QAAQtC,EAAA,EAAI;MACnB,OAAOP,QAAA,CAASY,OAAA,CAAQ+B,KAAA,CAAMpC,EAAE,GAAGqZ,SAAS;IAC7C;IAID,SAASwB,iBAAiBje,IAAA,EAAK;MAC7B,MAAM0C,IAAA,GAAO;QACXzC,IAAA,EAAMD,IAAA,CAAIkC,YAAA,CAAa,MAAM;QAC7B8Z,QAAA,EAAU;MACX;MAEDtB,YAAA,CAAa1a,IAAG;MAEhB,MAAMwC,QAAA,GAAWzC,oBAAA,CAAqBC,IAAA,EAAK,MAAM;MAEjD,SAASI,CAAA,GAAI,GAAGA,CAAA,GAAIoC,QAAA,CAASlC,MAAA,EAAQF,CAAA,IAAK;QACxCsC,IAAA,CAAKsZ,QAAA,CAASvb,IAAA,CAAKoa,SAAA,CAAUrY,QAAA,CAASpC,CAAC,CAAC,CAAC;MAC1C;MAEDqD,OAAA,CAAQuV,YAAA,CAAahZ,IAAA,CAAIkC,YAAA,CAAa,IAAI,CAAC,IAAIQ,IAAA;IAChD;IAED,SAASwb,iBAAiBxb,IAAA,EAAM;MAC9B,MAAMyb,KAAA,GAAQ,IAAId,KAAA,CAAO;MACzBc,KAAA,CAAMle,IAAA,GAAOyC,IAAA,CAAKzC,IAAA;MAElB,MAAM+b,QAAA,GAAWtZ,IAAA,CAAKsZ,QAAA;MAEtB,SAAS5b,CAAA,GAAI,GAAGA,CAAA,GAAI4b,QAAA,CAAS1b,MAAA,EAAQF,CAAA,IAAK;QACxC,MAAMG,KAAA,GAAQyb,QAAA,CAAS5b,CAAC;QAExB+d,KAAA,CAAMb,GAAA,CAAI5X,OAAA,CAAQnF,KAAA,CAAM6C,EAAE,CAAC;MAC5B;MAED,OAAO+a,KAAA;IACR;IAED,SAASpC,eAAe3Y,EAAA,EAAI;MAC1B,OAAOK,OAAA,CAAQuV,YAAA,CAAa5V,EAAE,MAAM;IACrC;IAED,SAASgW,eAAehW,EAAA,EAAI;MAC1B,OAAOP,QAAA,CAASY,OAAA,CAAQuV,YAAA,CAAa5V,EAAE,GAAG8a,gBAAgB;IAC3D;IAID,SAASE,WAAWpe,IAAA,EAAK;MACvB,MAAMub,QAAA,GAAWxb,oBAAA,CAAqBC,IAAA,EAAK,uBAAuB,EAAE,CAAC;MACrE,OAAOoZ,cAAA,CAAehY,OAAA,CAAQma,QAAA,CAASrZ,YAAA,CAAa,KAAK,CAAC,CAAC;IAC5D;IAED,SAASmc,gBAAA,EAAkB;MACzB,MAAMrV,KAAA,GAAQvF,OAAA,CAAQuF,KAAA;MAEtB,IAAIxH,OAAA,CAAQwH,KAAK,MAAM,MAAM;QAC3B,IAAIxH,OAAA,CAAQiC,OAAA,CAAQC,UAAU,MAAM,OAAO;UAGzC,MAAMgB,MAAA,GAAS,EAAE;UAEjB,WAAWtB,EAAA,IAAMK,OAAA,CAAQC,UAAA,EAAY;YACnC,MAAM0F,eAAA,GAAkB9D,YAAA,CAAalC,EAAE;YAEvC,SAAShD,CAAA,GAAI,GAAGC,CAAA,GAAI+I,eAAA,CAAgB9I,MAAA,EAAQF,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;cACtDsE,MAAA,CAAOjE,IAAA,CAAK2I,eAAA,CAAgBhJ,CAAC,CAAC;YAC/B;UACF;UAEDsD,UAAA,CAAWjD,IAAA,CAAK,IAAI4I,aAAA,CAAc,WAAW,IAAI3E,MAAM,CAAC;QACzD;MACT,OAAa;QACL,WAAWtB,EAAA,IAAM4F,KAAA,EAAO;UACtBtF,UAAA,CAAWjD,IAAA,CAAK6I,gBAAA,CAAiBlG,EAAE,CAAC;QACrC;MACF;IACF;IAKD,SAASkb,kBAAkBC,YAAA,EAAa;MACtC,IAAIC,MAAA,GAAS;MACb,MAAMC,KAAA,GAAQ,CAACF,YAAW;MAE1B,OAAOE,KAAA,CAAMne,MAAA,EAAQ;QACnB,MAAMiF,IAAA,GAAOkZ,KAAA,CAAMza,KAAA,CAAO;QAE1B,IAAIuB,IAAA,CAAKpC,QAAA,KAAaub,IAAA,CAAKC,SAAA,EAAW;UACpCH,MAAA,IAAUjZ,IAAA,CAAKpD,WAAA;QACzB,OAAe;UACLqc,MAAA,IAAU;UACVC,KAAA,CAAMhe,IAAA,CAAKme,KAAA,CAAMH,KAAA,EAAOlZ,IAAA,CAAKpF,UAAU;QACxC;MACF;MAED,OAAOqe,MAAA,CAAO3d,IAAA,CAAM;IACrB;IAED,IAAIpB,IAAA,CAAKa,MAAA,KAAW,GAAG;MACrB,OAAO;QAAEue,KAAA,EAAO,IAAIC,KAAA;MAAS;IAC9B;IAED,MAAMC,GAAA,GAAM,IAAIC,SAAA,CAAS,EAAGC,eAAA,CAAgBxf,IAAA,EAAM,iBAAiB;IAEnE,MAAM8Z,OAAA,GAAUxZ,oBAAA,CAAqBgf,GAAA,EAAK,SAAS,EAAE,CAAC;IAEtD,MAAMG,WAAA,GAAcH,GAAA,CAAIhf,oBAAA,CAAqB,aAAa,EAAE,CAAC;IAC7D,IAAImf,WAAA,KAAgB,QAAW;MAG7B,MAAMC,YAAA,GAAepf,oBAAA,CAAqBmf,WAAA,EAAa,KAAK,EAAE,CAAC;MAC/D,IAAIE,SAAA;MAEJ,IAAID,YAAA,EAAc;QAChBC,SAAA,GAAYD,YAAA,CAAahd,WAAA;MACjC,OAAa;QACLid,SAAA,GAAYd,iBAAA,CAAkBY,WAAW;MAC1C;MAEDtf,OAAA,CAAQC,KAAA,CAAM,wDAAwDuf,SAAS;MAE/E,OAAO;IACR;IAID,MAAMC,OAAA,GAAU9F,OAAA,CAAQrX,YAAA,CAAa,SAAS;IAC9CtC,OAAA,CAAQ4D,GAAA,CAAI,qCAAqC6b,OAAO;IAExD,MAAMC,KAAA,GAAQ1d,UAAA,CAAW7B,oBAAA,CAAqBwZ,OAAA,EAAS,OAAO,EAAE,CAAC,CAAC;IAClE,MAAM/K,aAAA,GAAgB,IAAI+Q,aAAA,CAAc,KAAK/gB,OAAO;IACpDgQ,aAAA,CAAcpP,OAAA,CAAQ,KAAKogB,YAAA,IAAgBzgB,IAAI,EAAE0gB,cAAA,CAAe,KAAKC,WAAW;IAEhF,IAAInR,SAAA;IAEJ,IAAIoR,SAAA,EAAW;MACbpR,SAAA,GAAY,IAAIoR,SAAA,CAAU,KAAKnhB,OAAO;MACtC+P,SAAA,CAAUnP,OAAA,CAAQ,KAAKogB,YAAA,IAAgBzgB,IAAI;IAC5C;IAID,MAAM2E,UAAA,GAAa,EAAE;IACrB,IAAIuW,UAAA,GAAa,CAAE;IACnB,IAAI1Y,KAAA,GAAQ;IAIZ,MAAMkC,OAAA,GAAU;MACdC,UAAA,EAAY,CAAE;MACdsF,KAAA,EAAO,CAAE;MACTU,WAAA,EAAa,CAAE;MACfuC,MAAA,EAAQ,CAAE;MACVM,OAAA,EAAS,CAAE;MACXyB,SAAA,EAAW,CAAE;MACbyD,OAAA,EAAS,CAAE;MACXiB,MAAA,EAAQ,CAAE;MACVrI,UAAA,EAAY,CAAE;MACd7E,KAAA,EAAO,CAAE;MACTwT,YAAA,EAAc,CAAE;MAChBzC,gBAAA,EAAkB,CAAE;MACpBuB,aAAA,EAAe,CAAE;MACjBO,gBAAA,EAAkB,CAAE;IACrB;IAEDjW,YAAA,CAAamX,OAAA,EAAS,sBAAsB,aAAazW,cAAc;IACvEV,YAAA,CAAamX,OAAA,EAAS,2BAA2B,kBAAkB1Q,kBAAkB;IACrFzG,YAAA,CAAamX,OAAA,EAAS,uBAAuB,cAAchQ,eAAe;IAC1EnH,YAAA,CAAamX,OAAA,EAAS,kBAAkB,SAASxN,UAAU;IAC3D3J,YAAA,CAAamX,OAAA,EAAS,mBAAmB,UAAUnN,WAAW;IAC9DhK,YAAA,CAAamX,OAAA,EAAS,qBAAqB,YAAYxL,aAAa;IACpE3L,YAAA,CAAamX,OAAA,EAAS,mBAAmB,UAAUjI,WAAW;IAC9DlP,YAAA,CAAamX,OAAA,EAAS,kBAAkB,SAAS/G,UAAU;IAC3DpQ,YAAA,CAAamX,OAAA,EAAS,sBAAsB,YAAY7F,aAAa;IACrEtR,YAAA,CAAamX,OAAA,EAAS,iBAAiB,QAAQsB,SAAS;IACxDzY,YAAA,CAAamX,OAAA,EAAS,yBAAyB,gBAAgB0E,gBAAgB;IAC/E7b,YAAA,CAAamX,OAAA,EAAS,6BAA6B,oBAAoBnD,oBAAoB;IAC3FhU,YAAA,CAAamX,OAAA,EAAS,0BAA0B,iBAAiB5B,iBAAiB;IAClFvV,YAAA,CAAamX,OAAA,EAAS,SAAS,6BAA6BrB,oBAAoB;IAEhFzV,YAAA,CAAagB,OAAA,CAAQC,UAAA,EAAYe,cAAc;IAC/ChC,YAAA,CAAagB,OAAA,CAAQuF,KAAA,EAAOC,kBAAkB;IAC9CxG,YAAA,CAAagB,OAAA,CAAQiG,WAAA,EAAaS,eAAe;IACjD1H,YAAA,CAAagB,OAAA,CAAQwI,MAAA,EAAQC,UAAU;IACvCzJ,YAAA,CAAagB,OAAA,CAAQ8I,OAAA,EAASsB,WAAW;IACzCpL,YAAA,CAAagB,OAAA,CAAQuK,SAAA,EAAWS,aAAa;IAC7ChM,YAAA,CAAagB,OAAA,CAAQgO,OAAA,EAASG,WAAW;IACzCnP,YAAA,CAAagB,OAAA,CAAQiP,MAAA,EAAQQ,UAAU;IACvCzQ,YAAA,CAAagB,OAAA,CAAQ4G,UAAA,EAAYqK,aAAa;IAC9CjS,YAAA,CAAagB,OAAA,CAAQuV,YAAA,EAAckF,gBAAgB;IAEnDG,eAAA,CAAiB;IACjBzF,eAAA,CAAiB;IAEjB,MAAMiG,KAAA,GAAQT,UAAA,CAAWre,oBAAA,CAAqBwZ,OAAA,EAAS,OAAO,EAAE,CAAC,CAAC;IAClEsF,KAAA,CAAMnb,UAAA,GAAaA,UAAA;IAEnB,IAAI4b,KAAA,CAAMvd,MAAA,KAAW,QAAQ;MAC3B8c,KAAA,CAAMzX,UAAA,CAAWwY,YAAA,CAAa,IAAIC,KAAA,CAAM,CAAC7M,IAAA,CAAK8M,EAAA,GAAK,GAAG,GAAG,CAAC,CAAC;IAC5D;IAEDjB,KAAA,CAAM1X,KAAA,CAAM4Y,cAAA,CAAeT,KAAA,CAAMzd,IAAI;IAErC,OAAO;MACL,IAAI6B,WAAA,EAAa;QACf9D,OAAA,CAAQ4G,IAAA,CAAK,0EAA0E;QACvF,OAAO9C,UAAA;MACR;MACDuW,UAAA;MACAxW,OAAA;MACAob;IACD;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}