{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport pick from 'lodash.pick';\nimport { SkeletonUtils } from 'three-stdlib';\nfunction createSpread(child, {\n  keys = ['near', 'far', 'color', 'distance', 'decay', 'penumbra', 'angle', 'intensity', 'skeleton', 'visible', 'castShadow', 'receiveShadow', 'morphTargetDictionary', 'morphTargetInfluences', 'name', 'geometry', 'material', 'position', 'rotation', 'scale', 'up', 'userData', 'bindMode', 'bindMatrix', 'bindMatrixInverse', 'skeleton'],\n  deep,\n  inject,\n  castShadow,\n  receiveShadow\n}) {\n  let spread = pick(child, keys);\n  if (deep) {\n    if (spread.geometry && deep !== 'materialsOnly') spread.geometry = spread.geometry.clone();\n    if (spread.material && deep !== 'geometriesOnly') spread.material = spread.material.clone();\n  }\n  if (inject) {\n    if (typeof inject === 'function') spread = {\n      ...spread,\n      children: inject(child)\n    };else if (/*#__PURE__*/React.isValidElement(inject)) spread = {\n      ...spread,\n      children: inject\n    };else spread = {\n      ...spread,\n      ...inject\n    };\n  }\n  if (child instanceof THREE.Mesh) {\n    if (castShadow) spread.castShadow = true;\n    if (receiveShadow) spread.receiveShadow = true;\n  }\n  return spread;\n}\nconst Clone = /*#__PURE__*/React.forwardRef(({\n  isChild = false,\n  object,\n  children,\n  deep,\n  castShadow,\n  receiveShadow,\n  inject,\n  keys,\n  ...props\n}, forwardRef) => {\n  var _object;\n  const config = {\n    keys,\n    deep,\n    inject,\n    castShadow,\n    receiveShadow\n  };\n  object = React.useMemo(() => {\n    if (isChild === false && !Array.isArray(object)) {\n      let isSkinned = false;\n      object.traverse(object => {\n        if (object.isSkinnedMesh) isSkinned = true;\n      });\n      if (isSkinned) return SkeletonUtils.clone(object);\n    }\n    return object;\n  }, [object, isChild]); // Deal with arrayed clones\n\n  if (Array.isArray(object)) {\n    return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n      ref: forwardRef\n    }), object.map(o => /*#__PURE__*/React.createElement(Clone, _extends({\n      key: o.uuid,\n      object: o\n    }, config))), children);\n  } // Singleton clones\n\n  const {\n    children: injectChildren,\n    ...spread\n  } = createSpread(object, config);\n  const Element = object.type[0].toLowerCase() + object.type.slice(1);\n  return /*#__PURE__*/React.createElement(Element, _extends({}, spread, props, {\n    ref: forwardRef\n  }), ((_object = object) == null ? void 0 : _object.children).map(child => {\n    if (child.type === 'Bone') return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n      key: child.uuid,\n      object: child\n    }, config));\n    return /*#__PURE__*/React.createElement(Clone, _extends({\n      key: child.uuid,\n      object: child\n    }, config, {\n      isChild: true\n    }));\n  }), children, injectChildren);\n});\nexport { Clone };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "pick", "SkeletonUtils", "createSpread", "child", "keys", "deep", "inject", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "spread", "geometry", "clone", "material", "children", "isValidElement", "<PERSON><PERSON>", "<PERSON><PERSON>", "forwardRef", "<PERSON><PERSON><PERSON><PERSON>", "object", "props", "_object", "config", "useMemo", "Array", "isArray", "isSkinned", "traverse", "isSkinnedMesh", "createElement", "ref", "map", "o", "key", "uuid", "injectChildren", "Element", "type", "toLowerCase", "slice"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Clone.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport pick from 'lodash.pick';\nimport { SkeletonUtils } from 'three-stdlib';\n\nfunction createSpread(child, {\n  keys = ['near', 'far', 'color', 'distance', 'decay', 'penumbra', 'angle', 'intensity', 'skeleton', 'visible', 'castShadow', 'receiveShadow', 'morphTargetDictionary', 'morphTargetInfluences', 'name', 'geometry', 'material', 'position', 'rotation', 'scale', 'up', 'userData', 'bindMode', 'bindMatrix', 'bindMatrixInverse', 'skeleton'],\n  deep,\n  inject,\n  castShadow,\n  receiveShadow\n}) {\n  let spread = pick(child, keys);\n\n  if (deep) {\n    if (spread.geometry && deep !== 'materialsOnly') spread.geometry = spread.geometry.clone();\n    if (spread.material && deep !== 'geometriesOnly') spread.material = spread.material.clone();\n  }\n\n  if (inject) {\n    if (typeof inject === 'function') spread = { ...spread,\n      children: inject(child)\n    };else if ( /*#__PURE__*/React.isValidElement(inject)) spread = { ...spread,\n      children: inject\n    };else spread = { ...spread,\n      ...inject\n    };\n  }\n\n  if (child instanceof THREE.Mesh) {\n    if (castShadow) spread.castShadow = true;\n    if (receiveShadow) spread.receiveShadow = true;\n  }\n\n  return spread;\n}\n\nconst Clone = /*#__PURE__*/React.forwardRef(({\n  isChild = false,\n  object,\n  children,\n  deep,\n  castShadow,\n  receiveShadow,\n  inject,\n  keys,\n  ...props\n}, forwardRef) => {\n  var _object;\n\n  const config = {\n    keys,\n    deep,\n    inject,\n    castShadow,\n    receiveShadow\n  };\n  object = React.useMemo(() => {\n    if (isChild === false && !Array.isArray(object)) {\n      let isSkinned = false;\n      object.traverse(object => {\n        if (object.isSkinnedMesh) isSkinned = true;\n      });\n      if (isSkinned) return SkeletonUtils.clone(object);\n    }\n\n    return object;\n  }, [object, isChild]); // Deal with arrayed clones\n\n  if (Array.isArray(object)) {\n    return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n      ref: forwardRef\n    }), object.map(o => /*#__PURE__*/React.createElement(Clone, _extends({\n      key: o.uuid,\n      object: o\n    }, config))), children);\n  } // Singleton clones\n\n\n  const {\n    children: injectChildren,\n    ...spread\n  } = createSpread(object, config);\n  const Element = object.type[0].toLowerCase() + object.type.slice(1);\n  return /*#__PURE__*/React.createElement(Element, _extends({}, spread, props, {\n    ref: forwardRef\n  }), ((_object = object) == null ? void 0 : _object.children).map(child => {\n    if (child.type === 'Bone') return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n      key: child.uuid,\n      object: child\n    }, config));\n    return /*#__PURE__*/React.createElement(Clone, _extends({\n      key: child.uuid,\n      object: child\n    }, config, {\n      isChild: true\n    }));\n  }), children, injectChildren);\n});\n\nexport { Clone };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3BC,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,EAAE,uBAAuB,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,CAAC;EAC5UC,IAAI;EACJC,MAAM;EACNC,UAAU;EACVC;AACF,CAAC,EAAE;EACD,IAAIC,MAAM,GAAGT,IAAI,CAACG,KAAK,EAAEC,IAAI,CAAC;EAE9B,IAAIC,IAAI,EAAE;IACR,IAAII,MAAM,CAACC,QAAQ,IAAIL,IAAI,KAAK,eAAe,EAAEI,MAAM,CAACC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAC;IAC1F,IAAIF,MAAM,CAACG,QAAQ,IAAIP,IAAI,KAAK,gBAAgB,EAAEI,MAAM,CAACG,QAAQ,GAAGH,MAAM,CAACG,QAAQ,CAACD,KAAK,CAAC,CAAC;EAC7F;EAEA,IAAIL,MAAM,EAAE;IACV,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAEG,MAAM,GAAG;MAAE,GAAGA,MAAM;MACpDI,QAAQ,EAAEP,MAAM,CAACH,KAAK;IACxB,CAAC,CAAC,KAAK,IAAK,aAAaJ,KAAK,CAACe,cAAc,CAACR,MAAM,CAAC,EAAEG,MAAM,GAAG;MAAE,GAAGA,MAAM;MACzEI,QAAQ,EAAEP;IACZ,CAAC,CAAC,KAAKG,MAAM,GAAG;MAAE,GAAGA,MAAM;MACzB,GAAGH;IACL,CAAC;EACH;EAEA,IAAIH,KAAK,YAAYL,KAAK,CAACiB,IAAI,EAAE;IAC/B,IAAIR,UAAU,EAAEE,MAAM,CAACF,UAAU,GAAG,IAAI;IACxC,IAAIC,aAAa,EAAEC,MAAM,CAACD,aAAa,GAAG,IAAI;EAChD;EAEA,OAAOC,MAAM;AACf;AAEA,MAAMO,KAAK,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,CAAC;EAC3CC,OAAO,GAAG,KAAK;EACfC,MAAM;EACNN,QAAQ;EACRR,IAAI;EACJE,UAAU;EACVC,aAAa;EACbF,MAAM;EACNF,IAAI;EACJ,GAAGgB;AACL,CAAC,EAAEH,UAAU,KAAK;EAChB,IAAII,OAAO;EAEX,MAAMC,MAAM,GAAG;IACblB,IAAI;IACJC,IAAI;IACJC,MAAM;IACNC,UAAU;IACVC;EACF,CAAC;EACDW,MAAM,GAAGpB,KAAK,CAACwB,OAAO,CAAC,MAAM;IAC3B,IAAIL,OAAO,KAAK,KAAK,IAAI,CAACM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;MAC/C,IAAIO,SAAS,GAAG,KAAK;MACrBP,MAAM,CAACQ,QAAQ,CAACR,MAAM,IAAI;QACxB,IAAIA,MAAM,CAACS,aAAa,EAAEF,SAAS,GAAG,IAAI;MAC5C,CAAC,CAAC;MACF,IAAIA,SAAS,EAAE,OAAOzB,aAAa,CAACU,KAAK,CAACQ,MAAM,CAAC;IACnD;IAEA,OAAOA,MAAM;EACf,CAAC,EAAE,CAACA,MAAM,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEvB,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;IACzB,OAAO,aAAapB,KAAK,CAAC8B,aAAa,CAAC,OAAO,EAAEhC,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;MACnEU,GAAG,EAAEb;IACP,CAAC,CAAC,EAAEE,MAAM,CAACY,GAAG,CAACC,CAAC,IAAI,aAAajC,KAAK,CAAC8B,aAAa,CAACb,KAAK,EAAEnB,QAAQ,CAAC;MACnEoC,GAAG,EAAED,CAAC,CAACE,IAAI;MACXf,MAAM,EAAEa;IACV,CAAC,EAAEV,MAAM,CAAC,CAAC,CAAC,EAAET,QAAQ,CAAC;EACzB,CAAC,CAAC;;EAGF,MAAM;IACJA,QAAQ,EAAEsB,cAAc;IACxB,GAAG1B;EACL,CAAC,GAAGP,YAAY,CAACiB,MAAM,EAAEG,MAAM,CAAC;EAChC,MAAMc,OAAO,GAAGjB,MAAM,CAACkB,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnB,MAAM,CAACkB,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;EACnE,OAAO,aAAaxC,KAAK,CAAC8B,aAAa,CAACO,OAAO,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAEY,MAAM,EAAEW,KAAK,EAAE;IAC3EU,GAAG,EAAEb;EACP,CAAC,CAAC,EAAE,CAAC,CAACI,OAAO,GAAGF,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,OAAO,CAACR,QAAQ,EAAEkB,GAAG,CAAC5B,KAAK,IAAI;IACxE,IAAIA,KAAK,CAACkC,IAAI,KAAK,MAAM,EAAE,OAAO,aAAatC,KAAK,CAAC8B,aAAa,CAAC,WAAW,EAAEhC,QAAQ,CAAC;MACvFoC,GAAG,EAAE9B,KAAK,CAAC+B,IAAI;MACff,MAAM,EAAEhB;IACV,CAAC,EAAEmB,MAAM,CAAC,CAAC;IACX,OAAO,aAAavB,KAAK,CAAC8B,aAAa,CAACb,KAAK,EAAEnB,QAAQ,CAAC;MACtDoC,GAAG,EAAE9B,KAAK,CAAC+B,IAAI;MACff,MAAM,EAAEhB;IACV,CAAC,EAAEmB,MAAM,EAAE;MACTJ,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,EAAEL,QAAQ,EAAEsB,cAAc,CAAC;AAC/B,CAAC,CAAC;AAEF,SAASnB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}