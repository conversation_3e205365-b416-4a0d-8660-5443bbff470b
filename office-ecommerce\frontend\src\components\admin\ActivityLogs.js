import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import apiClient from '../../services/apiClient';
import './ActivityLogs.css';

// Import icons
import {
  SearchIcon,
  FilterIcon,
  RefreshIcon,
  ExportIcon,
  InfoIcon,
  WarningIcon,
  ErrorIcon,
  CriticalIcon
} from './icons/ActivityLogIcons';

const ActivityLogs = () => {
  const { user } = useAuth();
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 50
  });

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    action: '',
    entityType: '',
    severity: '',
    startDate: '',
    endDate: '',
    userID: ''
  });

  // Available options for filters
  const [filterOptions, setFilterOptions] = useState({
    actions: [],
    entityTypes: [],
    severities: ['INFO', 'WARNING', 'ERROR', 'CRITICAL']
  });

  // Statistics
  const [stats, setStats] = useState(null);

  // Load activity logs
  const loadActivityLogs = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.itemsPerPage.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      });

      const response = await apiClient.get(`/api/admin/activity-logs?${params}`);
      
      if (response.success) {
        setLogs(response.data.logs);
        setPagination(response.data.pagination);
      } else {
        throw new Error(response.message || 'Failed to load activity logs');
      }
    } catch (err) {
      console.error('Failed to load activity logs:', err);
      setError(err.message);
      
      // Fallback to mock data if backend is not accessible or we're in mock mode
      if (err.message.includes('Network error') || isMockMode()) {
        console.log('Using mock activity logs data');
        setLogs(getMockActivityLogs());
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalItems: 5,
          itemsPerPage: 50
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Load filter options
  const loadFilterOptions = async () => {
    try {
      const [actionsResponse, entityTypesResponse] = await Promise.all([
        apiClient.get('/api/admin/activity-logs/actions'),
        apiClient.get('/api/admin/activity-logs/entity-types')
      ]);

      setFilterOptions(prev => ({
        ...prev,
        actions: actionsResponse.success ? actionsResponse.data : [],
        entityTypes: entityTypesResponse.success ? entityTypesResponse.data : []
      }));
    } catch (err) {
      console.error('Failed to load filter options:', err);
      // Use fallback options
      setFilterOptions(prev => ({
        ...prev,
        actions: ['LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW'],
        entityTypes: ['User', 'Product', 'Order', 'Inventory', 'Authentication', 'Dashboard']
      }));
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const response = await apiClient.get('/api/admin/activity-logs/stats');
      if (response.success) {
        setStats(response.data);
      }
    } catch (err) {
      console.error('Failed to load activity stats:', err);
      // Use mock stats
      setStats({
        overview: {
          totalActivities: 1250,
          last24Hours: 45,
          last7Days: 320,
          errorCount: 8
        },
        actionStats: [
          { Action: 'LOGIN', count: 150 },
          { Action: 'VIEW', count: 300 },
          { Action: 'CREATE', count: 80 }
        ]
      });
    }
  };

  // Check if we're in mock mode
  const isMockMode = () => localStorage.getItem('mockMode') === 'true';

  // Mock data for fallback
  const getMockActivityLogs = () => [
    {
      LogID: '1',
      UserName: 'Admin User',
      UserEmail: '<EMAIL>',
      UserRole: 'Admin',
      Action: 'LOGIN',
      EntityType: 'Authentication',
      Description: 'User logged in successfully (mock mode)',
      Severity: 'INFO',
      CreatedAt: new Date().toISOString(),
      IPAddress: '127.0.0.1',
      Duration: 150
    },
    {
      LogID: '2',
      UserName: 'Admin User',
      UserEmail: '<EMAIL>',
      UserRole: 'Admin',
      Action: 'VIEW',
      EntityType: 'Dashboard',
      Description: 'Accessed admin dashboard',
      Severity: 'INFO',
      CreatedAt: new Date(Date.now() - 300000).toISOString(),
      IPAddress: '127.0.0.1',
      Duration: 89
    },
    {
      LogID: '3',
      UserName: 'Admin User',
      UserEmail: '<EMAIL>',
      UserRole: 'Admin',
      Action: 'VIEW',
      EntityType: 'ActivityLogs',
      Description: 'Accessed activity logs page',
      Severity: 'INFO',
      CreatedAt: new Date(Date.now() - 600000).toISOString(),
      IPAddress: '127.0.0.1',
      Duration: 120
    },
    {
      LogID: '4',
      UserName: 'Manager User',
      UserEmail: '<EMAIL>',
      UserRole: 'Employee',
      Action: 'CREATE',
      EntityType: 'Product',
      Description: 'Created new product: Office Chair Pro',
      Severity: 'INFO',
      CreatedAt: new Date(Date.now() - 900000).toISOString(),
      IPAddress: '*************',
      Duration: 250
    },
    {
      LogID: '5',
      UserName: 'System',
      UserEmail: null,
      UserRole: 'System',
      Action: 'ERROR',
      EntityType: 'Database',
      Description: 'Database connection timeout',
      Severity: 'ERROR',
      CreatedAt: new Date(Date.now() - 1200000).toISOString(),
      IPAddress: null,
      Duration: 5000
    }
  ];

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Apply filters
  const applyFilters = () => {
    loadActivityLogs(1);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      search: '',
      action: '',
      entityType: '',
      severity: '',
      startDate: '',
      endDate: '',
      userID: ''
    });
    setTimeout(() => loadActivityLogs(1), 100);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  // Get severity icon
  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'INFO':
        return <InfoIcon color="#3B82F6" />;
      case 'WARNING':
        return <WarningIcon color="#F59E0B" />;
      case 'ERROR':
        return <ErrorIcon color="#EF4444" />;
      case 'CRITICAL':
        return <CriticalIcon color="#DC2626" />;
      default:
        return <InfoIcon color="#6B7280" />;
    }
  };

  // Load data on component mount
  useEffect(() => {
    // If we're in mock mode, load mock data immediately
    if (isMockMode()) {
      console.log('Mock mode detected, loading mock data');
      setLogs(getMockActivityLogs());
      setPagination({
        currentPage: 1,
        totalPages: 1,
        totalItems: 5,
        itemsPerPage: 50
      });
      setLoading(false);
    } else {
      loadActivityLogs();
    }
    loadFilterOptions();
    loadStats();
  }, []);

  if (loading && logs.length === 0) {
    return (
      <div className="activity-logs">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading activity logs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="activity-logs">
      <div className="activity-logs-header">
        <div className="header-title">
          <h1>Activity Logs</h1>
          <p>Monitor system activities and user actions</p>
          {isMockMode() && (
            <div className="mock-mode-indicator">
              <WarningIcon color="#F59E0B" />
              <span>Demo Mode - Showing sample data</span>
            </div>
          )}
        </div>
        <div className="header-actions">
          <button
            className="btn btn-secondary"
            onClick={() => loadActivityLogs(pagination.currentPage)}
            disabled={loading}
          >
            <RefreshIcon />
            Refresh
          </button>
          <button className="btn btn-primary">
            <ExportIcon />
            Export
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="activity-stats">
          <div className="stat-card">
            <div className="stat-value">{stats.overview.totalActivities}</div>
            <div className="stat-label">Total Activities</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.overview.last24Hours}</div>
            <div className="stat-label">Last 24 Hours</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.overview.last7Days}</div>
            <div className="stat-label">Last 7 Days</div>
          </div>
          <div className="stat-card error">
            <div className="stat-value">{stats.overview.errorCount}</div>
            <div className="stat-label">Errors</div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="activity-filters">
        <div className="filters-row">
          <div className="filter-group">
            <label>Search</label>
            <div className="search-input">
              <SearchIcon />
              <input
                type="text"
                placeholder="Search descriptions, users, entities..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
          </div>
          
          <div className="filter-group">
            <label>Action</label>
            <select
              value={filters.action}
              onChange={(e) => handleFilterChange('action', e.target.value)}
            >
              <option value="">All Actions</option>
              {filterOptions.actions.map(action => (
                <option key={action} value={action}>{action}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Entity Type</label>
            <select
              value={filters.entityType}
              onChange={(e) => handleFilterChange('entityType', e.target.value)}
            >
              <option value="">All Types</option>
              {filterOptions.entityTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Severity</label>
            <select
              value={filters.severity}
              onChange={(e) => handleFilterChange('severity', e.target.value)}
            >
              <option value="">All Severities</option>
              {filterOptions.severities.map(severity => (
                <option key={severity} value={severity}>{severity}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="filters-row">
          <div className="filter-group">
            <label>Start Date</label>
            <input
              type="datetime-local"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
            />
          </div>

          <div className="filter-group">
            <label>End Date</label>
            <input
              type="datetime-local"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
            />
          </div>

          <div className="filter-actions">
            <button className="btn btn-primary" onClick={applyFilters}>
              <FilterIcon />
              Apply Filters
            </button>
            <button className="btn btn-secondary" onClick={clearFilters}>
              Clear
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <ErrorIcon />
          <span>{error}</span>
          {error.includes('Network error') && (
            <span className="fallback-notice"> (Using mock data)</span>
          )}
        </div>
      )}

      {/* Activity Logs Table */}
      <div className="activity-table-container">
        <table className="activity-table">
          <thead>
            <tr>
              <th>Timestamp</th>
              <th>User</th>
              <th>Action</th>
              <th>Entity</th>
              <th>Description</th>
              <th>Severity</th>
              <th>IP Address</th>
              <th>Duration</th>
            </tr>
          </thead>
          <tbody>
            {logs.map(log => (
              <tr key={log.LogID} className={`severity-${log.Severity.toLowerCase()}`}>
                <td className="timestamp">
                  {formatDate(log.CreatedAt)}
                </td>
                <td className="user-info">
                  <div className="user-name">{log.UserName || 'System'}</div>
                  <div className="user-role">{log.UserRole}</div>
                </td>
                <td className="action">
                  <span className={`action-badge ${log.Action.toLowerCase()}`}>
                    {log.Action}
                  </span>
                </td>
                <td className="entity">{log.EntityType}</td>
                <td className="description">{log.Description}</td>
                <td className="severity">
                  <div className="severity-indicator">
                    {getSeverityIcon(log.Severity)}
                    <span>{log.Severity}</span>
                  </div>
                </td>
                <td className="ip-address">{log.IPAddress || '-'}</td>
                <td className="duration">{log.Duration ? `${log.Duration}ms` : '-'}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="pagination">
          <button
            className="btn btn-secondary"
            disabled={pagination.currentPage === 1}
            onClick={() => loadActivityLogs(pagination.currentPage - 1)}
          >
            Previous
          </button>
          
          <span className="pagination-info">
            Page {pagination.currentPage} of {pagination.totalPages} 
            ({pagination.totalItems} total items)
          </span>
          
          <button
            className="btn btn-secondary"
            disabled={pagination.currentPage === pagination.totalPages}
            onClick={() => loadActivityLogs(pagination.currentPage + 1)}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default ActivityLogs;
