{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { InterleavedBufferAttribute, Matrix4, MeshLambertMaterial, MeshBasicMaterial, MeshPhongMaterial, Color, DoubleSide, Mesh } from \"three\";\nimport { UV1 } from \"../_polyfill/uv1.js\";\nclass ColladaExporter {\n  constructor() {\n    __publicField(this, \"options\");\n    __publicField(this, \"geometryInfo\");\n    __publicField(this, \"materialMap\");\n    __publicField(this, \"imageMap\");\n    __publicField(this, \"textures\");\n    __publicField(this, \"libraryImages\");\n    __publicField(this, \"libraryGeometries\");\n    __publicField(this, \"libraryEffects\");\n    __publicField(this, \"libraryMaterials\");\n    __publicField(this, \"canvas\");\n    __publicField(this, \"ctx\");\n    __publicField(this, \"transMat\");\n    __publicField(this, \"getFuncs\", [\"getX\", \"getY\", \"getZ\", \"getW\"]);\n    this.options = {\n      version: \"1.4.1\",\n      author: null,\n      textureDirectory: \"\",\n      upAxis: \"Y_UP\",\n      unitName: null,\n      unitMeter: null\n    };\n    this.geometryInfo = /* @__PURE__ */new WeakMap();\n    this.materialMap = /* @__PURE__ */new WeakMap();\n    this.imageMap = /* @__PURE__ */new WeakMap();\n    this.textures = [];\n    this.libraryImages = [];\n    this.libraryGeometries = [];\n    this.libraryEffects = [];\n    this.libraryMaterials = [];\n    this.canvas = null;\n    this.ctx = null;\n    this.transMat = null;\n  }\n  parse(object, onDone, options = {}) {\n    this.options = {\n      ...this.options,\n      ...options\n    };\n    if (this.options.upAxis.match(/^[XYZ]_UP$/) === null) {\n      console.error(\"ColladaExporter: Invalid upAxis: valid values are X_UP, Y_UP or Z_UP.\");\n      return null;\n    }\n    if (this.options.unitName !== null && this.options.unitMeter === null) {\n      console.error(\"ColladaExporter: unitMeter needs to be specified if unitName is specified.\");\n      return null;\n    }\n    if (this.options.unitMeter !== null && this.options.unitName === null) {\n      console.error(\"ColladaExporter: unitName needs to be specified if unitMeter is specified.\");\n      return null;\n    }\n    if (this.options.textureDirectory !== \"\") {\n      this.options.textureDirectory = `${this.options.textureDirectory}/`.replace(/\\\\/g, \"/\").replace(/\\/+/g, \"/\");\n    }\n    if (this.options.version !== \"1.4.1\" && this.options.version !== \"1.5.0\") {\n      console.warn(`ColladaExporter : Version ${this.options.version} not supported for export. Only 1.4.1 and 1.5.0.`);\n      return null;\n    }\n    const libraryVisualScenes = this.processObject(object);\n    const specLink = this.options.version === \"1.4.1\" ? \"http://www.collada.org/2005/11/COLLADASchema\" : \"https://www.khronos.org/collada/\";\n    let dae = `<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\" ?>${`<COLLADA xmlns=\"${specLink}\" version=\"${this.options.version}\">`}<asset><contributor><authoring_tool>three.js Collada Exporter</authoring_tool>${this.options.author !== null ? `<author>${this.options.author}</author>` : \"\"}</contributor>${`<created>${(/* @__PURE__ */new Date()).toISOString()}</created>`}${`<modified>${(/* @__PURE__ */new Date()).toISOString()}</modified>`}<up_axis>Y_UP</up_axis></asset>`;\n    dae += `<library_images>${this.libraryImages.join(\"\")}</library_images>`;\n    dae += `<library_effects>${this.libraryEffects.join(\"\")}</library_effects>`;\n    dae += `<library_materials>${this.libraryMaterials.join(\"\")}</library_materials>`;\n    dae += `<library_geometries>${this.libraryGeometries.join(\"\")}</library_geometries>`;\n    dae += `<library_visual_scenes><visual_scene id=\"Scene\" name=\"scene\">${libraryVisualScenes}</visual_scene></library_visual_scenes>`;\n    dae += '<scene><instance_visual_scene url=\"#Scene\"/></scene>';\n    dae += \"</COLLADA>\";\n    const res = {\n      data: this.format(dae),\n      textures: this.textures\n    };\n    if (typeof onDone === \"function\") {\n      requestAnimationFrame(() => onDone(res));\n    }\n    return res;\n  }\n  // Convert the urdf xml into a well-formatted, indented format\n  format(urdf) {\n    var _a, _b;\n    const IS_END_TAG = /^<\\//;\n    const IS_SELF_CLOSING = /(\\?>$)|(\\/>$)/;\n    const HAS_TEXT = /<[^>]+>[^<]*<\\/[^<]+>/;\n    const pad = (ch, num) => num > 0 ? ch + pad(ch, num - 1) : \"\";\n    let tagnum = 0;\n    return (_b = (_a = urdf.match(/(<[^>]+>[^<]+<\\/[^<]+>)|(<[^>]+>)/g)) == null ? void 0 : _a.map(tag => {\n      if (!HAS_TEXT.test(tag) && !IS_SELF_CLOSING.test(tag) && IS_END_TAG.test(tag)) {\n        tagnum--;\n      }\n      const res = `${pad(\"  \", tagnum)}${tag}`;\n      if (!HAS_TEXT.test(tag) && !IS_SELF_CLOSING.test(tag) && !IS_END_TAG.test(tag)) {\n        tagnum++;\n      }\n      return res;\n    }).join(\"\\n\")) != null ? _b : \"\";\n  }\n  // Convert an image into a png format for saving\n  base64ToBuffer(str) {\n    const b = atob(str);\n    const buf = new Uint8Array(b.length);\n    for (let i = 0, l = buf.length; i < l; i++) {\n      buf[i] = b.charCodeAt(i);\n    }\n    return buf;\n  }\n  imageToData(image, ext) {\n    var _a;\n    this.canvas = this.canvas || document.createElement(\"canvas\");\n    this.ctx = this.ctx || this.canvas.getContext(\"2d\");\n    this.canvas.width = image.width instanceof SVGAnimatedLength ? 0 : image.width;\n    this.canvas.height = image.height instanceof SVGAnimatedLength ? 0 : image.height;\n    (_a = this.ctx) == null ? void 0 : _a.drawImage(image, 0, 0);\n    const base64data = this.canvas.toDataURL(`image/${ext}`, 1).replace(/^data:image\\/(png|jpg);base64,/, \"\");\n    return this.base64ToBuffer(base64data);\n  }\n  // gets the attribute array. Generate a new array if the attribute is interleaved\n  attrBufferToArray(attr) {\n    if (attr instanceof InterleavedBufferAttribute && attr.isInterleavedBufferAttribute) {\n      const TypedArrayConstructor = attr.array.constructor;\n      const arr = new TypedArrayConstructor(attr.count * attr.itemSize);\n      const size = attr.itemSize;\n      for (let i = 0, l = attr.count; i < l; i++) {\n        for (let j = 0; j < size; j++) {\n          arr[i * size + j] = attr[this.getFuncs[j]](i);\n        }\n      }\n      return arr;\n    } else {\n      return attr.array;\n    }\n  }\n  // Returns an array of the same type starting at the `st` index,\n  // and `ct` length\n  subArray(arr, st, ct) {\n    if (Array.isArray(arr)) {\n      return arr.slice(st, st + ct);\n    } else {\n      const TypedArrayConstructor = arr.constructor;\n      return new TypedArrayConstructor(arr.buffer, st * arr.BYTES_PER_ELEMENT, ct);\n    }\n  }\n  // Returns the string for a geometry's attribute\n  getAttribute(attr, name, params, type) {\n    const array = this.attrBufferToArray(attr);\n    const res = Array.isArray(array) ? `${`<source id=\"${name}\"><float_array id=\"${name}-array\" count=\"${array.length}\">` + array.join(\" \")}</float_array><technique_common>${`<accessor source=\"#${name}-array\" count=\"${Math.floor(array.length / attr.itemSize)}\" stride=\"${attr.itemSize}\">`}${params.map(n => `<param name=\"${n}\" type=\"${type}\" />`).join(\"\")}</accessor></technique_common></source>` : \"\";\n    return res;\n  }\n  // Returns the string for a node's transform information\n  getTransform(o) {\n    o.updateMatrix();\n    this.transMat = this.transMat || new Matrix4();\n    this.transMat.copy(o.matrix);\n    this.transMat.transpose();\n    return `<matrix>${this.transMat.toArray().join(\" \")}</matrix>`;\n  }\n  // Process the given piece of geometry into the geometry library\n  // Returns the mesh id\n  processGeometry(g) {\n    let info = this.geometryInfo.get(g);\n    if (!info) {\n      const bufferGeometry = g;\n      if (!bufferGeometry.isBufferGeometry) {\n        throw new Error(\"THREE.ColladaExporter: Geometry is not of type THREE.BufferGeometry.\");\n      }\n      const meshid = `Mesh${this.libraryGeometries.length + 1}`;\n      const indexCount = bufferGeometry.index ? bufferGeometry.index.count * bufferGeometry.index.itemSize : bufferGeometry.attributes.position.count;\n      const groups = bufferGeometry.groups != null && bufferGeometry.groups.length !== 0 ? bufferGeometry.groups : [{\n        start: 0,\n        count: indexCount,\n        materialIndex: 0\n      }];\n      const gname = g.name ? ` name=\"${g.name}\"` : \"\";\n      let gnode = `<geometry id=\"${meshid}\"${gname}><mesh>`;\n      const posName = `${meshid}-position`;\n      const vertName = `${meshid}-vertices`;\n      gnode += this.getAttribute(bufferGeometry.attributes.position, posName, [\"X\", \"Y\", \"Z\"], \"float\");\n      gnode += `<vertices id=\"${vertName}\"><input semantic=\"POSITION\" source=\"#${posName}\" /></vertices>`;\n      let triangleInputs = `<input semantic=\"VERTEX\" source=\"#${vertName}\" offset=\"0\" />`;\n      if (\"normal\" in bufferGeometry.attributes) {\n        const normName = `${meshid}-normal`;\n        gnode += this.getAttribute(bufferGeometry.attributes.normal, normName, [\"X\", \"Y\", \"Z\"], \"float\");\n        triangleInputs += `<input semantic=\"NORMAL\" source=\"#${normName}\" offset=\"0\" />`;\n      }\n      if (\"uv\" in bufferGeometry.attributes) {\n        const uvName = `${meshid}-texcoord`;\n        gnode += this.getAttribute(bufferGeometry.attributes.uv, uvName, [\"S\", \"T\"], \"float\");\n        triangleInputs += `<input semantic=\"TEXCOORD\" source=\"#${uvName}\" offset=\"0\" set=\"0\" />`;\n      }\n      if (UV1 in bufferGeometry.attributes) {\n        const uvName = `${meshid}-texcoord2`;\n        gnode += this.getAttribute(bufferGeometry.attributes[UV1], uvName, [\"S\", \"T\"], \"float\");\n        triangleInputs += `<input semantic=\"TEXCOORD\" source=\"#${uvName}\" offset=\"0\" set=\"1\" />`;\n      }\n      if (\"color\" in bufferGeometry.attributes) {\n        const colName = `${meshid}-color`;\n        gnode += this.getAttribute(bufferGeometry.attributes.color, colName, [\"X\", \"Y\", \"Z\"], \"uint8\");\n        triangleInputs += `<input semantic=\"COLOR\" source=\"#${colName}\" offset=\"0\" />`;\n      }\n      let indexArray = null;\n      if (bufferGeometry.index) {\n        indexArray = this.attrBufferToArray(bufferGeometry.index);\n      } else {\n        indexArray = new Array(indexCount);\n        for (let i = 0, l = indexArray.length; i < l && Array.isArray(indexArray); i++) indexArray[i] = i;\n      }\n      for (let i = 0, l = groups.length; i < l; i++) {\n        const group = groups[i];\n        const subarr = this.subArray(indexArray, group.start, group.count);\n        const polycount = subarr.length / 3;\n        gnode += `<triangles material=\"MESH_MATERIAL_${group.materialIndex}\" count=\"${polycount}\">`;\n        gnode += triangleInputs;\n        gnode += `<p>${subarr.join(\" \")}</p>`;\n        gnode += \"</triangles>\";\n      }\n      gnode += \"</mesh></geometry>\";\n      this.libraryGeometries.push(gnode);\n      info = {\n        meshid,\n        bufferGeometry\n      };\n      this.geometryInfo.set(g, info);\n    }\n    return info;\n  }\n  // Process the given texture into the image library\n  // Returns the image library\n  processTexture(tex) {\n    let texid = this.imageMap.get(tex);\n    if (texid == null) {\n      texid = `image-${this.libraryImages.length + 1}`;\n      const ext = \"png\";\n      const name = tex.name || texid;\n      let imageNode = `<image id=\"${texid}\" name=\"${name}\">`;\n      if (this.options.version === \"1.5.0\") {\n        imageNode += `<init_from><ref>${this.options.textureDirectory}${name}.${ext}</ref></init_from>`;\n      } else {\n        imageNode += `<init_from>${this.options.textureDirectory}${name}.${ext}</init_from>`;\n      }\n      imageNode += \"</image>\";\n      this.libraryImages.push(imageNode);\n      this.imageMap.set(tex, texid);\n      this.textures.push({\n        directory: this.options.textureDirectory,\n        name,\n        ext,\n        data: this.imageToData(tex.image, ext),\n        original: tex\n      });\n    }\n    return texid;\n  }\n  // Process the given material into the material and effect libraries\n  // Returns the material id\n  processMaterial(m) {\n    let matid = this.materialMap.get(m);\n    if (matid == null) {\n      matid = `Mat${this.libraryEffects.length + 1}`;\n      let type = \"phong\";\n      if (m instanceof MeshLambertMaterial) {\n        type = \"lambert\";\n      } else if (m instanceof MeshBasicMaterial) {\n        type = \"constant\";\n        if (m.map !== null) {\n          console.warn(\"ColladaExporter: Texture maps not supported with MeshBasicMaterial.\");\n        }\n      }\n      if (m instanceof MeshPhongMaterial) {\n        const emissive = m.emissive ? m.emissive : new Color(0, 0, 0);\n        const diffuse = m.color ? m.color : new Color(0, 0, 0);\n        const specular = m.specular ? m.specular : new Color(1, 1, 1);\n        const shininess = m.shininess || 0;\n        const reflectivity = m.reflectivity || 0;\n        let transparencyNode = \"\";\n        if (m.transparent) {\n          transparencyNode += `<transparent>${m.map ? '<texture texture=\"diffuse-sampler\"></texture>' : \"<float>1</float>\"}</transparent>`;\n          if (m.opacity < 1) {\n            transparencyNode += `<transparency><float>${m.opacity}</float></transparency>`;\n          }\n        }\n        const techniqueNode = `${`<technique sid=\"common\"><${type}>`}<emission>${m.emissiveMap ? '<texture texture=\"emissive-sampler\" texcoord=\"TEXCOORD\" />' : `<color sid=\"emission\">${emissive.r} ${emissive.g} ${emissive.b} 1</color>`}</emission>${type !== \"constant\" ? `<diffuse>${m.map ? '<texture texture=\"diffuse-sampler\" texcoord=\"TEXCOORD\" />' : `<color sid=\"diffuse\">${diffuse.r} ${diffuse.g} ${diffuse.b} 1</color>`}</diffuse>` : \"\"}${type !== \"constant\" ? `<bump>${m.normalMap ? '<texture texture=\"bump-sampler\" texcoord=\"TEXCOORD\" />' : \"\"}</bump>` : \"\"}${type === \"phong\" ? `${`<specular><color sid=\"specular\">${specular.r} ${specular.g} ${specular.b} 1</color></specular>`}<shininess>${m.specularMap ? '<texture texture=\"specular-sampler\" texcoord=\"TEXCOORD\" />' : `<float sid=\"shininess\">${shininess}</float>`}</shininess>` : \"\"}${`<reflective><color>${diffuse.r} ${diffuse.g} ${diffuse.b} 1</color></reflective>`}${`<reflectivity><float>${reflectivity}</float></reflectivity>`}${transparencyNode}${`</${type}></technique>`}`;\n        const effectnode = `${`<effect id=\"${matid}-effect\">`}<profile_COMMON>${m.map ? `<newparam sid=\"diffuse-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(m.map)}</init_from>`}</surface></newparam><newparam sid=\"diffuse-sampler\"><sampler2D><source>diffuse-surface</source></sampler2D></newparam>` : \"\"}${m.specularMap ? `<newparam sid=\"specular-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(m.specularMap)}</init_from>`}</surface></newparam><newparam sid=\"specular-sampler\"><sampler2D><source>specular-surface</source></sampler2D></newparam>` : \"\"}${m.emissiveMap ? `<newparam sid=\"emissive-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(m.emissiveMap)}</init_from>`}</surface></newparam><newparam sid=\"emissive-sampler\"><sampler2D><source>emissive-surface</source></sampler2D></newparam>` : \"\"}${m.normalMap ? `<newparam sid=\"bump-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(m.normalMap)}</init_from>`}</surface></newparam><newparam sid=\"bump-sampler\"><sampler2D><source>bump-surface</source></sampler2D></newparam>` : \"\"}${techniqueNode}${m.side === DoubleSide ? '<extra><technique profile=\"THREEJS\"><double_sided sid=\"double_sided\" type=\"int\">1</double_sided></technique></extra>' : \"\"}</profile_COMMON></effect>`;\n        const materialName = m.name ? ` name=\"${m.name}\"` : \"\";\n        const materialNode = `<material id=\"${matid}\"${materialName}><instance_effect url=\"#${matid}-effect\" /></material>`;\n        this.libraryMaterials.push(materialNode);\n        this.libraryEffects.push(effectnode);\n        this.materialMap.set(m, matid);\n      }\n    }\n    return matid;\n  }\n  // Recursively process the object into a scene\n  processObject(o) {\n    let node = `<node name=\"${o.name}\">`;\n    node += this.getTransform(o);\n    const a = new Mesh();\n    a.geometry;\n    if (o instanceof Mesh && o.isMesh && o.geometry !== null) {\n      const geomInfo = this.processGeometry(o.geometry);\n      const meshid = geomInfo.meshid;\n      const geometry = geomInfo.bufferGeometry;\n      let matids = null;\n      let matidsArray;\n      const mat = o.material || new MeshBasicMaterial();\n      const materials = Array.isArray(mat) ? mat : [mat];\n      if (geometry.groups.length > materials.length) {\n        matidsArray = new Array(geometry.groups.length);\n      } else {\n        matidsArray = new Array(materials.length);\n      }\n      matids = matidsArray.fill(null).map((_, i) => this.processMaterial(materials[i % materials.length]));\n      node += `${`<instance_geometry url=\"#${meshid}\">` + (matids != null ? `<bind_material><technique_common>${matids.map((id, i) => `${`<instance_material symbol=\"MESH_MATERIAL_${i}\" target=\"#${id}\" >`}<bind_vertex_input semantic=\"TEXCOORD\" input_semantic=\"TEXCOORD\" input_set=\"0\" /></instance_material>`).join(\"\")}</technique_common></bind_material>` : \"\")}</instance_geometry>`;\n    }\n    o.children.forEach(c => node += this.processObject(c));\n    node += \"</node>\";\n    return node;\n  }\n}\nexport { ColladaExporter };", "map": {"version": 3, "names": ["ColladaExporter", "constructor", "__publicField", "options", "version", "author", "textureDirectory", "upAxis", "unitName", "unitMeter", "geometryInfo", "WeakMap", "materialMap", "imageMap", "textures", "libraryImages", "libraryGeometries", "libraryEffects", "libraryMaterials", "canvas", "ctx", "transMat", "parse", "object", "onDone", "match", "console", "error", "replace", "warn", "libraryVisualScenes", "processObject", "specLink", "dae", "Date", "toISOString", "join", "res", "data", "format", "requestAnimationFrame", "urdf", "IS_END_TAG", "IS_SELF_CLOSING", "HAS_TEXT", "pad", "ch", "num", "tagnum", "_b", "_a", "map", "tag", "test", "base64ToBuffer", "str", "b", "atob", "buf", "Uint8Array", "length", "i", "l", "charCodeAt", "imageToData", "image", "ext", "document", "createElement", "getContext", "width", "SVGAnimatedLength", "height", "drawImage", "base64data", "toDataURL", "attrBufferToArray", "attr", "InterleavedBufferAttribute", "isInterleavedBufferAttribute", "TypedArrayConstructor", "array", "arr", "count", "itemSize", "size", "j", "getFuncs", "subArray", "st", "ct", "Array", "isArray", "slice", "buffer", "BYTES_PER_ELEMENT", "getAttribute", "name", "params", "type", "Math", "floor", "n", "getTransform", "o", "updateMatrix", "Matrix4", "copy", "matrix", "transpose", "toArray", "processGeometry", "g", "info", "get", "bufferGeometry", "isBufferGeometry", "Error", "meshid", "indexCount", "index", "attributes", "position", "groups", "start", "materialIndex", "gname", "gnode", "posName", "vertName", "triangleInputs", "<PERSON><PERSON><PERSON>", "normal", "uvName", "uv", "UV1", "colName", "color", "indexArray", "group", "subarr", "polycount", "push", "set", "processTexture", "tex", "texid", "imageNode", "directory", "original", "processMaterial", "m", "matid", "MeshLambertMaterial", "MeshBasicMaterial", "MeshPhongMaterial", "emissive", "Color", "diffuse", "specular", "shininess", "reflectivity", "transparencyNode", "transparent", "opacity", "techniqueNode", "emissiveMap", "r", "normalMap", "specularMap", "effectnode", "side", "DoubleSide", "materialName", "materialNode", "node", "a", "<PERSON><PERSON>", "geometry", "<PERSON><PERSON><PERSON>", "geomInfo", "matids", "matidsArray", "mat", "material", "materials", "fill", "_", "id", "children", "for<PERSON>ach", "c"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\exporters\\ColladaExporter.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Color,\n  DoubleSide,\n  InterleavedBufferAttribute,\n  Material,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  MeshLambertMaterial,\n  MeshPhongMaterial,\n  Object3D,\n  Texture,\n} from 'three'\nimport type { TypedArray, TypedArrayConstructors } from '../types/shared'\nimport { UV1 } from '../_polyfill/uv1'\n\n/**\n * https://github.com/gkjohnson/collada-exporter-js\n *\n * Usage:\n *  const exporter = new ColladaExporter();\n *\n *  const data = exporter.parse(mesh);\n *\n * Format Definition:\n *  https://www.khronos.org/collada/\n */\n\nexport interface ColladaExporterOptions {\n  author?: string\n  textureDirectory?: string\n  version?: string\n}\n\nexport interface ColladaExporterResult {\n  data: string\n  textures: object[]\n}\n\ntype GeometryInfo = { meshid: string; bufferGeometry: BufferGeometry }\n\ntype MaterialRepresentation = MeshPhongMaterial | MeshBasicMaterial | MeshLambertMaterial\n\nclass ColladaExporter {\n  private options: {\n    version: string\n    author: string | null\n    textureDirectory: string\n    upAxis: string\n    unitName: string | null\n    unitMeter: string | null\n  }\n\n  private geometryInfo: WeakMap<BufferGeometry, GeometryInfo>\n  private materialMap: WeakMap<MaterialRepresentation, string>\n  private imageMap: WeakMap<Texture, string>\n  private textures: {\n    directory: string\n    name: string\n    ext: string\n    data: Uint8Array\n    original: Texture\n  }[]\n\n  private libraryImages: string[]\n  private libraryGeometries: string[]\n  private libraryEffects: string[]\n  private libraryMaterials: string[]\n\n  private canvas: HTMLCanvasElement | null\n  private ctx: CanvasRenderingContext2D | null\n\n  private transMat: Matrix4 | null\n\n  private getFuncs = ['getX', 'getY', 'getZ', 'getW'] as const\n\n  constructor() {\n    this.options = {\n      version: '1.4.1',\n      author: null,\n      textureDirectory: '',\n      upAxis: 'Y_UP',\n      unitName: null,\n      unitMeter: null,\n    }\n\n    this.geometryInfo = new WeakMap()\n    this.materialMap = new WeakMap()\n    this.imageMap = new WeakMap()\n    this.textures = []\n\n    this.libraryImages = []\n    this.libraryGeometries = []\n    this.libraryEffects = []\n    this.libraryMaterials = []\n\n    this.canvas = null\n    this.ctx = null\n\n    this.transMat = null\n  }\n\n  public parse(\n    object: Object3D,\n    onDone: (res: ColladaExporterResult) => void,\n    options: ColladaExporterOptions = {},\n  ): ColladaExporterResult | null {\n    this.options = { ...this.options, ...options }\n\n    if (this.options.upAxis.match(/^[XYZ]_UP$/) === null) {\n      console.error('ColladaExporter: Invalid upAxis: valid values are X_UP, Y_UP or Z_UP.')\n      return null\n    }\n\n    if (this.options.unitName !== null && this.options.unitMeter === null) {\n      console.error('ColladaExporter: unitMeter needs to be specified if unitName is specified.')\n      return null\n    }\n\n    if (this.options.unitMeter !== null && this.options.unitName === null) {\n      console.error('ColladaExporter: unitName needs to be specified if unitMeter is specified.')\n      return null\n    }\n\n    if (this.options.textureDirectory !== '') {\n      this.options.textureDirectory = `${this.options.textureDirectory}/`.replace(/\\\\/g, '/').replace(/\\/+/g, '/')\n    }\n\n    if (this.options.version !== '1.4.1' && this.options.version !== '1.5.0') {\n      console.warn(`ColladaExporter : Version ${this.options.version} not supported for export. Only 1.4.1 and 1.5.0.`)\n      return null\n    }\n\n    const libraryVisualScenes = this.processObject(object)\n\n    const specLink =\n      this.options.version === '1.4.1'\n        ? 'http://www.collada.org/2005/11/COLLADASchema'\n        : 'https://www.khronos.org/collada/'\n    let dae = `<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\" ?>${`<COLLADA xmlns=\"${specLink}\" version=\"${this.options.version}\">`}<asset><contributor><authoring_tool>three.js Collada Exporter</authoring_tool>${\n      this.options.author !== null ? `<author>${this.options.author}</author>` : ''\n    }</contributor>${`<created>${new Date().toISOString()}</created>`}${`<modified>${new Date().toISOString()}</modified>`}<up_axis>Y_UP</up_axis></asset>`\n\n    dae += `<library_images>${this.libraryImages.join('')}</library_images>`\n\n    dae += `<library_effects>${this.libraryEffects.join('')}</library_effects>`\n\n    dae += `<library_materials>${this.libraryMaterials.join('')}</library_materials>`\n\n    dae += `<library_geometries>${this.libraryGeometries.join('')}</library_geometries>`\n\n    dae += `<library_visual_scenes><visual_scene id=\"Scene\" name=\"scene\">${libraryVisualScenes}</visual_scene></library_visual_scenes>`\n\n    dae += '<scene><instance_visual_scene url=\"#Scene\"/></scene>'\n\n    dae += '</COLLADA>'\n\n    const res = {\n      data: this.format(dae),\n      textures: this.textures,\n    }\n\n    if (typeof onDone === 'function') {\n      requestAnimationFrame(() => onDone(res))\n    }\n\n    return res\n  }\n\n  // Convert the urdf xml into a well-formatted, indented format\n  private format(urdf: string): string {\n    const IS_END_TAG = /^<\\//\n    const IS_SELF_CLOSING = /(\\?>$)|(\\/>$)/\n    const HAS_TEXT = /<[^>]+>[^<]*<\\/[^<]+>/\n\n    const pad = (ch: string, num: number): string => (num > 0 ? ch + pad(ch, num - 1) : '')\n\n    let tagnum = 0\n\n    return (\n      urdf\n        .match(/(<[^>]+>[^<]+<\\/[^<]+>)|(<[^>]+>)/g)\n        ?.map((tag) => {\n          if (!HAS_TEXT.test(tag) && !IS_SELF_CLOSING.test(tag) && IS_END_TAG.test(tag)) {\n            tagnum--\n          }\n\n          const res = `${pad('  ', tagnum)}${tag}`\n\n          if (!HAS_TEXT.test(tag) && !IS_SELF_CLOSING.test(tag) && !IS_END_TAG.test(tag)) {\n            tagnum++\n          }\n\n          return res\n        })\n        .join('\\n') ?? ''\n    )\n  }\n\n  // Convert an image into a png format for saving\n  private base64ToBuffer(str: string): Uint8Array {\n    const b = atob(str)\n    const buf = new Uint8Array(b.length)\n\n    for (let i = 0, l = buf.length; i < l; i++) {\n      buf[i] = b.charCodeAt(i)\n    }\n\n    return buf\n  }\n\n  private imageToData(image: CanvasImageSource, ext: string): Uint8Array {\n    this.canvas = this.canvas || document.createElement('canvas')\n    this.ctx = this.ctx || this.canvas.getContext('2d')\n\n    this.canvas.width = image.width instanceof SVGAnimatedLength ? 0 : image.width\n    this.canvas.height = image.height instanceof SVGAnimatedLength ? 0 : image.height\n\n    this.ctx?.drawImage(image, 0, 0)\n\n    // Get the base64 encoded data\n    const base64data = this.canvas.toDataURL(`image/${ext}`, 1).replace(/^data:image\\/(png|jpg);base64,/, '')\n\n    // Convert to a uint8 array\n    return this.base64ToBuffer(base64data)\n  }\n\n  // gets the attribute array. Generate a new array if the attribute is interleaved\n  private attrBufferToArray(attr: InterleavedBufferAttribute | BufferAttribute): number[] | ArrayLike<number> {\n    if (attr instanceof InterleavedBufferAttribute && attr.isInterleavedBufferAttribute) {\n      // use the typed array constructor to save on memory\n      const TypedArrayConstructor: TypedArrayConstructors = attr.array.constructor\n      // @ts-ignore\n      const arr: number[] = new TypedArrayConstructor(attr.count * attr.itemSize)\n      const size = attr.itemSize\n\n      for (let i = 0, l = attr.count; i < l; i++) {\n        for (let j = 0; j < size; j++) {\n          arr[i * size + j] = attr[this.getFuncs[j]](i)\n        }\n      }\n\n      return arr\n    } else {\n      return attr.array\n    }\n  }\n\n  // Returns an array of the same type starting at the `st` index,\n  // and `ct` length\n  private subArray(arr: number[] | ArrayLike<number>, st: number, ct: number): TypedArray | number[] {\n    if (Array.isArray(arr)) {\n      return arr.slice(st, st + ct)\n    } else {\n      const TypedArrayConstructor: TypedArrayConstructors = arr.constructor\n      // @ts-ignore\n      return new TypedArrayConstructor(arr.buffer, st * arr.BYTES_PER_ELEMENT, ct)\n    }\n  }\n\n  // Returns the string for a geometry's attribute\n  private getAttribute(\n    attr: InterleavedBufferAttribute | BufferAttribute,\n    name: string,\n    params: string[],\n    type: string,\n  ): string {\n    const array = this.attrBufferToArray(attr)\n    const res = Array.isArray(array)\n      ? `${\n          `<source id=\"${name}\">` + `<float_array id=\"${name}-array\" count=\"${array.length}\">` + array.join(' ')\n        }</float_array><technique_common>${`<accessor source=\"#${name}-array\" count=\"${Math.floor(\n          array.length / attr.itemSize,\n        )}\" stride=\"${attr.itemSize}\">`}${params\n          .map((n) => `<param name=\"${n}\" type=\"${type}\" />`)\n          .join('')}</accessor></technique_common></source>`\n      : ''\n\n    return res\n  }\n\n  // Returns the string for a node's transform information\n  private getTransform(o: Object3D): string {\n    // ensure the object's matrix is up to date\n    // before saving the transform\n    o.updateMatrix()\n\n    this.transMat = this.transMat || new Matrix4()\n    this.transMat.copy(o.matrix)\n    this.transMat.transpose()\n    return `<matrix>${this.transMat.toArray().join(' ')}</matrix>`\n  }\n\n  // Process the given piece of geometry into the geometry library\n  // Returns the mesh id\n  private processGeometry(g: BufferGeometry): GeometryInfo {\n    let info = this.geometryInfo.get(g)\n\n    if (!info) {\n      // convert the geometry to bufferGeometry if it isn't already\n      const bufferGeometry = g\n\n      if (!bufferGeometry.isBufferGeometry) {\n        throw new Error('THREE.ColladaExporter: Geometry is not of type THREE.BufferGeometry.')\n      }\n\n      const meshid = `Mesh${this.libraryGeometries.length + 1}`\n\n      const indexCount = bufferGeometry.index\n        ? bufferGeometry.index.count * bufferGeometry.index.itemSize\n        : bufferGeometry.attributes.position.count\n\n      const groups =\n        bufferGeometry.groups != null && bufferGeometry.groups.length !== 0\n          ? bufferGeometry.groups\n          : [{ start: 0, count: indexCount, materialIndex: 0 }]\n\n      const gname = g.name ? ` name=\"${g.name}\"` : ''\n      let gnode = `<geometry id=\"${meshid}\"${gname}><mesh>`\n\n      // define the geometry node and the vertices for the geometry\n      const posName = `${meshid}-position`\n      const vertName = `${meshid}-vertices`\n      gnode += this.getAttribute(bufferGeometry.attributes.position, posName, ['X', 'Y', 'Z'], 'float')\n      gnode += `<vertices id=\"${vertName}\"><input semantic=\"POSITION\" source=\"#${posName}\" /></vertices>`\n\n      // NOTE: We're not optimizing the attribute arrays here, so they're all the same length and\n      // can therefore share the same triangle indices. However, MeshLab seems to have trouble opening\n      // models with attributes that share an offset.\n      // MeshLab Bug#424: https://sourceforge.net/p/meshlab/bugs/424/\n\n      // serialize normals\n      let triangleInputs = `<input semantic=\"VERTEX\" source=\"#${vertName}\" offset=\"0\" />`\n      if ('normal' in bufferGeometry.attributes) {\n        const normName = `${meshid}-normal`\n        gnode += this.getAttribute(bufferGeometry.attributes.normal, normName, ['X', 'Y', 'Z'], 'float')\n        triangleInputs += `<input semantic=\"NORMAL\" source=\"#${normName}\" offset=\"0\" />`\n      }\n\n      // serialize uvs\n      if ('uv' in bufferGeometry.attributes) {\n        const uvName = `${meshid}-texcoord`\n        gnode += this.getAttribute(bufferGeometry.attributes.uv, uvName, ['S', 'T'], 'float')\n        triangleInputs += `<input semantic=\"TEXCOORD\" source=\"#${uvName}\" offset=\"0\" set=\"0\" />`\n      }\n\n      // serialize lightmap uvs\n      if (UV1 in bufferGeometry.attributes) {\n        const uvName = `${meshid}-texcoord2`\n        gnode += this.getAttribute(bufferGeometry.attributes[UV1], uvName, ['S', 'T'], 'float')\n        triangleInputs += `<input semantic=\"TEXCOORD\" source=\"#${uvName}\" offset=\"0\" set=\"1\" />`\n      }\n\n      // serialize colors\n      if ('color' in bufferGeometry.attributes) {\n        const colName = `${meshid}-color`\n        gnode += this.getAttribute(bufferGeometry.attributes.color, colName, ['X', 'Y', 'Z'], 'uint8')\n        triangleInputs += `<input semantic=\"COLOR\" source=\"#${colName}\" offset=\"0\" />`\n      }\n\n      let indexArray: number[] | ArrayLike<number> | null = null\n      if (bufferGeometry.index) {\n        indexArray = this.attrBufferToArray(bufferGeometry.index)\n      } else {\n        indexArray = new Array(indexCount)\n        for (let i = 0, l = indexArray.length; i < l && Array.isArray(indexArray); i++) indexArray[i] = i\n      }\n\n      for (let i = 0, l = groups.length; i < l; i++) {\n        const group = groups[i]\n        const subarr = this.subArray(indexArray, group.start, group.count)\n        const polycount = subarr.length / 3\n        gnode += `<triangles material=\"MESH_MATERIAL_${group.materialIndex}\" count=\"${polycount}\">`\n        gnode += triangleInputs\n\n        gnode += `<p>${subarr.join(' ')}</p>`\n        gnode += '</triangles>'\n      }\n\n      gnode += '</mesh></geometry>'\n\n      this.libraryGeometries.push(gnode)\n\n      info = { meshid, bufferGeometry }\n      this.geometryInfo.set(g, info)\n    }\n\n    return info\n  }\n\n  // Process the given texture into the image library\n  // Returns the image library\n  private processTexture(tex: Texture): string {\n    let texid = this.imageMap.get(tex)\n    if (texid == null) {\n      texid = `image-${this.libraryImages.length + 1}`\n\n      const ext = 'png'\n      const name = tex.name || texid\n      let imageNode = `<image id=\"${texid}\" name=\"${name}\">`\n\n      if (this.options.version === '1.5.0') {\n        imageNode += `<init_from><ref>${this.options.textureDirectory}${name}.${ext}</ref></init_from>`\n      } else {\n        // version image node 1.4.1\n        imageNode += `<init_from>${this.options.textureDirectory}${name}.${ext}</init_from>`\n      }\n\n      imageNode += '</image>'\n\n      this.libraryImages.push(imageNode)\n      this.imageMap.set(tex, texid)\n      this.textures.push({\n        directory: this.options.textureDirectory,\n        name,\n        ext,\n        data: this.imageToData(tex.image, ext),\n        original: tex,\n      })\n    }\n\n    return texid\n  }\n\n  // Process the given material into the material and effect libraries\n  // Returns the material id\n  private processMaterial(m: MaterialRepresentation): string {\n    let matid = this.materialMap.get(m)\n\n    if (matid == null) {\n      matid = `Mat${this.libraryEffects.length + 1}`\n\n      let type = 'phong'\n\n      if (m instanceof MeshLambertMaterial) {\n        type = 'lambert'\n      } else if (m instanceof MeshBasicMaterial) {\n        type = 'constant'\n\n        if (m.map !== null) {\n          // The Collada spec does not support diffuse texture maps with the\n          // constant shader type.\n          // mrdoob/three.js#15469\n          console.warn('ColladaExporter: Texture maps not supported with MeshBasicMaterial.')\n        }\n      }\n\n      if (m instanceof MeshPhongMaterial) {\n        const emissive = m.emissive ? m.emissive : new Color(0, 0, 0)\n        const diffuse = m.color ? m.color : new Color(0, 0, 0)\n        const specular = m.specular ? m.specular : new Color(1, 1, 1)\n        const shininess = m.shininess || 0\n        const reflectivity = m.reflectivity || 0\n\n        // Do not export and alpha map for the reasons mentioned in issue (#13792)\n        // in three.js alpha maps are black and white, but collada expects the alpha\n        // channel to specify the transparency\n        let transparencyNode = ''\n        if (m.transparent) {\n          transparencyNode += `<transparent>${\n            m.map ? '<texture texture=\"diffuse-sampler\"></texture>' : '<float>1</float>'\n          }</transparent>`\n\n          if (m.opacity < 1) {\n            transparencyNode += `<transparency><float>${m.opacity}</float></transparency>`\n          }\n        }\n\n        const techniqueNode = `${`<technique sid=\"common\"><${type}>`}<emission>${\n          m.emissiveMap\n            ? '<texture texture=\"emissive-sampler\" texcoord=\"TEXCOORD\" />'\n            : `<color sid=\"emission\">${emissive.r} ${emissive.g} ${emissive.b} 1</color>`\n        }</emission>${\n          type !== 'constant'\n            ? `<diffuse>${\n                m.map\n                  ? '<texture texture=\"diffuse-sampler\" texcoord=\"TEXCOORD\" />'\n                  : `<color sid=\"diffuse\">${diffuse.r} ${diffuse.g} ${diffuse.b} 1</color>`\n              }</diffuse>`\n            : ''\n        }${\n          type !== 'constant'\n            ? `<bump>${m.normalMap ? '<texture texture=\"bump-sampler\" texcoord=\"TEXCOORD\" />' : ''}</bump>`\n            : ''\n        }${\n          type === 'phong'\n            ? `${`<specular><color sid=\"specular\">${specular.r} ${specular.g} ${specular.b} 1</color></specular>`}<shininess>${\n                m.specularMap\n                  ? '<texture texture=\"specular-sampler\" texcoord=\"TEXCOORD\" />'\n                  : `<float sid=\"shininess\">${shininess}</float>`\n              }</shininess>`\n            : ''\n        }${`<reflective><color>${diffuse.r} ${diffuse.g} ${diffuse.b} 1</color></reflective>`}${`<reflectivity><float>${reflectivity}</float></reflectivity>`}${transparencyNode}${`</${type}></technique>`}`\n\n        const effectnode = `${`<effect id=\"${matid}-effect\">`}<profile_COMMON>${\n          m.map\n            ? `<newparam sid=\"diffuse-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.map,\n              )}</init_from>`}</surface></newparam><newparam sid=\"diffuse-sampler\"><sampler2D><source>diffuse-surface</source></sampler2D></newparam>`\n            : ''\n        }${\n          m.specularMap\n            ? `<newparam sid=\"specular-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.specularMap,\n              )}</init_from>`}</surface></newparam><newparam sid=\"specular-sampler\"><sampler2D><source>specular-surface</source></sampler2D></newparam>`\n            : ''\n        }${\n          m.emissiveMap\n            ? `<newparam sid=\"emissive-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.emissiveMap,\n              )}</init_from>`}</surface></newparam><newparam sid=\"emissive-sampler\"><sampler2D><source>emissive-surface</source></sampler2D></newparam>`\n            : ''\n        }${\n          m.normalMap\n            ? `<newparam sid=\"bump-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.normalMap,\n              )}</init_from>`}</surface></newparam><newparam sid=\"bump-sampler\"><sampler2D><source>bump-surface</source></sampler2D></newparam>`\n            : ''\n        }${techniqueNode}${\n          m.side === DoubleSide\n            ? '<extra><technique profile=\"THREEJS\"><double_sided sid=\"double_sided\" type=\"int\">1</double_sided></technique></extra>'\n            : ''\n        }</profile_COMMON></effect>`\n\n        const materialName = m.name ? ` name=\"${m.name}\"` : ''\n        const materialNode = `<material id=\"${matid}\"${materialName}><instance_effect url=\"#${matid}-effect\" /></material>`\n\n        this.libraryMaterials.push(materialNode)\n        this.libraryEffects.push(effectnode)\n        this.materialMap.set(m, matid)\n      }\n    }\n\n    return matid\n  }\n\n  // Recursively process the object into a scene\n  private processObject(o: Object3D): string {\n    let node = `<node name=\"${o.name}\">`\n\n    node += this.getTransform(o)\n    const a: Mesh<BufferGeometry, Material | Material[]> = new Mesh()\n    a.geometry\n\n    if (o instanceof Mesh && o.isMesh && o.geometry !== null) {\n      // function returns the id associated with the mesh and a \"BufferGeometry\" version\n      // of the geometry in case it's not a geometry.\n      const geomInfo = this.processGeometry(o.geometry)\n      const meshid = geomInfo.meshid\n      const geometry = geomInfo.bufferGeometry\n\n      // ids of the materials to bind to the geometry\n      let matids = null\n      let matidsArray\n\n      // get a list of materials to bind to the sub groups of the geometry.\n      // If the amount of subgroups is greater than the materials, than reuse\n      // the materials.\n      const mat: MaterialRepresentation | MaterialRepresentation[] = o.material || new MeshBasicMaterial()\n      const materials = Array.isArray(mat) ? mat : [mat]\n\n      if (geometry.groups.length > materials.length) {\n        matidsArray = new Array(geometry.groups.length)\n      } else {\n        matidsArray = new Array(materials.length)\n      }\n\n      matids = matidsArray.fill(null).map((_, i) => this.processMaterial(materials[i % materials.length]))\n\n      node += `${\n        `<instance_geometry url=\"#${meshid}\">` +\n        (matids != null\n          ? `<bind_material><technique_common>${matids\n              .map(\n                (id, i) =>\n                  `${`<instance_material symbol=\"MESH_MATERIAL_${i}\" target=\"#${id}\" >`}<bind_vertex_input semantic=\"TEXCOORD\" input_semantic=\"TEXCOORD\" input_set=\"0\" /></instance_material>`,\n              )\n              .join('')}</technique_common></bind_material>`\n          : '')\n      }</instance_geometry>`\n    }\n\n    o.children.forEach((c) => (node += this.processObject(c)))\n\n    node += '</node>'\n\n    return node\n  }\n}\n\nexport { ColladaExporter }\n"], "mappings": ";;;;;;;;;;;;;AA6CA,MAAMA,eAAA,CAAgB;EAiCpBC,YAAA,EAAc;IAhCNC,aAAA;IASAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAQAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAEAA,aAAA,mBAAW,CAAC,QAAQ,QAAQ,QAAQ,MAAM;IAGhD,KAAKC,OAAA,GAAU;MACbC,OAAA,EAAS;MACTC,MAAA,EAAQ;MACRC,gBAAA,EAAkB;MAClBC,MAAA,EAAQ;MACRC,QAAA,EAAU;MACVC,SAAA,EAAW;IAAA;IAGR,KAAAC,YAAA,sBAAmBC,OAAA;IACnB,KAAAC,WAAA,sBAAkBD,OAAA;IAClB,KAAAE,QAAA,sBAAeF,OAAA;IACpB,KAAKG,QAAA,GAAW;IAEhB,KAAKC,aAAA,GAAgB;IACrB,KAAKC,iBAAA,GAAoB;IACzB,KAAKC,cAAA,GAAiB;IACtB,KAAKC,gBAAA,GAAmB;IAExB,KAAKC,MAAA,GAAS;IACd,KAAKC,GAAA,GAAM;IAEX,KAAKC,QAAA,GAAW;EAClB;EAEOC,MACLC,MAAA,EACAC,MAAA,EACArB,OAAA,GAAkC,IACJ;IAC9B,KAAKA,OAAA,GAAU;MAAE,GAAG,KAAKA,OAAA;MAAS,GAAGA;IAAA;IAErC,IAAI,KAAKA,OAAA,CAAQI,MAAA,CAAOkB,KAAA,CAAM,YAAY,MAAM,MAAM;MACpDC,OAAA,CAAQC,KAAA,CAAM,uEAAuE;MAC9E;IACT;IAEA,IAAI,KAAKxB,OAAA,CAAQK,QAAA,KAAa,QAAQ,KAAKL,OAAA,CAAQM,SAAA,KAAc,MAAM;MACrEiB,OAAA,CAAQC,KAAA,CAAM,4EAA4E;MACnF;IACT;IAEA,IAAI,KAAKxB,OAAA,CAAQM,SAAA,KAAc,QAAQ,KAAKN,OAAA,CAAQK,QAAA,KAAa,MAAM;MACrEkB,OAAA,CAAQC,KAAA,CAAM,4EAA4E;MACnF;IACT;IAEI,SAAKxB,OAAA,CAAQG,gBAAA,KAAqB,IAAI;MACxC,KAAKH,OAAA,CAAQG,gBAAA,GAAmB,GAAG,KAAKH,OAAA,CAAQG,gBAAA,IAAoBsB,OAAA,CAAQ,OAAO,GAAG,EAAEA,OAAA,CAAQ,QAAQ,GAAG;IAC7G;IAEA,IAAI,KAAKzB,OAAA,CAAQC,OAAA,KAAY,WAAW,KAAKD,OAAA,CAAQC,OAAA,KAAY,SAAS;MACxEsB,OAAA,CAAQG,IAAA,CAAK,6BAA6B,KAAK1B,OAAA,CAAQC,OAAA,kDAAyD;MACzG;IACT;IAEM,MAAA0B,mBAAA,GAAsB,KAAKC,aAAA,CAAcR,MAAM;IAErD,MAAMS,QAAA,GACJ,KAAK7B,OAAA,CAAQC,OAAA,KAAY,UACrB,iDACA;IACF,IAAA6B,GAAA,GAAM,0DAA0D,mBAAmBD,QAAA,cAAsB,KAAK7B,OAAA,CAAQC,OAAA,qFACxH,KAAKD,OAAA,CAAQE,MAAA,KAAW,OAAO,WAAW,KAAKF,OAAA,CAAQE,MAAA,cAAoB,mBAC5D,aAAY,mBAAI6B,IAAA,IAAOC,WAAA,iBAA4B,cAAa,mBAAID,IAAA,CAAK,GAAEC,WAAA;IAE5FF,GAAA,IAAO,mBAAmB,KAAKlB,aAAA,CAAcqB,IAAA,CAAK,EAAE;IAEpDH,GAAA,IAAO,oBAAoB,KAAKhB,cAAA,CAAemB,IAAA,CAAK,EAAE;IAEtDH,GAAA,IAAO,sBAAsB,KAAKf,gBAAA,CAAiBkB,IAAA,CAAK,EAAE;IAE1DH,GAAA,IAAO,uBAAuB,KAAKjB,iBAAA,CAAkBoB,IAAA,CAAK,EAAE;IAE5DH,GAAA,IAAO,gEAAgEH,mBAAA;IAEhEG,GAAA;IAEAA,GAAA;IAEP,MAAMI,GAAA,GAAM;MACVC,IAAA,EAAM,KAAKC,MAAA,CAAON,GAAG;MACrBnB,QAAA,EAAU,KAAKA;IAAA;IAGb,WAAOU,MAAA,KAAW,YAAY;MACVgB,qBAAA,OAAMhB,MAAA,CAAOa,GAAG,CAAC;IACzC;IAEO,OAAAA,GAAA;EACT;EAAA;EAGQE,OAAOE,IAAA,EAAsB;;IACnC,MAAMC,UAAA,GAAa;IACnB,MAAMC,eAAA,GAAkB;IACxB,MAAMC,QAAA,GAAW;IAEX,MAAAC,GAAA,GAAMA,CAACC,EAAA,EAAYC,GAAA,KAAyBA,GAAA,GAAM,IAAID,EAAA,GAAKD,GAAA,CAAIC,EAAA,EAAIC,GAAA,GAAM,CAAC,IAAI;IAEpF,IAAIC,MAAA,GAAS;IAEb,QACEC,EAAA,IAAAC,EAAA,GAAAT,IAAA,CACGhB,KAAA,CAAM,oCAAoC,MAD7C,gBAAAyB,EAAA,CAEIC,GAAA,CAAKC,GAAA,IAAQ;MACb,IAAI,CAACR,QAAA,CAASS,IAAA,CAAKD,GAAG,KAAK,CAACT,eAAA,CAAgBU,IAAA,CAAKD,GAAG,KAAKV,UAAA,CAAWW,IAAA,CAAKD,GAAG,GAAG;QAC7EJ,MAAA;MACF;MAEA,MAAMX,GAAA,GAAM,GAAGQ,GAAA,CAAI,MAAMG,MAAM,IAAII,GAAA;MAEnC,IAAI,CAACR,QAAA,CAASS,IAAA,CAAKD,GAAG,KAAK,CAACT,eAAA,CAAgBU,IAAA,CAAKD,GAAG,KAAK,CAACV,UAAA,CAAWW,IAAA,CAAKD,GAAG,GAAG;QAC9EJ,MAAA;MACF;MAEO,OAAAX,GAAA;IACR,GACAD,IAAA,CAAK,UAfR,OAAAa,EAAA,GAeiB;EAErB;EAAA;EAGQK,eAAeC,GAAA,EAAyB;IACxC,MAAAC,CAAA,GAAIC,IAAA,CAAKF,GAAG;IAClB,MAAMG,GAAA,GAAM,IAAIC,UAAA,CAAWH,CAAA,CAAEI,MAAM;IAEnC,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIJ,GAAA,CAAIE,MAAA,EAAQC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC1CH,GAAA,CAAIG,CAAC,IAAIL,CAAA,CAAEO,UAAA,CAAWF,CAAC;IACzB;IAEO,OAAAH,GAAA;EACT;EAEQM,YAAYC,KAAA,EAA0BC,GAAA,EAAyB;;IACrE,KAAK/C,MAAA,GAAS,KAAKA,MAAA,IAAUgD,QAAA,CAASC,aAAA,CAAc,QAAQ;IAC5D,KAAKhD,GAAA,GAAM,KAAKA,GAAA,IAAO,KAAKD,MAAA,CAAOkD,UAAA,CAAW,IAAI;IAElD,KAAKlD,MAAA,CAAOmD,KAAA,GAAQL,KAAA,CAAMK,KAAA,YAAiBC,iBAAA,GAAoB,IAAIN,KAAA,CAAMK,KAAA;IACzE,KAAKnD,MAAA,CAAOqD,MAAA,GAASP,KAAA,CAAMO,MAAA,YAAkBD,iBAAA,GAAoB,IAAIN,KAAA,CAAMO,MAAA;IAE3E,CAAAtB,EAAA,QAAK9B,GAAA,KAAL,gBAAA8B,EAAA,CAAUuB,SAAA,CAAUR,KAAA,EAAO,GAAG;IAGxB,MAAAS,UAAA,GAAa,KAAKvD,MAAA,CAAOwD,SAAA,CAAU,SAAST,GAAA,IAAO,CAAC,EAAEtC,OAAA,CAAQ,kCAAkC,EAAE;IAGjG,YAAK0B,cAAA,CAAeoB,UAAU;EACvC;EAAA;EAGQE,kBAAkBC,IAAA,EAAkF;IACtG,IAAAA,IAAA,YAAgBC,0BAAA,IAA8BD,IAAA,CAAKE,4BAAA,EAA8B;MAE7E,MAAAC,qBAAA,GAAgDH,IAAA,CAAKI,KAAA,CAAMhF,WAAA;MAEjE,MAAMiF,GAAA,GAAgB,IAAIF,qBAAA,CAAsBH,IAAA,CAAKM,KAAA,GAAQN,IAAA,CAAKO,QAAQ;MAC1E,MAAMC,IAAA,GAAOR,IAAA,CAAKO,QAAA;MAElB,SAASvB,CAAA,GAAI,GAAGC,CAAA,GAAIe,IAAA,CAAKM,KAAA,EAAOtB,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC1C,SAASyB,CAAA,GAAI,GAAGA,CAAA,GAAID,IAAA,EAAMC,CAAA,IAAK;UACzBJ,GAAA,CAAArB,CAAA,GAAIwB,IAAA,GAAOC,CAAC,IAAIT,IAAA,CAAK,KAAKU,QAAA,CAASD,CAAC,CAAC,EAAEzB,CAAC;QAC9C;MACF;MAEO,OAAAqB,GAAA;IAAA,OACF;MACL,OAAOL,IAAA,CAAKI,KAAA;IACd;EACF;EAAA;EAAA;EAIQO,SAASN,GAAA,EAAmCO,EAAA,EAAYC,EAAA,EAAmC;IAC7F,IAAAC,KAAA,CAAMC,OAAA,CAAQV,GAAG,GAAG;MACtB,OAAOA,GAAA,CAAIW,KAAA,CAAMJ,EAAA,EAAIA,EAAA,GAAKC,EAAE;IAAA,OACvB;MACL,MAAMV,qBAAA,GAAgDE,GAAA,CAAIjF,WAAA;MAE1D,OAAO,IAAI+E,qBAAA,CAAsBE,GAAA,CAAIY,MAAA,EAAQL,EAAA,GAAKP,GAAA,CAAIa,iBAAA,EAAmBL,EAAE;IAC7E;EACF;EAAA;EAGQM,aACNnB,IAAA,EACAoB,IAAA,EACAC,MAAA,EACAC,IAAA,EACQ;IACF,MAAAlB,KAAA,GAAQ,KAAKL,iBAAA,CAAkBC,IAAI;IACzC,MAAMxC,GAAA,GAAMsD,KAAA,CAAMC,OAAA,CAAQX,KAAK,IAC3B,GACE,eAAegB,IAAA,sBAA+BA,IAAA,kBAAsBhB,KAAA,CAAMrB,MAAA,OAAaqB,KAAA,CAAM7C,IAAA,CAAK,GAAG,oCACpE,sBAAsB6D,IAAA,kBAAsBG,IAAA,CAAKC,KAAA,CAClFpB,KAAA,CAAMrB,MAAA,GAASiB,IAAA,CAAKO,QAAA,cACRP,IAAA,CAAKO,QAAA,OAAec,MAAA,CAC/B/C,GAAA,CAAKmD,CAAA,IAAM,gBAAgBA,CAAA,WAAYH,IAAA,MAAU,EACjD/D,IAAA,CAAK,EAAE,6CACV;IAEG,OAAAC,GAAA;EACT;EAAA;EAGQkE,aAAaC,CAAA,EAAqB;IAGxCA,CAAA,CAAEC,YAAA,CAAa;IAEf,KAAKpF,QAAA,GAAW,KAAKA,QAAA,IAAY,IAAIqF,OAAA,CAAQ;IACxC,KAAArF,QAAA,CAASsF,IAAA,CAAKH,CAAA,CAAEI,MAAM;IAC3B,KAAKvF,QAAA,CAASwF,SAAA;IACd,OAAO,WAAW,KAAKxF,QAAA,CAASyF,OAAA,CAAQ,EAAE1E,IAAA,CAAK,GAAG;EACpD;EAAA;EAAA;EAIQ2E,gBAAgBC,CAAA,EAAiC;IACvD,IAAIC,IAAA,GAAO,KAAKvG,YAAA,CAAawG,GAAA,CAAIF,CAAC;IAElC,IAAI,CAACC,IAAA,EAAM;MAET,MAAME,cAAA,GAAiBH,CAAA;MAEnB,KAACG,cAAA,CAAeC,gBAAA,EAAkB;QAC9B,UAAIC,KAAA,CAAM,sEAAsE;MACxF;MAEA,MAAMC,MAAA,GAAS,OAAO,KAAKtG,iBAAA,CAAkB4C,MAAA,GAAS;MAEhD,MAAA2D,UAAA,GAAaJ,cAAA,CAAeK,KAAA,GAC9BL,cAAA,CAAeK,KAAA,CAAMrC,KAAA,GAAQgC,cAAA,CAAeK,KAAA,CAAMpC,QAAA,GAClD+B,cAAA,CAAeM,UAAA,CAAWC,QAAA,CAASvC,KAAA;MAEvC,MAAMwC,MAAA,GACJR,cAAA,CAAeQ,MAAA,IAAU,QAAQR,cAAA,CAAeQ,MAAA,CAAO/D,MAAA,KAAW,IAC9DuD,cAAA,CAAeQ,MAAA,GACf,CAAC;QAAEC,KAAA,EAAO;QAAGzC,KAAA,EAAOoC,UAAA;QAAYM,aAAA,EAAe;MAAA,CAAG;MAExD,MAAMC,KAAA,GAAQd,CAAA,CAAEf,IAAA,GAAO,UAAUe,CAAA,CAAEf,IAAA,MAAU;MACzC,IAAA8B,KAAA,GAAQ,iBAAiBT,MAAA,IAAUQ,KAAA;MAGvC,MAAME,OAAA,GAAU,GAAGV,MAAA;MACnB,MAAMW,QAAA,GAAW,GAAGX,MAAA;MACXS,KAAA,SAAK/B,YAAA,CAAamB,cAAA,CAAeM,UAAA,CAAWC,QAAA,EAAUM,OAAA,EAAS,CAAC,KAAK,KAAK,GAAG,GAAG,OAAO;MAChGD,KAAA,IAAS,iBAAiBE,QAAA,yCAAiDD,OAAA;MAQ3E,IAAIE,cAAA,GAAiB,qCAAqCD,QAAA;MACtD,gBAAYd,cAAA,CAAeM,UAAA,EAAY;QACzC,MAAMU,QAAA,GAAW,GAAGb,MAAA;QACXS,KAAA,SAAK/B,YAAA,CAAamB,cAAA,CAAeM,UAAA,CAAWW,MAAA,EAAQD,QAAA,EAAU,CAAC,KAAK,KAAK,GAAG,GAAG,OAAO;QAC/FD,cAAA,IAAkB,qCAAqCC,QAAA;MACzD;MAGI,YAAQhB,cAAA,CAAeM,UAAA,EAAY;QACrC,MAAMY,MAAA,GAAS,GAAGf,MAAA;QACTS,KAAA,SAAK/B,YAAA,CAAamB,cAAA,CAAeM,UAAA,CAAWa,EAAA,EAAID,MAAA,EAAQ,CAAC,KAAK,GAAG,GAAG,OAAO;QACpFH,cAAA,IAAkB,uCAAuCG,MAAA;MAC3D;MAGI,IAAAE,GAAA,IAAOpB,cAAA,CAAeM,UAAA,EAAY;QACpC,MAAMY,MAAA,GAAS,GAAGf,MAAA;QACTS,KAAA,SAAK/B,YAAA,CAAamB,cAAA,CAAeM,UAAA,CAAWc,GAAG,GAAGF,MAAA,EAAQ,CAAC,KAAK,GAAG,GAAG,OAAO;QACtFH,cAAA,IAAkB,uCAAuCG,MAAA;MAC3D;MAGI,eAAWlB,cAAA,CAAeM,UAAA,EAAY;QACxC,MAAMe,OAAA,GAAU,GAAGlB,MAAA;QACVS,KAAA,SAAK/B,YAAA,CAAamB,cAAA,CAAeM,UAAA,CAAWgB,KAAA,EAAOD,OAAA,EAAS,CAAC,KAAK,KAAK,GAAG,GAAG,OAAO;QAC7FN,cAAA,IAAkB,oCAAoCM,OAAA;MACxD;MAEA,IAAIE,UAAA,GAAkD;MACtD,IAAIvB,cAAA,CAAeK,KAAA,EAAO;QACXkB,UAAA,QAAK9D,iBAAA,CAAkBuC,cAAA,CAAeK,KAAK;MAAA,OACnD;QACQkB,UAAA,OAAI/C,KAAA,CAAM4B,UAAU;QACxB,SAAA1D,CAAA,GAAI,GAAGC,CAAA,GAAI4E,UAAA,CAAW9E,MAAA,EAAQC,CAAA,GAAIC,CAAA,IAAK6B,KAAA,CAAMC,OAAA,CAAQ8C,UAAU,GAAG7E,CAAA,IAAK6E,UAAA,CAAW7E,CAAC,IAAIA,CAAA;MAClG;MAEA,SAASA,CAAA,GAAI,GAAGC,CAAA,GAAI6D,MAAA,CAAO/D,MAAA,EAAQC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACvC,MAAA8E,KAAA,GAAQhB,MAAA,CAAO9D,CAAC;QACtB,MAAM+E,MAAA,GAAS,KAAKpD,QAAA,CAASkD,UAAA,EAAYC,KAAA,CAAMf,KAAA,EAAOe,KAAA,CAAMxD,KAAK;QAC3D,MAAA0D,SAAA,GAAYD,MAAA,CAAOhF,MAAA,GAAS;QACzBmE,KAAA,0CAAsCY,KAAA,CAAMd,aAAA,YAAyBgB,SAAA;QACrEd,KAAA,IAAAG,cAAA;QAEAH,KAAA,UAAMa,MAAA,CAAOxG,IAAA,CAAK,GAAG;QACrB2F,KAAA;MACX;MAESA,KAAA;MAEJ,KAAA/G,iBAAA,CAAkB8H,IAAA,CAAKf,KAAK;MAE1Bd,IAAA;QAAEK,MAAA;QAAQH;MAAA;MACZ,KAAAzG,YAAA,CAAaqI,GAAA,CAAI/B,CAAA,EAAGC,IAAI;IAC/B;IAEO,OAAAA,IAAA;EACT;EAAA;EAAA;EAIQ+B,eAAeC,GAAA,EAAsB;IAC3C,IAAIC,KAAA,GAAQ,KAAKrI,QAAA,CAASqG,GAAA,CAAI+B,GAAG;IACjC,IAAIC,KAAA,IAAS,MAAM;MACTA,KAAA,YAAS,KAAKnI,aAAA,CAAc6C,MAAA,GAAS;MAE7C,MAAMM,GAAA,GAAM;MACN,MAAA+B,IAAA,GAAOgD,GAAA,CAAIhD,IAAA,IAAQiD,KAAA;MACrB,IAAAC,SAAA,GAAY,cAAcD,KAAA,WAAgBjD,IAAA;MAE1C,SAAK9F,OAAA,CAAQC,OAAA,KAAY,SAAS;QACpC+I,SAAA,IAAa,mBAAmB,KAAKhJ,OAAA,CAAQG,gBAAA,GAAmB2F,IAAA,IAAQ/B,GAAA;MAAA,OACnE;QAELiF,SAAA,IAAa,cAAc,KAAKhJ,OAAA,CAAQG,gBAAA,GAAmB2F,IAAA,IAAQ/B,GAAA;MACrE;MAEaiF,SAAA;MAER,KAAApI,aAAA,CAAc+H,IAAA,CAAKK,SAAS;MAC5B,KAAAtI,QAAA,CAASkI,GAAA,CAAIE,GAAA,EAAKC,KAAK;MAC5B,KAAKpI,QAAA,CAASgI,IAAA,CAAK;QACjBM,SAAA,EAAW,KAAKjJ,OAAA,CAAQG,gBAAA;QACxB2F,IAAA;QACA/B,GAAA;QACA5B,IAAA,EAAM,KAAK0B,WAAA,CAAYiF,GAAA,CAAIhF,KAAA,EAAOC,GAAG;QACrCmF,QAAA,EAAUJ;MAAA,CACX;IACH;IAEO,OAAAC,KAAA;EACT;EAAA;EAAA;EAIQI,gBAAgBC,CAAA,EAAmC;IACzD,IAAIC,KAAA,GAAQ,KAAK5I,WAAA,CAAYsG,GAAA,CAAIqC,CAAC;IAElC,IAAIC,KAAA,IAAS,MAAM;MACTA,KAAA,SAAM,KAAKvI,cAAA,CAAe2C,MAAA,GAAS;MAE3C,IAAIuC,IAAA,GAAO;MAEX,IAAIoD,CAAA,YAAaE,mBAAA,EAAqB;QAC7BtD,IAAA;MAAA,WACEoD,CAAA,YAAaG,iBAAA,EAAmB;QAClCvD,IAAA;QAEH,IAAAoD,CAAA,CAAEpG,GAAA,KAAQ,MAAM;UAIlBzB,OAAA,CAAQG,IAAA,CAAK,qEAAqE;QACpF;MACF;MAEA,IAAI0H,CAAA,YAAaI,iBAAA,EAAmB;QAC5B,MAAAC,QAAA,GAAWL,CAAA,CAAEK,QAAA,GAAWL,CAAA,CAAEK,QAAA,GAAW,IAAIC,KAAA,CAAM,GAAG,GAAG,CAAC;QACtD,MAAAC,OAAA,GAAUP,CAAA,CAAEd,KAAA,GAAQc,CAAA,CAAEd,KAAA,GAAQ,IAAIoB,KAAA,CAAM,GAAG,GAAG,CAAC;QAC/C,MAAAE,QAAA,GAAWR,CAAA,CAAEQ,QAAA,GAAWR,CAAA,CAAEQ,QAAA,GAAW,IAAIF,KAAA,CAAM,GAAG,GAAG,CAAC;QACtD,MAAAG,SAAA,GAAYT,CAAA,CAAES,SAAA,IAAa;QAC3B,MAAAC,YAAA,GAAeV,CAAA,CAAEU,YAAA,IAAgB;QAKvC,IAAIC,gBAAA,GAAmB;QACvB,IAAIX,CAAA,CAAEY,WAAA,EAAa;UACGD,gBAAA,oBAClBX,CAAA,CAAEpG,GAAA,GAAM,kDAAkD;UAGxD,IAAAoG,CAAA,CAAEa,OAAA,GAAU,GAAG;YACjBF,gBAAA,IAAoB,wBAAwBX,CAAA,CAAEa,OAAA;UAChD;QACF;QAEA,MAAMC,aAAA,GAAgB,GAAG,4BAA4BlE,IAAA,gBACnDoD,CAAA,CAAEe,WAAA,GACE,+DACA,yBAAyBV,QAAA,CAASW,CAAA,IAAKX,QAAA,CAAS5C,CAAA,IAAK4C,QAAA,CAASpG,CAAA,0BAElE2C,IAAA,KAAS,aACL,YACEoD,CAAA,CAAEpG,GAAA,GACE,8DACA,wBAAwB2G,OAAA,CAAQS,CAAA,IAAKT,OAAA,CAAQ9C,CAAA,IAAK8C,OAAA,CAAQtG,CAAA,2BAEhE,KAEJ2C,IAAA,KAAS,aACL,SAASoD,CAAA,CAAEiB,SAAA,GAAY,2DAA2D,cAClF,KAEJrE,IAAA,KAAS,UACL,GAAG,mCAAmC4D,QAAA,CAASQ,CAAA,IAAKR,QAAA,CAAS/C,CAAA,IAAK+C,QAAA,CAASvG,CAAA,qCACzE+F,CAAA,CAAEkB,WAAA,GACE,+DACA,0BAA0BT,SAAA,2BAEhC,KACH,sBAAsBF,OAAA,CAAQS,CAAA,IAAKT,OAAA,CAAQ9C,CAAA,IAAK8C,OAAA,CAAQtG,CAAA,4BAA6B,wBAAwByG,YAAA,4BAAwCC,gBAAA,GAAmB,KAAK/D,IAAA;QAE1K,MAAAuE,UAAA,GAAa,GAAG,eAAelB,KAAA,8BACnCD,CAAA,CAAEpG,GAAA,GACE,sDAAsD,cAAc,KAAK6F,cAAA,CACvEO,CAAA,CAAEpG,GAAA,2IAEJ,KAEJoG,CAAA,CAAEkB,WAAA,GACE,uDAAuD,cAAc,KAAKzB,cAAA,CACxEO,CAAA,CAAEkB,WAAA,6IAEJ,KAEJlB,CAAA,CAAEe,WAAA,GACE,uDAAuD,cAAc,KAAKtB,cAAA,CACxEO,CAAA,CAAEe,WAAA,6IAEJ,KAEJf,CAAA,CAAEiB,SAAA,GACE,mDAAmD,cAAc,KAAKxB,cAAA,CACpEO,CAAA,CAAEiB,SAAA,qIAEJ,KACHH,aAAA,GACDd,CAAA,CAAEoB,IAAA,KAASC,UAAA,GACP,yHACA;QAGN,MAAMC,YAAA,GAAetB,CAAA,CAAEtD,IAAA,GAAO,UAAUsD,CAAA,CAAEtD,IAAA,MAAU;QAC9C,MAAA6E,YAAA,GAAe,iBAAiBtB,KAAA,IAASqB,YAAA,2BAAuCrB,KAAA;QAEjF,KAAAtI,gBAAA,CAAiB4H,IAAA,CAAKgC,YAAY;QAClC,KAAA7J,cAAA,CAAe6H,IAAA,CAAK4B,UAAU;QAC9B,KAAA9J,WAAA,CAAYmI,GAAA,CAAIQ,CAAA,EAAGC,KAAK;MAC/B;IACF;IAEO,OAAAA,KAAA;EACT;EAAA;EAGQzH,cAAcyE,CAAA,EAAqB;IACrC,IAAAuE,IAAA,GAAO,eAAevE,CAAA,CAAEP,IAAA;IAEpB8E,IAAA,SAAKxE,YAAA,CAAaC,CAAC;IACrB,MAAAwE,CAAA,GAAiD,IAAIC,IAAA;IACzDD,CAAA,CAAAE,QAAA;IAEF,IAAI1E,CAAA,YAAayE,IAAA,IAAQzE,CAAA,CAAE2E,MAAA,IAAU3E,CAAA,CAAE0E,QAAA,KAAa,MAAM;MAGxD,MAAME,QAAA,GAAW,KAAKrE,eAAA,CAAgBP,CAAA,CAAE0E,QAAQ;MAChD,MAAM5D,MAAA,GAAS8D,QAAA,CAAS9D,MAAA;MACxB,MAAM4D,QAAA,GAAWE,QAAA,CAASjE,cAAA;MAG1B,IAAIkE,MAAA,GAAS;MACT,IAAAC,WAAA;MAKJ,MAAMC,GAAA,GAAyD/E,CAAA,CAAEgF,QAAA,IAAY,IAAI9B,iBAAA,CAAkB;MACnG,MAAM+B,SAAA,GAAY9F,KAAA,CAAMC,OAAA,CAAQ2F,GAAG,IAAIA,GAAA,GAAM,CAACA,GAAG;MAEjD,IAAIL,QAAA,CAASvD,MAAA,CAAO/D,MAAA,GAAS6H,SAAA,CAAU7H,MAAA,EAAQ;QAC7C0H,WAAA,GAAc,IAAI3F,KAAA,CAAMuF,QAAA,CAASvD,MAAA,CAAO/D,MAAM;MAAA,OACzC;QACS0H,WAAA,OAAI3F,KAAA,CAAM8F,SAAA,CAAU7H,MAAM;MAC1C;MAEAyH,MAAA,GAASC,WAAA,CAAYI,IAAA,CAAK,IAAI,EAAEvI,GAAA,CAAI,CAACwI,CAAA,EAAG9H,CAAA,KAAM,KAAKyF,eAAA,CAAgBmC,SAAA,CAAU5H,CAAA,GAAI4H,SAAA,CAAU7H,MAAM,CAAC,CAAC;MAEnGmH,IAAA,IAAQ,GACN,4BAA4BzD,MAAA,QAC3B+D,MAAA,IAAU,OACP,oCAAoCA,MAAA,CACjClI,GAAA,CACC,CAACyI,EAAA,EAAI/H,CAAA,KACH,GAAG,4CAA4CA,CAAA,cAAe+H,EAAA,8GAEjExJ,IAAA,CAAK,EAAE,yCACV;IAER;IAEEoE,CAAA,CAAAqF,QAAA,CAASC,OAAA,CAASC,CAAA,IAAOhB,IAAA,IAAQ,KAAKhJ,aAAA,CAAcgK,CAAC,CAAE;IAEjDhB,IAAA;IAED,OAAAA,IAAA;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}