{"ast": null, "code": "import { Texture, NearestFilter, ClampToEdgeWrapping } from \"three\";\nclass Data3DTexture extends Texture {\n  constructor(data = null, width = 1, height = 1, depth = 1) {\n    super(null);\n    this.isData3DTexture = true;\n    this.image = {\n      data,\n      width,\n      height,\n      depth\n    };\n    this.magFilter = NearestFilter;\n    this.minFilter = NearestFilter;\n    this.wrapR = ClampToEdgeWrapping;\n    this.generateMipmaps = false;\n    this.flipY = false;\n    this.unpackAlignment = 1;\n  }\n}\nexport { Data3DTexture };", "map": {"version": 3, "names": ["Data3DTexture", "Texture", "constructor", "data", "width", "height", "depth", "isData3DTexture", "image", "magFilter", "NearestFilter", "minFilter", "wrapR", "ClampToEdgeWrapping", "generateMipmaps", "flipY", "unpackAlignment"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\_polyfill\\Data3DTexture.js"], "sourcesContent": ["import { Texture, ClampToEdgeWrapping, NearestFilter } from 'three'\n\nclass Data3DTexture extends Texture {\n  constructor(data = null, width = 1, height = 1, depth = 1) {\n    super(null)\n\n    this.isData3DTexture = true\n\n    this.image = { data, width, height, depth }\n\n    this.magFilter = NearestFilter\n    this.minFilter = NearestFilter\n\n    this.wrapR = ClampToEdgeWrapping\n\n    this.generateMipmaps = false\n    this.flipY = false\n    this.unpackAlignment = 1\n  }\n}\n\nexport { Data3DTexture }\n"], "mappings": ";AAEA,MAAMA,aAAA,SAAsBC,OAAA,CAAQ;EAClCC,YAAYC,IAAA,GAAO,MAAMC,KAAA,GAAQ,GAAGC,MAAA,GAAS,GAAGC,KAAA,GAAQ,GAAG;IACzD,MAAM,IAAI;IAEV,KAAKC,eAAA,GAAkB;IAEvB,KAAKC,KAAA,GAAQ;MAAEL,IAAA;MAAMC,KAAA;MAAOC,MAAA;MAAQC;IAAO;IAE3C,KAAKG,SAAA,GAAYC,aAAA;IACjB,KAAKC,SAAA,GAAYD,aAAA;IAEjB,KAAKE,KAAA,GAAQC,mBAAA;IAEb,KAAKC,eAAA,GAAkB;IACvB,KAAKC,KAAA,GAAQ;IACb,KAAKC,eAAA,GAAkB;EACxB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}