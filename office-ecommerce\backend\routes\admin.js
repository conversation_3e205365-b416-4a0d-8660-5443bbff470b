const express = require('express');
const router = express.Router();
const { authenticateToken, requirePermission, requireAdmin } = require('../middleware/auth');
const ActivityLogService = require('../services/activityLogService');
const ActivityLoggerMiddleware = require('../middleware/activityLogger');

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard data
// @access  Private (Employee or Admin)
router.get('/dashboard',
  authenticateToken,
  requirePermission('access_admin_dashboard'),
  ActivityLoggerMiddleware.logDashboardAccess(),
  async (req, res) => {
  try {
    // Get activity log statistics
    let activityStats = null;
    try {
      activityStats = await ActivityLogService.getActivityStats();
    } catch (error) {
      console.error('Failed to get activity stats:', error);
    }

    // Mock dashboard data
    const dashboardData = {
      overview: {
        totalProducts: 150,
        totalOrders: 1250,
        totalRevenue: 125000.00,
        totalCustomers: 450,
        lowStockItems: 8,
        pendingOrders: 25
      },
      recentOrders: [
        {
          id: 'ORD001',
          orderNumber: 'ORD-2024-001',
          customerName: '<PERSON> Doe',
          total: 696.98,
          status: 'Processing',
          date: new Date().toISOString()
        }
      ],
      topProducts: [
        {
          id: 'PROD001',
          name: 'Executive Office Chair',
          sales: 45,
          revenue: 13499.55
        }
      ],
      salesC<PERSON>: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        data: [12000, 15000, 18000, 22000, 25000, 28000]
      },
      inventoryAlerts: [
        {
          productName: 'Standing Desk',
          currentStock: 8,
          reorderLevel: 15,
          status: 'Low Stock'
        }
      ],
      activityStats: activityStats ? {
        totalActivities: activityStats.overview.totalActivities,
        last24Hours: activityStats.overview.last24Hours,
        last7Days: activityStats.overview.last7Days,
        errorCount: activityStats.overview.errorCount,
        topActions: activityStats.actionStats.slice(0, 5),
        recentActivity: activityStats.dailyStats.slice(0, 7)
      } : null
    };
    
    res.json({
      success: true,
      data: dashboardData
    });
    
  } catch (error) {
    console.error('Get dashboard data error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/analytics/sales
// @desc    Get sales analytics
// @access  Private (Admin only)
router.get('/analytics/sales', async (req, res) => {
  try {
    const { period = 'month' } = req.query;
    
    // Mock sales analytics data
    const salesAnalytics = {
      period,
      totalSales: 125000.00,
      totalOrders: 1250,
      averageOrderValue: 100.00,
      salesGrowth: 15.5,
      topSellingProducts: [
        { name: 'Executive Office Chair', sales: 45, revenue: 13499.55 },
        { name: 'Standing Desk', sales: 32, revenue: 19199.68 },
        { name: 'Conference Table', sales: 18, revenue: 16199.82 }
      ],
      salesByCategory: [
        { category: 'Chairs', sales: 45000.00, percentage: 36 },
        { category: 'Desks', sales: 38000.00, percentage: 30.4 },
        { category: 'Tables', sales: 25000.00, percentage: 20 },
        { category: 'Storage', sales: 17000.00, percentage: 13.6 }
      ],
      monthlySales: [
        { month: 'Jan', sales: 18000 },
        { month: 'Feb', sales: 22000 },
        { month: 'Mar', sales: 25000 },
        { month: 'Apr', sales: 28000 },
        { month: 'May', sales: 32000 },
        { month: 'Jun', sales: 35000 }
      ]
    };
    
    res.json({
      success: true,
      data: salesAnalytics
    });
    
  } catch (error) {
    console.error('Get sales analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/analytics/inventory
// @desc    Get inventory analytics
// @access  Private (Admin only)
router.get('/analytics/inventory', async (req, res) => {
  try {
    // Mock inventory analytics data
    const inventoryAnalytics = {
      totalValue: 245000.00,
      totalItems: 150,
      lowStockItems: 8,
      outOfStockItems: 2,
      turnoverRate: 4.2,
      topMovingProducts: [
        { name: 'Executive Office Chair', turnover: 8.5 },
        { name: 'Standing Desk', turnover: 6.2 },
        { name: 'Office Cabinet', turnover: 5.8 }
      ],
      slowMovingProducts: [
        { name: 'Luxury Conference Table', turnover: 1.2 },
        { name: 'Executive Bookshelf', turnover: 1.5 }
      ],
      stockLevels: {
        inStock: 140,
        lowStock: 8,
        outOfStock: 2
      },
      warehouseUtilization: 78.5
    };
    
    res.json({
      success: true,
      data: inventoryAnalytics
    });
    
  } catch (error) {
    console.error('Get inventory analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/users
// @desc    Get all users
// @access  Private (Admin only)
router.get('/users', async (req, res) => {
  try {
    const { page = 1, limit = 10, role, search } = req.query;
    
    // Mock users data
    const users = [
      {
        id: '1',
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'Admin',
        isActive: true,
        lastLogin: new Date().toISOString(),
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        firstName: 'Manager',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'Employee',
        isActive: true,
        lastLogin: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString()
      }
    ];
    
    let filteredUsers = [...users];
    
    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }
    
    if (search) {
      filteredUsers = filteredUsers.filter(user => 
        user.firstName.toLowerCase().includes(search.toLowerCase()) ||
        user.lastName.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        users: paginatedUsers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredUsers.length / limit),
          totalItems: filteredUsers.length,
          itemsPerPage: parseInt(limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// =============================================
// ACTIVITY LOG ROUTES
// =============================================

// @route   GET /api/admin/activity-logs
// @desc    Get activity logs with filtering and pagination
// @access  Private (Admin only)
router.get('/activity-logs', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      userID,
      action,
      entityType,
      severity,
      startDate,
      endDate,
      search
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      userID,
      action,
      entityType,
      severity,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      search
    };

    const result = await ActivityLogService.getActivityLogs(options);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Get activity logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/activity-logs/stats
// @desc    Get activity log statistics for dashboard
// @access  Private (Admin only)
router.get('/activity-logs/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const stats = await ActivityLogService.getActivityStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get activity stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/activity-logs/actions
// @desc    Get available actions for filtering
// @access  Private (Admin only)
router.get('/activity-logs/actions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const actions = [
      'LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'UPLOAD',
      'BULK_CREATE', 'BULK_UPDATE', 'BULK_DELETE', 'EXPORT', 'IMPORT'
    ];

    res.json({
      success: true,
      data: actions
    });

  } catch (error) {
    console.error('Get actions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/activity-logs/entity-types
// @desc    Get available entity types for filtering
// @access  Private (Admin only)
router.get('/activity-logs/entity-types', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const entityTypes = [
      'User', 'Product', 'Order', 'Inventory', 'Supplier', 'Category',
      'Authentication', 'Dashboard', 'Settings', 'File', 'Report'
    ];

    res.json({
      success: true,
      data: entityTypes
    });

  } catch (error) {
    console.error('Get entity types error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
