{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useFrame } from '@react-three/fiber';\nconst boundingBox = new THREE.Box3();\nconst boundingBoxSize = new THREE.Vector3();\nconst BBAnchor = ({\n  anchor,\n  ...props\n}) => {\n  const ref = React.useRef(null);\n  const parentRef = React.useRef(null); // Reattach group created by this component to the parent's parent,\n  // so it becomes a sibling of its initial parent.\n  // We do that so the children have no impact on a bounding box of a parent.\n\n  React.useEffect(() => {\n    var _ref$current, _ref$current$parent;\n    if ((_ref$current = ref.current) != null && (_ref$current$parent = _ref$current.parent) != null && _ref$current$parent.parent) {\n      parentRef.current = ref.current.parent;\n      ref.current.parent.parent.add(ref.current);\n    }\n  }, []);\n  useFrame(() => {\n    if (parentRef.current) {\n      boundingBox.setFromObject(parentRef.current);\n      boundingBox.getSize(boundingBoxSize);\n      ref.current.position.set(parentRef.current.position.x + boundingBoxSize.x * anchor[0] / 2, parentRef.current.position.y + boundingBoxSize.y * anchor[1] / 2, parentRef.current.position.z + boundingBoxSize.z * anchor[2] / 2);\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n};\nexport { BBAnchor };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "useFrame", "boundingBox", "Box3", "boundingBoxSize", "Vector3", "BBAnchor", "anchor", "props", "ref", "useRef", "parentRef", "useEffect", "_ref$current", "_ref$current$parent", "current", "parent", "add", "setFromObject", "getSize", "position", "set", "x", "y", "z", "createElement"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/BBAnchor.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useFrame } from '@react-three/fiber';\n\nconst boundingBox = new THREE.Box3();\nconst boundingBoxSize = new THREE.Vector3();\nconst BBAnchor = ({\n  anchor,\n  ...props\n}) => {\n  const ref = React.useRef(null);\n  const parentRef = React.useRef(null); // Reattach group created by this component to the parent's parent,\n  // so it becomes a sibling of its initial parent.\n  // We do that so the children have no impact on a bounding box of a parent.\n\n  React.useEffect(() => {\n    var _ref$current, _ref$current$parent;\n\n    if ((_ref$current = ref.current) != null && (_ref$current$parent = _ref$current.parent) != null && _ref$current$parent.parent) {\n      parentRef.current = ref.current.parent;\n      ref.current.parent.parent.add(ref.current);\n    }\n  }, []);\n  useFrame(() => {\n    if (parentRef.current) {\n      boundingBox.setFromObject(parentRef.current);\n      boundingBox.getSize(boundingBoxSize);\n      ref.current.position.set(parentRef.current.position.x + boundingBoxSize.x * anchor[0] / 2, parentRef.current.position.y + boundingBoxSize.y * anchor[1] / 2, parentRef.current.position.z + boundingBoxSize.z * anchor[2] / 2);\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props));\n};\n\nexport { BBAnchor };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,WAAW,GAAG,IAAIF,KAAK,CAACG,IAAI,CAAC,CAAC;AACpC,MAAMC,eAAe,GAAG,IAAIJ,KAAK,CAACK,OAAO,CAAC,CAAC;AAC3C,MAAMC,QAAQ,GAAGA,CAAC;EAChBC,MAAM;EACN,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,SAAS,GAAGZ,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EACtC;EACA;;EAEAX,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,IAAIC,YAAY,EAAEC,mBAAmB;IAErC,IAAI,CAACD,YAAY,GAAGJ,GAAG,CAACM,OAAO,KAAK,IAAI,IAAI,CAACD,mBAAmB,GAAGD,YAAY,CAACG,MAAM,KAAK,IAAI,IAAIF,mBAAmB,CAACE,MAAM,EAAE;MAC7HL,SAAS,CAACI,OAAO,GAAGN,GAAG,CAACM,OAAO,CAACC,MAAM;MACtCP,GAAG,CAACM,OAAO,CAACC,MAAM,CAACA,MAAM,CAACC,GAAG,CAACR,GAAG,CAACM,OAAO,CAAC;IAC5C;EACF,CAAC,EAAE,EAAE,CAAC;EACNd,QAAQ,CAAC,MAAM;IACb,IAAIU,SAAS,CAACI,OAAO,EAAE;MACrBb,WAAW,CAACgB,aAAa,CAACP,SAAS,CAACI,OAAO,CAAC;MAC5Cb,WAAW,CAACiB,OAAO,CAACf,eAAe,CAAC;MACpCK,GAAG,CAACM,OAAO,CAACK,QAAQ,CAACC,GAAG,CAACV,SAAS,CAACI,OAAO,CAACK,QAAQ,CAACE,CAAC,GAAGlB,eAAe,CAACkB,CAAC,GAAGf,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEI,SAAS,CAACI,OAAO,CAACK,QAAQ,CAACG,CAAC,GAAGnB,eAAe,CAACmB,CAAC,GAAGhB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEI,SAAS,CAACI,OAAO,CAACK,QAAQ,CAACI,CAAC,GAAGpB,eAAe,CAACoB,CAAC,GAAGjB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChO;EACF,CAAC,CAAC;EACF,OAAO,aAAaR,KAAK,CAAC0B,aAAa,CAAC,OAAO,EAAE3B,QAAQ,CAAC;IACxDW,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC;AAED,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}