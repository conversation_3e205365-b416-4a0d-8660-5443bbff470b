{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nconst GridMaterial = shaderMaterial({\n  cellSize: 0.5,\n  sectionSize: 1,\n  fadeDistance: 100,\n  fadeStrength: 1,\n  cellThickness: 0.5,\n  sectionThickness: 1,\n  cellColor: new THREE.Color(),\n  sectionColor: new THREE.Color(),\n  infiniteGrid: false,\n  followCamera: false\n}, /* glsl */\n`\n    varying vec3 worldPosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      worldPosition = position.xzy;\n      if (infiniteGrid) worldPosition *= 1.0 + fadeDistance;\n      if (followCamera) worldPosition.xz +=cameraPosition.xz;\n\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(worldPosition, 1.0);\n    }\n  `, /* glsl */\n`\n    varying vec3 worldPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = worldPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1. - thickness;\n      return 1.0 - min(line, 1.);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      float d = 1.0 - min(distance(cameraPosition.xz, worldPosition.xz) / fadeDistance, 1.);\n      vec3 color = mix(cellColor, sectionColor, min(1.,sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d,fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }\n  `);\nconst Grid = /*#__PURE__*/React.forwardRef(({\n  args,\n  cellColor = '#000000',\n  sectionColor = '#2080ff',\n  cellSize = 0.5,\n  sectionSize = 1,\n  followCamera = false,\n  infiniteGrid = false,\n  fadeDistance = 100,\n  fadeStrength = 1,\n  cellThickness = 0.5,\n  sectionThickness = 1,\n  side = THREE.BackSide,\n  ...props\n}, fRef) => {\n  extend({\n    GridMaterial\n  });\n  const uniforms1 = {\n    cellSize,\n    sectionSize,\n    cellColor,\n    sectionColor,\n    cellThickness,\n    sectionThickness\n  };\n  const uniforms2 = {\n    fadeDistance,\n    fadeStrength,\n    infiniteGrid,\n    followCamera\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: fRef,\n    frustumCulled: false\n  }, props), /*#__PURE__*/React.createElement(\"gridMaterial\", _extends({\n    transparent: true,\n    \"extensions-derivatives\": true,\n    side: side\n  }, uniforms1, uniforms2)), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args\n  }));\n});\nexport { Grid };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "extend", "shaderMaterial", "GridMaterial", "cellSize", "sectionSize", "fadeDistance", "fadeStrength", "cellThickness", "sectionThickness", "cellColor", "Color", "sectionColor", "infiniteGrid", "followCamera", "Grid", "forwardRef", "args", "side", "BackSide", "props", "fRef", "uniforms1", "uniforms2", "createElement", "ref", "frustumCulled", "transparent"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Grid.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\n\nconst GridMaterial = shaderMaterial({\n  cellSize: 0.5,\n  sectionSize: 1,\n  fadeDistance: 100,\n  fadeStrength: 1,\n  cellThickness: 0.5,\n  sectionThickness: 1,\n  cellColor: new THREE.Color(),\n  sectionColor: new THREE.Color(),\n  infiniteGrid: false,\n  followCamera: false\n},\n/* glsl */\n`\n    varying vec3 worldPosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      worldPosition = position.xzy;\n      if (infiniteGrid) worldPosition *= 1.0 + fadeDistance;\n      if (followCamera) worldPosition.xz +=cameraPosition.xz;\n\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(worldPosition, 1.0);\n    }\n  `,\n/* glsl */\n`\n    varying vec3 worldPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = worldPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1. - thickness;\n      return 1.0 - min(line, 1.);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      float d = 1.0 - min(distance(cameraPosition.xz, worldPosition.xz) / fadeDistance, 1.);\n      vec3 color = mix(cellColor, sectionColor, min(1.,sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d,fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }\n  `);\nconst Grid = /*#__PURE__*/React.forwardRef(({\n  args,\n  cellColor = '#000000',\n  sectionColor = '#2080ff',\n  cellSize = 0.5,\n  sectionSize = 1,\n  followCamera = false,\n  infiniteGrid = false,\n  fadeDistance = 100,\n  fadeStrength = 1,\n  cellThickness = 0.5,\n  sectionThickness = 1,\n  side = THREE.BackSide,\n  ...props\n}, fRef) => {\n  extend({\n    GridMaterial\n  });\n  const uniforms1 = {\n    cellSize,\n    sectionSize,\n    cellColor,\n    sectionColor,\n    cellThickness,\n    sectionThickness\n  };\n  const uniforms2 = {\n    fadeDistance,\n    fadeStrength,\n    infiniteGrid,\n    followCamera\n  };\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: fRef,\n    frustumCulled: false\n  }, props), /*#__PURE__*/React.createElement(\"gridMaterial\", _extends({\n    transparent: true,\n    \"extensions-derivatives\": true,\n    side: side\n  }, uniforms1, uniforms2)), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args\n  }));\n});\n\nexport { Grid };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,MAAMC,YAAY,GAAGD,cAAc,CAAC;EAClCE,QAAQ,EAAE,GAAG;EACbC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,GAAG;EACjBC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAE,GAAG;EAClBC,gBAAgB,EAAE,CAAC;EACnBC,SAAS,EAAE,IAAIV,KAAK,CAACW,KAAK,CAAC,CAAC;EAC5BC,YAAY,EAAE,IAAIZ,KAAK,CAACW,KAAK,CAAC,CAAC;EAC/BE,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE;AAChB,CAAC,EACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;AACJ,MAAMC,IAAI,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,CAAC;EAC1CC,IAAI;EACJP,SAAS,GAAG,SAAS;EACrBE,YAAY,GAAG,SAAS;EACxBR,QAAQ,GAAG,GAAG;EACdC,WAAW,GAAG,CAAC;EACfS,YAAY,GAAG,KAAK;EACpBD,YAAY,GAAG,KAAK;EACpBP,YAAY,GAAG,GAAG;EAClBC,YAAY,GAAG,CAAC;EAChBC,aAAa,GAAG,GAAG;EACnBC,gBAAgB,GAAG,CAAC;EACpBS,IAAI,GAAGlB,KAAK,CAACmB,QAAQ;EACrB,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACVpB,MAAM,CAAC;IACLE;EACF,CAAC,CAAC;EACF,MAAMmB,SAAS,GAAG;IAChBlB,QAAQ;IACRC,WAAW;IACXK,SAAS;IACTE,YAAY;IACZJ,aAAa;IACbC;EACF,CAAC;EACD,MAAMc,SAAS,GAAG;IAChBjB,YAAY;IACZC,YAAY;IACZM,YAAY;IACZC;EACF,CAAC;EACD,OAAO,aAAaf,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAE1B,QAAQ,CAAC;IACvD2B,GAAG,EAAEJ,IAAI;IACTK,aAAa,EAAE;EACjB,CAAC,EAAEN,KAAK,CAAC,EAAE,aAAarB,KAAK,CAACyB,aAAa,CAAC,cAAc,EAAE1B,QAAQ,CAAC;IACnE6B,WAAW,EAAE,IAAI;IACjB,wBAAwB,EAAE,IAAI;IAC9BT,IAAI,EAAEA;EACR,CAAC,EAAEI,SAAS,EAAEC,SAAS,CAAC,CAAC,EAAE,aAAaxB,KAAK,CAACyB,aAAa,CAAC,eAAe,EAAE;IAC3EP,IAAI,EAAEA;EACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}