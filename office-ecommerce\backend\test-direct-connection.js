const http = require('http');

console.log('🔍 Testing direct HTTP connection to localhost:8001...');

const options = {
  hostname: 'localhost',
  port: 8001,
  path: '/health',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  console.log(`✅ Response status: ${res.statusCode}`);
  console.log(`✅ Response headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('✅ Response body:', data);
  });
});

req.on('error', (error) => {
  console.error('❌ Request error:', error.message);
  console.error('❌ Error code:', error.code);
});

req.on('timeout', () => {
  console.error('❌ Request timeout');
  req.destroy();
});

req.setTimeout(5000);
req.end();
