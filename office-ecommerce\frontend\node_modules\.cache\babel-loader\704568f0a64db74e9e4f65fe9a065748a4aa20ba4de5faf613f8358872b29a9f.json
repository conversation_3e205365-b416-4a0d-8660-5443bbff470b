{"ast": null, "code": "import { LineSegments2 } from \"./LineSegments2.js\";\nimport { LineGeometry } from \"./LineGeometry.js\";\nimport { LineMaterial } from \"./LineMaterial.js\";\nclass Line2 extends LineSegments2 {\n  constructor(geometry = new LineGeometry(), material = new LineMaterial({\n    color: Math.random() * 16777215\n  })) {\n    super(geometry, material);\n    this.isLine2 = true;\n    this.type = \"Line2\";\n  }\n}\nexport { Line2 };", "map": {"version": 3, "names": ["Line2", "LineSegments2", "constructor", "geometry", "LineGeometry", "material", "LineMaterial", "color", "Math", "random", "isLine2", "type"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\lines\\Line2.js"], "sourcesContent": ["import { LineSegments2 } from '../lines/LineSegments2'\nimport { LineGeometry } from '../lines/LineGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\n\nclass Line2 extends LineSegments2 {\n  constructor(geometry = new LineGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLine2 = true\n\n    this.type = 'Line2'\n  }\n}\n\nexport { Line2 }\n"], "mappings": ";;;AAIA,MAAMA,KAAA,SAAcC,aAAA,CAAc;EAChCC,YAAYC,QAAA,GAAW,IAAIC,YAAA,CAAc,GAAEC,QAAA,GAAW,IAAIC,YAAA,CAAa;IAAEC,KAAA,EAAOC,IAAA,CAAKC,MAAA,KAAW;EAAU,IAAG;IAC3G,MAAMN,QAAA,EAAUE,QAAQ;IAExB,KAAKK,OAAA,GAAU;IAEf,KAAKC,IAAA,GAAO;EACb;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}