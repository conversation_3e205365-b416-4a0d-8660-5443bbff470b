{"ast": null, "code": "import * as React from \"react\";\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar _a, _b;\nconst useIsomorphicLayoutEffect = typeof window !== \"undefined\" && (((_a = window.document) == null ? void 0 : _a.createElement) || ((_b = window.navigator) == null ? void 0 : _b.product) === \"ReactNative\") ? React.useLayoutEffect : React.useEffect;\nfunction traverseFiber(fiber, ascending, selector) {\n  if (!fiber) return;\n  if (selector(fiber) === true) return fiber;\n  let child = ascending ? fiber.return : fiber.child;\n  while (child) {\n    const match = traverseFiber(child, ascending, selector);\n    if (match) return match;\n    child = ascending ? null : child.sibling;\n  }\n}\nfunction wrapContext(context) {\n  try {\n    return Object.defineProperties(context, {\n      _currentRenderer: {\n        get() {\n          return null;\n        },\n        set() {}\n      },\n      _currentRenderer2: {\n        get() {\n          return null;\n        },\n        set() {}\n      }\n    });\n  } catch (_) {\n    return context;\n  }\n}\nconst error = console.error;\nconsole.error = function () {\n  const message = [...arguments].join(\"\");\n  if ((message == null ? void 0 : message.startsWith(\"Warning:\")) && message.includes(\"useContext\")) {\n    console.error = error;\n    return;\n  }\n  return error.apply(this, arguments);\n};\nconst FiberContext = wrapContext(React.createContext(null));\nclass FiberProvider extends React.Component {\n  render() {\n    return /* @__PURE__ */React.createElement(FiberContext.Provider, {\n      value: this._reactInternals\n    }, this.props.children);\n  }\n}\nfunction useFiber() {\n  const root = React.useContext(FiberContext);\n  if (root === null) throw new Error(\"its-fine: useFiber must be called within a <FiberProvider />!\");\n  const id = React.useId();\n  const fiber = React.useMemo(() => {\n    for (const maybeFiber of [root, root == null ? void 0 : root.alternate]) {\n      if (!maybeFiber) continue;\n      const fiber2 = traverseFiber(maybeFiber, false, node => {\n        let state = node.memoizedState;\n        while (state) {\n          if (state.memoizedState === id) return true;\n          state = state.next;\n        }\n      });\n      if (fiber2) return fiber2;\n    }\n  }, [root, id]);\n  return fiber;\n}\nfunction useContainer() {\n  const fiber = useFiber();\n  const root = React.useMemo(() => traverseFiber(fiber, true, node => {\n    var _a2;\n    return ((_a2 = node.stateNode) == null ? void 0 : _a2.containerInfo) != null;\n  }), [fiber]);\n  return root == null ? void 0 : root.stateNode.containerInfo;\n}\nfunction useNearestChild(type) {\n  const fiber = useFiber();\n  const childRef = React.useRef();\n  useIsomorphicLayoutEffect(() => {\n    var _a2;\n    childRef.current = (_a2 = traverseFiber(fiber, false, node => typeof node.type === \"string\" && (type === void 0 || node.type === type))) == null ? void 0 : _a2.stateNode;\n  }, [fiber]);\n  return childRef;\n}\nfunction useNearestParent(type) {\n  const fiber = useFiber();\n  const parentRef = React.useRef();\n  useIsomorphicLayoutEffect(() => {\n    var _a2;\n    parentRef.current = (_a2 = traverseFiber(fiber, true, node => typeof node.type === \"string\" && (type === void 0 || node.type === type))) == null ? void 0 : _a2.stateNode;\n  }, [fiber]);\n  return parentRef;\n}\nfunction useContextMap() {\n  const fiber = useFiber();\n  const [contextMap] = React.useState(() => /* @__PURE__ */new Map());\n  contextMap.clear();\n  let node = fiber;\n  while (node) {\n    if (node.type && typeof node.type === \"object\") {\n      const enableRenderableContext = node.type._context === void 0 && node.type.Provider === node.type;\n      const context = enableRenderableContext ? node.type : node.type._context;\n      if (context && context !== FiberContext && !contextMap.has(context)) {\n        contextMap.set(context, React.useContext(wrapContext(context)));\n      }\n    }\n    node = node.return;\n  }\n  return contextMap;\n}\nfunction useContextBridge() {\n  const contextMap = useContextMap();\n  return React.useMemo(() => Array.from(contextMap.keys()).reduce((Prev, context) => props => /* @__PURE__ */React.createElement(Prev, null, /* @__PURE__ */React.createElement(context.Provider, __spreadProps(__spreadValues({}, props), {\n    value: contextMap.get(context)\n  }))), props => /* @__PURE__ */React.createElement(FiberProvider, __spreadValues({}, props))), [contextMap]);\n}\nexport { FiberProvider, traverseFiber, useContainer, useContextBridge, useContextMap, useFiber, useNearestChild, useNearestParent };", "map": {"version": 3, "names": ["_a", "_b", "useIsomorphicLayoutEffect", "window", "document", "createElement", "navigator", "product", "React", "useLayoutEffect", "useEffect", "traverseFiber", "fiber", "ascending", "selector", "child", "return", "match", "sibling", "wrapContext", "context", "Object", "defineProperties", "_current<PERSON><PERSON><PERSON>", "get", "set", "_currentRenderer2", "_", "error", "console", "message", "arguments", "join", "startsWith", "includes", "apply", "FiberContext", "createContext", "FiberProvider", "Component", "render", "Provider", "value", "_reactInternals", "props", "children", "useFiber", "root", "useContext", "Error", "id", "useId", "useMemo", "maybeFiber", "alternate", "fiber2", "node", "state", "memoizedState", "next", "useContainer", "_a2", "stateNode", "containerInfo", "useNearestChild", "type", "childRef", "useRef", "current", "useNearestParent", "parentRef", "useContextMap", "contextMap", "useState", "Map", "clear", "enableRenderableContext", "_context", "has", "useContextBridge", "Array", "from", "keys", "reduce", "Prev", "__spreadProps", "__spreadValues"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\its-fine\\src\\index.tsx"], "sourcesContent": ["import * as React from 'react'\r\nimport type <PERSON>actR<PERSON>onciler from 'react-reconciler'\r\n\r\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * <PERSON>act currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\r\nconst useIsomorphicLayoutEffect =\r\n  typeof window !== 'undefined' && (window.document?.createElement || window.navigator?.product === 'ReactNative')\r\n    ? React.useLayoutEffect\r\n    : React.useEffect\r\n\r\n/**\r\n * Represents a react-internal Fiber node.\r\n */\r\nexport type Fiber<T = any> = Omit<ReactReconciler.Fiber, 'stateNode'> & { stateNode: T }\r\n\r\n/**\r\n * Represents a {@link Fiber} node selector for traversal.\r\n */\r\nexport type FiberSelector<T = any> = (\r\n  /** The current {@link Fiber} node. */\r\n  node: Fiber<T | null>,\r\n) => boolean | void\r\n\r\n/**\r\n * Traverses up or down a {@link Fiber}, return `true` to stop and select a node.\r\n */\r\nexport function traverseFiber<T = any>(\r\n  /** Input {@link Fiber} to traverse. */\r\n  fiber: Fiber | undefined,\r\n  /** Whether to ascend and walk up the tree. Will walk down if `false`. */\r\n  ascending: boolean,\r\n  /** A {@link Fiber} node selector, returns the first match when `true` is passed. */\r\n  selector: FiberSelector<T>,\r\n): Fiber<T> | undefined {\r\n  if (!fiber) return\r\n  if (selector(fiber) === true) return fiber\r\n\r\n  let child = ascending ? fiber.return : fiber.child\r\n  while (child) {\r\n    const match = traverseFiber(child, ascending, selector)\r\n    if (match) return match\r\n\r\n    child = ascending ? null : child.sibling\r\n  }\r\n}\r\n\r\n// In development, React will warn about using contexts between renderers.\r\n// Hide the warning because its-fine fixes this issue\r\n// https://github.com/facebook/react/pull/12779\r\nfunction wrapContext<T>(context: React.Context<T>): React.Context<T> {\r\n  try {\r\n    return Object.defineProperties(context, {\r\n      _currentRenderer: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n      _currentRenderer2: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n    })\r\n  } catch (_) {\r\n    return context\r\n  }\r\n}\r\n\r\nconst error = console.error\r\nconsole.error = function () {\r\n  const message = [...arguments].join('')\r\n  if (message?.startsWith('Warning:') && message.includes('useContext')) {\r\n    console.error = error\r\n    return\r\n  }\r\n\r\n  return error.apply(this, arguments as any)\r\n}\r\n\r\nconst FiberContext = wrapContext(React.createContext<Fiber>(null!))\r\n\r\n/**\r\n * A react-internal {@link Fiber} provider. This component binds React children to the React Fiber tree. Call its-fine hooks within this.\r\n */\r\nexport class FiberProvider extends React.Component<{ children?: React.ReactNode }> {\r\n  private _reactInternals!: Fiber\r\n\r\n  render() {\r\n    return <FiberContext.Provider value={this._reactInternals}>{this.props.children}</FiberContext.Provider>\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the current react-internal {@link Fiber}. This is an implementation detail of [react-reconciler](https://github.com/facebook/react/tree/main/packages/react-reconciler).\r\n */\r\nexport function useFiber(): Fiber<null> | undefined {\r\n  const root = React.useContext(FiberContext)\r\n  if (root === null) throw new Error('its-fine: useFiber must be called within a <FiberProvider />!')\r\n\r\n  const id = React.useId()\r\n  const fiber = React.useMemo(() => {\r\n    for (const maybeFiber of [root, root?.alternate]) {\r\n      if (!maybeFiber) continue\r\n      const fiber = traverseFiber<null>(maybeFiber, false, (node) => {\r\n        let state = node.memoizedState\r\n        while (state) {\r\n          if (state.memoizedState === id) return true\r\n          state = state.next\r\n        }\r\n      })\r\n      if (fiber) return fiber\r\n    }\r\n  }, [root, id])\r\n\r\n  return fiber\r\n}\r\n\r\n/**\r\n * Represents a react-reconciler container instance.\r\n */\r\nexport interface ContainerInstance<T = any> {\r\n  containerInfo: T\r\n}\r\n\r\n/**\r\n * Returns the current react-reconciler container info passed to {@link ReactReconciler.Reconciler.createContainer}.\r\n *\r\n * In react-dom, a container will point to the root DOM element; in react-three-fiber, it will point to the root Zustand store.\r\n */\r\nexport function useContainer<T = any>(): T | undefined {\r\n  const fiber = useFiber()\r\n  const root = React.useMemo(\r\n    () => traverseFiber<ContainerInstance<T>>(fiber, true, (node) => node.stateNode?.containerInfo != null),\r\n    [fiber],\r\n  )\r\n\r\n  return root?.stateNode.containerInfo\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler child instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestChild<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof JSX.IntrinsicElements,\r\n): React.MutableRefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const childRef = React.useRef<T>()\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    childRef.current = traverseFiber<T>(\r\n      fiber,\r\n      false,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return childRef\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler parent instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestParent<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof JSX.IntrinsicElements,\r\n): React.MutableRefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const parentRef = React.useRef<T>()\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    parentRef.current = traverseFiber<T>(\r\n      fiber,\r\n      true,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return parentRef\r\n}\r\n\r\nexport type ContextMap = Map<React.Context<any>, any> & {\r\n  get<T>(context: React.Context<T>): T | undefined\r\n}\r\n\r\n/**\r\n * Returns a map of all contexts and their values.\r\n */\r\nexport function useContextMap(): ContextMap {\r\n  const fiber = useFiber()\r\n  const [contextMap] = React.useState(() => new Map<React.Context<any>, any>())\r\n\r\n  // Collect live context\r\n  contextMap.clear()\r\n  let node = fiber\r\n  while (node) {\r\n    if (node.type && typeof node.type === 'object') {\r\n      // https://github.com/facebook/react/pull/28226\r\n      const enableRenderableContext = node.type._context === undefined && node.type.Provider === node.type\r\n      const context = enableRenderableContext ? node.type : node.type._context\r\n      if (context && context !== FiberContext && !contextMap.has(context)) {\r\n        contextMap.set(context, React.useContext(wrapContext(context)))\r\n      }\r\n    }\r\n\r\n    node = node.return!\r\n  }\r\n\r\n  return contextMap\r\n}\r\n\r\n/**\r\n * Represents a react-context bridge provider component.\r\n */\r\nexport type ContextBridge = React.FC<React.PropsWithChildren<{}>>\r\n\r\n/**\r\n * React Context currently cannot be shared across [React renderers](https://reactjs.org/docs/codebase-overview.html#renderers) but explicitly forwarded between providers (see [react#17275](https://github.com/facebook/react/issues/17275)). This hook returns a {@link ContextBridge} of live context providers to pierce Context across renderers.\r\n *\r\n * Pass {@link ContextBridge} as a component to a secondary renderer to enable context-sharing within its children.\r\n */\r\nexport function useContextBridge(): ContextBridge {\r\n  const contextMap = useContextMap()\r\n\r\n  // Flatten context and their memoized values into a `ContextBridge` provider\r\n  return React.useMemo(\r\n    () =>\r\n      Array.from(contextMap.keys()).reduce(\r\n        (Prev, context) => (props) =>\r\n          (\r\n            <Prev>\r\n              <context.Provider {...props} value={contextMap.get(context)} />\r\n            </Prev>\r\n          ),\r\n        (props) => <FiberProvider {...props} />,\r\n      ),\r\n    [contextMap],\r\n  )\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,EAAA,EAAAC,EAAA;AAYA,MAAMC,yBAAA,GACJ,OAAOC,MAAA,KAAW,kBAAgBH,EAAA,GAAAG,MAAA,CAAOC,QAAA,KAAP,gBAAAJ,EAAA,CAAiBK,aAAA,OAAiBJ,EAAA,GAAAE,MAAA,CAAOG,SAAA,KAAP,OAAkB,SAAAL,EAAA,CAAAM,OAAA,MAAY,iBAC9FC,KAAA,CAAMC,eAAA,GACND,KAAA,CAAME,SAAA;AAkBI,SAAAC,cAEdC,KAAA,EAEAC,SAAA,EAEAC,QAAA,EACsB;EACtB,IAAI,CAACF,KAAA,EAAO;EACR,IAAAE,QAAA,CAASF,KAAK,MAAM,MAAa,OAAAA,KAAA;EAErC,IAAIG,KAAA,GAAQF,SAAA,GAAYD,KAAA,CAAMI,MAAA,GAASJ,KAAA,CAAMG,KAAA;EAC7C,OAAOA,KAAA,EAAO;IACZ,MAAME,KAAA,GAAQN,aAAA,CAAcI,KAAA,EAAOF,SAAA,EAAWC,QAAQ;IAClD,IAAAG,KAAA,EAAc,OAAAA,KAAA;IAEVF,KAAA,GAAAF,SAAA,GAAY,OAAOE,KAAA,CAAMG,OAAA;EACnC;AACF;AAKA,SAASC,YAAeC,OAAA,EAA6C;EAC/D;IACK,OAAAC,MAAA,CAAOC,gBAAA,CAAiBF,OAAA,EAAS;MACtCG,gBAAA,EAAkB;QAChBC,IAAA,EAAM;UACG;QACT;QACAC,IAAA,EAAM,CAAC;MACT;MACAC,iBAAA,EAAmB;QACjBF,IAAA,EAAM;UACG;QACT;QACAC,IAAA,EAAM,CAAC;MACT;IAAA,CACD;EAAA,SACME,CAAA;IACA,OAAAP,OAAA;EACT;AACF;AAEA,MAAMQ,KAAA,GAAQC,OAAA,CAAQD,KAAA;AACtBC,OAAA,CAAQD,KAAA,GAAQ,YAAY;EAC1B,MAAME,OAAA,GAAU,CAAC,GAAGC,SAAS,EAAEC,IAAA,CAAK,EAAE;EACtC,KAAIF,OAAA,oBAAAA,OAAA,CAASG,UAAA,CAAW,gBAAeH,OAAA,CAAQI,QAAA,CAAS,YAAY,GAAG;IACrEL,OAAA,CAAQD,KAAA,GAAQA,KAAA;IAChB;EACF;EAEO,OAAAA,KAAA,CAAMO,KAAA,CAAM,MAAMJ,SAAgB;AAC3C;AAEA,MAAMK,YAAA,GAAejB,WAAA,CAAYX,KAAA,CAAM6B,aAAA,CAAqB,IAAK,CAAC;AAKrD,MAAAC,aAAA,SAAsB9B,KAAA,CAAM+B,SAAA,CAA0C;EAGjFC,OAAA,EAAS;IACA,sBAAAhC,KAAA,CAAAH,aAAA,CAAC+B,YAAA,CAAaK,QAAA,EAAb;MAAsBC,KAAA,EAAO,KAAKC;IAAA,GAAkB,KAAKC,KAAA,CAAMC,QAAS;EAClF;AACF;AAKO,SAASC,SAAA,EAAoC;EAC5C,MAAAC,IAAA,GAAOvC,KAAA,CAAMwC,UAAA,CAAWZ,YAAY;EAC1C,IAAIW,IAAA,KAAS,MAAY,UAAIE,KAAA,CAAM,+DAA+D;EAE5F,MAAAC,EAAA,GAAK1C,KAAA,CAAM2C,KAAA;EACX,MAAAvC,KAAA,GAAQJ,KAAA,CAAM4C,OAAA,CAAQ,MAAM;IAChC,WAAWC,UAAA,IAAc,CAACN,IAAA,EAAMA,IAAA,oBAAAA,IAAA,CAAMO,SAAS,GAAG;MAChD,IAAI,CAACD,UAAA,EAAY;MACjB,MAAME,MAAA,GAAQ5C,aAAA,CAAoB0C,UAAA,EAAY,OAAQG,IAAA,IAAS;QAC7D,IAAIC,KAAA,GAAQD,IAAA,CAAKE,aAAA;QACjB,OAAOD,KAAA,EAAO;UACZ,IAAIA,KAAA,CAAMC,aAAA,KAAkBR,EAAA,EAAW;UACvCO,KAAA,GAAQA,KAAA,CAAME,IAAA;QAChB;MAAA,CACD;MACG,IAAAJ,MAAA,EAAc,OAAAA,MAAA;IACpB;EAAA,GACC,CAACR,IAAA,EAAMG,EAAE,CAAC;EAEN,OAAAtC,KAAA;AACT;AAcO,SAASgD,aAAA,EAAuC;EACrD,MAAMhD,KAAA,GAAQkC,QAAA;EACd,MAAMC,IAAA,GAAOvC,KAAA,CAAM4C,OAAA,CACjB,MAAMzC,aAAA,CAAoCC,KAAA,EAAO,MAAO4C,IAAA,IAAM;IA7IlE,IAAAK,GAAA;IA6IqE,SAAAA,GAAA,GAAAL,IAAA,CAAKM,SAAA,KAAL,gBAAAD,GAAA,CAAgBE,aAAA,KAAiB;EAAA,CAAI,GACtG,CAACnD,KAAK;EAGR,OAAOmC,IAAA,oBAAAA,IAAA,CAAMe,SAAA,CAAUC,aAAA;AACzB;AAOO,SAASC,gBAEdC,IAAA,EACuC;EACvC,MAAMrD,KAAA,GAAQkC,QAAA;EACR,MAAAoB,QAAA,GAAW1D,KAAA,CAAM2D,MAAA;EAEvBjE,yBAAA,CAA0B,MAAM;IAhKlC,IAAA2D,GAAA;IAiKIK,QAAA,CAASE,OAAA,IAAUP,GAAA,GAAAlD,aAAA,CACjBC,KAAA,EACA,OACC4C,IAAA,IAAS,OAAOA,IAAA,CAAKS,IAAA,KAAS,aAAaA,IAAA,KAAS,UAAaT,IAAA,CAAKS,IAAA,KAASA,IAAA,CAClF,MAJmB,gBAAAJ,GAAA,CAIhBC,SAAA;EAAA,GACF,CAAClD,KAAK,CAAC;EAEH,OAAAsD,QAAA;AACT;AAOO,SAASG,iBAEdJ,IAAA,EACuC;EACvC,MAAMrD,KAAA,GAAQkC,QAAA;EACR,MAAAwB,SAAA,GAAY9D,KAAA,CAAM2D,MAAA;EAExBjE,yBAAA,CAA0B,MAAM;IAvLlC,IAAA2D,GAAA;IAwLIS,SAAA,CAAUF,OAAA,IAAUP,GAAA,GAAAlD,aAAA,CAClBC,KAAA,EACA,MACC4C,IAAA,IAAS,OAAOA,IAAA,CAAKS,IAAA,KAAS,aAAaA,IAAA,KAAS,UAAaT,IAAA,CAAKS,IAAA,KAASA,IAAA,CAClF,MAJoB,gBAAAJ,GAAA,CAIjBC,SAAA;EAAA,GACF,CAAClD,KAAK,CAAC;EAEH,OAAA0D,SAAA;AACT;AASO,SAASC,cAAA,EAA4B;EAC1C,MAAM3D,KAAA,GAAQkC,QAAA;EACR,OAAC0B,UAAU,IAAIhE,KAAA,CAAMiE,QAAA,CAAS,MAAM,mBAAIC,GAAA,EAA8B;EAG5EF,UAAA,CAAWG,KAAA,CAAM;EACjB,IAAInB,IAAA,GAAO5C,KAAA;EACX,OAAO4C,IAAA,EAAM;IACX,IAAIA,IAAA,CAAKS,IAAA,IAAQ,OAAOT,IAAA,CAAKS,IAAA,KAAS,UAAU;MAExC,MAAAW,uBAAA,GAA0BpB,IAAA,CAAKS,IAAA,CAAKY,QAAA,KAAa,UAAarB,IAAA,CAAKS,IAAA,CAAKxB,QAAA,KAAae,IAAA,CAAKS,IAAA;MAChG,MAAM7C,OAAA,GAAUwD,uBAAA,GAA0BpB,IAAA,CAAKS,IAAA,GAAOT,IAAA,CAAKS,IAAA,CAAKY,QAAA;MAChE,IAAIzD,OAAA,IAAWA,OAAA,KAAYgB,YAAA,IAAgB,CAACoC,UAAA,CAAWM,GAAA,CAAI1D,OAAO,GAAG;QACnEoD,UAAA,CAAW/C,GAAA,CAAIL,OAAA,EAASZ,KAAA,CAAMwC,UAAA,CAAW7B,WAAA,CAAYC,OAAO,CAAC,CAAC;MAChE;IACF;IAEAoC,IAAA,GAAOA,IAAA,CAAKxC,MAAA;EACd;EAEO,OAAAwD,UAAA;AACT;AAYO,SAASO,iBAAA,EAAkC;EAChD,MAAMP,UAAA,GAAaD,aAAA;EAGnB,OAAO/D,KAAA,CAAM4C,OAAA,CACX,MACE4B,KAAA,CAAMC,IAAA,CAAKT,UAAA,CAAWU,IAAA,EAAM,EAAEC,MAAA,CAC5B,CAACC,IAAA,EAAMhE,OAAA,KAAawB,KAAA,IAEf,eAAApC,KAAA,CAAAH,aAAA,CAAA+E,IAAA,QACE,eAAA5E,KAAA,CAAAH,aAAA,CAAAe,OAAA,CAAQqB,QAAA,EAAR4C,aAAA,CAAAC,cAAA,KAAqB1C,KAArB;IAA4BF,KAAA,EAAO8B,UAAA,CAAWhD,GAAA,CAAIJ,OAAO;EAAA,EAAG,CAC/D,GAEHwB,KAAA,IAAW,eAAApC,KAAA,CAAAH,aAAA,CAAAiC,aAAA,EAAAgD,cAAA,KAAkB1C,KAAO,EACvC,GACF,CAAC4B,UAAU;AAEf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}