{"ast": null, "code": "import { getGPUTier } from 'detect-gpu';\nimport { suspend } from 'suspend-react';\nconst useDetectGPU = props => suspend(() => getGPUTier(props), ['useDetectGPU']);\nexport { useDetectGPU };", "map": {"version": 3, "names": ["getGPUTier", "suspend", "useDetectGPU", "props"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useDetectGPU.js"], "sourcesContent": ["import { getGPUTier } from 'detect-gpu';\nimport { suspend } from 'suspend-react';\n\nconst useDetectGPU = props => suspend(() => getGPUTier(props), ['useDetectGPU']);\n\nexport { useDetectGPU };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,YAAY;AACvC,SAASC,OAAO,QAAQ,eAAe;AAEvC,MAAMC,YAAY,GAAGC,KAAK,IAAIF,OAAO,CAAC,MAAMD,UAAU,CAACG,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;AAEhF,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}