{"ast": null, "code": "function bidiFactory() {\n  var bidi = function (exports) {\n    // Bidi character types data, auto generated\n    var DATA = {\n      \"R\": \"13k,1a,2,3,3,2+1j,ch+16,a+1,5+2,2+n,5,a,4,6+16,4+3,h+1b,4mo,179q,2+9,2+11,2i9+7y,2+68,4,3+4,5+13,4+3,2+4k,3+29,8+cf,1t+7z,w+17,3+3m,1t+3z,16o1+5r,8+30,8+mc,29+1r,29+4v,75+73\",\n      \"EN\": \"1c+9,3d+1,6,187+9,513,4+5,7+9,sf+j,175h+9,qw+q,161f+1d,4xt+a,25i+9\",\n      \"ES\": \"17,2,6dp+1,f+1,av,16vr,mx+1,4o,2\",\n      \"ET\": \"z+2,3h+3,b+1,ym,3e+1,2o,p4+1,8,6u,7c,g6,1wc,1n9+4,30+1b,2n,6d,qhx+1,h0m,a+1,49+2,63+1,4+1,6bb+3,12jj\",\n      \"AN\": \"16o+5,2j+9,2+1,35,ed,1ff2+9,87+u\",\n      \"CS\": \"18,2+1,b,2u,12k,55v,l,17v0,2,3,53,2+1,b\",\n      \"B\": \"a,3,f+2,2v,690\",\n      \"S\": \"9,2,k\",\n      \"WS\": \"c,k,4f4,1vk+a,u,1j,335\",\n      \"ON\": \"x+1,4+4,h+5,r+5,r+3,z,5+3,2+1,2+1,5,2+2,3+4,o,w,ci+1,8+d,3+d,6+8,2+g,39+1,9,6+1,2,33,b8,3+1,3c+1,7+1,5r,b,7h+3,sa+5,2,3i+6,jg+3,ur+9,2v,ij+1,9g+9,7+a,8m,4+1,49+x,14u,2+2,c+2,e+2,e+2,e+1,i+n,e+e,2+p,u+2,e+2,36+1,2+3,2+1,b,2+2,6+5,2,2,2,h+1,5+4,6+3,3+f,16+2,5+3l,3+81,1y+p,2+40,q+a,m+13,2r+ch,2+9e,75+hf,3+v,2+2w,6e+5,f+6,75+2a,1a+p,2+2g,d+5x,r+b,6+3,4+o,g,6+1,6+2,2k+1,4,2j,5h+z,1m+1,1e+f,t+2,1f+e,d+3,4o+3,2s+1,w,535+1r,h3l+1i,93+2,2s,b+1,3l+x,2v,4g+3,21+3,kz+1,g5v+1,5a,j+9,n+v,2,3,2+8,2+1,3+2,2,3,46+1,4+4,h+5,r+5,r+a,3h+2,4+6,b+4,78,1r+24,4+c,4,1hb,ey+6,103+j,16j+c,1ux+7,5+g,fsh,jdq+1t,4,57+2e,p1,1m,1m,1m,1m,4kt+1,7j+17,5+2r,d+e,3+e,2+e,2+10,m+4,w,1n+5,1q,4z+5,4b+rb,9+c,4+c,4+37,d+2g,8+b,l+b,5+1j,9+9,7+13,9+t,3+1,27+3c,2+29,2+3q,d+d,3+4,4+2,6+6,a+o,8+6,a+2,e+6,16+42,2+1i\",\n      \"BN\": \"0+8,6+d,2s+5,2+p,e,4m9,1kt+2,2b+5,5+5,17q9+v,7k,6p+8,6+1,119d+3,440+7,96s+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+75,6p+2rz,1ben+1,1ekf+1,1ekf+1\",\n      \"NSM\": \"lc+33,7o+6,7c+18,2,2+1,2+1,2,21+a,1d+k,h,2u+6,3+5,3+1,2+3,10,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,g+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+g,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,k1+w,2db+2,3y,2p+v,ff+3,30+1,n9x+3,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,r2,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+5,3+1,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2d+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,f0c+4,1o+6,t5,1s+3,2a,f5l+1,43t+2,i+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,gzhy+6n\",\n      \"AL\": \"16w,3,2,e+1b,z+2,2+2s,g+1,8+1,b+m,2+t,s+2i,c+e,4h+f,1d+1e,1bwe+dp,3+3z,x+c,2+1,35+3y,2rm+z,5+7,b+5,dt+l,c+u,17nl+27,1t+27,4x+6n,3+d\",\n      \"LRO\": \"6ct\",\n      \"RLO\": \"6cu\",\n      \"LRE\": \"6cq\",\n      \"RLE\": \"6cr\",\n      \"PDF\": \"6cs\",\n      \"LRI\": \"6ee\",\n      \"RLI\": \"6ef\",\n      \"FSI\": \"6eg\",\n      \"PDI\": \"6eh\"\n    };\n    var TYPES = {};\n    var TYPES_TO_NAMES = {};\n    TYPES.L = 1; //L is the default\n    TYPES_TO_NAMES[1] = 'L';\n    Object.keys(DATA).forEach(function (type, i) {\n      TYPES[type] = 1 << i + 1;\n      TYPES_TO_NAMES[TYPES[type]] = type;\n    });\n    Object.freeze(TYPES);\n    var ISOLATE_INIT_TYPES = TYPES.LRI | TYPES.RLI | TYPES.FSI;\n    var STRONG_TYPES = TYPES.L | TYPES.R | TYPES.AL;\n    var NEUTRAL_ISOLATE_TYPES = TYPES.B | TYPES.S | TYPES.WS | TYPES.ON | TYPES.FSI | TYPES.LRI | TYPES.RLI | TYPES.PDI;\n    var BN_LIKE_TYPES = TYPES.BN | TYPES.RLE | TYPES.LRE | TYPES.RLO | TYPES.LRO | TYPES.PDF;\n    var TRAILING_TYPES = TYPES.S | TYPES.WS | TYPES.B | ISOLATE_INIT_TYPES | TYPES.PDI | BN_LIKE_TYPES;\n    var map = null;\n    function parseData() {\n      if (!map) {\n        //const start = performance.now()\n        map = new Map();\n        var loop = function (type) {\n          if (DATA.hasOwnProperty(type)) {\n            var lastCode = 0;\n            DATA[type].split(',').forEach(function (range) {\n              var ref = range.split('+');\n              var skip = ref[0];\n              var step = ref[1];\n              skip = parseInt(skip, 36);\n              step = step ? parseInt(step, 36) : 0;\n              map.set(lastCode += skip, TYPES[type]);\n              for (var i = 0; i < step; i++) {\n                map.set(++lastCode, TYPES[type]);\n              }\n            });\n          }\n        };\n        for (var type in DATA) loop(type);\n        //console.log(`char types parsed in ${performance.now() - start}ms`)\n      }\n    }\n\n    /**\n     * @param {string} char\n     * @return {number}\n     */\n    function getBidiCharType(char) {\n      parseData();\n      return map.get(char.codePointAt(0)) || TYPES.L;\n    }\n    function getBidiCharTypeName(char) {\n      return TYPES_TO_NAMES[getBidiCharType(char)];\n    }\n\n    // Bidi bracket pairs data, auto generated\n    var data$1 = {\n      \"pairs\": \"14>1,1e>2,u>2,2wt>1,1>1,1ge>1,1wp>1,1j>1,f>1,hm>1,1>1,u>1,u6>1,1>1,+5,28>1,w>1,1>1,+3,b8>1,1>1,+3,1>3,-1>-1,3>1,1>1,+2,1s>1,1>1,x>1,th>1,1>1,+2,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,4q>1,1e>2,u>2,2>1,+1\",\n      \"canonical\": \"6f1>-6dx,6dy>-6dx,6ec>-6ed,6ee>-6ed,6ww>2jj,-2ji>2jj,14r4>-1e7l,1e7m>-1e7l,1e7m>-1e5c,1e5d>-1e5b,1e5c>-14qx,14qy>-14qx,14vn>-1ecg,1ech>-1ecg,1edu>-1ecg,1eci>-1ecg,1eda>-1ecg,1eci>-1ecg,1eci>-168q,168r>-168q,168s>-14ye,14yf>-14ye\"\n    };\n\n    /**\n     * Parses an string that holds encoded codepoint mappings, e.g. for bracket pairs or\n     * mirroring characters, as encoded by scripts/generateBidiData.js. Returns an object\n     * holding the `map`, and optionally a `reverseMap` if `includeReverse:true`.\n     * @param {string} encodedString\n     * @param {boolean} includeReverse - true if you want reverseMap in the output\n     * @return {{map: Map<number, number>, reverseMap?: Map<number, number>}}\n     */\n    function parseCharacterMap(encodedString, includeReverse) {\n      var radix = 36;\n      var lastCode = 0;\n      var map = new Map();\n      var reverseMap = includeReverse && new Map();\n      var prevPair;\n      encodedString.split(',').forEach(function visit(entry) {\n        if (entry.indexOf('+') !== -1) {\n          for (var i = +entry; i--;) {\n            visit(prevPair);\n          }\n        } else {\n          prevPair = entry;\n          var ref = entry.split('>');\n          var a = ref[0];\n          var b = ref[1];\n          a = String.fromCodePoint(lastCode += parseInt(a, radix));\n          b = String.fromCodePoint(lastCode += parseInt(b, radix));\n          map.set(a, b);\n          includeReverse && reverseMap.set(b, a);\n        }\n      });\n      return {\n        map: map,\n        reverseMap: reverseMap\n      };\n    }\n    var openToClose, closeToOpen, canonical;\n    function parse$1() {\n      if (!openToClose) {\n        //const start = performance.now()\n        var ref = parseCharacterMap(data$1.pairs, true);\n        var map = ref.map;\n        var reverseMap = ref.reverseMap;\n        openToClose = map;\n        closeToOpen = reverseMap;\n        canonical = parseCharacterMap(data$1.canonical, false).map;\n        //console.log(`brackets parsed in ${performance.now() - start}ms`)\n      }\n    }\n    function openingToClosingBracket(char) {\n      parse$1();\n      return openToClose.get(char) || null;\n    }\n    function closingToOpeningBracket(char) {\n      parse$1();\n      return closeToOpen.get(char) || null;\n    }\n    function getCanonicalBracket(char) {\n      parse$1();\n      return canonical.get(char) || null;\n    }\n\n    // Local type aliases\n    var TYPE_L = TYPES.L;\n    var TYPE_R = TYPES.R;\n    var TYPE_EN = TYPES.EN;\n    var TYPE_ES = TYPES.ES;\n    var TYPE_ET = TYPES.ET;\n    var TYPE_AN = TYPES.AN;\n    var TYPE_CS = TYPES.CS;\n    var TYPE_B = TYPES.B;\n    var TYPE_S = TYPES.S;\n    var TYPE_ON = TYPES.ON;\n    var TYPE_BN = TYPES.BN;\n    var TYPE_NSM = TYPES.NSM;\n    var TYPE_AL = TYPES.AL;\n    var TYPE_LRO = TYPES.LRO;\n    var TYPE_RLO = TYPES.RLO;\n    var TYPE_LRE = TYPES.LRE;\n    var TYPE_RLE = TYPES.RLE;\n    var TYPE_PDF = TYPES.PDF;\n    var TYPE_LRI = TYPES.LRI;\n    var TYPE_RLI = TYPES.RLI;\n    var TYPE_FSI = TYPES.FSI;\n    var TYPE_PDI = TYPES.PDI;\n\n    /**\n     * @typedef {object} GetEmbeddingLevelsResult\n     * @property {{start, end, level}[]} paragraphs\n     * @property {Uint8Array} levels\n     */\n\n    /**\n     * This function applies the Bidirectional Algorithm to a string, returning the resolved embedding levels\n     * in a single Uint8Array plus a list of objects holding each paragraph's start and end indices and resolved\n     * base embedding level.\n     *\n     * @param {string} string - The input string\n     * @param {\"ltr\"|\"rtl\"|\"auto\"} [baseDirection] - Use \"ltr\" or \"rtl\" to force a base paragraph direction,\n     *        otherwise a direction will be chosen automatically from each paragraph's contents.\n     * @return {GetEmbeddingLevelsResult}\n     */\n    function getEmbeddingLevels(string, baseDirection) {\n      var MAX_DEPTH = 125;\n\n      // Start by mapping all characters to their unicode type, as a bitmask integer\n      var charTypes = new Uint32Array(string.length);\n      for (var i = 0; i < string.length; i++) {\n        charTypes[i] = getBidiCharType(string[i]);\n      }\n      var charTypeCounts = new Map(); //will be cleared at start of each paragraph\n      function changeCharType(i, type) {\n        var oldType = charTypes[i];\n        charTypes[i] = type;\n        charTypeCounts.set(oldType, charTypeCounts.get(oldType) - 1);\n        if (oldType & NEUTRAL_ISOLATE_TYPES) {\n          charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) - 1);\n        }\n        charTypeCounts.set(type, (charTypeCounts.get(type) || 0) + 1);\n        if (type & NEUTRAL_ISOLATE_TYPES) {\n          charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n        }\n      }\n      var embedLevels = new Uint8Array(string.length);\n      var isolationPairs = new Map(); //init->pdi and pdi->init\n\n      // === 3.3.1 The Paragraph Level ===\n      // 3.3.1 P1: Split the text into paragraphs\n      var paragraphs = []; // [{start, end, level}, ...]\n      var paragraph = null;\n      for (var i$1 = 0; i$1 < string.length; i$1++) {\n        if (!paragraph) {\n          paragraphs.push(paragraph = {\n            start: i$1,\n            end: string.length - 1,\n            // 3.3.1 P2-P3: Determine the paragraph level\n            level: baseDirection === 'rtl' ? 1 : baseDirection === 'ltr' ? 0 : determineAutoEmbedLevel(i$1, false)\n          });\n        }\n        if (charTypes[i$1] & TYPE_B) {\n          paragraph.end = i$1;\n          paragraph = null;\n        }\n      }\n      var FORMATTING_TYPES = TYPE_RLE | TYPE_LRE | TYPE_RLO | TYPE_LRO | ISOLATE_INIT_TYPES | TYPE_PDI | TYPE_PDF | TYPE_B;\n      var nextEven = function (n) {\n        return n + (n & 1 ? 1 : 2);\n      };\n      var nextOdd = function (n) {\n        return n + (n & 1 ? 2 : 1);\n      };\n\n      // Everything from here on will operate per paragraph.\n      for (var paraIdx = 0; paraIdx < paragraphs.length; paraIdx++) {\n        paragraph = paragraphs[paraIdx];\n        var statusStack = [{\n          _level: paragraph.level,\n          _override: 0,\n          //0=neutral, 1=L, 2=R\n          _isolate: 0 //bool\n        }];\n        var stackTop = void 0;\n        var overflowIsolateCount = 0;\n        var overflowEmbeddingCount = 0;\n        var validIsolateCount = 0;\n        charTypeCounts.clear();\n\n        // === 3.3.2 Explicit Levels and Directions ===\n        for (var i$2 = paragraph.start; i$2 <= paragraph.end; i$2++) {\n          var charType = charTypes[i$2];\n          stackTop = statusStack[statusStack.length - 1];\n\n          // Set initial counts\n          charTypeCounts.set(charType, (charTypeCounts.get(charType) || 0) + 1);\n          if (charType & NEUTRAL_ISOLATE_TYPES) {\n            charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n          }\n\n          // Explicit Embeddings: 3.3.2 X2 - X3\n          if (charType & FORMATTING_TYPES) {\n            //prefilter all formatters\n            if (charType & (TYPE_RLE | TYPE_LRE)) {\n              embedLevels[i$2] = stackTop._level; // 5.2\n              var level = (charType === TYPE_RLE ? nextOdd : nextEven)(stackTop._level);\n              if (level <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n                statusStack.push({\n                  _level: level,\n                  _override: 0,\n                  _isolate: 0\n                });\n              } else if (!overflowIsolateCount) {\n                overflowEmbeddingCount++;\n              }\n            }\n\n            // Explicit Overrides: 3.3.2 X4 - X5\n            else if (charType & (TYPE_RLO | TYPE_LRO)) {\n              embedLevels[i$2] = stackTop._level; // 5.2\n              var level$1 = (charType === TYPE_RLO ? nextOdd : nextEven)(stackTop._level);\n              if (level$1 <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n                statusStack.push({\n                  _level: level$1,\n                  _override: charType & TYPE_RLO ? TYPE_R : TYPE_L,\n                  _isolate: 0\n                });\n              } else if (!overflowIsolateCount) {\n                overflowEmbeddingCount++;\n              }\n            }\n\n            // Isolates: 3.3.2 X5a - X5c\n            else if (charType & ISOLATE_INIT_TYPES) {\n              // X5c - FSI becomes either RLI or LRI\n              if (charType & TYPE_FSI) {\n                charType = determineAutoEmbedLevel(i$2 + 1, true) === 1 ? TYPE_RLI : TYPE_LRI;\n              }\n              embedLevels[i$2] = stackTop._level;\n              if (stackTop._override) {\n                changeCharType(i$2, stackTop._override);\n              }\n              var level$2 = (charType === TYPE_RLI ? nextOdd : nextEven)(stackTop._level);\n              if (level$2 <= MAX_DEPTH && overflowIsolateCount === 0 && overflowEmbeddingCount === 0) {\n                validIsolateCount++;\n                statusStack.push({\n                  _level: level$2,\n                  _override: 0,\n                  _isolate: 1,\n                  _isolInitIndex: i$2\n                });\n              } else {\n                overflowIsolateCount++;\n              }\n            }\n\n            // Terminating Isolates: 3.3.2 X6a\n            else if (charType & TYPE_PDI) {\n              if (overflowIsolateCount > 0) {\n                overflowIsolateCount--;\n              } else if (validIsolateCount > 0) {\n                overflowEmbeddingCount = 0;\n                while (!statusStack[statusStack.length - 1]._isolate) {\n                  statusStack.pop();\n                }\n                // Add to isolation pairs bidirectional mapping:\n                var isolInitIndex = statusStack[statusStack.length - 1]._isolInitIndex;\n                if (isolInitIndex != null) {\n                  isolationPairs.set(isolInitIndex, i$2);\n                  isolationPairs.set(i$2, isolInitIndex);\n                }\n                statusStack.pop();\n                validIsolateCount--;\n              }\n              stackTop = statusStack[statusStack.length - 1];\n              embedLevels[i$2] = stackTop._level;\n              if (stackTop._override) {\n                changeCharType(i$2, stackTop._override);\n              }\n            }\n\n            // Terminating Embeddings and Overrides: 3.3.2 X7\n            else if (charType & TYPE_PDF) {\n              if (overflowIsolateCount === 0) {\n                if (overflowEmbeddingCount > 0) {\n                  overflowEmbeddingCount--;\n                } else if (!stackTop._isolate && statusStack.length > 1) {\n                  statusStack.pop();\n                  stackTop = statusStack[statusStack.length - 1];\n                }\n              }\n              embedLevels[i$2] = stackTop._level; // 5.2\n            }\n\n            // End of Paragraph: 3.3.2 X8\n            else if (charType & TYPE_B) {\n              embedLevels[i$2] = paragraph.level;\n            }\n          }\n\n          // Non-formatting characters: 3.3.2 X6\n          else {\n            embedLevels[i$2] = stackTop._level;\n            // NOTE: This exclusion of BN seems to go against what section 5.2 says, but is required for test passage\n            if (stackTop._override && charType !== TYPE_BN) {\n              changeCharType(i$2, stackTop._override);\n            }\n          }\n        }\n\n        // === 3.3.3 Preparations for Implicit Processing ===\n\n        // Remove all RLE, LRE, RLO, LRO, PDF, and BN characters: 3.3.3 X9\n        // Note: Due to section 5.2, we won't remove them, but we'll use the BN_LIKE_TYPES bitset to\n        // easily ignore them all from here on out.\n\n        // 3.3.3 X10\n        // Compute the set of isolating run sequences as specified by BD13\n        var levelRuns = [];\n        var currentRun = null;\n        for (var i$3 = paragraph.start; i$3 <= paragraph.end; i$3++) {\n          var charType$1 = charTypes[i$3];\n          if (!(charType$1 & BN_LIKE_TYPES)) {\n            var lvl = embedLevels[i$3];\n            var isIsolInit = charType$1 & ISOLATE_INIT_TYPES;\n            var isPDI = charType$1 === TYPE_PDI;\n            if (currentRun && lvl === currentRun._level) {\n              currentRun._end = i$3;\n              currentRun._endsWithIsolInit = isIsolInit;\n            } else {\n              levelRuns.push(currentRun = {\n                _start: i$3,\n                _end: i$3,\n                _level: lvl,\n                _startsWithPDI: isPDI,\n                _endsWithIsolInit: isIsolInit\n              });\n            }\n          }\n        }\n        var isolatingRunSeqs = []; // [{seqIndices: [], sosType: L|R, eosType: L|R}]\n        for (var runIdx = 0; runIdx < levelRuns.length; runIdx++) {\n          var run = levelRuns[runIdx];\n          if (!run._startsWithPDI || run._startsWithPDI && !isolationPairs.has(run._start)) {\n            var seqRuns = [currentRun = run];\n            for (var pdiIndex = void 0; currentRun && currentRun._endsWithIsolInit && (pdiIndex = isolationPairs.get(currentRun._end)) != null;) {\n              for (var i$4 = runIdx + 1; i$4 < levelRuns.length; i$4++) {\n                if (levelRuns[i$4]._start === pdiIndex) {\n                  seqRuns.push(currentRun = levelRuns[i$4]);\n                  break;\n                }\n              }\n            }\n            // build flat list of indices across all runs:\n            var seqIndices = [];\n            for (var i$5 = 0; i$5 < seqRuns.length; i$5++) {\n              var run$1 = seqRuns[i$5];\n              for (var j = run$1._start; j <= run$1._end; j++) {\n                seqIndices.push(j);\n              }\n            }\n            // determine the sos/eos types:\n            var firstLevel = embedLevels[seqIndices[0]];\n            var prevLevel = paragraph.level;\n            for (var i$6 = seqIndices[0] - 1; i$6 >= 0; i$6--) {\n              if (!(charTypes[i$6] & BN_LIKE_TYPES)) {\n                //5.2\n                prevLevel = embedLevels[i$6];\n                break;\n              }\n            }\n            var lastIndex = seqIndices[seqIndices.length - 1];\n            var lastLevel = embedLevels[lastIndex];\n            var nextLevel = paragraph.level;\n            if (!(charTypes[lastIndex] & ISOLATE_INIT_TYPES)) {\n              for (var i$7 = lastIndex + 1; i$7 <= paragraph.end; i$7++) {\n                if (!(charTypes[i$7] & BN_LIKE_TYPES)) {\n                  //5.2\n                  nextLevel = embedLevels[i$7];\n                  break;\n                }\n              }\n            }\n            isolatingRunSeqs.push({\n              _seqIndices: seqIndices,\n              _sosType: Math.max(prevLevel, firstLevel) % 2 ? TYPE_R : TYPE_L,\n              _eosType: Math.max(nextLevel, lastLevel) % 2 ? TYPE_R : TYPE_L\n            });\n          }\n        }\n\n        // The next steps are done per isolating run sequence\n        for (var seqIdx = 0; seqIdx < isolatingRunSeqs.length; seqIdx++) {\n          var ref = isolatingRunSeqs[seqIdx];\n          var seqIndices$1 = ref._seqIndices;\n          var sosType = ref._sosType;\n          var eosType = ref._eosType;\n          /**\n           * All the level runs in an isolating run sequence have the same embedding level.\n           * \n           * DO NOT change any `embedLevels[i]` within the current scope.\n           */\n          var embedDirection = embedLevels[seqIndices$1[0]] & 1 ? TYPE_R : TYPE_L;\n\n          // === 3.3.4 Resolving Weak Types ===\n\n          // W1 + 5.2. Search backward from each NSM to the first character in the isolating run sequence whose\n          // bidirectional type is not BN, and set the NSM to ON if it is an isolate initiator or PDI, and to its\n          // type otherwise. If the NSM is the first non-BN character, change the NSM to the type of sos.\n          if (charTypeCounts.get(TYPE_NSM)) {\n            for (var si = 0; si < seqIndices$1.length; si++) {\n              var i$8 = seqIndices$1[si];\n              if (charTypes[i$8] & TYPE_NSM) {\n                var prevType = sosType;\n                for (var sj = si - 1; sj >= 0; sj--) {\n                  if (!(charTypes[seqIndices$1[sj]] & BN_LIKE_TYPES)) {\n                    //5.2 scan back to first non-BN\n                    prevType = charTypes[seqIndices$1[sj]];\n                    break;\n                  }\n                }\n                changeCharType(i$8, prevType & (ISOLATE_INIT_TYPES | TYPE_PDI) ? TYPE_ON : prevType);\n              }\n            }\n          }\n\n          // W2. Search backward from each instance of a European number until the first strong type (R, L, AL, or sos)\n          // is found. If an AL is found, change the type of the European number to Arabic number.\n          if (charTypeCounts.get(TYPE_EN)) {\n            for (var si$1 = 0; si$1 < seqIndices$1.length; si$1++) {\n              var i$9 = seqIndices$1[si$1];\n              if (charTypes[i$9] & TYPE_EN) {\n                for (var sj$1 = si$1 - 1; sj$1 >= -1; sj$1--) {\n                  var prevCharType = sj$1 === -1 ? sosType : charTypes[seqIndices$1[sj$1]];\n                  if (prevCharType & STRONG_TYPES) {\n                    if (prevCharType === TYPE_AL) {\n                      changeCharType(i$9, TYPE_AN);\n                    }\n                    break;\n                  }\n                }\n              }\n            }\n          }\n\n          // W3. Change all ALs to R\n          if (charTypeCounts.get(TYPE_AL)) {\n            for (var si$2 = 0; si$2 < seqIndices$1.length; si$2++) {\n              var i$10 = seqIndices$1[si$2];\n              if (charTypes[i$10] & TYPE_AL) {\n                changeCharType(i$10, TYPE_R);\n              }\n            }\n          }\n\n          // W4. A single European separator between two European numbers changes to a European number. A single common\n          // separator between two numbers of the same type changes to that type.\n          if (charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n            for (var si$3 = 1; si$3 < seqIndices$1.length - 1; si$3++) {\n              var i$11 = seqIndices$1[si$3];\n              if (charTypes[i$11] & (TYPE_ES | TYPE_CS)) {\n                var prevType$1 = 0,\n                  nextType = 0;\n                for (var sj$2 = si$3 - 1; sj$2 >= 0; sj$2--) {\n                  prevType$1 = charTypes[seqIndices$1[sj$2]];\n                  if (!(prevType$1 & BN_LIKE_TYPES)) {\n                    //5.2\n                    break;\n                  }\n                }\n                for (var sj$3 = si$3 + 1; sj$3 < seqIndices$1.length; sj$3++) {\n                  nextType = charTypes[seqIndices$1[sj$3]];\n                  if (!(nextType & BN_LIKE_TYPES)) {\n                    //5.2\n                    break;\n                  }\n                }\n                if (prevType$1 === nextType && (charTypes[i$11] === TYPE_ES ? prevType$1 === TYPE_EN : prevType$1 & (TYPE_EN | TYPE_AN))) {\n                  changeCharType(i$11, prevType$1);\n                }\n              }\n            }\n          }\n\n          // W5. A sequence of European terminators adjacent to European numbers changes to all European numbers.\n          if (charTypeCounts.get(TYPE_EN)) {\n            for (var si$4 = 0; si$4 < seqIndices$1.length; si$4++) {\n              var i$12 = seqIndices$1[si$4];\n              if (charTypes[i$12] & TYPE_EN) {\n                for (var sj$4 = si$4 - 1; sj$4 >= 0 && charTypes[seqIndices$1[sj$4]] & (TYPE_ET | BN_LIKE_TYPES); sj$4--) {\n                  changeCharType(seqIndices$1[sj$4], TYPE_EN);\n                }\n                for (si$4++; si$4 < seqIndices$1.length && charTypes[seqIndices$1[si$4]] & (TYPE_ET | BN_LIKE_TYPES | TYPE_EN); si$4++) {\n                  if (charTypes[seqIndices$1[si$4]] !== TYPE_EN) {\n                    changeCharType(seqIndices$1[si$4], TYPE_EN);\n                  }\n                }\n              }\n            }\n          }\n\n          // W6. Otherwise, separators and terminators change to Other Neutral.\n          if (charTypeCounts.get(TYPE_ET) || charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n            for (var si$5 = 0; si$5 < seqIndices$1.length; si$5++) {\n              var i$13 = seqIndices$1[si$5];\n              if (charTypes[i$13] & (TYPE_ET | TYPE_ES | TYPE_CS)) {\n                changeCharType(i$13, TYPE_ON);\n                // 5.2 transform adjacent BNs too:\n                for (var sj$5 = si$5 - 1; sj$5 >= 0 && charTypes[seqIndices$1[sj$5]] & BN_LIKE_TYPES; sj$5--) {\n                  changeCharType(seqIndices$1[sj$5], TYPE_ON);\n                }\n                for (var sj$6 = si$5 + 1; sj$6 < seqIndices$1.length && charTypes[seqIndices$1[sj$6]] & BN_LIKE_TYPES; sj$6++) {\n                  changeCharType(seqIndices$1[sj$6], TYPE_ON);\n                }\n              }\n            }\n          }\n\n          // W7. Search backward from each instance of a European number until the first strong type (R, L, or sos)\n          // is found. If an L is found, then change the type of the European number to L.\n          // NOTE: implemented in single forward pass for efficiency\n          if (charTypeCounts.get(TYPE_EN)) {\n            for (var si$6 = 0, prevStrongType = sosType; si$6 < seqIndices$1.length; si$6++) {\n              var i$14 = seqIndices$1[si$6];\n              var type = charTypes[i$14];\n              if (type & TYPE_EN) {\n                if (prevStrongType === TYPE_L) {\n                  changeCharType(i$14, TYPE_L);\n                }\n              } else if (type & STRONG_TYPES) {\n                prevStrongType = type;\n              }\n            }\n          }\n\n          // === 3.3.5 Resolving Neutral and Isolate Formatting Types ===\n\n          if (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES)) {\n            // N0. Process bracket pairs in an isolating run sequence sequentially in the logical order of the text\n            // positions of the opening paired brackets using the logic given below. Within this scope, bidirectional\n            // types EN and AN are treated as R.\n            var R_TYPES_FOR_N_STEPS = TYPE_R | TYPE_EN | TYPE_AN;\n            var STRONG_TYPES_FOR_N_STEPS = R_TYPES_FOR_N_STEPS | TYPE_L;\n\n            // * Identify the bracket pairs in the current isolating run sequence according to BD16.\n            var bracketPairs = [];\n            {\n              var openerStack = [];\n              for (var si$7 = 0; si$7 < seqIndices$1.length; si$7++) {\n                // NOTE: for any potential bracket character we also test that it still carries a NI\n                // type, as that may have been changed earlier. This doesn't seem to be explicitly\n                // called out in the spec, but is required for passage of certain tests.\n                if (charTypes[seqIndices$1[si$7]] & NEUTRAL_ISOLATE_TYPES) {\n                  var char = string[seqIndices$1[si$7]];\n                  var oppositeBracket = void 0;\n                  // Opening bracket\n                  if (openingToClosingBracket(char) !== null) {\n                    if (openerStack.length < 63) {\n                      openerStack.push({\n                        char: char,\n                        seqIndex: si$7\n                      });\n                    } else {\n                      break;\n                    }\n                  }\n                  // Closing bracket\n                  else if ((oppositeBracket = closingToOpeningBracket(char)) !== null) {\n                    for (var stackIdx = openerStack.length - 1; stackIdx >= 0; stackIdx--) {\n                      var stackChar = openerStack[stackIdx].char;\n                      if (stackChar === oppositeBracket || stackChar === closingToOpeningBracket(getCanonicalBracket(char)) || openingToClosingBracket(getCanonicalBracket(stackChar)) === char) {\n                        bracketPairs.push([openerStack[stackIdx].seqIndex, si$7]);\n                        openerStack.length = stackIdx; //pop the matching bracket and all following\n                        break;\n                      }\n                    }\n                  }\n                }\n              }\n              bracketPairs.sort(function (a, b) {\n                return a[0] - b[0];\n              });\n            }\n            // * For each bracket-pair element in the list of pairs of text positions\n            for (var pairIdx = 0; pairIdx < bracketPairs.length; pairIdx++) {\n              var ref$1 = bracketPairs[pairIdx];\n              var openSeqIdx = ref$1[0];\n              var closeSeqIdx = ref$1[1];\n              // a. Inspect the bidirectional types of the characters enclosed within the bracket pair.\n              // b. If any strong type (either L or R) matching the embedding direction is found, set the type for both\n              // brackets in the pair to match the embedding direction.\n              var foundStrongType = false;\n              var useStrongType = 0;\n              for (var si$8 = openSeqIdx + 1; si$8 < closeSeqIdx; si$8++) {\n                var i$15 = seqIndices$1[si$8];\n                if (charTypes[i$15] & STRONG_TYPES_FOR_N_STEPS) {\n                  foundStrongType = true;\n                  var lr = charTypes[i$15] & R_TYPES_FOR_N_STEPS ? TYPE_R : TYPE_L;\n                  if (lr === embedDirection) {\n                    useStrongType = lr;\n                    break;\n                  }\n                }\n              }\n              // c. Otherwise, if there is a strong type it must be opposite the embedding direction. Therefore, test\n              // for an established context with a preceding strong type by checking backwards before the opening paired\n              // bracket until the first strong type (L, R, or sos) is found.\n              //    1. If the preceding strong type is also opposite the embedding direction, context is established, so\n              //    set the type for both brackets in the pair to that direction.\n              //    2. Otherwise set the type for both brackets in the pair to the embedding direction.\n              if (foundStrongType && !useStrongType) {\n                useStrongType = sosType;\n                for (var si$9 = openSeqIdx - 1; si$9 >= 0; si$9--) {\n                  var i$16 = seqIndices$1[si$9];\n                  if (charTypes[i$16] & STRONG_TYPES_FOR_N_STEPS) {\n                    var lr$1 = charTypes[i$16] & R_TYPES_FOR_N_STEPS ? TYPE_R : TYPE_L;\n                    if (lr$1 !== embedDirection) {\n                      useStrongType = lr$1;\n                    } else {\n                      useStrongType = embedDirection;\n                    }\n                    break;\n                  }\n                }\n              }\n              if (useStrongType) {\n                charTypes[seqIndices$1[openSeqIdx]] = charTypes[seqIndices$1[closeSeqIdx]] = useStrongType;\n                // * Any number of characters that had original bidirectional character type NSM prior to the application\n                // of W1 that immediately follow a paired bracket which changed to L or R under N0 should change to match\n                // the type of their preceding bracket.\n                if (useStrongType !== embedDirection) {\n                  for (var si$10 = openSeqIdx + 1; si$10 < seqIndices$1.length; si$10++) {\n                    if (!(charTypes[seqIndices$1[si$10]] & BN_LIKE_TYPES)) {\n                      if (getBidiCharType(string[seqIndices$1[si$10]]) & TYPE_NSM) {\n                        charTypes[seqIndices$1[si$10]] = useStrongType;\n                      }\n                      break;\n                    }\n                  }\n                }\n                if (useStrongType !== embedDirection) {\n                  for (var si$11 = closeSeqIdx + 1; si$11 < seqIndices$1.length; si$11++) {\n                    if (!(charTypes[seqIndices$1[si$11]] & BN_LIKE_TYPES)) {\n                      if (getBidiCharType(string[seqIndices$1[si$11]]) & TYPE_NSM) {\n                        charTypes[seqIndices$1[si$11]] = useStrongType;\n                      }\n                      break;\n                    }\n                  }\n                }\n              }\n            }\n\n            // N1. A sequence of NIs takes the direction of the surrounding strong text if the text on both sides has the\n            // same direction.\n            // N2. Any remaining NIs take the embedding direction.\n            for (var si$12 = 0; si$12 < seqIndices$1.length; si$12++) {\n              if (charTypes[seqIndices$1[si$12]] & NEUTRAL_ISOLATE_TYPES) {\n                var niRunStart = si$12,\n                  niRunEnd = si$12;\n                var prevType$2 = sosType; //si === 0 ? sosType : (charTypes[seqIndices[si - 1]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L\n                for (var si2 = si$12 - 1; si2 >= 0; si2--) {\n                  if (charTypes[seqIndices$1[si2]] & BN_LIKE_TYPES) {\n                    niRunStart = si2; //5.2 treat BNs adjacent to NIs as NIs\n                  } else {\n                    prevType$2 = charTypes[seqIndices$1[si2]] & R_TYPES_FOR_N_STEPS ? TYPE_R : TYPE_L;\n                    break;\n                  }\n                }\n                var nextType$1 = eosType;\n                for (var si2$1 = si$12 + 1; si2$1 < seqIndices$1.length; si2$1++) {\n                  if (charTypes[seqIndices$1[si2$1]] & (NEUTRAL_ISOLATE_TYPES | BN_LIKE_TYPES)) {\n                    niRunEnd = si2$1;\n                  } else {\n                    nextType$1 = charTypes[seqIndices$1[si2$1]] & R_TYPES_FOR_N_STEPS ? TYPE_R : TYPE_L;\n                    break;\n                  }\n                }\n                for (var sj$7 = niRunStart; sj$7 <= niRunEnd; sj$7++) {\n                  charTypes[seqIndices$1[sj$7]] = prevType$2 === nextType$1 ? prevType$2 : embedDirection;\n                }\n                si$12 = niRunEnd;\n              }\n            }\n          }\n        }\n\n        // === 3.3.6 Resolving Implicit Levels ===\n\n        for (var i$17 = paragraph.start; i$17 <= paragraph.end; i$17++) {\n          var level$3 = embedLevels[i$17];\n          var type$1 = charTypes[i$17];\n          // I2. For all characters with an odd (right-to-left) embedding level, those of type L, EN or AN go up one level.\n          if (level$3 & 1) {\n            if (type$1 & (TYPE_L | TYPE_EN | TYPE_AN)) {\n              embedLevels[i$17]++;\n            }\n          }\n          // I1. For all characters with an even (left-to-right) embedding level, those of type R go up one level\n          // and those of type AN or EN go up two levels.\n          else {\n            if (type$1 & TYPE_R) {\n              embedLevels[i$17]++;\n            } else if (type$1 & (TYPE_AN | TYPE_EN)) {\n              embedLevels[i$17] += 2;\n            }\n          }\n\n          // 5.2: Resolve any LRE, RLE, LRO, RLO, PDF, or BN to the level of the preceding character if there is one,\n          // and otherwise to the base level.\n          if (type$1 & BN_LIKE_TYPES) {\n            embedLevels[i$17] = i$17 === 0 ? paragraph.level : embedLevels[i$17 - 1];\n          }\n\n          // 3.4 L1.1-4: Reset the embedding level of segment/paragraph separators, and any sequence of whitespace or\n          // isolate formatting characters preceding them or the end of the paragraph, to the paragraph level.\n          // NOTE: this will also need to be applied to each individual line ending after line wrapping occurs.\n          if (i$17 === paragraph.end || getBidiCharType(string[i$17]) & (TYPE_S | TYPE_B)) {\n            for (var j$1 = i$17; j$1 >= 0 && getBidiCharType(string[j$1]) & TRAILING_TYPES; j$1--) {\n              embedLevels[j$1] = paragraph.level;\n            }\n          }\n        }\n      }\n\n      // DONE! The resolved levels can then be used, after line wrapping, to flip runs of characters\n      // according to section 3.4 Reordering Resolved Levels\n      return {\n        levels: embedLevels,\n        paragraphs: paragraphs\n      };\n      function determineAutoEmbedLevel(start, isFSI) {\n        // 3.3.1 P2 - P3\n        for (var i = start; i < string.length; i++) {\n          var charType = charTypes[i];\n          if (charType & (TYPE_R | TYPE_AL)) {\n            return 1;\n          }\n          if (charType & (TYPE_B | TYPE_L) || isFSI && charType === TYPE_PDI) {\n            return 0;\n          }\n          if (charType & ISOLATE_INIT_TYPES) {\n            var pdi = indexOfMatchingPDI(i);\n            i = pdi === -1 ? string.length : pdi;\n          }\n        }\n        return 0;\n      }\n      function indexOfMatchingPDI(isolateStart) {\n        // 3.1.2 BD9\n        var isolationLevel = 1;\n        for (var i = isolateStart + 1; i < string.length; i++) {\n          var charType = charTypes[i];\n          if (charType & TYPE_B) {\n            break;\n          }\n          if (charType & TYPE_PDI) {\n            if (--isolationLevel === 0) {\n              return i;\n            }\n          } else if (charType & ISOLATE_INIT_TYPES) {\n            isolationLevel++;\n          }\n        }\n        return -1;\n      }\n    }\n\n    // Bidi mirrored chars data, auto generated\n    var data = \"14>1,j>2,t>2,u>2,1a>g,2v3>1,1>1,1ge>1,1wd>1,b>1,1j>1,f>1,ai>3,-2>3,+1,8>1k0,-1jq>1y7,-1y6>1hf,-1he>1h6,-1h5>1ha,-1h8>1qi,-1pu>1,6>3u,-3s>7,6>1,1>1,f>1,1>1,+2,3>1,1>1,+13,4>1,1>1,6>1eo,-1ee>1,3>1mg,-1me>1mk,-1mj>1mi,-1mg>1mi,-1md>1,1>1,+2,1>10k,-103>1,1>1,4>1,5>1,1>1,+10,3>1,1>8,-7>8,+1,-6>7,+1,a>1,1>1,u>1,u6>1,1>1,+5,26>1,1>1,2>1,2>2,8>1,7>1,4>1,1>1,+5,b8>1,1>1,+3,1>3,-2>1,2>1,1>1,+2,c>1,3>1,1>1,+2,h>1,3>1,a>1,1>1,2>1,3>1,1>1,d>1,f>1,3>1,1a>1,1>1,6>1,7>1,13>1,k>1,1>1,+19,4>1,1>1,+2,2>1,1>1,+18,m>1,a>1,1>1,lk>1,1>1,4>1,2>1,f>1,3>1,1>1,+3,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,6>1,4j>1,j>2,t>2,u>2,2>1,+1\";\n    var mirrorMap;\n    function parse() {\n      if (!mirrorMap) {\n        //const start = performance.now()\n        var ref = parseCharacterMap(data, true);\n        var map = ref.map;\n        var reverseMap = ref.reverseMap;\n        // Combine both maps into one\n        reverseMap.forEach(function (value, key) {\n          map.set(key, value);\n        });\n        mirrorMap = map;\n        //console.log(`mirrored chars parsed in ${performance.now() - start}ms`)\n      }\n    }\n    function getMirroredCharacter(char) {\n      parse();\n      return mirrorMap.get(char) || null;\n    }\n\n    /**\n     * Given a string and its resolved embedding levels, build a map of indices to replacement chars\n     * for any characters in right-to-left segments that have defined mirrored characters.\n     * @param string\n     * @param embeddingLevels\n     * @param [start]\n     * @param [end]\n     * @return {Map<number, string>}\n     */\n    function getMirroredCharactersMap(string, embeddingLevels, start, end) {\n      var strLen = string.length;\n      start = Math.max(0, start == null ? 0 : +start);\n      end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n      var map = new Map();\n      for (var i = start; i <= end; i++) {\n        if (embeddingLevels[i] & 1) {\n          //only odd (rtl) levels\n          var mirror = getMirroredCharacter(string[i]);\n          if (mirror !== null) {\n            map.set(i, mirror);\n          }\n        }\n      }\n      return map;\n    }\n\n    /**\n     * Given a start and end denoting a single line within a string, and a set of precalculated\n     * bidi embedding levels, produce a list of segments whose ordering should be flipped, in sequence.\n     * @param {string} string - the full input string\n     * @param {GetEmbeddingLevelsResult} embeddingLevelsResult - the result object from getEmbeddingLevels\n     * @param {number} [start] - first character in a subset of the full string\n     * @param {number} [end] - last character in a subset of the full string\n     * @return {number[][]} - the list of start/end segments that should be flipped, in order.\n     */\n    function getReorderSegments(string, embeddingLevelsResult, start, end) {\n      var strLen = string.length;\n      start = Math.max(0, start == null ? 0 : +start);\n      end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n      var segments = [];\n      embeddingLevelsResult.paragraphs.forEach(function (paragraph) {\n        var lineStart = Math.max(start, paragraph.start);\n        var lineEnd = Math.min(end, paragraph.end);\n        if (lineStart < lineEnd) {\n          // Local slice for mutation\n          var lineLevels = embeddingLevelsResult.levels.slice(lineStart, lineEnd + 1);\n\n          // 3.4 L1.4: Reset any sequence of whitespace characters and/or isolate formatting characters at the\n          // end of the line to the paragraph level.\n          for (var i = lineEnd; i >= lineStart && getBidiCharType(string[i]) & TRAILING_TYPES; i--) {\n            lineLevels[i] = paragraph.level;\n          }\n\n          // L2. From the highest level found in the text to the lowest odd level on each line, including intermediate levels\n          // not actually present in the text, reverse any contiguous sequence of characters that are at that level or higher.\n          var maxLevel = paragraph.level;\n          var minOddLevel = Infinity;\n          for (var i$1 = 0; i$1 < lineLevels.length; i$1++) {\n            var level = lineLevels[i$1];\n            if (level > maxLevel) {\n              maxLevel = level;\n            }\n            if (level < minOddLevel) {\n              minOddLevel = level | 1;\n            }\n          }\n          for (var lvl = maxLevel; lvl >= minOddLevel; lvl--) {\n            for (var i$2 = 0; i$2 < lineLevels.length; i$2++) {\n              if (lineLevels[i$2] >= lvl) {\n                var segStart = i$2;\n                while (i$2 + 1 < lineLevels.length && lineLevels[i$2 + 1] >= lvl) {\n                  i$2++;\n                }\n                if (i$2 > segStart) {\n                  segments.push([segStart + lineStart, i$2 + lineStart]);\n                }\n              }\n            }\n          }\n        }\n      });\n      return segments;\n    }\n\n    /**\n     * @param {string} string\n     * @param {GetEmbeddingLevelsResult} embedLevelsResult\n     * @param {number} [start]\n     * @param {number} [end]\n     * @return {string} the new string with bidi segments reordered\n     */\n    function getReorderedString(string, embedLevelsResult, start, end) {\n      var indices = getReorderedIndices(string, embedLevelsResult, start, end);\n      var chars = [].concat(string);\n      indices.forEach(function (charIndex, i) {\n        chars[i] = (embedLevelsResult.levels[charIndex] & 1 ? getMirroredCharacter(string[charIndex]) : null) || string[charIndex];\n      });\n      return chars.join('');\n    }\n\n    /**\n     * @param {string} string\n     * @param {GetEmbeddingLevelsResult} embedLevelsResult\n     * @param {number} [start]\n     * @param {number} [end]\n     * @return {number[]} an array with character indices in their new bidi order\n     */\n    function getReorderedIndices(string, embedLevelsResult, start, end) {\n      var segments = getReorderSegments(string, embedLevelsResult, start, end);\n      // Fill an array with indices\n      var indices = [];\n      for (var i = 0; i < string.length; i++) {\n        indices[i] = i;\n      }\n      // Reverse each segment in order\n      segments.forEach(function (ref) {\n        var start = ref[0];\n        var end = ref[1];\n        var slice = indices.slice(start, end + 1);\n        for (var i = slice.length; i--;) {\n          indices[end - i] = slice[i];\n        }\n      });\n      return indices;\n    }\n    exports.closingToOpeningBracket = closingToOpeningBracket;\n    exports.getBidiCharType = getBidiCharType;\n    exports.getBidiCharTypeName = getBidiCharTypeName;\n    exports.getCanonicalBracket = getCanonicalBracket;\n    exports.getEmbeddingLevels = getEmbeddingLevels;\n    exports.getMirroredCharacter = getMirroredCharacter;\n    exports.getMirroredCharactersMap = getMirroredCharactersMap;\n    exports.getReorderSegments = getReorderSegments;\n    exports.getReorderedIndices = getReorderedIndices;\n    exports.getReorderedString = getReorderedString;\n    exports.openingToClosingBracket = openingToClosingBracket;\n    Object.defineProperty(exports, '__esModule', {\n      value: true\n    });\n    return exports;\n  }({});\n  return bidi;\n}\nexport default bidiFactory;", "map": {"version": 3, "names": ["bidiFactory", "bidi", "exports", "DATA", "TYPES", "TYPES_TO_NAMES", "L", "Object", "keys", "for<PERSON>ach", "type", "i", "freeze", "ISOLATE_INIT_TYPES", "LRI", "RLI", "FSI", "STRONG_TYPES", "R", "AL", "NEUTRAL_ISOLATE_TYPES", "B", "S", "WS", "ON", "PDI", "BN_LIKE_TYPES", "BN", "RLE", "LRE", "RLO", "LRO", "PDF", "TRAILING_TYPES", "map", "parseData", "Map", "loop", "hasOwnProperty", "lastCode", "split", "range", "ref", "skip", "step", "parseInt", "set", "getBidiCharType", "char", "get", "codePointAt", "getBidiCharTypeName", "data$1", "parseCharacterMap", "encodedString", "includeReverse", "radix", "reverseMap", "prevPair", "visit", "entry", "indexOf", "a", "b", "String", "fromCodePoint", "openToClose", "closeToOpen", "canonical", "parse$1", "pairs", "openingToClosingBracket", "closingToOpeningBracket", "getCanonicalBracket", "TYPE_L", "TYPE_R", "TYPE_EN", "EN", "TYPE_ES", "ES", "TYPE_ET", "ET", "TYPE_AN", "AN", "TYPE_CS", "CS", "TYPE_B", "TYPE_S", "TYPE_ON", "TYPE_BN", "TYPE_NSM", "NSM", "TYPE_AL", "TYPE_LRO", "TYPE_RLO", "TYPE_LRE", "TYPE_RLE", "TYPE_PDF", "TYPE_LRI", "TYPE_RLI", "TYPE_FSI", "TYPE_PDI", "getEmbeddingLevels", "string", "baseDirection", "MAX_DEPTH", "charTypes", "Uint32Array", "length", "charTypeCounts", "changeCharType", "oldType", "embedLevels", "Uint8Array", "isolationPairs", "paragraphs", "paragraph", "i$1", "push", "start", "end", "level", "determineAutoEmbedLevel", "FORMATTING_TYPES", "nextEven", "n", "nextOdd", "paraIdx", "statusStack", "_level", "_override", "_isolate", "stackTop", "overflowIsolateCount", "overflowEmbeddingCount", "validIsolateCount", "clear", "i$2", "charType", "level$1", "level$2", "_isolInitIndex", "pop", "isolInitIndex", "levelRuns", "currentRun", "i$3", "charType$1", "lvl", "isIsolInit", "isPDI", "_end", "_endsWithIsolInit", "_start", "_startsWithPDI", "isolatingRunSeqs", "runIdx", "run", "has", "seqRuns", "pdiIndex", "i$4", "seqIndices", "i$5", "run$1", "j", "firstLevel", "prevLevel", "i$6", "lastIndex", "lastLevel", "nextLevel", "i$7", "_seqIndices", "_sosType", "Math", "max", "_eosType", "seqIdx", "seqIndices$1", "sosType", "eosType", "embedDirection", "si", "i$8", "prevType", "sj", "si$1", "i$9", "sj$1", "prevCharType", "si$2", "i$10", "si$3", "i$11", "prevType$1", "nextType", "sj$2", "sj$3", "si$4", "i$12", "sj$4", "si$5", "i$13", "sj$5", "sj$6", "si$6", "prevStrongType", "i$14", "R_TYPES_FOR_N_STEPS", "STRONG_TYPES_FOR_N_STEPS", "bracketPairs", "openerStack", "si$7", "oppositeBracket", "seqIndex", "stackIdx", "stackChar", "sort", "pairIdx", "ref$1", "openSeqIdx", "closeSeqIdx", "foundStrongType", "useStrongType", "si$8", "i$15", "lr", "si$9", "i$16", "lr$1", "si$10", "si$11", "si$12", "niRunStart", "niRunEnd", "prevType$2", "si2", "nextType$1", "si2$1", "sj$7", "i$17", "level$3", "type$1", "j$1", "levels", "isFSI", "pdi", "indexOfMatchingPDI", "isolateStart", "isolationLevel", "data", "mirrorMap", "parse", "value", "key", "getMirroredCharacter", "getMirroredCharactersMap", "embeddingLevels", "strLen", "min", "mirror", "getReorderSegments", "embeddingLevelsResult", "segments", "lineStart", "lineEnd", "lineLevels", "slice", "maxLevel", "minOddLevel", "Infinity", "segStart", "getReorderedString", "embedLevelsResult", "indices", "getReorderedIndices", "chars", "concat", "charIndex", "join", "defineProperty"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/bidi-js/dist/bidi.mjs"], "sourcesContent": ["function bidiFactory() {\nvar bidi = (function (exports) {\n\n  // Bidi character types data, auto generated\n  var DATA = {\n    \"R\": \"13k,1a,2,3,3,2+1j,ch+16,a+1,5+2,2+n,5,a,4,6+16,4+3,h+1b,4mo,179q,2+9,2+11,2i9+7y,2+68,4,3+4,5+13,4+3,2+4k,3+29,8+cf,1t+7z,w+17,3+3m,1t+3z,16o1+5r,8+30,8+mc,29+1r,29+4v,75+73\",\n    \"EN\": \"1c+9,3d+1,6,187+9,513,4+5,7+9,sf+j,175h+9,qw+q,161f+1d,4xt+a,25i+9\",\n    \"ES\": \"17,2,6dp+1,f+1,av,16vr,mx+1,4o,2\",\n    \"ET\": \"z+2,3h+3,b+1,ym,3e+1,2o,p4+1,8,6u,7c,g6,1wc,1n9+4,30+1b,2n,6d,qhx+1,h0m,a+1,49+2,63+1,4+1,6bb+3,12jj\",\n    \"AN\": \"16o+5,2j+9,2+1,35,ed,1ff2+9,87+u\",\n    \"CS\": \"18,2+1,b,2u,12k,55v,l,17v0,2,3,53,2+1,b\",\n    \"B\": \"a,3,f+2,2v,690\",\n    \"S\": \"9,2,k\",\n    \"WS\": \"c,k,4f4,1vk+a,u,1j,335\",\n    \"ON\": \"x+1,4+4,h+5,r+5,r+3,z,5+3,2+1,2+1,5,2+2,3+4,o,w,ci+1,8+d,3+d,6+8,2+g,39+1,9,6+1,2,33,b8,3+1,3c+1,7+1,5r,b,7h+3,sa+5,2,3i+6,jg+3,ur+9,2v,ij+1,9g+9,7+a,8m,4+1,49+x,14u,2+2,c+2,e+2,e+2,e+1,i+n,e+e,2+p,u+2,e+2,36+1,2+3,2+1,b,2+2,6+5,2,2,2,h+1,5+4,6+3,3+f,16+2,5+3l,3+81,1y+p,2+40,q+a,m+13,2r+ch,2+9e,75+hf,3+v,2+2w,6e+5,f+6,75+2a,1a+p,2+2g,d+5x,r+b,6+3,4+o,g,6+1,6+2,2k+1,4,2j,5h+z,1m+1,1e+f,t+2,1f+e,d+3,4o+3,2s+1,w,535+1r,h3l+1i,93+2,2s,b+1,3l+x,2v,4g+3,21+3,kz+1,g5v+1,5a,j+9,n+v,2,3,2+8,2+1,3+2,2,3,46+1,4+4,h+5,r+5,r+a,3h+2,4+6,b+4,78,1r+24,4+c,4,1hb,ey+6,103+j,16j+c,1ux+7,5+g,fsh,jdq+1t,4,57+2e,p1,1m,1m,1m,1m,4kt+1,7j+17,5+2r,d+e,3+e,2+e,2+10,m+4,w,1n+5,1q,4z+5,4b+rb,9+c,4+c,4+37,d+2g,8+b,l+b,5+1j,9+9,7+13,9+t,3+1,27+3c,2+29,2+3q,d+d,3+4,4+2,6+6,a+o,8+6,a+2,e+6,16+42,2+1i\",\n    \"BN\": \"0+8,6+d,2s+5,2+p,e,4m9,1kt+2,2b+5,5+5,17q9+v,7k,6p+8,6+1,119d+3,440+7,96s+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+75,6p+2rz,1ben+1,1ekf+1,1ekf+1\",\n    \"NSM\": \"lc+33,7o+6,7c+18,2,2+1,2+1,2,21+a,1d+k,h,2u+6,3+5,3+1,2+3,10,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,g+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+g,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,k1+w,2db+2,3y,2p+v,ff+3,30+1,n9x+3,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,r2,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+5,3+1,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2d+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,f0c+4,1o+6,t5,1s+3,2a,f5l+1,43t+2,i+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,gzhy+6n\",\n    \"AL\": \"16w,3,2,e+1b,z+2,2+2s,g+1,8+1,b+m,2+t,s+2i,c+e,4h+f,1d+1e,1bwe+dp,3+3z,x+c,2+1,35+3y,2rm+z,5+7,b+5,dt+l,c+u,17nl+27,1t+27,4x+6n,3+d\",\n    \"LRO\": \"6ct\",\n    \"RLO\": \"6cu\",\n    \"LRE\": \"6cq\",\n    \"RLE\": \"6cr\",\n    \"PDF\": \"6cs\",\n    \"LRI\": \"6ee\",\n    \"RLI\": \"6ef\",\n    \"FSI\": \"6eg\",\n    \"PDI\": \"6eh\"\n  };\n\n  var TYPES = {};\n  var TYPES_TO_NAMES = {};\n  TYPES.L = 1; //L is the default\n  TYPES_TO_NAMES[1] = 'L';\n  Object.keys(DATA).forEach(function (type, i) {\n    TYPES[type] = 1 << (i + 1);\n    TYPES_TO_NAMES[TYPES[type]] = type;\n  });\n  Object.freeze(TYPES);\n\n  var ISOLATE_INIT_TYPES = TYPES.LRI | TYPES.RLI | TYPES.FSI;\n  var STRONG_TYPES = TYPES.L | TYPES.R | TYPES.AL;\n  var NEUTRAL_ISOLATE_TYPES = TYPES.B | TYPES.S | TYPES.WS | TYPES.ON | TYPES.FSI | TYPES.LRI | TYPES.RLI | TYPES.PDI;\n  var BN_LIKE_TYPES = TYPES.BN | TYPES.RLE | TYPES.LRE | TYPES.RLO | TYPES.LRO | TYPES.PDF;\n  var TRAILING_TYPES = TYPES.S | TYPES.WS | TYPES.B | ISOLATE_INIT_TYPES | TYPES.PDI | BN_LIKE_TYPES;\n\n  var map = null;\n\n  function parseData () {\n    if (!map) {\n      //const start = performance.now()\n      map = new Map();\n      var loop = function ( type ) {\n        if (DATA.hasOwnProperty(type)) {\n          var lastCode = 0;\n          DATA[type].split(',').forEach(function (range) {\n            var ref = range.split('+');\n            var skip = ref[0];\n            var step = ref[1];\n            skip = parseInt(skip, 36);\n            step = step ? parseInt(step, 36) : 0;\n            map.set(lastCode += skip, TYPES[type]);\n            for (var i = 0; i < step; i++) {\n              map.set(++lastCode, TYPES[type]);\n            }\n          });\n        }\n      };\n\n      for (var type in DATA) loop( type );\n      //console.log(`char types parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  /**\n   * @param {string} char\n   * @return {number}\n   */\n  function getBidiCharType (char) {\n    parseData();\n    return map.get(char.codePointAt(0)) || TYPES.L\n  }\n\n  function getBidiCharTypeName(char) {\n    return TYPES_TO_NAMES[getBidiCharType(char)]\n  }\n\n  // Bidi bracket pairs data, auto generated\n  var data$1 = {\n    \"pairs\": \"14>1,1e>2,u>2,2wt>1,1>1,1ge>1,1wp>1,1j>1,f>1,hm>1,1>1,u>1,u6>1,1>1,+5,28>1,w>1,1>1,+3,b8>1,1>1,+3,1>3,-1>-1,3>1,1>1,+2,1s>1,1>1,x>1,th>1,1>1,+2,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,4q>1,1e>2,u>2,2>1,+1\",\n    \"canonical\": \"6f1>-6dx,6dy>-6dx,6ec>-6ed,6ee>-6ed,6ww>2jj,-2ji>2jj,14r4>-1e7l,1e7m>-1e7l,1e7m>-1e5c,1e5d>-1e5b,1e5c>-14qx,14qy>-14qx,14vn>-1ecg,1ech>-1ecg,1edu>-1ecg,1eci>-1ecg,1eda>-1ecg,1eci>-1ecg,1eci>-168q,168r>-168q,168s>-14ye,14yf>-14ye\"\n  };\n\n  /**\n   * Parses an string that holds encoded codepoint mappings, e.g. for bracket pairs or\n   * mirroring characters, as encoded by scripts/generateBidiData.js. Returns an object\n   * holding the `map`, and optionally a `reverseMap` if `includeReverse:true`.\n   * @param {string} encodedString\n   * @param {boolean} includeReverse - true if you want reverseMap in the output\n   * @return {{map: Map<number, number>, reverseMap?: Map<number, number>}}\n   */\n  function parseCharacterMap (encodedString, includeReverse) {\n    var radix = 36;\n    var lastCode = 0;\n    var map = new Map();\n    var reverseMap = includeReverse && new Map();\n    var prevPair;\n    encodedString.split(',').forEach(function visit(entry) {\n      if (entry.indexOf('+') !== -1) {\n        for (var i = +entry; i--;) {\n          visit(prevPair);\n        }\n      } else {\n        prevPair = entry;\n        var ref = entry.split('>');\n        var a = ref[0];\n        var b = ref[1];\n        a = String.fromCodePoint(lastCode += parseInt(a, radix));\n        b = String.fromCodePoint(lastCode += parseInt(b, radix));\n        map.set(a, b);\n        includeReverse && reverseMap.set(b, a);\n      }\n    });\n    return { map: map, reverseMap: reverseMap }\n  }\n\n  var openToClose, closeToOpen, canonical;\n\n  function parse$1 () {\n    if (!openToClose) {\n      //const start = performance.now()\n      var ref = parseCharacterMap(data$1.pairs, true);\n      var map = ref.map;\n      var reverseMap = ref.reverseMap;\n      openToClose = map;\n      closeToOpen = reverseMap;\n      canonical = parseCharacterMap(data$1.canonical, false).map;\n      //console.log(`brackets parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  function openingToClosingBracket (char) {\n    parse$1();\n    return openToClose.get(char) || null\n  }\n\n  function closingToOpeningBracket (char) {\n    parse$1();\n    return closeToOpen.get(char) || null\n  }\n\n  function getCanonicalBracket (char) {\n    parse$1();\n    return canonical.get(char) || null\n  }\n\n  // Local type aliases\n  var TYPE_L = TYPES.L;\n  var TYPE_R = TYPES.R;\n  var TYPE_EN = TYPES.EN;\n  var TYPE_ES = TYPES.ES;\n  var TYPE_ET = TYPES.ET;\n  var TYPE_AN = TYPES.AN;\n  var TYPE_CS = TYPES.CS;\n  var TYPE_B = TYPES.B;\n  var TYPE_S = TYPES.S;\n  var TYPE_ON = TYPES.ON;\n  var TYPE_BN = TYPES.BN;\n  var TYPE_NSM = TYPES.NSM;\n  var TYPE_AL = TYPES.AL;\n  var TYPE_LRO = TYPES.LRO;\n  var TYPE_RLO = TYPES.RLO;\n  var TYPE_LRE = TYPES.LRE;\n  var TYPE_RLE = TYPES.RLE;\n  var TYPE_PDF = TYPES.PDF;\n  var TYPE_LRI = TYPES.LRI;\n  var TYPE_RLI = TYPES.RLI;\n  var TYPE_FSI = TYPES.FSI;\n  var TYPE_PDI = TYPES.PDI;\n\n  /**\n   * @typedef {object} GetEmbeddingLevelsResult\n   * @property {{start, end, level}[]} paragraphs\n   * @property {Uint8Array} levels\n   */\n\n  /**\n   * This function applies the Bidirectional Algorithm to a string, returning the resolved embedding levels\n   * in a single Uint8Array plus a list of objects holding each paragraph's start and end indices and resolved\n   * base embedding level.\n   *\n   * @param {string} string - The input string\n   * @param {\"ltr\"|\"rtl\"|\"auto\"} [baseDirection] - Use \"ltr\" or \"rtl\" to force a base paragraph direction,\n   *        otherwise a direction will be chosen automatically from each paragraph's contents.\n   * @return {GetEmbeddingLevelsResult}\n   */\n  function getEmbeddingLevels (string, baseDirection) {\n    var MAX_DEPTH = 125;\n\n    // Start by mapping all characters to their unicode type, as a bitmask integer\n    var charTypes = new Uint32Array(string.length);\n    for (var i = 0; i < string.length; i++) {\n      charTypes[i] = getBidiCharType(string[i]);\n    }\n\n    var charTypeCounts = new Map(); //will be cleared at start of each paragraph\n    function changeCharType(i, type) {\n      var oldType = charTypes[i];\n      charTypes[i] = type;\n      charTypeCounts.set(oldType, charTypeCounts.get(oldType) - 1);\n      if (oldType & NEUTRAL_ISOLATE_TYPES) {\n        charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) - 1);\n      }\n      charTypeCounts.set(type, (charTypeCounts.get(type) || 0) + 1);\n      if (type & NEUTRAL_ISOLATE_TYPES) {\n        charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n      }\n    }\n\n    var embedLevels = new Uint8Array(string.length);\n    var isolationPairs = new Map(); //init->pdi and pdi->init\n\n    // === 3.3.1 The Paragraph Level ===\n    // 3.3.1 P1: Split the text into paragraphs\n    var paragraphs = []; // [{start, end, level}, ...]\n    var paragraph = null;\n    for (var i$1 = 0; i$1 < string.length; i$1++) {\n      if (!paragraph) {\n        paragraphs.push(paragraph = {\n          start: i$1,\n          end: string.length - 1,\n          // 3.3.1 P2-P3: Determine the paragraph level\n          level: baseDirection === 'rtl' ? 1 : baseDirection === 'ltr' ? 0 : determineAutoEmbedLevel(i$1, false)\n        });\n      }\n      if (charTypes[i$1] & TYPE_B) {\n        paragraph.end = i$1;\n        paragraph = null;\n      }\n    }\n\n    var FORMATTING_TYPES = TYPE_RLE | TYPE_LRE | TYPE_RLO | TYPE_LRO | ISOLATE_INIT_TYPES | TYPE_PDI | TYPE_PDF | TYPE_B;\n    var nextEven = function (n) { return n + ((n & 1) ? 1 : 2); };\n    var nextOdd = function (n) { return n + ((n & 1) ? 2 : 1); };\n\n    // Everything from here on will operate per paragraph.\n    for (var paraIdx = 0; paraIdx < paragraphs.length; paraIdx++) {\n      paragraph = paragraphs[paraIdx];\n      var statusStack = [{\n        _level: paragraph.level,\n        _override: 0, //0=neutral, 1=L, 2=R\n        _isolate: 0 //bool\n      }];\n      var stackTop = (void 0);\n      var overflowIsolateCount = 0;\n      var overflowEmbeddingCount = 0;\n      var validIsolateCount = 0;\n      charTypeCounts.clear();\n\n      // === 3.3.2 Explicit Levels and Directions ===\n      for (var i$2 = paragraph.start; i$2 <= paragraph.end; i$2++) {\n        var charType = charTypes[i$2];\n        stackTop = statusStack[statusStack.length - 1];\n\n        // Set initial counts\n        charTypeCounts.set(charType, (charTypeCounts.get(charType) || 0) + 1);\n        if (charType & NEUTRAL_ISOLATE_TYPES) {\n          charTypeCounts.set(NEUTRAL_ISOLATE_TYPES, (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES) || 0) + 1);\n        }\n\n        // Explicit Embeddings: 3.3.2 X2 - X3\n        if (charType & FORMATTING_TYPES) { //prefilter all formatters\n          if (charType & (TYPE_RLE | TYPE_LRE)) {\n            embedLevels[i$2] = stackTop._level; // 5.2\n            var level = (charType === TYPE_RLE ? nextOdd : nextEven)(stackTop._level);\n            if (level <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n              statusStack.push({\n                _level: level,\n                _override: 0,\n                _isolate: 0\n              });\n            } else if (!overflowIsolateCount) {\n              overflowEmbeddingCount++;\n            }\n          }\n\n          // Explicit Overrides: 3.3.2 X4 - X5\n          else if (charType & (TYPE_RLO | TYPE_LRO)) {\n            embedLevels[i$2] = stackTop._level; // 5.2\n            var level$1 = (charType === TYPE_RLO ? nextOdd : nextEven)(stackTop._level);\n            if (level$1 <= MAX_DEPTH && !overflowIsolateCount && !overflowEmbeddingCount) {\n              statusStack.push({\n                _level: level$1,\n                _override: (charType & TYPE_RLO) ? TYPE_R : TYPE_L,\n                _isolate: 0\n              });\n            } else if (!overflowIsolateCount) {\n              overflowEmbeddingCount++;\n            }\n          }\n\n          // Isolates: 3.3.2 X5a - X5c\n          else if (charType & ISOLATE_INIT_TYPES) {\n            // X5c - FSI becomes either RLI or LRI\n            if (charType & TYPE_FSI) {\n              charType = determineAutoEmbedLevel(i$2 + 1, true) === 1 ? TYPE_RLI : TYPE_LRI;\n            }\n\n            embedLevels[i$2] = stackTop._level;\n            if (stackTop._override) {\n              changeCharType(i$2, stackTop._override);\n            }\n            var level$2 = (charType === TYPE_RLI ? nextOdd : nextEven)(stackTop._level);\n            if (level$2 <= MAX_DEPTH && overflowIsolateCount === 0 && overflowEmbeddingCount === 0) {\n              validIsolateCount++;\n              statusStack.push({\n                _level: level$2,\n                _override: 0,\n                _isolate: 1,\n                _isolInitIndex: i$2\n              });\n            } else {\n              overflowIsolateCount++;\n            }\n          }\n\n          // Terminating Isolates: 3.3.2 X6a\n          else if (charType & TYPE_PDI) {\n            if (overflowIsolateCount > 0) {\n              overflowIsolateCount--;\n            } else if (validIsolateCount > 0) {\n              overflowEmbeddingCount = 0;\n              while (!statusStack[statusStack.length - 1]._isolate) {\n                statusStack.pop();\n              }\n              // Add to isolation pairs bidirectional mapping:\n              var isolInitIndex = statusStack[statusStack.length - 1]._isolInitIndex;\n              if (isolInitIndex != null) {\n                isolationPairs.set(isolInitIndex, i$2);\n                isolationPairs.set(i$2, isolInitIndex);\n              }\n              statusStack.pop();\n              validIsolateCount--;\n            }\n            stackTop = statusStack[statusStack.length - 1];\n            embedLevels[i$2] = stackTop._level;\n            if (stackTop._override) {\n              changeCharType(i$2, stackTop._override);\n            }\n          }\n\n\n          // Terminating Embeddings and Overrides: 3.3.2 X7\n          else if (charType & TYPE_PDF) {\n            if (overflowIsolateCount === 0) {\n              if (overflowEmbeddingCount > 0) {\n                overflowEmbeddingCount--;\n              } else if (!stackTop._isolate && statusStack.length > 1) {\n                statusStack.pop();\n                stackTop = statusStack[statusStack.length - 1];\n              }\n            }\n            embedLevels[i$2] = stackTop._level; // 5.2\n          }\n\n          // End of Paragraph: 3.3.2 X8\n          else if (charType & TYPE_B) {\n            embedLevels[i$2] = paragraph.level;\n          }\n        }\n\n        // Non-formatting characters: 3.3.2 X6\n        else {\n          embedLevels[i$2] = stackTop._level;\n          // NOTE: This exclusion of BN seems to go against what section 5.2 says, but is required for test passage\n          if (stackTop._override && charType !== TYPE_BN) {\n            changeCharType(i$2, stackTop._override);\n          }\n        }\n      }\n\n      // === 3.3.3 Preparations for Implicit Processing ===\n\n      // Remove all RLE, LRE, RLO, LRO, PDF, and BN characters: 3.3.3 X9\n      // Note: Due to section 5.2, we won't remove them, but we'll use the BN_LIKE_TYPES bitset to\n      // easily ignore them all from here on out.\n\n      // 3.3.3 X10\n      // Compute the set of isolating run sequences as specified by BD13\n      var levelRuns = [];\n      var currentRun = null;\n      for (var i$3 = paragraph.start; i$3 <= paragraph.end; i$3++) {\n        var charType$1 = charTypes[i$3];\n        if (!(charType$1 & BN_LIKE_TYPES)) {\n          var lvl = embedLevels[i$3];\n          var isIsolInit = charType$1 & ISOLATE_INIT_TYPES;\n          var isPDI = charType$1 === TYPE_PDI;\n          if (currentRun && lvl === currentRun._level) {\n            currentRun._end = i$3;\n            currentRun._endsWithIsolInit = isIsolInit;\n          } else {\n            levelRuns.push(currentRun = {\n              _start: i$3,\n              _end: i$3,\n              _level: lvl,\n              _startsWithPDI: isPDI,\n              _endsWithIsolInit: isIsolInit\n            });\n          }\n        }\n      }\n      var isolatingRunSeqs = []; // [{seqIndices: [], sosType: L|R, eosType: L|R}]\n      for (var runIdx = 0; runIdx < levelRuns.length; runIdx++) {\n        var run = levelRuns[runIdx];\n        if (!run._startsWithPDI || (run._startsWithPDI && !isolationPairs.has(run._start))) {\n          var seqRuns = [currentRun = run];\n          for (var pdiIndex = (void 0); currentRun && currentRun._endsWithIsolInit && (pdiIndex = isolationPairs.get(currentRun._end)) != null;) {\n            for (var i$4 = runIdx + 1; i$4 < levelRuns.length; i$4++) {\n              if (levelRuns[i$4]._start === pdiIndex) {\n                seqRuns.push(currentRun = levelRuns[i$4]);\n                break\n              }\n            }\n          }\n          // build flat list of indices across all runs:\n          var seqIndices = [];\n          for (var i$5 = 0; i$5 < seqRuns.length; i$5++) {\n            var run$1 = seqRuns[i$5];\n            for (var j = run$1._start; j <= run$1._end; j++) {\n              seqIndices.push(j);\n            }\n          }\n          // determine the sos/eos types:\n          var firstLevel = embedLevels[seqIndices[0]];\n          var prevLevel = paragraph.level;\n          for (var i$6 = seqIndices[0] - 1; i$6 >= 0; i$6--) {\n            if (!(charTypes[i$6] & BN_LIKE_TYPES)) { //5.2\n              prevLevel = embedLevels[i$6];\n              break\n            }\n          }\n          var lastIndex = seqIndices[seqIndices.length - 1];\n          var lastLevel = embedLevels[lastIndex];\n          var nextLevel = paragraph.level;\n          if (!(charTypes[lastIndex] & ISOLATE_INIT_TYPES)) {\n            for (var i$7 = lastIndex + 1; i$7 <= paragraph.end; i$7++) {\n              if (!(charTypes[i$7] & BN_LIKE_TYPES)) { //5.2\n                nextLevel = embedLevels[i$7];\n                break\n              }\n            }\n          }\n          isolatingRunSeqs.push({\n            _seqIndices: seqIndices,\n            _sosType: Math.max(prevLevel, firstLevel) % 2 ? TYPE_R : TYPE_L,\n            _eosType: Math.max(nextLevel, lastLevel) % 2 ? TYPE_R : TYPE_L\n          });\n        }\n      }\n\n      // The next steps are done per isolating run sequence\n      for (var seqIdx = 0; seqIdx < isolatingRunSeqs.length; seqIdx++) {\n        var ref = isolatingRunSeqs[seqIdx];\n        var seqIndices$1 = ref._seqIndices;\n        var sosType = ref._sosType;\n        var eosType = ref._eosType;\n        /**\n         * All the level runs in an isolating run sequence have the same embedding level.\n         * \n         * DO NOT change any `embedLevels[i]` within the current scope.\n         */\n        var embedDirection = ((embedLevels[seqIndices$1[0]]) & 1) ? TYPE_R : TYPE_L;\n\n        // === 3.3.4 Resolving Weak Types ===\n\n        // W1 + 5.2. Search backward from each NSM to the first character in the isolating run sequence whose\n        // bidirectional type is not BN, and set the NSM to ON if it is an isolate initiator or PDI, and to its\n        // type otherwise. If the NSM is the first non-BN character, change the NSM to the type of sos.\n        if (charTypeCounts.get(TYPE_NSM)) {\n          for (var si = 0; si < seqIndices$1.length; si++) {\n            var i$8 = seqIndices$1[si];\n            if (charTypes[i$8] & TYPE_NSM) {\n              var prevType = sosType;\n              for (var sj = si - 1; sj >= 0; sj--) {\n                if (!(charTypes[seqIndices$1[sj]] & BN_LIKE_TYPES)) { //5.2 scan back to first non-BN\n                  prevType = charTypes[seqIndices$1[sj]];\n                  break\n                }\n              }\n              changeCharType(i$8, (prevType & (ISOLATE_INIT_TYPES | TYPE_PDI)) ? TYPE_ON : prevType);\n            }\n          }\n        }\n\n        // W2. Search backward from each instance of a European number until the first strong type (R, L, AL, or sos)\n        // is found. If an AL is found, change the type of the European number to Arabic number.\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$1 = 0; si$1 < seqIndices$1.length; si$1++) {\n            var i$9 = seqIndices$1[si$1];\n            if (charTypes[i$9] & TYPE_EN) {\n              for (var sj$1 = si$1 - 1; sj$1 >= -1; sj$1--) {\n                var prevCharType = sj$1 === -1 ? sosType : charTypes[seqIndices$1[sj$1]];\n                if (prevCharType & STRONG_TYPES) {\n                  if (prevCharType === TYPE_AL) {\n                    changeCharType(i$9, TYPE_AN);\n                  }\n                  break\n                }\n              }\n            }\n          }\n        }\n\n        // W3. Change all ALs to R\n        if (charTypeCounts.get(TYPE_AL)) {\n          for (var si$2 = 0; si$2 < seqIndices$1.length; si$2++) {\n            var i$10 = seqIndices$1[si$2];\n            if (charTypes[i$10] & TYPE_AL) {\n              changeCharType(i$10, TYPE_R);\n            }\n          }\n        }\n\n        // W4. A single European separator between two European numbers changes to a European number. A single common\n        // separator between two numbers of the same type changes to that type.\n        if (charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n          for (var si$3 = 1; si$3 < seqIndices$1.length - 1; si$3++) {\n            var i$11 = seqIndices$1[si$3];\n            if (charTypes[i$11] & (TYPE_ES | TYPE_CS)) {\n              var prevType$1 = 0, nextType = 0;\n              for (var sj$2 = si$3 - 1; sj$2 >= 0; sj$2--) {\n                prevType$1 = charTypes[seqIndices$1[sj$2]];\n                if (!(prevType$1 & BN_LIKE_TYPES)) { //5.2\n                  break\n                }\n              }\n              for (var sj$3 = si$3 + 1; sj$3 < seqIndices$1.length; sj$3++) {\n                nextType = charTypes[seqIndices$1[sj$3]];\n                if (!(nextType & BN_LIKE_TYPES)) { //5.2\n                  break\n                }\n              }\n              if (prevType$1 === nextType && (charTypes[i$11] === TYPE_ES ? prevType$1 === TYPE_EN : (prevType$1 & (TYPE_EN | TYPE_AN)))) {\n                changeCharType(i$11, prevType$1);\n              }\n            }\n          }\n        }\n\n        // W5. A sequence of European terminators adjacent to European numbers changes to all European numbers.\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$4 = 0; si$4 < seqIndices$1.length; si$4++) {\n            var i$12 = seqIndices$1[si$4];\n            if (charTypes[i$12] & TYPE_EN) {\n              for (var sj$4 = si$4 - 1; sj$4 >= 0 && (charTypes[seqIndices$1[sj$4]] & (TYPE_ET | BN_LIKE_TYPES)); sj$4--) {\n                changeCharType(seqIndices$1[sj$4], TYPE_EN);\n              }\n              for (si$4++; si$4 < seqIndices$1.length && (charTypes[seqIndices$1[si$4]] & (TYPE_ET | BN_LIKE_TYPES | TYPE_EN)); si$4++) {\n                if (charTypes[seqIndices$1[si$4]] !== TYPE_EN) {\n                  changeCharType(seqIndices$1[si$4], TYPE_EN);\n                }\n              }\n            }\n          }\n        }\n\n        // W6. Otherwise, separators and terminators change to Other Neutral.\n        if (charTypeCounts.get(TYPE_ET) || charTypeCounts.get(TYPE_ES) || charTypeCounts.get(TYPE_CS)) {\n          for (var si$5 = 0; si$5 < seqIndices$1.length; si$5++) {\n            var i$13 = seqIndices$1[si$5];\n            if (charTypes[i$13] & (TYPE_ET | TYPE_ES | TYPE_CS)) {\n              changeCharType(i$13, TYPE_ON);\n              // 5.2 transform adjacent BNs too:\n              for (var sj$5 = si$5 - 1; sj$5 >= 0 && (charTypes[seqIndices$1[sj$5]] & BN_LIKE_TYPES); sj$5--) {\n                changeCharType(seqIndices$1[sj$5], TYPE_ON);\n              }\n              for (var sj$6 = si$5 + 1; sj$6 < seqIndices$1.length && (charTypes[seqIndices$1[sj$6]] & BN_LIKE_TYPES); sj$6++) {\n                changeCharType(seqIndices$1[sj$6], TYPE_ON);\n              }\n            }\n          }\n        }\n\n        // W7. Search backward from each instance of a European number until the first strong type (R, L, or sos)\n        // is found. If an L is found, then change the type of the European number to L.\n        // NOTE: implemented in single forward pass for efficiency\n        if (charTypeCounts.get(TYPE_EN)) {\n          for (var si$6 = 0, prevStrongType = sosType; si$6 < seqIndices$1.length; si$6++) {\n            var i$14 = seqIndices$1[si$6];\n            var type = charTypes[i$14];\n            if (type & TYPE_EN) {\n              if (prevStrongType === TYPE_L) {\n                changeCharType(i$14, TYPE_L);\n              }\n            } else if (type & STRONG_TYPES) {\n              prevStrongType = type;\n            }\n          }\n        }\n\n        // === 3.3.5 Resolving Neutral and Isolate Formatting Types ===\n\n        if (charTypeCounts.get(NEUTRAL_ISOLATE_TYPES)) {\n          // N0. Process bracket pairs in an isolating run sequence sequentially in the logical order of the text\n          // positions of the opening paired brackets using the logic given below. Within this scope, bidirectional\n          // types EN and AN are treated as R.\n          var R_TYPES_FOR_N_STEPS = (TYPE_R | TYPE_EN | TYPE_AN);\n          var STRONG_TYPES_FOR_N_STEPS = R_TYPES_FOR_N_STEPS | TYPE_L;\n\n          // * Identify the bracket pairs in the current isolating run sequence according to BD16.\n          var bracketPairs = [];\n          {\n            var openerStack = [];\n            for (var si$7 = 0; si$7 < seqIndices$1.length; si$7++) {\n              // NOTE: for any potential bracket character we also test that it still carries a NI\n              // type, as that may have been changed earlier. This doesn't seem to be explicitly\n              // called out in the spec, but is required for passage of certain tests.\n              if (charTypes[seqIndices$1[si$7]] & NEUTRAL_ISOLATE_TYPES) {\n                var char = string[seqIndices$1[si$7]];\n                var oppositeBracket = (void 0);\n                // Opening bracket\n                if (openingToClosingBracket(char) !== null) {\n                  if (openerStack.length < 63) {\n                    openerStack.push({ char: char, seqIndex: si$7 });\n                  } else {\n                    break\n                  }\n                }\n                // Closing bracket\n                else if ((oppositeBracket = closingToOpeningBracket(char)) !== null) {\n                  for (var stackIdx = openerStack.length - 1; stackIdx >= 0; stackIdx--) {\n                    var stackChar = openerStack[stackIdx].char;\n                    if (stackChar === oppositeBracket ||\n                      stackChar === closingToOpeningBracket(getCanonicalBracket(char)) ||\n                      openingToClosingBracket(getCanonicalBracket(stackChar)) === char\n                    ) {\n                      bracketPairs.push([openerStack[stackIdx].seqIndex, si$7]);\n                      openerStack.length = stackIdx; //pop the matching bracket and all following\n                      break\n                    }\n                  }\n                }\n              }\n            }\n            bracketPairs.sort(function (a, b) { return a[0] - b[0]; });\n          }\n          // * For each bracket-pair element in the list of pairs of text positions\n          for (var pairIdx = 0; pairIdx < bracketPairs.length; pairIdx++) {\n            var ref$1 = bracketPairs[pairIdx];\n            var openSeqIdx = ref$1[0];\n            var closeSeqIdx = ref$1[1];\n            // a. Inspect the bidirectional types of the characters enclosed within the bracket pair.\n            // b. If any strong type (either L or R) matching the embedding direction is found, set the type for both\n            // brackets in the pair to match the embedding direction.\n            var foundStrongType = false;\n            var useStrongType = 0;\n            for (var si$8 = openSeqIdx + 1; si$8 < closeSeqIdx; si$8++) {\n              var i$15 = seqIndices$1[si$8];\n              if (charTypes[i$15] & STRONG_TYPES_FOR_N_STEPS) {\n                foundStrongType = true;\n                var lr = (charTypes[i$15] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                if (lr === embedDirection) {\n                  useStrongType = lr;\n                  break\n                }\n              }\n            }\n            // c. Otherwise, if there is a strong type it must be opposite the embedding direction. Therefore, test\n            // for an established context with a preceding strong type by checking backwards before the opening paired\n            // bracket until the first strong type (L, R, or sos) is found.\n            //    1. If the preceding strong type is also opposite the embedding direction, context is established, so\n            //    set the type for both brackets in the pair to that direction.\n            //    2. Otherwise set the type for both brackets in the pair to the embedding direction.\n            if (foundStrongType && !useStrongType) {\n              useStrongType = sosType;\n              for (var si$9 = openSeqIdx - 1; si$9 >= 0; si$9--) {\n                var i$16 = seqIndices$1[si$9];\n                if (charTypes[i$16] & STRONG_TYPES_FOR_N_STEPS) {\n                  var lr$1 = (charTypes[i$16] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  if (lr$1 !== embedDirection) {\n                    useStrongType = lr$1;\n                  } else {\n                    useStrongType = embedDirection;\n                  }\n                  break\n                }\n              }\n            }\n            if (useStrongType) {\n              charTypes[seqIndices$1[openSeqIdx]] = charTypes[seqIndices$1[closeSeqIdx]] = useStrongType;\n              // * Any number of characters that had original bidirectional character type NSM prior to the application\n              // of W1 that immediately follow a paired bracket which changed to L or R under N0 should change to match\n              // the type of their preceding bracket.\n              if (useStrongType !== embedDirection) {\n                for (var si$10 = openSeqIdx + 1; si$10 < seqIndices$1.length; si$10++) {\n                  if (!(charTypes[seqIndices$1[si$10]] & BN_LIKE_TYPES)) {\n                    if (getBidiCharType(string[seqIndices$1[si$10]]) & TYPE_NSM) {\n                      charTypes[seqIndices$1[si$10]] = useStrongType;\n                    }\n                    break\n                  }\n                }\n              }\n              if (useStrongType !== embedDirection) {\n                for (var si$11 = closeSeqIdx + 1; si$11 < seqIndices$1.length; si$11++) {\n                  if (!(charTypes[seqIndices$1[si$11]] & BN_LIKE_TYPES)) {\n                    if (getBidiCharType(string[seqIndices$1[si$11]]) & TYPE_NSM) {\n                      charTypes[seqIndices$1[si$11]] = useStrongType;\n                    }\n                    break\n                  }\n                }\n              }\n            }\n          }\n\n          // N1. A sequence of NIs takes the direction of the surrounding strong text if the text on both sides has the\n          // same direction.\n          // N2. Any remaining NIs take the embedding direction.\n          for (var si$12 = 0; si$12 < seqIndices$1.length; si$12++) {\n            if (charTypes[seqIndices$1[si$12]] & NEUTRAL_ISOLATE_TYPES) {\n              var niRunStart = si$12, niRunEnd = si$12;\n              var prevType$2 = sosType; //si === 0 ? sosType : (charTypes[seqIndices[si - 1]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L\n              for (var si2 = si$12 - 1; si2 >= 0; si2--) {\n                if (charTypes[seqIndices$1[si2]] & BN_LIKE_TYPES) {\n                  niRunStart = si2; //5.2 treat BNs adjacent to NIs as NIs\n                } else {\n                  prevType$2 = (charTypes[seqIndices$1[si2]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  break\n                }\n              }\n              var nextType$1 = eosType;\n              for (var si2$1 = si$12 + 1; si2$1 < seqIndices$1.length; si2$1++) {\n                if (charTypes[seqIndices$1[si2$1]] & (NEUTRAL_ISOLATE_TYPES | BN_LIKE_TYPES)) {\n                  niRunEnd = si2$1;\n                } else {\n                  nextType$1 = (charTypes[seqIndices$1[si2$1]] & R_TYPES_FOR_N_STEPS) ? TYPE_R : TYPE_L;\n                  break\n                }\n              }\n              for (var sj$7 = niRunStart; sj$7 <= niRunEnd; sj$7++) {\n                charTypes[seqIndices$1[sj$7]] = prevType$2 === nextType$1 ? prevType$2 : embedDirection;\n              }\n              si$12 = niRunEnd;\n            }\n          }\n        }\n      }\n\n      // === 3.3.6 Resolving Implicit Levels ===\n\n      for (var i$17 = paragraph.start; i$17 <= paragraph.end; i$17++) {\n        var level$3 = embedLevels[i$17];\n        var type$1 = charTypes[i$17];\n        // I2. For all characters with an odd (right-to-left) embedding level, those of type L, EN or AN go up one level.\n        if (level$3 & 1) {\n          if (type$1 & (TYPE_L | TYPE_EN | TYPE_AN)) {\n            embedLevels[i$17]++;\n          }\n        }\n          // I1. For all characters with an even (left-to-right) embedding level, those of type R go up one level\n        // and those of type AN or EN go up two levels.\n        else {\n          if (type$1 & TYPE_R) {\n            embedLevels[i$17]++;\n          } else if (type$1 & (TYPE_AN | TYPE_EN)) {\n            embedLevels[i$17] += 2;\n          }\n        }\n\n        // 5.2: Resolve any LRE, RLE, LRO, RLO, PDF, or BN to the level of the preceding character if there is one,\n        // and otherwise to the base level.\n        if (type$1 & BN_LIKE_TYPES) {\n          embedLevels[i$17] = i$17 === 0 ? paragraph.level : embedLevels[i$17 - 1];\n        }\n\n        // 3.4 L1.1-4: Reset the embedding level of segment/paragraph separators, and any sequence of whitespace or\n        // isolate formatting characters preceding them or the end of the paragraph, to the paragraph level.\n        // NOTE: this will also need to be applied to each individual line ending after line wrapping occurs.\n        if (i$17 === paragraph.end || getBidiCharType(string[i$17]) & (TYPE_S | TYPE_B)) {\n          for (var j$1 = i$17; j$1 >= 0 && (getBidiCharType(string[j$1]) & TRAILING_TYPES); j$1--) {\n            embedLevels[j$1] = paragraph.level;\n          }\n        }\n      }\n    }\n\n    // DONE! The resolved levels can then be used, after line wrapping, to flip runs of characters\n    // according to section 3.4 Reordering Resolved Levels\n    return {\n      levels: embedLevels,\n      paragraphs: paragraphs\n    }\n\n    function determineAutoEmbedLevel (start, isFSI) {\n      // 3.3.1 P2 - P3\n      for (var i = start; i < string.length; i++) {\n        var charType = charTypes[i];\n        if (charType & (TYPE_R | TYPE_AL)) {\n          return 1\n        }\n        if ((charType & (TYPE_B | TYPE_L)) || (isFSI && charType === TYPE_PDI)) {\n          return 0\n        }\n        if (charType & ISOLATE_INIT_TYPES) {\n          var pdi = indexOfMatchingPDI(i);\n          i = pdi === -1 ? string.length : pdi;\n        }\n      }\n      return 0\n    }\n\n    function indexOfMatchingPDI (isolateStart) {\n      // 3.1.2 BD9\n      var isolationLevel = 1;\n      for (var i = isolateStart + 1; i < string.length; i++) {\n        var charType = charTypes[i];\n        if (charType & TYPE_B) {\n          break\n        }\n        if (charType & TYPE_PDI) {\n          if (--isolationLevel === 0) {\n            return i\n          }\n        } else if (charType & ISOLATE_INIT_TYPES) {\n          isolationLevel++;\n        }\n      }\n      return -1\n    }\n  }\n\n  // Bidi mirrored chars data, auto generated\n  var data = \"14>1,j>2,t>2,u>2,1a>g,2v3>1,1>1,1ge>1,1wd>1,b>1,1j>1,f>1,ai>3,-2>3,+1,8>1k0,-1jq>1y7,-1y6>1hf,-1he>1h6,-1h5>1ha,-1h8>1qi,-1pu>1,6>3u,-3s>7,6>1,1>1,f>1,1>1,+2,3>1,1>1,+13,4>1,1>1,6>1eo,-1ee>1,3>1mg,-1me>1mk,-1mj>1mi,-1mg>1mi,-1md>1,1>1,+2,1>10k,-103>1,1>1,4>1,5>1,1>1,+10,3>1,1>8,-7>8,+1,-6>7,+1,a>1,1>1,u>1,u6>1,1>1,+5,26>1,1>1,2>1,2>2,8>1,7>1,4>1,1>1,+5,b8>1,1>1,+3,1>3,-2>1,2>1,1>1,+2,c>1,3>1,1>1,+2,h>1,3>1,a>1,1>1,2>1,3>1,1>1,d>1,f>1,3>1,1a>1,1>1,6>1,7>1,13>1,k>1,1>1,+19,4>1,1>1,+2,2>1,1>1,+18,m>1,a>1,1>1,lk>1,1>1,4>1,2>1,f>1,3>1,1>1,+3,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,6>1,4j>1,j>2,t>2,u>2,2>1,+1\";\n\n  var mirrorMap;\n\n  function parse () {\n    if (!mirrorMap) {\n      //const start = performance.now()\n      var ref = parseCharacterMap(data, true);\n      var map = ref.map;\n      var reverseMap = ref.reverseMap;\n      // Combine both maps into one\n      reverseMap.forEach(function (value, key) {\n        map.set(key, value);\n      });\n      mirrorMap = map;\n      //console.log(`mirrored chars parsed in ${performance.now() - start}ms`)\n    }\n  }\n\n  function getMirroredCharacter (char) {\n    parse();\n    return mirrorMap.get(char) || null\n  }\n\n  /**\n   * Given a string and its resolved embedding levels, build a map of indices to replacement chars\n   * for any characters in right-to-left segments that have defined mirrored characters.\n   * @param string\n   * @param embeddingLevels\n   * @param [start]\n   * @param [end]\n   * @return {Map<number, string>}\n   */\n  function getMirroredCharactersMap(string, embeddingLevels, start, end) {\n    var strLen = string.length;\n    start = Math.max(0, start == null ? 0 : +start);\n    end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n\n    var map = new Map();\n    for (var i = start; i <= end; i++) {\n      if (embeddingLevels[i] & 1) { //only odd (rtl) levels\n        var mirror = getMirroredCharacter(string[i]);\n        if (mirror !== null) {\n          map.set(i, mirror);\n        }\n      }\n    }\n    return map\n  }\n\n  /**\n   * Given a start and end denoting a single line within a string, and a set of precalculated\n   * bidi embedding levels, produce a list of segments whose ordering should be flipped, in sequence.\n   * @param {string} string - the full input string\n   * @param {GetEmbeddingLevelsResult} embeddingLevelsResult - the result object from getEmbeddingLevels\n   * @param {number} [start] - first character in a subset of the full string\n   * @param {number} [end] - last character in a subset of the full string\n   * @return {number[][]} - the list of start/end segments that should be flipped, in order.\n   */\n  function getReorderSegments(string, embeddingLevelsResult, start, end) {\n    var strLen = string.length;\n    start = Math.max(0, start == null ? 0 : +start);\n    end = Math.min(strLen - 1, end == null ? strLen - 1 : +end);\n\n    var segments = [];\n    embeddingLevelsResult.paragraphs.forEach(function (paragraph) {\n      var lineStart = Math.max(start, paragraph.start);\n      var lineEnd = Math.min(end, paragraph.end);\n      if (lineStart < lineEnd) {\n        // Local slice for mutation\n        var lineLevels = embeddingLevelsResult.levels.slice(lineStart, lineEnd + 1);\n\n        // 3.4 L1.4: Reset any sequence of whitespace characters and/or isolate formatting characters at the\n        // end of the line to the paragraph level.\n        for (var i = lineEnd; i >= lineStart && (getBidiCharType(string[i]) & TRAILING_TYPES); i--) {\n          lineLevels[i] = paragraph.level;\n        }\n\n        // L2. From the highest level found in the text to the lowest odd level on each line, including intermediate levels\n        // not actually present in the text, reverse any contiguous sequence of characters that are at that level or higher.\n        var maxLevel = paragraph.level;\n        var minOddLevel = Infinity;\n        for (var i$1 = 0; i$1 < lineLevels.length; i$1++) {\n          var level = lineLevels[i$1];\n          if (level > maxLevel) { maxLevel = level; }\n          if (level < minOddLevel) { minOddLevel = level | 1; }\n        }\n        for (var lvl = maxLevel; lvl >= minOddLevel; lvl--) {\n          for (var i$2 = 0; i$2 < lineLevels.length; i$2++) {\n            if (lineLevels[i$2] >= lvl) {\n              var segStart = i$2;\n              while (i$2 + 1 < lineLevels.length && lineLevels[i$2 + 1] >= lvl) {\n                i$2++;\n              }\n              if (i$2 > segStart) {\n                segments.push([segStart + lineStart, i$2 + lineStart]);\n              }\n            }\n          }\n        }\n      }\n    });\n    return segments\n  }\n\n  /**\n   * @param {string} string\n   * @param {GetEmbeddingLevelsResult} embedLevelsResult\n   * @param {number} [start]\n   * @param {number} [end]\n   * @return {string} the new string with bidi segments reordered\n   */\n  function getReorderedString(string, embedLevelsResult, start, end) {\n    var indices = getReorderedIndices(string, embedLevelsResult, start, end);\n    var chars = [].concat( string );\n    indices.forEach(function (charIndex, i) {\n      chars[i] = (\n        (embedLevelsResult.levels[charIndex] & 1) ? getMirroredCharacter(string[charIndex]) : null\n      ) || string[charIndex];\n    });\n    return chars.join('')\n  }\n\n  /**\n   * @param {string} string\n   * @param {GetEmbeddingLevelsResult} embedLevelsResult\n   * @param {number} [start]\n   * @param {number} [end]\n   * @return {number[]} an array with character indices in their new bidi order\n   */\n  function getReorderedIndices(string, embedLevelsResult, start, end) {\n    var segments = getReorderSegments(string, embedLevelsResult, start, end);\n    // Fill an array with indices\n    var indices = [];\n    for (var i = 0; i < string.length; i++) {\n      indices[i] = i;\n    }\n    // Reverse each segment in order\n    segments.forEach(function (ref) {\n      var start = ref[0];\n      var end = ref[1];\n\n      var slice = indices.slice(start, end + 1);\n      for (var i = slice.length; i--;) {\n        indices[end - i] = slice[i];\n      }\n    });\n    return indices\n  }\n\n  exports.closingToOpeningBracket = closingToOpeningBracket;\n  exports.getBidiCharType = getBidiCharType;\n  exports.getBidiCharTypeName = getBidiCharTypeName;\n  exports.getCanonicalBracket = getCanonicalBracket;\n  exports.getEmbeddingLevels = getEmbeddingLevels;\n  exports.getMirroredCharacter = getMirroredCharacter;\n  exports.getMirroredCharactersMap = getMirroredCharactersMap;\n  exports.getReorderSegments = getReorderSegments;\n  exports.getReorderedIndices = getReorderedIndices;\n  exports.getReorderedString = getReorderedString;\n  exports.openingToClosingBracket = openingToClosingBracket;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n}({}));\nreturn bidi}\n\nexport default bidiFactory;\n"], "mappings": "AAAA,SAASA,WAAWA,CAAA,EAAG;EACvB,IAAIC,IAAI,GAAI,UAAUC,OAAO,EAAE;IAE7B;IACA,IAAIC,IAAI,GAAG;MACT,GAAG,EAAE,+KAA+K;MACpL,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,kCAAkC;MACxC,IAAI,EAAE,sGAAsG;MAC5G,IAAI,EAAE,kCAAkC;MACxC,IAAI,EAAE,yCAAyC;MAC/C,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,OAAO;MACZ,IAAI,EAAE,wBAAwB;MAC9B,IAAI,EAAE,4wBAA4wB;MAClxB,IAAI,EAAE,8LAA8L;MACpM,KAAK,EAAE,+rCAA+rC;MACtsC,IAAI,EAAE,qIAAqI;MAC3I,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE,KAAK;MACZ,KAAK,EAAE;IACT,CAAC;IAED,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,IAAIC,cAAc,GAAG,CAAC,CAAC;IACvBD,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC;IACbD,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG;IACvBE,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,OAAO,CAAC,UAAUC,IAAI,EAAEC,CAAC,EAAE;MAC3CP,KAAK,CAACM,IAAI,CAAC,GAAG,CAAC,IAAKC,CAAC,GAAG,CAAE;MAC1BN,cAAc,CAACD,KAAK,CAACM,IAAI,CAAC,CAAC,GAAGA,IAAI;IACpC,CAAC,CAAC;IACFH,MAAM,CAACK,MAAM,CAACR,KAAK,CAAC;IAEpB,IAAIS,kBAAkB,GAAGT,KAAK,CAACU,GAAG,GAAGV,KAAK,CAACW,GAAG,GAAGX,KAAK,CAACY,GAAG;IAC1D,IAAIC,YAAY,GAAGb,KAAK,CAACE,CAAC,GAAGF,KAAK,CAACc,CAAC,GAAGd,KAAK,CAACe,EAAE;IAC/C,IAAIC,qBAAqB,GAAGhB,KAAK,CAACiB,CAAC,GAAGjB,KAAK,CAACkB,CAAC,GAAGlB,KAAK,CAACmB,EAAE,GAAGnB,KAAK,CAACoB,EAAE,GAAGpB,KAAK,CAACY,GAAG,GAAGZ,KAAK,CAACU,GAAG,GAAGV,KAAK,CAACW,GAAG,GAAGX,KAAK,CAACqB,GAAG;IACnH,IAAIC,aAAa,GAAGtB,KAAK,CAACuB,EAAE,GAAGvB,KAAK,CAACwB,GAAG,GAAGxB,KAAK,CAACyB,GAAG,GAAGzB,KAAK,CAAC0B,GAAG,GAAG1B,KAAK,CAAC2B,GAAG,GAAG3B,KAAK,CAAC4B,GAAG;IACxF,IAAIC,cAAc,GAAG7B,KAAK,CAACkB,CAAC,GAAGlB,KAAK,CAACmB,EAAE,GAAGnB,KAAK,CAACiB,CAAC,GAAGR,kBAAkB,GAAGT,KAAK,CAACqB,GAAG,GAAGC,aAAa;IAElG,IAAIQ,GAAG,GAAG,IAAI;IAEd,SAASC,SAASA,CAAA,EAAI;MACpB,IAAI,CAACD,GAAG,EAAE;QACR;QACAA,GAAG,GAAG,IAAIE,GAAG,CAAC,CAAC;QACf,IAAIC,IAAI,GAAG,SAAAA,CAAW3B,IAAI,EAAG;UAC3B,IAAIP,IAAI,CAACmC,cAAc,CAAC5B,IAAI,CAAC,EAAE;YAC7B,IAAI6B,QAAQ,GAAG,CAAC;YAChBpC,IAAI,CAACO,IAAI,CAAC,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAAC/B,OAAO,CAAC,UAAUgC,KAAK,EAAE;cAC7C,IAAIC,GAAG,GAAGD,KAAK,CAACD,KAAK,CAAC,GAAG,CAAC;cAC1B,IAAIG,IAAI,GAAGD,GAAG,CAAC,CAAC,CAAC;cACjB,IAAIE,IAAI,GAAGF,GAAG,CAAC,CAAC,CAAC;cACjBC,IAAI,GAAGE,QAAQ,CAACF,IAAI,EAAE,EAAE,CAAC;cACzBC,IAAI,GAAGA,IAAI,GAAGC,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC;cACpCV,GAAG,CAACY,GAAG,CAACP,QAAQ,IAAII,IAAI,EAAEvC,KAAK,CAACM,IAAI,CAAC,CAAC;cACtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,IAAI,EAAEjC,CAAC,EAAE,EAAE;gBAC7BuB,GAAG,CAACY,GAAG,CAAC,EAAEP,QAAQ,EAAEnC,KAAK,CAACM,IAAI,CAAC,CAAC;cAClC;YACF,CAAC,CAAC;UACJ;QACF,CAAC;QAED,KAAK,IAAIA,IAAI,IAAIP,IAAI,EAAEkC,IAAI,CAAE3B,IAAK,CAAC;QACnC;MACF;IACF;;IAEA;AACF;AACA;AACA;IACE,SAASqC,eAAeA,CAAEC,IAAI,EAAE;MAC9Bb,SAAS,CAAC,CAAC;MACX,OAAOD,GAAG,CAACe,GAAG,CAACD,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI9C,KAAK,CAACE,CAAC;IAChD;IAEA,SAAS6C,mBAAmBA,CAACH,IAAI,EAAE;MACjC,OAAO3C,cAAc,CAAC0C,eAAe,CAACC,IAAI,CAAC,CAAC;IAC9C;;IAEA;IACA,IAAII,MAAM,GAAG;MACX,OAAO,EAAE,2MAA2M;MACpN,WAAW,EAAE;IACf,CAAC;;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASC,iBAAiBA,CAAEC,aAAa,EAAEC,cAAc,EAAE;MACzD,IAAIC,KAAK,GAAG,EAAE;MACd,IAAIjB,QAAQ,GAAG,CAAC;MAChB,IAAIL,GAAG,GAAG,IAAIE,GAAG,CAAC,CAAC;MACnB,IAAIqB,UAAU,GAAGF,cAAc,IAAI,IAAInB,GAAG,CAAC,CAAC;MAC5C,IAAIsB,QAAQ;MACZJ,aAAa,CAACd,KAAK,CAAC,GAAG,CAAC,CAAC/B,OAAO,CAAC,SAASkD,KAAKA,CAACC,KAAK,EAAE;QACrD,IAAIA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UAC7B,KAAK,IAAIlD,CAAC,GAAG,CAACiD,KAAK,EAAEjD,CAAC,EAAE,GAAG;YACzBgD,KAAK,CAACD,QAAQ,CAAC;UACjB;QACF,CAAC,MAAM;UACLA,QAAQ,GAAGE,KAAK;UAChB,IAAIlB,GAAG,GAAGkB,KAAK,CAACpB,KAAK,CAAC,GAAG,CAAC;UAC1B,IAAIsB,CAAC,GAAGpB,GAAG,CAAC,CAAC,CAAC;UACd,IAAIqB,CAAC,GAAGrB,GAAG,CAAC,CAAC,CAAC;UACdoB,CAAC,GAAGE,MAAM,CAACC,aAAa,CAAC1B,QAAQ,IAAIM,QAAQ,CAACiB,CAAC,EAAEN,KAAK,CAAC,CAAC;UACxDO,CAAC,GAAGC,MAAM,CAACC,aAAa,CAAC1B,QAAQ,IAAIM,QAAQ,CAACkB,CAAC,EAAEP,KAAK,CAAC,CAAC;UACxDtB,GAAG,CAACY,GAAG,CAACgB,CAAC,EAAEC,CAAC,CAAC;UACbR,cAAc,IAAIE,UAAU,CAACX,GAAG,CAACiB,CAAC,EAAED,CAAC,CAAC;QACxC;MACF,CAAC,CAAC;MACF,OAAO;QAAE5B,GAAG,EAAEA,GAAG;QAAEuB,UAAU,EAAEA;MAAW,CAAC;IAC7C;IAEA,IAAIS,WAAW,EAAEC,WAAW,EAAEC,SAAS;IAEvC,SAASC,OAAOA,CAAA,EAAI;MAClB,IAAI,CAACH,WAAW,EAAE;QAChB;QACA,IAAIxB,GAAG,GAAGW,iBAAiB,CAACD,MAAM,CAACkB,KAAK,EAAE,IAAI,CAAC;QAC/C,IAAIpC,GAAG,GAAGQ,GAAG,CAACR,GAAG;QACjB,IAAIuB,UAAU,GAAGf,GAAG,CAACe,UAAU;QAC/BS,WAAW,GAAGhC,GAAG;QACjBiC,WAAW,GAAGV,UAAU;QACxBW,SAAS,GAAGf,iBAAiB,CAACD,MAAM,CAACgB,SAAS,EAAE,KAAK,CAAC,CAAClC,GAAG;QAC1D;MACF;IACF;IAEA,SAASqC,uBAAuBA,CAAEvB,IAAI,EAAE;MACtCqB,OAAO,CAAC,CAAC;MACT,OAAOH,WAAW,CAACjB,GAAG,CAACD,IAAI,CAAC,IAAI,IAAI;IACtC;IAEA,SAASwB,uBAAuBA,CAAExB,IAAI,EAAE;MACtCqB,OAAO,CAAC,CAAC;MACT,OAAOF,WAAW,CAAClB,GAAG,CAACD,IAAI,CAAC,IAAI,IAAI;IACtC;IAEA,SAASyB,mBAAmBA,CAAEzB,IAAI,EAAE;MAClCqB,OAAO,CAAC,CAAC;MACT,OAAOD,SAAS,CAACnB,GAAG,CAACD,IAAI,CAAC,IAAI,IAAI;IACpC;;IAEA;IACA,IAAI0B,MAAM,GAAGtE,KAAK,CAACE,CAAC;IACpB,IAAIqE,MAAM,GAAGvE,KAAK,CAACc,CAAC;IACpB,IAAI0D,OAAO,GAAGxE,KAAK,CAACyE,EAAE;IACtB,IAAIC,OAAO,GAAG1E,KAAK,CAAC2E,EAAE;IACtB,IAAIC,OAAO,GAAG5E,KAAK,CAAC6E,EAAE;IACtB,IAAIC,OAAO,GAAG9E,KAAK,CAAC+E,EAAE;IACtB,IAAIC,OAAO,GAAGhF,KAAK,CAACiF,EAAE;IACtB,IAAIC,MAAM,GAAGlF,KAAK,CAACiB,CAAC;IACpB,IAAIkE,MAAM,GAAGnF,KAAK,CAACkB,CAAC;IACpB,IAAIkE,OAAO,GAAGpF,KAAK,CAACoB,EAAE;IACtB,IAAIiE,OAAO,GAAGrF,KAAK,CAACuB,EAAE;IACtB,IAAI+D,QAAQ,GAAGtF,KAAK,CAACuF,GAAG;IACxB,IAAIC,OAAO,GAAGxF,KAAK,CAACe,EAAE;IACtB,IAAI0E,QAAQ,GAAGzF,KAAK,CAAC2B,GAAG;IACxB,IAAI+D,QAAQ,GAAG1F,KAAK,CAAC0B,GAAG;IACxB,IAAIiE,QAAQ,GAAG3F,KAAK,CAACyB,GAAG;IACxB,IAAImE,QAAQ,GAAG5F,KAAK,CAACwB,GAAG;IACxB,IAAIqE,QAAQ,GAAG7F,KAAK,CAAC4B,GAAG;IACxB,IAAIkE,QAAQ,GAAG9F,KAAK,CAACU,GAAG;IACxB,IAAIqF,QAAQ,GAAG/F,KAAK,CAACW,GAAG;IACxB,IAAIqF,QAAQ,GAAGhG,KAAK,CAACY,GAAG;IACxB,IAAIqF,QAAQ,GAAGjG,KAAK,CAACqB,GAAG;;IAExB;AACF;AACA;AACA;AACA;;IAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAAS6E,kBAAkBA,CAAEC,MAAM,EAAEC,aAAa,EAAE;MAClD,IAAIC,SAAS,GAAG,GAAG;;MAEnB;MACA,IAAIC,SAAS,GAAG,IAAIC,WAAW,CAACJ,MAAM,CAACK,MAAM,CAAC;MAC9C,KAAK,IAAIjG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,MAAM,CAACK,MAAM,EAAEjG,CAAC,EAAE,EAAE;QACtC+F,SAAS,CAAC/F,CAAC,CAAC,GAAGoC,eAAe,CAACwD,MAAM,CAAC5F,CAAC,CAAC,CAAC;MAC3C;MAEA,IAAIkG,cAAc,GAAG,IAAIzE,GAAG,CAAC,CAAC,CAAC,CAAC;MAChC,SAAS0E,cAAcA,CAACnG,CAAC,EAAED,IAAI,EAAE;QAC/B,IAAIqG,OAAO,GAAGL,SAAS,CAAC/F,CAAC,CAAC;QAC1B+F,SAAS,CAAC/F,CAAC,CAAC,GAAGD,IAAI;QACnBmG,cAAc,CAAC/D,GAAG,CAACiE,OAAO,EAAEF,cAAc,CAAC5D,GAAG,CAAC8D,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5D,IAAIA,OAAO,GAAG3F,qBAAqB,EAAE;UACnCyF,cAAc,CAAC/D,GAAG,CAAC1B,qBAAqB,EAAEyF,cAAc,CAAC5D,GAAG,CAAC7B,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAC1F;QACAyF,cAAc,CAAC/D,GAAG,CAACpC,IAAI,EAAE,CAACmG,cAAc,CAAC5D,GAAG,CAACvC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,IAAIA,IAAI,GAAGU,qBAAqB,EAAE;UAChCyF,cAAc,CAAC/D,GAAG,CAAC1B,qBAAqB,EAAE,CAACyF,cAAc,CAAC5D,GAAG,CAAC7B,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjG;MACF;MAEA,IAAI4F,WAAW,GAAG,IAAIC,UAAU,CAACV,MAAM,CAACK,MAAM,CAAC;MAC/C,IAAIM,cAAc,GAAG,IAAI9E,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEhC;MACA;MACA,IAAI+E,UAAU,GAAG,EAAE,CAAC,CAAC;MACrB,IAAIC,SAAS,GAAG,IAAI;MACpB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGd,MAAM,CAACK,MAAM,EAAES,GAAG,EAAE,EAAE;QAC5C,IAAI,CAACD,SAAS,EAAE;UACdD,UAAU,CAACG,IAAI,CAACF,SAAS,GAAG;YAC1BG,KAAK,EAAEF,GAAG;YACVG,GAAG,EAAEjB,MAAM,CAACK,MAAM,GAAG,CAAC;YACtB;YACAa,KAAK,EAAEjB,aAAa,KAAK,KAAK,GAAG,CAAC,GAAGA,aAAa,KAAK,KAAK,GAAG,CAAC,GAAGkB,uBAAuB,CAACL,GAAG,EAAE,KAAK;UACvG,CAAC,CAAC;QACJ;QACA,IAAIX,SAAS,CAACW,GAAG,CAAC,GAAG/B,MAAM,EAAE;UAC3B8B,SAAS,CAACI,GAAG,GAAGH,GAAG;UACnBD,SAAS,GAAG,IAAI;QAClB;MACF;MAEA,IAAIO,gBAAgB,GAAG3B,QAAQ,GAAGD,QAAQ,GAAGD,QAAQ,GAAGD,QAAQ,GAAGhF,kBAAkB,GAAGwF,QAAQ,GAAGJ,QAAQ,GAAGX,MAAM;MACpH,IAAIsC,QAAQ,GAAG,SAAAA,CAAUC,CAAC,EAAE;QAAE,OAAOA,CAAC,IAAKA,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC;MAC7D,IAAIC,OAAO,GAAG,SAAAA,CAAUD,CAAC,EAAE;QAAE,OAAOA,CAAC,IAAKA,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC;;MAE5D;MACA,KAAK,IAAIE,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGZ,UAAU,CAACP,MAAM,EAAEmB,OAAO,EAAE,EAAE;QAC5DX,SAAS,GAAGD,UAAU,CAACY,OAAO,CAAC;QAC/B,IAAIC,WAAW,GAAG,CAAC;UACjBC,MAAM,EAAEb,SAAS,CAACK,KAAK;UACvBS,SAAS,EAAE,CAAC;UAAE;UACdC,QAAQ,EAAE,CAAC,CAAC;QACd,CAAC,CAAC;QACF,IAAIC,QAAQ,GAAI,KAAK,CAAE;QACvB,IAAIC,oBAAoB,GAAG,CAAC;QAC5B,IAAIC,sBAAsB,GAAG,CAAC;QAC9B,IAAIC,iBAAiB,GAAG,CAAC;QACzB1B,cAAc,CAAC2B,KAAK,CAAC,CAAC;;QAEtB;QACA,KAAK,IAAIC,GAAG,GAAGrB,SAAS,CAACG,KAAK,EAAEkB,GAAG,IAAIrB,SAAS,CAACI,GAAG,EAAEiB,GAAG,EAAE,EAAE;UAC3D,IAAIC,QAAQ,GAAGhC,SAAS,CAAC+B,GAAG,CAAC;UAC7BL,QAAQ,GAAGJ,WAAW,CAACA,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC;;UAE9C;UACAC,cAAc,CAAC/D,GAAG,CAAC4F,QAAQ,EAAE,CAAC7B,cAAc,CAAC5D,GAAG,CAACyF,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACrE,IAAIA,QAAQ,GAAGtH,qBAAqB,EAAE;YACpCyF,cAAc,CAAC/D,GAAG,CAAC1B,qBAAqB,EAAE,CAACyF,cAAc,CAAC5D,GAAG,CAAC7B,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACjG;;UAEA;UACA,IAAIsH,QAAQ,GAAGf,gBAAgB,EAAE;YAAE;YACjC,IAAIe,QAAQ,IAAI1C,QAAQ,GAAGD,QAAQ,CAAC,EAAE;cACpCiB,WAAW,CAACyB,GAAG,CAAC,GAAGL,QAAQ,CAACH,MAAM,CAAC,CAAC;cACpC,IAAIR,KAAK,GAAG,CAACiB,QAAQ,KAAK1C,QAAQ,GAAG8B,OAAO,GAAGF,QAAQ,EAAEQ,QAAQ,CAACH,MAAM,CAAC;cACzE,IAAIR,KAAK,IAAIhB,SAAS,IAAI,CAAC4B,oBAAoB,IAAI,CAACC,sBAAsB,EAAE;gBAC1EN,WAAW,CAACV,IAAI,CAAC;kBACfW,MAAM,EAAER,KAAK;kBACbS,SAAS,EAAE,CAAC;kBACZC,QAAQ,EAAE;gBACZ,CAAC,CAAC;cACJ,CAAC,MAAM,IAAI,CAACE,oBAAoB,EAAE;gBAChCC,sBAAsB,EAAE;cAC1B;YACF;;YAEA;YAAA,KACK,IAAII,QAAQ,IAAI5C,QAAQ,GAAGD,QAAQ,CAAC,EAAE;cACzCmB,WAAW,CAACyB,GAAG,CAAC,GAAGL,QAAQ,CAACH,MAAM,CAAC,CAAC;cACpC,IAAIU,OAAO,GAAG,CAACD,QAAQ,KAAK5C,QAAQ,GAAGgC,OAAO,GAAGF,QAAQ,EAAEQ,QAAQ,CAACH,MAAM,CAAC;cAC3E,IAAIU,OAAO,IAAIlC,SAAS,IAAI,CAAC4B,oBAAoB,IAAI,CAACC,sBAAsB,EAAE;gBAC5EN,WAAW,CAACV,IAAI,CAAC;kBACfW,MAAM,EAAEU,OAAO;kBACfT,SAAS,EAAGQ,QAAQ,GAAG5C,QAAQ,GAAInB,MAAM,GAAGD,MAAM;kBAClDyD,QAAQ,EAAE;gBACZ,CAAC,CAAC;cACJ,CAAC,MAAM,IAAI,CAACE,oBAAoB,EAAE;gBAChCC,sBAAsB,EAAE;cAC1B;YACF;;YAEA;YAAA,KACK,IAAII,QAAQ,GAAG7H,kBAAkB,EAAE;cACtC;cACA,IAAI6H,QAAQ,GAAGtC,QAAQ,EAAE;gBACvBsC,QAAQ,GAAGhB,uBAAuB,CAACe,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAGtC,QAAQ,GAAGD,QAAQ;cAC/E;cAEAc,WAAW,CAACyB,GAAG,CAAC,GAAGL,QAAQ,CAACH,MAAM;cAClC,IAAIG,QAAQ,CAACF,SAAS,EAAE;gBACtBpB,cAAc,CAAC2B,GAAG,EAAEL,QAAQ,CAACF,SAAS,CAAC;cACzC;cACA,IAAIU,OAAO,GAAG,CAACF,QAAQ,KAAKvC,QAAQ,GAAG2B,OAAO,GAAGF,QAAQ,EAAEQ,QAAQ,CAACH,MAAM,CAAC;cAC3E,IAAIW,OAAO,IAAInC,SAAS,IAAI4B,oBAAoB,KAAK,CAAC,IAAIC,sBAAsB,KAAK,CAAC,EAAE;gBACtFC,iBAAiB,EAAE;gBACnBP,WAAW,CAACV,IAAI,CAAC;kBACfW,MAAM,EAAEW,OAAO;kBACfV,SAAS,EAAE,CAAC;kBACZC,QAAQ,EAAE,CAAC;kBACXU,cAAc,EAAEJ;gBAClB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLJ,oBAAoB,EAAE;cACxB;YACF;;YAEA;YAAA,KACK,IAAIK,QAAQ,GAAGrC,QAAQ,EAAE;cAC5B,IAAIgC,oBAAoB,GAAG,CAAC,EAAE;gBAC5BA,oBAAoB,EAAE;cACxB,CAAC,MAAM,IAAIE,iBAAiB,GAAG,CAAC,EAAE;gBAChCD,sBAAsB,GAAG,CAAC;gBAC1B,OAAO,CAACN,WAAW,CAACA,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC,CAACuB,QAAQ,EAAE;kBACpDH,WAAW,CAACc,GAAG,CAAC,CAAC;gBACnB;gBACA;gBACA,IAAIC,aAAa,GAAGf,WAAW,CAACA,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC,CAACiC,cAAc;gBACtE,IAAIE,aAAa,IAAI,IAAI,EAAE;kBACzB7B,cAAc,CAACpE,GAAG,CAACiG,aAAa,EAAEN,GAAG,CAAC;kBACtCvB,cAAc,CAACpE,GAAG,CAAC2F,GAAG,EAAEM,aAAa,CAAC;gBACxC;gBACAf,WAAW,CAACc,GAAG,CAAC,CAAC;gBACjBP,iBAAiB,EAAE;cACrB;cACAH,QAAQ,GAAGJ,WAAW,CAACA,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC;cAC9CI,WAAW,CAACyB,GAAG,CAAC,GAAGL,QAAQ,CAACH,MAAM;cAClC,IAAIG,QAAQ,CAACF,SAAS,EAAE;gBACtBpB,cAAc,CAAC2B,GAAG,EAAEL,QAAQ,CAACF,SAAS,CAAC;cACzC;YACF;;YAGA;YAAA,KACK,IAAIQ,QAAQ,GAAGzC,QAAQ,EAAE;cAC5B,IAAIoC,oBAAoB,KAAK,CAAC,EAAE;gBAC9B,IAAIC,sBAAsB,GAAG,CAAC,EAAE;kBAC9BA,sBAAsB,EAAE;gBAC1B,CAAC,MAAM,IAAI,CAACF,QAAQ,CAACD,QAAQ,IAAIH,WAAW,CAACpB,MAAM,GAAG,CAAC,EAAE;kBACvDoB,WAAW,CAACc,GAAG,CAAC,CAAC;kBACjBV,QAAQ,GAAGJ,WAAW,CAACA,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC;gBAChD;cACF;cACAI,WAAW,CAACyB,GAAG,CAAC,GAAGL,QAAQ,CAACH,MAAM,CAAC,CAAC;YACtC;;YAEA;YAAA,KACK,IAAIS,QAAQ,GAAGpD,MAAM,EAAE;cAC1B0B,WAAW,CAACyB,GAAG,CAAC,GAAGrB,SAAS,CAACK,KAAK;YACpC;UACF;;UAEA;UAAA,KACK;YACHT,WAAW,CAACyB,GAAG,CAAC,GAAGL,QAAQ,CAACH,MAAM;YAClC;YACA,IAAIG,QAAQ,CAACF,SAAS,IAAIQ,QAAQ,KAAKjD,OAAO,EAAE;cAC9CqB,cAAc,CAAC2B,GAAG,EAAEL,QAAQ,CAACF,SAAS,CAAC;YACzC;UACF;QACF;;QAEA;;QAEA;QACA;QACA;;QAEA;QACA;QACA,IAAIc,SAAS,GAAG,EAAE;QAClB,IAAIC,UAAU,GAAG,IAAI;QACrB,KAAK,IAAIC,GAAG,GAAG9B,SAAS,CAACG,KAAK,EAAE2B,GAAG,IAAI9B,SAAS,CAACI,GAAG,EAAE0B,GAAG,EAAE,EAAE;UAC3D,IAAIC,UAAU,GAAGzC,SAAS,CAACwC,GAAG,CAAC;UAC/B,IAAI,EAAEC,UAAU,GAAGzH,aAAa,CAAC,EAAE;YACjC,IAAI0H,GAAG,GAAGpC,WAAW,CAACkC,GAAG,CAAC;YAC1B,IAAIG,UAAU,GAAGF,UAAU,GAAGtI,kBAAkB;YAChD,IAAIyI,KAAK,GAAGH,UAAU,KAAK9C,QAAQ;YACnC,IAAI4C,UAAU,IAAIG,GAAG,KAAKH,UAAU,CAAChB,MAAM,EAAE;cAC3CgB,UAAU,CAACM,IAAI,GAAGL,GAAG;cACrBD,UAAU,CAACO,iBAAiB,GAAGH,UAAU;YAC3C,CAAC,MAAM;cACLL,SAAS,CAAC1B,IAAI,CAAC2B,UAAU,GAAG;gBAC1BQ,MAAM,EAAEP,GAAG;gBACXK,IAAI,EAAEL,GAAG;gBACTjB,MAAM,EAAEmB,GAAG;gBACXM,cAAc,EAAEJ,KAAK;gBACrBE,iBAAiB,EAAEH;cACrB,CAAC,CAAC;YACJ;UACF;QACF;QACA,IAAIM,gBAAgB,GAAG,EAAE,CAAC,CAAC;QAC3B,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGZ,SAAS,CAACpC,MAAM,EAAEgD,MAAM,EAAE,EAAE;UACxD,IAAIC,GAAG,GAAGb,SAAS,CAACY,MAAM,CAAC;UAC3B,IAAI,CAACC,GAAG,CAACH,cAAc,IAAKG,GAAG,CAACH,cAAc,IAAI,CAACxC,cAAc,CAAC4C,GAAG,CAACD,GAAG,CAACJ,MAAM,CAAE,EAAE;YAClF,IAAIM,OAAO,GAAG,CAACd,UAAU,GAAGY,GAAG,CAAC;YAChC,KAAK,IAAIG,QAAQ,GAAI,KAAK,CAAE,EAAEf,UAAU,IAAIA,UAAU,CAACO,iBAAiB,IAAI,CAACQ,QAAQ,GAAG9C,cAAc,CAACjE,GAAG,CAACgG,UAAU,CAACM,IAAI,CAAC,KAAK,IAAI,GAAG;cACrI,KAAK,IAAIU,GAAG,GAAGL,MAAM,GAAG,CAAC,EAAEK,GAAG,GAAGjB,SAAS,CAACpC,MAAM,EAAEqD,GAAG,EAAE,EAAE;gBACxD,IAAIjB,SAAS,CAACiB,GAAG,CAAC,CAACR,MAAM,KAAKO,QAAQ,EAAE;kBACtCD,OAAO,CAACzC,IAAI,CAAC2B,UAAU,GAAGD,SAAS,CAACiB,GAAG,CAAC,CAAC;kBACzC;gBACF;cACF;YACF;YACA;YACA,IAAIC,UAAU,GAAG,EAAE;YACnB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,OAAO,CAACnD,MAAM,EAAEuD,GAAG,EAAE,EAAE;cAC7C,IAAIC,KAAK,GAAGL,OAAO,CAACI,GAAG,CAAC;cACxB,KAAK,IAAIE,CAAC,GAAGD,KAAK,CAACX,MAAM,EAAEY,CAAC,IAAID,KAAK,CAACb,IAAI,EAAEc,CAAC,EAAE,EAAE;gBAC/CH,UAAU,CAAC5C,IAAI,CAAC+C,CAAC,CAAC;cACpB;YACF;YACA;YACA,IAAIC,UAAU,GAAGtD,WAAW,CAACkD,UAAU,CAAC,CAAC,CAAC,CAAC;YAC3C,IAAIK,SAAS,GAAGnD,SAAS,CAACK,KAAK;YAC/B,KAAK,IAAI+C,GAAG,GAAGN,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEM,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;cACjD,IAAI,EAAE9D,SAAS,CAAC8D,GAAG,CAAC,GAAG9I,aAAa,CAAC,EAAE;gBAAE;gBACvC6I,SAAS,GAAGvD,WAAW,CAACwD,GAAG,CAAC;gBAC5B;cACF;YACF;YACA,IAAIC,SAAS,GAAGP,UAAU,CAACA,UAAU,CAACtD,MAAM,GAAG,CAAC,CAAC;YACjD,IAAI8D,SAAS,GAAG1D,WAAW,CAACyD,SAAS,CAAC;YACtC,IAAIE,SAAS,GAAGvD,SAAS,CAACK,KAAK;YAC/B,IAAI,EAAEf,SAAS,CAAC+D,SAAS,CAAC,GAAG5J,kBAAkB,CAAC,EAAE;cAChD,KAAK,IAAI+J,GAAG,GAAGH,SAAS,GAAG,CAAC,EAAEG,GAAG,IAAIxD,SAAS,CAACI,GAAG,EAAEoD,GAAG,EAAE,EAAE;gBACzD,IAAI,EAAElE,SAAS,CAACkE,GAAG,CAAC,GAAGlJ,aAAa,CAAC,EAAE;kBAAE;kBACvCiJ,SAAS,GAAG3D,WAAW,CAAC4D,GAAG,CAAC;kBAC5B;gBACF;cACF;YACF;YACAjB,gBAAgB,CAACrC,IAAI,CAAC;cACpBuD,WAAW,EAAEX,UAAU;cACvBY,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAACT,SAAS,EAAED,UAAU,CAAC,GAAG,CAAC,GAAG3F,MAAM,GAAGD,MAAM;cAC/DuG,QAAQ,EAAEF,IAAI,CAACC,GAAG,CAACL,SAAS,EAAED,SAAS,CAAC,GAAG,CAAC,GAAG/F,MAAM,GAAGD;YAC1D,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,KAAK,IAAIwG,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGvB,gBAAgB,CAAC/C,MAAM,EAAEsE,MAAM,EAAE,EAAE;UAC/D,IAAIxI,GAAG,GAAGiH,gBAAgB,CAACuB,MAAM,CAAC;UAClC,IAAIC,YAAY,GAAGzI,GAAG,CAACmI,WAAW;UAClC,IAAIO,OAAO,GAAG1I,GAAG,CAACoI,QAAQ;UAC1B,IAAIO,OAAO,GAAG3I,GAAG,CAACuI,QAAQ;UAC1B;AACR;AACA;AACA;AACA;UACQ,IAAIK,cAAc,GAAKtE,WAAW,CAACmE,YAAY,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,GAAIxG,MAAM,GAAGD,MAAM;;UAE3E;;UAEA;UACA;UACA;UACA,IAAImC,cAAc,CAAC5D,GAAG,CAACyC,QAAQ,CAAC,EAAE;YAChC,KAAK,IAAI6F,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGJ,YAAY,CAACvE,MAAM,EAAE2E,EAAE,EAAE,EAAE;cAC/C,IAAIC,GAAG,GAAGL,YAAY,CAACI,EAAE,CAAC;cAC1B,IAAI7E,SAAS,CAAC8E,GAAG,CAAC,GAAG9F,QAAQ,EAAE;gBAC7B,IAAI+F,QAAQ,GAAGL,OAAO;gBACtB,KAAK,IAAIM,EAAE,GAAGH,EAAE,GAAG,CAAC,EAAEG,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;kBACnC,IAAI,EAAEhF,SAAS,CAACyE,YAAY,CAACO,EAAE,CAAC,CAAC,GAAGhK,aAAa,CAAC,EAAE;oBAAE;oBACpD+J,QAAQ,GAAG/E,SAAS,CAACyE,YAAY,CAACO,EAAE,CAAC,CAAC;oBACtC;kBACF;gBACF;gBACA5E,cAAc,CAAC0E,GAAG,EAAGC,QAAQ,IAAI5K,kBAAkB,GAAGwF,QAAQ,CAAC,GAAIb,OAAO,GAAGiG,QAAQ,CAAC;cACxF;YACF;UACF;;UAEA;UACA;UACA,IAAI5E,cAAc,CAAC5D,GAAG,CAAC2B,OAAO,CAAC,EAAE;YAC/B,KAAK,IAAI+G,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGR,YAAY,CAACvE,MAAM,EAAE+E,IAAI,EAAE,EAAE;cACrD,IAAIC,GAAG,GAAGT,YAAY,CAACQ,IAAI,CAAC;cAC5B,IAAIjF,SAAS,CAACkF,GAAG,CAAC,GAAGhH,OAAO,EAAE;gBAC5B,KAAK,IAAIiH,IAAI,GAAGF,IAAI,GAAG,CAAC,EAAEE,IAAI,IAAI,CAAC,CAAC,EAAEA,IAAI,EAAE,EAAE;kBAC5C,IAAIC,YAAY,GAAGD,IAAI,KAAK,CAAC,CAAC,GAAGT,OAAO,GAAG1E,SAAS,CAACyE,YAAY,CAACU,IAAI,CAAC,CAAC;kBACxE,IAAIC,YAAY,GAAG7K,YAAY,EAAE;oBAC/B,IAAI6K,YAAY,KAAKlG,OAAO,EAAE;sBAC5BkB,cAAc,CAAC8E,GAAG,EAAE1G,OAAO,CAAC;oBAC9B;oBACA;kBACF;gBACF;cACF;YACF;UACF;;UAEA;UACA,IAAI2B,cAAc,CAAC5D,GAAG,CAAC2C,OAAO,CAAC,EAAE;YAC/B,KAAK,IAAImG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGZ,YAAY,CAACvE,MAAM,EAAEmF,IAAI,EAAE,EAAE;cACrD,IAAIC,IAAI,GAAGb,YAAY,CAACY,IAAI,CAAC;cAC7B,IAAIrF,SAAS,CAACsF,IAAI,CAAC,GAAGpG,OAAO,EAAE;gBAC7BkB,cAAc,CAACkF,IAAI,EAAErH,MAAM,CAAC;cAC9B;YACF;UACF;;UAEA;UACA;UACA,IAAIkC,cAAc,CAAC5D,GAAG,CAAC6B,OAAO,CAAC,IAAI+B,cAAc,CAAC5D,GAAG,CAACmC,OAAO,CAAC,EAAE;YAC9D,KAAK,IAAI6G,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGd,YAAY,CAACvE,MAAM,GAAG,CAAC,EAAEqF,IAAI,EAAE,EAAE;cACzD,IAAIC,IAAI,GAAGf,YAAY,CAACc,IAAI,CAAC;cAC7B,IAAIvF,SAAS,CAACwF,IAAI,CAAC,IAAIpH,OAAO,GAAGM,OAAO,CAAC,EAAE;gBACzC,IAAI+G,UAAU,GAAG,CAAC;kBAAEC,QAAQ,GAAG,CAAC;gBAChC,KAAK,IAAIC,IAAI,GAAGJ,IAAI,GAAG,CAAC,EAAEI,IAAI,IAAI,CAAC,EAAEA,IAAI,EAAE,EAAE;kBAC3CF,UAAU,GAAGzF,SAAS,CAACyE,YAAY,CAACkB,IAAI,CAAC,CAAC;kBAC1C,IAAI,EAAEF,UAAU,GAAGzK,aAAa,CAAC,EAAE;oBAAE;oBACnC;kBACF;gBACF;gBACA,KAAK,IAAI4K,IAAI,GAAGL,IAAI,GAAG,CAAC,EAAEK,IAAI,GAAGnB,YAAY,CAACvE,MAAM,EAAE0F,IAAI,EAAE,EAAE;kBAC5DF,QAAQ,GAAG1F,SAAS,CAACyE,YAAY,CAACmB,IAAI,CAAC,CAAC;kBACxC,IAAI,EAAEF,QAAQ,GAAG1K,aAAa,CAAC,EAAE;oBAAE;oBACjC;kBACF;gBACF;gBACA,IAAIyK,UAAU,KAAKC,QAAQ,KAAK1F,SAAS,CAACwF,IAAI,CAAC,KAAKpH,OAAO,GAAGqH,UAAU,KAAKvH,OAAO,GAAIuH,UAAU,IAAIvH,OAAO,GAAGM,OAAO,CAAE,CAAC,EAAE;kBAC1H4B,cAAc,CAACoF,IAAI,EAAEC,UAAU,CAAC;gBAClC;cACF;YACF;UACF;;UAEA;UACA,IAAItF,cAAc,CAAC5D,GAAG,CAAC2B,OAAO,CAAC,EAAE;YAC/B,KAAK,IAAI2H,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGpB,YAAY,CAACvE,MAAM,EAAE2F,IAAI,EAAE,EAAE;cACrD,IAAIC,IAAI,GAAGrB,YAAY,CAACoB,IAAI,CAAC;cAC7B,IAAI7F,SAAS,CAAC8F,IAAI,CAAC,GAAG5H,OAAO,EAAE;gBAC7B,KAAK,IAAI6H,IAAI,GAAGF,IAAI,GAAG,CAAC,EAAEE,IAAI,IAAI,CAAC,IAAK/F,SAAS,CAACyE,YAAY,CAACsB,IAAI,CAAC,CAAC,IAAIzH,OAAO,GAAGtD,aAAa,CAAE,EAAE+K,IAAI,EAAE,EAAE;kBAC1G3F,cAAc,CAACqE,YAAY,CAACsB,IAAI,CAAC,EAAE7H,OAAO,CAAC;gBAC7C;gBACA,KAAK2H,IAAI,EAAE,EAAEA,IAAI,GAAGpB,YAAY,CAACvE,MAAM,IAAKF,SAAS,CAACyE,YAAY,CAACoB,IAAI,CAAC,CAAC,IAAIvH,OAAO,GAAGtD,aAAa,GAAGkD,OAAO,CAAE,EAAE2H,IAAI,EAAE,EAAE;kBACxH,IAAI7F,SAAS,CAACyE,YAAY,CAACoB,IAAI,CAAC,CAAC,KAAK3H,OAAO,EAAE;oBAC7CkC,cAAc,CAACqE,YAAY,CAACoB,IAAI,CAAC,EAAE3H,OAAO,CAAC;kBAC7C;gBACF;cACF;YACF;UACF;;UAEA;UACA,IAAIiC,cAAc,CAAC5D,GAAG,CAAC+B,OAAO,CAAC,IAAI6B,cAAc,CAAC5D,GAAG,CAAC6B,OAAO,CAAC,IAAI+B,cAAc,CAAC5D,GAAG,CAACmC,OAAO,CAAC,EAAE;YAC7F,KAAK,IAAIsH,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGvB,YAAY,CAACvE,MAAM,EAAE8F,IAAI,EAAE,EAAE;cACrD,IAAIC,IAAI,GAAGxB,YAAY,CAACuB,IAAI,CAAC;cAC7B,IAAIhG,SAAS,CAACiG,IAAI,CAAC,IAAI3H,OAAO,GAAGF,OAAO,GAAGM,OAAO,CAAC,EAAE;gBACnD0B,cAAc,CAAC6F,IAAI,EAAEnH,OAAO,CAAC;gBAC7B;gBACA,KAAK,IAAIoH,IAAI,GAAGF,IAAI,GAAG,CAAC,EAAEE,IAAI,IAAI,CAAC,IAAKlG,SAAS,CAACyE,YAAY,CAACyB,IAAI,CAAC,CAAC,GAAGlL,aAAc,EAAEkL,IAAI,EAAE,EAAE;kBAC9F9F,cAAc,CAACqE,YAAY,CAACyB,IAAI,CAAC,EAAEpH,OAAO,CAAC;gBAC7C;gBACA,KAAK,IAAIqH,IAAI,GAAGH,IAAI,GAAG,CAAC,EAAEG,IAAI,GAAG1B,YAAY,CAACvE,MAAM,IAAKF,SAAS,CAACyE,YAAY,CAAC0B,IAAI,CAAC,CAAC,GAAGnL,aAAc,EAAEmL,IAAI,EAAE,EAAE;kBAC/G/F,cAAc,CAACqE,YAAY,CAAC0B,IAAI,CAAC,EAAErH,OAAO,CAAC;gBAC7C;cACF;YACF;UACF;;UAEA;UACA;UACA;UACA,IAAIqB,cAAc,CAAC5D,GAAG,CAAC2B,OAAO,CAAC,EAAE;YAC/B,KAAK,IAAIkI,IAAI,GAAG,CAAC,EAAEC,cAAc,GAAG3B,OAAO,EAAE0B,IAAI,GAAG3B,YAAY,CAACvE,MAAM,EAAEkG,IAAI,EAAE,EAAE;cAC/E,IAAIE,IAAI,GAAG7B,YAAY,CAAC2B,IAAI,CAAC;cAC7B,IAAIpM,IAAI,GAAGgG,SAAS,CAACsG,IAAI,CAAC;cAC1B,IAAItM,IAAI,GAAGkE,OAAO,EAAE;gBAClB,IAAImI,cAAc,KAAKrI,MAAM,EAAE;kBAC7BoC,cAAc,CAACkG,IAAI,EAAEtI,MAAM,CAAC;gBAC9B;cACF,CAAC,MAAM,IAAIhE,IAAI,GAAGO,YAAY,EAAE;gBAC9B8L,cAAc,GAAGrM,IAAI;cACvB;YACF;UACF;;UAEA;;UAEA,IAAImG,cAAc,CAAC5D,GAAG,CAAC7B,qBAAqB,CAAC,EAAE;YAC7C;YACA;YACA;YACA,IAAI6L,mBAAmB,GAAItI,MAAM,GAAGC,OAAO,GAAGM,OAAQ;YACtD,IAAIgI,wBAAwB,GAAGD,mBAAmB,GAAGvI,MAAM;;YAE3D;YACA,IAAIyI,YAAY,GAAG,EAAE;YACrB;cACE,IAAIC,WAAW,GAAG,EAAE;cACpB,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGlC,YAAY,CAACvE,MAAM,EAAEyG,IAAI,EAAE,EAAE;gBACrD;gBACA;gBACA;gBACA,IAAI3G,SAAS,CAACyE,YAAY,CAACkC,IAAI,CAAC,CAAC,GAAGjM,qBAAqB,EAAE;kBACzD,IAAI4B,IAAI,GAAGuD,MAAM,CAAC4E,YAAY,CAACkC,IAAI,CAAC,CAAC;kBACrC,IAAIC,eAAe,GAAI,KAAK,CAAE;kBAC9B;kBACA,IAAI/I,uBAAuB,CAACvB,IAAI,CAAC,KAAK,IAAI,EAAE;oBAC1C,IAAIoK,WAAW,CAACxG,MAAM,GAAG,EAAE,EAAE;sBAC3BwG,WAAW,CAAC9F,IAAI,CAAC;wBAAEtE,IAAI,EAAEA,IAAI;wBAAEuK,QAAQ,EAAEF;sBAAK,CAAC,CAAC;oBAClD,CAAC,MAAM;sBACL;oBACF;kBACF;kBACA;kBAAA,KACK,IAAI,CAACC,eAAe,GAAG9I,uBAAuB,CAACxB,IAAI,CAAC,MAAM,IAAI,EAAE;oBACnE,KAAK,IAAIwK,QAAQ,GAAGJ,WAAW,CAACxG,MAAM,GAAG,CAAC,EAAE4G,QAAQ,IAAI,CAAC,EAAEA,QAAQ,EAAE,EAAE;sBACrE,IAAIC,SAAS,GAAGL,WAAW,CAACI,QAAQ,CAAC,CAACxK,IAAI;sBAC1C,IAAIyK,SAAS,KAAKH,eAAe,IAC/BG,SAAS,KAAKjJ,uBAAuB,CAACC,mBAAmB,CAACzB,IAAI,CAAC,CAAC,IAChEuB,uBAAuB,CAACE,mBAAmB,CAACgJ,SAAS,CAAC,CAAC,KAAKzK,IAAI,EAChE;wBACAmK,YAAY,CAAC7F,IAAI,CAAC,CAAC8F,WAAW,CAACI,QAAQ,CAAC,CAACD,QAAQ,EAAEF,IAAI,CAAC,CAAC;wBACzDD,WAAW,CAACxG,MAAM,GAAG4G,QAAQ,CAAC,CAAC;wBAC/B;sBACF;oBACF;kBACF;gBACF;cACF;cACAL,YAAY,CAACO,IAAI,CAAC,UAAU5J,CAAC,EAAEC,CAAC,EAAE;gBAAE,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;cAAE,CAAC,CAAC;YAC5D;YACA;YACA,KAAK,IAAI4J,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGR,YAAY,CAACvG,MAAM,EAAE+G,OAAO,EAAE,EAAE;cAC9D,IAAIC,KAAK,GAAGT,YAAY,CAACQ,OAAO,CAAC;cACjC,IAAIE,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC;cACzB,IAAIE,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;cAC1B;cACA;cACA;cACA,IAAIG,eAAe,GAAG,KAAK;cAC3B,IAAIC,aAAa,GAAG,CAAC;cACrB,KAAK,IAAIC,IAAI,GAAGJ,UAAU,GAAG,CAAC,EAAEI,IAAI,GAAGH,WAAW,EAAEG,IAAI,EAAE,EAAE;gBAC1D,IAAIC,IAAI,GAAG/C,YAAY,CAAC8C,IAAI,CAAC;gBAC7B,IAAIvH,SAAS,CAACwH,IAAI,CAAC,GAAGhB,wBAAwB,EAAE;kBAC9Ca,eAAe,GAAG,IAAI;kBACtB,IAAII,EAAE,GAAIzH,SAAS,CAACwH,IAAI,CAAC,GAAGjB,mBAAmB,GAAItI,MAAM,GAAGD,MAAM;kBAClE,IAAIyJ,EAAE,KAAK7C,cAAc,EAAE;oBACzB0C,aAAa,GAAGG,EAAE;oBAClB;kBACF;gBACF;cACF;cACA;cACA;cACA;cACA;cACA;cACA;cACA,IAAIJ,eAAe,IAAI,CAACC,aAAa,EAAE;gBACrCA,aAAa,GAAG5C,OAAO;gBACvB,KAAK,IAAIgD,IAAI,GAAGP,UAAU,GAAG,CAAC,EAAEO,IAAI,IAAI,CAAC,EAAEA,IAAI,EAAE,EAAE;kBACjD,IAAIC,IAAI,GAAGlD,YAAY,CAACiD,IAAI,CAAC;kBAC7B,IAAI1H,SAAS,CAAC2H,IAAI,CAAC,GAAGnB,wBAAwB,EAAE;oBAC9C,IAAIoB,IAAI,GAAI5H,SAAS,CAAC2H,IAAI,CAAC,GAAGpB,mBAAmB,GAAItI,MAAM,GAAGD,MAAM;oBACpE,IAAI4J,IAAI,KAAKhD,cAAc,EAAE;sBAC3B0C,aAAa,GAAGM,IAAI;oBACtB,CAAC,MAAM;sBACLN,aAAa,GAAG1C,cAAc;oBAChC;oBACA;kBACF;gBACF;cACF;cACA,IAAI0C,aAAa,EAAE;gBACjBtH,SAAS,CAACyE,YAAY,CAAC0C,UAAU,CAAC,CAAC,GAAGnH,SAAS,CAACyE,YAAY,CAAC2C,WAAW,CAAC,CAAC,GAAGE,aAAa;gBAC1F;gBACA;gBACA;gBACA,IAAIA,aAAa,KAAK1C,cAAc,EAAE;kBACpC,KAAK,IAAIiD,KAAK,GAAGV,UAAU,GAAG,CAAC,EAAEU,KAAK,GAAGpD,YAAY,CAACvE,MAAM,EAAE2H,KAAK,EAAE,EAAE;oBACrE,IAAI,EAAE7H,SAAS,CAACyE,YAAY,CAACoD,KAAK,CAAC,CAAC,GAAG7M,aAAa,CAAC,EAAE;sBACrD,IAAIqB,eAAe,CAACwD,MAAM,CAAC4E,YAAY,CAACoD,KAAK,CAAC,CAAC,CAAC,GAAG7I,QAAQ,EAAE;wBAC3DgB,SAAS,CAACyE,YAAY,CAACoD,KAAK,CAAC,CAAC,GAAGP,aAAa;sBAChD;sBACA;oBACF;kBACF;gBACF;gBACA,IAAIA,aAAa,KAAK1C,cAAc,EAAE;kBACpC,KAAK,IAAIkD,KAAK,GAAGV,WAAW,GAAG,CAAC,EAAEU,KAAK,GAAGrD,YAAY,CAACvE,MAAM,EAAE4H,KAAK,EAAE,EAAE;oBACtE,IAAI,EAAE9H,SAAS,CAACyE,YAAY,CAACqD,KAAK,CAAC,CAAC,GAAG9M,aAAa,CAAC,EAAE;sBACrD,IAAIqB,eAAe,CAACwD,MAAM,CAAC4E,YAAY,CAACqD,KAAK,CAAC,CAAC,CAAC,GAAG9I,QAAQ,EAAE;wBAC3DgB,SAAS,CAACyE,YAAY,CAACqD,KAAK,CAAC,CAAC,GAAGR,aAAa;sBAChD;sBACA;oBACF;kBACF;gBACF;cACF;YACF;;YAEA;YACA;YACA;YACA,KAAK,IAAIS,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGtD,YAAY,CAACvE,MAAM,EAAE6H,KAAK,EAAE,EAAE;cACxD,IAAI/H,SAAS,CAACyE,YAAY,CAACsD,KAAK,CAAC,CAAC,GAAGrN,qBAAqB,EAAE;gBAC1D,IAAIsN,UAAU,GAAGD,KAAK;kBAAEE,QAAQ,GAAGF,KAAK;gBACxC,IAAIG,UAAU,GAAGxD,OAAO,CAAC,CAAC;gBAC1B,KAAK,IAAIyD,GAAG,GAAGJ,KAAK,GAAG,CAAC,EAAEI,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;kBACzC,IAAInI,SAAS,CAACyE,YAAY,CAAC0D,GAAG,CAAC,CAAC,GAAGnN,aAAa,EAAE;oBAChDgN,UAAU,GAAGG,GAAG,CAAC,CAAC;kBACpB,CAAC,MAAM;oBACLD,UAAU,GAAIlI,SAAS,CAACyE,YAAY,CAAC0D,GAAG,CAAC,CAAC,GAAG5B,mBAAmB,GAAItI,MAAM,GAAGD,MAAM;oBACnF;kBACF;gBACF;gBACA,IAAIoK,UAAU,GAAGzD,OAAO;gBACxB,KAAK,IAAI0D,KAAK,GAAGN,KAAK,GAAG,CAAC,EAAEM,KAAK,GAAG5D,YAAY,CAACvE,MAAM,EAAEmI,KAAK,EAAE,EAAE;kBAChE,IAAIrI,SAAS,CAACyE,YAAY,CAAC4D,KAAK,CAAC,CAAC,IAAI3N,qBAAqB,GAAGM,aAAa,CAAC,EAAE;oBAC5EiN,QAAQ,GAAGI,KAAK;kBAClB,CAAC,MAAM;oBACLD,UAAU,GAAIpI,SAAS,CAACyE,YAAY,CAAC4D,KAAK,CAAC,CAAC,GAAG9B,mBAAmB,GAAItI,MAAM,GAAGD,MAAM;oBACrF;kBACF;gBACF;gBACA,KAAK,IAAIsK,IAAI,GAAGN,UAAU,EAAEM,IAAI,IAAIL,QAAQ,EAAEK,IAAI,EAAE,EAAE;kBACpDtI,SAAS,CAACyE,YAAY,CAAC6D,IAAI,CAAC,CAAC,GAAGJ,UAAU,KAAKE,UAAU,GAAGF,UAAU,GAAGtD,cAAc;gBACzF;gBACAmD,KAAK,GAAGE,QAAQ;cAClB;YACF;UACF;QACF;;QAEA;;QAEA,KAAK,IAAIM,IAAI,GAAG7H,SAAS,CAACG,KAAK,EAAE0H,IAAI,IAAI7H,SAAS,CAACI,GAAG,EAAEyH,IAAI,EAAE,EAAE;UAC9D,IAAIC,OAAO,GAAGlI,WAAW,CAACiI,IAAI,CAAC;UAC/B,IAAIE,MAAM,GAAGzI,SAAS,CAACuI,IAAI,CAAC;UAC5B;UACA,IAAIC,OAAO,GAAG,CAAC,EAAE;YACf,IAAIC,MAAM,IAAIzK,MAAM,GAAGE,OAAO,GAAGM,OAAO,CAAC,EAAE;cACzC8B,WAAW,CAACiI,IAAI,CAAC,EAAE;YACrB;UACF;UACE;UACF;UAAA,KACK;YACH,IAAIE,MAAM,GAAGxK,MAAM,EAAE;cACnBqC,WAAW,CAACiI,IAAI,CAAC,EAAE;YACrB,CAAC,MAAM,IAAIE,MAAM,IAAIjK,OAAO,GAAGN,OAAO,CAAC,EAAE;cACvCoC,WAAW,CAACiI,IAAI,CAAC,IAAI,CAAC;YACxB;UACF;;UAEA;UACA;UACA,IAAIE,MAAM,GAAGzN,aAAa,EAAE;YAC1BsF,WAAW,CAACiI,IAAI,CAAC,GAAGA,IAAI,KAAK,CAAC,GAAG7H,SAAS,CAACK,KAAK,GAAGT,WAAW,CAACiI,IAAI,GAAG,CAAC,CAAC;UAC1E;;UAEA;UACA;UACA;UACA,IAAIA,IAAI,KAAK7H,SAAS,CAACI,GAAG,IAAIzE,eAAe,CAACwD,MAAM,CAAC0I,IAAI,CAAC,CAAC,IAAI1J,MAAM,GAAGD,MAAM,CAAC,EAAE;YAC/E,KAAK,IAAI8J,GAAG,GAAGH,IAAI,EAAEG,GAAG,IAAI,CAAC,IAAKrM,eAAe,CAACwD,MAAM,CAAC6I,GAAG,CAAC,CAAC,GAAGnN,cAAe,EAAEmN,GAAG,EAAE,EAAE;cACvFpI,WAAW,CAACoI,GAAG,CAAC,GAAGhI,SAAS,CAACK,KAAK;YACpC;UACF;QACF;MACF;;MAEA;MACA;MACA,OAAO;QACL4H,MAAM,EAAErI,WAAW;QACnBG,UAAU,EAAEA;MACd,CAAC;MAED,SAASO,uBAAuBA,CAAEH,KAAK,EAAE+H,KAAK,EAAE;QAC9C;QACA,KAAK,IAAI3O,CAAC,GAAG4G,KAAK,EAAE5G,CAAC,GAAG4F,MAAM,CAACK,MAAM,EAAEjG,CAAC,EAAE,EAAE;UAC1C,IAAI+H,QAAQ,GAAGhC,SAAS,CAAC/F,CAAC,CAAC;UAC3B,IAAI+H,QAAQ,IAAI/D,MAAM,GAAGiB,OAAO,CAAC,EAAE;YACjC,OAAO,CAAC;UACV;UACA,IAAK8C,QAAQ,IAAIpD,MAAM,GAAGZ,MAAM,CAAC,IAAM4K,KAAK,IAAI5G,QAAQ,KAAKrC,QAAS,EAAE;YACtE,OAAO,CAAC;UACV;UACA,IAAIqC,QAAQ,GAAG7H,kBAAkB,EAAE;YACjC,IAAI0O,GAAG,GAAGC,kBAAkB,CAAC7O,CAAC,CAAC;YAC/BA,CAAC,GAAG4O,GAAG,KAAK,CAAC,CAAC,GAAGhJ,MAAM,CAACK,MAAM,GAAG2I,GAAG;UACtC;QACF;QACA,OAAO,CAAC;MACV;MAEA,SAASC,kBAAkBA,CAAEC,YAAY,EAAE;QACzC;QACA,IAAIC,cAAc,GAAG,CAAC;QACtB,KAAK,IAAI/O,CAAC,GAAG8O,YAAY,GAAG,CAAC,EAAE9O,CAAC,GAAG4F,MAAM,CAACK,MAAM,EAAEjG,CAAC,EAAE,EAAE;UACrD,IAAI+H,QAAQ,GAAGhC,SAAS,CAAC/F,CAAC,CAAC;UAC3B,IAAI+H,QAAQ,GAAGpD,MAAM,EAAE;YACrB;UACF;UACA,IAAIoD,QAAQ,GAAGrC,QAAQ,EAAE;YACvB,IAAI,EAAEqJ,cAAc,KAAK,CAAC,EAAE;cAC1B,OAAO/O,CAAC;YACV;UACF,CAAC,MAAM,IAAI+H,QAAQ,GAAG7H,kBAAkB,EAAE;YACxC6O,cAAc,EAAE;UAClB;QACF;QACA,OAAO,CAAC,CAAC;MACX;IACF;;IAEA;IACA,IAAIC,IAAI,GAAG,imBAAimB;IAE5mB,IAAIC,SAAS;IAEb,SAASC,KAAKA,CAAA,EAAI;MAChB,IAAI,CAACD,SAAS,EAAE;QACd;QACA,IAAIlN,GAAG,GAAGW,iBAAiB,CAACsM,IAAI,EAAE,IAAI,CAAC;QACvC,IAAIzN,GAAG,GAAGQ,GAAG,CAACR,GAAG;QACjB,IAAIuB,UAAU,GAAGf,GAAG,CAACe,UAAU;QAC/B;QACAA,UAAU,CAAChD,OAAO,CAAC,UAAUqP,KAAK,EAAEC,GAAG,EAAE;UACvC7N,GAAG,CAACY,GAAG,CAACiN,GAAG,EAAED,KAAK,CAAC;QACrB,CAAC,CAAC;QACFF,SAAS,GAAG1N,GAAG;QACf;MACF;IACF;IAEA,SAAS8N,oBAAoBA,CAAEhN,IAAI,EAAE;MACnC6M,KAAK,CAAC,CAAC;MACP,OAAOD,SAAS,CAAC3M,GAAG,CAACD,IAAI,CAAC,IAAI,IAAI;IACpC;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASiN,wBAAwBA,CAAC1J,MAAM,EAAE2J,eAAe,EAAE3I,KAAK,EAAEC,GAAG,EAAE;MACrE,IAAI2I,MAAM,GAAG5J,MAAM,CAACK,MAAM;MAC1BW,KAAK,GAAGwD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,KAAK,CAAC;MAC/CC,GAAG,GAAGuD,IAAI,CAACqF,GAAG,CAACD,MAAM,GAAG,CAAC,EAAE3I,GAAG,IAAI,IAAI,GAAG2I,MAAM,GAAG,CAAC,GAAG,CAAC3I,GAAG,CAAC;MAE3D,IAAItF,GAAG,GAAG,IAAIE,GAAG,CAAC,CAAC;MACnB,KAAK,IAAIzB,CAAC,GAAG4G,KAAK,EAAE5G,CAAC,IAAI6G,GAAG,EAAE7G,CAAC,EAAE,EAAE;QACjC,IAAIuP,eAAe,CAACvP,CAAC,CAAC,GAAG,CAAC,EAAE;UAAE;UAC5B,IAAI0P,MAAM,GAAGL,oBAAoB,CAACzJ,MAAM,CAAC5F,CAAC,CAAC,CAAC;UAC5C,IAAI0P,MAAM,KAAK,IAAI,EAAE;YACnBnO,GAAG,CAACY,GAAG,CAACnC,CAAC,EAAE0P,MAAM,CAAC;UACpB;QACF;MACF;MACA,OAAOnO,GAAG;IACZ;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAASoO,kBAAkBA,CAAC/J,MAAM,EAAEgK,qBAAqB,EAAEhJ,KAAK,EAAEC,GAAG,EAAE;MACrE,IAAI2I,MAAM,GAAG5J,MAAM,CAACK,MAAM;MAC1BW,KAAK,GAAGwD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,KAAK,CAAC;MAC/CC,GAAG,GAAGuD,IAAI,CAACqF,GAAG,CAACD,MAAM,GAAG,CAAC,EAAE3I,GAAG,IAAI,IAAI,GAAG2I,MAAM,GAAG,CAAC,GAAG,CAAC3I,GAAG,CAAC;MAE3D,IAAIgJ,QAAQ,GAAG,EAAE;MACjBD,qBAAqB,CAACpJ,UAAU,CAAC1G,OAAO,CAAC,UAAU2G,SAAS,EAAE;QAC5D,IAAIqJ,SAAS,GAAG1F,IAAI,CAACC,GAAG,CAACzD,KAAK,EAAEH,SAAS,CAACG,KAAK,CAAC;QAChD,IAAImJ,OAAO,GAAG3F,IAAI,CAACqF,GAAG,CAAC5I,GAAG,EAAEJ,SAAS,CAACI,GAAG,CAAC;QAC1C,IAAIiJ,SAAS,GAAGC,OAAO,EAAE;UACvB;UACA,IAAIC,UAAU,GAAGJ,qBAAqB,CAAClB,MAAM,CAACuB,KAAK,CAACH,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC;;UAE3E;UACA;UACA,KAAK,IAAI/P,CAAC,GAAG+P,OAAO,EAAE/P,CAAC,IAAI8P,SAAS,IAAK1N,eAAe,CAACwD,MAAM,CAAC5F,CAAC,CAAC,CAAC,GAAGsB,cAAe,EAAEtB,CAAC,EAAE,EAAE;YAC1FgQ,UAAU,CAAChQ,CAAC,CAAC,GAAGyG,SAAS,CAACK,KAAK;UACjC;;UAEA;UACA;UACA,IAAIoJ,QAAQ,GAAGzJ,SAAS,CAACK,KAAK;UAC9B,IAAIqJ,WAAW,GAAGC,QAAQ;UAC1B,KAAK,IAAI1J,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGsJ,UAAU,CAAC/J,MAAM,EAAES,GAAG,EAAE,EAAE;YAChD,IAAII,KAAK,GAAGkJ,UAAU,CAACtJ,GAAG,CAAC;YAC3B,IAAII,KAAK,GAAGoJ,QAAQ,EAAE;cAAEA,QAAQ,GAAGpJ,KAAK;YAAE;YAC1C,IAAIA,KAAK,GAAGqJ,WAAW,EAAE;cAAEA,WAAW,GAAGrJ,KAAK,GAAG,CAAC;YAAE;UACtD;UACA,KAAK,IAAI2B,GAAG,GAAGyH,QAAQ,EAAEzH,GAAG,IAAI0H,WAAW,EAAE1H,GAAG,EAAE,EAAE;YAClD,KAAK,IAAIX,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGkI,UAAU,CAAC/J,MAAM,EAAE6B,GAAG,EAAE,EAAE;cAChD,IAAIkI,UAAU,CAAClI,GAAG,CAAC,IAAIW,GAAG,EAAE;gBAC1B,IAAI4H,QAAQ,GAAGvI,GAAG;gBAClB,OAAOA,GAAG,GAAG,CAAC,GAAGkI,UAAU,CAAC/J,MAAM,IAAI+J,UAAU,CAAClI,GAAG,GAAG,CAAC,CAAC,IAAIW,GAAG,EAAE;kBAChEX,GAAG,EAAE;gBACP;gBACA,IAAIA,GAAG,GAAGuI,QAAQ,EAAE;kBAClBR,QAAQ,CAAClJ,IAAI,CAAC,CAAC0J,QAAQ,GAAGP,SAAS,EAAEhI,GAAG,GAAGgI,SAAS,CAAC,CAAC;gBACxD;cACF;YACF;UACF;QACF;MACF,CAAC,CAAC;MACF,OAAOD,QAAQ;IACjB;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;IACE,SAASS,kBAAkBA,CAAC1K,MAAM,EAAE2K,iBAAiB,EAAE3J,KAAK,EAAEC,GAAG,EAAE;MACjE,IAAI2J,OAAO,GAAGC,mBAAmB,CAAC7K,MAAM,EAAE2K,iBAAiB,EAAE3J,KAAK,EAAEC,GAAG,CAAC;MACxE,IAAI6J,KAAK,GAAG,EAAE,CAACC,MAAM,CAAE/K,MAAO,CAAC;MAC/B4K,OAAO,CAAC1Q,OAAO,CAAC,UAAU8Q,SAAS,EAAE5Q,CAAC,EAAE;QACtC0Q,KAAK,CAAC1Q,CAAC,CAAC,GAAG,CACRuQ,iBAAiB,CAAC7B,MAAM,CAACkC,SAAS,CAAC,GAAG,CAAC,GAAIvB,oBAAoB,CAACzJ,MAAM,CAACgL,SAAS,CAAC,CAAC,GAAG,IAAI,KACvFhL,MAAM,CAACgL,SAAS,CAAC;MACxB,CAAC,CAAC;MACF,OAAOF,KAAK,CAACG,IAAI,CAAC,EAAE,CAAC;IACvB;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;IACE,SAASJ,mBAAmBA,CAAC7K,MAAM,EAAE2K,iBAAiB,EAAE3J,KAAK,EAAEC,GAAG,EAAE;MAClE,IAAIgJ,QAAQ,GAAGF,kBAAkB,CAAC/J,MAAM,EAAE2K,iBAAiB,EAAE3J,KAAK,EAAEC,GAAG,CAAC;MACxE;MACA,IAAI2J,OAAO,GAAG,EAAE;MAChB,KAAK,IAAIxQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,MAAM,CAACK,MAAM,EAAEjG,CAAC,EAAE,EAAE;QACtCwQ,OAAO,CAACxQ,CAAC,CAAC,GAAGA,CAAC;MAChB;MACA;MACA6P,QAAQ,CAAC/P,OAAO,CAAC,UAAUiC,GAAG,EAAE;QAC9B,IAAI6E,KAAK,GAAG7E,GAAG,CAAC,CAAC,CAAC;QAClB,IAAI8E,GAAG,GAAG9E,GAAG,CAAC,CAAC,CAAC;QAEhB,IAAIkO,KAAK,GAAGO,OAAO,CAACP,KAAK,CAACrJ,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC;QACzC,KAAK,IAAI7G,CAAC,GAAGiQ,KAAK,CAAChK,MAAM,EAAEjG,CAAC,EAAE,GAAG;UAC/BwQ,OAAO,CAAC3J,GAAG,GAAG7G,CAAC,CAAC,GAAGiQ,KAAK,CAACjQ,CAAC,CAAC;QAC7B;MACF,CAAC,CAAC;MACF,OAAOwQ,OAAO;IAChB;IAEAjR,OAAO,CAACsE,uBAAuB,GAAGA,uBAAuB;IACzDtE,OAAO,CAAC6C,eAAe,GAAGA,eAAe;IACzC7C,OAAO,CAACiD,mBAAmB,GAAGA,mBAAmB;IACjDjD,OAAO,CAACuE,mBAAmB,GAAGA,mBAAmB;IACjDvE,OAAO,CAACoG,kBAAkB,GAAGA,kBAAkB;IAC/CpG,OAAO,CAAC8P,oBAAoB,GAAGA,oBAAoB;IACnD9P,OAAO,CAAC+P,wBAAwB,GAAGA,wBAAwB;IAC3D/P,OAAO,CAACoQ,kBAAkB,GAAGA,kBAAkB;IAC/CpQ,OAAO,CAACkR,mBAAmB,GAAGA,mBAAmB;IACjDlR,OAAO,CAAC+Q,kBAAkB,GAAGA,kBAAkB;IAC/C/Q,OAAO,CAACqE,uBAAuB,GAAGA,uBAAuB;IAEzDhE,MAAM,CAACkR,cAAc,CAACvR,OAAO,EAAE,YAAY,EAAE;MAAE4P,KAAK,EAAE;IAAK,CAAC,CAAC;IAE7D,OAAO5P,OAAO;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAE;EACN,OAAOD,IAAI;AAAA;AAEX,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}