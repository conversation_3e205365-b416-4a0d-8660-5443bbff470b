{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\3d\\\\3DConfigurator.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport '../../styles/configurator.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Advanced3DConfigurator = ({\n  onBack,\n  product\n}) => {\n  _s();\n  // 3D Scene refs\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const productRef = useRef(null);\n  const animationIdRef = useRef(null);\n  const cameraRef = useRef(null);\n\n  // Determine product type\n  const getProductType = () => {\n    if (!product) return 'table';\n    const name = product.name.toLowerCase();\n    if (name.includes('chair')) return 'chair';\n    if (name.includes('cabinet') || name.includes('storage')) return 'cabinet';\n    if (name.includes('shelf')) return 'shelf';\n    if (name.includes('workstation')) return 'workstation';\n    return 'table'; // default\n  };\n  const productType = getProductType();\n\n  // State for configuration\n  const [dimensions, setDimensions] = useState({\n    width: productType === 'chair' ? 60 : 280,\n    depth: productType === 'chair' ? 60 : 140,\n    height: productType === 'chair' ? 80 : 75\n  });\n  const [colors, setColors] = useState({\n    primary: '#6B7280',\n    secondary: '#374151',\n    accent: '#FFFFFF'\n  });\n  const [material, setMaterial] = useState(productType === 'chair' ? 'mesh' : 'wood');\n  const [quantity, setQuantity] = useState(1);\n\n  // Model loading state\n  const [isModelLoaded, setIsModelLoaded] = useState(false);\n  const [modelError, setModelError] = useState('');\n\n  // Store original model dimensions for scaling calculations\n  const originalModelDimensionsRef = useRef(null);\n  const baseScaleRef = useRef(1);\n\n  // Scaling notification state\n  const [showScalingNotification, setShowScalingNotification] = useState(false);\n  const scalingTimeoutRef = useRef(null);\n\n  // Mobile detection\n  const [isMobile, setIsMobile] = useState(false);\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      setIsMobile(mobile);\n    };\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Enhanced pricing logic\n  const getBasePrice = () => {\n    const basePrices = {\n      table: 500,\n      chair: 200,\n      cabinet: 800,\n      shelf: 300,\n      workstation: 1200\n    };\n    return basePrices[productType] || 500;\n  };\n  const calculatePrice = () => {\n    let price = getBasePrice();\n\n    // Size multiplier\n    const sizeMultiplier = dimensions.width * dimensions.depth * dimensions.height / 100000;\n    price *= Math.max(0.5, Math.min(2.0, sizeMultiplier));\n\n    // Material multiplier\n    const materialMultipliers = {\n      wood: 1.0,\n      metal: 1.2,\n      glass: 1.5,\n      plastic: 0.8,\n      leather: 1.8,\n      mesh: 1.1,\n      fabric: 0.9,\n      vinyl: 1.0\n    };\n    price *= materialMultipliers[material] || 1.0;\n    return price * quantity;\n  };\n  const getCurrentPrice = () => calculatePrice();\n\n  // GLB Model Loading\n  const loadGLBModel = async (scene, modelPath) => {\n    const loader = new GLTFLoader();\n    try {\n      console.log('Starting GLB model load from:', modelPath);\n      setModelError('');\n      const gltf = await new Promise((resolve, reject) => {\n        loader.load(modelPath, gltf => {\n          console.log('GLB file loaded successfully:', gltf);\n          resolve(gltf);\n        }, progress => {\n          console.log('Loading progress:', progress.loaded / progress.total * 100 + '%');\n        }, error => {\n          console.error('GLB loading error:', error);\n          reject(error);\n        });\n      });\n      const model = gltf.scene;\n\n      // Get model bounds and center it\n      const box = new THREE.Box3().setFromObject(model);\n      const center = box.getCenter(new THREE.Vector3());\n      const size = box.getSize(new THREE.Vector3());\n\n      // Store original model dimensions for real-time scaling\n      originalModelDimensionsRef.current = {\n        width: size.x,\n        height: size.y,\n        depth: size.z\n      };\n\n      // Center the model\n      model.position.sub(center);\n\n      // Scale model to reasonable size and store base scale\n      const maxDimension = Math.max(size.x, size.y, size.z);\n      if (maxDimension > 3) {\n        const scale = 3 / maxDimension;\n        model.scale.setScalar(scale);\n        baseScaleRef.current = scale;\n      } else {\n        baseScaleRef.current = 1;\n      }\n\n      // Apply initial dimension-based scaling for chairs\n      if (productType === 'chair') {\n        updateModelDimensions(model);\n      }\n\n      // Enable shadows for all meshes\n      model.traverse(child => {\n        if (child.isMesh) {\n          child.castShadow = true;\n          child.receiveShadow = true;\n\n          // Ensure materials are properly configured\n          if (child.material) {\n            child.material.needsUpdate = true;\n          }\n        }\n      });\n      productRef.current = model;\n      scene.add(model);\n      setIsModelLoaded(true);\n      return model;\n    } catch (error) {\n      console.error('Error loading GLB model:', error);\n      setModelError('Failed to load 3D model. Using default geometry.');\n      setIsModelLoaded(false);\n\n      // Fallback to geometric shapes\n      createProduct(scene);\n      return null;\n    }\n  };\n\n  // Camera control state - moved outside useEffect for global access\n  const cameraControlsRef = useRef({\n    targetRotationX: 0,\n    targetRotationY: 0,\n    currentRotationX: 0,\n    currentRotationY: 0,\n    targetDistance: 6,\n    currentDistance: 6,\n    isAnimating: false\n  });\n\n  // View control functions\n  const resetView = viewType => {\n    if (!cameraRef.current) return;\n    const controls = cameraControlsRef.current;\n    controls.isAnimating = true;\n    switch (viewType) {\n      case 'front':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 6;\n        break;\n      case 'side':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = Math.PI / 2;\n        controls.targetDistance = 6;\n        break;\n      case 'top':\n        controls.targetRotationX = Math.PI / 2;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 8;\n        break;\n      case 'iso':\n      default:\n        controls.targetRotationX = 0.3;\n        controls.targetRotationY = 0.8;\n        controls.targetDistance = 6;\n        break;\n    }\n  };\n  const adjustZoom = direction => {\n    if (!cameraRef.current || !cameraControlsRef.current) return;\n    const controls = cameraControlsRef.current;\n    const zoomAmount = direction * 1.5;\n    const newDistance = controls.targetDistance + zoomAmount;\n\n    // Smooth zoom with bounds checking\n    controls.targetDistance = Math.max(2.5, Math.min(15, newDistance));\n\n    // Add slight animation flag for smoother zoom\n    controls.isAnimating = true;\n    setTimeout(() => {\n      if (controls) controls.isAnimating = false;\n    }, 500);\n  };\n\n  // Create 3D product functions\n  const createTableGeometry = group => {\n    // Table top\n    const topGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.08, dimensions.depth / 100);\n    const topMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: material === 'glass' ? 0.1 : 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0,\n      transparent: material === 'glass',\n      opacity: material === 'glass' ? 0.8 : 1.0\n    });\n    const tableTop = new THREE.Mesh(topGeometry, topMaterial);\n    tableTop.position.y = dimensions.height / 100 - 0.04;\n    if (!isMobile) {\n      tableTop.castShadow = true;\n      tableTop.receiveShadow = true;\n    }\n    group.add(tableTop);\n\n    // Legs with mobile optimization\n    const legSegments = isMobile ? 8 : 12;\n    const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, dimensions.height / 100 - 0.08, legSegments);\n    const legMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0\n    });\n    const legPositions = [[-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1], [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1], [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1], [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1]];\n    legPositions.forEach(pos => {\n      const leg = new THREE.Mesh(legGeometry, legMaterial);\n      leg.position.set(pos[0], pos[1], pos[2]);\n      if (!isMobile) {\n        leg.castShadow = true;\n      }\n      group.add(leg);\n    });\n  };\n  const createChairGeometry = group => {\n    // Seat\n    const seatGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.06, dimensions.depth / 100);\n    const seatMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const seat = new THREE.Mesh(seatGeometry, seatMaterial);\n    seat.position.y = 0.4;\n    if (!isMobile) {\n      seat.castShadow = true;\n    }\n    group.add(seat);\n\n    // Backrest\n    const backGeometry = new THREE.BoxGeometry(dimensions.width / 100, 0.02, dimensions.height / 150);\n    const backMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const backrest = new THREE.Mesh(backGeometry, backMaterial);\n    backrest.position.set(0, 0.6, -dimensions.depth / 200 + 0.01);\n    if (!isMobile) {\n      backrest.castShadow = true;\n    }\n    group.add(backrest);\n\n    // Base with mobile optimization\n    const baseSegments = isMobile ? 5 : 8;\n    const baseGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.04, baseSegments);\n    const baseMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const base = new THREE.Mesh(baseGeometry, baseMaterial);\n    base.position.y = 0.02;\n    if (!isMobile) {\n      base.castShadow = true;\n    }\n    group.add(base);\n  };\n  const createProduct = async scene => {\n    // Check if product has a 3D model file\n    if (product && product.model3D) {\n      console.log('Loading GLB model:', product.model3D);\n      console.log('Product name:', product.name);\n      console.log('Product type detected:', productType);\n      const model = await loadGLBModel(scene, product.model3D);\n      if (model) {\n        console.log('GLB model loaded successfully!');\n        return; // Successfully loaded GLB model\n      }\n    }\n\n    // Fallback to geometric shapes if no GLB model or loading failed\n    const productGroup = new THREE.Group();\n    switch (productType) {\n      case 'table':\n        createTableGeometry(productGroup);\n        break;\n      case 'chair':\n        createChairGeometry(productGroup);\n        break;\n      default:\n        createTableGeometry(productGroup);\n    }\n    productRef.current = productGroup;\n    scene.add(productGroup);\n  };\n\n  // Update model dimensions in real-time for GLB models\n  const updateModelDimensions = (model = productRef.current) => {\n    if (!model || !originalModelDimensionsRef.current || productType !== 'chair') return;\n    const baseScale = baseScaleRef.current;\n\n    // Show scaling notification\n    setShowScalingNotification(true);\n\n    // Clear existing timeout\n    if (scalingTimeoutRef.current) {\n      clearTimeout(scalingTimeoutRef.current);\n    }\n\n    // Hide notification after 2 seconds\n    scalingTimeoutRef.current = setTimeout(() => {\n      setShowScalingNotification(false);\n    }, 2000);\n\n    // Calculate scale factors based on dimension changes\n    // Default chair dimensions (in cm) for reference\n    const defaultDimensions = {\n      width: 60,\n      height: 80,\n      depth: 60\n    };\n\n    // Calculate scale factors for each dimension\n    const scaleX = dimensions.width / defaultDimensions.width * baseScale;\n    const scaleY = dimensions.height / defaultDimensions.height * baseScale;\n    const scaleZ = dimensions.depth / defaultDimensions.depth * baseScale;\n\n    // Apply non-uniform scaling to maintain realistic proportions\n    // For chairs: width affects seat width, height affects overall height, depth affects seat depth\n    model.scale.set(scaleX, scaleY, scaleZ);\n\n    // Ensure the model stays centered after scaling\n    const box = new THREE.Box3().setFromObject(model);\n    const center = box.getCenter(new THREE.Vector3());\n    model.position.sub(center);\n  };\n\n  // Update product materials and geometry\n  const updateProduct = () => {\n    if (!productRef.current || !sceneRef.current) return;\n\n    // For GLB models with real-time dimension updates\n    if (isModelLoaded && productType === 'chair') {\n      updateModelDimensions();\n      return;\n    }\n\n    // For procedural geometry, recreate the model\n    sceneRef.current.remove(productRef.current);\n    createProduct(sceneRef.current);\n  };\n\n  // 3D Scene Setup\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf5f5f5); // Gray background\n    sceneRef.current = scene;\n\n    // Camera setup\n    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);\n    camera.position.set(4, 3, 4);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // Renderer setup with mobile optimizations\n    const renderer = new THREE.WebGLRenderer({\n      antialias: !isMobile,\n      alpha: true,\n      powerPreference: isMobile ? \"low-power\" : \"high-performance\"\n    });\n    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, isMobile ? 1.5 : 2));\n    renderer.setClearColor(0xf5f5f5, 1.0);\n    renderer.shadowMap.enabled = !isMobile;\n    if (!isMobile) {\n      renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n    }\n    mountRef.current.appendChild(renderer.domElement);\n    rendererRef.current = renderer;\n\n    // Lighting System with mobile optimizations\n    const ambientLight = new THREE.AmbientLight(0x404040, isMobile ? 0.8 : 0.6);\n    scene.add(ambientLight);\n    const directionalLight = new THREE.DirectionalLight(0xffffff, isMobile ? 0.8 : 1.0);\n    directionalLight.position.set(5, 5, 5);\n    if (!isMobile) {\n      directionalLight.castShadow = true;\n      directionalLight.shadow.mapSize.width = 1024;\n      directionalLight.shadow.mapSize.height = 1024;\n    }\n    scene.add(directionalLight);\n\n    // Mouse interaction variables\n    let isMouseDown = false;\n    let mouseX = 0;\n    let mouseY = 0;\n\n    // Get camera controls from ref\n    const controls = cameraControlsRef.current;\n\n    // Mouse event handlers\n    const onMouseDown = event => {\n      isMouseDown = true;\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n    const onMouseMove = event => {\n      if (!isMouseDown) return;\n      const deltaX = event.clientX - mouseX;\n      const deltaY = event.clientY - mouseY;\n\n      // Increased sensitivity for better responsiveness\n      controls.targetRotationY += deltaX * 0.015;\n      controls.targetRotationX += deltaY * 0.015;\n      controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n    const onMouseUp = () => {\n      isMouseDown = false;\n    };\n    const onWheel = event => {\n      event.preventDefault();\n      const zoomSpeed = isMobile ? 0.008 : 0.01;\n      controls.targetDistance += event.deltaY * zoomSpeed;\n      controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n    };\n\n    // Enhanced touch event handlers for mobile\n    let lastTouchDistance = 0;\n    let initialTouchDistance = 0;\n    const getTouchDistance = touches => {\n      if (touches.length < 2) return 0;\n      const dx = touches[0].clientX - touches[1].clientX;\n      const dy = touches[0].clientY - touches[1].clientY;\n      return Math.sqrt(dx * dx + dy * dy);\n    };\n    const onTouchStart = event => {\n      if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        isMouseDown = false;\n        initialTouchDistance = getTouchDistance(event.touches);\n        lastTouchDistance = initialTouchDistance;\n      }\n    };\n    const onTouchMove = event => {\n      event.preventDefault();\n      if (event.touches.length === 1 && isMouseDown) {\n        // Single finger rotation with increased sensitivity\n        const deltaX = event.touches[0].clientX - mouseX;\n        const deltaY = event.touches[0].clientY - mouseY;\n        controls.targetRotationY += deltaX * 0.012;\n        controls.targetRotationX += deltaY * 0.012;\n        controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        // Two finger pinch zoom\n        const currentDistance = getTouchDistance(event.touches);\n        const deltaDistance = currentDistance - lastTouchDistance;\n        if (Math.abs(deltaDistance) > 3) {\n          // Increased threshold for smoother zoom\n          controls.targetDistance -= deltaDistance * 0.015;\n          controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n          lastTouchDistance = currentDistance;\n        }\n      }\n    };\n    const onTouchEnd = event => {\n      if (event.touches.length === 0) {\n        isMouseDown = false;\n      } else if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      }\n    };\n\n    // Add event listeners to both canvas and document for better coverage\n    const canvas = renderer.domElement;\n\n    // Mouse events\n    canvas.addEventListener('mousedown', onMouseDown);\n    document.addEventListener('mousemove', onMouseMove); // Global mouse move for better tracking\n    document.addEventListener('mouseup', onMouseUp); // Global mouse up\n    canvas.addEventListener('wheel', onWheel, {\n      passive: false\n    });\n\n    // Touch events with better gesture handling\n    canvas.addEventListener('touchstart', onTouchStart, {\n      passive: false\n    });\n    canvas.addEventListener('touchmove', onTouchMove, {\n      passive: false\n    });\n    canvas.addEventListener('touchend', onTouchEnd, {\n      passive: false\n    });\n\n    // Prevent context menu on long press\n    canvas.addEventListener('contextmenu', e => e.preventDefault());\n\n    // Prevent default touch behaviors that might interfere\n    canvas.style.touchAction = 'none';\n    canvas.style.userSelect = 'none';\n\n    // Enhanced camera update function with smooth interpolation\n    const updateCamera = () => {\n      const lerpFactor = controls.isAnimating ? 0.15 : 0.08; // Faster when animating to presets\n\n      // Smooth interpolation\n      controls.currentRotationX += (controls.targetRotationX - controls.currentRotationX) * lerpFactor;\n      controls.currentRotationY += (controls.targetRotationY - controls.currentRotationY) * lerpFactor;\n      controls.currentDistance += (controls.targetDistance - controls.currentDistance) * lerpFactor;\n\n      // Calculate spherical coordinates for camera position\n      const x = Math.sin(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      const y = Math.sin(controls.currentRotationX) * controls.currentDistance + 2;\n      const z = Math.cos(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      camera.position.set(x, y, z);\n      camera.lookAt(0, 0, 0);\n\n      // Stop animation flag when close enough to target\n      if (controls.isAnimating) {\n        const rotXDiff = Math.abs(controls.targetRotationX - controls.currentRotationX);\n        const rotYDiff = Math.abs(controls.targetRotationY - controls.currentRotationY);\n        const distDiff = Math.abs(controls.targetDistance - controls.currentDistance);\n        if (rotXDiff < 0.01 && rotYDiff < 0.01 && distDiff < 0.1) {\n          controls.isAnimating = false;\n        }\n      }\n    };\n\n    // Initialize camera controls with default isometric view\n    controls.targetRotationX = 0.3;\n    controls.targetRotationY = 0.8;\n    controls.targetDistance = 6;\n    controls.currentRotationX = 0.3;\n    controls.currentRotationY = 0.8;\n    controls.currentDistance = 6;\n\n    // Create initial product\n    createProduct(scene);\n\n    // Animation loop\n    const animate = () => {\n      animationIdRef.current = requestAnimationFrame(animate);\n      updateCamera();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      if (!mountRef.current) return;\n      const width = mountRef.current.clientWidth;\n      const height = mountRef.current.clientHeight;\n      camera.aspect = width / height;\n      camera.updateProjectionMatrix();\n      renderer.setSize(width, height);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n\n      // Remove canvas event listeners\n      const canvas = renderer.domElement;\n      canvas.removeEventListener('mousedown', onMouseDown);\n      canvas.removeEventListener('wheel', onWheel);\n      canvas.removeEventListener('touchstart', onTouchStart);\n      canvas.removeEventListener('touchmove', onTouchMove);\n      canvas.removeEventListener('touchend', onTouchEnd);\n      canvas.removeEventListener('contextmenu', e => e.preventDefault());\n\n      // Remove global event listeners\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n      if (animationIdRef.current) {\n        cancelAnimationFrame(animationIdRef.current);\n      }\n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      if (scalingTimeoutRef.current) {\n        clearTimeout(scalingTimeoutRef.current);\n      }\n      renderer.dispose();\n    };\n  }, [isMobile, dimensions, colors, material, productType]);\n\n  // Update product when configuration changes\n  useEffect(() => {\n    if (sceneRef.current && productRef.current) {\n      updateProduct();\n    }\n  }, [dimensions, colors, material]);\n\n  // Real-time dimension updates for GLB models (chairs)\n  useEffect(() => {\n    if (isModelLoaded && productRef.current && productType === 'chair') {\n      updateModelDimensions();\n    }\n  }, [dimensions.width, dimensions.height, dimensions.depth, isModelLoaded, productType]);\n  const handleAddToCart = () => {\n    const configuration = {\n      productType,\n      dimensions,\n      colors,\n      material,\n      quantity,\n      price: getCurrentPrice()\n    };\n    console.log('Adding to cart:', configuration);\n    alert(`Added ${quantity} ${productType}(s) to cart for $${getCurrentPrice().toFixed(2)}`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"configurator-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"configurator-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"back-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M19 12H5M12 19l-7-7 7-7\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), \"Back to Products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [\"Advanced \", productType.charAt(0).toUpperCase() + productType.slice(1), \" Configurator\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"configurator-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"configurator-layout-horizontal\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"viewer-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card viewer-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2L2 7L12 12L22 7L12 2Z\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 17L12 22L22 17\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 755,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M2 12L12 17L22 12\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 753,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"3D Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Interactive model of your configured \", productType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"model-viewer-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `model-viewer ${productType === 'chair' && isModelLoaded ? 'real-time-active' : ''}`,\n                  ref: mountRef\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 19\n                }, this), productType === 'chair' && isModelLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `scaling-notification ${showScalingNotification ? 'show' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"16\",\n                    height: \"16\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 17h18M3 7h18M7 3v18M17 3v18\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4\",\n                      fill: \"currentColor\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 23\n                  }, this), \"Real-time scaling active\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"viewer-controls-new\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"view-presets-horizontal\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('front'),\n                      title: \"Front View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"4\",\n                          y: \"4\",\n                          width: \"16\",\n                          height: \"16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\",\n                          rx: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 787,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"1.5\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 788,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 786,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Front\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 790,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 781,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('side'),\n                      title: \"Side View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M4 12h16M12 4v16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 798,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"1.5\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 799,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 797,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Side\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 801,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 792,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('top'),\n                      title: \"Top View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 3L3 7L12 11L21 7L12 3Z\",\n                          fill: \"currentColor\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 809,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 16L12 20L21 16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 810,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 808,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Top\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 803,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"preset-btn-new\",\n                      onClick: () => resetView('iso'),\n                      title: \"3D View\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 3L3 7L12 11L21 7L12 3Z\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 820,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 16L12 20L21 16\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 11L12 15L21 11\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 822,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 819,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"3D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 824,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 780,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"zoom-controls-horizontal\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"zoom-btn-new\",\n                      onClick: () => adjustZoom(-1),\n                      title: \"Zoom Out\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"11\",\n                          cy: \"11\",\n                          r: \"8\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 834,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M8 11h6\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 835,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 21l-4.35-4.35\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 836,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 833,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 828,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"zoom-btn-new\",\n                      onClick: () => adjustZoom(1),\n                      title: \"Zoom In\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"11\",\n                          cy: \"11\",\n                          r: \"8\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 845,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M11 8v6M8 11h6\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 846,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 21l-4.35-4.35\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 847,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 844,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 839,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"viewer-instructions\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: isMobile ? \"Drag to rotate • Pinch to zoom • Use preset views\" : \"Drag to rotate • Scroll to zoom • Use preset views\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 852,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: (product === null || product === void 0 ? void 0 : product.name) || `Custom ${productType.charAt(0).toUpperCase() + productType.slice(1)}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-description\",\n                children: [\"Configure your perfect \", productType, \" with our advanced 3D customization tool. Adjust dimensions, choose materials, and see your changes in real-time.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-features\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Real-time 3D visualization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 876,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 880,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 881,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Custom dimensions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 887,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 888,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Premium materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"18\",\n                    height: \"18\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12l2 2 4-4\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 894,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 895,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 893,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Professional quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"config-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Dimensions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dimension-controls-enhanced\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Width\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 913,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          width: Math.max(productType === 'chair' ? 50 : 100, dimensions.width - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 920,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 919,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 915,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.width\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 924,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 925,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 923,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          width: Math.min(productType === 'chair' ? 80 : 400, dimensions.width + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 932,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 931,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 927,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 914,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 912,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 50 : 100,\n                    max: productType === 'chair' ? 80 : 400,\n                    value: dimensions.width,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      width: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Depth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          depth: Math.max(productType === 'chair' ? 50 : 60, dimensions.depth - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 956,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 955,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 951,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.depth\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 960,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 961,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 959,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          depth: Math.min(productType === 'chair' ? 80 : 200, dimensions.depth + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 968,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 967,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 963,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 950,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 50 : 60,\n                    max: productType === 'chair' ? 80 : 200,\n                    value: dimensions.depth,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      depth: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dimension-group-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dimension-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"dimension-label\",\n                      children: \"Height\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"dimension-value-controls\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          height: Math.max(productType === 'chair' ? 70 : 60, dimensions.height - 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 992,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 991,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 987,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"dimension-display\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-value\",\n                          children: dimensions.height\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 996,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"dimension-unit\",\n                          children: \"cm\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 997,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 995,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"dimension-btn\",\n                        onClick: () => setDimensions({\n                          ...dimensions,\n                          height: Math.min(productType === 'chair' ? 120 : 120, dimensions.height + 10)\n                        }),\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          width: \"14\",\n                          height: \"14\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"none\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12 5v14M5 12h14\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1004,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1003,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 999,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 986,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"range\",\n                    min: productType === 'chair' ? 70 : 60,\n                    max: productType === 'chair' ? 120 : 120,\n                    value: dimensions.height,\n                    onChange: e => setDimensions({\n                      ...dimensions,\n                      height: parseInt(e.target.value)\n                    }),\n                    className: \"dimension-slider-enhanced\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1026,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\",\n                      fill: \"#F0B21B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1027,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Colors & Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: isModelLoaded ? 'GLB model materials are preserved' : 'Choose colors and surface materials'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 17\n              }, this), modelError && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"model-error-notice\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"#ffc107\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1039,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 8v4M12 16h.01\",\n                    stroke: \"#ffc107\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 21\n                }, this), modelError]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"color-material-grid-enhanced\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"color-section-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: \"Primary Color\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1048,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"current-color-name\",\n                        children: colors.primary === '#6B7280' ? 'Slate Gray' : colors.primary === '#374151' ? 'Dark Gray' : colors.primary === '#1F2937' ? 'Charcoal' : colors.primary === '#111827' ? 'Black' : colors.primary === '#F3F4F6' ? 'Light Gray' : colors.primary === '#E5E7EB' ? 'Silver' : colors.primary === '#F0B21B' ? 'Golden Yellow' : colors.primary === '#DC2626' ? 'Red' : colors.primary === '#059669' ? 'Green' : colors.primary === '#2563EB' ? 'Blue' : colors.primary === '#7C3AED' ? 'Purple' : colors.primary === '#EA580C' ? 'Orange' : 'Custom'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1050,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"current-color-preview\",\n                        style: {\n                          backgroundColor: colors.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1064,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1049,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1047,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-palette-enhanced\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"color-group-label\",\n                        children: \"Neutrals\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1072,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"color-row\",\n                        children: ['#6B7280', '#374151', '#1F2937', '#111827', '#F3F4F6', '#E5E7EB'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: `color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`,\n                          style: {\n                            backgroundColor: color\n                          },\n                          onClick: () => setColors({\n                            ...colors,\n                            primary: color\n                          }),\n                          title: color === '#6B7280' ? 'Slate Gray' : color === '#374151' ? 'Dark Gray' : color === '#1F2937' ? 'Charcoal' : color === '#111827' ? 'Black' : color === '#F3F4F6' ? 'Light Gray' : 'Silver'\n                        }, color, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1075,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1073,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1071,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-group\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"color-group-label\",\n                        children: \"Accent Colors\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1090,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"color-row\",\n                        children: ['#F0B21B', '#DC2626', '#059669', '#2563EB', '#7C3AED', '#EA580C'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: `color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`,\n                          style: {\n                            backgroundColor: color\n                          },\n                          onClick: () => setColors({\n                            ...colors,\n                            primary: color\n                          }),\n                          title: color === '#F0B21B' ? 'Golden Yellow' : color === '#DC2626' ? 'Red' : color === '#059669' ? 'Green' : color === '#2563EB' ? 'Blue' : color === '#7C3AED' ? 'Purple' : 'Orange'\n                        }, color, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1093,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1091,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1089,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"material-section-enhanced\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      children: \"Material & Finish\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1112,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"current-material-name\",\n                      children: material.charAt(0).toUpperCase() + material.slice(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1113,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"material-options-enhanced\",\n                    children: (productType === 'chair' ? [{\n                      name: 'Mesh',\n                      value: 'mesh',\n                      icon: '🕸️',\n                      desc: 'Breathable mesh fabric'\n                    }, {\n                      name: 'Fabric',\n                      value: 'fabric',\n                      icon: '🧵',\n                      desc: 'Soft upholstery fabric'\n                    }, {\n                      name: 'Leather',\n                      value: 'leather',\n                      icon: '🐄',\n                      desc: 'Premium leather finish'\n                    }] : [{\n                      name: 'Wood',\n                      value: 'wood',\n                      icon: '🌳',\n                      desc: 'Natural wood grain'\n                    }, {\n                      name: 'Metal',\n                      value: 'metal',\n                      icon: '⚙️',\n                      desc: 'Brushed metal finish'\n                    }, {\n                      name: 'Glass',\n                      value: 'glass',\n                      icon: '💎',\n                      desc: 'Tempered glass surface'\n                    }]).map(mat => /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `material-option-enhanced ${material === mat.value ? 'active' : ''}`,\n                      onClick: () => setMaterial(mat.value),\n                      title: mat.desc,\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"material-icon\",\n                        children: mat.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1133,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"material-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"material-name\",\n                          children: mat.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1135,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"material-desc\",\n                          children: mat.desc\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1136,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1134,\n                        columnNumber: 27\n                      }, this)]\n                    }, mat.value, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1127,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"config-card pricing-card-modern\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-icon-modern\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"24\",\n                    height: \"24\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2M6.4 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm11.2 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z\",\n                      stroke: \"#F0B21B\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"card-title-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Order Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1154,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Configure and add to cart\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1155,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1153,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-content-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-summary-modern\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-info-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-icon\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                          x: \"3\",\n                          y: \"4\",\n                          width: \"18\",\n                          height: \"12\",\n                          rx: \"2\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"1.5\",\n                          fill: \"none\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1165,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M7 8h10M7 12h6\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"1.5\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1166,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1164,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1163,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"product-details\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"product-name\",\n                        children: [\"Custom \", productType.charAt(0).toUpperCase() + productType.slice(1)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1170,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"product-specs\",\n                        children: [dimensions.width, \"\\xD7\", dimensions.depth, \"\\xD7\", dimensions.height, \"cm\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1171,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1169,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1162,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"quantity-section-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"section-label\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"18\",\n                      height: \"18\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M9 12l2 2 4-4\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1180,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"9\",\n                        stroke: \"#F0B21B\",\n                        strokeWidth: \"1.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1181,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1179,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1183,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1178,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"quantity-controls-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setQuantity(Math.max(1, quantity - 1)),\n                      className: \"quantity-btn-modern\",\n                      disabled: quantity <= 1,\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5 12h14\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1192,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1191,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1186,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"quantity-display-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"quantity-number-modern\",\n                        children: quantity\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1196,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1195,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setQuantity(quantity + 1),\n                      className: \"quantity-btn-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 5v14M5 12h14\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1203,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1202,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1198,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1185,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-breakdown-modern\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"12\",\n                          cy: \"12\",\n                          r: \"10\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1214,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 6v6l4 2\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\",\n                          strokeLinecap: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1215,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1213,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Base Price\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1217,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1212,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount\",\n                      children: [\"$\", getBasePrice().toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1219,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1211,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1224,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                          points: \"3.27,6.96 12,12.01 20.73,6.96\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1225,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                          x1: \"12\",\n                          y1: \"22.08\",\n                          x2: \"12\",\n                          y2: \"12\",\n                          stroke: \"#64748b\",\n                          strokeWidth: \"1.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1226,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1223,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Material & Size\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1228,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1222,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount\",\n                      children: [\"+$\", (getCurrentPrice() / quantity - getBasePrice()).toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1230,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1221,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-divider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"price-row-modern total-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-label-modern total-label\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"18\",\n                        height: \"18\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12 2L2 7l10 5 10-5-10-5z\",\n                          fill: \"#F0B21B\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1236,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M2 17l10 5 10-5\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1237,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M2 12l10 5 10-5\",\n                          stroke: \"#F0B21B\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1238,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1235,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Total (\", quantity, \" item\", quantity > 1 ? 's' : '', \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1240,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1234,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price-amount-total\",\n                      children: [\"$\", getCurrentPrice().toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1242,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1233,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"add-to-cart-btn-modern\",\n                  onClick: handleAddToCart,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"btn-content-modern\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-icon-modern\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        width: \"20\",\n                        height: \"20\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\",\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1251,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"9\",\n                          cy: \"20\",\n                          r: \"1\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1252,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                          cx: \"20\",\n                          cy: \"20\",\n                          r: \"1\",\n                          stroke: \"currentColor\",\n                          strokeWidth: \"2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1253,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1250,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1249,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"btn-text-modern\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-action-modern\",\n                        children: \"Add to Cart\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1257,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"btn-price-modern\",\n                        children: [\"$\", getCurrentPrice().toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1258,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1256,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1248,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1247,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 730,\n    columnNumber: 5\n  }, this);\n};\n_s(Advanced3DConfigurator, \"FBzwAKkF2gSmnr0yp8k180vOieE=\");\n_c = Advanced3DConfigurator;\nexport default Advanced3DConfigurator;\nvar _c;\n$RefreshReg$(_c, \"Advanced3DConfigurator\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "THREE", "GLTFLoader", "jsxDEV", "_jsxDEV", "Advanced3DConfigurator", "onBack", "product", "_s", "mountRef", "sceneRef", "rendererRef", "productRef", "animationIdRef", "cameraRef", "getProductType", "name", "toLowerCase", "includes", "productType", "dimensions", "setDimensions", "width", "depth", "height", "colors", "setColors", "primary", "secondary", "accent", "material", "setMaterial", "quantity", "setQuantity", "isModelLoaded", "setIsModelLoaded", "modelError", "setModelError", "originalModelDimensionsRef", "baseScaleRef", "showScalingNotification", "setShowScalingNotification", "scalingTimeoutRef", "isMobile", "setIsMobile", "checkMobile", "mobile", "window", "innerWidth", "test", "navigator", "userAgent", "addEventListener", "removeEventListener", "getBasePrice", "basePrices", "table", "chair", "cabinet", "shelf", "workstation", "calculatePrice", "price", "sizeMultiplier", "Math", "max", "min", "materialMultipliers", "wood", "metal", "glass", "plastic", "leather", "mesh", "fabric", "vinyl", "getCurrentPrice", "loadGLBModel", "scene", "modelPath", "loader", "console", "log", "gltf", "Promise", "resolve", "reject", "load", "progress", "loaded", "total", "error", "model", "box", "Box3", "setFromObject", "center", "getCenter", "Vector3", "size", "getSize", "current", "x", "y", "z", "position", "sub", "maxDimension", "scale", "setScalar", "updateModelDimensions", "traverse", "child", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "needsUpdate", "add", "createProduct", "cameraControlsRef", "targetRotationX", "targetRotationY", "currentRotationX", "currentRotationY", "targetDistance", "currentDistance", "isAnimating", "resetView", "viewType", "controls", "PI", "adjustZoom", "direction", "zoomAmount", "newDistance", "setTimeout", "createTableGeometry", "group", "topGeometry", "BoxGeometry", "topMaterial", "MeshStandardMaterial", "color", "roughness", "metalness", "transparent", "opacity", "tableTop", "<PERSON><PERSON>", "legSegments", "legGeometry", "CylinderGeometry", "legMaterial", "legPositions", "for<PERSON>ach", "pos", "leg", "set", "createChairGeometry", "seatGeometry", "seatMaterial", "seat", "backGeometry", "backMaterial", "backrest", "baseSegments", "baseGeometry", "baseMaterial", "base", "model3D", "productGroup", "Group", "baseScale", "clearTimeout", "defaultDimensions", "scaleX", "scaleY", "scaleZ", "updateProduct", "remove", "Scene", "background", "Color", "camera", "PerspectiveCamera", "clientWidth", "clientHeight", "lookAt", "renderer", "WebGLRenderer", "antialias", "alpha", "powerPreference", "setSize", "setPixelRatio", "devicePixelRatio", "setClearColor", "shadowMap", "enabled", "type", "PCFSoftShadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight", "DirectionalLight", "shadow", "mapSize", "isMouseDown", "mouseX", "mouseY", "onMouseDown", "event", "clientX", "clientY", "onMouseMove", "deltaX", "deltaY", "onMouseUp", "onWheel", "preventDefault", "zoomSpeed", "lastTouchDistance", "initialTouchDistance", "getTouchDistance", "touches", "length", "dx", "dy", "sqrt", "onTouchStart", "onTouchMove", "deltaDistance", "abs", "onTouchEnd", "canvas", "document", "passive", "e", "style", "touchAction", "userSelect", "updateCamera", "lerpFactor", "sin", "cos", "rotXDiff", "rotYDiff", "distDiff", "animate", "requestAnimationFrame", "render", "handleResize", "aspect", "updateProjectionMatrix", "cancelAnimationFrame", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "handleAddToCart", "configuration", "alert", "toFixed", "className", "children", "onClick", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "slice", "ref", "title", "rx", "cx", "cy", "r", "value", "onChange", "parseInt", "target", "backgroundColor", "map", "icon", "desc", "mat", "disabled", "points", "x1", "y1", "x2", "y2", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/3d/3DConfigurator.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport '../../styles/configurator.css';\n\nconst Advanced3DConfigurator = ({ onBack, product }) => {\n  // 3D Scene refs\n  const mountRef = useRef(null);\n  const sceneRef = useRef(null);\n  const rendererRef = useRef(null);\n  const productRef = useRef(null);\n  const animationIdRef = useRef(null);\n  const cameraRef = useRef(null);\n\n  // Determine product type\n  const getProductType = () => {\n    if (!product) return 'table';\n    const name = product.name.toLowerCase();\n    if (name.includes('chair')) return 'chair';\n    if (name.includes('cabinet') || name.includes('storage')) return 'cabinet';\n    if (name.includes('shelf')) return 'shelf';\n    if (name.includes('workstation')) return 'workstation';\n    return 'table'; // default\n  };\n\n  const productType = getProductType();\n\n  // State for configuration\n  const [dimensions, setDimensions] = useState({\n    width: productType === 'chair' ? 60 : 280,\n    depth: productType === 'chair' ? 60 : 140,\n    height: productType === 'chair' ? 80 : 75\n  });\n\n  const [colors, setColors] = useState({\n    primary: '#6B7280',\n    secondary: '#374151',\n    accent: '#FFFFFF'\n  });\n\n  const [material, setMaterial] = useState(productType === 'chair' ? 'mesh' : 'wood');\n  const [quantity, setQuantity] = useState(1);\n\n  // Model loading state\n  const [isModelLoaded, setIsModelLoaded] = useState(false);\n  const [modelError, setModelError] = useState('');\n\n  // Store original model dimensions for scaling calculations\n  const originalModelDimensionsRef = useRef(null);\n  const baseScaleRef = useRef(1);\n\n  // Scaling notification state\n  const [showScalingNotification, setShowScalingNotification] = useState(false);\n  const scalingTimeoutRef = useRef(null);\n\n  // Mobile detection\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      const mobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      setIsMobile(mobile);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Enhanced pricing logic\n  const getBasePrice = () => {\n    const basePrices = {\n      table: 500,\n      chair: 200,\n      cabinet: 800,\n      shelf: 300,\n      workstation: 1200\n    };\n    return basePrices[productType] || 500;\n  };\n\n  const calculatePrice = () => {\n    let price = getBasePrice();\n\n    // Size multiplier\n    const sizeMultiplier = (dimensions.width * dimensions.depth * dimensions.height) / 100000;\n    price *= Math.max(0.5, Math.min(2.0, sizeMultiplier));\n\n    // Material multiplier\n    const materialMultipliers = {\n      wood: 1.0,\n      metal: 1.2,\n      glass: 1.5,\n      plastic: 0.8,\n      leather: 1.8,\n      mesh: 1.1,\n      fabric: 0.9,\n      vinyl: 1.0\n    };\n    price *= materialMultipliers[material] || 1.0;\n\n    return price * quantity;\n  };\n\n  const getCurrentPrice = () => calculatePrice();\n\n  // GLB Model Loading\n  const loadGLBModel = async (scene, modelPath) => {\n    const loader = new GLTFLoader();\n\n    try {\n      console.log('Starting GLB model load from:', modelPath);\n      setModelError('');\n      const gltf = await new Promise((resolve, reject) => {\n        loader.load(\n          modelPath,\n          (gltf) => {\n            console.log('GLB file loaded successfully:', gltf);\n            resolve(gltf);\n          },\n          (progress) => {\n            console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');\n          },\n          (error) => {\n            console.error('GLB loading error:', error);\n            reject(error);\n          }\n        );\n      });\n\n      const model = gltf.scene;\n\n      // Get model bounds and center it\n      const box = new THREE.Box3().setFromObject(model);\n      const center = box.getCenter(new THREE.Vector3());\n      const size = box.getSize(new THREE.Vector3());\n\n      // Store original model dimensions for real-time scaling\n      originalModelDimensionsRef.current = {\n        width: size.x,\n        height: size.y,\n        depth: size.z\n      };\n\n      // Center the model\n      model.position.sub(center);\n\n      // Scale model to reasonable size and store base scale\n      const maxDimension = Math.max(size.x, size.y, size.z);\n      if (maxDimension > 3) {\n        const scale = 3 / maxDimension;\n        model.scale.setScalar(scale);\n        baseScaleRef.current = scale;\n      } else {\n        baseScaleRef.current = 1;\n      }\n\n      // Apply initial dimension-based scaling for chairs\n      if (productType === 'chair') {\n        updateModelDimensions(model);\n      }\n\n      // Enable shadows for all meshes\n      model.traverse((child) => {\n        if (child.isMesh) {\n          child.castShadow = true;\n          child.receiveShadow = true;\n\n          // Ensure materials are properly configured\n          if (child.material) {\n            child.material.needsUpdate = true;\n          }\n        }\n      });\n\n      productRef.current = model;\n      scene.add(model);\n      setIsModelLoaded(true);\n\n      return model;\n    } catch (error) {\n      console.error('Error loading GLB model:', error);\n      setModelError('Failed to load 3D model. Using default geometry.');\n      setIsModelLoaded(false);\n\n      // Fallback to geometric shapes\n      createProduct(scene);\n      return null;\n    }\n  };\n\n  // Camera control state - moved outside useEffect for global access\n  const cameraControlsRef = useRef({\n    targetRotationX: 0,\n    targetRotationY: 0,\n    currentRotationX: 0,\n    currentRotationY: 0,\n    targetDistance: 6,\n    currentDistance: 6,\n    isAnimating: false\n  });\n\n  // View control functions\n  const resetView = (viewType) => {\n    if (!cameraRef.current) return;\n\n    const controls = cameraControlsRef.current;\n    controls.isAnimating = true;\n\n    switch (viewType) {\n      case 'front':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 6;\n        break;\n      case 'side':\n        controls.targetRotationX = 0;\n        controls.targetRotationY = Math.PI / 2;\n        controls.targetDistance = 6;\n        break;\n      case 'top':\n        controls.targetRotationX = Math.PI / 2;\n        controls.targetRotationY = 0;\n        controls.targetDistance = 8;\n        break;\n      case 'iso':\n      default:\n        controls.targetRotationX = 0.3;\n        controls.targetRotationY = 0.8;\n        controls.targetDistance = 6;\n        break;\n    }\n  };\n\n  const adjustZoom = (direction) => {\n    if (!cameraRef.current || !cameraControlsRef.current) return;\n\n    const controls = cameraControlsRef.current;\n    const zoomAmount = direction * 1.5;\n    const newDistance = controls.targetDistance + zoomAmount;\n\n    // Smooth zoom with bounds checking\n    controls.targetDistance = Math.max(2.5, Math.min(15, newDistance));\n\n    // Add slight animation flag for smoother zoom\n    controls.isAnimating = true;\n    setTimeout(() => {\n      if (controls) controls.isAnimating = false;\n    }, 500);\n  };\n\n  // Create 3D product functions\n  const createTableGeometry = (group) => {\n    // Table top\n    const topGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.08,\n      dimensions.depth / 100\n    );\n    const topMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: material === 'glass' ? 0.1 : 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0,\n      transparent: material === 'glass',\n      opacity: material === 'glass' ? 0.8 : 1.0\n    });\n\n    const tableTop = new THREE.Mesh(topGeometry, topMaterial);\n    tableTop.position.y = dimensions.height / 100 - 0.04;\n    if (!isMobile) {\n      tableTop.castShadow = true;\n      tableTop.receiveShadow = true;\n    }\n    group.add(tableTop);\n\n    // Legs with mobile optimization\n    const legSegments = isMobile ? 8 : 12;\n    const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, dimensions.height / 100 - 0.08, legSegments);\n    const legMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7,\n      metalness: material === 'metal' ? 0.8 : 0.0\n    });\n\n    const legPositions = [\n      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],\n      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.1],\n      [-dimensions.width / 200 + 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1],\n      [dimensions.width / 200 - 0.1, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.1]\n    ];\n\n    legPositions.forEach(pos => {\n      const leg = new THREE.Mesh(legGeometry, legMaterial);\n      leg.position.set(pos[0], pos[1], pos[2]);\n      if (!isMobile) {\n        leg.castShadow = true;\n      }\n      group.add(leg);\n    });\n  };\n\n  const createChairGeometry = (group) => {\n    // Seat\n    const seatGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.06,\n      dimensions.depth / 100\n    );\n    const seatMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const seat = new THREE.Mesh(seatGeometry, seatMaterial);\n    seat.position.y = 0.4;\n    if (!isMobile) {\n      seat.castShadow = true;\n    }\n    group.add(seat);\n\n    // Backrest\n    const backGeometry = new THREE.BoxGeometry(\n      dimensions.width / 100,\n      0.02,\n      dimensions.height / 150\n    );\n    const backMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const backrest = new THREE.Mesh(backGeometry, backMaterial);\n    backrest.position.set(0, 0.6, -dimensions.depth / 200 + 0.01);\n    if (!isMobile) {\n      backrest.castShadow = true;\n    }\n    group.add(backrest);\n\n    // Base with mobile optimization\n    const baseSegments = isMobile ? 5 : 8;\n    const baseGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.04, baseSegments);\n    const baseMaterial = new THREE.MeshStandardMaterial({\n      color: colors.primary,\n      roughness: 0.7\n    });\n    const base = new THREE.Mesh(baseGeometry, baseMaterial);\n    base.position.y = 0.02;\n    if (!isMobile) {\n      base.castShadow = true;\n    }\n    group.add(base);\n  };\n\n  const createProduct = async (scene) => {\n    // Check if product has a 3D model file\n    if (product && product.model3D) {\n      console.log('Loading GLB model:', product.model3D);\n      console.log('Product name:', product.name);\n      console.log('Product type detected:', productType);\n      const model = await loadGLBModel(scene, product.model3D);\n      if (model) {\n        console.log('GLB model loaded successfully!');\n        return; // Successfully loaded GLB model\n      }\n    }\n\n    // Fallback to geometric shapes if no GLB model or loading failed\n    const productGroup = new THREE.Group();\n\n    switch (productType) {\n      case 'table':\n        createTableGeometry(productGroup);\n        break;\n      case 'chair':\n        createChairGeometry(productGroup);\n        break;\n      default:\n        createTableGeometry(productGroup);\n    }\n\n    productRef.current = productGroup;\n    scene.add(productGroup);\n  };\n\n  // Update model dimensions in real-time for GLB models\n  const updateModelDimensions = (model = productRef.current) => {\n    if (!model || !originalModelDimensionsRef.current || productType !== 'chair') return;\n\n    const baseScale = baseScaleRef.current;\n\n    // Show scaling notification\n    setShowScalingNotification(true);\n\n    // Clear existing timeout\n    if (scalingTimeoutRef.current) {\n      clearTimeout(scalingTimeoutRef.current);\n    }\n\n    // Hide notification after 2 seconds\n    scalingTimeoutRef.current = setTimeout(() => {\n      setShowScalingNotification(false);\n    }, 2000);\n\n    // Calculate scale factors based on dimension changes\n    // Default chair dimensions (in cm) for reference\n    const defaultDimensions = {\n      width: 60,\n      height: 80,\n      depth: 60\n    };\n\n    // Calculate scale factors for each dimension\n    const scaleX = (dimensions.width / defaultDimensions.width) * baseScale;\n    const scaleY = (dimensions.height / defaultDimensions.height) * baseScale;\n    const scaleZ = (dimensions.depth / defaultDimensions.depth) * baseScale;\n\n    // Apply non-uniform scaling to maintain realistic proportions\n    // For chairs: width affects seat width, height affects overall height, depth affects seat depth\n    model.scale.set(scaleX, scaleY, scaleZ);\n\n    // Ensure the model stays centered after scaling\n    const box = new THREE.Box3().setFromObject(model);\n    const center = box.getCenter(new THREE.Vector3());\n    model.position.sub(center);\n  };\n\n  // Update product materials and geometry\n  const updateProduct = () => {\n    if (!productRef.current || !sceneRef.current) return;\n\n    // For GLB models with real-time dimension updates\n    if (isModelLoaded && productType === 'chair') {\n      updateModelDimensions();\n      return;\n    }\n\n    // For procedural geometry, recreate the model\n    sceneRef.current.remove(productRef.current);\n    createProduct(sceneRef.current);\n  };\n\n  // 3D Scene Setup\n  useEffect(() => {\n    if (!mountRef.current) return;\n\n    // Scene setup\n    const scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf5f5f5); // Gray background\n    sceneRef.current = scene;\n\n    // Camera setup\n    const camera = new THREE.PerspectiveCamera(\n      75,\n      mountRef.current.clientWidth / mountRef.current.clientHeight,\n      0.1,\n      1000\n    );\n    camera.position.set(4, 3, 4);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // Renderer setup with mobile optimizations\n    const renderer = new THREE.WebGLRenderer({\n      antialias: !isMobile,\n      alpha: true,\n      powerPreference: isMobile ? \"low-power\" : \"high-performance\"\n    });\n    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, isMobile ? 1.5 : 2));\n    renderer.setClearColor(0xf5f5f5, 1.0);\n    renderer.shadowMap.enabled = !isMobile;\n    if (!isMobile) {\n      renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n    }\n    mountRef.current.appendChild(renderer.domElement);\n    rendererRef.current = renderer;\n\n    // Lighting System with mobile optimizations\n    const ambientLight = new THREE.AmbientLight(0x404040, isMobile ? 0.8 : 0.6);\n    scene.add(ambientLight);\n\n    const directionalLight = new THREE.DirectionalLight(0xffffff, isMobile ? 0.8 : 1.0);\n    directionalLight.position.set(5, 5, 5);\n    if (!isMobile) {\n      directionalLight.castShadow = true;\n      directionalLight.shadow.mapSize.width = 1024;\n      directionalLight.shadow.mapSize.height = 1024;\n    }\n    scene.add(directionalLight);\n\n    // Mouse interaction variables\n    let isMouseDown = false;\n    let mouseX = 0;\n    let mouseY = 0;\n\n    // Get camera controls from ref\n    const controls = cameraControlsRef.current;\n\n    // Mouse event handlers\n    const onMouseDown = (event) => {\n      isMouseDown = true;\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n\n    const onMouseMove = (event) => {\n      if (!isMouseDown) return;\n      const deltaX = event.clientX - mouseX;\n      const deltaY = event.clientY - mouseY;\n\n      // Increased sensitivity for better responsiveness\n      controls.targetRotationY += deltaX * 0.015;\n      controls.targetRotationX += deltaY * 0.015;\n\n      controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n\n      mouseX = event.clientX;\n      mouseY = event.clientY;\n    };\n\n    const onMouseUp = () => {\n      isMouseDown = false;\n    };\n\n    const onWheel = (event) => {\n      event.preventDefault();\n      const zoomSpeed = isMobile ? 0.008 : 0.01;\n      controls.targetDistance += event.deltaY * zoomSpeed;\n      controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n    };\n\n    // Enhanced touch event handlers for mobile\n    let lastTouchDistance = 0;\n    let initialTouchDistance = 0;\n\n    const getTouchDistance = (touches) => {\n      if (touches.length < 2) return 0;\n      const dx = touches[0].clientX - touches[1].clientX;\n      const dy = touches[0].clientY - touches[1].clientY;\n      return Math.sqrt(dx * dx + dy * dy);\n    };\n\n    const onTouchStart = (event) => {\n      if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        isMouseDown = false;\n        initialTouchDistance = getTouchDistance(event.touches);\n        lastTouchDistance = initialTouchDistance;\n      }\n    };\n\n    const onTouchMove = (event) => {\n      event.preventDefault();\n\n      if (event.touches.length === 1 && isMouseDown) {\n        // Single finger rotation with increased sensitivity\n        const deltaX = event.touches[0].clientX - mouseX;\n        const deltaY = event.touches[0].clientY - mouseY;\n\n        controls.targetRotationY += deltaX * 0.012;\n        controls.targetRotationX += deltaY * 0.012;\n\n        controls.targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, controls.targetRotationX));\n\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      } else if (event.touches.length === 2) {\n        // Two finger pinch zoom\n        const currentDistance = getTouchDistance(event.touches);\n        const deltaDistance = currentDistance - lastTouchDistance;\n\n        if (Math.abs(deltaDistance) > 3) { // Increased threshold for smoother zoom\n          controls.targetDistance -= deltaDistance * 0.015;\n          controls.targetDistance = Math.max(2.5, Math.min(15, controls.targetDistance));\n          lastTouchDistance = currentDistance;\n        }\n      }\n    };\n\n    const onTouchEnd = (event) => {\n      if (event.touches.length === 0) {\n        isMouseDown = false;\n      } else if (event.touches.length === 1) {\n        isMouseDown = true;\n        mouseX = event.touches[0].clientX;\n        mouseY = event.touches[0].clientY;\n      }\n    };\n\n    // Add event listeners to both canvas and document for better coverage\n    const canvas = renderer.domElement;\n\n    // Mouse events\n    canvas.addEventListener('mousedown', onMouseDown);\n    document.addEventListener('mousemove', onMouseMove); // Global mouse move for better tracking\n    document.addEventListener('mouseup', onMouseUp); // Global mouse up\n    canvas.addEventListener('wheel', onWheel, { passive: false });\n\n    // Touch events with better gesture handling\n    canvas.addEventListener('touchstart', onTouchStart, { passive: false });\n    canvas.addEventListener('touchmove', onTouchMove, { passive: false });\n    canvas.addEventListener('touchend', onTouchEnd, { passive: false });\n\n    // Prevent context menu on long press\n    canvas.addEventListener('contextmenu', (e) => e.preventDefault());\n\n    // Prevent default touch behaviors that might interfere\n    canvas.style.touchAction = 'none';\n    canvas.style.userSelect = 'none';\n\n    // Enhanced camera update function with smooth interpolation\n    const updateCamera = () => {\n      const lerpFactor = controls.isAnimating ? 0.15 : 0.08; // Faster when animating to presets\n\n      // Smooth interpolation\n      controls.currentRotationX += (controls.targetRotationX - controls.currentRotationX) * lerpFactor;\n      controls.currentRotationY += (controls.targetRotationY - controls.currentRotationY) * lerpFactor;\n      controls.currentDistance += (controls.targetDistance - controls.currentDistance) * lerpFactor;\n\n      // Calculate spherical coordinates for camera position\n      const x = Math.sin(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n      const y = Math.sin(controls.currentRotationX) * controls.currentDistance + 2;\n      const z = Math.cos(controls.currentRotationY) * Math.cos(controls.currentRotationX) * controls.currentDistance;\n\n      camera.position.set(x, y, z);\n      camera.lookAt(0, 0, 0);\n\n      // Stop animation flag when close enough to target\n      if (controls.isAnimating) {\n        const rotXDiff = Math.abs(controls.targetRotationX - controls.currentRotationX);\n        const rotYDiff = Math.abs(controls.targetRotationY - controls.currentRotationY);\n        const distDiff = Math.abs(controls.targetDistance - controls.currentDistance);\n\n        if (rotXDiff < 0.01 && rotYDiff < 0.01 && distDiff < 0.1) {\n          controls.isAnimating = false;\n        }\n      }\n    };\n\n    // Initialize camera controls with default isometric view\n    controls.targetRotationX = 0.3;\n    controls.targetRotationY = 0.8;\n    controls.targetDistance = 6;\n    controls.currentRotationX = 0.3;\n    controls.currentRotationY = 0.8;\n    controls.currentDistance = 6;\n\n    // Create initial product\n    createProduct(scene);\n\n    // Animation loop\n    const animate = () => {\n      animationIdRef.current = requestAnimationFrame(animate);\n      updateCamera();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      if (!mountRef.current) return;\n      const width = mountRef.current.clientWidth;\n      const height = mountRef.current.clientHeight;\n      camera.aspect = width / height;\n      camera.updateProjectionMatrix();\n      renderer.setSize(width, height);\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n\n      // Remove canvas event listeners\n      const canvas = renderer.domElement;\n      canvas.removeEventListener('mousedown', onMouseDown);\n      canvas.removeEventListener('wheel', onWheel);\n      canvas.removeEventListener('touchstart', onTouchStart);\n      canvas.removeEventListener('touchmove', onTouchMove);\n      canvas.removeEventListener('touchend', onTouchEnd);\n      canvas.removeEventListener('contextmenu', (e) => e.preventDefault());\n\n      // Remove global event listeners\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n\n      if (animationIdRef.current) {\n        cancelAnimationFrame(animationIdRef.current);\n      }\n      if (mountRef.current && renderer.domElement) {\n        mountRef.current.removeChild(renderer.domElement);\n      }\n      if (scalingTimeoutRef.current) {\n        clearTimeout(scalingTimeoutRef.current);\n      }\n      renderer.dispose();\n    };\n  }, [isMobile, dimensions, colors, material, productType]);\n\n  // Update product when configuration changes\n  useEffect(() => {\n    if (sceneRef.current && productRef.current) {\n      updateProduct();\n    }\n  }, [dimensions, colors, material]);\n\n  // Real-time dimension updates for GLB models (chairs)\n  useEffect(() => {\n    if (isModelLoaded && productRef.current && productType === 'chair') {\n      updateModelDimensions();\n    }\n  }, [dimensions.width, dimensions.height, dimensions.depth, isModelLoaded, productType]);\n\n  const handleAddToCart = () => {\n    const configuration = {\n      productType,\n      dimensions,\n      colors,\n      material,\n      quantity,\n      price: getCurrentPrice()\n    };\n\n    console.log('Adding to cart:', configuration);\n    alert(`Added ${quantity} ${productType}(s) to cart for $${getCurrentPrice().toFixed(2)}`);\n  };\n\n  return (\n    <div className=\"configurator-container\">\n      {/* Header */}\n      <div className=\"configurator-header\">\n        <div className=\"container\">\n          <button onClick={onBack} className=\"back-btn\">\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n              <path d=\"M19 12H5M12 19l-7-7 7-7\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n            </svg>\n            Back to Products\n          </button>\n          <h1>Advanced {productType.charAt(0).toUpperCase() + productType.slice(1)} Configurator</h1>\n        </div>\n      </div>\n\n      {/* Main Configuration */}\n      <div className=\"configurator-main\">\n        <div className=\"container\">\n          <div className=\"configurator-layout-horizontal\">\n            {/* Left Side - 3D Model Viewer */}\n            <div className=\"viewer-panel\">\n              <div className=\"config-card viewer-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" fill=\"#F0B21B\"/>\n                      <path d=\"M2 17L12 22L22 17\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <path d=\"M2 12L12 17L22 12\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>3D Preview</h4>\n                    <p>Interactive model of your configured {productType}</p>\n                  </div>\n                </div>\n                <div className=\"model-viewer-container\">\n                  <div\n                    className={`model-viewer ${productType === 'chair' && isModelLoaded ? 'real-time-active' : ''}`}\n                    ref={mountRef}\n                  ></div>\n                  {/* Real-time scaling notification */}\n                  {productType === 'chair' && isModelLoaded && (\n                    <div className={`scaling-notification ${showScalingNotification ? 'show' : ''}`}>\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M3 17h18M3 7h18M7 3v18M17 3v18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <path d=\"M3 7l4-4 4 4M3 17l4 4 4-4M7 3l-4 4 4 4M17 3l4 4-4 4\" fill=\"currentColor\"/>\n                      </svg>\n                      Real-time scaling active\n                    </div>\n                  )}\n                  <div className=\"viewer-controls-new\">\n                    <div className=\"view-presets-horizontal\">\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('front')}\n                        title=\"Front View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <rect x=\"4\" y=\"4\" width=\"16\" height=\"16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\" rx=\"2\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\"/>\n                        </svg>\n                        <span>Front</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('side')}\n                        title=\"Side View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M4 12h16M12 4v16\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\"/>\n                        </svg>\n                        <span>Side</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('top')}\n                        title=\"Top View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 3L3 7L12 11L21 7L12 3Z\" fill=\"currentColor\"/>\n                          <path d=\"M3 16L12 20L21 16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                        </svg>\n                        <span>Top</span>\n                      </button>\n                      <button\n                        className=\"preset-btn-new\"\n                        onClick={() => resetView('iso')}\n                        title=\"3D View\"\n                      >\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 3L3 7L12 11L21 7L12 3Z\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M3 16L12 20L21 16\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M3 11L12 15L21 11\" stroke=\"currentColor\" strokeWidth=\"1.5\" fill=\"none\"/>\n                        </svg>\n                        <span>3D</span>\n                      </button>\n                    </div>\n                    <div className=\"zoom-controls-horizontal\">\n                      <button\n                        className=\"zoom-btn-new\"\n                        onClick={() => adjustZoom(-1)}\n                        title=\"Zoom Out\"\n                      >\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M8 11h6\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M21 21l-4.35-4.35\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                        </svg>\n                      </button>\n                      <button\n                        className=\"zoom-btn-new\"\n                        onClick={() => adjustZoom(1)}\n                        title=\"Zoom In\"\n                      >\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M11 8v6M8 11h6\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                          <path d=\"M21 21l-4.35-4.35\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"viewer-instructions\">\n                    <span>\n                      {isMobile\n                        ? \"Drag to rotate • Pinch to zoom • Use preset views\"\n                        : \"Drag to rotate • Scroll to zoom • Use preset views\"\n                      }\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Product Info Below 3D Viewer */}\n              <div className=\"product-info-section\">\n                <h3>{product?.name || `Custom ${productType.charAt(0).toUpperCase() + productType.slice(1)}`}</h3>\n                <p className=\"product-description\">\n                  Configure your perfect {productType} with our advanced 3D customization tool.\n                  Adjust dimensions, choose materials, and see your changes in real-time.\n                </p>\n                <div className=\"product-features\">\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Real-time 3D visualization</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Custom dimensions</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Premium materials</span>\n                  </div>\n                  <div className=\"feature-item\">\n                    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    </svg>\n                    <span>Professional quality</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Side - Configuration Panel */}\n            <div className=\"config-panel\">\n              {/* Dimensions */}\n              <div className=\"config-card\">\n                <div className=\"card-header\">\n                  <h4>Dimensions</h4>\n                </div>\n                <div className=\"dimension-controls-enhanced\">\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Width</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, width: Math.max(productType === 'chair' ? 50 : 100, dimensions.width - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.width}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, width: Math.min(productType === 'chair' ? 80 : 400, dimensions.width + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 50 : 100}\n                      max={productType === 'chair' ? 80 : 400}\n                      value={dimensions.width}\n                      onChange={(e) => setDimensions({...dimensions, width: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Depth</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, depth: Math.max(productType === 'chair' ? 50 : 60, dimensions.depth - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.depth}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, depth: Math.min(productType === 'chair' ? 80 : 200, dimensions.depth + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 50 : 60}\n                      max={productType === 'chair' ? 80 : 200}\n                      value={dimensions.depth}\n                      onChange={(e) => setDimensions({...dimensions, depth: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n\n                  <div className=\"dimension-group-enhanced\">\n                    <div className=\"dimension-header\">\n                      <label className=\"dimension-label\">Height</label>\n                      <div className=\"dimension-value-controls\">\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, height: Math.max(productType === 'chair' ? 70 : 60, dimensions.height - 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                        <div className=\"dimension-display\">\n                          <span className=\"dimension-value\">{dimensions.height}</span>\n                          <span className=\"dimension-unit\">cm</span>\n                        </div>\n                        <button\n                          className=\"dimension-btn\"\n                          onClick={() => setDimensions({...dimensions, height: Math.min(productType === 'chair' ? 120 : 120, dimensions.height + 10)})}\n                        >\n                          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min={productType === 'chair' ? 70 : 60}\n                      max={productType === 'chair' ? 120 : 120}\n                      value={dimensions.height}\n                      onChange={(e) => setDimensions({...dimensions, height: parseInt(e.target.value)})}\n                      className=\"dimension-slider-enhanced\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Colors and Materials */}\n              <div className=\"config-card\">\n                <div className=\"card-header\">\n                  <div className=\"card-icon\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                      <path d=\"M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z\" fill=\"#F0B21B\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title\">\n                    <h4>Colors & Materials</h4>\n                    <p>{isModelLoaded ? 'GLB model materials are preserved' : 'Choose colors and surface materials'}</p>\n                  </div>\n                </div>\n\n                {modelError && (\n                  <div className=\"model-error-notice\">\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#ffc107\" strokeWidth=\"2\"/>\n                      <path d=\"M12 8v4M12 16h.01\" stroke=\"#ffc107\" strokeWidth=\"2\"/>\n                    </svg>\n                    {modelError}\n                  </div>\n                )}\n                <div className=\"color-material-grid-enhanced\">\n                  <div className=\"color-section-enhanced\">\n                    <div className=\"section-header\">\n                      <h5>Primary Color</h5>\n                      <div className=\"color-info\">\n                        <span className=\"current-color-name\">\n                          {colors.primary === '#6B7280' ? 'Slate Gray' :\n                           colors.primary === '#374151' ? 'Dark Gray' :\n                           colors.primary === '#1F2937' ? 'Charcoal' :\n                           colors.primary === '#111827' ? 'Black' :\n                           colors.primary === '#F3F4F6' ? 'Light Gray' :\n                           colors.primary === '#E5E7EB' ? 'Silver' :\n                           colors.primary === '#F0B21B' ? 'Golden Yellow' :\n                           colors.primary === '#DC2626' ? 'Red' :\n                           colors.primary === '#059669' ? 'Green' :\n                           colors.primary === '#2563EB' ? 'Blue' :\n                           colors.primary === '#7C3AED' ? 'Purple' :\n                           colors.primary === '#EA580C' ? 'Orange' : 'Custom'}\n                        </span>\n                        <div\n                          className=\"current-color-preview\"\n                          style={{ backgroundColor: colors.primary }}\n                        ></div>\n                      </div>\n                    </div>\n                    <div className=\"color-palette-enhanced\">\n                      <div className=\"color-group\">\n                        <span className=\"color-group-label\">Neutrals</span>\n                        <div className=\"color-row\">\n                          {['#6B7280', '#374151', '#1F2937', '#111827', '#F3F4F6', '#E5E7EB'].map((color) => (\n                            <button\n                              key={color}\n                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => setColors({...colors, primary: color})}\n                              title={color === '#6B7280' ? 'Slate Gray' :\n                                     color === '#374151' ? 'Dark Gray' :\n                                     color === '#1F2937' ? 'Charcoal' :\n                                     color === '#111827' ? 'Black' :\n                                     color === '#F3F4F6' ? 'Light Gray' : 'Silver'}\n                            />\n                          ))}\n                        </div>\n                      </div>\n                      <div className=\"color-group\">\n                        <span className=\"color-group-label\">Accent Colors</span>\n                        <div className=\"color-row\">\n                          {['#F0B21B', '#DC2626', '#059669', '#2563EB', '#7C3AED', '#EA580C'].map((color) => (\n                            <button\n                              key={color}\n                              className={`color-swatch-enhanced ${colors.primary === color ? 'active' : ''}`}\n                              style={{ backgroundColor: color }}\n                              onClick={() => setColors({...colors, primary: color})}\n                              title={color === '#F0B21B' ? 'Golden Yellow' :\n                                     color === '#DC2626' ? 'Red' :\n                                     color === '#059669' ? 'Green' :\n                                     color === '#2563EB' ? 'Blue' :\n                                     color === '#7C3AED' ? 'Purple' : 'Orange'}\n                            />\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"material-section-enhanced\">\n                    <div className=\"section-header\">\n                      <h5>Material & Finish</h5>\n                      <span className=\"current-material-name\">\n                        {material.charAt(0).toUpperCase() + material.slice(1)}\n                      </span>\n                    </div>\n                    <div className=\"material-options-enhanced\">\n                      {(productType === 'chair' ? [\n                        { name: 'Mesh', value: 'mesh', icon: '🕸️', desc: 'Breathable mesh fabric' },\n                        { name: 'Fabric', value: 'fabric', icon: '🧵', desc: 'Soft upholstery fabric' },\n                        { name: 'Leather', value: 'leather', icon: '🐄', desc: 'Premium leather finish' }\n                      ] : [\n                        { name: 'Wood', value: 'wood', icon: '🌳', desc: 'Natural wood grain' },\n                        { name: 'Metal', value: 'metal', icon: '⚙️', desc: 'Brushed metal finish' },\n                        { name: 'Glass', value: 'glass', icon: '💎', desc: 'Tempered glass surface' }\n                      ]).map((mat) => (\n                        <button\n                          key={mat.value}\n                          className={`material-option-enhanced ${material === mat.value ? 'active' : ''}`}\n                          onClick={() => setMaterial(mat.value)}\n                          title={mat.desc}\n                        >\n                          <div className=\"material-icon\">{mat.icon}</div>\n                          <div className=\"material-info\">\n                            <span className=\"material-name\">{mat.name}</span>\n                            <span className=\"material-desc\">{mat.desc}</span>\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Modern Order Summary */}\n              <div className=\"config-card pricing-card-modern\">\n                <div className=\"card-header-modern\">\n                  <div className=\"card-icon-modern\">\n                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2M6.4 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm11.2 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4z\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                  </div>\n                  <div className=\"card-title-modern\">\n                    <h4>Order Summary</h4>\n                    <p>Configure and add to cart</p>\n                  </div>\n                </div>\n\n                <div className=\"order-content-modern\">\n                  {/* Product Summary */}\n                  <div className=\"product-summary-modern\">\n                    <div className=\"product-info-row\">\n                      <div className=\"product-icon\">\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <rect x=\"3\" y=\"4\" width=\"18\" height=\"12\" rx=\"2\" stroke=\"#F0B21B\" strokeWidth=\"1.5\" fill=\"none\"/>\n                          <path d=\"M7 8h10M7 12h6\" stroke=\"#F0B21B\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </div>\n                      <div className=\"product-details\">\n                        <span className=\"product-name\">Custom {productType.charAt(0).toUpperCase() + productType.slice(1)}</span>\n                        <span className=\"product-specs\">{dimensions.width}×{dimensions.depth}×{dimensions.height}cm</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Quantity Controls */}\n                  <div className=\"quantity-section-modern\">\n                    <div className=\"section-label\">\n                      <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12l2 2 4-4\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"#F0B21B\" strokeWidth=\"1.5\"/>\n                      </svg>\n                      <span>Quantity</span>\n                    </div>\n                    <div className=\"quantity-controls-modern\">\n                      <button\n                        onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                        className=\"quantity-btn-modern\"\n                        disabled={quantity <= 1}\n                      >\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </button>\n                      <div className=\"quantity-display-modern\">\n                        <span className=\"quantity-number-modern\">{quantity}</span>\n                      </div>\n                      <button\n                        onClick={() => setQuantity(quantity + 1)}\n                        className=\"quantity-btn-modern\"\n                      >\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 5v14M5 12h14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Price Breakdown */}\n                  <div className=\"price-breakdown-modern\">\n                    <div className=\"price-row-modern\">\n                      <div className=\"price-label-modern\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <path d=\"M12 6v6l4 2\" stroke=\"#64748b\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                        </svg>\n                        <span>Base Price</span>\n                      </div>\n                      <span className=\"price-amount\">${getBasePrice().toFixed(2)}</span>\n                    </div>\n                    <div className=\"price-row-modern\">\n                      <div className=\"price-label-modern\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <polyline points=\"3.27,6.96 12,12.01 20.73,6.96\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                          <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\" stroke=\"#64748b\" strokeWidth=\"1.5\"/>\n                        </svg>\n                        <span>Material & Size</span>\n                      </div>\n                      <span className=\"price-amount\">+${(getCurrentPrice() / quantity - getBasePrice()).toFixed(2)}</span>\n                    </div>\n                    <div className=\"price-divider\"></div>\n                    <div className=\"price-row-modern total-row\">\n                      <div className=\"price-label-modern total-label\">\n                        <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M12 2L2 7l10 5 10-5-10-5z\" fill=\"#F0B21B\"/>\n                          <path d=\"M2 17l10 5 10-5\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                          <path d=\"M2 12l10 5 10-5\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                        </svg>\n                        <span>Total ({quantity} item{quantity > 1 ? 's' : ''})</span>\n                      </div>\n                      <span className=\"price-amount-total\">${getCurrentPrice().toFixed(2)}</span>\n                    </div>\n                  </div>\n\n                  {/* Add to Cart Button */}\n                  <button className=\"add-to-cart-btn-modern\" onClick={handleAddToCart}>\n                    <div className=\"btn-content-modern\">\n                      <div className=\"btn-icon-modern\">\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M3 3h2l.4 2m0 0h13.2a1 1 0 0 1 .98 1.2l-1.6 8a1 1 0 0 1-.98.8H6.4m0-12L4.4 5M6.4 15l-1.4-7m1.4 7h11.2\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"9\" cy=\"20\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        <circle cx=\"20\" cy=\"20\" r=\"1\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        </svg>\n                      </div>\n                      <div className=\"btn-text-modern\">\n                        <span className=\"btn-action-modern\">Add to Cart</span>\n                        <span className=\"btn-price-modern\">${getCurrentPrice().toFixed(2)}</span>\n                      </div>\n                    </div>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Advanced3DConfigurator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,OAAO,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtD;EACA,MAAMC,QAAQ,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMW,QAAQ,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMY,WAAW,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMa,UAAU,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMc,cAAc,GAAGd,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMe,SAAS,GAAGf,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACR,OAAO,EAAE,OAAO,OAAO;IAC5B,MAAMS,IAAI,GAAGT,OAAO,CAACS,IAAI,CAACC,WAAW,CAAC,CAAC;IACvC,IAAID,IAAI,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS;IAC1E,IAAIF,IAAI,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAIF,IAAI,CAACE,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,aAAa;IACtD,OAAO,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,MAAMC,WAAW,GAAGJ,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC;IAC3CwB,KAAK,EAAEH,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG;IACzCI,KAAK,EAAEJ,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG;IACzCK,MAAM,EAAEL,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG;EACzC,CAAC,CAAC;EAEF,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC;IACnC6B,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAACqB,WAAW,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;EACnF,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMwC,0BAA0B,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAC/C,MAAMwC,YAAY,GAAGxC,MAAM,CAAC,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACyC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM4C,iBAAiB,GAAG3C,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAE/CE,SAAS,CAAC,MAAM;IACd,MAAM6C,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG,IAAI,gEAAgE,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;MACrIP,WAAW,CAACE,MAAM,CAAC;IACrB,CAAC;IAEDD,WAAW,CAAC,CAAC;IACbE,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEP,WAAW,CAAC;IAC9C,OAAO,MAAME,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAER,WAAW,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjBC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAE,GAAG;MACZC,KAAK,EAAE,GAAG;MACVC,WAAW,EAAE;IACf,CAAC;IACD,OAAOL,UAAU,CAACpC,WAAW,CAAC,IAAI,GAAG;EACvC,CAAC;EAED,MAAM0C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,KAAK,GAAGR,YAAY,CAAC,CAAC;;IAE1B;IACA,MAAMS,cAAc,GAAI3C,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACG,KAAK,GAAGH,UAAU,CAACI,MAAM,GAAI,MAAM;IACzFsC,KAAK,IAAIE,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEH,cAAc,CAAC,CAAC;;IAErD;IACA,MAAMI,mBAAmB,GAAG;MAC1BC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE,GAAG;MACXC,KAAK,EAAE;IACT,CAAC;IACDb,KAAK,IAAIK,mBAAmB,CAACrC,QAAQ,CAAC,IAAI,GAAG;IAE7C,OAAOgC,KAAK,GAAG9B,QAAQ;EACzB,CAAC;EAED,MAAM4C,eAAe,GAAGA,CAAA,KAAMf,cAAc,CAAC,CAAC;;EAE9C;EACA,MAAMgB,YAAY,GAAG,MAAAA,CAAOC,KAAK,EAAEC,SAAS,KAAK;IAC/C,MAAMC,MAAM,GAAG,IAAI9E,UAAU,CAAC,CAAC;IAE/B,IAAI;MACF+E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,SAAS,CAAC;MACvD1C,aAAa,CAAC,EAAE,CAAC;MACjB,MAAM8C,IAAI,GAAG,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QAClDN,MAAM,CAACO,IAAI,CACTR,SAAS,EACRI,IAAI,IAAK;UACRF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEC,IAAI,CAAC;UAClDE,OAAO,CAACF,IAAI,CAAC;QACf,CAAC,EACAK,QAAQ,IAAK;UACZP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAGM,QAAQ,CAACC,MAAM,GAAGD,QAAQ,CAACE,KAAK,GAAG,GAAG,GAAI,GAAG,CAAC;QAClF,CAAC,EACAC,KAAK,IAAK;UACTV,OAAO,CAACU,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CL,MAAM,CAACK,KAAK,CAAC;QACf,CACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMC,KAAK,GAAGT,IAAI,CAACL,KAAK;;MAExB;MACA,MAAMe,GAAG,GAAG,IAAI5F,KAAK,CAAC6F,IAAI,CAAC,CAAC,CAACC,aAAa,CAACH,KAAK,CAAC;MACjD,MAAMI,MAAM,GAAGH,GAAG,CAACI,SAAS,CAAC,IAAIhG,KAAK,CAACiG,OAAO,CAAC,CAAC,CAAC;MACjD,MAAMC,IAAI,GAAGN,GAAG,CAACO,OAAO,CAAC,IAAInG,KAAK,CAACiG,OAAO,CAAC,CAAC,CAAC;;MAE7C;MACA5D,0BAA0B,CAAC+D,OAAO,GAAG;QACnC/E,KAAK,EAAE6E,IAAI,CAACG,CAAC;QACb9E,MAAM,EAAE2E,IAAI,CAACI,CAAC;QACdhF,KAAK,EAAE4E,IAAI,CAACK;MACd,CAAC;;MAED;MACAZ,KAAK,CAACa,QAAQ,CAACC,GAAG,CAACV,MAAM,CAAC;;MAE1B;MACA,MAAMW,YAAY,GAAG3C,IAAI,CAACC,GAAG,CAACkC,IAAI,CAACG,CAAC,EAAEH,IAAI,CAACI,CAAC,EAAEJ,IAAI,CAACK,CAAC,CAAC;MACrD,IAAIG,YAAY,GAAG,CAAC,EAAE;QACpB,MAAMC,KAAK,GAAG,CAAC,GAAGD,YAAY;QAC9Bf,KAAK,CAACgB,KAAK,CAACC,SAAS,CAACD,KAAK,CAAC;QAC5BrE,YAAY,CAAC8D,OAAO,GAAGO,KAAK;MAC9B,CAAC,MAAM;QACLrE,YAAY,CAAC8D,OAAO,GAAG,CAAC;MAC1B;;MAEA;MACA,IAAIlF,WAAW,KAAK,OAAO,EAAE;QAC3B2F,qBAAqB,CAAClB,KAAK,CAAC;MAC9B;;MAEA;MACAA,KAAK,CAACmB,QAAQ,CAAEC,KAAK,IAAK;QACxB,IAAIA,KAAK,CAACC,MAAM,EAAE;UAChBD,KAAK,CAACE,UAAU,GAAG,IAAI;UACvBF,KAAK,CAACG,aAAa,GAAG,IAAI;;UAE1B;UACA,IAAIH,KAAK,CAAClF,QAAQ,EAAE;YAClBkF,KAAK,CAAClF,QAAQ,CAACsF,WAAW,GAAG,IAAI;UACnC;QACF;MACF,CAAC,CAAC;MAEFxG,UAAU,CAACyF,OAAO,GAAGT,KAAK;MAC1Bd,KAAK,CAACuC,GAAG,CAACzB,KAAK,CAAC;MAChBzD,gBAAgB,CAAC,IAAI,CAAC;MAEtB,OAAOyD,KAAK;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDtD,aAAa,CAAC,kDAAkD,CAAC;MACjEF,gBAAgB,CAAC,KAAK,CAAC;;MAEvB;MACAmF,aAAa,CAACxC,KAAK,CAAC;MACpB,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAMyC,iBAAiB,GAAGxH,MAAM,CAAC;IAC/ByH,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMC,SAAS,GAAIC,QAAQ,IAAK;IAC9B,IAAI,CAAClH,SAAS,CAACuF,OAAO,EAAE;IAExB,MAAM4B,QAAQ,GAAGV,iBAAiB,CAAClB,OAAO;IAC1C4B,QAAQ,CAACH,WAAW,GAAG,IAAI;IAE3B,QAAQE,QAAQ;MACd,KAAK,OAAO;QACVC,QAAQ,CAACT,eAAe,GAAG,CAAC;QAC5BS,QAAQ,CAACR,eAAe,GAAG,CAAC;QAC5BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,MAAM;QACTK,QAAQ,CAACT,eAAe,GAAG,CAAC;QAC5BS,QAAQ,CAACR,eAAe,GAAGzD,IAAI,CAACkE,EAAE,GAAG,CAAC;QACtCD,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,KAAK;QACRK,QAAQ,CAACT,eAAe,GAAGxD,IAAI,CAACkE,EAAE,GAAG,CAAC;QACtCD,QAAQ,CAACR,eAAe,GAAG,CAAC;QAC5BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;MACF,KAAK,KAAK;MACV;QACEK,QAAQ,CAACT,eAAe,GAAG,GAAG;QAC9BS,QAAQ,CAACR,eAAe,GAAG,GAAG;QAC9BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;QAC3B;IACJ;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,SAAS,IAAK;IAChC,IAAI,CAACtH,SAAS,CAACuF,OAAO,IAAI,CAACkB,iBAAiB,CAAClB,OAAO,EAAE;IAEtD,MAAM4B,QAAQ,GAAGV,iBAAiB,CAAClB,OAAO;IAC1C,MAAMgC,UAAU,GAAGD,SAAS,GAAG,GAAG;IAClC,MAAME,WAAW,GAAGL,QAAQ,CAACL,cAAc,GAAGS,UAAU;;IAExD;IACAJ,QAAQ,CAACL,cAAc,GAAG5D,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEoE,WAAW,CAAC,CAAC;;IAElE;IACAL,QAAQ,CAACH,WAAW,GAAG,IAAI;IAC3BS,UAAU,CAAC,MAAM;MACf,IAAIN,QAAQ,EAAEA,QAAQ,CAACH,WAAW,GAAG,KAAK;IAC5C,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMU,mBAAmB,GAAIC,KAAK,IAAK;IACrC;IACA,MAAMC,WAAW,GAAG,IAAIzI,KAAK,CAAC0I,WAAW,CACvCvH,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACG,KAAK,GAAG,GACrB,CAAC;IACD,MAAMqH,WAAW,GAAG,IAAI3I,KAAK,CAAC4I,oBAAoB,CAAC;MACjDC,KAAK,EAAErH,MAAM,CAACE,OAAO;MACrBoH,SAAS,EAAEjH,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;MAC3CkH,SAAS,EAAElH,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;MAC3CmH,WAAW,EAAEnH,QAAQ,KAAK,OAAO;MACjCoH,OAAO,EAAEpH,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG;IACxC,CAAC,CAAC;IAEF,MAAMqH,QAAQ,GAAG,IAAIlJ,KAAK,CAACmJ,IAAI,CAACV,WAAW,EAAEE,WAAW,CAAC;IACzDO,QAAQ,CAAC1C,QAAQ,CAACF,CAAC,GAAGnF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI;IACpD,IAAI,CAACmB,QAAQ,EAAE;MACbwG,QAAQ,CAACjC,UAAU,GAAG,IAAI;MAC1BiC,QAAQ,CAAChC,aAAa,GAAG,IAAI;IAC/B;IACAsB,KAAK,CAACpB,GAAG,CAAC8B,QAAQ,CAAC;;IAEnB;IACA,MAAME,WAAW,GAAG1G,QAAQ,GAAG,CAAC,GAAG,EAAE;IACrC,MAAM2G,WAAW,GAAG,IAAIrJ,KAAK,CAACsJ,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAEnI,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,EAAE6H,WAAW,CAAC;IACvG,MAAMG,WAAW,GAAG,IAAIvJ,KAAK,CAAC4I,oBAAoB,CAAC;MACjDC,KAAK,EAAErH,MAAM,CAACE,OAAO;MACrBoH,SAAS,EAAE,GAAG;MACdC,SAAS,EAAElH,QAAQ,KAAK,OAAO,GAAG,GAAG,GAAG;IAC1C,CAAC,CAAC;IAEF,MAAM2H,YAAY,GAAG,CACnB,CAAC,CAACrI,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAACJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACpG,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE,CAACJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACnG,CAAC,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAEJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,EACnG,CAACH,UAAU,CAACE,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,CAACF,UAAU,CAACI,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,EAAEJ,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CACnG;IAEDkI,YAAY,CAACC,OAAO,CAACC,GAAG,IAAI;MAC1B,MAAMC,GAAG,GAAG,IAAI3J,KAAK,CAACmJ,IAAI,CAACE,WAAW,EAAEE,WAAW,CAAC;MACpDI,GAAG,CAACnD,QAAQ,CAACoD,GAAG,CAACF,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;MACxC,IAAI,CAAChH,QAAQ,EAAE;QACbiH,GAAG,CAAC1C,UAAU,GAAG,IAAI;MACvB;MACAuB,KAAK,CAACpB,GAAG,CAACuC,GAAG,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,mBAAmB,GAAIrB,KAAK,IAAK;IACrC;IACA,MAAMsB,YAAY,GAAG,IAAI9J,KAAK,CAAC0I,WAAW,CACxCvH,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACG,KAAK,GAAG,GACrB,CAAC;IACD,MAAMyI,YAAY,GAAG,IAAI/J,KAAK,CAAC4I,oBAAoB,CAAC;MAClDC,KAAK,EAAErH,MAAM,CAACE,OAAO;MACrBoH,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMkB,IAAI,GAAG,IAAIhK,KAAK,CAACmJ,IAAI,CAACW,YAAY,EAAEC,YAAY,CAAC;IACvDC,IAAI,CAACxD,QAAQ,CAACF,CAAC,GAAG,GAAG;IACrB,IAAI,CAAC5D,QAAQ,EAAE;MACbsH,IAAI,CAAC/C,UAAU,GAAG,IAAI;IACxB;IACAuB,KAAK,CAACpB,GAAG,CAAC4C,IAAI,CAAC;;IAEf;IACA,MAAMC,YAAY,GAAG,IAAIjK,KAAK,CAAC0I,WAAW,CACxCvH,UAAU,CAACE,KAAK,GAAG,GAAG,EACtB,IAAI,EACJF,UAAU,CAACI,MAAM,GAAG,GACtB,CAAC;IACD,MAAM2I,YAAY,GAAG,IAAIlK,KAAK,CAAC4I,oBAAoB,CAAC;MAClDC,KAAK,EAAErH,MAAM,CAACE,OAAO;MACrBoH,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMqB,QAAQ,GAAG,IAAInK,KAAK,CAACmJ,IAAI,CAACc,YAAY,EAAEC,YAAY,CAAC;IAC3DC,QAAQ,CAAC3D,QAAQ,CAACoD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAACzI,UAAU,CAACG,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;IAC7D,IAAI,CAACoB,QAAQ,EAAE;MACbyH,QAAQ,CAAClD,UAAU,GAAG,IAAI;IAC5B;IACAuB,KAAK,CAACpB,GAAG,CAAC+C,QAAQ,CAAC;;IAEnB;IACA,MAAMC,YAAY,GAAG1H,QAAQ,GAAG,CAAC,GAAG,CAAC;IACrC,MAAM2H,YAAY,GAAG,IAAIrK,KAAK,CAACsJ,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAEc,YAAY,CAAC;IAC7E,MAAME,YAAY,GAAG,IAAItK,KAAK,CAAC4I,oBAAoB,CAAC;MAClDC,KAAK,EAAErH,MAAM,CAACE,OAAO;MACrBoH,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMyB,IAAI,GAAG,IAAIvK,KAAK,CAACmJ,IAAI,CAACkB,YAAY,EAAEC,YAAY,CAAC;IACvDC,IAAI,CAAC/D,QAAQ,CAACF,CAAC,GAAG,IAAI;IACtB,IAAI,CAAC5D,QAAQ,EAAE;MACb6H,IAAI,CAACtD,UAAU,GAAG,IAAI;IACxB;IACAuB,KAAK,CAACpB,GAAG,CAACmD,IAAI,CAAC;EACjB,CAAC;EAED,MAAMlD,aAAa,GAAG,MAAOxC,KAAK,IAAK;IACrC;IACA,IAAIvE,OAAO,IAAIA,OAAO,CAACkK,OAAO,EAAE;MAC9BxF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE3E,OAAO,CAACkK,OAAO,CAAC;MAClDxF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE3E,OAAO,CAACS,IAAI,CAAC;MAC1CiE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE/D,WAAW,CAAC;MAClD,MAAMyE,KAAK,GAAG,MAAMf,YAAY,CAACC,KAAK,EAAEvE,OAAO,CAACkK,OAAO,CAAC;MACxD,IAAI7E,KAAK,EAAE;QACTX,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,OAAO,CAAC;MACV;IACF;;IAEA;IACA,MAAMwF,YAAY,GAAG,IAAIzK,KAAK,CAAC0K,KAAK,CAAC,CAAC;IAEtC,QAAQxJ,WAAW;MACjB,KAAK,OAAO;QACVqH,mBAAmB,CAACkC,YAAY,CAAC;QACjC;MACF,KAAK,OAAO;QACVZ,mBAAmB,CAACY,YAAY,CAAC;QACjC;MACF;QACElC,mBAAmB,CAACkC,YAAY,CAAC;IACrC;IAEA9J,UAAU,CAACyF,OAAO,GAAGqE,YAAY;IACjC5F,KAAK,CAACuC,GAAG,CAACqD,YAAY,CAAC;EACzB,CAAC;;EAED;EACA,MAAM5D,qBAAqB,GAAGA,CAAClB,KAAK,GAAGhF,UAAU,CAACyF,OAAO,KAAK;IAC5D,IAAI,CAACT,KAAK,IAAI,CAACtD,0BAA0B,CAAC+D,OAAO,IAAIlF,WAAW,KAAK,OAAO,EAAE;IAE9E,MAAMyJ,SAAS,GAAGrI,YAAY,CAAC8D,OAAO;;IAEtC;IACA5D,0BAA0B,CAAC,IAAI,CAAC;;IAEhC;IACA,IAAIC,iBAAiB,CAAC2D,OAAO,EAAE;MAC7BwE,YAAY,CAACnI,iBAAiB,CAAC2D,OAAO,CAAC;IACzC;;IAEA;IACA3D,iBAAiB,CAAC2D,OAAO,GAAGkC,UAAU,CAAC,MAAM;MAC3C9F,0BAA0B,CAAC,KAAK,CAAC;IACnC,CAAC,EAAE,IAAI,CAAC;;IAER;IACA;IACA,MAAMqI,iBAAiB,GAAG;MACxBxJ,KAAK,EAAE,EAAE;MACTE,MAAM,EAAE,EAAE;MACVD,KAAK,EAAE;IACT,CAAC;;IAED;IACA,MAAMwJ,MAAM,GAAI3J,UAAU,CAACE,KAAK,GAAGwJ,iBAAiB,CAACxJ,KAAK,GAAIsJ,SAAS;IACvE,MAAMI,MAAM,GAAI5J,UAAU,CAACI,MAAM,GAAGsJ,iBAAiB,CAACtJ,MAAM,GAAIoJ,SAAS;IACzE,MAAMK,MAAM,GAAI7J,UAAU,CAACG,KAAK,GAAGuJ,iBAAiB,CAACvJ,KAAK,GAAIqJ,SAAS;;IAEvE;IACA;IACAhF,KAAK,CAACgB,KAAK,CAACiD,GAAG,CAACkB,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC;;IAEvC;IACA,MAAMpF,GAAG,GAAG,IAAI5F,KAAK,CAAC6F,IAAI,CAAC,CAAC,CAACC,aAAa,CAACH,KAAK,CAAC;IACjD,MAAMI,MAAM,GAAGH,GAAG,CAACI,SAAS,CAAC,IAAIhG,KAAK,CAACiG,OAAO,CAAC,CAAC,CAAC;IACjDN,KAAK,CAACa,QAAQ,CAACC,GAAG,CAACV,MAAM,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkF,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACtK,UAAU,CAACyF,OAAO,IAAI,CAAC3F,QAAQ,CAAC2F,OAAO,EAAE;;IAE9C;IACA,IAAInE,aAAa,IAAIf,WAAW,KAAK,OAAO,EAAE;MAC5C2F,qBAAqB,CAAC,CAAC;MACvB;IACF;;IAEA;IACApG,QAAQ,CAAC2F,OAAO,CAAC8E,MAAM,CAACvK,UAAU,CAACyF,OAAO,CAAC;IAC3CiB,aAAa,CAAC5G,QAAQ,CAAC2F,OAAO,CAAC;EACjC,CAAC;;EAED;EACArG,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,QAAQ,CAAC4F,OAAO,EAAE;;IAEvB;IACA,MAAMvB,KAAK,GAAG,IAAI7E,KAAK,CAACmL,KAAK,CAAC,CAAC;IAC/BtG,KAAK,CAACuG,UAAU,GAAG,IAAIpL,KAAK,CAACqL,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9C5K,QAAQ,CAAC2F,OAAO,GAAGvB,KAAK;;IAExB;IACA,MAAMyG,MAAM,GAAG,IAAItL,KAAK,CAACuL,iBAAiB,CACxC,EAAE,EACF/K,QAAQ,CAAC4F,OAAO,CAACoF,WAAW,GAAGhL,QAAQ,CAAC4F,OAAO,CAACqF,YAAY,EAC5D,GAAG,EACH,IACF,CAAC;IACDH,MAAM,CAAC9E,QAAQ,CAACoD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B0B,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB7K,SAAS,CAACuF,OAAO,GAAGkF,MAAM;;IAE1B;IACA,MAAMK,QAAQ,GAAG,IAAI3L,KAAK,CAAC4L,aAAa,CAAC;MACvCC,SAAS,EAAE,CAACnJ,QAAQ;MACpBoJ,KAAK,EAAE,IAAI;MACXC,eAAe,EAAErJ,QAAQ,GAAG,WAAW,GAAG;IAC5C,CAAC,CAAC;IACFiJ,QAAQ,CAACK,OAAO,CAACxL,QAAQ,CAAC4F,OAAO,CAACoF,WAAW,EAAEhL,QAAQ,CAAC4F,OAAO,CAACqF,YAAY,CAAC;IAC7EE,QAAQ,CAACM,aAAa,CAAClI,IAAI,CAACE,GAAG,CAACnB,MAAM,CAACoJ,gBAAgB,EAAExJ,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC7EiJ,QAAQ,CAACQ,aAAa,CAAC,QAAQ,EAAE,GAAG,CAAC;IACrCR,QAAQ,CAACS,SAAS,CAACC,OAAO,GAAG,CAAC3J,QAAQ;IACtC,IAAI,CAACA,QAAQ,EAAE;MACbiJ,QAAQ,CAACS,SAAS,CAACE,IAAI,GAAGtM,KAAK,CAACuM,gBAAgB;IAClD;IACA/L,QAAQ,CAAC4F,OAAO,CAACoG,WAAW,CAACb,QAAQ,CAACc,UAAU,CAAC;IACjD/L,WAAW,CAAC0F,OAAO,GAAGuF,QAAQ;;IAE9B;IACA,MAAMe,YAAY,GAAG,IAAI1M,KAAK,CAAC2M,YAAY,CAAC,QAAQ,EAAEjK,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;IAC3EmC,KAAK,CAACuC,GAAG,CAACsF,YAAY,CAAC;IAEvB,MAAME,gBAAgB,GAAG,IAAI5M,KAAK,CAAC6M,gBAAgB,CAAC,QAAQ,EAAEnK,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC;IACnFkK,gBAAgB,CAACpG,QAAQ,CAACoD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtC,IAAI,CAAClH,QAAQ,EAAE;MACbkK,gBAAgB,CAAC3F,UAAU,GAAG,IAAI;MAClC2F,gBAAgB,CAACE,MAAM,CAACC,OAAO,CAAC1L,KAAK,GAAG,IAAI;MAC5CuL,gBAAgB,CAACE,MAAM,CAACC,OAAO,CAACxL,MAAM,GAAG,IAAI;IAC/C;IACAsD,KAAK,CAACuC,GAAG,CAACwF,gBAAgB,CAAC;;IAE3B;IACA,IAAII,WAAW,GAAG,KAAK;IACvB,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;;IAEd;IACA,MAAMlF,QAAQ,GAAGV,iBAAiB,CAAClB,OAAO;;IAE1C;IACA,MAAM+G,WAAW,GAAIC,KAAK,IAAK;MAC7BJ,WAAW,GAAG,IAAI;MAClBC,MAAM,GAAGG,KAAK,CAACC,OAAO;MACtBH,MAAM,GAAGE,KAAK,CAACE,OAAO;IACxB,CAAC;IAED,MAAMC,WAAW,GAAIH,KAAK,IAAK;MAC7B,IAAI,CAACJ,WAAW,EAAE;MAClB,MAAMQ,MAAM,GAAGJ,KAAK,CAACC,OAAO,GAAGJ,MAAM;MACrC,MAAMQ,MAAM,GAAGL,KAAK,CAACE,OAAO,GAAGJ,MAAM;;MAErC;MACAlF,QAAQ,CAACR,eAAe,IAAIgG,MAAM,GAAG,KAAK;MAC1CxF,QAAQ,CAACT,eAAe,IAAIkG,MAAM,GAAG,KAAK;MAE1CzF,QAAQ,CAACT,eAAe,GAAGxD,IAAI,CAACC,GAAG,CAAC,CAACD,IAAI,CAACkE,EAAE,GAAG,CAAC,EAAElE,IAAI,CAACE,GAAG,CAACF,IAAI,CAACkE,EAAE,GAAG,CAAC,EAAED,QAAQ,CAACT,eAAe,CAAC,CAAC;MAElG0F,MAAM,GAAGG,KAAK,CAACC,OAAO;MACtBH,MAAM,GAAGE,KAAK,CAACE,OAAO;IACxB,CAAC;IAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;MACtBV,WAAW,GAAG,KAAK;IACrB,CAAC;IAED,MAAMW,OAAO,GAAIP,KAAK,IAAK;MACzBA,KAAK,CAACQ,cAAc,CAAC,CAAC;MACtB,MAAMC,SAAS,GAAGnL,QAAQ,GAAG,KAAK,GAAG,IAAI;MACzCsF,QAAQ,CAACL,cAAc,IAAIyF,KAAK,CAACK,MAAM,GAAGI,SAAS;MACnD7F,QAAQ,CAACL,cAAc,GAAG5D,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE+D,QAAQ,CAACL,cAAc,CAAC,CAAC;IAChF,CAAC;;IAED;IACA,IAAImG,iBAAiB,GAAG,CAAC;IACzB,IAAIC,oBAAoB,GAAG,CAAC;IAE5B,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;MACpC,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;MAChC,MAAMC,EAAE,GAAGF,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO,GAAGY,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;MAClD,MAAMe,EAAE,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO,GAAGW,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MAClD,OAAOvJ,IAAI,CAACsK,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IACrC,CAAC;IAED,MAAME,YAAY,GAAIlB,KAAK,IAAK;MAC9B,IAAIA,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9BlB,WAAW,GAAG,IAAI;QAClBC,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC,CAAC,MAAM,IAAIF,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrClB,WAAW,GAAG,KAAK;QACnBe,oBAAoB,GAAGC,gBAAgB,CAACZ,KAAK,CAACa,OAAO,CAAC;QACtDH,iBAAiB,GAAGC,oBAAoB;MAC1C;IACF,CAAC;IAED,MAAMQ,WAAW,GAAInB,KAAK,IAAK;MAC7BA,KAAK,CAACQ,cAAc,CAAC,CAAC;MAEtB,IAAIR,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,IAAIlB,WAAW,EAAE;QAC7C;QACA,MAAMQ,MAAM,GAAGJ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO,GAAGJ,MAAM;QAChD,MAAMQ,MAAM,GAAGL,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO,GAAGJ,MAAM;QAEhDlF,QAAQ,CAACR,eAAe,IAAIgG,MAAM,GAAG,KAAK;QAC1CxF,QAAQ,CAACT,eAAe,IAAIkG,MAAM,GAAG,KAAK;QAE1CzF,QAAQ,CAACT,eAAe,GAAGxD,IAAI,CAACC,GAAG,CAAC,CAACD,IAAI,CAACkE,EAAE,GAAG,CAAC,EAAElE,IAAI,CAACE,GAAG,CAACF,IAAI,CAACkE,EAAE,GAAG,CAAC,EAAED,QAAQ,CAACT,eAAe,CAAC,CAAC;QAElG0F,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC,CAAC,MAAM,IAAIF,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrC;QACA,MAAMtG,eAAe,GAAGoG,gBAAgB,CAACZ,KAAK,CAACa,OAAO,CAAC;QACvD,MAAMO,aAAa,GAAG5G,eAAe,GAAGkG,iBAAiB;QAEzD,IAAI/J,IAAI,CAAC0K,GAAG,CAACD,aAAa,CAAC,GAAG,CAAC,EAAE;UAAE;UACjCxG,QAAQ,CAACL,cAAc,IAAI6G,aAAa,GAAG,KAAK;UAChDxG,QAAQ,CAACL,cAAc,GAAG5D,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE+D,QAAQ,CAACL,cAAc,CAAC,CAAC;UAC9EmG,iBAAiB,GAAGlG,eAAe;QACrC;MACF;IACF,CAAC;IAED,MAAM8G,UAAU,GAAItB,KAAK,IAAK;MAC5B,IAAIA,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC9BlB,WAAW,GAAG,KAAK;MACrB,CAAC,MAAM,IAAII,KAAK,CAACa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACrClB,WAAW,GAAG,IAAI;QAClBC,MAAM,GAAGG,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACZ,OAAO;QACjCH,MAAM,GAAGE,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,CAACX,OAAO;MACnC;IACF,CAAC;;IAED;IACA,MAAMqB,MAAM,GAAGhD,QAAQ,CAACc,UAAU;;IAElC;IACAkC,MAAM,CAACxL,gBAAgB,CAAC,WAAW,EAAEgK,WAAW,CAAC;IACjDyB,QAAQ,CAACzL,gBAAgB,CAAC,WAAW,EAAEoK,WAAW,CAAC,CAAC,CAAC;IACrDqB,QAAQ,CAACzL,gBAAgB,CAAC,SAAS,EAAEuK,SAAS,CAAC,CAAC,CAAC;IACjDiB,MAAM,CAACxL,gBAAgB,CAAC,OAAO,EAAEwK,OAAO,EAAE;MAAEkB,OAAO,EAAE;IAAM,CAAC,CAAC;;IAE7D;IACAF,MAAM,CAACxL,gBAAgB,CAAC,YAAY,EAAEmL,YAAY,EAAE;MAAEO,OAAO,EAAE;IAAM,CAAC,CAAC;IACvEF,MAAM,CAACxL,gBAAgB,CAAC,WAAW,EAAEoL,WAAW,EAAE;MAAEM,OAAO,EAAE;IAAM,CAAC,CAAC;IACrEF,MAAM,CAACxL,gBAAgB,CAAC,UAAU,EAAEuL,UAAU,EAAE;MAAEG,OAAO,EAAE;IAAM,CAAC,CAAC;;IAEnE;IACAF,MAAM,CAACxL,gBAAgB,CAAC,aAAa,EAAG2L,CAAC,IAAKA,CAAC,CAAClB,cAAc,CAAC,CAAC,CAAC;;IAEjE;IACAe,MAAM,CAACI,KAAK,CAACC,WAAW,GAAG,MAAM;IACjCL,MAAM,CAACI,KAAK,CAACE,UAAU,GAAG,MAAM;;IAEhC;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,UAAU,GAAGnH,QAAQ,CAACH,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAACP,gBAAgB,IAAI,CAACO,QAAQ,CAACT,eAAe,GAAGS,QAAQ,CAACP,gBAAgB,IAAI0H,UAAU;MAChGnH,QAAQ,CAACN,gBAAgB,IAAI,CAACM,QAAQ,CAACR,eAAe,GAAGQ,QAAQ,CAACN,gBAAgB,IAAIyH,UAAU;MAChGnH,QAAQ,CAACJ,eAAe,IAAI,CAACI,QAAQ,CAACL,cAAc,GAAGK,QAAQ,CAACJ,eAAe,IAAIuH,UAAU;;MAE7F;MACA,MAAM9I,CAAC,GAAGtC,IAAI,CAACqL,GAAG,CAACpH,QAAQ,CAACN,gBAAgB,CAAC,GAAG3D,IAAI,CAACsL,GAAG,CAACrH,QAAQ,CAACP,gBAAgB,CAAC,GAAGO,QAAQ,CAACJ,eAAe;MAC9G,MAAMtB,CAAC,GAAGvC,IAAI,CAACqL,GAAG,CAACpH,QAAQ,CAACP,gBAAgB,CAAC,GAAGO,QAAQ,CAACJ,eAAe,GAAG,CAAC;MAC5E,MAAMrB,CAAC,GAAGxC,IAAI,CAACsL,GAAG,CAACrH,QAAQ,CAACN,gBAAgB,CAAC,GAAG3D,IAAI,CAACsL,GAAG,CAACrH,QAAQ,CAACP,gBAAgB,CAAC,GAAGO,QAAQ,CAACJ,eAAe;MAE9G0D,MAAM,CAAC9E,QAAQ,CAACoD,GAAG,CAACvD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAC5B+E,MAAM,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA,IAAI1D,QAAQ,CAACH,WAAW,EAAE;QACxB,MAAMyH,QAAQ,GAAGvL,IAAI,CAAC0K,GAAG,CAACzG,QAAQ,CAACT,eAAe,GAAGS,QAAQ,CAACP,gBAAgB,CAAC;QAC/E,MAAM8H,QAAQ,GAAGxL,IAAI,CAAC0K,GAAG,CAACzG,QAAQ,CAACR,eAAe,GAAGQ,QAAQ,CAACN,gBAAgB,CAAC;QAC/E,MAAM8H,QAAQ,GAAGzL,IAAI,CAAC0K,GAAG,CAACzG,QAAQ,CAACL,cAAc,GAAGK,QAAQ,CAACJ,eAAe,CAAC;QAE7E,IAAI0H,QAAQ,GAAG,IAAI,IAAIC,QAAQ,GAAG,IAAI,IAAIC,QAAQ,GAAG,GAAG,EAAE;UACxDxH,QAAQ,CAACH,WAAW,GAAG,KAAK;QAC9B;MACF;IACF,CAAC;;IAED;IACAG,QAAQ,CAACT,eAAe,GAAG,GAAG;IAC9BS,QAAQ,CAACR,eAAe,GAAG,GAAG;IAC9BQ,QAAQ,CAACL,cAAc,GAAG,CAAC;IAC3BK,QAAQ,CAACP,gBAAgB,GAAG,GAAG;IAC/BO,QAAQ,CAACN,gBAAgB,GAAG,GAAG;IAC/BM,QAAQ,CAACJ,eAAe,GAAG,CAAC;;IAE5B;IACAP,aAAa,CAACxC,KAAK,CAAC;;IAEpB;IACA,MAAM4K,OAAO,GAAGA,CAAA,KAAM;MACpB7O,cAAc,CAACwF,OAAO,GAAGsJ,qBAAqB,CAACD,OAAO,CAAC;MACvDP,YAAY,CAAC,CAAC;MACdvD,QAAQ,CAACgE,MAAM,CAAC9K,KAAK,EAAEyG,MAAM,CAAC;IAChC,CAAC;IACDmE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACpP,QAAQ,CAAC4F,OAAO,EAAE;MACvB,MAAM/E,KAAK,GAAGb,QAAQ,CAAC4F,OAAO,CAACoF,WAAW;MAC1C,MAAMjK,MAAM,GAAGf,QAAQ,CAAC4F,OAAO,CAACqF,YAAY;MAC5CH,MAAM,CAACuE,MAAM,GAAGxO,KAAK,GAAGE,MAAM;MAC9B+J,MAAM,CAACwE,sBAAsB,CAAC,CAAC;MAC/BnE,QAAQ,CAACK,OAAO,CAAC3K,KAAK,EAAEE,MAAM,CAAC;IACjC,CAAC;IAEDuB,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEyM,YAAY,CAAC;IAE/C,OAAO,MAAM;MACX9M,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEwM,YAAY,CAAC;;MAElD;MACA,MAAMjB,MAAM,GAAGhD,QAAQ,CAACc,UAAU;MAClCkC,MAAM,CAACvL,mBAAmB,CAAC,WAAW,EAAE+J,WAAW,CAAC;MACpDwB,MAAM,CAACvL,mBAAmB,CAAC,OAAO,EAAEuK,OAAO,CAAC;MAC5CgB,MAAM,CAACvL,mBAAmB,CAAC,YAAY,EAAEkL,YAAY,CAAC;MACtDK,MAAM,CAACvL,mBAAmB,CAAC,WAAW,EAAEmL,WAAW,CAAC;MACpDI,MAAM,CAACvL,mBAAmB,CAAC,UAAU,EAAEsL,UAAU,CAAC;MAClDC,MAAM,CAACvL,mBAAmB,CAAC,aAAa,EAAG0L,CAAC,IAAKA,CAAC,CAAClB,cAAc,CAAC,CAAC,CAAC;;MAEpE;MACAgB,QAAQ,CAACxL,mBAAmB,CAAC,WAAW,EAAEmK,WAAW,CAAC;MACtDqB,QAAQ,CAACxL,mBAAmB,CAAC,SAAS,EAAEsK,SAAS,CAAC;MAElD,IAAI9M,cAAc,CAACwF,OAAO,EAAE;QAC1B2J,oBAAoB,CAACnP,cAAc,CAACwF,OAAO,CAAC;MAC9C;MACA,IAAI5F,QAAQ,CAAC4F,OAAO,IAAIuF,QAAQ,CAACc,UAAU,EAAE;QAC3CjM,QAAQ,CAAC4F,OAAO,CAAC4J,WAAW,CAACrE,QAAQ,CAACc,UAAU,CAAC;MACnD;MACA,IAAIhK,iBAAiB,CAAC2D,OAAO,EAAE;QAC7BwE,YAAY,CAACnI,iBAAiB,CAAC2D,OAAO,CAAC;MACzC;MACAuF,QAAQ,CAACsE,OAAO,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAACvN,QAAQ,EAAEvB,UAAU,EAAEK,MAAM,EAAEK,QAAQ,EAAEX,WAAW,CAAC,CAAC;;EAEzD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIU,QAAQ,CAAC2F,OAAO,IAAIzF,UAAU,CAACyF,OAAO,EAAE;MAC1C6E,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAC9J,UAAU,EAAEK,MAAM,EAAEK,QAAQ,CAAC,CAAC;;EAElC;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIkC,aAAa,IAAItB,UAAU,CAACyF,OAAO,IAAIlF,WAAW,KAAK,OAAO,EAAE;MAClE2F,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC1F,UAAU,CAACE,KAAK,EAAEF,UAAU,CAACI,MAAM,EAAEJ,UAAU,CAACG,KAAK,EAAEW,aAAa,EAAEf,WAAW,CAAC,CAAC;EAEvF,MAAMgP,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,aAAa,GAAG;MACpBjP,WAAW;MACXC,UAAU;MACVK,MAAM;MACNK,QAAQ;MACRE,QAAQ;MACR8B,KAAK,EAAEc,eAAe,CAAC;IACzB,CAAC;IAEDK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkL,aAAa,CAAC;IAC7CC,KAAK,CAAC,SAASrO,QAAQ,IAAIb,WAAW,oBAAoByD,eAAe,CAAC,CAAC,CAAC0L,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;EAC3F,CAAC;EAED,oBACElQ,OAAA;IAAKmQ,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAErCpQ,OAAA;MAAKmQ,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCpQ,OAAA;QAAKmQ,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpQ,OAAA;UAAQqQ,OAAO,EAAEnQ,MAAO;UAACiQ,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAC3CpQ,OAAA;YAAKkB,KAAK,EAAC,IAAI;YAACE,MAAM,EAAC,IAAI;YAACkP,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAH,QAAA,eACzDpQ,OAAA;cAAMwQ,CAAC,EAAC,yBAAyB;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC,oBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThR,OAAA;UAAAoQ,QAAA,GAAI,WAAS,EAACrP,WAAW,CAACkQ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnQ,WAAW,CAACoQ,KAAK,CAAC,CAAC,CAAC,EAAC,eAAa;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhR,OAAA;MAAKmQ,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCpQ,OAAA;QAAKmQ,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBpQ,OAAA;UAAKmQ,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAE7CpQ,OAAA;YAAKmQ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BpQ,OAAA;cAAKmQ,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCpQ,OAAA;gBAAKmQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpQ,OAAA;kBAAKmQ,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDpQ,OAAA;sBAAMwQ,CAAC,EAAC,4BAA4B;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACrDhR,OAAA;sBAAMwQ,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAC9DhR,OAAA;sBAAMwQ,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhR,OAAA;kBAAKmQ,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpQ,OAAA;oBAAAoQ,QAAA,EAAI;kBAAU;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBhR,OAAA;oBAAAoQ,QAAA,GAAG,uCAAqC,EAACrP,WAAW;kBAAA;oBAAA8P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhR,OAAA;gBAAKmQ,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCpQ,OAAA;kBACEmQ,SAAS,EAAE,gBAAgBpP,WAAW,KAAK,OAAO,IAAIe,aAAa,GAAG,kBAAkB,GAAG,EAAE,EAAG;kBAChGsP,GAAG,EAAE/Q;gBAAS;kBAAAwQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,EAENjQ,WAAW,KAAK,OAAO,IAAIe,aAAa,iBACvC9B,OAAA;kBAAKmQ,SAAS,EAAE,wBAAwB/N,uBAAuB,GAAG,MAAM,GAAG,EAAE,EAAG;kBAAAgO,QAAA,gBAC9EpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDpQ,OAAA;sBAAMwQ,CAAC,EAAC,gCAAgC;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACtGhR,OAAA;sBAAMwQ,CAAC,EAAC,qDAAqD;sBAACD,IAAI,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC,4BAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eACDhR,OAAA;kBAAKmQ,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCpQ,OAAA;oBAAKmQ,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCpQ,OAAA;sBACEmQ,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM1I,SAAS,CAAC,OAAO,CAAE;sBAClC0J,KAAK,EAAC,YAAY;sBAAAjB,QAAA,gBAElBpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMkG,CAAC,EAAC,GAAG;0BAACC,CAAC,EAAC,GAAG;0BAACjF,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACqP,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC,MAAM;0BAACe,EAAE,EAAC;wBAAG;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACrGhR,OAAA;0BAAQuR,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,KAAK;0BAAClB,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACNhR,OAAA;wBAAAoQ,QAAA,EAAM;sBAAK;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACThR,OAAA;sBACEmQ,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM1I,SAAS,CAAC,MAAM,CAAE;sBACjC0J,KAAK,EAAC,WAAW;sBAAAjB,QAAA,gBAEjBpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,kBAAkB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpEhR,OAAA;0BAAQuR,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,KAAK;0BAAClB,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACNhR,OAAA;wBAAAoQ,QAAA,EAAM;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACThR,OAAA;sBACEmQ,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM1I,SAAS,CAAC,KAAK,CAAE;sBAChC0J,KAAK,EAAC,UAAU;sBAAAjB,QAAA,gBAEhBpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,4BAA4B;0BAACD,IAAI,EAAC;wBAAc;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC1DhR,OAAA;0BAAMwQ,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,eACNhR,OAAA;wBAAAoQ,QAAA,EAAM;sBAAG;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACThR,OAAA;sBACEmQ,SAAS,EAAC,gBAAgB;sBAC1BE,OAAO,EAAEA,CAAA,KAAM1I,SAAS,CAAC,KAAK,CAAE;sBAChC0J,KAAK,EAAC,SAAS;sBAAAjB,QAAA,gBAEfpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,4BAA4B;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC1FhR,OAAA;0BAAMwQ,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACjFhR,OAAA;0BAAMwQ,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E,CAAC,eACNhR,OAAA;wBAAAoQ,QAAA,EAAM;sBAAE;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNhR,OAAA;oBAAKmQ,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACvCpQ,OAAA;sBACEmQ,SAAS,EAAC,cAAc;sBACxBE,OAAO,EAAEA,CAAA,KAAMtI,UAAU,CAAC,CAAC,CAAC,CAAE;sBAC9BsJ,KAAK,EAAC,UAAU;sBAAAjB,QAAA,eAEhBpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAQuR,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACvEhR,OAAA;0BAAMwQ,CAAC,EAAC,SAAS;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC3DhR,OAAA;0BAAMwQ,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACThR,OAAA;sBACEmQ,SAAS,EAAC,cAAc;sBACxBE,OAAO,EAAEA,CAAA,KAAMtI,UAAU,CAAC,CAAC,CAAE;sBAC7BsJ,KAAK,EAAC,SAAS;sBAAAjB,QAAA,eAEfpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAQuR,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACvEhR,OAAA;0BAAMwQ,CAAC,EAAC,gBAAgB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAClEhR,OAAA;0BAAMwQ,CAAC,EAAC,mBAAmB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhR,OAAA;kBAAKmQ,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAClCpQ,OAAA;oBAAAoQ,QAAA,EACG7N,QAAQ,GACL,mDAAmD,GACnD;kBAAoD;oBAAAsO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhR,OAAA;cAAKmQ,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCpQ,OAAA;gBAAAoQ,QAAA,EAAK,CAAAjQ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,IAAI,KAAI,UAAUG,WAAW,CAACkQ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnQ,WAAW,CAACoQ,KAAK,CAAC,CAAC,CAAC;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClGhR,OAAA;gBAAGmQ,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,GAAC,yBACV,EAACrP,WAAW,EAAC,mHAEtC;cAAA;gBAAA8P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhR,OAAA;gBAAKmQ,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BpQ,OAAA;kBAAKmQ,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDpQ,OAAA;sBAAMwQ,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGhR,OAAA;sBAAQuR,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNhR,OAAA;oBAAAoQ,QAAA,EAAM;kBAA0B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNhR,OAAA;kBAAKmQ,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDpQ,OAAA;sBAAMwQ,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGhR,OAAA;sBAAQuR,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNhR,OAAA;oBAAAoQ,QAAA,EAAM;kBAAiB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNhR,OAAA;kBAAKmQ,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDpQ,OAAA;sBAAMwQ,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGhR,OAAA;sBAAQuR,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNhR,OAAA;oBAAAoQ,QAAA,EAAM;kBAAiB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNhR,OAAA;kBAAKmQ,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDpQ,OAAA;sBAAMwQ,CAAC,EAAC,eAAe;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACvGhR,OAAA;sBAAQuR,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNhR,OAAA;oBAAAoQ,QAAA,EAAM;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhR,OAAA;YAAKmQ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAE3BpQ,OAAA;cAAKmQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpQ,OAAA;gBAAKmQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BpQ,OAAA;kBAAAoQ,QAAA,EAAI;gBAAU;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACNhR,OAAA;gBAAKmQ,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpQ,OAAA;kBAAKmQ,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCpQ,OAAA;oBAAKmQ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BpQ,OAAA;sBAAOmQ,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChDhR,OAAA;sBAAKmQ,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCpQ,OAAA;wBACEmQ,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMpP,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEE,KAAK,EAAE0C,IAAI,CAACC,GAAG,CAAC9C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACE,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAkP,QAAA,eAE1HpQ,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkP,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDpQ,OAAA;4BAAMwQ,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACThR,OAAA;wBAAKmQ,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCpQ,OAAA;0BAAMmQ,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEpP,UAAU,CAACE;wBAAK;0BAAA2P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC3DhR,OAAA;0BAAMmQ,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNhR,OAAA;wBACEmQ,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMpP,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEE,KAAK,EAAE0C,IAAI,CAACE,GAAG,CAAC/C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACE,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAkP,QAAA,eAE1HpQ,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkP,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDpQ,OAAA;4BAAMwQ,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhR,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZrI,GAAG,EAAE/C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxC8C,GAAG,EAAE9C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxC2Q,KAAK,EAAE1Q,UAAU,CAACE,KAAM;oBACxByQ,QAAQ,EAAGhD,CAAC,IAAK1N,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEE,KAAK,EAAE0Q,QAAQ,CAACjD,CAAC,CAACkD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBACjFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENhR,OAAA;kBAAKmQ,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCpQ,OAAA;oBAAKmQ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BpQ,OAAA;sBAAOmQ,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChDhR,OAAA;sBAAKmQ,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCpQ,OAAA;wBACEmQ,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMpP,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEG,KAAK,EAAEyC,IAAI,CAACC,GAAG,CAAC9C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE,EAAEC,UAAU,CAACG,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAiP,QAAA,eAEzHpQ,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkP,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDpQ,OAAA;4BAAMwQ,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACThR,OAAA;wBAAKmQ,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCpQ,OAAA;0BAAMmQ,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEpP,UAAU,CAACG;wBAAK;0BAAA0P,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC3DhR,OAAA;0BAAMmQ,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNhR,OAAA;wBACEmQ,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMpP,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEG,KAAK,EAAEyC,IAAI,CAACE,GAAG,CAAC/C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG,EAAEC,UAAU,CAACG,KAAK,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAiP,QAAA,eAE1HpQ,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkP,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDpQ,OAAA;4BAAMwQ,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhR,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZrI,GAAG,EAAE/C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAG;oBACvC8C,GAAG,EAAE9C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,GAAI;oBACxC2Q,KAAK,EAAE1Q,UAAU,CAACG,KAAM;oBACxBwQ,QAAQ,EAAGhD,CAAC,IAAK1N,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEG,KAAK,EAAEyQ,QAAQ,CAACjD,CAAC,CAACkD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBACjFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENhR,OAAA;kBAAKmQ,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCpQ,OAAA;oBAAKmQ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BpQ,OAAA;sBAAOmQ,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACjDhR,OAAA;sBAAKmQ,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,gBACvCpQ,OAAA;wBACEmQ,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMpP,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEI,MAAM,EAAEwC,IAAI,CAACC,GAAG,CAAC9C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAE,EAAEC,UAAU,CAACI,MAAM,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAgP,QAAA,eAE3HpQ,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkP,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDpQ,OAAA;4BAAMwQ,CAAC,EAAC,UAAU;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACThR,OAAA;wBAAKmQ,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChCpQ,OAAA;0BAAMmQ,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,EAAEpP,UAAU,CAACI;wBAAM;0BAAAyP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC5DhR,OAAA;0BAAMmQ,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAE;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNhR,OAAA;wBACEmQ,SAAS,EAAC,eAAe;wBACzBE,OAAO,EAAEA,CAAA,KAAMpP,aAAa,CAAC;0BAAC,GAAGD,UAAU;0BAAEI,MAAM,EAAEwC,IAAI,CAACE,GAAG,CAAC/C,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG,EAAEC,UAAU,CAACI,MAAM,GAAG,EAAE;wBAAC,CAAC,CAAE;wBAAAgP,QAAA,eAE7HpQ,OAAA;0BAAKkB,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkP,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,MAAM;0BAAAH,QAAA,eACzDpQ,OAAA;4BAAMwQ,CAAC,EAAC,kBAAkB;4BAACC,MAAM,EAAC,cAAc;4BAACC,WAAW,EAAC;0BAAG;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhR,OAAA;oBACEmM,IAAI,EAAC,OAAO;oBACZrI,GAAG,EAAE/C,WAAW,KAAK,OAAO,GAAG,EAAE,GAAG,EAAG;oBACvC8C,GAAG,EAAE9C,WAAW,KAAK,OAAO,GAAG,GAAG,GAAG,GAAI;oBACzC2Q,KAAK,EAAE1Q,UAAU,CAACI,MAAO;oBACzBuQ,QAAQ,EAAGhD,CAAC,IAAK1N,aAAa,CAAC;sBAAC,GAAGD,UAAU;sBAAEI,MAAM,EAAEwQ,QAAQ,CAACjD,CAAC,CAACkD,MAAM,CAACH,KAAK;oBAAC,CAAC,CAAE;oBAClFvB,SAAS,EAAC;kBAA2B;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhR,OAAA;cAAKmQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpQ,OAAA;gBAAKmQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpQ,OAAA;kBAAKmQ,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,gBACzDpQ,OAAA;sBAAQuR,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAAChB,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACjEhR,OAAA;sBAAMwQ,CAAC,EAAC,0CAA0C;sBAACD,IAAI,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhR,OAAA;kBAAKmQ,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBpQ,OAAA;oBAAAoQ,QAAA,EAAI;kBAAkB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3BhR,OAAA;oBAAAoQ,QAAA,EAAItO,aAAa,GAAG,mCAAmC,GAAG;kBAAqC;oBAAA+O,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELhP,UAAU,iBACThC,OAAA;gBAAKmQ,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCpQ,OAAA;kBAAKkB,KAAK,EAAC,IAAI;kBAACE,MAAM,EAAC,IAAI;kBAACkP,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAH,QAAA,gBACzDpQ,OAAA;oBAAQuR,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAAChB,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACjEhR,OAAA;oBAAMwQ,CAAC,EAAC,mBAAmB;oBAACC,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,EACLhP,UAAU;cAAA;gBAAA6O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACN,eACDhR,OAAA;gBAAKmQ,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CpQ,OAAA;kBAAKmQ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCpQ,OAAA;oBAAKmQ,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BpQ,OAAA;sBAAAoQ,QAAA,EAAI;oBAAa;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtBhR,OAAA;sBAAKmQ,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBpQ,OAAA;wBAAMmQ,SAAS,EAAC,oBAAoB;wBAAAC,QAAA,EACjC/O,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,YAAY,GAC3CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,WAAW,GAC1CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,UAAU,GACzCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,OAAO,GACtCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,YAAY,GAC3CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GACvCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,eAAe,GAC9CF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,KAAK,GACpCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,OAAO,GACtCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,MAAM,GACrCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GACvCF,MAAM,CAACE,OAAO,KAAK,SAAS,GAAG,QAAQ,GAAG;sBAAQ;wBAAAsP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACPhR,OAAA;wBACEmQ,SAAS,EAAC,uBAAuB;wBACjCvB,KAAK,EAAE;0BAAEkD,eAAe,EAAEzQ,MAAM,CAACE;wBAAQ;sBAAE;wBAAAsP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhR,OAAA;oBAAKmQ,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCpQ,OAAA;sBAAKmQ,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BpQ,OAAA;wBAAMmQ,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnDhR,OAAA;wBAAKmQ,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC2B,GAAG,CAAErJ,KAAK,iBAC5E1I,OAAA;0BAEEmQ,SAAS,EAAE,yBAAyB9O,MAAM,CAACE,OAAO,KAAKmH,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;0BAC/EkG,KAAK,EAAE;4BAAEkD,eAAe,EAAEpJ;0BAAM,CAAE;0BAClC2H,OAAO,EAAEA,CAAA,KAAM/O,SAAS,CAAC;4BAAC,GAAGD,MAAM;4BAAEE,OAAO,EAAEmH;0BAAK,CAAC,CAAE;0BACtD2I,KAAK,EAAE3I,KAAK,KAAK,SAAS,GAAG,YAAY,GAClCA,KAAK,KAAK,SAAS,GAAG,WAAW,GACjCA,KAAK,KAAK,SAAS,GAAG,UAAU,GAChCA,KAAK,KAAK,SAAS,GAAG,OAAO,GAC7BA,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG;wBAAS,GARhDA,KAAK;0BAAAmI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OASX,CACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhR,OAAA;sBAAKmQ,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BpQ,OAAA;wBAAMmQ,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAa;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxDhR,OAAA;wBAAKmQ,SAAS,EAAC,WAAW;wBAAAC,QAAA,EACvB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC2B,GAAG,CAAErJ,KAAK,iBAC5E1I,OAAA;0BAEEmQ,SAAS,EAAE,yBAAyB9O,MAAM,CAACE,OAAO,KAAKmH,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;0BAC/EkG,KAAK,EAAE;4BAAEkD,eAAe,EAAEpJ;0BAAM,CAAE;0BAClC2H,OAAO,EAAEA,CAAA,KAAM/O,SAAS,CAAC;4BAAC,GAAGD,MAAM;4BAAEE,OAAO,EAAEmH;0BAAK,CAAC,CAAE;0BACtD2I,KAAK,EAAE3I,KAAK,KAAK,SAAS,GAAG,eAAe,GACrCA,KAAK,KAAK,SAAS,GAAG,KAAK,GAC3BA,KAAK,KAAK,SAAS,GAAG,OAAO,GAC7BA,KAAK,KAAK,SAAS,GAAG,MAAM,GAC5BA,KAAK,KAAK,SAAS,GAAG,QAAQ,GAAG;wBAAS,GAR5CA,KAAK;0BAAAmI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OASX,CACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhR,OAAA;kBAAKmQ,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCpQ,OAAA;oBAAKmQ,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BpQ,OAAA;sBAAAoQ,QAAA,EAAI;oBAAiB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1BhR,OAAA;sBAAMmQ,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpC1O,QAAQ,CAACuP,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGxP,QAAQ,CAACyP,KAAK,CAAC,CAAC;oBAAC;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNhR,OAAA;oBAAKmQ,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EACvC,CAACrP,WAAW,KAAK,OAAO,GAAG,CAC1B;sBAAEH,IAAI,EAAE,MAAM;sBAAE8Q,KAAK,EAAE,MAAM;sBAAEM,IAAI,EAAE,KAAK;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,EAC5E;sBAAErR,IAAI,EAAE,QAAQ;sBAAE8Q,KAAK,EAAE,QAAQ;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,EAC/E;sBAAErR,IAAI,EAAE,SAAS;sBAAE8Q,KAAK,EAAE,SAAS;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,CAClF,GAAG,CACF;sBAAErR,IAAI,EAAE,MAAM;sBAAE8Q,KAAK,EAAE,MAAM;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAqB,CAAC,EACvE;sBAAErR,IAAI,EAAE,OAAO;sBAAE8Q,KAAK,EAAE,OAAO;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAuB,CAAC,EAC3E;sBAAErR,IAAI,EAAE,OAAO;sBAAE8Q,KAAK,EAAE,OAAO;sBAAEM,IAAI,EAAE,IAAI;sBAAEC,IAAI,EAAE;oBAAyB,CAAC,CAC9E,EAAEF,GAAG,CAAEG,GAAG,iBACTlS,OAAA;sBAEEmQ,SAAS,EAAE,4BAA4BzO,QAAQ,KAAKwQ,GAAG,CAACR,KAAK,GAAG,QAAQ,GAAG,EAAE,EAAG;sBAChFrB,OAAO,EAAEA,CAAA,KAAM1O,WAAW,CAACuQ,GAAG,CAACR,KAAK,CAAE;sBACtCL,KAAK,EAAEa,GAAG,CAACD,IAAK;sBAAA7B,QAAA,gBAEhBpQ,OAAA;wBAAKmQ,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAE8B,GAAG,CAACF;sBAAI;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/ChR,OAAA;wBAAKmQ,SAAS,EAAC,eAAe;wBAAAC,QAAA,gBAC5BpQ,OAAA;0BAAMmQ,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAE8B,GAAG,CAACtR;wBAAI;0BAAAiQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACjDhR,OAAA;0BAAMmQ,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAE8B,GAAG,CAACD;wBAAI;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA,GATDkB,GAAG,CAACR,KAAK;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAUR,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhR,OAAA;cAAKmQ,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CpQ,OAAA;gBAAKmQ,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCpQ,OAAA;kBAAKmQ,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BpQ,OAAA;oBAAKkB,KAAK,EAAC,IAAI;oBAACE,MAAM,EAAC,IAAI;oBAACkP,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAH,QAAA,eACzDpQ,OAAA;sBAAMwQ,CAAC,EAAC,+KAA+K;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhR,OAAA;kBAAKmQ,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCpQ,OAAA;oBAAAoQ,QAAA,EAAI;kBAAa;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBhR,OAAA;oBAAAoQ,QAAA,EAAG;kBAAyB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhR,OAAA;gBAAKmQ,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBAEnCpQ,OAAA;kBAAKmQ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrCpQ,OAAA;oBAAKmQ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BpQ,OAAA;sBAAKmQ,SAAS,EAAC,cAAc;sBAAAC,QAAA,eAC3BpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMkG,CAAC,EAAC,GAAG;0BAACC,CAAC,EAAC,GAAG;0BAACjF,KAAK,EAAC,IAAI;0BAACE,MAAM,EAAC,IAAI;0BAACkQ,EAAE,EAAC,GAAG;0BAACb,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACH,IAAI,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAChGhR,OAAA;0BAAMwQ,CAAC,EAAC,gBAAgB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhR,OAAA;sBAAKmQ,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BpQ,OAAA;wBAAMmQ,SAAS,EAAC,cAAc;wBAAAC,QAAA,GAAC,SAAO,EAACrP,WAAW,CAACkQ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnQ,WAAW,CAACoQ,KAAK,CAAC,CAAC,CAAC;sBAAA;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzGhR,OAAA;wBAAMmQ,SAAS,EAAC,eAAe;wBAAAC,QAAA,GAAEpP,UAAU,CAACE,KAAK,EAAC,MAAC,EAACF,UAAU,CAACG,KAAK,EAAC,MAAC,EAACH,UAAU,CAACI,MAAM,EAAC,IAAE;sBAAA;wBAAAyP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNhR,OAAA;kBAAKmQ,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCpQ,OAAA;oBAAKmQ,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BpQ,OAAA;sBAAKkB,KAAK,EAAC,IAAI;sBAACE,MAAM,EAAC,IAAI;sBAACkP,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAH,QAAA,gBACzDpQ,OAAA;wBAAMwQ,CAAC,EAAC,eAAe;wBAACC,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC,GAAG;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACvGhR,OAAA;wBAAQuR,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAAChB,MAAM,EAAC,SAAS;wBAACC,WAAW,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,eACNhR,OAAA;sBAAAoQ,QAAA,EAAM;oBAAQ;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACNhR,OAAA;oBAAKmQ,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,gBACvCpQ,OAAA;sBACEqQ,OAAO,EAAEA,CAAA,KAAMxO,WAAW,CAAC+B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjC,QAAQ,GAAG,CAAC,CAAC,CAAE;sBACtDuO,SAAS,EAAC,qBAAqB;sBAC/BgC,QAAQ,EAAEvQ,QAAQ,IAAI,CAAE;sBAAAwO,QAAA,eAExBpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,eACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,UAAU;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACThR,OAAA;sBAAKmQ,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,eACtCpQ,OAAA;wBAAMmQ,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EAAExO;sBAAQ;wBAAAiP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC,eACNhR,OAAA;sBACEqQ,OAAO,EAAEA,CAAA,KAAMxO,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;sBACzCuO,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,eAE/BpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,eACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,kBAAkB;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNhR,OAAA;kBAAKmQ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCpQ,OAAA;oBAAKmQ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BpQ,OAAA;sBAAKmQ,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBACjCpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAQuR,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,IAAI;0BAAChB,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACnEhR,OAAA;0BAAMwQ,CAAC,EAAC,aAAa;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC,KAAK;0BAACC,aAAa,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7E,CAAC,eACNhR,OAAA;wBAAAoQ,QAAA,EAAM;sBAAU;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACNhR,OAAA;sBAAMmQ,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,GAAC,EAAClN,YAAY,CAAC,CAAC,CAACgN,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACNhR,OAAA;oBAAKmQ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BpQ,OAAA;sBAAKmQ,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBACjCpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,2HAA2H;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACxKhR,OAAA;0BAAUoS,MAAM,EAAC,+BAA+B;0BAAC3B,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACrFhR,OAAA;0BAAMqS,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,OAAO;0BAACC,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAAC/B,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1E,CAAC,eACNhR,OAAA;wBAAAoQ,QAAA,EAAM;sBAAe;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACNhR,OAAA;sBAAMmQ,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,IAAE,EAAC,CAAC5L,eAAe,CAAC,CAAC,GAAG5C,QAAQ,GAAGsB,YAAY,CAAC,CAAC,EAAEgN,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjG,CAAC,eACNhR,OAAA;oBAAKmQ,SAAS,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrChR,OAAA;oBAAKmQ,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACzCpQ,OAAA;sBAAKmQ,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7CpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,2BAA2B;0BAACD,IAAI,EAAC;wBAAS;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpDhR,OAAA;0BAAMwQ,CAAC,EAAC,iBAAiB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eAC5DhR,OAAA;0BAAMwQ,CAAC,EAAC,iBAAiB;0BAACC,MAAM,EAAC,SAAS;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACNhR,OAAA;wBAAAoQ,QAAA,GAAM,SAAO,EAACxO,QAAQ,EAAC,OAAK,EAACA,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,GAAC;sBAAA;wBAAAiP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACNhR,OAAA;sBAAMmQ,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,GAAC,GAAC,EAAC5L,eAAe,CAAC,CAAC,CAAC0L,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNhR,OAAA;kBAAQmQ,SAAS,EAAC,wBAAwB;kBAACE,OAAO,EAAEN,eAAgB;kBAAAK,QAAA,eAClEpQ,OAAA;oBAAKmQ,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjCpQ,OAAA;sBAAKmQ,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,eAC9BpQ,OAAA;wBAAKkB,KAAK,EAAC,IAAI;wBAACE,MAAM,EAAC,IAAI;wBAACkP,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,MAAM;wBAAAH,QAAA,gBACzDpQ,OAAA;0BAAMwQ,CAAC,EAAC,uGAAuG;0BAACC,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC,GAAG;0BAACC,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACtMhR,OAAA;0BAAQuR,EAAE,EAAC,GAAG;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACpEhR,OAAA;0BAAQuR,EAAE,EAAC,IAAI;0BAACC,EAAE,EAAC,IAAI;0BAACC,CAAC,EAAC,GAAG;0BAAChB,MAAM,EAAC,cAAc;0BAACC,WAAW,EAAC;wBAAG;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhR,OAAA;sBAAKmQ,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BpQ,OAAA;wBAAMmQ,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAAC;sBAAW;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtDhR,OAAA;wBAAMmQ,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,GAAC,GAAC,EAAC5L,eAAe,CAAC,CAAC,CAAC0L,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5Q,EAAA,CAhvCIH,sBAAsB;AAAAwS,EAAA,GAAtBxS,sBAAsB;AAkvC5B,eAAeA,sBAAsB;AAAC,IAAAwS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}