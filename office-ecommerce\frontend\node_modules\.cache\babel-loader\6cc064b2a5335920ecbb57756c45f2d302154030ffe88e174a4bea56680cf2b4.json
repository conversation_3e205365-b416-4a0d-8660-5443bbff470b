{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\OrderSuccessPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams, useLocation, Link } from 'react-router-dom';\nimport paymentService from '../services/paymentService';\nimport './OrderSuccessPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderSuccessPage = () => {\n  _s();\n  const {\n    orderId\n  } = useParams();\n  const location = useLocation();\n  const {\n    order,\n    message,\n    paymentStatus,\n    paymentMethod\n  } = location.state || {};\n  const [paymentDetails, setPaymentDetails] = useState(null);\n  useEffect(() => {\n    // Track successful order completion\n    if (order) {\n      console.log('Order completed successfully:', {\n        orderId: order.id || orderId,\n        amount: order.total_amount,\n        paymentStatus,\n        paymentMethod\n      });\n\n      // Set payment details for display\n      if (paymentStatus && paymentMethod) {\n        setPaymentDetails({\n          status: paymentStatus,\n          method: paymentMethod,\n          completedAt: new Date().toISOString()\n        });\n      }\n    }\n  }, [order, orderId, paymentStatus, paymentMethod]);\n  const formatPrice = price => {\n    return paymentService.formatAmount(price, (order === null || order === void 0 ? void 0 : order.currency) || 'PHP');\n  };\n  const getPaymentMethodIcon = () => {\n    if (paymentMethod === 'PayMongo') {\n      return '💳';\n    }\n    return paymentService.getPaymentMethodIcon(paymentMethod || 'card');\n  };\n  const getPaymentMethodDisplay = () => {\n    if (paymentMethod === 'PayMongo') {\n      return 'PayMongo Payment Gateway';\n    }\n    return paymentService.getPaymentMethodDisplayName(paymentMethod || 'card');\n  };\n  const getPaymentStatusDisplay = () => {\n    const statusMap = {\n      'completed': {\n        text: 'Payment Successful',\n        color: '#059669',\n        icon: '✅'\n      },\n      'pending': {\n        text: 'Payment Pending',\n        color: '#F0B21B',\n        icon: '⏳'\n      },\n      'failed': {\n        text: 'Payment Failed',\n        color: '#DC2626',\n        icon: '❌'\n      },\n      'cancelled': {\n        text: 'Payment Cancelled',\n        color: '#6B7280',\n        icon: '🚫'\n      }\n    };\n    return statusMap[paymentStatus] || statusMap['completed'];\n  };\n  if (!order) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-success-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Order Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"We couldn't find the details for this order.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-primary\",\n            children: \"Continue Shopping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"order-success-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"64\",\n            height: \"64\",\n            viewBox: \"0 0 64 64\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"32\",\n              cy: \"32\",\n              r: \"30\",\n              fill: \"#059669\",\n              stroke: \"#047857\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M20 32L28 40L44 24\",\n              stroke: \"white\",\n              strokeWidth: \"4\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"success-title\",\n          children: \"Order Confirmed!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"success-subtitle\",\n          children: message || 'Thank you for your order. We will process your order shortly.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Order Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Order Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: order.order_number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Order Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: new Date().toLocaleDateString('en-PH', {\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Total Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value total-amount\",\n                children: formatPrice(order.total_amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"Payment Method\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value payment-method\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-icon\",\n                  children: getPaymentMethodIcon()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 37\n                }, this), getPaymentMethodDisplay()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this), paymentDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-section payment-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-status-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-status-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"payment-status-icon\",\n                children: getPaymentStatusDisplay().icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"payment-status-text\",\n                style: {\n                  color: getPaymentStatusDisplay().color\n                },\n                children: getPaymentStatusDisplay().text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-details-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-detail-label\",\n                  children: \"Payment Method\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-detail-value\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"payment-method-icon\",\n                    children: getPaymentMethodIcon()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 45\n                  }, this), getPaymentMethodDisplay()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-detail-label\",\n                  children: \"Payment Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-detail-value\",\n                  children: new Date(paymentDetails.completedAt).toLocaleDateString('en-PH', {\n                    year: 'numeric',\n                    month: 'long',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-detail-label\",\n                  children: \"Transaction Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"payment-detail-value\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"status-badge success\",\n                    children: \"Confirmed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-security-notice\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"security-icon\",\n                children: \"\\uD83D\\uDD10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Payment processed securely through PayMongo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"next-steps\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"What's Next?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"steps-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-icon\",\n              children: \"\\uD83D\\uDCE7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Order Confirmation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"You'll receive an email confirmation with your order details shortly.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"We'll start processing your order and prepare it for shipment.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-icon\",\n              children: \"\\uD83D\\uDE9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Shipping\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"You'll receive tracking information once your order ships.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-actions\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/products\",\n          className: \"btn btn-primary\",\n          children: \"Continue Shopping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/orders\",\n          className: \"btn btn-secondary\",\n          children: \"View Orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"support-info\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Need help with your order?\", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"support-link\",\n            children: \" Contact our support team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 9\n  }, this);\n};\n_s(OrderSuccessPage, \"wGS91A/HVgJN3V/v+pVFaROb5qQ=\", false, function () {\n  return [useParams, useLocation];\n});\n_c = OrderSuccessPage;\nexport default OrderSuccessPage;\nvar _c;\n$RefreshReg$(_c, \"OrderSuccessPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "useLocation", "Link", "paymentService", "jsxDEV", "_jsxDEV", "OrderSuccessPage", "_s", "orderId", "location", "order", "message", "paymentStatus", "paymentMethod", "state", "paymentDetails", "setPaymentDetails", "console", "log", "id", "amount", "total_amount", "status", "method", "completedAt", "Date", "toISOString", "formatPrice", "price", "formatAmount", "currency", "getPaymentMethodIcon", "getPaymentMethodDisplay", "getPaymentMethodDisplayName", "getPaymentStatusDisplay", "statusMap", "text", "color", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "width", "height", "viewBox", "fill", "xmlns", "cx", "cy", "r", "stroke", "strokeWidth", "d", "strokeLinecap", "strokeLinejoin", "order_number", "toLocaleDateString", "year", "month", "day", "style", "hour", "minute", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/pages/OrderSuccessPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams, useLocation, Link } from 'react-router-dom';\nimport paymentService from '../services/paymentService';\nimport './OrderSuccessPage.css';\n\nconst OrderSuccessPage = () => {\n    const { orderId } = useParams();\n    const location = useLocation();\n    const { order, message, paymentStatus, paymentMethod } = location.state || {};\n    const [paymentDetails, setPaymentDetails] = useState(null);\n\n    useEffect(() => {\n        // Track successful order completion\n        if (order) {\n            console.log('Order completed successfully:', {\n                orderId: order.id || orderId,\n                amount: order.total_amount,\n                paymentStatus,\n                paymentMethod\n            });\n\n            // Set payment details for display\n            if (paymentStatus && paymentMethod) {\n                setPaymentDetails({\n                    status: paymentStatus,\n                    method: paymentMethod,\n                    completedAt: new Date().toISOString()\n                });\n            }\n        }\n    }, [order, orderId, paymentStatus, paymentMethod]);\n\n    const formatPrice = (price) => {\n        return paymentService.formatAmount(price, order?.currency || 'PHP');\n    };\n\n    const getPaymentMethodIcon = () => {\n        if (paymentMethod === 'PayMongo') {\n            return '💳';\n        }\n        return paymentService.getPaymentMethodIcon(paymentMethod || 'card');\n    };\n\n    const getPaymentMethodDisplay = () => {\n        if (paymentMethod === 'PayMongo') {\n            return 'PayMongo Payment Gateway';\n        }\n        return paymentService.getPaymentMethodDisplayName(paymentMethod || 'card');\n    };\n\n    const getPaymentStatusDisplay = () => {\n        const statusMap = {\n            'completed': { text: 'Payment Successful', color: '#059669', icon: '✅' },\n            'pending': { text: 'Payment Pending', color: '#F0B21B', icon: '⏳' },\n            'failed': { text: 'Payment Failed', color: '#DC2626', icon: '❌' },\n            'cancelled': { text: 'Payment Cancelled', color: '#6B7280', icon: '🚫' }\n        };\n        return statusMap[paymentStatus] || statusMap['completed'];\n    };\n\n\n\n    if (!order) {\n        return (\n            <div className=\"order-success-page\">\n                <div className=\"success-container\">\n                    <div className=\"error-state\">\n                        <h2>Order Not Found</h2>\n                        <p>We couldn't find the details for this order.</p>\n                        <Link to=\"/products\" className=\"btn btn-primary\">\n                            Continue Shopping\n                        </Link>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"order-success-page\">\n            <div className=\"success-container\">\n                {/* Success Header */}\n                <div className=\"success-header\">\n                    <div className=\"success-icon\">\n                        <svg width=\"64\" height=\"64\" viewBox=\"0 0 64 64\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                            <circle cx=\"32\" cy=\"32\" r=\"30\" fill=\"#059669\" stroke=\"#047857\" strokeWidth=\"2\"/>\n                            <path d=\"M20 32L28 40L44 24\" stroke=\"white\" strokeWidth=\"4\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        </svg>\n                    </div>\n                    <h1 className=\"success-title\">Order Confirmed!</h1>\n                    <p className=\"success-subtitle\">\n                        {message || 'Thank you for your order. We will process your order shortly.'}\n                    </p>\n                </div>\n\n                {/* Order Details */}\n                <div className=\"order-details\">\n                    <div className=\"detail-section\">\n                        <h3>Order Information</h3>\n                        <div className=\"detail-grid\">\n                            <div className=\"detail-item\">\n                                <span className=\"detail-label\">Order Number</span>\n                                <span className=\"detail-value\">{order.order_number}</span>\n                            </div>\n                            <div className=\"detail-item\">\n                                <span className=\"detail-label\">Order Date</span>\n                                <span className=\"detail-value\">\n                                    {new Date().toLocaleDateString('en-PH', {\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })}\n                                </span>\n                            </div>\n                            <div className=\"detail-item\">\n                                <span className=\"detail-label\">Total Amount</span>\n                                <span className=\"detail-value total-amount\">{formatPrice(order.total_amount)}</span>\n                            </div>\n                            <div className=\"detail-item\">\n                                <span className=\"detail-label\">Payment Method</span>\n                                <span className=\"detail-value payment-method\">\n                                    <span className=\"payment-icon\">{getPaymentMethodIcon()}</span>\n                                    {getPaymentMethodDisplay()}\n                                </span>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* PayMongo Payment Details */}\n                    {paymentDetails && (\n                        <div className=\"detail-section payment-section\">\n                            <h3>Payment Details</h3>\n                            <div className=\"payment-status-card\">\n                                <div className=\"payment-status-header\">\n                                    <span className=\"payment-status-icon\">\n                                        {getPaymentStatusDisplay().icon}\n                                    </span>\n                                    <span\n                                        className=\"payment-status-text\"\n                                        style={{ color: getPaymentStatusDisplay().color }}\n                                    >\n                                        {getPaymentStatusDisplay().text}\n                                    </span>\n                                </div>\n                                <div className=\"payment-details-grid\">\n                                    <div className=\"payment-detail-item\">\n                                        <span className=\"payment-detail-label\">Payment Method</span>\n                                        <span className=\"payment-detail-value\">\n                                            <span className=\"payment-method-icon\">{getPaymentMethodIcon()}</span>\n                                            {getPaymentMethodDisplay()}\n                                        </span>\n                                    </div>\n                                    <div className=\"payment-detail-item\">\n                                        <span className=\"payment-detail-label\">Payment Date</span>\n                                        <span className=\"payment-detail-value\">\n                                            {new Date(paymentDetails.completedAt).toLocaleDateString('en-PH', {\n                                                year: 'numeric',\n                                                month: 'long',\n                                                day: 'numeric',\n                                                hour: '2-digit',\n                                                minute: '2-digit'\n                                            })}\n                                        </span>\n                                    </div>\n                                    <div className=\"payment-detail-item\">\n                                        <span className=\"payment-detail-label\">Transaction Status</span>\n                                        <span className=\"payment-detail-value\">\n                                            <span className=\"status-badge success\">Confirmed</span>\n                                        </span>\n                                    </div>\n                                </div>\n                                <div className=\"payment-security-notice\">\n                                    <span className=\"security-icon\">🔐</span>\n                                    <span>Payment processed securely through PayMongo</span>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n                </div>\n\n                {/* Next Steps */}\n                <div className=\"next-steps\">\n                    <h3>What's Next?</h3>\n                    <div className=\"steps-grid\">\n                        <div className=\"step-item\">\n                            <div className=\"step-icon\">📧</div>\n                            <div className=\"step-content\">\n                                <h4>Order Confirmation</h4>\n                                <p>You'll receive an email confirmation with your order details shortly.</p>\n                            </div>\n                        </div>\n                        <div className=\"step-item\">\n                            <div className=\"step-icon\">📦</div>\n                            <div className=\"step-content\">\n                                <h4>Processing</h4>\n                                <p>We'll start processing your order and prepare it for shipment.</p>\n                            </div>\n                        </div>\n                        <div className=\"step-item\">\n                            <div className=\"step-icon\">🚚</div>\n                            <div className=\"step-content\">\n                                <h4>Shipping</h4>\n                                <p>You'll receive tracking information once your order ships.</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"success-actions\">\n                    <Link to=\"/products\" className=\"btn btn-primary\">\n                        Continue Shopping\n                    </Link>\n                    <Link to=\"/orders\" className=\"btn btn-secondary\">\n                        View Orders\n                    </Link>\n                </div>\n\n                {/* Support Information */}\n                <div className=\"support-info\">\n                    <p>\n                        Need help with your order? \n                        <Link to=\"/contact\" className=\"support-link\"> Contact our support team</Link>\n                    </p>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default OrderSuccessPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AAC/D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAQ,CAAC,GAAGR,SAAS,CAAC,CAAC;EAC/B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,KAAK;IAAEC,OAAO;IAAEC,aAAa;IAAEC;EAAc,CAAC,GAAGJ,QAAQ,CAACK,KAAK,IAAI,CAAC,CAAC;EAC7E,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAE1DD,SAAS,CAAC,MAAM;IACZ;IACA,IAAIY,KAAK,EAAE;MACPO,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QACzCV,OAAO,EAAEE,KAAK,CAACS,EAAE,IAAIX,OAAO;QAC5BY,MAAM,EAAEV,KAAK,CAACW,YAAY;QAC1BT,aAAa;QACbC;MACJ,CAAC,CAAC;;MAEF;MACA,IAAID,aAAa,IAAIC,aAAa,EAAE;QAChCG,iBAAiB,CAAC;UACdM,MAAM,EAAEV,aAAa;UACrBW,MAAM,EAAEV,aAAa;UACrBW,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACxC,CAAC,CAAC;MACN;IACJ;EACJ,CAAC,EAAE,CAAChB,KAAK,EAAEF,OAAO,EAAEI,aAAa,EAAEC,aAAa,CAAC,CAAC;EAElD,MAAMc,WAAW,GAAIC,KAAK,IAAK;IAC3B,OAAOzB,cAAc,CAAC0B,YAAY,CAACD,KAAK,EAAE,CAAAlB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoB,QAAQ,KAAI,KAAK,CAAC;EACvE,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAIlB,aAAa,KAAK,UAAU,EAAE;MAC9B,OAAO,IAAI;IACf;IACA,OAAOV,cAAc,CAAC4B,oBAAoB,CAAClB,aAAa,IAAI,MAAM,CAAC;EACvE,CAAC;EAED,MAAMmB,uBAAuB,GAAGA,CAAA,KAAM;IAClC,IAAInB,aAAa,KAAK,UAAU,EAAE;MAC9B,OAAO,0BAA0B;IACrC;IACA,OAAOV,cAAc,CAAC8B,2BAA2B,CAACpB,aAAa,IAAI,MAAM,CAAC;EAC9E,CAAC;EAED,MAAMqB,uBAAuB,GAAGA,CAAA,KAAM;IAClC,MAAMC,SAAS,GAAG;MACd,WAAW,EAAE;QAAEC,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAC;MACxE,SAAS,EAAE;QAAEF,IAAI,EAAE,iBAAiB;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAC;MACnE,QAAQ,EAAE;QAAEF,IAAI,EAAE,gBAAgB;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAC;MACjE,WAAW,EAAE;QAAEF,IAAI,EAAE,mBAAmB;QAAEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAK;IAC3E,CAAC;IACD,OAAOH,SAAS,CAACvB,aAAa,CAAC,IAAIuB,SAAS,CAAC,WAAW,CAAC;EAC7D,CAAC;EAID,IAAI,CAACzB,KAAK,EAAE;IACR,oBACIL,OAAA;MAAKkC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eAC/BnC,OAAA;QAAKkC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAC9BnC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBnC,OAAA;YAAAmC,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBvC,OAAA;YAAAmC,QAAA,EAAG;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnDvC,OAAA,CAACH,IAAI;YAAC2C,EAAE,EAAC,WAAW;YAACN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIvC,OAAA;IAAKkC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eAC/BnC,OAAA;MAAKkC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAE9BnC,OAAA;QAAKkC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BnC,OAAA;UAAKkC,SAAS,EAAC,cAAc;UAAAC,QAAA,eACzBnC,OAAA;YAAKyC,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAV,QAAA,gBAC1FnC,OAAA;cAAQ8C,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAACJ,IAAI,EAAC,SAAS;cAACK,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAChFvC,OAAA;cAAMmD,CAAC,EAAC,oBAAoB;cAACF,MAAM,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACE,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvC,OAAA;UAAIkC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnDvC,OAAA;UAAGkC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC1B7B,OAAO,IAAI;QAA+D;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BnC,OAAA;UAAKkC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3BnC,OAAA;YAAAmC,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BvC,OAAA;YAAKkC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBnC,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBnC,OAAA;gBAAMkC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDvC,OAAA;gBAAMkC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE9B,KAAK,CAACiD;cAAY;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNvC,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBnC,OAAA;gBAAMkC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDvC,OAAA;gBAAMkC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EACzB,IAAIf,IAAI,CAAC,CAAC,CAACmC,kBAAkB,CAAC,OAAO,EAAE;kBACpCC,IAAI,EAAE,SAAS;kBACfC,KAAK,EAAE,MAAM;kBACbC,GAAG,EAAE;gBACT,CAAC;cAAC;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvC,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBnC,OAAA;gBAAMkC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDvC,OAAA;gBAAMkC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEb,WAAW,CAACjB,KAAK,CAACW,YAAY;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACNvC,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBnC,OAAA;gBAAMkC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDvC,OAAA;gBAAMkC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBACzCnC,OAAA;kBAAMkC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAET,oBAAoB,CAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC7DZ,uBAAuB,CAAC,CAAC;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGL7B,cAAc,iBACXV,OAAA;UAAKkC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC3CnC,OAAA;YAAAmC,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBvC,OAAA;YAAKkC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAChCnC,OAAA;cAAKkC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBAClCnC,OAAA;gBAAMkC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAChCN,uBAAuB,CAAC,CAAC,CAACI;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACPvC,OAAA;gBACIkC,SAAS,EAAC,qBAAqB;gBAC/ByB,KAAK,EAAE;kBAAE3B,KAAK,EAAEH,uBAAuB,CAAC,CAAC,CAACG;gBAAM,CAAE;gBAAAG,QAAA,EAEjDN,uBAAuB,CAAC,CAAC,CAACE;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvC,OAAA;cAAKkC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCnC,OAAA;gBAAKkC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChCnC,OAAA;kBAAMkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DvC,OAAA;kBAAMkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBAClCnC,OAAA;oBAAMkC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAET,oBAAoB,CAAC;kBAAC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACpEZ,uBAAuB,CAAC,CAAC;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvC,OAAA;gBAAKkC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChCnC,OAAA;kBAAMkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DvC,OAAA;kBAAMkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjC,IAAIf,IAAI,CAACV,cAAc,CAACS,WAAW,CAAC,CAACoC,kBAAkB,CAAC,OAAO,EAAE;oBAC9DC,IAAI,EAAE,SAAS;oBACfC,KAAK,EAAE,MAAM;oBACbC,GAAG,EAAE,SAAS;oBACdE,IAAI,EAAE,SAAS;oBACfC,MAAM,EAAE;kBACZ,CAAC;gBAAC;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvC,OAAA;gBAAKkC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAChCnC,OAAA;kBAAMkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChEvC,OAAA;kBAAMkC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,eAClCnC,OAAA;oBAAMkC,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNvC,OAAA;cAAKkC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACpCnC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCvC,OAAA;gBAAAmC,QAAA,EAAM;cAA2C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBnC,OAAA;UAAAmC,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBvC,OAAA;UAAKkC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBnC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBnC,OAAA;cAAKkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCvC,OAAA;cAAKkC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBnC,OAAA;gBAAAmC,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BvC,OAAA;gBAAAmC,QAAA,EAAG;cAAqE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNvC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBnC,OAAA;cAAKkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCvC,OAAA;cAAKkC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBnC,OAAA;gBAAAmC,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBvC,OAAA;gBAAAmC,QAAA,EAAG;cAA8D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNvC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBnC,OAAA;cAAKkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCvC,OAAA;cAAKkC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBnC,OAAA;gBAAAmC,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBvC,OAAA;gBAAAmC,QAAA,EAAG;cAA0D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BnC,OAAA,CAACH,IAAI;UAAC2C,EAAE,EAAC,WAAW;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPvC,OAAA,CAACH,IAAI;UAAC2C,EAAE,EAAC,SAAS;UAACN,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,cAAc;QAAAC,QAAA,eACzBnC,OAAA;UAAAmC,QAAA,GAAG,4BAEC,eAAAnC,OAAA,CAACH,IAAI;YAAC2C,EAAE,EAAC,UAAU;YAACN,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACrC,EAAA,CA/NID,gBAAgB;EAAA,QACEN,SAAS,EACZC,WAAW;AAAA;AAAAkE,EAAA,GAF1B7D,gBAAgB;AAiOtB,eAAeA,gBAAgB;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}