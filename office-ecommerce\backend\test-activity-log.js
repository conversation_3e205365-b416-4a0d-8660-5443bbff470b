const ActivityLogService = require('./services/activityLogService');
const { connectDB } = require('./config/database');

async function testActivityLogging() {
  try {
    console.log('🔌 Connecting to database...');
    await connectDB();
    console.log('✅ Database connected');

    console.log('\n📝 Testing activity logging...');

    // Test logging a sample activity
    const sampleActivity = {
      userID: null,
      userEmail: '<EMAIL>',
      userName: 'Test User',
      userRole: 'Admin',
      action: 'TEST',
      entityType: 'System',
      entityID: null,
      entityName: 'Activity Log Test',
      description: 'Testing activity log functionality',
      ipAddress: '127.0.0.1',
      userAgent: 'Test Script',
      requestMethod: 'POST',
      requestPath: '/test',
      statusCode: 200,
      duration: 150,
      metadata: {
        test: true,
        timestamp: new Date().toISOString()
      },
      severity: 'INFO'
    };

    await ActivityLogService.logActivity(sampleActivity);
    console.log('✅ Sample activity logged successfully');

    console.log('\n📊 Getting activity statistics...');
    const stats = await ActivityLogService.getActivityStats();
    console.log('Activity Stats:', JSON.stringify(stats, null, 2));

    console.log('\n📋 Getting recent activity logs...');
    const logs = await ActivityLogService.getActivityLogs({
      page: 1,
      limit: 10
    });
    
    console.log(`Found ${logs.logs.length} activity logs:`);
    logs.logs.forEach((log, index) => {
      console.log(`${index + 1}. [${log.Severity}] ${log.Action} ${log.EntityType} - ${log.Description} (${log.CreatedAt})`);
    });

    console.log('\n✅ Activity logging test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testActivityLogging();
