{"ast": null, "code": "import { Object3D, Vector2, Vector3, Matrix4 } from \"three\";\nclass CSS2DObject extends Object3D {\n  constructor(element = document.createElement(\"div\")) {\n    super();\n    this.isCSS2DObject = true;\n    this.element = element;\n    this.element.style.position = \"absolute\";\n    this.element.style.userSelect = \"none\";\n    this.element.setAttribute(\"draggable\", false);\n    this.center = new Vector2(0.5, 0.5);\n    this.addEventListener(\"removed\", function () {\n      this.traverse(function (object) {\n        if (object.element instanceof Element && object.element.parentNode !== null) {\n          object.element.parentNode.removeChild(object.element);\n        }\n      });\n    });\n  }\n  copy(source, recursive) {\n    super.copy(source, recursive);\n    this.element = source.element.cloneNode(true);\n    this.center = source.center;\n    return this;\n  }\n}\nconst _vector = /* @__PURE__ */new Vector3();\nconst _viewMatrix = /* @__PURE__ */new Matrix4();\nconst _viewProjectionMatrix = /* @__PURE__ */new Matrix4();\nconst _a = /* @__PURE__ */new Vector3();\nconst _b = /* @__PURE__ */new Vector3();\nclass CSS2DRenderer {\n  constructor(parameters = {}) {\n    const _this = this;\n    let _width, _height;\n    let _widthHalf, _heightHalf;\n    const cache = {\n      objects: /* @__PURE__ */new WeakMap()\n    };\n    const domElement = parameters.element !== void 0 ? parameters.element : document.createElement(\"div\");\n    domElement.style.overflow = \"hidden\";\n    this.domElement = domElement;\n    this.getSize = function () {\n      return {\n        width: _width,\n        height: _height\n      };\n    };\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();\n      _viewMatrix.copy(camera.matrixWorldInverse);\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix);\n      renderObject(scene, scene, camera);\n      zOrder(scene);\n    };\n    this.setSize = function (width, height) {\n      _width = width;\n      _height = height;\n      _widthHalf = _width / 2;\n      _heightHalf = _height / 2;\n      domElement.style.width = width + \"px\";\n      domElement.style.height = height + \"px\";\n    };\n    function renderObject(object, scene, camera) {\n      if (object.isCSS2DObject) {\n        _vector.setFromMatrixPosition(object.matrixWorld);\n        _vector.applyMatrix4(_viewProjectionMatrix);\n        const visible = object.visible === true && _vector.z >= -1 && _vector.z <= 1 && object.layers.test(camera.layers) === true;\n        object.element.style.display = visible === true ? \"\" : \"none\";\n        if (visible === true) {\n          object.onBeforeRender(_this, scene, camera);\n          const element = object.element;\n          element.style.transform = \"translate(\" + -100 * object.center.x + \"%,\" + -100 * object.center.y + \"%)translate(\" + (_vector.x * _widthHalf + _widthHalf) + \"px,\" + (-_vector.y * _heightHalf + _heightHalf) + \"px)\";\n          if (element.parentNode !== domElement) {\n            domElement.appendChild(element);\n          }\n          object.onAfterRender(_this, scene, camera);\n        }\n        const objectData = {\n          distanceToCameraSquared: getDistanceToSquared(camera, object)\n        };\n        cache.objects.set(object, objectData);\n      }\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        renderObject(object.children[i], scene, camera);\n      }\n    }\n    function getDistanceToSquared(object1, object2) {\n      _a.setFromMatrixPosition(object1.matrixWorld);\n      _b.setFromMatrixPosition(object2.matrixWorld);\n      return _a.distanceToSquared(_b);\n    }\n    function filterAndFlatten(scene) {\n      const result = [];\n      scene.traverse(function (object) {\n        if (object.isCSS2DObject) result.push(object);\n      });\n      return result;\n    }\n    function zOrder(scene) {\n      const sorted = filterAndFlatten(scene).sort(function (a, b) {\n        if (a.renderOrder !== b.renderOrder) {\n          return b.renderOrder - a.renderOrder;\n        }\n        const distanceA = cache.objects.get(a).distanceToCameraSquared;\n        const distanceB = cache.objects.get(b).distanceToCameraSquared;\n        return distanceA - distanceB;\n      });\n      const zMax = sorted.length;\n      for (let i = 0, l = sorted.length; i < l; i++) {\n        sorted[i].element.style.zIndex = zMax - i;\n      }\n    }\n  }\n}\nexport { CSS2DObject, CSS2DRenderer };", "map": {"version": 3, "names": ["CSS2DObject", "Object3D", "constructor", "element", "document", "createElement", "isCSS2DObject", "style", "position", "userSelect", "setAttribute", "center", "Vector2", "addEventListener", "traverse", "object", "Element", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "copy", "source", "recursive", "cloneNode", "_vector", "Vector3", "_viewMatrix", "Matrix4", "_viewProjectionMatrix", "_a", "_b", "CSS2<PERSON><PERSON><PERSON>", "parameters", "_this", "_width", "_height", "_widthHalf", "_heightHalf", "cache", "objects", "WeakMap", "dom<PERSON>lement", "overflow", "getSize", "width", "height", "render", "scene", "camera", "matrixWorldAutoUpdate", "updateMatrixWorld", "parent", "matrixWorldInverse", "multiplyMatrices", "projectionMatrix", "renderObject", "zOrder", "setSize", "setFromMatrixPosition", "matrixWorld", "applyMatrix4", "visible", "z", "layers", "test", "display", "onBeforeRender", "transform", "x", "y", "append<PERSON><PERSON><PERSON>", "onAfterRender", "objectData", "distanceToCameraSquared", "getDistanceToSquared", "set", "i", "l", "children", "length", "object1", "object2", "distanceToSquared", "filterAndFlatten", "result", "push", "sorted", "sort", "a", "b", "renderOrder", "distanceA", "get", "distanceB", "zMax", "zIndex"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\renderers\\CSS2DRenderer.js"], "sourcesContent": ["import { Matrix4, Object3D, Vector2, Vector3 } from 'three'\n\nclass CSS2DObject extends Object3D {\n  constructor(element = document.createElement('div')) {\n    super()\n\n    this.isCSS2DObject = true\n\n    this.element = element\n\n    this.element.style.position = 'absolute'\n    this.element.style.userSelect = 'none'\n\n    this.element.setAttribute('draggable', false)\n\n    this.center = new Vector2(0.5, 0.5) // ( 0, 0 ) is the lower left; ( 1, 1 ) is the top right\n\n    this.addEventListener('removed', function () {\n      this.traverse(function (object) {\n        if (object.element instanceof Element && object.element.parentNode !== null) {\n          object.element.parentNode.removeChild(object.element)\n        }\n      })\n    })\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.element = source.element.cloneNode(true)\n\n    this.center = source.center\n\n    return this\n  }\n}\n\nconst _vector = /* @__PURE__ */ new Vector3()\nconst _viewMatrix = /* @__PURE__ */ new Matrix4()\nconst _viewProjectionMatrix = /* @__PURE__ */ new Matrix4()\nconst _a = /* @__PURE__ */ new Vector3()\nconst _b = /* @__PURE__ */ new Vector3()\n\nclass CSS2DRenderer {\n  constructor(parameters = {}) {\n    const _this = this\n\n    let _width, _height\n    let _widthHalf, _heightHalf\n\n    const cache = {\n      objects: new WeakMap(),\n    }\n\n    const domElement = parameters.element !== undefined ? parameters.element : document.createElement('div')\n\n    domElement.style.overflow = 'hidden'\n\n    this.domElement = domElement\n\n    this.getSize = function () {\n      return {\n        width: _width,\n        height: _height,\n      }\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _viewMatrix.copy(camera.matrixWorldInverse)\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix)\n\n      renderObject(scene, scene, camera)\n      zOrder(scene)\n    }\n\n    this.setSize = function (width, height) {\n      _width = width\n      _height = height\n\n      _widthHalf = _width / 2\n      _heightHalf = _height / 2\n\n      domElement.style.width = width + 'px'\n      domElement.style.height = height + 'px'\n    }\n\n    function renderObject(object, scene, camera) {\n      if (object.isCSS2DObject) {\n        _vector.setFromMatrixPosition(object.matrixWorld)\n        _vector.applyMatrix4(_viewProjectionMatrix)\n\n        const visible =\n          object.visible === true && _vector.z >= -1 && _vector.z <= 1 && object.layers.test(camera.layers) === true\n        object.element.style.display = visible === true ? '' : 'none'\n\n        if (visible === true) {\n          object.onBeforeRender(_this, scene, camera)\n\n          const element = object.element\n\n          element.style.transform =\n            'translate(' +\n            -100 * object.center.x +\n            '%,' +\n            -100 * object.center.y +\n            '%)' +\n            'translate(' +\n            (_vector.x * _widthHalf + _widthHalf) +\n            'px,' +\n            (-_vector.y * _heightHalf + _heightHalf) +\n            'px)'\n\n          if (element.parentNode !== domElement) {\n            domElement.appendChild(element)\n          }\n\n          object.onAfterRender(_this, scene, camera)\n        }\n\n        const objectData = {\n          distanceToCameraSquared: getDistanceToSquared(camera, object),\n        }\n\n        cache.objects.set(object, objectData)\n      }\n\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        renderObject(object.children[i], scene, camera)\n      }\n    }\n\n    function getDistanceToSquared(object1, object2) {\n      _a.setFromMatrixPosition(object1.matrixWorld)\n      _b.setFromMatrixPosition(object2.matrixWorld)\n\n      return _a.distanceToSquared(_b)\n    }\n\n    function filterAndFlatten(scene) {\n      const result = []\n\n      scene.traverse(function (object) {\n        if (object.isCSS2DObject) result.push(object)\n      })\n\n      return result\n    }\n\n    function zOrder(scene) {\n      const sorted = filterAndFlatten(scene).sort(function (a, b) {\n        if (a.renderOrder !== b.renderOrder) {\n          return b.renderOrder - a.renderOrder\n        }\n\n        const distanceA = cache.objects.get(a).distanceToCameraSquared\n        const distanceB = cache.objects.get(b).distanceToCameraSquared\n\n        return distanceA - distanceB\n      })\n\n      const zMax = sorted.length\n\n      for (let i = 0, l = sorted.length; i < l; i++) {\n        sorted[i].element.style.zIndex = zMax - i\n      }\n    }\n  }\n}\n\nexport { CSS2DObject, CSS2DRenderer }\n"], "mappings": ";AAEA,MAAMA,WAAA,SAAoBC,QAAA,CAAS;EACjCC,YAAYC,OAAA,GAAUC,QAAA,CAASC,aAAA,CAAc,KAAK,GAAG;IACnD,MAAO;IAEP,KAAKC,aAAA,GAAgB;IAErB,KAAKH,OAAA,GAAUA,OAAA;IAEf,KAAKA,OAAA,CAAQI,KAAA,CAAMC,QAAA,GAAW;IAC9B,KAAKL,OAAA,CAAQI,KAAA,CAAME,UAAA,GAAa;IAEhC,KAAKN,OAAA,CAAQO,YAAA,CAAa,aAAa,KAAK;IAE5C,KAAKC,MAAA,GAAS,IAAIC,OAAA,CAAQ,KAAK,GAAG;IAElC,KAAKC,gBAAA,CAAiB,WAAW,YAAY;MAC3C,KAAKC,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC9B,IAAIA,MAAA,CAAOZ,OAAA,YAAmBa,OAAA,IAAWD,MAAA,CAAOZ,OAAA,CAAQc,UAAA,KAAe,MAAM;UAC3EF,MAAA,CAAOZ,OAAA,CAAQc,UAAA,CAAWC,WAAA,CAAYH,MAAA,CAAOZ,OAAO;QACrD;MACT,CAAO;IACP,CAAK;EACF;EAEDgB,KAAKC,MAAA,EAAQC,SAAA,EAAW;IACtB,MAAMF,IAAA,CAAKC,MAAA,EAAQC,SAAS;IAE5B,KAAKlB,OAAA,GAAUiB,MAAA,CAAOjB,OAAA,CAAQmB,SAAA,CAAU,IAAI;IAE5C,KAAKX,MAAA,GAASS,MAAA,CAAOT,MAAA;IAErB,OAAO;EACR;AACH;AAEA,MAAMY,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAC7C,MAAMC,WAAA,GAA8B,mBAAIC,OAAA,CAAS;AACjD,MAAMC,qBAAA,GAAwC,mBAAID,OAAA,CAAS;AAC3D,MAAME,EAAA,GAAqB,mBAAIJ,OAAA,CAAS;AACxC,MAAMK,EAAA,GAAqB,mBAAIL,OAAA,CAAS;AAExC,MAAMM,aAAA,CAAc;EAClB5B,YAAY6B,UAAA,GAAa,IAAI;IAC3B,MAAMC,KAAA,GAAQ;IAEd,IAAIC,MAAA,EAAQC,OAAA;IACZ,IAAIC,UAAA,EAAYC,WAAA;IAEhB,MAAMC,KAAA,GAAQ;MACZC,OAAA,EAAS,mBAAIC,OAAA,CAAS;IACvB;IAED,MAAMC,UAAA,GAAaT,UAAA,CAAW5B,OAAA,KAAY,SAAY4B,UAAA,CAAW5B,OAAA,GAAUC,QAAA,CAASC,aAAA,CAAc,KAAK;IAEvGmC,UAAA,CAAWjC,KAAA,CAAMkC,QAAA,GAAW;IAE5B,KAAKD,UAAA,GAAaA,UAAA;IAElB,KAAKE,OAAA,GAAU,YAAY;MACzB,OAAO;QACLC,KAAA,EAAOV,MAAA;QACPW,MAAA,EAAQV;MACT;IACF;IAED,KAAKW,MAAA,GAAS,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACrC,IAAID,KAAA,CAAME,qBAAA,KAA0B,MAAMF,KAAA,CAAMG,iBAAA,CAAmB;MACnE,IAAIF,MAAA,CAAOG,MAAA,KAAW,QAAQH,MAAA,CAAOC,qBAAA,KAA0B,MAAMD,MAAA,CAAOE,iBAAA,CAAmB;MAE/FxB,WAAA,CAAYN,IAAA,CAAK4B,MAAA,CAAOI,kBAAkB;MAC1CxB,qBAAA,CAAsByB,gBAAA,CAAiBL,MAAA,CAAOM,gBAAA,EAAkB5B,WAAW;MAE3E6B,YAAA,CAAaR,KAAA,EAAOA,KAAA,EAAOC,MAAM;MACjCQ,MAAA,CAAOT,KAAK;IACb;IAED,KAAKU,OAAA,GAAU,UAAUb,KAAA,EAAOC,MAAA,EAAQ;MACtCX,MAAA,GAASU,KAAA;MACTT,OAAA,GAAUU,MAAA;MAEVT,UAAA,GAAaF,MAAA,GAAS;MACtBG,WAAA,GAAcF,OAAA,GAAU;MAExBM,UAAA,CAAWjC,KAAA,CAAMoC,KAAA,GAAQA,KAAA,GAAQ;MACjCH,UAAA,CAAWjC,KAAA,CAAMqC,MAAA,GAASA,MAAA,GAAS;IACpC;IAED,SAASU,aAAavC,MAAA,EAAQ+B,KAAA,EAAOC,MAAA,EAAQ;MAC3C,IAAIhC,MAAA,CAAOT,aAAA,EAAe;QACxBiB,OAAA,CAAQkC,qBAAA,CAAsB1C,MAAA,CAAO2C,WAAW;QAChDnC,OAAA,CAAQoC,YAAA,CAAahC,qBAAqB;QAE1C,MAAMiC,OAAA,GACJ7C,MAAA,CAAO6C,OAAA,KAAY,QAAQrC,OAAA,CAAQsC,CAAA,IAAK,MAAMtC,OAAA,CAAQsC,CAAA,IAAK,KAAK9C,MAAA,CAAO+C,MAAA,CAAOC,IAAA,CAAKhB,MAAA,CAAOe,MAAM,MAAM;QACxG/C,MAAA,CAAOZ,OAAA,CAAQI,KAAA,CAAMyD,OAAA,GAAUJ,OAAA,KAAY,OAAO,KAAK;QAEvD,IAAIA,OAAA,KAAY,MAAM;UACpB7C,MAAA,CAAOkD,cAAA,CAAejC,KAAA,EAAOc,KAAA,EAAOC,MAAM;UAE1C,MAAM5C,OAAA,GAAUY,MAAA,CAAOZ,OAAA;UAEvBA,OAAA,CAAQI,KAAA,CAAM2D,SAAA,GACZ,eACA,OAAOnD,MAAA,CAAOJ,MAAA,CAAOwD,CAAA,GACrB,OACA,OAAOpD,MAAA,CAAOJ,MAAA,CAAOyD,CAAA,GACrB,kBAEC7C,OAAA,CAAQ4C,CAAA,GAAIhC,UAAA,GAAaA,UAAA,IAC1B,SACC,CAACZ,OAAA,CAAQ6C,CAAA,GAAIhC,WAAA,GAAcA,WAAA,IAC5B;UAEF,IAAIjC,OAAA,CAAQc,UAAA,KAAeuB,UAAA,EAAY;YACrCA,UAAA,CAAW6B,WAAA,CAAYlE,OAAO;UAC/B;UAEDY,MAAA,CAAOuD,aAAA,CAActC,KAAA,EAAOc,KAAA,EAAOC,MAAM;QAC1C;QAED,MAAMwB,UAAA,GAAa;UACjBC,uBAAA,EAAyBC,oBAAA,CAAqB1B,MAAA,EAAQhC,MAAM;QAC7D;QAEDsB,KAAA,CAAMC,OAAA,CAAQoC,GAAA,CAAI3D,MAAA,EAAQwD,UAAU;MACrC;MAED,SAASI,CAAA,GAAI,GAAGC,CAAA,GAAI7D,MAAA,CAAO8D,QAAA,CAASC,MAAA,EAAQH,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACtDrB,YAAA,CAAavC,MAAA,CAAO8D,QAAA,CAASF,CAAC,GAAG7B,KAAA,EAAOC,MAAM;MAC/C;IACF;IAED,SAAS0B,qBAAqBM,OAAA,EAASC,OAAA,EAAS;MAC9CpD,EAAA,CAAG6B,qBAAA,CAAsBsB,OAAA,CAAQrB,WAAW;MAC5C7B,EAAA,CAAG4B,qBAAA,CAAsBuB,OAAA,CAAQtB,WAAW;MAE5C,OAAO9B,EAAA,CAAGqD,iBAAA,CAAkBpD,EAAE;IAC/B;IAED,SAASqD,iBAAiBpC,KAAA,EAAO;MAC/B,MAAMqC,MAAA,GAAS,EAAE;MAEjBrC,KAAA,CAAMhC,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC/B,IAAIA,MAAA,CAAOT,aAAA,EAAe6E,MAAA,CAAOC,IAAA,CAAKrE,MAAM;MACpD,CAAO;MAED,OAAOoE,MAAA;IACR;IAED,SAAS5B,OAAOT,KAAA,EAAO;MACrB,MAAMuC,MAAA,GAASH,gBAAA,CAAiBpC,KAAK,EAAEwC,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;QAC1D,IAAID,CAAA,CAAEE,WAAA,KAAgBD,CAAA,CAAEC,WAAA,EAAa;UACnC,OAAOD,CAAA,CAAEC,WAAA,GAAcF,CAAA,CAAEE,WAAA;QAC1B;QAED,MAAMC,SAAA,GAAYrD,KAAA,CAAMC,OAAA,CAAQqD,GAAA,CAAIJ,CAAC,EAAEf,uBAAA;QACvC,MAAMoB,SAAA,GAAYvD,KAAA,CAAMC,OAAA,CAAQqD,GAAA,CAAIH,CAAC,EAAEhB,uBAAA;QAEvC,OAAOkB,SAAA,GAAYE,SAAA;MAC3B,CAAO;MAED,MAAMC,IAAA,GAAOR,MAAA,CAAOP,MAAA;MAEpB,SAASH,CAAA,GAAI,GAAGC,CAAA,GAAIS,MAAA,CAAOP,MAAA,EAAQH,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7CU,MAAA,CAAOV,CAAC,EAAExE,OAAA,CAAQI,KAAA,CAAMuF,MAAA,GAASD,IAAA,GAAOlB,CAAA;MACzC;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}