{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-reconciler.production.min.js');\n} else {\n  module.exports = require('./cjs/react-reconciler.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/react-reconciler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-reconciler.production.min.js');\n} else {\n  module.exports = require('./cjs/react-reconciler.development.js');\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,0CAA0C,CAAC;AACtE,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,uCAAuC,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}