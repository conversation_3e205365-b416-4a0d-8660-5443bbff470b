{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\common\\\\Logo.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Logo = ({\n  className = \"\",\n  size = \"default\"\n}) => {\n  const sizes = {\n    small: {\n      width: 120,\n      height: 40\n    },\n    default: {\n      width: 180,\n      height: 60\n    },\n    large: {\n      width: 240,\n      height: 80\n    }\n  };\n  const {\n    width,\n    height\n  } = sizes[size] || sizes.default;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `logo-container ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"100%\",\n      height: \"100%\",\n      viewBox: \"0 0 240 80\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      className: \"design-excellence-logo\",\n      preserveAspectRatio: \"xMidYMid meet\",\n      style: {\n        maxWidth: `${width}px`,\n        maxHeight: `${height}px`,\n        width: '100%',\n        height: 'auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"g\", {\n        className: \"dc-letters\",\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M8 15 L8 65 L25 65 C35 65 42 58 42 40 C42 22 35 15 25 15 Z M18 25 L25 25 C28 25 32 27 32 40 C32 53 28 55 25 55 L18 55 Z\",\n          fill: \"#4A4A4A\",\n          strokeWidth: \"1\",\n          stroke: \"#4A4A4A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M50 25 C55 20 62 20 67 25 L72 18 C65 12 55 12 50 18 C45 24 45 36 45 40 C45 44 45 56 50 62 C55 68 65 68 72 62 L67 55 C62 60 55 60 50 55 C47 52 47 48 47 40 C47 32 47 28 50 25 Z\",\n          fill: \"#F59E0B\",\n          strokeWidth: \"1\",\n          stroke: \"#F59E0B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n        className: \"main-text\",\n        children: [/*#__PURE__*/_jsxDEV(\"text\", {\n          x: \"95\",\n          y: \"35\",\n          fontSize: \"16\",\n          fontWeight: \"bold\",\n          fill: \"#4A4A4A\",\n          fontFamily: \"Arial, sans-serif\",\n          children: \"DESIGN\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n          x: \"175\",\n          y: \"35\",\n          fontSize: \"16\",\n          fontWeight: \"bold\",\n          fill: \"#F59E0B\",\n          fontFamily: \"Arial, sans-serif\",\n          children: \"EXCELLENCE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"95\",\n        y1: \"45\",\n        x2: \"235\",\n        y2: \"45\",\n        stroke: \"#4A4A4A\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n        x: \"95\",\n        y: \"62\",\n        fontSize: \"10\",\n        fill: \"#4A4A4A\",\n        fontFamily: \"Arial, sans-serif\",\n        letterSpacing: \"2\",\n        children: \"HOME & OFFICE SYSTEM CO.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 9\n  }, this);\n};\n_c = Logo;\nexport default Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Logo", "className", "size", "sizes", "small", "width", "height", "default", "large", "children", "viewBox", "xmlns", "preserveAspectRatio", "style", "max<PERSON><PERSON><PERSON>", "maxHeight", "d", "fill", "strokeWidth", "stroke", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "y", "fontSize", "fontWeight", "fontFamily", "x1", "y1", "x2", "y2", "letterSpacing", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/common/Logo.js"], "sourcesContent": ["import React from 'react';\n\nconst Logo = ({ className = \"\", size = \"default\" }) => {\n    const sizes = {\n        small: { width: 120, height: 40 },\n        default: { width: 180, height: 60 },\n        large: { width: 240, height: 80 }\n    };\n\n    const { width, height } = sizes[size] || sizes.default;\n\n    return (\n        <div className={`logo-container ${className}`}>\n            <svg\n                width=\"100%\"\n                height=\"100%\"\n                viewBox=\"0 0 240 80\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"design-excellence-logo\"\n                preserveAspectRatio=\"xMidYMid meet\"\n                style={{\n                    maxWidth: `${width}px`,\n                    maxHeight: `${height}px`,\n                    width: '100%',\n                    height: 'auto'\n                }}\n            >\n                {/* DC Letters */}\n                <g className=\"dc-letters\">\n                    {/* D Letter */}\n                    <path \n                        d=\"M8 15 L8 65 L25 65 C35 65 42 58 42 40 C42 22 35 15 25 15 Z M18 25 L25 25 C28 25 32 27 32 40 C32 53 28 55 25 55 L18 55 Z\" \n                        fill=\"#4A4A4A\"\n                        strokeWidth=\"1\"\n                        stroke=\"#4A4A4A\"\n                    />\n                    \n                    {/* C Letter */}\n                    <path \n                        d=\"M50 25 C55 20 62 20 67 25 L72 18 C65 12 55 12 50 18 C45 24 45 36 45 40 C45 44 45 56 50 62 C55 68 65 68 72 62 L67 55 C62 60 55 60 50 55 C47 52 47 48 47 40 C47 32 47 28 50 25 Z\" \n                        fill=\"#F59E0B\"\n                        strokeWidth=\"1\"\n                        stroke=\"#F59E0B\"\n                    />\n                </g>\n\n                {/* DESIGN EXCELLENCE Text */}\n                <g className=\"main-text\">\n                    <text x=\"95\" y=\"35\" fontSize=\"16\" fontWeight=\"bold\" fill=\"#4A4A4A\" fontFamily=\"Arial, sans-serif\">\n                        DESIGN\n                    </text>\n                    <text x=\"175\" y=\"35\" fontSize=\"16\" fontWeight=\"bold\" fill=\"#F59E0B\" fontFamily=\"Arial, sans-serif\">\n                        EXCELLENCE\n                    </text>\n                </g>\n\n                {/* Horizontal Line */}\n                <line x1=\"95\" y1=\"45\" x2=\"235\" y2=\"45\" stroke=\"#4A4A4A\" strokeWidth=\"2\"/>\n\n                {/* Subtitle */}\n                <text x=\"95\" y=\"62\" fontSize=\"10\" fill=\"#4A4A4A\" fontFamily=\"Arial, sans-serif\" letterSpacing=\"2\">\n                    HOME & OFFICE SYSTEM CO.\n                </text>\n            </svg>\n        </div>\n    );\n};\n\nexport default Logo;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAU,CAAC,KAAK;EACnD,MAAMC,KAAK,GAAG;IACVC,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAC;IACjCC,OAAO,EAAE;MAAEF,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG,CAAC;IACnCE,KAAK,EAAE;MAAEH,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAG;EACpC,CAAC;EAED,MAAM;IAAED,KAAK;IAAEC;EAAO,CAAC,GAAGH,KAAK,CAACD,IAAI,CAAC,IAAIC,KAAK,CAACI,OAAO;EAEtD,oBACIR,OAAA;IAAKE,SAAS,EAAE,kBAAkBA,SAAS,EAAG;IAAAQ,QAAA,eAC1CV,OAAA;MACIM,KAAK,EAAC,MAAM;MACZC,MAAM,EAAC,MAAM;MACbI,OAAO,EAAC,YAAY;MACpBC,KAAK,EAAC,4BAA4B;MAClCV,SAAS,EAAC,wBAAwB;MAClCW,mBAAmB,EAAC,eAAe;MACnCC,KAAK,EAAE;QACHC,QAAQ,EAAE,GAAGT,KAAK,IAAI;QACtBU,SAAS,EAAE,GAAGT,MAAM,IAAI;QACxBD,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACZ,CAAE;MAAAG,QAAA,gBAGFV,OAAA;QAAGE,SAAS,EAAC,YAAY;QAAAQ,QAAA,gBAErBV,OAAA;UACIiB,CAAC,EAAC,yHAAyH;UAC3HC,IAAI,EAAC,SAAS;UACdC,WAAW,EAAC,GAAG;UACfC,MAAM,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGFxB,OAAA;UACIiB,CAAC,EAAC,gLAAgL;UAClLC,IAAI,EAAC,SAAS;UACdC,WAAW,EAAC,GAAG;UACfC,MAAM,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGJxB,OAAA;QAAGE,SAAS,EAAC,WAAW;QAAAQ,QAAA,gBACpBV,OAAA;UAAMyB,CAAC,EAAC,IAAI;UAACC,CAAC,EAAC,IAAI;UAACC,QAAQ,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAACV,IAAI,EAAC,SAAS;UAACW,UAAU,EAAC,mBAAmB;UAAAnB,QAAA,EAAC;QAElG;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPxB,OAAA;UAAMyB,CAAC,EAAC,KAAK;UAACC,CAAC,EAAC,IAAI;UAACC,QAAQ,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAACV,IAAI,EAAC,SAAS;UAACW,UAAU,EAAC,mBAAmB;UAAAnB,QAAA,EAAC;QAEnG;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGJxB,OAAA;QAAM8B,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,KAAK;QAACC,EAAE,EAAC,IAAI;QAACb,MAAM,EAAC,SAAS;QAACD,WAAW,EAAC;MAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAGzExB,OAAA;QAAMyB,CAAC,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACC,QAAQ,EAAC,IAAI;QAACT,IAAI,EAAC,SAAS;QAACW,UAAU,EAAC,mBAAmB;QAACK,aAAa,EAAC,GAAG;QAAAxB,QAAA,EAAC;MAElG;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACW,EAAA,GAhEIlC,IAAI;AAkEV,eAAeA,IAAI;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}