{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { DepthTexture, DepthStencilFormat, UnsignedInt248Type, WebGLRenderTarget, NearestFilter, ShaderMaterial, UniformsUtils, NoBlending, MeshNormalMaterial, DstColorFactor, ZeroFactor, AddEquation, DstAlphaFactor, Color, CustomBlending, Vector3, MathUtils, DataTexture, RedFormat, FloatType, RepeatWrapping } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { SimplexNoise } from \"../math/SimplexNoise.js\";\nimport { SSAOShader, SSAOBlurShader, SSAODepthShader } from \"../shaders/SSAOShader.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nconst SSAOPass = /* @__PURE__ */(() => {\n  const _SSAOPass = class extends Pass {\n    constructor(scene, camera, width, height) {\n      super();\n      this.width = width !== void 0 ? width : 512;\n      this.height = height !== void 0 ? height : 512;\n      this.clear = true;\n      this.camera = camera;\n      this.scene = scene;\n      this.kernelRadius = 8;\n      this.kernelSize = 32;\n      this.kernel = [];\n      this.noiseTexture = null;\n      this.output = 0;\n      this.minDistance = 5e-3;\n      this.maxDistance = 0.1;\n      this._visibilityCache = /* @__PURE__ */new Map();\n      this.generateSampleKernel();\n      this.generateRandomKernelRotations();\n      const depthTexture = new DepthTexture();\n      depthTexture.format = DepthStencilFormat;\n      depthTexture.type = UnsignedInt248Type;\n      this.beautyRenderTarget = new WebGLRenderTarget(this.width, this.height);\n      this.normalRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        depthTexture\n      });\n      this.ssaoRenderTarget = new WebGLRenderTarget(this.width, this.height);\n      this.blurRenderTarget = this.ssaoRenderTarget.clone();\n      if (SSAOShader === void 0) {\n        console.error(\"THREE.SSAOPass: The pass relies on SSAOShader.\");\n      }\n      this.ssaoMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAOShader.defines),\n        uniforms: UniformsUtils.clone(SSAOShader.uniforms),\n        vertexShader: SSAOShader.vertexShader,\n        fragmentShader: SSAOShader.fragmentShader,\n        blending: NoBlending\n      });\n      this.ssaoMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n      this.ssaoMaterial.uniforms[\"tNormal\"].value = this.normalRenderTarget.texture;\n      this.ssaoMaterial.uniforms[\"tDepth\"].value = this.normalRenderTarget.depthTexture;\n      this.ssaoMaterial.uniforms[\"tNoise\"].value = this.noiseTexture;\n      this.ssaoMaterial.uniforms[\"kernel\"].value = this.kernel;\n      this.ssaoMaterial.uniforms[\"cameraNear\"].value = this.camera.near;\n      this.ssaoMaterial.uniforms[\"cameraFar\"].value = this.camera.far;\n      this.ssaoMaterial.uniforms[\"resolution\"].value.set(this.width, this.height);\n      this.ssaoMaterial.uniforms[\"cameraProjectionMatrix\"].value.copy(this.camera.projectionMatrix);\n      this.ssaoMaterial.uniforms[\"cameraInverseProjectionMatrix\"].value.copy(this.camera.projectionMatrixInverse);\n      this.normalMaterial = new MeshNormalMaterial();\n      this.normalMaterial.blending = NoBlending;\n      this.blurMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAOBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSAOBlurShader.uniforms),\n        vertexShader: SSAOBlurShader.vertexShader,\n        fragmentShader: SSAOBlurShader.fragmentShader\n      });\n      this.blurMaterial.uniforms[\"tDiffuse\"].value = this.ssaoRenderTarget.texture;\n      this.blurMaterial.uniforms[\"resolution\"].value.set(this.width, this.height);\n      this.depthRenderMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAODepthShader.defines),\n        uniforms: UniformsUtils.clone(SSAODepthShader.uniforms),\n        vertexShader: SSAODepthShader.vertexShader,\n        fragmentShader: SSAODepthShader.fragmentShader,\n        blending: NoBlending\n      });\n      this.depthRenderMaterial.uniforms[\"tDepth\"].value = this.normalRenderTarget.depthTexture;\n      this.depthRenderMaterial.uniforms[\"cameraNear\"].value = this.camera.near;\n      this.depthRenderMaterial.uniforms[\"cameraFar\"].value = this.camera.far;\n      this.copyMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        transparent: true,\n        depthTest: false,\n        depthWrite: false,\n        blendSrc: DstColorFactor,\n        blendDst: ZeroFactor,\n        blendEquation: AddEquation,\n        blendSrcAlpha: DstAlphaFactor,\n        blendDstAlpha: ZeroFactor,\n        blendEquationAlpha: AddEquation\n      });\n      this.fsQuad = new FullScreenQuad(null);\n      this.originalClearColor = new Color();\n    }\n    dispose() {\n      this.beautyRenderTarget.dispose();\n      this.normalRenderTarget.dispose();\n      this.ssaoRenderTarget.dispose();\n      this.blurRenderTarget.dispose();\n      this.normalMaterial.dispose();\n      this.blurMaterial.dispose();\n      this.copyMaterial.dispose();\n      this.depthRenderMaterial.dispose();\n      this.fsQuad.dispose();\n    }\n    render(renderer, writeBuffer) {\n      renderer.setRenderTarget(this.beautyRenderTarget);\n      renderer.clear();\n      renderer.render(this.scene, this.camera);\n      this.overrideVisibility();\n      this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 7829503, 1);\n      this.restoreVisibility();\n      this.ssaoMaterial.uniforms[\"kernelRadius\"].value = this.kernelRadius;\n      this.ssaoMaterial.uniforms[\"minDistance\"].value = this.minDistance;\n      this.ssaoMaterial.uniforms[\"maxDistance\"].value = this.maxDistance;\n      this.renderPass(renderer, this.ssaoMaterial, this.ssaoRenderTarget);\n      this.renderPass(renderer, this.blurMaterial, this.blurRenderTarget);\n      switch (this.output) {\n        case _SSAOPass.OUTPUT.SSAO:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.ssaoRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSAOPass.OUTPUT.Blur:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.blurRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSAOPass.OUTPUT.Beauty:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSAOPass.OUTPUT.Depth:\n          this.renderPass(renderer, this.depthRenderMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSAOPass.OUTPUT.Normal:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.normalRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        case _SSAOPass.OUTPUT.Default:\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.beautyRenderTarget.texture;\n          this.copyMaterial.blending = NoBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          this.copyMaterial.uniforms[\"tDiffuse\"].value = this.blurRenderTarget.texture;\n          this.copyMaterial.blending = CustomBlending;\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer);\n          break;\n        default:\n          console.warn(\"THREE.SSAOPass: Unknown output type.\");\n      }\n    }\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor);\n      const originalClearAlpha = renderer.getClearAlpha();\n      const originalAutoClear = renderer.autoClear;\n      renderer.setRenderTarget(renderTarget);\n      renderer.autoClear = false;\n      if (clearColor !== void 0 && clearColor !== null) {\n        renderer.setClearColor(clearColor);\n        renderer.setClearAlpha(clearAlpha || 0);\n        renderer.clear();\n      }\n      this.fsQuad.material = passMaterial;\n      this.fsQuad.render(renderer);\n      renderer.autoClear = originalAutoClear;\n      renderer.setClearColor(this.originalClearColor);\n      renderer.setClearAlpha(originalClearAlpha);\n    }\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor);\n      const originalClearAlpha = renderer.getClearAlpha();\n      const originalAutoClear = renderer.autoClear;\n      renderer.setRenderTarget(renderTarget);\n      renderer.autoClear = false;\n      clearColor = overrideMaterial.clearColor || clearColor;\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha;\n      if (clearColor !== void 0 && clearColor !== null) {\n        renderer.setClearColor(clearColor);\n        renderer.setClearAlpha(clearAlpha || 0);\n        renderer.clear();\n      }\n      this.scene.overrideMaterial = overrideMaterial;\n      renderer.render(this.scene, this.camera);\n      this.scene.overrideMaterial = null;\n      renderer.autoClear = originalAutoClear;\n      renderer.setClearColor(this.originalClearColor);\n      renderer.setClearAlpha(originalClearAlpha);\n    }\n    setSize(width, height) {\n      this.width = width;\n      this.height = height;\n      this.beautyRenderTarget.setSize(width, height);\n      this.ssaoRenderTarget.setSize(width, height);\n      this.normalRenderTarget.setSize(width, height);\n      this.blurRenderTarget.setSize(width, height);\n      this.ssaoMaterial.uniforms[\"resolution\"].value.set(width, height);\n      this.ssaoMaterial.uniforms[\"cameraProjectionMatrix\"].value.copy(this.camera.projectionMatrix);\n      this.ssaoMaterial.uniforms[\"cameraInverseProjectionMatrix\"].value.copy(this.camera.projectionMatrixInverse);\n      this.blurMaterial.uniforms[\"resolution\"].value.set(width, height);\n    }\n    generateSampleKernel() {\n      const kernelSize = this.kernelSize;\n      const kernel = this.kernel;\n      for (let i = 0; i < kernelSize; i++) {\n        const sample = new Vector3();\n        sample.x = Math.random() * 2 - 1;\n        sample.y = Math.random() * 2 - 1;\n        sample.z = Math.random();\n        sample.normalize();\n        let scale = i / kernelSize;\n        scale = MathUtils.lerp(0.1, 1, scale * scale);\n        sample.multiplyScalar(scale);\n        kernel.push(sample);\n      }\n    }\n    generateRandomKernelRotations() {\n      const width = 4,\n        height = 4;\n      if (SimplexNoise === void 0) {\n        console.error(\"THREE.SSAOPass: The pass relies on SimplexNoise.\");\n      }\n      const simplex = new SimplexNoise();\n      const size = width * height;\n      const data = new Float32Array(size);\n      for (let i = 0; i < size; i++) {\n        const x = Math.random() * 2 - 1;\n        const y = Math.random() * 2 - 1;\n        const z = 0;\n        data[i] = simplex.noise3d(x, y, z);\n      }\n      this.noiseTexture = new DataTexture(data, width, height, RedFormat, FloatType);\n      this.noiseTexture.wrapS = RepeatWrapping;\n      this.noiseTexture.wrapT = RepeatWrapping;\n      this.noiseTexture.needsUpdate = true;\n    }\n    overrideVisibility() {\n      const scene = this.scene;\n      const cache = this._visibilityCache;\n      scene.traverse(function (object) {\n        cache.set(object, object.visible);\n        if (object.isPoints || object.isLine) object.visible = false;\n      });\n    }\n    restoreVisibility() {\n      const scene = this.scene;\n      const cache = this._visibilityCache;\n      scene.traverse(function (object) {\n        const visible = cache.get(object);\n        object.visible = visible;\n      });\n      cache.clear();\n    }\n  };\n  let SSAOPass2 = _SSAOPass;\n  __publicField(SSAOPass2, \"OUTPUT\", {\n    Default: 0,\n    SSAO: 1,\n    Blur: 2,\n    Beauty: 3,\n    Depth: 4,\n    Normal: 5\n  });\n  return SSAOPass2;\n})();\nexport { SSAOPass };", "map": {"version": 3, "names": ["SSAOPass", "_SSAOPass", "Pass", "constructor", "scene", "camera", "width", "height", "clear", "kernelRadius", "kernelSize", "kernel", "noiseTexture", "output", "minDistance", "maxDistance", "_visibilityCache", "Map", "generateSampleKernel", "generateRandomKernelRotations", "depthTexture", "DepthTexture", "format", "DepthStencilFormat", "type", "UnsignedInt248Type", "beautyR<PERSON><PERSON><PERSON><PERSON>", "WebGLRenderTarget", "normalRenderTarget", "minFilter", "NearestFilter", "magFilter", "ssaoRenderTarget", "blurRenderTarget", "clone", "SSA<PERSON><PERSON><PERSON>", "console", "error", "ssaoMaterial", "ShaderMaterial", "defines", "Object", "assign", "uniforms", "UniformsUtils", "vertexShader", "fragmentShader", "blending", "NoBlending", "value", "texture", "near", "far", "set", "copy", "projectionMatrix", "projectionMatrixInverse", "normalMaterial", "MeshNormalMaterial", "blurMaterial", "SSAOB<PERSON>r<PERSON><PERSON><PERSON>", "depthRenderMaterial", "SSAOD<PERSON><PERSON><PERSON><PERSON>", "copyMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transparent", "depthTest", "depthWrite", "blendSrc", "DstColorFactor", "blendDst", "ZeroFactor", "blendEquation", "AddEquation", "blendSrcAlpha", "DstAlphaFactor", "blendDstAlpha", "blendEquationAlpha", "fsQuad", "FullScreenQuad", "originalClearColor", "Color", "dispose", "render", "renderer", "writeBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overrideVisibility", "renderOverride", "restoreVisibility", "renderPass", "OUTPUT", "SSAO", "renderToScreen", "Blur", "Beauty", "De<PERSON><PERSON>", "Normal", "<PERSON><PERSON><PERSON>", "CustomBlending", "warn", "passMaterial", "renderTarget", "clearColor", "clearAlpha", "getClearColor", "originalClearAlpha", "getClearAlpha", "originalAutoClear", "autoClear", "setClearColor", "setClearAlpha", "material", "overrideMaterial", "setSize", "i", "sample", "Vector3", "x", "Math", "random", "y", "z", "normalize", "scale", "MathUtils", "lerp", "multiplyScalar", "push", "SimplexNoise", "simplex", "size", "data", "Float32Array", "noise3d", "DataTexture", "RedFormat", "FloatType", "wrapS", "RepeatWrapping", "wrapT", "needsUpdate", "cache", "traverse", "object", "visible", "isPoints", "isLine", "get", "SSAOPass2", "__publicField"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\SSAOPass.js"], "sourcesContent": ["import {\n  AddEquation,\n  Color,\n  CustomBlending,\n  DataTexture,\n  DepthTexture,\n  DstAlphaFactor,\n  DstColorFactor,\n  FloatType,\n  MathUtils,\n  MeshNormalMaterial,\n  NearestFilter,\n  NoBlending,\n  RedFormat,\n  DepthStencilFormat,\n  UnsignedInt248Type,\n  RepeatWrapping,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector3,\n  WebGLRenderTarget,\n  ZeroFactor,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { SimplexNoise } from '../math/SimplexNoise'\nimport { SSAOShader, SSAOBlurShader, SSAODepthShader } from '../shaders/SSAOShader'\n\nimport { CopyShader } from '../shaders/CopyShader'\n\nconst SSAOPass = /* @__PURE__ */ (() => {\n  class SSAOPass extends Pass {\n    static OUTPUT = {\n      Default: 0,\n      SSAO: 1,\n      Blur: 2,\n      Beauty: 3,\n      Depth: 4,\n      Normal: 5,\n    }\n\n    constructor(scene, camera, width, height) {\n      super()\n\n      this.width = width !== undefined ? width : 512\n      this.height = height !== undefined ? height : 512\n\n      this.clear = true\n\n      this.camera = camera\n      this.scene = scene\n\n      this.kernelRadius = 8\n      this.kernelSize = 32\n      this.kernel = []\n      this.noiseTexture = null\n      this.output = 0\n\n      this.minDistance = 0.005\n      this.maxDistance = 0.1\n\n      this._visibilityCache = new Map()\n\n      //\n\n      this.generateSampleKernel()\n      this.generateRandomKernelRotations()\n\n      // beauty render target\n\n      const depthTexture = new DepthTexture()\n      depthTexture.format = DepthStencilFormat\n      depthTexture.type = UnsignedInt248Type\n\n      this.beautyRenderTarget = new WebGLRenderTarget(this.width, this.height)\n\n      // normal render target with depth buffer\n\n      this.normalRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        depthTexture: depthTexture,\n      })\n\n      // ssao render target\n\n      this.ssaoRenderTarget = new WebGLRenderTarget(this.width, this.height)\n\n      this.blurRenderTarget = this.ssaoRenderTarget.clone()\n\n      // ssao material\n\n      if (SSAOShader === undefined) {\n        console.error('THREE.SSAOPass: The pass relies on SSAOShader.')\n      }\n\n      this.ssaoMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAOShader.defines),\n        uniforms: UniformsUtils.clone(SSAOShader.uniforms),\n        vertexShader: SSAOShader.vertexShader,\n        fragmentShader: SSAOShader.fragmentShader,\n        blending: NoBlending,\n      })\n\n      this.ssaoMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n      this.ssaoMaterial.uniforms['tNormal'].value = this.normalRenderTarget.texture\n      this.ssaoMaterial.uniforms['tDepth'].value = this.normalRenderTarget.depthTexture\n      this.ssaoMaterial.uniforms['tNoise'].value = this.noiseTexture\n      this.ssaoMaterial.uniforms['kernel'].value = this.kernel\n      this.ssaoMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.ssaoMaterial.uniforms['cameraFar'].value = this.camera.far\n      this.ssaoMaterial.uniforms['resolution'].value.set(this.width, this.height)\n      this.ssaoMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssaoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      // normal material\n\n      this.normalMaterial = new MeshNormalMaterial()\n      this.normalMaterial.blending = NoBlending\n\n      // blur material\n\n      this.blurMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAOBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSAOBlurShader.uniforms),\n        vertexShader: SSAOBlurShader.vertexShader,\n        fragmentShader: SSAOBlurShader.fragmentShader,\n      })\n      this.blurMaterial.uniforms['tDiffuse'].value = this.ssaoRenderTarget.texture\n      this.blurMaterial.uniforms['resolution'].value.set(this.width, this.height)\n\n      // material for rendering the depth\n\n      this.depthRenderMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAODepthShader.defines),\n        uniforms: UniformsUtils.clone(SSAODepthShader.uniforms),\n        vertexShader: SSAODepthShader.vertexShader,\n        fragmentShader: SSAODepthShader.fragmentShader,\n        blending: NoBlending,\n      })\n      this.depthRenderMaterial.uniforms['tDepth'].value = this.normalRenderTarget.depthTexture\n      this.depthRenderMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.depthRenderMaterial.uniforms['cameraFar'].value = this.camera.far\n\n      // material for rendering the content of a render target\n\n      this.copyMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        transparent: true,\n        depthTest: false,\n        depthWrite: false,\n        blendSrc: DstColorFactor,\n        blendDst: ZeroFactor,\n        blendEquation: AddEquation,\n        blendSrcAlpha: DstAlphaFactor,\n        blendDstAlpha: ZeroFactor,\n        blendEquationAlpha: AddEquation,\n      })\n\n      this.fsQuad = new FullScreenQuad(null)\n\n      this.originalClearColor = new Color()\n    }\n\n    dispose() {\n      // dispose render targets\n\n      this.beautyRenderTarget.dispose()\n      this.normalRenderTarget.dispose()\n      this.ssaoRenderTarget.dispose()\n      this.blurRenderTarget.dispose()\n\n      // dispose materials\n\n      this.normalMaterial.dispose()\n      this.blurMaterial.dispose()\n      this.copyMaterial.dispose()\n      this.depthRenderMaterial.dispose()\n\n      // dipsose full screen quad\n\n      this.fsQuad.dispose()\n    }\n\n    render(renderer, writeBuffer /*, readBuffer, deltaTime, maskActive */) {\n      // render beauty\n\n      renderer.setRenderTarget(this.beautyRenderTarget)\n      renderer.clear()\n      renderer.render(this.scene, this.camera)\n\n      // render normals and depth (honor only meshes, points and lines do not contribute to SSAO)\n\n      this.overrideVisibility()\n      this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 0x7777ff, 1.0)\n      this.restoreVisibility()\n\n      // render SSAO\n\n      this.ssaoMaterial.uniforms['kernelRadius'].value = this.kernelRadius\n      this.ssaoMaterial.uniforms['minDistance'].value = this.minDistance\n      this.ssaoMaterial.uniforms['maxDistance'].value = this.maxDistance\n      this.renderPass(renderer, this.ssaoMaterial, this.ssaoRenderTarget)\n\n      // render blur\n\n      this.renderPass(renderer, this.blurMaterial, this.blurRenderTarget)\n\n      // output result to screen\n\n      switch (this.output) {\n        case SSAOPass.OUTPUT.SSAO:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.ssaoRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Blur:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Beauty:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Depth:\n          this.renderPass(renderer, this.depthRenderMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Normal:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.normalRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Default:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget.texture\n          this.copyMaterial.blending = CustomBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        default:\n          console.warn('THREE.SSAOPass: Unknown output type.')\n      }\n    }\n\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      // save original state\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n\n      // setup pass state\n      renderer.autoClear = false\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.material = passMaterial\n      this.fsQuad.render(renderer)\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.overrideMaterial = overrideMaterial\n      renderer.render(this.scene, this.camera)\n      this.scene.overrideMaterial = null\n\n      // restore original state\n\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    setSize(width, height) {\n      this.width = width\n      this.height = height\n\n      this.beautyRenderTarget.setSize(width, height)\n      this.ssaoRenderTarget.setSize(width, height)\n      this.normalRenderTarget.setSize(width, height)\n      this.blurRenderTarget.setSize(width, height)\n\n      this.ssaoMaterial.uniforms['resolution'].value.set(width, height)\n      this.ssaoMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssaoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      this.blurMaterial.uniforms['resolution'].value.set(width, height)\n    }\n\n    generateSampleKernel() {\n      const kernelSize = this.kernelSize\n      const kernel = this.kernel\n\n      for (let i = 0; i < kernelSize; i++) {\n        const sample = new Vector3()\n        sample.x = Math.random() * 2 - 1\n        sample.y = Math.random() * 2 - 1\n        sample.z = Math.random()\n\n        sample.normalize()\n\n        let scale = i / kernelSize\n        scale = MathUtils.lerp(0.1, 1, scale * scale)\n        sample.multiplyScalar(scale)\n\n        kernel.push(sample)\n      }\n    }\n\n    generateRandomKernelRotations() {\n      const width = 4,\n        height = 4\n\n      if (SimplexNoise === undefined) {\n        console.error('THREE.SSAOPass: The pass relies on SimplexNoise.')\n      }\n\n      const simplex = new SimplexNoise()\n\n      const size = width * height\n      const data = new Float32Array(size)\n\n      for (let i = 0; i < size; i++) {\n        const x = Math.random() * 2 - 1\n        const y = Math.random() * 2 - 1\n        const z = 0\n\n        data[i] = simplex.noise3d(x, y, z)\n      }\n\n      this.noiseTexture = new DataTexture(data, width, height, RedFormat, FloatType)\n      this.noiseTexture.wrapS = RepeatWrapping\n      this.noiseTexture.wrapT = RepeatWrapping\n      this.noiseTexture.needsUpdate = true\n    }\n\n    overrideVisibility() {\n      const scene = this.scene\n      const cache = this._visibilityCache\n\n      scene.traverse(function (object) {\n        cache.set(object, object.visible)\n\n        if (object.isPoints || object.isLine) object.visible = false\n      })\n    }\n\n    restoreVisibility() {\n      const scene = this.scene\n      const cache = this._visibilityCache\n\n      scene.traverse(function (object) {\n        const visible = cache.get(object)\n        object.visible = visible\n      })\n\n      cache.clear()\n    }\n  }\n\n  return SSAOPass\n})()\n\nexport { SSAOPass }\n"], "mappings": ";;;;;;;;;;;;;;;;AA6BK,MAACA,QAAA,GAA4B,sBAAM;EACtC,MAAMC,SAAA,GAAN,cAAuBC,IAAA,CAAK;IAU1BC,YAAYC,KAAA,EAAOC,MAAA,EAAQC,KAAA,EAAOC,MAAA,EAAQ;MACxC,MAAO;MAEP,KAAKD,KAAA,GAAQA,KAAA,KAAU,SAAYA,KAAA,GAAQ;MAC3C,KAAKC,MAAA,GAASA,MAAA,KAAW,SAAYA,MAAA,GAAS;MAE9C,KAAKC,KAAA,GAAQ;MAEb,KAAKH,MAAA,GAASA,MAAA;MACd,KAAKD,KAAA,GAAQA,KAAA;MAEb,KAAKK,YAAA,GAAe;MACpB,KAAKC,UAAA,GAAa;MAClB,KAAKC,MAAA,GAAS,EAAE;MAChB,KAAKC,YAAA,GAAe;MACpB,KAAKC,MAAA,GAAS;MAEd,KAAKC,WAAA,GAAc;MACnB,KAAKC,WAAA,GAAc;MAEnB,KAAKC,gBAAA,GAAmB,mBAAIC,GAAA,CAAK;MAIjC,KAAKC,oBAAA,CAAsB;MAC3B,KAAKC,6BAAA,CAA+B;MAIpC,MAAMC,YAAA,GAAe,IAAIC,YAAA,CAAc;MACvCD,YAAA,CAAaE,MAAA,GAASC,kBAAA;MACtBH,YAAA,CAAaI,IAAA,GAAOC,kBAAA;MAEpB,KAAKC,kBAAA,GAAqB,IAAIC,iBAAA,CAAkB,KAAKrB,KAAA,EAAO,KAAKC,MAAM;MAIvE,KAAKqB,kBAAA,GAAqB,IAAID,iBAAA,CAAkB,KAAKrB,KAAA,EAAO,KAAKC,MAAA,EAAQ;QACvEsB,SAAA,EAAWC,aAAA;QACXC,SAAA,EAAWD,aAAA;QACXV;MACR,CAAO;MAID,KAAKY,gBAAA,GAAmB,IAAIL,iBAAA,CAAkB,KAAKrB,KAAA,EAAO,KAAKC,MAAM;MAErE,KAAK0B,gBAAA,GAAmB,KAAKD,gBAAA,CAAiBE,KAAA,CAAO;MAIrD,IAAIC,UAAA,KAAe,QAAW;QAC5BC,OAAA,CAAQC,KAAA,CAAM,gDAAgD;MAC/D;MAED,KAAKC,YAAA,GAAe,IAAIC,cAAA,CAAe;QACrCC,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIP,UAAA,CAAWK,OAAO;QAC7CG,QAAA,EAAUC,aAAA,CAAcV,KAAA,CAAMC,UAAA,CAAWQ,QAAQ;QACjDE,YAAA,EAAcV,UAAA,CAAWU,YAAA;QACzBC,cAAA,EAAgBX,UAAA,CAAWW,cAAA;QAC3BC,QAAA,EAAUC;MAClB,CAAO;MAED,KAAKV,YAAA,CAAaK,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKvB,kBAAA,CAAmBwB,OAAA;MACvE,KAAKZ,YAAA,CAAaK,QAAA,CAAS,SAAS,EAAEM,KAAA,GAAQ,KAAKrB,kBAAA,CAAmBsB,OAAA;MACtE,KAAKZ,YAAA,CAAaK,QAAA,CAAS,QAAQ,EAAEM,KAAA,GAAQ,KAAKrB,kBAAA,CAAmBR,YAAA;MACrE,KAAKkB,YAAA,CAAaK,QAAA,CAAS,QAAQ,EAAEM,KAAA,GAAQ,KAAKrC,YAAA;MAClD,KAAK0B,YAAA,CAAaK,QAAA,CAAS,QAAQ,EAAEM,KAAA,GAAQ,KAAKtC,MAAA;MAClD,KAAK2B,YAAA,CAAaK,QAAA,CAAS,YAAY,EAAEM,KAAA,GAAQ,KAAK5C,MAAA,CAAO8C,IAAA;MAC7D,KAAKb,YAAA,CAAaK,QAAA,CAAS,WAAW,EAAEM,KAAA,GAAQ,KAAK5C,MAAA,CAAO+C,GAAA;MAC5D,KAAKd,YAAA,CAAaK,QAAA,CAAS,YAAY,EAAEM,KAAA,CAAMI,GAAA,CAAI,KAAK/C,KAAA,EAAO,KAAKC,MAAM;MAC1E,KAAK+B,YAAA,CAAaK,QAAA,CAAS,wBAAwB,EAAEM,KAAA,CAAMK,IAAA,CAAK,KAAKjD,MAAA,CAAOkD,gBAAgB;MAC5F,KAAKjB,YAAA,CAAaK,QAAA,CAAS,+BAA+B,EAAEM,KAAA,CAAMK,IAAA,CAAK,KAAKjD,MAAA,CAAOmD,uBAAuB;MAI1G,KAAKC,cAAA,GAAiB,IAAIC,kBAAA,CAAoB;MAC9C,KAAKD,cAAA,CAAeV,QAAA,GAAWC,UAAA;MAI/B,KAAKW,YAAA,GAAe,IAAIpB,cAAA,CAAe;QACrCC,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIkB,cAAA,CAAepB,OAAO;QACjDG,QAAA,EAAUC,aAAA,CAAcV,KAAA,CAAM0B,cAAA,CAAejB,QAAQ;QACrDE,YAAA,EAAce,cAAA,CAAef,YAAA;QAC7BC,cAAA,EAAgBc,cAAA,CAAed;MACvC,CAAO;MACD,KAAKa,YAAA,CAAahB,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKjB,gBAAA,CAAiBkB,OAAA;MACrE,KAAKS,YAAA,CAAahB,QAAA,CAAS,YAAY,EAAEM,KAAA,CAAMI,GAAA,CAAI,KAAK/C,KAAA,EAAO,KAAKC,MAAM;MAI1E,KAAKsD,mBAAA,GAAsB,IAAItB,cAAA,CAAe;QAC5CC,OAAA,EAASC,MAAA,CAAOC,MAAA,CAAO,IAAIoB,eAAA,CAAgBtB,OAAO;QAClDG,QAAA,EAAUC,aAAA,CAAcV,KAAA,CAAM4B,eAAA,CAAgBnB,QAAQ;QACtDE,YAAA,EAAciB,eAAA,CAAgBjB,YAAA;QAC9BC,cAAA,EAAgBgB,eAAA,CAAgBhB,cAAA;QAChCC,QAAA,EAAUC;MAClB,CAAO;MACD,KAAKa,mBAAA,CAAoBlB,QAAA,CAAS,QAAQ,EAAEM,KAAA,GAAQ,KAAKrB,kBAAA,CAAmBR,YAAA;MAC5E,KAAKyC,mBAAA,CAAoBlB,QAAA,CAAS,YAAY,EAAEM,KAAA,GAAQ,KAAK5C,MAAA,CAAO8C,IAAA;MACpE,KAAKU,mBAAA,CAAoBlB,QAAA,CAAS,WAAW,EAAEM,KAAA,GAAQ,KAAK5C,MAAA,CAAO+C,GAAA;MAInE,KAAKW,YAAA,GAAe,IAAIxB,cAAA,CAAe;QACrCI,QAAA,EAAUC,aAAA,CAAcV,KAAA,CAAM8B,UAAA,CAAWrB,QAAQ;QACjDE,YAAA,EAAcmB,UAAA,CAAWnB,YAAA;QACzBC,cAAA,EAAgBkB,UAAA,CAAWlB,cAAA;QAC3BmB,WAAA,EAAa;QACbC,SAAA,EAAW;QACXC,UAAA,EAAY;QACZC,QAAA,EAAUC,cAAA;QACVC,QAAA,EAAUC,UAAA;QACVC,aAAA,EAAeC,WAAA;QACfC,aAAA,EAAeC,cAAA;QACfC,aAAA,EAAeL,UAAA;QACfM,kBAAA,EAAoBJ;MAC5B,CAAO;MAED,KAAKK,MAAA,GAAS,IAAIC,cAAA,CAAe,IAAI;MAErC,KAAKC,kBAAA,GAAqB,IAAIC,KAAA,CAAO;IACtC;IAEDC,QAAA,EAAU;MAGR,KAAKxD,kBAAA,CAAmBwD,OAAA,CAAS;MACjC,KAAKtD,kBAAA,CAAmBsD,OAAA,CAAS;MACjC,KAAKlD,gBAAA,CAAiBkD,OAAA,CAAS;MAC/B,KAAKjD,gBAAA,CAAiBiD,OAAA,CAAS;MAI/B,KAAKzB,cAAA,CAAeyB,OAAA,CAAS;MAC7B,KAAKvB,YAAA,CAAauB,OAAA,CAAS;MAC3B,KAAKnB,YAAA,CAAamB,OAAA,CAAS;MAC3B,KAAKrB,mBAAA,CAAoBqB,OAAA,CAAS;MAIlC,KAAKJ,MAAA,CAAOI,OAAA,CAAS;IACtB;IAEDC,OAAOC,QAAA,EAAUC,WAAA,EAAsD;MAGrED,QAAA,CAASE,eAAA,CAAgB,KAAK5D,kBAAkB;MAChD0D,QAAA,CAAS5E,KAAA,CAAO;MAChB4E,QAAA,CAASD,MAAA,CAAO,KAAK/E,KAAA,EAAO,KAAKC,MAAM;MAIvC,KAAKkF,kBAAA,CAAoB;MACzB,KAAKC,cAAA,CAAeJ,QAAA,EAAU,KAAK3B,cAAA,EAAgB,KAAK7B,kBAAA,EAAoB,SAAU,CAAG;MACzF,KAAK6D,iBAAA,CAAmB;MAIxB,KAAKnD,YAAA,CAAaK,QAAA,CAAS,cAAc,EAAEM,KAAA,GAAQ,KAAKxC,YAAA;MACxD,KAAK6B,YAAA,CAAaK,QAAA,CAAS,aAAa,EAAEM,KAAA,GAAQ,KAAKnC,WAAA;MACvD,KAAKwB,YAAA,CAAaK,QAAA,CAAS,aAAa,EAAEM,KAAA,GAAQ,KAAKlC,WAAA;MACvD,KAAK2E,UAAA,CAAWN,QAAA,EAAU,KAAK9C,YAAA,EAAc,KAAKN,gBAAgB;MAIlE,KAAK0D,UAAA,CAAWN,QAAA,EAAU,KAAKzB,YAAA,EAAc,KAAK1B,gBAAgB;MAIlE,QAAQ,KAAKpB,MAAA;QACX,KAAKZ,SAAA,CAAS0F,MAAA,CAAOC,IAAA;UACnB,KAAK7B,YAAA,CAAapB,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKjB,gBAAA,CAAiBkB,OAAA;UACrE,KAAKa,YAAA,CAAahB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAWN,QAAA,EAAU,KAAKrB,YAAA,EAAc,KAAK8B,cAAA,GAAiB,OAAOR,WAAW;UAErF;QAEF,KAAKpF,SAAA,CAAS0F,MAAA,CAAOG,IAAA;UACnB,KAAK/B,YAAA,CAAapB,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKhB,gBAAA,CAAiBiB,OAAA;UACrE,KAAKa,YAAA,CAAahB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAWN,QAAA,EAAU,KAAKrB,YAAA,EAAc,KAAK8B,cAAA,GAAiB,OAAOR,WAAW;UAErF;QAEF,KAAKpF,SAAA,CAAS0F,MAAA,CAAOI,MAAA;UACnB,KAAKhC,YAAA,CAAapB,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKvB,kBAAA,CAAmBwB,OAAA;UACvE,KAAKa,YAAA,CAAahB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAWN,QAAA,EAAU,KAAKrB,YAAA,EAAc,KAAK8B,cAAA,GAAiB,OAAOR,WAAW;UAErF;QAEF,KAAKpF,SAAA,CAAS0F,MAAA,CAAOK,KAAA;UACnB,KAAKN,UAAA,CAAWN,QAAA,EAAU,KAAKvB,mBAAA,EAAqB,KAAKgC,cAAA,GAAiB,OAAOR,WAAW;UAE5F;QAEF,KAAKpF,SAAA,CAAS0F,MAAA,CAAOM,MAAA;UACnB,KAAKlC,YAAA,CAAapB,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKrB,kBAAA,CAAmBsB,OAAA;UACvE,KAAKa,YAAA,CAAahB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAWN,QAAA,EAAU,KAAKrB,YAAA,EAAc,KAAK8B,cAAA,GAAiB,OAAOR,WAAW;UAErF;QAEF,KAAKpF,SAAA,CAAS0F,MAAA,CAAOO,OAAA;UACnB,KAAKnC,YAAA,CAAapB,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKvB,kBAAA,CAAmBwB,OAAA;UACvE,KAAKa,YAAA,CAAahB,QAAA,GAAWC,UAAA;UAC7B,KAAK0C,UAAA,CAAWN,QAAA,EAAU,KAAKrB,YAAA,EAAc,KAAK8B,cAAA,GAAiB,OAAOR,WAAW;UAErF,KAAKtB,YAAA,CAAapB,QAAA,CAAS,UAAU,EAAEM,KAAA,GAAQ,KAAKhB,gBAAA,CAAiBiB,OAAA;UACrE,KAAKa,YAAA,CAAahB,QAAA,GAAWoD,cAAA;UAC7B,KAAKT,UAAA,CAAWN,QAAA,EAAU,KAAKrB,YAAA,EAAc,KAAK8B,cAAA,GAAiB,OAAOR,WAAW;UAErF;QAEF;UACEjD,OAAA,CAAQgE,IAAA,CAAK,sCAAsC;MACtD;IACF;IAEDV,WAAWN,QAAA,EAAUiB,YAAA,EAAcC,YAAA,EAAcC,UAAA,EAAYC,UAAA,EAAY;MAEvEpB,QAAA,CAASqB,aAAA,CAAc,KAAKzB,kBAAkB;MAC9C,MAAM0B,kBAAA,GAAqBtB,QAAA,CAASuB,aAAA,CAAe;MACnD,MAAMC,iBAAA,GAAoBxB,QAAA,CAASyB,SAAA;MAEnCzB,QAAA,CAASE,eAAA,CAAgBgB,YAAY;MAGrClB,QAAA,CAASyB,SAAA,GAAY;MACrB,IAAIN,UAAA,KAAe,UAAaA,UAAA,KAAe,MAAM;QACnDnB,QAAA,CAAS0B,aAAA,CAAcP,UAAU;QACjCnB,QAAA,CAAS2B,aAAA,CAAcP,UAAA,IAAc,CAAG;QACxCpB,QAAA,CAAS5E,KAAA,CAAO;MACjB;MAED,KAAKsE,MAAA,CAAOkC,QAAA,GAAWX,YAAA;MACvB,KAAKvB,MAAA,CAAOK,MAAA,CAAOC,QAAQ;MAG3BA,QAAA,CAASyB,SAAA,GAAYD,iBAAA;MACrBxB,QAAA,CAAS0B,aAAA,CAAc,KAAK9B,kBAAkB;MAC9CI,QAAA,CAAS2B,aAAA,CAAcL,kBAAkB;IAC1C;IAEDlB,eAAeJ,QAAA,EAAU6B,gBAAA,EAAkBX,YAAA,EAAcC,UAAA,EAAYC,UAAA,EAAY;MAC/EpB,QAAA,CAASqB,aAAA,CAAc,KAAKzB,kBAAkB;MAC9C,MAAM0B,kBAAA,GAAqBtB,QAAA,CAASuB,aAAA,CAAe;MACnD,MAAMC,iBAAA,GAAoBxB,QAAA,CAASyB,SAAA;MAEnCzB,QAAA,CAASE,eAAA,CAAgBgB,YAAY;MACrClB,QAAA,CAASyB,SAAA,GAAY;MAErBN,UAAA,GAAaU,gBAAA,CAAiBV,UAAA,IAAcA,UAAA;MAC5CC,UAAA,GAAaS,gBAAA,CAAiBT,UAAA,IAAcA,UAAA;MAE5C,IAAID,UAAA,KAAe,UAAaA,UAAA,KAAe,MAAM;QACnDnB,QAAA,CAAS0B,aAAA,CAAcP,UAAU;QACjCnB,QAAA,CAAS2B,aAAA,CAAcP,UAAA,IAAc,CAAG;QACxCpB,QAAA,CAAS5E,KAAA,CAAO;MACjB;MAED,KAAKJ,KAAA,CAAM6G,gBAAA,GAAmBA,gBAAA;MAC9B7B,QAAA,CAASD,MAAA,CAAO,KAAK/E,KAAA,EAAO,KAAKC,MAAM;MACvC,KAAKD,KAAA,CAAM6G,gBAAA,GAAmB;MAI9B7B,QAAA,CAASyB,SAAA,GAAYD,iBAAA;MACrBxB,QAAA,CAAS0B,aAAA,CAAc,KAAK9B,kBAAkB;MAC9CI,QAAA,CAAS2B,aAAA,CAAcL,kBAAkB;IAC1C;IAEDQ,QAAQ5G,KAAA,EAAOC,MAAA,EAAQ;MACrB,KAAKD,KAAA,GAAQA,KAAA;MACb,KAAKC,MAAA,GAASA,MAAA;MAEd,KAAKmB,kBAAA,CAAmBwF,OAAA,CAAQ5G,KAAA,EAAOC,MAAM;MAC7C,KAAKyB,gBAAA,CAAiBkF,OAAA,CAAQ5G,KAAA,EAAOC,MAAM;MAC3C,KAAKqB,kBAAA,CAAmBsF,OAAA,CAAQ5G,KAAA,EAAOC,MAAM;MAC7C,KAAK0B,gBAAA,CAAiBiF,OAAA,CAAQ5G,KAAA,EAAOC,MAAM;MAE3C,KAAK+B,YAAA,CAAaK,QAAA,CAAS,YAAY,EAAEM,KAAA,CAAMI,GAAA,CAAI/C,KAAA,EAAOC,MAAM;MAChE,KAAK+B,YAAA,CAAaK,QAAA,CAAS,wBAAwB,EAAEM,KAAA,CAAMK,IAAA,CAAK,KAAKjD,MAAA,CAAOkD,gBAAgB;MAC5F,KAAKjB,YAAA,CAAaK,QAAA,CAAS,+BAA+B,EAAEM,KAAA,CAAMK,IAAA,CAAK,KAAKjD,MAAA,CAAOmD,uBAAuB;MAE1G,KAAKG,YAAA,CAAahB,QAAA,CAAS,YAAY,EAAEM,KAAA,CAAMI,GAAA,CAAI/C,KAAA,EAAOC,MAAM;IACjE;IAEDW,qBAAA,EAAuB;MACrB,MAAMR,UAAA,GAAa,KAAKA,UAAA;MACxB,MAAMC,MAAA,GAAS,KAAKA,MAAA;MAEpB,SAASwG,CAAA,GAAI,GAAGA,CAAA,GAAIzG,UAAA,EAAYyG,CAAA,IAAK;QACnC,MAAMC,MAAA,GAAS,IAAIC,OAAA,CAAS;QAC5BD,MAAA,CAAOE,CAAA,GAAIC,IAAA,CAAKC,MAAA,CAAQ,IAAG,IAAI;QAC/BJ,MAAA,CAAOK,CAAA,GAAIF,IAAA,CAAKC,MAAA,CAAQ,IAAG,IAAI;QAC/BJ,MAAA,CAAOM,CAAA,GAAIH,IAAA,CAAKC,MAAA,CAAQ;QAExBJ,MAAA,CAAOO,SAAA,CAAW;QAElB,IAAIC,KAAA,GAAQT,CAAA,GAAIzG,UAAA;QAChBkH,KAAA,GAAQC,SAAA,CAAUC,IAAA,CAAK,KAAK,GAAGF,KAAA,GAAQA,KAAK;QAC5CR,MAAA,CAAOW,cAAA,CAAeH,KAAK;QAE3BjH,MAAA,CAAOqH,IAAA,CAAKZ,MAAM;MACnB;IACF;IAEDjG,8BAAA,EAAgC;MAC9B,MAAMb,KAAA,GAAQ;QACZC,MAAA,GAAS;MAEX,IAAI0H,YAAA,KAAiB,QAAW;QAC9B7F,OAAA,CAAQC,KAAA,CAAM,kDAAkD;MACjE;MAED,MAAM6F,OAAA,GAAU,IAAID,YAAA,CAAc;MAElC,MAAME,IAAA,GAAO7H,KAAA,GAAQC,MAAA;MACrB,MAAM6H,IAAA,GAAO,IAAIC,YAAA,CAAaF,IAAI;MAElC,SAAShB,CAAA,GAAI,GAAGA,CAAA,GAAIgB,IAAA,EAAMhB,CAAA,IAAK;QAC7B,MAAMG,CAAA,GAAIC,IAAA,CAAKC,MAAA,CAAQ,IAAG,IAAI;QAC9B,MAAMC,CAAA,GAAIF,IAAA,CAAKC,MAAA,CAAQ,IAAG,IAAI;QAC9B,MAAME,CAAA,GAAI;QAEVU,IAAA,CAAKjB,CAAC,IAAIe,OAAA,CAAQI,OAAA,CAAQhB,CAAA,EAAGG,CAAA,EAAGC,CAAC;MAClC;MAED,KAAK9G,YAAA,GAAe,IAAI2H,WAAA,CAAYH,IAAA,EAAM9H,KAAA,EAAOC,MAAA,EAAQiI,SAAA,EAAWC,SAAS;MAC7E,KAAK7H,YAAA,CAAa8H,KAAA,GAAQC,cAAA;MAC1B,KAAK/H,YAAA,CAAagI,KAAA,GAAQD,cAAA;MAC1B,KAAK/H,YAAA,CAAaiI,WAAA,GAAc;IACjC;IAEDtD,mBAAA,EAAqB;MACnB,MAAMnF,KAAA,GAAQ,KAAKA,KAAA;MACnB,MAAM0I,KAAA,GAAQ,KAAK9H,gBAAA;MAEnBZ,KAAA,CAAM2I,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC/BF,KAAA,CAAMzF,GAAA,CAAI2F,MAAA,EAAQA,MAAA,CAAOC,OAAO;QAEhC,IAAID,MAAA,CAAOE,QAAA,IAAYF,MAAA,CAAOG,MAAA,EAAQH,MAAA,CAAOC,OAAA,GAAU;MAC/D,CAAO;IACF;IAEDxD,kBAAA,EAAoB;MAClB,MAAMrF,KAAA,GAAQ,KAAKA,KAAA;MACnB,MAAM0I,KAAA,GAAQ,KAAK9H,gBAAA;MAEnBZ,KAAA,CAAM2I,QAAA,CAAS,UAAUC,MAAA,EAAQ;QAC/B,MAAMC,OAAA,GAAUH,KAAA,CAAMM,GAAA,CAAIJ,MAAM;QAChCA,MAAA,CAAOC,OAAA,GAAUA,OAAA;MACzB,CAAO;MAEDH,KAAA,CAAMtI,KAAA,CAAO;IACd;EACF;EAjXD,IAAM6I,SAAA,GAANpJ,SAAA;EACEqJ,aAAA,CADID,SAAA,EACG,UAAS;IACdnD,OAAA,EAAS;IACTN,IAAA,EAAM;IACNE,IAAA,EAAM;IACNC,MAAA,EAAQ;IACRC,KAAA,EAAO;IACPC,MAAA,EAAQ;EACT;EA2WH,OAAOoD,SAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}