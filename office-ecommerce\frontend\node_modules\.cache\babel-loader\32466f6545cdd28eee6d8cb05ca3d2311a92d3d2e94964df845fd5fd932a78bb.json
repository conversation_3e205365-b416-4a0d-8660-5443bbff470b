{"ast": null, "code": "import { Matrix4, <PERSON>, Sphere, Vector3 } from 'three';\nconst _inverseMatrix = new Matrix4();\nconst _ray = new Ray();\nconst _sphere = new Sphere();\nconst _vA = new Vector3();\nfunction meshBounds(raycaster, intersects) {\n  const geometry = this.geometry;\n  const material = this.material;\n  const matrixWorld = this.matrixWorld;\n  if (material === undefined) return; // Checking boundingSphere distance to ray\n\n  if (geometry.boundingSphere === null) geometry.computeBoundingSphere();\n  _sphere.copy(geometry.boundingSphere);\n  _sphere.applyMatrix4(matrixWorld);\n  if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n  _inverseMatrix.copy(matrixWorld).invert();\n  _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix); // Check boundingBox before continuing\n\n  if (geometry.boundingBox !== null && _ray.intersectBox(geometry.boundingBox, _vA) === null) return;\n  intersects.push({\n    distance: _vA.distanceTo(raycaster.ray.origin),\n    point: _vA.clone(),\n    object: this\n  });\n}\nexport { meshBounds };", "map": {"version": 3, "names": ["Matrix4", "<PERSON>", "Sphere", "Vector3", "_inverseMatrix", "_ray", "_sphere", "_vA", "meshBounds", "raycaster", "intersects", "geometry", "material", "matrixWorld", "undefined", "boundingSphere", "computeBoundingSphere", "copy", "applyMatrix4", "ray", "intersectsSphere", "invert", "boundingBox", "intersectBox", "push", "distance", "distanceTo", "origin", "point", "clone", "object"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/meshBounds.js"], "sourcesContent": ["import { Matrix4, <PERSON>, Sphere, Vector3 } from 'three';\n\nconst _inverseMatrix = new Matrix4();\n\nconst _ray = new Ray();\n\nconst _sphere = new Sphere();\n\nconst _vA = new Vector3();\n\nfunction meshBounds(raycaster, intersects) {\n  const geometry = this.geometry;\n  const material = this.material;\n  const matrixWorld = this.matrixWorld;\n  if (material === undefined) return; // Checking boundingSphere distance to ray\n\n  if (geometry.boundingSphere === null) geometry.computeBoundingSphere();\n\n  _sphere.copy(geometry.boundingSphere);\n\n  _sphere.applyMatrix4(matrixWorld);\n\n  if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n\n  _inverseMatrix.copy(matrixWorld).invert();\n\n  _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix); // Check boundingBox before continuing\n\n\n  if (geometry.boundingBox !== null && _ray.intersectBox(geometry.boundingBox, _vA) === null) return;\n  intersects.push({\n    distance: _vA.distanceTo(raycaster.ray.origin),\n    point: _vA.clone(),\n    object: this\n  });\n}\n\nexport { meshBounds };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AAErD,MAAMC,cAAc,GAAG,IAAIJ,OAAO,CAAC,CAAC;AAEpC,MAAMK,IAAI,GAAG,IAAIJ,GAAG,CAAC,CAAC;AAEtB,MAAMK,OAAO,GAAG,IAAIJ,MAAM,CAAC,CAAC;AAE5B,MAAMK,GAAG,GAAG,IAAIJ,OAAO,CAAC,CAAC;AAEzB,SAASK,UAAUA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACzC,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC9B,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC9B,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;EACpC,IAAID,QAAQ,KAAKE,SAAS,EAAE,OAAO,CAAC;;EAEpC,IAAIH,QAAQ,CAACI,cAAc,KAAK,IAAI,EAAEJ,QAAQ,CAACK,qBAAqB,CAAC,CAAC;EAEtEV,OAAO,CAACW,IAAI,CAACN,QAAQ,CAACI,cAAc,CAAC;EAErCT,OAAO,CAACY,YAAY,CAACL,WAAW,CAAC;EAEjC,IAAIJ,SAAS,CAACU,GAAG,CAACC,gBAAgB,CAACd,OAAO,CAAC,KAAK,KAAK,EAAE;EAEvDF,cAAc,CAACa,IAAI,CAACJ,WAAW,CAAC,CAACQ,MAAM,CAAC,CAAC;EAEzChB,IAAI,CAACY,IAAI,CAACR,SAAS,CAACU,GAAG,CAAC,CAACD,YAAY,CAACd,cAAc,CAAC,CAAC,CAAC;;EAGvD,IAAIO,QAAQ,CAACW,WAAW,KAAK,IAAI,IAAIjB,IAAI,CAACkB,YAAY,CAACZ,QAAQ,CAACW,WAAW,EAAEf,GAAG,CAAC,KAAK,IAAI,EAAE;EAC5FG,UAAU,CAACc,IAAI,CAAC;IACdC,QAAQ,EAAElB,GAAG,CAACmB,UAAU,CAACjB,SAAS,CAACU,GAAG,CAACQ,MAAM,CAAC;IAC9CC,KAAK,EAAErB,GAAG,CAACsB,KAAK,CAAC,CAAC;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,SAAStB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}