{"ast": null, "code": "import { version } from \"./constants.js\";\nconst UV1 = version >= 125 ? \"uv1\" : \"uv2\";\nexport { UV1 };", "map": {"version": 3, "names": ["UV1", "version"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\_polyfill\\uv1.ts"], "sourcesContent": ["import { version } from \"./constants\";\n\n/** uv2 renamed to uv1 in r125\n * \n * https://github.com/mrdoob/three.js/pull/25943\n*/\nexport const UV1 = version >= 125 ? 'uv1' : 'uv2'"], "mappings": ";AAMa,MAAAA,GAAA,GAAMC,OAAA,IAAW,MAAM,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}