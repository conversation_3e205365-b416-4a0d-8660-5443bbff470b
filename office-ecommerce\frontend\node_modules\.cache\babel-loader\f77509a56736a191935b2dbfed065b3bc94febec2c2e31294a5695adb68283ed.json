{"ast": null, "code": "import { PerspectiveCamera, ShaderMaterial, Scene, OrthographicCamera, WebGLRenderTarget, UniformsUtils, Mesh, PlaneGeometry } from \"three\";\nimport { BokehDepthShader, BokehShader2 } from \"../shaders/BokehShader2.js\";\nclass CinematicCamera extends PerspectiveCamera {\n  constructor(fov, aspect, near, far) {\n    super(fov, aspect, near, far);\n    this.type = \"CinematicCamera\";\n    this.postprocessing = {\n      enabled: true\n    };\n    this.shaderSettings = {\n      rings: 3,\n      samples: 4\n    };\n    const depthShader = BokehDepthShader;\n    this.materialDepth = new ShaderMaterial({\n      uniforms: depthShader.uniforms,\n      vertexShader: depthShader.vertexShader,\n      fragmentShader: depthShader.fragmentShader\n    });\n    this.materialDepth.uniforms[\"mNear\"].value = near;\n    this.materialDepth.uniforms[\"mFar\"].value = far;\n    this.setLens();\n    this.initPostProcessing();\n  }\n  // providing fnumber and coc(Circle of Confusion) as extra arguments\n  setLens(focalLength, filmGauge, fNumber, coc) {\n    if (focalLength === void 0) focalLength = 35;\n    if (filmGauge !== void 0) this.filmGauge = filmGauge;\n    this.setFocalLength(focalLength);\n    if (fNumber === void 0) fNumber = 8;\n    if (coc === void 0) coc = 0.019;\n    this.fNumber = fNumber;\n    this.coc = coc;\n    this.aperture = focalLength / this.fNumber;\n    this.hyperFocal = focalLength * focalLength / (this.aperture * this.coc);\n  }\n  linearize(depth) {\n    const zfar = this.far;\n    const znear = this.near;\n    return -zfar * znear / (depth * (zfar - znear) - zfar);\n  }\n  smoothstep(near, far, depth) {\n    const x = this.saturate((depth - near) / (far - near));\n    return x * x * (3 - 2 * x);\n  }\n  saturate(x) {\n    return Math.max(0, Math.min(1, x));\n  }\n  // function for focusing at a distance from the camera\n  focusAt(focusDistance) {\n    if (focusDistance === void 0) focusDistance = 20;\n    const focalLength = this.getFocalLength();\n    this.focus = focusDistance;\n    this.nearPoint = this.hyperFocal * this.focus / (this.hyperFocal + (this.focus - focalLength));\n    this.farPoint = this.hyperFocal * this.focus / (this.hyperFocal - (this.focus - focalLength));\n    this.depthOfField = this.farPoint - this.nearPoint;\n    if (this.depthOfField < 0) this.depthOfField = 0;\n    this.sdistance = this.smoothstep(this.near, this.far, this.focus);\n    this.ldistance = this.linearize(1 - this.sdistance);\n    this.postprocessing.bokeh_uniforms[\"focalDepth\"].value = this.ldistance;\n  }\n  initPostProcessing() {\n    if (this.postprocessing.enabled) {\n      this.postprocessing.scene = new Scene();\n      this.postprocessing.camera = new OrthographicCamera(window.innerWidth / -2, window.innerWidth / 2, window.innerHeight / 2, window.innerHeight / -2, -1e4, 1e4);\n      this.postprocessing.scene.add(this.postprocessing.camera);\n      this.postprocessing.rtTextureDepth = new WebGLRenderTarget(window.innerWidth, window.innerHeight);\n      this.postprocessing.rtTextureColor = new WebGLRenderTarget(window.innerWidth, window.innerHeight);\n      const bokeh_shader = BokehShader2;\n      this.postprocessing.bokeh_uniforms = UniformsUtils.clone(bokeh_shader.uniforms);\n      this.postprocessing.bokeh_uniforms[\"tColor\"].value = this.postprocessing.rtTextureColor.texture;\n      this.postprocessing.bokeh_uniforms[\"tDepth\"].value = this.postprocessing.rtTextureDepth.texture;\n      this.postprocessing.bokeh_uniforms[\"manualdof\"].value = 0;\n      this.postprocessing.bokeh_uniforms[\"shaderFocus\"].value = 0;\n      this.postprocessing.bokeh_uniforms[\"fstop\"].value = 2.8;\n      this.postprocessing.bokeh_uniforms[\"showFocus\"].value = 1;\n      this.postprocessing.bokeh_uniforms[\"focalDepth\"].value = 0.1;\n      this.postprocessing.bokeh_uniforms[\"znear\"].value = this.near;\n      this.postprocessing.bokeh_uniforms[\"zfar\"].value = this.near;\n      this.postprocessing.bokeh_uniforms[\"textureWidth\"].value = window.innerWidth;\n      this.postprocessing.bokeh_uniforms[\"textureHeight\"].value = window.innerHeight;\n      this.postprocessing.materialBokeh = new ShaderMaterial({\n        uniforms: this.postprocessing.bokeh_uniforms,\n        vertexShader: bokeh_shader.vertexShader,\n        fragmentShader: bokeh_shader.fragmentShader,\n        defines: {\n          RINGS: this.shaderSettings.rings,\n          SAMPLES: this.shaderSettings.samples,\n          DEPTH_PACKING: 1\n        }\n      });\n      this.postprocessing.quad = new Mesh(new PlaneGeometry(window.innerWidth, window.innerHeight), this.postprocessing.materialBokeh);\n      this.postprocessing.quad.position.z = -500;\n      this.postprocessing.scene.add(this.postprocessing.quad);\n    }\n  }\n  renderCinematic(scene, renderer) {\n    if (this.postprocessing.enabled) {\n      const currentRenderTarget = renderer.getRenderTarget();\n      renderer.clear();\n      scene.overrideMaterial = null;\n      renderer.setRenderTarget(this.postprocessing.rtTextureColor);\n      renderer.clear();\n      renderer.render(scene, this);\n      scene.overrideMaterial = this.materialDepth;\n      renderer.setRenderTarget(this.postprocessing.rtTextureDepth);\n      renderer.clear();\n      renderer.render(scene, this);\n      renderer.setRenderTarget(null);\n      renderer.render(this.postprocessing.scene, this.postprocessing.camera);\n      renderer.setRenderTarget(currentRenderTarget);\n    }\n  }\n}\nexport { CinematicCamera };", "map": {"version": 3, "names": ["CinematicCamera", "PerspectiveCamera", "constructor", "fov", "aspect", "near", "far", "type", "postprocessing", "enabled", "shaderSettings", "rings", "samples", "depthShader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "material<PERSON><PERSON>h", "ShaderMaterial", "uniforms", "vertexShader", "fragmentShader", "value", "setLens", "initPostProcessing", "focal<PERSON>ength", "filmGauge", "fNumber", "coc", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aperture", "hyperFocal", "linearize", "depth", "zfar", "znear", "smoothstep", "x", "saturate", "Math", "max", "min", "focusAt", "focusDistance", "getFocal<PERSON><PERSON><PERSON>", "focus", "nearPoint", "farPoint", "depthOfField", "sdistance", "ldistance", "bokeh_uniforms", "scene", "Scene", "camera", "OrthographicCamera", "window", "innerWidth", "innerHeight", "add", "rtTextureDepth", "WebGLRenderTarget", "rtTextureColor", "bokeh_shader", "BokehShader2", "UniformsUtils", "clone", "texture", "materialBokeh", "defines", "RINGS", "SAMPLES", "DEPTH_PACKING", "quad", "<PERSON><PERSON>", "PlaneGeometry", "position", "z", "renderCinematic", "renderer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "overrideMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\cameras\\CinematicCamera.js"], "sourcesContent": ["import {\n  Mesh,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PlaneGeometry,\n  Scene,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderTarget,\n} from 'three'\nimport { BokehShader2, BokehDepthShader } from '../shaders/BokehShader2'\n\nclass CinematicCamera extends PerspectiveCamera {\n  constructor(fov, aspect, near, far) {\n    super(fov, aspect, near, far)\n\n    this.type = 'CinematicCamera'\n\n    this.postprocessing = { enabled: true }\n    this.shaderSettings = {\n      rings: 3,\n      samples: 4,\n    }\n\n    const depthShader = BokehDepthShader\n\n    this.materialDepth = new ShaderMaterial({\n      uniforms: depthShader.uniforms,\n      vertexShader: depthShader.vertexShader,\n      fragmentShader: depthShader.fragmentShader,\n    })\n\n    this.materialDepth.uniforms['mNear'].value = near\n    this.materialDepth.uniforms['mFar'].value = far\n\n    // In case of cinematicCamera, having a default lens set is important\n    this.setLens()\n\n    this.initPostProcessing()\n  }\n\n  // providing fnumber and coc(Circle of Confusion) as extra arguments\n  setLens(focalLength, filmGauge, fNumber, coc) {\n    // In case of cinematicCamera, having a default lens set is important\n    if (focalLength === undefined) focalLength = 35\n    if (filmGauge !== undefined) this.filmGauge = filmGauge\n\n    this.setFocalLength(focalLength)\n\n    // if fnumber and coc are not provided, cinematicCamera tries to act as a basic PerspectiveCamera\n    if (fNumber === undefined) fNumber = 8\n    if (coc === undefined) coc = 0.019\n\n    this.fNumber = fNumber\n    this.coc = coc\n\n    // fNumber is focalLength by aperture\n    this.aperture = focalLength / this.fNumber\n\n    // hyperFocal is required to calculate depthOfField when a lens tries to focus at a distance with given fNumber and focalLength\n    this.hyperFocal = (focalLength * focalLength) / (this.aperture * this.coc)\n  }\n\n  linearize(depth) {\n    const zfar = this.far\n    const znear = this.near\n    return (-zfar * znear) / (depth * (zfar - znear) - zfar)\n  }\n\n  smoothstep(near, far, depth) {\n    const x = this.saturate((depth - near) / (far - near))\n    return x * x * (3 - 2 * x)\n  }\n\n  saturate(x) {\n    return Math.max(0, Math.min(1, x))\n  }\n\n  // function for focusing at a distance from the camera\n  focusAt(focusDistance) {\n    if (focusDistance === undefined) focusDistance = 20\n\n    const focalLength = this.getFocalLength()\n\n    // distance from the camera (normal to frustrum) to focus on\n    this.focus = focusDistance\n\n    // the nearest point from the camera which is in focus (unused)\n    this.nearPoint = (this.hyperFocal * this.focus) / (this.hyperFocal + (this.focus - focalLength))\n\n    // the farthest point from the camera which is in focus (unused)\n    this.farPoint = (this.hyperFocal * this.focus) / (this.hyperFocal - (this.focus - focalLength))\n\n    // the gap or width of the space in which is everything is in focus (unused)\n    this.depthOfField = this.farPoint - this.nearPoint\n\n    // Considering minimum distance of focus for a standard lens (unused)\n    if (this.depthOfField < 0) this.depthOfField = 0\n\n    this.sdistance = this.smoothstep(this.near, this.far, this.focus)\n\n    this.ldistance = this.linearize(1 - this.sdistance)\n\n    this.postprocessing.bokeh_uniforms['focalDepth'].value = this.ldistance\n  }\n\n  initPostProcessing() {\n    if (this.postprocessing.enabled) {\n      this.postprocessing.scene = new Scene()\n\n      this.postprocessing.camera = new OrthographicCamera(\n        window.innerWidth / -2,\n        window.innerWidth / 2,\n        window.innerHeight / 2,\n        window.innerHeight / -2,\n        -10000,\n        10000,\n      )\n\n      this.postprocessing.scene.add(this.postprocessing.camera)\n\n      this.postprocessing.rtTextureDepth = new WebGLRenderTarget(window.innerWidth, window.innerHeight)\n      this.postprocessing.rtTextureColor = new WebGLRenderTarget(window.innerWidth, window.innerHeight)\n\n      const bokeh_shader = BokehShader2\n\n      this.postprocessing.bokeh_uniforms = UniformsUtils.clone(bokeh_shader.uniforms)\n\n      this.postprocessing.bokeh_uniforms['tColor'].value = this.postprocessing.rtTextureColor.texture\n      this.postprocessing.bokeh_uniforms['tDepth'].value = this.postprocessing.rtTextureDepth.texture\n\n      this.postprocessing.bokeh_uniforms['manualdof'].value = 0\n      this.postprocessing.bokeh_uniforms['shaderFocus'].value = 0\n\n      this.postprocessing.bokeh_uniforms['fstop'].value = 2.8\n\n      this.postprocessing.bokeh_uniforms['showFocus'].value = 1\n\n      this.postprocessing.bokeh_uniforms['focalDepth'].value = 0.1\n\n      //console.log( this.postprocessing.bokeh_uniforms[ \"focalDepth\" ].value );\n\n      this.postprocessing.bokeh_uniforms['znear'].value = this.near\n      this.postprocessing.bokeh_uniforms['zfar'].value = this.near\n\n      this.postprocessing.bokeh_uniforms['textureWidth'].value = window.innerWidth\n\n      this.postprocessing.bokeh_uniforms['textureHeight'].value = window.innerHeight\n\n      this.postprocessing.materialBokeh = new ShaderMaterial({\n        uniforms: this.postprocessing.bokeh_uniforms,\n        vertexShader: bokeh_shader.vertexShader,\n        fragmentShader: bokeh_shader.fragmentShader,\n        defines: {\n          RINGS: this.shaderSettings.rings,\n          SAMPLES: this.shaderSettings.samples,\n          DEPTH_PACKING: 1,\n        },\n      })\n\n      this.postprocessing.quad = new Mesh(\n        new PlaneGeometry(window.innerWidth, window.innerHeight),\n        this.postprocessing.materialBokeh,\n      )\n      this.postprocessing.quad.position.z = -500\n      this.postprocessing.scene.add(this.postprocessing.quad)\n    }\n  }\n\n  renderCinematic(scene, renderer) {\n    if (this.postprocessing.enabled) {\n      const currentRenderTarget = renderer.getRenderTarget()\n\n      renderer.clear()\n\n      // Render scene into texture\n\n      scene.overrideMaterial = null\n      renderer.setRenderTarget(this.postprocessing.rtTextureColor)\n      renderer.clear()\n      renderer.render(scene, this)\n\n      // Render depth into texture\n\n      scene.overrideMaterial = this.materialDepth\n      renderer.setRenderTarget(this.postprocessing.rtTextureDepth)\n      renderer.clear()\n      renderer.render(scene, this)\n\n      // Render bokeh composite\n\n      renderer.setRenderTarget(null)\n      renderer.render(this.postprocessing.scene, this.postprocessing.camera)\n\n      renderer.setRenderTarget(currentRenderTarget)\n    }\n  }\n}\n\nexport { CinematicCamera }\n"], "mappings": ";;AAYA,MAAMA,eAAA,SAAwBC,iBAAA,CAAkB;EAC9CC,YAAYC,GAAA,EAAKC,MAAA,EAAQC,IAAA,EAAMC,GAAA,EAAK;IAClC,MAAMH,GAAA,EAAKC,MAAA,EAAQC,IAAA,EAAMC,GAAG;IAE5B,KAAKC,IAAA,GAAO;IAEZ,KAAKC,cAAA,GAAiB;MAAEC,OAAA,EAAS;IAAM;IACvC,KAAKC,cAAA,GAAiB;MACpBC,KAAA,EAAO;MACPC,OAAA,EAAS;IACV;IAED,MAAMC,WAAA,GAAcC,gBAAA;IAEpB,KAAKC,aAAA,GAAgB,IAAIC,cAAA,CAAe;MACtCC,QAAA,EAAUJ,WAAA,CAAYI,QAAA;MACtBC,YAAA,EAAcL,WAAA,CAAYK,YAAA;MAC1BC,cAAA,EAAgBN,WAAA,CAAYM;IAClC,CAAK;IAED,KAAKJ,aAAA,CAAcE,QAAA,CAAS,OAAO,EAAEG,KAAA,GAAQf,IAAA;IAC7C,KAAKU,aAAA,CAAcE,QAAA,CAAS,MAAM,EAAEG,KAAA,GAAQd,GAAA;IAG5C,KAAKe,OAAA,CAAS;IAEd,KAAKC,kBAAA,CAAoB;EAC1B;EAAA;EAGDD,QAAQE,WAAA,EAAaC,SAAA,EAAWC,OAAA,EAASC,GAAA,EAAK;IAE5C,IAAIH,WAAA,KAAgB,QAAWA,WAAA,GAAc;IAC7C,IAAIC,SAAA,KAAc,QAAW,KAAKA,SAAA,GAAYA,SAAA;IAE9C,KAAKG,cAAA,CAAeJ,WAAW;IAG/B,IAAIE,OAAA,KAAY,QAAWA,OAAA,GAAU;IACrC,IAAIC,GAAA,KAAQ,QAAWA,GAAA,GAAM;IAE7B,KAAKD,OAAA,GAAUA,OAAA;IACf,KAAKC,GAAA,GAAMA,GAAA;IAGX,KAAKE,QAAA,GAAWL,WAAA,GAAc,KAAKE,OAAA;IAGnC,KAAKI,UAAA,GAAcN,WAAA,GAAcA,WAAA,IAAgB,KAAKK,QAAA,GAAW,KAAKF,GAAA;EACvE;EAEDI,UAAUC,KAAA,EAAO;IACf,MAAMC,IAAA,GAAO,KAAK1B,GAAA;IAClB,MAAM2B,KAAA,GAAQ,KAAK5B,IAAA;IACnB,OAAQ,CAAC2B,IAAA,GAAOC,KAAA,IAAUF,KAAA,IAASC,IAAA,GAAOC,KAAA,IAASD,IAAA;EACpD;EAEDE,WAAW7B,IAAA,EAAMC,GAAA,EAAKyB,KAAA,EAAO;IAC3B,MAAMI,CAAA,GAAI,KAAKC,QAAA,EAAUL,KAAA,GAAQ1B,IAAA,KAASC,GAAA,GAAMD,IAAA,CAAK;IACrD,OAAO8B,CAAA,GAAIA,CAAA,IAAK,IAAI,IAAIA,CAAA;EACzB;EAEDC,SAASD,CAAA,EAAG;IACV,OAAOE,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKE,GAAA,CAAI,GAAGJ,CAAC,CAAC;EAClC;EAAA;EAGDK,QAAQC,aAAA,EAAe;IACrB,IAAIA,aAAA,KAAkB,QAAWA,aAAA,GAAgB;IAEjD,MAAMlB,WAAA,GAAc,KAAKmB,cAAA,CAAgB;IAGzC,KAAKC,KAAA,GAAQF,aAAA;IAGb,KAAKG,SAAA,GAAa,KAAKf,UAAA,GAAa,KAAKc,KAAA,IAAU,KAAKd,UAAA,IAAc,KAAKc,KAAA,GAAQpB,WAAA;IAGnF,KAAKsB,QAAA,GAAY,KAAKhB,UAAA,GAAa,KAAKc,KAAA,IAAU,KAAKd,UAAA,IAAc,KAAKc,KAAA,GAAQpB,WAAA;IAGlF,KAAKuB,YAAA,GAAe,KAAKD,QAAA,GAAW,KAAKD,SAAA;IAGzC,IAAI,KAAKE,YAAA,GAAe,GAAG,KAAKA,YAAA,GAAe;IAE/C,KAAKC,SAAA,GAAY,KAAKb,UAAA,CAAW,KAAK7B,IAAA,EAAM,KAAKC,GAAA,EAAK,KAAKqC,KAAK;IAEhE,KAAKK,SAAA,GAAY,KAAKlB,SAAA,CAAU,IAAI,KAAKiB,SAAS;IAElD,KAAKvC,cAAA,CAAeyC,cAAA,CAAe,YAAY,EAAE7B,KAAA,GAAQ,KAAK4B,SAAA;EAC/D;EAED1B,mBAAA,EAAqB;IACnB,IAAI,KAAKd,cAAA,CAAeC,OAAA,EAAS;MAC/B,KAAKD,cAAA,CAAe0C,KAAA,GAAQ,IAAIC,KAAA,CAAO;MAEvC,KAAK3C,cAAA,CAAe4C,MAAA,GAAS,IAAIC,kBAAA,CAC/BC,MAAA,CAAOC,UAAA,GAAa,IACpBD,MAAA,CAAOC,UAAA,GAAa,GACpBD,MAAA,CAAOE,WAAA,GAAc,GACrBF,MAAA,CAAOE,WAAA,GAAc,IACrB,MACA,GACD;MAED,KAAKhD,cAAA,CAAe0C,KAAA,CAAMO,GAAA,CAAI,KAAKjD,cAAA,CAAe4C,MAAM;MAExD,KAAK5C,cAAA,CAAekD,cAAA,GAAiB,IAAIC,iBAAA,CAAkBL,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAW;MAChG,KAAKhD,cAAA,CAAeoD,cAAA,GAAiB,IAAID,iBAAA,CAAkBL,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAW;MAEhG,MAAMK,YAAA,GAAeC,YAAA;MAErB,KAAKtD,cAAA,CAAeyC,cAAA,GAAiBc,aAAA,CAAcC,KAAA,CAAMH,YAAA,CAAa5C,QAAQ;MAE9E,KAAKT,cAAA,CAAeyC,cAAA,CAAe,QAAQ,EAAE7B,KAAA,GAAQ,KAAKZ,cAAA,CAAeoD,cAAA,CAAeK,OAAA;MACxF,KAAKzD,cAAA,CAAeyC,cAAA,CAAe,QAAQ,EAAE7B,KAAA,GAAQ,KAAKZ,cAAA,CAAekD,cAAA,CAAeO,OAAA;MAExF,KAAKzD,cAAA,CAAeyC,cAAA,CAAe,WAAW,EAAE7B,KAAA,GAAQ;MACxD,KAAKZ,cAAA,CAAeyC,cAAA,CAAe,aAAa,EAAE7B,KAAA,GAAQ;MAE1D,KAAKZ,cAAA,CAAeyC,cAAA,CAAe,OAAO,EAAE7B,KAAA,GAAQ;MAEpD,KAAKZ,cAAA,CAAeyC,cAAA,CAAe,WAAW,EAAE7B,KAAA,GAAQ;MAExD,KAAKZ,cAAA,CAAeyC,cAAA,CAAe,YAAY,EAAE7B,KAAA,GAAQ;MAIzD,KAAKZ,cAAA,CAAeyC,cAAA,CAAe,OAAO,EAAE7B,KAAA,GAAQ,KAAKf,IAAA;MACzD,KAAKG,cAAA,CAAeyC,cAAA,CAAe,MAAM,EAAE7B,KAAA,GAAQ,KAAKf,IAAA;MAExD,KAAKG,cAAA,CAAeyC,cAAA,CAAe,cAAc,EAAE7B,KAAA,GAAQkC,MAAA,CAAOC,UAAA;MAElE,KAAK/C,cAAA,CAAeyC,cAAA,CAAe,eAAe,EAAE7B,KAAA,GAAQkC,MAAA,CAAOE,WAAA;MAEnE,KAAKhD,cAAA,CAAe0D,aAAA,GAAgB,IAAIlD,cAAA,CAAe;QACrDC,QAAA,EAAU,KAAKT,cAAA,CAAeyC,cAAA;QAC9B/B,YAAA,EAAc2C,YAAA,CAAa3C,YAAA;QAC3BC,cAAA,EAAgB0C,YAAA,CAAa1C,cAAA;QAC7BgD,OAAA,EAAS;UACPC,KAAA,EAAO,KAAK1D,cAAA,CAAeC,KAAA;UAC3B0D,OAAA,EAAS,KAAK3D,cAAA,CAAeE,OAAA;UAC7B0D,aAAA,EAAe;QAChB;MACT,CAAO;MAED,KAAK9D,cAAA,CAAe+D,IAAA,GAAO,IAAIC,IAAA,CAC7B,IAAIC,aAAA,CAAcnB,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAW,GACvD,KAAKhD,cAAA,CAAe0D,aACrB;MACD,KAAK1D,cAAA,CAAe+D,IAAA,CAAKG,QAAA,CAASC,CAAA,GAAI;MACtC,KAAKnE,cAAA,CAAe0C,KAAA,CAAMO,GAAA,CAAI,KAAKjD,cAAA,CAAe+D,IAAI;IACvD;EACF;EAEDK,gBAAgB1B,KAAA,EAAO2B,QAAA,EAAU;IAC/B,IAAI,KAAKrE,cAAA,CAAeC,OAAA,EAAS;MAC/B,MAAMqE,mBAAA,GAAsBD,QAAA,CAASE,eAAA,CAAiB;MAEtDF,QAAA,CAASG,KAAA,CAAO;MAIhB9B,KAAA,CAAM+B,gBAAA,GAAmB;MACzBJ,QAAA,CAASK,eAAA,CAAgB,KAAK1E,cAAA,CAAeoD,cAAc;MAC3DiB,QAAA,CAASG,KAAA,CAAO;MAChBH,QAAA,CAASM,MAAA,CAAOjC,KAAA,EAAO,IAAI;MAI3BA,KAAA,CAAM+B,gBAAA,GAAmB,KAAKlE,aAAA;MAC9B8D,QAAA,CAASK,eAAA,CAAgB,KAAK1E,cAAA,CAAekD,cAAc;MAC3DmB,QAAA,CAASG,KAAA,CAAO;MAChBH,QAAA,CAASM,MAAA,CAAOjC,KAAA,EAAO,IAAI;MAI3B2B,QAAA,CAASK,eAAA,CAAgB,IAAI;MAC7BL,QAAA,CAASM,MAAA,CAAO,KAAK3E,cAAA,CAAe0C,KAAA,EAAO,KAAK1C,cAAA,CAAe4C,MAAM;MAErEyB,QAAA,CAASK,eAAA,CAAgBJ,mBAAmB;IAC7C;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}