{"ast": null, "code": "import { NumberKeyframeTrack, AnimationClip, Vector3, VectorKeyframeTrack, BooleanKeyframeTrack, ColorKeyframeTrack } from \"three\";\nconst AnimationClipCreator = {\n  CreateRotationAnimation(period, axis = \"x\") {\n    const times = [0, period],\n      values = [0, 360];\n    const trackName = \".rotation[\" + axis + \"]\";\n    const track = new NumberKeyframeTrack(trackName, times, values);\n    return new AnimationClip(null, period, [track]);\n  },\n  CreateScaleAxisAnimation(period, axis = \"x\") {\n    const times = [0, period],\n      values = [0, 1];\n    const trackName = \".scale[\" + axis + \"]\";\n    const track = new NumberKeyframeTrack(trackName, times, values);\n    return new AnimationClip(null, period, [track]);\n  },\n  CreateShakeAnimation(duration, shakeScale) {\n    const times = [],\n      values = [],\n      tmp = new Vector3();\n    for (let i = 0; i < duration * 10; i++) {\n      times.push(i / 10);\n      tmp.set(Math.random() * 2 - 1, Math.random() * 2 - 1, Math.random() * 2 - 1).multiply(shakeScale).toArray(values, values.length);\n    }\n    const trackName = \".position\";\n    const track = new VectorKeyframeTrack(trackName, times, values);\n    return new AnimationClip(null, duration, [track]);\n  },\n  CreatePulsationAnimation(duration, pulseScale) {\n    const times = [],\n      values = [],\n      tmp = new Vector3();\n    for (let i = 0; i < duration * 10; i++) {\n      times.push(i / 10);\n      const scaleFactor = Math.random() * pulseScale;\n      tmp.set(scaleFactor, scaleFactor, scaleFactor).toArray(values, values.length);\n    }\n    const trackName = \".scale\";\n    const track = new VectorKeyframeTrack(trackName, times, values);\n    return new AnimationClip(null, duration, [track]);\n  },\n  CreateVisibilityAnimation(duration) {\n    const times = [0, duration / 2, duration],\n      values = [true, false, true];\n    const trackName = \".visible\";\n    const track = new BooleanKeyframeTrack(trackName, times, values);\n    return new AnimationClip(null, duration, [track]);\n  },\n  CreateMaterialColorAnimation(duration, colors) {\n    const times = [],\n      values = [],\n      timeStep = duration / colors.length;\n    for (let i = 0; i < colors.length; i++) {\n      times.push(i * timeStep);\n      const color = colors[i];\n      values.push(color.r, color.g, color.b);\n    }\n    const trackName = \".material.color\";\n    const track = new ColorKeyframeTrack(trackName, times, values);\n    return new AnimationClip(null, duration, [track]);\n  }\n};\nexport { AnimationClipCreator };", "map": {"version": 3, "names": ["AnimationClipCreator", "CreateRotationAnimation", "period", "axis", "times", "values", "trackName", "track", "NumberKeyframeTrack", "AnimationClip", "CreateScaleAxisAnimation", "CreateShakeAnimation", "duration", "shakeScale", "tmp", "Vector3", "i", "push", "set", "Math", "random", "multiply", "toArray", "length", "VectorKeyframeTrack", "CreatePulsationAnimation", "pulseScale", "scaleFactor", "CreateVisibilityAnimation", "BooleanKeyframeTrack", "CreateMaterialColorAnimation", "colors", "timeStep", "color", "r", "g", "b", "ColorKeyframeTrack"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\animation\\AnimationClipCreator.js"], "sourcesContent": ["import {\n  AnimationC<PERSON>,\n  BooleanKeyframeTrack,\n  ColorKeyframeTrack,\n  NumberKeyframeTrack,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\n\nconst AnimationClipCreator = {\n  CreateRotationAnimation(period, axis = 'x') {\n    const times = [0, period],\n      values = [0, 360]\n\n    const trackName = '.rotation[' + axis + ']'\n\n    const track = new NumberKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, period, [track])\n  },\n\n  CreateScaleAxisAnimation(period, axis = 'x') {\n    const times = [0, period],\n      values = [0, 1]\n\n    const trackName = '.scale[' + axis + ']'\n\n    const track = new NumberKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, period, [track])\n  },\n\n  CreateShakeAnimation(duration, shakeScale) {\n    const times = [],\n      values = [],\n      tmp = new Vector3()\n\n    for (let i = 0; i < duration * 10; i++) {\n      times.push(i / 10)\n\n      tmp\n        .set(Math.random() * 2.0 - 1.0, Math.random() * 2.0 - 1.0, Math.random() * 2.0 - 1.0)\n        .multiply(shakeScale)\n        .toArray(values, values.length)\n    }\n\n    const trackName = '.position'\n\n    const track = new VectorKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n\n  CreatePulsationAnimation(duration, pulseScale) {\n    const times = [],\n      values = [],\n      tmp = new Vector3()\n\n    for (let i = 0; i < duration * 10; i++) {\n      times.push(i / 10)\n\n      const scaleFactor = Math.random() * pulseScale\n      tmp.set(scaleFactor, scaleFactor, scaleFactor).toArray(values, values.length)\n    }\n\n    const trackName = '.scale'\n\n    const track = new VectorKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n\n  CreateVisibilityAnimation(duration) {\n    const times = [0, duration / 2, duration],\n      values = [true, false, true]\n\n    const trackName = '.visible'\n\n    const track = new BooleanKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n\n  CreateMaterialColorAnimation(duration, colors) {\n    const times = [],\n      values = [],\n      timeStep = duration / colors.length\n\n    for (let i = 0; i < colors.length; i++) {\n      times.push(i * timeStep)\n\n      const color = colors[i]\n      values.push(color.r, color.g, color.b)\n    }\n\n    const trackName = '.material.color'\n\n    const track = new ColorKeyframeTrack(trackName, times, values)\n\n    return new AnimationClip(null, duration, [track])\n  },\n}\n\nexport { AnimationClipCreator }\n"], "mappings": ";AASK,MAACA,oBAAA,GAAuB;EAC3BC,wBAAwBC,MAAA,EAAQC,IAAA,GAAO,KAAK;IAC1C,MAAMC,KAAA,GAAQ,CAAC,GAAGF,MAAM;MACtBG,MAAA,GAAS,CAAC,GAAG,GAAG;IAElB,MAAMC,SAAA,GAAY,eAAeH,IAAA,GAAO;IAExC,MAAMI,KAAA,GAAQ,IAAIC,mBAAA,CAAoBF,SAAA,EAAWF,KAAA,EAAOC,MAAM;IAE9D,OAAO,IAAII,aAAA,CAAc,MAAMP,MAAA,EAAQ,CAACK,KAAK,CAAC;EAC/C;EAEDG,yBAAyBR,MAAA,EAAQC,IAAA,GAAO,KAAK;IAC3C,MAAMC,KAAA,GAAQ,CAAC,GAAGF,MAAM;MACtBG,MAAA,GAAS,CAAC,GAAG,CAAC;IAEhB,MAAMC,SAAA,GAAY,YAAYH,IAAA,GAAO;IAErC,MAAMI,KAAA,GAAQ,IAAIC,mBAAA,CAAoBF,SAAA,EAAWF,KAAA,EAAOC,MAAM;IAE9D,OAAO,IAAII,aAAA,CAAc,MAAMP,MAAA,EAAQ,CAACK,KAAK,CAAC;EAC/C;EAEDI,qBAAqBC,QAAA,EAAUC,UAAA,EAAY;IACzC,MAAMT,KAAA,GAAQ,EAAE;MACdC,MAAA,GAAS,EAAE;MACXS,GAAA,GAAM,IAAIC,OAAA,CAAS;IAErB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,QAAA,GAAW,IAAII,CAAA,IAAK;MACtCZ,KAAA,CAAMa,IAAA,CAAKD,CAAA,GAAI,EAAE;MAEjBF,GAAA,CACGI,GAAA,CAAIC,IAAA,CAAKC,MAAA,CAAM,IAAK,IAAM,GAAKD,IAAA,CAAKC,MAAA,CAAQ,IAAG,IAAM,GAAKD,IAAA,CAAKC,MAAA,CAAM,IAAK,IAAM,CAAG,EACnFC,QAAA,CAASR,UAAU,EACnBS,OAAA,CAAQjB,MAAA,EAAQA,MAAA,CAAOkB,MAAM;IACjC;IAED,MAAMjB,SAAA,GAAY;IAElB,MAAMC,KAAA,GAAQ,IAAIiB,mBAAA,CAAoBlB,SAAA,EAAWF,KAAA,EAAOC,MAAM;IAE9D,OAAO,IAAII,aAAA,CAAc,MAAMG,QAAA,EAAU,CAACL,KAAK,CAAC;EACjD;EAEDkB,yBAAyBb,QAAA,EAAUc,UAAA,EAAY;IAC7C,MAAMtB,KAAA,GAAQ,EAAE;MACdC,MAAA,GAAS,EAAE;MACXS,GAAA,GAAM,IAAIC,OAAA,CAAS;IAErB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIJ,QAAA,GAAW,IAAII,CAAA,IAAK;MACtCZ,KAAA,CAAMa,IAAA,CAAKD,CAAA,GAAI,EAAE;MAEjB,MAAMW,WAAA,GAAcR,IAAA,CAAKC,MAAA,CAAM,IAAKM,UAAA;MACpCZ,GAAA,CAAII,GAAA,CAAIS,WAAA,EAAaA,WAAA,EAAaA,WAAW,EAAEL,OAAA,CAAQjB,MAAA,EAAQA,MAAA,CAAOkB,MAAM;IAC7E;IAED,MAAMjB,SAAA,GAAY;IAElB,MAAMC,KAAA,GAAQ,IAAIiB,mBAAA,CAAoBlB,SAAA,EAAWF,KAAA,EAAOC,MAAM;IAE9D,OAAO,IAAII,aAAA,CAAc,MAAMG,QAAA,EAAU,CAACL,KAAK,CAAC;EACjD;EAEDqB,0BAA0BhB,QAAA,EAAU;IAClC,MAAMR,KAAA,GAAQ,CAAC,GAAGQ,QAAA,GAAW,GAAGA,QAAQ;MACtCP,MAAA,GAAS,CAAC,MAAM,OAAO,IAAI;IAE7B,MAAMC,SAAA,GAAY;IAElB,MAAMC,KAAA,GAAQ,IAAIsB,oBAAA,CAAqBvB,SAAA,EAAWF,KAAA,EAAOC,MAAM;IAE/D,OAAO,IAAII,aAAA,CAAc,MAAMG,QAAA,EAAU,CAACL,KAAK,CAAC;EACjD;EAEDuB,6BAA6BlB,QAAA,EAAUmB,MAAA,EAAQ;IAC7C,MAAM3B,KAAA,GAAQ,EAAE;MACdC,MAAA,GAAS,EAAE;MACX2B,QAAA,GAAWpB,QAAA,GAAWmB,MAAA,CAAOR,MAAA;IAE/B,SAASP,CAAA,GAAI,GAAGA,CAAA,GAAIe,MAAA,CAAOR,MAAA,EAAQP,CAAA,IAAK;MACtCZ,KAAA,CAAMa,IAAA,CAAKD,CAAA,GAAIgB,QAAQ;MAEvB,MAAMC,KAAA,GAAQF,MAAA,CAAOf,CAAC;MACtBX,MAAA,CAAOY,IAAA,CAAKgB,KAAA,CAAMC,CAAA,EAAGD,KAAA,CAAME,CAAA,EAAGF,KAAA,CAAMG,CAAC;IACtC;IAED,MAAM9B,SAAA,GAAY;IAElB,MAAMC,KAAA,GAAQ,IAAI8B,kBAAA,CAAmB/B,SAAA,EAAWF,KAAA,EAAOC,MAAM;IAE7D,OAAO,IAAII,aAAA,CAAc,MAAMG,QAAA,EAAU,CAACL,KAAK,CAAC;EACjD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}