const express = require('express');
const cors = require('cors');
require('dotenv').config();

const { connectDB } = require('./config/database');
const logger = require('./utils/logger');

// Import services
const ActivityLogService = require('./services/activityLogService');

// Simple mock auth middleware for testing
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  // Mock user for testing
  req.user = {
    id: 1,
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'Admin'
  };

  next();
};

const app = express();
const PORT = 8001; // Use a different port to avoid conflicts

// Basic middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'Activity Logs Server'
  });
});

// Auth routes (simplified)
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Simple mock authentication for testing
    if (email === '<EMAIL>' && password === 'admin123') {
      const token = 'mock-jwt-token-admin';
      const user = {
        id: 1,
        firstName: 'Admin',
        lastName: 'User',
        email: email,
        role: 'Admin'
      };

      // Log the login activity
      await ActivityLogService.logActivity({
        userEmail: email,
        userName: `${user.firstName} ${user.lastName}`,
        userRole: user.role,
        action: 'LOGIN',
        entityType: 'Authentication',
        description: 'User logged in successfully',
        severity: 'INFO',
        ipAddress: req.ip
      });

      res.json({
        success: true,
        message: 'Login successful',
        data: { token, user }
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Activity Logs API Routes
app.get('/api/admin/activity-logs', authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      search,
      action,
      entityType,
      severity,
      userEmail,
      startDate,
      endDate
    } = req.query;

    const filters = {
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      action,
      entityType,
      severity,
      userEmail,
      startDate,
      endDate
    };

    const result = await ActivityLogService.getActivityLogs(filters);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Get activity logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve activity logs'
    });
  }
});

app.get('/api/admin/activity-logs/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await ActivityLogService.getActivityStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Get activity stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve activity statistics'
    });
  }
});

app.get('/api/admin/activity-logs/actions', authenticateToken, async (req, res) => {
  try {
    const actions = await ActivityLogService.getAvailableActions();
    
    res.json({
      success: true,
      data: actions
    });
  } catch (error) {
    logger.error('Get actions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve available actions'
    });
  }
});

app.get('/api/admin/activity-logs/entity-types', authenticateToken, async (req, res) => {
  try {
    const entityTypes = await ActivityLogService.getAvailableEntityTypes();
    
    res.json({
      success: true,
      data: entityTypes
    });
  } catch (error) {
    logger.error('Get entity types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve available entity types'
    });
  }
});

// Admin dashboard route (for testing middleware)
app.get('/api/admin/dashboard', authenticateToken, async (req, res) => {
  try {
    // Log dashboard access
    await ActivityLogService.logActivity({
      userEmail: req.user?.email || 'unknown',
      userName: req.user?.name || 'Unknown User',
      userRole: req.user?.role || 'Unknown',
      action: 'VIEW',
      entityType: 'Dashboard',
      description: 'Accessed admin dashboard',
      severity: 'INFO',
      ipAddress: req.ip
    });

    res.json({
      success: true,
      data: {
        message: 'Dashboard data',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load dashboard'
    });
  }
});

// Error handling
app.use((error, req, res, next) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// Start server
async function startServer() {
  try {
    console.log('🚀 Starting Activity Logs Server...');

    // Connect to database
    console.log('📊 Connecting to database...');
    await connectDB();
    console.log('✅ Database connected successfully');

    // Start server
    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log(`✅ Activity Logs Server running on http://localhost:${PORT}`);
      console.log(`✅ Server also accessible on http://0.0.0.0:${PORT}`);
      console.log('🎯 Server is ready to accept connections');
    });

    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
      } else {
        console.error('❌ Server error:', error);
      }
      process.exit(1);
    });

    return server;

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Handle shutdown
process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

// Start the server
startServer();
