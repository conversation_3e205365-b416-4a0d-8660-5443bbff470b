{"ast": null, "code": "function clamp(v, min, max) {\n  return Math.max(min, Math.min(v, max));\n}\nconst V = {\n  toVector(v, fallback) {\n    if (v === undefined) v = fallback;\n    return Array.isArray(v) ? v : [v, v];\n  },\n  add(v1, v2) {\n    return [v1[0] + v2[0], v1[1] + v2[1]];\n  },\n  sub(v1, v2) {\n    return [v1[0] - v2[0], v1[1] - v2[1]];\n  },\n  addTo(v1, v2) {\n    v1[0] += v2[0];\n    v1[1] += v2[1];\n  },\n  subTo(v1, v2) {\n    v1[0] -= v2[0];\n    v1[1] -= v2[1];\n  }\n};\nfunction rubberband(distance, dimension, constant) {\n  if (dimension === 0 || Math.abs(dimension) === Infinity) return Math.pow(distance, constant * 5);\n  return distance * dimension * constant / (dimension + constant * distance);\n}\nfunction rubberbandIfOutOfBounds(position, min, max, constant = 0.15) {\n  if (constant === 0) return clamp(position, min, max);\n  if (position < min) return -rubberband(min - position, max - min, constant) + min;\n  if (position > max) return +rubberband(position - max, max - min, constant) + max;\n  return position;\n}\nfunction computeRubberband(bounds, [Vx, Vy], [Rx, Ry]) {\n  const [[X0, X1], [Y0, Y1]] = bounds;\n  return [rubberbandIfOutOfBounds(Vx, X0, X1, Rx), rubberbandIfOutOfBounds(Vy, Y0, Y1, Ry)];\n}\nexport { V, computeRubberband as c, rubberbandIfOutOfBounds as r };", "map": {"version": 3, "names": ["clamp", "v", "min", "max", "Math", "V", "toVector", "fallback", "undefined", "Array", "isArray", "add", "v1", "v2", "sub", "addTo", "subTo", "rubberband", "distance", "dimension", "constant", "abs", "Infinity", "pow", "rubberbandIfOutOfBounds", "position", "computeRubberband", "bounds", "Vx", "Vy", "Rx", "<PERSON><PERSON>", "X0", "X1", "Y0", "Y1", "c", "r"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@use-gesture/core/dist/maths-0ab39ae9.esm.js"], "sourcesContent": ["function clamp(v, min, max) {\n  return Math.max(min, Math.min(v, max));\n}\nconst V = {\n  toVector(v, fallback) {\n    if (v === undefined) v = fallback;\n    return Array.isArray(v) ? v : [v, v];\n  },\n  add(v1, v2) {\n    return [v1[0] + v2[0], v1[1] + v2[1]];\n  },\n  sub(v1, v2) {\n    return [v1[0] - v2[0], v1[1] - v2[1]];\n  },\n  addTo(v1, v2) {\n    v1[0] += v2[0];\n    v1[1] += v2[1];\n  },\n  subTo(v1, v2) {\n    v1[0] -= v2[0];\n    v1[1] -= v2[1];\n  }\n};\nfunction rubberband(distance, dimension, constant) {\n  if (dimension === 0 || Math.abs(dimension) === Infinity) return Math.pow(distance, constant * 5);\n  return distance * dimension * constant / (dimension + constant * distance);\n}\nfunction rubberbandIfOutOfBounds(position, min, max, constant = 0.15) {\n  if (constant === 0) return clamp(position, min, max);\n  if (position < min) return -rubberband(min - position, max - min, constant) + min;\n  if (position > max) return +rubberband(position - max, max - min, constant) + max;\n  return position;\n}\nfunction computeRubberband(bounds, [Vx, Vy], [Rx, Ry]) {\n  const [[X0, X1], [Y0, Y1]] = bounds;\n  return [rubberbandIfOutOfBounds(Vx, X0, X1, Rx), rubberbandIfOutOfBounds(Vy, Y0, Y1, Ry)];\n}\n\nexport { V, computeRubberband as c, rubberbandIfOutOfBounds as r };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC1B,OAAOC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAEE,IAAI,CAACF,GAAG,CAACD,CAAC,EAAEE,GAAG,CAAC,CAAC;AACxC;AACA,MAAME,CAAC,GAAG;EACRC,QAAQA,CAACL,CAAC,EAAEM,QAAQ,EAAE;IACpB,IAAIN,CAAC,KAAKO,SAAS,EAAEP,CAAC,GAAGM,QAAQ;IACjC,OAAOE,KAAK,CAACC,OAAO,CAACT,CAAC,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,EAAEA,CAAC,CAAC;EACtC,CAAC;EACDU,GAAGA,CAACC,EAAE,EAAEC,EAAE,EAAE;IACV,OAAO,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;EACDC,GAAGA,CAACF,EAAE,EAAEC,EAAE,EAAE;IACV,OAAO,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;EACDE,KAAKA,CAACH,EAAE,EAAEC,EAAE,EAAE;IACZD,EAAE,CAAC,CAAC,CAAC,IAAIC,EAAE,CAAC,CAAC,CAAC;IACdD,EAAE,CAAC,CAAC,CAAC,IAAIC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;EACDG,KAAKA,CAACJ,EAAE,EAAEC,EAAE,EAAE;IACZD,EAAE,CAAC,CAAC,CAAC,IAAIC,EAAE,CAAC,CAAC,CAAC;IACdD,EAAE,CAAC,CAAC,CAAC,IAAIC,EAAE,CAAC,CAAC,CAAC;EAChB;AACF,CAAC;AACD,SAASI,UAAUA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EACjD,IAAID,SAAS,KAAK,CAAC,IAAIf,IAAI,CAACiB,GAAG,CAACF,SAAS,CAAC,KAAKG,QAAQ,EAAE,OAAOlB,IAAI,CAACmB,GAAG,CAACL,QAAQ,EAAEE,QAAQ,GAAG,CAAC,CAAC;EAChG,OAAOF,QAAQ,GAAGC,SAAS,GAAGC,QAAQ,IAAID,SAAS,GAAGC,QAAQ,GAAGF,QAAQ,CAAC;AAC5E;AACA,SAASM,uBAAuBA,CAACC,QAAQ,EAAEvB,GAAG,EAAEC,GAAG,EAAEiB,QAAQ,GAAG,IAAI,EAAE;EACpE,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAOpB,KAAK,CAACyB,QAAQ,EAAEvB,GAAG,EAAEC,GAAG,CAAC;EACpD,IAAIsB,QAAQ,GAAGvB,GAAG,EAAE,OAAO,CAACe,UAAU,CAACf,GAAG,GAAGuB,QAAQ,EAAEtB,GAAG,GAAGD,GAAG,EAAEkB,QAAQ,CAAC,GAAGlB,GAAG;EACjF,IAAIuB,QAAQ,GAAGtB,GAAG,EAAE,OAAO,CAACc,UAAU,CAACQ,QAAQ,GAAGtB,GAAG,EAAEA,GAAG,GAAGD,GAAG,EAAEkB,QAAQ,CAAC,GAAGjB,GAAG;EACjF,OAAOsB,QAAQ;AACjB;AACA,SAASC,iBAAiBA,CAACC,MAAM,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAE;EACrD,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC,GAAGR,MAAM;EACnC,OAAO,CAACH,uBAAuB,CAACI,EAAE,EAAEI,EAAE,EAAEC,EAAE,EAAEH,EAAE,CAAC,EAAEN,uBAAuB,CAACK,EAAE,EAAEK,EAAE,EAAEC,EAAE,EAAEJ,EAAE,CAAC,CAAC;AAC3F;AAEA,SAAS1B,CAAC,EAAEqB,iBAAiB,IAAIU,CAAC,EAAEZ,uBAAuB,IAAIa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}