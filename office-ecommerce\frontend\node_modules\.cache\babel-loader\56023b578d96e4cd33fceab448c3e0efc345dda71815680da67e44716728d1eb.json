{"ast": null, "code": "import { Vector2 } from \"three\";\nconst DotScreenShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    tSize: {\n      value: /* @__PURE__ */new Vector2(256, 256)\n    },\n    center: {\n      value: /* @__PURE__ */new Vector2(0.5, 0.5)\n    },\n    angle: {\n      value: 1.57\n    },\n    scale: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform vec2 center;\n    uniform float angle;\n    uniform float scale;\n    uniform vec2 tSize;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    float pattern() {\n\n    \tfloat s = sin( angle ), c = cos( angle );\n\n    \tvec2 tex = vUv * tSize - center;\n    \tvec2 point = vec2( c * tex.x - s * tex.y, s * tex.x + c * tex.y ) * scale;\n\n    \treturn ( sin( point.x ) * sin( point.y ) ) * 4.0;\n\n    }\n\n    void main() {\n\n    \tvec4 color = texture2D( tDiffuse, vUv );\n\n    \tfloat average = ( color.r + color.g + color.b ) / 3.0;\n\n    \tgl_FragColor = vec4( vec3( average * 10.0 - 5.0 + pattern() ), color.a );\n\n    }\n  `)\n};\nexport { DotScreenShader };", "map": {"version": 3, "names": ["DotScreenShader", "uniforms", "tDiffuse", "value", "tSize", "Vector2", "center", "angle", "scale", "vertexShader", "fragmentShader"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\shaders\\DotScreenShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Dot screen shader\n * based on glfx.js sepia shader\n * https://github.com/evanw/glfx.js\n */\n\nexport const DotScreenShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    tSize: { value: /* @__PURE__ */ new Vector2(256, 256) },\n    center: { value: /* @__PURE__ */ new Vector2(0.5, 0.5) },\n    angle: { value: 1.57 },\n    scale: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec2 center;\n    uniform float angle;\n    uniform float scale;\n    uniform vec2 tSize;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    float pattern() {\n\n    \tfloat s = sin( angle ), c = cos( angle );\n\n    \tvec2 tex = vUv * tSize - center;\n    \tvec2 point = vec2( c * tex.x - s * tex.y, s * tex.x + c * tex.y ) * scale;\n\n    \treturn ( sin( point.x ) * sin( point.y ) ) * 4.0;\n\n    }\n\n    void main() {\n\n    \tvec4 color = texture2D( tDiffuse, vUv );\n\n    \tfloat average = ( color.r + color.g + color.b ) / 3.0;\n\n    \tgl_FragColor = vec4( vec3( average * 10.0 - 5.0 + pattern() ), color.a );\n\n    }\n  `,\n}\n"], "mappings": ";AAQO,MAAMA,eAAA,GAAkB;EAC7BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,KAAA,EAAO;MAAED,KAAA,qBAA2BE,OAAA,CAAQ,KAAK,GAAG;IAAE;IACtDC,MAAA,EAAQ;MAAEH,KAAA,qBAA2BE,OAAA,CAAQ,KAAK,GAAG;IAAE;IACvDE,KAAA,EAAO;MAAEJ,KAAA,EAAO;IAAK;IACrBK,KAAA,EAAO;MAAEL,KAAA,EAAO;IAAI;EACtB;EAEAM,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+B7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}