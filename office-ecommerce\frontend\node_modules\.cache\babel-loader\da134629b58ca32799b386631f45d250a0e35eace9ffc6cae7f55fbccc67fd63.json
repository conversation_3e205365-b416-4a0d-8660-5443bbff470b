{"ast": null, "code": "// Centralized API Configuration\n// This service provides configuration for all API communications\n\nclass ApiConfig {\n  constructor() {\n    // Base API URL from environment variables\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    this.apiVersion = process.env.REACT_APP_API_VERSION || 'v1';\n    this.websocketURL = process.env.REACT_APP_WEBSOCKET_URL || process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n    // Environment settings\n    this.environment = process.env.REACT_APP_ENVIRONMENT || 'development';\n    this.debugMode = process.env.REACT_APP_DEBUG_MODE === 'true';\n    this.logLevel = process.env.REACT_APP_LOG_LEVEL || 'info';\n\n    // Feature flags\n    this.features = {\n      configurator3D: process.env.REACT_APP_ENABLE_3D_CONFIGURATOR === 'true',\n      realTimeUpdates: process.env.REACT_APP_ENABLE_REAL_TIME_UPDATES === 'true',\n      adminDashboard: process.env.REACT_APP_ENABLE_ADMIN_DASHBOARD === 'true',\n      paymentProcessing: process.env.REACT_APP_ENABLE_PAYMENT_PROCESSING === 'true'\n    };\n\n    // Payment configuration\n    this.payment = {\n      paymongoPublicKey: process.env.REACT_APP_PAYMONGO_PUBLIC_KEY || ''\n    };\n\n    // Asset paths\n    this.assets = {\n      modelsPath: process.env.REACT_APP_MODELS_PATH || '/models',\n      imagesPath: process.env.REACT_APP_IMAGES_PATH || '/images',\n      assetsURL: process.env.REACT_APP_ASSETS_URL || '/'\n    };\n\n    // API endpoints\n    this.endpoints = {\n      auth: '/api/auth',\n      products: '/api/products',\n      inventory: '/api/inventory',\n      orders: '/api/orders',\n      suppliers: '/api/suppliers',\n      admin: '/api/admin',\n      payments: '/api/payments',\n      health: '/health'\n    };\n  }\n\n  // Get full API URL\n  getApiUrl(endpoint = '') {\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    return `${this.baseURL}${cleanEndpoint}`;\n  }\n\n  // Get WebSocket URL\n  getWebSocketUrl() {\n    return this.websocketURL;\n  }\n\n  // Get endpoint URL\n  getEndpoint(name) {\n    const endpoint = this.endpoints[name];\n    if (!endpoint) {\n      throw new Error(`Unknown endpoint: ${name}`);\n    }\n    return this.getApiUrl(endpoint);\n  }\n\n  // Check if feature is enabled\n  isFeatureEnabled(featureName) {\n    return this.features[featureName] || false;\n  }\n\n  // Get asset URL\n  getAssetUrl(path, type = 'assets') {\n    const basePath = this.assets[`${type}Path`] || this.assets.assetsURL;\n    const cleanPath = path.startsWith('/') ? path.substring(1) : path;\n    return `${basePath}/${cleanPath}`;\n  }\n\n  // Get payment configuration\n  getPaymentConfig() {\n    return this.payment;\n  }\n\n  // Log configuration (for debugging)\n  logConfig() {\n    if (this.debugMode) {\n      console.group('🔧 API Configuration');\n      console.log('Base URL:', this.baseURL);\n      console.log('WebSocket URL:', this.websocketURL);\n      console.log('Environment:', this.environment);\n      console.log('Features:', this.features);\n      console.log('Endpoints:', this.endpoints);\n      console.groupEnd();\n    }\n  }\n\n  // Validate configuration\n  validateConfig() {\n    const errors = [];\n    if (!this.baseURL) {\n      errors.push('REACT_APP_API_URL is required');\n    }\n    if (this.features.paymentProcessing && !this.payment.paymongoPublicKey) {\n      errors.push('REACT_APP_PAYMONGO_PUBLIC_KEY is required when payment processing is enabled');\n    }\n    if (errors.length > 0) {\n      console.error('❌ API Configuration Errors:', errors);\n      return false;\n    }\n    return true;\n  }\n}\n\n// Create singleton instance\nconst apiConfig = new ApiConfig();\n\n// Validate configuration on startup\napiConfig.validateConfig();\n\n// Log configuration in development\nif (apiConfig.debugMode) {\n  apiConfig.logConfig();\n}\nexport default apiConfig;", "map": {"version": 3, "names": ["ApiConfig", "constructor", "baseURL", "process", "env", "REACT_APP_API_URL", "apiVersion", "REACT_APP_API_VERSION", "websocketURL", "REACT_APP_WEBSOCKET_URL", "environment", "REACT_APP_ENVIRONMENT", "debugMode", "REACT_APP_DEBUG_MODE", "logLevel", "REACT_APP_LOG_LEVEL", "features", "configurator3D", "REACT_APP_ENABLE_3D_CONFIGURATOR", "realTimeUpdates", "REACT_APP_ENABLE_REAL_TIME_UPDATES", "adminDashboard", "REACT_APP_ENABLE_ADMIN_DASHBOARD", "paymentProcessing", "REACT_APP_ENABLE_PAYMENT_PROCESSING", "payment", "paymongoPublicKey", "REACT_APP_PAYMONGO_PUBLIC_KEY", "assets", "modelsPath", "REACT_APP_MODELS_PATH", "imagesPath", "REACT_APP_IMAGES_PATH", "assetsURL", "REACT_APP_ASSETS_URL", "endpoints", "auth", "products", "inventory", "orders", "suppliers", "admin", "payments", "health", "getApiUrl", "endpoint", "cleanEndpoint", "startsWith", "getWebSocketUrl", "getEndpoint", "name", "Error", "isFeatureEnabled", "featureName", "getAssetUrl", "path", "type", "basePath", "cleanPath", "substring", "getPaymentConfig", "logConfig", "console", "group", "log", "groupEnd", "validateConfig", "errors", "push", "length", "error", "apiConfig"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/services/apiConfig.js"], "sourcesContent": ["// Centralized API Configuration\n// This service provides configuration for all API communications\n\nclass ApiConfig {\n  constructor() {\n    // Base API URL from environment variables\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    this.apiVersion = process.env.REACT_APP_API_VERSION || 'v1';\n    this.websocketURL = process.env.REACT_APP_WEBSOCKET_URL || process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    \n    // Environment settings\n    this.environment = process.env.REACT_APP_ENVIRONMENT || 'development';\n    this.debugMode = process.env.REACT_APP_DEBUG_MODE === 'true';\n    this.logLevel = process.env.REACT_APP_LOG_LEVEL || 'info';\n    \n    // Feature flags\n    this.features = {\n      configurator3D: process.env.REACT_APP_ENABLE_3D_CONFIGURATOR === 'true',\n      realTimeUpdates: process.env.REACT_APP_ENABLE_REAL_TIME_UPDATES === 'true',\n      adminDashboard: process.env.REACT_APP_ENABLE_ADMIN_DASHBOARD === 'true',\n      paymentProcessing: process.env.REACT_APP_ENABLE_PAYMENT_PROCESSING === 'true'\n    };\n    \n    // Payment configuration\n    this.payment = {\n      paymongoPublicKey: process.env.REACT_APP_PAYMONGO_PUBLIC_KEY || ''\n    };\n    \n    // Asset paths\n    this.assets = {\n      modelsPath: process.env.REACT_APP_MODELS_PATH || '/models',\n      imagesPath: process.env.REACT_APP_IMAGES_PATH || '/images',\n      assetsURL: process.env.REACT_APP_ASSETS_URL || '/'\n    };\n    \n    // API endpoints\n    this.endpoints = {\n      auth: '/api/auth',\n      products: '/api/products',\n      inventory: '/api/inventory',\n      orders: '/api/orders',\n      suppliers: '/api/suppliers',\n      admin: '/api/admin',\n      payments: '/api/payments',\n      health: '/health'\n    };\n  }\n\n  // Get full API URL\n  getApiUrl(endpoint = '') {\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    return `${this.baseURL}${cleanEndpoint}`;\n  }\n\n  // Get WebSocket URL\n  getWebSocketUrl() {\n    return this.websocketURL;\n  }\n\n  // Get endpoint URL\n  getEndpoint(name) {\n    const endpoint = this.endpoints[name];\n    if (!endpoint) {\n      throw new Error(`Unknown endpoint: ${name}`);\n    }\n    return this.getApiUrl(endpoint);\n  }\n\n  // Check if feature is enabled\n  isFeatureEnabled(featureName) {\n    return this.features[featureName] || false;\n  }\n\n  // Get asset URL\n  getAssetUrl(path, type = 'assets') {\n    const basePath = this.assets[`${type}Path`] || this.assets.assetsURL;\n    const cleanPath = path.startsWith('/') ? path.substring(1) : path;\n    return `${basePath}/${cleanPath}`;\n  }\n\n  // Get payment configuration\n  getPaymentConfig() {\n    return this.payment;\n  }\n\n  // Log configuration (for debugging)\n  logConfig() {\n    if (this.debugMode) {\n      console.group('🔧 API Configuration');\n      console.log('Base URL:', this.baseURL);\n      console.log('WebSocket URL:', this.websocketURL);\n      console.log('Environment:', this.environment);\n      console.log('Features:', this.features);\n      console.log('Endpoints:', this.endpoints);\n      console.groupEnd();\n    }\n  }\n\n  // Validate configuration\n  validateConfig() {\n    const errors = [];\n    \n    if (!this.baseURL) {\n      errors.push('REACT_APP_API_URL is required');\n    }\n    \n    if (this.features.paymentProcessing && !this.payment.paymongoPublicKey) {\n      errors.push('REACT_APP_PAYMONGO_PUBLIC_KEY is required when payment processing is enabled');\n    }\n    \n    if (errors.length > 0) {\n      console.error('❌ API Configuration Errors:', errors);\n      return false;\n    }\n    \n    return true;\n  }\n}\n\n// Create singleton instance\nconst apiConfig = new ApiConfig();\n\n// Validate configuration on startup\napiConfig.validateConfig();\n\n// Log configuration in development\nif (apiConfig.debugMode) {\n  apiConfig.logConfig();\n}\n\nexport default apiConfig;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,SAAS,CAAC;EACdC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IACvE,IAAI,CAACC,UAAU,GAAGH,OAAO,CAACC,GAAG,CAACG,qBAAqB,IAAI,IAAI;IAC3D,IAAI,CAACC,YAAY,GAAGL,OAAO,CAACC,GAAG,CAACK,uBAAuB,IAAIN,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;IAEnH;IACA,IAAI,CAACK,WAAW,GAAGP,OAAO,CAACC,GAAG,CAACO,qBAAqB,IAAI,aAAa;IACrE,IAAI,CAACC,SAAS,GAAGT,OAAO,CAACC,GAAG,CAACS,oBAAoB,KAAK,MAAM;IAC5D,IAAI,CAACC,QAAQ,GAAGX,OAAO,CAACC,GAAG,CAACW,mBAAmB,IAAI,MAAM;;IAEzD;IACA,IAAI,CAACC,QAAQ,GAAG;MACdC,cAAc,EAAEd,OAAO,CAACC,GAAG,CAACc,gCAAgC,KAAK,MAAM;MACvEC,eAAe,EAAEhB,OAAO,CAACC,GAAG,CAACgB,kCAAkC,KAAK,MAAM;MAC1EC,cAAc,EAAElB,OAAO,CAACC,GAAG,CAACkB,gCAAgC,KAAK,MAAM;MACvEC,iBAAiB,EAAEpB,OAAO,CAACC,GAAG,CAACoB,mCAAmC,KAAK;IACzE,CAAC;;IAED;IACA,IAAI,CAACC,OAAO,GAAG;MACbC,iBAAiB,EAAEvB,OAAO,CAACC,GAAG,CAACuB,6BAA6B,IAAI;IAClE,CAAC;;IAED;IACA,IAAI,CAACC,MAAM,GAAG;MACZC,UAAU,EAAE1B,OAAO,CAACC,GAAG,CAAC0B,qBAAqB,IAAI,SAAS;MAC1DC,UAAU,EAAE5B,OAAO,CAACC,GAAG,CAAC4B,qBAAqB,IAAI,SAAS;MAC1DC,SAAS,EAAE9B,OAAO,CAACC,GAAG,CAAC8B,oBAAoB,IAAI;IACjD,CAAC;;IAED;IACA,IAAI,CAACC,SAAS,GAAG;MACfC,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,eAAe;MACzBC,SAAS,EAAE,gBAAgB;MAC3BC,MAAM,EAAE,aAAa;MACrBC,SAAS,EAAE,gBAAgB;MAC3BC,KAAK,EAAE,YAAY;MACnBC,QAAQ,EAAE,eAAe;MACzBC,MAAM,EAAE;IACV,CAAC;EACH;;EAEA;EACAC,SAASA,CAACC,QAAQ,GAAG,EAAE,EAAE;IACvB,MAAMC,aAAa,GAAGD,QAAQ,CAACE,UAAU,CAAC,GAAG,CAAC,GAAGF,QAAQ,GAAG,IAAIA,QAAQ,EAAE;IAC1E,OAAO,GAAG,IAAI,CAAC3C,OAAO,GAAG4C,aAAa,EAAE;EAC1C;;EAEA;EACAE,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACxC,YAAY;EAC1B;;EAEA;EACAyC,WAAWA,CAACC,IAAI,EAAE;IAChB,MAAML,QAAQ,GAAG,IAAI,CAACV,SAAS,CAACe,IAAI,CAAC;IACrC,IAAI,CAACL,QAAQ,EAAE;MACb,MAAM,IAAIM,KAAK,CAAC,qBAAqBD,IAAI,EAAE,CAAC;IAC9C;IACA,OAAO,IAAI,CAACN,SAAS,CAACC,QAAQ,CAAC;EACjC;;EAEA;EACAO,gBAAgBA,CAACC,WAAW,EAAE;IAC5B,OAAO,IAAI,CAACrC,QAAQ,CAACqC,WAAW,CAAC,IAAI,KAAK;EAC5C;;EAEA;EACAC,WAAWA,CAACC,IAAI,EAAEC,IAAI,GAAG,QAAQ,EAAE;IACjC,MAAMC,QAAQ,GAAG,IAAI,CAAC7B,MAAM,CAAC,GAAG4B,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC5B,MAAM,CAACK,SAAS;IACpE,MAAMyB,SAAS,GAAGH,IAAI,CAACR,UAAU,CAAC,GAAG,CAAC,GAAGQ,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGJ,IAAI;IACjE,OAAO,GAAGE,QAAQ,IAAIC,SAAS,EAAE;EACnC;;EAEA;EACAE,gBAAgBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACnC,OAAO;EACrB;;EAEA;EACAoC,SAASA,CAAA,EAAG;IACV,IAAI,IAAI,CAACjD,SAAS,EAAE;MAClBkD,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAC;MACrCD,OAAO,CAACE,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC9D,OAAO,CAAC;MACtC4D,OAAO,CAACE,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACxD,YAAY,CAAC;MAChDsD,OAAO,CAACE,GAAG,CAAC,cAAc,EAAE,IAAI,CAACtD,WAAW,CAAC;MAC7CoD,OAAO,CAACE,GAAG,CAAC,WAAW,EAAE,IAAI,CAAChD,QAAQ,CAAC;MACvC8C,OAAO,CAACE,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC7B,SAAS,CAAC;MACzC2B,OAAO,CAACG,QAAQ,CAAC,CAAC;IACpB;EACF;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,MAAMC,MAAM,GAAG,EAAE;IAEjB,IAAI,CAAC,IAAI,CAACjE,OAAO,EAAE;MACjBiE,MAAM,CAACC,IAAI,CAAC,+BAA+B,CAAC;IAC9C;IAEA,IAAI,IAAI,CAACpD,QAAQ,CAACO,iBAAiB,IAAI,CAAC,IAAI,CAACE,OAAO,CAACC,iBAAiB,EAAE;MACtEyC,MAAM,CAACC,IAAI,CAAC,8EAA8E,CAAC;IAC7F;IAEA,IAAID,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;MACrBP,OAAO,CAACQ,KAAK,CAAC,6BAA6B,EAAEH,MAAM,CAAC;MACpD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,MAAMI,SAAS,GAAG,IAAIvE,SAAS,CAAC,CAAC;;AAEjC;AACAuE,SAAS,CAACL,cAAc,CAAC,CAAC;;AAE1B;AACA,IAAIK,SAAS,CAAC3D,SAAS,EAAE;EACvB2D,SAAS,CAACV,SAAS,CAAC,CAAC;AACvB;AAEA,eAAeU,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}