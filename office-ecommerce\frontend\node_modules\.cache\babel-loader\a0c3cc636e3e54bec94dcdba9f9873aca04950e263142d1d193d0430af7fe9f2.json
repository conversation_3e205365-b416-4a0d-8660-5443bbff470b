{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON>Loader, <PERSON>ufferGeometry, Float32BufferAttribute } from \"three\";\nclass XYZLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(text) {\n    const lines = text.split(\"\\n\");\n    const vertices = [];\n    const colors = [];\n    for (let line of lines) {\n      line = line.trim();\n      if (line.charAt(0) === \"#\") continue;\n      const lineValues = line.split(/\\s+/);\n      if (lineValues.length === 3) {\n        vertices.push(parseFloat(lineValues[0]));\n        vertices.push(parseFloat(lineValues[1]));\n        vertices.push(parseFloat(lineValues[2]));\n      }\n      if (lineValues.length === 6) {\n        vertices.push(parseFloat(lineValues[0]));\n        vertices.push(parseFloat(lineValues[1]));\n        vertices.push(parseFloat(lineValues[2]));\n        colors.push(parseFloat(lineValues[3]) / 255);\n        colors.push(parseFloat(lineValues[4]) / 255);\n        colors.push(parseFloat(lineValues[5]) / 255);\n      }\n    }\n    const geometry = new BufferGeometry();\n    geometry.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n    if (colors.length > 0) {\n      geometry.setAttribute(\"color\", new Float32BufferAttribute(colors, 3));\n    }\n    return geometry;\n  }\n}\nexport { XYZLoader };", "map": {"version": 3, "names": ["XYZLoader", "Loader", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "path", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "lines", "split", "vertices", "colors", "line", "trim", "char<PERSON>t", "lineValues", "length", "push", "parseFloat", "geometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\XYZLoader.js"], "sourcesContent": ["import { <PERSON>ufferG<PERSON><PERSON>, FileLoader, Float32<PERSON>uffer<PERSON>ttribute, Loader } from 'three'\n\nclass XYZLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(text) {\n    const lines = text.split('\\n')\n\n    const vertices = []\n    const colors = []\n\n    for (let line of lines) {\n      line = line.trim()\n\n      if (line.charAt(0) === '#') continue // skip comments\n\n      const lineValues = line.split(/\\s+/)\n\n      if (lineValues.length === 3) {\n        // XYZ\n\n        vertices.push(parseFloat(lineValues[0]))\n        vertices.push(parseFloat(lineValues[1]))\n        vertices.push(parseFloat(lineValues[2]))\n      }\n\n      if (lineValues.length === 6) {\n        // XYZRGB\n\n        vertices.push(parseFloat(lineValues[0]))\n        vertices.push(parseFloat(lineValues[1]))\n        vertices.push(parseFloat(lineValues[2]))\n\n        colors.push(parseFloat(lineValues[3]) / 255)\n        colors.push(parseFloat(lineValues[4]) / 255)\n        colors.push(parseFloat(lineValues[5]) / 255)\n      }\n    }\n\n    const geometry = new BufferGeometry()\n    geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n\n    if (colors.length > 0) {\n      geometry.setAttribute('color', new Float32BufferAttribute(colors, 3))\n    }\n\n    return geometry\n  }\n}\n\nexport { XYZLoader }\n"], "mappings": ";AAEA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKC,OAAO;IAC1CF,MAAA,CAAOG,OAAA,CAAQ,KAAKC,IAAI;IACxBJ,MAAA,CAAOK,gBAAA,CAAiB,KAAKC,aAAa;IAC1CN,MAAA,CAAOO,kBAAA,CAAmB,KAAKC,eAAe;IAC9CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMG,OAAA,CAAQY,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMD,IAAA,EAAM;IACV,MAAMM,KAAA,GAAQN,IAAA,CAAKO,KAAA,CAAM,IAAI;IAE7B,MAAMC,QAAA,GAAW,EAAE;IACnB,MAAMC,MAAA,GAAS,EAAE;IAEjB,SAASC,IAAA,IAAQJ,KAAA,EAAO;MACtBI,IAAA,GAAOA,IAAA,CAAKC,IAAA,CAAM;MAElB,IAAID,IAAA,CAAKE,MAAA,CAAO,CAAC,MAAM,KAAK;MAE5B,MAAMC,UAAA,GAAaH,IAAA,CAAKH,KAAA,CAAM,KAAK;MAEnC,IAAIM,UAAA,CAAWC,MAAA,KAAW,GAAG;QAG3BN,QAAA,CAASO,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,CAAC;QACvCL,QAAA,CAASO,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,CAAC;QACvCL,QAAA,CAASO,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,CAAC;MACxC;MAED,IAAIA,UAAA,CAAWC,MAAA,KAAW,GAAG;QAG3BN,QAAA,CAASO,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,CAAC;QACvCL,QAAA,CAASO,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,CAAC;QACvCL,QAAA,CAASO,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,CAAC;QAEvCJ,MAAA,CAAOM,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,IAAI,GAAG;QAC3CJ,MAAA,CAAOM,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,IAAI,GAAG;QAC3CJ,MAAA,CAAOM,IAAA,CAAKC,UAAA,CAAWH,UAAA,CAAW,CAAC,CAAC,IAAI,GAAG;MAC5C;IACF;IAED,MAAMI,QAAA,GAAW,IAAIC,cAAA,CAAgB;IACrCD,QAAA,CAASE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBZ,QAAA,EAAU,CAAC,CAAC;IAEzE,IAAIC,MAAA,CAAOK,MAAA,GAAS,GAAG;MACrBG,QAAA,CAASE,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBX,MAAA,EAAQ,CAAC,CAAC;IACrE;IAED,OAAOQ,QAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}