{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MeshPhysicalMaterial } from 'three';\nimport { useFrame } from '@react-three/fiber';\nimport distort from '../helpers/glsl/distort.vert.glsl.js';\nclass DistortMaterialImpl extends MeshPhysicalMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this.setValues(parameters);\n    this._time = {\n      value: 0\n    };\n    this._distort = {\n      value: 0.4\n    };\n    this._radius = {\n      value: 1\n    };\n  }\n  onBeforeCompile(shader) {\n    shader.uniforms.time = this._time;\n    shader.uniforms.radius = this._radius;\n    shader.uniforms.distort = this._distort;\n    shader.vertexShader = `\n      uniform float time;\n      uniform float radius;\n      uniform float distort;\n      ${distort}\n      ${shader.vertexShader}\n    `;\n    shader.vertexShader = shader.vertexShader.replace('#include <begin_vertex>', `\n        float updateTime = time / 50.0;\n        float noise = snoise(vec3(position / 2.0 + updateTime * 5.0));\n        vec3 transformed = vec3(position * (noise * pow(distort, 2.0) + radius));\n        `);\n  }\n  get time() {\n    return this._time.value;\n  }\n  set time(v) {\n    this._time.value = v;\n  }\n  get distort() {\n    return this._distort.value;\n  }\n  set distort(v) {\n    this._distort.value = v;\n  }\n  get radius() {\n    return this._radius.value;\n  }\n  set radius(v) {\n    this._radius.value = v;\n  }\n}\nconst MeshDistortMaterial = /*#__PURE__*/React.forwardRef(({\n  speed = 1,\n  ...props\n}, ref) => {\n  const [material] = React.useState(() => new DistortMaterialImpl());\n  useFrame(state => material && (material.time = state.clock.getElapsedTime() * speed));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }, props));\n});\nexport { MeshDistortMaterial };", "map": {"version": 3, "names": ["_extends", "React", "MeshPhysicalMaterial", "useFrame", "distort", "DistortMaterialImpl", "constructor", "parameters", "set<PERSON><PERSON><PERSON>", "_time", "value", "_distort", "_radius", "onBeforeCompile", "shader", "uniforms", "time", "radius", "vertexShader", "replace", "v", "MeshDistortMaterial", "forwardRef", "speed", "props", "ref", "material", "useState", "state", "clock", "getElapsedTime", "createElement", "object", "attach"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/MeshDistortMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MeshPhysicalMaterial } from 'three';\nimport { useFrame } from '@react-three/fiber';\nimport distort from '../helpers/glsl/distort.vert.glsl.js';\n\nclass DistortMaterialImpl extends MeshPhysicalMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this.setValues(parameters);\n    this._time = {\n      value: 0\n    };\n    this._distort = {\n      value: 0.4\n    };\n    this._radius = {\n      value: 1\n    };\n  }\n\n  onBeforeCompile(shader) {\n    shader.uniforms.time = this._time;\n    shader.uniforms.radius = this._radius;\n    shader.uniforms.distort = this._distort;\n    shader.vertexShader = `\n      uniform float time;\n      uniform float radius;\n      uniform float distort;\n      ${distort}\n      ${shader.vertexShader}\n    `;\n    shader.vertexShader = shader.vertexShader.replace('#include <begin_vertex>', `\n        float updateTime = time / 50.0;\n        float noise = snoise(vec3(position / 2.0 + updateTime * 5.0));\n        vec3 transformed = vec3(position * (noise * pow(distort, 2.0) + radius));\n        `);\n  }\n\n  get time() {\n    return this._time.value;\n  }\n\n  set time(v) {\n    this._time.value = v;\n  }\n\n  get distort() {\n    return this._distort.value;\n  }\n\n  set distort(v) {\n    this._distort.value = v;\n  }\n\n  get radius() {\n    return this._radius.value;\n  }\n\n  set radius(v) {\n    this._radius.value = v;\n  }\n\n}\n\nconst MeshDistortMaterial = /*#__PURE__*/React.forwardRef(({\n  speed = 1,\n  ...props\n}, ref) => {\n  const [material] = React.useState(() => new DistortMaterialImpl());\n  useFrame(state => material && (material.time = state.clock.getElapsedTime() * speed));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }, props));\n});\n\nexport { MeshDistortMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,OAAO;AAC5C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,sCAAsC;AAE1D,MAAMC,mBAAmB,SAASH,oBAAoB,CAAC;EACrDI,WAAWA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;IAC3B,KAAK,CAACA,UAAU,CAAC;IACjB,IAAI,CAACC,SAAS,CAACD,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,GAAG;MACXC,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG;MACdD,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACE,OAAO,GAAG;MACbF,KAAK,EAAE;IACT,CAAC;EACH;EAEAG,eAAeA,CAACC,MAAM,EAAE;IACtBA,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,IAAI,CAACP,KAAK;IACjCK,MAAM,CAACC,QAAQ,CAACE,MAAM,GAAG,IAAI,CAACL,OAAO;IACrCE,MAAM,CAACC,QAAQ,CAACX,OAAO,GAAG,IAAI,CAACO,QAAQ;IACvCG,MAAM,CAACI,YAAY,GAAG;AAC1B;AACA;AACA;AACA,QAAQd,OAAO;AACf,QAAQU,MAAM,CAACI,YAAY;AAC3B,KAAK;IACDJ,MAAM,CAACI,YAAY,GAAGJ,MAAM,CAACI,YAAY,CAACC,OAAO,CAAC,yBAAyB,EAAE;AACjF;AACA;AACA;AACA,SAAS,CAAC;EACR;EAEA,IAAIH,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACP,KAAK,CAACC,KAAK;EACzB;EAEA,IAAIM,IAAIA,CAACI,CAAC,EAAE;IACV,IAAI,CAACX,KAAK,CAACC,KAAK,GAAGU,CAAC;EACtB;EAEA,IAAIhB,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACO,QAAQ,CAACD,KAAK;EAC5B;EAEA,IAAIN,OAAOA,CAACgB,CAAC,EAAE;IACb,IAAI,CAACT,QAAQ,CAACD,KAAK,GAAGU,CAAC;EACzB;EAEA,IAAIH,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,OAAO,CAACF,KAAK;EAC3B;EAEA,IAAIO,MAAMA,CAACG,CAAC,EAAE;IACZ,IAAI,CAACR,OAAO,CAACF,KAAK,GAAGU,CAAC;EACxB;AAEF;AAEA,MAAMC,mBAAmB,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,CAAC;EACzDC,KAAK,GAAG,CAAC;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM,CAACC,QAAQ,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,MAAM,IAAItB,mBAAmB,CAAC,CAAC,CAAC;EAClEF,QAAQ,CAACyB,KAAK,IAAIF,QAAQ,KAAKA,QAAQ,CAACV,IAAI,GAAGY,KAAK,CAACC,KAAK,CAACC,cAAc,CAAC,CAAC,GAAGP,KAAK,CAAC,CAAC;EACrF,OAAO,aAAatB,KAAK,CAAC8B,aAAa,CAAC,WAAW,EAAE/B,QAAQ,CAAC;IAC5DgC,MAAM,EAAEN,QAAQ;IAChBD,GAAG,EAAEA,GAAG;IACRQ,MAAM,EAAE;EACV,CAAC,EAAET,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}