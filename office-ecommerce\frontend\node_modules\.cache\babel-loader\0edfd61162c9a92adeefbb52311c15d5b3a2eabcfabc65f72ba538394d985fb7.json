{"ast": null, "code": "import { Box3, Vector3, Matrix4 } from 'three';\nimport { CONTAINED } from './Constants.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { intersectTris, intersectClosestTri } from '../utils/GeometryRayIntersectUtilities.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX, SPLIT_AXIS } from './nodeBufferFunctions.js';\nconst boundingBox = new Box3();\nconst boxIntersection = new Vector3();\nconst xyzFields = ['x', 'y', 'z'];\nexport function raycast(nodeIndex32, geometry, side, ray, intersects) {\n  let nodeIndex16 = nodeIndex32 * 2,\n    float32Array = _float32Array,\n    uint16Array = _uint16Array,\n    uint32Array = _uint32Array;\n  const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n  if (isLeaf) {\n    const offset = OFFSET(nodeIndex32, uint32Array);\n    const count = COUNT(nodeIndex16, uint16Array);\n    intersectTris(geometry, side, ray, offset, count, intersects);\n  } else {\n    const leftIndex = LEFT_NODE(nodeIndex32);\n    if (intersectRay(leftIndex, float32Array, ray, boxIntersection)) {\n      raycast(leftIndex, geometry, side, ray, intersects);\n    }\n    const rightIndex = RIGHT_NODE(nodeIndex32, uint32Array);\n    if (intersectRay(rightIndex, float32Array, ray, boxIntersection)) {\n      raycast(rightIndex, geometry, side, ray, intersects);\n    }\n  }\n}\nexport function raycastFirst(nodeIndex32, geometry, side, ray) {\n  let nodeIndex16 = nodeIndex32 * 2,\n    float32Array = _float32Array,\n    uint16Array = _uint16Array,\n    uint32Array = _uint32Array;\n  const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n  if (isLeaf) {\n    const offset = OFFSET(nodeIndex32, uint32Array);\n    const count = COUNT(nodeIndex16, uint16Array);\n    return intersectClosestTri(geometry, side, ray, offset, count);\n  } else {\n    // consider the position of the split plane with respect to the oncoming ray; whichever direction\n    // the ray is coming from, look for an intersection among that side of the tree first\n    const splitAxis = SPLIT_AXIS(nodeIndex32, uint32Array);\n    const xyzAxis = xyzFields[splitAxis];\n    const rayDir = ray.direction[xyzAxis];\n    const leftToRight = rayDir >= 0;\n\n    // c1 is the child to check first\n    let c1, c2;\n    if (leftToRight) {\n      c1 = LEFT_NODE(nodeIndex32);\n      c2 = RIGHT_NODE(nodeIndex32, uint32Array);\n    } else {\n      c1 = RIGHT_NODE(nodeIndex32, uint32Array);\n      c2 = LEFT_NODE(nodeIndex32);\n    }\n    const c1Intersection = intersectRay(c1, float32Array, ray, boxIntersection);\n    const c1Result = c1Intersection ? raycastFirst(c1, geometry, side, ray) : null;\n\n    // if we got an intersection in the first node and it's closer than the second node's bounding\n    // box, we don't need to consider the second node because it couldn't possibly be a better result\n    if (c1Result) {\n      // check if the point is within the second bounds\n      // \"point\" is in the local frame of the bvh\n      const point = c1Result.point[xyzAxis];\n      const isOutside = leftToRight ? point <= float32Array[c2 + splitAxis] :\n      // min bounding data\n      point >= float32Array[c2 + splitAxis + 3]; // max bounding data\n\n      if (isOutside) {\n        return c1Result;\n      }\n    }\n\n    // either there was no intersection in the first node, or there could still be a closer\n    // intersection in the second, so check the second node and then take the better of the two\n    const c2Intersection = intersectRay(c2, float32Array, ray, boxIntersection);\n    const c2Result = c2Intersection ? raycastFirst(c2, geometry, side, ray) : null;\n    if (c1Result && c2Result) {\n      return c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n    } else {\n      return c1Result || c2Result || null;\n    }\n  }\n}\nexport const shapecast = function () {\n  let _box1, _box2;\n  const boxStack = [];\n  const boxPool = new PrimitivePool(() => new Box3());\n  return function shapecast(...args) {\n    _box1 = boxPool.getPrimitive();\n    _box2 = boxPool.getPrimitive();\n    boxStack.push(_box1, _box2);\n    const result = shapecastTraverse(...args);\n    boxPool.releasePrimitive(_box1);\n    boxPool.releasePrimitive(_box2);\n    boxStack.pop();\n    boxStack.pop();\n    const length = boxStack.length;\n    if (length > 0) {\n      _box2 = boxStack[length - 1];\n      _box1 = boxStack[length - 2];\n    }\n    return result;\n  };\n  function shapecastTraverse(nodeIndex32, geometry, intersectsBoundsFunc, intersectsRangeFunc, nodeScoreFunc = null, nodeIndexByteOffset = 0,\n  // offset for unique node identifier\n  depth = 0) {\n    // Define these inside the function so it has access to the local variables needed\n    // when converting to the buffer equivalents\n    function getLeftOffset(nodeIndex32) {\n      let nodeIndex16 = nodeIndex32 * 2,\n        uint16Array = _uint16Array,\n        uint32Array = _uint32Array;\n\n      // traverse until we find a leaf\n      while (!IS_LEAF(nodeIndex16, uint16Array)) {\n        nodeIndex32 = LEFT_NODE(nodeIndex32);\n        nodeIndex16 = nodeIndex32 * 2;\n      }\n      return OFFSET(nodeIndex32, uint32Array);\n    }\n    function getRightEndOffset(nodeIndex32) {\n      let nodeIndex16 = nodeIndex32 * 2,\n        uint16Array = _uint16Array,\n        uint32Array = _uint32Array;\n\n      // traverse until we find a leaf\n      while (!IS_LEAF(nodeIndex16, uint16Array)) {\n        // adjust offset to point to the right node\n        nodeIndex32 = RIGHT_NODE(nodeIndex32, uint32Array);\n        nodeIndex16 = nodeIndex32 * 2;\n      }\n\n      // return the end offset of the triangle range\n      return OFFSET(nodeIndex32, uint32Array) + COUNT(nodeIndex16, uint16Array);\n    }\n    let nodeIndex16 = nodeIndex32 * 2,\n      float32Array = _float32Array,\n      uint16Array = _uint16Array,\n      uint32Array = _uint32Array;\n    const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n    if (isLeaf) {\n      const offset = OFFSET(nodeIndex32, uint32Array);\n      const count = COUNT(nodeIndex16, uint16Array);\n      arrayToBox(BOUNDING_DATA_INDEX(nodeIndex32), float32Array, _box1);\n      return intersectsRangeFunc(offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1);\n    } else {\n      const left = LEFT_NODE(nodeIndex32);\n      const right = RIGHT_NODE(nodeIndex32, uint32Array);\n      let c1 = left;\n      let c2 = right;\n      let score1, score2;\n      let box1, box2;\n      if (nodeScoreFunc) {\n        box1 = _box1;\n        box2 = _box2;\n\n        // bounding data is not offset\n        arrayToBox(BOUNDING_DATA_INDEX(c1), float32Array, box1);\n        arrayToBox(BOUNDING_DATA_INDEX(c2), float32Array, box2);\n        score1 = nodeScoreFunc(box1);\n        score2 = nodeScoreFunc(box2);\n        if (score2 < score1) {\n          c1 = right;\n          c2 = left;\n          const temp = score1;\n          score1 = score2;\n          score2 = temp;\n          box1 = box2;\n          // box2 is always set before use below\n        }\n      }\n\n      // Check box 1 intersection\n      if (!box1) {\n        box1 = _box1;\n        arrayToBox(BOUNDING_DATA_INDEX(c1), float32Array, box1);\n      }\n      const isC1Leaf = IS_LEAF(c1 * 2, uint16Array);\n      const c1Intersection = intersectsBoundsFunc(box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1);\n      let c1StopTraversal;\n      if (c1Intersection === CONTAINED) {\n        const offset = getLeftOffset(c1);\n        const end = getRightEndOffset(c1);\n        const count = end - offset;\n        c1StopTraversal = intersectsRangeFunc(offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1);\n      } else {\n        c1StopTraversal = c1Intersection && shapecastTraverse(c1, geometry, intersectsBoundsFunc, intersectsRangeFunc, nodeScoreFunc, nodeIndexByteOffset, depth + 1);\n      }\n      if (c1StopTraversal) return true;\n\n      // Check box 2 intersection\n      // cached box2 will have been overwritten by previous traversal\n      box2 = _box2;\n      arrayToBox(BOUNDING_DATA_INDEX(c2), float32Array, box2);\n      const isC2Leaf = IS_LEAF(c2 * 2, uint16Array);\n      const c2Intersection = intersectsBoundsFunc(box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2);\n      let c2StopTraversal;\n      if (c2Intersection === CONTAINED) {\n        const offset = getLeftOffset(c2);\n        const end = getRightEndOffset(c2);\n        const count = end - offset;\n        c2StopTraversal = intersectsRangeFunc(offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2);\n      } else {\n        c2StopTraversal = c2Intersection && shapecastTraverse(c2, geometry, intersectsBoundsFunc, intersectsRangeFunc, nodeScoreFunc, nodeIndexByteOffset, depth + 1);\n      }\n      if (c2StopTraversal) return true;\n      return false;\n    }\n  }\n}();\nexport const intersectsGeometry = function () {\n  const triangle = new ExtendedTriangle();\n  const triangle2 = new ExtendedTriangle();\n  const invertedMat = new Matrix4();\n  const obb = new OrientedBox();\n  const obb2 = new OrientedBox();\n  return function intersectsGeometry(nodeIndex32, geometry, otherGeometry, geometryToBvh, cachedObb = null) {\n    let nodeIndex16 = nodeIndex32 * 2,\n      float32Array = _float32Array,\n      uint16Array = _uint16Array,\n      uint32Array = _uint32Array;\n    if (cachedObb === null) {\n      if (!otherGeometry.boundingBox) {\n        otherGeometry.computeBoundingBox();\n      }\n      obb.set(otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh);\n      cachedObb = obb;\n    }\n    const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n    if (isLeaf) {\n      const thisGeometry = geometry;\n      const thisIndex = thisGeometry.index;\n      const thisPos = thisGeometry.attributes.position;\n      const index = otherGeometry.index;\n      const pos = otherGeometry.attributes.position;\n      const offset = OFFSET(nodeIndex32, uint32Array);\n      const count = COUNT(nodeIndex16, uint16Array);\n\n      // get the inverse of the geometry matrix so we can transform our triangles into the\n      // geometry space we're trying to test. We assume there are fewer triangles being checked\n      // here.\n      invertedMat.copy(geometryToBvh).invert();\n      if (otherGeometry.boundsTree) {\n        arrayToBox(BOUNDING_DATA_INDEX(nodeIndex32), float32Array, obb2);\n        obb2.matrix.copy(invertedMat);\n        obb2.needsUpdate = true;\n        const res = otherGeometry.boundsTree.shapecast({\n          intersectsBounds: box => obb2.intersectsBox(box),\n          intersectsTriangle: tri => {\n            tri.a.applyMatrix4(geometryToBvh);\n            tri.b.applyMatrix4(geometryToBvh);\n            tri.c.applyMatrix4(geometryToBvh);\n            tri.needsUpdate = true;\n            for (let i = offset * 3, l = (count + offset) * 3; i < l; i += 3) {\n              // this triangle needs to be transformed into the current BVH coordinate frame\n              setTriangle(triangle2, i, thisIndex, thisPos);\n              triangle2.needsUpdate = true;\n              if (tri.intersectsTriangle(triangle2)) {\n                return true;\n              }\n            }\n            return false;\n          }\n        });\n        return res;\n      } else {\n        for (let i = offset * 3, l = count + offset * 3; i < l; i += 3) {\n          // this triangle needs to be transformed into the current BVH coordinate frame\n          setTriangle(triangle, i, thisIndex, thisPos);\n          triangle.a.applyMatrix4(invertedMat);\n          triangle.b.applyMatrix4(invertedMat);\n          triangle.c.applyMatrix4(invertedMat);\n          triangle.needsUpdate = true;\n          for (let i2 = 0, l2 = index.count; i2 < l2; i2 += 3) {\n            setTriangle(triangle2, i2, index, pos);\n            triangle2.needsUpdate = true;\n            if (triangle.intersectsTriangle(triangle2)) {\n              return true;\n            }\n          }\n        }\n      }\n    } else {\n      const left = nodeIndex32 + 8;\n      const right = uint32Array[nodeIndex32 + 6];\n      arrayToBox(BOUNDING_DATA_INDEX(left), float32Array, boundingBox);\n      const leftIntersection = cachedObb.intersectsBox(boundingBox) && intersectsGeometry(left, geometry, otherGeometry, geometryToBvh, cachedObb);\n      if (leftIntersection) return true;\n      arrayToBox(BOUNDING_DATA_INDEX(right), float32Array, boundingBox);\n      const rightIntersection = cachedObb.intersectsBox(boundingBox) && intersectsGeometry(right, geometry, otherGeometry, geometryToBvh, cachedObb);\n      if (rightIntersection) return true;\n      return false;\n    }\n  };\n}();\nfunction intersectRay(nodeIndex32, array, ray, target) {\n  arrayToBox(nodeIndex32, array, boundingBox);\n  return ray.intersectBox(boundingBox, target);\n}\nconst bufferStack = [];\nlet _prevBuffer;\nlet _float32Array;\nlet _uint16Array;\nlet _uint32Array;\nexport function setBuffer(buffer) {\n  if (_prevBuffer) {\n    bufferStack.push(_prevBuffer);\n  }\n  _prevBuffer = buffer;\n  _float32Array = new Float32Array(buffer);\n  _uint16Array = new Uint16Array(buffer);\n  _uint32Array = new Uint32Array(buffer);\n}\nexport function clearBuffer() {\n  _prevBuffer = null;\n  _float32Array = null;\n  _uint16Array = null;\n  _uint32Array = null;\n  if (bufferStack.length) {\n    setBuffer(bufferStack.pop());\n  }\n}", "map": {"version": 3, "names": ["Box3", "Vector3", "Matrix4", "CONTAINED", "OrientedBox", "ExtendedTriangle", "intersectTris", "intersectClosestTri", "set<PERSON>riangle", "arrayToBox", "PrimitivePool", "COUNT", "OFFSET", "LEFT_NODE", "RIGHT_NODE", "IS_LEAF", "BOUNDING_DATA_INDEX", "SPLIT_AXIS", "boundingBox", "boxIntersection", "xyzFields", "raycast", "nodeIndex32", "geometry", "side", "ray", "intersects", "nodeIndex16", "float32Array", "_float32Array", "uint16Array", "_uint16Array", "uint32Array", "_uint32Array", "<PERSON><PERSON><PERSON><PERSON>", "offset", "count", "leftIndex", "intersectRay", "rightIndex", "raycastFirst", "splitAxis", "xyzAxis", "rayDir", "direction", "leftToRight", "c1", "c2", "c1Intersection", "c1Result", "point", "isOutside", "c2Intersection", "c2Result", "distance", "shapecast", "_box1", "_box2", "boxStack", "boxPool", "args", "getPrimitive", "push", "result", "shapecastTraverse", "releasePrimitive", "pop", "length", "intersectsBoundsFunc", "intersectsRangeFunc", "nodeScoreFunc", "nodeIndexByteOffset", "depth", "getLeftOffset", "getRightEndOffset", "left", "right", "score1", "score2", "box1", "box2", "temp", "isC1Leaf", "c1StopTraversal", "end", "isC2Leaf", "c2StopTraversal", "intersectsGeometry", "triangle", "triangle2", "invertedMat", "obb", "obb2", "otherGeometry", "geometryToBvh", "cachedObb", "computeBoundingBox", "set", "min", "max", "thisGeometry", "thisIndex", "index", "thisPos", "attributes", "position", "pos", "copy", "invert", "boundsTree", "matrix", "needsUpdate", "res", "intersectsBounds", "box", "intersectsBox", "intersectsTriangle", "tri", "a", "applyMatrix4", "b", "c", "i", "l", "i2", "l2", "leftIntersection", "rightIntersection", "array", "target", "intersectBox", "bufferStack", "_prevBuffer", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "Float32Array", "Uint16Array", "Uint32Array", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/core/castFunctions.js"], "sourcesContent": ["import { Box3, Vector3, Matrix4 } from 'three';\nimport { CONTAINED } from './Constants.js';\n\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { intersectTris, intersectClosestTri } from '../utils/GeometryRayIntersectUtilities.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX, SPLIT_AXIS } from './nodeBufferFunctions.js';\n\nconst boundingBox = new Box3();\nconst boxIntersection = new Vector3();\nconst xyzFields = [ 'x', 'y', 'z' ];\n\nexport function raycast( nodeIndex32, geometry, side, ray, intersects ) {\n\n\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\tintersectTris( geometry, side, ray, offset, count, intersects );\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, boxIntersection ) ) {\n\n\t\t\traycast( leftIndex, geometry, side, ray, intersects );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, boxIntersection ) ) {\n\n\t\t\traycast( rightIndex, geometry, side, ray, intersects );\n\n\t\t}\n\n\t}\n\n}\n\nexport function raycastFirst( nodeIndex32, geometry, side, ray ) {\n\n\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\treturn intersectClosestTri( geometry, side, ray, offset, count );\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, boxIntersection );\n\t\tconst c1Result = c1Intersection ? raycastFirst( c1, geometry, side, ray ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, boxIntersection );\n\t\tconst c2Result = c2Intersection ? raycastFirst( c2, geometry, side, ray ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport const shapecast = ( function () {\n\n\tlet _box1, _box2;\n\tconst boxStack = [];\n\tconst boxPool = new PrimitivePool( () => new Box3() );\n\n\treturn function shapecast( ...args ) {\n\n\t\t_box1 = boxPool.getPrimitive();\n\t\t_box2 = boxPool.getPrimitive();\n\t\tboxStack.push( _box1, _box2 );\n\n\t\tconst result = shapecastTraverse( ...args );\n\n\t\tboxPool.releasePrimitive( _box1 );\n\t\tboxPool.releasePrimitive( _box2 );\n\t\tboxStack.pop();\n\t\tboxStack.pop();\n\n\t\tconst length = boxStack.length;\n\t\tif ( length > 0 ) {\n\n\t\t\t_box2 = boxStack[ length - 1 ];\n\t\t\t_box1 = boxStack[ length - 2 ];\n\n\t\t}\n\n\t\treturn result;\n\n\t};\n\n\tfunction shapecastTraverse(\n\t\tnodeIndex32,\n\t\tgeometry,\n\t\tintersectsBoundsFunc,\n\t\tintersectsRangeFunc,\n\t\tnodeScoreFunc = null,\n\t\tnodeIndexByteOffset = 0, // offset for unique node identifier\n\t\tdepth = 0\n\t) {\n\n\t\t// Define these inside the function so it has access to the local variables needed\n\t\t// when converting to the buffer equivalents\n\t\tfunction getLeftOffset( nodeIndex32 ) {\n\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\tnodeIndex32 = LEFT_NODE( nodeIndex32 );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\treturn OFFSET( nodeIndex32, uint32Array );\n\n\t\t}\n\n\t\tfunction getRightEndOffset( nodeIndex32 ) {\n\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\t// adjust offset to point to the right node\n\t\t\t\tnodeIndex32 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\t// return the end offset of the triangle range\n\t\t\treturn OFFSET( nodeIndex32, uint32Array ) + COUNT( nodeIndex16, uint16Array );\n\n\t\t}\n\n\t\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, _box1 );\n\t\t\treturn intersectsRangeFunc( offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1 );\n\n\t\t} else {\n\n\t\t\tconst left = LEFT_NODE( nodeIndex32 );\n\t\t\tconst right = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tlet c1 = left;\n\t\t\tlet c2 = right;\n\n\t\t\tlet score1, score2;\n\t\t\tlet box1, box2;\n\t\t\tif ( nodeScoreFunc ) {\n\n\t\t\t\tbox1 = _box1;\n\t\t\t\tbox2 = _box2;\n\n\t\t\t\t// bounding data is not offset\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\t\tscore1 = nodeScoreFunc( box1 );\n\t\t\t\tscore2 = nodeScoreFunc( box2 );\n\n\t\t\t\tif ( score2 < score1 ) {\n\n\t\t\t\t\tc1 = right;\n\t\t\t\t\tc2 = left;\n\n\t\t\t\t\tconst temp = score1;\n\t\t\t\t\tscore1 = score2;\n\t\t\t\t\tscore2 = temp;\n\n\t\t\t\t\tbox1 = box2;\n\t\t\t\t\t// box2 is always set before use below\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Check box 1 intersection\n\t\t\tif ( ! box1 ) {\n\n\t\t\t\tbox1 = _box1;\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\n\t\t\t}\n\n\t\t\tconst isC1Leaf = IS_LEAF( c1 * 2, uint16Array );\n\t\t\tconst c1Intersection = intersectsBoundsFunc( box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1 );\n\n\t\t\tlet c1StopTraversal;\n\t\t\tif ( c1Intersection === CONTAINED ) {\n\n\t\t\t\tconst offset = getLeftOffset( c1 );\n\t\t\t\tconst end = getRightEndOffset( c1 );\n\t\t\t\tconst count = end - offset;\n\n\t\t\t\tc1StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1 );\n\n\t\t\t} else {\n\n\t\t\t\tc1StopTraversal =\n\t\t\t\t\tc1Intersection &&\n\t\t\t\t\tshapecastTraverse(\n\t\t\t\t\t\tc1,\n\t\t\t\t\t\tgeometry,\n\t\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\t\tdepth + 1\n\t\t\t\t\t);\n\n\t\t\t}\n\n\t\t\tif ( c1StopTraversal ) return true;\n\n\t\t\t// Check box 2 intersection\n\t\t\t// cached box2 will have been overwritten by previous traversal\n\t\t\tbox2 = _box2;\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\tconst isC2Leaf = IS_LEAF( c2 * 2, uint16Array );\n\t\t\tconst c2Intersection = intersectsBoundsFunc( box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2 );\n\n\t\t\tlet c2StopTraversal;\n\t\t\tif ( c2Intersection === CONTAINED ) {\n\n\t\t\t\tconst offset = getLeftOffset( c2 );\n\t\t\t\tconst end = getRightEndOffset( c2 );\n\t\t\t\tconst count = end - offset;\n\n\t\t\t\tc2StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2 );\n\n\t\t\t} else {\n\n\t\t\t\tc2StopTraversal =\n\t\t\t\t\tc2Intersection &&\n\t\t\t\t\tshapecastTraverse(\n\t\t\t\t\t\tc2,\n\t\t\t\t\t\tgeometry,\n\t\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\t\tdepth + 1\n\t\t\t\t\t);\n\n\t\t\t}\n\n\t\t\tif ( c2StopTraversal ) return true;\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n} )();\n\nexport const intersectsGeometry = ( function () {\n\n\tconst triangle = new ExtendedTriangle();\n\tconst triangle2 = new ExtendedTriangle();\n\tconst invertedMat = new Matrix4();\n\n\tconst obb = new OrientedBox();\n\tconst obb2 = new OrientedBox();\n\n\treturn function intersectsGeometry( nodeIndex32, geometry, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\t\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\tif ( cachedObb === null ) {\n\n\t\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\t\totherGeometry.computeBoundingBox();\n\n\t\t\t}\n\n\t\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\t\tcachedObb = obb;\n\n\t\t}\n\n\t\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\t\tif ( isLeaf ) {\n\n\t\t\tconst thisGeometry = geometry;\n\t\t\tconst thisIndex = thisGeometry.index;\n\t\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\t\tconst index = otherGeometry.index;\n\t\t\tconst pos = otherGeometry.attributes.position;\n\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t\t// here.\n\t\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\t\tsetTriangle( triangle2, i, thisIndex, thisPos );\n\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn false;\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\treturn res;\n\n\t\t\t} else {\n\n\t\t\t\tfor ( let i = offset * 3, l = ( count + offset * 3 ); i < l; i += 3 ) {\n\n\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\tsetTriangle( triangle, i, thisIndex, thisPos );\n\t\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = nodeIndex32 + 8;\n\t\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\t\tconst leftIntersection =\n\t\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t\tintersectsGeometry( left, geometry, otherGeometry, geometryToBvh, cachedObb );\n\n\t\t\tif ( leftIntersection ) return true;\n\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\t\tconst rightIntersection =\n\t\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t\tintersectsGeometry( right, geometry, otherGeometry, geometryToBvh, cachedObb );\n\n\t\t\tif ( rightIntersection ) return true;\n\n\t\t\treturn false;\n\n\t\t}\n\n\t};\n\n} )();\n\nfunction intersectRay( nodeIndex32, array, ray, target ) {\n\n\tarrayToBox( nodeIndex32, array, boundingBox );\n\treturn ray.intersectBox( boundingBox, target );\n\n}\n\nconst bufferStack = [];\nlet _prevBuffer;\nlet _float32Array;\nlet _uint16Array;\nlet _uint32Array;\nexport function setBuffer( buffer ) {\n\n\tif ( _prevBuffer ) {\n\n\t\tbufferStack.push( _prevBuffer );\n\n\t}\n\n\t_prevBuffer = buffer;\n\t_float32Array = new Float32Array( buffer );\n\t_uint16Array = new Uint16Array( buffer );\n\t_uint32Array = new Uint32Array( buffer );\n\n}\n\nexport function clearBuffer() {\n\n\t_prevBuffer = null;\n\t_float32Array = null;\n\t_uint16Array = null;\n\t_uint32Array = null;\n\n\tif ( bufferStack.length ) {\n\n\t\tsetBuffer( bufferStack.pop() );\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,OAAO,QAAQ,OAAO;AAC9C,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,2CAA2C;AAC9F,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,0BAA0B;AAEzH,MAAMC,WAAW,GAAG,IAAIlB,IAAI,CAAC,CAAC;AAC9B,MAAMmB,eAAe,GAAG,IAAIlB,OAAO,CAAC,CAAC;AACrC,MAAMmB,SAAS,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;AAEnC,OAAO,SAASC,OAAOA,CAAEC,WAAW,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAG;EAEvE,IAAIC,WAAW,GAAGL,WAAW,GAAG,CAAC;IAAEM,YAAY,GAAGC,aAAa;IAAEC,WAAW,GAAGC,YAAY;IAAEC,WAAW,GAAGC,YAAY;EAEvH,MAAMC,MAAM,GAAGnB,OAAO,CAAEY,WAAW,EAAEG,WAAY,CAAC;EAClD,IAAKI,MAAM,EAAG;IAEb,MAAMC,MAAM,GAAGvB,MAAM,CAAEU,WAAW,EAAEU,WAAY,CAAC;IACjD,MAAMI,KAAK,GAAGzB,KAAK,CAAEgB,WAAW,EAAEG,WAAY,CAAC;IAE/CxB,aAAa,CAAEiB,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEU,MAAM,EAAEC,KAAK,EAAEV,UAAW,CAAC;EAEhE,CAAC,MAAM;IAEN,MAAMW,SAAS,GAAGxB,SAAS,CAAES,WAAY,CAAC;IAC1C,IAAKgB,YAAY,CAAED,SAAS,EAAET,YAAY,EAAEH,GAAG,EAAEN,eAAgB,CAAC,EAAG;MAEpEE,OAAO,CAAEgB,SAAS,EAAEd,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAW,CAAC;IAEtD;IAEA,MAAMa,UAAU,GAAGzB,UAAU,CAAEQ,WAAW,EAAEU,WAAY,CAAC;IACzD,IAAKM,YAAY,CAAEC,UAAU,EAAEX,YAAY,EAAEH,GAAG,EAAEN,eAAgB,CAAC,EAAG;MAErEE,OAAO,CAAEkB,UAAU,EAAEhB,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAW,CAAC;IAEvD;EAED;AAED;AAEA,OAAO,SAASc,YAAYA,CAAElB,WAAW,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAEhE,IAAIE,WAAW,GAAGL,WAAW,GAAG,CAAC;IAAEM,YAAY,GAAGC,aAAa;IAAEC,WAAW,GAAGC,YAAY;IAAEC,WAAW,GAAGC,YAAY;EAEvH,MAAMC,MAAM,GAAGnB,OAAO,CAAEY,WAAW,EAAEG,WAAY,CAAC;EAClD,IAAKI,MAAM,EAAG;IAEb,MAAMC,MAAM,GAAGvB,MAAM,CAAEU,WAAW,EAAEU,WAAY,CAAC;IACjD,MAAMI,KAAK,GAAGzB,KAAK,CAAEgB,WAAW,EAAEG,WAAY,CAAC;IAC/C,OAAOvB,mBAAmB,CAAEgB,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEU,MAAM,EAAEC,KAAM,CAAC;EAEjE,CAAC,MAAM;IAEN;IACA;IACA,MAAMK,SAAS,GAAGxB,UAAU,CAAEK,WAAW,EAAEU,WAAY,CAAC;IACxD,MAAMU,OAAO,GAAGtB,SAAS,CAAEqB,SAAS,CAAE;IACtC,MAAME,MAAM,GAAGlB,GAAG,CAACmB,SAAS,CAAEF,OAAO,CAAE;IACvC,MAAMG,WAAW,GAAGF,MAAM,IAAI,CAAC;;IAE/B;IACA,IAAIG,EAAE,EAAEC,EAAE;IACV,IAAKF,WAAW,EAAG;MAElBC,EAAE,GAAGjC,SAAS,CAAES,WAAY,CAAC;MAC7ByB,EAAE,GAAGjC,UAAU,CAAEQ,WAAW,EAAEU,WAAY,CAAC;IAE5C,CAAC,MAAM;MAENc,EAAE,GAAGhC,UAAU,CAAEQ,WAAW,EAAEU,WAAY,CAAC;MAC3Ce,EAAE,GAAGlC,SAAS,CAAES,WAAY,CAAC;IAE9B;IAEA,MAAM0B,cAAc,GAAGV,YAAY,CAAEQ,EAAE,EAAElB,YAAY,EAAEH,GAAG,EAAEN,eAAgB,CAAC;IAC7E,MAAM8B,QAAQ,GAAGD,cAAc,GAAGR,YAAY,CAAEM,EAAE,EAAEvB,QAAQ,EAAEC,IAAI,EAAEC,GAAI,CAAC,GAAG,IAAI;;IAEhF;IACA;IACA,IAAKwB,QAAQ,EAAG;MAEf;MACA;MACA,MAAMC,KAAK,GAAGD,QAAQ,CAACC,KAAK,CAAER,OAAO,CAAE;MACvC,MAAMS,SAAS,GAAGN,WAAW,GAC5BK,KAAK,IAAItB,YAAY,CAAEmB,EAAE,GAAGN,SAAS,CAAE;MAAG;MAC1CS,KAAK,IAAItB,YAAY,CAAEmB,EAAE,GAAGN,SAAS,GAAG,CAAC,CAAE,CAAC,CAAC;;MAE9C,IAAKU,SAAS,EAAG;QAEhB,OAAOF,QAAQ;MAEhB;IAED;;IAEA;IACA;IACA,MAAMG,cAAc,GAAGd,YAAY,CAAES,EAAE,EAAEnB,YAAY,EAAEH,GAAG,EAAEN,eAAgB,CAAC;IAC7E,MAAMkC,QAAQ,GAAGD,cAAc,GAAGZ,YAAY,CAAEO,EAAE,EAAExB,QAAQ,EAAEC,IAAI,EAAEC,GAAI,CAAC,GAAG,IAAI;IAEhF,IAAKwB,QAAQ,IAAII,QAAQ,EAAG;MAE3B,OAAOJ,QAAQ,CAACK,QAAQ,IAAID,QAAQ,CAACC,QAAQ,GAAGL,QAAQ,GAAGI,QAAQ;IAEpE,CAAC,MAAM;MAEN,OAAOJ,QAAQ,IAAII,QAAQ,IAAI,IAAI;IAEpC;EAED;AAED;AAEA,OAAO,MAAME,SAAS,GAAK,YAAY;EAEtC,IAAIC,KAAK,EAAEC,KAAK;EAChB,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,OAAO,GAAG,IAAIjD,aAAa,CAAE,MAAM,IAAIV,IAAI,CAAC,CAAE,CAAC;EAErD,OAAO,SAASuD,SAASA,CAAE,GAAGK,IAAI,EAAG;IAEpCJ,KAAK,GAAGG,OAAO,CAACE,YAAY,CAAC,CAAC;IAC9BJ,KAAK,GAAGE,OAAO,CAACE,YAAY,CAAC,CAAC;IAC9BH,QAAQ,CAACI,IAAI,CAAEN,KAAK,EAAEC,KAAM,CAAC;IAE7B,MAAMM,MAAM,GAAGC,iBAAiB,CAAE,GAAGJ,IAAK,CAAC;IAE3CD,OAAO,CAACM,gBAAgB,CAAET,KAAM,CAAC;IACjCG,OAAO,CAACM,gBAAgB,CAAER,KAAM,CAAC;IACjCC,QAAQ,CAACQ,GAAG,CAAC,CAAC;IACdR,QAAQ,CAACQ,GAAG,CAAC,CAAC;IAEd,MAAMC,MAAM,GAAGT,QAAQ,CAACS,MAAM;IAC9B,IAAKA,MAAM,GAAG,CAAC,EAAG;MAEjBV,KAAK,GAAGC,QAAQ,CAAES,MAAM,GAAG,CAAC,CAAE;MAC9BX,KAAK,GAAGE,QAAQ,CAAES,MAAM,GAAG,CAAC,CAAE;IAE/B;IAEA,OAAOJ,MAAM;EAEd,CAAC;EAED,SAASC,iBAAiBA,CACzB1C,WAAW,EACXC,QAAQ,EACR6C,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,GAAG,IAAI,EACpBC,mBAAmB,GAAG,CAAC;EAAE;EACzBC,KAAK,GAAG,CAAC,EACR;IAED;IACA;IACA,SAASC,aAAaA,CAAEnD,WAAW,EAAG;MAErC,IAAIK,WAAW,GAAGL,WAAW,GAAG,CAAC;QAAEQ,WAAW,GAAGC,YAAY;QAAEC,WAAW,GAAGC,YAAY;;MAEzF;MACA,OAAQ,CAAElB,OAAO,CAAEY,WAAW,EAAEG,WAAY,CAAC,EAAG;QAE/CR,WAAW,GAAGT,SAAS,CAAES,WAAY,CAAC;QACtCK,WAAW,GAAGL,WAAW,GAAG,CAAC;MAE9B;MAEA,OAAOV,MAAM,CAAEU,WAAW,EAAEU,WAAY,CAAC;IAE1C;IAEA,SAAS0C,iBAAiBA,CAAEpD,WAAW,EAAG;MAEzC,IAAIK,WAAW,GAAGL,WAAW,GAAG,CAAC;QAAEQ,WAAW,GAAGC,YAAY;QAAEC,WAAW,GAAGC,YAAY;;MAEzF;MACA,OAAQ,CAAElB,OAAO,CAAEY,WAAW,EAAEG,WAAY,CAAC,EAAG;QAE/C;QACAR,WAAW,GAAGR,UAAU,CAAEQ,WAAW,EAAEU,WAAY,CAAC;QACpDL,WAAW,GAAGL,WAAW,GAAG,CAAC;MAE9B;;MAEA;MACA,OAAOV,MAAM,CAAEU,WAAW,EAAEU,WAAY,CAAC,GAAGrB,KAAK,CAAEgB,WAAW,EAAEG,WAAY,CAAC;IAE9E;IAEA,IAAIH,WAAW,GAAGL,WAAW,GAAG,CAAC;MAAEM,YAAY,GAAGC,aAAa;MAAEC,WAAW,GAAGC,YAAY;MAAEC,WAAW,GAAGC,YAAY;IAEvH,MAAMC,MAAM,GAAGnB,OAAO,CAAEY,WAAW,EAAEG,WAAY,CAAC;IAClD,IAAKI,MAAM,EAAG;MAEb,MAAMC,MAAM,GAAGvB,MAAM,CAAEU,WAAW,EAAEU,WAAY,CAAC;MACjD,MAAMI,KAAK,GAAGzB,KAAK,CAAEgB,WAAW,EAAEG,WAAY,CAAC;MAC/CrB,UAAU,CAAEO,mBAAmB,CAAEM,WAAY,CAAC,EAAEM,YAAY,EAAE4B,KAAM,CAAC;MACrE,OAAOa,mBAAmB,CAAElC,MAAM,EAAEC,KAAK,EAAE,KAAK,EAAEoC,KAAK,EAAED,mBAAmB,GAAGjD,WAAW,EAAEkC,KAAM,CAAC;IAEpG,CAAC,MAAM;MAEN,MAAMmB,IAAI,GAAG9D,SAAS,CAAES,WAAY,CAAC;MACrC,MAAMsD,KAAK,GAAG9D,UAAU,CAAEQ,WAAW,EAAEU,WAAY,CAAC;MACpD,IAAIc,EAAE,GAAG6B,IAAI;MACb,IAAI5B,EAAE,GAAG6B,KAAK;MAEd,IAAIC,MAAM,EAAEC,MAAM;MAClB,IAAIC,IAAI,EAAEC,IAAI;MACd,IAAKV,aAAa,EAAG;QAEpBS,IAAI,GAAGvB,KAAK;QACZwB,IAAI,GAAGvB,KAAK;;QAEZ;QACAhD,UAAU,CAAEO,mBAAmB,CAAE8B,EAAG,CAAC,EAAElB,YAAY,EAAEmD,IAAK,CAAC;QAC3DtE,UAAU,CAAEO,mBAAmB,CAAE+B,EAAG,CAAC,EAAEnB,YAAY,EAAEoD,IAAK,CAAC;QAE3DH,MAAM,GAAGP,aAAa,CAAES,IAAK,CAAC;QAC9BD,MAAM,GAAGR,aAAa,CAAEU,IAAK,CAAC;QAE9B,IAAKF,MAAM,GAAGD,MAAM,EAAG;UAEtB/B,EAAE,GAAG8B,KAAK;UACV7B,EAAE,GAAG4B,IAAI;UAET,MAAMM,IAAI,GAAGJ,MAAM;UACnBA,MAAM,GAAGC,MAAM;UACfA,MAAM,GAAGG,IAAI;UAEbF,IAAI,GAAGC,IAAI;UACX;QAED;MAED;;MAEA;MACA,IAAK,CAAED,IAAI,EAAG;QAEbA,IAAI,GAAGvB,KAAK;QACZ/C,UAAU,CAAEO,mBAAmB,CAAE8B,EAAG,CAAC,EAAElB,YAAY,EAAEmD,IAAK,CAAC;MAE5D;MAEA,MAAMG,QAAQ,GAAGnE,OAAO,CAAE+B,EAAE,GAAG,CAAC,EAAEhB,WAAY,CAAC;MAC/C,MAAMkB,cAAc,GAAGoB,oBAAoB,CAAEW,IAAI,EAAEG,QAAQ,EAAEL,MAAM,EAAEL,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGzB,EAAG,CAAC;MAE1G,IAAIqC,eAAe;MACnB,IAAKnC,cAAc,KAAK7C,SAAS,EAAG;QAEnC,MAAMgC,MAAM,GAAGsC,aAAa,CAAE3B,EAAG,CAAC;QAClC,MAAMsC,GAAG,GAAGV,iBAAiB,CAAE5B,EAAG,CAAC;QACnC,MAAMV,KAAK,GAAGgD,GAAG,GAAGjD,MAAM;QAE1BgD,eAAe,GAAGd,mBAAmB,CAAElC,MAAM,EAAEC,KAAK,EAAE,IAAI,EAAEoC,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGzB,EAAE,EAAEiC,IAAK,CAAC;MAExG,CAAC,MAAM;QAENI,eAAe,GACdnC,cAAc,IACdgB,iBAAiB,CAChBlB,EAAE,EACFvB,QAAQ,EACR6C,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,mBAAmB,EACnBC,KAAK,GAAG,CACT,CAAC;MAEH;MAEA,IAAKW,eAAe,EAAG,OAAO,IAAI;;MAElC;MACA;MACAH,IAAI,GAAGvB,KAAK;MACZhD,UAAU,CAAEO,mBAAmB,CAAE+B,EAAG,CAAC,EAAEnB,YAAY,EAAEoD,IAAK,CAAC;MAE3D,MAAMK,QAAQ,GAAGtE,OAAO,CAAEgC,EAAE,GAAG,CAAC,EAAEjB,WAAY,CAAC;MAC/C,MAAMsB,cAAc,GAAGgB,oBAAoB,CAAEY,IAAI,EAAEK,QAAQ,EAAEP,MAAM,EAAEN,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGxB,EAAG,CAAC;MAE1G,IAAIuC,eAAe;MACnB,IAAKlC,cAAc,KAAKjD,SAAS,EAAG;QAEnC,MAAMgC,MAAM,GAAGsC,aAAa,CAAE1B,EAAG,CAAC;QAClC,MAAMqC,GAAG,GAAGV,iBAAiB,CAAE3B,EAAG,CAAC;QACnC,MAAMX,KAAK,GAAGgD,GAAG,GAAGjD,MAAM;QAE1BmD,eAAe,GAAGjB,mBAAmB,CAAElC,MAAM,EAAEC,KAAK,EAAE,IAAI,EAAEoC,KAAK,GAAG,CAAC,EAAED,mBAAmB,GAAGxB,EAAE,EAAEiC,IAAK,CAAC;MAExG,CAAC,MAAM;QAENM,eAAe,GACdlC,cAAc,IACdY,iBAAiB,CAChBjB,EAAE,EACFxB,QAAQ,EACR6C,oBAAoB,EACpBC,mBAAmB,EACnBC,aAAa,EACbC,mBAAmB,EACnBC,KAAK,GAAG,CACT,CAAC;MAEH;MAEA,IAAKc,eAAe,EAAG,OAAO,IAAI;MAElC,OAAO,KAAK;IAEb;EAED;AAED,CAAC,CAAG,CAAC;AAEL,OAAO,MAAMC,kBAAkB,GAAK,YAAY;EAE/C,MAAMC,QAAQ,GAAG,IAAInF,gBAAgB,CAAC,CAAC;EACvC,MAAMoF,SAAS,GAAG,IAAIpF,gBAAgB,CAAC,CAAC;EACxC,MAAMqF,WAAW,GAAG,IAAIxF,OAAO,CAAC,CAAC;EAEjC,MAAMyF,GAAG,GAAG,IAAIvF,WAAW,CAAC,CAAC;EAC7B,MAAMwF,IAAI,GAAG,IAAIxF,WAAW,CAAC,CAAC;EAE9B,OAAO,SAASmF,kBAAkBA,CAAEjE,WAAW,EAAEC,QAAQ,EAAEsE,aAAa,EAAEC,aAAa,EAAEC,SAAS,GAAG,IAAI,EAAG;IAE3G,IAAIpE,WAAW,GAAGL,WAAW,GAAG,CAAC;MAAEM,YAAY,GAAGC,aAAa;MAAEC,WAAW,GAAGC,YAAY;MAAEC,WAAW,GAAGC,YAAY;IAEvH,IAAK8D,SAAS,KAAK,IAAI,EAAG;MAEzB,IAAK,CAAEF,aAAa,CAAC3E,WAAW,EAAG;QAElC2E,aAAa,CAACG,kBAAkB,CAAC,CAAC;MAEnC;MAEAL,GAAG,CAACM,GAAG,CAAEJ,aAAa,CAAC3E,WAAW,CAACgF,GAAG,EAAEL,aAAa,CAAC3E,WAAW,CAACiF,GAAG,EAAEL,aAAc,CAAC;MACtFC,SAAS,GAAGJ,GAAG;IAEhB;IAEA,MAAMzD,MAAM,GAAGnB,OAAO,CAAEY,WAAW,EAAEG,WAAY,CAAC;IAClD,IAAKI,MAAM,EAAG;MAEb,MAAMkE,YAAY,GAAG7E,QAAQ;MAC7B,MAAM8E,SAAS,GAAGD,YAAY,CAACE,KAAK;MACpC,MAAMC,OAAO,GAAGH,YAAY,CAACI,UAAU,CAACC,QAAQ;MAEhD,MAAMH,KAAK,GAAGT,aAAa,CAACS,KAAK;MACjC,MAAMI,GAAG,GAAGb,aAAa,CAACW,UAAU,CAACC,QAAQ;MAE7C,MAAMtE,MAAM,GAAGvB,MAAM,CAAEU,WAAW,EAAEU,WAAY,CAAC;MACjD,MAAMI,KAAK,GAAGzB,KAAK,CAAEgB,WAAW,EAAEG,WAAY,CAAC;;MAE/C;MACA;MACA;MACA4D,WAAW,CAACiB,IAAI,CAAEb,aAAc,CAAC,CAACc,MAAM,CAAC,CAAC;MAE1C,IAAKf,aAAa,CAACgB,UAAU,EAAG;QAE/BpG,UAAU,CAAEO,mBAAmB,CAAEM,WAAY,CAAC,EAAEM,YAAY,EAAEgE,IAAK,CAAC;QACpEA,IAAI,CAACkB,MAAM,CAACH,IAAI,CAAEjB,WAAY,CAAC;QAC/BE,IAAI,CAACmB,WAAW,GAAG,IAAI;QAEvB,MAAMC,GAAG,GAAGnB,aAAa,CAACgB,UAAU,CAACtD,SAAS,CAAE;UAE/C0D,gBAAgB,EAAEC,GAAG,IAAItB,IAAI,CAACuB,aAAa,CAAED,GAAI,CAAC;UAElDE,kBAAkB,EAAEC,GAAG,IAAI;YAE1BA,GAAG,CAACC,CAAC,CAACC,YAAY,CAAEzB,aAAc,CAAC;YACnCuB,GAAG,CAACG,CAAC,CAACD,YAAY,CAAEzB,aAAc,CAAC;YACnCuB,GAAG,CAACI,CAAC,CAACF,YAAY,CAAEzB,aAAc,CAAC;YACnCuB,GAAG,CAACN,WAAW,GAAG,IAAI;YAEtB,KAAM,IAAIW,CAAC,GAAGvF,MAAM,GAAG,CAAC,EAAEwF,CAAC,GAAG,CAAEvF,KAAK,GAAGD,MAAM,IAAK,CAAC,EAAEuF,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;cAErE;cACAlH,WAAW,CAAEiF,SAAS,EAAEiC,CAAC,EAAErB,SAAS,EAAEE,OAAQ,CAAC;cAC/Cd,SAAS,CAACsB,WAAW,GAAG,IAAI;cAC5B,IAAKM,GAAG,CAACD,kBAAkB,CAAE3B,SAAU,CAAC,EAAG;gBAE1C,OAAO,IAAI;cAEZ;YAED;YAEA,OAAO,KAAK;UAEb;QAED,CAAE,CAAC;QAEH,OAAOuB,GAAG;MAEX,CAAC,MAAM;QAEN,KAAM,IAAIU,CAAC,GAAGvF,MAAM,GAAG,CAAC,EAAEwF,CAAC,GAAKvF,KAAK,GAAGD,MAAM,GAAG,CAAG,EAAEuF,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;UAErE;UACAlH,WAAW,CAAEgF,QAAQ,EAAEkC,CAAC,EAAErB,SAAS,EAAEE,OAAQ,CAAC;UAC9Cf,QAAQ,CAAC8B,CAAC,CAACC,YAAY,CAAE7B,WAAY,CAAC;UACtCF,QAAQ,CAACgC,CAAC,CAACD,YAAY,CAAE7B,WAAY,CAAC;UACtCF,QAAQ,CAACiC,CAAC,CAACF,YAAY,CAAE7B,WAAY,CAAC;UACtCF,QAAQ,CAACuB,WAAW,GAAG,IAAI;UAE3B,KAAM,IAAIa,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGvB,KAAK,CAAClE,KAAK,EAAEwF,EAAE,GAAGC,EAAE,EAAED,EAAE,IAAI,CAAC,EAAG;YAEtDpH,WAAW,CAAEiF,SAAS,EAAEmC,EAAE,EAAEtB,KAAK,EAAEI,GAAI,CAAC;YACxCjB,SAAS,CAACsB,WAAW,GAAG,IAAI;YAE5B,IAAKvB,QAAQ,CAAC4B,kBAAkB,CAAE3B,SAAU,CAAC,EAAG;cAE/C,OAAO,IAAI;YAEZ;UAED;QAED;MAED;IAED,CAAC,MAAM;MAEN,MAAMd,IAAI,GAAGrD,WAAW,GAAG,CAAC;MAC5B,MAAMsD,KAAK,GAAG5C,WAAW,CAAEV,WAAW,GAAG,CAAC,CAAE;MAE5Cb,UAAU,CAAEO,mBAAmB,CAAE2D,IAAK,CAAC,EAAE/C,YAAY,EAAEV,WAAY,CAAC;MACpE,MAAM4G,gBAAgB,GACrB/B,SAAS,CAACoB,aAAa,CAAEjG,WAAY,CAAC,IACtCqE,kBAAkB,CAAEZ,IAAI,EAAEpD,QAAQ,EAAEsE,aAAa,EAAEC,aAAa,EAAEC,SAAU,CAAC;MAE9E,IAAK+B,gBAAgB,EAAG,OAAO,IAAI;MAEnCrH,UAAU,CAAEO,mBAAmB,CAAE4D,KAAM,CAAC,EAAEhD,YAAY,EAAEV,WAAY,CAAC;MACrE,MAAM6G,iBAAiB,GACtBhC,SAAS,CAACoB,aAAa,CAAEjG,WAAY,CAAC,IACtCqE,kBAAkB,CAAEX,KAAK,EAAErD,QAAQ,EAAEsE,aAAa,EAAEC,aAAa,EAAEC,SAAU,CAAC;MAE/E,IAAKgC,iBAAiB,EAAG,OAAO,IAAI;MAEpC,OAAO,KAAK;IAEb;EAED,CAAC;AAEF,CAAC,CAAG,CAAC;AAEL,SAASzF,YAAYA,CAAEhB,WAAW,EAAE0G,KAAK,EAAEvG,GAAG,EAAEwG,MAAM,EAAG;EAExDxH,UAAU,CAAEa,WAAW,EAAE0G,KAAK,EAAE9G,WAAY,CAAC;EAC7C,OAAOO,GAAG,CAACyG,YAAY,CAAEhH,WAAW,EAAE+G,MAAO,CAAC;AAE/C;AAEA,MAAME,WAAW,GAAG,EAAE;AACtB,IAAIC,WAAW;AACf,IAAIvG,aAAa;AACjB,IAAIE,YAAY;AAChB,IAAIE,YAAY;AAChB,OAAO,SAASoG,SAASA,CAAEC,MAAM,EAAG;EAEnC,IAAKF,WAAW,EAAG;IAElBD,WAAW,CAACrE,IAAI,CAAEsE,WAAY,CAAC;EAEhC;EAEAA,WAAW,GAAGE,MAAM;EACpBzG,aAAa,GAAG,IAAI0G,YAAY,CAAED,MAAO,CAAC;EAC1CvG,YAAY,GAAG,IAAIyG,WAAW,CAAEF,MAAO,CAAC;EACxCrG,YAAY,GAAG,IAAIwG,WAAW,CAAEH,MAAO,CAAC;AAEzC;AAEA,OAAO,SAASI,WAAWA,CAAA,EAAG;EAE7BN,WAAW,GAAG,IAAI;EAClBvG,aAAa,GAAG,IAAI;EACpBE,YAAY,GAAG,IAAI;EACnBE,YAAY,GAAG,IAAI;EAEnB,IAAKkG,WAAW,CAAChE,MAAM,EAAG;IAEzBkE,SAAS,CAAEF,WAAW,CAACjE,GAAG,CAAC,CAAE,CAAC;EAE/B;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}