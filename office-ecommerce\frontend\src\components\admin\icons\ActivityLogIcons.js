import React from 'react';

const iconStyle = {
  width: '24px',
  height: '24px',
  fill: 'none',
  stroke: 'currentColor',
  strokeWidth: '2',
  strokeLinecap: 'round',
  strokeLinejoin: 'round'
};

export const SearchIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="11" cy="11" r="8"/>
    <path d="m21 21-4.35-4.35"/>
  </svg>
);

export const FilterIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
  </svg>
);

export const RefreshIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <polyline points="23,4 23,10 17,10"/>
    <polyline points="1,20 1,14 7,14"/>
    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
  </svg>
);

export const ExportIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
    <polyline points="7,10 12,15 17,10"/>
    <line x1="12" y1="15" x2="12" y2="3"/>
  </svg>
);

export const InfoIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <line x1="12" y1="16" x2="12" y2="12"/>
    <line x1="12" y1="8" x2="12.01" y2="8"/>
  </svg>
);

export const WarningIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
    <line x1="12" y1="9" x2="12" y2="13"/>
    <line x1="12" y1="17" x2="12.01" y2="17"/>
  </svg>
);

export const ErrorIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <line x1="15" y1="9" x2="9" y2="15"/>
    <line x1="9" y1="9" x2="15" y2="15"/>
  </svg>
);

export const CriticalIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
    <path d="M2 17l10 5 10-5"/>
    <path d="M2 12l10 5 10-5"/>
    <circle cx="12" cy="12" r="2" fill={color}/>
  </svg>
);

export const ActivityIcon = ({ className = '', color = 'currentColor' }) => (
  <svg className={className} style={{...iconStyle, stroke: color}} viewBox="0 0 24 24">
    <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
  </svg>
);
