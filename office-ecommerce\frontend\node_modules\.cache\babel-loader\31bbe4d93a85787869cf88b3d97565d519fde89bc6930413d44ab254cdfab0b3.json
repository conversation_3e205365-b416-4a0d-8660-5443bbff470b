{"ast": null, "code": "function shallow(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (let i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !Object.is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n  return true;\n}\nexport { shallow as default };", "map": {"version": 3, "names": ["shallow", "objA", "objB", "Object", "is", "keysA", "keys", "length", "i", "prototype", "hasOwnProperty", "call", "default"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/zustand/esm/shallow.js"], "sourcesContent": ["function shallow(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (let i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !Object.is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport { shallow as default };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3B,IAAIC,MAAM,CAACC,EAAE,CAACH,IAAI,EAAEC,IAAI,CAAC,EAAE;IACzB,OAAO,IAAI;EACb;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC1F,OAAO,KAAK;EACd;EACA,MAAMG,KAAK,GAAGF,MAAM,CAACG,IAAI,CAACL,IAAI,CAAC;EAC/B,IAAII,KAAK,CAACE,MAAM,KAAKJ,MAAM,CAACG,IAAI,CAACJ,IAAI,CAAC,CAACK,MAAM,EAAE;IAC7C,OAAO,KAAK;EACd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAI,CAACL,MAAM,CAACM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,IAAI,EAAEG,KAAK,CAACG,CAAC,CAAC,CAAC,IAAI,CAACL,MAAM,CAACC,EAAE,CAACH,IAAI,CAACI,KAAK,CAACG,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACG,KAAK,CAACG,CAAC,CAAC,CAAC,CAAC,EAAE;MACvG,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASR,OAAO,IAAIY,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}