{"ast": null, "code": "import { cloneElement } from 'react';\nimport PropTypes from 'prop-types';\nexport default function Composer(props) {\n  return renderRecursive(props.children, props.components);\n}\nComposer.propTypes = {\n  children: PropTypes.func.isRequired,\n  components: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.element, PropTypes.func])).isRequired\n};\n\n/**\n * Recursively build up elements from props.components and accumulate `results` along the way.\n * @param {function} render\n * @param {Array.<ReactElement|Function>} remaining\n * @param {Array} [results]\n * @returns {ReactElement}\n */\nfunction renderRecursive(render, remaining, results) {\n  results = results || [];\n  // Once components is exhausted, we can render out the results array.\n  if (!remaining[0]) {\n    return render(results);\n  }\n\n  // Continue recursion for remaining items.\n  // results.concat([value]) ensures [...results, value] instead of [...results, ...value]\n  function nextRender(value) {\n    return renderRecursive(render, remaining.slice(1), results.concat([value]));\n  }\n\n  // Each props.components entry is either an element or function [element factory]\n  return typeof remaining[0] === 'function' ?\n  // When it is a function, produce an element by invoking it with \"render component values\".\n  remaining[0]({\n    results: results,\n    render: nextRender\n  }) :\n  // When it is an element, enhance the element's props with the render prop.\n  cloneElement(remaining[0], {\n    children: nextRender\n  });\n}", "map": {"version": 3, "names": ["cloneElement", "PropTypes", "Composer", "props", "renderRecursive", "children", "components", "propTypes", "func", "isRequired", "arrayOf", "oneOfType", "element", "render", "remaining", "results", "nextR<PERSON>", "value", "slice", "concat"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/react-composer/es/index.js"], "sourcesContent": ["import { cloneElement } from 'react';\nimport PropTypes from 'prop-types';\n\nexport default function Composer(props) {\n  return renderRecursive(props.children, props.components);\n}\n\nComposer.propTypes = {\n  children: PropTypes.func.isRequired,\n  components: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.element, PropTypes.func])).isRequired\n};\n\n/**\n * Recursively build up elements from props.components and accumulate `results` along the way.\n * @param {function} render\n * @param {Array.<ReactElement|Function>} remaining\n * @param {Array} [results]\n * @returns {ReactElement}\n */\nfunction renderRecursive(render, remaining, results) {\n  results = results || [];\n  // Once components is exhausted, we can render out the results array.\n  if (!remaining[0]) {\n    return render(results);\n  }\n\n  // Continue recursion for remaining items.\n  // results.concat([value]) ensures [...results, value] instead of [...results, ...value]\n  function nextRender(value) {\n    return renderRecursive(render, remaining.slice(1), results.concat([value]));\n  }\n\n  // Each props.components entry is either an element or function [element factory]\n  return typeof remaining[0] === 'function' ? // When it is a function, produce an element by invoking it with \"render component values\".\n  remaining[0]({ results: results, render: nextRender }) : // When it is an element, enhance the element's props with the render prop.\n  cloneElement(remaining[0], { children: nextRender });\n}"], "mappings": "AAAA,SAASA,YAAY,QAAQ,OAAO;AACpC,OAAOC,SAAS,MAAM,YAAY;AAElC,eAAe,SAASC,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAOC,eAAe,CAACD,KAAK,CAACE,QAAQ,EAAEF,KAAK,CAACG,UAAU,CAAC;AAC1D;AAEAJ,QAAQ,CAACK,SAAS,GAAG;EACnBF,QAAQ,EAAEJ,SAAS,CAACO,IAAI,CAACC,UAAU;EACnCH,UAAU,EAAEL,SAAS,CAACS,OAAO,CAACT,SAAS,CAACU,SAAS,CAAC,CAACV,SAAS,CAACW,OAAO,EAAEX,SAAS,CAACO,IAAI,CAAC,CAAC,CAAC,CAACC;AAC1F,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,eAAeA,CAACS,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACnDA,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvB;EACA,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,EAAE;IACjB,OAAOD,MAAM,CAACE,OAAO,CAAC;EACxB;;EAEA;EACA;EACA,SAASC,UAAUA,CAACC,KAAK,EAAE;IACzB,OAAOb,eAAe,CAACS,MAAM,EAAEC,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEH,OAAO,CAACI,MAAM,CAAC,CAACF,KAAK,CAAC,CAAC,CAAC;EAC7E;;EAEA;EACA,OAAO,OAAOH,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU;EAAG;EAC5CA,SAAS,CAAC,CAAC,CAAC,CAAC;IAAEC,OAAO,EAAEA,OAAO;IAAEF,MAAM,EAAEG;EAAW,CAAC,CAAC;EAAG;EACzDhB,YAAY,CAACc,SAAS,CAAC,CAAC,CAAC,EAAE;IAAET,QAAQ,EAAEW;EAAW,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}