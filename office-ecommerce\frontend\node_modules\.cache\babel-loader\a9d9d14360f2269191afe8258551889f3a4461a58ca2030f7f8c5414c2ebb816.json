{"ast": null, "code": "import { ShaderLib, Mesh, BoxGeometry, ShaderMaterial, UniformsUtils, BackSide, Scene, PerspectiveCamera } from \"three\";\nimport { Pass } from \"./Pass.js\";\nclass CubeTexturePass extends Pass {\n  constructor(camera, tCube, opacity = 1) {\n    super();\n    this.camera = camera;\n    this.needsSwap = false;\n    this.cubeShader = ShaderLib[\"cube\"];\n    this.cubeMesh = new Mesh(new BoxGeometry(10, 10, 10), new ShaderMaterial({\n      uniforms: UniformsUtils.clone(this.cubeShader.uniforms),\n      vertexShader: this.cubeShader.vertexShader,\n      fragmentShader: this.cubeShader.fragmentShader,\n      depthTest: false,\n      depthWrite: false,\n      side: BackSide\n    }));\n    Object.defineProperty(this.cubeMesh.material, \"envMap\", {\n      get: function () {\n        return this.uniforms.tCube.value;\n      }\n    });\n    this.tCube = tCube;\n    this.opacity = opacity;\n    this.cubeScene = new Scene();\n    this.cubeCamera = new PerspectiveCamera();\n    this.cubeScene.add(this.cubeMesh);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    const oldAutoClear = renderer.autoClear;\n    renderer.autoClear = false;\n    this.cubeCamera.projectionMatrix.copy(this.camera.projectionMatrix);\n    this.cubeCamera.quaternion.setFromRotationMatrix(this.camera.matrixWorld);\n    this.cubeMesh.material.uniforms.tCube.value = this.tCube;\n    this.cubeMesh.material.uniforms.tFlip.value = this.tCube.isCubeTexture && this.tCube.isRenderTargetTexture === false ? -1 : 1;\n    this.cubeMesh.material.uniforms.opacity.value = this.opacity;\n    this.cubeMesh.material.transparent = this.opacity < 1;\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer);\n    if (this.clear) renderer.clear();\n    renderer.render(this.cubeScene, this.cubeCamera);\n    renderer.autoClear = oldAutoClear;\n  }\n  dispose() {\n    this.cubeMesh.geometry.dispose();\n    this.cubeMesh.material.dispose();\n  }\n}\nexport { CubeTexturePass };", "map": {"version": 3, "names": ["CubeTexturePass", "Pass", "constructor", "camera", "tCube", "opacity", "needsSwap", "cubeShader", "ShaderLib", "cubeMesh", "<PERSON><PERSON>", "BoxGeometry", "ShaderMaterial", "uniforms", "UniformsUtils", "clone", "vertexShader", "fragmentShader", "depthTest", "depthWrite", "side", "BackSide", "Object", "defineProperty", "material", "get", "value", "cubeScene", "Scene", "cubeCamera", "PerspectiveCamera", "add", "render", "renderer", "writeBuffer", "readBuffer", "oldAutoClear", "autoClear", "projectionMatrix", "copy", "quaternion", "setFromRotationMatrix", "matrixWorld", "tFlip", "isCubeTexture", "isRenderTargetTexture", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderToScreen", "clear", "dispose", "geometry"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\CubeTexturePass.js"], "sourcesContent": ["import { BackSide, BoxGeometry, Mesh, PerspectiveCamera, Scene, ShaderLib, ShaderMaterial, UniformsUtils } from 'three'\nimport { Pass } from './Pass'\n\nclass CubeTexturePass extends Pass {\n  constructor(camera, tCube, opacity = 1) {\n    super()\n\n    this.camera = camera\n\n    this.needsSwap = false\n\n    this.cubeShader = ShaderLib['cube']\n    this.cubeMesh = new Mesh(\n      new BoxGeometry(10, 10, 10),\n      new ShaderMaterial({\n        uniforms: UniformsUtils.clone(this.cubeShader.uniforms),\n        vertexShader: this.cubeShader.vertexShader,\n        fragmentShader: this.cubeShader.fragmentShader,\n        depthTest: false,\n        depthWrite: false,\n        side: BackSide,\n      }),\n    )\n\n    Object.defineProperty(this.cubeMesh.material, 'envMap', {\n      get: function () {\n        return this.uniforms.tCube.value\n      },\n    })\n\n    this.tCube = tCube\n    this.opacity = opacity\n\n    this.cubeScene = new Scene()\n    this.cubeCamera = new PerspectiveCamera()\n    this.cubeScene.add(this.cubeMesh)\n  }\n\n  render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive*/) {\n    const oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    this.cubeCamera.projectionMatrix.copy(this.camera.projectionMatrix)\n    this.cubeCamera.quaternion.setFromRotationMatrix(this.camera.matrixWorld)\n\n    this.cubeMesh.material.uniforms.tCube.value = this.tCube\n    this.cubeMesh.material.uniforms.tFlip.value =\n      this.tCube.isCubeTexture && this.tCube.isRenderTargetTexture === false ? -1 : 1\n    this.cubeMesh.material.uniforms.opacity.value = this.opacity\n    this.cubeMesh.material.transparent = this.opacity < 1.0\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.cubeScene, this.cubeCamera)\n\n    renderer.autoClear = oldAutoClear\n  }\n\n  dispose() {\n    this.cubeMesh.geometry.dispose()\n    this.cubeMesh.material.dispose()\n  }\n}\n\nexport { CubeTexturePass }\n"], "mappings": ";;AAGA,MAAMA,eAAA,SAAwBC,IAAA,CAAK;EACjCC,YAAYC,MAAA,EAAQC,KAAA,EAAOC,OAAA,GAAU,GAAG;IACtC,MAAO;IAEP,KAAKF,MAAA,GAASA,MAAA;IAEd,KAAKG,SAAA,GAAY;IAEjB,KAAKC,UAAA,GAAaC,SAAA,CAAU,MAAM;IAClC,KAAKC,QAAA,GAAW,IAAIC,IAAA,CAClB,IAAIC,WAAA,CAAY,IAAI,IAAI,EAAE,GAC1B,IAAIC,cAAA,CAAe;MACjBC,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAM,KAAKR,UAAA,CAAWM,QAAQ;MACtDG,YAAA,EAAc,KAAKT,UAAA,CAAWS,YAAA;MAC9BC,cAAA,EAAgB,KAAKV,UAAA,CAAWU,cAAA;MAChCC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,IAAA,EAAMC;IACd,CAAO,CACF;IAEDC,MAAA,CAAOC,cAAA,CAAe,KAAKd,QAAA,CAASe,QAAA,EAAU,UAAU;MACtDC,GAAA,EAAK,SAAAA,CAAA,EAAY;QACf,OAAO,KAAKZ,QAAA,CAAST,KAAA,CAAMsB,KAAA;MAC5B;IACP,CAAK;IAED,KAAKtB,KAAA,GAAQA,KAAA;IACb,KAAKC,OAAA,GAAUA,OAAA;IAEf,KAAKsB,SAAA,GAAY,IAAIC,KAAA,CAAO;IAC5B,KAAKC,UAAA,GAAa,IAAIC,iBAAA,CAAmB;IACzC,KAAKH,SAAA,CAAUI,GAAA,CAAI,KAAKtB,QAAQ;EACjC;EAEDuB,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAwC;IACpE,MAAMC,YAAA,GAAeH,QAAA,CAASI,SAAA;IAC9BJ,QAAA,CAASI,SAAA,GAAY;IAErB,KAAKR,UAAA,CAAWS,gBAAA,CAAiBC,IAAA,CAAK,KAAKpC,MAAA,CAAOmC,gBAAgB;IAClE,KAAKT,UAAA,CAAWW,UAAA,CAAWC,qBAAA,CAAsB,KAAKtC,MAAA,CAAOuC,WAAW;IAExE,KAAKjC,QAAA,CAASe,QAAA,CAASX,QAAA,CAAST,KAAA,CAAMsB,KAAA,GAAQ,KAAKtB,KAAA;IACnD,KAAKK,QAAA,CAASe,QAAA,CAASX,QAAA,CAAS8B,KAAA,CAAMjB,KAAA,GACpC,KAAKtB,KAAA,CAAMwC,aAAA,IAAiB,KAAKxC,KAAA,CAAMyC,qBAAA,KAA0B,QAAQ,KAAK;IAChF,KAAKpC,QAAA,CAASe,QAAA,CAASX,QAAA,CAASR,OAAA,CAAQqB,KAAA,GAAQ,KAAKrB,OAAA;IACrD,KAAKI,QAAA,CAASe,QAAA,CAASsB,WAAA,GAAc,KAAKzC,OAAA,GAAU;IAEpD4B,QAAA,CAASc,eAAA,CAAgB,KAAKC,cAAA,GAAiB,OAAOb,UAAU;IAChE,IAAI,KAAKc,KAAA,EAAOhB,QAAA,CAASgB,KAAA,CAAO;IAChChB,QAAA,CAASD,MAAA,CAAO,KAAKL,SAAA,EAAW,KAAKE,UAAU;IAE/CI,QAAA,CAASI,SAAA,GAAYD,YAAA;EACtB;EAEDc,QAAA,EAAU;IACR,KAAKzC,QAAA,CAAS0C,QAAA,CAASD,OAAA,CAAS;IAChC,KAAKzC,QAAA,CAASe,QAAA,CAAS0B,OAAA,CAAS;EACjC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}