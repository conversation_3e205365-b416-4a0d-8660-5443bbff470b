{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { applyProps } from '@react-three/fiber';\nimport { DecalGeometry } from 'three-stdlib';\nfunction isArray(vec) {\n  return Array.isArray(vec);\n}\nfunction vecToArray(vec = [0, 0, 0]) {\n  if (isArray(vec)) {\n    return vec;\n  } else if (vec instanceof THREE.Vector3 || vec instanceof THREE.Euler) {\n    return [vec.x, vec.y, vec.z];\n  } else {\n    return [vec, vec, vec];\n  }\n}\nconst Decal = /*#__PURE__*/React.forwardRef(function Decal({\n  debug,\n  mesh,\n  children,\n  position,\n  rotation,\n  scale,\n  ...props\n}, forwardRef) {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current);\n  const helper = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = (mesh == null ? void 0 : mesh.current) || ref.current.parent;\n    const target = ref.current;\n    if (!(parent instanceof THREE.Mesh)) {\n      throw new Error('Decal must have a Mesh as parent or specify its \"mesh\" prop');\n    }\n    const state = {\n      position: new THREE.Vector3(),\n      rotation: new THREE.Euler(),\n      scale: new THREE.Vector3(1, 1, 1)\n    };\n    if (parent) {\n      applyProps(state, {\n        position,\n        scale\n      }); // Zero out the parents matrix world for this operation\n\n      const matrixWorld = parent.matrixWorld.clone();\n      parent.matrixWorld.identity();\n      if (!rotation || typeof rotation === 'number') {\n        const o = new THREE.Object3D();\n        o.position.copy(state.position);\n        o.lookAt(parent.position);\n        if (typeof rotation === 'number') o.rotateZ(rotation);\n        applyProps(state, {\n          rotation: o.rotation\n        });\n      } else {\n        applyProps(state, {\n          rotation\n        });\n      }\n      target.geometry = new DecalGeometry(parent, state.position, state.rotation, state.scale);\n      if (helper.current) applyProps(helper.current, state); // Reset parents matix-world\n\n      parent.matrixWorld = matrixWorld;\n      return () => {\n        target.geometry.dispose();\n      };\n    }\n  }, [mesh, ...vecToArray(position), ...vecToArray(scale), ...vecToArray(rotation)]);\n  return /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: ref\n  }, children || /*#__PURE__*/React.createElement(\"meshStandardMaterial\", _extends({\n    transparent: true,\n    polygonOffset: true,\n    polygonOffsetFactor: -10\n  }, props)), debug && /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: helper\n  }, /*#__PURE__*/React.createElement(\"boxGeometry\", null), /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n    wireframe: true\n  }), /*#__PURE__*/React.createElement(\"axesHelper\", null)));\n});\nexport { Decal };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "applyProps", "DecalGeometry", "isArray", "vec", "Array", "vecToArray", "Vector3", "<PERSON>uler", "x", "y", "z", "Decal", "forwardRef", "debug", "mesh", "children", "position", "rotation", "scale", "props", "ref", "useRef", "useImperativeHandle", "current", "helper", "useLayoutEffect", "parent", "target", "<PERSON><PERSON>", "Error", "state", "matrixWorld", "clone", "identity", "o", "Object3D", "copy", "lookAt", "rotateZ", "geometry", "dispose", "createElement", "transparent", "polygonOffset", "polygonOffsetFactor", "wireframe"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Decal.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { applyProps } from '@react-three/fiber';\nimport { DecalGeometry } from 'three-stdlib';\n\nfunction isArray(vec) {\n  return Array.isArray(vec);\n}\n\nfunction vecToArray(vec = [0, 0, 0]) {\n  if (isArray(vec)) {\n    return vec;\n  } else if (vec instanceof THREE.Vector3 || vec instanceof THREE.Euler) {\n    return [vec.x, vec.y, vec.z];\n  } else {\n    return [vec, vec, vec];\n  }\n}\n\nconst Decal = /*#__PURE__*/React.forwardRef(function Decal({\n  debug,\n  mesh,\n  children,\n  position,\n  rotation,\n  scale,\n  ...props\n}, forwardRef) {\n  const ref = React.useRef(null);\n  React.useImperativeHandle(forwardRef, () => ref.current);\n  const helper = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = (mesh == null ? void 0 : mesh.current) || ref.current.parent;\n    const target = ref.current;\n\n    if (!(parent instanceof THREE.Mesh)) {\n      throw new Error('Decal must have a Mesh as parent or specify its \"mesh\" prop');\n    }\n\n    const state = {\n      position: new THREE.Vector3(),\n      rotation: new THREE.Euler(),\n      scale: new THREE.Vector3(1, 1, 1)\n    };\n\n    if (parent) {\n      applyProps(state, {\n        position,\n        scale\n      }); // Zero out the parents matrix world for this operation\n\n      const matrixWorld = parent.matrixWorld.clone();\n      parent.matrixWorld.identity();\n\n      if (!rotation || typeof rotation === 'number') {\n        const o = new THREE.Object3D();\n        o.position.copy(state.position);\n        o.lookAt(parent.position);\n        if (typeof rotation === 'number') o.rotateZ(rotation);\n        applyProps(state, {\n          rotation: o.rotation\n        });\n      } else {\n        applyProps(state, {\n          rotation\n        });\n      }\n\n      target.geometry = new DecalGeometry(parent, state.position, state.rotation, state.scale);\n      if (helper.current) applyProps(helper.current, state); // Reset parents matix-world\n\n      parent.matrixWorld = matrixWorld;\n      return () => {\n        target.geometry.dispose();\n      };\n    }\n  }, [mesh, ...vecToArray(position), ...vecToArray(scale), ...vecToArray(rotation)]);\n  return /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: ref\n  }, children || /*#__PURE__*/React.createElement(\"meshStandardMaterial\", _extends({\n    transparent: true,\n    polygonOffset: true,\n    polygonOffsetFactor: -10\n  }, props)), debug && /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: helper\n  }, /*#__PURE__*/React.createElement(\"boxGeometry\", null), /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n    wireframe: true\n  }), /*#__PURE__*/React.createElement(\"axesHelper\", null)));\n});\n\nexport { Decal };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,aAAa,QAAQ,cAAc;AAE5C,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B;AAEA,SAASE,UAAUA,CAACF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;EACnC,IAAID,OAAO,CAACC,GAAG,CAAC,EAAE;IAChB,OAAOA,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,YAAYJ,KAAK,CAACO,OAAO,IAAIH,GAAG,YAAYJ,KAAK,CAACQ,KAAK,EAAE;IACrE,OAAO,CAACJ,GAAG,CAACK,CAAC,EAAEL,GAAG,CAACM,CAAC,EAAEN,GAAG,CAACO,CAAC,CAAC;EAC9B,CAAC,MAAM;IACL,OAAO,CAACP,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC;EACxB;AACF;AAEA,MAAMQ,KAAK,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,SAASD,KAAKA,CAAC;EACzDE,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEP,UAAU,EAAE;EACb,MAAMQ,GAAG,GAAGtB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EAC9BvB,KAAK,CAACwB,mBAAmB,CAACV,UAAU,EAAE,MAAMQ,GAAG,CAACG,OAAO,CAAC;EACxD,MAAMC,MAAM,GAAG1B,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EACjCvB,KAAK,CAAC2B,eAAe,CAAC,MAAM;IAC1B,MAAMC,MAAM,GAAG,CAACZ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACS,OAAO,KAAKH,GAAG,CAACG,OAAO,CAACG,MAAM;IAC3E,MAAMC,MAAM,GAAGP,GAAG,CAACG,OAAO;IAE1B,IAAI,EAAEG,MAAM,YAAY3B,KAAK,CAAC6B,IAAI,CAAC,EAAE;MACnC,MAAM,IAAIC,KAAK,CAAC,6DAA6D,CAAC;IAChF;IAEA,MAAMC,KAAK,GAAG;MACZd,QAAQ,EAAE,IAAIjB,KAAK,CAACO,OAAO,CAAC,CAAC;MAC7BW,QAAQ,EAAE,IAAIlB,KAAK,CAACQ,KAAK,CAAC,CAAC;MAC3BW,KAAK,EAAE,IAAInB,KAAK,CAACO,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAClC,CAAC;IAED,IAAIoB,MAAM,EAAE;MACV1B,UAAU,CAAC8B,KAAK,EAAE;QAChBd,QAAQ;QACRE;MACF,CAAC,CAAC,CAAC,CAAC;;MAEJ,MAAMa,WAAW,GAAGL,MAAM,CAACK,WAAW,CAACC,KAAK,CAAC,CAAC;MAC9CN,MAAM,CAACK,WAAW,CAACE,QAAQ,CAAC,CAAC;MAE7B,IAAI,CAAChB,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAC7C,MAAMiB,CAAC,GAAG,IAAInC,KAAK,CAACoC,QAAQ,CAAC,CAAC;QAC9BD,CAAC,CAAClB,QAAQ,CAACoB,IAAI,CAACN,KAAK,CAACd,QAAQ,CAAC;QAC/BkB,CAAC,CAACG,MAAM,CAACX,MAAM,CAACV,QAAQ,CAAC;QACzB,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAEiB,CAAC,CAACI,OAAO,CAACrB,QAAQ,CAAC;QACrDjB,UAAU,CAAC8B,KAAK,EAAE;UAChBb,QAAQ,EAAEiB,CAAC,CAACjB;QACd,CAAC,CAAC;MACJ,CAAC,MAAM;QACLjB,UAAU,CAAC8B,KAAK,EAAE;UAChBb;QACF,CAAC,CAAC;MACJ;MAEAU,MAAM,CAACY,QAAQ,GAAG,IAAItC,aAAa,CAACyB,MAAM,EAAEI,KAAK,CAACd,QAAQ,EAAEc,KAAK,CAACb,QAAQ,EAAEa,KAAK,CAACZ,KAAK,CAAC;MACxF,IAAIM,MAAM,CAACD,OAAO,EAAEvB,UAAU,CAACwB,MAAM,CAACD,OAAO,EAAEO,KAAK,CAAC,CAAC,CAAC;;MAEvDJ,MAAM,CAACK,WAAW,GAAGA,WAAW;MAChC,OAAO,MAAM;QACXJ,MAAM,CAACY,QAAQ,CAACC,OAAO,CAAC,CAAC;MAC3B,CAAC;IACH;EACF,CAAC,EAAE,CAAC1B,IAAI,EAAE,GAAGT,UAAU,CAACW,QAAQ,CAAC,EAAE,GAAGX,UAAU,CAACa,KAAK,CAAC,EAAE,GAAGb,UAAU,CAACY,QAAQ,CAAC,CAAC,CAAC;EAClF,OAAO,aAAanB,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE;IAC9CrB,GAAG,EAAEA;EACP,CAAC,EAAEL,QAAQ,IAAI,aAAajB,KAAK,CAAC2C,aAAa,CAAC,sBAAsB,EAAE5C,QAAQ,CAAC;IAC/E6C,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,CAAC;EACxB,CAAC,EAAEzB,KAAK,CAAC,CAAC,EAAEN,KAAK,IAAI,aAAaf,KAAK,CAAC2C,aAAa,CAAC,MAAM,EAAE;IAC5DrB,GAAG,EAAEI;EACP,CAAC,EAAE,aAAa1B,KAAK,CAAC2C,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,aAAa3C,KAAK,CAAC2C,aAAa,CAAC,oBAAoB,EAAE;IAC/GI,SAAS,EAAE;EACb,CAAC,CAAC,EAAE,aAAa/C,KAAK,CAAC2C,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC;AAEF,SAAS9B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}