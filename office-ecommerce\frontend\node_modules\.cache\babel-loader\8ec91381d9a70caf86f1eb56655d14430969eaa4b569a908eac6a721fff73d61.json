{"ast": null, "code": "class AsciiEffect {\n  constructor(renderer, charSet = \" .:-=+*#%@\", options = {}) {\n    const fResolution = options[\"resolution\"] || 0.15;\n    const iScale = options[\"scale\"] || 1;\n    const bColor = options[\"color\"] || false;\n    const bAlpha = options[\"alpha\"] || false;\n    const bBlock = options[\"block\"] || false;\n    const bInvert = options[\"invert\"] || false;\n    const strResolution = options[\"strResolution\"] || \"low\";\n    let width, height;\n    const domElement = document.createElement(\"div\");\n    domElement.style.cursor = \"default\";\n    const oAscii = document.createElement(\"table\");\n    domElement.appendChild(oAscii);\n    let iWidth, iHeight;\n    let oImg;\n    this.setSize = function (w, h) {\n      width = w;\n      height = h;\n      renderer.setSize(w, h);\n      initAsciiSize();\n    };\n    this.render = function (scene, camera) {\n      renderer.render(scene, camera);\n      asciifyImage(oAscii);\n    };\n    this.domElement = domElement;\n    function initAsciiSize() {\n      iWidth = Math.floor(width * fResolution);\n      iHeight = Math.floor(height * fResolution);\n      oCanvas.width = iWidth;\n      oCanvas.height = iHeight;\n      oImg = renderer.domElement;\n      if (oImg.style.backgroundColor) {\n        oAscii.rows[0].cells[0].style.backgroundColor = oImg.style.backgroundColor;\n        oAscii.rows[0].cells[0].style.color = oImg.style.color;\n      }\n      oAscii.cellSpacing = 0;\n      oAscii.cellPadding = 0;\n      const oStyle = oAscii.style;\n      oStyle.whiteSpace = \"pre\";\n      oStyle.margin = \"0px\";\n      oStyle.padding = \"0px\";\n      oStyle.letterSpacing = fLetterSpacing + \"px\";\n      oStyle.fontFamily = strFont;\n      oStyle.fontSize = fFontSize + \"px\";\n      oStyle.lineHeight = fLineHeight + \"px\";\n      oStyle.textAlign = \"left\";\n      oStyle.textDecoration = \"none\";\n    }\n    const aDefaultCharList = \" .,:;i1tfLCG08@\".split(\"\");\n    const aDefaultColorCharList = \" CGO08@\".split(\"\");\n    const strFont = \"courier new, monospace\";\n    const oCanvasImg = renderer.domElement;\n    const oCanvas = document.createElement(\"canvas\");\n    if (!oCanvas.getContext) {\n      return;\n    }\n    const oCtx = oCanvas.getContext(\"2d\");\n    if (!oCtx.getImageData) {\n      return;\n    }\n    let aCharList = bColor ? aDefaultColorCharList : aDefaultCharList;\n    if (charSet) aCharList = charSet;\n    const fFontSize = 2 / fResolution * iScale;\n    const fLineHeight = 2 / fResolution * iScale;\n    let fLetterSpacing = 0;\n    if (strResolution == \"low\") {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = -1;\n          break;\n        case 2:\n        case 3:\n          fLetterSpacing = -2.1;\n          break;\n        case 4:\n          fLetterSpacing = -3.1;\n          break;\n        case 5:\n          fLetterSpacing = -4.15;\n          break;\n      }\n    }\n    if (strResolution == \"medium\") {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = 0;\n          break;\n        case 2:\n          fLetterSpacing = -1;\n          break;\n        case 3:\n          fLetterSpacing = -1.04;\n          break;\n        case 4:\n        case 5:\n          fLetterSpacing = -2.1;\n          break;\n      }\n    }\n    if (strResolution == \"high\") {\n      switch (iScale) {\n        case 1:\n        case 2:\n          fLetterSpacing = 0;\n          break;\n        case 3:\n        case 4:\n        case 5:\n          fLetterSpacing = -1;\n          break;\n      }\n    }\n    function asciifyImage(oAscii2) {\n      oCtx.clearRect(0, 0, iWidth, iHeight);\n      oCtx.drawImage(oCanvasImg, 0, 0, iWidth, iHeight);\n      const oImgData = oCtx.getImageData(0, 0, iWidth, iHeight).data;\n      let strChars = \"\";\n      for (let y = 0; y < iHeight; y += 2) {\n        for (let x = 0; x < iWidth; x++) {\n          const iOffset = (y * iWidth + x) * 4;\n          const iRed = oImgData[iOffset];\n          const iGreen = oImgData[iOffset + 1];\n          const iBlue = oImgData[iOffset + 2];\n          const iAlpha = oImgData[iOffset + 3];\n          let iCharIdx;\n          let fBrightness;\n          fBrightness = (0.3 * iRed + 0.59 * iGreen + 0.11 * iBlue) / 255;\n          if (iAlpha == 0) {\n            fBrightness = 1;\n          }\n          iCharIdx = Math.floor((1 - fBrightness) * (aCharList.length - 1));\n          if (bInvert) {\n            iCharIdx = aCharList.length - iCharIdx - 1;\n          }\n          let strThisChar = aCharList[iCharIdx];\n          if (strThisChar === void 0 || strThisChar == \" \") strThisChar = \"&nbsp;\";\n          if (bColor) {\n            strChars += \"<span style='color:rgb(\" + iRed + \",\" + iGreen + \",\" + iBlue + \");\" + (bBlock ? \"background-color:rgb(\" + iRed + \",\" + iGreen + \",\" + iBlue + \");\" : \"\") + (bAlpha ? \"opacity:\" + iAlpha / 255 + \";\" : \"\") + \"'>\" + strThisChar + \"</span>\";\n          } else {\n            strChars += strThisChar;\n          }\n        }\n        strChars += \"<br/>\";\n      }\n      oAscii2.innerHTML = `<tr><td style=\"display:block;width:${width}px;height:${height}px;overflow:hidden\">${strChars}</td></tr>`;\n    }\n  }\n}\nexport { AsciiEffect };", "map": {"version": 3, "names": ["AsciiEffect", "constructor", "renderer", "charSet", "options", "fResolution", "iScale", "bColor", "bAlpha", "bBlock", "bInvert", "strResolution", "width", "height", "dom<PERSON>lement", "document", "createElement", "style", "cursor", "oAscii", "append<PERSON><PERSON><PERSON>", "iWidth", "iHeight", "oImg", "setSize", "w", "h", "initAsciiSize", "render", "scene", "camera", "asciifyImage", "Math", "floor", "oCanvas", "backgroundColor", "rows", "cells", "color", "cellSpacing", "cellPadding", "oStyle", "whiteSpace", "margin", "padding", "letterSpacing", "fLetterSpacing", "fontFamily", "strFont", "fontSize", "fFontSize", "lineHeight", "fLineHeight", "textAlign", "textDecoration", "aDefaultCharList", "split", "aDefaultColorCharList", "oCanvasImg", "getContext", "oCtx", "getImageData", "aCharList", "oAscii2", "clearRect", "drawImage", "oImgData", "data", "str<PERSON><PERSON>s", "y", "x", "iOffset", "iRed", "iGreen", "iBlue", "iAlpha", "iCharIdx", "fBrightness", "length", "strThisChar", "innerHTML"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\effects\\AsciiEffect.js"], "sourcesContent": ["/**\n * Ascii generation is based on https://github.com/hassadee/jsascii/blob/master/jsascii.js\n *\n * 16 April 2012 - @blurspline\n */\n\nclass AsciiEffect {\n  constructor(renderer, charSet = ' .:-=+*#%@', options = {}) {\n    // ' .,:;=|iI+hHOE#`$';\n    // darker bolder character set from https://github.com/saw/Canvas-ASCII-Art/\n    // ' .\\'`^\",:;Il!i~+_-?][}{1)(|/tfjrxnuvczXYUJCLQ0OZmwqpdbkhao*#MW&8%B@$'.split('');\n\n    // Some ASCII settings\n\n    const fResolution = options['resolution'] || 0.15 // Higher for more details\n    const iScale = options['scale'] || 1\n    const bColor = options['color'] || false // nice but slows down rendering!\n    const bAlpha = options['alpha'] || false // Transparency\n    const bBlock = options['block'] || false // blocked characters. like good O dos\n    const bInvert = options['invert'] || false // black is white, white is black\n    const strResolution = options['strResolution'] || 'low'\n\n    let width, height\n\n    const domElement = document.createElement('div')\n    domElement.style.cursor = 'default'\n\n    const oAscii = document.createElement('table')\n    domElement.appendChild(oAscii)\n\n    let iWidth, iHeight\n    let oImg\n\n    this.setSize = function (w, h) {\n      width = w\n      height = h\n\n      renderer.setSize(w, h)\n\n      initAsciiSize()\n    }\n\n    this.render = function (scene, camera) {\n      renderer.render(scene, camera)\n      asciifyImage(oAscii)\n    }\n\n    this.domElement = domElement\n\n    // Throw in ascii library from https://github.com/hassadee/jsascii/blob/master/jsascii.js (MIT License)\n\n    function initAsciiSize() {\n      iWidth = Math.floor(width * fResolution)\n      iHeight = Math.floor(height * fResolution)\n\n      oCanvas.width = iWidth\n      oCanvas.height = iHeight\n      // oCanvas.style.display = \"none\";\n      // oCanvas.style.width = iWidth;\n      // oCanvas.style.height = iHeight;\n\n      oImg = renderer.domElement\n\n      if (oImg.style.backgroundColor) {\n        oAscii.rows[0].cells[0].style.backgroundColor = oImg.style.backgroundColor\n        oAscii.rows[0].cells[0].style.color = oImg.style.color\n      }\n\n      oAscii.cellSpacing = 0\n      oAscii.cellPadding = 0\n\n      const oStyle = oAscii.style\n      oStyle.whiteSpace = 'pre'\n      oStyle.margin = '0px'\n      oStyle.padding = '0px'\n      oStyle.letterSpacing = fLetterSpacing + 'px'\n      oStyle.fontFamily = strFont\n      oStyle.fontSize = fFontSize + 'px'\n      oStyle.lineHeight = fLineHeight + 'px'\n      oStyle.textAlign = 'left'\n      oStyle.textDecoration = 'none'\n    }\n\n    const aDefaultCharList = ' .,:;i1tfLCG08@'.split('')\n    const aDefaultColorCharList = ' CGO08@'.split('')\n    const strFont = 'courier new, monospace'\n\n    const oCanvasImg = renderer.domElement\n\n    const oCanvas = document.createElement('canvas')\n    if (!oCanvas.getContext) {\n      return\n    }\n\n    const oCtx = oCanvas.getContext('2d')\n    if (!oCtx.getImageData) {\n      return\n    }\n\n    let aCharList = bColor ? aDefaultColorCharList : aDefaultCharList\n\n    if (charSet) aCharList = charSet\n\n    // Setup dom\n\n    const fFontSize = (2 / fResolution) * iScale\n    const fLineHeight = (2 / fResolution) * iScale\n\n    // adjust letter-spacing for all combinations of scale and resolution to get it to fit the image width.\n\n    let fLetterSpacing = 0\n\n    if (strResolution == 'low') {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = -1\n          break\n        case 2:\n        case 3:\n          fLetterSpacing = -2.1\n          break\n        case 4:\n          fLetterSpacing = -3.1\n          break\n        case 5:\n          fLetterSpacing = -4.15\n          break\n      }\n    }\n\n    if (strResolution == 'medium') {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = 0\n          break\n        case 2:\n          fLetterSpacing = -1\n          break\n        case 3:\n          fLetterSpacing = -1.04\n          break\n        case 4:\n        case 5:\n          fLetterSpacing = -2.1\n          break\n      }\n    }\n\n    if (strResolution == 'high') {\n      switch (iScale) {\n        case 1:\n        case 2:\n          fLetterSpacing = 0\n          break\n        case 3:\n        case 4:\n        case 5:\n          fLetterSpacing = -1\n          break\n      }\n    }\n\n    // can't get a span or div to flow like an img element, but a table works?\n\n    // convert img element to ascii\n\n    function asciifyImage(oAscii) {\n      oCtx.clearRect(0, 0, iWidth, iHeight)\n      oCtx.drawImage(oCanvasImg, 0, 0, iWidth, iHeight)\n      const oImgData = oCtx.getImageData(0, 0, iWidth, iHeight).data\n\n      // Coloring loop starts now\n      let strChars = ''\n\n      // console.time('rendering');\n\n      for (let y = 0; y < iHeight; y += 2) {\n        for (let x = 0; x < iWidth; x++) {\n          const iOffset = (y * iWidth + x) * 4\n\n          const iRed = oImgData[iOffset]\n          const iGreen = oImgData[iOffset + 1]\n          const iBlue = oImgData[iOffset + 2]\n          const iAlpha = oImgData[iOffset + 3]\n          let iCharIdx\n\n          let fBrightness\n\n          fBrightness = (0.3 * iRed + 0.59 * iGreen + 0.11 * iBlue) / 255\n          // fBrightness = (0.3*iRed + 0.5*iGreen + 0.3*iBlue) / 255;\n\n          if (iAlpha == 0) {\n            // should calculate alpha instead, but quick hack :)\n            //fBrightness *= (iAlpha / 255);\n            fBrightness = 1\n          }\n\n          iCharIdx = Math.floor((1 - fBrightness) * (aCharList.length - 1))\n\n          if (bInvert) {\n            iCharIdx = aCharList.length - iCharIdx - 1\n          }\n\n          // good for debugging\n          //fBrightness = Math.floor(fBrightness * 10);\n          //strThisChar = fBrightness;\n\n          let strThisChar = aCharList[iCharIdx]\n\n          if (strThisChar === undefined || strThisChar == ' ') strThisChar = '&nbsp;'\n\n          if (bColor) {\n            strChars +=\n              \"<span style='\" +\n              'color:rgb(' +\n              iRed +\n              ',' +\n              iGreen +\n              ',' +\n              iBlue +\n              ');' +\n              (bBlock ? 'background-color:rgb(' + iRed + ',' + iGreen + ',' + iBlue + ');' : '') +\n              (bAlpha ? 'opacity:' + iAlpha / 255 + ';' : '') +\n              \"'>\" +\n              strThisChar +\n              '</span>'\n          } else {\n            strChars += strThisChar\n          }\n        }\n\n        strChars += '<br/>'\n      }\n\n      oAscii.innerHTML = `<tr><td style=\"display:block;width:${width}px;height:${height}px;overflow:hidden\">${strChars}</td></tr>`\n\n      // console.timeEnd('rendering');\n\n      // return oAscii;\n    }\n  }\n}\n\nexport { AsciiEffect }\n"], "mappings": "AAMA,MAAMA,WAAA,CAAY;EAChBC,YAAYC,QAAA,EAAUC,OAAA,GAAU,cAAcC,OAAA,GAAU,IAAI;IAO1D,MAAMC,WAAA,GAAcD,OAAA,CAAQ,YAAY,KAAK;IAC7C,MAAME,MAAA,GAASF,OAAA,CAAQ,OAAO,KAAK;IACnC,MAAMG,MAAA,GAASH,OAAA,CAAQ,OAAO,KAAK;IACnC,MAAMI,MAAA,GAASJ,OAAA,CAAQ,OAAO,KAAK;IACnC,MAAMK,MAAA,GAASL,OAAA,CAAQ,OAAO,KAAK;IACnC,MAAMM,OAAA,GAAUN,OAAA,CAAQ,QAAQ,KAAK;IACrC,MAAMO,aAAA,GAAgBP,OAAA,CAAQ,eAAe,KAAK;IAElD,IAAIQ,KAAA,EAAOC,MAAA;IAEX,MAAMC,UAAA,GAAaC,QAAA,CAASC,aAAA,CAAc,KAAK;IAC/CF,UAAA,CAAWG,KAAA,CAAMC,MAAA,GAAS;IAE1B,MAAMC,MAAA,GAASJ,QAAA,CAASC,aAAA,CAAc,OAAO;IAC7CF,UAAA,CAAWM,WAAA,CAAYD,MAAM;IAE7B,IAAIE,MAAA,EAAQC,OAAA;IACZ,IAAIC,IAAA;IAEJ,KAAKC,OAAA,GAAU,UAAUC,CAAA,EAAGC,CAAA,EAAG;MAC7Bd,KAAA,GAAQa,CAAA;MACRZ,MAAA,GAASa,CAAA;MAETxB,QAAA,CAASsB,OAAA,CAAQC,CAAA,EAAGC,CAAC;MAErBC,aAAA,CAAe;IAChB;IAED,KAAKC,MAAA,GAAS,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACrC5B,QAAA,CAAS0B,MAAA,CAAOC,KAAA,EAAOC,MAAM;MAC7BC,YAAA,CAAaZ,MAAM;IACpB;IAED,KAAKL,UAAA,GAAaA,UAAA;IAIlB,SAASa,cAAA,EAAgB;MACvBN,MAAA,GAASW,IAAA,CAAKC,KAAA,CAAMrB,KAAA,GAAQP,WAAW;MACvCiB,OAAA,GAAUU,IAAA,CAAKC,KAAA,CAAMpB,MAAA,GAASR,WAAW;MAEzC6B,OAAA,CAAQtB,KAAA,GAAQS,MAAA;MAChBa,OAAA,CAAQrB,MAAA,GAASS,OAAA;MAKjBC,IAAA,GAAOrB,QAAA,CAASY,UAAA;MAEhB,IAAIS,IAAA,CAAKN,KAAA,CAAMkB,eAAA,EAAiB;QAC9BhB,MAAA,CAAOiB,IAAA,CAAK,CAAC,EAAEC,KAAA,CAAM,CAAC,EAAEpB,KAAA,CAAMkB,eAAA,GAAkBZ,IAAA,CAAKN,KAAA,CAAMkB,eAAA;QAC3DhB,MAAA,CAAOiB,IAAA,CAAK,CAAC,EAAEC,KAAA,CAAM,CAAC,EAAEpB,KAAA,CAAMqB,KAAA,GAAQf,IAAA,CAAKN,KAAA,CAAMqB,KAAA;MAClD;MAEDnB,MAAA,CAAOoB,WAAA,GAAc;MACrBpB,MAAA,CAAOqB,WAAA,GAAc;MAErB,MAAMC,MAAA,GAAStB,MAAA,CAAOF,KAAA;MACtBwB,MAAA,CAAOC,UAAA,GAAa;MACpBD,MAAA,CAAOE,MAAA,GAAS;MAChBF,MAAA,CAAOG,OAAA,GAAU;MACjBH,MAAA,CAAOI,aAAA,GAAgBC,cAAA,GAAiB;MACxCL,MAAA,CAAOM,UAAA,GAAaC,OAAA;MACpBP,MAAA,CAAOQ,QAAA,GAAWC,SAAA,GAAY;MAC9BT,MAAA,CAAOU,UAAA,GAAaC,WAAA,GAAc;MAClCX,MAAA,CAAOY,SAAA,GAAY;MACnBZ,MAAA,CAAOa,cAAA,GAAiB;IACzB;IAED,MAAMC,gBAAA,GAAmB,kBAAkBC,KAAA,CAAM,EAAE;IACnD,MAAMC,qBAAA,GAAwB,UAAUD,KAAA,CAAM,EAAE;IAChD,MAAMR,OAAA,GAAU;IAEhB,MAAMU,UAAA,GAAaxD,QAAA,CAASY,UAAA;IAE5B,MAAMoB,OAAA,GAAUnB,QAAA,CAASC,aAAA,CAAc,QAAQ;IAC/C,IAAI,CAACkB,OAAA,CAAQyB,UAAA,EAAY;MACvB;IACD;IAED,MAAMC,IAAA,GAAO1B,OAAA,CAAQyB,UAAA,CAAW,IAAI;IACpC,IAAI,CAACC,IAAA,CAAKC,YAAA,EAAc;MACtB;IACD;IAED,IAAIC,SAAA,GAAYvD,MAAA,GAASkD,qBAAA,GAAwBF,gBAAA;IAEjD,IAAIpD,OAAA,EAAS2D,SAAA,GAAY3D,OAAA;IAIzB,MAAM+C,SAAA,GAAa,IAAI7C,WAAA,GAAeC,MAAA;IACtC,MAAM8C,WAAA,GAAe,IAAI/C,WAAA,GAAeC,MAAA;IAIxC,IAAIwC,cAAA,GAAiB;IAErB,IAAInC,aAAA,IAAiB,OAAO;MAC1B,QAAQL,MAAA;QACN,KAAK;UACHwC,cAAA,GAAiB;UACjB;QACF,KAAK;QACL,KAAK;UACHA,cAAA,GAAiB;UACjB;QACF,KAAK;UACHA,cAAA,GAAiB;UACjB;QACF,KAAK;UACHA,cAAA,GAAiB;UACjB;MACH;IACF;IAED,IAAInC,aAAA,IAAiB,UAAU;MAC7B,QAAQL,MAAA;QACN,KAAK;UACHwC,cAAA,GAAiB;UACjB;QACF,KAAK;UACHA,cAAA,GAAiB;UACjB;QACF,KAAK;UACHA,cAAA,GAAiB;UACjB;QACF,KAAK;QACL,KAAK;UACHA,cAAA,GAAiB;UACjB;MACH;IACF;IAED,IAAInC,aAAA,IAAiB,QAAQ;MAC3B,QAAQL,MAAA;QACN,KAAK;QACL,KAAK;UACHwC,cAAA,GAAiB;UACjB;QACF,KAAK;QACL,KAAK;QACL,KAAK;UACHA,cAAA,GAAiB;UACjB;MACH;IACF;IAMD,SAASf,aAAagC,OAAA,EAAQ;MAC5BH,IAAA,CAAKI,SAAA,CAAU,GAAG,GAAG3C,MAAA,EAAQC,OAAO;MACpCsC,IAAA,CAAKK,SAAA,CAAUP,UAAA,EAAY,GAAG,GAAGrC,MAAA,EAAQC,OAAO;MAChD,MAAM4C,QAAA,GAAWN,IAAA,CAAKC,YAAA,CAAa,GAAG,GAAGxC,MAAA,EAAQC,OAAO,EAAE6C,IAAA;MAG1D,IAAIC,QAAA,GAAW;MAIf,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI/C,OAAA,EAAS+C,CAAA,IAAK,GAAG;QACnC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIjD,MAAA,EAAQiD,CAAA,IAAK;UAC/B,MAAMC,OAAA,IAAWF,CAAA,GAAIhD,MAAA,GAASiD,CAAA,IAAK;UAEnC,MAAME,IAAA,GAAON,QAAA,CAASK,OAAO;UAC7B,MAAME,MAAA,GAASP,QAAA,CAASK,OAAA,GAAU,CAAC;UACnC,MAAMG,KAAA,GAAQR,QAAA,CAASK,OAAA,GAAU,CAAC;UAClC,MAAMI,MAAA,GAAST,QAAA,CAASK,OAAA,GAAU,CAAC;UACnC,IAAIK,QAAA;UAEJ,IAAIC,WAAA;UAEJA,WAAA,IAAe,MAAML,IAAA,GAAO,OAAOC,MAAA,GAAS,OAAOC,KAAA,IAAS;UAG5D,IAAIC,MAAA,IAAU,GAAG;YAGfE,WAAA,GAAc;UACf;UAEDD,QAAA,GAAW5C,IAAA,CAAKC,KAAA,EAAO,IAAI4C,WAAA,KAAgBf,SAAA,CAAUgB,MAAA,GAAS,EAAE;UAEhE,IAAIpE,OAAA,EAAS;YACXkE,QAAA,GAAWd,SAAA,CAAUgB,MAAA,GAASF,QAAA,GAAW;UAC1C;UAMD,IAAIG,WAAA,GAAcjB,SAAA,CAAUc,QAAQ;UAEpC,IAAIG,WAAA,KAAgB,UAAaA,WAAA,IAAe,KAAKA,WAAA,GAAc;UAEnE,IAAIxE,MAAA,EAAQ;YACV6D,QAAA,IACE,4BAEAI,IAAA,GACA,MACAC,MAAA,GACA,MACAC,KAAA,GACA,QACCjE,MAAA,GAAS,0BAA0B+D,IAAA,GAAO,MAAMC,MAAA,GAAS,MAAMC,KAAA,GAAQ,OAAO,OAC9ElE,MAAA,GAAS,aAAamE,MAAA,GAAS,MAAM,MAAM,MAC5C,OACAI,WAAA,GACA;UACd,OAAiB;YACLX,QAAA,IAAYW,WAAA;UACb;QACF;QAEDX,QAAA,IAAY;MACb;MAEDL,OAAA,CAAOiB,SAAA,GAAY,sCAAsCpE,KAAA,aAAkBC,MAAA,uBAA6BuD,QAAA;IAKzG;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}