const axios = require('axios');

async function testOrdersEndpoint() {
  try {
    console.log('🔍 Testing Orders Endpoint...');

    // First, login to get a valid token
    console.log('\n🔐 Logging in to get authentication token...');
    let authToken = null;
    try {
      const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      authToken = loginResponse.data.data.token;
      console.log('✅ Login successful, got token');
    } catch (error) {
      console.log('❌ Login failed:', error.response?.status, error.response?.data?.message || error.message);
      return;
    }

    // Test the orders endpoint that was failing
    console.log('\n🔍 Testing /api/orders endpoint...');
    try {
      const response = await axios.get('http://localhost:8000/api/orders?page=1&limit=10', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      console.log('✅ Orders endpoint working');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ Orders endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
      if (error.response?.status === 429) {
        console.log('🚫 Rate limiting is still active - this should be fixed now');
      }
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testOrdersEndpoint();
