// Simple server startup test
const express = require('express');
const cors = require('cors');

console.log('🚀 Testing basic server startup...');

const app = express();
const PORT = 8000;

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/test', (req, res) => {
  res.json({ success: true, message: 'Server is working!' });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`✅ Test server running on http://localhost:${PORT}`);
  console.log('🔍 Testing endpoints...');
  
  // Test the server
  setTimeout(async () => {
    try {
      const axios = require('axios');
      
      // Test health endpoint
      const healthResponse = await axios.get(`http://localhost:${PORT}/health`);
      console.log('✅ Health check:', healthResponse.data);
      
      // Test basic endpoint
      const testResponse = await axios.get(`http://localhost:${PORT}/test`);
      console.log('✅ Test endpoint:', testResponse.data);
      
      console.log('🎉 Basic server functionality confirmed!');
      
    } catch (error) {
      console.error('❌ Server test failed:', error.message);
    }
  }, 1000);
});

// Handle shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down test server...');
  server.close(() => {
    console.log('✅ Test server stopped');
    process.exit(0);
  });
});
