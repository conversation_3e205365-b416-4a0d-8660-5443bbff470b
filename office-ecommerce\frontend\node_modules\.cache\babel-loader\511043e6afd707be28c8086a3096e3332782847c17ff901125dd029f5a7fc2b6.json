{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { UniformsUtils, ShaderMaterial, MathUtils, DataTexture, RedFormat, FloatType } from \"three\";\nimport { DigitalGlitch } from \"../shaders/DigitalGlitch.js\";\nclass GlitchPass extends Pass {\n  constructor(dt_size = 64) {\n    super();\n    __publicField(this, \"material\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"goWild\");\n    __publicField(this, \"curF\");\n    __publicField(this, \"randX\");\n    __publicField(this, \"uniforms\");\n    this.uniforms = UniformsUtils.clone(DigitalGlitch.uniforms);\n    this.uniforms[\"tDisp\"].value = this.generateHeightmap(dt_size);\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: DigitalGlitch.vertexShader,\n      fragmentShader: DigitalGlitch.fragmentShader\n    });\n    this.fsQuad = new FullScreenQuad(this.material);\n    this.goWild = false;\n    this.curF = 0;\n    this.generateTrigger();\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    this.uniforms[\"tDiffuse\"].value = readBuffer.texture;\n    this.uniforms[\"seed\"].value = Math.random();\n    this.uniforms[\"byp\"].value = 0;\n    if (this.curF % this.randX == 0 || this.goWild == true) {\n      this.uniforms[\"amount\"].value = Math.random() / 30;\n      this.uniforms[\"angle\"].value = MathUtils.randFloat(-Math.PI, Math.PI);\n      this.uniforms[\"seed_x\"].value = MathUtils.randFloat(-1, 1);\n      this.uniforms[\"seed_y\"].value = MathUtils.randFloat(-1, 1);\n      this.uniforms[\"distortion_x\"].value = MathUtils.randFloat(0, 1);\n      this.uniforms[\"distortion_y\"].value = MathUtils.randFloat(0, 1);\n      this.curF = 0;\n      this.generateTrigger();\n    } else if (this.curF % this.randX < this.randX / 5) {\n      this.uniforms[\"amount\"].value = Math.random() / 90;\n      this.uniforms[\"angle\"].value = MathUtils.randFloat(-Math.PI, Math.PI);\n      this.uniforms[\"distortion_x\"].value = MathUtils.randFloat(0, 1);\n      this.uniforms[\"distortion_y\"].value = MathUtils.randFloat(0, 1);\n      this.uniforms[\"seed_x\"].value = MathUtils.randFloat(-0.3, 0.3);\n      this.uniforms[\"seed_y\"].value = MathUtils.randFloat(-0.3, 0.3);\n    } else if (this.goWild == false) {\n      this.uniforms[\"byp\"].value = 1;\n    }\n    this.curF++;\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null);\n      this.fsQuad.render(renderer);\n    } else {\n      renderer.setRenderTarget(writeBuffer);\n      if (this.clear) renderer.clear();\n      this.fsQuad.render(renderer);\n    }\n  }\n  generateTrigger() {\n    this.randX = MathUtils.randInt(120, 240);\n  }\n  generateHeightmap(dt_size) {\n    const data_arr = new Float32Array(dt_size * dt_size);\n    const length = dt_size * dt_size;\n    for (let i = 0; i < length; i++) {\n      const val = MathUtils.randFloat(0, 1);\n      data_arr[i] = val;\n    }\n    const texture = new DataTexture(data_arr, dt_size, dt_size, RedFormat, FloatType);\n    texture.needsUpdate = true;\n    return texture;\n  }\n}\nexport { GlitchPass };", "map": {"version": 3, "names": ["GlitchPass", "Pass", "constructor", "dt_size", "__publicField", "uniforms", "UniformsUtils", "clone", "DigitalGlitch", "value", "generateHeightmap", "material", "ShaderMaterial", "vertexShader", "fragmentShader", "fsQuad", "FullScreenQuad", "goWild", "curF", "generateTrigger", "render", "renderer", "writeBuffer", "readBuffer", "texture", "Math", "random", "randX", "MathUtils", "randFloat", "PI", "renderToScreen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "randInt", "data_arr", "Float32Array", "length", "i", "val", "DataTexture", "RedFormat", "FloatType", "needsUpdate"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\GlitchPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport {\n  DataTexture,\n  FloatType,\n  MathUtils,\n  RedFormat,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderTarget,\n  WebGLRenderer,\n  IUniform,\n} from 'three'\nimport { DigitalGlitch } from '../shaders/DigitalGlitch'\n\nclass GlitchPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n  public goWild: boolean\n  public curF: number\n  public randX!: number\n\n  public uniforms: Record<keyof typeof DigitalGlitch['uniforms'], IUniform<any>>\n\n  constructor(dt_size = 64) {\n    super()\n    this.uniforms = UniformsUtils.clone(DigitalGlitch.uniforms)\n    this.uniforms['tDisp'].value = this.generateHeightmap(dt_size)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: DigitalGlitch.vertexShader,\n      fragmentShader: DigitalGlitch.fragmentShader,\n    })\n\n    this.fsQuad = new FullScreenQuad(this.material)\n    this.goWild = false\n    this.curF = 0\n    this.generateTrigger()\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    this.uniforms['tDiffuse'].value = readBuffer.texture\n    this.uniforms['seed'].value = Math.random() //default seeding\n    this.uniforms['byp'].value = 0\n\n    if (this.curF % this.randX == 0 || this.goWild == true) {\n      this.uniforms['amount'].value = Math.random() / 30\n      this.uniforms['angle'].value = MathUtils.randFloat(-Math.PI, Math.PI)\n      this.uniforms['seed_x'].value = MathUtils.randFloat(-1, 1)\n      this.uniforms['seed_y'].value = MathUtils.randFloat(-1, 1)\n      this.uniforms['distortion_x'].value = MathUtils.randFloat(0, 1)\n      this.uniforms['distortion_y'].value = MathUtils.randFloat(0, 1)\n      this.curF = 0\n      this.generateTrigger()\n    } else if (this.curF % this.randX < this.randX / 5) {\n      this.uniforms['amount'].value = Math.random() / 90\n      this.uniforms['angle'].value = MathUtils.randFloat(-Math.PI, Math.PI)\n      this.uniforms['distortion_x'].value = MathUtils.randFloat(0, 1)\n      this.uniforms['distortion_y'].value = MathUtils.randFloat(0, 1)\n      this.uniforms['seed_x'].value = MathUtils.randFloat(-0.3, 0.3)\n      this.uniforms['seed_y'].value = MathUtils.randFloat(-0.3, 0.3)\n    } else if (this.goWild == false) {\n      this.uniforms['byp'].value = 1\n    }\n\n    this.curF++\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  generateTrigger(): void {\n    this.randX = MathUtils.randInt(120, 240)\n  }\n\n  generateHeightmap(dt_size: number): DataTexture {\n    const data_arr = new Float32Array(dt_size * dt_size)\n    const length = dt_size * dt_size\n\n    for (let i = 0; i < length; i++) {\n      const val = MathUtils.randFloat(0, 1)\n      data_arr[i] = val\n    }\n\n    const texture = new DataTexture(data_arr, dt_size, dt_size, RedFormat, FloatType)\n    texture.needsUpdate = true\n    return texture\n  }\n}\n\nexport { GlitchPass }\n"], "mappings": ";;;;;;;;;;;;;;AAcA,MAAMA,UAAA,SAAmBC,IAAA,CAAK;EAS5BC,YAAYC,OAAA,GAAU,IAAI;IAClB;IATDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA;IAIL,KAAKC,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAMC,aAAA,CAAcH,QAAQ;IAC1D,KAAKA,QAAA,CAAS,OAAO,EAAEI,KAAA,GAAQ,KAAKC,iBAAA,CAAkBP,OAAO;IAExD,KAAAQ,QAAA,GAAW,IAAIC,cAAA,CAAe;MACjCP,QAAA,EAAU,KAAKA,QAAA;MACfQ,YAAA,EAAcL,aAAA,CAAcK,YAAA;MAC5BC,cAAA,EAAgBN,aAAA,CAAcM;IAAA,CAC/B;IAED,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKL,QAAQ;IAC9C,KAAKM,MAAA,GAAS;IACd,KAAKC,IAAA,GAAO;IACZ,KAAKC,eAAA,CAAgB;EACvB;EAEOC,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EAEM;IACN,KAAKlB,QAAA,CAAS,UAAU,EAAEI,KAAA,GAAQc,UAAA,CAAWC,OAAA;IAC7C,KAAKnB,QAAA,CAAS,MAAM,EAAEI,KAAA,GAAQgB,IAAA,CAAKC,MAAA;IAC9B,KAAArB,QAAA,CAAS,KAAK,EAAEI,KAAA,GAAQ;IAE7B,IAAI,KAAKS,IAAA,GAAO,KAAKS,KAAA,IAAS,KAAK,KAAKV,MAAA,IAAU,MAAM;MACtD,KAAKZ,QAAA,CAAS,QAAQ,EAAEI,KAAA,GAAQgB,IAAA,CAAKC,MAAA,CAAW;MAC3C,KAAArB,QAAA,CAAS,OAAO,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,CAACJ,IAAA,CAAKK,EAAA,EAAIL,IAAA,CAAKK,EAAE;MACpE,KAAKzB,QAAA,CAAS,QAAQ,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,IAAI,CAAC;MACzD,KAAKxB,QAAA,CAAS,QAAQ,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,IAAI,CAAC;MACzD,KAAKxB,QAAA,CAAS,cAAc,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,GAAG,CAAC;MAC9D,KAAKxB,QAAA,CAAS,cAAc,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,GAAG,CAAC;MAC9D,KAAKX,IAAA,GAAO;MACZ,KAAKC,eAAA,CAAgB;IAAA,WACZ,KAAKD,IAAA,GAAO,KAAKS,KAAA,GAAQ,KAAKA,KAAA,GAAQ,GAAG;MAClD,KAAKtB,QAAA,CAAS,QAAQ,EAAEI,KAAA,GAAQgB,IAAA,CAAKC,MAAA,CAAW;MAC3C,KAAArB,QAAA,CAAS,OAAO,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,CAACJ,IAAA,CAAKK,EAAA,EAAIL,IAAA,CAAKK,EAAE;MACpE,KAAKzB,QAAA,CAAS,cAAc,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,GAAG,CAAC;MAC9D,KAAKxB,QAAA,CAAS,cAAc,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,GAAG,CAAC;MAC9D,KAAKxB,QAAA,CAAS,QAAQ,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,MAAM,GAAG;MAC7D,KAAKxB,QAAA,CAAS,QAAQ,EAAEI,KAAA,GAAQmB,SAAA,CAAUC,SAAA,CAAU,MAAM,GAAG;IAAA,WACpD,KAAKZ,MAAA,IAAU,OAAO;MAC1B,KAAAZ,QAAA,CAAS,KAAK,EAAEI,KAAA,GAAQ;IAC/B;IAEK,KAAAS,IAAA;IAEL,IAAI,KAAKa,cAAA,EAAgB;MACvBV,QAAA,CAASW,eAAA,CAAgB,IAAI;MACxB,KAAAjB,MAAA,CAAOK,MAAA,CAAOC,QAAQ;IAAA,OACtB;MACLA,QAAA,CAASW,eAAA,CAAgBV,WAAW;MACpC,IAAI,KAAKW,KAAA,EAAOZ,QAAA,CAASY,KAAA,CAAM;MAC1B,KAAAlB,MAAA,CAAOK,MAAA,CAAOC,QAAQ;IAC7B;EACF;EAEAF,gBAAA,EAAwB;IACtB,KAAKQ,KAAA,GAAQC,SAAA,CAAUM,OAAA,CAAQ,KAAK,GAAG;EACzC;EAEAxB,kBAAkBP,OAAA,EAA8B;IAC9C,MAAMgC,QAAA,GAAW,IAAIC,YAAA,CAAajC,OAAA,GAAUA,OAAO;IACnD,MAAMkC,MAAA,GAASlC,OAAA,GAAUA,OAAA;IAEzB,SAASmC,CAAA,GAAI,GAAGA,CAAA,GAAID,MAAA,EAAQC,CAAA,IAAK;MAC/B,MAAMC,GAAA,GAAMX,SAAA,CAAUC,SAAA,CAAU,GAAG,CAAC;MACpCM,QAAA,CAASG,CAAC,IAAIC,GAAA;IAChB;IAEA,MAAMf,OAAA,GAAU,IAAIgB,WAAA,CAAYL,QAAA,EAAUhC,OAAA,EAASA,OAAA,EAASsC,SAAA,EAAWC,SAAS;IAChFlB,OAAA,CAAQmB,WAAA,GAAc;IACf,OAAAnB,OAAA;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}