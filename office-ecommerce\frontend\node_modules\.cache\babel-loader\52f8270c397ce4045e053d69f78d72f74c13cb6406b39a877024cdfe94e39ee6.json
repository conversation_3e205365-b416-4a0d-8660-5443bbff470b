{"ast": null, "code": "import { Loader } from \"three\";\nimport { GLTFLoader } from \"./GLTFLoader.js\";\nclass VRMLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.gltfLoader = new GLTFLoader(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    this.gltfLoader.load(url, function (gltf) {\n      try {\n        scope.parse(gltf, onLoad);\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  setDRACOLoader(dracoLoader) {\n    this.gltfLoader.setDRACOLoader(dracoLoader);\n    return this;\n  }\n  parse(gltf, onLoad) {\n    onLoad(gltf);\n  }\n}\nexport { VRMLoader };", "map": {"version": 3, "names": ["VRMLoader", "Loader", "constructor", "manager", "gltfLoader", "GLTFLoader", "load", "url", "onLoad", "onProgress", "onError", "scope", "gltf", "parse", "e", "console", "error", "itemError", "setDRACOLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\VRMLoader.js"], "sourcesContent": ["import { Loader } from 'three'\nimport { GLTFLoader } from '../loaders/GLTFLoader.js'\n\n// VRM Specification: https://dwango.github.io/vrm/vrm_spec/\n//\n// VRM is based on glTF 2.0 and VRM extension is defined\n// in top-level json.extensions.VRM\n\nclass VRMLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n    this.gltfLoader = new GLTFLoader(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    this.gltfLoader.load(\n      url,\n      function (gltf) {\n        try {\n          scope.parse(gltf, onLoad)\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  setDRACOLoader(dracoLoader) {\n    this.gltfLoader.setDRACOLoader(dracoLoader)\n    return this\n  }\n\n  parse(gltf, onLoad) {\n    // const gltfParser = gltf.parser;\n    // const gltfExtensions = gltf.userData.gltfExtensions || {};\n    // const vrmExtension = gltfExtensions.VRM || {};\n\n    // handle VRM Extension here\n\n    onLoad(gltf)\n  }\n}\n\nexport { VRMLoader }\n"], "mappings": ";;AAQA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IACb,KAAKC,UAAA,GAAa,IAAIC,UAAA,CAAWF,OAAO;EACzC;EAEDG,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,KAAKP,UAAA,CAAWE,IAAA,CACdC,GAAA,EACA,UAAUK,IAAA,EAAM;MACd,IAAI;QACFD,KAAA,CAAME,KAAA,CAAMD,IAAA,EAAMJ,MAAM;MACzB,SAAQM,CAAA,EAAP;QACA,IAAIJ,OAAA,EAAS;UACXA,OAAA,CAAQI,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDH,KAAA,CAAMR,OAAA,CAAQc,SAAA,CAAUV,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDQ,eAAeC,WAAA,EAAa;IAC1B,KAAKf,UAAA,CAAWc,cAAA,CAAeC,WAAW;IAC1C,OAAO;EACR;EAEDN,MAAMD,IAAA,EAAMJ,MAAA,EAAQ;IAOlBA,MAAA,CAAOI,IAAI;EACZ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}