const AuthService = require('../services/authService');
const logger = require('../utils/logger');

/**
 * Authentication Controller
 * Handles HTTP requests and responses for authentication operations
 */
class AuthController {

  constructor() {
    this.authService = new AuthService();
  }

  /**
   * User login
   * @route POST /api/auth/login
   * @access Public
   */
  async login(req, res) {
    try {
      const { email, password } = req.body;

      // Validate credentials
      const userResult = await this.authService.validateCredentials(email, password);

      if (!userResult.success) {
        return res.status(401).json({
          success: false,
          message: userResult.error,
          code: userResult.code
        });
      }

      // Generate JWT token
      const token = this.authService.generateToken(userResult.data);

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          token,
          user: userResult.data
        }
      });

    } catch (error) {
      logger.error('AuthController.login error:', error);
      res.status(500).json({
        success: false,
        message: 'Login failed',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * User registration
   * @route POST /api/auth/register
   * @access Public
   */
  async register(req, res) {
    try {
      const userData = req.body;

      const result = await this.authService.registerUser(userData);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      // Generate JWT token for the new user
      const token = this.authService.generateToken(result.data);

      res.status(201).json({
        success: true,
        message: 'Registration successful',
        data: {
          token,
          user: result.data
        }
      });

    } catch (error) {
      logger.error('AuthController.register error:', error);
      res.status(500).json({
        success: false,
        message: 'Registration failed',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Refresh JWT token
   * @route POST /api/auth/refresh
   * @access Private
   */
  async refreshToken(req, res) {
    try {
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1];

      if (!token) {
        return res.status(401).json({
          success: false,
          message: 'Access token required'
        });
      }

      const result = this.authService.refreshToken(token);

      if (!result.success) {
        return res.status(401).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: result.data
      });

    } catch (error) {
      logger.error('AuthController.refreshToken error:', error);
      res.status(500).json({
        success: false,
        message: 'Token refresh failed',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get current user profile
   * @route GET /api/auth/profile
   * @access Private
   */
  async getProfile(req, res) {
    try {
      // User information is available from the auth middleware
      const user = {
        userId: req.user.userId,
        email: req.user.email,
        name: req.user.name,
        role: req.user.role
      };

      res.json({
        success: true,
        data: user
      });

    } catch (error) {
      logger.error('AuthController.getProfile error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch profile',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Update user profile
   * @route PUT /api/auth/profile
   * @access Private
   */
  async updateProfile(req, res) {
    try {
      const { name, email } = req.body;
      const userId = req.user.userId;

      // This would need to be implemented in the auth service
      // const result = await authService.updateProfile(userId, { name, email });

      // For now, return a placeholder response
      res.status(501).json({
        success: false,
        message: 'Profile update not yet implemented'
      });

    } catch (error) {
      logger.error('AuthController.updateProfile error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update profile',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Change password
   * @route POST /api/auth/change-password
   * @access Private
   */
  async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user.userId;

      // This would need to be implemented in the auth service
      // const result = await authService.changePassword(userId, currentPassword, newPassword);

      // For now, return a placeholder response
      res.status(501).json({
        success: false,
        message: 'Change password not yet implemented'
      });

    } catch (error) {
      logger.error('AuthController.changePassword error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to change password',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Request password reset
   * @route POST /api/auth/forgot-password
   * @access Public
   */
  async forgotPassword(req, res) {
    try {
      const { email } = req.body;

      const result = this.authService.generatePasswordResetToken(email);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      // In a real application, you would send this token via email
      // For now, we'll return it in the response (NOT recommended for production)
      res.json({
        success: true,
        message: 'Password reset token generated',
        data: {
          resetToken: result.data.resetToken,
          expiresIn: result.data.expiresIn,
          // In production, remove this and send via email instead
          note: 'In production, this token would be sent via email'
        }
      });

    } catch (error) {
      logger.error('AuthController.forgotPassword error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process password reset request',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Reset password with token
   * @route POST /api/auth/reset-password
   * @access Public
   */
  async resetPassword(req, res) {
    try {
      const { token, newPassword } = req.body;

      const verificationResult = this.authService.verifyPasswordResetToken(token);

      if (!verificationResult.success) {
        return res.status(400).json({
          success: false,
          message: verificationResult.error,
          code: verificationResult.code
        });
      }

      // This would need to be implemented in the auth service
      // const result = await authService.resetPassword(verificationResult.data.email, newPassword);

      // For now, return a placeholder response
      res.status(501).json({
        success: false,
        message: 'Password reset not yet implemented'
      });

    } catch (error) {
      logger.error('AuthController.resetPassword error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reset password',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Logout user (client-side token removal)
   * @route POST /api/auth/logout
   * @access Private
   */
  async logout(req, res) {
    try {
      // In a stateless JWT system, logout is typically handled client-side
      // by removing the token from storage. Server-side logout would require
      // token blacklisting or shorter token expiration times.

      res.json({
        success: true,
        message: 'Logout successful. Please remove the token from client storage.'
      });

    } catch (error) {
      logger.error('AuthController.logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Logout failed',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = AuthController;
