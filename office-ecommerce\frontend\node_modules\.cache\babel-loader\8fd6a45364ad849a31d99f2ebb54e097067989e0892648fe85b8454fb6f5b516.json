{"ast": null, "code": "export var fromCodePoint = String.fromCodePoint || function (astralCodePoint) {\n  return String.fromCharCode(Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800, (astralCodePoint - 0x10000) % 0x400 + 0xdc00);\n};\n// @ts-expect-error - String.prototype.codePointAt might not exist in older node versions\nexport var getCodePoint = String.prototype.codePointAt ? function (input, position) {\n  return input.codePointAt(position);\n} : function (input, position) {\n  return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n};\nexport var highSurrogateFrom = 0xd800;\nexport var highSurrogateTo = 0xdbff;", "map": {"version": 3, "names": ["fromCodePoint", "String", "astralCodePoint", "fromCharCode", "Math", "floor", "getCodePoint", "prototype", "codePointAt", "input", "position", "charCodeAt", "highSurrogateFrom", "highSurrogateTo"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\html-entities\\src\\surrogate-pairs.ts"], "sourcesContent": ["export const fromCodePoint =\n    String.fromCodePoint ||\n    function (astralCodePoint: number) {\n        return String.fromCharCode(\n            Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800,\n            ((astralCodePoint - 0x10000) % 0x400) + 0xdc00\n        );\n    };\n\n// @ts-expect-error - String.prototype.codePointAt might not exist in older node versions\nexport const getCodePoint = String.prototype.codePointAt\n    ? function (input: string, position: number) {\n          return input.codePointAt(position);\n      }\n    : function (input: string, position: number) {\n          return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n      };\n\nexport const highSurrogateFrom = 0xd800;\nexport const highSurrogateTo = 0xdbff;\n"], "mappings": "AAAA,OAAO,IAAMA,aAAa,GACtBC,MAAM,CAACD,aAAa,IACpB,UAAUE,eAAuB;EAC7B,OAAOD,MAAM,CAACE,YAAY,CACtBC,IAAI,CAACC,KAAK,CAAC,CAACH,eAAe,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,MAAM,EACvD,CAACA,eAAe,GAAG,OAAO,IAAI,KAAK,GAAI,MAAM,CACjD;AACL,CAAC;AAEL;AACA,OAAO,IAAMI,YAAY,GAAGL,MAAM,CAACM,SAAS,CAACC,WAAW,GAClD,UAAUC,KAAa,EAAEC,QAAgB;EACrC,OAAOD,KAAK,CAACD,WAAW,CAACE,QAAQ,CAAC;AACtC,CAAC,GACD,UAAUD,KAAa,EAAEC,QAAgB;EACrC,OAAO,CAACD,KAAK,CAACE,UAAU,CAACD,QAAQ,CAAC,GAAG,MAAM,IAAI,KAAK,GAAGD,KAAK,CAACE,UAAU,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;AAC5G,CAAC;AAEP,OAAO,IAAME,iBAAiB,GAAG,MAAM;AACvC,OAAO,IAAMC,eAAe,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}