{"ast": null, "code": "import { Ray, Matrix4, <PERSON><PERSON> } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\nconst ray = /* @__PURE__ */new Ray();\nconst tmpInverseMatrix = /* @__PURE__ */new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\nexport function acceleratedRaycast(raycaster, intersects) {\n  if (this.geometry.boundsTree) {\n    if (this.material === undefined) return;\n    tmpInverseMatrix.copy(this.matrixWorld).invert();\n    ray.copy(raycaster.ray).applyMatrix4(tmpInverseMatrix);\n    const bvh = this.geometry.boundsTree;\n    if (raycaster.firstHitOnly === true) {\n      const hit = convertRaycastIntersect(bvh.raycastFirst(ray, this.material), this, raycaster);\n      if (hit) {\n        intersects.push(hit);\n      }\n    } else {\n      const hits = bvh.raycast(ray, this.material);\n      for (let i = 0, l = hits.length; i < l; i++) {\n        const hit = convertRaycastIntersect(hits[i], this, raycaster);\n        if (hit) {\n          intersects.push(hit);\n        }\n      }\n    }\n  } else {\n    origMeshRaycastFunc.call(this, raycaster, intersects);\n  }\n}\nexport function computeBoundsTree(options) {\n  this.boundsTree = new MeshBVH(this, options);\n  return this.boundsTree;\n}\nexport function disposeBoundsTree() {\n  this.boundsTree = null;\n}", "map": {"version": 3, "names": ["<PERSON>", "Matrix4", "<PERSON><PERSON>", "convertRaycastIntersect", "MeshBVH", "ray", "tmpInverseMatrix", "origMeshRaycastFunc", "prototype", "raycast", "acceleratedRaycast", "raycaster", "intersects", "geometry", "boundsTree", "material", "undefined", "copy", "matrixWorld", "invert", "applyMatrix4", "bvh", "firstHitOnly", "hit", "raycastFirst", "push", "hits", "i", "l", "length", "call", "computeBoundsTree", "options", "disposeBoundsTree"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/utils/ExtensionUtilities.js"], "sourcesContent": ["import { Ray, Matrix4, <PERSON><PERSON> } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\n\nconst ray = /* @__PURE__ */ new Ray();\nconst tmpInverseMatrix = /* @__PURE__ */ new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\n\nexport function acceleratedRaycast( raycaster, intersects ) {\n\n\tif ( this.geometry.boundsTree ) {\n\n\t\tif ( this.material === undefined ) return;\n\n\t\ttmpInverseMatrix.copy( this.matrixWorld ).invert();\n\t\tray.copy( raycaster.ray ).applyMatrix4( tmpInverseMatrix );\n\n\t\tconst bvh = this.geometry.boundsTree;\n\t\tif ( raycaster.firstHitOnly === true ) {\n\n\t\t\tconst hit = convertRaycastIntersect( bvh.raycastFirst( ray, this.material ), this, raycaster );\n\t\t\tif ( hit ) {\n\n\t\t\t\tintersects.push( hit );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst hits = bvh.raycast( ray, this.material );\n\t\t\tfor ( let i = 0, l = hits.length; i < l; i ++ ) {\n\n\t\t\t\tconst hit = convertRaycastIntersect( hits[ i ], this, raycaster );\n\t\t\t\tif ( hit ) {\n\n\t\t\t\t\tintersects.push( hit );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\torigMeshRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nexport function computeBoundsTree( options ) {\n\n\tthis.boundsTree = new MeshBVH( this, options );\n\treturn this.boundsTree;\n\n}\n\nexport function disposeBoundsTree() {\n\n\tthis.boundsTree = null;\n\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,OAAO,EAAEC,IAAI,QAAQ,OAAO;AAC1C,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,OAAO,QAAQ,oBAAoB;AAE5C,MAAMC,GAAG,GAAG,eAAgB,IAAIL,GAAG,CAAC,CAAC;AACrC,MAAMM,gBAAgB,GAAG,eAAgB,IAAIL,OAAO,CAAC,CAAC;AACtD,MAAMM,mBAAmB,GAAGL,IAAI,CAACM,SAAS,CAACC,OAAO;AAElD,OAAO,SAASC,kBAAkBA,CAAEC,SAAS,EAAEC,UAAU,EAAG;EAE3D,IAAK,IAAI,CAACC,QAAQ,CAACC,UAAU,EAAG;IAE/B,IAAK,IAAI,CAACC,QAAQ,KAAKC,SAAS,EAAG;IAEnCV,gBAAgB,CAACW,IAAI,CAAE,IAAI,CAACC,WAAY,CAAC,CAACC,MAAM,CAAC,CAAC;IAClDd,GAAG,CAACY,IAAI,CAAEN,SAAS,CAACN,GAAI,CAAC,CAACe,YAAY,CAAEd,gBAAiB,CAAC;IAE1D,MAAMe,GAAG,GAAG,IAAI,CAACR,QAAQ,CAACC,UAAU;IACpC,IAAKH,SAAS,CAACW,YAAY,KAAK,IAAI,EAAG;MAEtC,MAAMC,GAAG,GAAGpB,uBAAuB,CAAEkB,GAAG,CAACG,YAAY,CAAEnB,GAAG,EAAE,IAAI,CAACU,QAAS,CAAC,EAAE,IAAI,EAAEJ,SAAU,CAAC;MAC9F,IAAKY,GAAG,EAAG;QAEVX,UAAU,CAACa,IAAI,CAAEF,GAAI,CAAC;MAEvB;IAED,CAAC,MAAM;MAEN,MAAMG,IAAI,GAAGL,GAAG,CAACZ,OAAO,CAAEJ,GAAG,EAAE,IAAI,CAACU,QAAS,CAAC;MAC9C,KAAM,IAAIY,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;QAE/C,MAAMJ,GAAG,GAAGpB,uBAAuB,CAAEuB,IAAI,CAAEC,CAAC,CAAE,EAAE,IAAI,EAAEhB,SAAU,CAAC;QACjE,IAAKY,GAAG,EAAG;UAEVX,UAAU,CAACa,IAAI,CAAEF,GAAI,CAAC;QAEvB;MAED;IAED;EAED,CAAC,MAAM;IAENhB,mBAAmB,CAACuB,IAAI,CAAE,IAAI,EAAEnB,SAAS,EAAEC,UAAW,CAAC;EAExD;AAED;AAEA,OAAO,SAASmB,iBAAiBA,CAAEC,OAAO,EAAG;EAE5C,IAAI,CAAClB,UAAU,GAAG,IAAIV,OAAO,CAAE,IAAI,EAAE4B,OAAQ,CAAC;EAC9C,OAAO,IAAI,CAAClB,UAAU;AAEvB;AAEA,OAAO,SAASmB,iBAAiBA,CAAA,EAAG;EAEnC,IAAI,CAACnB,UAAU,GAAG,IAAI;AAEvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}