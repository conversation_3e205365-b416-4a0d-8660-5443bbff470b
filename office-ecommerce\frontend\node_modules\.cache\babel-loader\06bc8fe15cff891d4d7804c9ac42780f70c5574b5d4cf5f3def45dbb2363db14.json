{"ast": null, "code": "import { Vector2 } from \"three\";\nconst NormalMapShader = {\n  uniforms: {\n    heightMap: {\n      value: null\n    },\n    resolution: {\n      value: /* @__PURE__ */new Vector2(512, 512)\n    },\n    scale: {\n      value: /* @__PURE__ */new Vector2(1, 1)\n    },\n    height: {\n      value: 0.05\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float height;\n    uniform vec2 resolution;\n    uniform sampler2D heightMap;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tfloat val = texture2D( heightMap, vUv ).x;\n\n    \tfloat valU = texture2D( heightMap, vUv + vec2( 1.0 / resolution.x, 0.0 ) ).x;\n    \tfloat valV = texture2D( heightMap, vUv + vec2( 0.0, 1.0 / resolution.y ) ).x;\n\n    \tgl_FragColor = vec4( ( 0.5 * normalize( vec3( val - valU, val - valV, height  ) ) + 0.5 ), 1.0 );\n\n    }\n  `)\n};\nexport { NormalMapShader };", "map": {"version": 3, "names": ["NormalMapShader", "uniforms", "heightMap", "value", "resolution", "Vector2", "scale", "height", "vertexShader", "fragmentShader"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\shaders\\NormalMapShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Normal map shader\n * - compute normals from heightmap\n */\n\nexport const NormalMapShader = {\n  uniforms: {\n    heightMap: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2(512, 512) },\n    scale: { value: /* @__PURE__ */ new Vector2(1, 1) },\n    height: { value: 0.05 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float height;\n    uniform vec2 resolution;\n    uniform sampler2D heightMap;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tfloat val = texture2D( heightMap, vUv ).x;\n\n    \tfloat valU = texture2D( heightMap, vUv + vec2( 1.0 / resolution.x, 0.0 ) ).x;\n    \tfloat valV = texture2D( heightMap, vUv + vec2( 0.0, 1.0 / resolution.y ) ).x;\n\n    \tgl_FragColor = vec4( ( 0.5 * normalize( vec3( val - valU, val - valV, height  ) ) + 0.5 ), 1.0 );\n\n    }\n  `,\n}\n"], "mappings": ";AAOO,MAAMA,eAAA,GAAkB;EAC7BC,QAAA,EAAU;IACRC,SAAA,EAAW;MAAEC,KAAA,EAAO;IAAK;IACzBC,UAAA,EAAY;MAAED,KAAA,qBAA2BE,OAAA,CAAQ,KAAK,GAAG;IAAE;IAC3DC,KAAA,EAAO;MAAEH,KAAA,qBAA2BE,OAAA,CAAQ,GAAG,CAAC;IAAE;IAClDE,MAAA,EAAQ;MAAEJ,KAAA,EAAO;IAAK;EACxB;EAEAK,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}