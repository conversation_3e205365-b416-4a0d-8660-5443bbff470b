-- Create Activity Logs table for admin dashboard
-- Run this script to add activity logging functionality

USE OfficeEcommerce;
GO

-- Check if ActivityLogs table already exists
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ActivityLogs')
BEGIN
    -- Create ActivityLogs table
    CREATE TABLE ActivityLogs (
        LogID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        UserID UNIQUEIDENTIFIER NULL,
        UserEmail NVARCHAR(255) NULL,
        UserName NVARCHAR(255) NULL,
        UserRole NVARCHAR(50) NULL,
        Action NVARCHAR(100) NOT NULL, -- CREATE, UPDATE, DELETE, LOGIN, LOGOUT, VIEW, etc.
        EntityType NVARCHAR(50) NOT NULL, -- User, Product, Order, Inventory, etc.
        EntityID UNIQUEIDENTIFIER NULL,
        EntityName NVARCHAR(255) NULL,
        Description NVARCHAR(500) NOT NULL,
        IPAddress NVARCHAR(45) NULL,
        UserAgent NVARCHAR(500) NULL,
        RequestMethod NVARCHAR(10) NULL, -- GET, POST, PUT, DELETE
        RequestPath NVARCHAR(500) NULL,
        StatusCode INT NULL,
        Duration INT NULL, -- Request duration in milliseconds
        OldValues NVARCHAR(MAX) NULL, -- JSON string of old values for updates
        NewValues NVARCHAR(MAX) NULL, -- JSON string of new values for updates
        Metadata NVARCHAR(MAX) NULL, -- Additional JSON metadata
        Severity NVARCHAR(20) NOT NULL DEFAULT 'INFO', -- INFO, WARNING, ERROR, CRITICAL
        CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
        FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE SET NULL
    );

    PRINT 'ActivityLogs table created successfully';
END
ELSE
BEGIN
    PRINT 'ActivityLogs table already exists';
END

-- Create indexes for performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ActivityLogs_UserID')
BEGIN
    CREATE INDEX IX_ActivityLogs_UserID ON ActivityLogs(UserID);
    PRINT 'Index IX_ActivityLogs_UserID created';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ActivityLogs_CreatedAt')
BEGIN
    CREATE INDEX IX_ActivityLogs_CreatedAt ON ActivityLogs(CreatedAt DESC);
    PRINT 'Index IX_ActivityLogs_CreatedAt created';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ActivityLogs_Action')
BEGIN
    CREATE INDEX IX_ActivityLogs_Action ON ActivityLogs(Action);
    PRINT 'Index IX_ActivityLogs_Action created';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ActivityLogs_EntityType')
BEGIN
    CREATE INDEX IX_ActivityLogs_EntityType ON ActivityLogs(EntityType);
    PRINT 'Index IX_ActivityLogs_EntityType created';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ActivityLogs_Severity')
BEGIN
    CREATE INDEX IX_ActivityLogs_Severity ON ActivityLogs(Severity);
    PRINT 'Index IX_ActivityLogs_Severity created';
END

-- Insert some sample activity logs for testing
INSERT INTO ActivityLogs (
    UserID, UserEmail, UserName, UserRole, Action, EntityType, EntityID, EntityName,
    Description, IPAddress, Severity, CreatedAt
)
SELECT 
    u.UserID,
    u.Email,
    CONCAT(u.FirstName, ' ', u.LastName),
    u.Role,
    'LOGIN',
    'Authentication',
    NULL,
    NULL,
    'User logged in successfully',
    '127.0.0.1',
    'INFO',
    DATEADD(HOUR, -2, GETUTCDATE())
FROM Users u
WHERE u.Role IN ('Admin', 'Employee')
AND NOT EXISTS (
    SELECT 1 FROM ActivityLogs 
    WHERE UserID = u.UserID AND Action = 'LOGIN' 
    AND CreatedAt > DATEADD(DAY, -1, GETUTCDATE())
);

-- Insert sample dashboard access logs
INSERT INTO ActivityLogs (
    UserID, UserEmail, UserName, UserRole, Action, EntityType,
    Description, IPAddress, Severity, CreatedAt
)
SELECT 
    u.UserID,
    u.Email,
    CONCAT(u.FirstName, ' ', u.LastName),
    u.Role,
    'VIEW',
    'Dashboard',
    'Accessed admin dashboard',
    '127.0.0.1',
    'INFO',
    DATEADD(MINUTE, -30, GETUTCDATE())
FROM Users u
WHERE u.Role IN ('Admin', 'Employee')
AND NOT EXISTS (
    SELECT 1 FROM ActivityLogs 
    WHERE UserID = u.UserID AND Action = 'VIEW' AND EntityType = 'Dashboard'
    AND CreatedAt > DATEADD(HOUR, -1, GETUTCDATE())
);

PRINT 'Sample activity logs inserted';
PRINT 'Activity logging setup completed successfully!';

-- Show some statistics
SELECT 
    'Total Activity Logs' as Metric,
    COUNT(*) as Value
FROM ActivityLogs

UNION ALL

SELECT 
    'Unique Users with Activity',
    COUNT(DISTINCT UserID)
FROM ActivityLogs
WHERE UserID IS NOT NULL

UNION ALL

SELECT 
    'Activities in Last 24 Hours',
    COUNT(*)
FROM ActivityLogs
WHERE CreatedAt >= DATEADD(DAY, -1, GETUTCDATE());

GO
