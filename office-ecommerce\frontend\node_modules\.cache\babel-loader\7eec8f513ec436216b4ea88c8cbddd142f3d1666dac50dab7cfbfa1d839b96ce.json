{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport websocketService from '../services/websocketService';\nimport { useAuth } from './useAuth';\nexport const useWebSocket = () => {\n  _s();\n  const {\n    token,\n    user\n  } = useAuth();\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('disconnected');\n  const [notifications, setNotifications] = useState([]);\n\n  // Check if we're in mock mode\n  const isMockMode = () => localStorage.getItem('mockMode') === 'true';\n\n  // Connect to WebSocket when user is authenticated\n  useEffect(() => {\n    if (token && user && (user.role === 'Admin' || user.role === 'Employee')) {\n      // Skip WebSocket connection in mock mode\n      if (isMockMode()) {\n        console.log('🔄 Mock mode detected, skipping WebSocket connection');\n        setConnectionStatus('mock');\n        setIsConnected(false);\n        return;\n      }\n      connectWebSocket();\n    } else {\n      disconnectWebSocket();\n    }\n    return () => {\n      disconnectWebSocket();\n    };\n  }, [token, user]);\n  const connectWebSocket = async () => {\n    try {\n      setConnectionStatus('connecting');\n      await websocketService.connect(token);\n      setIsConnected(true);\n      setConnectionStatus('connected');\n\n      // Subscribe to relevant updates based on user role\n      if (user.role === 'Admin' || user.role === 'Employee') {\n        websocketService.subscribeToInventory();\n        websocketService.subscribeToOrders();\n        websocketService.subscribeToDashboard();\n      }\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      setIsConnected(false);\n\n      // In mock mode, don't show error status\n      if (isMockMode()) {\n        setConnectionStatus('mock');\n        console.log('🔄 WebSocket connection failed in mock mode - this is expected');\n      } else {\n        setConnectionStatus('error');\n      }\n    }\n  };\n  const disconnectWebSocket = () => {\n    websocketService.disconnect();\n    setIsConnected(false);\n    setConnectionStatus('disconnected');\n  };\n\n  // Add notification to the list\n  const addNotification = useCallback(notification => {\n    const newNotification = {\n      id: notification.id || Date.now(),\n      title: notification.title,\n      message: notification.message,\n      type: notification.type || 'info',\n      timestamp: notification.timestamp || new Date().toISOString(),\n      read: false\n    };\n    setNotifications(prev => [newNotification, ...prev]);\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notification.type !== 'error') {\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, 5000);\n    }\n  }, []);\n\n  // Remove notification by ID\n  const removeNotification = useCallback(id => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  }, []);\n\n  // Clear all notifications\n  const clearAllNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  // Set up event listeners\n  useEffect(() => {\n    const handleNotification = data => {\n      addNotification(data);\n    };\n    const handleInventoryUpdate = data => {\n      addNotification({\n        title: 'Inventory Updated',\n        message: `${data.item.name} stock updated to ${data.item.currentStock}`,\n        type: 'info'\n      });\n    };\n    const handleLowStockAlert = data => {\n      addNotification({\n        title: 'Low Stock Alert',\n        message: `${data.item.name} is running low (${data.item.currentStock} remaining)`,\n        type: 'warning'\n      });\n    };\n    const handleOrderUpdate = data => {\n      addNotification({\n        title: 'Order Update',\n        message: `Order #${data.orderId} status: ${data.status}`,\n        type: 'info'\n      });\n    };\n    const handleDashboardUpdate = data => {\n      // Dashboard updates are usually silent, just trigger re-renders\n      console.log('Dashboard updated:', data);\n    };\n\n    // Register event listeners\n    websocketService.on('notification', handleNotification);\n    websocketService.on('inventoryUpdated', handleInventoryUpdate);\n    websocketService.on('lowStockAlert', handleLowStockAlert);\n    websocketService.on('orderUpdated', handleOrderUpdate);\n    websocketService.on('dashboardUpdated', handleDashboardUpdate);\n    return () => {\n      // Cleanup event listeners\n      websocketService.off('notification', handleNotification);\n      websocketService.off('inventoryUpdated', handleInventoryUpdate);\n      websocketService.off('lowStockAlert', handleLowStockAlert);\n      websocketService.off('orderUpdated', handleOrderUpdate);\n      websocketService.off('dashboardUpdated', handleDashboardUpdate);\n    };\n  }, [addNotification]);\n\n  // Monitor connection status\n  useEffect(() => {\n    const checkConnectionStatus = () => {\n      const status = websocketService.getConnectionStatus();\n      setConnectionStatus(status);\n      setIsConnected(status === 'connected');\n    };\n    const interval = setInterval(checkConnectionStatus, 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Send event to server\n  const sendEvent = useCallback((event, data) => {\n    websocketService.sendEvent(event, data);\n  }, []);\n\n  // Show notification helper\n  const showNotification = useCallback((title, message, type = 'info') => {\n    websocketService.showNotification(title, message, type);\n  }, []);\n  return {\n    isConnected,\n    connectionStatus,\n    notifications,\n    addNotification,\n    removeNotification,\n    clearAllNotifications,\n    sendEvent,\n    showNotification,\n    connect: connectWebSocket,\n    disconnect: disconnectWebSocket\n  };\n};\n_s(useWebSocket, \"Jr1xA65EDrX9cHGo0byXqMRr0d4=\", false, function () {\n  return [useAuth];\n});", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "websocketService", "useAuth", "useWebSocket", "_s", "token", "user", "isConnected", "setIsConnected", "connectionStatus", "setConnectionStatus", "notifications", "setNotifications", "isMockMode", "localStorage", "getItem", "role", "console", "log", "connectWebSocket", "disconnectWebSocket", "connect", "subscribeToInventory", "subscribeToOrders", "subscribeToDashboard", "error", "disconnect", "addNotification", "notification", "newNotification", "id", "Date", "now", "title", "message", "type", "timestamp", "toISOString", "read", "prev", "setTimeout", "removeNotification", "filter", "clearAllNotifications", "handleNotification", "data", "handleInventoryUpdate", "item", "name", "currentStock", "handleLowStockAlert", "handleOrderUpdate", "orderId", "status", "handleDashboardUpdate", "on", "off", "checkConnectionStatus", "getConnectionStatus", "interval", "setInterval", "clearInterval", "sendEvent", "event", "showNotification"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/hooks/useWebSocket.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport websocketService from '../services/websocketService';\nimport { useAuth } from './useAuth';\n\nexport const useWebSocket = () => {\n  const { token, user } = useAuth();\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('disconnected');\n  const [notifications, setNotifications] = useState([]);\n\n  // Check if we're in mock mode\n  const isMockMode = () => localStorage.getItem('mockMode') === 'true';\n\n  // Connect to WebSocket when user is authenticated\n  useEffect(() => {\n    if (token && user && (user.role === 'Admin' || user.role === 'Employee')) {\n      // Skip WebSocket connection in mock mode\n      if (isMockMode()) {\n        console.log('🔄 Mock mode detected, skipping WebSocket connection');\n        setConnectionStatus('mock');\n        setIsConnected(false);\n        return;\n      }\n      connectWebSocket();\n    } else {\n      disconnectWebSocket();\n    }\n\n    return () => {\n      disconnectWebSocket();\n    };\n  }, [token, user]);\n\n  const connectWebSocket = async () => {\n    try {\n      setConnectionStatus('connecting');\n      await websocketService.connect(token);\n      setIsConnected(true);\n      setConnectionStatus('connected');\n\n      // Subscribe to relevant updates based on user role\n      if (user.role === 'Admin' || user.role === 'Employee') {\n        websocketService.subscribeToInventory();\n        websocketService.subscribeToOrders();\n        websocketService.subscribeToDashboard();\n      }\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      setIsConnected(false);\n\n      // In mock mode, don't show error status\n      if (isMockMode()) {\n        setConnectionStatus('mock');\n        console.log('🔄 WebSocket connection failed in mock mode - this is expected');\n      } else {\n        setConnectionStatus('error');\n      }\n    }\n  };\n\n  const disconnectWebSocket = () => {\n    websocketService.disconnect();\n    setIsConnected(false);\n    setConnectionStatus('disconnected');\n  };\n\n  // Add notification to the list\n  const addNotification = useCallback((notification) => {\n    const newNotification = {\n      id: notification.id || Date.now(),\n      title: notification.title,\n      message: notification.message,\n      type: notification.type || 'info',\n      timestamp: notification.timestamp || new Date().toISOString(),\n      read: false\n    };\n\n    setNotifications(prev => [newNotification, ...prev]);\n\n    // Auto-remove notification after 5 seconds for non-error types\n    if (notification.type !== 'error') {\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, 5000);\n    }\n  }, []);\n\n  // Remove notification by ID\n  const removeNotification = useCallback((id) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  }, []);\n\n  // Clear all notifications\n  const clearAllNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  // Set up event listeners\n  useEffect(() => {\n    const handleNotification = (data) => {\n      addNotification(data);\n    };\n\n    const handleInventoryUpdate = (data) => {\n      addNotification({\n        title: 'Inventory Updated',\n        message: `${data.item.name} stock updated to ${data.item.currentStock}`,\n        type: 'info'\n      });\n    };\n\n    const handleLowStockAlert = (data) => {\n      addNotification({\n        title: 'Low Stock Alert',\n        message: `${data.item.name} is running low (${data.item.currentStock} remaining)`,\n        type: 'warning'\n      });\n    };\n\n    const handleOrderUpdate = (data) => {\n      addNotification({\n        title: 'Order Update',\n        message: `Order #${data.orderId} status: ${data.status}`,\n        type: 'info'\n      });\n    };\n\n    const handleDashboardUpdate = (data) => {\n      // Dashboard updates are usually silent, just trigger re-renders\n      console.log('Dashboard updated:', data);\n    };\n\n    // Register event listeners\n    websocketService.on('notification', handleNotification);\n    websocketService.on('inventoryUpdated', handleInventoryUpdate);\n    websocketService.on('lowStockAlert', handleLowStockAlert);\n    websocketService.on('orderUpdated', handleOrderUpdate);\n    websocketService.on('dashboardUpdated', handleDashboardUpdate);\n\n    return () => {\n      // Cleanup event listeners\n      websocketService.off('notification', handleNotification);\n      websocketService.off('inventoryUpdated', handleInventoryUpdate);\n      websocketService.off('lowStockAlert', handleLowStockAlert);\n      websocketService.off('orderUpdated', handleOrderUpdate);\n      websocketService.off('dashboardUpdated', handleDashboardUpdate);\n    };\n  }, [addNotification]);\n\n  // Monitor connection status\n  useEffect(() => {\n    const checkConnectionStatus = () => {\n      const status = websocketService.getConnectionStatus();\n      setConnectionStatus(status);\n      setIsConnected(status === 'connected');\n    };\n\n    const interval = setInterval(checkConnectionStatus, 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Send event to server\n  const sendEvent = useCallback((event, data) => {\n    websocketService.sendEvent(event, data);\n  }, []);\n\n  // Show notification helper\n  const showNotification = useCallback((title, message, type = 'info') => {\n    websocketService.showNotification(title, message, type);\n  }, []);\n\n  return {\n    isConnected,\n    connectionStatus,\n    notifications,\n    addNotification,\n    removeNotification,\n    clearAllNotifications,\n    sendEvent,\n    showNotification,\n    connect: connectWebSocket,\n    disconnect: disconnectWebSocket\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,OAAO,QAAQ,WAAW;AAEnC,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGJ,OAAO,CAAC,CAAC;EACjC,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,cAAc,CAAC;EACxE,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAMe,UAAU,GAAGA,CAAA,KAAMC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,MAAM;;EAEpE;EACAhB,SAAS,CAAC,MAAM;IACd,IAAIM,KAAK,IAAIC,IAAI,KAAKA,IAAI,CAACU,IAAI,KAAK,OAAO,IAAIV,IAAI,CAACU,IAAI,KAAK,UAAU,CAAC,EAAE;MACxE;MACA,IAAIH,UAAU,CAAC,CAAC,EAAE;QAChBI,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnER,mBAAmB,CAAC,MAAM,CAAC;QAC3BF,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;MACAW,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLC,mBAAmB,CAAC,CAAC;IACvB;IAEA,OAAO,MAAM;MACXA,mBAAmB,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACf,KAAK,EAAEC,IAAI,CAAC,CAAC;EAEjB,MAAMa,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFT,mBAAmB,CAAC,YAAY,CAAC;MACjC,MAAMT,gBAAgB,CAACoB,OAAO,CAAChB,KAAK,CAAC;MACrCG,cAAc,CAAC,IAAI,CAAC;MACpBE,mBAAmB,CAAC,WAAW,CAAC;;MAEhC;MACA,IAAIJ,IAAI,CAACU,IAAI,KAAK,OAAO,IAAIV,IAAI,CAACU,IAAI,KAAK,UAAU,EAAE;QACrDf,gBAAgB,CAACqB,oBAAoB,CAAC,CAAC;QACvCrB,gBAAgB,CAACsB,iBAAiB,CAAC,CAAC;QACpCtB,gBAAgB,CAACuB,oBAAoB,CAAC,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDjB,cAAc,CAAC,KAAK,CAAC;;MAErB;MACA,IAAIK,UAAU,CAAC,CAAC,EAAE;QAChBH,mBAAmB,CAAC,MAAM,CAAC;QAC3BO,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;MAC/E,CAAC,MAAM;QACLR,mBAAmB,CAAC,OAAO,CAAC;MAC9B;IACF;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChCnB,gBAAgB,CAACyB,UAAU,CAAC,CAAC;IAC7BlB,cAAc,CAAC,KAAK,CAAC;IACrBE,mBAAmB,CAAC,cAAc,CAAC;EACrC,CAAC;;EAED;EACA,MAAMiB,eAAe,GAAG3B,WAAW,CAAE4B,YAAY,IAAK;IACpD,MAAMC,eAAe,GAAG;MACtBC,EAAE,EAAEF,YAAY,CAACE,EAAE,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;MACjCC,KAAK,EAAEL,YAAY,CAACK,KAAK;MACzBC,OAAO,EAAEN,YAAY,CAACM,OAAO;MAC7BC,IAAI,EAAEP,YAAY,CAACO,IAAI,IAAI,MAAM;MACjCC,SAAS,EAAER,YAAY,CAACQ,SAAS,IAAI,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;MAC7DC,IAAI,EAAE;IACR,CAAC;IAED1B,gBAAgB,CAAC2B,IAAI,IAAI,CAACV,eAAe,EAAE,GAAGU,IAAI,CAAC,CAAC;;IAEpD;IACA,IAAIX,YAAY,CAACO,IAAI,KAAK,OAAO,EAAE;MACjCK,UAAU,CAAC,MAAM;QACfC,kBAAkB,CAACZ,eAAe,CAACC,EAAE,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,kBAAkB,GAAGzC,WAAW,CAAE8B,EAAE,IAAK;IAC7ClB,gBAAgB,CAAC2B,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACd,YAAY,IAAIA,YAAY,CAACE,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC/E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,qBAAqB,GAAG3C,WAAW,CAAC,MAAM;IAC9CY,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAb,SAAS,CAAC,MAAM;IACd,MAAM6C,kBAAkB,GAAIC,IAAI,IAAK;MACnClB,eAAe,CAACkB,IAAI,CAAC;IACvB,CAAC;IAED,MAAMC,qBAAqB,GAAID,IAAI,IAAK;MACtClB,eAAe,CAAC;QACdM,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE,GAAGW,IAAI,CAACE,IAAI,CAACC,IAAI,qBAAqBH,IAAI,CAACE,IAAI,CAACE,YAAY,EAAE;QACvEd,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED,MAAMe,mBAAmB,GAAIL,IAAI,IAAK;MACpClB,eAAe,CAAC;QACdM,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE,GAAGW,IAAI,CAACE,IAAI,CAACC,IAAI,oBAAoBH,IAAI,CAACE,IAAI,CAACE,YAAY,aAAa;QACjFd,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED,MAAMgB,iBAAiB,GAAIN,IAAI,IAAK;MAClClB,eAAe,CAAC;QACdM,KAAK,EAAE,cAAc;QACrBC,OAAO,EAAE,UAAUW,IAAI,CAACO,OAAO,YAAYP,IAAI,CAACQ,MAAM,EAAE;QACxDlB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAED,MAAMmB,qBAAqB,GAAIT,IAAI,IAAK;MACtC;MACA5B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2B,IAAI,CAAC;IACzC,CAAC;;IAED;IACA5C,gBAAgB,CAACsD,EAAE,CAAC,cAAc,EAAEX,kBAAkB,CAAC;IACvD3C,gBAAgB,CAACsD,EAAE,CAAC,kBAAkB,EAAET,qBAAqB,CAAC;IAC9D7C,gBAAgB,CAACsD,EAAE,CAAC,eAAe,EAAEL,mBAAmB,CAAC;IACzDjD,gBAAgB,CAACsD,EAAE,CAAC,cAAc,EAAEJ,iBAAiB,CAAC;IACtDlD,gBAAgB,CAACsD,EAAE,CAAC,kBAAkB,EAAED,qBAAqB,CAAC;IAE9D,OAAO,MAAM;MACX;MACArD,gBAAgB,CAACuD,GAAG,CAAC,cAAc,EAAEZ,kBAAkB,CAAC;MACxD3C,gBAAgB,CAACuD,GAAG,CAAC,kBAAkB,EAAEV,qBAAqB,CAAC;MAC/D7C,gBAAgB,CAACuD,GAAG,CAAC,eAAe,EAAEN,mBAAmB,CAAC;MAC1DjD,gBAAgB,CAACuD,GAAG,CAAC,cAAc,EAAEL,iBAAiB,CAAC;MACvDlD,gBAAgB,CAACuD,GAAG,CAAC,kBAAkB,EAAEF,qBAAqB,CAAC;IACjE,CAAC;EACH,CAAC,EAAE,CAAC3B,eAAe,CAAC,CAAC;;EAErB;EACA5B,SAAS,CAAC,MAAM;IACd,MAAM0D,qBAAqB,GAAGA,CAAA,KAAM;MAClC,MAAMJ,MAAM,GAAGpD,gBAAgB,CAACyD,mBAAmB,CAAC,CAAC;MACrDhD,mBAAmB,CAAC2C,MAAM,CAAC;MAC3B7C,cAAc,CAAC6C,MAAM,KAAK,WAAW,CAAC;IACxC,CAAC;IAED,MAAMM,QAAQ,GAAGC,WAAW,CAACH,qBAAqB,EAAE,IAAI,CAAC;IACzD,OAAO,MAAMI,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,SAAS,GAAG9D,WAAW,CAAC,CAAC+D,KAAK,EAAElB,IAAI,KAAK;IAC7C5C,gBAAgB,CAAC6D,SAAS,CAACC,KAAK,EAAElB,IAAI,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmB,gBAAgB,GAAGhE,WAAW,CAAC,CAACiC,KAAK,EAAEC,OAAO,EAAEC,IAAI,GAAG,MAAM,KAAK;IACtElC,gBAAgB,CAAC+D,gBAAgB,CAAC/B,KAAK,EAAEC,OAAO,EAAEC,IAAI,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL5B,WAAW;IACXE,gBAAgB;IAChBE,aAAa;IACbgB,eAAe;IACfc,kBAAkB;IAClBE,qBAAqB;IACrBmB,SAAS;IACTE,gBAAgB;IAChB3C,OAAO,EAAEF,gBAAgB;IACzBO,UAAU,EAAEN;EACd,CAAC;AACH,CAAC;AAAChB,EAAA,CAnLWD,YAAY;EAAA,QACCD,OAAO;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}