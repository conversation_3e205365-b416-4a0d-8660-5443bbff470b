const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { executeQuery } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Authentication Service
 * Handles all authentication-related business logic
 */
class AuthService {

  /**
   * Generate JWT token for user
   * @param {Object} user - User object
   * @returns {string} JWT token
   */
  generateToken(user) {
    try {
      const payload = {
        userId: user.UserID || user.id,
        email: user.Email || user.email,
        role: user.Role || user.role,
        name: user.Name || user.name
      };

      const token = jwt.sign(
        payload,
        process.env.JWT_SECRET || 'your-secret-key',
        { 
          expiresIn: process.env.JWT_EXPIRES_IN || '24h',
          issuer: 'office-ecommerce-api',
          audience: 'office-ecommerce-client'
        }
      );

      return token;

    } catch (error) {
      logger.error('AuthService.generateToken error:', error);
      throw new Error('Failed to generate authentication token');
    }
  }

  /**
   * Verify JWT token
   * @param {string} token - JWT token
   * @returns {Object} Decoded token payload
   */
  verifyToken(token) {
    try {
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || 'your-secret-key',
        {
          issuer: 'office-ecommerce-api',
          audience: 'office-ecommerce-client'
        }
      );

      return {
        success: true,
        data: decoded
      };

    } catch (error) {
      logger.error('AuthService.verifyToken error:', error);
      
      if (error.name === 'TokenExpiredError') {
        return {
          success: false,
          error: 'Token has expired',
          code: 'TOKEN_EXPIRED'
        };
      }

      if (error.name === 'JsonWebTokenError') {
        return {
          success: false,
          error: 'Invalid token',
          code: 'INVALID_TOKEN'
        };
      }

      return {
        success: false,
        error: 'Token verification failed',
        code: 'TOKEN_VERIFICATION_FAILED'
      };
    }
  }

  /**
   * Refresh JWT token
   * @param {string} token - Current JWT token
   * @returns {Object} New token result
   */
  refreshToken(token) {
    try {
      const verificationResult = this.verifyToken(token);
      
      if (!verificationResult.success) {
        return verificationResult;
      }

      const user = verificationResult.data;
      const newToken = this.generateToken(user);

      return {
        success: true,
        data: {
          token: newToken,
          user: {
            userId: user.userId,
            email: user.email,
            role: user.role,
            name: user.name
          }
        }
      };

    } catch (error) {
      logger.error('AuthService.refreshToken error:', error);
      return {
        success: false,
        error: 'Failed to refresh token',
        code: 'TOKEN_REFRESH_FAILED'
      };
    }
  }

  /**
   * Validate user credentials (database implementation)
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Object} Validation result
   */
  async validateCredentials(email, password) {
    try {
      // Query the database for the user
      const query = `
        SELECT UserID, Email, PasswordHash, FirstName, LastName, Role, IsActive, EmailVerified
        FROM Users
        WHERE Email = @email AND IsActive = 1
      `;

      const result = await executeQuery(query, { email });

      if (!result.recordset || result.recordset.length === 0) {
        return {
          success: false,
          error: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS'
        };
      }

      const user = result.recordset[0];

      // Check if email is verified (optional check)
      if (!user.EmailVerified) {
        return {
          success: false,
          error: 'Email not verified',
          code: 'EMAIL_NOT_VERIFIED'
        };
      }

      // Compare password with hash
      const isPasswordValid = await bcrypt.compare(password, user.PasswordHash);

      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS'
        };
      }

      // Remove password hash from user object
      const { PasswordHash, ...userWithoutPassword } = user;

      // Format user data for frontend
      const userData = {
        UserID: userWithoutPassword.UserID,
        Email: userWithoutPassword.Email,
        Name: `${userWithoutPassword.FirstName} ${userWithoutPassword.LastName}`,
        FirstName: userWithoutPassword.FirstName,
        LastName: userWithoutPassword.LastName,
        Role: userWithoutPassword.Role,
        IsActive: userWithoutPassword.IsActive,
        EmailVerified: userWithoutPassword.EmailVerified
      };

      return {
        success: true,
        data: userData
      };

    } catch (error) {
      logger.error('AuthService.validateCredentials error:', error);
      return {
        success: false,
        error: 'Authentication service error',
        code: 'SERVICE_ERROR'
      };
    }
  }

  /**
   * Register new user (mock implementation)
   * @param {Object} userData - User registration data
   * @returns {Object} Registration result
   */
  async registerUser(userData) {
    try {
      const { email, password, name, role = 'Customer' } = userData;

      // Validate registration data
      this.validateRegistrationData(userData);

      // Check if user already exists (mock check)
      const existingUser = await this.findUserByEmail(email);
      
      if (existingUser) {
        return {
          success: false,
          error: 'User with this email already exists',
          code: 'USER_ALREADY_EXISTS'
        };
      }

      // Create new user (mock implementation)
      const newUser = {
        UserID: Date.now().toString(), // Mock ID generation
        Email: email,
        Name: name,
        Role: role,
        CreatedAt: new Date().toISOString(),
        IsActive: true
      };

      logger.info(`New user registered: ${email} with role ${role}`);

      return {
        success: true,
        data: newUser
      };

    } catch (error) {
      logger.error('AuthService.registerUser error:', error);
      throw error;
    }
  }

  /**
   * Find user by email (mock implementation)
   * @param {string} email - User email
   * @returns {Object|null} User object or null
   */
  async findUserByEmail(email) {
    try {
      // Mock implementation - would query database in real app
      const mockUsers = [
        { UserID: '1', Email: '<EMAIL>', Name: 'Admin User', Role: 'Admin' },
        { UserID: '2', Email: '<EMAIL>', Name: 'Employee User', Role: 'Employee' },
        { UserID: '3', Email: '<EMAIL>', Name: 'Customer User', Role: 'Customer' }
      ];

      return mockUsers.find(user => user.Email === email) || null;

    } catch (error) {
      logger.error('AuthService.findUserByEmail error:', error);
      throw error;
    }
  }

  /**
   * Check if user has required permission
   * @param {string} userRole - User role
   * @param {string} permission - Required permission
   * @returns {boolean} Has permission
   */
  hasPermission(userRole, permission) {
    const rolePermissions = {
      'Admin': [
        'view_all_orders', 'manage_orders', 'manage_products', 'manage_inventory',
        'manage_suppliers', 'manage_users', 'view_analytics', 'manage_payments'
      ],
      'Employee': [
        'view_all_orders', 'manage_orders', 'manage_products', 'manage_inventory',
        'manage_suppliers', 'view_analytics'
      ],
      'Customer': [
        'view_own_orders', 'create_orders', 'view_products'
      ]
    };

    const userPermissions = rolePermissions[userRole] || [];
    return userPermissions.includes(permission);
  }

  /**
   * Validate registration data
   * @private
   */
  validateRegistrationData(userData) {
    const { email, password, name } = userData;

    if (!email || !this.isValidEmail(email)) {
      throw new Error('Valid email is required');
    }

    if (!password || password.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    if (!name || name.trim().length < 2) {
      throw new Error('Name must be at least 2 characters long');
    }
  }

  /**
   * Validate email format
   * @private
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generate password reset token
   * @param {string} email - User email
   * @returns {Object} Reset token result
   */
  generatePasswordResetToken(email) {
    try {
      const resetToken = jwt.sign(
        { email, type: 'password_reset' },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '1h' }
      );

      return {
        success: true,
        data: {
          resetToken,
          expiresIn: '1 hour'
        }
      };

    } catch (error) {
      logger.error('AuthService.generatePasswordResetToken error:', error);
      return {
        success: false,
        error: 'Failed to generate reset token',
        code: 'RESET_TOKEN_GENERATION_FAILED'
      };
    }
  }

  /**
   * Verify password reset token
   * @param {string} token - Reset token
   * @returns {Object} Verification result
   */
  verifyPasswordResetToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

      if (decoded.type !== 'password_reset') {
        return {
          success: false,
          error: 'Invalid reset token',
          code: 'INVALID_RESET_TOKEN'
        };
      }

      return {
        success: true,
        data: {
          email: decoded.email
        }
      };

    } catch (error) {
      logger.error('AuthService.verifyPasswordResetToken error:', error);
      
      if (error.name === 'TokenExpiredError') {
        return {
          success: false,
          error: 'Reset token has expired',
          code: 'RESET_TOKEN_EXPIRED'
        };
      }

      return {
        success: false,
        error: 'Invalid reset token',
        code: 'INVALID_RESET_TOKEN'
      };
    }
  }
}

module.exports = AuthService;
