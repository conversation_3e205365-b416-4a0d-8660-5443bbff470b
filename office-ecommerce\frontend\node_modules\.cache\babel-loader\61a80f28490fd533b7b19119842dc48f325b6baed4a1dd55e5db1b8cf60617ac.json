{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\Analytics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './AdminComponents.css';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';\nimport { Line, Bar, Doughnut, Pie } from 'react-chartjs-2';\nimport { AnalyticsIcon, InventoryIcon, ProductsIcon } from '../admin/icons/AdminIcons';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement);\nconst Analytics = () => {\n  _s();\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Mock data for now - Enhanced with chart data\n    setTimeout(() => {\n      setAnalyticsData({\n        salesData: {\n          totalSales: 125000,\n          monthlyGrowth: 12.5,\n          topSellingCategory: 'Chairs',\n          averageOrderValue: 850\n        },\n        inventoryData: {\n          totalProducts: 150,\n          lowStockItems: 8,\n          outOfStockItems: 2,\n          inventoryValue: 450000\n        },\n        chartData: {\n          monthlySales: {\n            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n            datasets: [{\n              label: 'Sales (PHP)',\n              data: [85000, 92000, 78000, 105000, 118000, 125000, 132000, 128000, 145000, 138000, 155000, 162000],\n              borderColor: '#F0B21B',\n              backgroundColor: 'rgba(240, 178, 27, 0.1)',\n              tension: 0.4,\n              fill: true\n            }]\n          },\n          categoryDistribution: {\n            labels: ['Chairs', 'Tables', 'Cabinets', 'Workstations', 'Accessories'],\n            datasets: [{\n              data: [35, 25, 20, 15, 5],\n              backgroundColor: ['#F0B21B', '#10B981', '#3B82F6', '#8B5CF6', '#EF4444'],\n              borderWidth: 2,\n              borderColor: '#ffffff'\n            }]\n          },\n          inventoryStatus: {\n            labels: ['In Stock', 'Low Stock', 'Out of Stock'],\n            datasets: [{\n              data: [140, 8, 2],\n              backgroundColor: ['#10B981', '#F59E0B', '#EF4444'],\n              borderWidth: 2,\n              borderColor: '#ffffff'\n            }]\n          },\n          weeklyOrders: {\n            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n            datasets: [{\n              label: 'Orders',\n              data: [12, 19, 15, 25, 22, 18, 8],\n              backgroundColor: 'rgba(240, 178, 27, 0.8)',\n              borderColor: '#F0B21B',\n              borderWidth: 1\n            }]\n          }\n        }\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        titleColor: '#ffffff',\n        bodyColor: '#ffffff',\n        borderColor: '#F0B21B',\n        borderWidth: 1,\n        cornerRadius: 8,\n        displayColors: true\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(0, 0, 0, 0.1)'\n        },\n        ticks: {\n          font: {\n            size: 11\n          }\n        }\n      },\n      x: {\n        grid: {\n          color: 'rgba(0, 0, 0, 0.1)'\n        },\n        ticks: {\n          font: {\n            size: 11\n          }\n        }\n      }\n    }\n  };\n  const doughnutOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        titleColor: '#ffffff',\n        bodyColor: '#ffffff',\n        borderColor: '#F0B21B',\n        borderWidth: 1,\n        cornerRadius: 8\n      }\n    },\n    cutout: '60%'\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading analytics...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"analytics\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"admin-card-title\",\n        children: \"Analytics & Reports\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"admin-btn admin-btn-primary\",\n        children: \"Generate Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"metrics-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n            color: \"#10B981\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: formatCurrency(analyticsData.salesData.totalSales)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [analyticsData.salesData.monthlyGrowth, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Monthly Growth\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(ProductsIcon, {\n            color: \"#F0B21B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: analyticsData.salesData.topSellingCategory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Top Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: \"\\uD83D\\uDED2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: formatCurrency(analyticsData.salesData.averageOrderValue)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Avg Order Value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"charts-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Monthly Sales Trend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"chart-subtitle\",\n            children: \"Revenue over the past 12 months\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: /*#__PURE__*/_jsxDEV(Line, {\n            data: analyticsData.chartData.monthlySales,\n            options: chartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Sales by Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"chart-subtitle\",\n            children: \"Product category distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: /*#__PURE__*/_jsxDEV(Doughnut, {\n            data: analyticsData.chartData.categoryDistribution,\n            options: doughnutOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Weekly Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"chart-subtitle\",\n            children: \"Orders by day of the week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: /*#__PURE__*/_jsxDEV(Bar, {\n            data: analyticsData.chartData.weeklyOrders,\n            options: chartOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Inventory Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"chart-subtitle\",\n            children: \"Stock level distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-container\",\n          children: /*#__PURE__*/_jsxDEV(Pie, {\n            data: analyticsData.chartData.inventoryStatus,\n            options: doughnutOptions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"admin-card-title\",\n        children: \"Inventory Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analytics-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analytics-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Total Products:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: analyticsData.inventoryData.totalProducts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analytics-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Low Stock Items:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"warning\",\n            children: analyticsData.inventoryData.lowStockItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analytics-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Out of Stock:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"danger\",\n            children: analyticsData.inventoryData.outOfStockItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analytics-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Total Inventory Value:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatCurrency(analyticsData.inventoryData.inventoryValue)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"0oYQfyzUsCCB17MBNsUa8HFJaaA=\");\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "Line", "Bar", "Doughnut", "Pie", "AnalyticsIcon", "InventoryIcon", "ProductsIcon", "jsxDEV", "_jsxDEV", "register", "Analytics", "_s", "analyticsData", "setAnalyticsData", "loading", "setLoading", "setTimeout", "salesData", "totalSales", "monthlyGrowth", "topSellingCategory", "averageOrderValue", "inventoryData", "totalProducts", "lowStockItems", "outOfStockItems", "inventoryValue", "chartData", "monthlySales", "labels", "datasets", "label", "data", "borderColor", "backgroundColor", "tension", "fill", "categoryDistribution", "borderWidth", "inventoryStatus", "weeklyOrders", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "usePointStyle", "padding", "font", "size", "tooltip", "titleColor", "bodyColor", "cornerRadius", "displayColors", "scales", "y", "beginAtZero", "grid", "color", "ticks", "x", "doughnutOptions", "cutout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "options", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/Analytics.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './AdminComponents.css';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport { Line, Bar, Doughnut, Pie } from 'react-chartjs-2';\nimport {\n  AnalyticsIcon,\n  InventoryIcon,\n  ProductsIcon\n} from '../admin/icons/AdminIcons';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\nconst Analytics = () => {\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Mock data for now - Enhanced with chart data\n    setTimeout(() => {\n      setAnalyticsData({\n        salesData: {\n          totalSales: 125000,\n          monthlyGrowth: 12.5,\n          topSellingCategory: 'Chairs',\n          averageOrderValue: 850\n        },\n        inventoryData: {\n          totalProducts: 150,\n          lowStockItems: 8,\n          outOfStockItems: 2,\n          inventoryValue: 450000\n        },\n        chartData: {\n          monthlySales: {\n            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n            datasets: [{\n              label: 'Sales (PHP)',\n              data: [85000, 92000, 78000, 105000, 118000, 125000, 132000, 128000, 145000, 138000, 155000, 162000],\n              borderColor: '#F0B21B',\n              backgroundColor: 'rgba(240, 178, 27, 0.1)',\n              tension: 0.4,\n              fill: true\n            }]\n          },\n          categoryDistribution: {\n            labels: ['Chairs', 'Tables', 'Cabinets', 'Workstations', 'Accessories'],\n            datasets: [{\n              data: [35, 25, 20, 15, 5],\n              backgroundColor: [\n                '#F0B21B',\n                '#10B981',\n                '#3B82F6',\n                '#8B5CF6',\n                '#EF4444'\n              ],\n              borderWidth: 2,\n              borderColor: '#ffffff'\n            }]\n          },\n          inventoryStatus: {\n            labels: ['In Stock', 'Low Stock', 'Out of Stock'],\n            datasets: [{\n              data: [140, 8, 2],\n              backgroundColor: [\n                '#10B981',\n                '#F59E0B',\n                '#EF4444'\n              ],\n              borderWidth: 2,\n              borderColor: '#ffffff'\n            }]\n          },\n          weeklyOrders: {\n            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n            datasets: [{\n              label: 'Orders',\n              data: [12, 19, 15, 25, 22, 18, 8],\n              backgroundColor: 'rgba(240, 178, 27, 0.8)',\n              borderColor: '#F0B21B',\n              borderWidth: 1\n            }]\n          }\n        }\n      });\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        titleColor: '#ffffff',\n        bodyColor: '#ffffff',\n        borderColor: '#F0B21B',\n        borderWidth: 1,\n        cornerRadius: 8,\n        displayColors: true\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: 'rgba(0, 0, 0, 0.1)'\n        },\n        ticks: {\n          font: {\n            size: 11\n          }\n        }\n      },\n      x: {\n        grid: {\n          color: 'rgba(0, 0, 0, 0.1)'\n        },\n        ticks: {\n          font: {\n            size: 11\n          }\n        }\n      }\n    }\n  };\n\n  const doughnutOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        titleColor: '#ffffff',\n        bodyColor: '#ffffff',\n        borderColor: '#F0B21B',\n        borderWidth: 1,\n        cornerRadius: 8\n      }\n    },\n    cutout: '60%'\n  };\n\n  if (loading) {\n    return (\n      <div className=\"admin-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading analytics...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"analytics\">\n      <div className=\"admin-card-header\">\n        <h1 className=\"admin-card-title\">Analytics & Reports</h1>\n        <button className=\"admin-btn admin-btn-primary\">Generate Report</button>\n      </div>\n\n      <div className=\"metrics-grid\">\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">\n            <AnalyticsIcon color=\"#10B981\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{formatCurrency(analyticsData.salesData.totalSales)}</h3>\n            <p>Total Sales</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">\n            <AnalyticsIcon color=\"#3B82F6\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{analyticsData.salesData.monthlyGrowth}%</h3>\n            <p>Monthly Growth</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">\n            <ProductsIcon color=\"#F0B21B\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{analyticsData.salesData.topSellingCategory}</h3>\n            <p>Top Category</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">🛒</div>\n          <div className=\"metric-content\">\n            <h3>{formatCurrency(analyticsData.salesData.averageOrderValue)}</h3>\n            <p>Avg Order Value</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts Section */}\n      <div className=\"charts-grid\">\n        {/* Monthly Sales Trend */}\n        <div className=\"chart-card\">\n          <div className=\"chart-header\">\n            <h3>Monthly Sales Trend</h3>\n            <span className=\"chart-subtitle\">Revenue over the past 12 months</span>\n          </div>\n          <div className=\"chart-container\">\n            <Line data={analyticsData.chartData.monthlySales} options={chartOptions} />\n          </div>\n        </div>\n\n        {/* Category Distribution */}\n        <div className=\"chart-card\">\n          <div className=\"chart-header\">\n            <h3>Sales by Category</h3>\n            <span className=\"chart-subtitle\">Product category distribution</span>\n          </div>\n          <div className=\"chart-container\">\n            <Doughnut data={analyticsData.chartData.categoryDistribution} options={doughnutOptions} />\n          </div>\n        </div>\n\n        {/* Weekly Orders */}\n        <div className=\"chart-card\">\n          <div className=\"chart-header\">\n            <h3>Weekly Orders</h3>\n            <span className=\"chart-subtitle\">Orders by day of the week</span>\n          </div>\n          <div className=\"chart-container\">\n            <Bar data={analyticsData.chartData.weeklyOrders} options={chartOptions} />\n          </div>\n        </div>\n\n        {/* Inventory Status */}\n        <div className=\"chart-card\">\n          <div className=\"chart-header\">\n            <h3>Inventory Status</h3>\n            <span className=\"chart-subtitle\">Stock level distribution</span>\n          </div>\n          <div className=\"chart-container\">\n            <Pie data={analyticsData.chartData.inventoryStatus} options={doughnutOptions} />\n          </div>\n        </div>\n      </div>\n\n      <div className=\"admin-card\">\n        <h2 className=\"admin-card-title\">Inventory Summary</h2>\n        <div className=\"analytics-content\">\n          <div className=\"analytics-item\">\n            <span>Total Products:</span>\n            <span>{analyticsData.inventoryData.totalProducts}</span>\n          </div>\n          <div className=\"analytics-item\">\n            <span>Low Stock Items:</span>\n            <span className=\"warning\">{analyticsData.inventoryData.lowStockItems}</span>\n          </div>\n          <div className=\"analytics-item\">\n            <span>Out of Stock:</span>\n            <span className=\"danger\">{analyticsData.inventoryData.outOfStockItems}</span>\n          </div>\n          <div className=\"analytics-item\">\n            <span>Total Inventory Value:</span>\n            <span>{formatCurrency(analyticsData.inventoryData.inventoryValue)}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Analytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,uBAAuB;AAC9B,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,SAASC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,iBAAiB;AAC1D,SACEC,aAAa,EACbC,aAAa,EACbC,YAAY,QACP,2BAA2B;;AAElC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAlB,OAAO,CAACmB,QAAQ,CACdlB,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UACF,CAAC;AAED,MAAMW,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA4B,UAAU,CAAC,MAAM;MACfH,gBAAgB,CAAC;QACfI,SAAS,EAAE;UACTC,UAAU,EAAE,MAAM;UAClBC,aAAa,EAAE,IAAI;UACnBC,kBAAkB,EAAE,QAAQ;UAC5BC,iBAAiB,EAAE;QACrB,CAAC;QACDC,aAAa,EAAE;UACbC,aAAa,EAAE,GAAG;UAClBC,aAAa,EAAE,CAAC;UAChBC,eAAe,EAAE,CAAC;UAClBC,cAAc,EAAE;QAClB,CAAC;QACDC,SAAS,EAAE;UACTC,YAAY,EAAE;YACZC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAC5FC,QAAQ,EAAE,CAAC;cACTC,KAAK,EAAE,aAAa;cACpBC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;cACnGC,WAAW,EAAE,SAAS;cACtBC,eAAe,EAAE,yBAAyB;cAC1CC,OAAO,EAAE,GAAG;cACZC,IAAI,EAAE;YACR,CAAC;UACH,CAAC;UACDC,oBAAoB,EAAE;YACpBR,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,CAAC;YACvEC,QAAQ,EAAE,CAAC;cACTE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;cACzBE,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;cACDI,WAAW,EAAE,CAAC;cACdL,WAAW,EAAE;YACf,CAAC;UACH,CAAC;UACDM,eAAe,EAAE;YACfV,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,CAAC;YACjDC,QAAQ,EAAE,CAAC;cACTE,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;cACjBE,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,CACV;cACDI,WAAW,EAAE,CAAC;cACdL,WAAW,EAAE;YACf,CAAC;UACH,CAAC;UACDO,YAAY,EAAE;YACZX,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACzDC,QAAQ,EAAE,CAAC;cACTC,KAAK,EAAE,QAAQ;cACfC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;cACjCE,eAAe,EAAE,yBAAyB;cAC1CD,WAAW,EAAE,SAAS;cACtBK,WAAW,EAAE;YACf,CAAC;UACH;QACF;MACF,CAAC,CAAC;MACFvB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,MAAMM,YAAY,GAAG;IACnBC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,KAAK;QACfxB,MAAM,EAAE;UACNyB,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE,EAAE;UACXC,IAAI,EAAE;YACJC,IAAI,EAAE;UACR;QACF;MACF,CAAC;MACDC,OAAO,EAAE;QACPxB,eAAe,EAAE,oBAAoB;QACrCyB,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,SAAS;QACpB3B,WAAW,EAAE,SAAS;QACtBK,WAAW,EAAE,CAAC;QACduB,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE;MACjB;IACF,CAAC;IACDC,MAAM,EAAE;MACNC,CAAC,EAAE;QACDC,WAAW,EAAE,IAAI;QACjBC,IAAI,EAAE;UACJC,KAAK,EAAE;QACT,CAAC;QACDC,KAAK,EAAE;UACLZ,IAAI,EAAE;YACJC,IAAI,EAAE;UACR;QACF;MACF,CAAC;MACDY,CAAC,EAAE;QACDH,IAAI,EAAE;UACJC,KAAK,EAAE;QACT,CAAC;QACDC,KAAK,EAAE;UACLZ,IAAI,EAAE;YACJC,IAAI,EAAE;UACR;QACF;MACF;IACF;EACF,CAAC;EAED,MAAMa,eAAe,GAAG;IACtBrB,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBxB,MAAM,EAAE;UACNyB,aAAa,EAAE,IAAI;UACnBC,OAAO,EAAE,EAAE;UACXC,IAAI,EAAE;YACJC,IAAI,EAAE;UACR;QACF;MACF,CAAC;MACDC,OAAO,EAAE;QACPxB,eAAe,EAAE,oBAAoB;QACrCyB,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,SAAS;QACpB3B,WAAW,EAAE,SAAS;QACtBK,WAAW,EAAE,CAAC;QACduB,YAAY,EAAE;MAChB;IACF,CAAC;IACDU,MAAM,EAAE;EACV,CAAC;EAED,IAAIzD,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKgE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BjE,OAAA;QAAKgE,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCrE,OAAA;QAAAiE,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEV;EAEA,oBACErE,OAAA;IAAKgE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBjE,OAAA;MAAKgE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCjE,OAAA;QAAIgE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDrE,OAAA;QAAQgE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eAENrE,OAAA;MAAKgE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BjE,OAAA;QAAKgE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjE,OAAA;UAAKgE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BjE,OAAA,CAACJ,aAAa;YAAC+D,KAAK,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,EAAKhC,cAAc,CAAC7B,aAAa,CAACK,SAAS,CAACC,UAAU;UAAC;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7DrE,OAAA;YAAAiE,QAAA,EAAG;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA;QAAKgE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjE,OAAA;UAAKgE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BjE,OAAA,CAACJ,aAAa;YAAC+D,KAAK,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,GAAK7D,aAAa,CAACK,SAAS,CAACE,aAAa,EAAC,GAAC;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDrE,OAAA;YAAAiE,QAAA,EAAG;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA;QAAKgE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjE,OAAA;UAAKgE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BjE,OAAA,CAACF,YAAY;YAAC6D,KAAK,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,EAAK7D,aAAa,CAACK,SAAS,CAACG;UAAkB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrDrE,OAAA;YAAAiE,QAAA,EAAG;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA;QAAKgE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjE,OAAA;UAAKgE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrCrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,EAAKhC,cAAc,CAAC7B,aAAa,CAACK,SAAS,CAACI,iBAAiB;UAAC;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpErE,OAAA;YAAAiE,QAAA,EAAG;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA;MAAKgE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1BjE,OAAA;QAAKgE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBjE,OAAA;UAAKgE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjE,OAAA;YAAAiE,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BrE,OAAA;YAAMgE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjE,OAAA,CAACR,IAAI;YAACgC,IAAI,EAAEpB,aAAa,CAACe,SAAS,CAACC,YAAa;YAACkD,OAAO,EAAE9B;UAAa;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAKgE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBjE,OAAA;UAAKgE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjE,OAAA;YAAAiE,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BrE,OAAA;YAAMgE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjE,OAAA,CAACN,QAAQ;YAAC8B,IAAI,EAAEpB,aAAa,CAACe,SAAS,CAACU,oBAAqB;YAACyC,OAAO,EAAER;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAKgE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBjE,OAAA;UAAKgE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjE,OAAA;YAAAiE,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBrE,OAAA;YAAMgE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjE,OAAA,CAACP,GAAG;YAAC+B,IAAI,EAAEpB,aAAa,CAACe,SAAS,CAACa,YAAa;YAACsC,OAAO,EAAE9B;UAAa;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAKgE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBjE,OAAA;UAAKgE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjE,OAAA;YAAAiE,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBrE,OAAA;YAAMgE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjE,OAAA,CAACL,GAAG;YAAC6B,IAAI,EAAEpB,aAAa,CAACe,SAAS,CAACY,eAAgB;YAACuC,OAAO,EAAER;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrE,OAAA;MAAKgE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBjE,OAAA;QAAIgE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDrE,OAAA;QAAKgE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCjE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,EAAM;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5BrE,OAAA;YAAAiE,QAAA,EAAO7D,aAAa,CAACU,aAAa,CAACC;UAAa;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,EAAM;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7BrE,OAAA;YAAMgE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAE7D,aAAa,CAACU,aAAa,CAACE;UAAa;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,EAAM;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BrE,OAAA;YAAMgE,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAAE7D,aAAa,CAACU,aAAa,CAACG;UAAe;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACNrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,EAAM;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnCrE,OAAA;YAAAiE,QAAA,EAAOhC,cAAc,CAAC7B,aAAa,CAACU,aAAa,CAACI,cAAc;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CA7RID,SAAS;AAAAqE,EAAA,GAATrE,SAAS;AA+Rf,eAAeA,SAAS;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}