{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nfunction GradientTexture({\n  stops,\n  colors,\n  size = 1024,\n  ...props\n}) {\n  const canvas = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    const context = canvas.getContext('2d');\n    canvas.width = 16;\n    canvas.height = size;\n    const gradient = context.createLinearGradient(0, 0, 0, size);\n    let i = stops.length;\n    while (i--) {\n      gradient.addColorStop(stops[i], colors[i]);\n    }\n    context.fillStyle = gradient;\n    context.fillRect(0, 0, 16, size);\n    return canvas;\n  }, [stops]); // @ts-ignore ????\n\n  return /*#__PURE__*/React.createElement(\"canvasTexture\", _extends({\n    args: [canvas],\n    attach: \"map\"\n  }, props));\n}\nexport { GradientTexture };", "map": {"version": 3, "names": ["_extends", "React", "GradientTexture", "stops", "colors", "size", "props", "canvas", "useMemo", "document", "createElement", "context", "getContext", "width", "height", "gradient", "createLinearGradient", "i", "length", "addColorStop", "fillStyle", "fillRect", "args", "attach"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/GradientTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\n\nfunction GradientTexture({\n  stops,\n  colors,\n  size = 1024,\n  ...props\n}) {\n  const canvas = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    const context = canvas.getContext('2d');\n    canvas.width = 16;\n    canvas.height = size;\n    const gradient = context.createLinearGradient(0, 0, 0, size);\n    let i = stops.length;\n\n    while (i--) {\n      gradient.addColorStop(stops[i], colors[i]);\n    }\n\n    context.fillStyle = gradient;\n    context.fillRect(0, 0, 16, size);\n    return canvas;\n  }, [stops]); // @ts-ignore ????\n\n  return /*#__PURE__*/React.createElement(\"canvasTexture\", _extends({\n    args: [canvas],\n    attach: \"map\"\n  }, props));\n}\n\nexport { GradientTexture };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,eAAeA,CAAC;EACvBC,KAAK;EACLC,MAAM;EACNC,IAAI,GAAG,IAAI;EACX,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGN,KAAK,CAACO,OAAO,CAAC,MAAM;IACjC,MAAMD,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,OAAO,GAAGJ,MAAM,CAACK,UAAU,CAAC,IAAI,CAAC;IACvCL,MAAM,CAACM,KAAK,GAAG,EAAE;IACjBN,MAAM,CAACO,MAAM,GAAGT,IAAI;IACpB,MAAMU,QAAQ,GAAGJ,OAAO,CAACK,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEX,IAAI,CAAC;IAC5D,IAAIY,CAAC,GAAGd,KAAK,CAACe,MAAM;IAEpB,OAAOD,CAAC,EAAE,EAAE;MACVF,QAAQ,CAACI,YAAY,CAAChB,KAAK,CAACc,CAAC,CAAC,EAAEb,MAAM,CAACa,CAAC,CAAC,CAAC;IAC5C;IAEAN,OAAO,CAACS,SAAS,GAAGL,QAAQ;IAC5BJ,OAAO,CAACU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAEhB,IAAI,CAAC;IAChC,OAAOE,MAAM;EACf,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEb,OAAO,aAAaF,KAAK,CAACS,aAAa,CAAC,eAAe,EAAEV,QAAQ,CAAC;IAChEsB,IAAI,EAAE,CAACf,MAAM,CAAC;IACdgB,MAAM,EAAE;EACV,CAAC,EAAEjB,KAAK,CAAC,CAAC;AACZ;AAEA,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}