{"ast": null, "code": "import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { SimplexNoise } from 'three-stdlib';\nconst CameraShake = /*#__PURE__*/React.forwardRef(({\n  intensity = 1,\n  decay,\n  decayRate = 0.65,\n  maxYaw = 0.1,\n  maxPitch = 0.1,\n  maxRoll = 0.1,\n  yawFrequency = 0.1,\n  pitchFrequency = 0.1,\n  rollFrequency = 0.1\n}, ref) => {\n  const camera = useThree(state => state.camera);\n  const defaultControls = useThree(state => state.controls);\n  const intensityRef = React.useRef(intensity);\n  const initialRotation = React.useRef(camera.rotation.clone());\n  const [yawNoise] = React.useState(() => new SimplexNoise());\n  const [pitchNoise] = React.useState(() => new SimplexNoise());\n  const [rollNoise] = React.useState(() => new SimplexNoise());\n  const constrainIntensity = () => {\n    if (intensityRef.current < 0 || intensityRef.current > 1) {\n      intensityRef.current = intensityRef.current < 0 ? 0 : 1;\n    }\n  };\n  React.useImperativeHandle(ref, () => ({\n    getIntensity: () => intensityRef.current,\n    setIntensity: val => {\n      intensityRef.current = val;\n      constrainIntensity();\n    }\n  }), []);\n  React.useEffect(() => {\n    if (defaultControls) {\n      const callback = () => void (initialRotation.current = camera.rotation.clone());\n      defaultControls.addEventListener('change', callback);\n      callback();\n      return () => void defaultControls.removeEventListener('change', callback);\n    }\n  }, [camera, defaultControls]);\n  useFrame((state, delta) => {\n    const shake = Math.pow(intensityRef.current, 2);\n    const yaw = maxYaw * shake * yawNoise.noise(state.clock.elapsedTime * yawFrequency, 1);\n    const pitch = maxPitch * shake * pitchNoise.noise(state.clock.elapsedTime * pitchFrequency, 1);\n    const roll = maxRoll * shake * rollNoise.noise(state.clock.elapsedTime * rollFrequency, 1);\n    camera.rotation.set(initialRotation.current.x + pitch, initialRotation.current.y + yaw, initialRotation.current.z + roll);\n    if (decay && intensityRef.current > 0) {\n      intensityRef.current -= decayRate * delta;\n      constrainIntensity();\n    }\n  });\n  return null;\n});\nexport { CameraShake };", "map": {"version": 3, "names": ["React", "useThree", "useFrame", "SimplexNoise", "CameraShake", "forwardRef", "intensity", "decay", "decayRate", "maxYaw", "max<PERSON><PERSON>", "maxRoll", "yawFrequency", "pitchFrequency", "rollFrequency", "ref", "camera", "state", "defaultControls", "controls", "intensityRef", "useRef", "initialRotation", "rotation", "clone", "yaw<PERSON><PERSON>", "useState", "pitchNoise", "rollN<PERSON>", "constrainIntensity", "current", "useImperativeHandle", "getIntensity", "setIntensity", "val", "useEffect", "callback", "addEventListener", "removeEventListener", "delta", "shake", "Math", "pow", "yaw", "noise", "clock", "elapsedTime", "pitch", "roll", "set", "x", "y", "z"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/CameraShake.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { SimplexNoise } from 'three-stdlib';\n\nconst CameraShake = /*#__PURE__*/React.forwardRef(({\n  intensity = 1,\n  decay,\n  decayRate = 0.65,\n  maxYaw = 0.1,\n  maxPitch = 0.1,\n  maxRoll = 0.1,\n  yawFrequency = 0.1,\n  pitchFrequency = 0.1,\n  rollFrequency = 0.1\n}, ref) => {\n  const camera = useThree(state => state.camera);\n  const defaultControls = useThree(state => state.controls);\n  const intensityRef = React.useRef(intensity);\n  const initialRotation = React.useRef(camera.rotation.clone());\n  const [yawNoise] = React.useState(() => new SimplexNoise());\n  const [pitchNoise] = React.useState(() => new SimplexNoise());\n  const [rollNoise] = React.useState(() => new SimplexNoise());\n\n  const constrainIntensity = () => {\n    if (intensityRef.current < 0 || intensityRef.current > 1) {\n      intensityRef.current = intensityRef.current < 0 ? 0 : 1;\n    }\n  };\n\n  React.useImperativeHandle(ref, () => ({\n    getIntensity: () => intensityRef.current,\n    setIntensity: val => {\n      intensityRef.current = val;\n      constrainIntensity();\n    }\n  }), []);\n  React.useEffect(() => {\n    if (defaultControls) {\n      const callback = () => void (initialRotation.current = camera.rotation.clone());\n\n      defaultControls.addEventListener('change', callback);\n      callback();\n      return () => void defaultControls.removeEventListener('change', callback);\n    }\n  }, [camera, defaultControls]);\n  useFrame((state, delta) => {\n    const shake = Math.pow(intensityRef.current, 2);\n    const yaw = maxYaw * shake * yawNoise.noise(state.clock.elapsedTime * yawFrequency, 1);\n    const pitch = maxPitch * shake * pitchNoise.noise(state.clock.elapsedTime * pitchFrequency, 1);\n    const roll = maxRoll * shake * rollNoise.noise(state.clock.elapsedTime * rollFrequency, 1);\n    camera.rotation.set(initialRotation.current.x + pitch, initialRotation.current.y + yaw, initialRotation.current.z + roll);\n\n    if (decay && intensityRef.current > 0) {\n      intensityRef.current -= decayRate * delta;\n      constrainIntensity();\n    }\n  });\n  return null;\n});\n\nexport { CameraShake };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,YAAY,QAAQ,cAAc;AAE3C,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACjDC,SAAS,GAAG,CAAC;EACbC,KAAK;EACLC,SAAS,GAAG,IAAI;EAChBC,MAAM,GAAG,GAAG;EACZC,QAAQ,GAAG,GAAG;EACdC,OAAO,GAAG,GAAG;EACbC,YAAY,GAAG,GAAG;EAClBC,cAAc,GAAG,GAAG;EACpBC,aAAa,GAAG;AAClB,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGf,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC;EAC9C,MAAME,eAAe,GAAGjB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACE,QAAQ,CAAC;EACzD,MAAMC,YAAY,GAAGpB,KAAK,CAACqB,MAAM,CAACf,SAAS,CAAC;EAC5C,MAAMgB,eAAe,GAAGtB,KAAK,CAACqB,MAAM,CAACL,MAAM,CAACO,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACC,QAAQ,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,MAAM,IAAIvB,YAAY,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACwB,UAAU,CAAC,GAAG3B,KAAK,CAAC0B,QAAQ,CAAC,MAAM,IAAIvB,YAAY,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACyB,SAAS,CAAC,GAAG5B,KAAK,CAAC0B,QAAQ,CAAC,MAAM,IAAIvB,YAAY,CAAC,CAAC,CAAC;EAE5D,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIT,YAAY,CAACU,OAAO,GAAG,CAAC,IAAIV,YAAY,CAACU,OAAO,GAAG,CAAC,EAAE;MACxDV,YAAY,CAACU,OAAO,GAAGV,YAAY,CAACU,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACzD;EACF,CAAC;EAED9B,KAAK,CAAC+B,mBAAmB,CAAChB,GAAG,EAAE,OAAO;IACpCiB,YAAY,EAAEA,CAAA,KAAMZ,YAAY,CAACU,OAAO;IACxCG,YAAY,EAAEC,GAAG,IAAI;MACnBd,YAAY,CAACU,OAAO,GAAGI,GAAG;MAC1BL,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP7B,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIjB,eAAe,EAAE;MACnB,MAAMkB,QAAQ,GAAGA,CAAA,KAAM,MAAMd,eAAe,CAACQ,OAAO,GAAGd,MAAM,CAACO,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC;MAE/EN,eAAe,CAACmB,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAAC;MACpDA,QAAQ,CAAC,CAAC;MACV,OAAO,MAAM,KAAKlB,eAAe,CAACoB,mBAAmB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC3E;EACF,CAAC,EAAE,CAACpB,MAAM,EAAEE,eAAe,CAAC,CAAC;EAC7BhB,QAAQ,CAAC,CAACe,KAAK,EAAEsB,KAAK,KAAK;IACzB,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACtB,YAAY,CAACU,OAAO,EAAE,CAAC,CAAC;IAC/C,MAAMa,GAAG,GAAGlC,MAAM,GAAG+B,KAAK,GAAGf,QAAQ,CAACmB,KAAK,CAAC3B,KAAK,CAAC4B,KAAK,CAACC,WAAW,GAAGlC,YAAY,EAAE,CAAC,CAAC;IACtF,MAAMmC,KAAK,GAAGrC,QAAQ,GAAG8B,KAAK,GAAGb,UAAU,CAACiB,KAAK,CAAC3B,KAAK,CAAC4B,KAAK,CAACC,WAAW,GAAGjC,cAAc,EAAE,CAAC,CAAC;IAC9F,MAAMmC,IAAI,GAAGrC,OAAO,GAAG6B,KAAK,GAAGZ,SAAS,CAACgB,KAAK,CAAC3B,KAAK,CAAC4B,KAAK,CAACC,WAAW,GAAGhC,aAAa,EAAE,CAAC,CAAC;IAC1FE,MAAM,CAACO,QAAQ,CAAC0B,GAAG,CAAC3B,eAAe,CAACQ,OAAO,CAACoB,CAAC,GAAGH,KAAK,EAAEzB,eAAe,CAACQ,OAAO,CAACqB,CAAC,GAAGR,GAAG,EAAErB,eAAe,CAACQ,OAAO,CAACsB,CAAC,GAAGJ,IAAI,CAAC;IAEzH,IAAIzC,KAAK,IAAIa,YAAY,CAACU,OAAO,GAAG,CAAC,EAAE;MACrCV,YAAY,CAACU,OAAO,IAAItB,SAAS,GAAG+B,KAAK;MACzCV,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC,CAAC;AAEF,SAASzB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}