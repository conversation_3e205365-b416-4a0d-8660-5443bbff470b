{"ast": null, "code": "import { ShaderPass } from \"./ShaderPass.js\";\nconst LUTShader = {\n  defines: {\n    USE_3DTEXTURE: 1\n  },\n  uniforms: {\n    lut3d: {\n      value: null\n    },\n    lut: {\n      value: null\n    },\n    lutSize: {\n      value: 0\n    },\n    tDiffuse: {\n      value: null\n    },\n    intensity: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n\n\t\tvarying vec2 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = uv;\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}\n\n\t`),\n  fragmentShader: (/* glsl */\n  `\n\n\t\tuniform float lutSize;\n\t\t#if USE_3DTEXTURE\n\t\tprecision highp sampler3D;\n\t\tuniform sampler3D lut3d;\n\t\t#else\n\t\tuniform sampler2D lut;\n\n\t\tvec3 lutLookup( sampler2D tex, float size, vec3 rgb ) {\n\n\t\t\tfloat sliceHeight = 1.0 / size;\n\t\t\tfloat yPixelHeight = 1.0 / ( size * size );\n\n\t\t\t// Get the slices on either side of the sample\n\t\t\tfloat slice = rgb.b * size;\n\t\t\tfloat interp = fract( slice );\n\t\t\tfloat slice0 = slice - interp;\n\t\t\tfloat centeredInterp = interp - 0.5;\n\n\t\t\tfloat slice1 = slice0 + sign( centeredInterp );\n\n\t\t\t// Pull y sample in by half a pixel in each direction to avoid color\n\t\t\t// bleeding from adjacent slices.\n\t\t\tfloat greenOffset = clamp( rgb.g * sliceHeight, yPixelHeight * 0.5, sliceHeight - yPixelHeight * 0.5 );\n\n\t\t\tvec2 uv0 = vec2(\n\t\t\t\trgb.r,\n\t\t\t\tslice0 * sliceHeight + greenOffset\n\t\t\t);\n\t\t\tvec2 uv1 = vec2(\n\t\t\t\trgb.r,\n\t\t\t\tslice1 * sliceHeight + greenOffset\n\t\t\t);\n\n\t\t\tvec3 sample0 = texture2D( tex, uv0 ).rgb;\n\t\t\tvec3 sample1 = texture2D( tex, uv1 ).rgb;\n\n\t\t\treturn mix( sample0, sample1, abs( centeredInterp ) );\n\n\t\t}\n\t\t#endif\n\n\t\tvarying vec2 vUv;\n\t\tuniform float intensity;\n\t\tuniform sampler2D tDiffuse;\n\t\tvoid main() {\n\n\t\t\tvec4 val = texture2D( tDiffuse, vUv );\n\t\t\tvec4 lutVal;\n\n\t\t\t// pull the sample in by half a pixel so the sample begins\n\t\t\t// at the center of the edge pixels.\n\t\t\tfloat pixelWidth = 1.0 / lutSize;\n\t\t\tfloat halfPixelWidth = 0.5 / lutSize;\n\t\t\tvec3 uvw = vec3( halfPixelWidth ) + val.rgb * ( 1.0 - pixelWidth );\n\n\t\t\t#if USE_3DTEXTURE\n\n\t\t\tlutVal = vec4( texture( lut3d, uvw ).rgb, val.a );\n\n\t\t\t#else\n\n\t\t\tlutVal = vec4( lutLookup( lut, lutSize, uvw ), val.a );\n\n\t\t\t#endif\n\n\t\t\tgl_FragColor = vec4( mix( val, lutVal, intensity ) );\n\n\t\t}\n\n\t`)\n};\nclass LUTPass extends ShaderPass {\n  set lut(v) {\n    const material = this.material;\n    if (v !== this.lut) {\n      material.uniforms.lut3d.value = null;\n      material.uniforms.lut.value = null;\n      if (v) {\n        const is3dTextureDefine = v.isData3DTexture ? 1 : 0;\n        if (is3dTextureDefine !== material.defines.USE_3DTEXTURE) {\n          material.defines.USE_3DTEXTURE = is3dTextureDefine;\n          material.needsUpdate = true;\n        }\n        material.uniforms.lutSize.value = v.image.width;\n        if (v.isData3DTexture) {\n          material.uniforms.lut3d.value = v;\n        } else {\n          material.uniforms.lut.value = v;\n        }\n      }\n    }\n  }\n  get lut() {\n    return this.material.uniforms.lut.value || this.material.uniforms.lut3d.value;\n  }\n  set intensity(v) {\n    this.material.uniforms.intensity.value = v;\n  }\n  get intensity() {\n    return this.material.uniforms.intensity.value;\n  }\n  constructor(options = {}) {\n    super(LUTShader);\n    this.lut = options.lut || null;\n    this.intensity = \"intensity\" in options ? options.intensity : 1;\n  }\n}\nexport { LUTPass };", "map": {"version": 3, "names": ["LUTShader", "defines", "USE_3DTEXTURE", "uniforms", "lut3d", "value", "lut", "lutSize", "tDiffuse", "intensity", "vertexShader", "fragmentShader", "LUTPass", "<PERSON><PERSON><PERSON><PERSON>", "v", "material", "is3dTextureDefine", "isData3DTexture", "needsUpdate", "image", "width", "constructor", "options"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\LUTPass.js"], "sourcesContent": ["import { ShaderPass } from './ShaderPass'\n\nconst LUTShader = {\n  defines: {\n    USE_3DTEXTURE: 1,\n  },\n\n  uniforms: {\n    lut3d: { value: null },\n\n    lut: { value: null },\n    lutSize: { value: 0 },\n\n    tDiffuse: { value: null },\n    intensity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n\n\t\tvarying vec2 vUv;\n\n\t\tvoid main() {\n\n\t\t\tvUv = uv;\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n\t\t}\n\n\t`,\n\n  fragmentShader: /* glsl */ `\n\n\t\tuniform float lutSize;\n\t\t#if USE_3DTEXTURE\n\t\tprecision highp sampler3D;\n\t\tuniform sampler3D lut3d;\n\t\t#else\n\t\tuniform sampler2D lut;\n\n\t\tvec3 lutLookup( sampler2D tex, float size, vec3 rgb ) {\n\n\t\t\tfloat sliceHeight = 1.0 / size;\n\t\t\tfloat yPixelHeight = 1.0 / ( size * size );\n\n\t\t\t// Get the slices on either side of the sample\n\t\t\tfloat slice = rgb.b * size;\n\t\t\tfloat interp = fract( slice );\n\t\t\tfloat slice0 = slice - interp;\n\t\t\tfloat centeredInterp = interp - 0.5;\n\n\t\t\tfloat slice1 = slice0 + sign( centeredInterp );\n\n\t\t\t// Pull y sample in by half a pixel in each direction to avoid color\n\t\t\t// bleeding from adjacent slices.\n\t\t\tfloat greenOffset = clamp( rgb.g * sliceHeight, yPixelHeight * 0.5, sliceHeight - yPixelHeight * 0.5 );\n\n\t\t\tvec2 uv0 = vec2(\n\t\t\t\trgb.r,\n\t\t\t\tslice0 * sliceHeight + greenOffset\n\t\t\t);\n\t\t\tvec2 uv1 = vec2(\n\t\t\t\trgb.r,\n\t\t\t\tslice1 * sliceHeight + greenOffset\n\t\t\t);\n\n\t\t\tvec3 sample0 = texture2D( tex, uv0 ).rgb;\n\t\t\tvec3 sample1 = texture2D( tex, uv1 ).rgb;\n\n\t\t\treturn mix( sample0, sample1, abs( centeredInterp ) );\n\n\t\t}\n\t\t#endif\n\n\t\tvarying vec2 vUv;\n\t\tuniform float intensity;\n\t\tuniform sampler2D tDiffuse;\n\t\tvoid main() {\n\n\t\t\tvec4 val = texture2D( tDiffuse, vUv );\n\t\t\tvec4 lutVal;\n\n\t\t\t// pull the sample in by half a pixel so the sample begins\n\t\t\t// at the center of the edge pixels.\n\t\t\tfloat pixelWidth = 1.0 / lutSize;\n\t\t\tfloat halfPixelWidth = 0.5 / lutSize;\n\t\t\tvec3 uvw = vec3( halfPixelWidth ) + val.rgb * ( 1.0 - pixelWidth );\n\n\t\t\t#if USE_3DTEXTURE\n\n\t\t\tlutVal = vec4( texture( lut3d, uvw ).rgb, val.a );\n\n\t\t\t#else\n\n\t\t\tlutVal = vec4( lutLookup( lut, lutSize, uvw ), val.a );\n\n\t\t\t#endif\n\n\t\t\tgl_FragColor = vec4( mix( val, lutVal, intensity ) );\n\n\t\t}\n\n\t`,\n}\n\nclass LUTPass extends ShaderPass {\n  set lut(v) {\n    const material = this.material\n    if (v !== this.lut) {\n      material.uniforms.lut3d.value = null\n      material.uniforms.lut.value = null\n\n      if (v) {\n        const is3dTextureDefine = v.isData3DTexture ? 1 : 0\n        if (is3dTextureDefine !== material.defines.USE_3DTEXTURE) {\n          material.defines.USE_3DTEXTURE = is3dTextureDefine\n          material.needsUpdate = true\n        }\n\n        material.uniforms.lutSize.value = v.image.width\n        if (v.isData3DTexture) {\n          material.uniforms.lut3d.value = v\n        } else {\n          material.uniforms.lut.value = v\n        }\n      }\n    }\n  }\n\n  get lut() {\n    return this.material.uniforms.lut.value || this.material.uniforms.lut3d.value\n  }\n\n  set intensity(v) {\n    this.material.uniforms.intensity.value = v\n  }\n\n  get intensity() {\n    return this.material.uniforms.intensity.value\n  }\n\n  constructor(options = {}) {\n    super(LUTShader)\n    this.lut = options.lut || null\n    this.intensity = 'intensity' in options ? options.intensity : 1\n  }\n}\n\nexport { LUTPass }\n"], "mappings": ";AAEA,MAAMA,SAAA,GAAY;EAChBC,OAAA,EAAS;IACPC,aAAA,EAAe;EAChB;EAEDC,QAAA,EAAU;IACRC,KAAA,EAAO;MAAEC,KAAA,EAAO;IAAM;IAEtBC,GAAA,EAAK;MAAED,KAAA,EAAO;IAAM;IACpBE,OAAA,EAAS;MAAEF,KAAA,EAAO;IAAG;IAErBG,QAAA,EAAU;MAAEH,KAAA,EAAO;IAAM;IACzBI,SAAA,EAAW;MAAEJ,KAAA,EAAO;IAAK;EAC1B;EAEDK,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAazBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwE7B;AAEA,MAAMC,OAAA,SAAgBC,UAAA,CAAW;EAC/B,IAAIP,IAAIQ,CAAA,EAAG;IACT,MAAMC,QAAA,GAAW,KAAKA,QAAA;IACtB,IAAID,CAAA,KAAM,KAAKR,GAAA,EAAK;MAClBS,QAAA,CAASZ,QAAA,CAASC,KAAA,CAAMC,KAAA,GAAQ;MAChCU,QAAA,CAASZ,QAAA,CAASG,GAAA,CAAID,KAAA,GAAQ;MAE9B,IAAIS,CAAA,EAAG;QACL,MAAME,iBAAA,GAAoBF,CAAA,CAAEG,eAAA,GAAkB,IAAI;QAClD,IAAID,iBAAA,KAAsBD,QAAA,CAASd,OAAA,CAAQC,aAAA,EAAe;UACxDa,QAAA,CAASd,OAAA,CAAQC,aAAA,GAAgBc,iBAAA;UACjCD,QAAA,CAASG,WAAA,GAAc;QACxB;QAEDH,QAAA,CAASZ,QAAA,CAASI,OAAA,CAAQF,KAAA,GAAQS,CAAA,CAAEK,KAAA,CAAMC,KAAA;QAC1C,IAAIN,CAAA,CAAEG,eAAA,EAAiB;UACrBF,QAAA,CAASZ,QAAA,CAASC,KAAA,CAAMC,KAAA,GAAQS,CAAA;QAC1C,OAAe;UACLC,QAAA,CAASZ,QAAA,CAASG,GAAA,CAAID,KAAA,GAAQS,CAAA;QAC/B;MACF;IACF;EACF;EAED,IAAIR,IAAA,EAAM;IACR,OAAO,KAAKS,QAAA,CAASZ,QAAA,CAASG,GAAA,CAAID,KAAA,IAAS,KAAKU,QAAA,CAASZ,QAAA,CAASC,KAAA,CAAMC,KAAA;EACzE;EAED,IAAII,UAAUK,CAAA,EAAG;IACf,KAAKC,QAAA,CAASZ,QAAA,CAASM,SAAA,CAAUJ,KAAA,GAAQS,CAAA;EAC1C;EAED,IAAIL,UAAA,EAAY;IACd,OAAO,KAAKM,QAAA,CAASZ,QAAA,CAASM,SAAA,CAAUJ,KAAA;EACzC;EAEDgB,YAAYC,OAAA,GAAU,IAAI;IACxB,MAAMtB,SAAS;IACf,KAAKM,GAAA,GAAMgB,OAAA,CAAQhB,GAAA,IAAO;IAC1B,KAAKG,SAAA,GAAY,eAAea,OAAA,GAAUA,OAAA,CAAQb,SAAA,GAAY;EAC/D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}