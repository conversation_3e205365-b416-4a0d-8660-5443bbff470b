{"ast": null, "code": "const MirrorShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    side: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    uniform int side;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 p = vUv;\n    \tif (side == 0){\n    \t\tif (p.x > 0.5) p.x = 1.0 - p.x;\n    \t}else if (side == 1){\n    \t\tif (p.x < 0.5) p.x = 1.0 - p.x;\n    \t}else if (side == 2){\n    \t\tif (p.y < 0.5) p.y = 1.0 - p.y;\n    \t}else if (side == 3){\n    \t\tif (p.y > 0.5) p.y = 1.0 - p.y;\n    \t} \n    \tvec4 color = texture2D(tDiffuse, p);\n    \tgl_FragColor = color;\n\n    }\n  `)\n};\nexport { MirrorShader };", "map": {"version": 3, "names": ["MirrorShader", "uniforms", "tDiffuse", "value", "side", "vertexShader", "fragmentShader"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\shaders\\MirrorShader.ts"], "sourcesContent": ["/**\n * Mirror Shader\n * Copies half the input to the other half\n *\n * side: side of input to mirror (0 = left, 1 = right, 2 = top, 3 = bottom)\n */\n\nexport const MirrorShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    side: { value: 1 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform int side;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 p = vUv;\n    \tif (side == 0){\n    \t\tif (p.x > 0.5) p.x = 1.0 - p.x;\n    \t}else if (side == 1){\n    \t\tif (p.x < 0.5) p.x = 1.0 - p.x;\n    \t}else if (side == 2){\n    \t\tif (p.y < 0.5) p.y = 1.0 - p.y;\n    \t}else if (side == 3){\n    \t\tif (p.y > 0.5) p.y = 1.0 - p.y;\n    \t} \n    \tvec4 color = texture2D(tDiffuse, p);\n    \tgl_FragColor = color;\n\n    }\n  `,\n}\n"], "mappings": "AAOO,MAAMA,YAAA,GAAe;EAC1BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,IAAA,EAAM;MAAED,KAAA,EAAO;IAAE;EACnB;EAEAE,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}