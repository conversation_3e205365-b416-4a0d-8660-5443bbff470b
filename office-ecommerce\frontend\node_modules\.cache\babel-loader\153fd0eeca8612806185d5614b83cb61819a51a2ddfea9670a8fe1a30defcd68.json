{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Quaternion, Vector3 } from \"three\";\nimport { EventDispatcher } from \"./EventDispatcher.js\";\nfunction contextmenu(event) {\n  event.preventDefault();\n}\nclass FlyControls extends EventDispatcher {\n  constructor(object, domElement) {\n    super();\n    __publicField(this, \"object\");\n    __publicField(this, \"domElement\", null);\n    __publicField(this, \"movementSpeed\", 1);\n    __publicField(this, \"rollSpeed\", 5e-3);\n    __publicField(this, \"dragToLook\", false);\n    __publicField(this, \"autoForward\", false);\n    __publicField(this, \"changeEvent\", {\n      type: \"change\"\n    });\n    __publicField(this, \"EPS\", 1e-6);\n    __publicField(this, \"tmpQuaternion\", new Quaternion());\n    __publicField(this, \"mouseStatus\", 0);\n    __publicField(this, \"movementSpeedMultiplier\", 1);\n    __publicField(this, \"moveState\", {\n      up: 0,\n      down: 0,\n      left: 0,\n      right: 0,\n      forward: 0,\n      back: 0,\n      pitchUp: 0,\n      pitchDown: 0,\n      yawLeft: 0,\n      yawRight: 0,\n      rollLeft: 0,\n      rollRight: 0\n    });\n    __publicField(this, \"moveVector\", new Vector3(0, 0, 0));\n    __publicField(this, \"rotationVector\", new Vector3(0, 0, 0));\n    __publicField(this, \"keydown\", event => {\n      if (event.altKey) {\n        return;\n      }\n      switch (event.code) {\n        case \"ShiftLeft\":\n        case \"ShiftRight\":\n          this.movementSpeedMultiplier = 0.1;\n          break;\n        case \"KeyW\":\n          this.moveState.forward = 1;\n          break;\n        case \"KeyS\":\n          this.moveState.back = 1;\n          break;\n        case \"KeyA\":\n          this.moveState.left = 1;\n          break;\n        case \"KeyD\":\n          this.moveState.right = 1;\n          break;\n        case \"KeyR\":\n          this.moveState.up = 1;\n          break;\n        case \"KeyF\":\n          this.moveState.down = 1;\n          break;\n        case \"ArrowUp\":\n          this.moveState.pitchUp = 1;\n          break;\n        case \"ArrowDown\":\n          this.moveState.pitchDown = 1;\n          break;\n        case \"ArrowLeft\":\n          this.moveState.yawLeft = 1;\n          break;\n        case \"ArrowRight\":\n          this.moveState.yawRight = 1;\n          break;\n        case \"KeyQ\":\n          this.moveState.rollLeft = 1;\n          break;\n        case \"KeyE\":\n          this.moveState.rollRight = 1;\n          break;\n      }\n      this.updateMovementVector();\n      this.updateRotationVector();\n    });\n    __publicField(this, \"keyup\", event => {\n      switch (event.code) {\n        case \"ShiftLeft\":\n        case \"ShiftRight\":\n          this.movementSpeedMultiplier = 1;\n          break;\n        case \"KeyW\":\n          this.moveState.forward = 0;\n          break;\n        case \"KeyS\":\n          this.moveState.back = 0;\n          break;\n        case \"KeyA\":\n          this.moveState.left = 0;\n          break;\n        case \"KeyD\":\n          this.moveState.right = 0;\n          break;\n        case \"KeyR\":\n          this.moveState.up = 0;\n          break;\n        case \"KeyF\":\n          this.moveState.down = 0;\n          break;\n        case \"ArrowUp\":\n          this.moveState.pitchUp = 0;\n          break;\n        case \"ArrowDown\":\n          this.moveState.pitchDown = 0;\n          break;\n        case \"ArrowLeft\":\n          this.moveState.yawLeft = 0;\n          break;\n        case \"ArrowRight\":\n          this.moveState.yawRight = 0;\n          break;\n        case \"KeyQ\":\n          this.moveState.rollLeft = 0;\n          break;\n        case \"KeyE\":\n          this.moveState.rollRight = 0;\n          break;\n      }\n      this.updateMovementVector();\n      this.updateRotationVector();\n    });\n    __publicField(this, \"pointerdown\", event => {\n      if (this.dragToLook) {\n        this.mouseStatus++;\n      } else {\n        switch (event.button) {\n          case 0:\n            this.moveState.forward = 1;\n            break;\n          case 2:\n            this.moveState.back = 1;\n            break;\n        }\n        this.updateMovementVector();\n      }\n    });\n    __publicField(this, \"pointermove\", event => {\n      if (!this.dragToLook || this.mouseStatus > 0) {\n        const container = this.getContainerDimensions();\n        const halfWidth = container.size[0] / 2;\n        const halfHeight = container.size[1] / 2;\n        this.moveState.yawLeft = -(event.pageX - container.offset[0] - halfWidth) / halfWidth;\n        this.moveState.pitchDown = (event.pageY - container.offset[1] - halfHeight) / halfHeight;\n        this.updateRotationVector();\n      }\n    });\n    __publicField(this, \"pointerup\", event => {\n      if (this.dragToLook) {\n        this.mouseStatus--;\n        this.moveState.yawLeft = this.moveState.pitchDown = 0;\n      } else {\n        switch (event.button) {\n          case 0:\n            this.moveState.forward = 0;\n            break;\n          case 2:\n            this.moveState.back = 0;\n            break;\n        }\n        this.updateMovementVector();\n      }\n      this.updateRotationVector();\n    });\n    __publicField(this, \"lastQuaternion\", new Quaternion());\n    __publicField(this, \"lastPosition\", new Vector3());\n    __publicField(this, \"update\", delta => {\n      const moveMult = delta * this.movementSpeed;\n      const rotMult = delta * this.rollSpeed;\n      this.object.translateX(this.moveVector.x * moveMult);\n      this.object.translateY(this.moveVector.y * moveMult);\n      this.object.translateZ(this.moveVector.z * moveMult);\n      this.tmpQuaternion.set(this.rotationVector.x * rotMult, this.rotationVector.y * rotMult, this.rotationVector.z * rotMult, 1).normalize();\n      this.object.quaternion.multiply(this.tmpQuaternion);\n      if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS || 8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS) {\n        this.dispatchEvent(this.changeEvent);\n        this.lastQuaternion.copy(this.object.quaternion);\n        this.lastPosition.copy(this.object.position);\n      }\n    });\n    __publicField(this, \"updateMovementVector\", () => {\n      const forward = this.moveState.forward || this.autoForward && !this.moveState.back ? 1 : 0;\n      this.moveVector.x = -this.moveState.left + this.moveState.right;\n      this.moveVector.y = -this.moveState.down + this.moveState.up;\n      this.moveVector.z = -forward + this.moveState.back;\n    });\n    __publicField(this, \"updateRotationVector\", () => {\n      this.rotationVector.x = -this.moveState.pitchDown + this.moveState.pitchUp;\n      this.rotationVector.y = -this.moveState.yawRight + this.moveState.yawLeft;\n      this.rotationVector.z = -this.moveState.rollRight + this.moveState.rollLeft;\n    });\n    __publicField(this, \"getContainerDimensions\", () => {\n      if (this.domElement != document && !(this.domElement instanceof Document)) {\n        return {\n          size: [this.domElement.offsetWidth, this.domElement.offsetHeight],\n          offset: [this.domElement.offsetLeft, this.domElement.offsetTop]\n        };\n      } else {\n        return {\n          size: [window.innerWidth, window.innerHeight],\n          offset: [0, 0]\n        };\n      }\n    });\n    // https://github.com/mrdoob/three.js/issues/20575\n    __publicField(this, \"connect\", domElement => {\n      this.domElement = domElement;\n      if (!(domElement instanceof Document)) {\n        domElement.setAttribute(\"tabindex\", -1);\n      }\n      this.domElement.addEventListener(\"contextmenu\", contextmenu);\n      this.domElement.addEventListener(\"pointermove\", this.pointermove);\n      this.domElement.addEventListener(\"pointerdown\", this.pointerdown);\n      this.domElement.addEventListener(\"pointerup\", this.pointerup);\n      window.addEventListener(\"keydown\", this.keydown);\n      window.addEventListener(\"keyup\", this.keyup);\n    });\n    __publicField(this, \"dispose\", () => {\n      this.domElement.removeEventListener(\"contextmenu\", contextmenu);\n      this.domElement.removeEventListener(\"pointermove\", this.pointermove);\n      this.domElement.removeEventListener(\"pointerdown\", this.pointerdown);\n      this.domElement.removeEventListener(\"pointerup\", this.pointerup);\n      window.removeEventListener(\"keydown\", this.keydown);\n      window.removeEventListener(\"keyup\", this.keyup);\n    });\n    this.object = object;\n    if (domElement !== void 0) this.connect(domElement);\n    this.updateMovementVector();\n    this.updateRotationVector();\n  }\n}\nexport { FlyControls };", "map": {"version": 3, "names": ["contextmenu", "event", "preventDefault", "FlyControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "object", "dom<PERSON>lement", "__publicField", "type", "Quaternion", "up", "down", "left", "right", "forward", "back", "pitchUp", "pitchDown", "yawLeft", "yawRight", "rollLeft", "rollRight", "Vector3", "altKey", "code", "movementSpeedMultiplier", "moveState", "updateMovementVector", "updateRotationVector", "dragToLook", "mouseStatus", "button", "container", "getContainerDimensions", "halfWidth", "size", "halfHeight", "pageX", "offset", "pageY", "delta", "moveMult", "movementSpeed", "rotMult", "rollSpeed", "translateX", "moveVector", "x", "translateY", "y", "translateZ", "z", "tmpQuaternion", "set", "rotationVector", "normalize", "quaternion", "multiply", "lastPosition", "distanceToSquared", "position", "EPS", "lastQuaternion", "dot", "dispatchEvent", "changeEvent", "copy", "autoForward", "document", "Document", "offsetWidth", "offsetHeight", "offsetLeft", "offsetTop", "window", "innerWidth", "innerHeight", "setAttribute", "addEventListener", "pointermove", "pointerdown", "pointerup", "keydown", "keyup", "removeEventListener", "connect"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\controls\\FlyControls.ts"], "sourcesContent": ["import { Camera, Quaternion, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\n\nfunction contextmenu(event: Event): void {\n  event.preventDefault()\n}\n\nexport interface FlyControlsEventMap {\n  /**\n   * Fires when the camera has been transformed by the controls.\n   */\n  change: {};\n}\n\nclass FlyControls extends EventDispatcher<FlyControlsEventMap> {\n  public object: Camera\n  public domElement: HTMLElement | Document = null!\n\n  public movementSpeed = 1.0\n  public rollSpeed = 0.005\n\n  public dragToLook = false\n  public autoForward = false\n\n  private changeEvent = { type: 'change' }\n  private EPS = 0.000001\n\n  private tmpQuaternion = new Quaternion()\n\n  private mouseStatus = 0\n\n  private movementSpeedMultiplier = 1\n\n  private moveState = {\n    up: 0,\n    down: 0,\n    left: 0,\n    right: 0,\n    forward: 0,\n    back: 0,\n    pitchUp: 0,\n    pitchDown: 0,\n    yawLeft: 0,\n    yawRight: 0,\n    rollLeft: 0,\n    rollRight: 0,\n  }\n  private moveVector = new Vector3(0, 0, 0)\n  private rotationVector = new Vector3(0, 0, 0)\n\n  constructor(object: Camera, domElement?: HTMLElement | Document) {\n    super()\n\n    this.object = object\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n\n    this.updateMovementVector()\n    this.updateRotationVector()\n  }\n\n  private keydown = (event: KeyboardEvent): void => {\n    if (event.altKey) {\n      return\n    }\n\n    switch (event.code) {\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        this.movementSpeedMultiplier = 0.1\n        break\n\n      case 'KeyW':\n        this.moveState.forward = 1\n        break\n      case 'KeyS':\n        this.moveState.back = 1\n        break\n\n      case 'KeyA':\n        this.moveState.left = 1\n        break\n      case 'KeyD':\n        this.moveState.right = 1\n        break\n\n      case 'KeyR':\n        this.moveState.up = 1\n        break\n      case 'KeyF':\n        this.moveState.down = 1\n        break\n\n      case 'ArrowUp':\n        this.moveState.pitchUp = 1\n        break\n      case 'ArrowDown':\n        this.moveState.pitchDown = 1\n        break\n\n      case 'ArrowLeft':\n        this.moveState.yawLeft = 1\n        break\n      case 'ArrowRight':\n        this.moveState.yawRight = 1\n        break\n\n      case 'KeyQ':\n        this.moveState.rollLeft = 1\n        break\n      case 'KeyE':\n        this.moveState.rollRight = 1\n        break\n    }\n\n    this.updateMovementVector()\n    this.updateRotationVector()\n  }\n\n  private keyup = (event: KeyboardEvent): void => {\n    switch (event.code) {\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        this.movementSpeedMultiplier = 1\n        break\n\n      case 'KeyW':\n        this.moveState.forward = 0\n        break\n      case 'KeyS':\n        this.moveState.back = 0\n        break\n\n      case 'KeyA':\n        this.moveState.left = 0\n        break\n      case 'KeyD':\n        this.moveState.right = 0\n        break\n\n      case 'KeyR':\n        this.moveState.up = 0\n        break\n      case 'KeyF':\n        this.moveState.down = 0\n        break\n\n      case 'ArrowUp':\n        this.moveState.pitchUp = 0\n        break\n      case 'ArrowDown':\n        this.moveState.pitchDown = 0\n        break\n\n      case 'ArrowLeft':\n        this.moveState.yawLeft = 0\n        break\n      case 'ArrowRight':\n        this.moveState.yawRight = 0\n        break\n\n      case 'KeyQ':\n        this.moveState.rollLeft = 0\n        break\n      case 'KeyE':\n        this.moveState.rollRight = 0\n        break\n    }\n\n    this.updateMovementVector()\n    this.updateRotationVector()\n  }\n\n  private pointerdown = (event: MouseEvent): void => {\n    if (this.dragToLook) {\n      this.mouseStatus++\n    } else {\n      switch (event.button) {\n        case 0:\n          this.moveState.forward = 1\n          break\n        case 2:\n          this.moveState.back = 1\n          break\n      }\n\n      this.updateMovementVector()\n    }\n  }\n\n  private pointermove = (event: MouseEvent): void => {\n    if (!this.dragToLook || this.mouseStatus > 0) {\n      const container = this.getContainerDimensions()\n      const halfWidth = container.size[0] / 2\n      const halfHeight = container.size[1] / 2\n\n      this.moveState.yawLeft = -(event.pageX - container.offset[0] - halfWidth) / halfWidth\n      this.moveState.pitchDown = (event.pageY - container.offset[1] - halfHeight) / halfHeight\n\n      this.updateRotationVector()\n    }\n  }\n\n  private pointerup = (event: MouseEvent): void => {\n    if (this.dragToLook) {\n      this.mouseStatus--\n\n      this.moveState.yawLeft = this.moveState.pitchDown = 0\n    } else {\n      switch (event.button) {\n        case 0:\n          this.moveState.forward = 0\n          break\n        case 2:\n          this.moveState.back = 0\n          break\n      }\n\n      this.updateMovementVector()\n    }\n\n    this.updateRotationVector()\n  }\n\n  private lastQuaternion = new Quaternion()\n  private lastPosition = new Vector3()\n\n  public update = (delta: number): void => {\n    const moveMult = delta * this.movementSpeed\n    const rotMult = delta * this.rollSpeed\n\n    this.object.translateX(this.moveVector.x * moveMult)\n    this.object.translateY(this.moveVector.y * moveMult)\n    this.object.translateZ(this.moveVector.z * moveMult)\n\n    this.tmpQuaternion\n      .set(this.rotationVector.x * rotMult, this.rotationVector.y * rotMult, this.rotationVector.z * rotMult, 1)\n      .normalize()\n    this.object.quaternion.multiply(this.tmpQuaternion)\n\n    if (\n      this.lastPosition.distanceToSquared(this.object.position) > this.EPS ||\n      8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS\n    ) {\n      // @ts-ignore\n      this.dispatchEvent(this.changeEvent)\n      this.lastQuaternion.copy(this.object.quaternion)\n      this.lastPosition.copy(this.object.position)\n    }\n  }\n\n  private updateMovementVector = (): void => {\n    const forward = this.moveState.forward || (this.autoForward && !this.moveState.back) ? 1 : 0\n\n    this.moveVector.x = -this.moveState.left + this.moveState.right\n    this.moveVector.y = -this.moveState.down + this.moveState.up\n    this.moveVector.z = -forward + this.moveState.back\n  }\n\n  private updateRotationVector = (): void => {\n    this.rotationVector.x = -this.moveState.pitchDown + this.moveState.pitchUp\n    this.rotationVector.y = -this.moveState.yawRight + this.moveState.yawLeft\n    this.rotationVector.z = -this.moveState.rollRight + this.moveState.rollLeft\n  }\n\n  private getContainerDimensions = (): {\n    size: number[]\n    offset: number[]\n  } => {\n    if (this.domElement != document && !(this.domElement instanceof Document)) {\n      return {\n        size: [this.domElement.offsetWidth, this.domElement.offsetHeight],\n        offset: [this.domElement.offsetLeft, this.domElement.offsetTop],\n      }\n    } else {\n      return {\n        size: [window.innerWidth, window.innerHeight],\n        offset: [0, 0],\n      }\n    }\n  }\n\n  // https://github.com/mrdoob/three.js/issues/20575\n  public connect = (domElement: HTMLElement | Document): void => {\n    this.domElement = domElement\n\n    if (!(domElement instanceof Document)) {\n      domElement.setAttribute('tabindex', -1 as any)\n    }\n\n    this.domElement.addEventListener('contextmenu', contextmenu)\n    ;(this.domElement as HTMLElement).addEventListener('pointermove', this.pointermove)\n    ;(this.domElement as HTMLElement).addEventListener('pointerdown', this.pointerdown)\n    ;(this.domElement as HTMLElement).addEventListener('pointerup', this.pointerup)\n\n    window.addEventListener('keydown', this.keydown)\n    window.addEventListener('keyup', this.keyup)\n  }\n\n  public dispose = (): void => {\n    this.domElement.removeEventListener('contextmenu', contextmenu)\n    ;(this.domElement as HTMLElement).removeEventListener('pointermove', this.pointermove)\n    ;(this.domElement as HTMLElement).removeEventListener('pointerdown', this.pointerdown)\n    ;(this.domElement as HTMLElement).removeEventListener('pointerup', this.pointerup)\n\n    window.removeEventListener('keydown', this.keydown)\n    window.removeEventListener('keyup', this.keyup)\n  }\n}\n\nexport { FlyControls }\n"], "mappings": ";;;;;;;;;;;;;AAGA,SAASA,YAAYC,KAAA,EAAoB;EACvCA,KAAA,CAAMC,cAAA,CAAe;AACvB;AASA,MAAMC,WAAA,SAAoBC,eAAA,CAAqC;EAoC7DC,YAAYC,MAAA,EAAgBC,UAAA,EAAqC;IACzD;IApCDC,aAAA;IACAA,aAAA,qBAAqC;IAErCA,aAAA,wBAAgB;IAChBA,aAAA,oBAAY;IAEZA,aAAA,qBAAa;IACbA,aAAA,sBAAc;IAEbA,aAAA,sBAAc;MAAEC,IAAA,EAAM;IAAA;IACtBD,aAAA,cAAM;IAENA,aAAA,wBAAgB,IAAIE,UAAA;IAEpBF,aAAA,sBAAc;IAEdA,aAAA,kCAA0B;IAE1BA,aAAA,oBAAY;MAClBG,EAAA,EAAI;MACJC,IAAA,EAAM;MACNC,IAAA,EAAM;MACNC,KAAA,EAAO;MACPC,OAAA,EAAS;MACTC,IAAA,EAAM;MACNC,OAAA,EAAS;MACTC,SAAA,EAAW;MACXC,OAAA,EAAS;MACTC,QAAA,EAAU;MACVC,QAAA,EAAU;MACVC,SAAA,EAAW;IAAA;IAELd,aAAA,qBAAa,IAAIe,OAAA,CAAQ,GAAG,GAAG,CAAC;IAChCf,aAAA,yBAAiB,IAAIe,OAAA,CAAQ,GAAG,GAAG,CAAC;IAcpCf,aAAA,kBAAWP,KAAA,IAA+B;MAChD,IAAIA,KAAA,CAAMuB,MAAA,EAAQ;QAChB;MACF;MAEA,QAAQvB,KAAA,CAAMwB,IAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKC,uBAAA,GAA0B;UAC/B;QAEF,KAAK;UACH,KAAKC,SAAA,CAAUZ,OAAA,GAAU;UACzB;QACF,KAAK;UACH,KAAKY,SAAA,CAAUX,IAAA,GAAO;UACtB;QAEF,KAAK;UACH,KAAKW,SAAA,CAAUd,IAAA,GAAO;UACtB;QACF,KAAK;UACH,KAAKc,SAAA,CAAUb,KAAA,GAAQ;UACvB;QAEF,KAAK;UACH,KAAKa,SAAA,CAAUhB,EAAA,GAAK;UACpB;QACF,KAAK;UACH,KAAKgB,SAAA,CAAUf,IAAA,GAAO;UACtB;QAEF,KAAK;UACH,KAAKe,SAAA,CAAUV,OAAA,GAAU;UACzB;QACF,KAAK;UACH,KAAKU,SAAA,CAAUT,SAAA,GAAY;UAC3B;QAEF,KAAK;UACH,KAAKS,SAAA,CAAUR,OAAA,GAAU;UACzB;QACF,KAAK;UACH,KAAKQ,SAAA,CAAUP,QAAA,GAAW;UAC1B;QAEF,KAAK;UACH,KAAKO,SAAA,CAAUN,QAAA,GAAW;UAC1B;QACF,KAAK;UACH,KAAKM,SAAA,CAAUL,SAAA,GAAY;UAC3B;MACJ;MAEA,KAAKM,oBAAA,CAAqB;MAC1B,KAAKC,oBAAA,CAAqB;IAAA;IAGpBrB,aAAA,gBAASP,KAAA,IAA+B;MAC9C,QAAQA,KAAA,CAAMwB,IAAA;QACZ,KAAK;QACL,KAAK;UACH,KAAKC,uBAAA,GAA0B;UAC/B;QAEF,KAAK;UACH,KAAKC,SAAA,CAAUZ,OAAA,GAAU;UACzB;QACF,KAAK;UACH,KAAKY,SAAA,CAAUX,IAAA,GAAO;UACtB;QAEF,KAAK;UACH,KAAKW,SAAA,CAAUd,IAAA,GAAO;UACtB;QACF,KAAK;UACH,KAAKc,SAAA,CAAUb,KAAA,GAAQ;UACvB;QAEF,KAAK;UACH,KAAKa,SAAA,CAAUhB,EAAA,GAAK;UACpB;QACF,KAAK;UACH,KAAKgB,SAAA,CAAUf,IAAA,GAAO;UACtB;QAEF,KAAK;UACH,KAAKe,SAAA,CAAUV,OAAA,GAAU;UACzB;QACF,KAAK;UACH,KAAKU,SAAA,CAAUT,SAAA,GAAY;UAC3B;QAEF,KAAK;UACH,KAAKS,SAAA,CAAUR,OAAA,GAAU;UACzB;QACF,KAAK;UACH,KAAKQ,SAAA,CAAUP,QAAA,GAAW;UAC1B;QAEF,KAAK;UACH,KAAKO,SAAA,CAAUN,QAAA,GAAW;UAC1B;QACF,KAAK;UACH,KAAKM,SAAA,CAAUL,SAAA,GAAY;UAC3B;MACJ;MAEA,KAAKM,oBAAA,CAAqB;MAC1B,KAAKC,oBAAA,CAAqB;IAAA;IAGpBrB,aAAA,sBAAeP,KAAA,IAA4B;MACjD,IAAI,KAAK6B,UAAA,EAAY;QACd,KAAAC,WAAA;MAAA,OACA;QACL,QAAQ9B,KAAA,CAAM+B,MAAA;UACZ,KAAK;YACH,KAAKL,SAAA,CAAUZ,OAAA,GAAU;YACzB;UACF,KAAK;YACH,KAAKY,SAAA,CAAUX,IAAA,GAAO;YACtB;QACJ;QAEA,KAAKY,oBAAA,CAAqB;MAC5B;IAAA;IAGMpB,aAAA,sBAAeP,KAAA,IAA4B;MACjD,IAAI,CAAC,KAAK6B,UAAA,IAAc,KAAKC,WAAA,GAAc,GAAG;QACtC,MAAAE,SAAA,GAAY,KAAKC,sBAAA;QACvB,MAAMC,SAAA,GAAYF,SAAA,CAAUG,IAAA,CAAK,CAAC,IAAI;QACtC,MAAMC,UAAA,GAAaJ,SAAA,CAAUG,IAAA,CAAK,CAAC,IAAI;QAElC,KAAAT,SAAA,CAAUR,OAAA,GAAU,EAAElB,KAAA,CAAMqC,KAAA,GAAQL,SAAA,CAAUM,MAAA,CAAO,CAAC,IAAIJ,SAAA,IAAaA,SAAA;QACvE,KAAAR,SAAA,CAAUT,SAAA,IAAajB,KAAA,CAAMuC,KAAA,GAAQP,SAAA,CAAUM,MAAA,CAAO,CAAC,IAAIF,UAAA,IAAcA,UAAA;QAE9E,KAAKR,oBAAA,CAAqB;MAC5B;IAAA;IAGMrB,aAAA,oBAAaP,KAAA,IAA4B;MAC/C,IAAI,KAAK6B,UAAA,EAAY;QACd,KAAAC,WAAA;QAEL,KAAKJ,SAAA,CAAUR,OAAA,GAAU,KAAKQ,SAAA,CAAUT,SAAA,GAAY;MAAA,OAC/C;QACL,QAAQjB,KAAA,CAAM+B,MAAA;UACZ,KAAK;YACH,KAAKL,SAAA,CAAUZ,OAAA,GAAU;YACzB;UACF,KAAK;YACH,KAAKY,SAAA,CAAUX,IAAA,GAAO;YACtB;QACJ;QAEA,KAAKY,oBAAA,CAAqB;MAC5B;MAEA,KAAKC,oBAAA,CAAqB;IAAA;IAGpBrB,aAAA,yBAAiB,IAAIE,UAAA;IACrBF,aAAA,uBAAe,IAAIe,OAAA;IAEpBf,aAAA,iBAAUiC,KAAA,IAAwB;MACjC,MAAAC,QAAA,GAAWD,KAAA,GAAQ,KAAKE,aAAA;MACxB,MAAAC,OAAA,GAAUH,KAAA,GAAQ,KAAKI,SAAA;MAE7B,KAAKvC,MAAA,CAAOwC,UAAA,CAAW,KAAKC,UAAA,CAAWC,CAAA,GAAIN,QAAQ;MACnD,KAAKpC,MAAA,CAAO2C,UAAA,CAAW,KAAKF,UAAA,CAAWG,CAAA,GAAIR,QAAQ;MACnD,KAAKpC,MAAA,CAAO6C,UAAA,CAAW,KAAKJ,UAAA,CAAWK,CAAA,GAAIV,QAAQ;MAEnD,KAAKW,aAAA,CACFC,GAAA,CAAI,KAAKC,cAAA,CAAeP,CAAA,GAAIJ,OAAA,EAAS,KAAKW,cAAA,CAAeL,CAAA,GAAIN,OAAA,EAAS,KAAKW,cAAA,CAAeH,CAAA,GAAIR,OAAA,EAAS,CAAC,EACxGY,SAAA;MACH,KAAKlD,MAAA,CAAOmD,UAAA,CAAWC,QAAA,CAAS,KAAKL,aAAa;MAElD,IACE,KAAKM,YAAA,CAAaC,iBAAA,CAAkB,KAAKtD,MAAA,CAAOuD,QAAQ,IAAI,KAAKC,GAAA,IACjE,KAAK,IAAI,KAAKC,cAAA,CAAeC,GAAA,CAAI,KAAK1D,MAAA,CAAOmD,UAAU,KAAK,KAAKK,GAAA,EACjE;QAEK,KAAAG,aAAA,CAAc,KAAKC,WAAW;QACnC,KAAKH,cAAA,CAAeI,IAAA,CAAK,KAAK7D,MAAA,CAAOmD,UAAU;QAC/C,KAAKE,YAAA,CAAaQ,IAAA,CAAK,KAAK7D,MAAA,CAAOuD,QAAQ;MAC7C;IAAA;IAGMrD,aAAA,+BAAuB,MAAY;MACnC,MAAAO,OAAA,GAAU,KAAKY,SAAA,CAAUZ,OAAA,IAAY,KAAKqD,WAAA,IAAe,CAAC,KAAKzC,SAAA,CAAUX,IAAA,GAAQ,IAAI;MAE3F,KAAK+B,UAAA,CAAWC,CAAA,GAAI,CAAC,KAAKrB,SAAA,CAAUd,IAAA,GAAO,KAAKc,SAAA,CAAUb,KAAA;MAC1D,KAAKiC,UAAA,CAAWG,CAAA,GAAI,CAAC,KAAKvB,SAAA,CAAUf,IAAA,GAAO,KAAKe,SAAA,CAAUhB,EAAA;MAC1D,KAAKoC,UAAA,CAAWK,CAAA,GAAI,CAACrC,OAAA,GAAU,KAAKY,SAAA,CAAUX,IAAA;IAAA;IAGxCR,aAAA,+BAAuB,MAAY;MACzC,KAAK+C,cAAA,CAAeP,CAAA,GAAI,CAAC,KAAKrB,SAAA,CAAUT,SAAA,GAAY,KAAKS,SAAA,CAAUV,OAAA;MACnE,KAAKsC,cAAA,CAAeL,CAAA,GAAI,CAAC,KAAKvB,SAAA,CAAUP,QAAA,GAAW,KAAKO,SAAA,CAAUR,OAAA;MAClE,KAAKoC,cAAA,CAAeH,CAAA,GAAI,CAAC,KAAKzB,SAAA,CAAUL,SAAA,GAAY,KAAKK,SAAA,CAAUN,QAAA;IAAA;IAG7Db,aAAA,iCAAyB,MAG5B;MACH,IAAI,KAAKD,UAAA,IAAc8D,QAAA,IAAY,EAAE,KAAK9D,UAAA,YAAsB+D,QAAA,GAAW;QAClE;UACLlC,IAAA,EAAM,CAAC,KAAK7B,UAAA,CAAWgE,WAAA,EAAa,KAAKhE,UAAA,CAAWiE,YAAY;UAChEjC,MAAA,EAAQ,CAAC,KAAKhC,UAAA,CAAWkE,UAAA,EAAY,KAAKlE,UAAA,CAAWmE,SAAS;QAAA;MAChE,OACK;QACE;UACLtC,IAAA,EAAM,CAACuC,MAAA,CAAOC,UAAA,EAAYD,MAAA,CAAOE,WAAW;UAC5CtC,MAAA,EAAQ,CAAC,GAAG,CAAC;QAAA;MAEjB;IAAA;IAIK;IAAA/B,aAAA,kBAAWD,UAAA,IAA6C;MAC7D,KAAKA,UAAA,GAAaA,UAAA;MAEd,MAAEA,UAAA,YAAsB+D,QAAA,GAAW;QAC1B/D,UAAA,CAAAuE,YAAA,CAAa,YAAY,EAAS;MAC/C;MAEK,KAAAvE,UAAA,CAAWwE,gBAAA,CAAiB,eAAe/E,WAAW;MACzD,KAAKO,UAAA,CAA2BwE,gBAAA,CAAiB,eAAe,KAAKC,WAAW;MAChF,KAAKzE,UAAA,CAA2BwE,gBAAA,CAAiB,eAAe,KAAKE,WAAW;MAChF,KAAK1E,UAAA,CAA2BwE,gBAAA,CAAiB,aAAa,KAAKG,SAAS;MAEvEP,MAAA,CAAAI,gBAAA,CAAiB,WAAW,KAAKI,OAAO;MACxCR,MAAA,CAAAI,gBAAA,CAAiB,SAAS,KAAKK,KAAK;IAAA;IAGtC5E,aAAA,kBAAU,MAAY;MACtB,KAAAD,UAAA,CAAW8E,mBAAA,CAAoB,eAAerF,WAAW;MAC5D,KAAKO,UAAA,CAA2B8E,mBAAA,CAAoB,eAAe,KAAKL,WAAW;MACnF,KAAKzE,UAAA,CAA2B8E,mBAAA,CAAoB,eAAe,KAAKJ,WAAW;MACnF,KAAK1E,UAAA,CAA2B8E,mBAAA,CAAoB,aAAa,KAAKH,SAAS;MAE1EP,MAAA,CAAAU,mBAAA,CAAoB,WAAW,KAAKF,OAAO;MAC3CR,MAAA,CAAAU,mBAAA,CAAoB,SAAS,KAAKD,KAAK;IAAA;IA9P9C,KAAK9E,MAAA,GAASA,MAAA;IAGd,IAAIC,UAAA,KAAe,QAAW,KAAK+E,OAAA,CAAQ/E,UAAU;IAErD,KAAKqB,oBAAA,CAAqB;IAC1B,KAAKC,oBAAA,CAAqB;EAC5B;AAyPF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}