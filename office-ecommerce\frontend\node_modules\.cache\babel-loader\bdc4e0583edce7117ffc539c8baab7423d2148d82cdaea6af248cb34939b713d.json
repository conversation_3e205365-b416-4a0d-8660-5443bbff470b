{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * ProtectedRoute Component\n * Protects routes that require authentication\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  redirectTo = '/login'\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Checking authentication...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 13\n    }, this);\n  }\n  if (!isAuthenticated) {\n    // Redirect to login with return URL\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: redirectTo,\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 16\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"fNj96oVmPd4sFazcimgf9N7S8ao=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "redirectTo", "_s", "isAuthenticated", "loading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * ProtectedRoute Component\n * Protects routes that require authentication\n */\nconst ProtectedRoute = ({ children, redirectTo = '/login' }) => {\n    const { isAuthenticated, loading } = useAuth();\n    const location = useLocation();\n\n    if (loading) {\n        return (\n            <div className=\"auth-loading\">\n                <div className=\"loading-spinner\"></div>\n                <p>Checking authentication...</p>\n            </div>\n        );\n    }\n\n    if (!isAuthenticated) {\n        // Redirect to login with return URL\n        return <Navigate to={redirectTo} state={{ from: location }} replace />;\n    }\n\n    return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,qBAAqB;;AAE7C;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,UAAU,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC9C,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,IAAIS,OAAO,EAAE;IACT,oBACIN,OAAA;MAAKQ,SAAS,EAAC,cAAc;MAAAN,QAAA,gBACzBF,OAAA;QAAKQ,SAAS,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCZ,OAAA;QAAAE,QAAA,EAAG;MAA0B;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAEd;EAEA,IAAI,CAACP,eAAe,EAAE;IAClB;IACA,oBAAOL,OAAA,CAACJ,QAAQ;MAACiB,EAAE,EAAEV,UAAW;MAACW,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1E;EAEA,OAAOV,QAAQ;AACnB,CAAC;AAACE,EAAA,CAnBIH,cAAc;EAAA,QACqBH,OAAO,EAC3BD,WAAW;AAAA;AAAAoB,EAAA,GAF1BhB,cAAc;AAqBpB,eAAeA,cAAc;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}