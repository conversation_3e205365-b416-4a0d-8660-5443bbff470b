{"ast": null, "code": "import { Matrix3, OrthographicCamera, Scene, StereoCamera, WebGLRenderTarget, ShaderMaterial, Mesh, PlaneGeometry, LinearFilter, NearestFilter, RGBAFormat } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nclass AnaglyphEffect {\n  constructor(renderer, width = 512, height = 512) {\n    this.colorMatrixLeft = new Matrix3().fromArray([0.4561, -0.0400822, -0.0152161, 0.500484, -0.0378246, -0.0205971, 0.176381, -0.0157589, -546856e-8]);\n    this.colorMatrixRight = new Matrix3().fromArray([-0.0434706, 0.378476, -0.0721527, -0.0879388, 0.73364, -0.112961, -155529e-8, -0.0184503, 1.2264]);\n    const _camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1);\n    const _scene = new Scene();\n    const _stereo = new StereoCamera();\n    const _params = {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat\n    };\n    const _renderTargetL = new WebGLRenderTarget(width, height, _params);\n    const _renderTargetR = new WebGLRenderTarget(width, height, _params);\n    const _material = new ShaderMaterial({\n      uniforms: {\n        mapLeft: {\n          value: _renderTargetL.texture\n        },\n        mapRight: {\n          value: _renderTargetR.texture\n        },\n        colorMatrixLeft: {\n          value: this.colorMatrixLeft\n        },\n        colorMatrixRight: {\n          value: this.colorMatrixRight\n        }\n      },\n      vertexShader: [\"varying vec2 vUv;\", \"void main() {\", \"\tvUv = vec2( uv.x, uv.y );\", \"\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\", \"}\"].join(\"\\n\"),\n      fragmentShader: [\"uniform sampler2D mapLeft;\", \"uniform sampler2D mapRight;\", \"varying vec2 vUv;\", \"uniform mat3 colorMatrixLeft;\", \"uniform mat3 colorMatrixRight;\", \"void main() {\", \"\tvec2 uv = vUv;\", \"\tvec4 colorL = texture2D( mapLeft, uv );\", \"\tvec4 colorR = texture2D( mapRight, uv );\", \"\tvec3 color = clamp(\", \"\t\t\tcolorMatrixLeft * colorL.rgb +\", \"\t\t\tcolorMatrixRight * colorR.rgb, 0., 1. );\", \"\tgl_FragColor = vec4(\", \"\t\t\tcolor.r, color.g, color.b,\", \"\t\t\tmax( colorL.a, colorR.a ) );\", \"\t#include <tonemapping_fragment>\", `\t#include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>`, \"}\"].join(\"\\n\")\n    });\n    const _mesh = new Mesh(new PlaneGeometry(2, 2), _material);\n    _scene.add(_mesh);\n    this.setSize = function (width2, height2) {\n      renderer.setSize(width2, height2);\n      const pixelRatio = renderer.getPixelRatio();\n      _renderTargetL.setSize(width2 * pixelRatio, height2 * pixelRatio);\n      _renderTargetR.setSize(width2 * pixelRatio, height2 * pixelRatio);\n    };\n    this.render = function (scene, camera) {\n      const currentRenderTarget = renderer.getRenderTarget();\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();\n      _stereo.update(camera);\n      renderer.setRenderTarget(_renderTargetL);\n      renderer.clear();\n      renderer.render(scene, _stereo.cameraL);\n      renderer.setRenderTarget(_renderTargetR);\n      renderer.clear();\n      renderer.render(scene, _stereo.cameraR);\n      renderer.setRenderTarget(null);\n      renderer.render(_scene, _camera);\n      renderer.setRenderTarget(currentRenderTarget);\n    };\n    this.dispose = function () {\n      _renderTargetL.dispose();\n      _renderTargetR.dispose();\n      _mesh.geometry.dispose();\n      _mesh.material.dispose();\n    };\n  }\n}\nexport { AnaglyphEffect };", "map": {"version": 3, "names": ["AnaglyphEffect", "constructor", "renderer", "width", "height", "colorMatrixLeft", "Matrix3", "fromArray", "colorMatrixRight", "_camera", "OrthographicCamera", "_scene", "Scene", "_stereo", "StereoCamera", "_params", "minFilter", "LinearFilter", "magFilter", "NearestFilter", "format", "RGBAFormat", "_renderTargetL", "WebGLRenderTarget", "_renderTargetR", "_material", "ShaderMaterial", "uniforms", "mapLeft", "value", "texture", "mapRight", "vertexShader", "join", "fragmentShader", "version", "_mesh", "<PERSON><PERSON>", "PlaneGeometry", "add", "setSize", "width2", "height2", "pixelRatio", "getPixelRatio", "render", "scene", "camera", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "matrixWorldAutoUpdate", "updateMatrixWorld", "parent", "update", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "cameraL", "cameraR", "dispose", "geometry", "material"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\effects\\AnaglyphEffect.js"], "sourcesContent": ["import {\n  <PERSON>ar<PERSON><PERSON><PERSON>,\n  <PERSON>3,\n  <PERSON><PERSON>,\n  NearestFilter,\n  OrthographicCamera,\n  PlaneGeometry,\n  RGBAFormat,\n  Scene,\n  ShaderMaterial,\n  StereoCamera,\n  WebGLRenderTarget,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nclass AnaglyphEffect {\n  constructor(renderer, width = 512, height = 512) {\n    // Dubois matrices from https://citeseerx.ist.psu.edu/viewdoc/download?doi=10.1.1.7.6968&rep=rep1&type=pdf#page=4\n\n    this.colorMatrixLeft = new Matrix3().fromArray([\n      0.4561,\n      -0.0400822,\n      -0.0152161,\n      0.500484,\n      -0.0378246,\n      -0.0205971,\n      0.176381,\n      -0.0157589,\n      -0.00546856,\n    ])\n\n    this.colorMatrixRight = new Matrix3().fromArray([\n      -0.0434706,\n      0.378476,\n      -0.0721527,\n      -0.0879388,\n      0.73364,\n      -0.112961,\n      -0.00155529,\n      -0.0184503,\n      1.2264,\n    ])\n\n    const _camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)\n\n    const _scene = new Scene()\n\n    const _stereo = new StereoCamera()\n\n    const _params = { minFilter: LinearFilter, magFilter: NearestFilter, format: RGBAFormat }\n\n    const _renderTargetL = new WebGLRenderTarget(width, height, _params)\n    const _renderTargetR = new WebGLRenderTarget(width, height, _params)\n\n    const _material = new ShaderMaterial({\n      uniforms: {\n        mapLeft: { value: _renderTargetL.texture },\n        mapRight: { value: _renderTargetR.texture },\n\n        colorMatrixLeft: { value: this.colorMatrixLeft },\n        colorMatrixRight: { value: this.colorMatrixRight },\n      },\n\n      vertexShader: [\n        'varying vec2 vUv;',\n\n        'void main() {',\n\n        '\tvUv = vec2( uv.x, uv.y );',\n        '\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );',\n\n        '}',\n      ].join('\\n'),\n\n      fragmentShader: [\n        'uniform sampler2D mapLeft;',\n        'uniform sampler2D mapRight;',\n        'varying vec2 vUv;',\n\n        'uniform mat3 colorMatrixLeft;',\n        'uniform mat3 colorMatrixRight;',\n\n        'void main() {',\n\n        '\tvec2 uv = vUv;',\n\n        '\tvec4 colorL = texture2D( mapLeft, uv );',\n        '\tvec4 colorR = texture2D( mapRight, uv );',\n\n        '\tvec3 color = clamp(',\n        '\t\t\tcolorMatrixLeft * colorL.rgb +',\n        '\t\t\tcolorMatrixRight * colorR.rgb, 0., 1. );',\n\n        '\tgl_FragColor = vec4(',\n        '\t\t\tcolor.r, color.g, color.b,',\n        '\t\t\tmax( colorL.a, colorR.a ) );',\n\n        '\t#include <tonemapping_fragment>',\n        `\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>`,\n\n        '}',\n      ].join('\\n'),\n    })\n\n    const _mesh = new Mesh(new PlaneGeometry(2, 2), _material)\n    _scene.add(_mesh)\n\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height)\n\n      const pixelRatio = renderer.getPixelRatio()\n\n      _renderTargetL.setSize(width * pixelRatio, height * pixelRatio)\n      _renderTargetR.setSize(width * pixelRatio, height * pixelRatio)\n    }\n\n    this.render = function (scene, camera) {\n      const currentRenderTarget = renderer.getRenderTarget()\n\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _stereo.update(camera)\n\n      renderer.setRenderTarget(_renderTargetL)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraL)\n\n      renderer.setRenderTarget(_renderTargetR)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraR)\n\n      renderer.setRenderTarget(null)\n      renderer.render(_scene, _camera)\n\n      renderer.setRenderTarget(currentRenderTarget)\n    }\n\n    this.dispose = function () {\n      _renderTargetL.dispose()\n      _renderTargetR.dispose()\n      _mesh.geometry.dispose()\n      _mesh.material.dispose()\n    }\n  }\n}\n\nexport { AnaglyphEffect }\n"], "mappings": ";;AAeA,MAAMA,cAAA,CAAe;EACnBC,YAAYC,QAAA,EAAUC,KAAA,GAAQ,KAAKC,MAAA,GAAS,KAAK;IAG/C,KAAKC,eAAA,GAAkB,IAAIC,OAAA,CAAO,EAAGC,SAAA,CAAU,CAC7C,QACA,YACA,YACA,UACA,YACA,YACA,UACA,YACA,WACD;IAED,KAAKC,gBAAA,GAAmB,IAAIF,OAAA,CAAO,EAAGC,SAAA,CAAU,CAC9C,YACA,UACA,YACA,YACA,SACA,WACA,YACA,YACA,OACD;IAED,MAAME,OAAA,GAAU,IAAIC,kBAAA,CAAmB,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;IAEzD,MAAMC,MAAA,GAAS,IAAIC,KAAA,CAAO;IAE1B,MAAMC,OAAA,GAAU,IAAIC,YAAA,CAAc;IAElC,MAAMC,OAAA,GAAU;MAAEC,SAAA,EAAWC,YAAA;MAAcC,SAAA,EAAWC,aAAA;MAAeC,MAAA,EAAQC;IAAY;IAEzF,MAAMC,cAAA,GAAiB,IAAIC,iBAAA,CAAkBpB,KAAA,EAAOC,MAAA,EAAQW,OAAO;IACnE,MAAMS,cAAA,GAAiB,IAAID,iBAAA,CAAkBpB,KAAA,EAAOC,MAAA,EAAQW,OAAO;IAEnE,MAAMU,SAAA,GAAY,IAAIC,cAAA,CAAe;MACnCC,QAAA,EAAU;QACRC,OAAA,EAAS;UAAEC,KAAA,EAAOP,cAAA,CAAeQ;QAAS;QAC1CC,QAAA,EAAU;UAAEF,KAAA,EAAOL,cAAA,CAAeM;QAAS;QAE3CzB,eAAA,EAAiB;UAAEwB,KAAA,EAAO,KAAKxB;QAAiB;QAChDG,gBAAA,EAAkB;UAAEqB,KAAA,EAAO,KAAKrB;QAAkB;MACnD;MAEDwB,YAAA,EAAc,CACZ,qBAEA,iBAEA,8BACA,8EAEA,IACR,CAAQC,IAAA,CAAK,IAAI;MAEXC,cAAA,EAAgB,CACd,8BACA,+BACA,qBAEA,iCACA,kCAEA,iBAEA,mBAEA,4CACA,6CAEA,wBACA,qCACA,+CAEA,yBACA,iCACA,mCAEA,oCACA,cAAcC,OAAA,IAAW,MAAM,wBAAwB,yBAEvD,IACR,CAAQF,IAAA,CAAK,IAAI;IACjB,CAAK;IAED,MAAMG,KAAA,GAAQ,IAAIC,IAAA,CAAK,IAAIC,aAAA,CAAc,GAAG,CAAC,GAAGb,SAAS;IACzDd,MAAA,CAAO4B,GAAA,CAAIH,KAAK;IAEhB,KAAKI,OAAA,GAAU,UAAUC,MAAA,EAAOC,OAAA,EAAQ;MACtCxC,QAAA,CAASsC,OAAA,CAAQC,MAAA,EAAOC,OAAM;MAE9B,MAAMC,UAAA,GAAazC,QAAA,CAAS0C,aAAA,CAAe;MAE3CtB,cAAA,CAAekB,OAAA,CAAQC,MAAA,GAAQE,UAAA,EAAYD,OAAA,GAASC,UAAU;MAC9DnB,cAAA,CAAegB,OAAA,CAAQC,MAAA,GAAQE,UAAA,EAAYD,OAAA,GAASC,UAAU;IAC/D;IAED,KAAKE,MAAA,GAAS,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACrC,MAAMC,mBAAA,GAAsB9C,QAAA,CAAS+C,eAAA,CAAiB;MAEtD,IAAIH,KAAA,CAAMI,qBAAA,KAA0B,MAAMJ,KAAA,CAAMK,iBAAA,CAAmB;MAEnE,IAAIJ,MAAA,CAAOK,MAAA,KAAW,QAAQL,MAAA,CAAOG,qBAAA,KAA0B,MAAMH,MAAA,CAAOI,iBAAA,CAAmB;MAE/FtC,OAAA,CAAQwC,MAAA,CAAON,MAAM;MAErB7C,QAAA,CAASoD,eAAA,CAAgBhC,cAAc;MACvCpB,QAAA,CAASqD,KAAA,CAAO;MAChBrD,QAAA,CAAS2C,MAAA,CAAOC,KAAA,EAAOjC,OAAA,CAAQ2C,OAAO;MAEtCtD,QAAA,CAASoD,eAAA,CAAgB9B,cAAc;MACvCtB,QAAA,CAASqD,KAAA,CAAO;MAChBrD,QAAA,CAAS2C,MAAA,CAAOC,KAAA,EAAOjC,OAAA,CAAQ4C,OAAO;MAEtCvD,QAAA,CAASoD,eAAA,CAAgB,IAAI;MAC7BpD,QAAA,CAAS2C,MAAA,CAAOlC,MAAA,EAAQF,OAAO;MAE/BP,QAAA,CAASoD,eAAA,CAAgBN,mBAAmB;IAC7C;IAED,KAAKU,OAAA,GAAU,YAAY;MACzBpC,cAAA,CAAeoC,OAAA,CAAS;MACxBlC,cAAA,CAAekC,OAAA,CAAS;MACxBtB,KAAA,CAAMuB,QAAA,CAASD,OAAA,CAAS;MACxBtB,KAAA,CAAMwB,QAAA,CAASF,OAAA,CAAS;IACzB;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}