import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useWebSocket } from '../../hooks/useWebSocket';
import { useAuth } from '../../hooks/useAuth';
import { usePermissions } from '../../hooks/usePermissions';
import './AdminDashboard.css';

// Import admin components
import DashboardOverview from '../../components/admin/DashboardOverview';
import InventoryManagement from '../../components/admin/InventoryManagement';
import ProductManagementNew from '../../components/admin/ProductManagementNew';
import OrderManagement from '../../components/admin/OrderManagement';
import SupplierManagement from '../../components/admin/SupplierManagement';
import UserManagement from '../../components/admin/UserManagement';
import Analytics from '../../components/admin/Analytics';
import ActivityLogs from '../../components/admin/ActivityLogs';

// Import modern SVG icons and logo
import {
  DashboardIcon,
  InventoryIcon,
  ProductsIcon,
  OrdersIcon,
  SuppliersIcon,
  UsersIcon,
  AnalyticsIcon,
  ActivityIcon,
  LogoutIcon,
  ConnectedIcon,
  DisconnectedIcon
} from '../../components/admin/icons/AdminIcons';
import AdminLogo from '../../components/common/AdminLogo';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { canAccessAdminSection, getAccessibleAdminSections, isAdmin, isEmployee } = usePermissions();
  const { isConnected, connectionStatus, notifications, removeNotification, clearAllNotifications } = useWebSocket();

  // Get accessible sections for current user
  const accessibleSections = getAccessibleAdminSections();

  // Check for URL parameters to set active tab
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabFromUrl = searchParams.get('tab');

    if (tabFromUrl && accessibleSections.includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    } else if (accessibleSections.length > 0 && !accessibleSections.includes(activeTab)) {
      setActiveTab(accessibleSections[0]);
    }
  }, [accessibleSections, activeTab, location.search]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Check if user can access a specific tab
  const canAccessTab = (tabName) => {
    return canAccessAdminSection(tabName);
  };

  // Handle tab change with permission check
  const handleTabChange = (tabName) => {
    if (canAccessTab(tabName)) {
      setActiveTab(tabName);
      // Update URL to reflect current tab
      navigate(`/admin?tab=${tabName}`, { replace: true });
    }
  };

  // Define all menu items with their permission requirements
  const allMenuItems = [
    { id: 'overview', label: 'Dashboard', icon: DashboardIcon, permission: 'access_admin_dashboard' },
    { id: 'inventory', label: 'Inventory', icon: InventoryIcon, permission: 'manage_inventory' },
    { id: 'products', label: 'Products', icon: ProductsIcon, permission: 'manage_products' },
    { id: 'orders', label: 'Orders', icon: OrdersIcon, permission: 'view_all_orders' },
    { id: 'suppliers', label: 'Suppliers', icon: SuppliersIcon, permission: 'manage_suppliers' },
    { id: 'users', label: 'Users', icon: UsersIcon, permission: 'manage_users' },
    { id: 'analytics', label: 'Analytics', icon: AnalyticsIcon, permission: 'view_analytics' },
    { id: 'activity-logs', label: 'Activity Logs', icon: ActivityIcon, permission: 'access_admin_dashboard' }
  ];

  // Filter menu items based on user permissions
  const menuItems = allMenuItems.filter(item => canAccessAdminSection(item.id));

  const renderContent = () => {
    // Check if user has permission to access current tab
    if (!canAccessAdminSection(activeTab)) {
      return (
        <div className="access-denied">
          <h2>Access Denied</h2>
          <p>You don't have permission to access this section.</p>
          <p>Your role: <strong>{user.role}</strong></p>
        </div>
      );
    }

    switch (activeTab) {
      case 'overview':
        return <DashboardOverview />;
      case 'inventory':
        return <InventoryManagement />;
      case 'products':
        return <ProductManagementNew />;
      case 'orders':
        return <OrderManagement />;
      case 'suppliers':
        return <SupplierManagement />;
      case 'users':
        return <UserManagement />;
      case 'analytics':
        return <Analytics />;
      case 'activity-logs':
        return <ActivityLogs />;
      default:
        return <DashboardOverview />;
    }
  };

  if (!user) {
    return (
      <div className="admin-loading">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      {/* Sidebar */}
      <div className="admin-sidebar">
        <div className="admin-header">
          <div className="admin-logo">
            <AdminLogo size="small" className="admin-logo-component" />
          </div>
          <h2>Admin Panel</h2>
          <div className="connection-status">
            <div className="status-indicator">
              {connectionStatus === 'mock' ? (
                <DisconnectedIcon className="status-icon mock" color="#F59E0B" />
              ) : isConnected ? (
                <ConnectedIcon className="status-icon connected" color="#10B981" />
              ) : (
                <DisconnectedIcon className="status-icon disconnected" color="#EF4444" />
              )}
            </div>
            <span className="status-text">
              {connectionStatus === 'mock' ? 'Demo Mode' : isConnected ? 'Live Updates' : 'Offline'}
            </span>
          </div>
          <div className="admin-user-info">
            <span className="user-name">{user.firstName} {user.lastName}</span>
            <span className="user-role">{user.role}</span>
          </div>
        </div>

        <nav className="admin-nav">
          {menuItems.map(item => {
            const IconComponent = item.icon;
            const isActive = activeTab === item.id;

            return (
              <button
                key={item.id}
                className={`nav-item ${isActive ? 'active' : ''}`}
                onClick={() => handleTabChange(item.id)}
                title={`${item.label} - ${item.permission}`}
              >
                <span className="nav-icon">
                  <IconComponent
                    className="nav-icon-svg"
                    color={isActive ? '#F0B21B' : 'currentColor'}
                  />
                </span>
                <span className="nav-label">{item.label}</span>
              </button>
            );
          })}
        </nav>

        <div className="admin-footer">
          <button className="logout-btn" onClick={handleLogout}>
            <span className="nav-icon">
              <LogoutIcon className="nav-icon-svg" color="#e74c3c" />
            </span>
            <span className="nav-label">Logout</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="admin-main">
        <div className="admin-content">
          {renderContent()}
        </div>
      </div>

      {/* Notifications Panel */}
      {notifications.length > 0 && (
        <div className="notifications-panel">
          <div className="notifications-header">
            <h3>Live Updates ({notifications.length})</h3>
            <button onClick={clearAllNotifications} className="clear-all-btn">
              Clear All
            </button>
          </div>
          <div className="notifications-list">
            {notifications.slice(0, 5).map(notification => (
              <div key={notification.id} className={`notification ${notification.type}`}>
                <div className="notification-content">
                  <h4>{notification.title}</h4>
                  <p>{notification.message}</p>
                  <span className="notification-time">
                    {new Date(notification.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <button
                  onClick={() => removeNotification(notification.id)}
                  className="notification-close"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
