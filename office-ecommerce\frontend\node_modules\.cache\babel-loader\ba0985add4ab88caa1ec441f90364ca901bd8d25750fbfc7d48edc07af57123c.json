{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nconst Edges = /*#__PURE__*/React.forwardRef(({\n  userData,\n  children,\n  geometry,\n  threshold = 15,\n  color = 'black',\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = ref.current.parent;\n    if (parent) {\n      const geom = geometry || parent.geometry;\n      if (geom !== ref.current.userData.currentGeom || threshold !== ref.current.userData.currentThreshold) {\n        ref.current.userData.currentGeom = geom;\n        ref.current.userData.currentThreshold = threshold;\n        ref.current.geometry = new THREE.EdgesGeometry(geom, threshold);\n      }\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current);\n  return /*#__PURE__*/React.createElement(\"lineSegments\", _extends({\n    ref: ref,\n    raycast: () => null\n  }, props), children ? children : /*#__PURE__*/React.createElement(\"lineBasicMaterial\", {\n    color: color\n  }));\n});\nexport { Edges };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "<PERSON>s", "forwardRef", "userData", "children", "geometry", "threshold", "color", "props", "fref", "ref", "useRef", "useLayoutEffect", "parent", "current", "geom", "currentGeom", "currentThreshold", "EdgesGeometry", "useImperativeHandle", "createElement", "raycast"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Edges.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\nconst Edges = /*#__PURE__*/React.forwardRef(({\n  userData,\n  children,\n  geometry,\n  threshold = 15,\n  color = 'black',\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const parent = ref.current.parent;\n\n    if (parent) {\n      const geom = geometry || parent.geometry;\n\n      if (geom !== ref.current.userData.currentGeom || threshold !== ref.current.userData.currentThreshold) {\n        ref.current.userData.currentGeom = geom;\n        ref.current.userData.currentThreshold = threshold;\n        ref.current.geometry = new THREE.EdgesGeometry(geom, threshold);\n      }\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current);\n  return /*#__PURE__*/React.createElement(\"lineSegments\", _extends({\n    ref: ref,\n    raycast: () => null\n  }, props), children ? children : /*#__PURE__*/React.createElement(\"lineBasicMaterial\", {\n    color: color\n  }));\n});\n\nexport { Edges };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,KAAK,GAAG,aAAaF,KAAK,CAACG,UAAU,CAAC,CAAC;EAC3CC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,OAAO;EACf,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EAC9BZ,KAAK,CAACa,eAAe,CAAC,MAAM;IAC1B,MAAMC,MAAM,GAAGH,GAAG,CAACI,OAAO,CAACD,MAAM;IAEjC,IAAIA,MAAM,EAAE;MACV,MAAME,IAAI,GAAGV,QAAQ,IAAIQ,MAAM,CAACR,QAAQ;MAExC,IAAIU,IAAI,KAAKL,GAAG,CAACI,OAAO,CAACX,QAAQ,CAACa,WAAW,IAAIV,SAAS,KAAKI,GAAG,CAACI,OAAO,CAACX,QAAQ,CAACc,gBAAgB,EAAE;QACpGP,GAAG,CAACI,OAAO,CAACX,QAAQ,CAACa,WAAW,GAAGD,IAAI;QACvCL,GAAG,CAACI,OAAO,CAACX,QAAQ,CAACc,gBAAgB,GAAGX,SAAS;QACjDI,GAAG,CAACI,OAAO,CAACT,QAAQ,GAAG,IAAIL,KAAK,CAACkB,aAAa,CAACH,IAAI,EAAET,SAAS,CAAC;MACjE;IACF;EACF,CAAC,CAAC;EACFP,KAAK,CAACoB,mBAAmB,CAACV,IAAI,EAAE,MAAMC,GAAG,CAACI,OAAO,CAAC;EAClD,OAAO,aAAaf,KAAK,CAACqB,aAAa,CAAC,cAAc,EAAEtB,QAAQ,CAAC;IAC/DY,GAAG,EAAEA,GAAG;IACRW,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAEb,KAAK,CAAC,EAAEJ,QAAQ,GAAGA,QAAQ,GAAG,aAAaL,KAAK,CAACqB,aAAa,CAAC,mBAAmB,EAAE;IACrFb,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}