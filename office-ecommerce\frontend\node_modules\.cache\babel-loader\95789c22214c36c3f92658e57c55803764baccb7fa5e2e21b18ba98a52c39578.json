{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\auth\\\\AdminRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport UnauthorizedAccess from './UnauthorizedAccess';\n\n/**\n * AdminRoute Component\n * Specifically for admin-only routes\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminRoute = ({\n  children,\n  allowEmployee = false\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Checking admin permissions...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Not authenticated - redirect to login\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 16\n    }, this);\n  }\n\n  // Check admin permissions\n  const isAdmin = user.role === 'Admin';\n  const isEmployee = user.role === 'Employee';\n  const hasPermission = isAdmin || allowEmployee && isEmployee;\n  if (!hasPermission) {\n    return /*#__PURE__*/_jsxDEV(UnauthorizedAccess, {\n      userRole: user.role,\n      requiredRoles: allowEmployee ? ['Admin', 'Employee'] : ['Admin'],\n      isAdminArea: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this);\n  }\n  return children;\n};\n_s(AdminRoute, \"pvp8inAvQHEb0BVUe3eLqKvyMMQ=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = AdminRoute;\nexport default AdminRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "UnauthorizedAccess", "jsxDEV", "_jsxDEV", "AdminRoute", "children", "allowEmployee", "_s", "user", "isAuthenticated", "loading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "isAdmin", "role", "isEmployee", "hasPermission", "userRole", "requiredRoles", "isAdminArea", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/auth/AdminRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport UnauthorizedAccess from './UnauthorizedAccess';\n\n/**\n * AdminRoute Component\n * Specifically for admin-only routes\n */\nconst AdminRoute = ({ children, allowEmployee = false }) => {\n    const { user, isAuthenticated, loading } = useAuth();\n    const location = useLocation();\n\n    if (loading) {\n        return (\n            <div className=\"auth-loading\">\n                <div className=\"loading-spinner\"></div>\n                <p>Checking admin permissions...</p>\n            </div>\n        );\n    }\n\n    // Not authenticated - redirect to login\n    if (!isAuthenticated) {\n        return <Navigate to=\"/login\" state={{ from: location }} replace />;\n    }\n\n    // Check admin permissions\n    const isAdmin = user.role === 'Admin';\n    const isEmployee = user.role === 'Employee';\n    const hasPermission = isAdmin || (allowEmployee && isEmployee);\n\n    if (!hasPermission) {\n        return (\n            <UnauthorizedAccess \n                userRole={user.role} \n                requiredRoles={allowEmployee ? ['Admin', 'Employee'] : ['Admin']}\n                isAdminArea={true}\n            />\n        );\n    }\n\n    return children;\n};\n\nexport default AdminRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAOC,kBAAkB,MAAM,sBAAsB;;AAErD;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EACpD,MAAMW,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,IAAIW,OAAO,EAAE;IACT,oBACIP,OAAA;MAAKS,SAAS,EAAC,cAAc;MAAAP,QAAA,gBACzBF,OAAA;QAAKS,SAAS,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCb,OAAA;QAAAE,QAAA,EAAG;MAA6B;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAEd;;EAEA;EACA,IAAI,CAACP,eAAe,EAAE;IAClB,oBAAON,OAAA,CAACL,QAAQ;MAACmB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtE;;EAEA;EACA,MAAMK,OAAO,GAAGb,IAAI,CAACc,IAAI,KAAK,OAAO;EACrC,MAAMC,UAAU,GAAGf,IAAI,CAACc,IAAI,KAAK,UAAU;EAC3C,MAAME,aAAa,GAAGH,OAAO,IAAKf,aAAa,IAAIiB,UAAW;EAE9D,IAAI,CAACC,aAAa,EAAE;IAChB,oBACIrB,OAAA,CAACF,kBAAkB;MACfwB,QAAQ,EAAEjB,IAAI,CAACc,IAAK;MACpBI,aAAa,EAAEpB,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC,OAAO,CAAE;MACjEqB,WAAW,EAAE;IAAK;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAEV;EAEA,OAAOX,QAAQ;AACnB,CAAC;AAACE,EAAA,CAlCIH,UAAU;EAAA,QAC+BJ,OAAO,EACjCD,WAAW;AAAA;AAAA6B,EAAA,GAF1BxB,UAAU;AAoChB,eAAeA,UAAU;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}