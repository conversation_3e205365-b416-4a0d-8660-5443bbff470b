{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\ActivityLogs.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\nimport apiClient from '../../services/apiClient';\nimport './ActivityLogs.css';\n\n// Import icons\nimport { SearchIcon, FilterIcon, RefreshIcon, ExportIcon, InfoIcon, WarningIcon, ErrorIcon, CriticalIcon } from './icons/ActivityLogIcons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ActivityLogs = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    currentPage: 1,\n    totalPages: 1,\n    totalItems: 0,\n    itemsPerPage: 50\n  });\n\n  // Filter states\n  const [filters, setFilters] = useState({\n    search: '',\n    action: '',\n    entityType: '',\n    severity: '',\n    startDate: '',\n    endDate: '',\n    userID: ''\n  });\n\n  // Available options for filters\n  const [filterOptions, setFilterOptions] = useState({\n    actions: [],\n    entityTypes: [],\n    severities: ['INFO', 'WARNING', 'ERROR', 'CRITICAL']\n  });\n\n  // Statistics\n  const [stats, setStats] = useState(null);\n\n  // Load activity logs\n  const loadActivityLogs = async (page = 1) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: pagination.itemsPerPage.toString(),\n        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value !== ''))\n      });\n      const response = await apiClient.get(`/api/admin/activity-logs?${params}`);\n      if (response.success) {\n        setLogs(response.data.logs);\n        setPagination(response.data.pagination);\n      } else {\n        throw new Error(response.message || 'Failed to load activity logs');\n      }\n    } catch (err) {\n      console.error('Failed to load activity logs:', err);\n      setError(err.message);\n\n      // Fallback to mock data if backend is not accessible or we're in mock mode\n      if (err.message.includes('Network error') || isMockMode()) {\n        console.log('Using mock activity logs data');\n        setLogs(getMockActivityLogs());\n        setPagination({\n          currentPage: 1,\n          totalPages: 1,\n          totalItems: 5,\n          itemsPerPage: 50\n        });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load filter options\n  const loadFilterOptions = async () => {\n    try {\n      const [actionsResponse, entityTypesResponse] = await Promise.all([apiClient.get('/api/admin/activity-logs/actions'), apiClient.get('/api/admin/activity-logs/entity-types')]);\n      setFilterOptions(prev => ({\n        ...prev,\n        actions: actionsResponse.success ? actionsResponse.data : [],\n        entityTypes: entityTypesResponse.success ? entityTypesResponse.data : []\n      }));\n    } catch (err) {\n      console.error('Failed to load filter options:', err);\n      // Use fallback options\n      setFilterOptions(prev => ({\n        ...prev,\n        actions: ['LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW'],\n        entityTypes: ['User', 'Product', 'Order', 'Inventory', 'Authentication', 'Dashboard']\n      }));\n    }\n  };\n\n  // Load statistics\n  const loadStats = async () => {\n    try {\n      const response = await apiClient.get('/api/admin/activity-logs/stats');\n      if (response.success) {\n        setStats(response.data);\n      }\n    } catch (err) {\n      console.error('Failed to load activity stats:', err);\n      // Use mock stats\n      setStats({\n        overview: {\n          totalActivities: 1250,\n          last24Hours: 45,\n          last7Days: 320,\n          errorCount: 8\n        },\n        actionStats: [{\n          Action: 'LOGIN',\n          count: 150\n        }, {\n          Action: 'VIEW',\n          count: 300\n        }, {\n          Action: 'CREATE',\n          count: 80\n        }]\n      });\n    }\n  };\n\n  // Check if we're in mock mode\n  const isMockMode = () => localStorage.getItem('mockMode') === 'true';\n\n  // Mock data for fallback\n  const getMockActivityLogs = () => [{\n    LogID: '1',\n    UserName: 'Admin User',\n    UserEmail: '<EMAIL>',\n    UserRole: 'Admin',\n    Action: 'LOGIN',\n    EntityType: 'Authentication',\n    Description: 'User logged in successfully (mock mode)',\n    Severity: 'INFO',\n    CreatedAt: new Date().toISOString(),\n    IPAddress: '127.0.0.1',\n    Duration: 150\n  }, {\n    LogID: '2',\n    UserName: 'Admin User',\n    UserEmail: '<EMAIL>',\n    UserRole: 'Admin',\n    Action: 'VIEW',\n    EntityType: 'Dashboard',\n    Description: 'Accessed admin dashboard',\n    Severity: 'INFO',\n    CreatedAt: new Date(Date.now() - 300000).toISOString(),\n    IPAddress: '127.0.0.1',\n    Duration: 89\n  }, {\n    LogID: '3',\n    UserName: 'Admin User',\n    UserEmail: '<EMAIL>',\n    UserRole: 'Admin',\n    Action: 'VIEW',\n    EntityType: 'ActivityLogs',\n    Description: 'Accessed activity logs page',\n    Severity: 'INFO',\n    CreatedAt: new Date(Date.now() - 600000).toISOString(),\n    IPAddress: '127.0.0.1',\n    Duration: 120\n  }, {\n    LogID: '4',\n    UserName: 'Manager User',\n    UserEmail: '<EMAIL>',\n    UserRole: 'Employee',\n    Action: 'CREATE',\n    EntityType: 'Product',\n    Description: 'Created new product: Office Chair Pro',\n    Severity: 'INFO',\n    CreatedAt: new Date(Date.now() - 900000).toISOString(),\n    IPAddress: '*************',\n    Duration: 250\n  }, {\n    LogID: '5',\n    UserName: 'System',\n    UserEmail: null,\n    UserRole: 'System',\n    Action: 'ERROR',\n    EntityType: 'Database',\n    Description: 'Database connection timeout',\n    Severity: 'ERROR',\n    CreatedAt: new Date(Date.now() - 1200000).toISOString(),\n    IPAddress: null,\n    Duration: 5000\n  }];\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  // Apply filters\n  const applyFilters = () => {\n    loadActivityLogs(1);\n  };\n\n  // Clear filters\n  const clearFilters = () => {\n    setFilters({\n      search: '',\n      action: '',\n      entityType: '',\n      severity: '',\n      startDate: '',\n      endDate: '',\n      userID: ''\n    });\n    setTimeout(() => loadActivityLogs(1), 100);\n  };\n\n  // Format date\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleString();\n  };\n\n  // Get severity icon\n  const getSeverityIcon = severity => {\n    switch (severity) {\n      case 'INFO':\n        return /*#__PURE__*/_jsxDEV(InfoIcon, {\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 16\n        }, this);\n      case 'WARNING':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"#F59E0B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 16\n        }, this);\n      case 'ERROR':\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          color: \"#EF4444\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 16\n        }, this);\n      case 'CRITICAL':\n        return /*#__PURE__*/_jsxDEV(CriticalIcon, {\n          color: \"#DC2626\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(InfoIcon, {\n          color: \"#6B7280\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Load data on component mount\n  useEffect(() => {\n    // If we're in mock mode, load mock data immediately\n    if (isMockMode()) {\n      console.log('Mock mode detected, loading mock data');\n      setLogs(getMockActivityLogs());\n      setPagination({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 5,\n        itemsPerPage: 50\n      });\n      setLoading(false);\n    } else {\n      loadActivityLogs();\n    }\n    loadFilterOptions();\n    loadStats();\n  }, []);\n  if (loading && logs.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"activity-logs\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading activity logs...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"activity-logs\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"activity-logs-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Activity Logs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Monitor system activities and user actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), isMockMode() && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mock-mode-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"#F59E0B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Demo Mode - Showing sample data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: () => loadActivityLogs(pagination.currentPage),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), \"Refresh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), stats && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"activity-stats\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.overview.totalActivities\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"Total Activities\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.overview.last24Hours\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"Last 24 Hours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.overview.last7Days\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"Last 7 Days\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card error\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.overview.errorCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"Errors\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"activity-filters\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filters-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input\",\n            children: [/*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search descriptions, users, entities...\",\n              value: filters.search,\n              onChange: e => handleFilterChange('search', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.action,\n            onChange: e => handleFilterChange('action', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), filterOptions.actions.map(action => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: action,\n              children: action\n            }, action, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Entity Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.entityType,\n            onChange: e => handleFilterChange('entityType', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Types\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), filterOptions.entityTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: type,\n              children: type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Severity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filters.severity,\n            onChange: e => handleFilterChange('severity', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Severities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), filterOptions.severities.map(severity => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: severity,\n              children: severity\n            }, severity, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filters-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Start Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"datetime-local\",\n            value: filters.startDate,\n            onChange: e => handleFilterChange('startDate', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"End Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"datetime-local\",\n            value: filters.endDate,\n            onChange: e => handleFilterChange('endDate', e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: applyFilters,\n            children: [/*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), \"Apply Filters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearFilters,\n            children: \"Clear\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 11\n      }, this), error.includes('Network error') && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"fallback-notice\",\n        children: \" (Using mock data)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"activity-table-container\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"activity-table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Timestamp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Action\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Entity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Severity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"IP Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: logs.map(log => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: `severity-${log.Severity.toLowerCase()}`,\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"timestamp\",\n              children: formatDate(log.CreatedAt)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"user-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-name\",\n                children: log.UserName || 'System'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-role\",\n                children: log.UserRole\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"action\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `action-badge ${log.Action.toLowerCase()}`,\n                children: log.Action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"entity\",\n              children: log.EntityType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"description\",\n              children: log.Description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"severity\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"severity-indicator\",\n                children: [getSeverityIcon(log.Severity), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: log.Severity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"ip-address\",\n              children: log.IPAddress || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"duration\",\n              children: log.Duration ? `${log.Duration}ms` : '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, log.LogID, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this), pagination.totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"pagination\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        disabled: pagination.currentPage === 1,\n        onClick: () => loadActivityLogs(pagination.currentPage - 1),\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"pagination-info\",\n        children: [\"Page \", pagination.currentPage, \" of \", pagination.totalPages, \"(\", pagination.totalItems, \" total items)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-secondary\",\n        disabled: pagination.currentPage === pagination.totalPages,\n        onClick: () => loadActivityLogs(pagination.currentPage + 1),\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 5\n  }, this);\n};\n_s(ActivityLogs, \"xymePA9R6qapRLeqUaxGkgUZwCw=\", false, function () {\n  return [useAuth];\n});\n_c = ActivityLogs;\nexport default ActivityLogs;\nvar _c;\n$RefreshReg$(_c, \"ActivityLogs\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "apiClient", "SearchIcon", "FilterIcon", "RefreshIcon", "ExportIcon", "InfoIcon", "WarningIcon", "ErrorIcon", "CriticalIcon", "jsxDEV", "_jsxDEV", "ActivityLogs", "_s", "user", "logs", "setLogs", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "currentPage", "totalPages", "totalItems", "itemsPerPage", "filters", "setFilters", "search", "action", "entityType", "severity", "startDate", "endDate", "userID", "filterOptions", "setFilterOptions", "actions", "entityTypes", "severities", "stats", "setStats", "loadActivityLogs", "page", "params", "URLSearchParams", "toString", "limit", "Object", "fromEntries", "entries", "filter", "_", "value", "response", "get", "success", "data", "Error", "message", "err", "console", "includes", "isMockMode", "log", "getMockActivityLogs", "loadFilterOptions", "actionsResponse", "entityTypesResponse", "Promise", "all", "prev", "loadStats", "overview", "totalActivities", "last24Hours", "last7Days", "errorCount", "actionStats", "Action", "count", "localStorage", "getItem", "LogID", "UserName", "UserEmail", "UserRole", "EntityType", "Description", "Severity", "CreatedAt", "Date", "toISOString", "<PERSON><PERSON><PERSON>", "Duration", "now", "handleFilterChange", "key", "applyFilters", "clearFilters", "setTimeout", "formatDate", "dateString", "toLocaleString", "getSeverityIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "className", "children", "onClick", "disabled", "type", "placeholder", "onChange", "e", "target", "map", "toLowerCase", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/ActivityLogs.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\nimport apiClient from '../../services/apiClient';\nimport './ActivityLogs.css';\n\n// Import icons\nimport {\n  SearchIcon,\n  FilterIcon,\n  RefreshIcon,\n  ExportIcon,\n  InfoIcon,\n  WarningIcon,\n  ErrorIcon,\n  CriticalIcon\n} from './icons/ActivityLogIcons';\n\nconst ActivityLogs = () => {\n  const { user } = useAuth();\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    currentPage: 1,\n    totalPages: 1,\n    totalItems: 0,\n    itemsPerPage: 50\n  });\n\n  // Filter states\n  const [filters, setFilters] = useState({\n    search: '',\n    action: '',\n    entityType: '',\n    severity: '',\n    startDate: '',\n    endDate: '',\n    userID: ''\n  });\n\n  // Available options for filters\n  const [filterOptions, setFilterOptions] = useState({\n    actions: [],\n    entityTypes: [],\n    severities: ['INFO', 'WARNING', 'ERROR', 'CRITICAL']\n  });\n\n  // Statistics\n  const [stats, setStats] = useState(null);\n\n  // Load activity logs\n  const loadActivityLogs = async (page = 1) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: pagination.itemsPerPage.toString(),\n        ...Object.fromEntries(\n          Object.entries(filters).filter(([_, value]) => value !== '')\n        )\n      });\n\n      const response = await apiClient.get(`/api/admin/activity-logs?${params}`);\n      \n      if (response.success) {\n        setLogs(response.data.logs);\n        setPagination(response.data.pagination);\n      } else {\n        throw new Error(response.message || 'Failed to load activity logs');\n      }\n    } catch (err) {\n      console.error('Failed to load activity logs:', err);\n      setError(err.message);\n      \n      // Fallback to mock data if backend is not accessible or we're in mock mode\n      if (err.message.includes('Network error') || isMockMode()) {\n        console.log('Using mock activity logs data');\n        setLogs(getMockActivityLogs());\n        setPagination({\n          currentPage: 1,\n          totalPages: 1,\n          totalItems: 5,\n          itemsPerPage: 50\n        });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Load filter options\n  const loadFilterOptions = async () => {\n    try {\n      const [actionsResponse, entityTypesResponse] = await Promise.all([\n        apiClient.get('/api/admin/activity-logs/actions'),\n        apiClient.get('/api/admin/activity-logs/entity-types')\n      ]);\n\n      setFilterOptions(prev => ({\n        ...prev,\n        actions: actionsResponse.success ? actionsResponse.data : [],\n        entityTypes: entityTypesResponse.success ? entityTypesResponse.data : []\n      }));\n    } catch (err) {\n      console.error('Failed to load filter options:', err);\n      // Use fallback options\n      setFilterOptions(prev => ({\n        ...prev,\n        actions: ['LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW'],\n        entityTypes: ['User', 'Product', 'Order', 'Inventory', 'Authentication', 'Dashboard']\n      }));\n    }\n  };\n\n  // Load statistics\n  const loadStats = async () => {\n    try {\n      const response = await apiClient.get('/api/admin/activity-logs/stats');\n      if (response.success) {\n        setStats(response.data);\n      }\n    } catch (err) {\n      console.error('Failed to load activity stats:', err);\n      // Use mock stats\n      setStats({\n        overview: {\n          totalActivities: 1250,\n          last24Hours: 45,\n          last7Days: 320,\n          errorCount: 8\n        },\n        actionStats: [\n          { Action: 'LOGIN', count: 150 },\n          { Action: 'VIEW', count: 300 },\n          { Action: 'CREATE', count: 80 }\n        ]\n      });\n    }\n  };\n\n  // Check if we're in mock mode\n  const isMockMode = () => localStorage.getItem('mockMode') === 'true';\n\n  // Mock data for fallback\n  const getMockActivityLogs = () => [\n    {\n      LogID: '1',\n      UserName: 'Admin User',\n      UserEmail: '<EMAIL>',\n      UserRole: 'Admin',\n      Action: 'LOGIN',\n      EntityType: 'Authentication',\n      Description: 'User logged in successfully (mock mode)',\n      Severity: 'INFO',\n      CreatedAt: new Date().toISOString(),\n      IPAddress: '127.0.0.1',\n      Duration: 150\n    },\n    {\n      LogID: '2',\n      UserName: 'Admin User',\n      UserEmail: '<EMAIL>',\n      UserRole: 'Admin',\n      Action: 'VIEW',\n      EntityType: 'Dashboard',\n      Description: 'Accessed admin dashboard',\n      Severity: 'INFO',\n      CreatedAt: new Date(Date.now() - 300000).toISOString(),\n      IPAddress: '127.0.0.1',\n      Duration: 89\n    },\n    {\n      LogID: '3',\n      UserName: 'Admin User',\n      UserEmail: '<EMAIL>',\n      UserRole: 'Admin',\n      Action: 'VIEW',\n      EntityType: 'ActivityLogs',\n      Description: 'Accessed activity logs page',\n      Severity: 'INFO',\n      CreatedAt: new Date(Date.now() - 600000).toISOString(),\n      IPAddress: '127.0.0.1',\n      Duration: 120\n    },\n    {\n      LogID: '4',\n      UserName: 'Manager User',\n      UserEmail: '<EMAIL>',\n      UserRole: 'Employee',\n      Action: 'CREATE',\n      EntityType: 'Product',\n      Description: 'Created new product: Office Chair Pro',\n      Severity: 'INFO',\n      CreatedAt: new Date(Date.now() - 900000).toISOString(),\n      IPAddress: '*************',\n      Duration: 250\n    },\n    {\n      LogID: '5',\n      UserName: 'System',\n      UserEmail: null,\n      UserRole: 'System',\n      Action: 'ERROR',\n      EntityType: 'Database',\n      Description: 'Database connection timeout',\n      Severity: 'ERROR',\n      CreatedAt: new Date(Date.now() - 1200000).toISOString(),\n      IPAddress: null,\n      Duration: 5000\n    }\n  ];\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  // Apply filters\n  const applyFilters = () => {\n    loadActivityLogs(1);\n  };\n\n  // Clear filters\n  const clearFilters = () => {\n    setFilters({\n      search: '',\n      action: '',\n      entityType: '',\n      severity: '',\n      startDate: '',\n      endDate: '',\n      userID: ''\n    });\n    setTimeout(() => loadActivityLogs(1), 100);\n  };\n\n  // Format date\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleString();\n  };\n\n  // Get severity icon\n  const getSeverityIcon = (severity) => {\n    switch (severity) {\n      case 'INFO':\n        return <InfoIcon color=\"#3B82F6\" />;\n      case 'WARNING':\n        return <WarningIcon color=\"#F59E0B\" />;\n      case 'ERROR':\n        return <ErrorIcon color=\"#EF4444\" />;\n      case 'CRITICAL':\n        return <CriticalIcon color=\"#DC2626\" />;\n      default:\n        return <InfoIcon color=\"#6B7280\" />;\n    }\n  };\n\n  // Load data on component mount\n  useEffect(() => {\n    // If we're in mock mode, load mock data immediately\n    if (isMockMode()) {\n      console.log('Mock mode detected, loading mock data');\n      setLogs(getMockActivityLogs());\n      setPagination({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 5,\n        itemsPerPage: 50\n      });\n      setLoading(false);\n    } else {\n      loadActivityLogs();\n    }\n    loadFilterOptions();\n    loadStats();\n  }, []);\n\n  if (loading && logs.length === 0) {\n    return (\n      <div className=\"activity-logs\">\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>Loading activity logs...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"activity-logs\">\n      <div className=\"activity-logs-header\">\n        <div className=\"header-title\">\n          <h1>Activity Logs</h1>\n          <p>Monitor system activities and user actions</p>\n          {isMockMode() && (\n            <div className=\"mock-mode-indicator\">\n              <WarningIcon color=\"#F59E0B\" />\n              <span>Demo Mode - Showing sample data</span>\n            </div>\n          )}\n        </div>\n        <div className=\"header-actions\">\n          <button\n            className=\"btn btn-secondary\"\n            onClick={() => loadActivityLogs(pagination.currentPage)}\n            disabled={loading}\n          >\n            <RefreshIcon />\n            Refresh\n          </button>\n          <button className=\"btn btn-primary\">\n            <ExportIcon />\n            Export\n          </button>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      {stats && (\n        <div className=\"activity-stats\">\n          <div className=\"stat-card\">\n            <div className=\"stat-value\">{stats.overview.totalActivities}</div>\n            <div className=\"stat-label\">Total Activities</div>\n          </div>\n          <div className=\"stat-card\">\n            <div className=\"stat-value\">{stats.overview.last24Hours}</div>\n            <div className=\"stat-label\">Last 24 Hours</div>\n          </div>\n          <div className=\"stat-card\">\n            <div className=\"stat-value\">{stats.overview.last7Days}</div>\n            <div className=\"stat-label\">Last 7 Days</div>\n          </div>\n          <div className=\"stat-card error\">\n            <div className=\"stat-value\">{stats.overview.errorCount}</div>\n            <div className=\"stat-label\">Errors</div>\n          </div>\n        </div>\n      )}\n\n      {/* Filters */}\n      <div className=\"activity-filters\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Search</label>\n            <div className=\"search-input\">\n              <SearchIcon />\n              <input\n                type=\"text\"\n                placeholder=\"Search descriptions, users, entities...\"\n                value={filters.search}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n              />\n            </div>\n          </div>\n          \n          <div className=\"filter-group\">\n            <label>Action</label>\n            <select\n              value={filters.action}\n              onChange={(e) => handleFilterChange('action', e.target.value)}\n            >\n              <option value=\"\">All Actions</option>\n              {filterOptions.actions.map(action => (\n                <option key={action} value={action}>{action}</option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Entity Type</label>\n            <select\n              value={filters.entityType}\n              onChange={(e) => handleFilterChange('entityType', e.target.value)}\n            >\n              <option value=\"\">All Types</option>\n              {filterOptions.entityTypes.map(type => (\n                <option key={type} value={type}>{type}</option>\n              ))}\n            </select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Severity</label>\n            <select\n              value={filters.severity}\n              onChange={(e) => handleFilterChange('severity', e.target.value)}\n            >\n              <option value=\"\">All Severities</option>\n              {filterOptions.severities.map(severity => (\n                <option key={severity} value={severity}>{severity}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Start Date</label>\n            <input\n              type=\"datetime-local\"\n              value={filters.startDate}\n              onChange={(e) => handleFilterChange('startDate', e.target.value)}\n            />\n          </div>\n\n          <div className=\"filter-group\">\n            <label>End Date</label>\n            <input\n              type=\"datetime-local\"\n              value={filters.endDate}\n              onChange={(e) => handleFilterChange('endDate', e.target.value)}\n            />\n          </div>\n\n          <div className=\"filter-actions\">\n            <button className=\"btn btn-primary\" onClick={applyFilters}>\n              <FilterIcon />\n              Apply Filters\n            </button>\n            <button className=\"btn btn-secondary\" onClick={clearFilters}>\n              Clear\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {error && (\n        <div className=\"error-message\">\n          <ErrorIcon />\n          <span>{error}</span>\n          {error.includes('Network error') && (\n            <span className=\"fallback-notice\"> (Using mock data)</span>\n          )}\n        </div>\n      )}\n\n      {/* Activity Logs Table */}\n      <div className=\"activity-table-container\">\n        <table className=\"activity-table\">\n          <thead>\n            <tr>\n              <th>Timestamp</th>\n              <th>User</th>\n              <th>Action</th>\n              <th>Entity</th>\n              <th>Description</th>\n              <th>Severity</th>\n              <th>IP Address</th>\n              <th>Duration</th>\n            </tr>\n          </thead>\n          <tbody>\n            {logs.map(log => (\n              <tr key={log.LogID} className={`severity-${log.Severity.toLowerCase()}`}>\n                <td className=\"timestamp\">\n                  {formatDate(log.CreatedAt)}\n                </td>\n                <td className=\"user-info\">\n                  <div className=\"user-name\">{log.UserName || 'System'}</div>\n                  <div className=\"user-role\">{log.UserRole}</div>\n                </td>\n                <td className=\"action\">\n                  <span className={`action-badge ${log.Action.toLowerCase()}`}>\n                    {log.Action}\n                  </span>\n                </td>\n                <td className=\"entity\">{log.EntityType}</td>\n                <td className=\"description\">{log.Description}</td>\n                <td className=\"severity\">\n                  <div className=\"severity-indicator\">\n                    {getSeverityIcon(log.Severity)}\n                    <span>{log.Severity}</span>\n                  </div>\n                </td>\n                <td className=\"ip-address\">{log.IPAddress || '-'}</td>\n                <td className=\"duration\">{log.Duration ? `${log.Duration}ms` : '-'}</td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Pagination */}\n      {pagination.totalPages > 1 && (\n        <div className=\"pagination\">\n          <button\n            className=\"btn btn-secondary\"\n            disabled={pagination.currentPage === 1}\n            onClick={() => loadActivityLogs(pagination.currentPage - 1)}\n          >\n            Previous\n          </button>\n          \n          <span className=\"pagination-info\">\n            Page {pagination.currentPage} of {pagination.totalPages} \n            ({pagination.totalItems} total items)\n          </span>\n          \n          <button\n            className=\"btn btn-secondary\"\n            disabled={pagination.currentPage === pagination.totalPages}\n            onClick={() => loadActivityLogs(pagination.currentPage + 1)}\n          >\n            Next\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ActivityLogs;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,oBAAoB;;AAE3B;AACA,SACEC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,YAAY,QACP,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACe,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC;IAC3CyB,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC;IACrC+B,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC;IACjDwC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU;EACrD,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM6C,gBAAgB,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IAC3C,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMyB,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCF,IAAI,EAAEA,IAAI,CAACG,QAAQ,CAAC,CAAC;QACrBC,KAAK,EAAE3B,UAAU,CAACK,YAAY,CAACqB,QAAQ,CAAC,CAAC;QACzC,GAAGE,MAAM,CAACC,WAAW,CACnBD,MAAM,CAACE,OAAO,CAACxB,OAAO,CAAC,CAACyB,MAAM,CAAC,CAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,KAAKA,KAAK,KAAK,EAAE,CAC7D;MACF,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,MAAMtD,SAAS,CAACuD,GAAG,CAAC,4BAA4BX,MAAM,EAAE,CAAC;MAE1E,IAAIU,QAAQ,CAACE,OAAO,EAAE;QACpBzC,OAAO,CAACuC,QAAQ,CAACG,IAAI,CAAC3C,IAAI,CAAC;QAC3BO,aAAa,CAACiC,QAAQ,CAACG,IAAI,CAACrC,UAAU,CAAC;MACzC,CAAC,MAAM;QACL,MAAM,IAAIsC,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAC3C,KAAK,CAAC,+BAA+B,EAAE0C,GAAG,CAAC;MACnDzC,QAAQ,CAACyC,GAAG,CAACD,OAAO,CAAC;;MAErB;MACA,IAAIC,GAAG,CAACD,OAAO,CAACG,QAAQ,CAAC,eAAe,CAAC,IAAIC,UAAU,CAAC,CAAC,EAAE;QACzDF,OAAO,CAACG,GAAG,CAAC,+BAA+B,CAAC;QAC5CjD,OAAO,CAACkD,mBAAmB,CAAC,CAAC,CAAC;QAC9B5C,aAAa,CAAC;UACZC,WAAW,EAAE,CAAC;UACdC,UAAU,EAAE,CAAC;UACbC,UAAU,EAAE,CAAC;UACbC,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAM,CAACC,eAAe,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/DtE,SAAS,CAACuD,GAAG,CAAC,kCAAkC,CAAC,EACjDvD,SAAS,CAACuD,GAAG,CAAC,uCAAuC,CAAC,CACvD,CAAC;MAEFnB,gBAAgB,CAACmC,IAAI,KAAK;QACxB,GAAGA,IAAI;QACPlC,OAAO,EAAE8B,eAAe,CAACX,OAAO,GAAGW,eAAe,CAACV,IAAI,GAAG,EAAE;QAC5DnB,WAAW,EAAE8B,mBAAmB,CAACZ,OAAO,GAAGY,mBAAmB,CAACX,IAAI,GAAG;MACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC3C,KAAK,CAAC,gCAAgC,EAAE0C,GAAG,CAAC;MACpD;MACAxB,gBAAgB,CAACmC,IAAI,KAAK;QACxB,GAAGA,IAAI;QACPlC,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC;QAClEC,WAAW,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW;MACtF,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMkC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMtD,SAAS,CAACuD,GAAG,CAAC,gCAAgC,CAAC;MACtE,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpBf,QAAQ,CAACa,QAAQ,CAACG,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC3C,KAAK,CAAC,gCAAgC,EAAE0C,GAAG,CAAC;MACpD;MACAnB,QAAQ,CAAC;QACPgC,QAAQ,EAAE;UACRC,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE,EAAE;UACfC,SAAS,EAAE,GAAG;UACdC,UAAU,EAAE;QACd,CAAC;QACDC,WAAW,EAAE,CACX;UAAEC,MAAM,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAI,CAAC,EAC/B;UAAED,MAAM,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAI,CAAC,EAC9B;UAAED,MAAM,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAG,CAAC;MAEnC,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMjB,UAAU,GAAGA,CAAA,KAAMkB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,MAAM;;EAEpE;EACA,MAAMjB,mBAAmB,GAAGA,CAAA,KAAM,CAChC;IACEkB,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,sBAAsB;IACjCC,QAAQ,EAAE,OAAO;IACjBP,MAAM,EAAE,OAAO;IACfQ,UAAU,EAAE,gBAAgB;IAC5BC,WAAW,EAAE,yCAAyC;IACtDC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEX,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,sBAAsB;IACjCC,QAAQ,EAAE,OAAO;IACjBP,MAAM,EAAE,MAAM;IACdQ,UAAU,EAAE,WAAW;IACvBC,WAAW,EAAE,0BAA0B;IACvCC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACH,WAAW,CAAC,CAAC;IACtDC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEX,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE,sBAAsB;IACjCC,QAAQ,EAAE,OAAO;IACjBP,MAAM,EAAE,MAAM;IACdQ,UAAU,EAAE,cAAc;IAC1BC,WAAW,EAAE,6BAA6B;IAC1CC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACH,WAAW,CAAC,CAAC;IACtDC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEX,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,cAAc;IACxBC,SAAS,EAAE,wBAAwB;IACnCC,QAAQ,EAAE,UAAU;IACpBP,MAAM,EAAE,QAAQ;IAChBQ,UAAU,EAAE,SAAS;IACrBC,WAAW,EAAE,uCAAuC;IACpDC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACH,WAAW,CAAC,CAAC;IACtDC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEX,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,QAAQ;IAClBP,MAAM,EAAE,OAAO;IACfQ,UAAU,EAAE,UAAU;IACtBC,WAAW,EAAE,6BAA6B;IAC1CC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACH,WAAW,CAAC,CAAC;IACvDC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;EACZ,CAAC,CACF;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACC,GAAG,EAAE5C,KAAK,KAAK;IACzC1B,UAAU,CAAC4C,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAAC0B,GAAG,GAAG5C;IACT,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM6C,YAAY,GAAGA,CAAA,KAAM;IACzBxD,gBAAgB,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMyD,YAAY,GAAGA,CAAA,KAAM;IACzBxE,UAAU,CAAC;MACTC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE;IACV,CAAC,CAAC;IACFkE,UAAU,CAAC,MAAM1D,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;EAC5C,CAAC;;EAED;EACA,MAAM2D,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIX,IAAI,CAACW,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIzE,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QACT,oBAAOrB,OAAA,CAACL,QAAQ;UAACoG,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC,KAAK,SAAS;QACZ,oBAAOnG,OAAA,CAACJ,WAAW;UAACmG,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,KAAK,OAAO;QACV,oBAAOnG,OAAA,CAACH,SAAS;UAACkG,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,KAAK,UAAU;QACb,oBAAOnG,OAAA,CAACF,YAAY;UAACiG,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzC;QACE,oBAAOnG,OAAA,CAACL,QAAQ;UAACoG,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACvC;EACF,CAAC;;EAED;EACA/G,SAAS,CAAC,MAAM;IACd;IACA,IAAIiE,UAAU,CAAC,CAAC,EAAE;MAChBF,OAAO,CAACG,GAAG,CAAC,uCAAuC,CAAC;MACpDjD,OAAO,CAACkD,mBAAmB,CAAC,CAAC,CAAC;MAC9B5C,aAAa,CAAC;QACZC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,MAAM;MACLyB,gBAAgB,CAAC,CAAC;IACpB;IACAwB,iBAAiB,CAAC,CAAC;IACnBM,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIxD,OAAO,IAAIF,IAAI,CAACgG,MAAM,KAAK,CAAC,EAAE;IAChC,oBACEpG,OAAA;MAAKqG,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BtG,OAAA;QAAKqG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtG,OAAA;UAAKqG,SAAS,EAAC;QAAiB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCnG,OAAA;UAAAsG,QAAA,EAAG;QAAwB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnG,OAAA;IAAKqG,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BtG,OAAA;MAAKqG,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCtG,OAAA;QAAKqG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtG,OAAA;UAAAsG,QAAA,EAAI;QAAa;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBnG,OAAA;UAAAsG,QAAA,EAAG;QAA0C;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAChD9C,UAAU,CAAC,CAAC,iBACXrD,OAAA;UAAKqG,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCtG,OAAA,CAACJ,WAAW;YAACmG,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BnG,OAAA;YAAAsG,QAAA,EAAM;UAA+B;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNnG,OAAA;QAAKqG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BtG,OAAA;UACEqG,SAAS,EAAC,mBAAmB;UAC7BE,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACtB,UAAU,CAACE,WAAW,CAAE;UACxD4F,QAAQ,EAAElG,OAAQ;UAAAgG,QAAA,gBAElBtG,OAAA,CAACP,WAAW;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnG,OAAA;UAAQqG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBACjCtG,OAAA,CAACN,UAAU;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEhB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrE,KAAK,iBACJ9B,OAAA;MAAKqG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BtG,OAAA;QAAKqG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExE,KAAK,CAACiC,QAAQ,CAACC;QAAe;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClEnG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNnG,OAAA;QAAKqG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExE,KAAK,CAACiC,QAAQ,CAACE;QAAW;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9DnG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACNnG,OAAA;QAAKqG,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExE,KAAK,CAACiC,QAAQ,CAACG;QAAS;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5DnG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACNnG,OAAA;QAAKqG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BtG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAExE,KAAK,CAACiC,QAAQ,CAACI;QAAU;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7DnG,OAAA;UAAKqG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnG,OAAA;MAAKqG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BtG,OAAA;QAAKqG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtG,OAAA;YAAAsG,QAAA,EAAO;UAAM;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrBnG,OAAA;YAAKqG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BtG,OAAA,CAACT,UAAU;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACdnG,OAAA;cACEyG,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,yCAAyC;cACrD/D,KAAK,EAAE3B,OAAO,CAACE,MAAO;cACtByF,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,QAAQ,EAAEsB,CAAC,CAACC,MAAM,CAAClE,KAAK;YAAE;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtG,OAAA;YAAAsG,QAAA,EAAO;UAAM;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrBnG,OAAA;YACE2C,KAAK,EAAE3B,OAAO,CAACG,MAAO;YACtBwF,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,QAAQ,EAAEsB,CAAC,CAACC,MAAM,CAAClE,KAAK,CAAE;YAAA2D,QAAA,gBAE9DtG,OAAA;cAAQ2C,KAAK,EAAC,EAAE;cAAA2D,QAAA,EAAC;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACpC1E,aAAa,CAACE,OAAO,CAACmF,GAAG,CAAC3F,MAAM,iBAC/BnB,OAAA;cAAqB2C,KAAK,EAAExB,MAAO;cAAAmF,QAAA,EAAEnF;YAAM,GAA9BA,MAAM;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiC,CACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtG,OAAA;YAAAsG,QAAA,EAAO;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1BnG,OAAA;YACE2C,KAAK,EAAE3B,OAAO,CAACI,UAAW;YAC1BuF,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,YAAY,EAAEsB,CAAC,CAACC,MAAM,CAAClE,KAAK,CAAE;YAAA2D,QAAA,gBAElEtG,OAAA;cAAQ2C,KAAK,EAAC,EAAE;cAAA2D,QAAA,EAAC;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClC1E,aAAa,CAACG,WAAW,CAACkF,GAAG,CAACL,IAAI,iBACjCzG,OAAA;cAAmB2C,KAAK,EAAE8D,IAAK;cAAAH,QAAA,EAAEG;YAAI,GAAxBA,IAAI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA6B,CAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtG,OAAA;YAAAsG,QAAA,EAAO;UAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvBnG,OAAA;YACE2C,KAAK,EAAE3B,OAAO,CAACK,QAAS;YACxBsF,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAAClE,KAAK,CAAE;YAAA2D,QAAA,gBAEhEtG,OAAA;cAAQ2C,KAAK,EAAC,EAAE;cAAA2D,QAAA,EAAC;YAAc;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvC1E,aAAa,CAACI,UAAU,CAACiF,GAAG,CAACzF,QAAQ,iBACpCrB,OAAA;cAAuB2C,KAAK,EAAEtB,QAAS;cAAAiF,QAAA,EAAEjF;YAAQ,GAApCA,QAAQ;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnG,OAAA;QAAKqG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtG,OAAA;YAAAsG,QAAA,EAAO;UAAU;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzBnG,OAAA;YACEyG,IAAI,EAAC,gBAAgB;YACrB9D,KAAK,EAAE3B,OAAO,CAACM,SAAU;YACzBqF,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,WAAW,EAAEsB,CAAC,CAACC,MAAM,CAAClE,KAAK;UAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnG,OAAA;UAAKqG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtG,OAAA;YAAAsG,QAAA,EAAO;UAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvBnG,OAAA;YACEyG,IAAI,EAAC,gBAAgB;YACrB9D,KAAK,EAAE3B,OAAO,CAACO,OAAQ;YACvBoF,QAAQ,EAAGC,CAAC,IAAKtB,kBAAkB,CAAC,SAAS,EAAEsB,CAAC,CAACC,MAAM,CAAClE,KAAK;UAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnG,OAAA;UAAKqG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtG,OAAA;YAAQqG,SAAS,EAAC,iBAAiB;YAACE,OAAO,EAAEf,YAAa;YAAAc,QAAA,gBACxDtG,OAAA,CAACR,UAAU;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnG,OAAA;YAAQqG,SAAS,EAAC,mBAAmB;YAACE,OAAO,EAAEd,YAAa;YAAAa,QAAA,EAAC;UAE7D;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL3F,KAAK,iBACJR,OAAA;MAAKqG,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BtG,OAAA,CAACH,SAAS;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbnG,OAAA;QAAAsG,QAAA,EAAO9F;MAAK;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACnB3F,KAAK,CAAC4C,QAAQ,CAAC,eAAe,CAAC,iBAC9BpD,OAAA;QAAMqG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAkB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAC3D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDnG,OAAA;MAAKqG,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvCtG,OAAA;QAAOqG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC/BtG,OAAA;UAAAsG,QAAA,eACEtG,OAAA;YAAAsG,QAAA,gBACEtG,OAAA;cAAAsG,QAAA,EAAI;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBnG,OAAA;cAAAsG,QAAA,EAAI;YAAI;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbnG,OAAA;cAAAsG,QAAA,EAAI;YAAM;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfnG,OAAA;cAAAsG,QAAA,EAAI;YAAM;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACfnG,OAAA;cAAAsG,QAAA,EAAI;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBnG,OAAA;cAAAsG,QAAA,EAAI;YAAQ;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBnG,OAAA;cAAAsG,QAAA,EAAI;YAAU;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBnG,OAAA;cAAAsG,QAAA,EAAI;YAAQ;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRnG,OAAA;UAAAsG,QAAA,EACGlG,IAAI,CAAC0G,GAAG,CAACxD,GAAG,iBACXtD,OAAA;YAAoBqG,SAAS,EAAE,YAAY/C,GAAG,CAACyB,QAAQ,CAACgC,WAAW,CAAC,CAAC,EAAG;YAAAT,QAAA,gBACtEtG,OAAA;cAAIqG,SAAS,EAAC,WAAW;cAAAC,QAAA,EACtBX,UAAU,CAACrC,GAAG,CAAC0B,SAAS;YAAC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACLnG,OAAA;cAAIqG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBtG,OAAA;gBAAKqG,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhD,GAAG,CAACoB,QAAQ,IAAI;cAAQ;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DnG,OAAA;gBAAKqG,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhD,GAAG,CAACsB;cAAQ;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACLnG,OAAA;cAAIqG,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACpBtG,OAAA;gBAAMqG,SAAS,EAAE,gBAAgB/C,GAAG,CAACe,MAAM,CAAC0C,WAAW,CAAC,CAAC,EAAG;gBAAAT,QAAA,EACzDhD,GAAG,CAACe;cAAM;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLnG,OAAA;cAAIqG,SAAS,EAAC,QAAQ;cAAAC,QAAA,EAAEhD,GAAG,CAACuB;YAAU;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5CnG,OAAA;cAAIqG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEhD,GAAG,CAACwB;YAAW;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClDnG,OAAA;cAAIqG,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBtG,OAAA;gBAAKqG,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAChCR,eAAe,CAACxC,GAAG,CAACyB,QAAQ,CAAC,eAC9B/E,OAAA;kBAAAsG,QAAA,EAAOhD,GAAG,CAACyB;gBAAQ;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLnG,OAAA;cAAIqG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEhD,GAAG,CAAC6B,SAAS,IAAI;YAAG;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtDnG,OAAA;cAAIqG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEhD,GAAG,CAAC8B,QAAQ,GAAG,GAAG9B,GAAG,CAAC8B,QAAQ,IAAI,GAAG;YAAG;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,GAtBjE7C,GAAG,CAACmB,KAAK;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuBd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGLzF,UAAU,CAACG,UAAU,GAAG,CAAC,iBACxBb,OAAA;MAAKqG,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBtG,OAAA;QACEqG,SAAS,EAAC,mBAAmB;QAC7BG,QAAQ,EAAE9F,UAAU,CAACE,WAAW,KAAK,CAAE;QACvC2F,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACtB,UAAU,CAACE,WAAW,GAAG,CAAC,CAAE;QAAA0F,QAAA,EAC7D;MAED;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETnG,OAAA;QAAMqG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,OAC3B,EAAC5F,UAAU,CAACE,WAAW,EAAC,MAAI,EAACF,UAAU,CAACG,UAAU,EAAC,GACvD,EAACH,UAAU,CAACI,UAAU,EAAC,eAC1B;MAAA;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPnG,OAAA;QACEqG,SAAS,EAAC,mBAAmB;QAC7BG,QAAQ,EAAE9F,UAAU,CAACE,WAAW,KAAKF,UAAU,CAACG,UAAW;QAC3D0F,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACtB,UAAU,CAACE,WAAW,GAAG,CAAC,CAAE;QAAA0F,QAAA,EAC7D;MAED;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjG,EAAA,CAjfID,YAAY;EAAA,QACCZ,OAAO;AAAA;AAAA2H,EAAA,GADpB/G,YAAY;AAmflB,eAAeA,YAAY;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}