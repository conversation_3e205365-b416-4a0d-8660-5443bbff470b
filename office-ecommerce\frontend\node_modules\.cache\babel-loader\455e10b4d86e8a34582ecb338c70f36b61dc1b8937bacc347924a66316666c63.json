{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Plane, Vector3, Matrix4, Vector4, Perspective<PERSON>amera, WebGLRenderTarget, DepthTexture, DepthFormat, UnsignedShortType, LinearFilter, HalfFloatType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { BlurPass } from '../materials/BlurPass.js';\nimport { MeshReflectorMaterial as MeshReflectorMaterial$1 } from '../materials/MeshReflectorMaterial.js';\nextend({\n  MeshReflectorMaterialImpl: MeshReflectorMaterial$1\n});\nconst MeshReflectorMaterial = /*#__PURE__*/React.forwardRef(({\n  mixBlur = 0,\n  mixStrength = 1,\n  resolution = 256,\n  blur = [0, 0],\n  minDepthThreshold = 0.9,\n  maxDepthThreshold = 1,\n  depthScale = 0,\n  depthToBlurRatioBias = 0.25,\n  mirror = 0,\n  distortion = 1,\n  mixContrast = 1,\n  distortionMap,\n  reflectorOffset = 0,\n  ...props\n}, ref) => {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  blur = Array.isArray(blur) ? blur : [blur, blur];\n  const hasBlur = blur[0] + blur[1] > 0;\n  const materialRef = React.useRef(null);\n  const [reflectorPlane] = React.useState(() => new Plane());\n  const [normal] = React.useState(() => new Vector3());\n  const [reflectorWorldPosition] = React.useState(() => new Vector3());\n  const [cameraWorldPosition] = React.useState(() => new Vector3());\n  const [rotationMatrix] = React.useState(() => new Matrix4());\n  const [lookAtPosition] = React.useState(() => new Vector3(0, 0, -1));\n  const [clipPlane] = React.useState(() => new Vector4());\n  const [view] = React.useState(() => new Vector3());\n  const [target] = React.useState(() => new Vector3());\n  const [q] = React.useState(() => new Vector4());\n  const [textureMatrix] = React.useState(() => new Matrix4());\n  const [virtualCamera] = React.useState(() => new PerspectiveCamera());\n  const beforeRender = React.useCallback(() => {\n    var _materialRef$current;\n\n    // TODO: As of R3f 7-8 this should be __r3f.parent\n    const parent = materialRef.current.parent || ((_materialRef$current = materialRef.current) == null ? void 0 : _materialRef$current.__r3f.parent);\n    if (!parent) return;\n    reflectorWorldPosition.setFromMatrixPosition(parent.matrixWorld);\n    cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n    rotationMatrix.extractRotation(parent.matrixWorld);\n    normal.set(0, 0, 1);\n    normal.applyMatrix4(rotationMatrix);\n    reflectorWorldPosition.addScaledVector(normal, reflectorOffset);\n    view.subVectors(reflectorWorldPosition, cameraWorldPosition); // Avoid rendering when reflector is facing away\n\n    if (view.dot(normal) > 0) return;\n    view.reflect(normal).negate();\n    view.add(reflectorWorldPosition);\n    rotationMatrix.extractRotation(camera.matrixWorld);\n    lookAtPosition.set(0, 0, -1);\n    lookAtPosition.applyMatrix4(rotationMatrix);\n    lookAtPosition.add(cameraWorldPosition);\n    target.subVectors(reflectorWorldPosition, lookAtPosition);\n    target.reflect(normal).negate();\n    target.add(reflectorWorldPosition);\n    virtualCamera.position.copy(view);\n    virtualCamera.up.set(0, 1, 0);\n    virtualCamera.up.applyMatrix4(rotationMatrix);\n    virtualCamera.up.reflect(normal);\n    virtualCamera.lookAt(target);\n    virtualCamera.far = camera.far; // Used in WebGLBackground\n\n    virtualCamera.updateMatrixWorld();\n    virtualCamera.projectionMatrix.copy(camera.projectionMatrix); // Update the texture matrix\n\n    textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0);\n    textureMatrix.multiply(virtualCamera.projectionMatrix);\n    textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n    textureMatrix.multiply(parent.matrixWorld); // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n    // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n\n    reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n    reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n    clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n    const projectionMatrix = virtualCamera.projectionMatrix;\n    q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n    q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n    q.z = -1.0;\n    q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]; // Calculate the scaled plane vector\n\n    clipPlane.multiplyScalar(2.0 / clipPlane.dot(q)); // Replacing the third row of the projection matrix\n\n    projectionMatrix.elements[2] = clipPlane.x;\n    projectionMatrix.elements[6] = clipPlane.y;\n    projectionMatrix.elements[10] = clipPlane.z + 1.0;\n    projectionMatrix.elements[14] = clipPlane.w;\n  }, [camera, reflectorOffset]);\n  const [fbo1, fbo2, blurpass, reflectorProps] = React.useMemo(() => {\n    const parameters = {\n      minFilter: LinearFilter,\n      magFilter: LinearFilter,\n      type: HalfFloatType\n    };\n    const fbo1 = new WebGLRenderTarget(resolution, resolution, parameters);\n    fbo1.depthBuffer = true;\n    fbo1.depthTexture = new DepthTexture(resolution, resolution);\n    fbo1.depthTexture.format = DepthFormat;\n    fbo1.depthTexture.type = UnsignedShortType;\n    const fbo2 = new WebGLRenderTarget(resolution, resolution, parameters);\n    const blurpass = new BlurPass({\n      gl,\n      resolution,\n      width: blur[0],\n      height: blur[1],\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias\n    });\n    const reflectorProps = {\n      mirror,\n      textureMatrix,\n      mixBlur,\n      tDiffuse: fbo1.texture,\n      tDepth: fbo1.depthTexture,\n      tDiffuseBlur: fbo2.texture,\n      hasBlur,\n      mixStrength,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias,\n      distortion,\n      distortionMap,\n      mixContrast,\n      'defines-USE_BLUR': hasBlur ? '' : undefined,\n      'defines-USE_DEPTH': depthScale > 0 ? '' : undefined,\n      'defines-USE_DISTORTION': distortionMap ? '' : undefined\n    };\n    return [fbo1, fbo2, blurpass, reflectorProps];\n  }, [gl, blur, textureMatrix, resolution, mirror, hasBlur, mixBlur, mixStrength, minDepthThreshold, maxDepthThreshold, depthScale, depthToBlurRatioBias, distortion, distortionMap, mixContrast]);\n  useFrame(() => {\n    var _materialRef$current2;\n\n    // TODO: As of R3f 7-8 this should be __r3f.parent\n    const parent = materialRef.current.parent || ((_materialRef$current2 = materialRef.current) == null ? void 0 : _materialRef$current2.__r3f.parent);\n    if (!parent) return;\n    parent.visible = false;\n    const currentXrEnabled = gl.xr.enabled;\n    const currentShadowAutoUpdate = gl.shadowMap.autoUpdate;\n    beforeRender();\n    gl.xr.enabled = false;\n    gl.shadowMap.autoUpdate = false;\n    gl.setRenderTarget(fbo1);\n    gl.state.buffers.depth.setMask(true);\n    if (!gl.autoClear) gl.clear();\n    gl.render(scene, virtualCamera);\n    if (hasBlur) blurpass.render(gl, fbo1, fbo2);\n    gl.xr.enabled = currentXrEnabled;\n    gl.shadowMap.autoUpdate = currentShadowAutoUpdate;\n    parent.visible = true;\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(\"meshReflectorMaterialImpl\", _extends({\n    attach: \"material\" // Defines can't be updated dynamically, so we need to recreate the material\n    ,\n\n    key: 'key' + reflectorProps['defines-USE_BLUR'] + reflectorProps['defines-USE_DEPTH'] + reflectorProps['defines-USE_DISTORTION'],\n    ref: mergeRefs([materialRef, ref])\n  }, reflectorProps, props));\n});\nexport { MeshReflectorMaterial };", "map": {"version": 3, "names": ["_extends", "React", "Plane", "Vector3", "Matrix4", "Vector4", "PerspectiveCamera", "WebGLRenderTarget", "DepthTexture", "DepthFormat", "UnsignedShortType", "LinearFilter", "HalfFloatType", "extend", "useThree", "useFrame", "mergeRefs", "BlurPass", "MeshReflectorMaterial", "MeshReflectorMaterial$1", "MeshReflectorMaterialImpl", "forwardRef", "mixBlur", "mixStrength", "resolution", "blur", "minDepthThr<PERSON>old", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depthScale", "depthToBlurRatioBias", "mirror", "distortion", "mixContrast", "distortionMap", "reflectorOffset", "props", "ref", "gl", "camera", "scene", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "materialRef", "useRef", "reflectorPlane", "useState", "normal", "reflectorWorldPosition", "cameraWorldPosition", "rotationMatrix", "lookAtPosition", "clipPlane", "view", "target", "q", "textureMatrix", "virtualCamera", "beforeRender", "useCallback", "_materialRef$current", "parent", "current", "__r3f", "setFromMatrixPosition", "matrixWorld", "extractRotation", "set", "applyMatrix4", "addScaledVector", "subVectors", "dot", "reflect", "negate", "add", "position", "copy", "up", "lookAt", "far", "updateMatrixWorld", "projectionMatrix", "multiply", "matrixWorldInverse", "setFromNormalAndCoplanarPoint", "x", "y", "z", "constant", "Math", "sign", "elements", "w", "multiplyScalar", "fbo1", "fbo2", "blurpass", "reflectorProps", "useMemo", "parameters", "minFilter", "magFilter", "type", "depthBuffer", "depthTexture", "format", "width", "height", "tDiffuse", "texture", "tD<PERSON>h", "tDiffuseBlur", "undefined", "_materialRef$current2", "visible", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "buffers", "depth", "setMask", "autoClear", "clear", "render", "createElement", "attach", "key"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/MeshReflectorMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Plane, Vector3, Matrix4, Vector4, Perspective<PERSON>amera, WebGLRenderTarget, DepthTexture, DepthFormat, UnsignedShortType, LinearFilter, HalfFloatType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { BlurPass } from '../materials/BlurPass.js';\nimport { MeshReflectorMaterial as MeshReflectorMaterial$1 } from '../materials/MeshReflectorMaterial.js';\n\nextend({\n  MeshReflectorMaterialImpl: MeshReflectorMaterial$1\n});\nconst MeshReflectorMaterial = /*#__PURE__*/React.forwardRef(({\n  mixBlur = 0,\n  mixStrength = 1,\n  resolution = 256,\n  blur = [0, 0],\n  minDepthThreshold = 0.9,\n  maxDepthThreshold = 1,\n  depthScale = 0,\n  depthToBlurRatioBias = 0.25,\n  mirror = 0,\n  distortion = 1,\n  mixContrast = 1,\n  distortionMap,\n  reflectorOffset = 0,\n  ...props\n}, ref) => {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  blur = Array.isArray(blur) ? blur : [blur, blur];\n  const hasBlur = blur[0] + blur[1] > 0;\n  const materialRef = React.useRef(null);\n  const [reflectorPlane] = React.useState(() => new Plane());\n  const [normal] = React.useState(() => new Vector3());\n  const [reflectorWorldPosition] = React.useState(() => new Vector3());\n  const [cameraWorldPosition] = React.useState(() => new Vector3());\n  const [rotationMatrix] = React.useState(() => new Matrix4());\n  const [lookAtPosition] = React.useState(() => new Vector3(0, 0, -1));\n  const [clipPlane] = React.useState(() => new Vector4());\n  const [view] = React.useState(() => new Vector3());\n  const [target] = React.useState(() => new Vector3());\n  const [q] = React.useState(() => new Vector4());\n  const [textureMatrix] = React.useState(() => new Matrix4());\n  const [virtualCamera] = React.useState(() => new PerspectiveCamera());\n  const beforeRender = React.useCallback(() => {\n    var _materialRef$current;\n\n    // TODO: As of R3f 7-8 this should be __r3f.parent\n    const parent = materialRef.current.parent || ((_materialRef$current = materialRef.current) == null ? void 0 : _materialRef$current.__r3f.parent);\n    if (!parent) return;\n    reflectorWorldPosition.setFromMatrixPosition(parent.matrixWorld);\n    cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n    rotationMatrix.extractRotation(parent.matrixWorld);\n    normal.set(0, 0, 1);\n    normal.applyMatrix4(rotationMatrix);\n    reflectorWorldPosition.addScaledVector(normal, reflectorOffset);\n    view.subVectors(reflectorWorldPosition, cameraWorldPosition); // Avoid rendering when reflector is facing away\n\n    if (view.dot(normal) > 0) return;\n    view.reflect(normal).negate();\n    view.add(reflectorWorldPosition);\n    rotationMatrix.extractRotation(camera.matrixWorld);\n    lookAtPosition.set(0, 0, -1);\n    lookAtPosition.applyMatrix4(rotationMatrix);\n    lookAtPosition.add(cameraWorldPosition);\n    target.subVectors(reflectorWorldPosition, lookAtPosition);\n    target.reflect(normal).negate();\n    target.add(reflectorWorldPosition);\n    virtualCamera.position.copy(view);\n    virtualCamera.up.set(0, 1, 0);\n    virtualCamera.up.applyMatrix4(rotationMatrix);\n    virtualCamera.up.reflect(normal);\n    virtualCamera.lookAt(target);\n    virtualCamera.far = camera.far; // Used in WebGLBackground\n\n    virtualCamera.updateMatrixWorld();\n    virtualCamera.projectionMatrix.copy(camera.projectionMatrix); // Update the texture matrix\n\n    textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0);\n    textureMatrix.multiply(virtualCamera.projectionMatrix);\n    textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n    textureMatrix.multiply(parent.matrixWorld); // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n    // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n\n    reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n    reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n    clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n    const projectionMatrix = virtualCamera.projectionMatrix;\n    q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n    q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n    q.z = -1.0;\n    q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]; // Calculate the scaled plane vector\n\n    clipPlane.multiplyScalar(2.0 / clipPlane.dot(q)); // Replacing the third row of the projection matrix\n\n    projectionMatrix.elements[2] = clipPlane.x;\n    projectionMatrix.elements[6] = clipPlane.y;\n    projectionMatrix.elements[10] = clipPlane.z + 1.0;\n    projectionMatrix.elements[14] = clipPlane.w;\n  }, [camera, reflectorOffset]);\n  const [fbo1, fbo2, blurpass, reflectorProps] = React.useMemo(() => {\n    const parameters = {\n      minFilter: LinearFilter,\n      magFilter: LinearFilter,\n      type: HalfFloatType\n    };\n    const fbo1 = new WebGLRenderTarget(resolution, resolution, parameters);\n    fbo1.depthBuffer = true;\n    fbo1.depthTexture = new DepthTexture(resolution, resolution);\n    fbo1.depthTexture.format = DepthFormat;\n    fbo1.depthTexture.type = UnsignedShortType;\n    const fbo2 = new WebGLRenderTarget(resolution, resolution, parameters);\n    const blurpass = new BlurPass({\n      gl,\n      resolution,\n      width: blur[0],\n      height: blur[1],\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias\n    });\n    const reflectorProps = {\n      mirror,\n      textureMatrix,\n      mixBlur,\n      tDiffuse: fbo1.texture,\n      tDepth: fbo1.depthTexture,\n      tDiffuseBlur: fbo2.texture,\n      hasBlur,\n      mixStrength,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias,\n      distortion,\n      distortionMap,\n      mixContrast,\n      'defines-USE_BLUR': hasBlur ? '' : undefined,\n      'defines-USE_DEPTH': depthScale > 0 ? '' : undefined,\n      'defines-USE_DISTORTION': distortionMap ? '' : undefined\n    };\n    return [fbo1, fbo2, blurpass, reflectorProps];\n  }, [gl, blur, textureMatrix, resolution, mirror, hasBlur, mixBlur, mixStrength, minDepthThreshold, maxDepthThreshold, depthScale, depthToBlurRatioBias, distortion, distortionMap, mixContrast]);\n  useFrame(() => {\n    var _materialRef$current2;\n\n    // TODO: As of R3f 7-8 this should be __r3f.parent\n    const parent = materialRef.current.parent || ((_materialRef$current2 = materialRef.current) == null ? void 0 : _materialRef$current2.__r3f.parent);\n    if (!parent) return;\n    parent.visible = false;\n    const currentXrEnabled = gl.xr.enabled;\n    const currentShadowAutoUpdate = gl.shadowMap.autoUpdate;\n    beforeRender();\n    gl.xr.enabled = false;\n    gl.shadowMap.autoUpdate = false;\n    gl.setRenderTarget(fbo1);\n    gl.state.buffers.depth.setMask(true);\n    if (!gl.autoClear) gl.clear();\n    gl.render(scene, virtualCamera);\n    if (hasBlur) blurpass.render(gl, fbo1, fbo2);\n    gl.xr.enabled = currentXrEnabled;\n    gl.shadowMap.autoUpdate = currentShadowAutoUpdate;\n    parent.visible = true;\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(\"meshReflectorMaterialImpl\", _extends({\n    attach: \"material\" // Defines can't be updated dynamically, so we need to recreate the material\n    ,\n    key: 'key' + reflectorProps['defines-USE_BLUR'] + reflectorProps['defines-USE_DEPTH'] + reflectorProps['defines-USE_DISTORTION'],\n    ref: mergeRefs([materialRef, ref])\n  }, reflectorProps, props));\n});\n\nexport { MeshReflectorMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,aAAa,QAAQ,OAAO;AACzK,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,IAAIC,uBAAuB,QAAQ,uCAAuC;AAExGN,MAAM,CAAC;EACLO,yBAAyB,EAAED;AAC7B,CAAC,CAAC;AACF,MAAMD,qBAAqB,GAAG,aAAajB,KAAK,CAACoB,UAAU,CAAC,CAAC;EAC3DC,OAAO,GAAG,CAAC;EACXC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG,GAAG;EAChBC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACbC,iBAAiB,GAAG,GAAG;EACvBC,iBAAiB,GAAG,CAAC;EACrBC,UAAU,GAAG,CAAC;EACdC,oBAAoB,GAAG,IAAI;EAC3BC,MAAM,GAAG,CAAC;EACVC,UAAU,GAAG,CAAC;EACdC,WAAW,GAAG,CAAC;EACfC,aAAa;EACbC,eAAe,GAAG,CAAC;EACnB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,EAAE,GAAGvB,QAAQ,CAAC,CAAC;IACnBuB;EACF,CAAC,KAAKA,EAAE,CAAC;EACT,MAAMC,MAAM,GAAGxB,QAAQ,CAAC,CAAC;IACvBwB;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMC,KAAK,GAAGzB,QAAQ,CAAC,CAAC;IACtByB;EACF,CAAC,KAAKA,KAAK,CAAC;EACZd,IAAI,GAAGe,KAAK,CAACC,OAAO,CAAChB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC;EAChD,MAAMiB,OAAO,GAAGjB,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EACrC,MAAMkB,WAAW,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM,CAACC,cAAc,CAAC,GAAG5C,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI5C,KAAK,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC6C,MAAM,CAAC,GAAG9C,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI3C,OAAO,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC6C,sBAAsB,CAAC,GAAG/C,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI3C,OAAO,CAAC,CAAC,CAAC;EACpE,MAAM,CAAC8C,mBAAmB,CAAC,GAAGhD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI3C,OAAO,CAAC,CAAC,CAAC;EACjE,MAAM,CAAC+C,cAAc,CAAC,GAAGjD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI1C,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC+C,cAAc,CAAC,GAAGlD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI3C,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM,CAACiD,SAAS,CAAC,GAAGnD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAIzC,OAAO,CAAC,CAAC,CAAC;EACvD,MAAM,CAACgD,IAAI,CAAC,GAAGpD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI3C,OAAO,CAAC,CAAC,CAAC;EAClD,MAAM,CAACmD,MAAM,CAAC,GAAGrD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI3C,OAAO,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoD,CAAC,CAAC,GAAGtD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAIzC,OAAO,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmD,aAAa,CAAC,GAAGvD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAI1C,OAAO,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACqD,aAAa,CAAC,GAAGxD,KAAK,CAAC6C,QAAQ,CAAC,MAAM,IAAIxC,iBAAiB,CAAC,CAAC,CAAC;EACrE,MAAMoD,YAAY,GAAGzD,KAAK,CAAC0D,WAAW,CAAC,MAAM;IAC3C,IAAIC,oBAAoB;;IAExB;IACA,MAAMC,MAAM,GAAGlB,WAAW,CAACmB,OAAO,CAACD,MAAM,KAAK,CAACD,oBAAoB,GAAGjB,WAAW,CAACmB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,oBAAoB,CAACG,KAAK,CAACF,MAAM,CAAC;IAChJ,IAAI,CAACA,MAAM,EAAE;IACbb,sBAAsB,CAACgB,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;IAChEhB,mBAAmB,CAACe,qBAAqB,CAAC1B,MAAM,CAAC2B,WAAW,CAAC;IAC7Df,cAAc,CAACgB,eAAe,CAACL,MAAM,CAACI,WAAW,CAAC;IAClDlB,MAAM,CAACoB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnBpB,MAAM,CAACqB,YAAY,CAAClB,cAAc,CAAC;IACnCF,sBAAsB,CAACqB,eAAe,CAACtB,MAAM,EAAEb,eAAe,CAAC;IAC/DmB,IAAI,CAACiB,UAAU,CAACtB,sBAAsB,EAAEC,mBAAmB,CAAC,CAAC,CAAC;;IAE9D,IAAII,IAAI,CAACkB,GAAG,CAACxB,MAAM,CAAC,GAAG,CAAC,EAAE;IAC1BM,IAAI,CAACmB,OAAO,CAACzB,MAAM,CAAC,CAAC0B,MAAM,CAAC,CAAC;IAC7BpB,IAAI,CAACqB,GAAG,CAAC1B,sBAAsB,CAAC;IAChCE,cAAc,CAACgB,eAAe,CAAC5B,MAAM,CAAC2B,WAAW,CAAC;IAClDd,cAAc,CAACgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5BhB,cAAc,CAACiB,YAAY,CAAClB,cAAc,CAAC;IAC3CC,cAAc,CAACuB,GAAG,CAACzB,mBAAmB,CAAC;IACvCK,MAAM,CAACgB,UAAU,CAACtB,sBAAsB,EAAEG,cAAc,CAAC;IACzDG,MAAM,CAACkB,OAAO,CAACzB,MAAM,CAAC,CAAC0B,MAAM,CAAC,CAAC;IAC/BnB,MAAM,CAACoB,GAAG,CAAC1B,sBAAsB,CAAC;IAClCS,aAAa,CAACkB,QAAQ,CAACC,IAAI,CAACvB,IAAI,CAAC;IACjCI,aAAa,CAACoB,EAAE,CAACV,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7BV,aAAa,CAACoB,EAAE,CAACT,YAAY,CAAClB,cAAc,CAAC;IAC7CO,aAAa,CAACoB,EAAE,CAACL,OAAO,CAACzB,MAAM,CAAC;IAChCU,aAAa,CAACqB,MAAM,CAACxB,MAAM,CAAC;IAC5BG,aAAa,CAACsB,GAAG,GAAGzC,MAAM,CAACyC,GAAG,CAAC,CAAC;;IAEhCtB,aAAa,CAACuB,iBAAiB,CAAC,CAAC;IACjCvB,aAAa,CAACwB,gBAAgB,CAACL,IAAI,CAACtC,MAAM,CAAC2C,gBAAgB,CAAC,CAAC,CAAC;;IAE9DzB,aAAa,CAACW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjGX,aAAa,CAAC0B,QAAQ,CAACzB,aAAa,CAACwB,gBAAgB,CAAC;IACtDzB,aAAa,CAAC0B,QAAQ,CAACzB,aAAa,CAAC0B,kBAAkB,CAAC;IACxD3B,aAAa,CAAC0B,QAAQ,CAACrB,MAAM,CAACI,WAAW,CAAC,CAAC,CAAC;IAC5C;;IAEApB,cAAc,CAACuC,6BAA6B,CAACrC,MAAM,EAAEC,sBAAsB,CAAC;IAC5EH,cAAc,CAACuB,YAAY,CAACX,aAAa,CAAC0B,kBAAkB,CAAC;IAC7D/B,SAAS,CAACe,GAAG,CAACtB,cAAc,CAACE,MAAM,CAACsC,CAAC,EAAExC,cAAc,CAACE,MAAM,CAACuC,CAAC,EAAEzC,cAAc,CAACE,MAAM,CAACwC,CAAC,EAAE1C,cAAc,CAAC2C,QAAQ,CAAC;IACjH,MAAMP,gBAAgB,GAAGxB,aAAa,CAACwB,gBAAgB;IACvD1B,CAAC,CAAC8B,CAAC,GAAG,CAACI,IAAI,CAACC,IAAI,CAACtC,SAAS,CAACiC,CAAC,CAAC,GAAGJ,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC;IAC5FpC,CAAC,CAAC+B,CAAC,GAAG,CAACG,IAAI,CAACC,IAAI,CAACtC,SAAS,CAACkC,CAAC,CAAC,GAAGL,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC;IAC5FpC,CAAC,CAACgC,CAAC,GAAG,CAAC,GAAG;IACVhC,CAAC,CAACqC,CAAC,GAAG,CAAC,GAAG,GAAGX,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAE7EvC,SAAS,CAACyC,cAAc,CAAC,GAAG,GAAGzC,SAAS,CAACmB,GAAG,CAAChB,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElD0B,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,GAAGvC,SAAS,CAACiC,CAAC;IAC1CJ,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,GAAGvC,SAAS,CAACkC,CAAC;IAC1CL,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,GAAGvC,SAAS,CAACmC,CAAC,GAAG,GAAG;IACjDN,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,GAAGvC,SAAS,CAACwC,CAAC;EAC7C,CAAC,EAAE,CAACtD,MAAM,EAAEJ,eAAe,CAAC,CAAC;EAC7B,MAAM,CAAC4D,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,cAAc,CAAC,GAAGhG,KAAK,CAACiG,OAAO,CAAC,MAAM;IACjE,MAAMC,UAAU,GAAG;MACjBC,SAAS,EAAEzF,YAAY;MACvB0F,SAAS,EAAE1F,YAAY;MACvB2F,IAAI,EAAE1F;IACR,CAAC;IACD,MAAMkF,IAAI,GAAG,IAAIvF,iBAAiB,CAACiB,UAAU,EAAEA,UAAU,EAAE2E,UAAU,CAAC;IACtEL,IAAI,CAACS,WAAW,GAAG,IAAI;IACvBT,IAAI,CAACU,YAAY,GAAG,IAAIhG,YAAY,CAACgB,UAAU,EAAEA,UAAU,CAAC;IAC5DsE,IAAI,CAACU,YAAY,CAACC,MAAM,GAAGhG,WAAW;IACtCqF,IAAI,CAACU,YAAY,CAACF,IAAI,GAAG5F,iBAAiB;IAC1C,MAAMqF,IAAI,GAAG,IAAIxF,iBAAiB,CAACiB,UAAU,EAAEA,UAAU,EAAE2E,UAAU,CAAC;IACtE,MAAMH,QAAQ,GAAG,IAAI/E,QAAQ,CAAC;MAC5BoB,EAAE;MACFb,UAAU;MACVkF,KAAK,EAAEjF,IAAI,CAAC,CAAC,CAAC;MACdkF,MAAM,EAAElF,IAAI,CAAC,CAAC,CAAC;MACfC,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVC;IACF,CAAC,CAAC;IACF,MAAMoE,cAAc,GAAG;MACrBnE,MAAM;MACN0B,aAAa;MACblC,OAAO;MACPsF,QAAQ,EAAEd,IAAI,CAACe,OAAO;MACtBC,MAAM,EAAEhB,IAAI,CAACU,YAAY;MACzBO,YAAY,EAAEhB,IAAI,CAACc,OAAO;MAC1BnE,OAAO;MACPnB,WAAW;MACXG,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVC,oBAAoB;MACpBE,UAAU;MACVE,aAAa;MACbD,WAAW;MACX,kBAAkB,EAAEU,OAAO,GAAG,EAAE,GAAGsE,SAAS;MAC5C,mBAAmB,EAAEpF,UAAU,GAAG,CAAC,GAAG,EAAE,GAAGoF,SAAS;MACpD,wBAAwB,EAAE/E,aAAa,GAAG,EAAE,GAAG+E;IACjD,CAAC;IACD,OAAO,CAAClB,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,cAAc,CAAC;EAC/C,CAAC,EAAE,CAAC5D,EAAE,EAAEZ,IAAI,EAAE+B,aAAa,EAAEhC,UAAU,EAAEM,MAAM,EAAEY,OAAO,EAAEpB,OAAO,EAAEC,WAAW,EAAEG,iBAAiB,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,oBAAoB,EAAEE,UAAU,EAAEE,aAAa,EAAED,WAAW,CAAC,CAAC;EAChMjB,QAAQ,CAAC,MAAM;IACb,IAAIkG,qBAAqB;;IAEzB;IACA,MAAMpD,MAAM,GAAGlB,WAAW,CAACmB,OAAO,CAACD,MAAM,KAAK,CAACoD,qBAAqB,GAAGtE,WAAW,CAACmB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmD,qBAAqB,CAAClD,KAAK,CAACF,MAAM,CAAC;IAClJ,IAAI,CAACA,MAAM,EAAE;IACbA,MAAM,CAACqD,OAAO,GAAG,KAAK;IACtB,MAAMC,gBAAgB,GAAG9E,EAAE,CAAC+E,EAAE,CAACC,OAAO;IACtC,MAAMC,uBAAuB,GAAGjF,EAAE,CAACkF,SAAS,CAACC,UAAU;IACvD9D,YAAY,CAAC,CAAC;IACdrB,EAAE,CAAC+E,EAAE,CAACC,OAAO,GAAG,KAAK;IACrBhF,EAAE,CAACkF,SAAS,CAACC,UAAU,GAAG,KAAK;IAC/BnF,EAAE,CAACoF,eAAe,CAAC3B,IAAI,CAAC;IACxBzD,EAAE,CAACqF,KAAK,CAACC,OAAO,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC;IACpC,IAAI,CAACxF,EAAE,CAACyF,SAAS,EAAEzF,EAAE,CAAC0F,KAAK,CAAC,CAAC;IAC7B1F,EAAE,CAAC2F,MAAM,CAACzF,KAAK,EAAEkB,aAAa,CAAC;IAC/B,IAAIf,OAAO,EAAEsD,QAAQ,CAACgC,MAAM,CAAC3F,EAAE,EAAEyD,IAAI,EAAEC,IAAI,CAAC;IAC5C1D,EAAE,CAAC+E,EAAE,CAACC,OAAO,GAAGF,gBAAgB;IAChC9E,EAAE,CAACkF,SAAS,CAACC,UAAU,GAAGF,uBAAuB;IACjDzD,MAAM,CAACqD,OAAO,GAAG,IAAI;IACrB7E,EAAE,CAACoF,eAAe,CAAC,IAAI,CAAC;EAC1B,CAAC,CAAC;EACF,OAAO,aAAaxH,KAAK,CAACgI,aAAa,CAAC,2BAA2B,EAAEjI,QAAQ,CAAC;IAC5EkI,MAAM,EAAE,UAAU,CAAC;IAAA;;IAEnBC,GAAG,EAAE,KAAK,GAAGlC,cAAc,CAAC,kBAAkB,CAAC,GAAGA,cAAc,CAAC,mBAAmB,CAAC,GAAGA,cAAc,CAAC,wBAAwB,CAAC;IAChI7D,GAAG,EAAEpB,SAAS,CAAC,CAAC2B,WAAW,EAAEP,GAAG,CAAC;EACnC,CAAC,EAAE6D,cAAc,EAAE9D,KAAK,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF,SAASjB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}