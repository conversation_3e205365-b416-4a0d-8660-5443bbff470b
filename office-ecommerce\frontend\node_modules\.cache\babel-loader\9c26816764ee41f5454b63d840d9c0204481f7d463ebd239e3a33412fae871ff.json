{"ast": null, "code": "import { Color, ShaderMaterial, UniformsUtils, UniformsLib, BackSide } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nclass OutlineEffect {\n  constructor(renderer, parameters = {}) {\n    this.enabled = true;\n    const defaultThickness = parameters.defaultThickness !== void 0 ? parameters.defaultThickness : 3e-3;\n    const defaultColor = new Color().fromArray(parameters.defaultColor !== void 0 ? parameters.defaultColor : [0, 0, 0]);\n    const defaultAlpha = parameters.defaultAlpha !== void 0 ? parameters.defaultAlpha : 1;\n    const defaultKeepAlive = parameters.defaultKeepAlive !== void 0 ? parameters.defaultKeepAlive : false;\n    const cache = {};\n    const removeThresholdCount = 60;\n    const originalMaterials = {};\n    const originalOnBeforeRenders = {};\n    const uniformsOutline = {\n      outlineThickness: {\n        value: defaultThickness\n      },\n      outlineColor: {\n        value: defaultColor\n      },\n      outlineAlpha: {\n        value: defaultAlpha\n      }\n    };\n    const vertexShader = [\"#include <common>\", \"#include <uv_pars_vertex>\", \"#include <displacementmap_pars_vertex>\", \"#include <fog_pars_vertex>\", \"#include <morphtarget_pars_vertex>\", \"#include <skinning_pars_vertex>\", \"#include <logdepthbuf_pars_vertex>\", \"#include <clipping_planes_pars_vertex>\", \"uniform float outlineThickness;\", \"vec4 calculateOutline( vec4 pos, vec3 normal, vec4 skinned ) {\", \"\tfloat thickness = outlineThickness;\", \"\tconst float ratio = 1.0;\",\n    // TODO: support outline thickness ratio for each vertex\n    \"\tvec4 pos2 = projectionMatrix * modelViewMatrix * vec4( skinned.xyz + normal, 1.0 );\",\n    // NOTE: subtract pos2 from pos because BackSide objectNormal is negative\n    \"\tvec4 norm = normalize( pos - pos2 );\", \"\treturn pos + norm * thickness * pos.w * ratio;\", \"}\", \"void main() {\", \"\t#include <uv_vertex>\", \"\t#include <beginnormal_vertex>\", \"\t#include <morphnormal_vertex>\", \"\t#include <skinbase_vertex>\", \"\t#include <skinnormal_vertex>\", \"\t#include <begin_vertex>\", \"\t#include <morphtarget_vertex>\", \"\t#include <skinning_vertex>\", \"\t#include <displacementmap_vertex>\", \"\t#include <project_vertex>\", \"\tvec3 outlineNormal = - objectNormal;\",\n    // the outline material is always rendered with BackSide\n    \"\tgl_Position = calculateOutline( gl_Position, outlineNormal, vec4( transformed, 1.0 ) );\", \"\t#include <logdepthbuf_vertex>\", \"\t#include <clipping_planes_vertex>\", \"\t#include <fog_vertex>\", \"}\"].join(\"\\n\");\n    const fragmentShader = [\"#include <common>\", \"#include <fog_pars_fragment>\", \"#include <logdepthbuf_pars_fragment>\", \"#include <clipping_planes_pars_fragment>\", \"uniform vec3 outlineColor;\", \"uniform float outlineAlpha;\", \"void main() {\", \"\t#include <clipping_planes_fragment>\", \"\t#include <logdepthbuf_fragment>\", \"\tgl_FragColor = vec4( outlineColor, outlineAlpha );\", \"\t#include <tonemapping_fragment>\", `\t#include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>`, \"\t#include <fog_fragment>\", \"\t#include <premultiplied_alpha_fragment>\", \"}\"].join(\"\\n\");\n    function createMaterial() {\n      return new ShaderMaterial({\n        type: \"OutlineEffect\",\n        uniforms: UniformsUtils.merge([UniformsLib[\"fog\"], UniformsLib[\"displacementmap\"], uniformsOutline]),\n        vertexShader,\n        fragmentShader,\n        side: BackSide\n      });\n    }\n    function getOutlineMaterialFromCache(originalMaterial) {\n      let data = cache[originalMaterial.uuid];\n      if (data === void 0) {\n        data = {\n          material: createMaterial(),\n          used: true,\n          keepAlive: defaultKeepAlive,\n          count: 0\n        };\n        cache[originalMaterial.uuid] = data;\n      }\n      data.used = true;\n      return data.material;\n    }\n    function getOutlineMaterial(originalMaterial) {\n      const outlineMaterial = getOutlineMaterialFromCache(originalMaterial);\n      originalMaterials[outlineMaterial.uuid] = originalMaterial;\n      updateOutlineMaterial(outlineMaterial, originalMaterial);\n      return outlineMaterial;\n    }\n    function isCompatible(object) {\n      const geometry = object.geometry;\n      const hasNormals = geometry !== void 0 && geometry.attributes.normal !== void 0;\n      return object.isMesh === true && object.material !== void 0 && hasNormals === true;\n    }\n    function setOutlineMaterial(object) {\n      if (isCompatible(object) === false) return;\n      if (Array.isArray(object.material)) {\n        for (let i = 0, il = object.material.length; i < il; i++) {\n          object.material[i] = getOutlineMaterial(object.material[i]);\n        }\n      } else {\n        object.material = getOutlineMaterial(object.material);\n      }\n      originalOnBeforeRenders[object.uuid] = object.onBeforeRender;\n      object.onBeforeRender = onBeforeRender;\n    }\n    function restoreOriginalMaterial(object) {\n      if (isCompatible(object) === false) return;\n      if (Array.isArray(object.material)) {\n        for (let i = 0, il = object.material.length; i < il; i++) {\n          object.material[i] = originalMaterials[object.material[i].uuid];\n        }\n      } else {\n        object.material = originalMaterials[object.material.uuid];\n      }\n      object.onBeforeRender = originalOnBeforeRenders[object.uuid];\n    }\n    function onBeforeRender(renderer2, scene, camera, geometry, material) {\n      const originalMaterial = originalMaterials[material.uuid];\n      if (originalMaterial === void 0) return;\n      updateUniforms(material, originalMaterial);\n    }\n    function updateUniforms(material, originalMaterial) {\n      const outlineParameters = originalMaterial.userData.outlineParameters;\n      material.uniforms.outlineAlpha.value = originalMaterial.opacity;\n      if (outlineParameters !== void 0) {\n        if (outlineParameters.thickness !== void 0) material.uniforms.outlineThickness.value = outlineParameters.thickness;\n        if (outlineParameters.color !== void 0) material.uniforms.outlineColor.value.fromArray(outlineParameters.color);\n        if (outlineParameters.alpha !== void 0) material.uniforms.outlineAlpha.value = outlineParameters.alpha;\n      }\n      if (originalMaterial.displacementMap) {\n        material.uniforms.displacementMap.value = originalMaterial.displacementMap;\n        material.uniforms.displacementScale.value = originalMaterial.displacementScale;\n        material.uniforms.displacementBias.value = originalMaterial.displacementBias;\n      }\n    }\n    function updateOutlineMaterial(material, originalMaterial) {\n      if (material.name === \"invisible\") return;\n      const outlineParameters = originalMaterial.userData.outlineParameters;\n      material.fog = originalMaterial.fog;\n      material.toneMapped = originalMaterial.toneMapped;\n      material.premultipliedAlpha = originalMaterial.premultipliedAlpha;\n      material.displacementMap = originalMaterial.displacementMap;\n      if (outlineParameters !== void 0) {\n        if (originalMaterial.visible === false) {\n          material.visible = false;\n        } else {\n          material.visible = outlineParameters.visible !== void 0 ? outlineParameters.visible : true;\n        }\n        material.transparent = outlineParameters.alpha !== void 0 && outlineParameters.alpha < 1 ? true : originalMaterial.transparent;\n        if (outlineParameters.keepAlive !== void 0) cache[originalMaterial.uuid].keepAlive = outlineParameters.keepAlive;\n      } else {\n        material.transparent = originalMaterial.transparent;\n        material.visible = originalMaterial.visible;\n      }\n      if (originalMaterial.wireframe === true || originalMaterial.depthTest === false) material.visible = false;\n      if (originalMaterial.clippingPlanes) {\n        material.clipping = true;\n        material.clippingPlanes = originalMaterial.clippingPlanes;\n        material.clipIntersection = originalMaterial.clipIntersection;\n        material.clipShadows = originalMaterial.clipShadows;\n      }\n      material.version = originalMaterial.version;\n    }\n    function cleanupCache() {\n      let keys;\n      keys = Object.keys(originalMaterials);\n      for (let i = 0, il = keys.length; i < il; i++) {\n        originalMaterials[keys[i]] = void 0;\n      }\n      keys = Object.keys(originalOnBeforeRenders);\n      for (let i = 0, il = keys.length; i < il; i++) {\n        originalOnBeforeRenders[keys[i]] = void 0;\n      }\n      keys = Object.keys(cache);\n      for (let i = 0, il = keys.length; i < il; i++) {\n        const key = keys[i];\n        if (cache[key].used === false) {\n          cache[key].count++;\n          if (cache[key].keepAlive === false && cache[key].count > removeThresholdCount) {\n            delete cache[key];\n          }\n        } else {\n          cache[key].used = false;\n          cache[key].count = 0;\n        }\n      }\n    }\n    this.render = function (scene, camera) {\n      if (this.enabled === false) {\n        renderer.render(scene, camera);\n        return;\n      }\n      const currentAutoClear = renderer.autoClear;\n      renderer.autoClear = this.autoClear;\n      renderer.render(scene, camera);\n      renderer.autoClear = currentAutoClear;\n      this.renderOutline(scene, camera);\n    };\n    this.renderOutline = function (scene, camera) {\n      const currentAutoClear = renderer.autoClear;\n      const currentSceneAutoUpdate = scene.matrixWorldAutoUpdate;\n      const currentSceneBackground = scene.background;\n      const currentShadowMapEnabled = renderer.shadowMap.enabled;\n      scene.matrixWorldAutoUpdate = false;\n      scene.background = null;\n      renderer.autoClear = false;\n      renderer.shadowMap.enabled = false;\n      scene.traverse(setOutlineMaterial);\n      renderer.render(scene, camera);\n      scene.traverse(restoreOriginalMaterial);\n      cleanupCache();\n      scene.matrixWorldAutoUpdate = currentSceneAutoUpdate;\n      scene.background = currentSceneBackground;\n      renderer.autoClear = currentAutoClear;\n      renderer.shadowMap.enabled = currentShadowMapEnabled;\n    };\n    this.autoClear = renderer.autoClear;\n    this.domElement = renderer.domElement;\n    this.shadowMap = renderer.shadowMap;\n    this.clear = function (color, depth, stencil) {\n      renderer.clear(color, depth, stencil);\n    };\n    this.getPixelRatio = function () {\n      return renderer.getPixelRatio();\n    };\n    this.setPixelRatio = function (value) {\n      renderer.setPixelRatio(value);\n    };\n    this.getSize = function (target) {\n      return renderer.getSize(target);\n    };\n    this.setSize = function (width, height, updateStyle) {\n      renderer.setSize(width, height, updateStyle);\n    };\n    this.setViewport = function (x, y, width, height) {\n      renderer.setViewport(x, y, width, height);\n    };\n    this.setScissor = function (x, y, width, height) {\n      renderer.setScissor(x, y, width, height);\n    };\n    this.setScissorTest = function (boolean) {\n      renderer.setScissorTest(boolean);\n    };\n    this.setRenderTarget = function (renderTarget) {\n      renderer.setRenderTarget(renderTarget);\n    };\n  }\n}\nexport { OutlineEffect };", "map": {"version": 3, "names": ["OutlineEffect", "constructor", "renderer", "parameters", "enabled", "defaultThickness", "defaultColor", "Color", "fromArray", "defaultAlpha", "defaultKeepAlive", "cache", "removeThresholdCount", "originalMaterials", "originalOnBeforeRenders", "uniformsOutline", "outlineThickness", "value", "outlineColor", "outlineAlpha", "vertexShader", "join", "fragmentShader", "version", "createMaterial", "ShaderMaterial", "type", "uniforms", "UniformsUtils", "merge", "UniformsLib", "side", "BackSide", "getOutlineMaterialFromCache", "originalMaterial", "data", "uuid", "material", "used", "keepAlive", "count", "getOutlineMaterial", "outlineMaterial", "updateOutlineMaterial", "isCompatible", "object", "geometry", "hasNormals", "attributes", "normal", "<PERSON><PERSON><PERSON>", "setOutlineMaterial", "Array", "isArray", "i", "il", "length", "onBeforeRender", "restoreOriginalMaterial", "renderer2", "scene", "camera", "updateUniforms", "outlineParameters", "userData", "opacity", "thickness", "color", "alpha", "displacementMap", "displacementScale", "displacementBias", "name", "fog", "toneMapped", "premultipliedAlpha", "visible", "transparent", "wireframe", "depthTest", "clippingPlanes", "clipping", "clipIntersection", "clipShadows", "cleanupCache", "keys", "Object", "key", "render", "currentAutoClear", "autoClear", "renderOutline", "currentSceneAutoUpdate", "matrixWorldAutoUpdate", "currentSceneBackground", "background", "currentShadowMapEnabled", "shadowMap", "traverse", "dom<PERSON>lement", "clear", "depth", "stencil", "getPixelRatio", "setPixelRatio", "getSize", "target", "setSize", "width", "height", "updateStyle", "setViewport", "x", "y", "set<PERSON><PERSON>sor", "setScissorTest", "boolean", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderTarget"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\effects\\OutlineEffect.js"], "sourcesContent": ["import { BackSide, Color, ShaderMaterial, UniformsLib, UniformsUtils } from 'three'\nimport { version } from '../_polyfill/constants'\n\n/**\n * Reference: https://en.wikipedia.org/wiki/Cel_shading\n *\n * API\n *\n * 1. Traditional\n *\n * const effect = new OutlineEffect( renderer );\n *\n * function render() {\n *\n * \teffect.render( scene, camera );\n *\n * }\n *\n * 2. VR compatible\n *\n * const effect = new OutlineEffect( renderer );\n * let renderingOutline = false;\n *\n * scene.onAfterRender = function () {\n *\n * \tif ( renderingOutline ) return;\n *\n * \trenderingOutline = true;\n *\n * \teffect.renderOutline( scene, camera );\n *\n * \trenderingOutline = false;\n *\n * };\n *\n * function render() {\n *\n * \trenderer.render( scene, camera );\n *\n * }\n *\n * // How to set default outline parameters\n * new OutlineEffect( renderer, {\n * \tdefaultThickness: 0.01,\n * \tdefaultColor: [ 0, 0, 0 ],\n * \tdefaultAlpha: 0.8,\n * \tdefaultKeepAlive: true // keeps outline material in cache even if material is removed from scene\n * } );\n *\n * // How to set outline parameters for each material\n * material.userData.outlineParameters = {\n * \tthickness: 0.01,\n * \tcolor: [ 0, 0, 0 ],\n * \talpha: 0.8,\n * \tvisible: true,\n * \tkeepAlive: true\n * };\n */\n\nclass OutlineEffect {\n  constructor(renderer, parameters = {}) {\n    this.enabled = true\n\n    const defaultThickness = parameters.defaultThickness !== undefined ? parameters.defaultThickness : 0.003\n    const defaultColor = new Color().fromArray(\n      parameters.defaultColor !== undefined ? parameters.defaultColor : [0, 0, 0],\n    )\n    const defaultAlpha = parameters.defaultAlpha !== undefined ? parameters.defaultAlpha : 1.0\n    const defaultKeepAlive = parameters.defaultKeepAlive !== undefined ? parameters.defaultKeepAlive : false\n\n    // object.material.uuid -> outlineMaterial or\n    // object.material[ n ].uuid -> outlineMaterial\n    // save at the outline material creation and release\n    // if it's unused removeThresholdCount frames\n    // unless keepAlive is true.\n    const cache = {}\n\n    const removeThresholdCount = 60\n\n    // outlineMaterial.uuid -> object.material or\n    // outlineMaterial.uuid -> object.material[ n ]\n    // save before render and release after render.\n    const originalMaterials = {}\n\n    // object.uuid -> originalOnBeforeRender\n    // save before render and release after render.\n    const originalOnBeforeRenders = {}\n\n    //this.cache = cache;  // for debug\n\n    const uniformsOutline = {\n      outlineThickness: { value: defaultThickness },\n      outlineColor: { value: defaultColor },\n      outlineAlpha: { value: defaultAlpha },\n    }\n\n    const vertexShader = [\n      '#include <common>',\n      '#include <uv_pars_vertex>',\n      '#include <displacementmap_pars_vertex>',\n      '#include <fog_pars_vertex>',\n      '#include <morphtarget_pars_vertex>',\n      '#include <skinning_pars_vertex>',\n      '#include <logdepthbuf_pars_vertex>',\n      '#include <clipping_planes_pars_vertex>',\n\n      'uniform float outlineThickness;',\n\n      'vec4 calculateOutline( vec4 pos, vec3 normal, vec4 skinned ) {',\n      '\tfloat thickness = outlineThickness;',\n      '\tconst float ratio = 1.0;', // TODO: support outline thickness ratio for each vertex\n      '\tvec4 pos2 = projectionMatrix * modelViewMatrix * vec4( skinned.xyz + normal, 1.0 );',\n      // NOTE: subtract pos2 from pos because BackSide objectNormal is negative\n      '\tvec4 norm = normalize( pos - pos2 );',\n      '\treturn pos + norm * thickness * pos.w * ratio;',\n      '}',\n\n      'void main() {',\n\n      '\t#include <uv_vertex>',\n\n      '\t#include <beginnormal_vertex>',\n      '\t#include <morphnormal_vertex>',\n      '\t#include <skinbase_vertex>',\n      '\t#include <skinnormal_vertex>',\n\n      '\t#include <begin_vertex>',\n      '\t#include <morphtarget_vertex>',\n      '\t#include <skinning_vertex>',\n      '\t#include <displacementmap_vertex>',\n      '\t#include <project_vertex>',\n\n      '\tvec3 outlineNormal = - objectNormal;', // the outline material is always rendered with BackSide\n\n      '\tgl_Position = calculateOutline( gl_Position, outlineNormal, vec4( transformed, 1.0 ) );',\n\n      '\t#include <logdepthbuf_vertex>',\n      '\t#include <clipping_planes_vertex>',\n      '\t#include <fog_vertex>',\n\n      '}',\n    ].join('\\n')\n\n    const fragmentShader = [\n      '#include <common>',\n      '#include <fog_pars_fragment>',\n      '#include <logdepthbuf_pars_fragment>',\n      '#include <clipping_planes_pars_fragment>',\n\n      'uniform vec3 outlineColor;',\n      'uniform float outlineAlpha;',\n\n      'void main() {',\n\n      '\t#include <clipping_planes_fragment>',\n      '\t#include <logdepthbuf_fragment>',\n\n      '\tgl_FragColor = vec4( outlineColor, outlineAlpha );',\n\n      '\t#include <tonemapping_fragment>',\n      `\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>`,\n      '\t#include <fog_fragment>',\n      '\t#include <premultiplied_alpha_fragment>',\n\n      '}',\n    ].join('\\n')\n\n    function createMaterial() {\n      return new ShaderMaterial({\n        type: 'OutlineEffect',\n        uniforms: UniformsUtils.merge([UniformsLib['fog'], UniformsLib['displacementmap'], uniformsOutline]),\n        vertexShader: vertexShader,\n        fragmentShader: fragmentShader,\n        side: BackSide,\n      })\n    }\n\n    function getOutlineMaterialFromCache(originalMaterial) {\n      let data = cache[originalMaterial.uuid]\n\n      if (data === undefined) {\n        data = {\n          material: createMaterial(),\n          used: true,\n          keepAlive: defaultKeepAlive,\n          count: 0,\n        }\n\n        cache[originalMaterial.uuid] = data\n      }\n\n      data.used = true\n\n      return data.material\n    }\n\n    function getOutlineMaterial(originalMaterial) {\n      const outlineMaterial = getOutlineMaterialFromCache(originalMaterial)\n\n      originalMaterials[outlineMaterial.uuid] = originalMaterial\n\n      updateOutlineMaterial(outlineMaterial, originalMaterial)\n\n      return outlineMaterial\n    }\n\n    function isCompatible(object) {\n      const geometry = object.geometry\n      const hasNormals = geometry !== undefined && geometry.attributes.normal !== undefined\n\n      return object.isMesh === true && object.material !== undefined && hasNormals === true\n    }\n\n    function setOutlineMaterial(object) {\n      if (isCompatible(object) === false) return\n\n      if (Array.isArray(object.material)) {\n        for (let i = 0, il = object.material.length; i < il; i++) {\n          object.material[i] = getOutlineMaterial(object.material[i])\n        }\n      } else {\n        object.material = getOutlineMaterial(object.material)\n      }\n\n      originalOnBeforeRenders[object.uuid] = object.onBeforeRender\n      object.onBeforeRender = onBeforeRender\n    }\n\n    function restoreOriginalMaterial(object) {\n      if (isCompatible(object) === false) return\n\n      if (Array.isArray(object.material)) {\n        for (let i = 0, il = object.material.length; i < il; i++) {\n          object.material[i] = originalMaterials[object.material[i].uuid]\n        }\n      } else {\n        object.material = originalMaterials[object.material.uuid]\n      }\n\n      object.onBeforeRender = originalOnBeforeRenders[object.uuid]\n    }\n\n    function onBeforeRender(renderer, scene, camera, geometry, material) {\n      const originalMaterial = originalMaterials[material.uuid]\n\n      // just in case\n      if (originalMaterial === undefined) return\n\n      updateUniforms(material, originalMaterial)\n    }\n\n    function updateUniforms(material, originalMaterial) {\n      const outlineParameters = originalMaterial.userData.outlineParameters\n\n      material.uniforms.outlineAlpha.value = originalMaterial.opacity\n\n      if (outlineParameters !== undefined) {\n        if (outlineParameters.thickness !== undefined)\n          material.uniforms.outlineThickness.value = outlineParameters.thickness\n        if (outlineParameters.color !== undefined)\n          material.uniforms.outlineColor.value.fromArray(outlineParameters.color)\n        if (outlineParameters.alpha !== undefined) material.uniforms.outlineAlpha.value = outlineParameters.alpha\n      }\n\n      if (originalMaterial.displacementMap) {\n        material.uniforms.displacementMap.value = originalMaterial.displacementMap\n        material.uniforms.displacementScale.value = originalMaterial.displacementScale\n        material.uniforms.displacementBias.value = originalMaterial.displacementBias\n      }\n    }\n\n    function updateOutlineMaterial(material, originalMaterial) {\n      if (material.name === 'invisible') return\n\n      const outlineParameters = originalMaterial.userData.outlineParameters\n\n      material.fog = originalMaterial.fog\n      material.toneMapped = originalMaterial.toneMapped\n      material.premultipliedAlpha = originalMaterial.premultipliedAlpha\n      material.displacementMap = originalMaterial.displacementMap\n\n      if (outlineParameters !== undefined) {\n        if (originalMaterial.visible === false) {\n          material.visible = false\n        } else {\n          material.visible = outlineParameters.visible !== undefined ? outlineParameters.visible : true\n        }\n\n        material.transparent =\n          outlineParameters.alpha !== undefined && outlineParameters.alpha < 1.0 ? true : originalMaterial.transparent\n\n        if (outlineParameters.keepAlive !== undefined)\n          cache[originalMaterial.uuid].keepAlive = outlineParameters.keepAlive\n      } else {\n        material.transparent = originalMaterial.transparent\n        material.visible = originalMaterial.visible\n      }\n\n      if (originalMaterial.wireframe === true || originalMaterial.depthTest === false) material.visible = false\n\n      if (originalMaterial.clippingPlanes) {\n        material.clipping = true\n\n        material.clippingPlanes = originalMaterial.clippingPlanes\n        material.clipIntersection = originalMaterial.clipIntersection\n        material.clipShadows = originalMaterial.clipShadows\n      }\n\n      material.version = originalMaterial.version // update outline material if necessary\n    }\n\n    function cleanupCache() {\n      let keys\n\n      // clear originialMaterials\n      keys = Object.keys(originalMaterials)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        originalMaterials[keys[i]] = undefined\n      }\n\n      // clear originalOnBeforeRenders\n      keys = Object.keys(originalOnBeforeRenders)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        originalOnBeforeRenders[keys[i]] = undefined\n      }\n\n      // remove unused outlineMaterial from cache\n      keys = Object.keys(cache)\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        const key = keys[i]\n\n        if (cache[key].used === false) {\n          cache[key].count++\n\n          if (cache[key].keepAlive === false && cache[key].count > removeThresholdCount) {\n            delete cache[key]\n          }\n        } else {\n          cache[key].used = false\n          cache[key].count = 0\n        }\n      }\n    }\n\n    this.render = function (scene, camera) {\n      if (this.enabled === false) {\n        renderer.render(scene, camera)\n        return\n      }\n\n      const currentAutoClear = renderer.autoClear\n      renderer.autoClear = this.autoClear\n\n      renderer.render(scene, camera)\n\n      renderer.autoClear = currentAutoClear\n\n      this.renderOutline(scene, camera)\n    }\n\n    this.renderOutline = function (scene, camera) {\n      const currentAutoClear = renderer.autoClear\n      const currentSceneAutoUpdate = scene.matrixWorldAutoUpdate\n      const currentSceneBackground = scene.background\n      const currentShadowMapEnabled = renderer.shadowMap.enabled\n\n      scene.matrixWorldAutoUpdate = false\n      scene.background = null\n      renderer.autoClear = false\n      renderer.shadowMap.enabled = false\n\n      scene.traverse(setOutlineMaterial)\n\n      renderer.render(scene, camera)\n\n      scene.traverse(restoreOriginalMaterial)\n\n      cleanupCache()\n\n      scene.matrixWorldAutoUpdate = currentSceneAutoUpdate\n      scene.background = currentSceneBackground\n      renderer.autoClear = currentAutoClear\n      renderer.shadowMap.enabled = currentShadowMapEnabled\n    }\n\n    /*\n     * See #9918\n     *\n     * The following property copies and wrapper methods enable\n     * OutlineEffect to be called from other *Effect, like\n     *\n     * effect = new StereoEffect( new OutlineEffect( renderer ) );\n     *\n     * function render () {\n     *\n     * \teffect.render( scene, camera );\n     *\n     * }\n     */\n    this.autoClear = renderer.autoClear\n    this.domElement = renderer.domElement\n    this.shadowMap = renderer.shadowMap\n\n    this.clear = function (color, depth, stencil) {\n      renderer.clear(color, depth, stencil)\n    }\n\n    this.getPixelRatio = function () {\n      return renderer.getPixelRatio()\n    }\n\n    this.setPixelRatio = function (value) {\n      renderer.setPixelRatio(value)\n    }\n\n    this.getSize = function (target) {\n      return renderer.getSize(target)\n    }\n\n    this.setSize = function (width, height, updateStyle) {\n      renderer.setSize(width, height, updateStyle)\n    }\n\n    this.setViewport = function (x, y, width, height) {\n      renderer.setViewport(x, y, width, height)\n    }\n\n    this.setScissor = function (x, y, width, height) {\n      renderer.setScissor(x, y, width, height)\n    }\n\n    this.setScissorTest = function (boolean) {\n      renderer.setScissorTest(boolean)\n    }\n\n    this.setRenderTarget = function (renderTarget) {\n      renderer.setRenderTarget(renderTarget)\n    }\n  }\n}\n\nexport { OutlineEffect }\n"], "mappings": ";;AA2DA,MAAMA,aAAA,CAAc;EAClBC,YAAYC,QAAA,EAAUC,UAAA,GAAa,IAAI;IACrC,KAAKC,OAAA,GAAU;IAEf,MAAMC,gBAAA,GAAmBF,UAAA,CAAWE,gBAAA,KAAqB,SAAYF,UAAA,CAAWE,gBAAA,GAAmB;IACnG,MAAMC,YAAA,GAAe,IAAIC,KAAA,CAAK,EAAGC,SAAA,CAC/BL,UAAA,CAAWG,YAAA,KAAiB,SAAYH,UAAA,CAAWG,YAAA,GAAe,CAAC,GAAG,GAAG,CAAC,CAC3E;IACD,MAAMG,YAAA,GAAeN,UAAA,CAAWM,YAAA,KAAiB,SAAYN,UAAA,CAAWM,YAAA,GAAe;IACvF,MAAMC,gBAAA,GAAmBP,UAAA,CAAWO,gBAAA,KAAqB,SAAYP,UAAA,CAAWO,gBAAA,GAAmB;IAOnG,MAAMC,KAAA,GAAQ,CAAE;IAEhB,MAAMC,oBAAA,GAAuB;IAK7B,MAAMC,iBAAA,GAAoB,CAAE;IAI5B,MAAMC,uBAAA,GAA0B,CAAE;IAIlC,MAAMC,eAAA,GAAkB;MACtBC,gBAAA,EAAkB;QAAEC,KAAA,EAAOZ;MAAkB;MAC7Ca,YAAA,EAAc;QAAED,KAAA,EAAOX;MAAc;MACrCa,YAAA,EAAc;QAAEF,KAAA,EAAOR;MAAc;IACtC;IAED,MAAMW,YAAA,GAAe,CACnB,qBACA,6BACA,0CACA,8BACA,sCACA,mCACA,sCACA,0CAEA,mCAEA,kEACA,wCACA;IAAA;IACA;IAAA;IAEA,yCACA,mDACA,KAEA,iBAEA,yBAEA,kCACA,kCACA,+BACA,iCAEA,4BACA,kCACA,+BACA,sCACA,8BAEA;IAAA;IAEA,4FAEA,kCACA,sCACA,0BAEA,IACN,CAAMC,IAAA,CAAK,IAAI;IAEX,MAAMC,cAAA,GAAiB,CACrB,qBACA,gCACA,wCACA,4CAEA,8BACA,+BAEA,iBAEA,wCACA,oCAEA,uDAEA,oCACA,cAAcC,OAAA,IAAW,MAAM,wBAAwB,yBACvD,4BACA,4CAEA,IACN,CAAMF,IAAA,CAAK,IAAI;IAEX,SAASG,eAAA,EAAiB;MACxB,OAAO,IAAIC,cAAA,CAAe;QACxBC,IAAA,EAAM;QACNC,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAM,CAACC,WAAA,CAAY,KAAK,GAAGA,WAAA,CAAY,iBAAiB,GAAGf,eAAe,CAAC;QACnGK,YAAA;QACAE,cAAA;QACAS,IAAA,EAAMC;MACd,CAAO;IACF;IAED,SAASC,4BAA4BC,gBAAA,EAAkB;MACrD,IAAIC,IAAA,GAAOxB,KAAA,CAAMuB,gBAAA,CAAiBE,IAAI;MAEtC,IAAID,IAAA,KAAS,QAAW;QACtBA,IAAA,GAAO;UACLE,QAAA,EAAUb,cAAA,CAAgB;UAC1Bc,IAAA,EAAM;UACNC,SAAA,EAAW7B,gBAAA;UACX8B,KAAA,EAAO;QACR;QAED7B,KAAA,CAAMuB,gBAAA,CAAiBE,IAAI,IAAID,IAAA;MAChC;MAEDA,IAAA,CAAKG,IAAA,GAAO;MAEZ,OAAOH,IAAA,CAAKE,QAAA;IACb;IAED,SAASI,mBAAmBP,gBAAA,EAAkB;MAC5C,MAAMQ,eAAA,GAAkBT,2BAAA,CAA4BC,gBAAgB;MAEpErB,iBAAA,CAAkB6B,eAAA,CAAgBN,IAAI,IAAIF,gBAAA;MAE1CS,qBAAA,CAAsBD,eAAA,EAAiBR,gBAAgB;MAEvD,OAAOQ,eAAA;IACR;IAED,SAASE,aAAaC,MAAA,EAAQ;MAC5B,MAAMC,QAAA,GAAWD,MAAA,CAAOC,QAAA;MACxB,MAAMC,UAAA,GAAaD,QAAA,KAAa,UAAaA,QAAA,CAASE,UAAA,CAAWC,MAAA,KAAW;MAE5E,OAAOJ,MAAA,CAAOK,MAAA,KAAW,QAAQL,MAAA,CAAOR,QAAA,KAAa,UAAaU,UAAA,KAAe;IAClF;IAED,SAASI,mBAAmBN,MAAA,EAAQ;MAClC,IAAID,YAAA,CAAaC,MAAM,MAAM,OAAO;MAEpC,IAAIO,KAAA,CAAMC,OAAA,CAAQR,MAAA,CAAOR,QAAQ,GAAG;QAClC,SAASiB,CAAA,GAAI,GAAGC,EAAA,GAAKV,MAAA,CAAOR,QAAA,CAASmB,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACxDT,MAAA,CAAOR,QAAA,CAASiB,CAAC,IAAIb,kBAAA,CAAmBI,MAAA,CAAOR,QAAA,CAASiB,CAAC,CAAC;QAC3D;MACT,OAAa;QACLT,MAAA,CAAOR,QAAA,GAAWI,kBAAA,CAAmBI,MAAA,CAAOR,QAAQ;MACrD;MAEDvB,uBAAA,CAAwB+B,MAAA,CAAOT,IAAI,IAAIS,MAAA,CAAOY,cAAA;MAC9CZ,MAAA,CAAOY,cAAA,GAAiBA,cAAA;IACzB;IAED,SAASC,wBAAwBb,MAAA,EAAQ;MACvC,IAAID,YAAA,CAAaC,MAAM,MAAM,OAAO;MAEpC,IAAIO,KAAA,CAAMC,OAAA,CAAQR,MAAA,CAAOR,QAAQ,GAAG;QAClC,SAASiB,CAAA,GAAI,GAAGC,EAAA,GAAKV,MAAA,CAAOR,QAAA,CAASmB,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACxDT,MAAA,CAAOR,QAAA,CAASiB,CAAC,IAAIzC,iBAAA,CAAkBgC,MAAA,CAAOR,QAAA,CAASiB,CAAC,EAAElB,IAAI;QAC/D;MACT,OAAa;QACLS,MAAA,CAAOR,QAAA,GAAWxB,iBAAA,CAAkBgC,MAAA,CAAOR,QAAA,CAASD,IAAI;MACzD;MAEDS,MAAA,CAAOY,cAAA,GAAiB3C,uBAAA,CAAwB+B,MAAA,CAAOT,IAAI;IAC5D;IAED,SAASqB,eAAeE,SAAA,EAAUC,KAAA,EAAOC,MAAA,EAAQf,QAAA,EAAUT,QAAA,EAAU;MACnE,MAAMH,gBAAA,GAAmBrB,iBAAA,CAAkBwB,QAAA,CAASD,IAAI;MAGxD,IAAIF,gBAAA,KAAqB,QAAW;MAEpC4B,cAAA,CAAezB,QAAA,EAAUH,gBAAgB;IAC1C;IAED,SAAS4B,eAAezB,QAAA,EAAUH,gBAAA,EAAkB;MAClD,MAAM6B,iBAAA,GAAoB7B,gBAAA,CAAiB8B,QAAA,CAASD,iBAAA;MAEpD1B,QAAA,CAASV,QAAA,CAASR,YAAA,CAAaF,KAAA,GAAQiB,gBAAA,CAAiB+B,OAAA;MAExD,IAAIF,iBAAA,KAAsB,QAAW;QACnC,IAAIA,iBAAA,CAAkBG,SAAA,KAAc,QAClC7B,QAAA,CAASV,QAAA,CAASX,gBAAA,CAAiBC,KAAA,GAAQ8C,iBAAA,CAAkBG,SAAA;QAC/D,IAAIH,iBAAA,CAAkBI,KAAA,KAAU,QAC9B9B,QAAA,CAASV,QAAA,CAAST,YAAA,CAAaD,KAAA,CAAMT,SAAA,CAAUuD,iBAAA,CAAkBI,KAAK;QACxE,IAAIJ,iBAAA,CAAkBK,KAAA,KAAU,QAAW/B,QAAA,CAASV,QAAA,CAASR,YAAA,CAAaF,KAAA,GAAQ8C,iBAAA,CAAkBK,KAAA;MACrG;MAED,IAAIlC,gBAAA,CAAiBmC,eAAA,EAAiB;QACpChC,QAAA,CAASV,QAAA,CAAS0C,eAAA,CAAgBpD,KAAA,GAAQiB,gBAAA,CAAiBmC,eAAA;QAC3DhC,QAAA,CAASV,QAAA,CAAS2C,iBAAA,CAAkBrD,KAAA,GAAQiB,gBAAA,CAAiBoC,iBAAA;QAC7DjC,QAAA,CAASV,QAAA,CAAS4C,gBAAA,CAAiBtD,KAAA,GAAQiB,gBAAA,CAAiBqC,gBAAA;MAC7D;IACF;IAED,SAAS5B,sBAAsBN,QAAA,EAAUH,gBAAA,EAAkB;MACzD,IAAIG,QAAA,CAASmC,IAAA,KAAS,aAAa;MAEnC,MAAMT,iBAAA,GAAoB7B,gBAAA,CAAiB8B,QAAA,CAASD,iBAAA;MAEpD1B,QAAA,CAASoC,GAAA,GAAMvC,gBAAA,CAAiBuC,GAAA;MAChCpC,QAAA,CAASqC,UAAA,GAAaxC,gBAAA,CAAiBwC,UAAA;MACvCrC,QAAA,CAASsC,kBAAA,GAAqBzC,gBAAA,CAAiByC,kBAAA;MAC/CtC,QAAA,CAASgC,eAAA,GAAkBnC,gBAAA,CAAiBmC,eAAA;MAE5C,IAAIN,iBAAA,KAAsB,QAAW;QACnC,IAAI7B,gBAAA,CAAiB0C,OAAA,KAAY,OAAO;UACtCvC,QAAA,CAASuC,OAAA,GAAU;QAC7B,OAAe;UACLvC,QAAA,CAASuC,OAAA,GAAUb,iBAAA,CAAkBa,OAAA,KAAY,SAAYb,iBAAA,CAAkBa,OAAA,GAAU;QAC1F;QAEDvC,QAAA,CAASwC,WAAA,GACPd,iBAAA,CAAkBK,KAAA,KAAU,UAAaL,iBAAA,CAAkBK,KAAA,GAAQ,IAAM,OAAOlC,gBAAA,CAAiB2C,WAAA;QAEnG,IAAId,iBAAA,CAAkBxB,SAAA,KAAc,QAClC5B,KAAA,CAAMuB,gBAAA,CAAiBE,IAAI,EAAEG,SAAA,GAAYwB,iBAAA,CAAkBxB,SAAA;MACrE,OAAa;QACLF,QAAA,CAASwC,WAAA,GAAc3C,gBAAA,CAAiB2C,WAAA;QACxCxC,QAAA,CAASuC,OAAA,GAAU1C,gBAAA,CAAiB0C,OAAA;MACrC;MAED,IAAI1C,gBAAA,CAAiB4C,SAAA,KAAc,QAAQ5C,gBAAA,CAAiB6C,SAAA,KAAc,OAAO1C,QAAA,CAASuC,OAAA,GAAU;MAEpG,IAAI1C,gBAAA,CAAiB8C,cAAA,EAAgB;QACnC3C,QAAA,CAAS4C,QAAA,GAAW;QAEpB5C,QAAA,CAAS2C,cAAA,GAAiB9C,gBAAA,CAAiB8C,cAAA;QAC3C3C,QAAA,CAAS6C,gBAAA,GAAmBhD,gBAAA,CAAiBgD,gBAAA;QAC7C7C,QAAA,CAAS8C,WAAA,GAAcjD,gBAAA,CAAiBiD,WAAA;MACzC;MAED9C,QAAA,CAASd,OAAA,GAAUW,gBAAA,CAAiBX,OAAA;IACrC;IAED,SAAS6D,aAAA,EAAe;MACtB,IAAIC,IAAA;MAGJA,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAKxE,iBAAiB;MAEpC,SAASyC,CAAA,GAAI,GAAGC,EAAA,GAAK8B,IAAA,CAAK7B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC7CzC,iBAAA,CAAkBwE,IAAA,CAAK/B,CAAC,CAAC,IAAI;MAC9B;MAGD+B,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAKvE,uBAAuB;MAE1C,SAASwC,CAAA,GAAI,GAAGC,EAAA,GAAK8B,IAAA,CAAK7B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC7CxC,uBAAA,CAAwBuE,IAAA,CAAK/B,CAAC,CAAC,IAAI;MACpC;MAGD+B,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAK1E,KAAK;MAExB,SAAS2C,CAAA,GAAI,GAAGC,EAAA,GAAK8B,IAAA,CAAK7B,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC7C,MAAMiC,GAAA,GAAMF,IAAA,CAAK/B,CAAC;QAElB,IAAI3C,KAAA,CAAM4E,GAAG,EAAEjD,IAAA,KAAS,OAAO;UAC7B3B,KAAA,CAAM4E,GAAG,EAAE/C,KAAA;UAEX,IAAI7B,KAAA,CAAM4E,GAAG,EAAEhD,SAAA,KAAc,SAAS5B,KAAA,CAAM4E,GAAG,EAAE/C,KAAA,GAAQ5B,oBAAA,EAAsB;YAC7E,OAAOD,KAAA,CAAM4E,GAAG;UACjB;QACX,OAAe;UACL5E,KAAA,CAAM4E,GAAG,EAAEjD,IAAA,GAAO;UAClB3B,KAAA,CAAM4E,GAAG,EAAE/C,KAAA,GAAQ;QACpB;MACF;IACF;IAED,KAAKgD,MAAA,GAAS,UAAU5B,KAAA,EAAOC,MAAA,EAAQ;MACrC,IAAI,KAAKzD,OAAA,KAAY,OAAO;QAC1BF,QAAA,CAASsF,MAAA,CAAO5B,KAAA,EAAOC,MAAM;QAC7B;MACD;MAED,MAAM4B,gBAAA,GAAmBvF,QAAA,CAASwF,SAAA;MAClCxF,QAAA,CAASwF,SAAA,GAAY,KAAKA,SAAA;MAE1BxF,QAAA,CAASsF,MAAA,CAAO5B,KAAA,EAAOC,MAAM;MAE7B3D,QAAA,CAASwF,SAAA,GAAYD,gBAAA;MAErB,KAAKE,aAAA,CAAc/B,KAAA,EAAOC,MAAM;IACjC;IAED,KAAK8B,aAAA,GAAgB,UAAU/B,KAAA,EAAOC,MAAA,EAAQ;MAC5C,MAAM4B,gBAAA,GAAmBvF,QAAA,CAASwF,SAAA;MAClC,MAAME,sBAAA,GAAyBhC,KAAA,CAAMiC,qBAAA;MACrC,MAAMC,sBAAA,GAAyBlC,KAAA,CAAMmC,UAAA;MACrC,MAAMC,uBAAA,GAA0B9F,QAAA,CAAS+F,SAAA,CAAU7F,OAAA;MAEnDwD,KAAA,CAAMiC,qBAAA,GAAwB;MAC9BjC,KAAA,CAAMmC,UAAA,GAAa;MACnB7F,QAAA,CAASwF,SAAA,GAAY;MACrBxF,QAAA,CAAS+F,SAAA,CAAU7F,OAAA,GAAU;MAE7BwD,KAAA,CAAMsC,QAAA,CAAS/C,kBAAkB;MAEjCjD,QAAA,CAASsF,MAAA,CAAO5B,KAAA,EAAOC,MAAM;MAE7BD,KAAA,CAAMsC,QAAA,CAASxC,uBAAuB;MAEtC0B,YAAA,CAAc;MAEdxB,KAAA,CAAMiC,qBAAA,GAAwBD,sBAAA;MAC9BhC,KAAA,CAAMmC,UAAA,GAAaD,sBAAA;MACnB5F,QAAA,CAASwF,SAAA,GAAYD,gBAAA;MACrBvF,QAAA,CAAS+F,SAAA,CAAU7F,OAAA,GAAU4F,uBAAA;IAC9B;IAgBD,KAAKN,SAAA,GAAYxF,QAAA,CAASwF,SAAA;IAC1B,KAAKS,UAAA,GAAajG,QAAA,CAASiG,UAAA;IAC3B,KAAKF,SAAA,GAAY/F,QAAA,CAAS+F,SAAA;IAE1B,KAAKG,KAAA,GAAQ,UAAUjC,KAAA,EAAOkC,KAAA,EAAOC,OAAA,EAAS;MAC5CpG,QAAA,CAASkG,KAAA,CAAMjC,KAAA,EAAOkC,KAAA,EAAOC,OAAO;IACrC;IAED,KAAKC,aAAA,GAAgB,YAAY;MAC/B,OAAOrG,QAAA,CAASqG,aAAA,CAAe;IAChC;IAED,KAAKC,aAAA,GAAgB,UAAUvF,KAAA,EAAO;MACpCf,QAAA,CAASsG,aAAA,CAAcvF,KAAK;IAC7B;IAED,KAAKwF,OAAA,GAAU,UAAUC,MAAA,EAAQ;MAC/B,OAAOxG,QAAA,CAASuG,OAAA,CAAQC,MAAM;IAC/B;IAED,KAAKC,OAAA,GAAU,UAAUC,KAAA,EAAOC,MAAA,EAAQC,WAAA,EAAa;MACnD5G,QAAA,CAASyG,OAAA,CAAQC,KAAA,EAAOC,MAAA,EAAQC,WAAW;IAC5C;IAED,KAAKC,WAAA,GAAc,UAAUC,CAAA,EAAGC,CAAA,EAAGL,KAAA,EAAOC,MAAA,EAAQ;MAChD3G,QAAA,CAAS6G,WAAA,CAAYC,CAAA,EAAGC,CAAA,EAAGL,KAAA,EAAOC,MAAM;IACzC;IAED,KAAKK,UAAA,GAAa,UAAUF,CAAA,EAAGC,CAAA,EAAGL,KAAA,EAAOC,MAAA,EAAQ;MAC/C3G,QAAA,CAASgH,UAAA,CAAWF,CAAA,EAAGC,CAAA,EAAGL,KAAA,EAAOC,MAAM;IACxC;IAED,KAAKM,cAAA,GAAiB,UAAUC,OAAA,EAAS;MACvClH,QAAA,CAASiH,cAAA,CAAeC,OAAO;IAChC;IAED,KAAKC,eAAA,GAAkB,UAAUC,YAAA,EAAc;MAC7CpH,QAAA,CAASmH,eAAA,CAAgBC,YAAY;IACtC;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}