{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport ProductCard from '../components/product/ProductCard';\nimport { getFeaturedProducts } from '../services/products';\nimport '../styles/pages.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    loadFeaturedProducts();\n  }, []);\n  const loadFeaturedProducts = async () => {\n    try {\n      const response = await getFeaturedProducts();\n      // Handle both API response and mock data structure\n      const products = response.products || response;\n      setFeaturedProducts(products);\n    } catch (error) {\n      console.error('Error loading featured products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const categories = [{\n    id: 'desks',\n    name: 'Office Desks',\n    count: 12,\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"3\",\n        y: \"4\",\n        width: \"18\",\n        height: \"12\",\n        rx: \"2\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 10h18\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M8 21v-7\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M16 21v-7\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 17\n    }, this),\n    categoryName: 'Desk'\n  }, {\n    id: 'chairs',\n    name: 'Office Chairs',\n    count: 8,\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M5 12V7a7 7 0 0 1 14 0v5\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M22 19H2l2-7h16l2 7Z\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M6 19v2\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M18 19v2\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 17\n    }, this),\n    categoryName: 'Chair'\n  }, {\n    id: 'storage',\n    name: 'Storage Solutions',\n    count: 15,\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"14,2 14,8 20,8\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"13\",\n        x2: \"8\",\n        y2: \"13\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"16\",\n        y1: \"17\",\n        x2: \"8\",\n        y2: \"17\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n        points: \"10,9 9,9 8,9\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 17\n    }, this),\n    categoryName: 'Storage'\n  }, {\n    id: 'conference',\n    name: 'Conference Furniture',\n    count: 6,\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M3 21h18\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M5 21V7l8-4v18\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M19 21V11l-6-4\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M9 9v.01\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M9 12v.01\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M9 15v.01\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M9 18v.01\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 17\n    }, this),\n    categoryName: 'Conference'\n  }, {\n    id: 'accessories',\n    name: 'Office Accessories',\n    count: 20,\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      width: \"48\",\n      height: \"48\",\n      viewBox: \"0 0 24 24\",\n      fill: \"none\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48\",\n        stroke: \"#F0B21B\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 17\n    }, this),\n    categoryName: 'Accessories'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Premium Office Furniture Solutions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Transform your workspace with our premium collection of office furniture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Discover our premium collection of office furniture designed for modern professionals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"cta-button\",\n            children: \"SHOP NOW\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-navigation\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"nav-arrow left\",\n          children: \"\\u2039\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"nav-arrow right\",\n          children: \"\\u203A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-indicators\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"indicator active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"indicator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"indicator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"featured-categories\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Featured Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"view-all\",\n            children: \"View All Categories \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"categories-grid\",\n          children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/products?category=${encodeURIComponent(category.categoryName)}`,\n            className: \"category-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"category-icon\",\n              children: category.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [category.count, \" Products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 33\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"featured-products\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"view-all\",\n            children: \"View All Products \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 21\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: \"Loading featured products...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-grid\",\n          children: featuredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"testimonials\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"testimonials-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"testimonials-label\",\n            children: \"TESTIMONIALS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"What Our Clients Say\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"testimonials-underline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"testimonials-slider\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"testimonial-nav prev\",\n            \"aria-label\": \"Previous testimonial\",\n            children: \"\\u2039\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"testimonial-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-stars\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"star\",\n                children: \"\\u2605\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"star\",\n                children: \"\\u2605\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"star\",\n                children: \"\\u2605\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"star\",\n                children: \"\\u2605\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"star\",\n                children: \"\\u2605\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-avatar\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"avatar-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-quote\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"quote-mark\",\n                children: \"\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"The ergonomic solutions provided have significantly improved our team's comfort and productivity. Outstanding products!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"testimonial-author\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Emily Rodriguez\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"HR Director, Tech Innovations Ltd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"testimonial-nav next\",\n            \"aria-label\": \"Next testimonial\",\n            children: \"\\u203A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"testimonials-indicators\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"indicator active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"indicator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"indicator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 9\n  }, this);\n};\n_s(Home, \"P3nxhaVCbAVDF9mcColQrEkr+LU=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "ProductCard", "getFeaturedProducts", "jsxDEV", "_jsxDEV", "Home", "_s", "featuredProducts", "setFeaturedProducts", "loading", "setLoading", "loadFeaturedProducts", "response", "products", "error", "console", "categories", "id", "name", "count", "icon", "width", "height", "viewBox", "fill", "xmlns", "children", "x", "y", "rx", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "categoryName", "points", "x1", "y1", "x2", "y2", "className", "to", "map", "category", "index", "encodeURIComponent", "product", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport ProductCard from '../components/product/ProductCard';\nimport { getFeaturedProducts } from '../services/products';\nimport '../styles/pages.css';\n\nconst Home = () => {\n    const [featuredProducts, setFeaturedProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n\n    useEffect(() => {\n        loadFeaturedProducts();\n    }, []);\n\n    const loadFeaturedProducts = async () => {\n        try {\n            const response = await getFeaturedProducts();\n            // Handle both API response and mock data structure\n            const products = response.products || response;\n            setFeaturedProducts(products);\n        } catch (error) {\n            console.error('Error loading featured products:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const categories = [\n        {\n            id: 'desks',\n            name: 'Office Desks',\n            count: 12,\n            icon: (\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <rect x=\"3\" y=\"4\" width=\"18\" height=\"12\" rx=\"2\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M3 10h18\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M8 21v-7\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M16 21v-7\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                </svg>\n            ),\n            categoryName: 'Desk'\n        },\n        {\n            id: 'chairs',\n            name: 'Office Chairs',\n            count: 8,\n            icon: (\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M5 12V7a7 7 0 0 1 14 0v5\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M22 19H2l2-7h16l2 7Z\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M6 19v2\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M18 19v2\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                </svg>\n            ),\n            categoryName: 'Chair'\n        },\n        {\n            id: 'storage',\n            name: 'Storage Solutions',\n            count: 15,\n            icon: (\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <polyline points=\"14,2 14,8 20,8\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <polyline points=\"10,9 9,9 8,9\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                </svg>\n            ),\n            categoryName: 'Storage'\n        },\n        {\n            id: 'conference',\n            name: 'Conference Furniture',\n            count: 6,\n            icon: (\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M3 21h18\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M5 21V7l8-4v18\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M19 21V11l-6-4\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M9 9v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M9 12v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M9 15v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                    <path d=\"M9 18v.01\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                </svg>\n            ),\n            categoryName: 'Conference'\n        },\n        {\n            id: 'accessories',\n            name: 'Office Accessories',\n            count: 20,\n            icon: (\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                </svg>\n            ),\n            categoryName: 'Accessories'\n        }\n    ];\n\n    return (\n        <div className=\"home\">\n            {/* Hero Section */}\n            <section className=\"hero\">\n                <div className=\"hero-content\">\n                    <div className=\"hero-text\">\n                        <h1>Premium Office Furniture Solutions</h1>\n                        <p>Transform your workspace with our premium collection of office furniture</p>\n                        <p>Discover our premium collection of office furniture designed for modern professionals</p>\n                        <Link to=\"/products\" className=\"cta-button\">SHOP NOW</Link>\n                    </div>\n                </div>\n                <div className=\"hero-navigation\">\n                    <button className=\"nav-arrow left\">‹</button>\n                    <button className=\"nav-arrow right\">›</button>\n                </div>\n                <div className=\"hero-indicators\">\n                    <span className=\"indicator active\"></span>\n                    <span className=\"indicator\"></span>\n                    <span className=\"indicator\"></span>\n                </div>\n            </section>\n\n            {/* Featured Categories */}\n            <section className=\"featured-categories\">\n                <div className=\"container\">\n                    <div className=\"section-header\">\n                        <h2>Featured Categories</h2>\n                        <Link to=\"/products\" className=\"view-all\">View All Categories →</Link>\n                    </div>\n                    <div className=\"categories-grid\">\n                        {categories.map((category, index) => (\n                            <Link\n                                key={index}\n                                to={`/products?category=${encodeURIComponent(category.categoryName)}`}\n                                className=\"category-card\"\n                            >\n                                <div className=\"category-icon\">{category.icon}</div>\n                                <h3>{category.name}</h3>\n                                <p>{category.count} Products</p>\n                            </Link>\n                        ))}\n                    </div>\n                </div>\n            </section>\n\n            {/* Featured Products */}\n            <section className=\"featured-products\">\n                <div className=\"container\">\n                    <div className=\"section-header\">\n                        <h2>Featured Products</h2>\n                        <Link to=\"/products\" className=\"view-all\">View All Products →</Link>\n                    </div>\n                    {loading ? (\n                        <div className=\"loading\">Loading featured products...</div>\n                    ) : (\n                        <div className=\"products-grid\">\n                            {featuredProducts.map(product => (\n                                <ProductCard key={product.id} product={product} />\n                            ))}\n                        </div>\n                    )}\n                </div>\n            </section>\n\n            {/* Testimonials Section */}\n            <section className=\"testimonials\">\n                <div className=\"container\">\n                    <div className=\"testimonials-header\">\n                        <span className=\"testimonials-label\">TESTIMONIALS</span>\n                        <h2>What Our Clients Say</h2>\n                        <div className=\"testimonials-underline\"></div>\n                    </div>\n\n                    <div className=\"testimonials-slider\">\n                        <button className=\"testimonial-nav prev\" aria-label=\"Previous testimonial\">‹</button>\n\n                        <div className=\"testimonial-content\">\n                            <div className=\"testimonial-stars\">\n                                <span className=\"star\">★</span>\n                                <span className=\"star\">★</span>\n                                <span className=\"star\">★</span>\n                                <span className=\"star\">★</span>\n                                <span className=\"star\">★</span>\n                            </div>\n\n                            <div className=\"testimonial-avatar\">\n                                <div className=\"avatar-circle\"></div>\n                            </div>\n\n                            <div className=\"testimonial-quote\">\n                                <span className=\"quote-mark\">\"</span>\n                                <p>The ergonomic solutions provided have significantly improved our team's comfort and productivity. Outstanding products!</p>\n                            </div>\n\n                            <div className=\"testimonial-author\">\n                                <h4>Emily Rodriguez</h4>\n                                <p>HR Director, Tech Innovations Ltd</p>\n                            </div>\n                        </div>\n\n                        <button className=\"testimonial-nav next\" aria-label=\"Next testimonial\">›</button>\n                    </div>\n\n                    <div className=\"testimonials-indicators\">\n                        <span className=\"indicator active\"></span>\n                        <span className=\"indicator\"></span>\n                        <span className=\"indicator\"></span>\n                    </div>\n                </div>\n            </section>\n        </div>\n    );\n};\n\nexport default Home;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,WAAW,MAAM,mCAAmC;AAC3D,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACf,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACZY,oBAAoB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMV,mBAAmB,CAAC,CAAC;MAC5C;MACA,MAAMW,QAAQ,GAAGD,QAAQ,CAACC,QAAQ,IAAID,QAAQ;MAC9CJ,mBAAmB,CAACK,QAAQ,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC5D,CAAC,SAAS;MACNJ,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMM,UAAU,GAAG,CACf;IACIC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,EAAE;IACTC,IAAI,eACAhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,4BAA4B;MAAAC,QAAA,gBAC1FtB,OAAA;QAAMuB,CAAC,EAAC,GAAG;QAACC,CAAC,EAAC,GAAG;QAACP,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACO,EAAE,EAAC,GAAG;QAACC,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAClF/B,OAAA;QAAMgC,CAAC,EAAC,UAAU;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACrD/B,OAAA;QAAMgC,CAAC,EAAC,UAAU;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACrD/B,OAAA;QAAMgC,CAAC,EAAC,WAAW;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACR;IACDE,YAAY,EAAE;EAClB,CAAC,EACD;IACIpB,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,CAAC;IACRC,IAAI,eACAhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,4BAA4B;MAAAC,QAAA,gBAC1FtB,OAAA;QAAMgC,CAAC,EAAC,0BAA0B;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACrE/B,OAAA;QAAMgC,CAAC,EAAC,sBAAsB;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACjE/B,OAAA;QAAMgC,CAAC,EAAC,SAAS;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACpD/B,OAAA;QAAMgC,CAAC,EAAC,UAAU;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CACR;IACDE,YAAY,EAAE;EAClB,CAAC,EACD;IACIpB,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,EAAE;IACTC,IAAI,eACAhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,4BAA4B;MAAAC,QAAA,gBAC1FtB,OAAA;QAAMgC,CAAC,EAAC,4DAA4D;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACvG/B,OAAA;QAAUkC,MAAM,EAAC,gBAAgB;QAACR,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACpE/B,OAAA;QAAMmC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACZ,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACvE/B,OAAA;QAAMmC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,GAAG;QAACC,EAAE,EAAC,IAAI;QAACZ,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACvE/B,OAAA;QAAUkC,MAAM,EAAC,cAAc;QAACR,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CACR;IACDE,YAAY,EAAE;EAClB,CAAC,EACD;IACIpB,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,CAAC;IACRC,IAAI,eACAhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,4BAA4B;MAAAC,QAAA,gBAC1FtB,OAAA;QAAMgC,CAAC,EAAC,UAAU;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACrD/B,OAAA;QAAMgC,CAAC,EAAC,gBAAgB;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC3D/B,OAAA;QAAMgC,CAAC,EAAC,gBAAgB;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC3D/B,OAAA;QAAMgC,CAAC,EAAC,UAAU;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACrD/B,OAAA;QAAMgC,CAAC,EAAC,WAAW;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACtD/B,OAAA;QAAMgC,CAAC,EAAC,WAAW;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACtD/B,OAAA;QAAMgC,CAAC,EAAC,WAAW;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACR;IACDE,YAAY,EAAE;EAClB,CAAC,EACD;IACIpB,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,EAAE;IACTC,IAAI,eACAhB,OAAA;MAAKiB,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,MAAM;MAACC,KAAK,EAAC,4BAA4B;MAAAC,QAAA,eAC1FtB,OAAA;QAAMgC,CAAC,EAAC,mHAAmH;QAACN,MAAM,EAAC,SAAS;QAACC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7J,CACR;IACDE,YAAY,EAAE;EAClB,CAAC,CACJ;EAED,oBACIjC,OAAA;IAAKuC,SAAS,EAAC,MAAM;IAAAjB,QAAA,gBAEjBtB,OAAA;MAASuC,SAAS,EAAC,MAAM;MAAAjB,QAAA,gBACrBtB,OAAA;QAAKuC,SAAS,EAAC,cAAc;QAAAjB,QAAA,eACzBtB,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAjB,QAAA,gBACtBtB,OAAA;YAAAsB,QAAA,EAAI;UAAkC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3C/B,OAAA;YAAAsB,QAAA,EAAG;UAAwE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/E/B,OAAA;YAAAsB,QAAA,EAAG;UAAqF;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5F/B,OAAA,CAACJ,IAAI;YAAC4C,EAAE,EAAC,WAAW;YAACD,SAAS,EAAC,YAAY;YAAAjB,QAAA,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/B,OAAA;QAAKuC,SAAS,EAAC,iBAAiB;QAAAjB,QAAA,gBAC5BtB,OAAA;UAAQuC,SAAS,EAAC,gBAAgB;UAAAjB,QAAA,EAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7C/B,OAAA;UAAQuC,SAAS,EAAC,iBAAiB;UAAAjB,QAAA,EAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACN/B,OAAA;QAAKuC,SAAS,EAAC,iBAAiB;QAAAjB,QAAA,gBAC5BtB,OAAA;UAAMuC,SAAS,EAAC;QAAkB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C/B,OAAA;UAAMuC,SAAS,EAAC;QAAW;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnC/B,OAAA;UAAMuC,SAAS,EAAC;QAAW;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGV/B,OAAA;MAASuC,SAAS,EAAC,qBAAqB;MAAAjB,QAAA,eACpCtB,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAjB,QAAA,gBACtBtB,OAAA;UAAKuC,SAAS,EAAC,gBAAgB;UAAAjB,QAAA,gBAC3BtB,OAAA;YAAAsB,QAAA,EAAI;UAAmB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B/B,OAAA,CAACJ,IAAI;YAAC4C,EAAE,EAAC,WAAW;YAACD,SAAS,EAAC,UAAU;YAAAjB,QAAA,EAAC;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACN/B,OAAA;UAAKuC,SAAS,EAAC,iBAAiB;UAAAjB,QAAA,EAC3BV,UAAU,CAAC6B,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC5B3C,OAAA,CAACJ,IAAI;YAED4C,EAAE,EAAE,sBAAsBI,kBAAkB,CAACF,QAAQ,CAACT,YAAY,CAAC,EAAG;YACtEM,SAAS,EAAC,eAAe;YAAAjB,QAAA,gBAEzBtB,OAAA;cAAKuC,SAAS,EAAC,eAAe;cAAAjB,QAAA,EAAEoB,QAAQ,CAAC1B;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD/B,OAAA;cAAAsB,QAAA,EAAKoB,QAAQ,CAAC5B;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxB/B,OAAA;cAAAsB,QAAA,GAAIoB,QAAQ,CAAC3B,KAAK,EAAC,WAAS;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,GAN3BY,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOR,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGV/B,OAAA;MAASuC,SAAS,EAAC,mBAAmB;MAAAjB,QAAA,eAClCtB,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAjB,QAAA,gBACtBtB,OAAA;UAAKuC,SAAS,EAAC,gBAAgB;UAAAjB,QAAA,gBAC3BtB,OAAA;YAAAsB,QAAA,EAAI;UAAiB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B/B,OAAA,CAACJ,IAAI;YAAC4C,EAAE,EAAC,WAAW;YAACD,SAAS,EAAC,UAAU;YAAAjB,QAAA,EAAC;UAAmB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EACL1B,OAAO,gBACJL,OAAA;UAAKuC,SAAS,EAAC,SAAS;UAAAjB,QAAA,EAAC;QAA4B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAE3D/B,OAAA;UAAKuC,SAAS,EAAC,eAAe;UAAAjB,QAAA,EACzBnB,gBAAgB,CAACsC,GAAG,CAACI,OAAO,iBACzB7C,OAAA,CAACH,WAAW;YAAkBgD,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAAChC,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGV/B,OAAA;MAASuC,SAAS,EAAC,cAAc;MAAAjB,QAAA,eAC7BtB,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAjB,QAAA,gBACtBtB,OAAA;UAAKuC,SAAS,EAAC,qBAAqB;UAAAjB,QAAA,gBAChCtB,OAAA;YAAMuC,SAAS,EAAC,oBAAoB;YAAAjB,QAAA,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxD/B,OAAA;YAAAsB,QAAA,EAAI;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7B/B,OAAA;YAAKuC,SAAS,EAAC;UAAwB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEN/B,OAAA;UAAKuC,SAAS,EAAC,qBAAqB;UAAAjB,QAAA,gBAChCtB,OAAA;YAAQuC,SAAS,EAAC,sBAAsB;YAAC,cAAW,sBAAsB;YAAAjB,QAAA,EAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAErF/B,OAAA;YAAKuC,SAAS,EAAC,qBAAqB;YAAAjB,QAAA,gBAChCtB,OAAA;cAAKuC,SAAS,EAAC,mBAAmB;cAAAjB,QAAA,gBAC9BtB,OAAA;gBAAMuC,SAAS,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/B/B,OAAA;gBAAMuC,SAAS,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/B/B,OAAA;gBAAMuC,SAAS,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/B/B,OAAA;gBAAMuC,SAAS,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/B/B,OAAA;gBAAMuC,SAAS,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAEN/B,OAAA;cAAKuC,SAAS,EAAC,oBAAoB;cAAAjB,QAAA,eAC/BtB,OAAA;gBAAKuC,SAAS,EAAC;cAAe;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eAEN/B,OAAA;cAAKuC,SAAS,EAAC,mBAAmB;cAAAjB,QAAA,gBAC9BtB,OAAA;gBAAMuC,SAAS,EAAC,YAAY;gBAAAjB,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrC/B,OAAA;gBAAAsB,QAAA,EAAG;cAAuH;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7H,CAAC,eAEN/B,OAAA;cAAKuC,SAAS,EAAC,oBAAoB;cAAAjB,QAAA,gBAC/BtB,OAAA;gBAAAsB,QAAA,EAAI;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB/B,OAAA;gBAAAsB,QAAA,EAAG;cAAiC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/B,OAAA;YAAQuC,SAAS,EAAC,sBAAsB;YAAC,cAAW,kBAAkB;YAAAjB,QAAA,EAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eAEN/B,OAAA;UAAKuC,SAAS,EAAC,yBAAyB;UAAAjB,QAAA,gBACpCtB,OAAA;YAAMuC,SAAS,EAAC;UAAkB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1C/B,OAAA;YAAMuC,SAAS,EAAC;UAAW;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnC/B,OAAA;YAAMuC,SAAS,EAAC;UAAW;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEd,CAAC;AAAC7B,EAAA,CAhNID,IAAI;AAAA6C,EAAA,GAAJ7C,IAAI;AAkNV,eAAeA,IAAI;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}