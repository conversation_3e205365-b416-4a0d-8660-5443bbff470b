{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport mergeRefs from 'react-merge-refs';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { Line2, LineMaterial, LineSegmentsGeometry } from 'three-stdlib';\nconst context = /*#__PURE__*/React.createContext(null);\nconst Segments = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  React.useMemo(() => extend({\n    SegmentObject\n  }), []);\n  const {\n    limit = 1000,\n    lineWidth = 1.0,\n    children,\n    ...rest\n  } = props;\n  const [segments, setSegments] = React.useState([]);\n  const [line] = React.useState(() => new Line2());\n  const [material] = React.useState(() => new LineMaterial());\n  const [geometry] = React.useState(() => new LineSegmentsGeometry());\n  const [resolution] = React.useState(() => new THREE.Vector2(512, 512));\n  const [positions] = React.useState(() => Array(limit * 6).fill(0));\n  const [colors] = React.useState(() => Array(limit * 6).fill(0));\n  const api = React.useMemo(() => ({\n    subscribe: ref => {\n      setSegments(segments => [...segments, ref]);\n      return () => setSegments(segments => segments.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  useFrame(() => {\n    for (let i = 0; i < limit; i++) {\n      var _segments$i;\n      const segment = (_segments$i = segments[i]) == null ? void 0 : _segments$i.current;\n      if (segment) {\n        positions[i * 6 + 0] = segment.start.x;\n        positions[i * 6 + 1] = segment.start.y;\n        positions[i * 6 + 2] = segment.start.z;\n        positions[i * 6 + 3] = segment.end.x;\n        positions[i * 6 + 4] = segment.end.y;\n        positions[i * 6 + 5] = segment.end.z;\n        colors[i * 6 + 0] = segment.color.r;\n        colors[i * 6 + 1] = segment.color.g;\n        colors[i * 6 + 2] = segment.color.b;\n        colors[i * 6 + 3] = segment.color.r;\n        colors[i * 6 + 4] = segment.color.g;\n        colors[i * 6 + 5] = segment.color.b;\n      }\n    }\n    geometry.setColors(colors);\n    geometry.setPositions(positions);\n    line.computeLineDistances();\n  });\n  return /*#__PURE__*/React.createElement(\"primitive\", {\n    object: line,\n    ref: forwardedRef\n  }, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: geometry,\n    attach: \"geometry\"\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: material,\n    attach: \"material\",\n    vertexColors: true,\n    resolution: resolution,\n    linewidth: lineWidth\n  }, rest)), /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n});\nclass SegmentObject {\n  constructor() {\n    this.color = new THREE.Color('white');\n    this.start = new THREE.Vector3(0, 0, 0);\n    this.end = new THREE.Vector3(0, 0, 0);\n  }\n}\nconst normPos = pos => pos instanceof THREE.Vector3 ? pos : new THREE.Vector3(...(typeof pos === 'number' ? [pos, pos, pos] : pos));\nconst Segment = /*#__PURE__*/React.forwardRef(({\n  color,\n  start,\n  end\n}, forwardedRef) => {\n  const api = React.useContext(context);\n  if (!api) throw 'Segment must used inside Segments component.';\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => api.subscribe(ref), []);\n  return /*#__PURE__*/React.createElement(\"segmentObject\", {\n    ref: mergeRefs([ref, forwardedRef]),\n    color: color,\n    start: normPos(start),\n    end: normPos(end)\n  });\n});\nexport { Segment, SegmentObject, Segments };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "mergeRefs", "extend", "useFrame", "Line2", "LineMaterial", "LineSegmentsGeometry", "context", "createContext", "Segments", "forwardRef", "props", "forwardedRef", "useMemo", "SegmentObject", "limit", "lineWidth", "children", "rest", "segments", "setSegments", "useState", "line", "material", "geometry", "resolution", "Vector2", "positions", "Array", "fill", "colors", "api", "subscribe", "ref", "filter", "item", "current", "i", "_segments$i", "segment", "start", "x", "y", "z", "end", "color", "r", "g", "b", "setColors", "setPositions", "computeLineDistances", "createElement", "object", "attach", "vertexColors", "linewidth", "Provider", "value", "constructor", "Color", "Vector3", "normPos", "pos", "Segment", "useContext", "useRef", "useLayoutEffect"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Segments.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport mergeRefs from 'react-merge-refs';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { Line2, LineMaterial, LineSegmentsGeometry } from 'three-stdlib';\n\nconst context = /*#__PURE__*/React.createContext(null);\nconst Segments = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  React.useMemo(() => extend({\n    SegmentObject\n  }), []);\n  const {\n    limit = 1000,\n    lineWidth = 1.0,\n    children,\n    ...rest\n  } = props;\n  const [segments, setSegments] = React.useState([]);\n  const [line] = React.useState(() => new Line2());\n  const [material] = React.useState(() => new LineMaterial());\n  const [geometry] = React.useState(() => new LineSegmentsGeometry());\n  const [resolution] = React.useState(() => new THREE.Vector2(512, 512));\n  const [positions] = React.useState(() => Array(limit * 6).fill(0));\n  const [colors] = React.useState(() => Array(limit * 6).fill(0));\n  const api = React.useMemo(() => ({\n    subscribe: ref => {\n      setSegments(segments => [...segments, ref]);\n      return () => setSegments(segments => segments.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  useFrame(() => {\n    for (let i = 0; i < limit; i++) {\n      var _segments$i;\n\n      const segment = (_segments$i = segments[i]) == null ? void 0 : _segments$i.current;\n\n      if (segment) {\n        positions[i * 6 + 0] = segment.start.x;\n        positions[i * 6 + 1] = segment.start.y;\n        positions[i * 6 + 2] = segment.start.z;\n        positions[i * 6 + 3] = segment.end.x;\n        positions[i * 6 + 4] = segment.end.y;\n        positions[i * 6 + 5] = segment.end.z;\n        colors[i * 6 + 0] = segment.color.r;\n        colors[i * 6 + 1] = segment.color.g;\n        colors[i * 6 + 2] = segment.color.b;\n        colors[i * 6 + 3] = segment.color.r;\n        colors[i * 6 + 4] = segment.color.g;\n        colors[i * 6 + 5] = segment.color.b;\n      }\n    }\n\n    geometry.setColors(colors);\n    geometry.setPositions(positions);\n    line.computeLineDistances();\n  });\n  return /*#__PURE__*/React.createElement(\"primitive\", {\n    object: line,\n    ref: forwardedRef\n  }, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: geometry,\n    attach: \"geometry\"\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: material,\n    attach: \"material\",\n    vertexColors: true,\n    resolution: resolution,\n    linewidth: lineWidth\n  }, rest)), /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n});\nclass SegmentObject {\n  constructor() {\n    this.color = new THREE.Color('white');\n    this.start = new THREE.Vector3(0, 0, 0);\n    this.end = new THREE.Vector3(0, 0, 0);\n  }\n\n}\n\nconst normPos = pos => pos instanceof THREE.Vector3 ? pos : new THREE.Vector3(...(typeof pos === 'number' ? [pos, pos, pos] : pos));\n\nconst Segment = /*#__PURE__*/React.forwardRef(({\n  color,\n  start,\n  end\n}, forwardedRef) => {\n  const api = React.useContext(context);\n  if (!api) throw 'Segment must used inside Segments component.';\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => api.subscribe(ref), []);\n  return /*#__PURE__*/React.createElement(\"segmentObject\", {\n    ref: mergeRefs([ref, forwardedRef]),\n    color: color,\n    start: normPos(start),\n    end: normPos(end)\n  });\n});\n\nexport { Segment, SegmentObject, Segments };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,KAAK,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,cAAc;AAExE,MAAMC,OAAO,GAAG,aAAaP,KAAK,CAACQ,aAAa,CAAC,IAAI,CAAC;AACtD,MAAMC,QAAQ,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,CAACC,KAAK,EAAEC,YAAY,KAAK;EACtEZ,KAAK,CAACa,OAAO,CAAC,MAAMX,MAAM,CAAC;IACzBY;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM;IACJC,KAAK,GAAG,IAAI;IACZC,SAAS,GAAG,GAAG;IACfC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGP,KAAK;EACT,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,KAAK,CAACqB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACC,IAAI,CAAC,GAAGtB,KAAK,CAACqB,QAAQ,CAAC,MAAM,IAAIjB,KAAK,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmB,QAAQ,CAAC,GAAGvB,KAAK,CAACqB,QAAQ,CAAC,MAAM,IAAIhB,YAAY,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACmB,QAAQ,CAAC,GAAGxB,KAAK,CAACqB,QAAQ,CAAC,MAAM,IAAIf,oBAAoB,CAAC,CAAC,CAAC;EACnE,MAAM,CAACmB,UAAU,CAAC,GAAGzB,KAAK,CAACqB,QAAQ,CAAC,MAAM,IAAItB,KAAK,CAAC2B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EACtE,MAAM,CAACC,SAAS,CAAC,GAAG3B,KAAK,CAACqB,QAAQ,CAAC,MAAMO,KAAK,CAACb,KAAK,GAAG,CAAC,CAAC,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC;EAClE,MAAM,CAACC,MAAM,CAAC,GAAG9B,KAAK,CAACqB,QAAQ,CAAC,MAAMO,KAAK,CAACb,KAAK,GAAG,CAAC,CAAC,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAME,GAAG,GAAG/B,KAAK,CAACa,OAAO,CAAC,OAAO;IAC/BmB,SAAS,EAAEC,GAAG,IAAI;MAChBb,WAAW,CAACD,QAAQ,IAAI,CAAC,GAAGA,QAAQ,EAAEc,GAAG,CAAC,CAAC;MAC3C,OAAO,MAAMb,WAAW,CAACD,QAAQ,IAAIA,QAAQ,CAACe,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKH,GAAG,CAACG,OAAO,CAAC,CAAC;IAC7F;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACPjC,QAAQ,CAAC,MAAM;IACb,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,KAAK,EAAEsB,CAAC,EAAE,EAAE;MAC9B,IAAIC,WAAW;MAEf,MAAMC,OAAO,GAAG,CAACD,WAAW,GAAGnB,QAAQ,CAACkB,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,WAAW,CAACF,OAAO;MAElF,IAAIG,OAAO,EAAE;QACXZ,SAAS,CAACU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACC,KAAK,CAACC,CAAC;QACtCd,SAAS,CAACU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACC,KAAK,CAACE,CAAC;QACtCf,SAAS,CAACU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACC,KAAK,CAACG,CAAC;QACtChB,SAAS,CAACU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACK,GAAG,CAACH,CAAC;QACpCd,SAAS,CAACU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACK,GAAG,CAACF,CAAC;QACpCf,SAAS,CAACU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACK,GAAG,CAACD,CAAC;QACpCb,MAAM,CAACO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACM,KAAK,CAACC,CAAC;QACnChB,MAAM,CAACO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACM,KAAK,CAACE,CAAC;QACnCjB,MAAM,CAACO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACM,KAAK,CAACG,CAAC;QACnClB,MAAM,CAACO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACM,KAAK,CAACC,CAAC;QACnChB,MAAM,CAACO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACM,KAAK,CAACE,CAAC;QACnCjB,MAAM,CAACO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGE,OAAO,CAACM,KAAK,CAACG,CAAC;MACrC;IACF;IAEAxB,QAAQ,CAACyB,SAAS,CAACnB,MAAM,CAAC;IAC1BN,QAAQ,CAAC0B,YAAY,CAACvB,SAAS,CAAC;IAChCL,IAAI,CAAC6B,oBAAoB,CAAC,CAAC;EAC7B,CAAC,CAAC;EACF,OAAO,aAAanD,KAAK,CAACoD,aAAa,CAAC,WAAW,EAAE;IACnDC,MAAM,EAAE/B,IAAI;IACZW,GAAG,EAAErB;EACP,CAAC,EAAE,aAAaZ,KAAK,CAACoD,aAAa,CAAC,WAAW,EAAE;IAC/CC,MAAM,EAAE7B,QAAQ;IAChB8B,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAatD,KAAK,CAACoD,aAAa,CAAC,WAAW,EAAEtD,QAAQ,CAAC;IACzDuD,MAAM,EAAE9B,QAAQ;IAChB+B,MAAM,EAAE,UAAU;IAClBC,YAAY,EAAE,IAAI;IAClB9B,UAAU,EAAEA,UAAU;IACtB+B,SAAS,EAAExC;EACb,CAAC,EAAEE,IAAI,CAAC,CAAC,EAAE,aAAalB,KAAK,CAACoD,aAAa,CAAC7C,OAAO,CAACkD,QAAQ,EAAE;IAC5DC,KAAK,EAAE3B;EACT,CAAC,EAAEd,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAMH,aAAa,CAAC;EAClB6C,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACd,KAAK,GAAG,IAAI9C,KAAK,CAAC6D,KAAK,CAAC,OAAO,CAAC;IACrC,IAAI,CAACpB,KAAK,GAAG,IAAIzC,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,CAACjB,GAAG,GAAG,IAAI7C,KAAK,CAAC8D,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvC;AAEF;AAEA,MAAMC,OAAO,GAAGC,GAAG,IAAIA,GAAG,YAAYhE,KAAK,CAAC8D,OAAO,GAAGE,GAAG,GAAG,IAAIhE,KAAK,CAAC8D,OAAO,CAAC,IAAI,OAAOE,GAAG,KAAK,QAAQ,GAAG,CAACA,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC,GAAGA,GAAG,CAAC,CAAC;AAEnI,MAAMC,OAAO,GAAG,aAAahE,KAAK,CAACU,UAAU,CAAC,CAAC;EAC7CmC,KAAK;EACLL,KAAK;EACLI;AACF,CAAC,EAAEhC,YAAY,KAAK;EAClB,MAAMmB,GAAG,GAAG/B,KAAK,CAACiE,UAAU,CAAC1D,OAAO,CAAC;EACrC,IAAI,CAACwB,GAAG,EAAE,MAAM,8CAA8C;EAC9D,MAAME,GAAG,GAAGjC,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EAC9BlE,KAAK,CAACmE,eAAe,CAAC,MAAMpC,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,EAAE,EAAE,CAAC;EACnD,OAAO,aAAajC,KAAK,CAACoD,aAAa,CAAC,eAAe,EAAE;IACvDnB,GAAG,EAAEhC,SAAS,CAAC,CAACgC,GAAG,EAAErB,YAAY,CAAC,CAAC;IACnCiC,KAAK,EAAEA,KAAK;IACZL,KAAK,EAAEsB,OAAO,CAACtB,KAAK,CAAC;IACrBI,GAAG,EAAEkB,OAAO,CAAClB,GAAG;EAClB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,SAASoB,OAAO,EAAElD,aAAa,EAAEL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}