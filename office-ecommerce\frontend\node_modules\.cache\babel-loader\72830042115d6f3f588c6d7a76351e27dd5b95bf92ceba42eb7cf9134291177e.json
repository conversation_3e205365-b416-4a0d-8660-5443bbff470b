{"ast": null, "code": "import { Vector3, Matrix4, Line3 } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { ExtendedTriangle } from './ExtendedTriangle.js';\nimport { closestPointsSegmentToSegment } from './MathUtilities.js';\nexport class OrientedBox {\n  constructor(min, max, matrix) {\n    this.isOrientedBox = true;\n    this.min = new Vector3();\n    this.max = new Vector3();\n    this.matrix = new Matrix4();\n    this.invMatrix = new Matrix4();\n    this.points = new Array(8).fill().map(() => new Vector3());\n    this.satAxes = new Array(3).fill().map(() => new Vector3());\n    this.satBounds = new Array(3).fill().map(() => new SeparatingAxisBounds());\n    this.alignedSatBounds = new Array(3).fill().map(() => new SeparatingAxisBounds());\n    this.needsUpdate = false;\n    if (min) this.min.copy(min);\n    if (max) this.max.copy(max);\n    if (matrix) this.matrix.copy(matrix);\n  }\n  set(min, max, matrix) {\n    this.min.copy(min);\n    this.max.copy(max);\n    this.matrix.copy(matrix);\n    this.needsUpdate = true;\n  }\n  copy(other) {\n    this.min.copy(other.min);\n    this.max.copy(other.max);\n    this.matrix.copy(other.matrix);\n    this.needsUpdate = true;\n  }\n}\nOrientedBox.prototype.update = function () {\n  return function update() {\n    const matrix = this.matrix;\n    const min = this.min;\n    const max = this.max;\n    const points = this.points;\n    for (let x = 0; x <= 1; x++) {\n      for (let y = 0; y <= 1; y++) {\n        for (let z = 0; z <= 1; z++) {\n          const i = (1 << 0) * x | (1 << 1) * y | (1 << 2) * z;\n          const v = points[i];\n          v.x = x ? max.x : min.x;\n          v.y = y ? max.y : min.y;\n          v.z = z ? max.z : min.z;\n          v.applyMatrix4(matrix);\n        }\n      }\n    }\n    const satBounds = this.satBounds;\n    const satAxes = this.satAxes;\n    const minVec = points[0];\n    for (let i = 0; i < 3; i++) {\n      const axis = satAxes[i];\n      const sb = satBounds[i];\n      const index = 1 << i;\n      const pi = points[index];\n      axis.subVectors(minVec, pi);\n      sb.setFromPoints(axis, points);\n    }\n    const alignedSatBounds = this.alignedSatBounds;\n    alignedSatBounds[0].setFromPointsField(points, 'x');\n    alignedSatBounds[1].setFromPointsField(points, 'y');\n    alignedSatBounds[2].setFromPointsField(points, 'z');\n    this.invMatrix.copy(this.matrix).invert();\n    this.needsUpdate = false;\n  };\n}();\nOrientedBox.prototype.intersectsBox = function () {\n  const aabbBounds = new SeparatingAxisBounds();\n  return function intersectsBox(box) {\n    // TODO: should this be doing SAT against the AABB?\n    if (this.needsUpdate) {\n      this.update();\n    }\n    const min = box.min;\n    const max = box.max;\n    const satBounds = this.satBounds;\n    const satAxes = this.satAxes;\n    const alignedSatBounds = this.alignedSatBounds;\n    aabbBounds.min = min.x;\n    aabbBounds.max = max.x;\n    if (alignedSatBounds[0].isSeparated(aabbBounds)) return false;\n    aabbBounds.min = min.y;\n    aabbBounds.max = max.y;\n    if (alignedSatBounds[1].isSeparated(aabbBounds)) return false;\n    aabbBounds.min = min.z;\n    aabbBounds.max = max.z;\n    if (alignedSatBounds[2].isSeparated(aabbBounds)) return false;\n    for (let i = 0; i < 3; i++) {\n      const axis = satAxes[i];\n      const sb = satBounds[i];\n      aabbBounds.setFromBox(axis, box);\n      if (sb.isSeparated(aabbBounds)) return false;\n    }\n    return true;\n  };\n}();\nOrientedBox.prototype.intersectsTriangle = function () {\n  const saTri = new ExtendedTriangle();\n  const pointsArr = new Array(3);\n  const cachedSatBounds = new SeparatingAxisBounds();\n  const cachedSatBounds2 = new SeparatingAxisBounds();\n  const cachedAxis = new Vector3();\n  return function intersectsTriangle(triangle) {\n    if (this.needsUpdate) {\n      this.update();\n    }\n    if (!triangle.isExtendedTriangle) {\n      saTri.copy(triangle);\n      saTri.update();\n      triangle = saTri;\n    } else if (triangle.needsUpdate) {\n      triangle.update();\n    }\n    const satBounds = this.satBounds;\n    const satAxes = this.satAxes;\n    pointsArr[0] = triangle.a;\n    pointsArr[1] = triangle.b;\n    pointsArr[2] = triangle.c;\n    for (let i = 0; i < 3; i++) {\n      const sb = satBounds[i];\n      const sa = satAxes[i];\n      cachedSatBounds.setFromPoints(sa, pointsArr);\n      if (sb.isSeparated(cachedSatBounds)) return false;\n    }\n    const triSatBounds = triangle.satBounds;\n    const triSatAxes = triangle.satAxes;\n    const points = this.points;\n    for (let i = 0; i < 3; i++) {\n      const sb = triSatBounds[i];\n      const sa = triSatAxes[i];\n      cachedSatBounds.setFromPoints(sa, points);\n      if (sb.isSeparated(cachedSatBounds)) return false;\n    }\n\n    // check crossed axes\n    for (let i = 0; i < 3; i++) {\n      const sa1 = satAxes[i];\n      for (let i2 = 0; i2 < 4; i2++) {\n        const sa2 = triSatAxes[i2];\n        cachedAxis.crossVectors(sa1, sa2);\n        cachedSatBounds.setFromPoints(cachedAxis, pointsArr);\n        cachedSatBounds2.setFromPoints(cachedAxis, points);\n        if (cachedSatBounds.isSeparated(cachedSatBounds2)) return false;\n      }\n    }\n    return true;\n  };\n}();\nOrientedBox.prototype.closestPointToPoint = function () {\n  return function closestPointToPoint(point, target1) {\n    if (this.needsUpdate) {\n      this.update();\n    }\n    target1.copy(point).applyMatrix4(this.invMatrix).clamp(this.min, this.max).applyMatrix4(this.matrix);\n    return target1;\n  };\n}();\nOrientedBox.prototype.distanceToPoint = function () {\n  const target = new Vector3();\n  return function distanceToPoint(point) {\n    this.closestPointToPoint(point, target);\n    return point.distanceTo(target);\n  };\n}();\nOrientedBox.prototype.distanceToBox = function () {\n  const xyzFields = ['x', 'y', 'z'];\n  const segments1 = new Array(12).fill().map(() => new Line3());\n  const segments2 = new Array(12).fill().map(() => new Line3());\n  const point1 = new Vector3();\n  const point2 = new Vector3();\n\n  // early out if we find a value below threshold\n  return function distanceToBox(box, threshold = 0, target1 = null, target2 = null) {\n    if (this.needsUpdate) {\n      this.update();\n    }\n    if (this.intersectsBox(box)) {\n      if (target1 || target2) {\n        box.getCenter(point2);\n        this.closestPointToPoint(point2, point1);\n        box.closestPointToPoint(point1, point2);\n        if (target1) target1.copy(point1);\n        if (target2) target2.copy(point2);\n      }\n      return 0;\n    }\n    const threshold2 = threshold * threshold;\n    const min = box.min;\n    const max = box.max;\n    const points = this.points;\n\n    // iterate over every edge and compare distances\n    let closestDistanceSq = Infinity;\n\n    // check over all these points\n    for (let i = 0; i < 8; i++) {\n      const p = points[i];\n      point2.copy(p).clamp(min, max);\n      const dist = p.distanceToSquared(point2);\n      if (dist < closestDistanceSq) {\n        closestDistanceSq = dist;\n        if (target1) target1.copy(p);\n        if (target2) target2.copy(point2);\n        if (dist < threshold2) return Math.sqrt(dist);\n      }\n    }\n\n    // generate and check all line segment distances\n    let count = 0;\n    for (let i = 0; i < 3; i++) {\n      for (let i1 = 0; i1 <= 1; i1++) {\n        for (let i2 = 0; i2 <= 1; i2++) {\n          const nextIndex = (i + 1) % 3;\n          const nextIndex2 = (i + 2) % 3;\n\n          // get obb line segments\n          const index = i1 << nextIndex | i2 << nextIndex2;\n          const index2 = 1 << i | i1 << nextIndex | i2 << nextIndex2;\n          const p1 = points[index];\n          const p2 = points[index2];\n          const line1 = segments1[count];\n          line1.set(p1, p2);\n\n          // get aabb line segments\n          const f1 = xyzFields[i];\n          const f2 = xyzFields[nextIndex];\n          const f3 = xyzFields[nextIndex2];\n          const line2 = segments2[count];\n          const start = line2.start;\n          const end = line2.end;\n          start[f1] = min[f1];\n          start[f2] = i1 ? min[f2] : max[f2];\n          start[f3] = i2 ? min[f3] : max[f2];\n          end[f1] = max[f1];\n          end[f2] = i1 ? min[f2] : max[f2];\n          end[f3] = i2 ? min[f3] : max[f2];\n          count++;\n        }\n      }\n    }\n\n    // check all the other boxes point\n    for (let x = 0; x <= 1; x++) {\n      for (let y = 0; y <= 1; y++) {\n        for (let z = 0; z <= 1; z++) {\n          point2.x = x ? max.x : min.x;\n          point2.y = y ? max.y : min.y;\n          point2.z = z ? max.z : min.z;\n          this.closestPointToPoint(point2, point1);\n          const dist = point2.distanceToSquared(point1);\n          if (dist < closestDistanceSq) {\n            closestDistanceSq = dist;\n            if (target1) target1.copy(point1);\n            if (target2) target2.copy(point2);\n            if (dist < threshold2) return Math.sqrt(dist);\n          }\n        }\n      }\n    }\n    for (let i = 0; i < 12; i++) {\n      const l1 = segments1[i];\n      for (let i2 = 0; i2 < 12; i2++) {\n        const l2 = segments2[i2];\n        closestPointsSegmentToSegment(l1, l2, point1, point2);\n        const dist = point1.distanceToSquared(point2);\n        if (dist < closestDistanceSq) {\n          closestDistanceSq = dist;\n          if (target1) target1.copy(point1);\n          if (target2) target2.copy(point2);\n          if (dist < threshold2) return Math.sqrt(dist);\n        }\n      }\n    }\n    return Math.sqrt(closestDistanceSq);\n  };\n}();", "map": {"version": 3, "names": ["Vector3", "Matrix4", "Line3", "SeparatingAxisBounds", "ExtendedTriangle", "closestPointsSegmentToSegment", "OrientedBox", "constructor", "min", "max", "matrix", "isOrientedBox", "invMatrix", "points", "Array", "fill", "map", "satAxes", "satBounds", "alignedSatBounds", "needsUpdate", "copy", "set", "other", "prototype", "update", "x", "y", "z", "i", "v", "applyMatrix4", "minVec", "axis", "sb", "index", "pi", "subVectors", "setFromPoints", "setFromPointsField", "invert", "intersectsBox", "aabbBounds", "box", "isSeparated", "setFromBox", "intersectsTriangle", "saTri", "pointsArr", "cachedSatBounds", "cachedSatBounds2", "cachedAxis", "triangle", "isExtendedTriangle", "a", "b", "c", "sa", "triSatBounds", "triSatAxes", "sa1", "i2", "sa2", "crossVectors", "closestPointToPoint", "point", "target1", "clamp", "distanceToPoint", "target", "distanceTo", "distanceToBox", "xyzFields", "segments1", "segments2", "point1", "point2", "threshold", "target2", "getCenter", "threshold2", "closestDistanceSq", "Infinity", "p", "dist", "distanceToSquared", "Math", "sqrt", "count", "i1", "nextIndex", "nextIndex2", "index2", "p1", "p2", "line1", "f1", "f2", "f3", "line2", "start", "end", "l1", "l2"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/math/OrientedBox.js"], "sourcesContent": ["import { Vector3, Matrix4, Line3 } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { ExtendedTriangle } from './ExtendedTriangle.js';\nimport { closestPointsSegmentToSegment } from './MathUtilities.js';\n\nexport class OrientedBox {\n\n\tconstructor( min, max, matrix ) {\n\n\t\tthis.isOrientedBox = true;\n\t\tthis.min = new Vector3();\n\t\tthis.max = new Vector3();\n\t\tthis.matrix = new Matrix4();\n\t\tthis.invMatrix = new Matrix4();\n\t\tthis.points = new Array( 8 ).fill().map( () => new Vector3() );\n\t\tthis.satAxes = new Array( 3 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.alignedSatBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.needsUpdate = false;\n\n\t\tif ( min ) this.min.copy( min );\n\t\tif ( max ) this.max.copy( max );\n\t\tif ( matrix ) this.matrix.copy( matrix );\n\n\t}\n\n\tset( min, max, matrix ) {\n\n\t\tthis.min.copy( min );\n\t\tthis.max.copy( max );\n\t\tthis.matrix.copy( matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tcopy( other ) {\n\n\t\tthis.min.copy( other.min );\n\t\tthis.max.copy( other.max );\n\t\tthis.matrix.copy( other.matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n}\n\nOrientedBox.prototype.update = ( function () {\n\n\treturn function update() {\n\n\t\tconst matrix = this.matrix;\n\t\tconst min = this.min;\n\t\tconst max = this.max;\n\n\t\tconst points = this.points;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tconst i = ( ( 1 << 0 ) * x ) | ( ( 1 << 1 ) * y ) | ( ( 1 << 2 ) * z );\n\t\t\t\t\tconst v = points[ i ];\n\t\t\t\t\tv.x = x ? max.x : min.x;\n\t\t\t\t\tv.y = y ? max.y : min.y;\n\t\t\t\t\tv.z = z ? max.z : min.z;\n\n\t\t\t\t\tv.applyMatrix4( matrix );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst minVec = points[ 0 ];\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst index = 1 << i;\n\t\t\tconst pi = points[ index ];\n\n\t\t\taxis.subVectors( minVec, pi );\n\t\t\tsb.setFromPoints( axis, points );\n\n\t\t}\n\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\t\talignedSatBounds[ 0 ].setFromPointsField( points, 'x' );\n\t\talignedSatBounds[ 1 ].setFromPointsField( points, 'y' );\n\t\talignedSatBounds[ 2 ].setFromPointsField( points, 'z' );\n\n\t\tthis.invMatrix.copy( this.matrix ).invert();\n\t\tthis.needsUpdate = false;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsBox = ( function () {\n\n\tconst aabbBounds = new SeparatingAxisBounds();\n\treturn function intersectsBox( box ) {\n\n\t\t// TODO: should this be doing SAT against the AABB?\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\n\t\taabbBounds.min = min.x;\n\t\taabbBounds.max = max.x;\n\t\tif ( alignedSatBounds[ 0 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.y;\n\t\taabbBounds.max = max.y;\n\t\tif ( alignedSatBounds[ 1 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.z;\n\t\taabbBounds.max = max.z;\n\t\tif ( alignedSatBounds[ 2 ].isSeparated( aabbBounds ) ) return false;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\taabbBounds.setFromBox( axis, box );\n\t\t\tif ( sb.isSeparated( aabbBounds ) ) return false;\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri = new ExtendedTriangle();\n\tconst pointsArr = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\treturn function intersectsTriangle( triangle ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! triangle.isExtendedTriangle ) {\n\n\t\t\tsaTri.copy( triangle );\n\t\t\tsaTri.update();\n\t\t\ttriangle = saTri;\n\n\t\t} else if ( triangle.needsUpdate ) {\n\n\t\t\ttriangle.update();\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\n\t\tpointsArr[ 0 ] = triangle.a;\n\t\tpointsArr[ 1 ] = triangle.b;\n\t\tpointsArr[ 2 ] = triangle.c;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst sa = satAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, pointsArr );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\tconst triSatBounds = triangle.satBounds;\n\t\tconst triSatAxes = triangle.satAxes;\n\t\tconst points = this.points;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = triSatBounds[ i ];\n\t\t\tconst sa = triSatAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, points );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check crossed axes\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sa1 = satAxes[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\tconst sa2 = triSatAxes[ i2 ];\n\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, pointsArr );\n\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, points );\n\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.closestPointToPoint = ( function () {\n\n\treturn function closestPointToPoint( point, target1 ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\ttarget1\n\t\t\t.copy( point )\n\t\t\t.applyMatrix4( this.invMatrix )\n\t\t\t.clamp( this.min, this.max )\n\t\t\t.applyMatrix4( this.matrix );\n\n\t\treturn target1;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToBox = ( function () {\n\n\tconst xyzFields = [ 'x', 'y', 'z' ];\n\tconst segments1 = new Array( 12 ).fill().map( () => new Line3() );\n\tconst segments2 = new Array( 12 ).fill().map( () => new Line3() );\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\n\t// early out if we find a value below threshold\n\treturn function distanceToBox( box, threshold = 0, target1 = null, target2 = null ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( this.intersectsBox( box ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tbox.getCenter( point2 );\n\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\tbox.closestPointToPoint( point1, point2 );\n\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tconst threshold2 = threshold * threshold;\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst points = this.points;\n\n\n\t\t// iterate over every edge and compare distances\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check over all these points\n\t\tfor ( let i = 0; i < 8; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tpoint2.copy( p ).clamp( min, max );\n\n\t\t\tconst dist = p.distanceToSquared( point2 );\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( p );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// generate and check all line segment distances\n\t\tlet count = 0;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tfor ( let i1 = 0; i1 <= 1; i1 ++ ) {\n\n\t\t\t\tfor ( let i2 = 0; i2 <= 1; i2 ++ ) {\n\n\t\t\t\t\tconst nextIndex = ( i + 1 ) % 3;\n\t\t\t\t\tconst nextIndex2 = ( i + 2 ) % 3;\n\n\t\t\t\t\t// get obb line segments\n\t\t\t\t\tconst index = i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst index2 = 1 << i | i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst p1 = points[ index ];\n\t\t\t\t\tconst p2 = points[ index2 ];\n\t\t\t\t\tconst line1 = segments1[ count ];\n\t\t\t\t\tline1.set( p1, p2 );\n\n\n\t\t\t\t\t// get aabb line segments\n\t\t\t\t\tconst f1 = xyzFields[ i ];\n\t\t\t\t\tconst f2 = xyzFields[ nextIndex ];\n\t\t\t\t\tconst f3 = xyzFields[ nextIndex2 ];\n\t\t\t\t\tconst line2 = segments2[ count ];\n\t\t\t\t\tconst start = line2.start;\n\t\t\t\t\tconst end = line2.end;\n\n\t\t\t\t\tstart[ f1 ] = min[ f1 ];\n\t\t\t\t\tstart[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tstart[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tend[ f1 ] = max[ f1 ];\n\t\t\t\t\tend[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tend[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tcount ++;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check all the other boxes point\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tpoint2.x = x ? max.x : min.x;\n\t\t\t\t\tpoint2.y = y ? max.y : min.y;\n\t\t\t\t\tpoint2.z = z ? max.z : min.z;\n\n\t\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\t\tconst dist = point2.distanceToSquared( point1 );\n\t\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 12; i ++ ) {\n\n\t\t\tconst l1 = segments1[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 12; i2 ++ ) {\n\n\t\t\t\tconst l2 = segments2[ i2 ];\n\t\t\t\tclosestPointsSegmentToSegment( l1, l2, point1, point2 );\n\t\t\t\tconst dist = point1.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,OAAO;AAC/C,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,6BAA6B,QAAQ,oBAAoB;AAElE,OAAO,MAAMC,WAAW,CAAC;EAExBC,WAAWA,CAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAG;IAE/B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACH,GAAG,GAAG,IAAIR,OAAO,CAAC,CAAC;IACxB,IAAI,CAACS,GAAG,GAAG,IAAIT,OAAO,CAAC,CAAC;IACxB,IAAI,CAACU,MAAM,GAAG,IAAIT,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACW,SAAS,GAAG,IAAIX,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACY,MAAM,GAAG,IAAIC,KAAK,CAAE,CAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAIhB,OAAO,CAAC,CAAE,CAAC;IAC9D,IAAI,CAACiB,OAAO,GAAG,IAAIH,KAAK,CAAE,CAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAIhB,OAAO,CAAC,CAAE,CAAC;IAC/D,IAAI,CAACkB,SAAS,GAAG,IAAIJ,KAAK,CAAE,CAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAIb,oBAAoB,CAAC,CAAE,CAAC;IAC9E,IAAI,CAACgB,gBAAgB,GAAG,IAAIL,KAAK,CAAE,CAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAIb,oBAAoB,CAAC,CAAE,CAAC;IACrF,IAAI,CAACiB,WAAW,GAAG,KAAK;IAExB,IAAKZ,GAAG,EAAG,IAAI,CAACA,GAAG,CAACa,IAAI,CAAEb,GAAI,CAAC;IAC/B,IAAKC,GAAG,EAAG,IAAI,CAACA,GAAG,CAACY,IAAI,CAAEZ,GAAI,CAAC;IAC/B,IAAKC,MAAM,EAAG,IAAI,CAACA,MAAM,CAACW,IAAI,CAAEX,MAAO,CAAC;EAEzC;EAEAY,GAAGA,CAAEd,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAG;IAEvB,IAAI,CAACF,GAAG,CAACa,IAAI,CAAEb,GAAI,CAAC;IACpB,IAAI,CAACC,GAAG,CAACY,IAAI,CAAEZ,GAAI,CAAC;IACpB,IAAI,CAACC,MAAM,CAACW,IAAI,CAAEX,MAAO,CAAC;IAC1B,IAAI,CAACU,WAAW,GAAG,IAAI;EAExB;EAEAC,IAAIA,CAAEE,KAAK,EAAG;IAEb,IAAI,CAACf,GAAG,CAACa,IAAI,CAAEE,KAAK,CAACf,GAAI,CAAC;IAC1B,IAAI,CAACC,GAAG,CAACY,IAAI,CAAEE,KAAK,CAACd,GAAI,CAAC;IAC1B,IAAI,CAACC,MAAM,CAACW,IAAI,CAAEE,KAAK,CAACb,MAAO,CAAC;IAChC,IAAI,CAACU,WAAW,GAAG,IAAI;EAExB;AAED;AAEAd,WAAW,CAACkB,SAAS,CAACC,MAAM,GAAK,YAAY;EAE5C,OAAO,SAASA,MAAMA,CAAA,EAAG;IAExB,MAAMf,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMF,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,MAAMC,GAAG,GAAG,IAAI,CAACA,GAAG;IAEpB,MAAMI,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,KAAM,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE/B,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE/B,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;UAE/B,MAAMC,CAAC,GAAK,CAAE,CAAC,IAAI,CAAC,IAAKH,CAAC,GAAO,CAAE,CAAC,IAAI,CAAC,IAAKC,CAAG,GAAK,CAAE,CAAC,IAAI,CAAC,IAAKC,CAAG;UACtE,MAAME,CAAC,GAAGjB,MAAM,CAAEgB,CAAC,CAAE;UACrBC,CAAC,CAACJ,CAAC,GAAGA,CAAC,GAAGjB,GAAG,CAACiB,CAAC,GAAGlB,GAAG,CAACkB,CAAC;UACvBI,CAAC,CAACH,CAAC,GAAGA,CAAC,GAAGlB,GAAG,CAACkB,CAAC,GAAGnB,GAAG,CAACmB,CAAC;UACvBG,CAAC,CAACF,CAAC,GAAGA,CAAC,GAAGnB,GAAG,CAACmB,CAAC,GAAGpB,GAAG,CAACoB,CAAC;UAEvBE,CAAC,CAACC,YAAY,CAAErB,MAAO,CAAC;QAEzB;MAED;IAED;IAEA,MAAMQ,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMe,MAAM,GAAGnB,MAAM,CAAE,CAAC,CAAE;IAC1B,KAAM,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMI,IAAI,GAAGhB,OAAO,CAAEY,CAAC,CAAE;MACzB,MAAMK,EAAE,GAAGhB,SAAS,CAAEW,CAAC,CAAE;MACzB,MAAMM,KAAK,GAAG,CAAC,IAAIN,CAAC;MACpB,MAAMO,EAAE,GAAGvB,MAAM,CAAEsB,KAAK,CAAE;MAE1BF,IAAI,CAACI,UAAU,CAAEL,MAAM,EAAEI,EAAG,CAAC;MAC7BF,EAAE,CAACI,aAAa,CAAEL,IAAI,EAAEpB,MAAO,CAAC;IAEjC;IAEA,MAAMM,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9CA,gBAAgB,CAAE,CAAC,CAAE,CAACoB,kBAAkB,CAAE1B,MAAM,EAAE,GAAI,CAAC;IACvDM,gBAAgB,CAAE,CAAC,CAAE,CAACoB,kBAAkB,CAAE1B,MAAM,EAAE,GAAI,CAAC;IACvDM,gBAAgB,CAAE,CAAC,CAAE,CAACoB,kBAAkB,CAAE1B,MAAM,EAAE,GAAI,CAAC;IAEvD,IAAI,CAACD,SAAS,CAACS,IAAI,CAAE,IAAI,CAACX,MAAO,CAAC,CAAC8B,MAAM,CAAC,CAAC;IAC3C,IAAI,CAACpB,WAAW,GAAG,KAAK;EAEzB,CAAC;AAEF,CAAC,CAAG,CAAC;AAELd,WAAW,CAACkB,SAAS,CAACiB,aAAa,GAAK,YAAY;EAEnD,MAAMC,UAAU,GAAG,IAAIvC,oBAAoB,CAAC,CAAC;EAC7C,OAAO,SAASsC,aAAaA,CAAEE,GAAG,EAAG;IAEpC;IACA,IAAK,IAAI,CAACvB,WAAW,EAAG;MAEvB,IAAI,CAACK,MAAM,CAAC,CAAC;IAEd;IAEA,MAAMjB,GAAG,GAAGmC,GAAG,CAACnC,GAAG;IACnB,MAAMC,GAAG,GAAGkC,GAAG,CAAClC,GAAG;IACnB,MAAMS,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAME,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAE9CuB,UAAU,CAAClC,GAAG,GAAGA,GAAG,CAACkB,CAAC;IACtBgB,UAAU,CAACjC,GAAG,GAAGA,GAAG,CAACiB,CAAC;IACtB,IAAKP,gBAAgB,CAAE,CAAC,CAAE,CAACyB,WAAW,CAAEF,UAAW,CAAC,EAAG,OAAO,KAAK;IAEnEA,UAAU,CAAClC,GAAG,GAAGA,GAAG,CAACmB,CAAC;IACtBe,UAAU,CAACjC,GAAG,GAAGA,GAAG,CAACkB,CAAC;IACtB,IAAKR,gBAAgB,CAAE,CAAC,CAAE,CAACyB,WAAW,CAAEF,UAAW,CAAC,EAAG,OAAO,KAAK;IAEnEA,UAAU,CAAClC,GAAG,GAAGA,GAAG,CAACoB,CAAC;IACtBc,UAAU,CAACjC,GAAG,GAAGA,GAAG,CAACmB,CAAC;IACtB,IAAKT,gBAAgB,CAAE,CAAC,CAAE,CAACyB,WAAW,CAAEF,UAAW,CAAC,EAAG,OAAO,KAAK;IAEnE,KAAM,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMI,IAAI,GAAGhB,OAAO,CAAEY,CAAC,CAAE;MACzB,MAAMK,EAAE,GAAGhB,SAAS,CAAEW,CAAC,CAAE;MACzBa,UAAU,CAACG,UAAU,CAAEZ,IAAI,EAAEU,GAAI,CAAC;MAClC,IAAKT,EAAE,CAACU,WAAW,CAAEF,UAAW,CAAC,EAAG,OAAO,KAAK;IAEjD;IAEA,OAAO,IAAI;EAEZ,CAAC;AAEF,CAAC,CAAG,CAAC;AAELpC,WAAW,CAACkB,SAAS,CAACsB,kBAAkB,GAAK,YAAY;EAExD,MAAMC,KAAK,GAAG,IAAI3C,gBAAgB,CAAC,CAAC;EACpC,MAAM4C,SAAS,GAAG,IAAIlC,KAAK,CAAE,CAAE,CAAC;EAChC,MAAMmC,eAAe,GAAG,IAAI9C,oBAAoB,CAAC,CAAC;EAClD,MAAM+C,gBAAgB,GAAG,IAAI/C,oBAAoB,CAAC,CAAC;EACnD,MAAMgD,UAAU,GAAG,IAAInD,OAAO,CAAC,CAAC;EAChC,OAAO,SAAS8C,kBAAkBA,CAAEM,QAAQ,EAAG;IAE9C,IAAK,IAAI,CAAChC,WAAW,EAAG;MAEvB,IAAI,CAACK,MAAM,CAAC,CAAC;IAEd;IAEA,IAAK,CAAE2B,QAAQ,CAACC,kBAAkB,EAAG;MAEpCN,KAAK,CAAC1B,IAAI,CAAE+B,QAAS,CAAC;MACtBL,KAAK,CAACtB,MAAM,CAAC,CAAC;MACd2B,QAAQ,GAAGL,KAAK;IAEjB,CAAC,MAAM,IAAKK,QAAQ,CAAChC,WAAW,EAAG;MAElCgC,QAAQ,CAAC3B,MAAM,CAAC,CAAC;IAElB;IAEA,MAAMP,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMD,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B+B,SAAS,CAAE,CAAC,CAAE,GAAGI,QAAQ,CAACE,CAAC;IAC3BN,SAAS,CAAE,CAAC,CAAE,GAAGI,QAAQ,CAACG,CAAC;IAC3BP,SAAS,CAAE,CAAC,CAAE,GAAGI,QAAQ,CAACI,CAAC;IAE3B,KAAM,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMK,EAAE,GAAGhB,SAAS,CAAEW,CAAC,CAAE;MACzB,MAAM4B,EAAE,GAAGxC,OAAO,CAAEY,CAAC,CAAE;MACvBoB,eAAe,CAACX,aAAa,CAAEmB,EAAE,EAAET,SAAU,CAAC;MAC9C,IAAKd,EAAE,CAACU,WAAW,CAAEK,eAAgB,CAAC,EAAG,OAAO,KAAK;IAEtD;IAEA,MAAMS,YAAY,GAAGN,QAAQ,CAAClC,SAAS;IACvC,MAAMyC,UAAU,GAAGP,QAAQ,CAACnC,OAAO;IACnC,MAAMJ,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,KAAM,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMK,EAAE,GAAGwB,YAAY,CAAE7B,CAAC,CAAE;MAC5B,MAAM4B,EAAE,GAAGE,UAAU,CAAE9B,CAAC,CAAE;MAC1BoB,eAAe,CAACX,aAAa,CAAEmB,EAAE,EAAE5C,MAAO,CAAC;MAC3C,IAAKqB,EAAE,CAACU,WAAW,CAAEK,eAAgB,CAAC,EAAG,OAAO,KAAK;IAEtD;;IAEA;IACA,KAAM,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAM+B,GAAG,GAAG3C,OAAO,CAAEY,CAAC,CAAE;MACxB,KAAM,IAAIgC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAG,EAAG;QAEjC,MAAMC,GAAG,GAAGH,UAAU,CAAEE,EAAE,CAAE;QAC5BV,UAAU,CAACY,YAAY,CAAEH,GAAG,EAAEE,GAAI,CAAC;QACnCb,eAAe,CAACX,aAAa,CAAEa,UAAU,EAAEH,SAAU,CAAC;QACtDE,gBAAgB,CAACZ,aAAa,CAAEa,UAAU,EAAEtC,MAAO,CAAC;QACpD,IAAKoC,eAAe,CAACL,WAAW,CAAEM,gBAAiB,CAAC,EAAG,OAAO,KAAK;MAEpE;IAED;IAEA,OAAO,IAAI;EAEZ,CAAC;AAEF,CAAC,CAAG,CAAC;AAEL5C,WAAW,CAACkB,SAAS,CAACwC,mBAAmB,GAAK,YAAY;EAEzD,OAAO,SAASA,mBAAmBA,CAAEC,KAAK,EAAEC,OAAO,EAAG;IAErD,IAAK,IAAI,CAAC9C,WAAW,EAAG;MAEvB,IAAI,CAACK,MAAM,CAAC,CAAC;IAEd;IAEAyC,OAAO,CACL7C,IAAI,CAAE4C,KAAM,CAAC,CACblC,YAAY,CAAE,IAAI,CAACnB,SAAU,CAAC,CAC9BuD,KAAK,CAAE,IAAI,CAAC3D,GAAG,EAAE,IAAI,CAACC,GAAI,CAAC,CAC3BsB,YAAY,CAAE,IAAI,CAACrB,MAAO,CAAC;IAE7B,OAAOwD,OAAO;EAEf,CAAC;AAEF,CAAC,CAAG,CAAC;AAEL5D,WAAW,CAACkB,SAAS,CAAC4C,eAAe,GAAK,YAAY;EAErD,MAAMC,MAAM,GAAG,IAAIrE,OAAO,CAAC,CAAC;EAC5B,OAAO,SAASoE,eAAeA,CAAEH,KAAK,EAAG;IAExC,IAAI,CAACD,mBAAmB,CAAEC,KAAK,EAAEI,MAAO,CAAC;IACzC,OAAOJ,KAAK,CAACK,UAAU,CAAED,MAAO,CAAC;EAElC,CAAC;AAEF,CAAC,CAAG,CAAC;AAEL/D,WAAW,CAACkB,SAAS,CAAC+C,aAAa,GAAK,YAAY;EAEnD,MAAMC,SAAS,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;EACnC,MAAMC,SAAS,GAAG,IAAI3D,KAAK,CAAE,EAAG,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAId,KAAK,CAAC,CAAE,CAAC;EACjE,MAAMwE,SAAS,GAAG,IAAI5D,KAAK,CAAE,EAAG,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAId,KAAK,CAAC,CAAE,CAAC;EAEjE,MAAMyE,MAAM,GAAG,IAAI3E,OAAO,CAAC,CAAC;EAC5B,MAAM4E,MAAM,GAAG,IAAI5E,OAAO,CAAC,CAAC;;EAE5B;EACA,OAAO,SAASuE,aAAaA,CAAE5B,GAAG,EAAEkC,SAAS,GAAG,CAAC,EAAEX,OAAO,GAAG,IAAI,EAAEY,OAAO,GAAG,IAAI,EAAG;IAEnF,IAAK,IAAI,CAAC1D,WAAW,EAAG;MAEvB,IAAI,CAACK,MAAM,CAAC,CAAC;IAEd;IAEA,IAAK,IAAI,CAACgB,aAAa,CAAEE,GAAI,CAAC,EAAG;MAEhC,IAAKuB,OAAO,IAAIY,OAAO,EAAG;QAEzBnC,GAAG,CAACoC,SAAS,CAAEH,MAAO,CAAC;QACvB,IAAI,CAACZ,mBAAmB,CAAEY,MAAM,EAAED,MAAO,CAAC;QAC1ChC,GAAG,CAACqB,mBAAmB,CAAEW,MAAM,EAAEC,MAAO,CAAC;QAEzC,IAAKV,OAAO,EAAGA,OAAO,CAAC7C,IAAI,CAAEsD,MAAO,CAAC;QACrC,IAAKG,OAAO,EAAGA,OAAO,CAACzD,IAAI,CAAEuD,MAAO,CAAC;MAEtC;MAEA,OAAO,CAAC;IAET;IAEA,MAAMI,UAAU,GAAGH,SAAS,GAAGA,SAAS;IACxC,MAAMrE,GAAG,GAAGmC,GAAG,CAACnC,GAAG;IACnB,MAAMC,GAAG,GAAGkC,GAAG,CAAClC,GAAG;IACnB,MAAMI,MAAM,GAAG,IAAI,CAACA,MAAM;;IAG1B;IACA,IAAIoE,iBAAiB,GAAGC,QAAQ;;IAEhC;IACA,KAAM,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMsD,CAAC,GAAGtE,MAAM,CAAEgB,CAAC,CAAE;MACrB+C,MAAM,CAACvD,IAAI,CAAE8D,CAAE,CAAC,CAAChB,KAAK,CAAE3D,GAAG,EAAEC,GAAI,CAAC;MAElC,MAAM2E,IAAI,GAAGD,CAAC,CAACE,iBAAiB,CAAET,MAAO,CAAC;MAC1C,IAAKQ,IAAI,GAAGH,iBAAiB,EAAG;QAE/BA,iBAAiB,GAAGG,IAAI;QACxB,IAAKlB,OAAO,EAAGA,OAAO,CAAC7C,IAAI,CAAE8D,CAAE,CAAC;QAChC,IAAKL,OAAO,EAAGA,OAAO,CAACzD,IAAI,CAAEuD,MAAO,CAAC;QAErC,IAAKQ,IAAI,GAAGJ,UAAU,EAAG,OAAOM,IAAI,CAACC,IAAI,CAAEH,IAAK,CAAC;MAElD;IAED;;IAEA;IACA,IAAII,KAAK,GAAG,CAAC;IACb,KAAM,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,KAAM,IAAI4D,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAG,EAAG;QAElC,KAAM,IAAI5B,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAG,EAAG;UAElC,MAAM6B,SAAS,GAAG,CAAE7D,CAAC,GAAG,CAAC,IAAK,CAAC;UAC/B,MAAM8D,UAAU,GAAG,CAAE9D,CAAC,GAAG,CAAC,IAAK,CAAC;;UAEhC;UACA,MAAMM,KAAK,GAAGsD,EAAE,IAAIC,SAAS,GAAG7B,EAAE,IAAI8B,UAAU;UAChD,MAAMC,MAAM,GAAG,CAAC,IAAI/D,CAAC,GAAG4D,EAAE,IAAIC,SAAS,GAAG7B,EAAE,IAAI8B,UAAU;UAC1D,MAAME,EAAE,GAAGhF,MAAM,CAAEsB,KAAK,CAAE;UAC1B,MAAM2D,EAAE,GAAGjF,MAAM,CAAE+E,MAAM,CAAE;UAC3B,MAAMG,KAAK,GAAGtB,SAAS,CAAEe,KAAK,CAAE;UAChCO,KAAK,CAACzE,GAAG,CAAEuE,EAAE,EAAEC,EAAG,CAAC;;UAGnB;UACA,MAAME,EAAE,GAAGxB,SAAS,CAAE3C,CAAC,CAAE;UACzB,MAAMoE,EAAE,GAAGzB,SAAS,CAAEkB,SAAS,CAAE;UACjC,MAAMQ,EAAE,GAAG1B,SAAS,CAAEmB,UAAU,CAAE;UAClC,MAAMQ,KAAK,GAAGzB,SAAS,CAAEc,KAAK,CAAE;UAChC,MAAMY,KAAK,GAAGD,KAAK,CAACC,KAAK;UACzB,MAAMC,GAAG,GAAGF,KAAK,CAACE,GAAG;UAErBD,KAAK,CAAEJ,EAAE,CAAE,GAAGxF,GAAG,CAAEwF,EAAE,CAAE;UACvBI,KAAK,CAAEH,EAAE,CAAE,GAAGR,EAAE,GAAGjF,GAAG,CAAEyF,EAAE,CAAE,GAAGxF,GAAG,CAAEwF,EAAE,CAAE;UACxCG,KAAK,CAAEF,EAAE,CAAE,GAAGrC,EAAE,GAAGrD,GAAG,CAAE0F,EAAE,CAAE,GAAGzF,GAAG,CAAEwF,EAAE,CAAE;UAExCI,GAAG,CAAEL,EAAE,CAAE,GAAGvF,GAAG,CAAEuF,EAAE,CAAE;UACrBK,GAAG,CAAEJ,EAAE,CAAE,GAAGR,EAAE,GAAGjF,GAAG,CAAEyF,EAAE,CAAE,GAAGxF,GAAG,CAAEwF,EAAE,CAAE;UACtCI,GAAG,CAAEH,EAAE,CAAE,GAAGrC,EAAE,GAAGrD,GAAG,CAAE0F,EAAE,CAAE,GAAGzF,GAAG,CAAEwF,EAAE,CAAE;UAEtCT,KAAK,EAAG;QAET;MAED;IAED;;IAEA;IACA,KAAM,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE/B,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE/B,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;UAE/BgD,MAAM,CAAClD,CAAC,GAAGA,CAAC,GAAGjB,GAAG,CAACiB,CAAC,GAAGlB,GAAG,CAACkB,CAAC;UAC5BkD,MAAM,CAACjD,CAAC,GAAGA,CAAC,GAAGlB,GAAG,CAACkB,CAAC,GAAGnB,GAAG,CAACmB,CAAC;UAC5BiD,MAAM,CAAChD,CAAC,GAAGA,CAAC,GAAGnB,GAAG,CAACmB,CAAC,GAAGpB,GAAG,CAACoB,CAAC;UAE5B,IAAI,CAACoC,mBAAmB,CAAEY,MAAM,EAAED,MAAO,CAAC;UAC1C,MAAMS,IAAI,GAAGR,MAAM,CAACS,iBAAiB,CAAEV,MAAO,CAAC;UAC/C,IAAKS,IAAI,GAAGH,iBAAiB,EAAG;YAE/BA,iBAAiB,GAAGG,IAAI;YACxB,IAAKlB,OAAO,EAAGA,OAAO,CAAC7C,IAAI,CAAEsD,MAAO,CAAC;YACrC,IAAKG,OAAO,EAAGA,OAAO,CAACzD,IAAI,CAAEuD,MAAO,CAAC;YAErC,IAAKQ,IAAI,GAAGJ,UAAU,EAAG,OAAOM,IAAI,CAACC,IAAI,CAAEH,IAAK,CAAC;UAElD;QAED;MAED;IAED;IAEA,KAAM,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAG,EAAG;MAE/B,MAAMyE,EAAE,GAAG7B,SAAS,CAAE5C,CAAC,CAAE;MACzB,KAAM,IAAIgC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,EAAE,EAAEA,EAAE,EAAG,EAAG;QAElC,MAAM0C,EAAE,GAAG7B,SAAS,CAAEb,EAAE,CAAE;QAC1BxD,6BAA6B,CAAEiG,EAAE,EAAEC,EAAE,EAAE5B,MAAM,EAAEC,MAAO,CAAC;QACvD,MAAMQ,IAAI,GAAGT,MAAM,CAACU,iBAAiB,CAAET,MAAO,CAAC;QAC/C,IAAKQ,IAAI,GAAGH,iBAAiB,EAAG;UAE/BA,iBAAiB,GAAGG,IAAI;UACxB,IAAKlB,OAAO,EAAGA,OAAO,CAAC7C,IAAI,CAAEsD,MAAO,CAAC;UACrC,IAAKG,OAAO,EAAGA,OAAO,CAACzD,IAAI,CAAEuD,MAAO,CAAC;UAErC,IAAKQ,IAAI,GAAGJ,UAAU,EAAG,OAAOM,IAAI,CAACC,IAAI,CAAEH,IAAK,CAAC;QAElD;MAED;IAED;IAEA,OAAOE,IAAI,CAACC,IAAI,CAAEN,iBAAkB,CAAC;EAEtC,CAAC;AAEF,CAAC,CAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}