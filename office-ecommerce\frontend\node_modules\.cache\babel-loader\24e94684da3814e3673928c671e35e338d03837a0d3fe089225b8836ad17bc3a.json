{"ast": null, "code": "export { C as ConfigResolverMap, E as EngineMap, e as dragAction, h as hoverAction, m as moveAction, f as pinchAction, r as registerAction, s as scrollAction, w as wheelAction } from '../../dist/actions-fe213e88.esm.js';\nimport '../../dist/maths-0ab39ae9.esm.js';", "map": {"version": 3, "names": ["C", "ConfigResolverMap", "E", "EngineMap", "e", "dragAction", "h", "hoverAction", "m", "moveAction", "f", "pinchAction", "r", "registerAction", "s", "scrollAction", "w", "wheelAction"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@use-gesture/core/actions/dist/use-gesture-core-actions.esm.js"], "sourcesContent": ["export { C as ConfigResolverMap, E as EngineMap, e as dragAction, h as hoverAction, m as moveAction, f as pinchAction, r as registerAction, s as scrollAction, w as wheelAction } from '../../dist/actions-fe213e88.esm.js';\nimport '../../dist/maths-0ab39ae9.esm.js';\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,WAAW,QAAQ,oCAAoC;AAC3N,OAAO,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}