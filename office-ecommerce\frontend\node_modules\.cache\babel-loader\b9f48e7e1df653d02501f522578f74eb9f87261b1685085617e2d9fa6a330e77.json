{"ast": null, "code": "import { DefaultLoadingManager } from 'three';\nimport create from 'zustand';\nlet saveLastTotalLoaded = 0;\nconst useProgress = create(set => {\n  DefaultLoadingManager.onStart = (item, loaded, total) => {\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100\n    });\n  };\n  DefaultLoadingManager.onLoad = () => {\n    set({\n      active: false\n    });\n  };\n  DefaultLoadingManager.onError = item => set(state => ({\n    errors: [...state.errors, item]\n  }));\n  DefaultLoadingManager.onProgress = (item, loaded, total) => {\n    if (loaded === total) {\n      saveLastTotalLoaded = total;\n    }\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100 || 100\n    });\n  };\n  return {\n    errors: [],\n    active: false,\n    progress: 0,\n    item: '',\n    loaded: 0,\n    total: 0\n  };\n});\nexport { useProgress };", "map": {"version": 3, "names": ["DefaultLoadingManager", "create", "saveLastTotalLoaded", "useProgress", "set", "onStart", "item", "loaded", "total", "active", "progress", "onLoad", "onError", "state", "errors", "onProgress"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useProgress.js"], "sourcesContent": ["import { DefaultLoadingManager } from 'three';\nimport create from 'zustand';\n\nlet saveLastTotalLoaded = 0;\nconst useProgress = create(set => {\n  DefaultLoadingManager.onStart = (item, loaded, total) => {\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100\n    });\n  };\n\n  DefaultLoadingManager.onLoad = () => {\n    set({\n      active: false\n    });\n  };\n\n  DefaultLoadingManager.onError = item => set(state => ({\n    errors: [...state.errors, item]\n  }));\n\n  DefaultLoadingManager.onProgress = (item, loaded, total) => {\n    if (loaded === total) {\n      saveLastTotalLoaded = total;\n    }\n\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100 || 100\n    });\n  };\n\n  return {\n    errors: [],\n    active: false,\n    progress: 0,\n    item: '',\n    loaded: 0,\n    total: 0\n  };\n});\n\nexport { useProgress };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,OAAO;AAC7C,OAAOC,MAAM,MAAM,SAAS;AAE5B,IAAIC,mBAAmB,GAAG,CAAC;AAC3B,MAAMC,WAAW,GAAGF,MAAM,CAACG,GAAG,IAAI;EAChCJ,qBAAqB,CAACK,OAAO,GAAG,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,KAAK;IACvDJ,GAAG,CAAC;MACFK,MAAM,EAAE,IAAI;MACZH,IAAI;MACJC,MAAM;MACNC,KAAK;MACLE,QAAQ,EAAE,CAACH,MAAM,GAAGL,mBAAmB,KAAKM,KAAK,GAAGN,mBAAmB,CAAC,GAAG;IAC7E,CAAC,CAAC;EACJ,CAAC;EAEDF,qBAAqB,CAACW,MAAM,GAAG,MAAM;IACnCP,GAAG,CAAC;MACFK,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAEDT,qBAAqB,CAACY,OAAO,GAAGN,IAAI,IAAIF,GAAG,CAACS,KAAK,KAAK;IACpDC,MAAM,EAAE,CAAC,GAAGD,KAAK,CAACC,MAAM,EAAER,IAAI;EAChC,CAAC,CAAC,CAAC;EAEHN,qBAAqB,CAACe,UAAU,GAAG,CAACT,IAAI,EAAEC,MAAM,EAAEC,KAAK,KAAK;IAC1D,IAAID,MAAM,KAAKC,KAAK,EAAE;MACpBN,mBAAmB,GAAGM,KAAK;IAC7B;IAEAJ,GAAG,CAAC;MACFK,MAAM,EAAE,IAAI;MACZH,IAAI;MACJC,MAAM;MACNC,KAAK;MACLE,QAAQ,EAAE,CAACH,MAAM,GAAGL,mBAAmB,KAAKM,KAAK,GAAGN,mBAAmB,CAAC,GAAG,GAAG,IAAI;IACpF,CAAC,CAAC;EACJ,CAAC;EAED,OAAO;IACLY,MAAM,EAAE,EAAE;IACVL,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,CAAC;IACXJ,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC;AACH,CAAC,CAAC;AAEF,SAASL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}