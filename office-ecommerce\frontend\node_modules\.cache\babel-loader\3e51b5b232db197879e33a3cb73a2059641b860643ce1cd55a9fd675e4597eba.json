{"ast": null, "code": "import { InstancedBufferGeometry, Float32BufferAttribute, InstancedInterleavedBuffer, InterleavedBufferAttribute, WireframeGeometry, Box3, Sphere, Vector3 } from \"three\";\nconst _box = /* @__PURE__ */new Box3();\nconst _vector = /* @__PURE__ */new Vector3();\nclass LineSegmentsGeometry extends InstancedBufferGeometry {\n  constructor() {\n    super();\n    this.isLineSegmentsGeometry = true;\n    this.type = \"LineSegmentsGeometry\";\n    const positions = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0];\n    const uvs = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2];\n    const index = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5];\n    this.setIndex(index);\n    this.setAttribute(\"position\", new Float32BufferAttribute(positions, 3));\n    this.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n  }\n  applyMatrix4(matrix) {\n    const start = this.attributes.instanceStart;\n    const end = this.attributes.instanceEnd;\n    if (start !== void 0) {\n      start.applyMatrix4(matrix);\n      end.applyMatrix4(matrix);\n      start.needsUpdate = true;\n    }\n    if (this.boundingBox !== null) {\n      this.computeBoundingBox();\n    }\n    if (this.boundingSphere !== null) {\n      this.computeBoundingSphere();\n    }\n    return this;\n  }\n  setPositions(array) {\n    let lineSegments;\n    if (array instanceof Float32Array) {\n      lineSegments = array;\n    } else if (Array.isArray(array)) {\n      lineSegments = new Float32Array(array);\n    }\n    const instanceBuffer = new InstancedInterleavedBuffer(lineSegments, 6, 1);\n    this.setAttribute(\"instanceStart\", new InterleavedBufferAttribute(instanceBuffer, 3, 0));\n    this.setAttribute(\"instanceEnd\", new InterleavedBufferAttribute(instanceBuffer, 3, 3));\n    this.computeBoundingBox();\n    this.computeBoundingSphere();\n    return this;\n  }\n  setColors(array, itemSize = 3) {\n    let colors;\n    if (array instanceof Float32Array) {\n      colors = array;\n    } else if (Array.isArray(array)) {\n      colors = new Float32Array(array);\n    }\n    const instanceColorBuffer = new InstancedInterleavedBuffer(colors, itemSize * 2, 1);\n    this.setAttribute(\"instanceColorStart\", new InterleavedBufferAttribute(instanceColorBuffer, itemSize, 0));\n    this.setAttribute(\"instanceColorEnd\", new InterleavedBufferAttribute(instanceColorBuffer, itemSize, itemSize));\n    return this;\n  }\n  fromWireframeGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array);\n    return this;\n  }\n  fromEdgesGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array);\n    return this;\n  }\n  fromMesh(mesh) {\n    this.fromWireframeGeometry(new WireframeGeometry(mesh.geometry));\n    return this;\n  }\n  fromLineSegments(lineSegments) {\n    const geometry = lineSegments.geometry;\n    this.setPositions(geometry.attributes.position.array);\n    return this;\n  }\n  computeBoundingBox() {\n    if (this.boundingBox === null) {\n      this.boundingBox = new Box3();\n    }\n    const start = this.attributes.instanceStart;\n    const end = this.attributes.instanceEnd;\n    if (start !== void 0 && end !== void 0) {\n      this.boundingBox.setFromBufferAttribute(start);\n      _box.setFromBufferAttribute(end);\n      this.boundingBox.union(_box);\n    }\n  }\n  computeBoundingSphere() {\n    if (this.boundingSphere === null) {\n      this.boundingSphere = new Sphere();\n    }\n    if (this.boundingBox === null) {\n      this.computeBoundingBox();\n    }\n    const start = this.attributes.instanceStart;\n    const end = this.attributes.instanceEnd;\n    if (start !== void 0 && end !== void 0) {\n      const center = this.boundingSphere.center;\n      this.boundingBox.getCenter(center);\n      let maxRadiusSq = 0;\n      for (let i = 0, il = start.count; i < il; i++) {\n        _vector.fromBufferAttribute(start, i);\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector));\n        _vector.fromBufferAttribute(end, i);\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector));\n      }\n      this.boundingSphere.radius = Math.sqrt(maxRadiusSq);\n      if (isNaN(this.boundingSphere.radius)) {\n        console.error(\"THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.\", this);\n      }\n    }\n  }\n  toJSON() {}\n  applyMatrix(matrix) {\n    console.warn(\"THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().\");\n    return this.applyMatrix4(matrix);\n  }\n}\nexport { LineSegmentsGeometry };", "map": {"version": 3, "names": ["_box", "Box3", "_vector", "Vector3", "LineSegmentsGeometry", "InstancedBufferGeometry", "constructor", "isLineSegmentsGeometry", "type", "positions", "uvs", "index", "setIndex", "setAttribute", "Float32BufferAttribute", "applyMatrix4", "matrix", "start", "attributes", "instanceStart", "end", "instanceEnd", "needsUpdate", "boundingBox", "computeBoundingBox", "boundingSphere", "computeBoundingSphere", "setPositions", "array", "lineSegments", "Float32Array", "Array", "isArray", "instance<PERSON><PERSON><PERSON>", "InstancedInterleavedBuffer", "InterleavedBufferAttribute", "setColors", "itemSize", "colors", "instanceColorBuffer", "fromWireframeGeometry", "geometry", "position", "fromEdgesGeometry", "fromMesh", "mesh", "WireframeGeometry", "fromLineSegments", "setFromBufferAttribute", "union", "Sphere", "center", "getCenter", "maxRadiusSq", "i", "il", "count", "fromBufferAttribute", "Math", "max", "distanceToSquared", "radius", "sqrt", "isNaN", "console", "error", "toJSON", "applyMatrix", "warn"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\lines\\LineSegmentsGeometry.js"], "sourcesContent": ["import {\n  Box3,\n  Float32<PERSON><PERSON>er<PERSON>ttribute,\n  InstancedBufferGeometry,\n  InstancedInterleavedBuffer,\n  InterleavedBufferAttribute,\n  Sphere,\n  Vector3,\n  WireframeGeometry,\n} from 'three'\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _vector = /* @__PURE__ */ new Vector3()\n\nclass LineSegmentsGeometry extends InstancedBufferGeometry {\n  constructor() {\n    super()\n\n    this.isLineSegmentsGeometry = true\n\n    this.type = 'LineSegmentsGeometry'\n\n    const positions = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0]\n    const uvs = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2]\n    const index = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5]\n\n    this.setIndex(index)\n    this.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n  }\n\n  applyMatrix4(matrix) {\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined) {\n      start.applyMatrix4(matrix)\n\n      end.applyMatrix4(matrix)\n\n      start.needsUpdate = true\n    }\n\n    if (this.boundingBox !== null) {\n      this.computeBoundingBox()\n    }\n\n    if (this.boundingSphere !== null) {\n      this.computeBoundingSphere()\n    }\n\n    return this\n  }\n\n  setPositions(array) {\n    let lineSegments\n\n    if (array instanceof Float32Array) {\n      lineSegments = array\n    } else if (Array.isArray(array)) {\n      lineSegments = new Float32Array(array)\n    }\n\n    const instanceBuffer = new InstancedInterleavedBuffer(lineSegments, 6, 1) // xyz, xyz\n\n    this.setAttribute('instanceStart', new InterleavedBufferAttribute(instanceBuffer, 3, 0)) // xyz\n    this.setAttribute('instanceEnd', new InterleavedBufferAttribute(instanceBuffer, 3, 3)) // xyz\n\n    //\n\n    this.computeBoundingBox()\n    this.computeBoundingSphere()\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    let colors\n\n    if (array instanceof Float32Array) {\n      colors = array\n    } else if (Array.isArray(array)) {\n      colors = new Float32Array(array)\n    }\n\n    const instanceColorBuffer = new InstancedInterleavedBuffer(colors, itemSize * 2, 1) // rgb(a), rgb(a)\n\n    this.setAttribute('instanceColorStart', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, 0)) // rgb(a)\n    this.setAttribute('instanceColorEnd', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, itemSize)) // rgb(a)\n\n    return this\n  }\n\n  fromWireframeGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromEdgesGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromMesh(mesh) {\n    this.fromWireframeGeometry(new WireframeGeometry(mesh.geometry))\n\n    // set colors, maybe\n\n    return this\n  }\n\n  fromLineSegments(lineSegments) {\n    const geometry = lineSegments.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n\n  computeBoundingBox() {\n    if (this.boundingBox === null) {\n      this.boundingBox = new Box3()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      this.boundingBox.setFromBufferAttribute(start)\n\n      _box.setFromBufferAttribute(end)\n\n      this.boundingBox.union(_box)\n    }\n  }\n\n  computeBoundingSphere() {\n    if (this.boundingSphere === null) {\n      this.boundingSphere = new Sphere()\n    }\n\n    if (this.boundingBox === null) {\n      this.computeBoundingBox()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      const center = this.boundingSphere.center\n\n      this.boundingBox.getCenter(center)\n\n      let maxRadiusSq = 0\n\n      for (let i = 0, il = start.count; i < il; i++) {\n        _vector.fromBufferAttribute(start, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n\n        _vector.fromBufferAttribute(end, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n      }\n\n      this.boundingSphere.radius = Math.sqrt(maxRadiusSq)\n\n      if (isNaN(this.boundingSphere.radius)) {\n        console.error(\n          'THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.',\n          this,\n        )\n      }\n    }\n  }\n\n  toJSON() {\n    // todo\n  }\n\n  applyMatrix(matrix) {\n    console.warn('THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().')\n\n    return this.applyMatrix4(matrix)\n  }\n}\n\nexport { LineSegmentsGeometry }\n"], "mappings": ";AAWA,MAAMA,IAAA,GAAuB,mBAAIC,IAAA,CAAM;AACvC,MAAMC,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAE7C,MAAMC,oBAAA,SAA6BC,uBAAA,CAAwB;EACzDC,YAAA,EAAc;IACZ,MAAO;IAEP,KAAKC,sBAAA,GAAyB;IAE9B,KAAKC,IAAA,GAAO;IAEZ,MAAMC,SAAA,GAAY,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC;IAC/F,MAAMC,GAAA,GAAM,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE;IACnE,MAAMC,KAAA,GAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAEnE,KAAKC,QAAA,CAASD,KAAK;IACnB,KAAKE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBL,SAAA,EAAW,CAAC,CAAC;IACtE,KAAKI,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBJ,GAAA,EAAK,CAAC,CAAC;EAC3D;EAEDK,aAAaC,MAAA,EAAQ;IACnB,MAAMC,KAAA,GAAQ,KAAKC,UAAA,CAAWC,aAAA;IAC9B,MAAMC,GAAA,GAAM,KAAKF,UAAA,CAAWG,WAAA;IAE5B,IAAIJ,KAAA,KAAU,QAAW;MACvBA,KAAA,CAAMF,YAAA,CAAaC,MAAM;MAEzBI,GAAA,CAAIL,YAAA,CAAaC,MAAM;MAEvBC,KAAA,CAAMK,WAAA,GAAc;IACrB;IAED,IAAI,KAAKC,WAAA,KAAgB,MAAM;MAC7B,KAAKC,kBAAA,CAAoB;IAC1B;IAED,IAAI,KAAKC,cAAA,KAAmB,MAAM;MAChC,KAAKC,qBAAA,CAAuB;IAC7B;IAED,OAAO;EACR;EAEDC,aAAaC,KAAA,EAAO;IAClB,IAAIC,YAAA;IAEJ,IAAID,KAAA,YAAiBE,YAAA,EAAc;MACjCD,YAAA,GAAeD,KAAA;IAChB,WAAUG,KAAA,CAAMC,OAAA,CAAQJ,KAAK,GAAG;MAC/BC,YAAA,GAAe,IAAIC,YAAA,CAAaF,KAAK;IACtC;IAED,MAAMK,cAAA,GAAiB,IAAIC,0BAAA,CAA2BL,YAAA,EAAc,GAAG,CAAC;IAExE,KAAKhB,YAAA,CAAa,iBAAiB,IAAIsB,0BAAA,CAA2BF,cAAA,EAAgB,GAAG,CAAC,CAAC;IACvF,KAAKpB,YAAA,CAAa,eAAe,IAAIsB,0BAAA,CAA2BF,cAAA,EAAgB,GAAG,CAAC,CAAC;IAIrF,KAAKT,kBAAA,CAAoB;IACzB,KAAKE,qBAAA,CAAuB;IAE5B,OAAO;EACR;EAEDU,UAAUR,KAAA,EAAOS,QAAA,GAAW,GAAG;IAC7B,IAAIC,MAAA;IAEJ,IAAIV,KAAA,YAAiBE,YAAA,EAAc;MACjCQ,MAAA,GAASV,KAAA;IACV,WAAUG,KAAA,CAAMC,OAAA,CAAQJ,KAAK,GAAG;MAC/BU,MAAA,GAAS,IAAIR,YAAA,CAAaF,KAAK;IAChC;IAED,MAAMW,mBAAA,GAAsB,IAAIL,0BAAA,CAA2BI,MAAA,EAAQD,QAAA,GAAW,GAAG,CAAC;IAElF,KAAKxB,YAAA,CAAa,sBAAsB,IAAIsB,0BAAA,CAA2BI,mBAAA,EAAqBF,QAAA,EAAU,CAAC,CAAC;IACxG,KAAKxB,YAAA,CAAa,oBAAoB,IAAIsB,0BAAA,CAA2BI,mBAAA,EAAqBF,QAAA,EAAUA,QAAQ,CAAC;IAE7G,OAAO;EACR;EAEDG,sBAAsBC,QAAA,EAAU;IAC9B,KAAKd,YAAA,CAAac,QAAA,CAASvB,UAAA,CAAWwB,QAAA,CAASd,KAAK;IAEpD,OAAO;EACR;EAEDe,kBAAkBF,QAAA,EAAU;IAC1B,KAAKd,YAAA,CAAac,QAAA,CAASvB,UAAA,CAAWwB,QAAA,CAASd,KAAK;IAEpD,OAAO;EACR;EAEDgB,SAASC,IAAA,EAAM;IACb,KAAKL,qBAAA,CAAsB,IAAIM,iBAAA,CAAkBD,IAAA,CAAKJ,QAAQ,CAAC;IAI/D,OAAO;EACR;EAEDM,iBAAiBlB,YAAA,EAAc;IAC7B,MAAMY,QAAA,GAAWZ,YAAA,CAAaY,QAAA;IAE9B,KAAKd,YAAA,CAAac,QAAA,CAASvB,UAAA,CAAWwB,QAAA,CAASd,KAAK;IAIpD,OAAO;EACR;EAEDJ,mBAAA,EAAqB;IACnB,IAAI,KAAKD,WAAA,KAAgB,MAAM;MAC7B,KAAKA,WAAA,GAAc,IAAItB,IAAA,CAAM;IAC9B;IAED,MAAMgB,KAAA,GAAQ,KAAKC,UAAA,CAAWC,aAAA;IAC9B,MAAMC,GAAA,GAAM,KAAKF,UAAA,CAAWG,WAAA;IAE5B,IAAIJ,KAAA,KAAU,UAAaG,GAAA,KAAQ,QAAW;MAC5C,KAAKG,WAAA,CAAYyB,sBAAA,CAAuB/B,KAAK;MAE7CjB,IAAA,CAAKgD,sBAAA,CAAuB5B,GAAG;MAE/B,KAAKG,WAAA,CAAY0B,KAAA,CAAMjD,IAAI;IAC5B;EACF;EAED0B,sBAAA,EAAwB;IACtB,IAAI,KAAKD,cAAA,KAAmB,MAAM;MAChC,KAAKA,cAAA,GAAiB,IAAIyB,MAAA,CAAQ;IACnC;IAED,IAAI,KAAK3B,WAAA,KAAgB,MAAM;MAC7B,KAAKC,kBAAA,CAAoB;IAC1B;IAED,MAAMP,KAAA,GAAQ,KAAKC,UAAA,CAAWC,aAAA;IAC9B,MAAMC,GAAA,GAAM,KAAKF,UAAA,CAAWG,WAAA;IAE5B,IAAIJ,KAAA,KAAU,UAAaG,GAAA,KAAQ,QAAW;MAC5C,MAAM+B,MAAA,GAAS,KAAK1B,cAAA,CAAe0B,MAAA;MAEnC,KAAK5B,WAAA,CAAY6B,SAAA,CAAUD,MAAM;MAEjC,IAAIE,WAAA,GAAc;MAElB,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKtC,KAAA,CAAMuC,KAAA,EAAOF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC7CpD,OAAA,CAAQuD,mBAAA,CAAoBxC,KAAA,EAAOqC,CAAC;QACpCD,WAAA,GAAcK,IAAA,CAAKC,GAAA,CAAIN,WAAA,EAAaF,MAAA,CAAOS,iBAAA,CAAkB1D,OAAO,CAAC;QAErEA,OAAA,CAAQuD,mBAAA,CAAoBrC,GAAA,EAAKkC,CAAC;QAClCD,WAAA,GAAcK,IAAA,CAAKC,GAAA,CAAIN,WAAA,EAAaF,MAAA,CAAOS,iBAAA,CAAkB1D,OAAO,CAAC;MACtE;MAED,KAAKuB,cAAA,CAAeoC,MAAA,GAASH,IAAA,CAAKI,IAAA,CAAKT,WAAW;MAElD,IAAIU,KAAA,CAAM,KAAKtC,cAAA,CAAeoC,MAAM,GAAG;QACrCG,OAAA,CAAQC,KAAA,CACN,yIACA,IACD;MACF;IACF;EACF;EAEDC,OAAA,EAAS,CAER;EAEDC,YAAYnD,MAAA,EAAQ;IAClBgD,OAAA,CAAQI,IAAA,CAAK,+EAA+E;IAE5F,OAAO,KAAKrD,YAAA,CAAaC,MAAM;EAChC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}