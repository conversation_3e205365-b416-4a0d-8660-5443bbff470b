{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\cart\\\\CheckoutModal.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CheckoutModal = ({\n  isOpen,\n  onClose,\n  product,\n  quantity = 1\n}) => {\n  if (!isOpen || !product) return null;\n  const formatPrice = price => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n  const displayPrice = product.discountPrice || product.price;\n  const totalPrice = displayPrice * quantity;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"checkout-modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-modal-simple\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"checkout-modal-close\",\n        onClick: onClose,\n        \"aria-label\": \"Close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"checkout-success-icon\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"32\",\n          height: \"32\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            fill: \"#F0B21B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M9 12L11 14L15 10\",\n            stroke: \"white\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"checkout-modal-title\",\n        children: \"Item Added to Cart!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"checkout-product-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.images && product.images[0] ? product.images[0] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop',\n          alt: product.name,\n          className: \"checkout-product-image\",\n          onError: e => {\n            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"checkout-product-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"checkout-product-name\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"checkout-product-pricing\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"checkout-product-price\",\n              children: formatPrice(displayPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 29\n            }, this), quantity > 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"checkout-product-quantity\",\n              children: [\"Qty: \", quantity]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"checkout-product-total\",\n            children: [\"Total: \", formatPrice(totalPrice)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"checkout-modal-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"checkout-btn checkout-btn-secondary\",\n          onClick: onClose,\n          children: \"Continue Shopping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/cart\",\n          className: \"checkout-btn checkout-btn-primary\",\n          onClick: onClose,\n          children: \"View Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_c = CheckoutModal;\nexport default CheckoutModal;\nvar _c;\n$RefreshReg$(_c, \"CheckoutModal\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "CheckoutModal", "isOpen", "onClose", "product", "quantity", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "displayPrice", "discountPrice", "totalPrice", "className", "onClick", "children", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "xmlns", "cx", "cy", "r", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "src", "images", "alt", "name", "onError", "target", "to", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/cart/CheckoutModal.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst CheckoutModal = ({ isOpen, onClose, product, quantity = 1 }) => {\n    if (!isOpen || !product) return null;\n\n    const formatPrice = (price) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(price);\n    };\n\n    const displayPrice = product.discountPrice || product.price;\n    const totalPrice = displayPrice * quantity;\n\n    return (\n        <div className=\"checkout-modal-overlay\" onClick={onClose}>\n            <div className=\"checkout-modal-simple\" onClick={(e) => e.stopPropagation()}>\n                {/* Close Button */}\n                <button className=\"checkout-modal-close\" onClick={onClose} aria-label=\"Close\">\n                    ×\n                </button>\n\n                {/* Success Icon */}\n                <div className=\"checkout-success-icon\">\n                    <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#F0B21B\"/>\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"white\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                </div>\n\n                {/* Title */}\n                <h2 className=\"checkout-modal-title\">Item Added to Cart!</h2>\n\n                {/* Product Info */}\n                <div className=\"checkout-product-card\">\n                    <img\n                        src={product.images && product.images[0] ? product.images[0] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop'}\n                        alt={product.name}\n                        className=\"checkout-product-image\"\n                        onError={(e) => {\n                            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop';\n                        }}\n                    />\n                    <div className=\"checkout-product-details\">\n                        <h3 className=\"checkout-product-name\">{product.name}</h3>\n                        <div className=\"checkout-product-pricing\">\n                            <span className=\"checkout-product-price\">{formatPrice(displayPrice)}</span>\n                            {quantity > 1 && (\n                                <span className=\"checkout-product-quantity\">Qty: {quantity}</span>\n                            )}\n                        </div>\n                        <div className=\"checkout-product-total\">Total: {formatPrice(totalPrice)}</div>\n                    </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"checkout-modal-buttons\">\n                    <button\n                        className=\"checkout-btn checkout-btn-secondary\"\n                        onClick={onClose}\n                    >\n                        Continue Shopping\n                    </button>\n                    <Link\n                        to=\"/cart\"\n                        className=\"checkout-btn checkout-btn-primary\"\n                        onClick={onClose}\n                    >\n                        View Cart\n                    </Link>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default CheckoutModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAE,CAAC,KAAK;EAClE,IAAI,CAACH,MAAM,IAAI,CAACE,OAAO,EAAE,OAAO,IAAI;EAEpC,MAAME,WAAW,GAAIC,KAAK,IAAK;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EACpB,CAAC;EAED,MAAMM,YAAY,GAAGT,OAAO,CAACU,aAAa,IAAIV,OAAO,CAACG,KAAK;EAC3D,MAAMQ,UAAU,GAAGF,YAAY,GAAGR,QAAQ;EAE1C,oBACIL,OAAA;IAAKgB,SAAS,EAAC,wBAAwB;IAACC,OAAO,EAAEd,OAAQ;IAAAe,QAAA,eACrDlB,OAAA;MAAKgB,SAAS,EAAC,uBAAuB;MAACC,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBAEvElB,OAAA;QAAQgB,SAAS,EAAC,sBAAsB;QAACC,OAAO,EAAEd,OAAQ;QAAC,cAAW,OAAO;QAAAe,QAAA,EAAC;MAE9E;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGTxB,OAAA;QAAKgB,SAAS,EAAC,uBAAuB;QAAAE,QAAA,eAClClB,OAAA;UAAKyB,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAAAX,QAAA,gBAC1FlB,OAAA;YAAQ8B,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACJ,IAAI,EAAC;UAAS;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC/CxB,OAAA;YAAMiC,CAAC,EAAC,mBAAmB;YAACC,MAAM,EAAC,OAAO;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxB,OAAA;QAAIgB,SAAS,EAAC,sBAAsB;QAAAE,QAAA,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG7DxB,OAAA;QAAKgB,SAAS,EAAC,uBAAuB;QAAAE,QAAA,gBAClClB,OAAA;UACIsC,GAAG,EAAElC,OAAO,CAACmC,MAAM,IAAInC,OAAO,CAACmC,MAAM,CAAC,CAAC,CAAC,GAAGnC,OAAO,CAACmC,MAAM,CAAC,CAAC,CAAC,GAAG,mFAAoF;UACnJC,GAAG,EAAEpC,OAAO,CAACqC,IAAK;UAClBzB,SAAS,EAAC,wBAAwB;UAClC0B,OAAO,EAAGvB,CAAC,IAAK;YACZA,CAAC,CAACwB,MAAM,CAACL,GAAG,GAAG,mFAAmF;UACtG;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACFxB,OAAA;UAAKgB,SAAS,EAAC,0BAA0B;UAAAE,QAAA,gBACrClB,OAAA;YAAIgB,SAAS,EAAC,uBAAuB;YAAAE,QAAA,EAAEd,OAAO,CAACqC;UAAI;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzDxB,OAAA;YAAKgB,SAAS,EAAC,0BAA0B;YAAAE,QAAA,gBACrClB,OAAA;cAAMgB,SAAS,EAAC,wBAAwB;cAAAE,QAAA,EAAEZ,WAAW,CAACO,YAAY;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1EnB,QAAQ,GAAG,CAAC,iBACTL,OAAA;cAAMgB,SAAS,EAAC,2BAA2B;cAAAE,QAAA,GAAC,OAAK,EAACb,QAAQ;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACpE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACNxB,OAAA;YAAKgB,SAAS,EAAC,wBAAwB;YAAAE,QAAA,GAAC,SAAO,EAACZ,WAAW,CAACS,UAAU,CAAC;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxB,OAAA;QAAKgB,SAAS,EAAC,wBAAwB;QAAAE,QAAA,gBACnClB,OAAA;UACIgB,SAAS,EAAC,qCAAqC;UAC/CC,OAAO,EAAEd,OAAQ;UAAAe,QAAA,EACpB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA,CAACF,IAAI;UACD8C,EAAE,EAAC,OAAO;UACV5B,SAAS,EAAC,mCAAmC;UAC7CC,OAAO,EAAEd,OAAQ;UAAAe,QAAA,EACpB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACqB,EAAA,GAzEI5C,aAAa;AA2EnB,eAAeA,aAAa;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}