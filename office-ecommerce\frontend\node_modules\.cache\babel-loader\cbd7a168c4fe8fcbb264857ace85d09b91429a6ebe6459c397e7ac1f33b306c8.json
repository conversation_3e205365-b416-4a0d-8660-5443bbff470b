{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { HorizontalBlurShader, VerticalBlurShader } from 'three-stdlib';\nconst ContactShadows = /*#__PURE__*/React.forwardRef(({\n  scale = 10,\n  frames = Infinity,\n  opacity = 1,\n  width = 1,\n  height = 1,\n  blur = 1,\n  far = 10,\n  resolution = 512,\n  smooth = true,\n  color = '#000000',\n  depthWrite = false,\n  renderOrder,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const scene = useThree(state => state.scene);\n  const gl = useThree(state => state.gl);\n  const shadowCamera = React.useRef(null);\n  width = width * (Array.isArray(scale) ? scale[0] : scale || 1);\n  height = height * (Array.isArray(scale) ? scale[1] : scale || 1);\n  const [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur] = React.useMemo(() => {\n    const renderTarget = new THREE.WebGLRenderTarget(resolution, resolution);\n    const renderTargetBlur = new THREE.WebGLRenderTarget(resolution, resolution);\n    renderTargetBlur.texture.generateMipmaps = renderTarget.texture.generateMipmaps = false;\n    const planeGeometry = new THREE.PlaneGeometry(width, height).rotateX(Math.PI / 2);\n    const blurPlane = new THREE.Mesh(planeGeometry);\n    const depthMaterial = new THREE.MeshDepthMaterial();\n    depthMaterial.depthTest = depthMaterial.depthWrite = false;\n    depthMaterial.onBeforeCompile = shader => {\n      shader.uniforms = {\n        ...shader.uniforms,\n        ucolor: {\n          value: new THREE.Color(color)\n        }\n      };\n      shader.fragmentShader = shader.fragmentShader.replace(`void main() {`,\n      //\n      `uniform vec3 ucolor;\n           void main() {\n          `);\n      shader.fragmentShader = shader.fragmentShader.replace('vec4( vec3( 1.0 - fragCoordZ ), opacity );',\n      // Colorize the shadow, multiply by the falloff so that the center can remain darker\n      'vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );');\n    };\n    const horizontalBlurMaterial = new THREE.ShaderMaterial(HorizontalBlurShader);\n    const verticalBlurMaterial = new THREE.ShaderMaterial(VerticalBlurShader);\n    verticalBlurMaterial.depthTest = horizontalBlurMaterial.depthTest = false;\n    return [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur];\n  }, [resolution, width, height, scale, color]);\n  const blurShadows = blur => {\n    blurPlane.visible = true;\n    blurPlane.material = horizontalBlurMaterial;\n    horizontalBlurMaterial.uniforms.tDiffuse.value = renderTarget.texture;\n    horizontalBlurMaterial.uniforms.h.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTargetBlur);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.material = verticalBlurMaterial;\n    verticalBlurMaterial.uniforms.tDiffuse.value = renderTargetBlur.texture;\n    verticalBlurMaterial.uniforms.v.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTarget);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.visible = false;\n  };\n  let count = 0;\n  let initialBackground;\n  let initialOverrideMaterial;\n  useFrame(() => {\n    if (shadowCamera.current && (frames === Infinity || count < frames)) {\n      count++;\n      initialBackground = scene.background;\n      initialOverrideMaterial = scene.overrideMaterial;\n      ref.current.visible = false;\n      scene.background = null;\n      scene.overrideMaterial = depthMaterial;\n      gl.setRenderTarget(renderTarget);\n      gl.render(scene, shadowCamera.current);\n      blurShadows(blur);\n      if (smooth) blurShadows(blur * 0.4);\n      gl.setRenderTarget(null);\n      ref.current.visible = true;\n      scene.overrideMaterial = initialOverrideMaterial;\n      scene.background = initialBackground;\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    \"rotation-x\": Math.PI / 2\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"mesh\", {\n    renderOrder: renderOrder,\n    geometry: planeGeometry,\n    scale: [1, -1, 1],\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    map: renderTarget.texture,\n    opacity: opacity,\n    depthWrite: depthWrite\n  })), /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    ref: shadowCamera,\n    args: [-width / 2, width / 2, height / 2, -height / 2, 0, far]\n  }));\n});\nexport { ContactShadows };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "useThree", "useFrame", "HorizontalBlur<PERSON><PERSON>er", "VerticalBlurShader", "ContactShadows", "forwardRef", "scale", "frames", "Infinity", "opacity", "width", "height", "blur", "far", "resolution", "smooth", "color", "depthWrite", "renderOrder", "props", "fref", "ref", "useRef", "scene", "state", "gl", "shadowCamera", "Array", "isArray", "renderTarget", "planeGeometry", "depthMaterial", "blurPlane", "horizontalBlurMaterial", "verticalBlurMaterial", "renderTargetBlur", "useMemo", "WebGLRenderTarget", "texture", "generateMipmaps", "PlaneGeometry", "rotateX", "Math", "PI", "<PERSON><PERSON>", "MeshDepthMaterial", "depthTest", "onBeforeCompile", "shader", "uniforms", "ucolor", "value", "Color", "fragmentShader", "replace", "ShaderMaterial", "blurShadows", "visible", "material", "tDiffuse", "h", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "current", "v", "count", "initialBackground", "initialOverrideMaterial", "background", "overrideMaterial", "useImperativeHandle", "createElement", "geometry", "rotation", "transparent", "map", "args"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/ContactShadows.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { HorizontalBlurShader, VerticalBlurShader } from 'three-stdlib';\n\nconst ContactShadows = /*#__PURE__*/React.forwardRef(({\n  scale = 10,\n  frames = Infinity,\n  opacity = 1,\n  width = 1,\n  height = 1,\n  blur = 1,\n  far = 10,\n  resolution = 512,\n  smooth = true,\n  color = '#000000',\n  depthWrite = false,\n  renderOrder,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const scene = useThree(state => state.scene);\n  const gl = useThree(state => state.gl);\n  const shadowCamera = React.useRef(null);\n  width = width * (Array.isArray(scale) ? scale[0] : scale || 1);\n  height = height * (Array.isArray(scale) ? scale[1] : scale || 1);\n  const [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur] = React.useMemo(() => {\n    const renderTarget = new THREE.WebGLRenderTarget(resolution, resolution);\n    const renderTargetBlur = new THREE.WebGLRenderTarget(resolution, resolution);\n    renderTargetBlur.texture.generateMipmaps = renderTarget.texture.generateMipmaps = false;\n    const planeGeometry = new THREE.PlaneGeometry(width, height).rotateX(Math.PI / 2);\n    const blurPlane = new THREE.Mesh(planeGeometry);\n    const depthMaterial = new THREE.MeshDepthMaterial();\n    depthMaterial.depthTest = depthMaterial.depthWrite = false;\n\n    depthMaterial.onBeforeCompile = shader => {\n      shader.uniforms = { ...shader.uniforms,\n        ucolor: {\n          value: new THREE.Color(color)\n        }\n      };\n      shader.fragmentShader = shader.fragmentShader.replace(`void main() {`, //\n      `uniform vec3 ucolor;\n           void main() {\n          `);\n      shader.fragmentShader = shader.fragmentShader.replace('vec4( vec3( 1.0 - fragCoordZ ), opacity );', // Colorize the shadow, multiply by the falloff so that the center can remain darker\n      'vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );');\n    };\n\n    const horizontalBlurMaterial = new THREE.ShaderMaterial(HorizontalBlurShader);\n    const verticalBlurMaterial = new THREE.ShaderMaterial(VerticalBlurShader);\n    verticalBlurMaterial.depthTest = horizontalBlurMaterial.depthTest = false;\n    return [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur];\n  }, [resolution, width, height, scale, color]);\n\n  const blurShadows = blur => {\n    blurPlane.visible = true;\n    blurPlane.material = horizontalBlurMaterial;\n    horizontalBlurMaterial.uniforms.tDiffuse.value = renderTarget.texture;\n    horizontalBlurMaterial.uniforms.h.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTargetBlur);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.material = verticalBlurMaterial;\n    verticalBlurMaterial.uniforms.tDiffuse.value = renderTargetBlur.texture;\n    verticalBlurMaterial.uniforms.v.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTarget);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.visible = false;\n  };\n\n  let count = 0;\n  let initialBackground;\n  let initialOverrideMaterial;\n  useFrame(() => {\n    if (shadowCamera.current && (frames === Infinity || count < frames)) {\n      count++;\n      initialBackground = scene.background;\n      initialOverrideMaterial = scene.overrideMaterial;\n      ref.current.visible = false;\n      scene.background = null;\n      scene.overrideMaterial = depthMaterial;\n      gl.setRenderTarget(renderTarget);\n      gl.render(scene, shadowCamera.current);\n      blurShadows(blur);\n      if (smooth) blurShadows(blur * 0.4);\n      gl.setRenderTarget(null);\n      ref.current.visible = true;\n      scene.overrideMaterial = initialOverrideMaterial;\n      scene.background = initialBackground;\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    \"rotation-x\": Math.PI / 2\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"mesh\", {\n    renderOrder: renderOrder,\n    geometry: planeGeometry,\n    scale: [1, -1, 1],\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    map: renderTarget.texture,\n    opacity: opacity,\n    depthWrite: depthWrite\n  })), /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    ref: shadowCamera,\n    args: [-width / 2, width / 2, height / 2, -height / 2, 0, far]\n  }));\n});\n\nexport { ContactShadows };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,oBAAoB,EAAEC,kBAAkB,QAAQ,cAAc;AAEvE,MAAMC,cAAc,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EACpDC,KAAK,GAAG,EAAE;EACVC,MAAM,GAAGC,QAAQ;EACjBC,OAAO,GAAG,CAAC;EACXC,KAAK,GAAG,CAAC;EACTC,MAAM,GAAG,CAAC;EACVC,IAAI,GAAG,CAAC;EACRC,GAAG,GAAG,EAAE;EACRC,UAAU,GAAG,GAAG;EAChBC,MAAM,GAAG,IAAI;EACbC,KAAK,GAAG,SAAS;EACjBC,UAAU,GAAG,KAAK;EAClBC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,KAAK,GAAGvB,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACD,KAAK,CAAC;EAC5C,MAAME,EAAE,GAAGzB,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACC,EAAE,CAAC;EACtC,MAAMC,YAAY,GAAG5B,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EACvCZ,KAAK,GAAGA,KAAK,IAAIiB,KAAK,CAACC,OAAO,CAACtB,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,IAAI,CAAC,CAAC;EAC9DK,MAAM,GAAGA,MAAM,IAAIgB,KAAK,CAACC,OAAO,CAACtB,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,IAAI,CAAC,CAAC;EAChE,MAAM,CAACuB,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,SAAS,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,gBAAgB,CAAC,GAAGrC,KAAK,CAACsC,OAAO,CAAC,MAAM;IAClJ,MAAMP,YAAY,GAAG,IAAI9B,KAAK,CAACsC,iBAAiB,CAACvB,UAAU,EAAEA,UAAU,CAAC;IACxE,MAAMqB,gBAAgB,GAAG,IAAIpC,KAAK,CAACsC,iBAAiB,CAACvB,UAAU,EAAEA,UAAU,CAAC;IAC5EqB,gBAAgB,CAACG,OAAO,CAACC,eAAe,GAAGV,YAAY,CAACS,OAAO,CAACC,eAAe,GAAG,KAAK;IACvF,MAAMT,aAAa,GAAG,IAAI/B,KAAK,CAACyC,aAAa,CAAC9B,KAAK,EAAEC,MAAM,CAAC,CAAC8B,OAAO,CAACC,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;IACjF,MAAMX,SAAS,GAAG,IAAIjC,KAAK,CAAC6C,IAAI,CAACd,aAAa,CAAC;IAC/C,MAAMC,aAAa,GAAG,IAAIhC,KAAK,CAAC8C,iBAAiB,CAAC,CAAC;IACnDd,aAAa,CAACe,SAAS,GAAGf,aAAa,CAACd,UAAU,GAAG,KAAK;IAE1Dc,aAAa,CAACgB,eAAe,GAAGC,MAAM,IAAI;MACxCA,MAAM,CAACC,QAAQ,GAAG;QAAE,GAAGD,MAAM,CAACC,QAAQ;QACpCC,MAAM,EAAE;UACNC,KAAK,EAAE,IAAIpD,KAAK,CAACqD,KAAK,CAACpC,KAAK;QAC9B;MACF,CAAC;MACDgC,MAAM,CAACK,cAAc,GAAGL,MAAM,CAACK,cAAc,CAACC,OAAO,CAAC,eAAe;MAAE;MACvE;AACN;AACA,WAAW,CAAC;MACNN,MAAM,CAACK,cAAc,GAAGL,MAAM,CAACK,cAAc,CAACC,OAAO,CAAC,4CAA4C;MAAE;MACpG,gEAAgE,CAAC;IACnE,CAAC;IAED,MAAMrB,sBAAsB,GAAG,IAAIlC,KAAK,CAACwD,cAAc,CAACrD,oBAAoB,CAAC;IAC7E,MAAMgC,oBAAoB,GAAG,IAAInC,KAAK,CAACwD,cAAc,CAACpD,kBAAkB,CAAC;IACzE+B,oBAAoB,CAACY,SAAS,GAAGb,sBAAsB,CAACa,SAAS,GAAG,KAAK;IACzE,OAAO,CAACjB,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,SAAS,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,gBAAgB,CAAC;EAChI,CAAC,EAAE,CAACrB,UAAU,EAAEJ,KAAK,EAAEC,MAAM,EAAEL,KAAK,EAAEU,KAAK,CAAC,CAAC;EAE7C,MAAMwC,WAAW,GAAG5C,IAAI,IAAI;IAC1BoB,SAAS,CAACyB,OAAO,GAAG,IAAI;IACxBzB,SAAS,CAAC0B,QAAQ,GAAGzB,sBAAsB;IAC3CA,sBAAsB,CAACgB,QAAQ,CAACU,QAAQ,CAACR,KAAK,GAAGtB,YAAY,CAACS,OAAO;IACrEL,sBAAsB,CAACgB,QAAQ,CAACW,CAAC,CAACT,KAAK,GAAGvC,IAAI,GAAG,CAAC,GAAG,GAAG;IACxDa,EAAE,CAACoC,eAAe,CAAC1B,gBAAgB,CAAC;IACpCV,EAAE,CAACqC,MAAM,CAAC9B,SAAS,EAAEN,YAAY,CAACqC,OAAO,CAAC;IAC1C/B,SAAS,CAAC0B,QAAQ,GAAGxB,oBAAoB;IACzCA,oBAAoB,CAACe,QAAQ,CAACU,QAAQ,CAACR,KAAK,GAAGhB,gBAAgB,CAACG,OAAO;IACvEJ,oBAAoB,CAACe,QAAQ,CAACe,CAAC,CAACb,KAAK,GAAGvC,IAAI,GAAG,CAAC,GAAG,GAAG;IACtDa,EAAE,CAACoC,eAAe,CAAChC,YAAY,CAAC;IAChCJ,EAAE,CAACqC,MAAM,CAAC9B,SAAS,EAAEN,YAAY,CAACqC,OAAO,CAAC;IAC1C/B,SAAS,CAACyB,OAAO,GAAG,KAAK;EAC3B,CAAC;EAED,IAAIQ,KAAK,GAAG,CAAC;EACb,IAAIC,iBAAiB;EACrB,IAAIC,uBAAuB;EAC3BlE,QAAQ,CAAC,MAAM;IACb,IAAIyB,YAAY,CAACqC,OAAO,KAAKxD,MAAM,KAAKC,QAAQ,IAAIyD,KAAK,GAAG1D,MAAM,CAAC,EAAE;MACnE0D,KAAK,EAAE;MACPC,iBAAiB,GAAG3C,KAAK,CAAC6C,UAAU;MACpCD,uBAAuB,GAAG5C,KAAK,CAAC8C,gBAAgB;MAChDhD,GAAG,CAAC0C,OAAO,CAACN,OAAO,GAAG,KAAK;MAC3BlC,KAAK,CAAC6C,UAAU,GAAG,IAAI;MACvB7C,KAAK,CAAC8C,gBAAgB,GAAGtC,aAAa;MACtCN,EAAE,CAACoC,eAAe,CAAChC,YAAY,CAAC;MAChCJ,EAAE,CAACqC,MAAM,CAACvC,KAAK,EAAEG,YAAY,CAACqC,OAAO,CAAC;MACtCP,WAAW,CAAC5C,IAAI,CAAC;MACjB,IAAIG,MAAM,EAAEyC,WAAW,CAAC5C,IAAI,GAAG,GAAG,CAAC;MACnCa,EAAE,CAACoC,eAAe,CAAC,IAAI,CAAC;MACxBxC,GAAG,CAAC0C,OAAO,CAACN,OAAO,GAAG,IAAI;MAC1BlC,KAAK,CAAC8C,gBAAgB,GAAGF,uBAAuB;MAChD5C,KAAK,CAAC6C,UAAU,GAAGF,iBAAiB;IACtC;EACF,CAAC,CAAC;EACFpE,KAAK,CAACwE,mBAAmB,CAAClD,IAAI,EAAE,MAAMC,GAAG,CAAC0C,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAajE,KAAK,CAACyE,aAAa,CAAC,OAAO,EAAE1E,QAAQ,CAAC;IACxD,YAAY,EAAE6C,IAAI,CAACC,EAAE,GAAG;EAC1B,CAAC,EAAExB,KAAK,EAAE;IACRE,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAavB,KAAK,CAACyE,aAAa,CAAC,MAAM,EAAE;IAC3CrD,WAAW,EAAEA,WAAW;IACxBsD,QAAQ,EAAE1C,aAAa;IACvBxB,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACjBmE,QAAQ,EAAE,CAAC,CAAC/B,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;EAC/B,CAAC,EAAE,aAAa7C,KAAK,CAACyE,aAAa,CAAC,mBAAmB,EAAE;IACvDG,WAAW,EAAE,IAAI;IACjBC,GAAG,EAAE9C,YAAY,CAACS,OAAO;IACzB7B,OAAO,EAAEA,OAAO;IAChBQ,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC,EAAE,aAAanB,KAAK,CAACyE,aAAa,CAAC,oBAAoB,EAAE;IAC1DlD,GAAG,EAAEK,YAAY;IACjBkD,IAAI,EAAE,CAAC,CAAClE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE,CAACA,MAAM,GAAG,CAAC,EAAE,CAAC,EAAEE,GAAG;EAC/D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAAST,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}