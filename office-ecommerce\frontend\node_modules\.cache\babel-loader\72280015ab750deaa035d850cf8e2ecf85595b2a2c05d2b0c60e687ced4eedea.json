{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\InventoryManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useWebSocket } from '../../hooks/useWebSocket';\nimport './InventoryManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InventoryManagement = () => {\n  _s();\n  const [inventoryItems, setInventoryItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [showAdjustModal, setShowAdjustModal] = useState(false);\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [adjustmentData, setAdjustmentData] = useState({\n    adjustment: '',\n    reason: '',\n    notes: ''\n  });\n  const {\n    isConnected,\n    showNotification\n  } = useWebSocket();\n  useEffect(() => {\n    fetchInventoryData();\n  }, []);\n  const fetchInventoryData = async () => {\n    try {\n      // Mock data for now - will be replaced with API call\n      const mockData = [{\n        id: '1',\n        productId: 'PROD001',\n        productName: 'Executive Office Chair',\n        sku: 'EOC-001',\n        currentStock: 25,\n        reservedStock: 5,\n        availableStock: 20,\n        reorderLevel: 10,\n        maxStock: 100,\n        unitCost: 299.99,\n        totalValue: 7499.75,\n        location: 'Warehouse A-1',\n        lastUpdated: new Date().toISOString(),\n        status: 'In Stock'\n      }, {\n        id: '2',\n        productId: 'PROD002',\n        productName: 'Standing Desk',\n        sku: 'SD-002',\n        currentStock: 8,\n        reservedStock: 2,\n        availableStock: 6,\n        reorderLevel: 15,\n        maxStock: 50,\n        unitCost: 599.99,\n        totalValue: 4799.92,\n        location: 'Warehouse B-2',\n        lastUpdated: new Date().toISOString(),\n        status: 'Low Stock'\n      }, {\n        id: '3',\n        productId: 'PROD003',\n        productName: 'Conference Table',\n        sku: 'CT-003',\n        currentStock: 3,\n        reservedStock: 1,\n        availableStock: 2,\n        reorderLevel: 5,\n        maxStock: 20,\n        unitCost: 899.99,\n        totalValue: 2699.97,\n        location: 'Warehouse C-1',\n        lastUpdated: new Date().toISOString(),\n        status: 'Critical'\n      }];\n      setInventoryItems(mockData);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching inventory data:', error);\n      setLoading(false);\n    }\n  };\n  const handleAdjustStock = item => {\n    setSelectedItem(item);\n    setShowAdjustModal(true);\n    setAdjustmentData({\n      adjustment: '',\n      reason: '',\n      notes: ''\n    });\n  };\n  const handleSubmitAdjustment = async () => {\n    try {\n      // Mock adjustment - will be replaced with API call\n      const adjustment = parseInt(adjustmentData.adjustment);\n      const updatedItems = inventoryItems.map(item => {\n        if (item.id === selectedItem.id) {\n          const newStock = item.currentStock + adjustment;\n          const newAvailable = newStock - item.reservedStock;\n          const newValue = newStock * item.unitCost;\n          let newStatus = 'In Stock';\n          if (newStock <= 0) {\n            newStatus = 'Out of Stock';\n          } else if (newStock <= item.reorderLevel) {\n            newStatus = 'Critical';\n          } else if (newStock <= item.reorderLevel * 1.5) {\n            newStatus = 'Low Stock';\n          }\n          return {\n            ...item,\n            currentStock: newStock,\n            availableStock: newAvailable,\n            totalValue: newValue,\n            status: newStatus,\n            lastUpdated: new Date().toISOString()\n          };\n        }\n        return item;\n      });\n      setInventoryItems(updatedItems);\n      setShowAdjustModal(false);\n      setSelectedItem(null);\n    } catch (error) {\n      console.error('Error adjusting stock:', error);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'in stock':\n        return '#27ae60';\n      case 'low stock':\n        return '#f39c12';\n      case 'critical':\n        return '#e74c3c';\n      case 'out of stock':\n        return '#95a5a6';\n      default:\n        return '#95a5a6';\n    }\n  };\n  const filteredItems = inventoryItems.filter(item => {\n    const matchesSearch = item.productName.toLowerCase().includes(searchTerm.toLowerCase()) || item.sku.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || item.status.toLowerCase() === statusFilter.toLowerCase();\n    return matchesSearch && matchesStatus;\n  });\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"inventory-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading inventory...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"inventory-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"inventory-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Inventory Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Monitor and manage your product inventory levels\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"realtime-status\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `status-indicator ${isConnected ? 'connected' : 'disconnected'}`,\n          children: isConnected ? '🟢' : '🔴'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-text\",\n          children: isConnected ? 'Real-time Updates Active' : 'Offline Mode'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"inventory-controls\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search products...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-container\",\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: statusFilter,\n          onChange: e => setStatusFilter(e.target.value),\n          className: \"filter-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"in stock\",\n            children: \"In Stock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"low stock\",\n            children: \"Low Stock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"critical\",\n            children: \"Critical\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"out of stock\",\n            children: \"Out of Stock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"admin-btn admin-btn-primary\",\n        children: \"Export Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"admin-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"SKU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Current Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Reserved\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Reorder Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Unit Cost\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Total Value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredItems.map(item => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-cell\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: item.productName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: item.sku\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stock-number\",\n                  children: item.currentStock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: item.availableStock\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: item.reservedStock\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: item.reorderLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(item.unitCost)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(item.totalValue)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: getStatusColor(item.status)\n                  },\n                  children: item.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: item.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"admin-btn admin-btn-secondary btn-small\",\n                  onClick: () => handleAdjustStock(item),\n                  children: \"Adjust\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), showAdjustModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Adjust Stock - \", selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.productName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close\",\n            onClick: () => setShowAdjustModal(false),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-stock-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Current Stock:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 20\n              }, this), \" \", selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.currentStock]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Available:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 20\n              }, this), \" \", selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.availableStock]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"admin-form-label\",\n              children: \"Adjustment Amount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              className: \"admin-form-input\",\n              placeholder: \"Enter positive or negative number\",\n              value: adjustmentData.adjustment,\n              onChange: e => setAdjustmentData({\n                ...adjustmentData,\n                adjustment: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"admin-form-label\",\n              children: \"Reason\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"admin-form-input\",\n              value: adjustmentData.reason,\n              onChange: e => setAdjustmentData({\n                ...adjustmentData,\n                reason: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select reason\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Stock Count\",\n                children: \"Stock Count\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Damaged Goods\",\n                children: \"Damaged Goods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Returned Items\",\n                children: \"Returned Items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"New Shipment\",\n                children: \"New Shipment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Other\",\n                children: \"Other\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"admin-form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"admin-form-label\",\n              children: \"Notes (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"admin-form-input\",\n              rows: \"3\",\n              placeholder: \"Additional notes...\",\n              value: adjustmentData.notes,\n              onChange: e => setAdjustmentData({\n                ...adjustmentData,\n                notes: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary\",\n            onClick: () => setShowAdjustModal(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-primary\",\n            onClick: handleSubmitAdjustment,\n            disabled: !adjustmentData.adjustment || !adjustmentData.reason,\n            children: \"Apply Adjustment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(InventoryManagement, \"f9IHux1JgBfUaRui8nVlP8Rbwu0=\", false, function () {\n  return [useWebSocket];\n});\n_c = InventoryManagement;\nexport default InventoryManagement;\nvar _c;\n$RefreshReg$(_c, \"InventoryManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useWebSocket", "jsxDEV", "_jsxDEV", "InventoryManagement", "_s", "inventoryItems", "setInventoryItems", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "showAdjustModal", "setShowAdjustModal", "selectedItem", "setSelectedItem", "adjustmentData", "setAdjustmentData", "adjustment", "reason", "notes", "isConnected", "showNotification", "fetchInventoryData", "mockData", "id", "productId", "productName", "sku", "currentStock", "reservedStock", "availableStock", "reorderLevel", "maxStock", "unitCost", "totalValue", "location", "lastUpdated", "Date", "toISOString", "status", "error", "console", "handleAdjustStock", "item", "handleSubmitAdjustment", "parseInt", "updatedItems", "map", "newStock", "newAvailable", "newValue", "newStatus", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "toLowerCase", "filteredItems", "filter", "matchesSearch", "includes", "matchesStatus", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "target", "backgroundColor", "onClick", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/InventoryManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useWebSocket } from '../../hooks/useWebSocket';\nimport './InventoryManagement.css';\n\nconst InventoryManagement = () => {\n  const [inventoryItems, setInventoryItems] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [showAdjustModal, setShowAdjustModal] = useState(false);\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [adjustmentData, setAdjustmentData] = useState({\n    adjustment: '',\n    reason: '',\n    notes: ''\n  });\n  const { isConnected, showNotification } = useWebSocket();\n\n  useEffect(() => {\n    fetchInventoryData();\n  }, []);\n\n  const fetchInventoryData = async () => {\n    try {\n      // Mock data for now - will be replaced with API call\n      const mockData = [\n        {\n          id: '1',\n          productId: 'PROD001',\n          productName: 'Executive Office Chair',\n          sku: 'EOC-001',\n          currentStock: 25,\n          reservedStock: 5,\n          availableStock: 20,\n          reorderLevel: 10,\n          maxStock: 100,\n          unitCost: 299.99,\n          totalValue: 7499.75,\n          location: 'Warehouse A-1',\n          lastUpdated: new Date().toISOString(),\n          status: 'In Stock'\n        },\n        {\n          id: '2',\n          productId: 'PROD002',\n          productName: 'Standing Desk',\n          sku: 'SD-002',\n          currentStock: 8,\n          reservedStock: 2,\n          availableStock: 6,\n          reorderLevel: 15,\n          maxStock: 50,\n          unitCost: 599.99,\n          totalValue: 4799.92,\n          location: 'Warehouse B-2',\n          lastUpdated: new Date().toISOString(),\n          status: 'Low Stock'\n        },\n        {\n          id: '3',\n          productId: 'PROD003',\n          productName: 'Conference Table',\n          sku: 'CT-003',\n          currentStock: 3,\n          reservedStock: 1,\n          availableStock: 2,\n          reorderLevel: 5,\n          maxStock: 20,\n          unitCost: 899.99,\n          totalValue: 2699.97,\n          location: 'Warehouse C-1',\n          lastUpdated: new Date().toISOString(),\n          status: 'Critical'\n        }\n      ];\n\n      setInventoryItems(mockData);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching inventory data:', error);\n      setLoading(false);\n    }\n  };\n\n  const handleAdjustStock = (item) => {\n    setSelectedItem(item);\n    setShowAdjustModal(true);\n    setAdjustmentData({ adjustment: '', reason: '', notes: '' });\n  };\n\n  const handleSubmitAdjustment = async () => {\n    try {\n      // Mock adjustment - will be replaced with API call\n      const adjustment = parseInt(adjustmentData.adjustment);\n      const updatedItems = inventoryItems.map(item => {\n        if (item.id === selectedItem.id) {\n          const newStock = item.currentStock + adjustment;\n          const newAvailable = newStock - item.reservedStock;\n          const newValue = newStock * item.unitCost;\n          \n          let newStatus = 'In Stock';\n          if (newStock <= 0) {\n            newStatus = 'Out of Stock';\n          } else if (newStock <= item.reorderLevel) {\n            newStatus = 'Critical';\n          } else if (newStock <= item.reorderLevel * 1.5) {\n            newStatus = 'Low Stock';\n          }\n\n          return {\n            ...item,\n            currentStock: newStock,\n            availableStock: newAvailable,\n            totalValue: newValue,\n            status: newStatus,\n            lastUpdated: new Date().toISOString()\n          };\n        }\n        return item;\n      });\n\n      setInventoryItems(updatedItems);\n      setShowAdjustModal(false);\n      setSelectedItem(null);\n    } catch (error) {\n      console.error('Error adjusting stock:', error);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'in stock':\n        return '#27ae60';\n      case 'low stock':\n        return '#f39c12';\n      case 'critical':\n        return '#e74c3c';\n      case 'out of stock':\n        return '#95a5a6';\n      default:\n        return '#95a5a6';\n    }\n  };\n\n  const filteredItems = inventoryItems.filter(item => {\n    const matchesSearch = item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.sku.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || item.status.toLowerCase() === statusFilter.toLowerCase();\n    return matchesSearch && matchesStatus;\n  });\n\n  if (loading) {\n    return (\n      <div className=\"inventory-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading inventory...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"inventory-management\">\n      <div className=\"inventory-header\">\n        <div className=\"header-content\">\n          <h1>Inventory Management</h1>\n          <p>Monitor and manage your product inventory levels</p>\n        </div>\n        <div className=\"realtime-status\">\n          <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>\n            {isConnected ? '🟢' : '🔴'}\n          </span>\n          <span className=\"status-text\">\n            {isConnected ? 'Real-time Updates Active' : 'Offline Mode'}\n          </span>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"inventory-controls\">\n        <div className=\"search-container\">\n          <input\n            type=\"text\"\n            placeholder=\"Search products...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n        \n        <div className=\"filter-container\">\n          <select\n            value={statusFilter}\n            onChange={(e) => setStatusFilter(e.target.value)}\n            className=\"filter-select\"\n          >\n            <option value=\"all\">All Status</option>\n            <option value=\"in stock\">In Stock</option>\n            <option value=\"low stock\">Low Stock</option>\n            <option value=\"critical\">Critical</option>\n            <option value=\"out of stock\">Out of Stock</option>\n          </select>\n        </div>\n\n        <button className=\"admin-btn admin-btn-primary\">\n          Export Report\n        </button>\n      </div>\n\n      {/* Inventory Table */}\n      <div className=\"admin-card\">\n        <div className=\"table-container\">\n          <table className=\"admin-table\">\n            <thead>\n              <tr>\n                <th>Product</th>\n                <th>SKU</th>\n                <th>Current Stock</th>\n                <th>Available</th>\n                <th>Reserved</th>\n                <th>Reorder Level</th>\n                <th>Unit Cost</th>\n                <th>Total Value</th>\n                <th>Status</th>\n                <th>Location</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredItems.map(item => (\n                <tr key={item.id}>\n                  <td>\n                    <div className=\"product-cell\">\n                      <strong>{item.productName}</strong>\n                    </div>\n                  </td>\n                  <td>{item.sku}</td>\n                  <td>\n                    <span className=\"stock-number\">{item.currentStock}</span>\n                  </td>\n                  <td>{item.availableStock}</td>\n                  <td>{item.reservedStock}</td>\n                  <td>{item.reorderLevel}</td>\n                  <td>{formatCurrency(item.unitCost)}</td>\n                  <td>{formatCurrency(item.totalValue)}</td>\n                  <td>\n                    <span \n                      className=\"status-badge\"\n                      style={{ backgroundColor: getStatusColor(item.status) }}\n                    >\n                      {item.status}\n                    </span>\n                  </td>\n                  <td>{item.location}</td>\n                  <td>\n                    <button\n                      className=\"admin-btn admin-btn-secondary btn-small\"\n                      onClick={() => handleAdjustStock(item)}\n                    >\n                      Adjust\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Adjustment Modal */}\n      {showAdjustModal && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content\">\n            <div className=\"modal-header\">\n              <h3>Adjust Stock - {selectedItem?.productName}</h3>\n              <button \n                className=\"modal-close\"\n                onClick={() => setShowAdjustModal(false)}\n              >\n                ×\n              </button>\n            </div>\n            \n            <div className=\"modal-body\">\n              <div className=\"current-stock-info\">\n                <p><strong>Current Stock:</strong> {selectedItem?.currentStock}</p>\n                <p><strong>Available:</strong> {selectedItem?.availableStock}</p>\n              </div>\n\n              <div className=\"admin-form-group\">\n                <label className=\"admin-form-label\">Adjustment Amount</label>\n                <input\n                  type=\"number\"\n                  className=\"admin-form-input\"\n                  placeholder=\"Enter positive or negative number\"\n                  value={adjustmentData.adjustment}\n                  onChange={(e) => setAdjustmentData({\n                    ...adjustmentData,\n                    adjustment: e.target.value\n                  })}\n                />\n              </div>\n\n              <div className=\"admin-form-group\">\n                <label className=\"admin-form-label\">Reason</label>\n                <select\n                  className=\"admin-form-input\"\n                  value={adjustmentData.reason}\n                  onChange={(e) => setAdjustmentData({\n                    ...adjustmentData,\n                    reason: e.target.value\n                  })}\n                >\n                  <option value=\"\">Select reason</option>\n                  <option value=\"Stock Count\">Stock Count</option>\n                  <option value=\"Damaged Goods\">Damaged Goods</option>\n                  <option value=\"Returned Items\">Returned Items</option>\n                  <option value=\"New Shipment\">New Shipment</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n\n              <div className=\"admin-form-group\">\n                <label className=\"admin-form-label\">Notes (Optional)</label>\n                <textarea\n                  className=\"admin-form-input\"\n                  rows=\"3\"\n                  placeholder=\"Additional notes...\"\n                  value={adjustmentData.notes}\n                  onChange={(e) => setAdjustmentData({\n                    ...adjustmentData,\n                    notes: e.target.value\n                  })}\n                />\n              </div>\n            </div>\n\n            <div className=\"modal-footer\">\n              <button\n                className=\"admin-btn admin-btn-secondary\"\n                onClick={() => setShowAdjustModal(false)}\n              >\n                Cancel\n              </button>\n              <button\n                className=\"admin-btn admin-btn-primary\"\n                onClick={handleSubmitAdjustment}\n                disabled={!adjustmentData.adjustment || !adjustmentData.reason}\n              >\n                Apply Adjustment\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default InventoryManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC;IACnDqB,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM;IAAEC,WAAW;IAAEC;EAAiB,CAAC,GAAGvB,YAAY,CAAC,CAAC;EAExDD,SAAS,CAAC,MAAM;IACdyB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,CACf;QACEC,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,wBAAwB;QACrCC,GAAG,EAAE,SAAS;QACdC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,GAAG;QACbC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,QAAQ,EAAE,eAAe;QACzBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrCC,MAAM,EAAE;MACV,CAAC,EACD;QACEf,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,eAAe;QAC5BC,GAAG,EAAE,QAAQ;QACbC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,QAAQ,EAAE,eAAe;QACzBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrCC,MAAM,EAAE;MACV,CAAC,EACD;QACEf,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,kBAAkB;QAC/BC,GAAG,EAAE,QAAQ;QACbC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,QAAQ,EAAE,eAAe;QACzBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrCC,MAAM,EAAE;MACV,CAAC,CACF;MAEDnC,iBAAiB,CAACmB,QAAQ,CAAC;MAC3BjB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,iBAAiB,GAAIC,IAAI,IAAK;IAClC7B,eAAe,CAAC6B,IAAI,CAAC;IACrB/B,kBAAkB,CAAC,IAAI,CAAC;IACxBI,iBAAiB,CAAC;MAAEC,UAAU,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAC9D,CAAC;EAED,MAAMyB,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF;MACA,MAAM3B,UAAU,GAAG4B,QAAQ,CAAC9B,cAAc,CAACE,UAAU,CAAC;MACtD,MAAM6B,YAAY,GAAG3C,cAAc,CAAC4C,GAAG,CAACJ,IAAI,IAAI;QAC9C,IAAIA,IAAI,CAACnB,EAAE,KAAKX,YAAY,CAACW,EAAE,EAAE;UAC/B,MAAMwB,QAAQ,GAAGL,IAAI,CAACf,YAAY,GAAGX,UAAU;UAC/C,MAAMgC,YAAY,GAAGD,QAAQ,GAAGL,IAAI,CAACd,aAAa;UAClD,MAAMqB,QAAQ,GAAGF,QAAQ,GAAGL,IAAI,CAACV,QAAQ;UAEzC,IAAIkB,SAAS,GAAG,UAAU;UAC1B,IAAIH,QAAQ,IAAI,CAAC,EAAE;YACjBG,SAAS,GAAG,cAAc;UAC5B,CAAC,MAAM,IAAIH,QAAQ,IAAIL,IAAI,CAACZ,YAAY,EAAE;YACxCoB,SAAS,GAAG,UAAU;UACxB,CAAC,MAAM,IAAIH,QAAQ,IAAIL,IAAI,CAACZ,YAAY,GAAG,GAAG,EAAE;YAC9CoB,SAAS,GAAG,WAAW;UACzB;UAEA,OAAO;YACL,GAAGR,IAAI;YACPf,YAAY,EAAEoB,QAAQ;YACtBlB,cAAc,EAAEmB,YAAY;YAC5Bf,UAAU,EAAEgB,QAAQ;YACpBX,MAAM,EAAEY,SAAS;YACjBf,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACtC,CAAC;QACH;QACA,OAAOK,IAAI;MACb,CAAC,CAAC;MAEFvC,iBAAiB,CAAC0C,YAAY,CAAC;MAC/BlC,kBAAkB,CAAC,KAAK,CAAC;MACzBE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMY,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,cAAc,GAAIpB,MAAM,IAAK;IACjC,QAAQA,MAAM,CAACqB,WAAW,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,cAAc;QACjB,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,aAAa,GAAG1D,cAAc,CAAC2D,MAAM,CAACnB,IAAI,IAAI;IAClD,MAAMoB,aAAa,GAAGpB,IAAI,CAACjB,WAAW,CAACkC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACzD,UAAU,CAACqD,WAAW,CAAC,CAAC,CAAC,IAClEjB,IAAI,CAAChB,GAAG,CAACiC,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACzD,UAAU,CAACqD,WAAW,CAAC,CAAC,CAAC;IAC9E,MAAMK,aAAa,GAAGxD,YAAY,KAAK,KAAK,IAAIkC,IAAI,CAACJ,MAAM,CAACqB,WAAW,CAAC,CAAC,KAAKnD,YAAY,CAACmD,WAAW,CAAC,CAAC;IACxG,OAAOG,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;EAEF,IAAI5D,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKkE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCnE,OAAA;QAAKkE,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCvE,OAAA;QAAAmE,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEV;EAEA,oBACEvE,OAAA;IAAKkE,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCnE,OAAA;MAAKkE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BnE,OAAA;QAAKkE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnE,OAAA;UAAAmE,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BvE,OAAA;UAAAmE,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACNvE,OAAA;QAAKkE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnE,OAAA;UAAMkE,SAAS,EAAE,oBAAoB9C,WAAW,GAAG,WAAW,GAAG,cAAc,EAAG;UAAA+C,QAAA,EAC/E/C,WAAW,GAAG,IAAI,GAAG;QAAI;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACPvE,OAAA;UAAMkE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAC1B/C,WAAW,GAAG,0BAA0B,GAAG;QAAc;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvE,OAAA;MAAKkE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCnE,OAAA;QAAKkE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BnE,OAAA;UACEwE,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,oBAAoB;UAChCC,KAAK,EAAEnE,UAAW;UAClBoE,QAAQ,EAAGC,CAAC,IAAKpE,aAAa,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CR,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENvE,OAAA;QAAKkE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BnE,OAAA;UACE0E,KAAK,EAAEjE,YAAa;UACpBkE,QAAQ,EAAGC,CAAC,IAAKlE,eAAe,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACjDR,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzBnE,OAAA;YAAQ0E,KAAK,EAAC,KAAK;YAAAP,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvCvE,OAAA;YAAQ0E,KAAK,EAAC,UAAU;YAAAP,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CvE,OAAA;YAAQ0E,KAAK,EAAC,WAAW;YAAAP,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CvE,OAAA;YAAQ0E,KAAK,EAAC,UAAU;YAAAP,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CvE,OAAA;YAAQ0E,KAAK,EAAC,cAAc;YAAAP,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvE,OAAA;QAAQkE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAEhD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNvE,OAAA;MAAKkE,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBnE,OAAA;QAAKkE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BnE,OAAA;UAAOkE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC5BnE,OAAA;YAAAmE,QAAA,eACEnE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAAmE,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZvE,OAAA;gBAAAmE,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfvE,OAAA;gBAAAmE,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBvE,OAAA;gBAAAmE,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRvE,OAAA;YAAAmE,QAAA,EACGN,aAAa,CAACd,GAAG,CAACJ,IAAI,iBACrB3C,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAAmE,QAAA,eACEnE,OAAA;kBAAKkE,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BnE,OAAA;oBAAAmE,QAAA,EAASxB,IAAI,CAACjB;kBAAW;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLvE,OAAA;gBAAAmE,QAAA,EAAKxB,IAAI,CAAChB;cAAG;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnBvE,OAAA;gBAAAmE,QAAA,eACEnE,OAAA;kBAAMkE,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAExB,IAAI,CAACf;gBAAY;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACLvE,OAAA;gBAAAmE,QAAA,EAAKxB,IAAI,CAACb;cAAc;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BvE,OAAA;gBAAAmE,QAAA,EAAKxB,IAAI,CAACd;cAAa;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BvE,OAAA;gBAAAmE,QAAA,EAAKxB,IAAI,CAACZ;cAAY;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BvE,OAAA;gBAAAmE,QAAA,EAAKf,cAAc,CAACT,IAAI,CAACV,QAAQ;cAAC;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxCvE,OAAA;gBAAAmE,QAAA,EAAKf,cAAc,CAACT,IAAI,CAACT,UAAU;cAAC;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1CvE,OAAA;gBAAAmE,QAAA,eACEnE,OAAA;kBACEkE,SAAS,EAAC,cAAc;kBACxBV,KAAK,EAAE;oBAAEsB,eAAe,EAAEnB,cAAc,CAAChB,IAAI,CAACJ,MAAM;kBAAE,CAAE;kBAAA4B,QAAA,EAEvDxB,IAAI,CAACJ;gBAAM;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLvE,OAAA;gBAAAmE,QAAA,EAAKxB,IAAI,CAACR;cAAQ;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxBvE,OAAA;gBAAAmE,QAAA,eACEnE,OAAA;kBACEkE,SAAS,EAAC,yCAAyC;kBACnDa,OAAO,EAAEA,CAAA,KAAMrC,iBAAiB,CAACC,IAAI,CAAE;kBAAAwB,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GA/BE5B,IAAI,CAACnB,EAAE;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5D,eAAe,iBACdX,OAAA;MAAKkE,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BnE,OAAA;QAAKkE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BnE,OAAA;UAAKkE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnE,OAAA;YAAAmE,QAAA,GAAI,iBAAe,EAACtD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,WAAW;UAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDvE,OAAA;YACEkE,SAAS,EAAC,aAAa;YACvBa,OAAO,EAAEA,CAAA,KAAMnE,kBAAkB,CAAC,KAAK,CAAE;YAAAuD,QAAA,EAC1C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENvE,OAAA;UAAKkE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBnE,OAAA;YAAKkE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCnE,OAAA;cAAAmE,QAAA,gBAAGnE,OAAA;gBAAAmE,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEe,YAAY;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEvE,OAAA;cAAAmE,QAAA,gBAAGnE,OAAA;gBAAAmE,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiB,cAAc;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAENvE,OAAA;YAAKkE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnE,OAAA;cAAOkE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7DvE,OAAA;cACEwE,IAAI,EAAC,QAAQ;cACbN,SAAS,EAAC,kBAAkB;cAC5BO,WAAW,EAAC,mCAAmC;cAC/CC,KAAK,EAAE3D,cAAc,CAACE,UAAW;cACjC0D,QAAQ,EAAGC,CAAC,IAAK5D,iBAAiB,CAAC;gBACjC,GAAGD,cAAc;gBACjBE,UAAU,EAAE2D,CAAC,CAACC,MAAM,CAACH;cACvB,CAAC;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvE,OAAA;YAAKkE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnE,OAAA;cAAOkE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDvE,OAAA;cACEkE,SAAS,EAAC,kBAAkB;cAC5BQ,KAAK,EAAE3D,cAAc,CAACG,MAAO;cAC7ByD,QAAQ,EAAGC,CAAC,IAAK5D,iBAAiB,CAAC;gBACjC,GAAGD,cAAc;gBACjBG,MAAM,EAAE0D,CAAC,CAACC,MAAM,CAACH;cACnB,CAAC,CAAE;cAAAP,QAAA,gBAEHnE,OAAA;gBAAQ0E,KAAK,EAAC,EAAE;gBAAAP,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCvE,OAAA;gBAAQ0E,KAAK,EAAC,aAAa;gBAAAP,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDvE,OAAA;gBAAQ0E,KAAK,EAAC,eAAe;gBAAAP,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpDvE,OAAA;gBAAQ0E,KAAK,EAAC,gBAAgB;gBAAAP,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDvE,OAAA;gBAAQ0E,KAAK,EAAC,cAAc;gBAAAP,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDvE,OAAA;gBAAQ0E,KAAK,EAAC,OAAO;gBAAAP,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENvE,OAAA;YAAKkE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnE,OAAA;cAAOkE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5DvE,OAAA;cACEkE,SAAS,EAAC,kBAAkB;cAC5Bc,IAAI,EAAC,GAAG;cACRP,WAAW,EAAC,qBAAqB;cACjCC,KAAK,EAAE3D,cAAc,CAACI,KAAM;cAC5BwD,QAAQ,EAAGC,CAAC,IAAK5D,iBAAiB,CAAC;gBACjC,GAAGD,cAAc;gBACjBI,KAAK,EAAEyD,CAAC,CAACC,MAAM,CAACH;cAClB,CAAC;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvE,OAAA;UAAKkE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BnE,OAAA;YACEkE,SAAS,EAAC,+BAA+B;YACzCa,OAAO,EAAEA,CAAA,KAAMnE,kBAAkB,CAAC,KAAK,CAAE;YAAAuD,QAAA,EAC1C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvE,OAAA;YACEkE,SAAS,EAAC,6BAA6B;YACvCa,OAAO,EAAEnC,sBAAuB;YAChCqC,QAAQ,EAAE,CAAClE,cAAc,CAACE,UAAU,IAAI,CAACF,cAAc,CAACG,MAAO;YAAAiD,QAAA,EAChE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrE,EAAA,CAvWID,mBAAmB;EAAA,QAYmBH,YAAY;AAAA;AAAAoF,EAAA,GAZlDjF,mBAAmB;AAyWzB,eAAeA,mBAAmB;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}