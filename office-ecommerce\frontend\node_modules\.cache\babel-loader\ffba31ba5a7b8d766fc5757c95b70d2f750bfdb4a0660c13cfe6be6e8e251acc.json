{"ast": null, "code": "import { <PERSON><PERSON>, FileLoader, BufferGeometry, Float32BufferAttribute, BufferAttribute } from \"three\";\nimport { unzlibSync } from \"fflate\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nclass VTKLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data) {\n    function parseASCII(data2) {\n      var indices = [];\n      var positions = [];\n      var colors = [];\n      var normals = [];\n      var result;\n      var patWord = /^[^\\d.\\s-]+/;\n      var pat3Floats = /(\\-?\\d+\\.?[\\d\\-\\+e]*)\\s+(\\-?\\d+\\.?[\\d\\-\\+e]*)\\s+(\\-?\\d+\\.?[\\d\\-\\+e]*)/g;\n      var patConnectivity = /^(\\d+)\\s+([\\s\\d]*)/;\n      var patPOINTS = /^POINTS /;\n      var patPOLYGONS = /^POLYGONS /;\n      var patTRIANGLE_STRIPS = /^TRIANGLE_STRIPS /;\n      var patPOINT_DATA = /^POINT_DATA[ ]+(\\d+)/;\n      var patCELL_DATA = /^CELL_DATA[ ]+(\\d+)/;\n      var patCOLOR_SCALARS = /^COLOR_SCALARS[ ]+(\\w+)[ ]+3/;\n      var patNORMALS = /^NORMALS[ ]+(\\w+)[ ]+(\\w+)/;\n      var inPointsSection = false;\n      var inPolygonsSection = false;\n      var inTriangleStripSection = false;\n      var inPointDataSection = false;\n      var inCellDataSection = false;\n      var inColorSection = false;\n      var inNormalsSection = false;\n      var lines = data2.split(\"\\n\");\n      for (var i in lines) {\n        var line = lines[i].trim();\n        if (line.indexOf(\"DATASET\") === 0) {\n          var dataset = line.split(\" \")[1];\n          if (dataset !== \"POLYDATA\") throw new Error(\"Unsupported DATASET type: \" + dataset);\n        } else if (inPointsSection) {\n          while ((result = pat3Floats.exec(line)) !== null) {\n            if (patWord.exec(line) !== null) break;\n            var x = parseFloat(result[1]);\n            var y = parseFloat(result[2]);\n            var z = parseFloat(result[3]);\n            positions.push(x, y, z);\n          }\n        } else if (inPolygonsSection) {\n          if ((result = patConnectivity.exec(line)) !== null) {\n            var numVertices = parseInt(result[1]);\n            var inds = result[2].split(/\\s+/);\n            if (numVertices >= 3) {\n              var i0 = parseInt(inds[0]);\n              var i1, i2;\n              var k = 1;\n              for (var j = 0; j < numVertices - 2; ++j) {\n                i1 = parseInt(inds[k]);\n                i2 = parseInt(inds[k + 1]);\n                indices.push(i0, i1, i2);\n                k++;\n              }\n            }\n          }\n        } else if (inTriangleStripSection) {\n          if ((result = patConnectivity.exec(line)) !== null) {\n            var numVertices = parseInt(result[1]);\n            var inds = result[2].split(/\\s+/);\n            if (numVertices >= 3) {\n              var i0, i1, i2;\n              for (var j = 0; j < numVertices - 2; j++) {\n                if (j % 2 === 1) {\n                  i0 = parseInt(inds[j]);\n                  i1 = parseInt(inds[j + 2]);\n                  i2 = parseInt(inds[j + 1]);\n                  indices.push(i0, i1, i2);\n                } else {\n                  i0 = parseInt(inds[j]);\n                  i1 = parseInt(inds[j + 1]);\n                  i2 = parseInt(inds[j + 2]);\n                  indices.push(i0, i1, i2);\n                }\n              }\n            }\n          }\n        } else if (inPointDataSection || inCellDataSection) {\n          if (inColorSection) {\n            while ((result = pat3Floats.exec(line)) !== null) {\n              if (patWord.exec(line) !== null) break;\n              var r = parseFloat(result[1]);\n              var g = parseFloat(result[2]);\n              var b = parseFloat(result[3]);\n              colors.push(r, g, b);\n            }\n          } else if (inNormalsSection) {\n            while ((result = pat3Floats.exec(line)) !== null) {\n              if (patWord.exec(line) !== null) break;\n              var nx = parseFloat(result[1]);\n              var ny = parseFloat(result[2]);\n              var nz = parseFloat(result[3]);\n              normals.push(nx, ny, nz);\n            }\n          }\n        }\n        if (patPOLYGONS.exec(line) !== null) {\n          inPolygonsSection = true;\n          inPointsSection = false;\n          inTriangleStripSection = false;\n        } else if (patPOINTS.exec(line) !== null) {\n          inPolygonsSection = false;\n          inPointsSection = true;\n          inTriangleStripSection = false;\n        } else if (patTRIANGLE_STRIPS.exec(line) !== null) {\n          inPolygonsSection = false;\n          inPointsSection = false;\n          inTriangleStripSection = true;\n        } else if (patPOINT_DATA.exec(line) !== null) {\n          inPointDataSection = true;\n          inPointsSection = false;\n          inPolygonsSection = false;\n          inTriangleStripSection = false;\n        } else if (patCELL_DATA.exec(line) !== null) {\n          inCellDataSection = true;\n          inPointsSection = false;\n          inPolygonsSection = false;\n          inTriangleStripSection = false;\n        } else if (patCOLOR_SCALARS.exec(line) !== null) {\n          inColorSection = true;\n          inNormalsSection = false;\n          inPointsSection = false;\n          inPolygonsSection = false;\n          inTriangleStripSection = false;\n        } else if (patNORMALS.exec(line) !== null) {\n          inNormalsSection = true;\n          inColorSection = false;\n          inPointsSection = false;\n          inPolygonsSection = false;\n          inTriangleStripSection = false;\n        }\n      }\n      var geometry = new BufferGeometry();\n      geometry.setIndex(indices);\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(positions, 3));\n      if (normals.length === positions.length) {\n        geometry.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n      }\n      if (colors.length !== indices.length) {\n        if (colors.length === positions.length) {\n          geometry.setAttribute(\"color\", new Float32BufferAttribute(colors, 3));\n        }\n      } else {\n        geometry = geometry.toNonIndexed();\n        var numTriangles = geometry.attributes.position.count / 3;\n        if (colors.length === numTriangles * 3) {\n          var newColors = [];\n          for (var i = 0; i < numTriangles; i++) {\n            var r = colors[3 * i + 0];\n            var g = colors[3 * i + 1];\n            var b = colors[3 * i + 2];\n            newColors.push(r, g, b);\n            newColors.push(r, g, b);\n            newColors.push(r, g, b);\n          }\n          geometry.setAttribute(\"color\", new Float32BufferAttribute(newColors, 3));\n        }\n      }\n      return geometry;\n    }\n    function parseBinary(data2) {\n      var count, pointIndex, i, numberOfPoints, s;\n      var buffer = new Uint8Array(data2);\n      var dataView = new DataView(data2);\n      var points = [];\n      var normals = [];\n      var indices = [];\n      var index = 0;\n      function findString(buffer2, start) {\n        var index2 = start;\n        var c = buffer2[index2];\n        var s2 = [];\n        while (c !== 10) {\n          s2.push(String.fromCharCode(c));\n          index2++;\n          c = buffer2[index2];\n        }\n        return {\n          start,\n          end: index2,\n          next: index2 + 1,\n          parsedString: s2.join(\"\")\n        };\n      }\n      var state, line;\n      while (true) {\n        state = findString(buffer, index);\n        line = state.parsedString;\n        if (line.indexOf(\"DATASET\") === 0) {\n          var dataset = line.split(\" \")[1];\n          if (dataset !== \"POLYDATA\") throw new Error(\"Unsupported DATASET type: \" + dataset);\n        } else if (line.indexOf(\"POINTS\") === 0) {\n          numberOfPoints = parseInt(line.split(\" \")[1], 10);\n          count = numberOfPoints * 4 * 3;\n          points = new Float32Array(numberOfPoints * 3);\n          pointIndex = state.next;\n          for (i = 0; i < numberOfPoints; i++) {\n            points[3 * i] = dataView.getFloat32(pointIndex, false);\n            points[3 * i + 1] = dataView.getFloat32(pointIndex + 4, false);\n            points[3 * i + 2] = dataView.getFloat32(pointIndex + 8, false);\n            pointIndex = pointIndex + 12;\n          }\n          state.next = state.next + count + 1;\n        } else if (line.indexOf(\"TRIANGLE_STRIPS\") === 0) {\n          var numberOfStrips = parseInt(line.split(\" \")[1], 10);\n          var size = parseInt(line.split(\" \")[2], 10);\n          count = size * 4;\n          indices = new Uint32Array(3 * size - 9 * numberOfStrips);\n          var indicesIndex = 0;\n          pointIndex = state.next;\n          for (i = 0; i < numberOfStrips; i++) {\n            var indexCount = dataView.getInt32(pointIndex, false);\n            var strip = [];\n            pointIndex += 4;\n            for (s = 0; s < indexCount; s++) {\n              strip.push(dataView.getInt32(pointIndex, false));\n              pointIndex += 4;\n            }\n            for (var j = 0; j < indexCount - 2; j++) {\n              if (j % 2) {\n                indices[indicesIndex++] = strip[j];\n                indices[indicesIndex++] = strip[j + 2];\n                indices[indicesIndex++] = strip[j + 1];\n              } else {\n                indices[indicesIndex++] = strip[j];\n                indices[indicesIndex++] = strip[j + 1];\n                indices[indicesIndex++] = strip[j + 2];\n              }\n            }\n          }\n          state.next = state.next + count + 1;\n        } else if (line.indexOf(\"POLYGONS\") === 0) {\n          var numberOfStrips = parseInt(line.split(\" \")[1], 10);\n          var size = parseInt(line.split(\" \")[2], 10);\n          count = size * 4;\n          indices = new Uint32Array(3 * size - 9 * numberOfStrips);\n          var indicesIndex = 0;\n          pointIndex = state.next;\n          for (i = 0; i < numberOfStrips; i++) {\n            var indexCount = dataView.getInt32(pointIndex, false);\n            var strip = [];\n            pointIndex += 4;\n            for (s = 0; s < indexCount; s++) {\n              strip.push(dataView.getInt32(pointIndex, false));\n              pointIndex += 4;\n            }\n            for (var j = 1; j < indexCount - 1; j++) {\n              indices[indicesIndex++] = strip[0];\n              indices[indicesIndex++] = strip[j];\n              indices[indicesIndex++] = strip[j + 1];\n            }\n          }\n          state.next = state.next + count + 1;\n        } else if (line.indexOf(\"POINT_DATA\") === 0) {\n          numberOfPoints = parseInt(line.split(\" \")[1], 10);\n          state = findString(buffer, state.next);\n          count = numberOfPoints * 4 * 3;\n          normals = new Float32Array(numberOfPoints * 3);\n          pointIndex = state.next;\n          for (i = 0; i < numberOfPoints; i++) {\n            normals[3 * i] = dataView.getFloat32(pointIndex, false);\n            normals[3 * i + 1] = dataView.getFloat32(pointIndex + 4, false);\n            normals[3 * i + 2] = dataView.getFloat32(pointIndex + 8, false);\n            pointIndex += 12;\n          }\n          state.next = state.next + count;\n        }\n        index = state.next;\n        if (index >= buffer.byteLength) {\n          break;\n        }\n      }\n      var geometry = new BufferGeometry();\n      geometry.setIndex(new BufferAttribute(indices, 1));\n      geometry.setAttribute(\"position\", new BufferAttribute(points, 3));\n      if (normals.length === points.length) {\n        geometry.setAttribute(\"normal\", new BufferAttribute(normals, 3));\n      }\n      return geometry;\n    }\n    function Float32Concat(first, second) {\n      const firstLength = first.length,\n        result = new Float32Array(firstLength + second.length);\n      result.set(first);\n      result.set(second, firstLength);\n      return result;\n    }\n    function Int32Concat(first, second) {\n      var firstLength = first.length,\n        result = new Int32Array(firstLength + second.length);\n      result.set(first);\n      result.set(second, firstLength);\n      return result;\n    }\n    function parseXML(stringFile) {\n      function xmlToJson(xml) {\n        var obj = {};\n        if (xml.nodeType === 1) {\n          if (xml.attributes) {\n            if (xml.attributes.length > 0) {\n              obj[\"attributes\"] = {};\n              for (var j2 = 0; j2 < xml.attributes.length; j2++) {\n                var attribute = xml.attributes.item(j2);\n                obj[\"attributes\"][attribute.nodeName] = attribute.nodeValue.trim();\n              }\n            }\n          }\n        } else if (xml.nodeType === 3) {\n          obj = xml.nodeValue.trim();\n        }\n        if (xml.hasChildNodes()) {\n          for (var i2 = 0; i2 < xml.childNodes.length; i2++) {\n            var item = xml.childNodes.item(i2);\n            var nodeName = item.nodeName;\n            if (typeof obj[nodeName] === \"undefined\") {\n              var tmp = xmlToJson(item);\n              if (tmp !== \"\") obj[nodeName] = tmp;\n            } else {\n              if (typeof obj[nodeName].push === \"undefined\") {\n                var old = obj[nodeName];\n                obj[nodeName] = [old];\n              }\n              var tmp = xmlToJson(item);\n              if (tmp !== \"\") obj[nodeName].push(tmp);\n            }\n          }\n        }\n        return obj;\n      }\n      function Base64toByteArray(b64) {\n        var Arr = typeof Uint8Array !== \"undefined\" ? Uint8Array : Array;\n        var i2;\n        var revLookup = [];\n        var code = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n        var len2 = code.length;\n        for (i2 = 0; i2 < len2; i2++) {}\n        for (i2 = 0; i2 < len2; ++i2) {\n          revLookup[code.charCodeAt(i2)] = i2;\n        }\n        revLookup[\"-\".charCodeAt(0)] = 62;\n        revLookup[\"_\".charCodeAt(0)] = 63;\n        var j2, l, tmp, placeHolders, arr2;\n        var len2 = b64.length;\n        if (len2 % 4 > 0) {\n          throw new Error(\"Invalid string. Length must be a multiple of 4\");\n        }\n        placeHolders = b64[len2 - 2] === \"=\" ? 2 : b64[len2 - 1] === \"=\" ? 1 : 0;\n        arr2 = new Arr(len2 * 3 / 4 - placeHolders);\n        l = placeHolders > 0 ? len2 - 4 : len2;\n        var L = 0;\n        for (i2 = 0, j2 = 0; i2 < l; i2 += 4, j2 += 3) {\n          tmp = revLookup[b64.charCodeAt(i2)] << 18 | revLookup[b64.charCodeAt(i2 + 1)] << 12 | revLookup[b64.charCodeAt(i2 + 2)] << 6 | revLookup[b64.charCodeAt(i2 + 3)];\n          arr2[L++] = (tmp & 16711680) >> 16;\n          arr2[L++] = (tmp & 65280) >> 8;\n          arr2[L++] = tmp & 255;\n        }\n        if (placeHolders === 2) {\n          tmp = revLookup[b64.charCodeAt(i2)] << 2 | revLookup[b64.charCodeAt(i2 + 1)] >> 4;\n          arr2[L++] = tmp & 255;\n        } else if (placeHolders === 1) {\n          tmp = revLookup[b64.charCodeAt(i2)] << 10 | revLookup[b64.charCodeAt(i2 + 1)] << 4 | revLookup[b64.charCodeAt(i2 + 2)] >> 2;\n          arr2[L++] = tmp >> 8 & 255;\n          arr2[L++] = tmp & 255;\n        }\n        return arr2;\n      }\n      function parseDataArray(ele, compressed2) {\n        var numBytes = 0;\n        if (json.attributes.header_type === \"UInt64\") {\n          numBytes = 8;\n        } else if (json.attributes.header_type === \"UInt32\") {\n          numBytes = 4;\n        }\n        if (ele.attributes.format === \"binary\" && compressed2) {\n          var rawData, content, byteData, blocks, cSizeStart, headerSize, padding, dataOffsets, currentOffset;\n          if (ele.attributes.type === \"Float32\") {\n            var txt = new Float32Array();\n          } else if (ele.attributes.type === \"Int64\") {\n            var txt = new Int32Array();\n          }\n          rawData = ele[\"#text\"];\n          byteData = Base64toByteArray(rawData);\n          blocks = byteData[0];\n          for (var i2 = 1; i2 < numBytes - 1; i2++) {\n            blocks = blocks | byteData[i2] << i2 * numBytes;\n          }\n          headerSize = (blocks + 3) * numBytes;\n          padding = headerSize % 3 > 0 ? 3 - headerSize % 3 : 0;\n          headerSize = headerSize + padding;\n          dataOffsets = [];\n          currentOffset = headerSize;\n          dataOffsets.push(currentOffset);\n          cSizeStart = 3 * numBytes;\n          for (var i2 = 0; i2 < blocks; i2++) {\n            var currentBlockSize = byteData[i2 * numBytes + cSizeStart];\n            for (var j2 = 1; j2 < numBytes - 1; j2++) {\n              currentBlockSize = currentBlockSize | byteData[i2 * numBytes + cSizeStart + j2] << j2 * 8;\n            }\n            currentOffset = currentOffset + currentBlockSize;\n            dataOffsets.push(currentOffset);\n          }\n          for (var i2 = 0; i2 < dataOffsets.length - 1; i2++) {\n            var data2 = unzlibSync(byteData.slice(dataOffsets[i2], dataOffsets[i2 + 1]));\n            content = data2.buffer;\n            if (ele.attributes.type === \"Float32\") {\n              content = new Float32Array(content);\n              txt = Float32Concat(txt, content);\n            } else if (ele.attributes.type === \"Int64\") {\n              content = new Int32Array(content);\n              txt = Int32Concat(txt, content);\n            }\n          }\n          delete ele[\"#text\"];\n          if (ele.attributes.type === \"Int64\") {\n            if (ele.attributes.format === \"binary\") {\n              txt = txt.filter(function (el, idx) {\n                if (idx % 2 !== 1) return true;\n              });\n            }\n          }\n        } else {\n          if (ele.attributes.format === \"binary\" && !compressed2) {\n            var content = Base64toByteArray(ele[\"#text\"]);\n            content = content.slice(numBytes).buffer;\n          } else {\n            if (ele[\"#text\"]) {\n              var content = ele[\"#text\"].split(/\\s+/).filter(function (el) {\n                if (el !== \"\") return el;\n              });\n            } else {\n              var content = new Int32Array(0).buffer;\n            }\n          }\n          delete ele[\"#text\"];\n          if (ele.attributes.type === \"Float32\") {\n            var txt = new Float32Array(content);\n          } else if (ele.attributes.type === \"Int32\") {\n            var txt = new Int32Array(content);\n          } else if (ele.attributes.type === \"Int64\") {\n            var txt = new Int32Array(content);\n            if (ele.attributes.format === \"binary\") {\n              txt = txt.filter(function (el, idx) {\n                if (idx % 2 !== 1) return true;\n              });\n            }\n          }\n        }\n        return txt;\n      }\n      var dom = null;\n      if (window.DOMParser) {\n        try {\n          dom = new DOMParser().parseFromString(stringFile, \"text/xml\");\n        } catch (e) {\n          dom = null;\n        }\n      } else if (window.ActiveXObject) {\n        try {\n          dom = new ActiveXObject(\"Microsoft.XMLDOM\");\n          dom.async = false;\n          if (!dom.loadXML(\n            /* xml */\n          )) {\n            throw new Error(dom.parseError.reason + dom.parseError.srcText);\n          }\n        } catch (e) {\n          dom = null;\n        }\n      } else {\n        throw new Error(\"Cannot parse xml string!\");\n      }\n      var doc = dom.documentElement;\n      var json = xmlToJson(doc);\n      var points = [];\n      var normals = [];\n      var indices = [];\n      if (json.PolyData) {\n        var piece = json.PolyData.Piece;\n        var compressed = json.attributes.hasOwnProperty(\"compressor\");\n        var sections = [\"PointData\", \"Points\", \"Strips\", \"Polys\"];\n        var sectionIndex = 0,\n          numberOfSections = sections.length;\n        while (sectionIndex < numberOfSections) {\n          var section = piece[sections[sectionIndex]];\n          if (section && section.DataArray) {\n            if (Object.prototype.toString.call(section.DataArray) === \"[object Array]\") {\n              var arr = section.DataArray;\n            } else {\n              var arr = [section.DataArray];\n            }\n            var dataArrayIndex = 0,\n              numberOfDataArrays = arr.length;\n            while (dataArrayIndex < numberOfDataArrays) {\n              if (\"#text\" in arr[dataArrayIndex] && arr[dataArrayIndex][\"#text\"].length > 0) {\n                arr[dataArrayIndex].text = parseDataArray(arr[dataArrayIndex], compressed);\n              }\n              dataArrayIndex++;\n            }\n            switch (sections[sectionIndex]) {\n              case \"PointData\":\n                var numberOfPoints = parseInt(piece.attributes.NumberOfPoints);\n                var normalsName = section.attributes.Normals;\n                if (numberOfPoints > 0) {\n                  for (var i = 0, len = arr.length; i < len; i++) {\n                    if (normalsName === arr[i].attributes.Name) {\n                      var components = arr[i].attributes.NumberOfComponents;\n                      normals = new Float32Array(numberOfPoints * components);\n                      normals.set(arr[i].text, 0);\n                    }\n                  }\n                }\n                break;\n              case \"Points\":\n                var numberOfPoints = parseInt(piece.attributes.NumberOfPoints);\n                if (numberOfPoints > 0) {\n                  var components = section.DataArray.attributes.NumberOfComponents;\n                  points = new Float32Array(numberOfPoints * components);\n                  points.set(section.DataArray.text, 0);\n                }\n                break;\n              case \"Strips\":\n                var numberOfStrips = parseInt(piece.attributes.NumberOfStrips);\n                if (numberOfStrips > 0) {\n                  var connectivity = new Int32Array(section.DataArray[0].text.length);\n                  var offset = new Int32Array(section.DataArray[1].text.length);\n                  connectivity.set(section.DataArray[0].text, 0);\n                  offset.set(section.DataArray[1].text, 0);\n                  var size = numberOfStrips + connectivity.length;\n                  indices = new Uint32Array(3 * size - 9 * numberOfStrips);\n                  var indicesIndex = 0;\n                  for (var i = 0, len = numberOfStrips; i < len; i++) {\n                    var strip = [];\n                    for (var s = 0, len1 = offset[i], len0 = 0; s < len1 - len0; s++) {\n                      strip.push(connectivity[s]);\n                      if (i > 0) len0 = offset[i - 1];\n                    }\n                    for (var j = 0, len1 = offset[i], len0 = 0; j < len1 - len0 - 2; j++) {\n                      if (j % 2) {\n                        indices[indicesIndex++] = strip[j];\n                        indices[indicesIndex++] = strip[j + 2];\n                        indices[indicesIndex++] = strip[j + 1];\n                      } else {\n                        indices[indicesIndex++] = strip[j];\n                        indices[indicesIndex++] = strip[j + 1];\n                        indices[indicesIndex++] = strip[j + 2];\n                      }\n                      if (i > 0) len0 = offset[i - 1];\n                    }\n                  }\n                }\n                break;\n              case \"Polys\":\n                var numberOfPolys = parseInt(piece.attributes.NumberOfPolys);\n                if (numberOfPolys > 0) {\n                  var connectivity = new Int32Array(section.DataArray[0].text.length);\n                  var offset = new Int32Array(section.DataArray[1].text.length);\n                  connectivity.set(section.DataArray[0].text, 0);\n                  offset.set(section.DataArray[1].text, 0);\n                  var size = numberOfPolys + connectivity.length;\n                  indices = new Uint32Array(3 * size - 9 * numberOfPolys);\n                  var indicesIndex = 0,\n                    connectivityIndex = 0;\n                  var i = 0,\n                    len = numberOfPolys,\n                    len0 = 0;\n                  while (i < len) {\n                    var poly = [];\n                    var s = 0,\n                      len1 = offset[i];\n                    while (s < len1 - len0) {\n                      poly.push(connectivity[connectivityIndex++]);\n                      s++;\n                    }\n                    var j = 1;\n                    while (j < len1 - len0 - 1) {\n                      indices[indicesIndex++] = poly[0];\n                      indices[indicesIndex++] = poly[j];\n                      indices[indicesIndex++] = poly[j + 1];\n                      j++;\n                    }\n                    i++;\n                    len0 = offset[i - 1];\n                  }\n                }\n                break;\n            }\n          }\n          sectionIndex++;\n        }\n        var geometry = new BufferGeometry();\n        geometry.setIndex(new BufferAttribute(indices, 1));\n        geometry.setAttribute(\"position\", new BufferAttribute(points, 3));\n        if (normals.length === points.length) {\n          geometry.setAttribute(\"normal\", new BufferAttribute(normals, 3));\n        }\n        return geometry;\n      } else {\n        throw new Error(\"Unsupported DATASET type\");\n      }\n    }\n    var meta = decodeText(new Uint8Array(data, 0, 250)).split(\"\\n\");\n    if (meta[0].indexOf(\"xml\") !== -1) {\n      return parseXML(decodeText(data));\n    } else if (meta[2].includes(\"ASCII\")) {\n      return parseASCII(decodeText(data));\n    } else {\n      return parseBinary(data);\n    }\n  }\n}\nexport { VTKLoader };", "map": {"version": 3, "names": ["VTKLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "data", "parseASCII", "data2", "indices", "positions", "colors", "normals", "result", "<PERSON><PERSON><PERSON>", "pat3Floats", "patConnectivity", "patPOINTS", "patPOLYGONS", "patTRIANGLE_STRIPS", "patPOINT_DATA", "patCELL_DATA", "patCOLOR_SCALARS", "patNORMALS", "inPointsSection", "inPolygonsSection", "inTriangleStripSection", "inPointDataSection", "inCellDataSection", "inColorSection", "inNormalsSection", "lines", "split", "i", "line", "trim", "indexOf", "dataset", "Error", "exec", "x", "parseFloat", "y", "z", "push", "numVertices", "parseInt", "inds", "i0", "i1", "i2", "k", "j", "r", "g", "b", "nx", "ny", "nz", "geometry", "BufferGeometry", "setIndex", "setAttribute", "Float32BufferAttribute", "length", "toNonIndexed", "numTriangles", "attributes", "position", "count", "newColors", "parseBinary", "pointIndex", "numberOfPoints", "s", "buffer", "Uint8Array", "dataView", "DataView", "points", "index", "findString", "buffer2", "start", "index2", "c", "s2", "String", "fromCharCode", "end", "next", "parsedString", "join", "state", "Float32Array", "getFloat32", "numberOfStrips", "size", "Uint32Array", "indicesIndex", "indexCount", "getInt32", "strip", "byteLength", "BufferAttribute", "Float32Concat", "first", "second", "first<PERSON><PERSON><PERSON>", "set", "Int32Concat", "Int32Array", "parseXML", "stringFile", "xmlToJson", "xml", "obj", "nodeType", "j2", "attribute", "item", "nodeName", "nodeValue", "hasChildNodes", "childNodes", "tmp", "old", "Base64toByteArray", "b64", "Arr", "Array", "revLookup", "code", "len2", "charCodeAt", "l", "placeHolders", "arr2", "L", "parseDataArray", "ele", "compressed2", "numBytes", "json", "header_type", "format", "rawData", "content", "byteData", "blocks", "cSizeStart", "headerSize", "padding", "dataOffsets", "currentOffset", "type", "txt", "currentBlockSize", "unzlibSync", "slice", "filter", "el", "idx", "dom", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "ActiveXObject", "async", "loadXML", "parseError", "reason", "srcText", "doc", "documentElement", "PolyData", "piece", "Piece", "compressed", "hasOwnProperty", "sections", "sectionIndex", "numberOfSections", "section", "DataArray", "Object", "prototype", "toString", "call", "arr", "dataArrayIndex", "numberOfDataArrays", "NumberOfPoints", "normalsName", "Normals", "len", "Name", "components", "NumberOfComponents", "NumberOfStrips", "connectivity", "offset", "len1", "len0", "numberOfPolys", "NumberOfPolys", "connectivityIndex", "poly", "meta", "decodeText", "includes"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\VTKLoader.js"], "sourcesContent": ["import { BufferAttribute, BufferGeometry, FileLoader, Float32BufferAttribute, Loader, LoaderUtils } from 'three'\nimport { unzlibSync } from 'fflate'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nclass VTKLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    function parseASCII(data) {\n      // connectivity of the triangles\n      var indices = []\n\n      // triangles vertices\n      var positions = []\n\n      // red, green, blue colors in the range 0 to 1\n      var colors = []\n\n      // normal vector, one per vertex\n      var normals = []\n\n      var result\n\n      // pattern for detecting the end of a number sequence\n      var patWord = /^[^\\d.\\s-]+/\n\n      // pattern for reading vertices, 3 floats or integers\n      var pat3Floats = /(\\-?\\d+\\.?[\\d\\-\\+e]*)\\s+(\\-?\\d+\\.?[\\d\\-\\+e]*)\\s+(\\-?\\d+\\.?[\\d\\-\\+e]*)/g\n\n      // pattern for connectivity, an integer followed by any number of ints\n      // the first integer is the number of polygon nodes\n      var patConnectivity = /^(\\d+)\\s+([\\s\\d]*)/\n\n      // indicates start of vertex data section\n      var patPOINTS = /^POINTS /\n\n      // indicates start of polygon connectivity section\n      var patPOLYGONS = /^POLYGONS /\n\n      // indicates start of triangle strips section\n      var patTRIANGLE_STRIPS = /^TRIANGLE_STRIPS /\n\n      // POINT_DATA number_of_values\n      var patPOINT_DATA = /^POINT_DATA[ ]+(\\d+)/\n\n      // CELL_DATA number_of_polys\n      var patCELL_DATA = /^CELL_DATA[ ]+(\\d+)/\n\n      // Start of color section\n      var patCOLOR_SCALARS = /^COLOR_SCALARS[ ]+(\\w+)[ ]+3/\n\n      // NORMALS Normals float\n      var patNORMALS = /^NORMALS[ ]+(\\w+)[ ]+(\\w+)/\n\n      var inPointsSection = false\n      var inPolygonsSection = false\n      var inTriangleStripSection = false\n      var inPointDataSection = false\n      var inCellDataSection = false\n      var inColorSection = false\n      var inNormalsSection = false\n\n      var lines = data.split('\\n')\n\n      for (var i in lines) {\n        var line = lines[i].trim()\n\n        if (line.indexOf('DATASET') === 0) {\n          var dataset = line.split(' ')[1]\n\n          if (dataset !== 'POLYDATA') throw new Error('Unsupported DATASET type: ' + dataset)\n        } else if (inPointsSection) {\n          // get the vertices\n          while ((result = pat3Floats.exec(line)) !== null) {\n            if (patWord.exec(line) !== null) break\n\n            var x = parseFloat(result[1])\n            var y = parseFloat(result[2])\n            var z = parseFloat(result[3])\n            positions.push(x, y, z)\n          }\n        } else if (inPolygonsSection) {\n          if ((result = patConnectivity.exec(line)) !== null) {\n            // numVertices i0 i1 i2 ...\n            var numVertices = parseInt(result[1])\n            var inds = result[2].split(/\\s+/)\n\n            if (numVertices >= 3) {\n              var i0 = parseInt(inds[0])\n              var i1, i2\n              var k = 1\n              // split the polygon in numVertices - 2 triangles\n              for (var j = 0; j < numVertices - 2; ++j) {\n                i1 = parseInt(inds[k])\n                i2 = parseInt(inds[k + 1])\n                indices.push(i0, i1, i2)\n                k++\n              }\n            }\n          }\n        } else if (inTriangleStripSection) {\n          if ((result = patConnectivity.exec(line)) !== null) {\n            // numVertices i0 i1 i2 ...\n            var numVertices = parseInt(result[1])\n            var inds = result[2].split(/\\s+/)\n\n            if (numVertices >= 3) {\n              var i0, i1, i2\n              // split the polygon in numVertices - 2 triangles\n              for (var j = 0; j < numVertices - 2; j++) {\n                if (j % 2 === 1) {\n                  i0 = parseInt(inds[j])\n                  i1 = parseInt(inds[j + 2])\n                  i2 = parseInt(inds[j + 1])\n                  indices.push(i0, i1, i2)\n                } else {\n                  i0 = parseInt(inds[j])\n                  i1 = parseInt(inds[j + 1])\n                  i2 = parseInt(inds[j + 2])\n                  indices.push(i0, i1, i2)\n                }\n              }\n            }\n          }\n        } else if (inPointDataSection || inCellDataSection) {\n          if (inColorSection) {\n            // Get the colors\n\n            while ((result = pat3Floats.exec(line)) !== null) {\n              if (patWord.exec(line) !== null) break\n\n              var r = parseFloat(result[1])\n              var g = parseFloat(result[2])\n              var b = parseFloat(result[3])\n              colors.push(r, g, b)\n            }\n          } else if (inNormalsSection) {\n            // Get the normal vectors\n\n            while ((result = pat3Floats.exec(line)) !== null) {\n              if (patWord.exec(line) !== null) break\n\n              var nx = parseFloat(result[1])\n              var ny = parseFloat(result[2])\n              var nz = parseFloat(result[3])\n              normals.push(nx, ny, nz)\n            }\n          }\n        }\n\n        if (patPOLYGONS.exec(line) !== null) {\n          inPolygonsSection = true\n          inPointsSection = false\n          inTriangleStripSection = false\n        } else if (patPOINTS.exec(line) !== null) {\n          inPolygonsSection = false\n          inPointsSection = true\n          inTriangleStripSection = false\n        } else if (patTRIANGLE_STRIPS.exec(line) !== null) {\n          inPolygonsSection = false\n          inPointsSection = false\n          inTriangleStripSection = true\n        } else if (patPOINT_DATA.exec(line) !== null) {\n          inPointDataSection = true\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        } else if (patCELL_DATA.exec(line) !== null) {\n          inCellDataSection = true\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        } else if (patCOLOR_SCALARS.exec(line) !== null) {\n          inColorSection = true\n          inNormalsSection = false\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        } else if (patNORMALS.exec(line) !== null) {\n          inNormalsSection = true\n          inColorSection = false\n          inPointsSection = false\n          inPolygonsSection = false\n          inTriangleStripSection = false\n        }\n      }\n\n      var geometry = new BufferGeometry()\n      geometry.setIndex(indices)\n      geometry.setAttribute('position', new Float32BufferAttribute(positions, 3))\n\n      if (normals.length === positions.length) {\n        geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n      }\n\n      if (colors.length !== indices.length) {\n        // stagger\n\n        if (colors.length === positions.length) {\n          geometry.setAttribute('color', new Float32BufferAttribute(colors, 3))\n        }\n      } else {\n        // cell\n\n        geometry = geometry.toNonIndexed()\n        var numTriangles = geometry.attributes.position.count / 3\n\n        if (colors.length === numTriangles * 3) {\n          var newColors = []\n\n          for (var i = 0; i < numTriangles; i++) {\n            var r = colors[3 * i + 0]\n            var g = colors[3 * i + 1]\n            var b = colors[3 * i + 2]\n\n            newColors.push(r, g, b)\n            newColors.push(r, g, b)\n            newColors.push(r, g, b)\n          }\n\n          geometry.setAttribute('color', new Float32BufferAttribute(newColors, 3))\n        }\n      }\n\n      return geometry\n    }\n\n    function parseBinary(data) {\n      var count, pointIndex, i, numberOfPoints, s\n      var buffer = new Uint8Array(data)\n      var dataView = new DataView(data)\n\n      // Points and normals, by default, are empty\n      var points = []\n      var normals = []\n      var indices = []\n\n      // Going to make a big array of strings\n      var vtk = []\n      var index = 0\n\n      function findString(buffer, start) {\n        var index = start\n        var c = buffer[index]\n        var s = []\n        while (c !== 10) {\n          s.push(String.fromCharCode(c))\n          index++\n          c = buffer[index]\n        }\n\n        return { start: start, end: index, next: index + 1, parsedString: s.join('') }\n      }\n\n      var state, line\n\n      while (true) {\n        // Get a string\n        state = findString(buffer, index)\n        line = state.parsedString\n\n        if (line.indexOf('DATASET') === 0) {\n          var dataset = line.split(' ')[1]\n\n          if (dataset !== 'POLYDATA') throw new Error('Unsupported DATASET type: ' + dataset)\n        } else if (line.indexOf('POINTS') === 0) {\n          vtk.push(line)\n          // Add the points\n          numberOfPoints = parseInt(line.split(' ')[1], 10)\n\n          // Each point is 3 4-byte floats\n          count = numberOfPoints * 4 * 3\n\n          points = new Float32Array(numberOfPoints * 3)\n\n          pointIndex = state.next\n          for (i = 0; i < numberOfPoints; i++) {\n            points[3 * i] = dataView.getFloat32(pointIndex, false)\n            points[3 * i + 1] = dataView.getFloat32(pointIndex + 4, false)\n            points[3 * i + 2] = dataView.getFloat32(pointIndex + 8, false)\n            pointIndex = pointIndex + 12\n          }\n\n          // increment our next pointer\n          state.next = state.next + count + 1\n        } else if (line.indexOf('TRIANGLE_STRIPS') === 0) {\n          var numberOfStrips = parseInt(line.split(' ')[1], 10)\n          var size = parseInt(line.split(' ')[2], 10)\n          // 4 byte integers\n          count = size * 4\n\n          indices = new Uint32Array(3 * size - 9 * numberOfStrips)\n          var indicesIndex = 0\n\n          pointIndex = state.next\n          for (i = 0; i < numberOfStrips; i++) {\n            // For each strip, read the first value, then record that many more points\n            var indexCount = dataView.getInt32(pointIndex, false)\n            var strip = []\n            pointIndex += 4\n            for (s = 0; s < indexCount; s++) {\n              strip.push(dataView.getInt32(pointIndex, false))\n              pointIndex += 4\n            }\n\n            // retrieves the n-2 triangles from the triangle strip\n            for (var j = 0; j < indexCount - 2; j++) {\n              if (j % 2) {\n                indices[indicesIndex++] = strip[j]\n                indices[indicesIndex++] = strip[j + 2]\n                indices[indicesIndex++] = strip[j + 1]\n              } else {\n                indices[indicesIndex++] = strip[j]\n                indices[indicesIndex++] = strip[j + 1]\n                indices[indicesIndex++] = strip[j + 2]\n              }\n            }\n          }\n\n          // increment our next pointer\n          state.next = state.next + count + 1\n        } else if (line.indexOf('POLYGONS') === 0) {\n          var numberOfStrips = parseInt(line.split(' ')[1], 10)\n          var size = parseInt(line.split(' ')[2], 10)\n          // 4 byte integers\n          count = size * 4\n\n          indices = new Uint32Array(3 * size - 9 * numberOfStrips)\n          var indicesIndex = 0\n\n          pointIndex = state.next\n          for (i = 0; i < numberOfStrips; i++) {\n            // For each strip, read the first value, then record that many more points\n            var indexCount = dataView.getInt32(pointIndex, false)\n            var strip = []\n            pointIndex += 4\n            for (s = 0; s < indexCount; s++) {\n              strip.push(dataView.getInt32(pointIndex, false))\n              pointIndex += 4\n            }\n\n            // divide the polygon in n-2 triangle\n            for (var j = 1; j < indexCount - 1; j++) {\n              indices[indicesIndex++] = strip[0]\n              indices[indicesIndex++] = strip[j]\n              indices[indicesIndex++] = strip[j + 1]\n            }\n          }\n\n          // increment our next pointer\n          state.next = state.next + count + 1\n        } else if (line.indexOf('POINT_DATA') === 0) {\n          numberOfPoints = parseInt(line.split(' ')[1], 10)\n\n          // Grab the next line\n          state = findString(buffer, state.next)\n\n          // Now grab the binary data\n          count = numberOfPoints * 4 * 3\n\n          normals = new Float32Array(numberOfPoints * 3)\n          pointIndex = state.next\n          for (i = 0; i < numberOfPoints; i++) {\n            normals[3 * i] = dataView.getFloat32(pointIndex, false)\n            normals[3 * i + 1] = dataView.getFloat32(pointIndex + 4, false)\n            normals[3 * i + 2] = dataView.getFloat32(pointIndex + 8, false)\n            pointIndex += 12\n          }\n\n          // Increment past our data\n          state.next = state.next + count\n        }\n\n        // Increment index\n        index = state.next\n\n        if (index >= buffer.byteLength) {\n          break\n        }\n      }\n\n      var geometry = new BufferGeometry()\n      geometry.setIndex(new BufferAttribute(indices, 1))\n      geometry.setAttribute('position', new BufferAttribute(points, 3))\n\n      if (normals.length === points.length) {\n        geometry.setAttribute('normal', new BufferAttribute(normals, 3))\n      }\n\n      return geometry\n    }\n\n    function Float32Concat(first, second) {\n      const firstLength = first.length,\n        result = new Float32Array(firstLength + second.length)\n\n      result.set(first)\n      result.set(second, firstLength)\n\n      return result\n    }\n\n    function Int32Concat(first, second) {\n      var firstLength = first.length,\n        result = new Int32Array(firstLength + second.length)\n\n      result.set(first)\n      result.set(second, firstLength)\n\n      return result\n    }\n\n    function parseXML(stringFile) {\n      // Changes XML to JSON, based on https://davidwalsh.name/convert-xml-json\n\n      function xmlToJson(xml) {\n        // Create the return object\n        var obj = {}\n\n        if (xml.nodeType === 1) {\n          // element\n\n          // do attributes\n\n          if (xml.attributes) {\n            if (xml.attributes.length > 0) {\n              obj['attributes'] = {}\n\n              for (var j = 0; j < xml.attributes.length; j++) {\n                var attribute = xml.attributes.item(j)\n                obj['attributes'][attribute.nodeName] = attribute.nodeValue.trim()\n              }\n            }\n          }\n        } else if (xml.nodeType === 3) {\n          // text\n\n          obj = xml.nodeValue.trim()\n        }\n\n        // do children\n        if (xml.hasChildNodes()) {\n          for (var i = 0; i < xml.childNodes.length; i++) {\n            var item = xml.childNodes.item(i)\n            var nodeName = item.nodeName\n\n            if (typeof obj[nodeName] === 'undefined') {\n              var tmp = xmlToJson(item)\n\n              if (tmp !== '') obj[nodeName] = tmp\n            } else {\n              if (typeof obj[nodeName].push === 'undefined') {\n                var old = obj[nodeName]\n                obj[nodeName] = [old]\n              }\n\n              var tmp = xmlToJson(item)\n\n              if (tmp !== '') obj[nodeName].push(tmp)\n            }\n          }\n        }\n\n        return obj\n      }\n\n      // Taken from Base64-js\n      function Base64toByteArray(b64) {\n        var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n        var i\n        var lookup = []\n        var revLookup = []\n        var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\n        var len = code.length\n\n        for (i = 0; i < len; i++) {\n          lookup[i] = code[i]\n        }\n\n        for (i = 0; i < len; ++i) {\n          revLookup[code.charCodeAt(i)] = i\n        }\n\n        revLookup['-'.charCodeAt(0)] = 62\n        revLookup['_'.charCodeAt(0)] = 63\n\n        var j, l, tmp, placeHolders, arr\n        var len = b64.length\n\n        if (len % 4 > 0) {\n          throw new Error('Invalid string. Length must be a multiple of 4')\n        }\n\n        placeHolders = b64[len - 2] === '=' ? 2 : b64[len - 1] === '=' ? 1 : 0\n        arr = new Arr((len * 3) / 4 - placeHolders)\n        l = placeHolders > 0 ? len - 4 : len\n\n        var L = 0\n\n        for (i = 0, j = 0; i < l; i += 4, j += 3) {\n          tmp =\n            (revLookup[b64.charCodeAt(i)] << 18) |\n            (revLookup[b64.charCodeAt(i + 1)] << 12) |\n            (revLookup[b64.charCodeAt(i + 2)] << 6) |\n            revLookup[b64.charCodeAt(i + 3)]\n          arr[L++] = (tmp & 0xff0000) >> 16\n          arr[L++] = (tmp & 0xff00) >> 8\n          arr[L++] = tmp & 0xff\n        }\n\n        if (placeHolders === 2) {\n          tmp = (revLookup[b64.charCodeAt(i)] << 2) | (revLookup[b64.charCodeAt(i + 1)] >> 4)\n          arr[L++] = tmp & 0xff\n        } else if (placeHolders === 1) {\n          tmp =\n            (revLookup[b64.charCodeAt(i)] << 10) |\n            (revLookup[b64.charCodeAt(i + 1)] << 4) |\n            (revLookup[b64.charCodeAt(i + 2)] >> 2)\n          arr[L++] = (tmp >> 8) & 0xff\n          arr[L++] = tmp & 0xff\n        }\n\n        return arr\n      }\n\n      function parseDataArray(ele, compressed) {\n        var numBytes = 0\n\n        if (json.attributes.header_type === 'UInt64') {\n          numBytes = 8\n        } else if (json.attributes.header_type === 'UInt32') {\n          numBytes = 4\n        }\n\n        // Check the format\n        if (ele.attributes.format === 'binary' && compressed) {\n          var rawData, content, byteData, blocks, cSizeStart, headerSize, padding, dataOffsets, currentOffset\n\n          if (ele.attributes.type === 'Float32') {\n            var txt = new Float32Array()\n          } else if (ele.attributes.type === 'Int64') {\n            var txt = new Int32Array()\n          }\n\n          // VTP data with the header has the following structure:\n          // [#blocks][#u-size][#p-size][#c-size-1][#c-size-2]...[#c-size-#blocks][DATA]\n          //\n          // Each token is an integer value whose type is specified by \"header_type\" at the top of the file (UInt32 if no type specified). The token meanings are:\n          // [#blocks] = Number of blocks\n          // [#u-size] = Block size before compression\n          // [#p-size] = Size of last partial block (zero if it not needed)\n          // [#c-size-i] = Size in bytes of block i after compression\n          //\n          // The [DATA] portion stores contiguously every block appended together. The offset from the beginning of the data section to the beginning of a block is\n          // computed by summing the compressed block sizes from preceding blocks according to the header.\n\n          rawData = ele['#text']\n\n          byteData = Base64toByteArray(rawData)\n\n          blocks = byteData[0]\n          for (var i = 1; i < numBytes - 1; i++) {\n            blocks = blocks | (byteData[i] << (i * numBytes))\n          }\n\n          headerSize = (blocks + 3) * numBytes\n          padding = headerSize % 3 > 0 ? 3 - (headerSize % 3) : 0\n          headerSize = headerSize + padding\n\n          dataOffsets = []\n          currentOffset = headerSize\n          dataOffsets.push(currentOffset)\n\n          // Get the blocks sizes after the compression.\n          // There are three blocks before c-size-i, so we skip 3*numBytes\n          cSizeStart = 3 * numBytes\n\n          for (var i = 0; i < blocks; i++) {\n            var currentBlockSize = byteData[i * numBytes + cSizeStart]\n\n            for (var j = 1; j < numBytes - 1; j++) {\n              // Each data point consists of 8 bytes regardless of the header type\n              currentBlockSize = currentBlockSize | (byteData[i * numBytes + cSizeStart + j] << (j * 8))\n            }\n\n            currentOffset = currentOffset + currentBlockSize\n            dataOffsets.push(currentOffset)\n          }\n\n          for (var i = 0; i < dataOffsets.length - 1; i++) {\n            var data = unzlibSync(byteData.slice(dataOffsets[i], dataOffsets[i + 1]))\n            content = data.buffer\n\n            if (ele.attributes.type === 'Float32') {\n              content = new Float32Array(content)\n              txt = Float32Concat(txt, content)\n            } else if (ele.attributes.type === 'Int64') {\n              content = new Int32Array(content)\n              txt = Int32Concat(txt, content)\n            }\n          }\n\n          delete ele['#text']\n\n          if (ele.attributes.type === 'Int64') {\n            if (ele.attributes.format === 'binary') {\n              txt = txt.filter(function (el, idx) {\n                if (idx % 2 !== 1) return true\n              })\n            }\n          }\n        } else {\n          if (ele.attributes.format === 'binary' && !compressed) {\n            var content = Base64toByteArray(ele['#text'])\n\n            //  VTP data for the uncompressed case has the following structure:\n            // [#bytes][DATA]\n            // where \"[#bytes]\" is an integer value specifying the number of bytes in the block of data following it.\n            content = content.slice(numBytes).buffer\n          } else {\n            if (ele['#text']) {\n              var content = ele['#text'].split(/\\s+/).filter(function (el) {\n                if (el !== '') return el\n              })\n            } else {\n              var content = new Int32Array(0).buffer\n            }\n          }\n\n          delete ele['#text']\n\n          // Get the content and optimize it\n          if (ele.attributes.type === 'Float32') {\n            var txt = new Float32Array(content)\n          } else if (ele.attributes.type === 'Int32') {\n            var txt = new Int32Array(content)\n          } else if (ele.attributes.type === 'Int64') {\n            var txt = new Int32Array(content)\n\n            if (ele.attributes.format === 'binary') {\n              txt = txt.filter(function (el, idx) {\n                if (idx % 2 !== 1) return true\n              })\n            }\n          }\n        } // endif ( ele.attributes.format === 'binary' && compressed )\n\n        return txt\n      }\n\n      // Main part\n      // Get Dom\n      var dom = null\n\n      if (window.DOMParser) {\n        try {\n          dom = new DOMParser().parseFromString(stringFile, 'text/xml')\n        } catch (e) {\n          dom = null\n        }\n      } else if (window.ActiveXObject) {\n        try {\n          dom = new ActiveXObject('Microsoft.XMLDOM')\n          dom.async = false\n\n          if (!(dom.loadXML(/* xml */))) {\n            throw new Error(dom.parseError.reason + dom.parseError.srcText)\n          }\n        } catch (e) {\n          dom = null\n        }\n      } else {\n        throw new Error('Cannot parse xml string!')\n      }\n\n      // Get the doc\n      var doc = dom.documentElement\n      // Convert to json\n      var json = xmlToJson(doc)\n      var points = []\n      var normals = []\n      var indices = []\n\n      if (json.PolyData) {\n        var piece = json.PolyData.Piece\n        var compressed = json.attributes.hasOwnProperty('compressor')\n\n        // Can be optimized\n        // Loop through the sections\n        var sections = ['PointData', 'Points', 'Strips', 'Polys'] // +['CellData', 'Verts', 'Lines'];\n        var sectionIndex = 0,\n          numberOfSections = sections.length\n\n        while (sectionIndex < numberOfSections) {\n          var section = piece[sections[sectionIndex]]\n\n          // If it has a DataArray in it\n\n          if (section && section.DataArray) {\n            // Depending on the number of DataArrays\n\n            if (Object.prototype.toString.call(section.DataArray) === '[object Array]') {\n              var arr = section.DataArray\n            } else {\n              var arr = [section.DataArray]\n            }\n\n            var dataArrayIndex = 0,\n              numberOfDataArrays = arr.length\n\n            while (dataArrayIndex < numberOfDataArrays) {\n              // Parse the DataArray\n              if ('#text' in arr[dataArrayIndex] && arr[dataArrayIndex]['#text'].length > 0) {\n                arr[dataArrayIndex].text = parseDataArray(arr[dataArrayIndex], compressed)\n              }\n\n              dataArrayIndex++\n            }\n\n            switch (sections[sectionIndex]) {\n              // if iti is point data\n              case 'PointData':\n                var numberOfPoints = parseInt(piece.attributes.NumberOfPoints)\n                var normalsName = section.attributes.Normals\n\n                if (numberOfPoints > 0) {\n                  for (var i = 0, len = arr.length; i < len; i++) {\n                    if (normalsName === arr[i].attributes.Name) {\n                      var components = arr[i].attributes.NumberOfComponents\n                      normals = new Float32Array(numberOfPoints * components)\n                      normals.set(arr[i].text, 0)\n                    }\n                  }\n                }\n\n                break\n\n              // if it is points\n              case 'Points':\n                var numberOfPoints = parseInt(piece.attributes.NumberOfPoints)\n\n                if (numberOfPoints > 0) {\n                  var components = section.DataArray.attributes.NumberOfComponents\n                  points = new Float32Array(numberOfPoints * components)\n                  points.set(section.DataArray.text, 0)\n                }\n\n                break\n\n              // if it is strips\n              case 'Strips':\n                var numberOfStrips = parseInt(piece.attributes.NumberOfStrips)\n\n                if (numberOfStrips > 0) {\n                  var connectivity = new Int32Array(section.DataArray[0].text.length)\n                  var offset = new Int32Array(section.DataArray[1].text.length)\n                  connectivity.set(section.DataArray[0].text, 0)\n                  offset.set(section.DataArray[1].text, 0)\n\n                  var size = numberOfStrips + connectivity.length\n                  indices = new Uint32Array(3 * size - 9 * numberOfStrips)\n\n                  var indicesIndex = 0\n\n                  for (var i = 0, len = numberOfStrips; i < len; i++) {\n                    var strip = []\n\n                    for (var s = 0, len1 = offset[i], len0 = 0; s < len1 - len0; s++) {\n                      strip.push(connectivity[s])\n\n                      if (i > 0) len0 = offset[i - 1]\n                    }\n\n                    for (var j = 0, len1 = offset[i], len0 = 0; j < len1 - len0 - 2; j++) {\n                      if (j % 2) {\n                        indices[indicesIndex++] = strip[j]\n                        indices[indicesIndex++] = strip[j + 2]\n                        indices[indicesIndex++] = strip[j + 1]\n                      } else {\n                        indices[indicesIndex++] = strip[j]\n                        indices[indicesIndex++] = strip[j + 1]\n                        indices[indicesIndex++] = strip[j + 2]\n                      }\n\n                      if (i > 0) len0 = offset[i - 1]\n                    }\n                  }\n                }\n\n                break\n\n              // if it is polys\n              case 'Polys':\n                var numberOfPolys = parseInt(piece.attributes.NumberOfPolys)\n\n                if (numberOfPolys > 0) {\n                  var connectivity = new Int32Array(section.DataArray[0].text.length)\n                  var offset = new Int32Array(section.DataArray[1].text.length)\n                  connectivity.set(section.DataArray[0].text, 0)\n                  offset.set(section.DataArray[1].text, 0)\n\n                  var size = numberOfPolys + connectivity.length\n                  indices = new Uint32Array(3 * size - 9 * numberOfPolys)\n                  var indicesIndex = 0,\n                    connectivityIndex = 0\n                  var i = 0,\n                    len = numberOfPolys,\n                    len0 = 0\n\n                  while (i < len) {\n                    var poly = []\n                    var s = 0,\n                      len1 = offset[i]\n\n                    while (s < len1 - len0) {\n                      poly.push(connectivity[connectivityIndex++])\n                      s++\n                    }\n\n                    var j = 1\n\n                    while (j < len1 - len0 - 1) {\n                      indices[indicesIndex++] = poly[0]\n                      indices[indicesIndex++] = poly[j]\n                      indices[indicesIndex++] = poly[j + 1]\n                      j++\n                    }\n\n                    i++\n                    len0 = offset[i - 1]\n                  }\n                }\n\n                break\n\n              default:\n                break\n            }\n          }\n\n          sectionIndex++\n        }\n\n        var geometry = new BufferGeometry()\n        geometry.setIndex(new BufferAttribute(indices, 1))\n        geometry.setAttribute('position', new BufferAttribute(points, 3))\n\n        if (normals.length === points.length) {\n          geometry.setAttribute('normal', new BufferAttribute(normals, 3))\n        }\n\n        return geometry\n      } else {\n        throw new Error('Unsupported DATASET type')\n      }\n    }\n\n    // get the 5 first lines of the files to check if there is the key word binary\n    var meta = decodeText(new Uint8Array(data, 0, 250)).split('\\n')\n\n    if (meta[0].indexOf('xml') !== -1) {\n      return parseXML(decodeText(data))\n    } else if (meta[2].includes('ASCII')) {\n      return parseASCII(decodeText(data))\n    } else {\n      return parseBinary(data)\n    }\n  }\n}\n\nexport { VTKLoader }\n"], "mappings": ";;;AAIA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMN,OAAO;IAC3CO,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;IAC/CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMN,OAAA,CAAQqB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMK,IAAA,EAAM;IACV,SAASC,WAAWC,KAAA,EAAM;MAExB,IAAIC,OAAA,GAAU,EAAE;MAGhB,IAAIC,SAAA,GAAY,EAAE;MAGlB,IAAIC,MAAA,GAAS,EAAE;MAGf,IAAIC,OAAA,GAAU,EAAE;MAEhB,IAAIC,MAAA;MAGJ,IAAIC,OAAA,GAAU;MAGd,IAAIC,UAAA,GAAa;MAIjB,IAAIC,eAAA,GAAkB;MAGtB,IAAIC,SAAA,GAAY;MAGhB,IAAIC,WAAA,GAAc;MAGlB,IAAIC,kBAAA,GAAqB;MAGzB,IAAIC,aAAA,GAAgB;MAGpB,IAAIC,YAAA,GAAe;MAGnB,IAAIC,gBAAA,GAAmB;MAGvB,IAAIC,UAAA,GAAa;MAEjB,IAAIC,eAAA,GAAkB;MACtB,IAAIC,iBAAA,GAAoB;MACxB,IAAIC,sBAAA,GAAyB;MAC7B,IAAIC,kBAAA,GAAqB;MACzB,IAAIC,iBAAA,GAAoB;MACxB,IAAIC,cAAA,GAAiB;MACrB,IAAIC,gBAAA,GAAmB;MAEvB,IAAIC,KAAA,GAAQvB,KAAA,CAAKwB,KAAA,CAAM,IAAI;MAE3B,SAASC,CAAA,IAAKF,KAAA,EAAO;QACnB,IAAIG,IAAA,GAAOH,KAAA,CAAME,CAAC,EAAEE,IAAA,CAAM;QAE1B,IAAID,IAAA,CAAKE,OAAA,CAAQ,SAAS,MAAM,GAAG;UACjC,IAAIC,OAAA,GAAUH,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC;UAE/B,IAAIK,OAAA,KAAY,YAAY,MAAM,IAAIC,KAAA,CAAM,+BAA+BD,OAAO;QACnF,WAAUb,eAAA,EAAiB;UAE1B,QAAQX,MAAA,GAASE,UAAA,CAAWwB,IAAA,CAAKL,IAAI,OAAO,MAAM;YAChD,IAAIpB,OAAA,CAAQyB,IAAA,CAAKL,IAAI,MAAM,MAAM;YAEjC,IAAIM,CAAA,GAAIC,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;YAC5B,IAAI6B,CAAA,GAAID,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;YAC5B,IAAI8B,CAAA,GAAIF,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;YAC5BH,SAAA,CAAUkC,IAAA,CAAKJ,CAAA,EAAGE,CAAA,EAAGC,CAAC;UACvB;QACF,WAAUlB,iBAAA,EAAmB;UAC5B,KAAKZ,MAAA,GAASG,eAAA,CAAgBuB,IAAA,CAAKL,IAAI,OAAO,MAAM;YAElD,IAAIW,WAAA,GAAcC,QAAA,CAASjC,MAAA,CAAO,CAAC,CAAC;YACpC,IAAIkC,IAAA,GAAOlC,MAAA,CAAO,CAAC,EAAEmB,KAAA,CAAM,KAAK;YAEhC,IAAIa,WAAA,IAAe,GAAG;cACpB,IAAIG,EAAA,GAAKF,QAAA,CAASC,IAAA,CAAK,CAAC,CAAC;cACzB,IAAIE,EAAA,EAAIC,EAAA;cACR,IAAIC,CAAA,GAAI;cAER,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIP,WAAA,GAAc,GAAG,EAAEO,CAAA,EAAG;gBACxCH,EAAA,GAAKH,QAAA,CAASC,IAAA,CAAKI,CAAC,CAAC;gBACrBD,EAAA,GAAKJ,QAAA,CAASC,IAAA,CAAKI,CAAA,GAAI,CAAC,CAAC;gBACzB1C,OAAA,CAAQmC,IAAA,CAAKI,EAAA,EAAIC,EAAA,EAAIC,EAAE;gBACvBC,CAAA;cACD;YACF;UACF;QACF,WAAUzB,sBAAA,EAAwB;UACjC,KAAKb,MAAA,GAASG,eAAA,CAAgBuB,IAAA,CAAKL,IAAI,OAAO,MAAM;YAElD,IAAIW,WAAA,GAAcC,QAAA,CAASjC,MAAA,CAAO,CAAC,CAAC;YACpC,IAAIkC,IAAA,GAAOlC,MAAA,CAAO,CAAC,EAAEmB,KAAA,CAAM,KAAK;YAEhC,IAAIa,WAAA,IAAe,GAAG;cACpB,IAAIG,EAAA,EAAIC,EAAA,EAAIC,EAAA;cAEZ,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAIP,WAAA,GAAc,GAAGO,CAAA,IAAK;gBACxC,IAAIA,CAAA,GAAI,MAAM,GAAG;kBACfJ,EAAA,GAAKF,QAAA,CAASC,IAAA,CAAKK,CAAC,CAAC;kBACrBH,EAAA,GAAKH,QAAA,CAASC,IAAA,CAAKK,CAAA,GAAI,CAAC,CAAC;kBACzBF,EAAA,GAAKJ,QAAA,CAASC,IAAA,CAAKK,CAAA,GAAI,CAAC,CAAC;kBACzB3C,OAAA,CAAQmC,IAAA,CAAKI,EAAA,EAAIC,EAAA,EAAIC,EAAE;gBACzC,OAAuB;kBACLF,EAAA,GAAKF,QAAA,CAASC,IAAA,CAAKK,CAAC,CAAC;kBACrBH,EAAA,GAAKH,QAAA,CAASC,IAAA,CAAKK,CAAA,GAAI,CAAC,CAAC;kBACzBF,EAAA,GAAKJ,QAAA,CAASC,IAAA,CAAKK,CAAA,GAAI,CAAC,CAAC;kBACzB3C,OAAA,CAAQmC,IAAA,CAAKI,EAAA,EAAIC,EAAA,EAAIC,EAAE;gBACxB;cACF;YACF;UACF;QACX,WAAmBvB,kBAAA,IAAsBC,iBAAA,EAAmB;UAClD,IAAIC,cAAA,EAAgB;YAGlB,QAAQhB,MAAA,GAASE,UAAA,CAAWwB,IAAA,CAAKL,IAAI,OAAO,MAAM;cAChD,IAAIpB,OAAA,CAAQyB,IAAA,CAAKL,IAAI,MAAM,MAAM;cAEjC,IAAImB,CAAA,GAAIZ,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;cAC5B,IAAIyC,CAAA,GAAIb,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;cAC5B,IAAI0C,CAAA,GAAId,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;cAC5BF,MAAA,CAAOiC,IAAA,CAAKS,CAAA,EAAGC,CAAA,EAAGC,CAAC;YACpB;UACF,WAAUzB,gBAAA,EAAkB;YAG3B,QAAQjB,MAAA,GAASE,UAAA,CAAWwB,IAAA,CAAKL,IAAI,OAAO,MAAM;cAChD,IAAIpB,OAAA,CAAQyB,IAAA,CAAKL,IAAI,MAAM,MAAM;cAEjC,IAAIsB,EAAA,GAAKf,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;cAC7B,IAAI4C,EAAA,GAAKhB,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;cAC7B,IAAI6C,EAAA,GAAKjB,UAAA,CAAW5B,MAAA,CAAO,CAAC,CAAC;cAC7BD,OAAA,CAAQgC,IAAA,CAAKY,EAAA,EAAIC,EAAA,EAAIC,EAAE;YACxB;UACF;QACF;QAED,IAAIxC,WAAA,CAAYqB,IAAA,CAAKL,IAAI,MAAM,MAAM;UACnCT,iBAAA,GAAoB;UACpBD,eAAA,GAAkB;UAClBE,sBAAA,GAAyB;QAC1B,WAAUT,SAAA,CAAUsB,IAAA,CAAKL,IAAI,MAAM,MAAM;UACxCT,iBAAA,GAAoB;UACpBD,eAAA,GAAkB;UAClBE,sBAAA,GAAyB;QAC1B,WAAUP,kBAAA,CAAmBoB,IAAA,CAAKL,IAAI,MAAM,MAAM;UACjDT,iBAAA,GAAoB;UACpBD,eAAA,GAAkB;UAClBE,sBAAA,GAAyB;QAC1B,WAAUN,aAAA,CAAcmB,IAAA,CAAKL,IAAI,MAAM,MAAM;UAC5CP,kBAAA,GAAqB;UACrBH,eAAA,GAAkB;UAClBC,iBAAA,GAAoB;UACpBC,sBAAA,GAAyB;QAC1B,WAAUL,YAAA,CAAakB,IAAA,CAAKL,IAAI,MAAM,MAAM;UAC3CN,iBAAA,GAAoB;UACpBJ,eAAA,GAAkB;UAClBC,iBAAA,GAAoB;UACpBC,sBAAA,GAAyB;QAC1B,WAAUJ,gBAAA,CAAiBiB,IAAA,CAAKL,IAAI,MAAM,MAAM;UAC/CL,cAAA,GAAiB;UACjBC,gBAAA,GAAmB;UACnBN,eAAA,GAAkB;UAClBC,iBAAA,GAAoB;UACpBC,sBAAA,GAAyB;QAC1B,WAAUH,UAAA,CAAWgB,IAAA,CAAKL,IAAI,MAAM,MAAM;UACzCJ,gBAAA,GAAmB;UACnBD,cAAA,GAAiB;UACjBL,eAAA,GAAkB;UAClBC,iBAAA,GAAoB;UACpBC,sBAAA,GAAyB;QAC1B;MACF;MAED,IAAIiC,QAAA,GAAW,IAAIC,cAAA,CAAgB;MACnCD,QAAA,CAASE,QAAA,CAASpD,OAAO;MACzBkD,QAAA,CAASG,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBrD,SAAA,EAAW,CAAC,CAAC;MAE1E,IAAIE,OAAA,CAAQoD,MAAA,KAAWtD,SAAA,CAAUsD,MAAA,EAAQ;QACvCL,QAAA,CAASG,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBnD,OAAA,EAAS,CAAC,CAAC;MACvE;MAED,IAAID,MAAA,CAAOqD,MAAA,KAAWvD,OAAA,CAAQuD,MAAA,EAAQ;QAGpC,IAAIrD,MAAA,CAAOqD,MAAA,KAAWtD,SAAA,CAAUsD,MAAA,EAAQ;UACtCL,QAAA,CAASG,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBpD,MAAA,EAAQ,CAAC,CAAC;QACrE;MACT,OAAa;QAGLgD,QAAA,GAAWA,QAAA,CAASM,YAAA,CAAc;QAClC,IAAIC,YAAA,GAAeP,QAAA,CAASQ,UAAA,CAAWC,QAAA,CAASC,KAAA,GAAQ;QAExD,IAAI1D,MAAA,CAAOqD,MAAA,KAAWE,YAAA,GAAe,GAAG;UACtC,IAAII,SAAA,GAAY,EAAE;UAElB,SAASrC,CAAA,GAAI,GAAGA,CAAA,GAAIiC,YAAA,EAAcjC,CAAA,IAAK;YACrC,IAAIoB,CAAA,GAAI1C,MAAA,CAAO,IAAIsB,CAAA,GAAI,CAAC;YACxB,IAAIqB,CAAA,GAAI3C,MAAA,CAAO,IAAIsB,CAAA,GAAI,CAAC;YACxB,IAAIsB,CAAA,GAAI5C,MAAA,CAAO,IAAIsB,CAAA,GAAI,CAAC;YAExBqC,SAAA,CAAU1B,IAAA,CAAKS,CAAA,EAAGC,CAAA,EAAGC,CAAC;YACtBe,SAAA,CAAU1B,IAAA,CAAKS,CAAA,EAAGC,CAAA,EAAGC,CAAC;YACtBe,SAAA,CAAU1B,IAAA,CAAKS,CAAA,EAAGC,CAAA,EAAGC,CAAC;UACvB;UAEDI,QAAA,CAASG,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuBO,SAAA,EAAW,CAAC,CAAC;QACxE;MACF;MAED,OAAOX,QAAA;IACR;IAED,SAASY,YAAY/D,KAAA,EAAM;MACzB,IAAI6D,KAAA,EAAOG,UAAA,EAAYvC,CAAA,EAAGwC,cAAA,EAAgBC,CAAA;MAC1C,IAAIC,MAAA,GAAS,IAAIC,UAAA,CAAWpE,KAAI;MAChC,IAAIqE,QAAA,GAAW,IAAIC,QAAA,CAAStE,KAAI;MAGhC,IAAIuE,MAAA,GAAS,EAAE;MACf,IAAInE,OAAA,GAAU,EAAE;MAChB,IAAIH,OAAA,GAAU,EAAE;MAIhB,IAAIuE,KAAA,GAAQ;MAEZ,SAASC,WAAWC,OAAA,EAAQC,KAAA,EAAO;QACjC,IAAIC,MAAA,GAAQD,KAAA;QACZ,IAAIE,CAAA,GAAIH,OAAA,CAAOE,MAAK;QACpB,IAAIE,EAAA,GAAI,EAAE;QACV,OAAOD,CAAA,KAAM,IAAI;UACfC,EAAA,CAAE1C,IAAA,CAAK2C,MAAA,CAAOC,YAAA,CAAaH,CAAC,CAAC;UAC7BD,MAAA;UACAC,CAAA,GAAIH,OAAA,CAAOE,MAAK;QACjB;QAED,OAAO;UAAED,KAAA;UAAcM,GAAA,EAAKL,MAAA;UAAOM,IAAA,EAAMN,MAAA,GAAQ;UAAGO,YAAA,EAAcL,EAAA,CAAEM,IAAA,CAAK,EAAE;QAAG;MAC/E;MAED,IAAIC,KAAA,EAAO3D,IAAA;MAEX,OAAO,MAAM;QAEX2D,KAAA,GAAQZ,UAAA,CAAWN,MAAA,EAAQK,KAAK;QAChC9C,IAAA,GAAO2D,KAAA,CAAMF,YAAA;QAEb,IAAIzD,IAAA,CAAKE,OAAA,CAAQ,SAAS,MAAM,GAAG;UACjC,IAAIC,OAAA,GAAUH,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC;UAE/B,IAAIK,OAAA,KAAY,YAAY,MAAM,IAAIC,KAAA,CAAM,+BAA+BD,OAAO;QACnF,WAAUH,IAAA,CAAKE,OAAA,CAAQ,QAAQ,MAAM,GAAG;UAGvCqC,cAAA,GAAiB3B,QAAA,CAASZ,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC,GAAG,EAAE;UAGhDqC,KAAA,GAAQI,cAAA,GAAiB,IAAI;UAE7BM,MAAA,GAAS,IAAIe,YAAA,CAAarB,cAAA,GAAiB,CAAC;UAE5CD,UAAA,GAAaqB,KAAA,CAAMH,IAAA;UACnB,KAAKzD,CAAA,GAAI,GAAGA,CAAA,GAAIwC,cAAA,EAAgBxC,CAAA,IAAK;YACnC8C,MAAA,CAAO,IAAI9C,CAAC,IAAI4C,QAAA,CAASkB,UAAA,CAAWvB,UAAA,EAAY,KAAK;YACrDO,MAAA,CAAO,IAAI9C,CAAA,GAAI,CAAC,IAAI4C,QAAA,CAASkB,UAAA,CAAWvB,UAAA,GAAa,GAAG,KAAK;YAC7DO,MAAA,CAAO,IAAI9C,CAAA,GAAI,CAAC,IAAI4C,QAAA,CAASkB,UAAA,CAAWvB,UAAA,GAAa,GAAG,KAAK;YAC7DA,UAAA,GAAaA,UAAA,GAAa;UAC3B;UAGDqB,KAAA,CAAMH,IAAA,GAAOG,KAAA,CAAMH,IAAA,GAAOrB,KAAA,GAAQ;QACnC,WAAUnC,IAAA,CAAKE,OAAA,CAAQ,iBAAiB,MAAM,GAAG;UAChD,IAAI4D,cAAA,GAAiBlD,QAAA,CAASZ,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC,GAAG,EAAE;UACpD,IAAIiE,IAAA,GAAOnD,QAAA,CAASZ,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC,GAAG,EAAE;UAE1CqC,KAAA,GAAQ4B,IAAA,GAAO;UAEfxF,OAAA,GAAU,IAAIyF,WAAA,CAAY,IAAID,IAAA,GAAO,IAAID,cAAc;UACvD,IAAIG,YAAA,GAAe;UAEnB3B,UAAA,GAAaqB,KAAA,CAAMH,IAAA;UACnB,KAAKzD,CAAA,GAAI,GAAGA,CAAA,GAAI+D,cAAA,EAAgB/D,CAAA,IAAK;YAEnC,IAAImE,UAAA,GAAavB,QAAA,CAASwB,QAAA,CAAS7B,UAAA,EAAY,KAAK;YACpD,IAAI8B,KAAA,GAAQ,EAAE;YACd9B,UAAA,IAAc;YACd,KAAKE,CAAA,GAAI,GAAGA,CAAA,GAAI0B,UAAA,EAAY1B,CAAA,IAAK;cAC/B4B,KAAA,CAAM1D,IAAA,CAAKiC,QAAA,CAASwB,QAAA,CAAS7B,UAAA,EAAY,KAAK,CAAC;cAC/CA,UAAA,IAAc;YACf;YAGD,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAIgD,UAAA,GAAa,GAAGhD,CAAA,IAAK;cACvC,IAAIA,CAAA,GAAI,GAAG;gBACT3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAC;gBACjC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;gBACrC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;cACrD,OAAqB;gBACL3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAC;gBACjC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;gBACrC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;cACtC;YACF;UACF;UAGDyC,KAAA,CAAMH,IAAA,GAAOG,KAAA,CAAMH,IAAA,GAAOrB,KAAA,GAAQ;QACnC,WAAUnC,IAAA,CAAKE,OAAA,CAAQ,UAAU,MAAM,GAAG;UACzC,IAAI4D,cAAA,GAAiBlD,QAAA,CAASZ,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC,GAAG,EAAE;UACpD,IAAIiE,IAAA,GAAOnD,QAAA,CAASZ,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC,GAAG,EAAE;UAE1CqC,KAAA,GAAQ4B,IAAA,GAAO;UAEfxF,OAAA,GAAU,IAAIyF,WAAA,CAAY,IAAID,IAAA,GAAO,IAAID,cAAc;UACvD,IAAIG,YAAA,GAAe;UAEnB3B,UAAA,GAAaqB,KAAA,CAAMH,IAAA;UACnB,KAAKzD,CAAA,GAAI,GAAGA,CAAA,GAAI+D,cAAA,EAAgB/D,CAAA,IAAK;YAEnC,IAAImE,UAAA,GAAavB,QAAA,CAASwB,QAAA,CAAS7B,UAAA,EAAY,KAAK;YACpD,IAAI8B,KAAA,GAAQ,EAAE;YACd9B,UAAA,IAAc;YACd,KAAKE,CAAA,GAAI,GAAGA,CAAA,GAAI0B,UAAA,EAAY1B,CAAA,IAAK;cAC/B4B,KAAA,CAAM1D,IAAA,CAAKiC,QAAA,CAASwB,QAAA,CAAS7B,UAAA,EAAY,KAAK,CAAC;cAC/CA,UAAA,IAAc;YACf;YAGD,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAIgD,UAAA,GAAa,GAAGhD,CAAA,IAAK;cACvC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAM,CAAC;cACjC7F,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAC;cACjC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;YACtC;UACF;UAGDyC,KAAA,CAAMH,IAAA,GAAOG,KAAA,CAAMH,IAAA,GAAOrB,KAAA,GAAQ;QACnC,WAAUnC,IAAA,CAAKE,OAAA,CAAQ,YAAY,MAAM,GAAG;UAC3CqC,cAAA,GAAiB3B,QAAA,CAASZ,IAAA,CAAKF,KAAA,CAAM,GAAG,EAAE,CAAC,GAAG,EAAE;UAGhD6D,KAAA,GAAQZ,UAAA,CAAWN,MAAA,EAAQkB,KAAA,CAAMH,IAAI;UAGrCrB,KAAA,GAAQI,cAAA,GAAiB,IAAI;UAE7B7D,OAAA,GAAU,IAAIkF,YAAA,CAAarB,cAAA,GAAiB,CAAC;UAC7CD,UAAA,GAAaqB,KAAA,CAAMH,IAAA;UACnB,KAAKzD,CAAA,GAAI,GAAGA,CAAA,GAAIwC,cAAA,EAAgBxC,CAAA,IAAK;YACnCrB,OAAA,CAAQ,IAAIqB,CAAC,IAAI4C,QAAA,CAASkB,UAAA,CAAWvB,UAAA,EAAY,KAAK;YACtD5D,OAAA,CAAQ,IAAIqB,CAAA,GAAI,CAAC,IAAI4C,QAAA,CAASkB,UAAA,CAAWvB,UAAA,GAAa,GAAG,KAAK;YAC9D5D,OAAA,CAAQ,IAAIqB,CAAA,GAAI,CAAC,IAAI4C,QAAA,CAASkB,UAAA,CAAWvB,UAAA,GAAa,GAAG,KAAK;YAC9DA,UAAA,IAAc;UACf;UAGDqB,KAAA,CAAMH,IAAA,GAAOG,KAAA,CAAMH,IAAA,GAAOrB,KAAA;QAC3B;QAGDW,KAAA,GAAQa,KAAA,CAAMH,IAAA;QAEd,IAAIV,KAAA,IAASL,MAAA,CAAO4B,UAAA,EAAY;UAC9B;QACD;MACF;MAED,IAAI5C,QAAA,GAAW,IAAIC,cAAA,CAAgB;MACnCD,QAAA,CAASE,QAAA,CAAS,IAAI2C,eAAA,CAAgB/F,OAAA,EAAS,CAAC,CAAC;MACjDkD,QAAA,CAASG,YAAA,CAAa,YAAY,IAAI0C,eAAA,CAAgBzB,MAAA,EAAQ,CAAC,CAAC;MAEhE,IAAInE,OAAA,CAAQoD,MAAA,KAAWe,MAAA,CAAOf,MAAA,EAAQ;QACpCL,QAAA,CAASG,YAAA,CAAa,UAAU,IAAI0C,eAAA,CAAgB5F,OAAA,EAAS,CAAC,CAAC;MAChE;MAED,OAAO+C,QAAA;IACR;IAED,SAAS8C,cAAcC,KAAA,EAAOC,MAAA,EAAQ;MACpC,MAAMC,WAAA,GAAcF,KAAA,CAAM1C,MAAA;QACxBnD,MAAA,GAAS,IAAIiF,YAAA,CAAac,WAAA,GAAcD,MAAA,CAAO3C,MAAM;MAEvDnD,MAAA,CAAOgG,GAAA,CAAIH,KAAK;MAChB7F,MAAA,CAAOgG,GAAA,CAAIF,MAAA,EAAQC,WAAW;MAE9B,OAAO/F,MAAA;IACR;IAED,SAASiG,YAAYJ,KAAA,EAAOC,MAAA,EAAQ;MAClC,IAAIC,WAAA,GAAcF,KAAA,CAAM1C,MAAA;QACtBnD,MAAA,GAAS,IAAIkG,UAAA,CAAWH,WAAA,GAAcD,MAAA,CAAO3C,MAAM;MAErDnD,MAAA,CAAOgG,GAAA,CAAIH,KAAK;MAChB7F,MAAA,CAAOgG,GAAA,CAAIF,MAAA,EAAQC,WAAW;MAE9B,OAAO/F,MAAA;IACR;IAED,SAASmG,SAASC,UAAA,EAAY;MAG5B,SAASC,UAAUC,GAAA,EAAK;QAEtB,IAAIC,GAAA,GAAM,CAAE;QAEZ,IAAID,GAAA,CAAIE,QAAA,KAAa,GAAG;UAKtB,IAAIF,GAAA,CAAIhD,UAAA,EAAY;YAClB,IAAIgD,GAAA,CAAIhD,UAAA,CAAWH,MAAA,GAAS,GAAG;cAC7BoD,GAAA,CAAI,YAAY,IAAI,CAAE;cAEtB,SAASE,EAAA,GAAI,GAAGA,EAAA,GAAIH,GAAA,CAAIhD,UAAA,CAAWH,MAAA,EAAQsD,EAAA,IAAK;gBAC9C,IAAIC,SAAA,GAAYJ,GAAA,CAAIhD,UAAA,CAAWqD,IAAA,CAAKF,EAAC;gBACrCF,GAAA,CAAI,YAAY,EAAEG,SAAA,CAAUE,QAAQ,IAAIF,SAAA,CAAUG,SAAA,CAAUvF,IAAA,CAAM;cACnE;YACF;UACF;QACX,WAAmBgF,GAAA,CAAIE,QAAA,KAAa,GAAG;UAG7BD,GAAA,GAAMD,GAAA,CAAIO,SAAA,CAAUvF,IAAA,CAAM;QAC3B;QAGD,IAAIgF,GAAA,CAAIQ,aAAA,IAAiB;UACvB,SAASzE,EAAA,GAAI,GAAGA,EAAA,GAAIiE,GAAA,CAAIS,UAAA,CAAW5D,MAAA,EAAQd,EAAA,IAAK;YAC9C,IAAIsE,IAAA,GAAOL,GAAA,CAAIS,UAAA,CAAWJ,IAAA,CAAKtE,EAAC;YAChC,IAAIuE,QAAA,GAAWD,IAAA,CAAKC,QAAA;YAEpB,IAAI,OAAOL,GAAA,CAAIK,QAAQ,MAAM,aAAa;cACxC,IAAII,GAAA,GAAMX,SAAA,CAAUM,IAAI;cAExB,IAAIK,GAAA,KAAQ,IAAIT,GAAA,CAAIK,QAAQ,IAAII,GAAA;YAC9C,OAAmB;cACL,IAAI,OAAOT,GAAA,CAAIK,QAAQ,EAAE7E,IAAA,KAAS,aAAa;gBAC7C,IAAIkF,GAAA,GAAMV,GAAA,CAAIK,QAAQ;gBACtBL,GAAA,CAAIK,QAAQ,IAAI,CAACK,GAAG;cACrB;cAED,IAAID,GAAA,GAAMX,SAAA,CAAUM,IAAI;cAExB,IAAIK,GAAA,KAAQ,IAAIT,GAAA,CAAIK,QAAQ,EAAE7E,IAAA,CAAKiF,GAAG;YACvC;UACF;QACF;QAED,OAAOT,GAAA;MACR;MAGD,SAASW,kBAAkBC,GAAA,EAAK;QAC9B,IAAIC,GAAA,GAAM,OAAOrD,UAAA,KAAe,cAAcA,UAAA,GAAasD,KAAA;QAC3D,IAAIhF,EAAA;QAEJ,IAAIiF,SAAA,GAAY,EAAE;QAClB,IAAIC,IAAA,GAAO;QACX,IAAIC,IAAA,GAAMD,IAAA,CAAKpE,MAAA;QAEf,KAAKd,EAAA,GAAI,GAAGA,EAAA,GAAImF,IAAA,EAAKnF,EAAA,IAAK,CAEzB;QAED,KAAKA,EAAA,GAAI,GAAGA,EAAA,GAAImF,IAAA,EAAK,EAAEnF,EAAA,EAAG;UACxBiF,SAAA,CAAUC,IAAA,CAAKE,UAAA,CAAWpF,EAAC,CAAC,IAAIA,EAAA;QACjC;QAEDiF,SAAA,CAAU,IAAIG,UAAA,CAAW,CAAC,CAAC,IAAI;QAC/BH,SAAA,CAAU,IAAIG,UAAA,CAAW,CAAC,CAAC,IAAI;QAE/B,IAAIhB,EAAA,EAAGiB,CAAA,EAAGV,GAAA,EAAKW,YAAA,EAAcC,IAAA;QAC7B,IAAIJ,IAAA,GAAML,GAAA,CAAIhE,MAAA;QAEd,IAAIqE,IAAA,GAAM,IAAI,GAAG;UACf,MAAM,IAAI/F,KAAA,CAAM,gDAAgD;QACjE;QAEDkG,YAAA,GAAeR,GAAA,CAAIK,IAAA,GAAM,CAAC,MAAM,MAAM,IAAIL,GAAA,CAAIK,IAAA,GAAM,CAAC,MAAM,MAAM,IAAI;QACrEI,IAAA,GAAM,IAAIR,GAAA,CAAKI,IAAA,GAAM,IAAK,IAAIG,YAAY;QAC1CD,CAAA,GAAIC,YAAA,GAAe,IAAIH,IAAA,GAAM,IAAIA,IAAA;QAEjC,IAAIK,CAAA,GAAI;QAER,KAAKxF,EAAA,GAAI,GAAGoE,EAAA,GAAI,GAAGpE,EAAA,GAAIqF,CAAA,EAAGrF,EAAA,IAAK,GAAGoE,EAAA,IAAK,GAAG;UACxCO,GAAA,GACGM,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAC,CAAC,KAAK,KAChCiF,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAA,GAAI,CAAC,CAAC,KAAK,KACpCiF,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAA,GAAI,CAAC,CAAC,KAAK,IACrCiF,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAA,GAAI,CAAC,CAAC;UACjCuF,IAAA,CAAIC,CAAA,EAAG,KAAKb,GAAA,GAAM,aAAa;UAC/BY,IAAA,CAAIC,CAAA,EAAG,KAAKb,GAAA,GAAM,UAAW;UAC7BY,IAAA,CAAIC,CAAA,EAAG,IAAIb,GAAA,GAAM;QAClB;QAED,IAAIW,YAAA,KAAiB,GAAG;UACtBX,GAAA,GAAOM,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAC,CAAC,KAAK,IAAMiF,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAA,GAAI,CAAC,CAAC,KAAK;UACjFuF,IAAA,CAAIC,CAAA,EAAG,IAAIb,GAAA,GAAM;QAC3B,WAAmBW,YAAA,KAAiB,GAAG;UAC7BX,GAAA,GACGM,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAC,CAAC,KAAK,KAChCiF,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAA,GAAI,CAAC,CAAC,KAAK,IACpCiF,SAAA,CAAUH,GAAA,CAAIM,UAAA,CAAWpF,EAAA,GAAI,CAAC,CAAC,KAAK;UACvCuF,IAAA,CAAIC,CAAA,EAAG,IAAKb,GAAA,IAAO,IAAK;UACxBY,IAAA,CAAIC,CAAA,EAAG,IAAIb,GAAA,GAAM;QAClB;QAED,OAAOY,IAAA;MACR;MAED,SAASE,eAAeC,GAAA,EAAKC,WAAA,EAAY;QACvC,IAAIC,QAAA,GAAW;QAEf,IAAIC,IAAA,CAAK5E,UAAA,CAAW6E,WAAA,KAAgB,UAAU;UAC5CF,QAAA,GAAW;QACZ,WAAUC,IAAA,CAAK5E,UAAA,CAAW6E,WAAA,KAAgB,UAAU;UACnDF,QAAA,GAAW;QACZ;QAGD,IAAIF,GAAA,CAAIzE,UAAA,CAAW8E,MAAA,KAAW,YAAYJ,WAAA,EAAY;UACpD,IAAIK,OAAA,EAASC,OAAA,EAASC,QAAA,EAAUC,MAAA,EAAQC,UAAA,EAAYC,UAAA,EAAYC,OAAA,EAASC,WAAA,EAAaC,aAAA;UAEtF,IAAId,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,WAAW;YACrC,IAAIC,GAAA,GAAM,IAAI9D,YAAA,CAAc;UAC7B,WAAU8C,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,SAAS;YAC1C,IAAIC,GAAA,GAAM,IAAI7C,UAAA,CAAY;UAC3B;UAcDmC,OAAA,GAAUN,GAAA,CAAI,OAAO;UAErBQ,QAAA,GAAWrB,iBAAA,CAAkBmB,OAAO;UAEpCG,MAAA,GAASD,QAAA,CAAS,CAAC;UACnB,SAASlG,EAAA,GAAI,GAAGA,EAAA,GAAI4F,QAAA,GAAW,GAAG5F,EAAA,IAAK;YACrCmG,MAAA,GAASA,MAAA,GAAUD,QAAA,CAASlG,EAAC,KAAMA,EAAA,GAAI4F,QAAA;UACxC;UAEDS,UAAA,IAAcF,MAAA,GAAS,KAAKP,QAAA;UAC5BU,OAAA,GAAUD,UAAA,GAAa,IAAI,IAAI,IAAKA,UAAA,GAAa,IAAK;UACtDA,UAAA,GAAaA,UAAA,GAAaC,OAAA;UAE1BC,WAAA,GAAc,EAAE;UAChBC,aAAA,GAAgBH,UAAA;UAChBE,WAAA,CAAY7G,IAAA,CAAK8G,aAAa;UAI9BJ,UAAA,GAAa,IAAIR,QAAA;UAEjB,SAAS5F,EAAA,GAAI,GAAGA,EAAA,GAAImG,MAAA,EAAQnG,EAAA,IAAK;YAC/B,IAAI2G,gBAAA,GAAmBT,QAAA,CAASlG,EAAA,GAAI4F,QAAA,GAAWQ,UAAU;YAEzD,SAAShC,EAAA,GAAI,GAAGA,EAAA,GAAIwB,QAAA,GAAW,GAAGxB,EAAA,IAAK;cAErCuC,gBAAA,GAAmBA,gBAAA,GAAoBT,QAAA,CAASlG,EAAA,GAAI4F,QAAA,GAAWQ,UAAA,GAAahC,EAAC,KAAMA,EAAA,GAAI;YACxF;YAEDoC,aAAA,GAAgBA,aAAA,GAAgBG,gBAAA;YAChCJ,WAAA,CAAY7G,IAAA,CAAK8G,aAAa;UAC/B;UAED,SAASxG,EAAA,GAAI,GAAGA,EAAA,GAAIuG,WAAA,CAAYzF,MAAA,GAAS,GAAGd,EAAA,IAAK;YAC/C,IAAI1C,KAAA,GAAOsJ,UAAA,CAAWV,QAAA,CAASW,KAAA,CAAMN,WAAA,CAAYvG,EAAC,GAAGuG,WAAA,CAAYvG,EAAA,GAAI,CAAC,CAAC,CAAC;YACxEiG,OAAA,GAAU3I,KAAA,CAAKmE,MAAA;YAEf,IAAIiE,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,WAAW;cACrCR,OAAA,GAAU,IAAIrD,YAAA,CAAaqD,OAAO;cAClCS,GAAA,GAAMnD,aAAA,CAAcmD,GAAA,EAAKT,OAAO;YACjC,WAAUP,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,SAAS;cAC1CR,OAAA,GAAU,IAAIpC,UAAA,CAAWoC,OAAO;cAChCS,GAAA,GAAM9C,WAAA,CAAY8C,GAAA,EAAKT,OAAO;YAC/B;UACF;UAED,OAAOP,GAAA,CAAI,OAAO;UAElB,IAAIA,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,SAAS;YACnC,IAAIf,GAAA,CAAIzE,UAAA,CAAW8E,MAAA,KAAW,UAAU;cACtCW,GAAA,GAAMA,GAAA,CAAII,MAAA,CAAO,UAAUC,EAAA,EAAIC,GAAA,EAAK;gBAClC,IAAIA,GAAA,GAAM,MAAM,GAAG,OAAO;cAC1C,CAAe;YACF;UACF;QACX,OAAe;UACL,IAAItB,GAAA,CAAIzE,UAAA,CAAW8E,MAAA,KAAW,YAAY,CAACJ,WAAA,EAAY;YACrD,IAAIM,OAAA,GAAUpB,iBAAA,CAAkBa,GAAA,CAAI,OAAO,CAAC;YAK5CO,OAAA,GAAUA,OAAA,CAAQY,KAAA,CAAMjB,QAAQ,EAAEnE,MAAA;UAC9C,OAAiB;YACL,IAAIiE,GAAA,CAAI,OAAO,GAAG;cAChB,IAAIO,OAAA,GAAUP,GAAA,CAAI,OAAO,EAAE5G,KAAA,CAAM,KAAK,EAAEgI,MAAA,CAAO,UAAUC,EAAA,EAAI;gBAC3D,IAAIA,EAAA,KAAO,IAAI,OAAOA,EAAA;cACtC,CAAe;YACf,OAAmB;cACL,IAAId,OAAA,GAAU,IAAIpC,UAAA,CAAW,CAAC,EAAEpC,MAAA;YACjC;UACF;UAED,OAAOiE,GAAA,CAAI,OAAO;UAGlB,IAAIA,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,WAAW;YACrC,IAAIC,GAAA,GAAM,IAAI9D,YAAA,CAAaqD,OAAO;UACnC,WAAUP,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,SAAS;YAC1C,IAAIC,GAAA,GAAM,IAAI7C,UAAA,CAAWoC,OAAO;UACjC,WAAUP,GAAA,CAAIzE,UAAA,CAAWwF,IAAA,KAAS,SAAS;YAC1C,IAAIC,GAAA,GAAM,IAAI7C,UAAA,CAAWoC,OAAO;YAEhC,IAAIP,GAAA,CAAIzE,UAAA,CAAW8E,MAAA,KAAW,UAAU;cACtCW,GAAA,GAAMA,GAAA,CAAII,MAAA,CAAO,UAAUC,EAAA,EAAIC,GAAA,EAAK;gBAClC,IAAIA,GAAA,GAAM,MAAM,GAAG,OAAO;cAC1C,CAAe;YACF;UACF;QACF;QAED,OAAON,GAAA;MACR;MAID,IAAIO,GAAA,GAAM;MAEV,IAAIC,MAAA,CAAOC,SAAA,EAAW;QACpB,IAAI;UACFF,GAAA,GAAM,IAAIE,SAAA,CAAS,EAAGC,eAAA,CAAgBrD,UAAA,EAAY,UAAU;QAC7D,SAAQ/G,CAAA,EAAP;UACAiK,GAAA,GAAM;QACP;MACT,WAAiBC,MAAA,CAAOG,aAAA,EAAe;QAC/B,IAAI;UACFJ,GAAA,GAAM,IAAII,aAAA,CAAc,kBAAkB;UAC1CJ,GAAA,CAAIK,KAAA,GAAQ;UAEZ,IAAI,CAAEL,GAAA,CAAIM,OAAA;YAAA;UAAA,CAAO,EAAc;YAC7B,MAAM,IAAInI,KAAA,CAAM6H,GAAA,CAAIO,UAAA,CAAWC,MAAA,GAASR,GAAA,CAAIO,UAAA,CAAWE,OAAO;UAC/D;QACF,SAAQ1K,CAAA,EAAP;UACAiK,GAAA,GAAM;QACP;MACT,OAAa;QACL,MAAM,IAAI7H,KAAA,CAAM,0BAA0B;MAC3C;MAGD,IAAIuI,GAAA,GAAMV,GAAA,CAAIW,eAAA;MAEd,IAAI/B,IAAA,GAAO7B,SAAA,CAAU2D,GAAG;MACxB,IAAI9F,MAAA,GAAS,EAAE;MACf,IAAInE,OAAA,GAAU,EAAE;MAChB,IAAIH,OAAA,GAAU,EAAE;MAEhB,IAAIsI,IAAA,CAAKgC,QAAA,EAAU;QACjB,IAAIC,KAAA,GAAQjC,IAAA,CAAKgC,QAAA,CAASE,KAAA;QAC1B,IAAIC,UAAA,GAAanC,IAAA,CAAK5E,UAAA,CAAWgH,cAAA,CAAe,YAAY;QAI5D,IAAIC,QAAA,GAAW,CAAC,aAAa,UAAU,UAAU,OAAO;QACxD,IAAIC,YAAA,GAAe;UACjBC,gBAAA,GAAmBF,QAAA,CAASpH,MAAA;QAE9B,OAAOqH,YAAA,GAAeC,gBAAA,EAAkB;UACtC,IAAIC,OAAA,GAAUP,KAAA,CAAMI,QAAA,CAASC,YAAY,CAAC;UAI1C,IAAIE,OAAA,IAAWA,OAAA,CAAQC,SAAA,EAAW;YAGhC,IAAIC,MAAA,CAAOC,SAAA,CAAUC,QAAA,CAASC,IAAA,CAAKL,OAAA,CAAQC,SAAS,MAAM,kBAAkB;cAC1E,IAAIK,GAAA,GAAMN,OAAA,CAAQC,SAAA;YAChC,OAAmB;cACL,IAAIK,GAAA,GAAM,CAACN,OAAA,CAAQC,SAAS;YAC7B;YAED,IAAIM,cAAA,GAAiB;cACnBC,kBAAA,GAAqBF,GAAA,CAAI7H,MAAA;YAE3B,OAAO8H,cAAA,GAAiBC,kBAAA,EAAoB;cAE1C,IAAI,WAAWF,GAAA,CAAIC,cAAc,KAAKD,GAAA,CAAIC,cAAc,EAAE,OAAO,EAAE9H,MAAA,GAAS,GAAG;gBAC7E6H,GAAA,CAAIC,cAAc,EAAE9L,IAAA,GAAO2I,cAAA,CAAekD,GAAA,CAAIC,cAAc,GAAGZ,UAAU;cAC1E;cAEDY,cAAA;YACD;YAED,QAAQV,QAAA,CAASC,YAAY;cAE3B,KAAK;gBACH,IAAI5G,cAAA,GAAiB3B,QAAA,CAASkI,KAAA,CAAM7G,UAAA,CAAW6H,cAAc;gBAC7D,IAAIC,WAAA,GAAcV,OAAA,CAAQpH,UAAA,CAAW+H,OAAA;gBAErC,IAAIzH,cAAA,GAAiB,GAAG;kBACtB,SAASxC,CAAA,GAAI,GAAGkK,GAAA,GAAMN,GAAA,CAAI7H,MAAA,EAAQ/B,CAAA,GAAIkK,GAAA,EAAKlK,CAAA,IAAK;oBAC9C,IAAIgK,WAAA,KAAgBJ,GAAA,CAAI5J,CAAC,EAAEkC,UAAA,CAAWiI,IAAA,EAAM;sBAC1C,IAAIC,UAAA,GAAaR,GAAA,CAAI5J,CAAC,EAAEkC,UAAA,CAAWmI,kBAAA;sBACnC1L,OAAA,GAAU,IAAIkF,YAAA,CAAarB,cAAA,GAAiB4H,UAAU;sBACtDzL,OAAA,CAAQiG,GAAA,CAAIgF,GAAA,CAAI5J,CAAC,EAAEjC,IAAA,EAAM,CAAC;oBAC3B;kBACF;gBACF;gBAED;cAGF,KAAK;gBACH,IAAIyE,cAAA,GAAiB3B,QAAA,CAASkI,KAAA,CAAM7G,UAAA,CAAW6H,cAAc;gBAE7D,IAAIvH,cAAA,GAAiB,GAAG;kBACtB,IAAI4H,UAAA,GAAad,OAAA,CAAQC,SAAA,CAAUrH,UAAA,CAAWmI,kBAAA;kBAC9CvH,MAAA,GAAS,IAAIe,YAAA,CAAarB,cAAA,GAAiB4H,UAAU;kBACrDtH,MAAA,CAAO8B,GAAA,CAAI0E,OAAA,CAAQC,SAAA,CAAUxL,IAAA,EAAM,CAAC;gBACrC;gBAED;cAGF,KAAK;gBACH,IAAIgG,cAAA,GAAiBlD,QAAA,CAASkI,KAAA,CAAM7G,UAAA,CAAWoI,cAAc;gBAE7D,IAAIvG,cAAA,GAAiB,GAAG;kBACtB,IAAIwG,YAAA,GAAe,IAAIzF,UAAA,CAAWwE,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,CAAKgE,MAAM;kBAClE,IAAIyI,MAAA,GAAS,IAAI1F,UAAA,CAAWwE,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,CAAKgE,MAAM;kBAC5DwI,YAAA,CAAa3F,GAAA,CAAI0E,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,EAAM,CAAC;kBAC7CyM,MAAA,CAAO5F,GAAA,CAAI0E,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,EAAM,CAAC;kBAEvC,IAAIiG,IAAA,GAAOD,cAAA,GAAiBwG,YAAA,CAAaxI,MAAA;kBACzCvD,OAAA,GAAU,IAAIyF,WAAA,CAAY,IAAID,IAAA,GAAO,IAAID,cAAc;kBAEvD,IAAIG,YAAA,GAAe;kBAEnB,SAASlE,CAAA,GAAI,GAAGkK,GAAA,GAAMnG,cAAA,EAAgB/D,CAAA,GAAIkK,GAAA,EAAKlK,CAAA,IAAK;oBAClD,IAAIqE,KAAA,GAAQ,EAAE;oBAEd,SAAS5B,CAAA,GAAI,GAAGgI,IAAA,GAAOD,MAAA,CAAOxK,CAAC,GAAG0K,IAAA,GAAO,GAAGjI,CAAA,GAAIgI,IAAA,GAAOC,IAAA,EAAMjI,CAAA,IAAK;sBAChE4B,KAAA,CAAM1D,IAAA,CAAK4J,YAAA,CAAa9H,CAAC,CAAC;sBAE1B,IAAIzC,CAAA,GAAI,GAAG0K,IAAA,GAAOF,MAAA,CAAOxK,CAAA,GAAI,CAAC;oBAC/B;oBAED,SAASmB,CAAA,GAAI,GAAGsJ,IAAA,GAAOD,MAAA,CAAOxK,CAAC,GAAG0K,IAAA,GAAO,GAAGvJ,CAAA,GAAIsJ,IAAA,GAAOC,IAAA,GAAO,GAAGvJ,CAAA,IAAK;sBACpE,IAAIA,CAAA,GAAI,GAAG;wBACT3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAC;wBACjC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;wBACrC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;sBAC7D,OAA6B;wBACL3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAC;wBACjC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;wBACrC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAIG,KAAA,CAAMlD,CAAA,GAAI,CAAC;sBACtC;sBAED,IAAInB,CAAA,GAAI,GAAG0K,IAAA,GAAOF,MAAA,CAAOxK,CAAA,GAAI,CAAC;oBAC/B;kBACF;gBACF;gBAED;cAGF,KAAK;gBACH,IAAI2K,aAAA,GAAgB9J,QAAA,CAASkI,KAAA,CAAM7G,UAAA,CAAW0I,aAAa;gBAE3D,IAAID,aAAA,GAAgB,GAAG;kBACrB,IAAIJ,YAAA,GAAe,IAAIzF,UAAA,CAAWwE,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,CAAKgE,MAAM;kBAClE,IAAIyI,MAAA,GAAS,IAAI1F,UAAA,CAAWwE,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,CAAKgE,MAAM;kBAC5DwI,YAAA,CAAa3F,GAAA,CAAI0E,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,EAAM,CAAC;kBAC7CyM,MAAA,CAAO5F,GAAA,CAAI0E,OAAA,CAAQC,SAAA,CAAU,CAAC,EAAExL,IAAA,EAAM,CAAC;kBAEvC,IAAIiG,IAAA,GAAO2G,aAAA,GAAgBJ,YAAA,CAAaxI,MAAA;kBACxCvD,OAAA,GAAU,IAAIyF,WAAA,CAAY,IAAID,IAAA,GAAO,IAAI2G,aAAa;kBACtD,IAAIzG,YAAA,GAAe;oBACjB2G,iBAAA,GAAoB;kBACtB,IAAI7K,CAAA,GAAI;oBACNkK,GAAA,GAAMS,aAAA;oBACND,IAAA,GAAO;kBAET,OAAO1K,CAAA,GAAIkK,GAAA,EAAK;oBACd,IAAIY,IAAA,GAAO,EAAE;oBACb,IAAIrI,CAAA,GAAI;sBACNgI,IAAA,GAAOD,MAAA,CAAOxK,CAAC;oBAEjB,OAAOyC,CAAA,GAAIgI,IAAA,GAAOC,IAAA,EAAM;sBACtBI,IAAA,CAAKnK,IAAA,CAAK4J,YAAA,CAAaM,iBAAA,EAAmB,CAAC;sBAC3CpI,CAAA;oBACD;oBAED,IAAItB,CAAA,GAAI;oBAER,OAAOA,CAAA,GAAIsJ,IAAA,GAAOC,IAAA,GAAO,GAAG;sBAC1BlM,OAAA,CAAQ0F,YAAA,EAAc,IAAI4G,IAAA,CAAK,CAAC;sBAChCtM,OAAA,CAAQ0F,YAAA,EAAc,IAAI4G,IAAA,CAAK3J,CAAC;sBAChC3C,OAAA,CAAQ0F,YAAA,EAAc,IAAI4G,IAAA,CAAK3J,CAAA,GAAI,CAAC;sBACpCA,CAAA;oBACD;oBAEDnB,CAAA;oBACA0K,IAAA,GAAOF,MAAA,CAAOxK,CAAA,GAAI,CAAC;kBACpB;gBACF;gBAED;YAIH;UACF;UAEDoJ,YAAA;QACD;QAED,IAAI1H,QAAA,GAAW,IAAIC,cAAA,CAAgB;QACnCD,QAAA,CAASE,QAAA,CAAS,IAAI2C,eAAA,CAAgB/F,OAAA,EAAS,CAAC,CAAC;QACjDkD,QAAA,CAASG,YAAA,CAAa,YAAY,IAAI0C,eAAA,CAAgBzB,MAAA,EAAQ,CAAC,CAAC;QAEhE,IAAInE,OAAA,CAAQoD,MAAA,KAAWe,MAAA,CAAOf,MAAA,EAAQ;UACpCL,QAAA,CAASG,YAAA,CAAa,UAAU,IAAI0C,eAAA,CAAgB5F,OAAA,EAAS,CAAC,CAAC;QAChE;QAED,OAAO+C,QAAA;MACf,OAAa;QACL,MAAM,IAAIrB,KAAA,CAAM,0BAA0B;MAC3C;IACF;IAGD,IAAI0K,IAAA,GAAOC,UAAA,CAAW,IAAIrI,UAAA,CAAWtE,IAAA,EAAM,GAAG,GAAG,CAAC,EAAE0B,KAAA,CAAM,IAAI;IAE9D,IAAIgL,IAAA,CAAK,CAAC,EAAE5K,OAAA,CAAQ,KAAK,MAAM,IAAI;MACjC,OAAO4E,QAAA,CAASiG,UAAA,CAAW3M,IAAI,CAAC;IACjC,WAAU0M,IAAA,CAAK,CAAC,EAAEE,QAAA,CAAS,OAAO,GAAG;MACpC,OAAO3M,UAAA,CAAW0M,UAAA,CAAW3M,IAAI,CAAC;IACxC,OAAW;MACL,OAAOiE,WAAA,CAAYjE,IAAI;IACxB;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}