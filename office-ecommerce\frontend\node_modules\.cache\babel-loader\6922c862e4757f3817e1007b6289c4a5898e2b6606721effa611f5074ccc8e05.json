{"ast": null, "code": "import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nfunction AdaptiveEvents() {\n  const get = useThree(state => state.get);\n  const setEvents = useThree(state => state.setEvents);\n  const current = useThree(state => state.performance.current);\n  React.useEffect(() => {\n    const enabled = get().events.enabled;\n    return () => setEvents({\n      enabled\n    });\n  }, []);\n  React.useEffect(() => setEvents({\n    enabled: current === 1\n  }), [current]);\n  return null;\n}\nexport { AdaptiveEvents };", "map": {"version": 3, "names": ["React", "useThree", "AdaptiveEvents", "get", "state", "setEvents", "current", "performance", "useEffect", "enabled", "events"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/AdaptiveEvents.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction AdaptiveEvents() {\n  const get = useThree(state => state.get);\n  const setEvents = useThree(state => state.setEvents);\n  const current = useThree(state => state.performance.current);\n  React.useEffect(() => {\n    const enabled = get().events.enabled;\n    return () => setEvents({\n      enabled\n    });\n  }, []);\n  React.useEffect(() => setEvents({\n    enabled: current === 1\n  }), [current]);\n  return null;\n}\n\nexport { AdaptiveEvents };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,cAAcA,CAAA,EAAG;EACxB,MAAMC,GAAG,GAAGF,QAAQ,CAACG,KAAK,IAAIA,KAAK,CAACD,GAAG,CAAC;EACxC,MAAME,SAAS,GAAGJ,QAAQ,CAACG,KAAK,IAAIA,KAAK,CAACC,SAAS,CAAC;EACpD,MAAMC,OAAO,GAAGL,QAAQ,CAACG,KAAK,IAAIA,KAAK,CAACG,WAAW,CAACD,OAAO,CAAC;EAC5DN,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,MAAMC,OAAO,GAAGN,GAAG,CAAC,CAAC,CAACO,MAAM,CAACD,OAAO;IACpC,OAAO,MAAMJ,SAAS,CAAC;MACrBI;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACNT,KAAK,CAACQ,SAAS,CAAC,MAAMH,SAAS,CAAC;IAC9BI,OAAO,EAAEH,OAAO,KAAK;EACvB,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACd,OAAO,IAAI;AACb;AAEA,SAASJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}