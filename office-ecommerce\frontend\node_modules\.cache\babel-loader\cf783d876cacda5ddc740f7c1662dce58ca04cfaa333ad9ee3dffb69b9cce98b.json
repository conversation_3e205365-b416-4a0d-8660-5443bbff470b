{"ast": null, "code": "import { Loader, FileLoader, LoaderUtils, SkinnedMesh, Skeleton, Bone, Vector3, Float32BufferAttribute, BufferGeometry, Uint16BufferAttribute, TextureLoader, Color, MultiplyOperation, AddOperation, MeshToonMaterial, NearestFilter, RepeatWrapping, AnimationClip, VectorKeyframeTrack, QuaternionKeyframeTrack, NumberKeyframeTrack, Quaternion, Euler, Interpolant, CustomBlending, SrcAlphaFactor, OneMinusSrcAlphaFactor, DstAlphaFactor, DoubleSide, FrontSide } from \"three\";\nimport { TGALoader } from \"./TGALoader.js\";\nimport { Parser } from \"../libs/mmdparser.js\";\nclass MMDLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.loader = new FileLoader(this.manager);\n    this.parser = null;\n    this.meshBuilder = new MeshBuilder(this.manager);\n    this.animationBuilder = new AnimationBuilder();\n  }\n  /**\n   * @param {string} animationPath\n   * @return {MMDLoader}\n   */\n  setAnimationPath(animationPath) {\n    this.animationPath = animationPath;\n    return this;\n  }\n  // Load MMD assets as Three.js Object\n  /**\n   * Loads Model file (.pmd or .pmx) as a SkinnedMesh.\n   *\n   * @param {string} url - url to Model(.pmd or .pmx) file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  load(url, onLoad, onProgress, onError) {\n    const builder = this.meshBuilder.setCrossOrigin(this.crossOrigin);\n    let resourcePath;\n    if (this.resourcePath !== \"\") {\n      resourcePath = this.resourcePath;\n    } else if (this.path !== \"\") {\n      resourcePath = this.path;\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url);\n    }\n    const modelExtension = this._extractExtension(url).toLowerCase();\n    if (modelExtension !== \"pmd\" && modelExtension !== \"pmx\") {\n      if (onError) onError(new Error(\"THREE.MMDLoader: Unknown model file extension .\" + modelExtension + \".\"));\n      return;\n    }\n    this[modelExtension === \"pmd\" ? \"loadPMD\" : \"loadPMX\"](url, function (data) {\n      onLoad(builder.build(data, resourcePath, onProgress, onError));\n    }, onProgress, onError);\n  }\n  /**\n   * Loads Motion file(s) (.vmd) as a AnimationClip.\n   * If two or more files are specified, they'll be merged.\n   *\n   * @param {string|Array<string>} url - url(s) to animation(.vmd) file(s)\n   * @param {SkinnedMesh|THREE.Camera} object - tracks will be fitting to this object\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadAnimation(url, object, onLoad, onProgress, onError) {\n    const builder = this.animationBuilder;\n    this.loadVMD(url, function (vmd) {\n      onLoad(object.isCamera ? builder.buildCameraAnimation(vmd) : builder.build(vmd, object));\n    }, onProgress, onError);\n  }\n  /**\n   * Loads mode file and motion file(s) as an object containing\n   * a SkinnedMesh and a AnimationClip.\n   * Tracks of AnimationClip are fitting to the model.\n   *\n   * @param {string} modelUrl - url to Model(.pmd or .pmx) file\n   * @param {string|Array{string}} vmdUrl - url(s) to animation(.vmd) file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadWithAnimation(modelUrl, vmdUrl, onLoad, onProgress, onError) {\n    const scope = this;\n    this.load(modelUrl, function (mesh) {\n      scope.loadAnimation(vmdUrl, mesh, function (animation) {\n        onLoad({\n          mesh,\n          animation\n        });\n      }, onProgress, onError);\n    }, onProgress, onError);\n  }\n  // Load MMD assets as Object data parsed by MMDParser\n  /**\n   * Loads .pmd file as an Object.\n   *\n   * @param {string} url - url to .pmd file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadPMD(url, onLoad, onProgress, onError) {\n    const parser = this._getParser();\n    this.loader.setMimeType(void 0).setPath(this.path).setResponseType(\"arraybuffer\").setRequestHeader(this.requestHeader).setWithCredentials(this.withCredentials).load(url, function (buffer) {\n      onLoad(parser.parsePmd(buffer, true));\n    }, onProgress, onError);\n  }\n  /**\n   * Loads .pmx file as an Object.\n   *\n   * @param {string} url - url to .pmx file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadPMX(url, onLoad, onProgress, onError) {\n    const parser = this._getParser();\n    this.loader.setMimeType(void 0).setPath(this.path).setResponseType(\"arraybuffer\").setRequestHeader(this.requestHeader).setWithCredentials(this.withCredentials).load(url, function (buffer) {\n      onLoad(parser.parsePmx(buffer, true));\n    }, onProgress, onError);\n  }\n  /**\n   * Loads .vmd file as an Object. If two or more files are specified\n   * they'll be merged.\n   *\n   * @param {string|Array<string>} url - url(s) to .vmd file(s)\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadVMD(url, onLoad, onProgress, onError) {\n    const urls = Array.isArray(url) ? url : [url];\n    const vmds = [];\n    const vmdNum = urls.length;\n    const parser = this._getParser();\n    this.loader.setMimeType(void 0).setPath(this.animationPath).setResponseType(\"arraybuffer\").setRequestHeader(this.requestHeader).setWithCredentials(this.withCredentials);\n    for (let i = 0, il = urls.length; i < il; i++) {\n      this.loader.load(urls[i], function (buffer) {\n        vmds.push(parser.parseVmd(buffer, true));\n        if (vmds.length === vmdNum) onLoad(parser.mergeVmds(vmds));\n      }, onProgress, onError);\n    }\n  }\n  /**\n   * Loads .vpd file as an Object.\n   *\n   * @param {string} url - url to .vpd file\n   * @param {boolean} isUnicode\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadVPD(url, isUnicode, onLoad, onProgress, onError) {\n    const parser = this._getParser();\n    this.loader.setMimeType(isUnicode ? void 0 : \"text/plain; charset=shift_jis\").setPath(this.animationPath).setResponseType(\"text\").setRequestHeader(this.requestHeader).setWithCredentials(this.withCredentials).load(url, function (text) {\n      onLoad(parser.parseVpd(text, true));\n    }, onProgress, onError);\n  }\n  // private methods\n  _extractExtension(url) {\n    const index = url.lastIndexOf(\".\");\n    return index < 0 ? \"\" : url.slice(index + 1);\n  }\n  _getParser() {\n    if (this.parser === null) {\n      this.parser = new Parser();\n    }\n    return this.parser;\n  }\n}\nconst DEFAULT_TOON_TEXTURES = [\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAN0lEQVRYR+3WQREAMBACsZ5/bWiiMvgEBTt5cW37hjsBBAgQIECAwFwgyfYPCCBAgAABAgTWAh8aBHZBl14e8wAAAABJRU5ErkJggg==\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAOUlEQVRYR+3WMREAMAwDsYY/yoDI7MLwIiP40+RJklfcCCBAgAABAgTqArfb/QMCCBAgQIAAgbbAB3z/e0F3js2cAAAAAElFTkSuQmCC\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAN0lEQVRYR+3WQREAMBACsZ5/B5ilMvgEBTt5cW37hjsBBAgQIECAwFwgyfYPCCBAgAABAgTWAh81dWyx0gFwKAAAAABJRU5ErkJggg==\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAOklEQVRYR+3WoREAMAwDsWb/UQtCy9wxTOQJ/oQ8SXKKGwEECBAgQIBAXeDt7f4BAQQIECBAgEBb4AOz8Hzx7WLY4wAAAABJRU5ErkJggg==\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABPUlEQVRYR+1XwW7CMAy1+f9fZOMysSEOEweEOPRNdm3HbdOyIhAcklPrOs/PLy9RygBALxzcCDQFmgJNgaZAU6Ap0BR4PwX8gsRMVLssMRH5HcpzJEaWL7EVg9F1IHRlyqQohgVr4FGUlUcMJSjcUlDw0zvjeun70cLWmneoyf7NgBTQSniBTQQSuJAZsOnnaczjIMb5hCiuHKxokCrJfVnrctyZL0PkJAJe1HMil4nxeyi3Ypfn1kX51jpPvo/JeCNC4PhVdHdJw2XjBR8brF8PEIhNVn12AgP7uHsTBguBn53MUZCqv7Lp07Pn5k1Ro+uWmUNn7D+M57rtk7aG0Vo73xyF/fbFf0bPJjDXngnGocDTdFhygZjwUQrMNrDcmZlQT50VJ/g/UwNyHpu778+yW+/ksOz/BFo54P4AsUXMfRq7XWsAAAAASUVORK5CYII=\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACMElEQVRYR+2Xv4pTQRTGf2dubhLdICiii2KnYKHVolhauKWPoGAnNr6BD6CvIVaihYuI2i1ia0BY0MZGRHQXjZj/mSPnnskfNWiWZUlzJ5k7M2cm833nO5Mziej2DWWJRUoCpQKlAntSQCqgw39/iUWAGmh37jrRnVsKlgpiqmkoGVABA7E57fvY+pJDdgKqF6HzFCSADkDq+F6AHABtQ+UMVE5D7zXod7fFNhTEckTbj5XQgHzNN+5tQvc5NG7C6BNkp6D3EmpXHDR+dQAjFLchW3VS9rlw3JBh+B7ys5Cf9z0GW1C/7P32AyBAOAz1q4jGliIH3YPuBnSfQX4OGreTIgEYQb/pBDtPnEQ4CivXYPAWBk13oHrB54yA9QuSn2H4AcKRpEILDt0BUzj+RLR1V5EqjD66NPRBVpLcQwjHoHYJOhsQv6U4mnzmrIXJCFr4LDwm/xBUoboG9XX4cc9VKdYoSA2yk5NQLJaKDUjTBoveG3Z2TElTxwjNK4M3LEZgUdDdruvcXzKBpStgp2NPiWi3ks9ZXxIoFVi+AvHLdc9TqtjL3/aYjpPlrzOcEnK62Szhimdd7xX232zFDTgtxezOu3WNMRLjiKgjtOhHVMd1loynVHvOgjuIIJMaELEqhJAV/RCSLbWTcfPFakFgFlALTRRvx+ok6Hlp/Q+v3fmx90bMyUzaEAhmM3KvHlXTL5DxnbGf/1M8RNNACLL5MNtPxP/mypJAqcDSFfgFhpYqWUzhTEAAAAAASUVORK5CYII=\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=\", \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=\"];\nclass MeshBuilder {\n  constructor(manager) {\n    this.crossOrigin = \"anonymous\";\n    this.geometryBuilder = new GeometryBuilder();\n    this.materialBuilder = new MaterialBuilder(manager);\n  }\n  /**\n   * @param {string} crossOrigin\n   * @return {MeshBuilder}\n   */\n  setCrossOrigin(crossOrigin) {\n    this.crossOrigin = crossOrigin;\n    return this;\n  }\n  /**\n   * @param {Object} data - parsed PMD/PMX data\n   * @param {string} resourcePath\n   * @param {function} onProgress\n   * @param {function} onError\n   * @return {SkinnedMesh}\n   */\n  build(data, resourcePath, onProgress, onError) {\n    const geometry = this.geometryBuilder.build(data);\n    const material = this.materialBuilder.setCrossOrigin(this.crossOrigin).setResourcePath(resourcePath).build(data, geometry, onProgress, onError);\n    const mesh = new SkinnedMesh(geometry, material);\n    const skeleton = new Skeleton(initBones(mesh));\n    mesh.bind(skeleton);\n    return mesh;\n  }\n}\nfunction initBones(mesh) {\n  const geometry = mesh.geometry;\n  const bones = [];\n  if (geometry && geometry.bones !== void 0) {\n    for (let i = 0, il = geometry.bones.length; i < il; i++) {\n      const gbone = geometry.bones[i];\n      const bone = new Bone();\n      bones.push(bone);\n      bone.name = gbone.name;\n      bone.position.fromArray(gbone.pos);\n      bone.quaternion.fromArray(gbone.rotq);\n      if (gbone.scl !== void 0) bone.scale.fromArray(gbone.scl);\n    }\n    for (let i = 0, il = geometry.bones.length; i < il; i++) {\n      const gbone = geometry.bones[i];\n      if (gbone.parent !== -1 && gbone.parent !== null && bones[gbone.parent] !== void 0) {\n        bones[gbone.parent].add(bones[i]);\n      } else {\n        mesh.add(bones[i]);\n      }\n    }\n  }\n  mesh.updateMatrixWorld(true);\n  return bones;\n}\nclass GeometryBuilder {\n  /**\n   * @param {Object} data - parsed PMD/PMX data\n   * @return {BufferGeometry}\n   */\n  build(data) {\n    const positions = [];\n    const uvs = [];\n    const normals = [];\n    const indices = [];\n    const groups = [];\n    const bones = [];\n    const skinIndices = [];\n    const skinWeights = [];\n    const morphTargets = [];\n    const morphPositions = [];\n    const iks = [];\n    const grants = [];\n    const rigidBodies = [];\n    const constraints = [];\n    let offset = 0;\n    const boneTypeTable = {};\n    for (let i = 0; i < data.metadata.vertexCount; i++) {\n      const v = data.vertices[i];\n      for (let j = 0, jl = v.position.length; j < jl; j++) {\n        positions.push(v.position[j]);\n      }\n      for (let j = 0, jl = v.normal.length; j < jl; j++) {\n        normals.push(v.normal[j]);\n      }\n      for (let j = 0, jl = v.uv.length; j < jl; j++) {\n        uvs.push(v.uv[j]);\n      }\n      for (let j = 0; j < 4; j++) {\n        skinIndices.push(v.skinIndices.length - 1 >= j ? v.skinIndices[j] : 0);\n      }\n      for (let j = 0; j < 4; j++) {\n        skinWeights.push(v.skinWeights.length - 1 >= j ? v.skinWeights[j] : 0);\n      }\n    }\n    for (let i = 0; i < data.metadata.faceCount; i++) {\n      const face = data.faces[i];\n      for (let j = 0, jl = face.indices.length; j < jl; j++) {\n        indices.push(face.indices[j]);\n      }\n    }\n    for (let i = 0; i < data.metadata.materialCount; i++) {\n      const material = data.materials[i];\n      groups.push({\n        offset: offset * 3,\n        count: material.faceCount * 3\n      });\n      offset += material.faceCount;\n    }\n    for (let i = 0; i < data.metadata.rigidBodyCount; i++) {\n      const body = data.rigidBodies[i];\n      let value = boneTypeTable[body.boneIndex];\n      value = value === void 0 ? body.type : Math.max(body.type, value);\n      boneTypeTable[body.boneIndex] = value;\n    }\n    for (let i = 0; i < data.metadata.boneCount; i++) {\n      const boneData = data.bones[i];\n      const bone = {\n        index: i,\n        transformationClass: boneData.transformationClass,\n        parent: boneData.parentIndex,\n        name: boneData.name,\n        pos: boneData.position.slice(0, 3),\n        rotq: [0, 0, 0, 1],\n        scl: [1, 1, 1],\n        rigidBodyType: boneTypeTable[i] !== void 0 ? boneTypeTable[i] : -1\n      };\n      if (bone.parent !== -1) {\n        bone.pos[0] -= data.bones[bone.parent].position[0];\n        bone.pos[1] -= data.bones[bone.parent].position[1];\n        bone.pos[2] -= data.bones[bone.parent].position[2];\n      }\n      bones.push(bone);\n    }\n    if (data.metadata.format === \"pmd\") {\n      for (let i = 0; i < data.metadata.ikCount; i++) {\n        const ik = data.iks[i];\n        const param = {\n          target: ik.target,\n          effector: ik.effector,\n          iteration: ik.iteration,\n          maxAngle: ik.maxAngle * 4,\n          links: []\n        };\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = {};\n          link.index = ik.links[j].index;\n          link.enabled = true;\n          if (data.bones[link.index].name.indexOf(\"ひざ\") >= 0) {\n            link.limitation = new Vector3(1, 0, 0);\n          }\n          param.links.push(link);\n        }\n        iks.push(param);\n      }\n    } else {\n      for (let i = 0; i < data.metadata.boneCount; i++) {\n        const ik = data.bones[i].ik;\n        if (ik === void 0) continue;\n        const param = {\n          target: i,\n          effector: ik.effector,\n          iteration: ik.iteration,\n          maxAngle: ik.maxAngle,\n          links: []\n        };\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = {};\n          link.index = ik.links[j].index;\n          link.enabled = true;\n          if (ik.links[j].angleLimitation === 1) {\n            const rotationMin = ik.links[j].lowerLimitationAngle;\n            const rotationMax = ik.links[j].upperLimitationAngle;\n            const tmp1 = -rotationMax[0];\n            const tmp2 = -rotationMax[1];\n            rotationMax[0] = -rotationMin[0];\n            rotationMax[1] = -rotationMin[1];\n            rotationMin[0] = tmp1;\n            rotationMin[1] = tmp2;\n            link.rotationMin = new Vector3().fromArray(rotationMin);\n            link.rotationMax = new Vector3().fromArray(rotationMax);\n          }\n          param.links.push(link);\n        }\n        iks.push(param);\n        bones[i].ik = param;\n      }\n    }\n    if (data.metadata.format === \"pmx\") {\n      let traverse = function (entry) {\n        if (entry.param) {\n          grants.push(entry.param);\n          bones[entry.param.index].grant = entry.param;\n        }\n        entry.visited = true;\n        for (let i = 0, il = entry.children.length; i < il; i++) {\n          const child = entry.children[i];\n          if (!child.visited) traverse(child);\n        }\n      };\n      const grantEntryMap = {};\n      for (let i = 0; i < data.metadata.boneCount; i++) {\n        const boneData = data.bones[i];\n        const grant = boneData.grant;\n        if (grant === void 0) continue;\n        const param = {\n          index: i,\n          parentIndex: grant.parentIndex,\n          ratio: grant.ratio,\n          isLocal: grant.isLocal,\n          affectRotation: grant.affectRotation,\n          affectPosition: grant.affectPosition,\n          transformationClass: boneData.transformationClass\n        };\n        grantEntryMap[i] = {\n          parent: null,\n          children: [],\n          param,\n          visited: false\n        };\n      }\n      const rootEntry = {\n        parent: null,\n        children: [],\n        param: null,\n        visited: false\n      };\n      for (const boneIndex in grantEntryMap) {\n        const grantEntry = grantEntryMap[boneIndex];\n        const parentGrantEntry = grantEntryMap[grantEntry.parentIndex] || rootEntry;\n        grantEntry.parent = parentGrantEntry;\n        parentGrantEntry.children.push(grantEntry);\n      }\n      traverse(rootEntry);\n    }\n    function updateAttributes(attribute, morph, ratio) {\n      for (let i = 0; i < morph.elementCount; i++) {\n        const element = morph.elements[i];\n        let index;\n        if (data.metadata.format === \"pmd\") {\n          index = data.morphs[0].elements[element.index].index;\n        } else {\n          index = element.index;\n        }\n        attribute.array[index * 3 + 0] += element.position[0] * ratio;\n        attribute.array[index * 3 + 1] += element.position[1] * ratio;\n        attribute.array[index * 3 + 2] += element.position[2] * ratio;\n      }\n    }\n    for (let i = 0; i < data.metadata.morphCount; i++) {\n      const morph = data.morphs[i];\n      const params = {\n        name: morph.name\n      };\n      const attribute = new Float32BufferAttribute(data.metadata.vertexCount * 3, 3);\n      attribute.name = morph.name;\n      for (let j = 0; j < data.metadata.vertexCount * 3; j++) {\n        attribute.array[j] = positions[j];\n      }\n      if (data.metadata.format === \"pmd\") {\n        if (i !== 0) {\n          updateAttributes(attribute, morph, 1);\n        }\n      } else {\n        if (morph.type === 0) {\n          for (let j = 0; j < morph.elementCount; j++) {\n            const morph2 = data.morphs[morph.elements[j].index];\n            const ratio = morph.elements[j].ratio;\n            if (morph2.type === 1) {\n              updateAttributes(attribute, morph2, ratio);\n            }\n          }\n        } else if (morph.type === 1) {\n          updateAttributes(attribute, morph, 1);\n        } else if (morph.type === 2) ;else if (morph.type === 3) ;else if (morph.type === 4) ;else if (morph.type === 5) ;else if (morph.type === 6) ;else if (morph.type === 7) ;else if (morph.type === 8) ;\n      }\n      morphTargets.push(params);\n      morphPositions.push(attribute);\n    }\n    for (let i = 0; i < data.metadata.rigidBodyCount; i++) {\n      const rigidBody = data.rigidBodies[i];\n      const params = {};\n      for (const key in rigidBody) {\n        params[key] = rigidBody[key];\n      }\n      if (data.metadata.format === \"pmx\") {\n        if (params.boneIndex !== -1) {\n          const bone = data.bones[params.boneIndex];\n          params.position[0] -= bone.position[0];\n          params.position[1] -= bone.position[1];\n          params.position[2] -= bone.position[2];\n        }\n      }\n      rigidBodies.push(params);\n    }\n    for (let i = 0; i < data.metadata.constraintCount; i++) {\n      const constraint = data.constraints[i];\n      const params = {};\n      for (const key in constraint) {\n        params[key] = constraint[key];\n      }\n      const bodyA = rigidBodies[params.rigidBodyIndex1];\n      const bodyB = rigidBodies[params.rigidBodyIndex2];\n      if (bodyA.type !== 0 && bodyB.type === 2) {\n        if (bodyA.boneIndex !== -1 && bodyB.boneIndex !== -1 && data.bones[bodyB.boneIndex].parentIndex === bodyA.boneIndex) {\n          bodyB.type = 1;\n        }\n      }\n      constraints.push(params);\n    }\n    const geometry = new BufferGeometry();\n    geometry.setAttribute(\"position\", new Float32BufferAttribute(positions, 3));\n    geometry.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n    geometry.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n    geometry.setAttribute(\"skinIndex\", new Uint16BufferAttribute(skinIndices, 4));\n    geometry.setAttribute(\"skinWeight\", new Float32BufferAttribute(skinWeights, 4));\n    geometry.setIndex(indices);\n    for (let i = 0, il = groups.length; i < il; i++) {\n      geometry.addGroup(groups[i].offset, groups[i].count, i);\n    }\n    geometry.bones = bones;\n    geometry.morphTargets = morphTargets;\n    geometry.morphAttributes.position = morphPositions;\n    geometry.morphTargetsRelative = false;\n    geometry.userData.MMD = {\n      bones,\n      iks,\n      grants,\n      rigidBodies,\n      constraints,\n      format: data.metadata.format\n    };\n    geometry.computeBoundingSphere();\n    return geometry;\n  }\n}\nclass MaterialBuilder {\n  constructor(manager) {\n    this.manager = manager;\n    this.textureLoader = new TextureLoader(this.manager);\n    this.tgaLoader = null;\n    this.crossOrigin = \"anonymous\";\n    this.resourcePath = void 0;\n  }\n  /**\n   * @param {string} crossOrigin\n   * @return {MaterialBuilder}\n   */\n  setCrossOrigin(crossOrigin) {\n    this.crossOrigin = crossOrigin;\n    return this;\n  }\n  /**\n   * @param {string} resourcePath\n   * @return {MaterialBuilder}\n   */\n  setResourcePath(resourcePath) {\n    this.resourcePath = resourcePath;\n    return this;\n  }\n  /**\n   * @param {Object} data - parsed PMD/PMX data\n   * @param {BufferGeometry} geometry - some properties are dependend on geometry\n   * @param {function} onProgress\n   * @param {function} onError\n   * @return {Array<MeshToonMaterial>}\n   */\n  build(data, geometry) {\n    const materials = [];\n    const textures = {};\n    this.textureLoader.setCrossOrigin(this.crossOrigin);\n    for (let i = 0; i < data.metadata.materialCount; i++) {\n      const material = data.materials[i];\n      const params = {\n        userData: {}\n      };\n      if (material.name !== void 0) params.name = material.name;\n      params.color = new Color().fromArray(material.diffuse);\n      params.opacity = material.diffuse[3];\n      params.emissive = new Color().fromArray(material.ambient);\n      params.transparent = params.opacity !== 1;\n      params.skinning = geometry.bones.length > 0 ? true : false;\n      params.morphTargets = geometry.morphTargets.length > 0 ? true : false;\n      params.fog = true;\n      params.blending = CustomBlending;\n      params.blendSrc = SrcAlphaFactor;\n      params.blendDst = OneMinusSrcAlphaFactor;\n      params.blendSrcAlpha = SrcAlphaFactor;\n      params.blendDstAlpha = DstAlphaFactor;\n      if (data.metadata.format === \"pmx\" && (material.flag & 1) === 1) {\n        params.side = DoubleSide;\n      } else {\n        params.side = params.opacity === 1 ? FrontSide : DoubleSide;\n      }\n      if (data.metadata.format === \"pmd\") {\n        if (material.fileName) {\n          const fileName = material.fileName;\n          const fileNames = fileName.split(\"*\");\n          params.map = this._loadTexture(fileNames[0], textures);\n          if (fileNames.length > 1) {\n            const extension = fileNames[1].slice(-4).toLowerCase();\n            params.envMap = this._loadTexture(fileNames[1], textures);\n            params.combine = extension === \".sph\" ? MultiplyOperation : AddOperation;\n          }\n        }\n        const toonFileName = material.toonIndex === -1 ? \"toon00.bmp\" : data.toonTextures[material.toonIndex].fileName;\n        params.gradientMap = this._loadTexture(toonFileName, textures, {\n          isToonTexture: true,\n          isDefaultToonTexture: this._isDefaultToonTexture(toonFileName)\n        });\n        params.userData.outlineParameters = {\n          thickness: material.edgeFlag === 1 ? 3e-3 : 0,\n          color: [0, 0, 0],\n          alpha: 1,\n          visible: material.edgeFlag === 1\n        };\n      } else {\n        if (material.textureIndex !== -1) {\n          params.map = this._loadTexture(data.textures[material.textureIndex], textures);\n        }\n        if (material.envTextureIndex !== -1 && (material.envFlag === 1 || material.envFlag == 2)) {\n          params.envMap = this._loadTexture(data.textures[material.envTextureIndex], textures);\n          params.combine = material.envFlag === 1 ? MultiplyOperation : AddOperation;\n        }\n        let toonFileName, isDefaultToon;\n        if (material.toonIndex === -1 || material.toonFlag !== 0) {\n          toonFileName = \"toon\" + (\"0\" + (material.toonIndex + 1)).slice(-2) + \".bmp\";\n          isDefaultToon = true;\n        } else {\n          toonFileName = data.textures[material.toonIndex];\n          isDefaultToon = false;\n        }\n        params.gradientMap = this._loadTexture(toonFileName, textures, {\n          isToonTexture: true,\n          isDefaultToonTexture: isDefaultToon\n        });\n        params.userData.outlineParameters = {\n          thickness: material.edgeSize / 300,\n          // TODO: better calculation?\n          color: material.edgeColor.slice(0, 3),\n          alpha: material.edgeColor[3],\n          visible: (material.flag & 16) !== 0 && material.edgeSize > 0\n        };\n      }\n      if (params.map !== void 0) {\n        if (!params.transparent) {\n          this._checkImageTransparency(params.map, geometry, i);\n        }\n        params.emissive.multiplyScalar(0.2);\n      }\n      materials.push(new MeshToonMaterial(params));\n    }\n    if (data.metadata.format === \"pmx\") {\n      let checkAlphaMorph = function (elements, materials2) {\n        for (let i = 0, il = elements.length; i < il; i++) {\n          const element = elements[i];\n          if (element.index === -1) continue;\n          const material = materials2[element.index];\n          if (material.opacity !== element.diffuse[3]) {\n            material.transparent = true;\n          }\n        }\n      };\n      for (let i = 0, il = data.morphs.length; i < il; i++) {\n        const morph = data.morphs[i];\n        const elements = morph.elements;\n        if (morph.type === 0) {\n          for (let j = 0, jl = elements.length; j < jl; j++) {\n            const morph2 = data.morphs[elements[j].index];\n            if (morph2.type !== 8) continue;\n            checkAlphaMorph(morph2.elements, materials);\n          }\n        } else if (morph.type === 8) {\n          checkAlphaMorph(elements, materials);\n        }\n      }\n    }\n    return materials;\n  }\n  // private methods\n  _getTGALoader() {\n    if (this.tgaLoader === null) {\n      if (TGALoader === void 0) {\n        throw new Error(\"THREE.MMDLoader: Import TGALoader\");\n      }\n      this.tgaLoader = new TGALoader(this.manager);\n    }\n    return this.tgaLoader;\n  }\n  _isDefaultToonTexture(name) {\n    if (name.length !== 10) return false;\n    return /toon(10|0[0-9])\\.bmp/.test(name);\n  }\n  _loadTexture(filePath, textures, params, onProgress, onError) {\n    params = params || {};\n    const scope = this;\n    let fullPath;\n    if (params.isDefaultToonTexture === true) {\n      let index;\n      try {\n        index = parseInt(filePath.match(/toon([0-9]{2})\\.bmp$/)[1]);\n      } catch (e) {\n        console.warn(\"THREE.MMDLoader: \" + filePath + \" seems like a not right default texture path. Using toon00.bmp instead.\");\n        index = 0;\n      }\n      fullPath = DEFAULT_TOON_TEXTURES[index];\n    } else {\n      fullPath = this.resourcePath + filePath;\n    }\n    if (textures[fullPath] !== void 0) return textures[fullPath];\n    let loader = this.manager.getHandler(fullPath);\n    if (loader === null) {\n      loader = filePath.slice(-4).toLowerCase() === \".tga\" ? this._getTGALoader() : this.textureLoader;\n    }\n    const texture = loader.load(fullPath, function (t) {\n      if (params.isToonTexture === true) {\n        t.image = scope._getRotatedImage(t.image);\n        t.magFilter = NearestFilter;\n        t.minFilter = NearestFilter;\n      }\n      t.flipY = false;\n      t.wrapS = RepeatWrapping;\n      t.wrapT = RepeatWrapping;\n      for (let i = 0; i < texture.readyCallbacks.length; i++) {\n        texture.readyCallbacks[i](texture);\n      }\n      delete texture.readyCallbacks;\n    }, onProgress, onError);\n    texture.readyCallbacks = [];\n    textures[fullPath] = texture;\n    return texture;\n  }\n  _getRotatedImage(image) {\n    const canvas = document.createElement(\"canvas\");\n    const context = canvas.getContext(\"2d\");\n    const width = image.width;\n    const height = image.height;\n    canvas.width = width;\n    canvas.height = height;\n    context.clearRect(0, 0, width, height);\n    context.translate(width / 2, height / 2);\n    context.rotate(0.5 * Math.PI);\n    context.translate(-width / 2, -height / 2);\n    context.drawImage(image, 0, 0);\n    return context.getImageData(0, 0, width, height);\n  }\n  // Check if the partial image area used by the texture is transparent.\n  _checkImageTransparency(map, geometry, groupIndex) {\n    map.readyCallbacks.push(function (texture) {\n      function createImageData(image) {\n        const canvas = document.createElement(\"canvas\");\n        canvas.width = image.width;\n        canvas.height = image.height;\n        const context = canvas.getContext(\"2d\");\n        context.drawImage(image, 0, 0);\n        return context.getImageData(0, 0, canvas.width, canvas.height);\n      }\n      function detectImageTransparency(image, uvs, indices) {\n        const width = image.width;\n        const height = image.height;\n        const data = image.data;\n        const threshold = 253;\n        if (data.length / (width * height) !== 4) return false;\n        for (let i = 0; i < indices.length; i += 3) {\n          const centerUV = {\n            x: 0,\n            y: 0\n          };\n          for (let j = 0; j < 3; j++) {\n            const index = indices[i * 3 + j];\n            const uv = {\n              x: uvs[index * 2 + 0],\n              y: uvs[index * 2 + 1]\n            };\n            if (getAlphaByUv(image, uv) < threshold) return true;\n            centerUV.x += uv.x;\n            centerUV.y += uv.y;\n          }\n          centerUV.x /= 3;\n          centerUV.y /= 3;\n          if (getAlphaByUv(image, centerUV) < threshold) return true;\n        }\n        return false;\n      }\n      function getAlphaByUv(image, uv) {\n        const width = image.width;\n        const height = image.height;\n        let x = Math.round(uv.x * width) % width;\n        let y = Math.round(uv.y * height) % height;\n        if (x < 0) x += width;\n        if (y < 0) y += height;\n        const index = y * width + x;\n        return image.data[index * 4 + 3];\n      }\n      const imageData = texture.image.data !== void 0 ? texture.image : createImageData(texture.image);\n      const group = geometry.groups[groupIndex];\n      if (detectImageTransparency(imageData, geometry.attributes.uv.array, geometry.index.array.slice(group.start, group.start + group.count))) {\n        map.transparent = true;\n      }\n    });\n  }\n}\nclass AnimationBuilder {\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @param {SkinnedMesh} mesh - tracks will be fitting to mesh\n   * @return {AnimationClip}\n   */\n  build(vmd, mesh) {\n    const tracks = this.buildSkeletalAnimation(vmd, mesh).tracks;\n    const tracks2 = this.buildMorphAnimation(vmd, mesh).tracks;\n    for (let i = 0, il = tracks2.length; i < il; i++) {\n      tracks.push(tracks2[i]);\n    }\n    return new AnimationClip(\"\", -1, tracks);\n  }\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @param {SkinnedMesh} mesh - tracks will be fitting to mesh\n   * @return {AnimationClip}\n   */\n  buildSkeletalAnimation(vmd, mesh) {\n    function pushInterpolation(array, interpolation, index) {\n      array.push(interpolation[index + 0] / 127);\n      array.push(interpolation[index + 8] / 127);\n      array.push(interpolation[index + 4] / 127);\n      array.push(interpolation[index + 12] / 127);\n    }\n    const tracks = [];\n    const motions = {};\n    const bones = mesh.skeleton.bones;\n    const boneNameDictionary = {};\n    for (let i = 0, il = bones.length; i < il; i++) {\n      boneNameDictionary[bones[i].name] = true;\n    }\n    for (let i = 0; i < vmd.metadata.motionCount; i++) {\n      const motion = vmd.motions[i];\n      const boneName = motion.boneName;\n      if (boneNameDictionary[boneName] === void 0) continue;\n      motions[boneName] = motions[boneName] || [];\n      motions[boneName].push(motion);\n    }\n    for (const key in motions) {\n      const array = motions[key];\n      array.sort(function (a, b) {\n        return a.frameNum - b.frameNum;\n      });\n      const times = [];\n      const positions = [];\n      const rotations = [];\n      const pInterpolations = [];\n      const rInterpolations = [];\n      const basePosition = mesh.skeleton.getBoneByName(key).position.toArray();\n      for (let i = 0, il = array.length; i < il; i++) {\n        const time = array[i].frameNum / 30;\n        const position = array[i].position;\n        const rotation = array[i].rotation;\n        const interpolation = array[i].interpolation;\n        times.push(time);\n        for (let j = 0; j < 3; j++) positions.push(basePosition[j] + position[j]);\n        for (let j = 0; j < 4; j++) rotations.push(rotation[j]);\n        for (let j = 0; j < 3; j++) pushInterpolation(pInterpolations, interpolation, j);\n        pushInterpolation(rInterpolations, interpolation, 3);\n      }\n      const targetName = \".bones[\" + key + \"]\";\n      tracks.push(this._createTrack(targetName + \".position\", VectorKeyframeTrack, times, positions, pInterpolations));\n      tracks.push(this._createTrack(targetName + \".quaternion\", QuaternionKeyframeTrack, times, rotations, rInterpolations));\n    }\n    return new AnimationClip(\"\", -1, tracks);\n  }\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @param {SkinnedMesh} mesh - tracks will be fitting to mesh\n   * @return {AnimationClip}\n   */\n  buildMorphAnimation(vmd, mesh) {\n    const tracks = [];\n    const morphs = {};\n    const morphTargetDictionary = mesh.morphTargetDictionary;\n    for (let i = 0; i < vmd.metadata.morphCount; i++) {\n      const morph = vmd.morphs[i];\n      const morphName = morph.morphName;\n      if (morphTargetDictionary[morphName] === void 0) continue;\n      morphs[morphName] = morphs[morphName] || [];\n      morphs[morphName].push(morph);\n    }\n    for (const key in morphs) {\n      const array = morphs[key];\n      array.sort(function (a, b) {\n        return a.frameNum - b.frameNum;\n      });\n      const times = [];\n      const values = [];\n      for (let i = 0, il = array.length; i < il; i++) {\n        times.push(array[i].frameNum / 30);\n        values.push(array[i].weight);\n      }\n      tracks.push(new NumberKeyframeTrack(\".morphTargetInfluences[\" + morphTargetDictionary[key] + \"]\", times, values));\n    }\n    return new AnimationClip(\"\", -1, tracks);\n  }\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @return {AnimationClip}\n   */\n  buildCameraAnimation(vmd) {\n    function pushVector3(array, vec) {\n      array.push(vec.x);\n      array.push(vec.y);\n      array.push(vec.z);\n    }\n    function pushQuaternion(array, q) {\n      array.push(q.x);\n      array.push(q.y);\n      array.push(q.z);\n      array.push(q.w);\n    }\n    function pushInterpolation(array, interpolation, index) {\n      array.push(interpolation[index * 4 + 0] / 127);\n      array.push(interpolation[index * 4 + 1] / 127);\n      array.push(interpolation[index * 4 + 2] / 127);\n      array.push(interpolation[index * 4 + 3] / 127);\n    }\n    const cameras = vmd.cameras === void 0 ? [] : vmd.cameras.slice();\n    cameras.sort(function (a, b) {\n      return a.frameNum - b.frameNum;\n    });\n    const times = [];\n    const centers = [];\n    const quaternions = [];\n    const positions = [];\n    const fovs = [];\n    const cInterpolations = [];\n    const qInterpolations = [];\n    const pInterpolations = [];\n    const fInterpolations = [];\n    const quaternion = new Quaternion();\n    const euler = new Euler();\n    const position = new Vector3();\n    const center = new Vector3();\n    for (let i = 0, il = cameras.length; i < il; i++) {\n      const motion = cameras[i];\n      const time = motion.frameNum / 30;\n      const pos = motion.position;\n      const rot = motion.rotation;\n      const distance = motion.distance;\n      const fov = motion.fov;\n      const interpolation = motion.interpolation;\n      times.push(time);\n      position.set(0, 0, -distance);\n      center.set(pos[0], pos[1], pos[2]);\n      euler.set(-rot[0], -rot[1], -rot[2]);\n      quaternion.setFromEuler(euler);\n      position.add(center);\n      position.applyQuaternion(quaternion);\n      pushVector3(centers, center);\n      pushQuaternion(quaternions, quaternion);\n      pushVector3(positions, position);\n      fovs.push(fov);\n      for (let j = 0; j < 3; j++) {\n        pushInterpolation(cInterpolations, interpolation, j);\n      }\n      pushInterpolation(qInterpolations, interpolation, 3);\n      for (let j = 0; j < 3; j++) {\n        pushInterpolation(pInterpolations, interpolation, 4);\n      }\n      pushInterpolation(fInterpolations, interpolation, 5);\n    }\n    const tracks = [];\n    tracks.push(this._createTrack(\"target.position\", VectorKeyframeTrack, times, centers, cInterpolations));\n    tracks.push(this._createTrack(\".quaternion\", QuaternionKeyframeTrack, times, quaternions, qInterpolations));\n    tracks.push(this._createTrack(\".position\", VectorKeyframeTrack, times, positions, pInterpolations));\n    tracks.push(this._createTrack(\".fov\", NumberKeyframeTrack, times, fovs, fInterpolations));\n    return new AnimationClip(\"\", -1, tracks);\n  }\n  // private method\n  _createTrack(node, typedKeyframeTrack, times, values, interpolations) {\n    if (times.length > 2) {\n      times = times.slice();\n      values = values.slice();\n      interpolations = interpolations.slice();\n      const stride = values.length / times.length;\n      const interpolateStride = interpolations.length / times.length;\n      let index = 1;\n      for (let aheadIndex = 2, endIndex = times.length; aheadIndex < endIndex; aheadIndex++) {\n        for (let i = 0; i < stride; i++) {\n          if (values[index * stride + i] !== values[(index - 1) * stride + i] || values[index * stride + i] !== values[aheadIndex * stride + i]) {\n            index++;\n            break;\n          }\n        }\n        if (aheadIndex > index) {\n          times[index] = times[aheadIndex];\n          for (let i = 0; i < stride; i++) {\n            values[index * stride + i] = values[aheadIndex * stride + i];\n          }\n          for (let i = 0; i < interpolateStride; i++) {\n            interpolations[index * interpolateStride + i] = interpolations[aheadIndex * interpolateStride + i];\n          }\n        }\n      }\n      times.length = index + 1;\n      values.length = (index + 1) * stride;\n      interpolations.length = (index + 1) * interpolateStride;\n    }\n    const track = new typedKeyframeTrack(node, times, values);\n    track.createInterpolant = function InterpolantFactoryMethodCubicBezier(result) {\n      return new CubicBezierInterpolation(this.times, this.values, this.getValueSize(), result, new Float32Array(interpolations));\n    };\n    return track;\n  }\n}\nclass CubicBezierInterpolation extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer, params) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer);\n    this.interpolationParams = params;\n  }\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer;\n    const values = this.sampleValues;\n    const stride = this.valueSize;\n    const params = this.interpolationParams;\n    const offset1 = i1 * stride;\n    const offset0 = offset1 - stride;\n    const weight1 = t1 - t0 < 1 / 30 * 1.5 ? 0 : (t - t0) / (t1 - t0);\n    if (stride === 4) {\n      const x1 = params[i1 * 4 + 0];\n      const x2 = params[i1 * 4 + 1];\n      const y1 = params[i1 * 4 + 2];\n      const y2 = params[i1 * 4 + 3];\n      const ratio = this._calculate(x1, x2, y1, y2, weight1);\n      Quaternion.slerpFlat(result, 0, values, offset0, values, offset1, ratio);\n    } else if (stride === 3) {\n      for (let i = 0; i !== stride; ++i) {\n        const x1 = params[i1 * 12 + i * 4 + 0];\n        const x2 = params[i1 * 12 + i * 4 + 1];\n        const y1 = params[i1 * 12 + i * 4 + 2];\n        const y2 = params[i1 * 12 + i * 4 + 3];\n        const ratio = this._calculate(x1, x2, y1, y2, weight1);\n        result[i] = values[offset0 + i] * (1 - ratio) + values[offset1 + i] * ratio;\n      }\n    } else {\n      const x1 = params[i1 * 4 + 0];\n      const x2 = params[i1 * 4 + 1];\n      const y1 = params[i1 * 4 + 2];\n      const y2 = params[i1 * 4 + 3];\n      const ratio = this._calculate(x1, x2, y1, y2, weight1);\n      result[0] = values[offset0] * (1 - ratio) + values[offset1] * ratio;\n    }\n    return result;\n  }\n  _calculate(x1, x2, y1, y2, x) {\n    let c = 0.5;\n    let t = c;\n    let s = 1 - t;\n    const loop = 15;\n    const eps = 1e-5;\n    const math = Math;\n    let sst3, stt3, ttt;\n    for (let i = 0; i < loop; i++) {\n      sst3 = 3 * s * s * t;\n      stt3 = 3 * s * t * t;\n      ttt = t * t * t;\n      const ft = sst3 * x1 + stt3 * x2 + ttt - x;\n      if (math.abs(ft) < eps) break;\n      c /= 2;\n      t += ft < 0 ? c : -c;\n      s = 1 - t;\n    }\n    return sst3 * y1 + stt3 * y2 + ttt;\n  }\n}\nexport { MMDLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader", "constructor", "manager", "loader", "<PERSON><PERSON><PERSON><PERSON>", "parser", "meshBuilder", "MeshBuilder", "animationBuilder", "AnimationBuilder", "setAnimationPath", "animationPath", "load", "url", "onLoad", "onProgress", "onError", "builder", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "resourcePath", "path", "LoaderUtils", "extractUrlBase", "modelExtension", "_extractExtension", "toLowerCase", "Error", "data", "build", "loadAnimation", "object", "loadVMD", "vmd", "isCamera", "buildCameraAnimation", "loadWithAnimation", "modelUrl", "vmdUrl", "scope", "mesh", "animation", "loadPMD", "_get<PERSON><PERSON>er", "setMimeType", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "buffer", "parsePmd", "loadPMX", "parsePmx", "urls", "Array", "isArray", "vmds", "vmdNum", "length", "i", "il", "push", "parseVmd", "mergeVmds", "loadVPD", "isUnicode", "text", "parseVpd", "index", "lastIndexOf", "slice", "<PERSON><PERSON><PERSON>", "DEFAULT_TOON_TEXTURES", "geometryBuilder", "GeometryBuilder", "materialBuilder", "MaterialBuilder", "geometry", "material", "setResourcePath", "<PERSON><PERSON><PERSON><PERSON>", "skeleton", "Skeleton", "initBones", "bind", "bones", "gbone", "bone", "Bone", "name", "position", "fromArray", "pos", "quaternion", "rotq", "scl", "scale", "parent", "add", "updateMatrixWorld", "positions", "uvs", "normals", "indices", "groups", "skinIndices", "skinWeights", "morphTargets", "morphPositions", "iks", "grants", "rigidBodies", "constraints", "offset", "boneTypeTable", "metadata", "vertexCount", "v", "vertices", "j", "jl", "normal", "uv", "faceCount", "face", "faces", "materialCount", "materials", "count", "rigidBodyCount", "body", "value", "boneIndex", "type", "Math", "max", "boneCount", "boneData", "transformationClass", "parentIndex", "rigidBodyType", "format", "ikCount", "ik", "param", "target", "effector", "iteration", "maxAngle", "links", "link", "enabled", "indexOf", "limitation", "Vector3", "angleLimitation", "rotationMin", "lowerLimitationAngle", "rotationMax", "upperLimitationAngle", "tmp1", "tmp2", "traverse", "entry", "grant", "visited", "children", "child", "grantEntryMap", "ratio", "isLocal", "affectRotation", "affectPosition", "rootEntry", "grantEntry", "parentGrantEntry", "updateAttributes", "attribute", "morph", "elementCount", "element", "elements", "morphs", "array", "morphCount", "params", "Float32BufferAttribute", "morph2", "rigidBody", "key", "constraintCount", "constraint", "bodyA", "rigidBodyIndex1", "bodyB", "rigidBodyIndex2", "BufferGeometry", "setAttribute", "Uint16BufferAttribute", "setIndex", "addGroup", "morphAttributes", "morphTargetsRelative", "userData", "MMD", "computeBoundingSphere", "textureLoader", "TextureLoader", "tga<PERSON><PERSON><PERSON>", "textures", "color", "Color", "diffuse", "opacity", "emissive", "ambient", "transparent", "skinning", "fog", "blending", "CustomBlending", "blendSrc", "SrcAlphaFactor", "blendDst", "OneMinusSrcAlphaFactor", "blendSrcAlpha", "blendDstAlpha", "DstAlphaFactor", "flag", "side", "DoubleSide", "FrontSide", "fileName", "fileNames", "split", "map", "_loadTexture", "extension", "envMap", "combine", "MultiplyOperation", "AddOperation", "toonFileName", "toonIndex", "toonTextures", "gradientMap", "isToonTexture", "isDefaultToonTexture", "_isDefaultToonTexture", "outlineParameters", "thickness", "edgeFlag", "alpha", "visible", "textureIndex", "envTextureIndex", "envFlag", "isDefaultToon", "toonFlag", "edgeSize", "edgeColor", "_checkImageTransparency", "multiplyScalar", "MeshToonMaterial", "checkAlphaMorph", "materials2", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test", "filePath", "fullPath", "parseInt", "match", "e", "console", "warn", "<PERSON><PERSON><PERSON><PERSON>", "texture", "t", "image", "_getRotatedImage", "magFilter", "NearestFilter", "minFilter", "flipY", "wrapS", "RepeatWrapping", "wrapT", "readyCallbacks", "canvas", "document", "createElement", "context", "getContext", "width", "height", "clearRect", "translate", "rotate", "PI", "drawImage", "getImageData", "groupIndex", "createImageData", "detectImageTransparency", "threshold", "centerUV", "x", "y", "getAlphaByUv", "round", "imageData", "group", "attributes", "start", "tracks", "buildSkeletalAnimation", "tracks2", "buildMorphAnimation", "AnimationClip", "pushInterpolation", "interpolation", "motions", "boneNameDictionary", "motionCount", "motion", "boneName", "sort", "a", "b", "frameNum", "times", "rotations", "pInterpolations", "rInterpolations", "basePosition", "getBoneByName", "toArray", "time", "rotation", "targetName", "_createTrack", "VectorKeyframeTrack", "QuaternionKeyframeTrack", "morphTargetDictionary", "morphName", "values", "weight", "NumberKeyframeTrack", "pushVector3", "vec", "z", "pushQuaternion", "q", "w", "cameras", "centers", "quaternions", "fovs", "cInterpolations", "qInterpolations", "fInterpolations", "Quaternion", "euler", "<PERSON>uler", "center", "rot", "distance", "fov", "set", "setFromEuler", "applyQuaternion", "node", "typedKeyframeTrack", "interpolations", "stride", "interpolate<PERSON><PERSON><PERSON>", "aheadIndex", "endIndex", "track", "createInterpolant", "InterpolantFactoryMethodCubicBezier", "result", "CubicBezierInterpolation", "getValueSize", "Float32Array", "Interpolant", "parameterPositions", "sampleValues", "sampleSize", "result<PERSON><PERSON><PERSON>", "interpolationParams", "interpolate_", "i1", "t0", "t1", "valueSize", "offset1", "offset0", "weight1", "x1", "x2", "y1", "y2", "_calculate", "s<PERSON><PERSON><PERSON><PERSON>", "c", "s", "loop", "eps", "math", "sst3", "stt3", "ttt", "ft", "abs"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\MMDLoader.js"], "sourcesContent": ["import {\n  Add<PERSON><PERSON>ation,\n  AnimationClip,\n  Bone,\n  BufferGeometry,\n  Color,\n  CustomBlending,\n  DoubleSide,\n  DstAlphaFactor,\n  Euler,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  Interpolant,\n  Loader,\n  LoaderUtils,\n  MeshToonMaterial,\n  MultiplyOperation,\n  NearestFilter,\n  NumberKeyframeTrack,\n  OneMinusSrcAlphaFactor,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  RepeatWrapping,\n  Skeleton,\n  SkinnedMesh,\n  SrcAlphaFactor,\n  TextureLoader,\n  Uint16BufferAttribute,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\nimport { TGALoader } from '../loaders/TGALoader'\nimport { Parser } from '../libs/mmdparser'\n\n/**\n * Dependencies\n *  - mmd-parser https://github.com/takahirox/mmd-parser\n *  - TGALoader\n *  - OutlineEffect\n *\n * MMDLoader creates Three.js Objects from MMD resources as\n * PMD, PMX, VMD, and VPD files.\n *\n * PMD/PMX is a model data format, VMD is a motion data format\n * VPD is a posing data format used in MMD(Miku Miku Dance).\n *\n * MMD official site\n *  - https://sites.google.com/view/evpvp/\n *\n * PMD, VMD format (in Japanese)\n *  - http://blog.goo.ne.jp/torisu_tetosuki/e/209ad341d3ece2b1b4df24abf619d6e4\n *\n * PMX format\n *  - https://gist.github.com/felixjones/f8a06bd48f9da9a4539f\n *\n * TODO\n *  - light motion in vmd support.\n *  - SDEF support.\n *  - uv/material/bone morphing support.\n *  - more precise grant skinning support.\n *  - shadow support.\n */\n\n/**\n * @param {THREE.LoadingManager} manager\n */\nclass MMDLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.loader = new FileLoader(this.manager)\n\n    this.parser = null // lazy generation\n    this.meshBuilder = new MeshBuilder(this.manager)\n    this.animationBuilder = new AnimationBuilder()\n  }\n\n  /**\n   * @param {string} animationPath\n   * @return {MMDLoader}\n   */\n  setAnimationPath(animationPath) {\n    this.animationPath = animationPath\n    return this\n  }\n\n  // Load MMD assets as Three.js Object\n\n  /**\n   * Loads Model file (.pmd or .pmx) as a SkinnedMesh.\n   *\n   * @param {string} url - url to Model(.pmd or .pmx) file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  load(url, onLoad, onProgress, onError) {\n    const builder = this.meshBuilder.setCrossOrigin(this.crossOrigin)\n\n    // resource path\n\n    let resourcePath\n\n    if (this.resourcePath !== '') {\n      resourcePath = this.resourcePath\n    } else if (this.path !== '') {\n      resourcePath = this.path\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url)\n    }\n\n    const modelExtension = this._extractExtension(url).toLowerCase()\n\n    // Should I detect by seeing header?\n    if (modelExtension !== 'pmd' && modelExtension !== 'pmx') {\n      if (onError) onError(new Error('THREE.MMDLoader: Unknown model file extension .' + modelExtension + '.'))\n\n      return\n    }\n\n    this[modelExtension === 'pmd' ? 'loadPMD' : 'loadPMX'](\n      url,\n      function (data) {\n        onLoad(builder.build(data, resourcePath, onProgress, onError))\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  /**\n   * Loads Motion file(s) (.vmd) as a AnimationClip.\n   * If two or more files are specified, they'll be merged.\n   *\n   * @param {string|Array<string>} url - url(s) to animation(.vmd) file(s)\n   * @param {SkinnedMesh|THREE.Camera} object - tracks will be fitting to this object\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadAnimation(url, object, onLoad, onProgress, onError) {\n    const builder = this.animationBuilder\n\n    this.loadVMD(\n      url,\n      function (vmd) {\n        onLoad(object.isCamera ? builder.buildCameraAnimation(vmd) : builder.build(vmd, object))\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  /**\n   * Loads mode file and motion file(s) as an object containing\n   * a SkinnedMesh and a AnimationClip.\n   * Tracks of AnimationClip are fitting to the model.\n   *\n   * @param {string} modelUrl - url to Model(.pmd or .pmx) file\n   * @param {string|Array{string}} vmdUrl - url(s) to animation(.vmd) file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadWithAnimation(modelUrl, vmdUrl, onLoad, onProgress, onError) {\n    const scope = this\n\n    this.load(\n      modelUrl,\n      function (mesh) {\n        scope.loadAnimation(\n          vmdUrl,\n          mesh,\n          function (animation) {\n            onLoad({\n              mesh: mesh,\n              animation: animation,\n            })\n          },\n          onProgress,\n          onError,\n        )\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  // Load MMD assets as Object data parsed by MMDParser\n\n  /**\n   * Loads .pmd file as an Object.\n   *\n   * @param {string} url - url to .pmd file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadPMD(url, onLoad, onProgress, onError) {\n    const parser = this._getParser()\n\n    this.loader\n      .setMimeType(undefined)\n      .setPath(this.path)\n      .setResponseType('arraybuffer')\n      .setRequestHeader(this.requestHeader)\n      .setWithCredentials(this.withCredentials)\n      .load(\n        url,\n        function (buffer) {\n          onLoad(parser.parsePmd(buffer, true))\n        },\n        onProgress,\n        onError,\n      )\n  }\n\n  /**\n   * Loads .pmx file as an Object.\n   *\n   * @param {string} url - url to .pmx file\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadPMX(url, onLoad, onProgress, onError) {\n    const parser = this._getParser()\n\n    this.loader\n      .setMimeType(undefined)\n      .setPath(this.path)\n      .setResponseType('arraybuffer')\n      .setRequestHeader(this.requestHeader)\n      .setWithCredentials(this.withCredentials)\n      .load(\n        url,\n        function (buffer) {\n          onLoad(parser.parsePmx(buffer, true))\n        },\n        onProgress,\n        onError,\n      )\n  }\n\n  /**\n   * Loads .vmd file as an Object. If two or more files are specified\n   * they'll be merged.\n   *\n   * @param {string|Array<string>} url - url(s) to .vmd file(s)\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadVMD(url, onLoad, onProgress, onError) {\n    const urls = Array.isArray(url) ? url : [url]\n\n    const vmds = []\n    const vmdNum = urls.length\n\n    const parser = this._getParser()\n\n    this.loader\n      .setMimeType(undefined)\n      .setPath(this.animationPath)\n      .setResponseType('arraybuffer')\n      .setRequestHeader(this.requestHeader)\n      .setWithCredentials(this.withCredentials)\n\n    for (let i = 0, il = urls.length; i < il; i++) {\n      this.loader.load(\n        urls[i],\n        function (buffer) {\n          vmds.push(parser.parseVmd(buffer, true))\n\n          if (vmds.length === vmdNum) onLoad(parser.mergeVmds(vmds))\n        },\n        onProgress,\n        onError,\n      )\n    }\n  }\n\n  /**\n   * Loads .vpd file as an Object.\n   *\n   * @param {string} url - url to .vpd file\n   * @param {boolean} isUnicode\n   * @param {function} onLoad\n   * @param {function} onProgress\n   * @param {function} onError\n   */\n  loadVPD(url, isUnicode, onLoad, onProgress, onError) {\n    const parser = this._getParser()\n\n    this.loader\n      .setMimeType(isUnicode ? undefined : 'text/plain; charset=shift_jis')\n      .setPath(this.animationPath)\n      .setResponseType('text')\n      .setRequestHeader(this.requestHeader)\n      .setWithCredentials(this.withCredentials)\n      .load(\n        url,\n        function (text) {\n          onLoad(parser.parseVpd(text, true))\n        },\n        onProgress,\n        onError,\n      )\n  }\n\n  // private methods\n\n  _extractExtension(url) {\n    const index = url.lastIndexOf('.')\n    return index < 0 ? '' : url.slice(index + 1)\n  }\n\n  _getParser() {\n    if (this.parser === null) {\n      this.parser = new Parser()\n    }\n\n    return this.parser\n  }\n}\n\n// Utilities\n\n/*\n * base64 encoded defalut toon textures toon00.bmp - toon10.bmp.\n * We don't need to request external toon image files.\n * This idea is from http://www20.atpages.jp/katwat/three.js_r58/examples/mytest37/mmd.three.js\n */\nconst DEFAULT_TOON_TEXTURES = [\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAN0lEQVRYR+3WQREAMBACsZ5/bWiiMvgEBTt5cW37hjsBBAgQIECAwFwgyfYPCCBAgAABAgTWAh8aBHZBl14e8wAAAABJRU5ErkJggg==',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAOUlEQVRYR+3WMREAMAwDsYY/yoDI7MLwIiP40+RJklfcCCBAgAABAgTqArfb/QMCCBAgQIAAgbbAB3z/e0F3js2cAAAAAElFTkSuQmCC',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAN0lEQVRYR+3WQREAMBACsZ5/B5ilMvgEBTt5cW37hjsBBAgQIECAwFwgyfYPCCBAgAABAgTWAh81dWyx0gFwKAAAAABJRU5ErkJggg==',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAOklEQVRYR+3WoREAMAwDsWb/UQtCy9wxTOQJ/oQ8SXKKGwEECBAgQIBAXeDt7f4BAQQIECBAgEBb4AOz8Hzx7WLY4wAAAABJRU5ErkJggg==',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABPUlEQVRYR+1XwW7CMAy1+f9fZOMysSEOEweEOPRNdm3HbdOyIhAcklPrOs/PLy9RygBALxzcCDQFmgJNgaZAU6Ap0BR4PwX8gsRMVLssMRH5HcpzJEaWL7EVg9F1IHRlyqQohgVr4FGUlUcMJSjcUlDw0zvjeun70cLWmneoyf7NgBTQSniBTQQSuJAZsOnnaczjIMb5hCiuHKxokCrJfVnrctyZL0PkJAJe1HMil4nxeyi3Ypfn1kX51jpPvo/JeCNC4PhVdHdJw2XjBR8brF8PEIhNVn12AgP7uHsTBguBn53MUZCqv7Lp07Pn5k1Ro+uWmUNn7D+M57rtk7aG0Vo73xyF/fbFf0bPJjDXngnGocDTdFhygZjwUQrMNrDcmZlQT50VJ/g/UwNyHpu778+yW+/ksOz/BFo54P4AsUXMfRq7XWsAAAAASUVORK5CYII=',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACMElEQVRYR+2Xv4pTQRTGf2dubhLdICiii2KnYKHVolhauKWPoGAnNr6BD6CvIVaihYuI2i1ia0BY0MZGRHQXjZj/mSPnnskfNWiWZUlzJ5k7M2cm833nO5Mziej2DWWJRUoCpQKlAntSQCqgw39/iUWAGmh37jrRnVsKlgpiqmkoGVABA7E57fvY+pJDdgKqF6HzFCSADkDq+F6AHABtQ+UMVE5D7zXod7fFNhTEckTbj5XQgHzNN+5tQvc5NG7C6BNkp6D3EmpXHDR+dQAjFLchW3VS9rlw3JBh+B7ys5Cf9z0GW1C/7P32AyBAOAz1q4jGliIH3YPuBnSfQX4OGreTIgEYQb/pBDtPnEQ4CivXYPAWBk13oHrB54yA9QuSn2H4AcKRpEILDt0BUzj+RLR1V5EqjD66NPRBVpLcQwjHoHYJOhsQv6U4mnzmrIXJCFr4LDwm/xBUoboG9XX4cc9VKdYoSA2yk5NQLJaKDUjTBoveG3Z2TElTxwjNK4M3LEZgUdDdruvcXzKBpStgp2NPiWi3ks9ZXxIoFVi+AvHLdc9TqtjL3/aYjpPlrzOcEnK62Szhimdd7xX232zFDTgtxezOu3WNMRLjiKgjtOhHVMd1loynVHvOgjuIIJMaELEqhJAV/RCSLbWTcfPFakFgFlALTRRvx+ok6Hlp/Q+v3fmx90bMyUzaEAhmM3KvHlXTL5DxnbGf/1M8RNNACLL5MNtPxP/mypJAqcDSFfgFhpYqWUzhTEAAAAAASUVORK5CYII=',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=',\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAL0lEQVRYR+3QQREAAAzCsOFfNJPBJ1XQS9r2hsUAAQIECBAgQIAAAQIECBAgsBZ4MUx/ofm2I/kAAAAASUVORK5CYII=',\n]\n\n// Builders. They build Three.js object from Object data parsed by MMDParser.\n\n/**\n * @param {THREE.LoadingManager} manager\n */\nclass MeshBuilder {\n  constructor(manager) {\n    this.crossOrigin = 'anonymous'\n    this.geometryBuilder = new GeometryBuilder()\n    this.materialBuilder = new MaterialBuilder(manager)\n  }\n\n  /**\n   * @param {string} crossOrigin\n   * @return {MeshBuilder}\n   */\n  setCrossOrigin(crossOrigin) {\n    this.crossOrigin = crossOrigin\n    return this\n  }\n\n  /**\n   * @param {Object} data - parsed PMD/PMX data\n   * @param {string} resourcePath\n   * @param {function} onProgress\n   * @param {function} onError\n   * @return {SkinnedMesh}\n   */\n  build(data, resourcePath, onProgress, onError) {\n    const geometry = this.geometryBuilder.build(data)\n    const material = this.materialBuilder\n      .setCrossOrigin(this.crossOrigin)\n      .setResourcePath(resourcePath)\n      .build(data, geometry, onProgress, onError)\n\n    const mesh = new SkinnedMesh(geometry, material)\n\n    const skeleton = new Skeleton(initBones(mesh))\n    mesh.bind(skeleton)\n\n    // console.log( mesh ); // for console debug\n\n    return mesh\n  }\n}\n\n// TODO: Try to remove this function\n\nfunction initBones(mesh) {\n  const geometry = mesh.geometry\n\n  const bones = []\n\n  if (geometry && geometry.bones !== undefined) {\n    // first, create array of 'Bone' objects from geometry data\n\n    for (let i = 0, il = geometry.bones.length; i < il; i++) {\n      const gbone = geometry.bones[i]\n\n      // create new 'Bone' object\n\n      const bone = new Bone()\n      bones.push(bone)\n\n      // apply values\n\n      bone.name = gbone.name\n      bone.position.fromArray(gbone.pos)\n      bone.quaternion.fromArray(gbone.rotq)\n      if (gbone.scl !== undefined) bone.scale.fromArray(gbone.scl)\n    }\n\n    // second, create bone hierarchy\n\n    for (let i = 0, il = geometry.bones.length; i < il; i++) {\n      const gbone = geometry.bones[i]\n\n      if (gbone.parent !== -1 && gbone.parent !== null && bones[gbone.parent] !== undefined) {\n        // subsequent bones in the hierarchy\n\n        bones[gbone.parent].add(bones[i])\n      } else {\n        // topmost bone, immediate child of the skinned mesh\n\n        mesh.add(bones[i])\n      }\n    }\n  }\n\n  // now the bones are part of the scene graph and children of the skinned mesh.\n  // let's update the corresponding matrices\n\n  mesh.updateMatrixWorld(true)\n\n  return bones\n}\n\n//\n\nclass GeometryBuilder {\n  /**\n   * @param {Object} data - parsed PMD/PMX data\n   * @return {BufferGeometry}\n   */\n  build(data) {\n    // for geometry\n    const positions = []\n    const uvs = []\n    const normals = []\n\n    const indices = []\n\n    const groups = []\n\n    const bones = []\n    const skinIndices = []\n    const skinWeights = []\n\n    const morphTargets = []\n    const morphPositions = []\n\n    const iks = []\n    const grants = []\n\n    const rigidBodies = []\n    const constraints = []\n\n    // for work\n    let offset = 0\n    const boneTypeTable = {}\n\n    // positions, normals, uvs, skinIndices, skinWeights\n\n    for (let i = 0; i < data.metadata.vertexCount; i++) {\n      const v = data.vertices[i]\n\n      for (let j = 0, jl = v.position.length; j < jl; j++) {\n        positions.push(v.position[j])\n      }\n\n      for (let j = 0, jl = v.normal.length; j < jl; j++) {\n        normals.push(v.normal[j])\n      }\n\n      for (let j = 0, jl = v.uv.length; j < jl; j++) {\n        uvs.push(v.uv[j])\n      }\n\n      for (let j = 0; j < 4; j++) {\n        skinIndices.push(v.skinIndices.length - 1 >= j ? v.skinIndices[j] : 0.0)\n      }\n\n      for (let j = 0; j < 4; j++) {\n        skinWeights.push(v.skinWeights.length - 1 >= j ? v.skinWeights[j] : 0.0)\n      }\n    }\n\n    // indices\n\n    for (let i = 0; i < data.metadata.faceCount; i++) {\n      const face = data.faces[i]\n\n      for (let j = 0, jl = face.indices.length; j < jl; j++) {\n        indices.push(face.indices[j])\n      }\n    }\n\n    // groups\n\n    for (let i = 0; i < data.metadata.materialCount; i++) {\n      const material = data.materials[i]\n\n      groups.push({\n        offset: offset * 3,\n        count: material.faceCount * 3,\n      })\n\n      offset += material.faceCount\n    }\n\n    // bones\n\n    for (let i = 0; i < data.metadata.rigidBodyCount; i++) {\n      const body = data.rigidBodies[i]\n      let value = boneTypeTable[body.boneIndex]\n\n      // keeps greater number if already value is set without any special reasons\n      value = value === undefined ? body.type : Math.max(body.type, value)\n\n      boneTypeTable[body.boneIndex] = value\n    }\n\n    for (let i = 0; i < data.metadata.boneCount; i++) {\n      const boneData = data.bones[i]\n\n      const bone = {\n        index: i,\n        transformationClass: boneData.transformationClass,\n        parent: boneData.parentIndex,\n        name: boneData.name,\n        pos: boneData.position.slice(0, 3),\n        rotq: [0, 0, 0, 1],\n        scl: [1, 1, 1],\n        rigidBodyType: boneTypeTable[i] !== undefined ? boneTypeTable[i] : -1,\n      }\n\n      if (bone.parent !== -1) {\n        bone.pos[0] -= data.bones[bone.parent].position[0]\n        bone.pos[1] -= data.bones[bone.parent].position[1]\n        bone.pos[2] -= data.bones[bone.parent].position[2]\n      }\n\n      bones.push(bone)\n    }\n\n    // iks\n\n    // TODO: remove duplicated codes between PMD and PMX\n    if (data.metadata.format === 'pmd') {\n      for (let i = 0; i < data.metadata.ikCount; i++) {\n        const ik = data.iks[i]\n\n        const param = {\n          target: ik.target,\n          effector: ik.effector,\n          iteration: ik.iteration,\n          maxAngle: ik.maxAngle * 4,\n          links: [],\n        }\n\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = {}\n          link.index = ik.links[j].index\n          link.enabled = true\n\n          if (data.bones[link.index].name.indexOf('ひざ') >= 0) {\n            link.limitation = new Vector3(1.0, 0.0, 0.0)\n          }\n\n          param.links.push(link)\n        }\n\n        iks.push(param)\n      }\n    } else {\n      for (let i = 0; i < data.metadata.boneCount; i++) {\n        const ik = data.bones[i].ik\n\n        if (ik === undefined) continue\n\n        const param = {\n          target: i,\n          effector: ik.effector,\n          iteration: ik.iteration,\n          maxAngle: ik.maxAngle,\n          links: [],\n        }\n\n        for (let j = 0, jl = ik.links.length; j < jl; j++) {\n          const link = {}\n          link.index = ik.links[j].index\n          link.enabled = true\n\n          if (ik.links[j].angleLimitation === 1) {\n            // Revert if rotationMin/Max doesn't work well\n            // link.limitation = new Vector3( 1.0, 0.0, 0.0 );\n\n            const rotationMin = ik.links[j].lowerLimitationAngle\n            const rotationMax = ik.links[j].upperLimitationAngle\n\n            // Convert Left to Right coordinate by myself because\n            // MMDParser doesn't convert. It's a MMDParser's bug\n\n            const tmp1 = -rotationMax[0]\n            const tmp2 = -rotationMax[1]\n            rotationMax[0] = -rotationMin[0]\n            rotationMax[1] = -rotationMin[1]\n            rotationMin[0] = tmp1\n            rotationMin[1] = tmp2\n\n            link.rotationMin = new Vector3().fromArray(rotationMin)\n            link.rotationMax = new Vector3().fromArray(rotationMax)\n          }\n\n          param.links.push(link)\n        }\n\n        iks.push(param)\n\n        // Save the reference even from bone data for efficiently\n        // simulating PMX animation system\n        bones[i].ik = param\n      }\n    }\n\n    // grants\n\n    if (data.metadata.format === 'pmx') {\n      // bone index -> grant entry map\n      const grantEntryMap = {}\n\n      for (let i = 0; i < data.metadata.boneCount; i++) {\n        const boneData = data.bones[i]\n        const grant = boneData.grant\n\n        if (grant === undefined) continue\n\n        const param = {\n          index: i,\n          parentIndex: grant.parentIndex,\n          ratio: grant.ratio,\n          isLocal: grant.isLocal,\n          affectRotation: grant.affectRotation,\n          affectPosition: grant.affectPosition,\n          transformationClass: boneData.transformationClass,\n        }\n\n        grantEntryMap[i] = { parent: null, children: [], param: param, visited: false }\n      }\n\n      const rootEntry = { parent: null, children: [], param: null, visited: false }\n\n      // Build a tree representing grant hierarchy\n\n      for (const boneIndex in grantEntryMap) {\n        const grantEntry = grantEntryMap[boneIndex]\n        const parentGrantEntry = grantEntryMap[grantEntry.parentIndex] || rootEntry\n\n        grantEntry.parent = parentGrantEntry\n        parentGrantEntry.children.push(grantEntry)\n      }\n\n      // Sort grant parameters from parents to children because\n      // grant uses parent's transform that parent's grant is already applied\n      // so grant should be applied in order from parents to children\n\n      function traverse(entry) {\n        if (entry.param) {\n          grants.push(entry.param)\n\n          // Save the reference even from bone data for efficiently\n          // simulating PMX animation system\n          bones[entry.param.index].grant = entry.param\n        }\n\n        entry.visited = true\n\n        for (let i = 0, il = entry.children.length; i < il; i++) {\n          const child = entry.children[i]\n\n          // Cut off a loop if exists. (Is a grant loop invalid?)\n          if (!child.visited) traverse(child)\n        }\n      }\n\n      traverse(rootEntry)\n    }\n\n    // morph\n\n    function updateAttributes(attribute, morph, ratio) {\n      for (let i = 0; i < morph.elementCount; i++) {\n        const element = morph.elements[i]\n\n        let index\n\n        if (data.metadata.format === 'pmd') {\n          index = data.morphs[0].elements[element.index].index\n        } else {\n          index = element.index\n        }\n\n        attribute.array[index * 3 + 0] += element.position[0] * ratio\n        attribute.array[index * 3 + 1] += element.position[1] * ratio\n        attribute.array[index * 3 + 2] += element.position[2] * ratio\n      }\n    }\n\n    for (let i = 0; i < data.metadata.morphCount; i++) {\n      const morph = data.morphs[i]\n      const params = { name: morph.name }\n\n      const attribute = new Float32BufferAttribute(data.metadata.vertexCount * 3, 3)\n      attribute.name = morph.name\n\n      for (let j = 0; j < data.metadata.vertexCount * 3; j++) {\n        attribute.array[j] = positions[j]\n      }\n\n      if (data.metadata.format === 'pmd') {\n        if (i !== 0) {\n          updateAttributes(attribute, morph, 1.0)\n        }\n      } else {\n        if (morph.type === 0) {\n          // group\n\n          for (let j = 0; j < morph.elementCount; j++) {\n            const morph2 = data.morphs[morph.elements[j].index]\n            const ratio = morph.elements[j].ratio\n\n            if (morph2.type === 1) {\n              updateAttributes(attribute, morph2, ratio)\n            } else {\n              // TODO: implement\n            }\n          }\n        } else if (morph.type === 1) {\n          // vertex\n\n          updateAttributes(attribute, morph, 1.0)\n        } else if (morph.type === 2) {\n          // bone\n          // TODO: implement\n        } else if (morph.type === 3) {\n          // uv\n          // TODO: implement\n        } else if (morph.type === 4) {\n          // additional uv1\n          // TODO: implement\n        } else if (morph.type === 5) {\n          // additional uv2\n          // TODO: implement\n        } else if (morph.type === 6) {\n          // additional uv3\n          // TODO: implement\n        } else if (morph.type === 7) {\n          // additional uv4\n          // TODO: implement\n        } else if (morph.type === 8) {\n          // material\n          // TODO: implement\n        }\n      }\n\n      morphTargets.push(params)\n      morphPositions.push(attribute)\n    }\n\n    // rigid bodies from rigidBodies field.\n\n    for (let i = 0; i < data.metadata.rigidBodyCount; i++) {\n      const rigidBody = data.rigidBodies[i]\n      const params = {}\n\n      for (const key in rigidBody) {\n        params[key] = rigidBody[key]\n      }\n\n      /*\n       * RigidBody position parameter in PMX seems global position\n       * while the one in PMD seems offset from corresponding bone.\n       * So unify being offset.\n       */\n      if (data.metadata.format === 'pmx') {\n        if (params.boneIndex !== -1) {\n          const bone = data.bones[params.boneIndex]\n          params.position[0] -= bone.position[0]\n          params.position[1] -= bone.position[1]\n          params.position[2] -= bone.position[2]\n        }\n      }\n\n      rigidBodies.push(params)\n    }\n\n    // constraints from constraints field.\n\n    for (let i = 0; i < data.metadata.constraintCount; i++) {\n      const constraint = data.constraints[i]\n      const params = {}\n\n      for (const key in constraint) {\n        params[key] = constraint[key]\n      }\n\n      const bodyA = rigidBodies[params.rigidBodyIndex1]\n      const bodyB = rigidBodies[params.rigidBodyIndex2]\n\n      // Refer to http://www20.atpages.jp/katwat/wp/?p=4135\n      if (bodyA.type !== 0 && bodyB.type === 2) {\n        if (\n          bodyA.boneIndex !== -1 &&\n          bodyB.boneIndex !== -1 &&\n          data.bones[bodyB.boneIndex].parentIndex === bodyA.boneIndex\n        ) {\n          bodyB.type = 1\n        }\n      }\n\n      constraints.push(params)\n    }\n\n    // build BufferGeometry.\n\n    const geometry = new BufferGeometry()\n\n    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    geometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n    geometry.setAttribute('skinIndex', new Uint16BufferAttribute(skinIndices, 4))\n    geometry.setAttribute('skinWeight', new Float32BufferAttribute(skinWeights, 4))\n    geometry.setIndex(indices)\n\n    for (let i = 0, il = groups.length; i < il; i++) {\n      geometry.addGroup(groups[i].offset, groups[i].count, i)\n    }\n\n    geometry.bones = bones\n\n    geometry.morphTargets = morphTargets\n    geometry.morphAttributes.position = morphPositions\n    geometry.morphTargetsRelative = false\n\n    geometry.userData.MMD = {\n      bones: bones,\n      iks: iks,\n      grants: grants,\n      rigidBodies: rigidBodies,\n      constraints: constraints,\n      format: data.metadata.format,\n    }\n\n    geometry.computeBoundingSphere()\n\n    return geometry\n  }\n}\n\n//\n\n/**\n * @param {THREE.LoadingManager} manager\n */\nclass MaterialBuilder {\n  constructor(manager) {\n    this.manager = manager\n\n    this.textureLoader = new TextureLoader(this.manager)\n    this.tgaLoader = null // lazy generation\n\n    this.crossOrigin = 'anonymous'\n    this.resourcePath = undefined\n  }\n\n  /**\n   * @param {string} crossOrigin\n   * @return {MaterialBuilder}\n   */\n  setCrossOrigin(crossOrigin) {\n    this.crossOrigin = crossOrigin\n    return this\n  }\n\n  /**\n   * @param {string} resourcePath\n   * @return {MaterialBuilder}\n   */\n  setResourcePath(resourcePath) {\n    this.resourcePath = resourcePath\n    return this\n  }\n\n  /**\n   * @param {Object} data - parsed PMD/PMX data\n   * @param {BufferGeometry} geometry - some properties are dependend on geometry\n   * @param {function} onProgress\n   * @param {function} onError\n   * @return {Array<MeshToonMaterial>}\n   */\n  build(data, geometry /*, onProgress, onError */) {\n    const materials = []\n\n    const textures = {}\n\n    this.textureLoader.setCrossOrigin(this.crossOrigin)\n\n    // materials\n\n    for (let i = 0; i < data.metadata.materialCount; i++) {\n      const material = data.materials[i]\n\n      const params = { userData: {} }\n\n      if (material.name !== undefined) params.name = material.name\n\n      /*\n       * Color\n       *\n       * MMD         MeshToonMaterial\n       * diffuse  -  color\n       * ambient  -  emissive * a\n       *               (a = 1.0 without map texture or 0.2 with map texture)\n       *\n       * MeshToonMaterial doesn't have ambient. Set it to emissive instead.\n       * It'll be too bright if material has map texture so using coef 0.2.\n       */\n      params.color = new Color().fromArray(material.diffuse)\n      params.opacity = material.diffuse[3]\n      params.emissive = new Color().fromArray(material.ambient)\n      params.transparent = params.opacity !== 1.0\n\n      //\n\n      params.skinning = geometry.bones.length > 0 ? true : false\n      params.morphTargets = geometry.morphTargets.length > 0 ? true : false\n      params.fog = true\n\n      // blend\n\n      params.blending = CustomBlending\n      params.blendSrc = SrcAlphaFactor\n      params.blendDst = OneMinusSrcAlphaFactor\n      params.blendSrcAlpha = SrcAlphaFactor\n      params.blendDstAlpha = DstAlphaFactor\n\n      // side\n\n      if (data.metadata.format === 'pmx' && (material.flag & 0x1) === 1) {\n        params.side = DoubleSide\n      } else {\n        params.side = params.opacity === 1.0 ? FrontSide : DoubleSide\n      }\n\n      if (data.metadata.format === 'pmd') {\n        // map, envMap\n\n        if (material.fileName) {\n          const fileName = material.fileName\n          const fileNames = fileName.split('*')\n\n          // fileNames[ 0 ]: mapFileName\n          // fileNames[ 1 ]: envMapFileName( optional )\n\n          params.map = this._loadTexture(fileNames[0], textures)\n\n          if (fileNames.length > 1) {\n            const extension = fileNames[1].slice(-4).toLowerCase()\n\n            params.envMap = this._loadTexture(fileNames[1], textures)\n\n            params.combine = extension === '.sph' ? MultiplyOperation : AddOperation\n          }\n        }\n\n        // gradientMap\n\n        const toonFileName = material.toonIndex === -1 ? 'toon00.bmp' : data.toonTextures[material.toonIndex].fileName\n\n        params.gradientMap = this._loadTexture(toonFileName, textures, {\n          isToonTexture: true,\n          isDefaultToonTexture: this._isDefaultToonTexture(toonFileName),\n        })\n\n        // parameters for OutlineEffect\n\n        params.userData.outlineParameters = {\n          thickness: material.edgeFlag === 1 ? 0.003 : 0.0,\n          color: [0, 0, 0],\n          alpha: 1.0,\n          visible: material.edgeFlag === 1,\n        }\n      } else {\n        // map\n\n        if (material.textureIndex !== -1) {\n          params.map = this._loadTexture(data.textures[material.textureIndex], textures)\n        }\n\n        // envMap TODO: support m.envFlag === 3\n\n        if (material.envTextureIndex !== -1 && (material.envFlag === 1 || material.envFlag == 2)) {\n          params.envMap = this._loadTexture(data.textures[material.envTextureIndex], textures)\n\n          params.combine = material.envFlag === 1 ? MultiplyOperation : AddOperation\n        }\n\n        // gradientMap\n\n        let toonFileName, isDefaultToon\n\n        if (material.toonIndex === -1 || material.toonFlag !== 0) {\n          toonFileName = 'toon' + ('0' + (material.toonIndex + 1)).slice(-2) + '.bmp'\n          isDefaultToon = true\n        } else {\n          toonFileName = data.textures[material.toonIndex]\n          isDefaultToon = false\n        }\n\n        params.gradientMap = this._loadTexture(toonFileName, textures, {\n          isToonTexture: true,\n          isDefaultToonTexture: isDefaultToon,\n        })\n\n        // parameters for OutlineEffect\n        params.userData.outlineParameters = {\n          thickness: material.edgeSize / 300, // TODO: better calculation?\n          color: material.edgeColor.slice(0, 3),\n          alpha: material.edgeColor[3],\n          visible: (material.flag & 0x10) !== 0 && material.edgeSize > 0.0,\n        }\n      }\n\n      if (params.map !== undefined) {\n        if (!params.transparent) {\n          this._checkImageTransparency(params.map, geometry, i)\n        }\n\n        params.emissive.multiplyScalar(0.2)\n      }\n\n      materials.push(new MeshToonMaterial(params))\n    }\n\n    if (data.metadata.format === 'pmx') {\n      // set transparent true if alpha morph is defined.\n\n      function checkAlphaMorph(elements, materials) {\n        for (let i = 0, il = elements.length; i < il; i++) {\n          const element = elements[i]\n\n          if (element.index === -1) continue\n\n          const material = materials[element.index]\n\n          if (material.opacity !== element.diffuse[3]) {\n            material.transparent = true\n          }\n        }\n      }\n\n      for (let i = 0, il = data.morphs.length; i < il; i++) {\n        const morph = data.morphs[i]\n        const elements = morph.elements\n\n        if (morph.type === 0) {\n          for (let j = 0, jl = elements.length; j < jl; j++) {\n            const morph2 = data.morphs[elements[j].index]\n\n            if (morph2.type !== 8) continue\n\n            checkAlphaMorph(morph2.elements, materials)\n          }\n        } else if (morph.type === 8) {\n          checkAlphaMorph(elements, materials)\n        }\n      }\n    }\n\n    return materials\n  }\n\n  // private methods\n\n  _getTGALoader() {\n    if (this.tgaLoader === null) {\n      if (TGALoader === undefined) {\n        throw new Error('THREE.MMDLoader: Import TGALoader')\n      }\n\n      this.tgaLoader = new TGALoader(this.manager)\n    }\n\n    return this.tgaLoader\n  }\n\n  _isDefaultToonTexture(name) {\n    if (name.length !== 10) return false\n\n    return /toon(10|0[0-9])\\.bmp/.test(name)\n  }\n\n  _loadTexture(filePath, textures, params, onProgress, onError) {\n    params = params || {}\n\n    const scope = this\n\n    let fullPath\n\n    if (params.isDefaultToonTexture === true) {\n      let index\n\n      try {\n        index = parseInt(filePath.match(/toon([0-9]{2})\\.bmp$/)[1])\n      } catch (e) {\n        console.warn(\n          'THREE.MMDLoader: ' +\n            filePath +\n            ' seems like a ' +\n            'not right default texture path. Using toon00.bmp instead.',\n        )\n\n        index = 0\n      }\n\n      fullPath = DEFAULT_TOON_TEXTURES[index]\n    } else {\n      fullPath = this.resourcePath + filePath\n    }\n\n    if (textures[fullPath] !== undefined) return textures[fullPath]\n\n    let loader = this.manager.getHandler(fullPath)\n\n    if (loader === null) {\n      loader = filePath.slice(-4).toLowerCase() === '.tga' ? this._getTGALoader() : this.textureLoader\n    }\n\n    const texture = loader.load(\n      fullPath,\n      function (t) {\n        // MMD toon texture is Axis-Y oriented\n        // but Three.js gradient map is Axis-X oriented.\n        // So here replaces the toon texture image with the rotated one.\n        if (params.isToonTexture === true) {\n          t.image = scope._getRotatedImage(t.image)\n\n          t.magFilter = NearestFilter\n          t.minFilter = NearestFilter\n        }\n\n        t.flipY = false\n        t.wrapS = RepeatWrapping\n        t.wrapT = RepeatWrapping\n\n        for (let i = 0; i < texture.readyCallbacks.length; i++) {\n          texture.readyCallbacks[i](texture)\n        }\n\n        delete texture.readyCallbacks\n      },\n      onProgress,\n      onError,\n    )\n\n    texture.readyCallbacks = []\n\n    textures[fullPath] = texture\n\n    return texture\n  }\n\n  _getRotatedImage(image) {\n    const canvas = document.createElement('canvas')\n    const context = canvas.getContext('2d')\n\n    const width = image.width\n    const height = image.height\n\n    canvas.width = width\n    canvas.height = height\n\n    context.clearRect(0, 0, width, height)\n    context.translate(width / 2.0, height / 2.0)\n    context.rotate(0.5 * Math.PI) // 90.0 * Math.PI / 180.0\n    context.translate(-width / 2.0, -height / 2.0)\n    context.drawImage(image, 0, 0)\n\n    return context.getImageData(0, 0, width, height)\n  }\n\n  // Check if the partial image area used by the texture is transparent.\n  _checkImageTransparency(map, geometry, groupIndex) {\n    map.readyCallbacks.push(function (texture) {\n      // Is there any efficient ways?\n      function createImageData(image) {\n        const canvas = document.createElement('canvas')\n        canvas.width = image.width\n        canvas.height = image.height\n\n        const context = canvas.getContext('2d')\n        context.drawImage(image, 0, 0)\n\n        return context.getImageData(0, 0, canvas.width, canvas.height)\n      }\n\n      function detectImageTransparency(image, uvs, indices) {\n        const width = image.width\n        const height = image.height\n        const data = image.data\n        const threshold = 253\n\n        if (data.length / (width * height) !== 4) return false\n\n        for (let i = 0; i < indices.length; i += 3) {\n          const centerUV = { x: 0.0, y: 0.0 }\n\n          for (let j = 0; j < 3; j++) {\n            const index = indices[i * 3 + j]\n            const uv = { x: uvs[index * 2 + 0], y: uvs[index * 2 + 1] }\n\n            if (getAlphaByUv(image, uv) < threshold) return true\n\n            centerUV.x += uv.x\n            centerUV.y += uv.y\n          }\n\n          centerUV.x /= 3\n          centerUV.y /= 3\n\n          if (getAlphaByUv(image, centerUV) < threshold) return true\n        }\n\n        return false\n      }\n\n      /*\n       * This method expects\n       *   texture.flipY = false\n       *   texture.wrapS = RepeatWrapping\n       *   texture.wrapT = RepeatWrapping\n       * TODO: more precise\n       */\n      function getAlphaByUv(image, uv) {\n        const width = image.width\n        const height = image.height\n\n        let x = Math.round(uv.x * width) % width\n        let y = Math.round(uv.y * height) % height\n\n        if (x < 0) x += width\n        if (y < 0) y += height\n\n        const index = y * width + x\n\n        return image.data[index * 4 + 3]\n      }\n\n      const imageData = texture.image.data !== undefined ? texture.image : createImageData(texture.image)\n\n      const group = geometry.groups[groupIndex]\n\n      if (\n        detectImageTransparency(\n          imageData,\n          geometry.attributes.uv.array,\n          geometry.index.array.slice(group.start, group.start + group.count),\n        )\n      ) {\n        map.transparent = true\n      }\n    })\n  }\n}\n\n//\n\nclass AnimationBuilder {\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @param {SkinnedMesh} mesh - tracks will be fitting to mesh\n   * @return {AnimationClip}\n   */\n  build(vmd, mesh) {\n    // combine skeletal and morph animations\n\n    const tracks = this.buildSkeletalAnimation(vmd, mesh).tracks\n    const tracks2 = this.buildMorphAnimation(vmd, mesh).tracks\n\n    for (let i = 0, il = tracks2.length; i < il; i++) {\n      tracks.push(tracks2[i])\n    }\n\n    return new AnimationClip('', -1, tracks)\n  }\n\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @param {SkinnedMesh} mesh - tracks will be fitting to mesh\n   * @return {AnimationClip}\n   */\n  buildSkeletalAnimation(vmd, mesh) {\n    function pushInterpolation(array, interpolation, index) {\n      array.push(interpolation[index + 0] / 127) // x1\n      array.push(interpolation[index + 8] / 127) // x2\n      array.push(interpolation[index + 4] / 127) // y1\n      array.push(interpolation[index + 12] / 127) // y2\n    }\n\n    const tracks = []\n\n    const motions = {}\n    const bones = mesh.skeleton.bones\n    const boneNameDictionary = {}\n\n    for (let i = 0, il = bones.length; i < il; i++) {\n      boneNameDictionary[bones[i].name] = true\n    }\n\n    for (let i = 0; i < vmd.metadata.motionCount; i++) {\n      const motion = vmd.motions[i]\n      const boneName = motion.boneName\n\n      if (boneNameDictionary[boneName] === undefined) continue\n\n      motions[boneName] = motions[boneName] || []\n      motions[boneName].push(motion)\n    }\n\n    for (const key in motions) {\n      const array = motions[key]\n\n      array.sort(function (a, b) {\n        return a.frameNum - b.frameNum\n      })\n\n      const times = []\n      const positions = []\n      const rotations = []\n      const pInterpolations = []\n      const rInterpolations = []\n\n      const basePosition = mesh.skeleton.getBoneByName(key).position.toArray()\n\n      for (let i = 0, il = array.length; i < il; i++) {\n        const time = array[i].frameNum / 30\n        const position = array[i].position\n        const rotation = array[i].rotation\n        const interpolation = array[i].interpolation\n\n        times.push(time)\n\n        for (let j = 0; j < 3; j++) positions.push(basePosition[j] + position[j])\n        for (let j = 0; j < 4; j++) rotations.push(rotation[j])\n        for (let j = 0; j < 3; j++) pushInterpolation(pInterpolations, interpolation, j)\n\n        pushInterpolation(rInterpolations, interpolation, 3)\n      }\n\n      const targetName = '.bones[' + key + ']'\n\n      tracks.push(this._createTrack(targetName + '.position', VectorKeyframeTrack, times, positions, pInterpolations))\n      tracks.push(\n        this._createTrack(targetName + '.quaternion', QuaternionKeyframeTrack, times, rotations, rInterpolations),\n      )\n    }\n\n    return new AnimationClip('', -1, tracks)\n  }\n\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @param {SkinnedMesh} mesh - tracks will be fitting to mesh\n   * @return {AnimationClip}\n   */\n  buildMorphAnimation(vmd, mesh) {\n    const tracks = []\n\n    const morphs = {}\n    const morphTargetDictionary = mesh.morphTargetDictionary\n\n    for (let i = 0; i < vmd.metadata.morphCount; i++) {\n      const morph = vmd.morphs[i]\n      const morphName = morph.morphName\n\n      if (morphTargetDictionary[morphName] === undefined) continue\n\n      morphs[morphName] = morphs[morphName] || []\n      morphs[morphName].push(morph)\n    }\n\n    for (const key in morphs) {\n      const array = morphs[key]\n\n      array.sort(function (a, b) {\n        return a.frameNum - b.frameNum\n      })\n\n      const times = []\n      const values = []\n\n      for (let i = 0, il = array.length; i < il; i++) {\n        times.push(array[i].frameNum / 30)\n        values.push(array[i].weight)\n      }\n\n      tracks.push(new NumberKeyframeTrack('.morphTargetInfluences[' + morphTargetDictionary[key] + ']', times, values))\n    }\n\n    return new AnimationClip('', -1, tracks)\n  }\n\n  /**\n   * @param {Object} vmd - parsed VMD data\n   * @return {AnimationClip}\n   */\n  buildCameraAnimation(vmd) {\n    function pushVector3(array, vec) {\n      array.push(vec.x)\n      array.push(vec.y)\n      array.push(vec.z)\n    }\n\n    function pushQuaternion(array, q) {\n      array.push(q.x)\n      array.push(q.y)\n      array.push(q.z)\n      array.push(q.w)\n    }\n\n    function pushInterpolation(array, interpolation, index) {\n      array.push(interpolation[index * 4 + 0] / 127) // x1\n      array.push(interpolation[index * 4 + 1] / 127) // x2\n      array.push(interpolation[index * 4 + 2] / 127) // y1\n      array.push(interpolation[index * 4 + 3] / 127) // y2\n    }\n\n    const cameras = vmd.cameras === undefined ? [] : vmd.cameras.slice()\n\n    cameras.sort(function (a, b) {\n      return a.frameNum - b.frameNum\n    })\n\n    const times = []\n    const centers = []\n    const quaternions = []\n    const positions = []\n    const fovs = []\n\n    const cInterpolations = []\n    const qInterpolations = []\n    const pInterpolations = []\n    const fInterpolations = []\n\n    const quaternion = new Quaternion()\n    const euler = new Euler()\n    const position = new Vector3()\n    const center = new Vector3()\n\n    for (let i = 0, il = cameras.length; i < il; i++) {\n      const motion = cameras[i]\n\n      const time = motion.frameNum / 30\n      const pos = motion.position\n      const rot = motion.rotation\n      const distance = motion.distance\n      const fov = motion.fov\n      const interpolation = motion.interpolation\n\n      times.push(time)\n\n      position.set(0, 0, -distance)\n      center.set(pos[0], pos[1], pos[2])\n\n      euler.set(-rot[0], -rot[1], -rot[2])\n      quaternion.setFromEuler(euler)\n\n      position.add(center)\n      position.applyQuaternion(quaternion)\n\n      pushVector3(centers, center)\n      pushQuaternion(quaternions, quaternion)\n      pushVector3(positions, position)\n\n      fovs.push(fov)\n\n      for (let j = 0; j < 3; j++) {\n        pushInterpolation(cInterpolations, interpolation, j)\n      }\n\n      pushInterpolation(qInterpolations, interpolation, 3)\n\n      // use the same parameter for x, y, z axis.\n      for (let j = 0; j < 3; j++) {\n        pushInterpolation(pInterpolations, interpolation, 4)\n      }\n\n      pushInterpolation(fInterpolations, interpolation, 5)\n    }\n\n    const tracks = []\n\n    // I expect an object whose name 'target' exists under THREE.Camera\n    tracks.push(this._createTrack('target.position', VectorKeyframeTrack, times, centers, cInterpolations))\n\n    tracks.push(this._createTrack('.quaternion', QuaternionKeyframeTrack, times, quaternions, qInterpolations))\n    tracks.push(this._createTrack('.position', VectorKeyframeTrack, times, positions, pInterpolations))\n    tracks.push(this._createTrack('.fov', NumberKeyframeTrack, times, fovs, fInterpolations))\n\n    return new AnimationClip('', -1, tracks)\n  }\n\n  // private method\n\n  _createTrack(node, typedKeyframeTrack, times, values, interpolations) {\n    /*\n     * optimizes here not to let KeyframeTrackPrototype optimize\n     * because KeyframeTrackPrototype optimizes times and values but\n     * doesn't optimize interpolations.\n     */\n    if (times.length > 2) {\n      times = times.slice()\n      values = values.slice()\n      interpolations = interpolations.slice()\n\n      const stride = values.length / times.length\n      const interpolateStride = interpolations.length / times.length\n\n      let index = 1\n\n      for (let aheadIndex = 2, endIndex = times.length; aheadIndex < endIndex; aheadIndex++) {\n        for (let i = 0; i < stride; i++) {\n          if (\n            values[index * stride + i] !== values[(index - 1) * stride + i] ||\n            values[index * stride + i] !== values[aheadIndex * stride + i]\n          ) {\n            index++\n            break\n          }\n        }\n\n        if (aheadIndex > index) {\n          times[index] = times[aheadIndex]\n\n          for (let i = 0; i < stride; i++) {\n            values[index * stride + i] = values[aheadIndex * stride + i]\n          }\n\n          for (let i = 0; i < interpolateStride; i++) {\n            interpolations[index * interpolateStride + i] = interpolations[aheadIndex * interpolateStride + i]\n          }\n        }\n      }\n\n      times.length = index + 1\n      values.length = (index + 1) * stride\n      interpolations.length = (index + 1) * interpolateStride\n    }\n\n    const track = new typedKeyframeTrack(node, times, values)\n\n    track.createInterpolant = function InterpolantFactoryMethodCubicBezier(result) {\n      return new CubicBezierInterpolation(\n        this.times,\n        this.values,\n        this.getValueSize(),\n        result,\n        new Float32Array(interpolations),\n      )\n    }\n\n    return track\n  }\n}\n\n// interpolation\n\nclass CubicBezierInterpolation extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer, params) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer)\n\n    this.interpolationParams = params\n  }\n\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer\n    const values = this.sampleValues\n    const stride = this.valueSize\n    const params = this.interpolationParams\n\n    const offset1 = i1 * stride\n    const offset0 = offset1 - stride\n\n    // No interpolation if next key frame is in one frame in 30fps.\n    // This is from MMD animation spec.\n    // '1.5' is for precision loss. times are Float32 in Three.js Animation system.\n    const weight1 = t1 - t0 < (1 / 30) * 1.5 ? 0.0 : (t - t0) / (t1 - t0)\n\n    if (stride === 4) {\n      // Quaternion\n\n      const x1 = params[i1 * 4 + 0]\n      const x2 = params[i1 * 4 + 1]\n      const y1 = params[i1 * 4 + 2]\n      const y2 = params[i1 * 4 + 3]\n\n      const ratio = this._calculate(x1, x2, y1, y2, weight1)\n\n      Quaternion.slerpFlat(result, 0, values, offset0, values, offset1, ratio)\n    } else if (stride === 3) {\n      // Vector3\n\n      for (let i = 0; i !== stride; ++i) {\n        const x1 = params[i1 * 12 + i * 4 + 0]\n        const x2 = params[i1 * 12 + i * 4 + 1]\n        const y1 = params[i1 * 12 + i * 4 + 2]\n        const y2 = params[i1 * 12 + i * 4 + 3]\n\n        const ratio = this._calculate(x1, x2, y1, y2, weight1)\n\n        result[i] = values[offset0 + i] * (1 - ratio) + values[offset1 + i] * ratio\n      }\n    } else {\n      // Number\n\n      const x1 = params[i1 * 4 + 0]\n      const x2 = params[i1 * 4 + 1]\n      const y1 = params[i1 * 4 + 2]\n      const y2 = params[i1 * 4 + 3]\n\n      const ratio = this._calculate(x1, x2, y1, y2, weight1)\n\n      result[0] = values[offset0] * (1 - ratio) + values[offset1] * ratio\n    }\n\n    return result\n  }\n\n  _calculate(x1, x2, y1, y2, x) {\n    /*\n     * Cubic Bezier curves\n     *   https://en.wikipedia.org/wiki/B%C3%A9zier_curve#Cubic_B.C3.A9zier_curves\n     *\n     * B(t) = ( 1 - t ) ^ 3 * P0\n     *      + 3 * ( 1 - t ) ^ 2 * t * P1\n     *      + 3 * ( 1 - t ) * t^2 * P2\n     *      + t ^ 3 * P3\n     *      ( 0 <= t <= 1 )\n     *\n     * MMD uses Cubic Bezier curves for bone and camera animation interpolation.\n     *   http://d.hatena.ne.jp/edvakf/20111016/1318716097\n     *\n     *    x = ( 1 - t ) ^ 3 * x0\n     *      + 3 * ( 1 - t ) ^ 2 * t * x1\n     *      + 3 * ( 1 - t ) * t^2 * x2\n     *      + t ^ 3 * x3\n     *    y = ( 1 - t ) ^ 3 * y0\n     *      + 3 * ( 1 - t ) ^ 2 * t * y1\n     *      + 3 * ( 1 - t ) * t^2 * y2\n     *      + t ^ 3 * y3\n     *      ( x0 = 0, y0 = 0 )\n     *      ( x3 = 1, y3 = 1 )\n     *      ( 0 <= t, x1, x2, y1, y2 <= 1 )\n     *\n     * Here solves this equation with Bisection method,\n     *   https://en.wikipedia.org/wiki/Bisection_method\n     * gets t, and then calculate y.\n     *\n     * f(t) = 3 * ( 1 - t ) ^ 2 * t * x1\n     *      + 3 * ( 1 - t ) * t^2 * x2\n     *      + t ^ 3 - x = 0\n     *\n     * (Another option: Newton's method\n     *    https://en.wikipedia.org/wiki/Newton%27s_method)\n     */\n\n    let c = 0.5\n    let t = c\n    let s = 1.0 - t\n    const loop = 15\n    const eps = 1e-5\n    const math = Math\n\n    let sst3, stt3, ttt\n\n    for (let i = 0; i < loop; i++) {\n      sst3 = 3.0 * s * s * t\n      stt3 = 3.0 * s * t * t\n      ttt = t * t * t\n\n      const ft = sst3 * x1 + stt3 * x2 + ttt - x\n\n      if (math.abs(ft) < eps) break\n\n      c /= 2.0\n\n      t += ft < 0 ? c : -c\n      s = 1.0 - t\n    }\n\n    return sst3 * y1 + stt3 * y2 + ttt\n  }\n}\n\nexport { MMDLoader }\n"], "mappings": ";;;AAmEA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKF,OAAO;IAEzC,KAAKG,MAAA,GAAS;IACd,KAAKC,WAAA,GAAc,IAAIC,WAAA,CAAY,KAAKL,OAAO;IAC/C,KAAKM,gBAAA,GAAmB,IAAIC,gBAAA,CAAkB;EAC/C;EAAA;AAAA;AAAA;AAAA;EAMDC,iBAAiBC,aAAA,EAAe;IAC9B,KAAKA,aAAA,GAAgBA,aAAA;IACrB,OAAO;EACR;EAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,OAAA,GAAU,KAAKX,WAAA,CAAYY,cAAA,CAAe,KAAKC,WAAW;IAIhE,IAAIC,YAAA;IAEJ,IAAI,KAAKA,YAAA,KAAiB,IAAI;MAC5BA,YAAA,GAAe,KAAKA,YAAA;IAC1B,WAAe,KAAKC,IAAA,KAAS,IAAI;MAC3BD,YAAA,GAAe,KAAKC,IAAA;IAC1B,OAAW;MACLD,YAAA,GAAeE,WAAA,CAAYC,cAAA,CAAeV,GAAG;IAC9C;IAED,MAAMW,cAAA,GAAiB,KAAKC,iBAAA,CAAkBZ,GAAG,EAAEa,WAAA,CAAa;IAGhE,IAAIF,cAAA,KAAmB,SAASA,cAAA,KAAmB,OAAO;MACxD,IAAIR,OAAA,EAASA,OAAA,CAAQ,IAAIW,KAAA,CAAM,oDAAoDH,cAAA,GAAiB,GAAG,CAAC;MAExG;IACD;IAED,KAAKA,cAAA,KAAmB,QAAQ,YAAY,SAAS,EACnDX,GAAA,EACA,UAAUe,IAAA,EAAM;MACdd,MAAA,CAAOG,OAAA,CAAQY,KAAA,CAAMD,IAAA,EAAMR,YAAA,EAAcL,UAAA,EAAYC,OAAO,CAAC;IAC9D,GACDD,UAAA,EACAC,OACD;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYDc,cAAcjB,GAAA,EAAKkB,MAAA,EAAQjB,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACtD,MAAMC,OAAA,GAAU,KAAKT,gBAAA;IAErB,KAAKwB,OAAA,CACHnB,GAAA,EACA,UAAUoB,GAAA,EAAK;MACbnB,MAAA,CAAOiB,MAAA,CAAOG,QAAA,GAAWjB,OAAA,CAAQkB,oBAAA,CAAqBF,GAAG,IAAIhB,OAAA,CAAQY,KAAA,CAAMI,GAAA,EAAKF,MAAM,CAAC;IACxF,GACDhB,UAAA,EACAC,OACD;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAaDoB,kBAAkBC,QAAA,EAAUC,MAAA,EAAQxB,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IAC/D,MAAMuB,KAAA,GAAQ;IAEd,KAAK3B,IAAA,CACHyB,QAAA,EACA,UAAUG,IAAA,EAAM;MACdD,KAAA,CAAMT,aAAA,CACJQ,MAAA,EACAE,IAAA,EACA,UAAUC,SAAA,EAAW;QACnB3B,MAAA,CAAO;UACL0B,IAAA;UACAC;QACd,CAAa;MACF,GACD1B,UAAA,EACAC,OACD;IACF,GACDD,UAAA,EACAC,OACD;EACF;EAAA;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYD0B,QAAQ7B,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACxC,MAAMX,MAAA,GAAS,KAAKsC,UAAA,CAAY;IAEhC,KAAKxC,MAAA,CACFyC,WAAA,CAAY,MAAS,EACrBC,OAAA,CAAQ,KAAKxB,IAAI,EACjByB,eAAA,CAAgB,aAAa,EAC7BC,gBAAA,CAAiB,KAAKC,aAAa,EACnCC,kBAAA,CAAmB,KAAKC,eAAe,EACvCtC,IAAA,CACCC,GAAA,EACA,UAAUsC,MAAA,EAAQ;MAChBrC,MAAA,CAAOT,MAAA,CAAO+C,QAAA,CAASD,MAAA,EAAQ,IAAI,CAAC;IACrC,GACDpC,UAAA,EACAC,OACD;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDqC,QAAQxC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACxC,MAAMX,MAAA,GAAS,KAAKsC,UAAA,CAAY;IAEhC,KAAKxC,MAAA,CACFyC,WAAA,CAAY,MAAS,EACrBC,OAAA,CAAQ,KAAKxB,IAAI,EACjByB,eAAA,CAAgB,aAAa,EAC7BC,gBAAA,CAAiB,KAAKC,aAAa,EACnCC,kBAAA,CAAmB,KAAKC,eAAe,EACvCtC,IAAA,CACCC,GAAA,EACA,UAAUsC,MAAA,EAAQ;MAChBrC,MAAA,CAAOT,MAAA,CAAOiD,QAAA,CAASH,MAAA,EAAQ,IAAI,CAAC;IACrC,GACDpC,UAAA,EACAC,OACD;EACJ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWDgB,QAAQnB,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACxC,MAAMuC,IAAA,GAAOC,KAAA,CAAMC,OAAA,CAAQ5C,GAAG,IAAIA,GAAA,GAAM,CAACA,GAAG;IAE5C,MAAM6C,IAAA,GAAO,EAAE;IACf,MAAMC,MAAA,GAASJ,IAAA,CAAKK,MAAA;IAEpB,MAAMvD,MAAA,GAAS,KAAKsC,UAAA,CAAY;IAEhC,KAAKxC,MAAA,CACFyC,WAAA,CAAY,MAAS,EACrBC,OAAA,CAAQ,KAAKlC,aAAa,EAC1BmC,eAAA,CAAgB,aAAa,EAC7BC,gBAAA,CAAiB,KAAKC,aAAa,EACnCC,kBAAA,CAAmB,KAAKC,eAAe;IAE1C,SAASW,CAAA,GAAI,GAAGC,EAAA,GAAKP,IAAA,CAAKK,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC7C,KAAK1D,MAAA,CAAOS,IAAA,CACV2C,IAAA,CAAKM,CAAC,GACN,UAAUV,MAAA,EAAQ;QAChBO,IAAA,CAAKK,IAAA,CAAK1D,MAAA,CAAO2D,QAAA,CAASb,MAAA,EAAQ,IAAI,CAAC;QAEvC,IAAIO,IAAA,CAAKE,MAAA,KAAWD,MAAA,EAAQ7C,MAAA,CAAOT,MAAA,CAAO4D,SAAA,CAAUP,IAAI,CAAC;MAC1D,GACD3C,UAAA,EACAC,OACD;IACF;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWDkD,QAAQrD,GAAA,EAAKsD,SAAA,EAAWrD,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACnD,MAAMX,MAAA,GAAS,KAAKsC,UAAA,CAAY;IAEhC,KAAKxC,MAAA,CACFyC,WAAA,CAAYuB,SAAA,GAAY,SAAY,+BAA+B,EACnEtB,OAAA,CAAQ,KAAKlC,aAAa,EAC1BmC,eAAA,CAAgB,MAAM,EACtBC,gBAAA,CAAiB,KAAKC,aAAa,EACnCC,kBAAA,CAAmB,KAAKC,eAAe,EACvCtC,IAAA,CACCC,GAAA,EACA,UAAUuD,IAAA,EAAM;MACdtD,MAAA,CAAOT,MAAA,CAAOgE,QAAA,CAASD,IAAA,EAAM,IAAI,CAAC;IACnC,GACDrD,UAAA,EACAC,OACD;EACJ;EAAA;EAIDS,kBAAkBZ,GAAA,EAAK;IACrB,MAAMyD,KAAA,GAAQzD,GAAA,CAAI0D,WAAA,CAAY,GAAG;IACjC,OAAOD,KAAA,GAAQ,IAAI,KAAKzD,GAAA,CAAI2D,KAAA,CAAMF,KAAA,GAAQ,CAAC;EAC5C;EAED3B,WAAA,EAAa;IACX,IAAI,KAAKtC,MAAA,KAAW,MAAM;MACxB,KAAKA,MAAA,GAAS,IAAIoE,MAAA,CAAQ;IAC3B;IAED,OAAO,KAAKpE,MAAA;EACb;AACH;AASA,MAAMqE,qBAAA,GAAwB,CAC5B,sKACA,kLACA,kLACA,kLACA,sLACA,8gBACA,k1BACA,sKACA,sKACA,sKACA,qKACF;AAOA,MAAMnE,WAAA,CAAY;EAChBN,YAAYC,OAAA,EAAS;IACnB,KAAKiB,WAAA,GAAc;IACnB,KAAKwD,eAAA,GAAkB,IAAIC,eAAA,CAAiB;IAC5C,KAAKC,eAAA,GAAkB,IAAIC,eAAA,CAAgB5E,OAAO;EACnD;EAAA;AAAA;AAAA;AAAA;EAMDgB,eAAeC,WAAA,EAAa;IAC1B,KAAKA,WAAA,GAAcA,WAAA;IACnB,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDU,MAAMD,IAAA,EAAMR,YAAA,EAAcL,UAAA,EAAYC,OAAA,EAAS;IAC7C,MAAM+D,QAAA,GAAW,KAAKJ,eAAA,CAAgB9C,KAAA,CAAMD,IAAI;IAChD,MAAMoD,QAAA,GAAW,KAAKH,eAAA,CACnB3D,cAAA,CAAe,KAAKC,WAAW,EAC/B8D,eAAA,CAAgB7D,YAAY,EAC5BS,KAAA,CAAMD,IAAA,EAAMmD,QAAA,EAAUhE,UAAA,EAAYC,OAAO;IAE5C,MAAMwB,IAAA,GAAO,IAAI0C,WAAA,CAAYH,QAAA,EAAUC,QAAQ;IAE/C,MAAMG,QAAA,GAAW,IAAIC,QAAA,CAASC,SAAA,CAAU7C,IAAI,CAAC;IAC7CA,IAAA,CAAK8C,IAAA,CAAKH,QAAQ;IAIlB,OAAO3C,IAAA;EACR;AACH;AAIA,SAAS6C,UAAU7C,IAAA,EAAM;EACvB,MAAMuC,QAAA,GAAWvC,IAAA,CAAKuC,QAAA;EAEtB,MAAMQ,KAAA,GAAQ,EAAE;EAEhB,IAAIR,QAAA,IAAYA,QAAA,CAASQ,KAAA,KAAU,QAAW;IAG5C,SAAS1B,CAAA,GAAI,GAAGC,EAAA,GAAKiB,QAAA,CAASQ,KAAA,CAAM3B,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACvD,MAAM2B,KAAA,GAAQT,QAAA,CAASQ,KAAA,CAAM1B,CAAC;MAI9B,MAAM4B,IAAA,GAAO,IAAIC,IAAA,CAAM;MACvBH,KAAA,CAAMxB,IAAA,CAAK0B,IAAI;MAIfA,IAAA,CAAKE,IAAA,GAAOH,KAAA,CAAMG,IAAA;MAClBF,IAAA,CAAKG,QAAA,CAASC,SAAA,CAAUL,KAAA,CAAMM,GAAG;MACjCL,IAAA,CAAKM,UAAA,CAAWF,SAAA,CAAUL,KAAA,CAAMQ,IAAI;MACpC,IAAIR,KAAA,CAAMS,GAAA,KAAQ,QAAWR,IAAA,CAAKS,KAAA,CAAML,SAAA,CAAUL,KAAA,CAAMS,GAAG;IAC5D;IAID,SAASpC,CAAA,GAAI,GAAGC,EAAA,GAAKiB,QAAA,CAASQ,KAAA,CAAM3B,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MACvD,MAAM2B,KAAA,GAAQT,QAAA,CAASQ,KAAA,CAAM1B,CAAC;MAE9B,IAAI2B,KAAA,CAAMW,MAAA,KAAW,MAAMX,KAAA,CAAMW,MAAA,KAAW,QAAQZ,KAAA,CAAMC,KAAA,CAAMW,MAAM,MAAM,QAAW;QAGrFZ,KAAA,CAAMC,KAAA,CAAMW,MAAM,EAAEC,GAAA,CAAIb,KAAA,CAAM1B,CAAC,CAAC;MACxC,OAAa;QAGLrB,IAAA,CAAK4D,GAAA,CAAIb,KAAA,CAAM1B,CAAC,CAAC;MAClB;IACF;EACF;EAKDrB,IAAA,CAAK6D,iBAAA,CAAkB,IAAI;EAE3B,OAAOd,KAAA;AACT;AAIA,MAAMX,eAAA,CAAgB;EAAA;AAAA;AAAA;AAAA;EAKpB/C,MAAMD,IAAA,EAAM;IAEV,MAAM0E,SAAA,GAAY,EAAE;IACpB,MAAMC,GAAA,GAAM,EAAE;IACd,MAAMC,OAAA,GAAU,EAAE;IAElB,MAAMC,OAAA,GAAU,EAAE;IAElB,MAAMC,MAAA,GAAS,EAAE;IAEjB,MAAMnB,KAAA,GAAQ,EAAE;IAChB,MAAMoB,WAAA,GAAc,EAAE;IACtB,MAAMC,WAAA,GAAc,EAAE;IAEtB,MAAMC,YAAA,GAAe,EAAE;IACvB,MAAMC,cAAA,GAAiB,EAAE;IAEzB,MAAMC,GAAA,GAAM,EAAE;IACd,MAAMC,MAAA,GAAS,EAAE;IAEjB,MAAMC,WAAA,GAAc,EAAE;IACtB,MAAMC,WAAA,GAAc,EAAE;IAGtB,IAAIC,MAAA,GAAS;IACb,MAAMC,aAAA,GAAgB,CAAE;IAIxB,SAASvD,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASC,WAAA,EAAazD,CAAA,IAAK;MAClD,MAAM0D,CAAA,GAAI3F,IAAA,CAAK4F,QAAA,CAAS3D,CAAC;MAEzB,SAAS4D,CAAA,GAAI,GAAGC,EAAA,GAAKH,CAAA,CAAE3B,QAAA,CAAShC,MAAA,EAAQ6D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnDnB,SAAA,CAAUvC,IAAA,CAAKwD,CAAA,CAAE3B,QAAA,CAAS6B,CAAC,CAAC;MAC7B;MAED,SAASA,CAAA,GAAI,GAAGC,EAAA,GAAKH,CAAA,CAAEI,MAAA,CAAO/D,MAAA,EAAQ6D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACjDjB,OAAA,CAAQzC,IAAA,CAAKwD,CAAA,CAAEI,MAAA,CAAOF,CAAC,CAAC;MACzB;MAED,SAASA,CAAA,GAAI,GAAGC,EAAA,GAAKH,CAAA,CAAEK,EAAA,CAAGhE,MAAA,EAAQ6D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC7ClB,GAAA,CAAIxC,IAAA,CAAKwD,CAAA,CAAEK,EAAA,CAAGH,CAAC,CAAC;MACjB;MAED,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1Bd,WAAA,CAAY5C,IAAA,CAAKwD,CAAA,CAAEZ,WAAA,CAAY/C,MAAA,GAAS,KAAK6D,CAAA,GAAIF,CAAA,CAAEZ,WAAA,CAAYc,CAAC,IAAI,CAAG;MACxE;MAED,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1Bb,WAAA,CAAY7C,IAAA,CAAKwD,CAAA,CAAEX,WAAA,CAAYhD,MAAA,GAAS,KAAK6D,CAAA,GAAIF,CAAA,CAAEX,WAAA,CAAYa,CAAC,IAAI,CAAG;MACxE;IACF;IAID,SAAS5D,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASQ,SAAA,EAAWhE,CAAA,IAAK;MAChD,MAAMiE,IAAA,GAAOlG,IAAA,CAAKmG,KAAA,CAAMlE,CAAC;MAEzB,SAAS4D,CAAA,GAAI,GAAGC,EAAA,GAAKI,IAAA,CAAKrB,OAAA,CAAQ7C,MAAA,EAAQ6D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACrDhB,OAAA,CAAQ1C,IAAA,CAAK+D,IAAA,CAAKrB,OAAA,CAAQgB,CAAC,CAAC;MAC7B;IACF;IAID,SAAS5D,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASW,aAAA,EAAenE,CAAA,IAAK;MACpD,MAAMmB,QAAA,GAAWpD,IAAA,CAAKqG,SAAA,CAAUpE,CAAC;MAEjC6C,MAAA,CAAO3C,IAAA,CAAK;QACVoD,MAAA,EAAQA,MAAA,GAAS;QACjBe,KAAA,EAAOlD,QAAA,CAAS6C,SAAA,GAAY;MACpC,CAAO;MAEDV,MAAA,IAAUnC,QAAA,CAAS6C,SAAA;IACpB;IAID,SAAShE,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASc,cAAA,EAAgBtE,CAAA,IAAK;MACrD,MAAMuE,IAAA,GAAOxG,IAAA,CAAKqF,WAAA,CAAYpD,CAAC;MAC/B,IAAIwE,KAAA,GAAQjB,aAAA,CAAcgB,IAAA,CAAKE,SAAS;MAGxCD,KAAA,GAAQA,KAAA,KAAU,SAAYD,IAAA,CAAKG,IAAA,GAAOC,IAAA,CAAKC,GAAA,CAAIL,IAAA,CAAKG,IAAA,EAAMF,KAAK;MAEnEjB,aAAA,CAAcgB,IAAA,CAAKE,SAAS,IAAID,KAAA;IACjC;IAED,SAASxE,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASqB,SAAA,EAAW7E,CAAA,IAAK;MAChD,MAAM8E,QAAA,GAAW/G,IAAA,CAAK2D,KAAA,CAAM1B,CAAC;MAE7B,MAAM4B,IAAA,GAAO;QACXnB,KAAA,EAAOT,CAAA;QACP+E,mBAAA,EAAqBD,QAAA,CAASC,mBAAA;QAC9BzC,MAAA,EAAQwC,QAAA,CAASE,WAAA;QACjBlD,IAAA,EAAMgD,QAAA,CAAShD,IAAA;QACfG,GAAA,EAAK6C,QAAA,CAAS/C,QAAA,CAASpB,KAAA,CAAM,GAAG,CAAC;QACjCwB,IAAA,EAAM,CAAC,GAAG,GAAG,GAAG,CAAC;QACjBC,GAAA,EAAK,CAAC,GAAG,GAAG,CAAC;QACb6C,aAAA,EAAe1B,aAAA,CAAcvD,CAAC,MAAM,SAAYuD,aAAA,CAAcvD,CAAC,IAAI;MACpE;MAED,IAAI4B,IAAA,CAAKU,MAAA,KAAW,IAAI;QACtBV,IAAA,CAAKK,GAAA,CAAI,CAAC,KAAKlE,IAAA,CAAK2D,KAAA,CAAME,IAAA,CAAKU,MAAM,EAAEP,QAAA,CAAS,CAAC;QACjDH,IAAA,CAAKK,GAAA,CAAI,CAAC,KAAKlE,IAAA,CAAK2D,KAAA,CAAME,IAAA,CAAKU,MAAM,EAAEP,QAAA,CAAS,CAAC;QACjDH,IAAA,CAAKK,GAAA,CAAI,CAAC,KAAKlE,IAAA,CAAK2D,KAAA,CAAME,IAAA,CAAKU,MAAM,EAAEP,QAAA,CAAS,CAAC;MAClD;MAEDL,KAAA,CAAMxB,IAAA,CAAK0B,IAAI;IAChB;IAKD,IAAI7D,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,OAAO;MAClC,SAASlF,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAAS2B,OAAA,EAASnF,CAAA,IAAK;QAC9C,MAAMoF,EAAA,GAAKrH,IAAA,CAAKmF,GAAA,CAAIlD,CAAC;QAErB,MAAMqF,KAAA,GAAQ;UACZC,MAAA,EAAQF,EAAA,CAAGE,MAAA;UACXC,QAAA,EAAUH,EAAA,CAAGG,QAAA;UACbC,SAAA,EAAWJ,EAAA,CAAGI,SAAA;UACdC,QAAA,EAAUL,EAAA,CAAGK,QAAA,GAAW;UACxBC,KAAA,EAAO;QACR;QAED,SAAS9B,CAAA,GAAI,GAAGC,EAAA,GAAKuB,EAAA,CAAGM,KAAA,CAAM3F,MAAA,EAAQ6D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACjD,MAAM+B,IAAA,GAAO,CAAE;UACfA,IAAA,CAAKlF,KAAA,GAAQ2E,EAAA,CAAGM,KAAA,CAAM9B,CAAC,EAAEnD,KAAA;UACzBkF,IAAA,CAAKC,OAAA,GAAU;UAEf,IAAI7H,IAAA,CAAK2D,KAAA,CAAMiE,IAAA,CAAKlF,KAAK,EAAEqB,IAAA,CAAK+D,OAAA,CAAQ,IAAI,KAAK,GAAG;YAClDF,IAAA,CAAKG,UAAA,GAAa,IAAIC,OAAA,CAAQ,GAAK,GAAK,CAAG;UAC5C;UAEDV,KAAA,CAAMK,KAAA,CAAMxF,IAAA,CAAKyF,IAAI;QACtB;QAEDzC,GAAA,CAAIhD,IAAA,CAAKmF,KAAK;MACf;IACP,OAAW;MACL,SAASrF,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASqB,SAAA,EAAW7E,CAAA,IAAK;QAChD,MAAMoF,EAAA,GAAKrH,IAAA,CAAK2D,KAAA,CAAM1B,CAAC,EAAEoF,EAAA;QAEzB,IAAIA,EAAA,KAAO,QAAW;QAEtB,MAAMC,KAAA,GAAQ;UACZC,MAAA,EAAQtF,CAAA;UACRuF,QAAA,EAAUH,EAAA,CAAGG,QAAA;UACbC,SAAA,EAAWJ,EAAA,CAAGI,SAAA;UACdC,QAAA,EAAUL,EAAA,CAAGK,QAAA;UACbC,KAAA,EAAO;QACR;QAED,SAAS9B,CAAA,GAAI,GAAGC,EAAA,GAAKuB,EAAA,CAAGM,KAAA,CAAM3F,MAAA,EAAQ6D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACjD,MAAM+B,IAAA,GAAO,CAAE;UACfA,IAAA,CAAKlF,KAAA,GAAQ2E,EAAA,CAAGM,KAAA,CAAM9B,CAAC,EAAEnD,KAAA;UACzBkF,IAAA,CAAKC,OAAA,GAAU;UAEf,IAAIR,EAAA,CAAGM,KAAA,CAAM9B,CAAC,EAAEoC,eAAA,KAAoB,GAAG;YAIrC,MAAMC,WAAA,GAAcb,EAAA,CAAGM,KAAA,CAAM9B,CAAC,EAAEsC,oBAAA;YAChC,MAAMC,WAAA,GAAcf,EAAA,CAAGM,KAAA,CAAM9B,CAAC,EAAEwC,oBAAA;YAKhC,MAAMC,IAAA,GAAO,CAACF,WAAA,CAAY,CAAC;YAC3B,MAAMG,IAAA,GAAO,CAACH,WAAA,CAAY,CAAC;YAC3BA,WAAA,CAAY,CAAC,IAAI,CAACF,WAAA,CAAY,CAAC;YAC/BE,WAAA,CAAY,CAAC,IAAI,CAACF,WAAA,CAAY,CAAC;YAC/BA,WAAA,CAAY,CAAC,IAAII,IAAA;YACjBJ,WAAA,CAAY,CAAC,IAAIK,IAAA;YAEjBX,IAAA,CAAKM,WAAA,GAAc,IAAIF,OAAA,CAAO,EAAG/D,SAAA,CAAUiE,WAAW;YACtDN,IAAA,CAAKQ,WAAA,GAAc,IAAIJ,OAAA,CAAO,EAAG/D,SAAA,CAAUmE,WAAW;UACvD;UAEDd,KAAA,CAAMK,KAAA,CAAMxF,IAAA,CAAKyF,IAAI;QACtB;QAEDzC,GAAA,CAAIhD,IAAA,CAAKmF,KAAK;QAId3D,KAAA,CAAM1B,CAAC,EAAEoF,EAAA,GAAKC,KAAA;MACf;IACF;IAID,IAAItH,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,OAAO;MAuClC,IAASqB,QAAA,GAAT,SAAAA,CAAkBC,KAAA,EAAO;QACvB,IAAIA,KAAA,CAAMnB,KAAA,EAAO;UACflC,MAAA,CAAOjD,IAAA,CAAKsG,KAAA,CAAMnB,KAAK;UAIvB3D,KAAA,CAAM8E,KAAA,CAAMnB,KAAA,CAAM5E,KAAK,EAAEgG,KAAA,GAAQD,KAAA,CAAMnB,KAAA;QACxC;QAEDmB,KAAA,CAAME,OAAA,GAAU;QAEhB,SAAS1G,CAAA,GAAI,GAAGC,EAAA,GAAKuG,KAAA,CAAMG,QAAA,CAAS5G,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACvD,MAAM4G,KAAA,GAAQJ,KAAA,CAAMG,QAAA,CAAS3G,CAAC;UAG9B,IAAI,CAAC4G,KAAA,CAAMF,OAAA,EAASH,QAAA,CAASK,KAAK;QACnC;MACF;MAtDD,MAAMC,aAAA,GAAgB,CAAE;MAExB,SAAS7G,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASqB,SAAA,EAAW7E,CAAA,IAAK;QAChD,MAAM8E,QAAA,GAAW/G,IAAA,CAAK2D,KAAA,CAAM1B,CAAC;QAC7B,MAAMyG,KAAA,GAAQ3B,QAAA,CAAS2B,KAAA;QAEvB,IAAIA,KAAA,KAAU,QAAW;QAEzB,MAAMpB,KAAA,GAAQ;UACZ5E,KAAA,EAAOT,CAAA;UACPgF,WAAA,EAAayB,KAAA,CAAMzB,WAAA;UACnB8B,KAAA,EAAOL,KAAA,CAAMK,KAAA;UACbC,OAAA,EAASN,KAAA,CAAMM,OAAA;UACfC,cAAA,EAAgBP,KAAA,CAAMO,cAAA;UACtBC,cAAA,EAAgBR,KAAA,CAAMQ,cAAA;UACtBlC,mBAAA,EAAqBD,QAAA,CAASC;QAC/B;QAED8B,aAAA,CAAc7G,CAAC,IAAI;UAAEsC,MAAA,EAAQ;UAAMqE,QAAA,EAAU;UAAItB,KAAA;UAAcqB,OAAA,EAAS;QAAO;MAChF;MAED,MAAMQ,SAAA,GAAY;QAAE5E,MAAA,EAAQ;QAAMqE,QAAA,EAAU,EAAE;QAAEtB,KAAA,EAAO;QAAMqB,OAAA,EAAS;MAAO;MAI7E,WAAWjC,SAAA,IAAaoC,aAAA,EAAe;QACrC,MAAMM,UAAA,GAAaN,aAAA,CAAcpC,SAAS;QAC1C,MAAM2C,gBAAA,GAAmBP,aAAA,CAAcM,UAAA,CAAWnC,WAAW,KAAKkC,SAAA;QAElEC,UAAA,CAAW7E,MAAA,GAAS8E,gBAAA;QACpBA,gBAAA,CAAiBT,QAAA,CAASzG,IAAA,CAAKiH,UAAU;MAC1C;MAyBDZ,QAAA,CAASW,SAAS;IACnB;IAID,SAASG,iBAAiBC,SAAA,EAAWC,KAAA,EAAOT,KAAA,EAAO;MACjD,SAAS9G,CAAA,GAAI,GAAGA,CAAA,GAAIuH,KAAA,CAAMC,YAAA,EAAcxH,CAAA,IAAK;QAC3C,MAAMyH,OAAA,GAAUF,KAAA,CAAMG,QAAA,CAAS1H,CAAC;QAEhC,IAAIS,KAAA;QAEJ,IAAI1C,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,OAAO;UAClCzE,KAAA,GAAQ1C,IAAA,CAAK4J,MAAA,CAAO,CAAC,EAAED,QAAA,CAASD,OAAA,CAAQhH,KAAK,EAAEA,KAAA;QACzD,OAAe;UACLA,KAAA,GAAQgH,OAAA,CAAQhH,KAAA;QACjB;QAED6G,SAAA,CAAUM,KAAA,CAAMnH,KAAA,GAAQ,IAAI,CAAC,KAAKgH,OAAA,CAAQ1F,QAAA,CAAS,CAAC,IAAI+E,KAAA;QACxDQ,SAAA,CAAUM,KAAA,CAAMnH,KAAA,GAAQ,IAAI,CAAC,KAAKgH,OAAA,CAAQ1F,QAAA,CAAS,CAAC,IAAI+E,KAAA;QACxDQ,SAAA,CAAUM,KAAA,CAAMnH,KAAA,GAAQ,IAAI,CAAC,KAAKgH,OAAA,CAAQ1F,QAAA,CAAS,CAAC,IAAI+E,KAAA;MACzD;IACF;IAED,SAAS9G,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASqE,UAAA,EAAY7H,CAAA,IAAK;MACjD,MAAMuH,KAAA,GAAQxJ,IAAA,CAAK4J,MAAA,CAAO3H,CAAC;MAC3B,MAAM8H,MAAA,GAAS;QAAEhG,IAAA,EAAMyF,KAAA,CAAMzF;MAAM;MAEnC,MAAMwF,SAAA,GAAY,IAAIS,sBAAA,CAAuBhK,IAAA,CAAKyF,QAAA,CAASC,WAAA,GAAc,GAAG,CAAC;MAC7E6D,SAAA,CAAUxF,IAAA,GAAOyF,KAAA,CAAMzF,IAAA;MAEvB,SAAS8B,CAAA,GAAI,GAAGA,CAAA,GAAI7F,IAAA,CAAKyF,QAAA,CAASC,WAAA,GAAc,GAAGG,CAAA,IAAK;QACtD0D,SAAA,CAAUM,KAAA,CAAMhE,CAAC,IAAInB,SAAA,CAAUmB,CAAC;MACjC;MAED,IAAI7F,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,OAAO;QAClC,IAAIlF,CAAA,KAAM,GAAG;UACXqH,gBAAA,CAAiBC,SAAA,EAAWC,KAAA,EAAO,CAAG;QACvC;MACT,OAAa;QACL,IAAIA,KAAA,CAAM7C,IAAA,KAAS,GAAG;UAGpB,SAASd,CAAA,GAAI,GAAGA,CAAA,GAAI2D,KAAA,CAAMC,YAAA,EAAc5D,CAAA,IAAK;YAC3C,MAAMoE,MAAA,GAASjK,IAAA,CAAK4J,MAAA,CAAOJ,KAAA,CAAMG,QAAA,CAAS9D,CAAC,EAAEnD,KAAK;YAClD,MAAMqG,KAAA,GAAQS,KAAA,CAAMG,QAAA,CAAS9D,CAAC,EAAEkD,KAAA;YAEhC,IAAIkB,MAAA,CAAOtD,IAAA,KAAS,GAAG;cACrB2C,gBAAA,CAAiBC,SAAA,EAAWU,MAAA,EAAQlB,KAAK;YAG1C;UACF;QACX,WAAmBS,KAAA,CAAM7C,IAAA,KAAS,GAAG;UAG3B2C,gBAAA,CAAiBC,SAAA,EAAWC,KAAA,EAAO,CAAG;QAChD,WAAmBA,KAAA,CAAM7C,IAAA,KAAS,GAAG,UAGlB6C,KAAA,CAAM7C,IAAA,KAAS,GAAG,UAGlB6C,KAAA,CAAM7C,IAAA,KAAS,GAAG,UAGlB6C,KAAA,CAAM7C,IAAA,KAAS,GAAG,UAGlB6C,KAAA,CAAM7C,IAAA,KAAS,GAAG,UAGlB6C,KAAA,CAAM7C,IAAA,KAAS,GAAG,UAGlB6C,KAAA,CAAM7C,IAAA,KAAS,GAAG;MAI9B;MAED1B,YAAA,CAAa9C,IAAA,CAAK4H,MAAM;MACxB7E,cAAA,CAAe/C,IAAA,CAAKoH,SAAS;IAC9B;IAID,SAAStH,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASc,cAAA,EAAgBtE,CAAA,IAAK;MACrD,MAAMiI,SAAA,GAAYlK,IAAA,CAAKqF,WAAA,CAAYpD,CAAC;MACpC,MAAM8H,MAAA,GAAS,CAAE;MAEjB,WAAWI,GAAA,IAAOD,SAAA,EAAW;QAC3BH,MAAA,CAAOI,GAAG,IAAID,SAAA,CAAUC,GAAG;MAC5B;MAOD,IAAInK,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,OAAO;QAClC,IAAI4C,MAAA,CAAOrD,SAAA,KAAc,IAAI;UAC3B,MAAM7C,IAAA,GAAO7D,IAAA,CAAK2D,KAAA,CAAMoG,MAAA,CAAOrD,SAAS;UACxCqD,MAAA,CAAO/F,QAAA,CAAS,CAAC,KAAKH,IAAA,CAAKG,QAAA,CAAS,CAAC;UACrC+F,MAAA,CAAO/F,QAAA,CAAS,CAAC,KAAKH,IAAA,CAAKG,QAAA,CAAS,CAAC;UACrC+F,MAAA,CAAO/F,QAAA,CAAS,CAAC,KAAKH,IAAA,CAAKG,QAAA,CAAS,CAAC;QACtC;MACF;MAEDqB,WAAA,CAAYlD,IAAA,CAAK4H,MAAM;IACxB;IAID,SAAS9H,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAAS2E,eAAA,EAAiBnI,CAAA,IAAK;MACtD,MAAMoI,UAAA,GAAarK,IAAA,CAAKsF,WAAA,CAAYrD,CAAC;MACrC,MAAM8H,MAAA,GAAS,CAAE;MAEjB,WAAWI,GAAA,IAAOE,UAAA,EAAY;QAC5BN,MAAA,CAAOI,GAAG,IAAIE,UAAA,CAAWF,GAAG;MAC7B;MAED,MAAMG,KAAA,GAAQjF,WAAA,CAAY0E,MAAA,CAAOQ,eAAe;MAChD,MAAMC,KAAA,GAAQnF,WAAA,CAAY0E,MAAA,CAAOU,eAAe;MAGhD,IAAIH,KAAA,CAAM3D,IAAA,KAAS,KAAK6D,KAAA,CAAM7D,IAAA,KAAS,GAAG;QACxC,IACE2D,KAAA,CAAM5D,SAAA,KAAc,MACpB8D,KAAA,CAAM9D,SAAA,KAAc,MACpB1G,IAAA,CAAK2D,KAAA,CAAM6G,KAAA,CAAM9D,SAAS,EAAEO,WAAA,KAAgBqD,KAAA,CAAM5D,SAAA,EAClD;UACA8D,KAAA,CAAM7D,IAAA,GAAO;QACd;MACF;MAEDrB,WAAA,CAAYnD,IAAA,CAAK4H,MAAM;IACxB;IAID,MAAM5G,QAAA,GAAW,IAAIuH,cAAA,CAAgB;IAErCvH,QAAA,CAASwH,YAAA,CAAa,YAAY,IAAIX,sBAAA,CAAuBtF,SAAA,EAAW,CAAC,CAAC;IAC1EvB,QAAA,CAASwH,YAAA,CAAa,UAAU,IAAIX,sBAAA,CAAuBpF,OAAA,EAAS,CAAC,CAAC;IACtEzB,QAAA,CAASwH,YAAA,CAAa,MAAM,IAAIX,sBAAA,CAAuBrF,GAAA,EAAK,CAAC,CAAC;IAC9DxB,QAAA,CAASwH,YAAA,CAAa,aAAa,IAAIC,qBAAA,CAAsB7F,WAAA,EAAa,CAAC,CAAC;IAC5E5B,QAAA,CAASwH,YAAA,CAAa,cAAc,IAAIX,sBAAA,CAAuBhF,WAAA,EAAa,CAAC,CAAC;IAC9E7B,QAAA,CAAS0H,QAAA,CAAShG,OAAO;IAEzB,SAAS5C,CAAA,GAAI,GAAGC,EAAA,GAAK4C,MAAA,CAAO9C,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC/CkB,QAAA,CAAS2H,QAAA,CAAShG,MAAA,CAAO7C,CAAC,EAAEsD,MAAA,EAAQT,MAAA,CAAO7C,CAAC,EAAEqE,KAAA,EAAOrE,CAAC;IACvD;IAEDkB,QAAA,CAASQ,KAAA,GAAQA,KAAA;IAEjBR,QAAA,CAAS8B,YAAA,GAAeA,YAAA;IACxB9B,QAAA,CAAS4H,eAAA,CAAgB/G,QAAA,GAAWkB,cAAA;IACpC/B,QAAA,CAAS6H,oBAAA,GAAuB;IAEhC7H,QAAA,CAAS8H,QAAA,CAASC,GAAA,GAAM;MACtBvH,KAAA;MACAwB,GAAA;MACAC,MAAA;MACAC,WAAA;MACAC,WAAA;MACA6B,MAAA,EAAQnH,IAAA,CAAKyF,QAAA,CAAS0B;IACvB;IAEDhE,QAAA,CAASgI,qBAAA,CAAuB;IAEhC,OAAOhI,QAAA;EACR;AACH;AAOA,MAAMD,eAAA,CAAgB;EACpB7E,YAAYC,OAAA,EAAS;IACnB,KAAKA,OAAA,GAAUA,OAAA;IAEf,KAAK8M,aAAA,GAAgB,IAAIC,aAAA,CAAc,KAAK/M,OAAO;IACnD,KAAKgN,SAAA,GAAY;IAEjB,KAAK/L,WAAA,GAAc;IACnB,KAAKC,YAAA,GAAe;EACrB;EAAA;AAAA;AAAA;AAAA;EAMDF,eAAeC,WAAA,EAAa;IAC1B,KAAKA,WAAA,GAAcA,WAAA;IACnB,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;EAMD8D,gBAAgB7D,YAAA,EAAc;IAC5B,KAAKA,YAAA,GAAeA,YAAA;IACpB,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDS,MAAMD,IAAA,EAAMmD,QAAA,EAAqC;IAC/C,MAAMkD,SAAA,GAAY,EAAE;IAEpB,MAAMkF,QAAA,GAAW,CAAE;IAEnB,KAAKH,aAAA,CAAc9L,cAAA,CAAe,KAAKC,WAAW;IAIlD,SAAS0C,CAAA,GAAI,GAAGA,CAAA,GAAIjC,IAAA,CAAKyF,QAAA,CAASW,aAAA,EAAenE,CAAA,IAAK;MACpD,MAAMmB,QAAA,GAAWpD,IAAA,CAAKqG,SAAA,CAAUpE,CAAC;MAEjC,MAAM8H,MAAA,GAAS;QAAEkB,QAAA,EAAU;MAAI;MAE/B,IAAI7H,QAAA,CAASW,IAAA,KAAS,QAAWgG,MAAA,CAAOhG,IAAA,GAAOX,QAAA,CAASW,IAAA;MAaxDgG,MAAA,CAAOyB,KAAA,GAAQ,IAAIC,KAAA,CAAO,EAACxH,SAAA,CAAUb,QAAA,CAASsI,OAAO;MACrD3B,MAAA,CAAO4B,OAAA,GAAUvI,QAAA,CAASsI,OAAA,CAAQ,CAAC;MACnC3B,MAAA,CAAO6B,QAAA,GAAW,IAAIH,KAAA,CAAO,EAACxH,SAAA,CAAUb,QAAA,CAASyI,OAAO;MACxD9B,MAAA,CAAO+B,WAAA,GAAc/B,MAAA,CAAO4B,OAAA,KAAY;MAIxC5B,MAAA,CAAOgC,QAAA,GAAW5I,QAAA,CAASQ,KAAA,CAAM3B,MAAA,GAAS,IAAI,OAAO;MACrD+H,MAAA,CAAO9E,YAAA,GAAe9B,QAAA,CAAS8B,YAAA,CAAajD,MAAA,GAAS,IAAI,OAAO;MAChE+H,MAAA,CAAOiC,GAAA,GAAM;MAIbjC,MAAA,CAAOkC,QAAA,GAAWC,cAAA;MAClBnC,MAAA,CAAOoC,QAAA,GAAWC,cAAA;MAClBrC,MAAA,CAAOsC,QAAA,GAAWC,sBAAA;MAClBvC,MAAA,CAAOwC,aAAA,GAAgBH,cAAA;MACvBrC,MAAA,CAAOyC,aAAA,GAAgBC,cAAA;MAIvB,IAAIzM,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,UAAU/D,QAAA,CAASsJ,IAAA,GAAO,OAAS,GAAG;QACjE3C,MAAA,CAAO4C,IAAA,GAAOC,UAAA;MACtB,OAAa;QACL7C,MAAA,CAAO4C,IAAA,GAAO5C,MAAA,CAAO4B,OAAA,KAAY,IAAMkB,SAAA,GAAYD,UAAA;MACpD;MAED,IAAI5M,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,OAAO;QAGlC,IAAI/D,QAAA,CAAS0J,QAAA,EAAU;UACrB,MAAMA,QAAA,GAAW1J,QAAA,CAAS0J,QAAA;UAC1B,MAAMC,SAAA,GAAYD,QAAA,CAASE,KAAA,CAAM,GAAG;UAKpCjD,MAAA,CAAOkD,GAAA,GAAM,KAAKC,YAAA,CAAaH,SAAA,CAAU,CAAC,GAAGxB,QAAQ;UAErD,IAAIwB,SAAA,CAAU/K,MAAA,GAAS,GAAG;YACxB,MAAMmL,SAAA,GAAYJ,SAAA,CAAU,CAAC,EAAEnK,KAAA,CAAM,EAAE,EAAE9C,WAAA,CAAa;YAEtDiK,MAAA,CAAOqD,MAAA,GAAS,KAAKF,YAAA,CAAaH,SAAA,CAAU,CAAC,GAAGxB,QAAQ;YAExDxB,MAAA,CAAOsD,OAAA,GAAUF,SAAA,KAAc,SAASG,iBAAA,GAAoBC,YAAA;UAC7D;QACF;QAID,MAAMC,YAAA,GAAepK,QAAA,CAASqK,SAAA,KAAc,KAAK,eAAezN,IAAA,CAAK0N,YAAA,CAAatK,QAAA,CAASqK,SAAS,EAAEX,QAAA;QAEtG/C,MAAA,CAAO4D,WAAA,GAAc,KAAKT,YAAA,CAAaM,YAAA,EAAcjC,QAAA,EAAU;UAC7DqC,aAAA,EAAe;UACfC,oBAAA,EAAsB,KAAKC,qBAAA,CAAsBN,YAAY;QACvE,CAAS;QAIDzD,MAAA,CAAOkB,QAAA,CAAS8C,iBAAA,GAAoB;UAClCC,SAAA,EAAW5K,QAAA,CAAS6K,QAAA,KAAa,IAAI,OAAQ;UAC7CzC,KAAA,EAAO,CAAC,GAAG,GAAG,CAAC;UACf0C,KAAA,EAAO;UACPC,OAAA,EAAS/K,QAAA,CAAS6K,QAAA,KAAa;QAChC;MACT,OAAa;QAGL,IAAI7K,QAAA,CAASgL,YAAA,KAAiB,IAAI;UAChCrE,MAAA,CAAOkD,GAAA,GAAM,KAAKC,YAAA,CAAalN,IAAA,CAAKuL,QAAA,CAASnI,QAAA,CAASgL,YAAY,GAAG7C,QAAQ;QAC9E;QAID,IAAInI,QAAA,CAASiL,eAAA,KAAoB,OAAOjL,QAAA,CAASkL,OAAA,KAAY,KAAKlL,QAAA,CAASkL,OAAA,IAAW,IAAI;UACxFvE,MAAA,CAAOqD,MAAA,GAAS,KAAKF,YAAA,CAAalN,IAAA,CAAKuL,QAAA,CAASnI,QAAA,CAASiL,eAAe,GAAG9C,QAAQ;UAEnFxB,MAAA,CAAOsD,OAAA,GAAUjK,QAAA,CAASkL,OAAA,KAAY,IAAIhB,iBAAA,GAAoBC,YAAA;QAC/D;QAID,IAAIC,YAAA,EAAce,aAAA;QAElB,IAAInL,QAAA,CAASqK,SAAA,KAAc,MAAMrK,QAAA,CAASoL,QAAA,KAAa,GAAG;UACxDhB,YAAA,GAAe,UAAU,OAAOpK,QAAA,CAASqK,SAAA,GAAY,IAAI7K,KAAA,CAAM,EAAE,IAAI;UACrE2L,aAAA,GAAgB;QAC1B,OAAe;UACLf,YAAA,GAAexN,IAAA,CAAKuL,QAAA,CAASnI,QAAA,CAASqK,SAAS;UAC/Cc,aAAA,GAAgB;QACjB;QAEDxE,MAAA,CAAO4D,WAAA,GAAc,KAAKT,YAAA,CAAaM,YAAA,EAAcjC,QAAA,EAAU;UAC7DqC,aAAA,EAAe;UACfC,oBAAA,EAAsBU;QAChC,CAAS;QAGDxE,MAAA,CAAOkB,QAAA,CAAS8C,iBAAA,GAAoB;UAClCC,SAAA,EAAW5K,QAAA,CAASqL,QAAA,GAAW;UAAA;UAC/BjD,KAAA,EAAOpI,QAAA,CAASsL,SAAA,CAAU9L,KAAA,CAAM,GAAG,CAAC;UACpCsL,KAAA,EAAO9K,QAAA,CAASsL,SAAA,CAAU,CAAC;UAC3BP,OAAA,GAAU/K,QAAA,CAASsJ,IAAA,GAAO,QAAU,KAAKtJ,QAAA,CAASqL,QAAA,GAAW;QAC9D;MACF;MAED,IAAI1E,MAAA,CAAOkD,GAAA,KAAQ,QAAW;QAC5B,IAAI,CAAClD,MAAA,CAAO+B,WAAA,EAAa;UACvB,KAAK6C,uBAAA,CAAwB5E,MAAA,CAAOkD,GAAA,EAAK9J,QAAA,EAAUlB,CAAC;QACrD;QAED8H,MAAA,CAAO6B,QAAA,CAASgD,cAAA,CAAe,GAAG;MACnC;MAEDvI,SAAA,CAAUlE,IAAA,CAAK,IAAI0M,gBAAA,CAAiB9E,MAAM,CAAC;IAC5C;IAED,IAAI/J,IAAA,CAAKyF,QAAA,CAAS0B,MAAA,KAAW,OAAO;MAGlC,IAAS2H,eAAA,GAAT,SAAAA,CAAyBnF,QAAA,EAAUoF,UAAA,EAAW;QAC5C,SAAS9M,CAAA,GAAI,GAAGC,EAAA,GAAKyH,QAAA,CAAS3H,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACjD,MAAMyH,OAAA,GAAUC,QAAA,CAAS1H,CAAC;UAE1B,IAAIyH,OAAA,CAAQhH,KAAA,KAAU,IAAI;UAE1B,MAAMU,QAAA,GAAW2L,UAAA,CAAUrF,OAAA,CAAQhH,KAAK;UAExC,IAAIU,QAAA,CAASuI,OAAA,KAAYjC,OAAA,CAAQgC,OAAA,CAAQ,CAAC,GAAG;YAC3CtI,QAAA,CAAS0I,WAAA,GAAc;UACxB;QACF;MACF;MAED,SAAS7J,CAAA,GAAI,GAAGC,EAAA,GAAKlC,IAAA,CAAK4J,MAAA,CAAO5H,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACpD,MAAMuH,KAAA,GAAQxJ,IAAA,CAAK4J,MAAA,CAAO3H,CAAC;QAC3B,MAAM0H,QAAA,GAAWH,KAAA,CAAMG,QAAA;QAEvB,IAAIH,KAAA,CAAM7C,IAAA,KAAS,GAAG;UACpB,SAASd,CAAA,GAAI,GAAGC,EAAA,GAAK6D,QAAA,CAAS3H,MAAA,EAAQ6D,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YACjD,MAAMoE,MAAA,GAASjK,IAAA,CAAK4J,MAAA,CAAOD,QAAA,CAAS9D,CAAC,EAAEnD,KAAK;YAE5C,IAAIuH,MAAA,CAAOtD,IAAA,KAAS,GAAG;YAEvBmI,eAAA,CAAgB7E,MAAA,CAAON,QAAA,EAAUtD,SAAS;UAC3C;QACX,WAAmBmD,KAAA,CAAM7C,IAAA,KAAS,GAAG;UAC3BmI,eAAA,CAAgBnF,QAAA,EAAUtD,SAAS;QACpC;MACF;IACF;IAED,OAAOA,SAAA;EACR;EAAA;EAID2I,cAAA,EAAgB;IACd,IAAI,KAAK1D,SAAA,KAAc,MAAM;MAC3B,IAAI2D,SAAA,KAAc,QAAW;QAC3B,MAAM,IAAIlP,KAAA,CAAM,mCAAmC;MACpD;MAED,KAAKuL,SAAA,GAAY,IAAI2D,SAAA,CAAU,KAAK3Q,OAAO;IAC5C;IAED,OAAO,KAAKgN,SAAA;EACb;EAEDwC,sBAAsB/J,IAAA,EAAM;IAC1B,IAAIA,IAAA,CAAK/B,MAAA,KAAW,IAAI,OAAO;IAE/B,OAAO,uBAAuBkN,IAAA,CAAKnL,IAAI;EACxC;EAEDmJ,aAAaiC,QAAA,EAAU5D,QAAA,EAAUxB,MAAA,EAAQ5K,UAAA,EAAYC,OAAA,EAAS;IAC5D2K,MAAA,GAASA,MAAA,IAAU,CAAE;IAErB,MAAMpJ,KAAA,GAAQ;IAEd,IAAIyO,QAAA;IAEJ,IAAIrF,MAAA,CAAO8D,oBAAA,KAAyB,MAAM;MACxC,IAAInL,KAAA;MAEJ,IAAI;QACFA,KAAA,GAAQ2M,QAAA,CAASF,QAAA,CAASG,KAAA,CAAM,sBAAsB,EAAE,CAAC,CAAC;MAC3D,SAAQC,CAAA,EAAP;QACAC,OAAA,CAAQC,IAAA,CACN,sBACEN,QAAA,GACA,yEAEH;QAEDzM,KAAA,GAAQ;MACT;MAED0M,QAAA,GAAWtM,qBAAA,CAAsBJ,KAAK;IAC5C,OAAW;MACL0M,QAAA,GAAW,KAAK5P,YAAA,GAAe2P,QAAA;IAChC;IAED,IAAI5D,QAAA,CAAS6D,QAAQ,MAAM,QAAW,OAAO7D,QAAA,CAAS6D,QAAQ;IAE9D,IAAI7Q,MAAA,GAAS,KAAKD,OAAA,CAAQoR,UAAA,CAAWN,QAAQ;IAE7C,IAAI7Q,MAAA,KAAW,MAAM;MACnBA,MAAA,GAAS4Q,QAAA,CAASvM,KAAA,CAAM,EAAE,EAAE9C,WAAA,OAAkB,SAAS,KAAKkP,aAAA,CAAe,IAAG,KAAK5D,aAAA;IACpF;IAED,MAAMuE,OAAA,GAAUpR,MAAA,CAAOS,IAAA,CACrBoQ,QAAA,EACA,UAAUQ,CAAA,EAAG;MAIX,IAAI7F,MAAA,CAAO6D,aAAA,KAAkB,MAAM;QACjCgC,CAAA,CAAEC,KAAA,GAAQlP,KAAA,CAAMmP,gBAAA,CAAiBF,CAAA,CAAEC,KAAK;QAExCD,CAAA,CAAEG,SAAA,GAAYC,aAAA;QACdJ,CAAA,CAAEK,SAAA,GAAYD,aAAA;MACf;MAEDJ,CAAA,CAAEM,KAAA,GAAQ;MACVN,CAAA,CAAEO,KAAA,GAAQC,cAAA;MACVR,CAAA,CAAES,KAAA,GAAQD,cAAA;MAEV,SAASnO,CAAA,GAAI,GAAGA,CAAA,GAAI0N,OAAA,CAAQW,cAAA,CAAetO,MAAA,EAAQC,CAAA,IAAK;QACtD0N,OAAA,CAAQW,cAAA,CAAerO,CAAC,EAAE0N,OAAO;MAClC;MAED,OAAOA,OAAA,CAAQW,cAAA;IAChB,GACDnR,UAAA,EACAC,OACD;IAEDuQ,OAAA,CAAQW,cAAA,GAAiB,EAAE;IAE3B/E,QAAA,CAAS6D,QAAQ,IAAIO,OAAA;IAErB,OAAOA,OAAA;EACR;EAEDG,iBAAiBD,KAAA,EAAO;IACtB,MAAMU,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAC9C,MAAMC,OAAA,GAAUH,MAAA,CAAOI,UAAA,CAAW,IAAI;IAEtC,MAAMC,KAAA,GAAQf,KAAA,CAAMe,KAAA;IACpB,MAAMC,MAAA,GAAShB,KAAA,CAAMgB,MAAA;IAErBN,MAAA,CAAOK,KAAA,GAAQA,KAAA;IACfL,MAAA,CAAOM,MAAA,GAASA,MAAA;IAEhBH,OAAA,CAAQI,SAAA,CAAU,GAAG,GAAGF,KAAA,EAAOC,MAAM;IACrCH,OAAA,CAAQK,SAAA,CAAUH,KAAA,GAAQ,GAAKC,MAAA,GAAS,CAAG;IAC3CH,OAAA,CAAQM,MAAA,CAAO,MAAMpK,IAAA,CAAKqK,EAAE;IAC5BP,OAAA,CAAQK,SAAA,CAAU,CAACH,KAAA,GAAQ,GAAK,CAACC,MAAA,GAAS,CAAG;IAC7CH,OAAA,CAAQQ,SAAA,CAAUrB,KAAA,EAAO,GAAG,CAAC;IAE7B,OAAOa,OAAA,CAAQS,YAAA,CAAa,GAAG,GAAGP,KAAA,EAAOC,MAAM;EAChD;EAAA;EAGDlC,wBAAwB1B,GAAA,EAAK9J,QAAA,EAAUiO,UAAA,EAAY;IACjDnE,GAAA,CAAIqD,cAAA,CAAenO,IAAA,CAAK,UAAUwN,OAAA,EAAS;MAEzC,SAAS0B,gBAAgBxB,KAAA,EAAO;QAC9B,MAAMU,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;QAC9CF,MAAA,CAAOK,KAAA,GAAQf,KAAA,CAAMe,KAAA;QACrBL,MAAA,CAAOM,MAAA,GAAShB,KAAA,CAAMgB,MAAA;QAEtB,MAAMH,OAAA,GAAUH,MAAA,CAAOI,UAAA,CAAW,IAAI;QACtCD,OAAA,CAAQQ,SAAA,CAAUrB,KAAA,EAAO,GAAG,CAAC;QAE7B,OAAOa,OAAA,CAAQS,YAAA,CAAa,GAAG,GAAGZ,MAAA,CAAOK,KAAA,EAAOL,MAAA,CAAOM,MAAM;MAC9D;MAED,SAASS,wBAAwBzB,KAAA,EAAOlL,GAAA,EAAKE,OAAA,EAAS;QACpD,MAAM+L,KAAA,GAAQf,KAAA,CAAMe,KAAA;QACpB,MAAMC,MAAA,GAAShB,KAAA,CAAMgB,MAAA;QACrB,MAAM7Q,IAAA,GAAO6P,KAAA,CAAM7P,IAAA;QACnB,MAAMuR,SAAA,GAAY;QAElB,IAAIvR,IAAA,CAAKgC,MAAA,IAAU4O,KAAA,GAAQC,MAAA,MAAY,GAAG,OAAO;QAEjD,SAAS5O,CAAA,GAAI,GAAGA,CAAA,GAAI4C,OAAA,CAAQ7C,MAAA,EAAQC,CAAA,IAAK,GAAG;UAC1C,MAAMuP,QAAA,GAAW;YAAEC,CAAA,EAAG;YAAKC,CAAA,EAAG;UAAK;UAEnC,SAAS7L,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;YAC1B,MAAMnD,KAAA,GAAQmC,OAAA,CAAQ5C,CAAA,GAAI,IAAI4D,CAAC;YAC/B,MAAMG,EAAA,GAAK;cAAEyL,CAAA,EAAG9M,GAAA,CAAIjC,KAAA,GAAQ,IAAI,CAAC;cAAGgP,CAAA,EAAG/M,GAAA,CAAIjC,KAAA,GAAQ,IAAI,CAAC;YAAG;YAE3D,IAAIiP,YAAA,CAAa9B,KAAA,EAAO7J,EAAE,IAAIuL,SAAA,EAAW,OAAO;YAEhDC,QAAA,CAASC,CAAA,IAAKzL,EAAA,CAAGyL,CAAA;YACjBD,QAAA,CAASE,CAAA,IAAK1L,EAAA,CAAG0L,CAAA;UAClB;UAEDF,QAAA,CAASC,CAAA,IAAK;UACdD,QAAA,CAASE,CAAA,IAAK;UAEd,IAAIC,YAAA,CAAa9B,KAAA,EAAO2B,QAAQ,IAAID,SAAA,EAAW,OAAO;QACvD;QAED,OAAO;MACR;MASD,SAASI,aAAa9B,KAAA,EAAO7J,EAAA,EAAI;QAC/B,MAAM4K,KAAA,GAAQf,KAAA,CAAMe,KAAA;QACpB,MAAMC,MAAA,GAAShB,KAAA,CAAMgB,MAAA;QAErB,IAAIY,CAAA,GAAI7K,IAAA,CAAKgL,KAAA,CAAM5L,EAAA,CAAGyL,CAAA,GAAIb,KAAK,IAAIA,KAAA;QACnC,IAAIc,CAAA,GAAI9K,IAAA,CAAKgL,KAAA,CAAM5L,EAAA,CAAG0L,CAAA,GAAIb,MAAM,IAAIA,MAAA;QAEpC,IAAIY,CAAA,GAAI,GAAGA,CAAA,IAAKb,KAAA;QAChB,IAAIc,CAAA,GAAI,GAAGA,CAAA,IAAKb,MAAA;QAEhB,MAAMnO,KAAA,GAAQgP,CAAA,GAAId,KAAA,GAAQa,CAAA;QAE1B,OAAO5B,KAAA,CAAM7P,IAAA,CAAK0C,KAAA,GAAQ,IAAI,CAAC;MAChC;MAED,MAAMmP,SAAA,GAAYlC,OAAA,CAAQE,KAAA,CAAM7P,IAAA,KAAS,SAAY2P,OAAA,CAAQE,KAAA,GAAQwB,eAAA,CAAgB1B,OAAA,CAAQE,KAAK;MAElG,MAAMiC,KAAA,GAAQ3O,QAAA,CAAS2B,MAAA,CAAOsM,UAAU;MAExC,IACEE,uBAAA,CACEO,SAAA,EACA1O,QAAA,CAAS4O,UAAA,CAAW/L,EAAA,CAAG6D,KAAA,EACvB1G,QAAA,CAAST,KAAA,CAAMmH,KAAA,CAAMjH,KAAA,CAAMkP,KAAA,CAAME,KAAA,EAAOF,KAAA,CAAME,KAAA,GAAQF,KAAA,CAAMxL,KAAK,CAClE,GACD;QACA2G,GAAA,CAAInB,WAAA,GAAc;MACnB;IACP,CAAK;EACF;AACH;AAIA,MAAMjN,gBAAA,CAAiB;EAAA;AAAA;AAAA;AAAA;AAAA;EAMrBoB,MAAMI,GAAA,EAAKO,IAAA,EAAM;IAGf,MAAMqR,MAAA,GAAS,KAAKC,sBAAA,CAAuB7R,GAAA,EAAKO,IAAI,EAAEqR,MAAA;IACtD,MAAME,OAAA,GAAU,KAAKC,mBAAA,CAAoB/R,GAAA,EAAKO,IAAI,EAAEqR,MAAA;IAEpD,SAAShQ,CAAA,GAAI,GAAGC,EAAA,GAAKiQ,OAAA,CAAQnQ,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAChDgQ,MAAA,CAAO9P,IAAA,CAAKgQ,OAAA,CAAQlQ,CAAC,CAAC;IACvB;IAED,OAAO,IAAIoQ,aAAA,CAAc,IAAI,IAAIJ,MAAM;EACxC;EAAA;AAAA;AAAA;AAAA;AAAA;EAODC,uBAAuB7R,GAAA,EAAKO,IAAA,EAAM;IAChC,SAAS0R,kBAAkBzI,KAAA,EAAO0I,aAAA,EAAe7P,KAAA,EAAO;MACtDmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,CAAC,IAAI,GAAG;MACzCmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,CAAC,IAAI,GAAG;MACzCmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,CAAC,IAAI,GAAG;MACzCmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,EAAE,IAAI,GAAG;IAC3C;IAED,MAAMuP,MAAA,GAAS,EAAE;IAEjB,MAAMO,OAAA,GAAU,CAAE;IAClB,MAAM7O,KAAA,GAAQ/C,IAAA,CAAK2C,QAAA,CAASI,KAAA;IAC5B,MAAM8O,kBAAA,GAAqB,CAAE;IAE7B,SAASxQ,CAAA,GAAI,GAAGC,EAAA,GAAKyB,KAAA,CAAM3B,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC9CwQ,kBAAA,CAAmB9O,KAAA,CAAM1B,CAAC,EAAE8B,IAAI,IAAI;IACrC;IAED,SAAS9B,CAAA,GAAI,GAAGA,CAAA,GAAI5B,GAAA,CAAIoF,QAAA,CAASiN,WAAA,EAAazQ,CAAA,IAAK;MACjD,MAAM0Q,MAAA,GAAStS,GAAA,CAAImS,OAAA,CAAQvQ,CAAC;MAC5B,MAAM2Q,QAAA,GAAWD,MAAA,CAAOC,QAAA;MAExB,IAAIH,kBAAA,CAAmBG,QAAQ,MAAM,QAAW;MAEhDJ,OAAA,CAAQI,QAAQ,IAAIJ,OAAA,CAAQI,QAAQ,KAAK,EAAE;MAC3CJ,OAAA,CAAQI,QAAQ,EAAEzQ,IAAA,CAAKwQ,MAAM;IAC9B;IAED,WAAWxI,GAAA,IAAOqI,OAAA,EAAS;MACzB,MAAM3I,KAAA,GAAQ2I,OAAA,CAAQrI,GAAG;MAEzBN,KAAA,CAAMgJ,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;QACzB,OAAOD,CAAA,CAAEE,QAAA,GAAWD,CAAA,CAAEC,QAAA;MAC9B,CAAO;MAED,MAAMC,KAAA,GAAQ,EAAE;MAChB,MAAMvO,SAAA,GAAY,EAAE;MACpB,MAAMwO,SAAA,GAAY,EAAE;MACpB,MAAMC,eAAA,GAAkB,EAAE;MAC1B,MAAMC,eAAA,GAAkB,EAAE;MAE1B,MAAMC,YAAA,GAAezS,IAAA,CAAK2C,QAAA,CAAS+P,aAAA,CAAcnJ,GAAG,EAAEnG,QAAA,CAASuP,OAAA,CAAS;MAExE,SAAStR,CAAA,GAAI,GAAGC,EAAA,GAAK2H,KAAA,CAAM7H,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9C,MAAMuR,IAAA,GAAO3J,KAAA,CAAM5H,CAAC,EAAE+Q,QAAA,GAAW;QACjC,MAAMhP,QAAA,GAAW6F,KAAA,CAAM5H,CAAC,EAAE+B,QAAA;QAC1B,MAAMyP,QAAA,GAAW5J,KAAA,CAAM5H,CAAC,EAAEwR,QAAA;QAC1B,MAAMlB,aAAA,GAAgB1I,KAAA,CAAM5H,CAAC,EAAEsQ,aAAA;QAE/BU,KAAA,CAAM9Q,IAAA,CAAKqR,IAAI;QAEf,SAAS3N,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAKnB,SAAA,CAAUvC,IAAA,CAAKkR,YAAA,CAAaxN,CAAC,IAAI7B,QAAA,CAAS6B,CAAC,CAAC;QACxE,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAKqN,SAAA,CAAU/Q,IAAA,CAAKsR,QAAA,CAAS5N,CAAC,CAAC;QACtD,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAKyM,iBAAA,CAAkBa,eAAA,EAAiBZ,aAAA,EAAe1M,CAAC;QAE/EyM,iBAAA,CAAkBc,eAAA,EAAiBb,aAAA,EAAe,CAAC;MACpD;MAED,MAAMmB,UAAA,GAAa,YAAYvJ,GAAA,GAAM;MAErC8H,MAAA,CAAO9P,IAAA,CAAK,KAAKwR,YAAA,CAAaD,UAAA,GAAa,aAAaE,mBAAA,EAAqBX,KAAA,EAAOvO,SAAA,EAAWyO,eAAe,CAAC;MAC/GlB,MAAA,CAAO9P,IAAA,CACL,KAAKwR,YAAA,CAAaD,UAAA,GAAa,eAAeG,uBAAA,EAAyBZ,KAAA,EAAOC,SAAA,EAAWE,eAAe,CACzG;IACF;IAED,OAAO,IAAIf,aAAA,CAAc,IAAI,IAAIJ,MAAM;EACxC;EAAA;AAAA;AAAA;AAAA;AAAA;EAODG,oBAAoB/R,GAAA,EAAKO,IAAA,EAAM;IAC7B,MAAMqR,MAAA,GAAS,EAAE;IAEjB,MAAMrI,MAAA,GAAS,CAAE;IACjB,MAAMkK,qBAAA,GAAwBlT,IAAA,CAAKkT,qBAAA;IAEnC,SAAS7R,CAAA,GAAI,GAAGA,CAAA,GAAI5B,GAAA,CAAIoF,QAAA,CAASqE,UAAA,EAAY7H,CAAA,IAAK;MAChD,MAAMuH,KAAA,GAAQnJ,GAAA,CAAIuJ,MAAA,CAAO3H,CAAC;MAC1B,MAAM8R,SAAA,GAAYvK,KAAA,CAAMuK,SAAA;MAExB,IAAID,qBAAA,CAAsBC,SAAS,MAAM,QAAW;MAEpDnK,MAAA,CAAOmK,SAAS,IAAInK,MAAA,CAAOmK,SAAS,KAAK,EAAE;MAC3CnK,MAAA,CAAOmK,SAAS,EAAE5R,IAAA,CAAKqH,KAAK;IAC7B;IAED,WAAWW,GAAA,IAAOP,MAAA,EAAQ;MACxB,MAAMC,KAAA,GAAQD,MAAA,CAAOO,GAAG;MAExBN,KAAA,CAAMgJ,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;QACzB,OAAOD,CAAA,CAAEE,QAAA,GAAWD,CAAA,CAAEC,QAAA;MAC9B,CAAO;MAED,MAAMC,KAAA,GAAQ,EAAE;MAChB,MAAMe,MAAA,GAAS,EAAE;MAEjB,SAAS/R,CAAA,GAAI,GAAGC,EAAA,GAAK2H,KAAA,CAAM7H,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9CgR,KAAA,CAAM9Q,IAAA,CAAK0H,KAAA,CAAM5H,CAAC,EAAE+Q,QAAA,GAAW,EAAE;QACjCgB,MAAA,CAAO7R,IAAA,CAAK0H,KAAA,CAAM5H,CAAC,EAAEgS,MAAM;MAC5B;MAEDhC,MAAA,CAAO9P,IAAA,CAAK,IAAI+R,mBAAA,CAAoB,4BAA4BJ,qBAAA,CAAsB3J,GAAG,IAAI,KAAK8I,KAAA,EAAOe,MAAM,CAAC;IACjH;IAED,OAAO,IAAI3B,aAAA,CAAc,IAAI,IAAIJ,MAAM;EACxC;EAAA;AAAA;AAAA;AAAA;EAMD1R,qBAAqBF,GAAA,EAAK;IACxB,SAAS8T,YAAYtK,KAAA,EAAOuK,GAAA,EAAK;MAC/BvK,KAAA,CAAM1H,IAAA,CAAKiS,GAAA,CAAI3C,CAAC;MAChB5H,KAAA,CAAM1H,IAAA,CAAKiS,GAAA,CAAI1C,CAAC;MAChB7H,KAAA,CAAM1H,IAAA,CAAKiS,GAAA,CAAIC,CAAC;IACjB;IAED,SAASC,eAAezK,KAAA,EAAO0K,CAAA,EAAG;MAChC1K,KAAA,CAAM1H,IAAA,CAAKoS,CAAA,CAAE9C,CAAC;MACd5H,KAAA,CAAM1H,IAAA,CAAKoS,CAAA,CAAE7C,CAAC;MACd7H,KAAA,CAAM1H,IAAA,CAAKoS,CAAA,CAAEF,CAAC;MACdxK,KAAA,CAAM1H,IAAA,CAAKoS,CAAA,CAAEC,CAAC;IACf;IAED,SAASlC,kBAAkBzI,KAAA,EAAO0I,aAAA,EAAe7P,KAAA,EAAO;MACtDmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,IAAI,CAAC,IAAI,GAAG;MAC7CmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,IAAI,CAAC,IAAI,GAAG;MAC7CmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,IAAI,CAAC,IAAI,GAAG;MAC7CmH,KAAA,CAAM1H,IAAA,CAAKoQ,aAAA,CAAc7P,KAAA,GAAQ,IAAI,CAAC,IAAI,GAAG;IAC9C;IAED,MAAM+R,OAAA,GAAUpU,GAAA,CAAIoU,OAAA,KAAY,SAAY,KAAKpU,GAAA,CAAIoU,OAAA,CAAQ7R,KAAA,CAAO;IAEpE6R,OAAA,CAAQ5B,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;MAC3B,OAAOD,CAAA,CAAEE,QAAA,GAAWD,CAAA,CAAEC,QAAA;IAC5B,CAAK;IAED,MAAMC,KAAA,GAAQ,EAAE;IAChB,MAAMyB,OAAA,GAAU,EAAE;IAClB,MAAMC,WAAA,GAAc,EAAE;IACtB,MAAMjQ,SAAA,GAAY,EAAE;IACpB,MAAMkQ,IAAA,GAAO,EAAE;IAEf,MAAMC,eAAA,GAAkB,EAAE;IAC1B,MAAMC,eAAA,GAAkB,EAAE;IAC1B,MAAM3B,eAAA,GAAkB,EAAE;IAC1B,MAAM4B,eAAA,GAAkB,EAAE;IAE1B,MAAM5Q,UAAA,GAAa,IAAI6Q,UAAA,CAAY;IACnC,MAAMC,KAAA,GAAQ,IAAIC,KAAA,CAAO;IACzB,MAAMlR,QAAA,GAAW,IAAIgE,OAAA,CAAS;IAC9B,MAAMmN,MAAA,GAAS,IAAInN,OAAA,CAAS;IAE5B,SAAS/F,CAAA,GAAI,GAAGC,EAAA,GAAKuS,OAAA,CAAQzS,MAAA,EAAQC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAChD,MAAM0Q,MAAA,GAAS8B,OAAA,CAAQxS,CAAC;MAExB,MAAMuR,IAAA,GAAOb,MAAA,CAAOK,QAAA,GAAW;MAC/B,MAAM9O,GAAA,GAAMyO,MAAA,CAAO3O,QAAA;MACnB,MAAMoR,GAAA,GAAMzC,MAAA,CAAOc,QAAA;MACnB,MAAM4B,QAAA,GAAW1C,MAAA,CAAO0C,QAAA;MACxB,MAAMC,GAAA,GAAM3C,MAAA,CAAO2C,GAAA;MACnB,MAAM/C,aAAA,GAAgBI,MAAA,CAAOJ,aAAA;MAE7BU,KAAA,CAAM9Q,IAAA,CAAKqR,IAAI;MAEfxP,QAAA,CAASuR,GAAA,CAAI,GAAG,GAAG,CAACF,QAAQ;MAC5BF,MAAA,CAAOI,GAAA,CAAIrR,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,CAAC;MAEjC+Q,KAAA,CAAMM,GAAA,CAAI,CAACH,GAAA,CAAI,CAAC,GAAG,CAACA,GAAA,CAAI,CAAC,GAAG,CAACA,GAAA,CAAI,CAAC,CAAC;MACnCjR,UAAA,CAAWqR,YAAA,CAAaP,KAAK;MAE7BjR,QAAA,CAASQ,GAAA,CAAI2Q,MAAM;MACnBnR,QAAA,CAASyR,eAAA,CAAgBtR,UAAU;MAEnCgQ,WAAA,CAAYO,OAAA,EAASS,MAAM;MAC3Bb,cAAA,CAAeK,WAAA,EAAaxQ,UAAU;MACtCgQ,WAAA,CAAYzP,SAAA,EAAWV,QAAQ;MAE/B4Q,IAAA,CAAKzS,IAAA,CAAKmT,GAAG;MAEb,SAASzP,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1ByM,iBAAA,CAAkBuC,eAAA,EAAiBtC,aAAA,EAAe1M,CAAC;MACpD;MAEDyM,iBAAA,CAAkBwC,eAAA,EAAiBvC,aAAA,EAAe,CAAC;MAGnD,SAAS1M,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1ByM,iBAAA,CAAkBa,eAAA,EAAiBZ,aAAA,EAAe,CAAC;MACpD;MAEDD,iBAAA,CAAkByC,eAAA,EAAiBxC,aAAA,EAAe,CAAC;IACpD;IAED,MAAMN,MAAA,GAAS,EAAE;IAGjBA,MAAA,CAAO9P,IAAA,CAAK,KAAKwR,YAAA,CAAa,mBAAmBC,mBAAA,EAAqBX,KAAA,EAAOyB,OAAA,EAASG,eAAe,CAAC;IAEtG5C,MAAA,CAAO9P,IAAA,CAAK,KAAKwR,YAAA,CAAa,eAAeE,uBAAA,EAAyBZ,KAAA,EAAO0B,WAAA,EAAaG,eAAe,CAAC;IAC1G7C,MAAA,CAAO9P,IAAA,CAAK,KAAKwR,YAAA,CAAa,aAAaC,mBAAA,EAAqBX,KAAA,EAAOvO,SAAA,EAAWyO,eAAe,CAAC;IAClGlB,MAAA,CAAO9P,IAAA,CAAK,KAAKwR,YAAA,CAAa,QAAQO,mBAAA,EAAqBjB,KAAA,EAAO2B,IAAA,EAAMG,eAAe,CAAC;IAExF,OAAO,IAAI1C,aAAA,CAAc,IAAI,IAAIJ,MAAM;EACxC;EAAA;EAID0B,aAAa+B,IAAA,EAAMC,kBAAA,EAAoB1C,KAAA,EAAOe,MAAA,EAAQ4B,cAAA,EAAgB;IAMpE,IAAI3C,KAAA,CAAMjR,MAAA,GAAS,GAAG;MACpBiR,KAAA,GAAQA,KAAA,CAAMrQ,KAAA,CAAO;MACrBoR,MAAA,GAASA,MAAA,CAAOpR,KAAA,CAAO;MACvBgT,cAAA,GAAiBA,cAAA,CAAehT,KAAA,CAAO;MAEvC,MAAMiT,MAAA,GAAS7B,MAAA,CAAOhS,MAAA,GAASiR,KAAA,CAAMjR,MAAA;MACrC,MAAM8T,iBAAA,GAAoBF,cAAA,CAAe5T,MAAA,GAASiR,KAAA,CAAMjR,MAAA;MAExD,IAAIU,KAAA,GAAQ;MAEZ,SAASqT,UAAA,GAAa,GAAGC,QAAA,GAAW/C,KAAA,CAAMjR,MAAA,EAAQ+T,UAAA,GAAaC,QAAA,EAAUD,UAAA,IAAc;QACrF,SAAS9T,CAAA,GAAI,GAAGA,CAAA,GAAI4T,MAAA,EAAQ5T,CAAA,IAAK;UAC/B,IACE+R,MAAA,CAAOtR,KAAA,GAAQmT,MAAA,GAAS5T,CAAC,MAAM+R,MAAA,EAAQtR,KAAA,GAAQ,KAAKmT,MAAA,GAAS5T,CAAC,KAC9D+R,MAAA,CAAOtR,KAAA,GAAQmT,MAAA,GAAS5T,CAAC,MAAM+R,MAAA,CAAO+B,UAAA,GAAaF,MAAA,GAAS5T,CAAC,GAC7D;YACAS,KAAA;YACA;UACD;QACF;QAED,IAAIqT,UAAA,GAAarT,KAAA,EAAO;UACtBuQ,KAAA,CAAMvQ,KAAK,IAAIuQ,KAAA,CAAM8C,UAAU;UAE/B,SAAS9T,CAAA,GAAI,GAAGA,CAAA,GAAI4T,MAAA,EAAQ5T,CAAA,IAAK;YAC/B+R,MAAA,CAAOtR,KAAA,GAAQmT,MAAA,GAAS5T,CAAC,IAAI+R,MAAA,CAAO+B,UAAA,GAAaF,MAAA,GAAS5T,CAAC;UAC5D;UAED,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAI6T,iBAAA,EAAmB7T,CAAA,IAAK;YAC1C2T,cAAA,CAAelT,KAAA,GAAQoT,iBAAA,GAAoB7T,CAAC,IAAI2T,cAAA,CAAeG,UAAA,GAAaD,iBAAA,GAAoB7T,CAAC;UAClG;QACF;MACF;MAEDgR,KAAA,CAAMjR,MAAA,GAASU,KAAA,GAAQ;MACvBsR,MAAA,CAAOhS,MAAA,IAAUU,KAAA,GAAQ,KAAKmT,MAAA;MAC9BD,cAAA,CAAe5T,MAAA,IAAUU,KAAA,GAAQ,KAAKoT,iBAAA;IACvC;IAED,MAAMG,KAAA,GAAQ,IAAIN,kBAAA,CAAmBD,IAAA,EAAMzC,KAAA,EAAOe,MAAM;IAExDiC,KAAA,CAAMC,iBAAA,GAAoB,SAASC,oCAAoCC,MAAA,EAAQ;MAC7E,OAAO,IAAIC,wBAAA,CACT,KAAKpD,KAAA,EACL,KAAKe,MAAA,EACL,KAAKsC,YAAA,CAAc,GACnBF,MAAA,EACA,IAAIG,YAAA,CAAaX,cAAc,CAChC;IACF;IAED,OAAOK,KAAA;EACR;AACH;AAIA,MAAMI,wBAAA,SAAiCG,WAAA,CAAY;EACjDnY,YAAYoY,kBAAA,EAAoBC,YAAA,EAAcC,UAAA,EAAYC,YAAA,EAAc7M,MAAA,EAAQ;IAC9E,MAAM0M,kBAAA,EAAoBC,YAAA,EAAcC,UAAA,EAAYC,YAAY;IAEhE,KAAKC,mBAAA,GAAsB9M,MAAA;EAC5B;EAED+M,aAAaC,EAAA,EAAIC,EAAA,EAAIpH,CAAA,EAAGqH,EAAA,EAAI;IAC1B,MAAMb,MAAA,GAAS,KAAKQ,YAAA;IACpB,MAAM5C,MAAA,GAAS,KAAK0C,YAAA;IACpB,MAAMb,MAAA,GAAS,KAAKqB,SAAA;IACpB,MAAMnN,MAAA,GAAS,KAAK8M,mBAAA;IAEpB,MAAMM,OAAA,GAAUJ,EAAA,GAAKlB,MAAA;IACrB,MAAMuB,OAAA,GAAUD,OAAA,GAAUtB,MAAA;IAK1B,MAAMwB,OAAA,GAAUJ,EAAA,GAAKD,EAAA,GAAM,IAAI,KAAM,MAAM,KAAOpH,CAAA,GAAIoH,EAAA,KAAOC,EAAA,GAAKD,EAAA;IAElE,IAAInB,MAAA,KAAW,GAAG;MAGhB,MAAMyB,EAAA,GAAKvN,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAC5B,MAAMQ,EAAA,GAAKxN,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAC5B,MAAMS,EAAA,GAAKzN,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAC5B,MAAMU,EAAA,GAAK1N,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAE5B,MAAMhO,KAAA,GAAQ,KAAK2O,UAAA,CAAWJ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIJ,OAAO;MAErDrC,UAAA,CAAW2C,SAAA,CAAUvB,MAAA,EAAQ,GAAGpC,MAAA,EAAQoD,OAAA,EAASpD,MAAA,EAAQmD,OAAA,EAASpO,KAAK;IAC7E,WAAe8M,MAAA,KAAW,GAAG;MAGvB,SAAS5T,CAAA,GAAI,GAAGA,CAAA,KAAM4T,MAAA,EAAQ,EAAE5T,CAAA,EAAG;QACjC,MAAMqV,EAAA,GAAKvN,MAAA,CAAOgN,EAAA,GAAK,KAAK9U,CAAA,GAAI,IAAI,CAAC;QACrC,MAAMsV,EAAA,GAAKxN,MAAA,CAAOgN,EAAA,GAAK,KAAK9U,CAAA,GAAI,IAAI,CAAC;QACrC,MAAMuV,EAAA,GAAKzN,MAAA,CAAOgN,EAAA,GAAK,KAAK9U,CAAA,GAAI,IAAI,CAAC;QACrC,MAAMwV,EAAA,GAAK1N,MAAA,CAAOgN,EAAA,GAAK,KAAK9U,CAAA,GAAI,IAAI,CAAC;QAErC,MAAM8G,KAAA,GAAQ,KAAK2O,UAAA,CAAWJ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIJ,OAAO;QAErDjB,MAAA,CAAOnU,CAAC,IAAI+R,MAAA,CAAOoD,OAAA,GAAUnV,CAAC,KAAK,IAAI8G,KAAA,IAASiL,MAAA,CAAOmD,OAAA,GAAUlV,CAAC,IAAI8G,KAAA;MACvE;IACP,OAAW;MAGL,MAAMuO,EAAA,GAAKvN,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAC5B,MAAMQ,EAAA,GAAKxN,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAC5B,MAAMS,EAAA,GAAKzN,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAC5B,MAAMU,EAAA,GAAK1N,MAAA,CAAOgN,EAAA,GAAK,IAAI,CAAC;MAE5B,MAAMhO,KAAA,GAAQ,KAAK2O,UAAA,CAAWJ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIJ,OAAO;MAErDjB,MAAA,CAAO,CAAC,IAAIpC,MAAA,CAAOoD,OAAO,KAAK,IAAIrO,KAAA,IAASiL,MAAA,CAAOmD,OAAO,IAAIpO,KAAA;IAC/D;IAED,OAAOqN,MAAA;EACR;EAEDsB,WAAWJ,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIhG,CAAA,EAAG;IAsC5B,IAAImG,CAAA,GAAI;IACR,IAAIhI,CAAA,GAAIgI,CAAA;IACR,IAAIC,CAAA,GAAI,IAAMjI,CAAA;IACd,MAAMkI,IAAA,GAAO;IACb,MAAMC,GAAA,GAAM;IACZ,MAAMC,IAAA,GAAOpR,IAAA;IAEb,IAAIqR,IAAA,EAAMC,IAAA,EAAMC,GAAA;IAEhB,SAASlW,CAAA,GAAI,GAAGA,CAAA,GAAI6V,IAAA,EAAM7V,CAAA,IAAK;MAC7BgW,IAAA,GAAO,IAAMJ,CAAA,GAAIA,CAAA,GAAIjI,CAAA;MACrBsI,IAAA,GAAO,IAAML,CAAA,GAAIjI,CAAA,GAAIA,CAAA;MACrBuI,GAAA,GAAMvI,CAAA,GAAIA,CAAA,GAAIA,CAAA;MAEd,MAAMwI,EAAA,GAAKH,IAAA,GAAOX,EAAA,GAAKY,IAAA,GAAOX,EAAA,GAAKY,GAAA,GAAM1G,CAAA;MAEzC,IAAIuG,IAAA,CAAKK,GAAA,CAAID,EAAE,IAAIL,GAAA,EAAK;MAExBH,CAAA,IAAK;MAELhI,CAAA,IAAKwI,EAAA,GAAK,IAAIR,CAAA,GAAI,CAACA,CAAA;MACnBC,CAAA,GAAI,IAAMjI,CAAA;IACX;IAED,OAAOqI,IAAA,GAAOT,EAAA,GAAKU,IAAA,GAAOT,EAAA,GAAKU,GAAA;EAChC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}