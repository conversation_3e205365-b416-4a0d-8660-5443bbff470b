{"ast": null, "code": "import { Vector2, Vector3, Triangle } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle(tri, i, index, pos) {\n  const ta = tri.a;\n  const tb = tri.b;\n  const tc = tri.c;\n  let i0 = i;\n  let i1 = i + 1;\n  let i2 = i + 2;\n  if (index) {\n    i0 = index.getX(i);\n    i1 = index.getX(i + 1);\n    i2 = index.getX(i + 2);\n  }\n  ta.x = pos.getX(i0);\n  ta.y = pos.getY(i0);\n  ta.z = pos.getZ(i0);\n  tb.x = pos.getX(i1);\n  tb.y = pos.getY(i1);\n  tb.z = pos.getZ(i1);\n  tc.x = pos.getX(i2);\n  tc.y = pos.getY(i2);\n  tc.z = pos.getZ(i2);\n}\nexport function iterateOverTriangles(offset, count, geometry, intersectsTriangleFunc, contained, depth, triangle) {\n  const index = geometry.index;\n  const pos = geometry.attributes.position;\n  for (let i = offset, l = count + offset; i < l; i++) {\n    setTriangle(triangle, i * 3, index, pos);\n    triangle.needsUpdate = true;\n    if (intersectsTriangleFunc(triangle, i, contained, depth)) {\n      return true;\n    }\n  }\n  return false;\n}\nconst tempV1 = /* @__PURE__ */new Vector3();\nconst tempV2 = /* @__PURE__ */new Vector3();\nconst tempV3 = /* @__PURE__ */new Vector3();\nconst tempUV1 = /* @__PURE__ */new Vector2();\nconst tempUV2 = /* @__PURE__ */new Vector2();\nconst tempUV3 = /* @__PURE__ */new Vector2();\nexport function getTriangleHitPointInfo(point, geometry, triangleIndex, target) {\n  const indices = geometry.getIndex().array;\n  const positions = geometry.getAttribute('position');\n  const uvs = geometry.getAttribute('uv');\n  const a = indices[triangleIndex * 3];\n  const b = indices[triangleIndex * 3 + 1];\n  const c = indices[triangleIndex * 3 + 2];\n  tempV1.fromBufferAttribute(positions, a);\n  tempV2.fromBufferAttribute(positions, b);\n  tempV3.fromBufferAttribute(positions, c);\n\n  // find the associated material index\n  let materialIndex = 0;\n  const groups = geometry.groups;\n  const firstVertexIndex = triangleIndex * 3;\n  for (let i = 0, l = groups.length; i < l; i++) {\n    const group = groups[i];\n    const {\n      start,\n      count\n    } = group;\n    if (firstVertexIndex >= start && firstVertexIndex < start + count) {\n      materialIndex = group.materialIndex;\n      break;\n    }\n  }\n\n  // extract uvs\n  let uv = null;\n  if (uvs) {\n    tempUV1.fromBufferAttribute(uvs, a);\n    tempUV2.fromBufferAttribute(uvs, b);\n    tempUV3.fromBufferAttribute(uvs, c);\n    if (target && target.uv) uv = target.uv;else uv = new Vector2();\n    Triangle.getUV(point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv);\n  }\n\n  // adjust the provided target or create a new one\n  if (target) {\n    if (!target.face) target.face = {};\n    target.face.a = a;\n    target.face.b = b;\n    target.face.c = c;\n    target.face.materialIndex = materialIndex;\n    if (!target.face.normal) target.face.normal = new Vector3();\n    Triangle.getNormal(tempV1, tempV2, tempV3, target.face.normal);\n    if (uv) target.uv = uv;\n    return target;\n  } else {\n    return {\n      face: {\n        a: a,\n        b: b,\n        c: c,\n        materialIndex: materialIndex,\n        normal: Triangle.getNormal(tempV1, tempV2, tempV3, new Vector3())\n      },\n      uv: uv\n    };\n  }\n}", "map": {"version": 3, "names": ["Vector2", "Vector3", "Triangle", "set<PERSON>riangle", "tri", "i", "index", "pos", "ta", "a", "tb", "b", "tc", "c", "i0", "i1", "i2", "getX", "x", "y", "getY", "z", "getZ", "iterateOverTriangles", "offset", "count", "geometry", "intersectsTriangleFunc", "contained", "depth", "triangle", "attributes", "position", "l", "needsUpdate", "tempV1", "tempV2", "tempV3", "tempUV1", "tempUV2", "tempUV3", "getTriangleHitPointInfo", "point", "triangleIndex", "target", "indices", "getIndex", "array", "positions", "getAttribute", "uvs", "fromBufferAttribute", "materialIndex", "groups", "firstVertexIndex", "length", "group", "start", "uv", "getUV", "face", "normal", "getNormal"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/utils/TriangleUtilities.js"], "sourcesContent": ["\nimport { Vector2, Vector3, Triangle } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle( tri, i, index, pos ) {\n\n\tconst ta = tri.a;\n\tconst tb = tri.b;\n\tconst tc = tri.c;\n\n\tlet i0 = i;\n\tlet i1 = i + 1;\n\tlet i2 = i + 2;\n\tif ( index ) {\n\n\t\ti0 = index.getX( i );\n\t\ti1 = index.getX( i + 1 );\n\t\ti2 = index.getX( i + 2 );\n\n\t}\n\n\tta.x = pos.getX( i0 );\n\tta.y = pos.getY( i0 );\n\tta.z = pos.getZ( i0 );\n\n\ttb.x = pos.getX( i1 );\n\ttb.y = pos.getY( i1 );\n\ttb.z = pos.getZ( i1 );\n\n\ttc.x = pos.getX( i2 );\n\ttc.y = pos.getY( i2 );\n\ttc.z = pos.getZ( i2 );\n\n}\n\nexport function iterateOverTriangles(\n\toffset,\n\tcount,\n\tgeometry,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst index = geometry.index;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tsetTriangle( triangle, i * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, i, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nconst tempV1 = /* @__PURE__ */ new Vector3();\nconst tempV2 = /* @__PURE__ */ new Vector3();\nconst tempV3 = /* @__PURE__ */ new Vector3();\nconst tempUV1 = /* @__PURE__ */ new Vector2();\nconst tempUV2 = /* @__PURE__ */ new Vector2();\nconst tempUV3 = /* @__PURE__ */ new Vector2();\n\nexport function getTriangleHitPointInfo( point, geometry, triangleIndex, target ) {\n\n\tconst indices = geometry.getIndex().array;\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst uvs = geometry.getAttribute( 'uv' );\n\n\tconst a = indices[ triangleIndex * 3 ];\n\tconst b = indices[ triangleIndex * 3 + 1 ];\n\tconst c = indices[ triangleIndex * 3 + 2 ];\n\n\ttempV1.fromBufferAttribute( positions, a );\n\ttempV2.fromBufferAttribute( positions, b );\n\ttempV3.fromBufferAttribute( positions, c );\n\n\t// find the associated material index\n\tlet materialIndex = 0;\n\tconst groups = geometry.groups;\n\tconst firstVertexIndex = triangleIndex * 3;\n\tfor ( let i = 0, l = groups.length; i < l; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\t\tconst { start, count } = group;\n\t\tif ( firstVertexIndex >= start && firstVertexIndex < start + count ) {\n\n\t\t\tmaterialIndex = group.materialIndex;\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t// extract uvs\n\tlet uv = null;\n\tif ( uvs ) {\n\n\t\ttempUV1.fromBufferAttribute( uvs, a );\n\t\ttempUV2.fromBufferAttribute( uvs, b );\n\t\ttempUV3.fromBufferAttribute( uvs, c );\n\n\t\tif ( target && target.uv ) uv = target.uv;\n\t\telse uv = new Vector2();\n\n\t\tTriangle.getUV( point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv );\n\n\t}\n\n\t// adjust the provided target or create a new one\n\tif ( target ) {\n\n\t\tif ( ! target.face ) target.face = { };\n\t\ttarget.face.a = a;\n\t\ttarget.face.b = b;\n\t\ttarget.face.c = c;\n\t\ttarget.face.materialIndex = materialIndex;\n\t\tif ( ! target.face.normal ) target.face.normal = new Vector3();\n\t\tTriangle.getNormal( tempV1, tempV2, tempV3, target.face.normal );\n\n\t\tif ( uv ) target.uv = uv;\n\n\t\treturn target;\n\n\t} else {\n\n\t\treturn {\n\t\t\tface: {\n\t\t\t\ta: a,\n\t\t\t\tb: b,\n\t\t\t\tc: c,\n\t\t\t\tmaterialIndex: materialIndex,\n\t\t\t\tnormal: Triangle.getNormal( tempV1, tempV2, tempV3, new Vector3() )\n\t\t\t},\n\t\t\tuv: uv\n\t\t};\n\n\t}\n\n}\n"], "mappings": "AACA,SAASA,OAAO,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;;AAElD;AACA,OAAO,SAASC,WAAWA,CAAEC,GAAG,EAAEC,CAAC,EAAEC,KAAK,EAAEC,GAAG,EAAG;EAEjD,MAAMC,EAAE,GAAGJ,GAAG,CAACK,CAAC;EAChB,MAAMC,EAAE,GAAGN,GAAG,CAACO,CAAC;EAChB,MAAMC,EAAE,GAAGR,GAAG,CAACS,CAAC;EAEhB,IAAIC,EAAE,GAAGT,CAAC;EACV,IAAIU,EAAE,GAAGV,CAAC,GAAG,CAAC;EACd,IAAIW,EAAE,GAAGX,CAAC,GAAG,CAAC;EACd,IAAKC,KAAK,EAAG;IAEZQ,EAAE,GAAGR,KAAK,CAACW,IAAI,CAAEZ,CAAE,CAAC;IACpBU,EAAE,GAAGT,KAAK,CAACW,IAAI,CAAEZ,CAAC,GAAG,CAAE,CAAC;IACxBW,EAAE,GAAGV,KAAK,CAACW,IAAI,CAAEZ,CAAC,GAAG,CAAE,CAAC;EAEzB;EAEAG,EAAE,CAACU,CAAC,GAAGX,GAAG,CAACU,IAAI,CAAEH,EAAG,CAAC;EACrBN,EAAE,CAACW,CAAC,GAAGZ,GAAG,CAACa,IAAI,CAAEN,EAAG,CAAC;EACrBN,EAAE,CAACa,CAAC,GAAGd,GAAG,CAACe,IAAI,CAAER,EAAG,CAAC;EAErBJ,EAAE,CAACQ,CAAC,GAAGX,GAAG,CAACU,IAAI,CAAEF,EAAG,CAAC;EACrBL,EAAE,CAACS,CAAC,GAAGZ,GAAG,CAACa,IAAI,CAAEL,EAAG,CAAC;EACrBL,EAAE,CAACW,CAAC,GAAGd,GAAG,CAACe,IAAI,CAAEP,EAAG,CAAC;EAErBH,EAAE,CAACM,CAAC,GAAGX,GAAG,CAACU,IAAI,CAAED,EAAG,CAAC;EACrBJ,EAAE,CAACO,CAAC,GAAGZ,GAAG,CAACa,IAAI,CAAEJ,EAAG,CAAC;EACrBJ,EAAE,CAACS,CAAC,GAAGd,GAAG,CAACe,IAAI,CAAEN,EAAG,CAAC;AAEtB;AAEA,OAAO,SAASO,oBAAoBA,CACnCC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,sBAAsB,EACtBC,SAAS,EACTC,KAAK,EACLC,QAAQ,EACP;EAED,MAAMxB,KAAK,GAAGoB,QAAQ,CAACpB,KAAK;EAC5B,MAAMC,GAAG,GAAGmB,QAAQ,CAACK,UAAU,CAACC,QAAQ;EACxC,KAAM,IAAI3B,CAAC,GAAGmB,MAAM,EAAES,CAAC,GAAGR,KAAK,GAAGD,MAAM,EAAEnB,CAAC,GAAG4B,CAAC,EAAE5B,CAAC,EAAG,EAAG;IAEvDF,WAAW,CAAE2B,QAAQ,EAAEzB,CAAC,GAAG,CAAC,EAAEC,KAAK,EAAEC,GAAI,CAAC;IAC1CuB,QAAQ,CAACI,WAAW,GAAG,IAAI;IAE3B,IAAKP,sBAAsB,CAAEG,QAAQ,EAAEzB,CAAC,EAAEuB,SAAS,EAAEC,KAAM,CAAC,EAAG;MAE9D,OAAO,IAAI;IAEZ;EAED;EAEA,OAAO,KAAK;AAEb;AAEA,MAAMM,MAAM,GAAG,eAAgB,IAAIlC,OAAO,CAAC,CAAC;AAC5C,MAAMmC,MAAM,GAAG,eAAgB,IAAInC,OAAO,CAAC,CAAC;AAC5C,MAAMoC,MAAM,GAAG,eAAgB,IAAIpC,OAAO,CAAC,CAAC;AAC5C,MAAMqC,OAAO,GAAG,eAAgB,IAAItC,OAAO,CAAC,CAAC;AAC7C,MAAMuC,OAAO,GAAG,eAAgB,IAAIvC,OAAO,CAAC,CAAC;AAC7C,MAAMwC,OAAO,GAAG,eAAgB,IAAIxC,OAAO,CAAC,CAAC;AAE7C,OAAO,SAASyC,uBAAuBA,CAAEC,KAAK,EAAEhB,QAAQ,EAAEiB,aAAa,EAAEC,MAAM,EAAG;EAEjF,MAAMC,OAAO,GAAGnB,QAAQ,CAACoB,QAAQ,CAAC,CAAC,CAACC,KAAK;EACzC,MAAMC,SAAS,GAAGtB,QAAQ,CAACuB,YAAY,CAAE,UAAW,CAAC;EACrD,MAAMC,GAAG,GAAGxB,QAAQ,CAACuB,YAAY,CAAE,IAAK,CAAC;EAEzC,MAAMxC,CAAC,GAAGoC,OAAO,CAAEF,aAAa,GAAG,CAAC,CAAE;EACtC,MAAMhC,CAAC,GAAGkC,OAAO,CAAEF,aAAa,GAAG,CAAC,GAAG,CAAC,CAAE;EAC1C,MAAM9B,CAAC,GAAGgC,OAAO,CAAEF,aAAa,GAAG,CAAC,GAAG,CAAC,CAAE;EAE1CR,MAAM,CAACgB,mBAAmB,CAAEH,SAAS,EAAEvC,CAAE,CAAC;EAC1C2B,MAAM,CAACe,mBAAmB,CAAEH,SAAS,EAAErC,CAAE,CAAC;EAC1C0B,MAAM,CAACc,mBAAmB,CAAEH,SAAS,EAAEnC,CAAE,CAAC;;EAE1C;EACA,IAAIuC,aAAa,GAAG,CAAC;EACrB,MAAMC,MAAM,GAAG3B,QAAQ,CAAC2B,MAAM;EAC9B,MAAMC,gBAAgB,GAAGX,aAAa,GAAG,CAAC;EAC1C,KAAM,IAAItC,CAAC,GAAG,CAAC,EAAE4B,CAAC,GAAGoB,MAAM,CAACE,MAAM,EAAElD,CAAC,GAAG4B,CAAC,EAAE5B,CAAC,EAAG,EAAG;IAEjD,MAAMmD,KAAK,GAAGH,MAAM,CAAEhD,CAAC,CAAE;IACzB,MAAM;MAAEoD,KAAK;MAAEhC;IAAM,CAAC,GAAG+B,KAAK;IAC9B,IAAKF,gBAAgB,IAAIG,KAAK,IAAIH,gBAAgB,GAAGG,KAAK,GAAGhC,KAAK,EAAG;MAEpE2B,aAAa,GAAGI,KAAK,CAACJ,aAAa;MACnC;IAED;EAED;;EAEA;EACA,IAAIM,EAAE,GAAG,IAAI;EACb,IAAKR,GAAG,EAAG;IAEVZ,OAAO,CAACa,mBAAmB,CAAED,GAAG,EAAEzC,CAAE,CAAC;IACrC8B,OAAO,CAACY,mBAAmB,CAAED,GAAG,EAAEvC,CAAE,CAAC;IACrC6B,OAAO,CAACW,mBAAmB,CAAED,GAAG,EAAErC,CAAE,CAAC;IAErC,IAAK+B,MAAM,IAAIA,MAAM,CAACc,EAAE,EAAGA,EAAE,GAAGd,MAAM,CAACc,EAAE,CAAC,KACrCA,EAAE,GAAG,IAAI1D,OAAO,CAAC,CAAC;IAEvBE,QAAQ,CAACyD,KAAK,CAAEjB,KAAK,EAAEP,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEkB,EAAG,CAAC;EAE/E;;EAEA;EACA,IAAKd,MAAM,EAAG;IAEb,IAAK,CAAEA,MAAM,CAACgB,IAAI,EAAGhB,MAAM,CAACgB,IAAI,GAAG,CAAE,CAAC;IACtChB,MAAM,CAACgB,IAAI,CAACnD,CAAC,GAAGA,CAAC;IACjBmC,MAAM,CAACgB,IAAI,CAACjD,CAAC,GAAGA,CAAC;IACjBiC,MAAM,CAACgB,IAAI,CAAC/C,CAAC,GAAGA,CAAC;IACjB+B,MAAM,CAACgB,IAAI,CAACR,aAAa,GAAGA,aAAa;IACzC,IAAK,CAAER,MAAM,CAACgB,IAAI,CAACC,MAAM,EAAGjB,MAAM,CAACgB,IAAI,CAACC,MAAM,GAAG,IAAI5D,OAAO,CAAC,CAAC;IAC9DC,QAAQ,CAAC4D,SAAS,CAAE3B,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEO,MAAM,CAACgB,IAAI,CAACC,MAAO,CAAC;IAEhE,IAAKH,EAAE,EAAGd,MAAM,CAACc,EAAE,GAAGA,EAAE;IAExB,OAAOd,MAAM;EAEd,CAAC,MAAM;IAEN,OAAO;MACNgB,IAAI,EAAE;QACLnD,CAAC,EAAEA,CAAC;QACJE,CAAC,EAAEA,CAAC;QACJE,CAAC,EAAEA,CAAC;QACJuC,aAAa,EAAEA,aAAa;QAC5BS,MAAM,EAAE3D,QAAQ,CAAC4D,SAAS,CAAE3B,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE,IAAIpC,OAAO,CAAC,CAAE;MACnE,CAAC;MACDyD,EAAE,EAAEA;IACL,CAAC;EAEF;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}