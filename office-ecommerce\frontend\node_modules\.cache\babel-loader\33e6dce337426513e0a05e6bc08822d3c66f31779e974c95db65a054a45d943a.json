{"ast": null, "code": "import { a as _defineProperty, _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { l as lerp, f as fade } from './misc-7d870b3c.esm.js';\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by <PERSON> (<EMAIL>).\n * Optimisations by <PERSON> (<EMAIL>).\n * Better rank ordering method by <PERSON> in 2012.\n * Converted to Javascript by <PERSON>.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * <PERSON>. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n  _classCallCheck(this, Grad);\n  _defineProperty(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n  _defineProperty(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n  _seed = Math.floor(_seed);\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n  for (var i = 0; i < 256; i++) {\n    var v;\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n    Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = fade(x); // Interpolate the four results\n\n  return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n    Y = Math.floor(y),\n    Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = fade(x);\n  var v = fade(y);\n  var w = fade(z); // Interpolate\n\n  return lerp(lerp(lerp(n000, n100, u), lerp(n001, n101, u), w), lerp(lerp(n010, n110, u), lerp(n011, n111, u), w), v);\n};\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\nvar TAU = Math.PI * 2; // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n  if (seed === 0) {\n    seed = 311;\n  }\n  return seed;\n}\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\nvar Generator = function Generator(_seed) {\n  var _this = this;\n  _classCallCheck(this, Generator);\n  _defineProperty(this, \"seed\", 0);\n  _defineProperty(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n  _defineProperty(this, \"value\", lcgRandom(this.seed));\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultSphere$sphere = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n    radius = _defaultSphere$sphere.radius,\n    center = _defaultSphere$sphere.center;\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultSphere$sphere2 = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n    radius = _defaultSphere$sphere2.radius,\n    center = _defaultSphere$sphere2.center;\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultCircle$circle = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n    radius = _defaultCircle$circle.radius,\n    center = _defaultCircle$circle.center;\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultCircle$circle2 = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n    radius = _defaultCircle$circle2.radius,\n    center = _defaultCircle$circle2.center;\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultRect$rect = _objectSpread2(_objectSpread2({}, defaultRect), rect),\n    sides = _defaultRect$rect.sides,\n    center = _defaultRect$rect.center;\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultBox$box = _objectSpread2(_objectSpread2({}, defaultBox), box),\n    sides = _defaultBox$box.sides,\n    center = _defaultBox$box.center;\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n  var _defaultBox$box2 = _objectSpread2(_objectSpread2({}, defaultBox), box),\n    sides = _defaultBox$box2.sides,\n    center = _defaultBox$box2.center;\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n  return buffer;\n}\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\nexport { Generator as G, inSphere as a, inCircle as b, onCircle as c, inRect as d, onRect as e, inBox as f, onBox as g, index as i, noise as n, onSphere as o };", "map": {"version": 3, "names": ["a", "_defineProperty", "_", "_objectSpread2", "_classCallCheck", "l", "lerp", "f", "fade", "Grad", "x", "y", "z", "_this", "grad3", "p", "perm", "Array", "gradP", "seed", "_seed", "Math", "floor", "i", "v", "F2", "sqrt", "G2", "F3", "G3", "simplex2", "xin", "yin", "n0", "n1", "n2", "s", "j", "t", "x0", "y0", "i1", "j1", "x1", "y1", "x2", "y2", "gi0", "gi1", "gi2", "t0", "dot2", "t1", "t2", "simplex3", "zin", "n3", "k", "z0", "k1", "i2", "j2", "k2", "z1", "z2", "x3", "y3", "z3", "gi3", "dot3", "t3", "perlin2", "X", "Y", "n00", "n01", "n10", "n11", "u", "perlin3", "Z", "n000", "n001", "n010", "n011", "n100", "n101", "n110", "n111", "w", "noise", "Object", "freeze", "__proto__", "TAU", "PI", "normalizeSeed", "abs", "string", "length", "charCodeAt", "lcgRandom", "state", "result", "Generator", "value", "init", "defaultGen", "random", "defaultSphere", "radius", "center", "onSphere", "buffer", "sphere", "rng", "arguments", "undefined", "_defaultSphere$sphere", "theta", "acos", "phi", "sin", "cos", "inSphere", "_defaultSphere$sphere2", "pow", "mag", "defaultCircle", "inCircle", "circle", "_defaultCircle$circle", "r", "onCircle", "_defaultCircle$circle2", "defaultRect", "sides", "inRect", "rect", "_defaultRect$rect", "sideX", "sideY", "onRect", "inBox", "box", "_defaultBox$box", "defaultBox", "sideZ", "onBox", "_defaultBox$box2", "index", "G", "b", "c", "d", "e", "g", "n", "o"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/maath/dist/index-43782085.esm.js"], "sourcesContent": ["import { a as _defineProperty, _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { l as lerp, f as fade } from './misc-7d870b3c.esm.js';\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by <PERSON> (<EMAIL>).\n * Optimisations by <PERSON> (<EMAIL>).\n * Better rank ordering method by <PERSON> in 2012.\n * Converted to Javascript by <PERSON>.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * <PERSON>. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n\n  _classCallCheck(this, Grad);\n\n  _defineProperty(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n\n  _defineProperty(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\n\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n\n  _seed = Math.floor(_seed);\n\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n\n  for (var i = 0; i < 256; i++) {\n    var v;\n\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = fade(x); // Interpolate the four results\n\n  return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y),\n      Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = fade(x);\n  var v = fade(y);\n  var w = fade(z); // Interpolate\n\n  return lerp(lerp(lerp(n000, n100, u), lerp(n001, n101, u), w), lerp(lerp(n010, n110, u), lerp(n011, n111, u), w), v);\n};\n\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\n\nvar TAU = Math.PI * 2; // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n\n  if (seed === 0) {\n    seed = 311;\n  }\n\n  return seed;\n}\n\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\n\nvar Generator = function Generator(_seed) {\n  var _this = this;\n\n  _classCallCheck(this, Generator);\n\n  _defineProperty(this, \"seed\", 0);\n\n  _defineProperty(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n\n  _defineProperty(this, \"value\", lcgRandom(this.seed));\n\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere.radius,\n      center = _defaultSphere$sphere.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere2 = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere2.radius,\n      center = _defaultSphere$sphere2.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle.radius,\n      center = _defaultCircle$circle.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle2 = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle2.radius,\n      center = _defaultCircle$circle2.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultRect$rect = _objectSpread2(_objectSpread2({}, defaultRect), rect),\n      sides = _defaultRect$rect.sides,\n      center = _defaultRect$rect.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box.sides,\n      center = _defaultBox$box.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box2 = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box2.sides,\n      center = _defaultBox$box2.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\n\nexport { Generator as G, inSphere as a, inCircle as b, onCircle as c, inRect as d, onRect as e, inBox as f, onBox as g, index as i, noise as n, onSphere as o };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,QAAQ,iCAAiC;AAC3F,SAASD,CAAC,IAAIE,eAAe,QAAQ,kCAAkC;AACvE,SAASC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,IAAI,QAAQ,wBAAwB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAIC,KAAK,GAAG,IAAI;EAEhBT,eAAe,CAAC,IAAI,EAAEK,IAAI,CAAC;EAE3BR,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,UAAUS,CAAC,EAAEC,CAAC,EAAE;IAC5C,OAAOE,KAAK,CAACH,CAAC,GAAGA,CAAC,GAAGG,KAAK,CAACF,CAAC,GAAGA,CAAC;EAClC,CAAC,CAAC;EAEFV,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,UAAUS,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC/C,OAAOC,KAAK,CAACH,CAAC,GAAGA,CAAC,GAAGG,KAAK,CAACF,CAAC,GAAGA,CAAC,GAAGE,KAAK,CAACD,CAAC,GAAGA,CAAC;EAChD,CAAC,CAAC;EAEF,IAAI,CAACF,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;AACZ,CAAC;AAED,IAAIE,KAAK,GAAG,CAAC,IAAIL,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5P,IAAIM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;AAE5pC,IAAIC,IAAI,GAAG,IAAIC,KAAK,CAAC,GAAG,CAAC;AACzB,IAAIC,KAAK,GAAG,IAAID,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B;;AAEA,IAAIE,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;IAC1B;IACAA,KAAK,IAAI,KAAK;EAChB;EAEAA,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;EAEzB,IAAIA,KAAK,GAAG,GAAG,EAAE;IACfA,KAAK,IAAIA,KAAK,IAAI,CAAC;EACrB;EAEA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5B,IAAIC,CAAC;IAEL,IAAID,CAAC,GAAG,CAAC,EAAE;MACTC,CAAC,GAAGT,CAAC,CAACQ,CAAC,CAAC,GAAGH,KAAK,GAAG,GAAG;IACxB,CAAC,MAAM;MACLI,CAAC,GAAGT,CAAC,CAACQ,CAAC,CAAC,GAAGH,KAAK,IAAI,CAAC,GAAG,GAAG;IAC7B;IAEAJ,IAAI,CAACO,CAAC,CAAC,GAAGP,IAAI,CAACO,CAAC,GAAG,GAAG,CAAC,GAAGC,CAAC;IAC3BN,KAAK,CAACK,CAAC,CAAC,GAAGL,KAAK,CAACK,CAAC,GAAG,GAAG,CAAC,GAAGT,KAAK,CAACU,CAAC,GAAG,EAAE,CAAC;EAC3C;AACF,CAAC;AACDL,IAAI,CAAC,CAAC,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIM,EAAE,GAAG,GAAG,IAAIJ,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACjC,IAAIC,EAAE,GAAG,CAAC,CAAC,GAAGN,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC/B,IAAIE,EAAE,GAAG,CAAC,GAAG,CAAC;AACd,IAAIC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;AAEhB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACzC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;EAChB;;EAEA,IAAIC,CAAC,GAAG,CAACL,GAAG,GAAGC,GAAG,IAAIP,EAAE,CAAC,CAAC;;EAE1B,IAAIF,CAAC,GAAGF,IAAI,CAACC,KAAK,CAACS,GAAG,GAAGK,CAAC,CAAC;EAC3B,IAAIC,CAAC,GAAGhB,IAAI,CAACC,KAAK,CAACU,GAAG,GAAGI,CAAC,CAAC;EAC3B,IAAIE,CAAC,GAAG,CAACf,CAAC,GAAGc,CAAC,IAAIV,EAAE;EACpB,IAAIY,EAAE,GAAGR,GAAG,GAAGR,CAAC,GAAGe,CAAC,CAAC,CAAC;;EAEtB,IAAIE,EAAE,GAAGR,GAAG,GAAGK,CAAC,GAAGC,CAAC,CAAC,CAAC;EACtB;;EAEA,IAAIG,EAAE,EAAEC,EAAE,CAAC,CAAC;;EAEZ,IAAIH,EAAE,GAAGC,EAAE,EAAE;IACX;IACAC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACL;IACAD,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;EACR,CAAC,CAAC;EACF;EACA;;EAGA,IAAIC,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGd,EAAE,CAAC,CAAC;;EAEvB,IAAIiB,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGf,EAAE;EACrB,IAAIkB,EAAE,GAAGN,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGZ,EAAE,CAAC,CAAC;;EAE1B,IAAImB,EAAE,GAAGN,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGb,EAAE,CAAC,CAAC;;EAE1BJ,CAAC,IAAI,GAAG;EACRc,CAAC,IAAI,GAAG;EACR,IAAIU,GAAG,GAAG7B,KAAK,CAACK,CAAC,GAAGP,IAAI,CAACqB,CAAC,CAAC,CAAC;EAC5B,IAAIW,GAAG,GAAG9B,KAAK,CAACK,CAAC,GAAGkB,EAAE,GAAGzB,IAAI,CAACqB,CAAC,GAAGK,EAAE,CAAC,CAAC;EACtC,IAAIO,GAAG,GAAG/B,KAAK,CAACK,CAAC,GAAG,CAAC,GAAGP,IAAI,CAACqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtC,IAAIa,EAAE,GAAG,GAAG,GAAGX,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAEhC,IAAIU,EAAE,GAAG,CAAC,EAAE;IACVjB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLiB,EAAE,IAAIA,EAAE;IACRjB,EAAE,GAAGiB,EAAE,GAAGA,EAAE,GAAGH,GAAG,CAACI,IAAI,CAACZ,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;EACnC;EAEA,IAAIY,EAAE,GAAG,GAAG,GAAGT,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAEhC,IAAIQ,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACG,IAAI,CAACR,EAAE,EAAEC,EAAE,CAAC;EACjC;EAEA,IAAIS,EAAE,GAAG,GAAG,GAAGR,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAEhC,IAAIO,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACE,IAAI,CAACN,EAAE,EAAEC,EAAE,CAAC;EACjC,CAAC,CAAC;EACF;;EAGA,OAAO,EAAE,IAAIb,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;AAC5B,CAAC,CAAC,CAAC;;AAEH,IAAImB,QAAQ,GAAG,SAASA,QAAQA,CAACvB,GAAG,EAAEC,GAAG,EAAEuB,GAAG,EAAE;EAC9C,IAAItB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEqB,EAAE,CAAC,CAAC;EACpB;;EAEA,IAAIpB,CAAC,GAAG,CAACL,GAAG,GAAGC,GAAG,GAAGuB,GAAG,IAAI3B,EAAE,CAAC,CAAC;;EAEhC,IAAIL,CAAC,GAAGF,IAAI,CAACC,KAAK,CAACS,GAAG,GAAGK,CAAC,CAAC;EAC3B,IAAIC,CAAC,GAAGhB,IAAI,CAACC,KAAK,CAACU,GAAG,GAAGI,CAAC,CAAC;EAC3B,IAAIqB,CAAC,GAAGpC,IAAI,CAACC,KAAK,CAACiC,GAAG,GAAGnB,CAAC,CAAC;EAC3B,IAAIE,CAAC,GAAG,CAACf,CAAC,GAAGc,CAAC,GAAGoB,CAAC,IAAI5B,EAAE;EACxB,IAAIU,EAAE,GAAGR,GAAG,GAAGR,CAAC,GAAGe,CAAC,CAAC,CAAC;;EAEtB,IAAIE,EAAE,GAAGR,GAAG,GAAGK,CAAC,GAAGC,CAAC;EACpB,IAAIoB,EAAE,GAAGH,GAAG,GAAGE,CAAC,GAAGnB,CAAC,CAAC,CAAC;EACtB;;EAEA,IAAIG,EAAE,EAAEC,EAAE,EAAEiB,EAAE,CAAC,CAAC;;EAEhB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;;EAEhB,IAAIvB,EAAE,IAAIC,EAAE,EAAE;IACZ,IAAIA,EAAE,IAAIkB,EAAE,EAAE;MACZjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAIvB,EAAE,IAAImB,EAAE,EAAE;MACnBjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM;MACLrB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR;EACF,CAAC,MAAM;IACL,IAAItB,EAAE,GAAGkB,EAAE,EAAE;MACXjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAIvB,EAAE,GAAGmB,EAAE,EAAE;MAClBjB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM;MACLrB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNiB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IACR;EACF,CAAC,CAAC;EACF;EACA;EACA;;EAGA,IAAInB,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGZ,EAAE,CAAC,CAAC;;EAEvB,IAAIe,EAAE,GAAGJ,EAAE,GAAGE,EAAE,GAAGb,EAAE;EACrB,IAAIkC,EAAE,GAAGL,EAAE,GAAGC,EAAE,GAAG9B,EAAE;EACrB,IAAIgB,EAAE,GAAGN,EAAE,GAAGqB,EAAE,GAAG,CAAC,GAAG/B,EAAE,CAAC,CAAC;;EAE3B,IAAIiB,EAAE,GAAGN,EAAE,GAAGqB,EAAE,GAAG,CAAC,GAAGhC,EAAE;EACzB,IAAImC,EAAE,GAAGN,EAAE,GAAGI,EAAE,GAAG,CAAC,GAAGjC,EAAE;EACzB,IAAIoC,EAAE,GAAG1B,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGV,EAAE,CAAC,CAAC;;EAE1B,IAAIqC,EAAE,GAAG1B,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGX,EAAE;EACxB,IAAIsC,EAAE,GAAGT,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG7B,EAAE,CAAC,CAAC;;EAE1BN,CAAC,IAAI,GAAG;EACRc,CAAC,IAAI,GAAG;EACRoB,CAAC,IAAI,GAAG;EACR,IAAIV,GAAG,GAAG7B,KAAK,CAACK,CAAC,GAAGP,IAAI,CAACqB,CAAC,GAAGrB,IAAI,CAACyC,CAAC,CAAC,CAAC,CAAC;EACtC,IAAIT,GAAG,GAAG9B,KAAK,CAACK,CAAC,GAAGkB,EAAE,GAAGzB,IAAI,CAACqB,CAAC,GAAGK,EAAE,GAAG1B,IAAI,CAACyC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC;EACrD,IAAIV,GAAG,GAAG/B,KAAK,CAACK,CAAC,GAAGqC,EAAE,GAAG5C,IAAI,CAACqB,CAAC,GAAGwB,EAAE,GAAG7C,IAAI,CAACyC,CAAC,GAAGK,EAAE,CAAC,CAAC,CAAC;EACrD,IAAIM,GAAG,GAAGlD,KAAK,CAACK,CAAC,GAAG,CAAC,GAAGP,IAAI,CAACqB,CAAC,GAAG,CAAC,GAAGrB,IAAI,CAACyC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpD,IAAIP,EAAE,GAAG,GAAG,GAAGX,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGkB,EAAE,GAAGA,EAAE;EAE1C,IAAIR,EAAE,GAAG,CAAC,EAAE;IACVjB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLiB,EAAE,IAAIA,EAAE;IACRjB,EAAE,GAAGiB,EAAE,GAAGA,EAAE,GAAGH,GAAG,CAACsB,IAAI,CAAC9B,EAAE,EAAEC,EAAE,EAAEkB,EAAE,CAAC,CAAC,CAAC;EACvC;EAEA,IAAIN,EAAE,GAAG,GAAG,GAAGT,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGmB,EAAE,GAAGA,EAAE;EAE1C,IAAIX,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACqB,IAAI,CAAC1B,EAAE,EAAEC,EAAE,EAAEmB,EAAE,CAAC;EACrC;EAEA,IAAIV,EAAE,GAAG,GAAG,GAAGR,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGkB,EAAE,GAAGA,EAAE;EAE1C,IAAIX,EAAE,GAAG,CAAC,EAAE;IACVlB,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLkB,EAAE,IAAIA,EAAE;IACRlB,EAAE,GAAGkB,EAAE,GAAGA,EAAE,GAAGJ,GAAG,CAACoB,IAAI,CAACxB,EAAE,EAAEC,EAAE,EAAEkB,EAAE,CAAC;EACrC;EAEA,IAAIM,EAAE,GAAG,GAAG,GAAGL,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;EAE1C,IAAIG,EAAE,GAAG,CAAC,EAAE;IACVd,EAAE,GAAG,CAAC;EACR,CAAC,MAAM;IACLc,EAAE,IAAIA,EAAE;IACRd,EAAE,GAAGc,EAAE,GAAGA,EAAE,GAAGF,GAAG,CAACC,IAAI,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACrC,CAAC,CAAC;EACF;;EAGA,OAAO,EAAE,IAAIlC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGqB,EAAE,CAAC;AACjC,CAAC,CAAC,CAAC;AACH;;AAEA,IAAIe,OAAO,GAAG,SAASA,OAAOA,CAAC7D,CAAC,EAAEC,CAAC,EAAE;EACnC;EACA,IAAI6D,CAAC,GAAGnD,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC;IACjB+D,CAAC,GAAGpD,IAAI,CAACC,KAAK,CAACX,CAAC,CAAC,CAAC,CAAC;;EAEvBD,CAAC,GAAGA,CAAC,GAAG8D,CAAC;EACT7D,CAAC,GAAGA,CAAC,GAAG8D,CAAC,CAAC,CAAC;;EAEXD,CAAC,GAAGA,CAAC,GAAG,GAAG;EACXC,CAAC,GAAGA,CAAC,GAAG,GAAG,CAAC,CAAC;;EAEb,IAAIC,GAAG,GAAGxD,KAAK,CAACsD,CAAC,GAAGxD,IAAI,CAACyD,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACzC,CAAC,EAAEC,CAAC,CAAC;EACvC,IAAIgE,GAAG,GAAGzD,KAAK,CAACsD,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAG,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACzC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EAC/C,IAAIiE,GAAG,GAAG1D,KAAK,CAACsD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAACyD,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACzC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;EAC/C,IAAIkE,GAAG,GAAG3D,KAAK,CAACsD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAG,CAAC,CAAC,CAAC,CAACtB,IAAI,CAACzC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEzD,IAAImE,CAAC,GAAGtE,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC;;EAEjB,OAAOJ,IAAI,CAACA,IAAI,CAACoE,GAAG,EAAEE,GAAG,EAAEE,CAAC,CAAC,EAAExE,IAAI,CAACqE,GAAG,EAAEE,GAAG,EAAEC,CAAC,CAAC,EAAEtE,IAAI,CAACG,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC;;AAEH,IAAIoE,OAAO,GAAG,SAASA,OAAOA,CAACrE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACtC;EACA,IAAI4D,CAAC,GAAGnD,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC;IACjB+D,CAAC,GAAGpD,IAAI,CAACC,KAAK,CAACX,CAAC,CAAC;IACjBqE,CAAC,GAAG3D,IAAI,CAACC,KAAK,CAACV,CAAC,CAAC,CAAC,CAAC;;EAEvBF,CAAC,GAAGA,CAAC,GAAG8D,CAAC;EACT7D,CAAC,GAAGA,CAAC,GAAG8D,CAAC;EACT7D,CAAC,GAAGA,CAAC,GAAGoE,CAAC,CAAC,CAAC;;EAEXR,CAAC,GAAGA,CAAC,GAAG,GAAG;EACXC,CAAC,GAAGA,CAAC,GAAG,GAAG;EACXO,CAAC,GAAGA,CAAC,GAAG,GAAG,CAAC,CAAC;;EAEb,IAAIC,IAAI,GAAG/D,KAAK,CAACsD,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAGzD,IAAI,CAACgE,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACrD,IAAIsE,IAAI,GAAGhE,KAAK,CAACsD,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAGzD,IAAI,CAACgE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EAC7D,IAAIuE,IAAI,GAAGjE,KAAK,CAACsD,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAG,CAAC,GAAGzD,IAAI,CAACgE,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;EAC7D,IAAIwE,IAAI,GAAGlE,KAAK,CAACsD,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAG,CAAC,GAAGzD,IAAI,CAACgE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EACrE,IAAIyE,IAAI,GAAGnE,KAAK,CAACsD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAGzD,IAAI,CAACgE,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC7D,IAAI0E,IAAI,GAAGpE,KAAK,CAACsD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAGzD,IAAI,CAACgE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EACrE,IAAI2E,IAAI,GAAGrE,KAAK,CAACsD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAG,CAAC,GAAGzD,IAAI,CAACgE,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;EACrE,IAAI4E,IAAI,GAAGtE,KAAK,CAACsD,CAAC,GAAG,CAAC,GAAGxD,IAAI,CAACyD,CAAC,GAAG,CAAC,GAAGzD,IAAI,CAACgE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC3D,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAE/E,IAAIkE,CAAC,GAAGtE,IAAI,CAACE,CAAC,CAAC;EACf,IAAIc,CAAC,GAAGhB,IAAI,CAACG,CAAC,CAAC;EACf,IAAI8E,CAAC,GAAGjF,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC;;EAEjB,OAAON,IAAI,CAACA,IAAI,CAACA,IAAI,CAAC2E,IAAI,EAAEI,IAAI,EAAEP,CAAC,CAAC,EAAExE,IAAI,CAAC4E,IAAI,EAAEI,IAAI,EAAER,CAAC,CAAC,EAAEW,CAAC,CAAC,EAAEnF,IAAI,CAACA,IAAI,CAAC6E,IAAI,EAAEI,IAAI,EAAET,CAAC,CAAC,EAAExE,IAAI,CAAC8E,IAAI,EAAEI,IAAI,EAAEV,CAAC,CAAC,EAAEW,CAAC,CAAC,EAAEjE,CAAC,CAAC;AACtH,CAAC;AAED,IAAIkE,KAAK,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EACrCC,SAAS,EAAE,IAAI;EACf1E,IAAI,EAAEA,IAAI;EACVW,QAAQ,EAAEA,QAAQ;EAClBwB,QAAQ,EAAEA,QAAQ;EAClBiB,OAAO,EAAEA,OAAO;EAChBQ,OAAO,EAAEA;AACX,CAAC,CAAC;AAEF,IAAIe,GAAG,GAAGzE,IAAI,CAAC0E,EAAE,GAAG,CAAC,CAAC,CAAC;;AAEvB,SAASC,aAAaA,CAAC7E,IAAI,EAAE;EAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAGE,IAAI,CAAC4E,GAAG,CAAC9E,IAAI,CAAC;EACvB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnC,IAAI+E,MAAM,GAAG/E,IAAI;IACjBA,IAAI,GAAG,CAAC;IAER,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2E,MAAM,CAACC,MAAM,EAAE5E,CAAC,EAAE,EAAE;MACtCJ,IAAI,GAAG,CAACA,IAAI,GAAG,CAACI,CAAC,GAAG,CAAC,KAAK2E,MAAM,CAACE,UAAU,CAAC7E,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,UAAU;IACpE;EACF;EAEA,IAAIJ,IAAI,KAAK,CAAC,EAAE;IACdA,IAAI,GAAG,GAAG;EACZ;EAEA,OAAOA,IAAI;AACb;AAEA,SAASkF,SAASA,CAAClF,IAAI,EAAE;EACvB,IAAImF,KAAK,GAAGN,aAAa,CAAC7E,IAAI,CAAC;EAC/B,OAAO,YAAY;IACjB,IAAIoF,MAAM,GAAGD,KAAK,GAAG,KAAK,GAAG,UAAU;IACvCA,KAAK,GAAGC,MAAM;IACd,OAAOA,MAAM,GAAG,UAAU;EAC5B,CAAC;AACH;AAEA,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACpF,KAAK,EAAE;EACxC,IAAIP,KAAK,GAAG,IAAI;EAEhBT,eAAe,CAAC,IAAI,EAAEoG,SAAS,CAAC;EAEhCvG,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;EAEhCA,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,UAAUkB,IAAI,EAAE;IAC5CN,KAAK,CAACM,IAAI,GAAGA,IAAI;IACjBN,KAAK,CAAC4F,KAAK,GAAGJ,SAAS,CAAClF,IAAI,CAAC;EAC/B,CAAC,CAAC;EAEFlB,eAAe,CAAC,IAAI,EAAE,OAAO,EAAEoG,SAAS,CAAC,IAAI,CAAClF,IAAI,CAAC,CAAC;EAEpD,IAAI,CAACuF,IAAI,CAACtF,KAAK,CAAC;AAClB,CAAC;AACD,IAAIuF,UAAU,GAAG,IAAIH,SAAS,CAACnF,IAAI,CAACuF,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;;AAEA,IAAIC,aAAa,GAAG;EAClBC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAClB,CAAC,CAAC,CAAC;AACH;AACA;;AAEA,SAASC,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAIC,GAAG,GAAGC,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGT,UAAU;EAExF,IAAIW,qBAAqB,GAAGnH,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE0G,aAAa,CAAC,EAAEK,MAAM,CAAC;IACjFJ,MAAM,GAAGQ,qBAAqB,CAACR,MAAM;IACrCC,MAAM,GAAGO,qBAAqB,CAACP,MAAM;EAEzC,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,MAAM,CAACd,MAAM,EAAE5E,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIuD,CAAC,GAAGqC,GAAG,CAACV,KAAK,CAAC,CAAC;IACnB,IAAIjF,CAAC,GAAG2F,GAAG,CAACV,KAAK,CAAC,CAAC;IACnB,IAAIc,KAAK,GAAGlG,IAAI,CAACmG,IAAI,CAAC,CAAC,GAAGhG,CAAC,GAAG,CAAC,CAAC;IAChC,IAAIiG,GAAG,GAAG3B,GAAG,GAAGhB,CAAC;IACjBmC,MAAM,CAAC1F,CAAC,CAAC,GAAGF,IAAI,CAACqG,GAAG,CAACH,KAAK,CAAC,GAAGlG,IAAI,CAACsG,GAAG,CAACF,GAAG,CAAC,GAAGX,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IAChEE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAGF,IAAI,CAACqG,GAAG,CAACH,KAAK,CAAC,GAAGlG,IAAI,CAACqG,GAAG,CAACD,GAAG,CAAC,GAAGX,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IACpEE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAGF,IAAI,CAACsG,GAAG,CAACJ,KAAK,CAAC,GAAGT,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;EACtD;EAEA,OAAOE,MAAM;AACf,CAAC,CAAC;;AAEF,SAASW,QAAQA,CAACX,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAIC,GAAG,GAAGC,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGT,UAAU;EAExF,IAAIkB,sBAAsB,GAAG1H,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE0G,aAAa,CAAC,EAAEK,MAAM,CAAC;IAClFJ,MAAM,GAAGe,sBAAsB,CAACf,MAAM;IACtCC,MAAM,GAAGc,sBAAsB,CAACd,MAAM;EAE1C,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,MAAM,CAACd,MAAM,EAAE5E,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIuD,CAAC,GAAGzD,IAAI,CAACyG,GAAG,CAACX,GAAG,CAACV,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI/F,CAAC,GAAGyG,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAI9F,CAAC,GAAGwG,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAI7F,CAAC,GAAGuG,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAIsB,GAAG,GAAG1G,IAAI,CAACK,IAAI,CAAChB,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;IAC1CF,CAAC,GAAGoE,CAAC,GAAGpE,CAAC,GAAGqH,GAAG;IACfpH,CAAC,GAAGmE,CAAC,GAAGnE,CAAC,GAAGoH,GAAG;IACfnH,CAAC,GAAGkE,CAAC,GAAGlE,CAAC,GAAGmH,GAAG;IACfd,MAAM,CAAC1F,CAAC,CAAC,GAAGb,CAAC,GAAGoG,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IAClCE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAGZ,CAAC,GAAGmG,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IACtCE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAGX,CAAC,GAAGkG,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;EACxC;EAEA,OAAOE,MAAM;AACf;AACA;AACA;AACA;;AAEA,IAAIe,aAAa,GAAG;EAClBlB,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAC;;AAEH,SAASkB,QAAQA,CAAChB,MAAM,EAAEiB,MAAM,EAAE;EAChC,IAAIf,GAAG,GAAGC,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGT,UAAU;EAExF,IAAIwB,qBAAqB,GAAGhI,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6H,aAAa,CAAC,EAAEE,MAAM,CAAC;IACjFpB,MAAM,GAAGqB,qBAAqB,CAACrB,MAAM;IACrCC,MAAM,GAAGoB,qBAAqB,CAACpB,MAAM;EAEzC,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,MAAM,CAACd,MAAM,EAAE5E,CAAC,IAAI,CAAC,EAAE;IACzC,IAAI6G,CAAC,GAAGtB,MAAM,GAAGzF,IAAI,CAACK,IAAI,CAACyF,GAAG,CAACV,KAAK,CAAC,CAAC,CAAC;IACvC,IAAIc,KAAK,GAAGJ,GAAG,CAACV,KAAK,CAAC,CAAC,GAAGX,GAAG;IAC7BmB,MAAM,CAAC1F,CAAC,CAAC,GAAGF,IAAI,CAACqG,GAAG,CAACH,KAAK,CAAC,GAAGa,CAAC,GAAGrB,MAAM,CAAC,CAAC,CAAC;IAC3CE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAGF,IAAI,CAACsG,GAAG,CAACJ,KAAK,CAAC,GAAGa,CAAC,GAAGrB,MAAM,CAAC,CAAC,CAAC;EACjD;EAEA,OAAOE,MAAM;AACf;AACA,SAASoB,QAAQA,CAACpB,MAAM,EAAEiB,MAAM,EAAE;EAChC,IAAIf,GAAG,GAAGC,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGT,UAAU;EAExF,IAAI2B,sBAAsB,GAAGnI,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6H,aAAa,CAAC,EAAEE,MAAM,CAAC;IAClFpB,MAAM,GAAGwB,sBAAsB,CAACxB,MAAM;IACtCC,MAAM,GAAGuB,sBAAsB,CAACvB,MAAM;EAE1C,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,MAAM,CAACd,MAAM,EAAE5E,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIgG,KAAK,GAAGJ,GAAG,CAACV,KAAK,CAAC,CAAC,GAAGX,GAAG;IAC7BmB,MAAM,CAAC1F,CAAC,CAAC,GAAGF,IAAI,CAACqG,GAAG,CAACH,KAAK,CAAC,GAAGT,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;IAChDE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAGF,IAAI,CAACsG,GAAG,CAACJ,KAAK,CAAC,GAAGT,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAC;EACtD;EAEA,OAAOE,MAAM;AACf;AACA;AACA;AACA;;AAEA,IAAIsB,WAAW,GAAG;EAChBC,KAAK,EAAE,CAAC;EACRzB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;AACf,CAAC;AACD,SAAS0B,MAAMA,CAACxB,MAAM,EAAEyB,IAAI,EAAE;EAC5B,IAAIvB,GAAG,GAAGC,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGT,UAAU;EAExF,IAAIgC,iBAAiB,GAAGxI,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoI,WAAW,CAAC,EAAEG,IAAI,CAAC;IACzEF,KAAK,GAAGG,iBAAiB,CAACH,KAAK;IAC/BzB,MAAM,GAAG4B,iBAAiB,CAAC5B,MAAM;EAErC,IAAI6B,KAAK,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIK,KAAK,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAExD,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,MAAM,CAACd,MAAM,EAAE5E,CAAC,IAAI,CAAC,EAAE;IACzC0F,MAAM,CAAC1F,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAImC,KAAK,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACnDE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIoC,KAAK,GAAG9B,MAAM,CAAC,CAAC,CAAC;EACzD;EAEA,OAAOE,MAAM;AACf;AACA,SAAS6B,MAAMA,CAAC7B,MAAM,EAAEyB,IAAI,EAAE;EAC5B,OAAOzB,MAAM;AACf;AACA;AACA;AACA;;AAEA,SAAS8B,KAAKA,CAAC9B,MAAM,EAAE+B,GAAG,EAAE;EAC1B,IAAI7B,GAAG,GAAGC,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGT,UAAU;EAExF,IAAIsC,eAAe,GAAG9I,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE+I,UAAU,CAAC,EAAEF,GAAG,CAAC;IACrER,KAAK,GAAGS,eAAe,CAACT,KAAK;IAC7BzB,MAAM,GAAGkC,eAAe,CAAClC,MAAM;EAEnC,IAAI6B,KAAK,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIK,KAAK,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIW,KAAK,GAAG,OAAOX,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAExD,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,MAAM,CAACd,MAAM,EAAE5E,CAAC,IAAI,CAAC,EAAE;IACzC0F,MAAM,CAAC1F,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAImC,KAAK,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACnDE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIoC,KAAK,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACvDE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI0C,KAAK,GAAGpC,MAAM,CAAC,CAAC,CAAC;EACzD;EAEA,OAAOE,MAAM;AACf;AACA,IAAIiC,UAAU,GAAG;EACfV,KAAK,EAAE,CAAC;EACRzB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAClB,CAAC;AACD,SAASqC,KAAKA,CAACnC,MAAM,EAAE+B,GAAG,EAAE;EAC1B,IAAI7B,GAAG,GAAGC,SAAS,CAACjB,MAAM,GAAG,CAAC,IAAIiB,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGT,UAAU;EAExF,IAAI0C,gBAAgB,GAAGlJ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE+I,UAAU,CAAC,EAAEF,GAAG,CAAC;IACtER,KAAK,GAAGa,gBAAgB,CAACb,KAAK;IAC9BzB,MAAM,GAAGsC,gBAAgB,CAACtC,MAAM;EAEpC,IAAI6B,KAAK,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIK,KAAK,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EACxD,IAAIW,KAAK,GAAG,OAAOX,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAExD,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,MAAM,CAACd,MAAM,EAAE5E,CAAC,IAAI,CAAC,EAAE;IACzC0F,MAAM,CAAC1F,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAImC,KAAK,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACnDE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAIoC,KAAK,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACvDE,MAAM,CAAC1F,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC4F,GAAG,CAACV,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI0C,KAAK,GAAGpC,MAAM,CAAC,CAAC,CAAC;EACzD;EAEA,OAAOE,MAAM;AACf;AAEA,IAAIqC,KAAK,GAAG,aAAa3D,MAAM,CAACC,MAAM,CAAC;EACrCC,SAAS,EAAE,IAAI;EACfW,SAAS,EAAEA,SAAS;EACpBQ,QAAQ,EAAEA,QAAQ;EAClBY,QAAQ,EAAEA,QAAQ;EAClBK,QAAQ,EAAEA,QAAQ;EAClBI,QAAQ,EAAEA,QAAQ;EAClBI,MAAM,EAAEA,MAAM;EACdK,MAAM,EAAEA,MAAM;EACdC,KAAK,EAAEA,KAAK;EACZK,KAAK,EAAEA,KAAK;EACZ1D,KAAK,EAAEA;AACT,CAAC,CAAC;AAEF,SAASc,SAAS,IAAI+C,CAAC,EAAE3B,QAAQ,IAAI5H,CAAC,EAAEiI,QAAQ,IAAIuB,CAAC,EAAEnB,QAAQ,IAAIoB,CAAC,EAAEhB,MAAM,IAAIiB,CAAC,EAAEZ,MAAM,IAAIa,CAAC,EAAEZ,KAAK,IAAIxI,CAAC,EAAE6I,KAAK,IAAIQ,CAAC,EAAEN,KAAK,IAAI/H,CAAC,EAAEmE,KAAK,IAAImE,CAAC,EAAE7C,QAAQ,IAAI8C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}