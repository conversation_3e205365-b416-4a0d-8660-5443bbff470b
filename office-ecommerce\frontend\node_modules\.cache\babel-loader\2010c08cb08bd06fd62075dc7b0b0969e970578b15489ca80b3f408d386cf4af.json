{"ast": null, "code": "import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nfunction CycleRaycast({\n  onChanged,\n  portal,\n  preventDefault = true,\n  scroll = true,\n  keyCode = 9\n}) {\n  const cycle = React.useRef(0);\n  const setEvents = useThree(state => state.setEvents);\n  const get = useThree(state => state.get);\n  const gl = useThree(state => state.gl);\n  React.useEffect(() => {\n    var _portal$current;\n    let hits = [];\n    let lastEvent = undefined;\n    const prev = get().events.filter;\n    const target = (_portal$current = portal == null ? void 0 : portal.current) !== null && _portal$current !== void 0 ? _portal$current : gl.domElement.parentNode; // Render custom status\n\n    const renderStatus = () => target && onChanged && onChanged(hits, Math.round(cycle.current) % hits.length); // Overwrite the raycasters custom filter (this only exists in r3f)\n\n    setEvents({\n      filter: (intersections, state) => {\n        // Reset cycle when the intersections change\n        let clone = [...intersections];\n        if (clone.length !== hits.length || !hits.every(hit => clone.map(e => e.object.uuid).includes(hit.object.uuid))) {\n          cycle.current = 0;\n          hits = clone;\n          renderStatus();\n        } // Run custom filter if there is one\n\n        if (prev) clone = prev(clone, state); // Cycle through the actual raycast intersects\n\n        for (let i = 0; i < Math.round(cycle.current) % clone.length; i++) {\n          const first = clone.shift();\n          clone = [...clone, first];\n        }\n        return clone;\n      }\n    }); // Cycle, refresh events and render status\n\n    const refresh = fn => {\n      var _get$events$handlers, _get$events$handlers2;\n      cycle.current = fn(cycle.current); // Cancel hovered elements and fake a pointer-move\n\n      (_get$events$handlers = get().events.handlers) == null ? void 0 : _get$events$handlers.onPointerCancel(undefined);\n      (_get$events$handlers2 = get().events.handlers) == null ? void 0 : _get$events$handlers2.onPointerMove(lastEvent);\n      renderStatus();\n    }; // Key events\n\n    const tabEvent = event => {\n      if (event.keyCode || event.which === keyCode) {\n        if (preventDefault) event.preventDefault();\n        if (hits.length > 1) refresh(current => current + 1);\n      }\n    }; // Wheel events\n\n    const wheelEvent = event => {\n      if (preventDefault) event.preventDefault();\n      let delta = 0;\n      if (!event) event = window.event;\n      if (event.wheelDelta) delta = event.wheelDelta / 120;else if (event.detail) delta = -event.detail / 3;\n      if (hits.length > 1) refresh(current => Math.abs(current - delta));\n    }; // Catch last move event and position custom status\n\n    const moveEvent = event => lastEvent = event;\n    document.addEventListener('pointermove', moveEvent, {\n      passive: true\n    });\n    if (scroll) document.addEventListener('wheel', wheelEvent);\n    if (keyCode !== undefined) document.addEventListener('keydown', tabEvent);\n    return () => {\n      // Clean up\n      setEvents({\n        filter: prev\n      });\n      if (keyCode !== undefined) document.removeEventListener('keydown', tabEvent);\n      if (scroll) document.removeEventListener('wheel', wheelEvent);\n      document.removeEventListener('pointermove', moveEvent);\n    };\n  }, [gl, get, setEvents, preventDefault, scroll, keyCode]);\n  return null;\n}\nexport { CycleRaycast };", "map": {"version": 3, "names": ["React", "useThree", "CycleRaycast", "onChanged", "portal", "preventDefault", "scroll", "keyCode", "cycle", "useRef", "setEvents", "state", "get", "gl", "useEffect", "_portal$current", "hits", "lastEvent", "undefined", "prev", "events", "filter", "target", "current", "dom<PERSON>lement", "parentNode", "renderStatus", "Math", "round", "length", "intersections", "clone", "every", "hit", "map", "e", "object", "uuid", "includes", "i", "first", "shift", "refresh", "fn", "_get$events$handlers", "_get$events$handlers2", "handlers", "onPointerCancel", "onPointerMove", "tabEvent", "event", "which", "wheelEvent", "delta", "window", "wheelDelta", "detail", "abs", "moveEvent", "document", "addEventListener", "passive", "removeEventListener"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/CycleRaycast.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction CycleRaycast({\n  onChanged,\n  portal,\n  preventDefault = true,\n  scroll = true,\n  keyCode = 9\n}) {\n  const cycle = React.useRef(0);\n  const setEvents = useThree(state => state.setEvents);\n  const get = useThree(state => state.get);\n  const gl = useThree(state => state.gl);\n  React.useEffect(() => {\n    var _portal$current;\n\n    let hits = [];\n    let lastEvent = undefined;\n    const prev = get().events.filter;\n    const target = (_portal$current = portal == null ? void 0 : portal.current) !== null && _portal$current !== void 0 ? _portal$current : gl.domElement.parentNode; // Render custom status\n\n    const renderStatus = () => target && onChanged && onChanged(hits, Math.round(cycle.current) % hits.length); // Overwrite the raycasters custom filter (this only exists in r3f)\n\n\n    setEvents({\n      filter: (intersections, state) => {\n        // Reset cycle when the intersections change\n        let clone = [...intersections];\n\n        if (clone.length !== hits.length || !hits.every(hit => clone.map(e => e.object.uuid).includes(hit.object.uuid))) {\n          cycle.current = 0;\n          hits = clone;\n          renderStatus();\n        } // Run custom filter if there is one\n\n\n        if (prev) clone = prev(clone, state); // Cycle through the actual raycast intersects\n\n        for (let i = 0; i < Math.round(cycle.current) % clone.length; i++) {\n          const first = clone.shift();\n          clone = [...clone, first];\n        }\n\n        return clone;\n      }\n    }); // Cycle, refresh events and render status\n\n    const refresh = fn => {\n      var _get$events$handlers, _get$events$handlers2;\n\n      cycle.current = fn(cycle.current); // Cancel hovered elements and fake a pointer-move\n\n      (_get$events$handlers = get().events.handlers) == null ? void 0 : _get$events$handlers.onPointerCancel(undefined);\n      (_get$events$handlers2 = get().events.handlers) == null ? void 0 : _get$events$handlers2.onPointerMove(lastEvent);\n      renderStatus();\n    }; // Key events\n\n\n    const tabEvent = event => {\n      if (event.keyCode || event.which === keyCode) {\n        if (preventDefault) event.preventDefault();\n        if (hits.length > 1) refresh(current => current + 1);\n      }\n    }; // Wheel events\n\n\n    const wheelEvent = event => {\n      if (preventDefault) event.preventDefault();\n      let delta = 0;\n      if (!event) event = window.event;\n      if (event.wheelDelta) delta = event.wheelDelta / 120;else if (event.detail) delta = -event.detail / 3;\n      if (hits.length > 1) refresh(current => Math.abs(current - delta));\n    }; // Catch last move event and position custom status\n\n\n    const moveEvent = event => lastEvent = event;\n\n    document.addEventListener('pointermove', moveEvent, {\n      passive: true\n    });\n    if (scroll) document.addEventListener('wheel', wheelEvent);\n    if (keyCode !== undefined) document.addEventListener('keydown', tabEvent);\n    return () => {\n      // Clean up\n      setEvents({\n        filter: prev\n      });\n      if (keyCode !== undefined) document.removeEventListener('keydown', tabEvent);\n      if (scroll) document.removeEventListener('wheel', wheelEvent);\n      document.removeEventListener('pointermove', moveEvent);\n    };\n  }, [gl, get, setEvents, preventDefault, scroll, keyCode]);\n  return null;\n}\n\nexport { CycleRaycast };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,YAAYA,CAAC;EACpBC,SAAS;EACTC,MAAM;EACNC,cAAc,GAAG,IAAI;EACrBC,MAAM,GAAG,IAAI;EACbC,OAAO,GAAG;AACZ,CAAC,EAAE;EACD,MAAMC,KAAK,GAAGR,KAAK,CAACS,MAAM,CAAC,CAAC,CAAC;EAC7B,MAAMC,SAAS,GAAGT,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACD,SAAS,CAAC;EACpD,MAAME,GAAG,GAAGX,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACC,GAAG,CAAC;EACxC,MAAMC,EAAE,GAAGZ,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACE,EAAE,CAAC;EACtCb,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IAAIC,eAAe;IAEnB,IAAIC,IAAI,GAAG,EAAE;IACb,IAAIC,SAAS,GAAGC,SAAS;IACzB,MAAMC,IAAI,GAAGP,GAAG,CAAC,CAAC,CAACQ,MAAM,CAACC,MAAM;IAChC,MAAMC,MAAM,GAAG,CAACP,eAAe,GAAGX,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACmB,OAAO,MAAM,IAAI,IAAIR,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGF,EAAE,CAACW,UAAU,CAACC,UAAU,CAAC,CAAC;;IAEjK,MAAMC,YAAY,GAAGA,CAAA,KAAMJ,MAAM,IAAInB,SAAS,IAAIA,SAAS,CAACa,IAAI,EAAEW,IAAI,CAACC,KAAK,CAACpB,KAAK,CAACe,OAAO,CAAC,GAAGP,IAAI,CAACa,MAAM,CAAC,CAAC,CAAC;;IAG5GnB,SAAS,CAAC;MACRW,MAAM,EAAEA,CAACS,aAAa,EAAEnB,KAAK,KAAK;QAChC;QACA,IAAIoB,KAAK,GAAG,CAAC,GAAGD,aAAa,CAAC;QAE9B,IAAIC,KAAK,CAACF,MAAM,KAAKb,IAAI,CAACa,MAAM,IAAI,CAACb,IAAI,CAACgB,KAAK,CAACC,GAAG,IAAIF,KAAK,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,CAACC,IAAI,CAAC,CAACC,QAAQ,CAACL,GAAG,CAACG,MAAM,CAACC,IAAI,CAAC,CAAC,EAAE;UAC/G7B,KAAK,CAACe,OAAO,GAAG,CAAC;UACjBP,IAAI,GAAGe,KAAK;UACZL,YAAY,CAAC,CAAC;QAChB,CAAC,CAAC;;QAGF,IAAIP,IAAI,EAAEY,KAAK,GAAGZ,IAAI,CAACY,KAAK,EAAEpB,KAAK,CAAC,CAAC,CAAC;;QAEtC,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,IAAI,CAACC,KAAK,CAACpB,KAAK,CAACe,OAAO,CAAC,GAAGQ,KAAK,CAACF,MAAM,EAAEU,CAAC,EAAE,EAAE;UACjE,MAAMC,KAAK,GAAGT,KAAK,CAACU,KAAK,CAAC,CAAC;UAC3BV,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAES,KAAK,CAAC;QAC3B;QAEA,OAAOT,KAAK;MACd;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,MAAMW,OAAO,GAAGC,EAAE,IAAI;MACpB,IAAIC,oBAAoB,EAAEC,qBAAqB;MAE/CrC,KAAK,CAACe,OAAO,GAAGoB,EAAE,CAACnC,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;;MAEnC,CAACqB,oBAAoB,GAAGhC,GAAG,CAAC,CAAC,CAACQ,MAAM,CAAC0B,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,oBAAoB,CAACG,eAAe,CAAC7B,SAAS,CAAC;MACjH,CAAC2B,qBAAqB,GAAGjC,GAAG,CAAC,CAAC,CAACQ,MAAM,CAAC0B,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACG,aAAa,CAAC/B,SAAS,CAAC;MACjHS,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC;;IAGH,MAAMuB,QAAQ,GAAGC,KAAK,IAAI;MACxB,IAAIA,KAAK,CAAC3C,OAAO,IAAI2C,KAAK,CAACC,KAAK,KAAK5C,OAAO,EAAE;QAC5C,IAAIF,cAAc,EAAE6C,KAAK,CAAC7C,cAAc,CAAC,CAAC;QAC1C,IAAIW,IAAI,CAACa,MAAM,GAAG,CAAC,EAAEa,OAAO,CAACnB,OAAO,IAAIA,OAAO,GAAG,CAAC,CAAC;MACtD;IACF,CAAC,CAAC,CAAC;;IAGH,MAAM6B,UAAU,GAAGF,KAAK,IAAI;MAC1B,IAAI7C,cAAc,EAAE6C,KAAK,CAAC7C,cAAc,CAAC,CAAC;MAC1C,IAAIgD,KAAK,GAAG,CAAC;MACb,IAAI,CAACH,KAAK,EAAEA,KAAK,GAAGI,MAAM,CAACJ,KAAK;MAChC,IAAIA,KAAK,CAACK,UAAU,EAAEF,KAAK,GAAGH,KAAK,CAACK,UAAU,GAAG,GAAG,CAAC,KAAK,IAAIL,KAAK,CAACM,MAAM,EAAEH,KAAK,GAAG,CAACH,KAAK,CAACM,MAAM,GAAG,CAAC;MACrG,IAAIxC,IAAI,CAACa,MAAM,GAAG,CAAC,EAAEa,OAAO,CAACnB,OAAO,IAAII,IAAI,CAAC8B,GAAG,CAAClC,OAAO,GAAG8B,KAAK,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;;IAGH,MAAMK,SAAS,GAAGR,KAAK,IAAIjC,SAAS,GAAGiC,KAAK;IAE5CS,QAAQ,CAACC,gBAAgB,CAAC,aAAa,EAAEF,SAAS,EAAE;MAClDG,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAIvD,MAAM,EAAEqD,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAER,UAAU,CAAC;IAC1D,IAAI7C,OAAO,KAAKW,SAAS,EAAEyC,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEX,QAAQ,CAAC;IACzE,OAAO,MAAM;MACX;MACAvC,SAAS,CAAC;QACRW,MAAM,EAAEF;MACV,CAAC,CAAC;MACF,IAAIZ,OAAO,KAAKW,SAAS,EAAEyC,QAAQ,CAACG,mBAAmB,CAAC,SAAS,EAAEb,QAAQ,CAAC;MAC5E,IAAI3C,MAAM,EAAEqD,QAAQ,CAACG,mBAAmB,CAAC,OAAO,EAAEV,UAAU,CAAC;MAC7DO,QAAQ,CAACG,mBAAmB,CAAC,aAAa,EAAEJ,SAAS,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAAC7C,EAAE,EAAED,GAAG,EAAEF,SAAS,EAAEL,cAAc,EAAEC,MAAM,EAAEC,OAAO,CAAC,CAAC;EACzD,OAAO,IAAI;AACb;AAEA,SAASL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}