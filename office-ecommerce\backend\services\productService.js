const logger = require('../utils/logger');
const Product = require('../models/Product');

/**
 * Product Service
 * Handles all product-related business logic
 */
class ProductService {

  constructor() {
    this.productModel = new Product();
  }

  /**
   * Get products with pagination and filters
   * @param {Object} filters - Filter parameters
   * @returns {Object} Paginated products result
   */
  async getProducts(filters) {
    try {
      const {
        page = 1,
        limit = 12,
        category,
        search,
        minPrice,
        maxPrice,
        inStock = false,
        featured = false,
        sortBy = 'name',
        sortOrder = 'asc'
      } = filters;

      // Build filters for model
      const modelFilters = {
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        sortBy,
        sortOrder
      };

      if (category) modelFilters.category = category;
      if (search) modelFilters.search = search;
      if (minPrice) modelFilters.minPrice = parseFloat(minPrice);
      if (maxPrice) modelFilters.maxPrice = parseFloat(maxPrice);
      if (inStock) modelFilters.inStock = true;
      if (featured) modelFilters.featured = true;

      const result = await this.productModel.getProductsWithPagination(modelFilters);

      return {
        success: true,
        data: {
          products: result.products,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(result.total / parseInt(limit)),
            totalItems: result.total,
            itemsPerPage: parseInt(limit)
          }
        }
      };

    } catch (error) {
      logger.error('ProductService.getProducts error:', error);
      throw error;
    }
  }

  /**
   * Get a single product by ID
   * @param {string} productId - Product ID
   * @returns {Object} Product result
   */
  async getProductById(productId) {
    try {
      // Validate UUID format
      if (!this.isValidUUID(productId)) {
        return {
          success: false,
          error: 'Invalid product ID format',
          code: 'INVALID_PRODUCT_ID'
        };
      }

      const product = await this.productModel.getProductById(productId);

      if (!product) {
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }

      return {
        success: true,
        data: product
      };

    } catch (error) {
      logger.error('ProductService.getProductById error:', error);
      throw error;
    }
  }

  /**
   * Create a new product
   * @param {Object} productData - Product information
   * @param {Object} user - User creating the product
   * @returns {Object} Created product result
   */
  async createProduct(productData, user) {
    try {
      // Validate product data
      this.validateProductData(productData);

      // Prepare product data
      const preparedData = {
        ...productData,
        CreatedBy: user.userId,
        CreatedAt: new Date().toISOString(),
        UpdatedAt: new Date().toISOString()
      };

      const result = await this.productModel.createProduct(preparedData);

      logger.info(`Product created: ${result.ProductID} by user ${user.userId}`);

      return {
        success: true,
        data: result
      };

    } catch (error) {
      logger.error('ProductService.createProduct error:', error);
      
      // Handle duplicate product code error
      if (error.message.includes('duplicate') || error.message.includes('unique')) {
        return {
          success: false,
          error: 'Product code already exists',
          code: 'DUPLICATE_PRODUCT_CODE'
        };
      }

      throw error;
    }
  }

  /**
   * Update an existing product
   * @param {string} productId - Product ID
   * @param {Object} updateData - Updated product information
   * @param {Object} user - User updating the product
   * @returns {Object} Updated product result
   */
  async updateProduct(productId, updateData, user) {
    try {
      // Validate UUID format
      if (!this.isValidUUID(productId)) {
        return {
          success: false,
          error: 'Invalid product ID format',
          code: 'INVALID_PRODUCT_ID'
        };
      }

      // Validate update data
      this.validateProductUpdateData(updateData);

      // Prepare update data
      const preparedData = {
        ...updateData,
        UpdatedBy: user.userId,
        UpdatedAt: new Date().toISOString()
      };

      const result = await this.productModel.updateProduct(productId, preparedData);

      if (!result) {
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }

      logger.info(`Product updated: ${productId} by user ${user.userId}`);

      return {
        success: true,
        data: result
      };

    } catch (error) {
      logger.error('ProductService.updateProduct error:', error);
      throw error;
    }
  }

  /**
   * Delete a product
   * @param {string} productId - Product ID
   * @param {Object} user - User deleting the product
   * @returns {Object} Deletion result
   */
  async deleteProduct(productId, user) {
    try {
      // Validate UUID format
      if (!this.isValidUUID(productId)) {
        return {
          success: false,
          error: 'Invalid product ID format',
          code: 'INVALID_PRODUCT_ID'
        };
      }

      // Check if product has active orders
      const hasActiveOrders = await this.productModel.hasActiveOrders(productId);

      if (hasActiveOrders) {
        return {
          success: false,
          error: 'Cannot delete product with active orders',
          code: 'PRODUCT_HAS_ACTIVE_ORDERS'
        };
      }

      const result = await this.productModel.deleteProduct(productId);

      if (!result) {
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }

      logger.info(`Product deleted: ${productId} by user ${user.userId}`);

      return {
        success: true,
        message: 'Product deleted successfully'
      };

    } catch (error) {
      logger.error('ProductService.deleteProduct error:', error);
      throw error;
    }
  }

  /**
   * Get product categories
   * @returns {Object} Categories result
   */
  async getProductCategories() {
    try {
      const categories = await this.productModel.getProductCategories();

      return {
        success: true,
        data: categories
      };

    } catch (error) {
      logger.error('ProductService.getProductCategories error:', error);
      throw error;
    }
  }

  /**
   * Get featured products
   * @param {number} limit - Number of products to return
   * @returns {Object} Featured products result
   */
  async getFeaturedProducts(limit = 8) {
    try {
      const products = await this.productModel.getFeaturedProducts(parseInt(limit));

      return {
        success: true,
        data: products
      };

    } catch (error) {
      logger.error('ProductService.getFeaturedProducts error:', error);
      throw error;
    }
  }

  /**
   * Search products
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Object} Search results
   */
  async searchProducts(query, filters = {}) {
    try {
      const {
        limit = 20,
        category,
        minPrice,
        maxPrice
      } = filters;

      const searchFilters = {
        query,
        limit: parseInt(limit)
      };

      if (category) searchFilters.category = category;
      if (minPrice) searchFilters.minPrice = parseFloat(minPrice);
      if (maxPrice) searchFilters.maxPrice = parseFloat(maxPrice);

      const results = await this.productModel.searchProducts(searchFilters);

      return {
        success: true,
        data: {
          products: results,
          count: results.length,
          query: query
        }
      };

    } catch (error) {
      logger.error('ProductService.searchProducts error:', error);
      throw error;
    }
  }

  /**
   * Validate product data
   * @private
   */
  validateProductData(productData) {
    const required = ['ProductName', 'ProductCode', 'Category', 'BasePrice'];
    
    for (const field of required) {
      if (!productData[field]) {
        throw new Error(`${field} is required`);
      }
    }

    if (productData.BasePrice <= 0) {
      throw new Error('Base price must be greater than 0');
    }

    if (productData.ProductCode && productData.ProductCode.length < 3) {
      throw new Error('Product code must be at least 3 characters');
    }
  }

  /**
   * Validate product update data
   * @private
   */
  validateProductUpdateData(updateData) {
    if (updateData.BasePrice !== undefined && updateData.BasePrice <= 0) {
      throw new Error('Base price must be greater than 0');
    }

    if (updateData.ProductCode && updateData.ProductCode.length < 3) {
      throw new Error('Product code must be at least 3 characters');
    }
  }

  /**
   * Validate UUID format
   * @private
   */
  isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
}

module.exports = ProductService;
