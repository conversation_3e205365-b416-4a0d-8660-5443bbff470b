-- Office Furniture E-commerce Inventory Management Database Schema
-- MSSQL Server Database

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'OfficeEcommerce')
BEGIN
    CREATE DATABASE OfficeEcommerce;
END
GO

USE OfficeEcommerce;
GO

-- Enable snapshot isolation for better concurrency
ALTER DATABASE OfficeEcommerce SET ALLOW_SNAPSHOT_ISOLATION ON;
ALTER DATABASE OfficeEcommerce SET READ_COMMITTED_SNAPSHOT ON;
GO

-- =============================================
-- USERS AND AUTHENTICATION TABLES
-- =============================================

-- Users table for authentication and authorization
CREATE TABLE Users (
    UserID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Email NVARCHAR(255) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Role NVARCHAR(50) NOT NULL DEFAULT 'Customer', -- Admin, Employee, Customer
    IsActive BIT DEFAULT 1,
    EmailVerified BIT DEFAULT 0,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    LastLoginAt DATETIME2 NULL
);

-- User sessions for JWT token management
CREATE TABLE UserSessions (
    SessionID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserID UNIQUEIDENTIFIER NOT NULL,
    RefreshToken NVARCHAR(500) NOT NULL,
    ExpiresAt DATETIME2 NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    IsRevoked BIT DEFAULT 0,
    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE CASCADE
);

-- =============================================
-- SUPPLIER MANAGEMENT TABLES
-- =============================================

-- Suppliers table for raw materials and parts
CREATE TABLE Suppliers (
    SupplierID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    SupplierCode NVARCHAR(50) UNIQUE NOT NULL,
    CompanyName NVARCHAR(255) NOT NULL,
    ContactPerson NVARCHAR(100),
    Email NVARCHAR(255),
    Phone NVARCHAR(50),
    Address NVARCHAR(500),
    City NVARCHAR(100),
    State NVARCHAR(100),
    Country NVARCHAR(100),
    PostalCode NVARCHAR(20),
    PaymentTerms NVARCHAR(100),
    LeadTimeDays INT DEFAULT 7,
    IsActive BIT DEFAULT 1,
    Rating DECIMAL(3,2) DEFAULT 0.00,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE()
);

-- =============================================
-- PRODUCT CATALOG TABLES
-- =============================================

-- Product categories
CREATE TABLE Categories (
    CategoryID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CategoryName NVARCHAR(100) UNIQUE NOT NULL,
    Description NVARCHAR(500),
    ParentCategoryID UNIQUEIDENTIFIER NULL,
    IsActive BIT DEFAULT 1,
    SortOrder INT DEFAULT 0,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (ParentCategoryID) REFERENCES Categories(CategoryID)
);

-- Main products table
CREATE TABLE Products (
    ProductID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ProductCode NVARCHAR(50) UNIQUE NOT NULL,
    ProductName NVARCHAR(255) NOT NULL,
    CategoryID UNIQUEIDENTIFIER NOT NULL,
    Description NVARCHAR(MAX),
    BasePrice DECIMAL(10,2) NOT NULL,
    IsCustomizable BIT DEFAULT 0,
    IsActive BIT DEFAULT 1,
    Weight DECIMAL(8,2),
    Dimensions NVARCHAR(100), -- LxWxH format
    Material NVARCHAR(100),
    Color NVARCHAR(50),
    ImageURL NVARCHAR(500),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID)
);

-- Product variants for different configurations
CREATE TABLE ProductVariants (
    VariantID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ProductID UNIQUEIDENTIFIER NOT NULL,
    VariantCode NVARCHAR(50) UNIQUE NOT NULL,
    VariantName NVARCHAR(255) NOT NULL,
    SKU NVARCHAR(100) UNIQUE NOT NULL,
    Price DECIMAL(10,2) NOT NULL,
    Dimensions NVARCHAR(100),
    Material NVARCHAR(100),
    Color NVARCHAR(50),
    Finish NVARCHAR(50),
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID) ON DELETE CASCADE
);

-- =============================================
-- PARTS AND RAW MATERIALS TABLES
-- =============================================

-- Raw materials inventory
CREATE TABLE RawMaterials (
    MaterialID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    MaterialCode NVARCHAR(50) UNIQUE NOT NULL,
    MaterialName NVARCHAR(255) NOT NULL,
    MaterialType NVARCHAR(100) NOT NULL, -- Wood, Metal, Fabric, Hardware, etc.
    Description NVARCHAR(500),
    Unit NVARCHAR(50) NOT NULL, -- pieces, kg, meters, etc.
    UnitCost DECIMAL(10,4) NOT NULL,
    SupplierID UNIQUEIDENTIFIER,
    MinStockLevel INT DEFAULT 0,
    MaxStockLevel INT DEFAULT 1000,
    ReorderPoint INT DEFAULT 0,
    ReorderQuantity INT DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
);

-- Parts/Components that make up products
CREATE TABLE Parts (
    PartID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    PartCode NVARCHAR(50) UNIQUE NOT NULL,
    PartName NVARCHAR(255) NOT NULL,
    PartType NVARCHAR(100) NOT NULL, -- Leg, Top, Handle, Drawer, etc.
    Description NVARCHAR(500),
    UnitCost DECIMAL(10,2) NOT NULL,
    Weight DECIMAL(8,2),
    Dimensions NVARCHAR(100),
    Material NVARCHAR(100),
    SupplierID UNIQUEIDENTIFIER,
    MinStockLevel INT DEFAULT 0,
    MaxStockLevel INT DEFAULT 1000,
    ReorderPoint INT DEFAULT 0,
    ReorderQuantity INT DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
);

-- Bill of Materials - defines what parts/materials are needed for each product variant
CREATE TABLE BillOfMaterials (
    BOMID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    VariantID UNIQUEIDENTIFIER NOT NULL,
    PartID UNIQUEIDENTIFIER NULL,
    MaterialID UNIQUEIDENTIFIER NULL,
    Quantity DECIMAL(10,4) NOT NULL,
    Unit NVARCHAR(50) NOT NULL,
    Notes NVARCHAR(500),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (VariantID) REFERENCES ProductVariants(VariantID) ON DELETE CASCADE,
    FOREIGN KEY (PartID) REFERENCES Parts(PartID),
    FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID),
    CONSTRAINT CK_BOM_PartOrMaterial CHECK (
        (PartID IS NOT NULL AND MaterialID IS NULL) OR 
        (PartID IS NULL AND MaterialID IS NOT NULL)
    )
);

-- =============================================
-- INVENTORY TRACKING TABLES
-- =============================================

-- Product inventory levels
CREATE TABLE ProductInventory (
    InventoryID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    VariantID UNIQUEIDENTIFIER NOT NULL,
    QuantityOnHand INT NOT NULL DEFAULT 0,
    QuantityReserved INT NOT NULL DEFAULT 0,
    QuantityAvailable AS (QuantityOnHand - QuantityReserved) PERSISTED,
    MinStockLevel INT DEFAULT 0,
    MaxStockLevel INT DEFAULT 1000,
    ReorderPoint INT DEFAULT 0,
    ReorderQuantity INT DEFAULT 0,
    LastRestockedAt DATETIME2,
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (VariantID) REFERENCES ProductVariants(VariantID) ON DELETE CASCADE
);

-- Parts inventory levels
CREATE TABLE PartsInventory (
    InventoryID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    PartID UNIQUEIDENTIFIER NOT NULL,
    QuantityOnHand INT NOT NULL DEFAULT 0,
    QuantityReserved INT NOT NULL DEFAULT 0,
    QuantityAvailable AS (QuantityOnHand - QuantityReserved) PERSISTED,
    LastRestockedAt DATETIME2,
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (PartID) REFERENCES Parts(PartID) ON DELETE CASCADE
);

-- Raw materials inventory levels
CREATE TABLE MaterialsInventory (
    InventoryID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    MaterialID UNIQUEIDENTIFIER NOT NULL,
    QuantityOnHand DECIMAL(10,4) NOT NULL DEFAULT 0,
    QuantityReserved DECIMAL(10,4) NOT NULL DEFAULT 0,
    QuantityAvailable AS (QuantityOnHand - QuantityReserved) PERSISTED,
    LastRestockedAt DATETIME2,
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID) ON DELETE CASCADE
);

-- =============================================
-- ORDERS AND TRANSACTIONS TABLES
-- =============================================

-- Customer orders
CREATE TABLE Orders (
    OrderID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    OrderNumber NVARCHAR(50) UNIQUE NOT NULL,
    CustomerID UNIQUEIDENTIFIER,
    CustomerEmail NVARCHAR(255) NOT NULL,
    CustomerName NVARCHAR(255) NOT NULL,
    CustomerPhone NVARCHAR(50),
    ShippingAddress NVARCHAR(MAX) NOT NULL,
    BillingAddress NVARCHAR(MAX),
    OrderStatus NVARCHAR(50) NOT NULL DEFAULT 'Pending', -- Pending, Confirmed, Processing, Shipped, Delivered, Cancelled
    PaymentStatus NVARCHAR(50) NOT NULL DEFAULT 'Pending', -- Pending, Paid, Failed, Refunded
    PaymentMethod NVARCHAR(50),
    PaymentReference NVARCHAR(255),
    SubTotal DECIMAL(10,2) NOT NULL,
    TaxAmount DECIMAL(10,2) DEFAULT 0,
    ShippingAmount DECIMAL(10,2) DEFAULT 0,
    DiscountAmount DECIMAL(10,2) DEFAULT 0,
    TotalAmount DECIMAL(10,2) NOT NULL,
    Currency NVARCHAR(10) DEFAULT 'PHP',
    Notes NVARCHAR(MAX),
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (CustomerID) REFERENCES Users(UserID)
);

-- Order line items
CREATE TABLE OrderItems (
    OrderItemID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    OrderID UNIQUEIDENTIFIER NOT NULL,
    VariantID UNIQUEIDENTIFIER NOT NULL,
    Quantity INT NOT NULL,
    UnitPrice DECIMAL(10,2) NOT NULL,
    TotalPrice DECIMAL(10,2) NOT NULL,
    CustomConfiguration NVARCHAR(MAX), -- JSON for custom dimensions, colors, etc.
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (OrderID) REFERENCES Orders(OrderID) ON DELETE CASCADE,
    FOREIGN KEY (VariantID) REFERENCES ProductVariants(VariantID)
);

-- =============================================
-- INVENTORY TRANSACTIONS AND AUDIT TRAIL
-- =============================================

-- Inventory transactions for audit trail
CREATE TABLE InventoryTransactions (
    TransactionID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TransactionType NVARCHAR(50) NOT NULL, -- Sale, Purchase, Adjustment, Transfer, Return
    ReferenceType NVARCHAR(50) NOT NULL, -- Order, PurchaseOrder, Adjustment, etc.
    ReferenceID UNIQUEIDENTIFIER NOT NULL,
    VariantID UNIQUEIDENTIFIER NULL,
    PartID UNIQUEIDENTIFIER NULL,
    MaterialID UNIQUEIDENTIFIER NULL,
    QuantityChange DECIMAL(10,4) NOT NULL, -- Positive for increase, negative for decrease
    UnitCost DECIMAL(10,4),
    TotalCost DECIMAL(10,2),
    Notes NVARCHAR(500),
    CreatedBy UNIQUEIDENTIFIER,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (VariantID) REFERENCES ProductVariants(VariantID),
    FOREIGN KEY (PartID) REFERENCES Parts(PartID),
    FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    CONSTRAINT CK_IT_OneItemType CHECK (
        (VariantID IS NOT NULL AND PartID IS NULL AND MaterialID IS NULL) OR
        (VariantID IS NULL AND PartID IS NOT NULL AND MaterialID IS NULL) OR
        (VariantID IS NULL AND PartID IS NULL AND MaterialID IS NOT NULL)
    )
);

-- =============================================
-- PURCHASE ORDERS AND SUPPLIER MANAGEMENT
-- =============================================

-- Purchase orders to suppliers
CREATE TABLE PurchaseOrders (
    PurchaseOrderID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    PONumber NVARCHAR(50) UNIQUE NOT NULL,
    SupplierID UNIQUEIDENTIFIER NOT NULL,
    OrderStatus NVARCHAR(50) NOT NULL DEFAULT 'Draft', -- Draft, Sent, Confirmed, Received, Cancelled
    OrderDate DATETIME2 DEFAULT GETUTCDATE(),
    ExpectedDeliveryDate DATETIME2,
    ActualDeliveryDate DATETIME2,
    SubTotal DECIMAL(10,2) NOT NULL,
    TaxAmount DECIMAL(10,2) DEFAULT 0,
    ShippingAmount DECIMAL(10,2) DEFAULT 0,
    TotalAmount DECIMAL(10,2) NOT NULL,
    Currency NVARCHAR(10) DEFAULT 'PHP',
    Notes NVARCHAR(MAX),
    CreatedBy UNIQUEIDENTIFIER,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- Purchase order line items
CREATE TABLE PurchaseOrderItems (
    POItemID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    PurchaseOrderID UNIQUEIDENTIFIER NOT NULL,
    PartID UNIQUEIDENTIFIER NULL,
    MaterialID UNIQUEIDENTIFIER NULL,
    Quantity DECIMAL(10,4) NOT NULL,
    UnitCost DECIMAL(10,4) NOT NULL,
    TotalCost DECIMAL(10,2) NOT NULL,
    QuantityReceived DECIMAL(10,4) DEFAULT 0,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (PurchaseOrderID) REFERENCES PurchaseOrders(PurchaseOrderID) ON DELETE CASCADE,
    FOREIGN KEY (PartID) REFERENCES Parts(PartID),
    FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID),
    CONSTRAINT CK_POI_PartOrMaterial CHECK (
        (PartID IS NOT NULL AND MaterialID IS NULL) OR 
        (PartID IS NULL AND MaterialID IS NOT NULL)
    )
);

-- =============================================
-- ACTIVITY LOGS AND AUDIT TRAIL
-- =============================================

-- Activity logs for admin dashboard
CREATE TABLE ActivityLogs (
    LogID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserID UNIQUEIDENTIFIER NULL,
    UserEmail NVARCHAR(255) NULL,
    UserName NVARCHAR(255) NULL,
    UserRole NVARCHAR(50) NULL,
    Action NVARCHAR(100) NOT NULL, -- CREATE, UPDATE, DELETE, LOGIN, LOGOUT, VIEW, etc.
    EntityType NVARCHAR(50) NOT NULL, -- User, Product, Order, Inventory, etc.
    EntityID UNIQUEIDENTIFIER NULL,
    EntityName NVARCHAR(255) NULL,
    Description NVARCHAR(500) NOT NULL,
    IPAddress NVARCHAR(45) NULL,
    UserAgent NVARCHAR(500) NULL,
    RequestMethod NVARCHAR(10) NULL, -- GET, POST, PUT, DELETE
    RequestPath NVARCHAR(500) NULL,
    StatusCode INT NULL,
    Duration INT NULL, -- Request duration in milliseconds
    OldValues NVARCHAR(MAX) NULL, -- JSON string of old values for updates
    NewValues NVARCHAR(MAX) NULL, -- JSON string of new values for updates
    Metadata NVARCHAR(MAX) NULL, -- Additional JSON metadata
    Severity NVARCHAR(20) NOT NULL DEFAULT 'INFO', -- INFO, WARNING, ERROR, CRITICAL
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE SET NULL
);

-- Index for performance on common queries
CREATE INDEX IX_ActivityLogs_UserID ON ActivityLogs(UserID);
CREATE INDEX IX_ActivityLogs_CreatedAt ON ActivityLogs(CreatedAt DESC);
CREATE INDEX IX_ActivityLogs_Action ON ActivityLogs(Action);
CREATE INDEX IX_ActivityLogs_EntityType ON ActivityLogs(EntityType);
CREATE INDEX IX_ActivityLogs_Severity ON ActivityLogs(Severity);

-- =============================================
-- ALERTS AND NOTIFICATIONS
-- =============================================

-- Low stock alerts and notifications
CREATE TABLE StockAlerts (
    AlertID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    AlertType NVARCHAR(50) NOT NULL, -- LowStock, OutOfStock, ReorderPoint
    ItemType NVARCHAR(50) NOT NULL, -- Product, Part, Material
    ItemID UNIQUEIDENTIFIER NOT NULL,
    CurrentStock DECIMAL(10,4) NOT NULL,
    ThresholdLevel DECIMAL(10,4) NOT NULL,
    AlertStatus NVARCHAR(50) DEFAULT 'Active', -- Active, Acknowledged, Resolved
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    AcknowledgedAt DATETIME2,
    AcknowledgedBy UNIQUEIDENTIFIER,
    ResolvedAt DATETIME2,
    FOREIGN KEY (AcknowledgedBy) REFERENCES Users(UserID)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users indexes
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_Role ON Users(Role);
CREATE INDEX IX_UserSessions_UserID ON UserSessions(UserID);

-- Products indexes
CREATE INDEX IX_Products_CategoryID ON Products(CategoryID);
CREATE INDEX IX_Products_ProductCode ON Products(ProductCode);
CREATE INDEX IX_ProductVariants_ProductID ON ProductVariants(ProductID);
CREATE INDEX IX_ProductVariants_SKU ON ProductVariants(SKU);

-- Inventory indexes
CREATE INDEX IX_ProductInventory_VariantID ON ProductInventory(VariantID);
CREATE INDEX IX_PartsInventory_PartID ON PartsInventory(PartID);
CREATE INDEX IX_MaterialsInventory_MaterialID ON MaterialsInventory(MaterialID);

-- Orders indexes
CREATE INDEX IX_Orders_CustomerID ON Orders(CustomerID);
CREATE INDEX IX_Orders_OrderStatus ON Orders(OrderStatus);
CREATE INDEX IX_Orders_CreatedAt ON Orders(CreatedAt);
CREATE INDEX IX_OrderItems_OrderID ON OrderItems(OrderID);
CREATE INDEX IX_OrderItems_VariantID ON OrderItems(VariantID);

-- Transactions indexes
CREATE INDEX IX_InventoryTransactions_VariantID ON InventoryTransactions(VariantID);
CREATE INDEX IX_InventoryTransactions_PartID ON InventoryTransactions(PartID);
CREATE INDEX IX_InventoryTransactions_MaterialID ON InventoryTransactions(MaterialID);
CREATE INDEX IX_InventoryTransactions_CreatedAt ON InventoryTransactions(CreatedAt);

-- Suppliers and Purchase Orders indexes
CREATE INDEX IX_Suppliers_SupplierCode ON Suppliers(SupplierCode);
CREATE INDEX IX_PurchaseOrders_SupplierID ON PurchaseOrders(SupplierID);
CREATE INDEX IX_PurchaseOrders_OrderStatus ON PurchaseOrders(OrderStatus);

-- Stock alerts indexes
CREATE INDEX IX_StockAlerts_AlertType ON StockAlerts(AlertType);
CREATE INDEX IX_StockAlerts_AlertStatus ON StockAlerts(AlertStatus);
CREATE INDEX IX_StockAlerts_CreatedAt ON StockAlerts(CreatedAt);

GO

-- =============================================
-- STORED PROCEDURES FOR INVENTORY MANAGEMENT
-- =============================================

-- Procedure to check and update stock levels
CREATE PROCEDURE sp_UpdateInventoryLevels
    @VariantID UNIQUEIDENTIFIER = NULL,
    @PartID UNIQUEIDENTIFIER = NULL,
    @MaterialID UNIQUEIDENTIFIER = NULL,
    @QuantityChange DECIMAL(10,4),
    @TransactionType NVARCHAR(50),
    @ReferenceType NVARCHAR(50),
    @ReferenceID UNIQUEIDENTIFIER,
    @CreatedBy UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;

        -- Update product inventory
        IF @VariantID IS NOT NULL
        BEGIN
            UPDATE ProductInventory
            SET QuantityOnHand = QuantityOnHand + @QuantityChange,
                UpdatedAt = GETUTCDATE()
            WHERE VariantID = @VariantID;

            -- Check for low stock alerts
            INSERT INTO StockAlerts (AlertType, ItemType, ItemID, CurrentStock, ThresholdLevel)
            SELECT 'LowStock', 'Product', @VariantID, pi.QuantityAvailable, pi.ReorderPoint
            FROM ProductInventory pi
            WHERE pi.VariantID = @VariantID
            AND pi.QuantityAvailable <= pi.ReorderPoint
            AND NOT EXISTS (
                SELECT 1 FROM StockAlerts sa
                WHERE sa.ItemID = @VariantID AND sa.AlertStatus = 'Active'
            );
        END

        -- Update parts inventory
        IF @PartID IS NOT NULL
        BEGIN
            UPDATE PartsInventory
            SET QuantityOnHand = QuantityOnHand + @QuantityChange,
                UpdatedAt = GETUTCDATE()
            WHERE PartID = @PartID;

            -- Check for low stock alerts
            INSERT INTO StockAlerts (AlertType, ItemType, ItemID, CurrentStock, ThresholdLevel)
            SELECT 'LowStock', 'Part', @PartID, pi.QuantityAvailable, p.ReorderPoint
            FROM PartsInventory pi
            INNER JOIN Parts p ON pi.PartID = p.PartID
            WHERE pi.PartID = @PartID
            AND pi.QuantityAvailable <= p.ReorderPoint
            AND NOT EXISTS (
                SELECT 1 FROM StockAlerts sa
                WHERE sa.ItemID = @PartID AND sa.AlertStatus = 'Active'
            );
        END

        -- Update materials inventory
        IF @MaterialID IS NOT NULL
        BEGIN
            UPDATE MaterialsInventory
            SET QuantityOnHand = QuantityOnHand + @QuantityChange,
                UpdatedAt = GETUTCDATE()
            WHERE MaterialID = @MaterialID;

            -- Check for low stock alerts
            INSERT INTO StockAlerts (AlertType, ItemType, ItemID, CurrentStock, ThresholdLevel)
            SELECT 'LowStock', 'Material', @MaterialID, mi.QuantityAvailable, rm.ReorderPoint
            FROM MaterialsInventory mi
            INNER JOIN RawMaterials rm ON mi.MaterialID = rm.MaterialID
            WHERE mi.MaterialID = @MaterialID
            AND mi.QuantityAvailable <= rm.ReorderPoint
            AND NOT EXISTS (
                SELECT 1 FROM StockAlerts sa
                WHERE sa.ItemID = @MaterialID AND sa.AlertStatus = 'Active'
            );
        END

        -- Record transaction
        INSERT INTO InventoryTransactions (
            TransactionType, ReferenceType, ReferenceID, VariantID, PartID, MaterialID,
            QuantityChange, CreatedBy
        )
        VALUES (
            @TransactionType, @ReferenceType, @ReferenceID, @VariantID, @PartID, @MaterialID,
            @QuantityChange, @CreatedBy
        );

        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;
GO

-- Procedure to reserve inventory for orders
CREATE PROCEDURE sp_ReserveInventory
    @OrderID UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;

        -- Reserve product inventory
        UPDATE pi
        SET QuantityReserved = pi.QuantityReserved + oi.Quantity,
            UpdatedAt = GETUTCDATE()
        FROM ProductInventory pi
        INNER JOIN OrderItems oi ON pi.VariantID = oi.VariantID
        WHERE oi.OrderID = @OrderID;

        -- Check if we have enough stock
        IF EXISTS (
            SELECT 1
            FROM ProductInventory pi
            INNER JOIN OrderItems oi ON pi.VariantID = oi.VariantID
            WHERE oi.OrderID = @OrderID
            AND pi.QuantityAvailable < 0
        )
        BEGIN
            ROLLBACK TRANSACTION;
            THROW 50001, 'Insufficient inventory for order', 1;
        END

        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;
GO

-- Procedure to release reserved inventory
CREATE PROCEDURE sp_ReleaseReservedInventory
    @OrderID UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;

        UPDATE pi
        SET QuantityReserved = pi.QuantityReserved - oi.Quantity,
            UpdatedAt = GETUTCDATE()
        FROM ProductInventory pi
        INNER JOIN OrderItems oi ON pi.VariantID = oi.VariantID
        WHERE oi.OrderID = @OrderID;

        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;
GO

-- =============================================
-- VIEWS FOR REPORTING AND ANALYTICS
-- =============================================

-- View for low stock items
CREATE VIEW vw_LowStockItems AS
SELECT
    'Product' as ItemType,
    pv.VariantID as ItemID,
    pv.VariantCode as ItemCode,
    pv.VariantName as ItemName,
    pi.QuantityAvailable as CurrentStock,
    pi.ReorderPoint as ThresholdLevel,
    p.ProductName,
    c.CategoryName
FROM ProductInventory pi
INNER JOIN ProductVariants pv ON pi.VariantID = pv.VariantID
INNER JOIN Products p ON pv.ProductID = p.ProductID
INNER JOIN Categories c ON p.CategoryID = c.CategoryID
WHERE pi.QuantityAvailable <= pi.ReorderPoint

UNION ALL

SELECT
    'Part' as ItemType,
    pt.PartID as ItemID,
    pt.PartCode as ItemCode,
    pt.PartName as ItemName,
    pi.QuantityAvailable as CurrentStock,
    pt.ReorderPoint as ThresholdLevel,
    pt.PartType as ProductName,
    'Parts' as CategoryName
FROM PartsInventory pi
INNER JOIN Parts pt ON pi.PartID = pt.PartID
WHERE pi.QuantityAvailable <= pt.ReorderPoint

UNION ALL

SELECT
    'Material' as ItemType,
    rm.MaterialID as ItemID,
    rm.MaterialCode as ItemCode,
    rm.MaterialName as ItemName,
    mi.QuantityAvailable as CurrentStock,
    rm.ReorderPoint as ThresholdLevel,
    rm.MaterialType as ProductName,
    'Raw Materials' as CategoryName
FROM MaterialsInventory mi
INNER JOIN RawMaterials rm ON mi.MaterialID = rm.MaterialID
WHERE mi.QuantityAvailable <= rm.ReorderPoint;
GO

-- View for inventory valuation
CREATE VIEW vw_InventoryValuation AS
SELECT
    'Product' as ItemType,
    pv.VariantID as ItemID,
    pv.VariantCode as ItemCode,
    pv.VariantName as ItemName,
    pi.QuantityOnHand,
    pi.QuantityReserved,
    pi.QuantityAvailable,
    pv.Price as UnitCost,
    (pi.QuantityOnHand * pv.Price) as TotalValue
FROM ProductInventory pi
INNER JOIN ProductVariants pv ON pi.VariantID = pv.VariantID

UNION ALL

SELECT
    'Part' as ItemType,
    pt.PartID as ItemID,
    pt.PartCode as ItemCode,
    pt.PartName as ItemName,
    pi.QuantityOnHand,
    pi.QuantityReserved,
    pi.QuantityAvailable,
    pt.UnitCost,
    (pi.QuantityOnHand * pt.UnitCost) as TotalValue
FROM PartsInventory pi
INNER JOIN Parts pt ON pi.PartID = pt.PartID

UNION ALL

SELECT
    'Material' as ItemType,
    rm.MaterialID as ItemID,
    rm.MaterialCode as ItemCode,
    rm.MaterialName as ItemName,
    mi.QuantityOnHand,
    mi.QuantityReserved,
    mi.QuantityAvailable,
    rm.UnitCost,
    (mi.QuantityOnHand * rm.UnitCost) as TotalValue
FROM MaterialsInventory mi
INNER JOIN RawMaterials rm ON mi.MaterialID = rm.MaterialID;
GO
