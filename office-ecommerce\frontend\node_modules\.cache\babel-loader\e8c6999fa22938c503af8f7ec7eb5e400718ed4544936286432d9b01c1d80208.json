{"ast": null, "code": "class FlakesTexture {\n  constructor(width = 512, height = 512) {\n    const canvas = document.createElement(\"canvas\");\n    canvas.width = width;\n    canvas.height = height;\n    const context = canvas.getContext(\"2d\");\n    context.fillStyle = \"rgb(127,127,255)\";\n    context.fillRect(0, 0, width, height);\n    for (let i = 0; i < 4e3; i++) {\n      const x = Math.random() * width;\n      const y = Math.random() * height;\n      const r = Math.random() * 3 + 3;\n      let nx = Math.random() * 2 - 1;\n      let ny = Math.random() * 2 - 1;\n      let nz = 1.5;\n      const l = Math.sqrt(nx * nx + ny * ny + nz * nz);\n      nx /= l;\n      ny /= l;\n      nz /= l;\n      context.fillStyle = \"rgb(\" + (nx * 127 + 127) + \",\" + (ny * 127 + 127) + \",\" + nz * 255 + \")\";\n      context.beginPath();\n      context.arc(x, y, r, 0, Math.PI * 2);\n      context.fill();\n    }\n    return canvas;\n  }\n}\nexport { FlakesTexture };", "map": {"version": 3, "names": ["FlakesTexture", "constructor", "width", "height", "canvas", "document", "createElement", "context", "getContext", "fillStyle", "fillRect", "i", "x", "Math", "random", "y", "r", "nx", "ny", "nz", "l", "sqrt", "beginPath", "arc", "PI", "fill"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\textures\\FlakesTexture.ts"], "sourcesContent": ["class FlakesTexture {\n  constructor(width = 512, height = 512) {\n    const canvas = document.createElement('canvas')\n    canvas.width = width\n    canvas.height = height\n\n    const context = canvas.getContext('2d') as CanvasRenderingContext2D\n    context.fillStyle = 'rgb(127,127,255)'\n    context.fillRect(0, 0, width, height)\n\n    for (let i = 0; i < 4000; i++) {\n      const x = Math.random() * width\n      const y = Math.random() * height\n      const r = Math.random() * 3 + 3\n\n      let nx = Math.random() * 2 - 1\n      let ny = Math.random() * 2 - 1\n      let nz = 1.5\n\n      const l = Math.sqrt(nx * nx + ny * ny + nz * nz)\n\n      nx /= l\n      ny /= l\n      nz /= l\n\n      context.fillStyle = 'rgb(' + (nx * 127 + 127) + ',' + (ny * 127 + 127) + ',' + nz * 255 + ')'\n      context.beginPath()\n      context.arc(x, y, r, 0, Math.PI * 2)\n      context.fill()\n    }\n\n    return canvas\n  }\n}\n\nexport { FlakesTexture }\n"], "mappings": "AAAA,MAAMA,aAAA,CAAc;EAClBC,YAAYC,KAAA,GAAQ,KAAKC,MAAA,GAAS,KAAK;IAC/B,MAAAC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAC9CF,MAAA,CAAOF,KAAA,GAAQA,KAAA;IACfE,MAAA,CAAOD,MAAA,GAASA,MAAA;IAEV,MAAAI,OAAA,GAAUH,MAAA,CAAOI,UAAA,CAAW,IAAI;IACtCD,OAAA,CAAQE,SAAA,GAAY;IACpBF,OAAA,CAAQG,QAAA,CAAS,GAAG,GAAGR,KAAA,EAAOC,MAAM;IAEpC,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAMA,CAAA,IAAK;MACvB,MAAAC,CAAA,GAAIC,IAAA,CAAKC,MAAA,KAAWZ,KAAA;MACpB,MAAAa,CAAA,GAAIF,IAAA,CAAKC,MAAA,KAAWX,MAAA;MAC1B,MAAMa,CAAA,GAAIH,IAAA,CAAKC,MAAA,CAAO,IAAI,IAAI;MAE9B,IAAIG,EAAA,GAAKJ,IAAA,CAAKC,MAAA,CAAO,IAAI,IAAI;MAC7B,IAAII,EAAA,GAAKL,IAAA,CAAKC,MAAA,CAAO,IAAI,IAAI;MAC7B,IAAIK,EAAA,GAAK;MAEH,MAAAC,CAAA,GAAIP,IAAA,CAAKQ,IAAA,CAAKJ,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;MAEzCF,EAAA,IAAAG,CAAA;MACAF,EAAA,IAAAE,CAAA;MACAD,EAAA,IAAAC,CAAA;MAEEb,OAAA,CAAAE,SAAA,GAAY,UAAUQ,EAAA,GAAK,MAAM,OAAO,OAAOC,EAAA,GAAK,MAAM,OAAO,MAAMC,EAAA,GAAK,MAAM;MAC1FZ,OAAA,CAAQe,SAAA,CAAU;MAClBf,OAAA,CAAQgB,GAAA,CAAIX,CAAA,EAAGG,CAAA,EAAGC,CAAA,EAAG,GAAGH,IAAA,CAAKW,EAAA,GAAK,CAAC;MACnCjB,OAAA,CAAQkB,IAAA,CAAK;IACf;IAEO,OAAArB,MAAA;EACT;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}