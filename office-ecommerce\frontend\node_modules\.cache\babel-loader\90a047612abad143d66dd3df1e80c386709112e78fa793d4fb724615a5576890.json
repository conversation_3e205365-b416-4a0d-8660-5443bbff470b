{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nconst _inverseMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst _ray = /*@__PURE__*/new THREE.Ray();\nconst _sphere = /*@__PURE__*/new THREE.Sphere();\nconst _position = /*@__PURE__*/new THREE.Vector3();\nclass PositionPoint extends THREE.Group {\n  constructor() {\n    super();\n    this.size = 0;\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  } // This will allow the virtual instance have bounds\n\n  get geometry() {\n    var _this$instance$curren;\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  }\n  raycast(raycaster, intersects) {\n    var _raycaster$params$Poi, _raycaster$params$Poi2;\n    const parent = this.instance.current;\n    if (!parent || !parent.geometry) return;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey); // If the instance wasn't found or exceeds the parents draw range, bail out\n\n    if (instanceId === -1 || instanceId > parent.geometry.drawRange.count) return;\n    const threshold = (_raycaster$params$Poi = (_raycaster$params$Poi2 = raycaster.params.Points) == null ? void 0 : _raycaster$params$Poi2.threshold) !== null && _raycaster$params$Poi !== void 0 ? _raycaster$params$Poi : 1;\n    _sphere.set(this.getWorldPosition(_position), threshold);\n    if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n    _inverseMatrix.copy(parent.matrixWorld).invert();\n    _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix);\n    const localThreshold = threshold / ((this.scale.x + this.scale.y + this.scale.z) / 3);\n    const localThresholdSq = localThreshold * localThreshold;\n    const rayPointDistanceSq = _ray.distanceSqToPoint(this.position);\n    if (rayPointDistanceSq < localThresholdSq) {\n      const intersectPoint = new THREE.Vector3();\n      _ray.closestPointToPoint(this.position, intersectPoint);\n      intersectPoint.applyMatrix4(this.matrixWorld);\n      const distance = raycaster.ray.origin.distanceTo(intersectPoint);\n      if (distance < raycaster.near || distance > raycaster.far) return;\n      intersects.push({\n        distance: distance,\n        distanceToRay: Math.sqrt(rayPointDistanceSq),\n        point: intersectPoint,\n        index: instanceId,\n        face: null,\n        object: this\n      });\n    }\n  }\n}\nlet i, positionRef;\nconst context = /*@__PURE__*/React.createContext(null);\nconst parentMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst position = /*@__PURE__*/new THREE.Vector3();\n/**\n * Instance implementation, relies on react + context to update the attributes based on the children of this component\n */\n\nconst PointsInstances = /*#__PURE__*/React.forwardRef(({\n  children,\n  range,\n  limit = 1000,\n  ...props\n}, ref) => {\n  const parentRef = React.useRef(null);\n  const [refs, setRefs] = React.useState([]);\n  const [[positions, colors, sizes]] = React.useState(() => [new Float32Array(limit * 3), Float32Array.from({\n    length: limit * 3\n  }, () => 1), Float32Array.from({\n    length: limit\n  }, () => 1)]);\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.geometry.attributes.position.needsUpdate = true;\n  });\n  useFrame(() => {\n    parentRef.current.updateMatrix();\n    parentRef.current.updateMatrixWorld();\n    parentMatrix.copy(parentRef.current.matrixWorld).invert();\n    parentRef.current.geometry.drawRange.count = Math.min(limit, range !== undefined ? range : limit, refs.length);\n    for (i = 0; i < refs.length; i++) {\n      positionRef = refs[i].current;\n      positionRef.getWorldPosition(position).applyMatrix4(parentMatrix);\n      position.toArray(positions, i * 3);\n      parentRef.current.geometry.attributes.position.needsUpdate = true;\n      positionRef.matrixWorldNeedsUpdate = true;\n      positionRef.color.toArray(colors, i * 3);\n      parentRef.current.geometry.attributes.color.needsUpdate = true;\n      sizes.set([positionRef.size], i);\n      parentRef.current.geometry.attributes.size.needsUpdate = true;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setRefs(refs => [...refs, ref]);\n      return () => setRefs(refs => refs.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    userData: {\n      instances: refs\n    },\n    matrixAutoUpdate: false,\n    ref: mergeRefs([ref, parentRef]),\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    count: positions.length / 3,\n    array: positions,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    count: colors.length / 3,\n    array: colors,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    count: sizes.length,\n    array: sizes,\n    itemSize: 1,\n    usage: THREE.DynamicDrawUsage\n  })), /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n});\nconst Point = /*#__PURE__*/React.forwardRef(({\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionPoint\n  }), []);\n  const group = React.useRef();\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionPoint\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: mergeRefs([ref, group])\n  }, props), children);\n});\n/**\n * Buffer implementation, relies on complete buffers of the correct number, leaves it to the user to update them\n */\n\nconst PointsBuffer = /*#__PURE__*/React.forwardRef(({\n  children,\n  positions,\n  colors,\n  sizes,\n  stride = 3,\n  ...props\n}, forwardedRef) => {\n  const pointsRef = React.useRef(null);\n  useFrame(() => {\n    const attr = pointsRef.current.geometry.attributes;\n    attr.position.needsUpdate = true;\n    if (colors) attr.color.needsUpdate = true;\n    if (sizes) attr.size.needsUpdate = true;\n  });\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    ref: mergeRefs([forwardedRef, pointsRef])\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    count: positions.length / stride,\n    array: positions,\n    itemSize: stride,\n    usage: THREE.DynamicDrawUsage\n  }), colors && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    count: colors.length / stride,\n    array: colors,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), sizes && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    count: sizes.length / stride,\n    array: sizes,\n    itemSize: 1,\n    usage: THREE.DynamicDrawUsage\n  })), children);\n});\nconst Points = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  if (props.positions instanceof Float32Array) {\n    return /*#__PURE__*/React.createElement(PointsBuffer, _extends({}, props, {\n      ref: forwardedRef\n    }));\n  } else return /*#__PURE__*/React.createElement(PointsInstances, _extends({}, props, {\n    ref: forwardedRef\n  }));\n});\nexport { Point, Points, PointsBuffer, PositionPoint };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useFrame", "mergeRefs", "_inverseMatrix", "Matrix4", "_ray", "<PERSON>", "_sphere", "Sphere", "_position", "Vector3", "PositionPoint", "Group", "constructor", "size", "color", "Color", "instance", "current", "undefined", "<PERSON><PERSON><PERSON>", "geometry", "_this$instance$curren", "raycast", "raycaster", "intersects", "_raycaster$params$Poi", "_raycaster$params$Poi2", "parent", "instanceId", "userData", "instances", "indexOf", "drawRange", "count", "threshold", "params", "Points", "set", "getWorldPosition", "ray", "intersectsSphere", "copy", "matrixWorld", "invert", "applyMatrix4", "localThreshold", "scale", "x", "y", "z", "localThresholdSq", "rayPointDistanceSq", "distanceSqToPoint", "position", "intersectPoint", "closestPointToPoint", "distance", "origin", "distanceTo", "near", "far", "push", "distanceToRay", "Math", "sqrt", "point", "index", "face", "object", "i", "positionRef", "context", "createContext", "parentMatrix", "PointsInstances", "forwardRef", "children", "range", "limit", "props", "ref", "parentRef", "useRef", "refs", "setRefs", "useState", "positions", "colors", "sizes", "Float32Array", "from", "length", "useEffect", "attributes", "needsUpdate", "updateMatrix", "updateMatrixWorld", "min", "toArray", "matrixWorldNeedsUpdate", "api", "useMemo", "getParent", "subscribe", "filter", "item", "createElement", "matrixAutoUpdate", "attach", "array", "itemSize", "usage", "DynamicDrawUsage", "Provider", "value", "Point", "group", "useContext", "useLayoutEffect", "PointsBuffer", "stride", "forwardedRef", "pointsRef", "attr"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Points.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\n\nconst _inverseMatrix = /*@__PURE__*/new THREE.Matrix4();\n\nconst _ray = /*@__PURE__*/new THREE.Ray();\n\nconst _sphere = /*@__PURE__*/new THREE.Sphere();\n\nconst _position = /*@__PURE__*/new THREE.Vector3();\n\nclass PositionPoint extends THREE.Group {\n  constructor() {\n    super();\n    this.size = 0;\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  } // This will allow the virtual instance have bounds\n\n\n  get geometry() {\n    var _this$instance$curren;\n\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  }\n\n  raycast(raycaster, intersects) {\n    var _raycaster$params$Poi, _raycaster$params$Poi2;\n\n    const parent = this.instance.current;\n    if (!parent || !parent.geometry) return;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey); // If the instance wasn't found or exceeds the parents draw range, bail out\n\n    if (instanceId === -1 || instanceId > parent.geometry.drawRange.count) return;\n    const threshold = (_raycaster$params$Poi = (_raycaster$params$Poi2 = raycaster.params.Points) == null ? void 0 : _raycaster$params$Poi2.threshold) !== null && _raycaster$params$Poi !== void 0 ? _raycaster$params$Poi : 1;\n\n    _sphere.set(this.getWorldPosition(_position), threshold);\n\n    if (raycaster.ray.intersectsSphere(_sphere) === false) return;\n\n    _inverseMatrix.copy(parent.matrixWorld).invert();\n\n    _ray.copy(raycaster.ray).applyMatrix4(_inverseMatrix);\n\n    const localThreshold = threshold / ((this.scale.x + this.scale.y + this.scale.z) / 3);\n    const localThresholdSq = localThreshold * localThreshold;\n\n    const rayPointDistanceSq = _ray.distanceSqToPoint(this.position);\n\n    if (rayPointDistanceSq < localThresholdSq) {\n      const intersectPoint = new THREE.Vector3();\n\n      _ray.closestPointToPoint(this.position, intersectPoint);\n\n      intersectPoint.applyMatrix4(this.matrixWorld);\n      const distance = raycaster.ray.origin.distanceTo(intersectPoint);\n      if (distance < raycaster.near || distance > raycaster.far) return;\n      intersects.push({\n        distance: distance,\n        distanceToRay: Math.sqrt(rayPointDistanceSq),\n        point: intersectPoint,\n        index: instanceId,\n        face: null,\n        object: this\n      });\n    }\n  }\n\n}\nlet i, positionRef;\nconst context = /*@__PURE__*/React.createContext(null);\nconst parentMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst position = /*@__PURE__*/new THREE.Vector3();\n/**\n * Instance implementation, relies on react + context to update the attributes based on the children of this component\n */\n\nconst PointsInstances = /*#__PURE__*/React.forwardRef(({\n  children,\n  range,\n  limit = 1000,\n  ...props\n}, ref) => {\n  const parentRef = React.useRef(null);\n  const [refs, setRefs] = React.useState([]);\n  const [[positions, colors, sizes]] = React.useState(() => [new Float32Array(limit * 3), Float32Array.from({\n    length: limit * 3\n  }, () => 1), Float32Array.from({\n    length: limit\n  }, () => 1)]);\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.geometry.attributes.position.needsUpdate = true;\n  });\n  useFrame(() => {\n    parentRef.current.updateMatrix();\n    parentRef.current.updateMatrixWorld();\n    parentMatrix.copy(parentRef.current.matrixWorld).invert();\n    parentRef.current.geometry.drawRange.count = Math.min(limit, range !== undefined ? range : limit, refs.length);\n\n    for (i = 0; i < refs.length; i++) {\n      positionRef = refs[i].current;\n      positionRef.getWorldPosition(position).applyMatrix4(parentMatrix);\n      position.toArray(positions, i * 3);\n      parentRef.current.geometry.attributes.position.needsUpdate = true;\n      positionRef.matrixWorldNeedsUpdate = true;\n      positionRef.color.toArray(colors, i * 3);\n      parentRef.current.geometry.attributes.color.needsUpdate = true;\n      sizes.set([positionRef.size], i);\n      parentRef.current.geometry.attributes.size.needsUpdate = true;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setRefs(refs => [...refs, ref]);\n      return () => setRefs(refs => refs.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    userData: {\n      instances: refs\n    },\n    matrixAutoUpdate: false,\n    ref: mergeRefs([ref, parentRef]),\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    count: positions.length / 3,\n    array: positions,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    count: colors.length / 3,\n    array: colors,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    count: sizes.length,\n    array: sizes,\n    itemSize: 1,\n    usage: THREE.DynamicDrawUsage\n  })), /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n});\nconst Point = /*#__PURE__*/React.forwardRef(({\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionPoint\n  }), []);\n  const group = React.useRef();\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionPoint\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: mergeRefs([ref, group])\n  }, props), children);\n});\n/**\n * Buffer implementation, relies on complete buffers of the correct number, leaves it to the user to update them\n */\n\nconst PointsBuffer = /*#__PURE__*/React.forwardRef(({\n  children,\n  positions,\n  colors,\n  sizes,\n  stride = 3,\n  ...props\n}, forwardedRef) => {\n  const pointsRef = React.useRef(null);\n  useFrame(() => {\n    const attr = pointsRef.current.geometry.attributes;\n    attr.position.needsUpdate = true;\n    if (colors) attr.color.needsUpdate = true;\n    if (sizes) attr.size.needsUpdate = true;\n  });\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    ref: mergeRefs([forwardedRef, pointsRef])\n  }, props), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    count: positions.length / stride,\n    array: positions,\n    itemSize: stride,\n    usage: THREE.DynamicDrawUsage\n  }), colors && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    count: colors.length / stride,\n    array: colors,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), sizes && /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    count: sizes.length / stride,\n    array: sizes,\n    itemSize: 1,\n    usage: THREE.DynamicDrawUsage\n  })), children);\n});\nconst Points = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  if (props.positions instanceof Float32Array) {\n    return /*#__PURE__*/React.createElement(PointsBuffer, _extends({}, props, {\n      ref: forwardedRef\n    }));\n  } else return /*#__PURE__*/React.createElement(PointsInstances, _extends({}, props, {\n    ref: forwardedRef\n  }));\n});\n\nexport { Point, Points, PointsBuffer, PositionPoint };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,OAAOC,SAAS,MAAM,kBAAkB;AAExC,MAAMC,cAAc,GAAG,aAAa,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC;AAEvD,MAAMC,IAAI,GAAG,aAAa,IAAIP,KAAK,CAACQ,GAAG,CAAC,CAAC;AAEzC,MAAMC,OAAO,GAAG,aAAa,IAAIT,KAAK,CAACU,MAAM,CAAC,CAAC;AAE/C,MAAMC,SAAS,GAAG,aAAa,IAAIX,KAAK,CAACY,OAAO,CAAC,CAAC;AAElD,MAAMC,aAAa,SAASb,KAAK,CAACc,KAAK,CAAC;EACtCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,KAAK,GAAG,IAAIjB,KAAK,CAACkB,KAAK,CAAC,OAAO,CAAC;IACrC,IAAI,CAACC,QAAQ,GAAG;MACdC,OAAO,EAAEC;IACX,CAAC;IACD,IAAI,CAACC,WAAW,GAAG;MACjBF,OAAO,EAAEC;IACX,CAAC;EACH,CAAC,CAAC;;EAGF,IAAIE,QAAQA,CAAA,EAAG;IACb,IAAIC,qBAAqB;IAEzB,OAAO,CAACA,qBAAqB,GAAG,IAAI,CAACL,QAAQ,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,qBAAqB,CAACD,QAAQ;EAC1G;EAEAE,OAAOA,CAACC,SAAS,EAAEC,UAAU,EAAE;IAC7B,IAAIC,qBAAqB,EAAEC,sBAAsB;IAEjD,MAAMC,MAAM,GAAG,IAAI,CAACX,QAAQ,CAACC,OAAO;IACpC,IAAI,CAACU,MAAM,IAAI,CAACA,MAAM,CAACP,QAAQ,EAAE;IACjC,MAAMQ,UAAU,GAAGD,MAAM,CAACE,QAAQ,CAACC,SAAS,CAACC,OAAO,CAAC,IAAI,CAACZ,WAAW,CAAC,CAAC,CAAC;;IAExE,IAAIS,UAAU,KAAK,CAAC,CAAC,IAAIA,UAAU,GAAGD,MAAM,CAACP,QAAQ,CAACY,SAAS,CAACC,KAAK,EAAE;IACvE,MAAMC,SAAS,GAAG,CAACT,qBAAqB,GAAG,CAACC,sBAAsB,GAAGH,SAAS,CAACY,MAAM,CAACC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGV,sBAAsB,CAACQ,SAAS,MAAM,IAAI,IAAIT,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC;IAE3NnB,OAAO,CAAC+B,GAAG,CAAC,IAAI,CAACC,gBAAgB,CAAC9B,SAAS,CAAC,EAAE0B,SAAS,CAAC;IAExD,IAAIX,SAAS,CAACgB,GAAG,CAACC,gBAAgB,CAAClC,OAAO,CAAC,KAAK,KAAK,EAAE;IAEvDJ,cAAc,CAACuC,IAAI,CAACd,MAAM,CAACe,WAAW,CAAC,CAACC,MAAM,CAAC,CAAC;IAEhDvC,IAAI,CAACqC,IAAI,CAAClB,SAAS,CAACgB,GAAG,CAAC,CAACK,YAAY,CAAC1C,cAAc,CAAC;IAErD,MAAM2C,cAAc,GAAGX,SAAS,IAAI,CAAC,IAAI,CAACY,KAAK,CAACC,CAAC,GAAG,IAAI,CAACD,KAAK,CAACE,CAAC,GAAG,IAAI,CAACF,KAAK,CAACG,CAAC,IAAI,CAAC,CAAC;IACrF,MAAMC,gBAAgB,GAAGL,cAAc,GAAGA,cAAc;IAExD,MAAMM,kBAAkB,GAAG/C,IAAI,CAACgD,iBAAiB,CAAC,IAAI,CAACC,QAAQ,CAAC;IAEhE,IAAIF,kBAAkB,GAAGD,gBAAgB,EAAE;MACzC,MAAMI,cAAc,GAAG,IAAIzD,KAAK,CAACY,OAAO,CAAC,CAAC;MAE1CL,IAAI,CAACmD,mBAAmB,CAAC,IAAI,CAACF,QAAQ,EAAEC,cAAc,CAAC;MAEvDA,cAAc,CAACV,YAAY,CAAC,IAAI,CAACF,WAAW,CAAC;MAC7C,MAAMc,QAAQ,GAAGjC,SAAS,CAACgB,GAAG,CAACkB,MAAM,CAACC,UAAU,CAACJ,cAAc,CAAC;MAChE,IAAIE,QAAQ,GAAGjC,SAAS,CAACoC,IAAI,IAAIH,QAAQ,GAAGjC,SAAS,CAACqC,GAAG,EAAE;MAC3DpC,UAAU,CAACqC,IAAI,CAAC;QACdL,QAAQ,EAAEA,QAAQ;QAClBM,aAAa,EAAEC,IAAI,CAACC,IAAI,CAACb,kBAAkB,CAAC;QAC5Cc,KAAK,EAAEX,cAAc;QACrBY,KAAK,EAAEtC,UAAU;QACjBuC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF;AAEF;AACA,IAAIC,CAAC,EAAEC,WAAW;AAClB,MAAMC,OAAO,GAAG,aAAazE,KAAK,CAAC0E,aAAa,CAAC,IAAI,CAAC;AACtD,MAAMC,YAAY,GAAG,aAAa,IAAI5E,KAAK,CAACM,OAAO,CAAC,CAAC;AACrD,MAAMkD,QAAQ,GAAG,aAAa,IAAIxD,KAAK,CAACY,OAAO,CAAC,CAAC;AACjD;AACA;AACA;;AAEA,MAAMiE,eAAe,GAAG,aAAa5E,KAAK,CAAC6E,UAAU,CAAC,CAAC;EACrDC,QAAQ;EACRC,KAAK;EACLC,KAAK,GAAG,IAAI;EACZ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,SAAS,GAAGnF,KAAK,CAACoF,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtF,KAAK,CAACuF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC,CAACC,SAAS,EAAEC,MAAM,EAAEC,KAAK,CAAC,CAAC,GAAG1F,KAAK,CAACuF,QAAQ,CAAC,MAAM,CAAC,IAAII,YAAY,CAACX,KAAK,GAAG,CAAC,CAAC,EAAEW,YAAY,CAACC,IAAI,CAAC;IACxGC,MAAM,EAAEb,KAAK,GAAG;EAClB,CAAC,EAAE,MAAM,CAAC,CAAC,EAAEW,YAAY,CAACC,IAAI,CAAC;IAC7BC,MAAM,EAAEb;EACV,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EACbhF,KAAK,CAAC8F,SAAS,CAAC,MAAM;IACpB;IACAX,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAACyE,UAAU,CAACxC,QAAQ,CAACyC,WAAW,GAAG,IAAI;EACnE,CAAC,CAAC;EACF9F,QAAQ,CAAC,MAAM;IACbiF,SAAS,CAAChE,OAAO,CAAC8E,YAAY,CAAC,CAAC;IAChCd,SAAS,CAAChE,OAAO,CAAC+E,iBAAiB,CAAC,CAAC;IACrCvB,YAAY,CAAChC,IAAI,CAACwC,SAAS,CAAChE,OAAO,CAACyB,WAAW,CAAC,CAACC,MAAM,CAAC,CAAC;IACzDsC,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAACY,SAAS,CAACC,KAAK,GAAG8B,IAAI,CAACkC,GAAG,CAACnB,KAAK,EAAED,KAAK,KAAK3D,SAAS,GAAG2D,KAAK,GAAGC,KAAK,EAAEK,IAAI,CAACQ,MAAM,CAAC;IAE9G,KAAKtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,IAAI,CAACQ,MAAM,EAAEtB,CAAC,EAAE,EAAE;MAChCC,WAAW,GAAGa,IAAI,CAACd,CAAC,CAAC,CAACpD,OAAO;MAC7BqD,WAAW,CAAChC,gBAAgB,CAACe,QAAQ,CAAC,CAACT,YAAY,CAAC6B,YAAY,CAAC;MACjEpB,QAAQ,CAAC6C,OAAO,CAACZ,SAAS,EAAEjB,CAAC,GAAG,CAAC,CAAC;MAClCY,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAACyE,UAAU,CAACxC,QAAQ,CAACyC,WAAW,GAAG,IAAI;MACjExB,WAAW,CAAC6B,sBAAsB,GAAG,IAAI;MACzC7B,WAAW,CAACxD,KAAK,CAACoF,OAAO,CAACX,MAAM,EAAElB,CAAC,GAAG,CAAC,CAAC;MACxCY,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAACyE,UAAU,CAAC/E,KAAK,CAACgF,WAAW,GAAG,IAAI;MAC9DN,KAAK,CAACnD,GAAG,CAAC,CAACiC,WAAW,CAACzD,IAAI,CAAC,EAAEwD,CAAC,CAAC;MAChCY,SAAS,CAAChE,OAAO,CAACG,QAAQ,CAACyE,UAAU,CAAChF,IAAI,CAACiF,WAAW,GAAG,IAAI;IAC/D;EACF,CAAC,CAAC;EACF,MAAMM,GAAG,GAAGtG,KAAK,CAACuG,OAAO,CAAC,OAAO;IAC/BC,SAAS,EAAEA,CAAA,KAAMrB,SAAS;IAC1BsB,SAAS,EAAEvB,GAAG,IAAI;MAChBI,OAAO,CAACD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,GAAG,CAAC,CAAC;MAC/B,OAAO,MAAMI,OAAO,CAACD,IAAI,IAAIA,IAAI,CAACqB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxF,OAAO,KAAK+D,GAAG,CAAC/D,OAAO,CAAC,CAAC;IACjF;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAO,aAAanB,KAAK,CAAC4G,aAAa,CAAC,QAAQ,EAAE9G,QAAQ,CAAC;IACzDiC,QAAQ,EAAE;MACRC,SAAS,EAAEqD;IACb,CAAC;IACDwB,gBAAgB,EAAE,KAAK;IACvB3B,GAAG,EAAE/E,SAAS,CAAC,CAAC+E,GAAG,EAAEC,SAAS,CAAC,CAAC;IAChC3D,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAEyD,KAAK,CAAC,EAAE,aAAajF,KAAK,CAAC4G,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa5G,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtHE,MAAM,EAAE,qBAAqB;IAC7B3E,KAAK,EAAEqD,SAAS,CAACK,MAAM,GAAG,CAAC;IAC3BkB,KAAK,EAAEvB,SAAS;IAChBwB,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAElH,KAAK,CAACmH;EACf,CAAC,CAAC,EAAE,aAAalH,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtDE,MAAM,EAAE,kBAAkB;IAC1B3E,KAAK,EAAEsD,MAAM,CAACI,MAAM,GAAG,CAAC;IACxBkB,KAAK,EAAEtB,MAAM;IACbuB,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAElH,KAAK,CAACmH;EACf,CAAC,CAAC,EAAE,aAAalH,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtDE,MAAM,EAAE,iBAAiB;IACzB3E,KAAK,EAAEuD,KAAK,CAACG,MAAM;IACnBkB,KAAK,EAAErB,KAAK;IACZsB,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAElH,KAAK,CAACmH;EACf,CAAC,CAAC,CAAC,EAAE,aAAalH,KAAK,CAAC4G,aAAa,CAACnC,OAAO,CAAC0C,QAAQ,EAAE;IACtDC,KAAK,EAAEd;EACT,CAAC,EAAExB,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAMuC,KAAK,GAAG,aAAarH,KAAK,CAAC6E,UAAU,CAAC,CAAC;EAC3CC,QAAQ;EACR,GAAGG;AACL,CAAC,EAAEC,GAAG,KAAK;EACTlF,KAAK,CAACuG,OAAO,CAAC,MAAMtG,MAAM,CAAC;IACzBW;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM0G,KAAK,GAAGtH,KAAK,CAACoF,MAAM,CAAC,CAAC;EAC5B,MAAM;IACJqB,SAAS;IACTD;EACF,CAAC,GAAGxG,KAAK,CAACuH,UAAU,CAAC9C,OAAO,CAAC;EAC7BzE,KAAK,CAACwH,eAAe,CAAC,MAAMf,SAAS,CAACa,KAAK,CAAC,EAAE,EAAE,CAAC;EACjD,OAAO,aAAatH,KAAK,CAAC4G,aAAa,CAAC,eAAe,EAAE9G,QAAQ,CAAC;IAChEoB,QAAQ,EAAEsF,SAAS,CAAC,CAAC;IACrBnF,WAAW,EAAEiG,KAAK;IAClBpC,GAAG,EAAE/E,SAAS,CAAC,CAAC+E,GAAG,EAAEoC,KAAK,CAAC;EAC7B,CAAC,EAAErC,KAAK,CAAC,EAAEH,QAAQ,CAAC;AACtB,CAAC,CAAC;AACF;AACA;AACA;;AAEA,MAAM2C,YAAY,GAAG,aAAazH,KAAK,CAAC6E,UAAU,CAAC,CAAC;EAClDC,QAAQ;EACRU,SAAS;EACTC,MAAM;EACNC,KAAK;EACLgC,MAAM,GAAG,CAAC;EACV,GAAGzC;AACL,CAAC,EAAE0C,YAAY,KAAK;EAClB,MAAMC,SAAS,GAAG5H,KAAK,CAACoF,MAAM,CAAC,IAAI,CAAC;EACpClF,QAAQ,CAAC,MAAM;IACb,MAAM2H,IAAI,GAAGD,SAAS,CAACzG,OAAO,CAACG,QAAQ,CAACyE,UAAU;IAClD8B,IAAI,CAACtE,QAAQ,CAACyC,WAAW,GAAG,IAAI;IAChC,IAAIP,MAAM,EAAEoC,IAAI,CAAC7G,KAAK,CAACgF,WAAW,GAAG,IAAI;IACzC,IAAIN,KAAK,EAAEmC,IAAI,CAAC9G,IAAI,CAACiF,WAAW,GAAG,IAAI;EACzC,CAAC,CAAC;EACF,OAAO,aAAahG,KAAK,CAAC4G,aAAa,CAAC,QAAQ,EAAE9G,QAAQ,CAAC;IACzDoF,GAAG,EAAE/E,SAAS,CAAC,CAACwH,YAAY,EAAEC,SAAS,CAAC;EAC1C,CAAC,EAAE3C,KAAK,CAAC,EAAE,aAAajF,KAAK,CAAC4G,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAa5G,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IACtHE,MAAM,EAAE,qBAAqB;IAC7B3E,KAAK,EAAEqD,SAAS,CAACK,MAAM,GAAG6B,MAAM;IAChCX,KAAK,EAAEvB,SAAS;IAChBwB,QAAQ,EAAEU,MAAM;IAChBT,KAAK,EAAElH,KAAK,CAACmH;EACf,CAAC,CAAC,EAAEzB,MAAM,IAAI,aAAazF,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IAChEE,MAAM,EAAE,kBAAkB;IAC1B3E,KAAK,EAAEsD,MAAM,CAACI,MAAM,GAAG6B,MAAM;IAC7BX,KAAK,EAAEtB,MAAM;IACbuB,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAElH,KAAK,CAACmH;EACf,CAAC,CAAC,EAAExB,KAAK,IAAI,aAAa1F,KAAK,CAAC4G,aAAa,CAAC,iBAAiB,EAAE;IAC/DE,MAAM,EAAE,iBAAiB;IACzB3E,KAAK,EAAEuD,KAAK,CAACG,MAAM,GAAG6B,MAAM;IAC5BX,KAAK,EAAErB,KAAK;IACZsB,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAElH,KAAK,CAACmH;EACf,CAAC,CAAC,CAAC,EAAEpC,QAAQ,CAAC;AAChB,CAAC,CAAC;AACF,MAAMxC,MAAM,GAAG,aAAatC,KAAK,CAAC6E,UAAU,CAAC,CAACI,KAAK,EAAE0C,YAAY,KAAK;EACpE,IAAI1C,KAAK,CAACO,SAAS,YAAYG,YAAY,EAAE;IAC3C,OAAO,aAAa3F,KAAK,CAAC4G,aAAa,CAACa,YAAY,EAAE3H,QAAQ,CAAC,CAAC,CAAC,EAAEmF,KAAK,EAAE;MACxEC,GAAG,EAAEyC;IACP,CAAC,CAAC,CAAC;EACL,CAAC,MAAM,OAAO,aAAa3H,KAAK,CAAC4G,aAAa,CAAChC,eAAe,EAAE9E,QAAQ,CAAC,CAAC,CAAC,EAAEmF,KAAK,EAAE;IAClFC,GAAG,EAAEyC;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASN,KAAK,EAAE/E,MAAM,EAAEmF,YAAY,EAAE7G,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}