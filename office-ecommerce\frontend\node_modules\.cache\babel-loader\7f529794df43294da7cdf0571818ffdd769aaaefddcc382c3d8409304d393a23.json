{"ast": null, "code": "import { Vector3 } from 'three';\nexport class SeparatingAxisBounds {\n  constructor() {\n    this.min = Infinity;\n    this.max = -Infinity;\n  }\n  setFromPointsField(points, field) {\n    let min = Infinity;\n    let max = -Infinity;\n    for (let i = 0, l = points.length; i < l; i++) {\n      const p = points[i];\n      const val = p[field];\n      min = val < min ? val : min;\n      max = val > max ? val : max;\n    }\n    this.min = min;\n    this.max = max;\n  }\n  setFromPoints(axis, points) {\n    let min = Infinity;\n    let max = -Infinity;\n    for (let i = 0, l = points.length; i < l; i++) {\n      const p = points[i];\n      const val = axis.dot(p);\n      min = val < min ? val : min;\n      max = val > max ? val : max;\n    }\n    this.min = min;\n    this.max = max;\n  }\n  isSeparated(other) {\n    return this.min > other.max || other.min > this.max;\n  }\n}\nSeparatingAxisBounds.prototype.setFromBox = function () {\n  const p = new Vector3();\n  return function setFromBox(axis, box) {\n    const boxMin = box.min;\n    const boxMax = box.max;\n    let min = Infinity;\n    let max = -Infinity;\n    for (let x = 0; x <= 1; x++) {\n      for (let y = 0; y <= 1; y++) {\n        for (let z = 0; z <= 1; z++) {\n          p.x = boxMin.x * x + boxMax.x * (1 - x);\n          p.y = boxMin.y * y + boxMax.y * (1 - y);\n          p.z = boxMin.z * z + boxMax.z * (1 - z);\n          const val = axis.dot(p);\n          min = Math.min(val, min);\n          max = Math.max(val, max);\n        }\n      }\n    }\n    this.min = min;\n    this.max = max;\n  };\n}();\nexport const areIntersecting = function () {\n  const cacheSatBounds = new SeparatingAxisBounds();\n  return function areIntersecting(shape1, shape2) {\n    const points1 = shape1.points;\n    const satAxes1 = shape1.satAxes;\n    const satBounds1 = shape1.satBounds;\n    const points2 = shape2.points;\n    const satAxes2 = shape2.satAxes;\n    const satBounds2 = shape2.satBounds;\n\n    // check axes of the first shape\n    for (let i = 0; i < 3; i++) {\n      const sb = satBounds1[i];\n      const sa = satAxes1[i];\n      cacheSatBounds.setFromPoints(sa, points2);\n      if (sb.isSeparated(cacheSatBounds)) return false;\n    }\n\n    // check axes of the second shape\n    for (let i = 0; i < 3; i++) {\n      const sb = satBounds2[i];\n      const sa = satAxes2[i];\n      cacheSatBounds.setFromPoints(sa, points1);\n      if (sb.isSeparated(cacheSatBounds)) return false;\n    }\n  };\n}();", "map": {"version": 3, "names": ["Vector3", "SeparatingAxisBounds", "constructor", "min", "Infinity", "max", "setFromPointsField", "points", "field", "i", "l", "length", "p", "val", "setFromPoints", "axis", "dot", "isSeparated", "other", "prototype", "setFromBox", "box", "boxMin", "boxMax", "x", "y", "z", "Math", "areIntersecting", "cacheSatBounds", "shape1", "shape2", "points1", "satAxes1", "satAxes", "satBounds1", "satBounds", "points2", "satAxes2", "satBounds2", "sb", "sa"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/math/SeparatingAxisBounds.js"], "sourcesContent": ["import { Vector3 } from 'three';\n\nexport class SeparatingAxisBounds {\n\n\tconstructor() {\n\n\t\tthis.min = Infinity;\n\t\tthis.max = - Infinity;\n\n\t}\n\n\tsetFromPointsField( points, field ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = p[ field ];\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tsetFromPoints( axis, points ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = axis.dot( p );\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tisSeparated( other ) {\n\n\t\treturn this.min > other.max || other.min > this.max;\n\n\t}\n\n}\n\nSeparatingAxisBounds.prototype.setFromBox = ( function () {\n\n\tconst p = new Vector3();\n\treturn function setFromBox( axis, box ) {\n\n\t\tconst boxMin = box.min;\n\t\tconst boxMax = box.max;\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tp.x = boxMin.x * x + boxMax.x * ( 1 - x );\n\t\t\t\t\tp.y = boxMin.y * y + boxMax.y * ( 1 - y );\n\t\t\t\t\tp.z = boxMin.z * z + boxMax.z * ( 1 - z );\n\n\t\t\t\t\tconst val = axis.dot( p );\n\t\t\t\t\tmin = Math.min( val, min );\n\t\t\t\t\tmax = Math.max( val, max );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t};\n\n} )();\n\nexport const areIntersecting = ( function () {\n\n\tconst cacheSatBounds = new SeparatingAxisBounds();\n\treturn function areIntersecting( shape1, shape2 ) {\n\n\t\tconst points1 = shape1.points;\n\t\tconst satAxes1 = shape1.satAxes;\n\t\tconst satBounds1 = shape1.satBounds;\n\n\t\tconst points2 = shape2.points;\n\t\tconst satAxes2 = shape2.satAxes;\n\t\tconst satBounds2 = shape2.satBounds;\n\n\t\t// check axes of the first shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds1[ i ];\n\t\t\tconst sa = satAxes1[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points2 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check axes of the second shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds2[ i ];\n\t\t\tconst sa = satAxes2[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points1 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t};\n\n} )();\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAE/B,OAAO,MAAMC,oBAAoB,CAAC;EAEjCC,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACC,GAAG,GAAGC,QAAQ;IACnB,IAAI,CAACC,GAAG,GAAG,CAAED,QAAQ;EAEtB;EAEAE,kBAAkBA,CAAEC,MAAM,EAAEC,KAAK,EAAG;IAEnC,IAAIL,GAAG,GAAGC,QAAQ;IAClB,IAAIC,GAAG,GAAG,CAAED,QAAQ;IACpB,KAAM,IAAIK,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEjD,MAAMG,CAAC,GAAGL,MAAM,CAAEE,CAAC,CAAE;MACrB,MAAMI,GAAG,GAAGD,CAAC,CAAEJ,KAAK,CAAE;MACtBL,GAAG,GAAGU,GAAG,GAAGV,GAAG,GAAGU,GAAG,GAAGV,GAAG;MAC3BE,GAAG,GAAGQ,GAAG,GAAGR,GAAG,GAAGQ,GAAG,GAAGR,GAAG;IAE5B;IAEA,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,GAAG,GAAGA,GAAG;EAEf;EAEAS,aAAaA,CAAEC,IAAI,EAAER,MAAM,EAAG;IAE7B,IAAIJ,GAAG,GAAGC,QAAQ;IAClB,IAAIC,GAAG,GAAG,CAAED,QAAQ;IACpB,KAAM,IAAIK,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEjD,MAAMG,CAAC,GAAGL,MAAM,CAAEE,CAAC,CAAE;MACrB,MAAMI,GAAG,GAAGE,IAAI,CAACC,GAAG,CAAEJ,CAAE,CAAC;MACzBT,GAAG,GAAGU,GAAG,GAAGV,GAAG,GAAGU,GAAG,GAAGV,GAAG;MAC3BE,GAAG,GAAGQ,GAAG,GAAGR,GAAG,GAAGQ,GAAG,GAAGR,GAAG;IAE5B;IAEA,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,GAAG,GAAGA,GAAG;EAEf;EAEAY,WAAWA,CAAEC,KAAK,EAAG;IAEpB,OAAO,IAAI,CAACf,GAAG,GAAGe,KAAK,CAACb,GAAG,IAAIa,KAAK,CAACf,GAAG,GAAG,IAAI,CAACE,GAAG;EAEpD;AAED;AAEAJ,oBAAoB,CAACkB,SAAS,CAACC,UAAU,GAAK,YAAY;EAEzD,MAAMR,CAAC,GAAG,IAAIZ,OAAO,CAAC,CAAC;EACvB,OAAO,SAASoB,UAAUA,CAAEL,IAAI,EAAEM,GAAG,EAAG;IAEvC,MAAMC,MAAM,GAAGD,GAAG,CAAClB,GAAG;IACtB,MAAMoB,MAAM,GAAGF,GAAG,CAAChB,GAAG;IACtB,IAAIF,GAAG,GAAGC,QAAQ;IAClB,IAAIC,GAAG,GAAG,CAAED,QAAQ;IACpB,KAAM,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE/B,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE/B,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;UAE/Bd,CAAC,CAACY,CAAC,GAAGF,MAAM,CAACE,CAAC,GAAGA,CAAC,GAAGD,MAAM,CAACC,CAAC,IAAK,CAAC,GAAGA,CAAC,CAAE;UACzCZ,CAAC,CAACa,CAAC,GAAGH,MAAM,CAACG,CAAC,GAAGA,CAAC,GAAGF,MAAM,CAACE,CAAC,IAAK,CAAC,GAAGA,CAAC,CAAE;UACzCb,CAAC,CAACc,CAAC,GAAGJ,MAAM,CAACI,CAAC,GAAGA,CAAC,GAAGH,MAAM,CAACG,CAAC,IAAK,CAAC,GAAGA,CAAC,CAAE;UAEzC,MAAMb,GAAG,GAAGE,IAAI,CAACC,GAAG,CAAEJ,CAAE,CAAC;UACzBT,GAAG,GAAGwB,IAAI,CAACxB,GAAG,CAAEU,GAAG,EAAEV,GAAI,CAAC;UAC1BE,GAAG,GAAGsB,IAAI,CAACtB,GAAG,CAAEQ,GAAG,EAAER,GAAI,CAAC;QAE3B;MAED;IAED;IAEA,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,GAAG,GAAGA,GAAG;EAEf,CAAC;AAEF,CAAC,CAAG,CAAC;AAEL,OAAO,MAAMuB,eAAe,GAAK,YAAY;EAE5C,MAAMC,cAAc,GAAG,IAAI5B,oBAAoB,CAAC,CAAC;EACjD,OAAO,SAAS2B,eAAeA,CAAEE,MAAM,EAAEC,MAAM,EAAG;IAEjD,MAAMC,OAAO,GAAGF,MAAM,CAACvB,MAAM;IAC7B,MAAM0B,QAAQ,GAAGH,MAAM,CAACI,OAAO;IAC/B,MAAMC,UAAU,GAAGL,MAAM,CAACM,SAAS;IAEnC,MAAMC,OAAO,GAAGN,MAAM,CAACxB,MAAM;IAC7B,MAAM+B,QAAQ,GAAGP,MAAM,CAACG,OAAO;IAC/B,MAAMK,UAAU,GAAGR,MAAM,CAACK,SAAS;;IAEnC;IACA,KAAM,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAM+B,EAAE,GAAGL,UAAU,CAAE1B,CAAC,CAAE;MAC1B,MAAMgC,EAAE,GAAGR,QAAQ,CAAExB,CAAC,CAAE;MACxBoB,cAAc,CAACf,aAAa,CAAE2B,EAAE,EAAEJ,OAAQ,CAAC;MAC3C,IAAKG,EAAE,CAACvB,WAAW,CAAEY,cAAe,CAAC,EAAG,OAAO,KAAK;IAErD;;IAEA;IACA,KAAM,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAM+B,EAAE,GAAGD,UAAU,CAAE9B,CAAC,CAAE;MAC1B,MAAMgC,EAAE,GAAGH,QAAQ,CAAE7B,CAAC,CAAE;MACxBoB,cAAc,CAACf,aAAa,CAAE2B,EAAE,EAAET,OAAQ,CAAC;MAC3C,IAAKQ,EAAE,CAACvB,WAAW,CAAEY,cAAe,CAAC,EAAG,OAAO,KAAK;IAErD;EAED,CAAC;AAEF,CAAC,CAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}