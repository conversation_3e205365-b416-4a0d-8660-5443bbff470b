const axios = require('axios');

async function testHealth() {
  try {
    console.log('🏥 Testing Backend Health...');

    // Test basic health endpoint
    console.log('\n🔍 Testing /health endpoint...');
    try {
      const healthResponse = await axios.get('http://localhost:5001/health');
      console.log('✅ Health endpoint working');
      console.log('Response:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Health endpoint failed:', error.code, error.message);
    }

    // Test API health endpoint
    console.log('\n🔍 Testing /api/health endpoint...');
    try {
      const apiHealthResponse = await axios.get('http://localhost:5001/api/health');
      console.log('✅ API health endpoint working');
      console.log('Response:', JSON.stringify(apiHealthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ API health endpoint failed:', error.code, error.message);
    }

    // Test root endpoint
    console.log('\n🔍 Testing root endpoint...');
    try {
      const rootResponse = await axios.get('http://localhost:5001/');
      console.log('✅ Root endpoint working');
      console.log('Response:', JSON.stringify(rootResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Root endpoint failed:', error.code, error.message);
    }

    console.log('\n✅ Health check completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testHealth();
