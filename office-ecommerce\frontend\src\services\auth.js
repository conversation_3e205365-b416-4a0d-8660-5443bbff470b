import apiClient from './apiClient';

export const authService = {
    async login(email, password) {
        try {
            const response = await apiClient.post('/api/auth/login', { email, password });
            return response;
        } catch (error) {
            // Fallback to mock authentication if backend is not accessible
            console.warn('Backend not accessible, using mock authentication:', error.message);

            // Store that we're in mock mode
            localStorage.setItem('mockMode', 'true');

            // Mock authentication logic
            if (email === '<EMAIL>' && password === 'admin123') {
                return {
                    success: true,
                    message: 'Login successful (mock)',
                    data: {
                        token: 'mock-jwt-token-admin',
                        user: {
                            id: 1,
                            firstName: 'Admin',
                            lastName: 'User',
                            email: email,
                            role: 'Admin'
                        }
                    }
                };
            } else if (email === '<EMAIL>' && password === 'admin123') {
                return {
                    success: true,
                    message: 'Login successful (mock)',
                    data: {
                        token: 'mock-jwt-token-manager',
                        user: {
                            id: 2,
                            firstName: 'Manager',
                            lastName: 'User',
                            email: email,
                            role: 'Employee'
                        }
                    }
                };
            } else {
                return {
                    success: false,
                    message: 'Invalid credentials',
                    error: 'Invalid email or password'
                };
            }
        }
    },

    async register(userData) {
        // TODO: Implement register endpoint in backend
        throw new Error('Registration not implemented yet');
    },

    async getProfile() {
        // Mock profile data
        return {
            success: true,
            data: {
                id: 1,
                name: 'Demo User',
                email: '<EMAIL>',
                phone: '(*************',
                address: '123 Demo Street, Demo City, DC 12345'
            }
        };
    },

    async updateProfile(profileData) {
        // Mock profile update (always succeeds)
        return {
            success: true,
            data: {
                ...profileData,
                id: 1
            }
        };
    }
};
