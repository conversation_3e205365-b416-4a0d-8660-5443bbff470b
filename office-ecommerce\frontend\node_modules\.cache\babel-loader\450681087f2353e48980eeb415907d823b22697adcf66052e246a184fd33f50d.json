{"ast": null, "code": "import { DepthTexture, DepthFormat, UnsignedShortType } from 'three';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\nfunction useDepthBuffer({\n  size = 256,\n  frames = Infinity\n} = {}) {\n  const dpr = useThree(state => state.viewport.dpr);\n  const {\n    width,\n    height\n  } = useThree(state => state.size);\n  const w = size || width * dpr;\n  const h = size || height * dpr;\n  const depthConfig = React.useMemo(() => {\n    const depthTexture = new DepthTexture(w, h);\n    depthTexture.format = DepthFormat;\n    depthTexture.type = UnsignedShortType;\n    return {\n      depthTexture\n    };\n  }, [w, h]);\n  let count = 0;\n  const depthFBO = useFBO(w, h, depthConfig);\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      state.gl.setRenderTarget(depthFBO);\n      state.gl.render(state.scene, state.camera);\n      state.gl.setRenderTarget(null);\n      count++;\n    }\n  });\n  return depthFBO.depthTexture;\n}\nexport { useDepthBuffer };", "map": {"version": 3, "names": ["DepthTexture", "DepthFormat", "UnsignedShortType", "React", "useThree", "useFrame", "useFBO", "useDepthBuffer", "size", "frames", "Infinity", "dpr", "state", "viewport", "width", "height", "w", "h", "depthConfig", "useMemo", "depthTexture", "format", "type", "count", "depthFBO", "gl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "scene", "camera"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useDepthBuffer.js"], "sourcesContent": ["import { DepthTexture, DepthFormat, UnsignedShortType } from 'three';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\n\nfunction useDepthBuffer({\n  size = 256,\n  frames = Infinity\n} = {}) {\n  const dpr = useThree(state => state.viewport.dpr);\n  const {\n    width,\n    height\n  } = useThree(state => state.size);\n  const w = size || width * dpr;\n  const h = size || height * dpr;\n  const depthConfig = React.useMemo(() => {\n    const depthTexture = new DepthTexture(w, h);\n    depthTexture.format = DepthFormat;\n    depthTexture.type = UnsignedShortType;\n    return {\n      depthTexture\n    };\n  }, [w, h]);\n  let count = 0;\n  const depthFBO = useFBO(w, h, depthConfig);\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      state.gl.setRenderTarget(depthFBO);\n      state.gl.render(state.scene, state.camera);\n      state.gl.setRenderTarget(null);\n      count++;\n    }\n  });\n  return depthFBO.depthTexture;\n}\n\nexport { useDepthBuffer };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,WAAW,EAAEC,iBAAiB,QAAQ,OAAO;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,MAAM,QAAQ,aAAa;AAEpC,SAASC,cAAcA,CAAC;EACtBC,IAAI,GAAG,GAAG;EACVC,MAAM,GAAGC;AACX,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMC,GAAG,GAAGP,QAAQ,CAACQ,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAACF,GAAG,CAAC;EACjD,MAAM;IACJG,KAAK;IACLC;EACF,CAAC,GAAGX,QAAQ,CAACQ,KAAK,IAAIA,KAAK,CAACJ,IAAI,CAAC;EACjC,MAAMQ,CAAC,GAAGR,IAAI,IAAIM,KAAK,GAAGH,GAAG;EAC7B,MAAMM,CAAC,GAAGT,IAAI,IAAIO,MAAM,GAAGJ,GAAG;EAC9B,MAAMO,WAAW,GAAGf,KAAK,CAACgB,OAAO,CAAC,MAAM;IACtC,MAAMC,YAAY,GAAG,IAAIpB,YAAY,CAACgB,CAAC,EAAEC,CAAC,CAAC;IAC3CG,YAAY,CAACC,MAAM,GAAGpB,WAAW;IACjCmB,YAAY,CAACE,IAAI,GAAGpB,iBAAiB;IACrC,OAAO;MACLkB;IACF,CAAC;EACH,CAAC,EAAE,CAACJ,CAAC,EAAEC,CAAC,CAAC,CAAC;EACV,IAAIM,KAAK,GAAG,CAAC;EACb,MAAMC,QAAQ,GAAGlB,MAAM,CAACU,CAAC,EAAEC,CAAC,EAAEC,WAAW,CAAC;EAC1Cb,QAAQ,CAACO,KAAK,IAAI;IAChB,IAAIH,MAAM,KAAKC,QAAQ,IAAIa,KAAK,GAAGd,MAAM,EAAE;MACzCG,KAAK,CAACa,EAAE,CAACC,eAAe,CAACF,QAAQ,CAAC;MAClCZ,KAAK,CAACa,EAAE,CAACE,MAAM,CAACf,KAAK,CAACgB,KAAK,EAAEhB,KAAK,CAACiB,MAAM,CAAC;MAC1CjB,KAAK,CAACa,EAAE,CAACC,eAAe,CAAC,IAAI,CAAC;MAC9BH,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ,CAACJ,YAAY;AAC9B;AAEA,SAASb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}