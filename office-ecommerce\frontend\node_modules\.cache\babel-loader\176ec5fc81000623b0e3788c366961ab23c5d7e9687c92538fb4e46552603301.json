{"ast": null, "code": "import { AnimationClip, Bone, Box3, BufferAttribute, BufferGeometry, ClampToEdgeWrapping, Color, DirectionalLight, DoubleSide, FileLoader, FrontSide, Group, ImageBitmapLoader, InstancedMesh, InterleavedBuffer, InterleavedBufferAttribute, Interpolant, InterpolateDiscrete, InterpolateLinear, Line, LineBasicMaterial, LineLoop, LineSegments, LinearFilter, LinearMipmapLinearFilter, LinearMipmapNearestFilter, Loader, LoaderUtils, Material, MathUtils, Matrix4, Mesh, MeshBasicMaterial, MeshPhysicalMaterial, MeshStandardMaterial, MirroredRepeatWrapping, NearestFilter, NearestMipmapLinearFilter, NearestMipmapNearestFilter, NumberKeyframeTrack, Object3D, OrthographicCamera, PerspectiveCamera, PointLight, Points, PointsMaterial, PropertyBinding, Quaternion, QuaternionKeyframeTrack, RepeatWrapping, Skeleton, SkinnedMesh, Sphere, SpotLight, Texture, TextureLoader, TriangleFanDrawMode, TriangleStripDrawMode, Vector2, Vector3, VectorKeyframeTrack, SRGBColorSpace } from 'three';\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils.js';\nclass GLTFLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.dracoLoader = null;\n    this.ktx2Loader = null;\n    this.meshoptDecoder = null;\n    this.pluginCallbacks = [];\n    this.register(function (parser) {\n      return new GLTFMaterialsClearcoatExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureBasisUExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureWebPExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureAVIFExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSheenExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsTransmissionExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsVolumeExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIorExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsEmissiveStrengthExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSpecularExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIridescenceExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsAnisotropyExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFLightsExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshoptCompression(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshGpuInstancing(parser);\n    });\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    let resourcePath;\n    if (this.resourcePath !== '') {\n      resourcePath = this.resourcePath;\n    } else if (this.path !== '') {\n      resourcePath = this.path;\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url);\n    }\n\n    // Tells the LoadingManager to track an extra item, which resolves after\n    // the model is fully loaded. This means the count of items loaded will\n    // be incorrect, but ensures manager.onLoad() does not fire early.\n    this.manager.itemStart(url);\n    const _onError = function (e) {\n      if (onError) {\n        onError(e);\n      } else {\n        console.error(e);\n      }\n      scope.manager.itemError(url);\n      scope.manager.itemEnd(url);\n    };\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType('arraybuffer');\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (data) {\n      try {\n        scope.parse(data, resourcePath, function (gltf) {\n          onLoad(gltf);\n          scope.manager.itemEnd(url);\n        }, _onError);\n      } catch (e) {\n        _onError(e);\n      }\n    }, onProgress, _onError);\n  }\n  setDRACOLoader(dracoLoader) {\n    this.dracoLoader = dracoLoader;\n    return this;\n  }\n  setDDSLoader() {\n    throw new Error('THREE.GLTFLoader: \"MSFT_texture_dds\" no longer supported. Please update to \"KHR_texture_basisu\".');\n  }\n  setKTX2Loader(ktx2Loader) {\n    this.ktx2Loader = ktx2Loader;\n    return this;\n  }\n  setMeshoptDecoder(meshoptDecoder) {\n    this.meshoptDecoder = meshoptDecoder;\n    return this;\n  }\n  register(callback) {\n    if (this.pluginCallbacks.indexOf(callback) === -1) {\n      this.pluginCallbacks.push(callback);\n    }\n    return this;\n  }\n  unregister(callback) {\n    if (this.pluginCallbacks.indexOf(callback) !== -1) {\n      this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1);\n    }\n    return this;\n  }\n  parse(data, path, onLoad, onError) {\n    let json;\n    const extensions = {};\n    const plugins = {};\n    const textDecoder = new TextDecoder();\n    if (typeof data === 'string') {\n      json = JSON.parse(data);\n    } else if (data instanceof ArrayBuffer) {\n      const magic = textDecoder.decode(new Uint8Array(data, 0, 4));\n      if (magic === BINARY_EXTENSION_HEADER_MAGIC) {\n        try {\n          extensions[EXTENSIONS.KHR_BINARY_GLTF] = new GLTFBinaryExtension(data);\n        } catch (error) {\n          if (onError) onError(error);\n          return;\n        }\n        json = JSON.parse(extensions[EXTENSIONS.KHR_BINARY_GLTF].content);\n      } else {\n        json = JSON.parse(textDecoder.decode(data));\n      }\n    } else {\n      json = data;\n    }\n    if (json.asset === undefined || json.asset.version[0] < 2) {\n      if (onError) onError(new Error('THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.'));\n      return;\n    }\n    const parser = new GLTFParser(json, {\n      path: path || this.resourcePath || '',\n      crossOrigin: this.crossOrigin,\n      requestHeader: this.requestHeader,\n      manager: this.manager,\n      ktx2Loader: this.ktx2Loader,\n      meshoptDecoder: this.meshoptDecoder\n    });\n    parser.fileLoader.setRequestHeader(this.requestHeader);\n    for (let i = 0; i < this.pluginCallbacks.length; i++) {\n      const plugin = this.pluginCallbacks[i](parser);\n      plugins[plugin.name] = plugin;\n\n      // Workaround to avoid determining as unknown extension\n      // in addUnknownExtensionsToUserData().\n      // Remove this workaround if we move all the existing\n      // extension handlers to plugin system\n      extensions[plugin.name] = true;\n    }\n    if (json.extensionsUsed) {\n      for (let i = 0; i < json.extensionsUsed.length; ++i) {\n        const extensionName = json.extensionsUsed[i];\n        const extensionsRequired = json.extensionsRequired || [];\n        switch (extensionName) {\n          case EXTENSIONS.KHR_MATERIALS_UNLIT:\n            extensions[extensionName] = new GLTFMaterialsUnlitExtension();\n            break;\n          case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n            extensions[extensionName] = new GLTFDracoMeshCompressionExtension(json, this.dracoLoader);\n            break;\n          case EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n            extensions[extensionName] = new GLTFTextureTransformExtension();\n            break;\n          case EXTENSIONS.KHR_MESH_QUANTIZATION:\n            extensions[extensionName] = new GLTFMeshQuantizationExtension();\n            break;\n          default:\n            if (extensionsRequired.indexOf(extensionName) >= 0 && plugins[extensionName] === undefined) {\n              console.warn('THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".');\n            }\n        }\n      }\n    }\n    parser.setExtensions(extensions);\n    parser.setPlugins(plugins);\n    parser.parse(onLoad, onError);\n  }\n  parseAsync(data, path) {\n    const scope = this;\n    return new Promise(function (resolve, reject) {\n      scope.parse(data, path, resolve, reject);\n    });\n  }\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n  let objects = {};\n  return {\n    get: function (key) {\n      return objects[key];\n    },\n    add: function (key, object) {\n      objects[key] = object;\n    },\n    remove: function (key) {\n      delete objects[key];\n    },\n    removeAll: function () {\n      objects = {};\n    }\n  };\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n  KHR_BINARY_GLTF: 'KHR_binary_glTF',\n  KHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n  KHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n  KHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n  KHR_MATERIALS_IOR: 'KHR_materials_ior',\n  KHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n  KHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n  KHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n  KHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n  KHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n  KHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n  KHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n  KHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n  KHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n  KHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n  KHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n  EXT_TEXTURE_WEBP: 'EXT_texture_webp',\n  EXT_TEXTURE_AVIF: 'EXT_texture_avif',\n  EXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n  EXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing'\n};\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightsExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;\n\n    // Object3D instance caches\n    this.cache = {\n      refs: {},\n      uses: {}\n    };\n  }\n  _markDefs() {\n    const parser = this.parser;\n    const nodeDefs = this.parser.json.nodes || [];\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.extensions && nodeDef.extensions[this.name] && nodeDef.extensions[this.name].light !== undefined) {\n        parser._addNodeRef(this.cache, nodeDef.extensions[this.name].light);\n      }\n    }\n  }\n  _loadLight(lightIndex) {\n    const parser = this.parser;\n    const cacheKey = 'light:' + lightIndex;\n    let dependency = parser.cache.get(cacheKey);\n    if (dependency) return dependency;\n    const json = parser.json;\n    const extensions = json.extensions && json.extensions[this.name] || {};\n    const lightDefs = extensions.lights || [];\n    const lightDef = lightDefs[lightIndex];\n    let lightNode;\n    const color = new Color(0xffffff);\n    if (lightDef.color !== undefined) color.fromArray(lightDef.color);\n    const range = lightDef.range !== undefined ? lightDef.range : 0;\n    switch (lightDef.type) {\n      case 'directional':\n        lightNode = new DirectionalLight(color);\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      case 'point':\n        lightNode = new PointLight(color);\n        lightNode.distance = range;\n        break;\n      case 'spot':\n        lightNode = new SpotLight(color);\n        lightNode.distance = range;\n        // Handle spotlight properties.\n        lightDef.spot = lightDef.spot || {};\n        lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0;\n        lightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0;\n        lightNode.angle = lightDef.spot.outerConeAngle;\n        lightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      default:\n        throw new Error('THREE.GLTFLoader: Unexpected light type: ' + lightDef.type);\n    }\n\n    // Some lights (e.g. spot) default to a position other than the origin. Reset the position\n    // here, because node-level parsing will only override position if explicitly specified.\n    lightNode.position.set(0, 0, 0);\n    lightNode.decay = 2;\n    assignExtrasToUserData(lightNode, lightDef);\n    if (lightDef.intensity !== undefined) lightNode.intensity = lightDef.intensity;\n    lightNode.name = parser.createUniqueName(lightDef.name || 'light_' + lightIndex);\n    dependency = Promise.resolve(lightNode);\n    parser.cache.add(cacheKey, dependency);\n    return dependency;\n  }\n  getDependency(type, index) {\n    if (type !== 'light') return;\n    return this._loadLight(index);\n  }\n  createNodeAttachment(nodeIndex) {\n    const self = this;\n    const parser = this.parser;\n    const json = parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    const lightDef = nodeDef.extensions && nodeDef.extensions[this.name] || {};\n    const lightIndex = lightDef.light;\n    if (lightIndex === undefined) return null;\n    return this._loadLight(lightIndex).then(function (light) {\n      return parser._getNodeRef(self.cache, lightIndex, light);\n    });\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MATERIALS_UNLIT;\n  }\n  getMaterialType() {\n    return MeshBasicMaterial;\n  }\n  extendParams(materialParams, materialDef, parser) {\n    const pending = [];\n    materialParams.color = new Color(1.0, 1.0, 1.0);\n    materialParams.opacity = 1.0;\n    const metallicRoughness = materialDef.pbrMetallicRoughness;\n    if (metallicRoughness) {\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.fromArray(array);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const emissiveStrength = materialDef.extensions[this.name].emissiveStrength;\n    if (emissiveStrength !== undefined) {\n      materialParams.emissiveIntensity = emissiveStrength;\n    }\n    return Promise.resolve();\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.clearcoatFactor !== undefined) {\n      materialParams.clearcoat = extension.clearcoatFactor;\n    }\n    if (extension.clearcoatTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatMap', extension.clearcoatTexture));\n    }\n    if (extension.clearcoatRoughnessFactor !== undefined) {\n      materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;\n    }\n    if (extension.clearcoatRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture));\n    }\n    if (extension.clearcoatNormalTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture));\n      if (extension.clearcoatNormalTexture.scale !== undefined) {\n        const scale = extension.clearcoatNormalTexture.scale;\n        materialParams.clearcoatNormalScale = new Vector2(scale, scale);\n      }\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.iridescenceFactor !== undefined) {\n      materialParams.iridescence = extension.iridescenceFactor;\n    }\n    if (extension.iridescenceTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceMap', extension.iridescenceTexture));\n    }\n    if (extension.iridescenceIor !== undefined) {\n      materialParams.iridescenceIOR = extension.iridescenceIor;\n    }\n    if (materialParams.iridescenceThicknessRange === undefined) {\n      materialParams.iridescenceThicknessRange = [100, 400];\n    }\n    if (extension.iridescenceThicknessMinimum !== undefined) {\n      materialParams.iridescenceThicknessRange[0] = extension.iridescenceThicknessMinimum;\n    }\n    if (extension.iridescenceThicknessMaximum !== undefined) {\n      materialParams.iridescenceThicknessRange[1] = extension.iridescenceThicknessMaximum;\n    }\n    if (extension.iridescenceThicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SHEEN;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    materialParams.sheenColor = new Color(0, 0, 0);\n    materialParams.sheenRoughness = 0;\n    materialParams.sheen = 1;\n    const extension = materialDef.extensions[this.name];\n    if (extension.sheenColorFactor !== undefined) {\n      materialParams.sheenColor.fromArray(extension.sheenColorFactor);\n    }\n    if (extension.sheenRoughnessFactor !== undefined) {\n      materialParams.sheenRoughness = extension.sheenRoughnessFactor;\n    }\n    if (extension.sheenColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace));\n    }\n    if (extension.sheenRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.transmissionFactor !== undefined) {\n      materialParams.transmission = extension.transmissionFactor;\n    }\n    if (extension.transmissionTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'transmissionMap', extension.transmissionTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_VOLUME;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0;\n    if (extension.thicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'thicknessMap', extension.thicknessTexture));\n    }\n    materialParams.attenuationDistance = extension.attenuationDistance || Infinity;\n    const colorArray = extension.attenuationColor || [1, 1, 1];\n    materialParams.attenuationColor = new Color(colorArray[0], colorArray[1], colorArray[2]);\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IOR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const extension = materialDef.extensions[this.name];\n    materialParams.ior = extension.ior !== undefined ? extension.ior : 1.5;\n    return Promise.resolve();\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0;\n    if (extension.specularTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularIntensityMap', extension.specularTexture));\n    }\n    const colorArray = extension.specularColorFactor || [1, 1, 1];\n    materialParams.specularColor = new Color(colorArray[0], colorArray[1], colorArray[2]);\n    if (extension.specularColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.anisotropyStrength !== undefined) {\n      materialParams.anisotropy = extension.anisotropyStrength;\n    }\n    if (extension.anisotropyRotation !== undefined) {\n      materialParams.anisotropyRotation = extension.anisotropyRotation;\n    }\n    if (extension.anisotropyTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'anisotropyMap', extension.anisotropyTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n */\nclass GLTFTextureBasisUExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_TEXTURE_BASISU;\n  }\n  loadTexture(textureIndex) {\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[this.name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[this.name];\n    const loader = parser.options.ktx2Loader;\n    if (!loader) {\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n        throw new Error('THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures');\n      } else {\n        // Assumes that the extension is optional and that a fallback texture is present\n        return null;\n      }\n    }\n    return parser.loadTextureImage(textureIndex, extension.source, loader);\n  }\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n */\nclass GLTFTextureWebPExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_WEBP;\n    this.isSupported = null;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader);\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: WebP required by asset but unsupported.');\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex);\n    });\n  }\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image();\n\n        // Lossy test image. Support for lossy images doesn't guarantee support for all\n        // WebP images, unfortunately.\n        image.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1);\n        };\n      });\n    }\n    return this.isSupported;\n  }\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n */\nclass GLTFTextureAVIFExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_AVIF;\n    this.isSupported = null;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.detectSupport().then(function (isSupported) {\n      if (isSupported) return parser.loadTextureImage(textureIndex, extension.source, loader);\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(name) >= 0) {\n        throw new Error('THREE.GLTFLoader: AVIF required by asset but unsupported.');\n      }\n\n      // Fall back to PNG or JPEG.\n      return parser.loadTexture(textureIndex);\n    });\n  }\n  detectSupport() {\n    if (!this.isSupported) {\n      this.isSupported = new Promise(function (resolve) {\n        const image = new Image();\n\n        // Lossy test image.\n        image.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI=';\n        image.onload = image.onerror = function () {\n          resolve(image.height === 1);\n        };\n      });\n    }\n    return this.isSupported;\n  }\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n */\nclass GLTFMeshoptCompression {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;\n    this.parser = parser;\n  }\n  loadBufferView(index) {\n    const json = this.parser.json;\n    const bufferView = json.bufferViews[index];\n    if (bufferView.extensions && bufferView.extensions[this.name]) {\n      const extensionDef = bufferView.extensions[this.name];\n      const buffer = this.parser.getDependency('buffer', extensionDef.buffer);\n      const decoder = this.parser.options.meshoptDecoder;\n      if (!decoder || !decoder.supported) {\n        if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n          throw new Error('THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files');\n        } else {\n          // Assumes that the extension is optional and that fallback buffer data is present\n          return null;\n        }\n      }\n      return buffer.then(function (res) {\n        const byteOffset = extensionDef.byteOffset || 0;\n        const byteLength = extensionDef.byteLength || 0;\n        const count = extensionDef.count;\n        const stride = extensionDef.byteStride;\n        const source = new Uint8Array(res, byteOffset, byteLength);\n        if (decoder.decodeGltfBufferAsync) {\n          return decoder.decodeGltfBufferAsync(count, stride, source, extensionDef.mode, extensionDef.filter).then(function (res) {\n            return res.buffer;\n          });\n        } else {\n          // Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n          return decoder.ready.then(function () {\n            const result = new ArrayBuffer(count * stride);\n            decoder.decodeGltfBuffer(new Uint8Array(result), count, stride, source, extensionDef.mode, extensionDef.filter);\n            return result;\n          });\n        }\n      });\n    } else {\n      return null;\n    }\n  }\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n */\nclass GLTFMeshGpuInstancing {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING;\n    this.parser = parser;\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    if (!nodeDef.extensions || !nodeDef.extensions[this.name] || nodeDef.mesh === undefined) {\n      return null;\n    }\n    const meshDef = json.meshes[nodeDef.mesh];\n\n    // No Points or Lines + Instancing support yet\n\n    for (const primitive of meshDef.primitives) {\n      if (primitive.mode !== WEBGL_CONSTANTS.TRIANGLES && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN && primitive.mode !== undefined) {\n        return null;\n      }\n    }\n    const extensionDef = nodeDef.extensions[this.name];\n    const attributesDef = extensionDef.attributes;\n\n    // @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n    const pending = [];\n    const attributes = {};\n    for (const key in attributesDef) {\n      pending.push(this.parser.getDependency('accessor', attributesDef[key]).then(accessor => {\n        attributes[key] = accessor;\n        return attributes[key];\n      }));\n    }\n    if (pending.length < 1) {\n      return null;\n    }\n    pending.push(this.parser.createNodeMesh(nodeIndex));\n    return Promise.all(pending).then(results => {\n      const nodeObject = results.pop();\n      const meshes = nodeObject.isGroup ? nodeObject.children : [nodeObject];\n      const count = results[0].count; // All attribute counts should be same\n      const instancedMeshes = [];\n      for (const mesh of meshes) {\n        // Temporal variables\n        const m = new Matrix4();\n        const p = new Vector3();\n        const q = new Quaternion();\n        const s = new Vector3(1, 1, 1);\n        const instancedMesh = new InstancedMesh(mesh.geometry, mesh.material, count);\n        for (let i = 0; i < count; i++) {\n          if (attributes.TRANSLATION) {\n            p.fromBufferAttribute(attributes.TRANSLATION, i);\n          }\n          if (attributes.ROTATION) {\n            q.fromBufferAttribute(attributes.ROTATION, i);\n          }\n          if (attributes.SCALE) {\n            s.fromBufferAttribute(attributes.SCALE, i);\n          }\n          instancedMesh.setMatrixAt(i, m.compose(p, q, s));\n        }\n\n        // Add instance attributes to the geometry, excluding TRS.\n        for (const attributeName in attributes) {\n          if (attributeName !== 'TRANSLATION' && attributeName !== 'ROTATION' && attributeName !== 'SCALE') {\n            mesh.geometry.setAttribute(attributeName, attributes[attributeName]);\n          }\n        }\n\n        // Just in case\n        Object3D.prototype.copy.call(instancedMesh, mesh);\n        this.parser.assignFinalMaterial(instancedMesh);\n        instancedMeshes.push(instancedMesh);\n      }\n      if (nodeObject.isGroup) {\n        nodeObject.clear();\n        nodeObject.add(...instancedMeshes);\n        return nodeObject;\n      }\n      return instancedMeshes[0];\n    });\n  }\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF';\nconst BINARY_EXTENSION_HEADER_LENGTH = 12;\nconst BINARY_EXTENSION_CHUNK_TYPES = {\n  JSON: 0x4E4F534A,\n  BIN: 0x004E4942\n};\nclass GLTFBinaryExtension {\n  constructor(data) {\n    this.name = EXTENSIONS.KHR_BINARY_GLTF;\n    this.content = null;\n    this.body = null;\n    const headerView = new DataView(data, 0, BINARY_EXTENSION_HEADER_LENGTH);\n    const textDecoder = new TextDecoder();\n    this.header = {\n      magic: textDecoder.decode(new Uint8Array(data.slice(0, 4))),\n      version: headerView.getUint32(4, true),\n      length: headerView.getUint32(8, true)\n    };\n    if (this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC) {\n      throw new Error('THREE.GLTFLoader: Unsupported glTF-Binary header.');\n    } else if (this.header.version < 2.0) {\n      throw new Error('THREE.GLTFLoader: Legacy binary file detected.');\n    }\n    const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;\n    const chunkView = new DataView(data, BINARY_EXTENSION_HEADER_LENGTH);\n    let chunkIndex = 0;\n    while (chunkIndex < chunkContentsLength) {\n      const chunkLength = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      const chunkType = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON) {\n        const contentArray = new Uint8Array(data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength);\n        this.content = textDecoder.decode(contentArray);\n      } else if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN) {\n        const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;\n        this.body = data.slice(byteOffset, byteOffset + chunkLength);\n      }\n\n      // Clients must ignore chunks with unknown types.\n\n      chunkIndex += chunkLength;\n    }\n    if (this.content === null) {\n      throw new Error('THREE.GLTFLoader: JSON content not found.');\n    }\n  }\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n */\nclass GLTFDracoMeshCompressionExtension {\n  constructor(json, dracoLoader) {\n    if (!dracoLoader) {\n      throw new Error('THREE.GLTFLoader: No DRACOLoader instance provided.');\n    }\n    this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;\n    this.json = json;\n    this.dracoLoader = dracoLoader;\n    this.dracoLoader.preload();\n  }\n  decodePrimitive(primitive, parser) {\n    const json = this.json;\n    const dracoLoader = this.dracoLoader;\n    const bufferViewIndex = primitive.extensions[this.name].bufferView;\n    const gltfAttributeMap = primitive.extensions[this.name].attributes;\n    const threeAttributeMap = {};\n    const attributeNormalizedMap = {};\n    const attributeTypeMap = {};\n    for (const attributeName in gltfAttributeMap) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      threeAttributeMap[threeAttributeName] = gltfAttributeMap[attributeName];\n    }\n    for (const attributeName in primitive.attributes) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      if (gltfAttributeMap[attributeName] !== undefined) {\n        const accessorDef = json.accessors[primitive.attributes[attributeName]];\n        const componentType = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n        attributeTypeMap[threeAttributeName] = componentType.name;\n        attributeNormalizedMap[threeAttributeName] = accessorDef.normalized === true;\n      }\n    }\n    return parser.getDependency('bufferView', bufferViewIndex).then(function (bufferView) {\n      return new Promise(function (resolve) {\n        dracoLoader.decodeDracoFile(bufferView, function (geometry) {\n          for (const attributeName in geometry.attributes) {\n            const attribute = geometry.attributes[attributeName];\n            const normalized = attributeNormalizedMap[attributeName];\n            if (normalized !== undefined) attribute.normalized = normalized;\n          }\n          resolve(geometry);\n        }, threeAttributeMap, attributeTypeMap);\n      });\n    });\n  }\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n */\nclass GLTFTextureTransformExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;\n  }\n  extendTexture(texture, transform) {\n    if ((transform.texCoord === undefined || transform.texCoord === texture.channel) && transform.offset === undefined && transform.rotation === undefined && transform.scale === undefined) {\n      // See https://github.com/mrdoob/three.js/issues/21819.\n      return texture;\n    }\n    texture = texture.clone();\n    if (transform.texCoord !== undefined) {\n      texture.channel = transform.texCoord;\n    }\n    if (transform.offset !== undefined) {\n      texture.offset.fromArray(transform.offset);\n    }\n    if (transform.rotation !== undefined) {\n      texture.rotation = transform.rotation;\n    }\n    if (transform.scale !== undefined) {\n      texture.repeat.fromArray(transform.scale);\n    }\n    texture.needsUpdate = true;\n    return texture;\n  }\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n */\nclass GLTFMeshQuantizationExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MESH_QUANTIZATION;\n  }\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer);\n  }\n  copySampleValue_(index) {\n    // Copies a sample value to the result buffer. See description of glTF\n    // CUBICSPLINE values layout in interpolate_() function below.\n\n    const result = this.resultBuffer,\n      values = this.sampleValues,\n      valueSize = this.valueSize,\n      offset = index * valueSize * 3 + valueSize;\n    for (let i = 0; i !== valueSize; i++) {\n      result[i] = values[offset + i];\n    }\n    return result;\n  }\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer;\n    const values = this.sampleValues;\n    const stride = this.valueSize;\n    const stride2 = stride * 2;\n    const stride3 = stride * 3;\n    const td = t1 - t0;\n    const p = (t - t0) / td;\n    const pp = p * p;\n    const ppp = pp * p;\n    const offset1 = i1 * stride3;\n    const offset0 = offset1 - stride3;\n    const s2 = -2 * ppp + 3 * pp;\n    const s3 = ppp - pp;\n    const s0 = 1 - s2;\n    const s1 = s3 - pp + p;\n\n    // Layout of keyframe output values for CUBICSPLINE animations:\n    //   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n    for (let i = 0; i !== stride; i++) {\n      const p0 = values[offset0 + i + stride]; // splineVertex_k\n      const m0 = values[offset0 + i + stride2] * td; // outTangent_k * (t_k+1 - t_k)\n      const p1 = values[offset1 + i + stride]; // splineVertex_k+1\n      const m1 = values[offset1 + i] * td; // inTangent_k+1 * (t_k+1 - t_k)\n\n      result[i] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;\n    }\n    return result;\n  }\n}\nconst _q = new Quaternion();\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n  interpolate_(i1, t0, t, t1) {\n    const result = super.interpolate_(i1, t0, t, t1);\n    _q.fromArray(result).normalize().toArray(result);\n    return result;\n  }\n}\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n  FLOAT: 5126,\n  //FLOAT_MAT2: 35674,\n  FLOAT_MAT3: 35675,\n  FLOAT_MAT4: 35676,\n  FLOAT_VEC2: 35664,\n  FLOAT_VEC3: 35665,\n  FLOAT_VEC4: 35666,\n  LINEAR: 9729,\n  REPEAT: 10497,\n  SAMPLER_2D: 35678,\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  UNSIGNED_BYTE: 5121,\n  UNSIGNED_SHORT: 5123\n};\nconst WEBGL_COMPONENT_TYPES = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array\n};\nconst WEBGL_FILTERS = {\n  9728: NearestFilter,\n  9729: LinearFilter,\n  9984: NearestMipmapNearestFilter,\n  9985: LinearMipmapNearestFilter,\n  9986: NearestMipmapLinearFilter,\n  9987: LinearMipmapLinearFilter\n};\nconst WEBGL_WRAPPINGS = {\n  33071: ClampToEdgeWrapping,\n  33648: MirroredRepeatWrapping,\n  10497: RepeatWrapping\n};\nconst WEBGL_TYPE_SIZES = {\n  'SCALAR': 1,\n  'VEC2': 2,\n  'VEC3': 3,\n  'VEC4': 4,\n  'MAT2': 4,\n  'MAT3': 9,\n  'MAT4': 16\n};\nconst ATTRIBUTES = {\n  POSITION: 'position',\n  NORMAL: 'normal',\n  TANGENT: 'tangent',\n  TEXCOORD_0: 'uv',\n  TEXCOORD_1: 'uv1',\n  TEXCOORD_2: 'uv2',\n  TEXCOORD_3: 'uv3',\n  COLOR_0: 'color',\n  WEIGHTS_0: 'skinWeight',\n  JOINTS_0: 'skinIndex'\n};\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  translation: 'position',\n  rotation: 'quaternion',\n  weights: 'morphTargetInfluences'\n};\nconst INTERPOLATION = {\n  CUBICSPLINE: undefined,\n  // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n  // keyframe track will be initialized with a default interpolation type, then modified.\n  LINEAR: InterpolateLinear,\n  STEP: InterpolateDiscrete\n};\nconst ALPHA_MODES = {\n  OPAQUE: 'OPAQUE',\n  MASK: 'MASK',\n  BLEND: 'BLEND'\n};\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n */\nfunction createDefaultMaterial(cache) {\n  if (cache['DefaultMaterial'] === undefined) {\n    cache['DefaultMaterial'] = new MeshStandardMaterial({\n      color: 0xFFFFFF,\n      emissive: 0x000000,\n      metalness: 1,\n      roughness: 1,\n      transparent: false,\n      depthTest: true,\n      side: FrontSide\n    });\n  }\n  return cache['DefaultMaterial'];\n}\nfunction addUnknownExtensionsToUserData(knownExtensions, object, objectDef) {\n  // Add unknown glTF extensions to an object's userData.\n\n  for (const name in objectDef.extensions) {\n    if (knownExtensions[name] === undefined) {\n      object.userData.gltfExtensions = object.userData.gltfExtensions || {};\n      object.userData.gltfExtensions[name] = objectDef.extensions[name];\n    }\n  }\n}\n\n/**\n * @param {Object3D|Material|BufferGeometry} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData(object, gltfDef) {\n  if (gltfDef.extras !== undefined) {\n    if (typeof gltfDef.extras === 'object') {\n      Object.assign(object.userData, gltfDef.extras);\n    } else {\n      console.warn('THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras);\n    }\n  }\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets(geometry, targets, parser) {\n  let hasMorphPosition = false;\n  let hasMorphNormal = false;\n  let hasMorphColor = false;\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (target.POSITION !== undefined) hasMorphPosition = true;\n    if (target.NORMAL !== undefined) hasMorphNormal = true;\n    if (target.COLOR_0 !== undefined) hasMorphColor = true;\n    if (hasMorphPosition && hasMorphNormal && hasMorphColor) break;\n  }\n  if (!hasMorphPosition && !hasMorphNormal && !hasMorphColor) return Promise.resolve(geometry);\n  const pendingPositionAccessors = [];\n  const pendingNormalAccessors = [];\n  const pendingColorAccessors = [];\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (hasMorphPosition) {\n      const pendingAccessor = target.POSITION !== undefined ? parser.getDependency('accessor', target.POSITION) : geometry.attributes.position;\n      pendingPositionAccessors.push(pendingAccessor);\n    }\n    if (hasMorphNormal) {\n      const pendingAccessor = target.NORMAL !== undefined ? parser.getDependency('accessor', target.NORMAL) : geometry.attributes.normal;\n      pendingNormalAccessors.push(pendingAccessor);\n    }\n    if (hasMorphColor) {\n      const pendingAccessor = target.COLOR_0 !== undefined ? parser.getDependency('accessor', target.COLOR_0) : geometry.attributes.color;\n      pendingColorAccessors.push(pendingAccessor);\n    }\n  }\n  return Promise.all([Promise.all(pendingPositionAccessors), Promise.all(pendingNormalAccessors), Promise.all(pendingColorAccessors)]).then(function (accessors) {\n    const morphPositions = accessors[0];\n    const morphNormals = accessors[1];\n    const morphColors = accessors[2];\n    if (hasMorphPosition) geometry.morphAttributes.position = morphPositions;\n    if (hasMorphNormal) geometry.morphAttributes.normal = morphNormals;\n    if (hasMorphColor) geometry.morphAttributes.color = morphColors;\n    geometry.morphTargetsRelative = true;\n    return geometry;\n  });\n}\n\n/**\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets(mesh, meshDef) {\n  mesh.updateMorphTargets();\n  if (meshDef.weights !== undefined) {\n    for (let i = 0, il = meshDef.weights.length; i < il; i++) {\n      mesh.morphTargetInfluences[i] = meshDef.weights[i];\n    }\n  }\n\n  // .extras has user-defined data, so check that .extras.targetNames is an array.\n  if (meshDef.extras && Array.isArray(meshDef.extras.targetNames)) {\n    const targetNames = meshDef.extras.targetNames;\n    if (mesh.morphTargetInfluences.length === targetNames.length) {\n      mesh.morphTargetDictionary = {};\n      for (let i = 0, il = targetNames.length; i < il; i++) {\n        mesh.morphTargetDictionary[targetNames[i]] = i;\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.');\n    }\n  }\n}\nfunction createPrimitiveKey(primitiveDef) {\n  let geometryKey;\n  const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION];\n  if (dracoExtension) {\n    geometryKey = 'draco:' + dracoExtension.bufferView + ':' + dracoExtension.indices + ':' + createAttributesKey(dracoExtension.attributes);\n  } else {\n    geometryKey = primitiveDef.indices + ':' + createAttributesKey(primitiveDef.attributes) + ':' + primitiveDef.mode;\n  }\n  if (primitiveDef.targets !== undefined) {\n    for (let i = 0, il = primitiveDef.targets.length; i < il; i++) {\n      geometryKey += ':' + createAttributesKey(primitiveDef.targets[i]);\n    }\n  }\n  return geometryKey;\n}\nfunction createAttributesKey(attributes) {\n  let attributesKey = '';\n  const keys = Object.keys(attributes).sort();\n  for (let i = 0, il = keys.length; i < il; i++) {\n    attributesKey += keys[i] + ':' + attributes[keys[i]] + ';';\n  }\n  return attributesKey;\n}\nfunction getNormalizedComponentScale(constructor) {\n  // Reference:\n  // https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n  switch (constructor) {\n    case Int8Array:\n      return 1 / 127;\n    case Uint8Array:\n      return 1 / 255;\n    case Int16Array:\n      return 1 / 32767;\n    case Uint16Array:\n      return 1 / 65535;\n    default:\n      throw new Error('THREE.GLTFLoader: Unsupported normalized accessor component type.');\n  }\n}\nfunction getImageURIMimeType(uri) {\n  if (uri.search(/\\.jpe?g($|\\?)/i) > 0 || uri.search(/^data\\:image\\/jpeg/) === 0) return 'image/jpeg';\n  if (uri.search(/\\.webp($|\\?)/i) > 0 || uri.search(/^data\\:image\\/webp/) === 0) return 'image/webp';\n  return 'image/png';\n}\nconst _identityMatrix = new Matrix4();\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n  constructor(json = {}, options = {}) {\n    this.json = json;\n    this.extensions = {};\n    this.plugins = {};\n    this.options = options;\n\n    // loader object cache\n    this.cache = new GLTFRegistry();\n\n    // associations between Three.js objects and glTF elements\n    this.associations = new Map();\n\n    // BufferGeometry caching\n    this.primitiveCache = {};\n\n    // Node cache\n    this.nodeCache = {};\n\n    // Object3D instance caches\n    this.meshCache = {\n      refs: {},\n      uses: {}\n    };\n    this.cameraCache = {\n      refs: {},\n      uses: {}\n    };\n    this.lightCache = {\n      refs: {},\n      uses: {}\n    };\n    this.sourceCache = {};\n    this.textureCache = {};\n\n    // Track node names, to ensure no duplicates\n    this.nodeNamesUsed = {};\n\n    // Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n    // expensive work of uploading a texture to the GPU off the main thread.\n\n    let isSafari = false;\n    let isFirefox = false;\n    let firefoxVersion = -1;\n    if (typeof navigator !== 'undefined') {\n      isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) === true;\n      isFirefox = navigator.userAgent.indexOf('Firefox') > -1;\n      firefoxVersion = isFirefox ? navigator.userAgent.match(/Firefox\\/([0-9]+)\\./)[1] : -1;\n    }\n    if (typeof createImageBitmap === 'undefined' || isSafari || isFirefox && firefoxVersion < 98) {\n      this.textureLoader = new TextureLoader(this.options.manager);\n    } else {\n      this.textureLoader = new ImageBitmapLoader(this.options.manager);\n    }\n    this.textureLoader.setCrossOrigin(this.options.crossOrigin);\n    this.textureLoader.setRequestHeader(this.options.requestHeader);\n    this.fileLoader = new FileLoader(this.options.manager);\n    this.fileLoader.setResponseType('arraybuffer');\n    if (this.options.crossOrigin === 'use-credentials') {\n      this.fileLoader.setWithCredentials(true);\n    }\n  }\n  setExtensions(extensions) {\n    this.extensions = extensions;\n  }\n  setPlugins(plugins) {\n    this.plugins = plugins;\n  }\n  parse(onLoad, onError) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n\n    // Clear the loader cache\n    this.cache.removeAll();\n    this.nodeCache = {};\n\n    // Mark the special nodes/meshes in json for efficient parse\n    this._invokeAll(function (ext) {\n      return ext._markDefs && ext._markDefs();\n    });\n    Promise.all(this._invokeAll(function (ext) {\n      return ext.beforeRoot && ext.beforeRoot();\n    })).then(function () {\n      return Promise.all([parser.getDependencies('scene'), parser.getDependencies('animation'), parser.getDependencies('camera')]);\n    }).then(function (dependencies) {\n      const result = {\n        scene: dependencies[0][json.scene || 0],\n        scenes: dependencies[0],\n        animations: dependencies[1],\n        cameras: dependencies[2],\n        asset: json.asset,\n        parser: parser,\n        userData: {}\n      };\n      addUnknownExtensionsToUserData(extensions, result, json);\n      assignExtrasToUserData(result, json);\n      Promise.all(parser._invokeAll(function (ext) {\n        return ext.afterRoot && ext.afterRoot(result);\n      })).then(function () {\n        onLoad(result);\n      });\n    }).catch(onError);\n  }\n\n  /**\n   * Marks the special nodes/meshes in json for efficient parse.\n   */\n  _markDefs() {\n    const nodeDefs = this.json.nodes || [];\n    const skinDefs = this.json.skins || [];\n    const meshDefs = this.json.meshes || [];\n\n    // Nothing in the node definition indicates whether it is a Bone or an\n    // Object3D. Use the skins' joint references to mark bones.\n    for (let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex++) {\n      const joints = skinDefs[skinIndex].joints;\n      for (let i = 0, il = joints.length; i < il; i++) {\n        nodeDefs[joints[i]].isBone = true;\n      }\n    }\n\n    // Iterate over all nodes, marking references to shared resources,\n    // as well as skeleton joints.\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.mesh !== undefined) {\n        this._addNodeRef(this.meshCache, nodeDef.mesh);\n\n        // Nothing in the mesh definition indicates whether it is\n        // a SkinnedMesh or Mesh. Use the node's mesh reference\n        // to mark SkinnedMesh if node has skin.\n        if (nodeDef.skin !== undefined) {\n          meshDefs[nodeDef.mesh].isSkinnedMesh = true;\n        }\n      }\n      if (nodeDef.camera !== undefined) {\n        this._addNodeRef(this.cameraCache, nodeDef.camera);\n      }\n    }\n  }\n\n  /**\n   * Counts references to shared node / Object3D resources. These resources\n   * can be reused, or \"instantiated\", at multiple nodes in the scene\n   * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n   * be marked. Non-scenegraph resources (like Materials, Geometries, and\n   * Textures) can be reused directly and are not marked here.\n   *\n   * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n   */\n  _addNodeRef(cache, index) {\n    if (index === undefined) return;\n    if (cache.refs[index] === undefined) {\n      cache.refs[index] = cache.uses[index] = 0;\n    }\n    cache.refs[index]++;\n  }\n\n  /** Returns a reference to a shared resource, cloning it if necessary. */\n  _getNodeRef(cache, index, object) {\n    if (cache.refs[index] <= 1) return object;\n    const ref = object.clone();\n\n    // Propagates mappings to the cloned object, prevents mappings on the\n    // original object from being lost.\n    const updateMappings = (original, clone) => {\n      const mappings = this.associations.get(original);\n      if (mappings != null) {\n        this.associations.set(clone, mappings);\n      }\n      for (const [i, child] of original.children.entries()) {\n        updateMappings(child, clone.children[i]);\n      }\n    };\n    updateMappings(object, ref);\n    ref.name += '_instance_' + cache.uses[index]++;\n    return ref;\n  }\n  _invokeOne(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.push(this);\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) return result;\n    }\n    return null;\n  }\n  _invokeAll(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.unshift(this);\n    const pending = [];\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) pending.push(result);\n    }\n    return pending;\n  }\n\n  /**\n   * Requests the specified dependency asynchronously, with caching.\n   * @param {string} type\n   * @param {number} index\n   * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n   */\n  getDependency(type, index) {\n    const cacheKey = type + ':' + index;\n    let dependency = this.cache.get(cacheKey);\n    if (!dependency) {\n      switch (type) {\n        case 'scene':\n          dependency = this.loadScene(index);\n          break;\n        case 'node':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadNode && ext.loadNode(index);\n          });\n          break;\n        case 'mesh':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMesh && ext.loadMesh(index);\n          });\n          break;\n        case 'accessor':\n          dependency = this.loadAccessor(index);\n          break;\n        case 'bufferView':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadBufferView && ext.loadBufferView(index);\n          });\n          break;\n        case 'buffer':\n          dependency = this.loadBuffer(index);\n          break;\n        case 'material':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMaterial && ext.loadMaterial(index);\n          });\n          break;\n        case 'texture':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadTexture && ext.loadTexture(index);\n          });\n          break;\n        case 'skin':\n          dependency = this.loadSkin(index);\n          break;\n        case 'animation':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadAnimation && ext.loadAnimation(index);\n          });\n          break;\n        case 'camera':\n          dependency = this.loadCamera(index);\n          break;\n        default:\n          dependency = this._invokeOne(function (ext) {\n            return ext != this && ext.getDependency && ext.getDependency(type, index);\n          });\n          if (!dependency) {\n            throw new Error('Unknown type: ' + type);\n          }\n          break;\n      }\n      this.cache.add(cacheKey, dependency);\n    }\n    return dependency;\n  }\n\n  /**\n   * Requests all dependencies of the specified type asynchronously, with caching.\n   * @param {string} type\n   * @return {Promise<Array<Object>>}\n   */\n  getDependencies(type) {\n    let dependencies = this.cache.get(type);\n    if (!dependencies) {\n      const parser = this;\n      const defs = this.json[type + (type === 'mesh' ? 'es' : 's')] || [];\n      dependencies = Promise.all(defs.map(function (def, index) {\n        return parser.getDependency(type, index);\n      }));\n      this.cache.add(type, dependencies);\n    }\n    return dependencies;\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBuffer(bufferIndex) {\n    const bufferDef = this.json.buffers[bufferIndex];\n    const loader = this.fileLoader;\n    if (bufferDef.type && bufferDef.type !== 'arraybuffer') {\n      throw new Error('THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.');\n    }\n\n    // If present, GLB container is required to be the first buffer.\n    if (bufferDef.uri === undefined && bufferIndex === 0) {\n      return Promise.resolve(this.extensions[EXTENSIONS.KHR_BINARY_GLTF].body);\n    }\n    const options = this.options;\n    return new Promise(function (resolve, reject) {\n      loader.load(LoaderUtils.resolveURL(bufferDef.uri, options.path), resolve, undefined, function () {\n        reject(new Error('THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".'));\n      });\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   * @param {number} bufferViewIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBufferView(bufferViewIndex) {\n    const bufferViewDef = this.json.bufferViews[bufferViewIndex];\n    return this.getDependency('buffer', bufferViewDef.buffer).then(function (buffer) {\n      const byteLength = bufferViewDef.byteLength || 0;\n      const byteOffset = bufferViewDef.byteOffset || 0;\n      return buffer.slice(byteOffset, byteOffset + byteLength);\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n   * @param {number} accessorIndex\n   * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n   */\n  loadAccessor(accessorIndex) {\n    const parser = this;\n    const json = this.json;\n    const accessorDef = this.json.accessors[accessorIndex];\n    if (accessorDef.bufferView === undefined && accessorDef.sparse === undefined) {\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n      const normalized = accessorDef.normalized === true;\n      const array = new TypedArray(accessorDef.count * itemSize);\n      return Promise.resolve(new BufferAttribute(array, itemSize, normalized));\n    }\n    const pendingBufferViews = [];\n    if (accessorDef.bufferView !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.bufferView));\n    } else {\n      pendingBufferViews.push(null);\n    }\n    if (accessorDef.sparse !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.indices.bufferView));\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.values.bufferView));\n    }\n    return Promise.all(pendingBufferViews).then(function (bufferViews) {\n      const bufferView = bufferViews[0];\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n\n      // For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n      const elementBytes = TypedArray.BYTES_PER_ELEMENT;\n      const itemBytes = elementBytes * itemSize;\n      const byteOffset = accessorDef.byteOffset || 0;\n      const byteStride = accessorDef.bufferView !== undefined ? json.bufferViews[accessorDef.bufferView].byteStride : undefined;\n      const normalized = accessorDef.normalized === true;\n      let array, bufferAttribute;\n\n      // The buffer is not interleaved if the stride is the item size in bytes.\n      if (byteStride && byteStride !== itemBytes) {\n        // Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n        // This makes sure that IBA.count reflects accessor.count properly\n        const ibSlice = Math.floor(byteOffset / byteStride);\n        const ibCacheKey = 'InterleavedBuffer:' + accessorDef.bufferView + ':' + accessorDef.componentType + ':' + ibSlice + ':' + accessorDef.count;\n        let ib = parser.cache.get(ibCacheKey);\n        if (!ib) {\n          array = new TypedArray(bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes);\n\n          // Integer parameters to IB/IBA are in array elements, not bytes.\n          ib = new InterleavedBuffer(array, byteStride / elementBytes);\n          parser.cache.add(ibCacheKey, ib);\n        }\n        bufferAttribute = new InterleavedBufferAttribute(ib, itemSize, byteOffset % byteStride / elementBytes, normalized);\n      } else {\n        if (bufferView === null) {\n          array = new TypedArray(accessorDef.count * itemSize);\n        } else {\n          array = new TypedArray(bufferView, byteOffset, accessorDef.count * itemSize);\n        }\n        bufferAttribute = new BufferAttribute(array, itemSize, normalized);\n      }\n\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n      if (accessorDef.sparse !== undefined) {\n        const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;\n        const TypedArrayIndices = WEBGL_COMPONENT_TYPES[accessorDef.sparse.indices.componentType];\n        const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;\n        const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;\n        const sparseIndices = new TypedArrayIndices(bufferViews[1], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices);\n        const sparseValues = new TypedArray(bufferViews[2], byteOffsetValues, accessorDef.sparse.count * itemSize);\n        if (bufferView !== null) {\n          // Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n          bufferAttribute = new BufferAttribute(bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized);\n        }\n        for (let i = 0, il = sparseIndices.length; i < il; i++) {\n          const index = sparseIndices[i];\n          bufferAttribute.setX(index, sparseValues[i * itemSize]);\n          if (itemSize >= 2) bufferAttribute.setY(index, sparseValues[i * itemSize + 1]);\n          if (itemSize >= 3) bufferAttribute.setZ(index, sparseValues[i * itemSize + 2]);\n          if (itemSize >= 4) bufferAttribute.setW(index, sparseValues[i * itemSize + 3]);\n          if (itemSize >= 5) throw new Error('THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.');\n        }\n      }\n      return bufferAttribute;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n   * @param {number} textureIndex\n   * @return {Promise<THREE.Texture|null>}\n   */\n  loadTexture(textureIndex) {\n    const json = this.json;\n    const options = this.options;\n    const textureDef = json.textures[textureIndex];\n    const sourceIndex = textureDef.source;\n    const sourceDef = json.images[sourceIndex];\n    let loader = this.textureLoader;\n    if (sourceDef.uri) {\n      const handler = options.manager.getHandler(sourceDef.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.loadTextureImage(textureIndex, sourceIndex, loader);\n  }\n  loadTextureImage(textureIndex, sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const textureDef = json.textures[textureIndex];\n    const sourceDef = json.images[sourceIndex];\n    const cacheKey = (sourceDef.uri || sourceDef.bufferView) + ':' + textureDef.sampler;\n    if (this.textureCache[cacheKey]) {\n      // See https://github.com/mrdoob/three.js/issues/21559.\n      return this.textureCache[cacheKey];\n    }\n    const promise = this.loadImageSource(sourceIndex, loader).then(function (texture) {\n      texture.flipY = false;\n      texture.name = textureDef.name || sourceDef.name || '';\n      if (texture.name === '' && typeof sourceDef.uri === 'string' && sourceDef.uri.startsWith('data:image/') === false) {\n        texture.name = sourceDef.uri;\n      }\n      const samplers = json.samplers || {};\n      const sampler = samplers[textureDef.sampler] || {};\n      texture.magFilter = WEBGL_FILTERS[sampler.magFilter] || LinearFilter;\n      texture.minFilter = WEBGL_FILTERS[sampler.minFilter] || LinearMipmapLinearFilter;\n      texture.wrapS = WEBGL_WRAPPINGS[sampler.wrapS] || RepeatWrapping;\n      texture.wrapT = WEBGL_WRAPPINGS[sampler.wrapT] || RepeatWrapping;\n      parser.associations.set(texture, {\n        textures: textureIndex\n      });\n      return texture;\n    }).catch(function () {\n      return null;\n    });\n    this.textureCache[cacheKey] = promise;\n    return promise;\n  }\n  loadImageSource(sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const options = this.options;\n    if (this.sourceCache[sourceIndex] !== undefined) {\n      return this.sourceCache[sourceIndex].then(texture => texture.clone());\n    }\n    const sourceDef = json.images[sourceIndex];\n    const URL = self.URL || self.webkitURL;\n    let sourceURI = sourceDef.uri || '';\n    let isObjectURL = false;\n    if (sourceDef.bufferView !== undefined) {\n      // Load binary image data from bufferView, if provided.\n\n      sourceURI = parser.getDependency('bufferView', sourceDef.bufferView).then(function (bufferView) {\n        isObjectURL = true;\n        const blob = new Blob([bufferView], {\n          type: sourceDef.mimeType\n        });\n        sourceURI = URL.createObjectURL(blob);\n        return sourceURI;\n      });\n    } else if (sourceDef.uri === undefined) {\n      throw new Error('THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView');\n    }\n    const promise = Promise.resolve(sourceURI).then(function (sourceURI) {\n      return new Promise(function (resolve, reject) {\n        let onLoad = resolve;\n        if (loader.isImageBitmapLoader === true) {\n          onLoad = function (imageBitmap) {\n            const texture = new Texture(imageBitmap);\n            texture.needsUpdate = true;\n            resolve(texture);\n          };\n        }\n        loader.load(LoaderUtils.resolveURL(sourceURI, options.path), onLoad, undefined, reject);\n      });\n    }).then(function (texture) {\n      // Clean up resources and configure Texture.\n\n      if (isObjectURL === true) {\n        URL.revokeObjectURL(sourceURI);\n      }\n      texture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType(sourceDef.uri);\n      return texture;\n    }).catch(function (error) {\n      console.error('THREE.GLTFLoader: Couldn\\'t load texture', sourceURI);\n      throw error;\n    });\n    this.sourceCache[sourceIndex] = promise;\n    return promise;\n  }\n\n  /**\n   * Asynchronously assigns a texture to the given material parameters.\n   * @param {Object} materialParams\n   * @param {string} mapName\n   * @param {Object} mapDef\n   * @return {Promise<Texture>}\n   */\n  assignTexture(materialParams, mapName, mapDef, colorSpace) {\n    const parser = this;\n    return this.getDependency('texture', mapDef.index).then(function (texture) {\n      if (!texture) return null;\n      if (mapDef.texCoord !== undefined && mapDef.texCoord > 0) {\n        texture = texture.clone();\n        texture.channel = mapDef.texCoord;\n      }\n      if (parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM]) {\n        const transform = mapDef.extensions !== undefined ? mapDef.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM] : undefined;\n        if (transform) {\n          const gltfReference = parser.associations.get(texture);\n          texture = parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM].extendTexture(texture, transform);\n          parser.associations.set(texture, gltfReference);\n        }\n      }\n      if (colorSpace !== undefined) {\n        texture.colorSpace = colorSpace;\n      }\n      materialParams[mapName] = texture;\n      return texture;\n    });\n  }\n\n  /**\n   * Assigns final material to a Mesh, Line, or Points instance. The instance\n   * already has a material (generated from the glTF material options alone)\n   * but reuse of the same glTF material may require multiple threejs materials\n   * to accommodate different primitive types, defines, etc. New materials will\n   * be created if necessary, and reused from a cache.\n   * @param  {Object3D} mesh Mesh, Line, or Points instance.\n   */\n  assignFinalMaterial(mesh) {\n    const geometry = mesh.geometry;\n    let material = mesh.material;\n    const useDerivativeTangents = geometry.attributes.tangent === undefined;\n    const useVertexColors = geometry.attributes.color !== undefined;\n    const useFlatShading = geometry.attributes.normal === undefined;\n    if (mesh.isPoints) {\n      const cacheKey = 'PointsMaterial:' + material.uuid;\n      let pointsMaterial = this.cache.get(cacheKey);\n      if (!pointsMaterial) {\n        pointsMaterial = new PointsMaterial();\n        Material.prototype.copy.call(pointsMaterial, material);\n        pointsMaterial.color.copy(material.color);\n        pointsMaterial.map = material.map;\n        pointsMaterial.sizeAttenuation = false; // glTF spec says points should be 1px\n\n        this.cache.add(cacheKey, pointsMaterial);\n      }\n      material = pointsMaterial;\n    } else if (mesh.isLine) {\n      const cacheKey = 'LineBasicMaterial:' + material.uuid;\n      let lineMaterial = this.cache.get(cacheKey);\n      if (!lineMaterial) {\n        lineMaterial = new LineBasicMaterial();\n        Material.prototype.copy.call(lineMaterial, material);\n        lineMaterial.color.copy(material.color);\n        lineMaterial.map = material.map;\n        this.cache.add(cacheKey, lineMaterial);\n      }\n      material = lineMaterial;\n    }\n\n    // Clone the material if it will be modified\n    if (useDerivativeTangents || useVertexColors || useFlatShading) {\n      let cacheKey = 'ClonedMaterial:' + material.uuid + ':';\n      if (useDerivativeTangents) cacheKey += 'derivative-tangents:';\n      if (useVertexColors) cacheKey += 'vertex-colors:';\n      if (useFlatShading) cacheKey += 'flat-shading:';\n      let cachedMaterial = this.cache.get(cacheKey);\n      if (!cachedMaterial) {\n        cachedMaterial = material.clone();\n        if (useVertexColors) cachedMaterial.vertexColors = true;\n        if (useFlatShading) cachedMaterial.flatShading = true;\n        if (useDerivativeTangents) {\n          // https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n          if (cachedMaterial.normalScale) cachedMaterial.normalScale.y *= -1;\n          if (cachedMaterial.clearcoatNormalScale) cachedMaterial.clearcoatNormalScale.y *= -1;\n        }\n        this.cache.add(cacheKey, cachedMaterial);\n        this.associations.set(cachedMaterial, this.associations.get(material));\n      }\n      material = cachedMaterial;\n    }\n    mesh.material = material;\n  }\n  getMaterialType(/* materialIndex */\n  ) {\n    return MeshStandardMaterial;\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n   * @param {number} materialIndex\n   * @return {Promise<Material>}\n   */\n  loadMaterial(materialIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const materialDef = json.materials[materialIndex];\n    let materialType;\n    const materialParams = {};\n    const materialExtensions = materialDef.extensions || {};\n    const pending = [];\n    if (materialExtensions[EXTENSIONS.KHR_MATERIALS_UNLIT]) {\n      const kmuExtension = extensions[EXTENSIONS.KHR_MATERIALS_UNLIT];\n      materialType = kmuExtension.getMaterialType();\n      pending.push(kmuExtension.extendParams(materialParams, materialDef, parser));\n    } else {\n      // Specification:\n      // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n      const metallicRoughness = materialDef.pbrMetallicRoughness || {};\n      materialParams.color = new Color(1.0, 1.0, 1.0);\n      materialParams.opacity = 1.0;\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.fromArray(array);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n      materialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0;\n      materialParams.roughness = metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0;\n      if (metallicRoughness.metallicRoughnessTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture));\n        pending.push(parser.assignTexture(materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture));\n      }\n      materialType = this._invokeOne(function (ext) {\n        return ext.getMaterialType && ext.getMaterialType(materialIndex);\n      });\n      pending.push(Promise.all(this._invokeAll(function (ext) {\n        return ext.extendMaterialParams && ext.extendMaterialParams(materialIndex, materialParams);\n      })));\n    }\n    if (materialDef.doubleSided === true) {\n      materialParams.side = DoubleSide;\n    }\n    const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;\n    if (alphaMode === ALPHA_MODES.BLEND) {\n      materialParams.transparent = true;\n\n      // See: https://github.com/mrdoob/three.js/issues/17706\n      materialParams.depthWrite = false;\n    } else {\n      materialParams.transparent = false;\n      if (alphaMode === ALPHA_MODES.MASK) {\n        materialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5;\n      }\n    }\n    if (materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'normalMap', materialDef.normalTexture));\n      materialParams.normalScale = new Vector2(1, 1);\n      if (materialDef.normalTexture.scale !== undefined) {\n        const scale = materialDef.normalTexture.scale;\n        materialParams.normalScale.set(scale, scale);\n      }\n    }\n    if (materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'aoMap', materialDef.occlusionTexture));\n      if (materialDef.occlusionTexture.strength !== undefined) {\n        materialParams.aoMapIntensity = materialDef.occlusionTexture.strength;\n      }\n    }\n    if (materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial) {\n      materialParams.emissive = new Color().fromArray(materialDef.emissiveFactor);\n    }\n    if (materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending).then(function () {\n      const material = new materialType(materialParams);\n      if (materialDef.name) material.name = materialDef.name;\n      assignExtrasToUserData(material, materialDef);\n      parser.associations.set(material, {\n        materials: materialIndex\n      });\n      if (materialDef.extensions) addUnknownExtensionsToUserData(extensions, material, materialDef);\n      return material;\n    });\n  }\n\n  /** When Object3D instances are targeted by animation, they need unique names. */\n  createUniqueName(originalName) {\n    const sanitizedName = PropertyBinding.sanitizeNodeName(originalName || '');\n    if (sanitizedName in this.nodeNamesUsed) {\n      return sanitizedName + '_' + ++this.nodeNamesUsed[sanitizedName];\n    } else {\n      this.nodeNamesUsed[sanitizedName] = 0;\n      return sanitizedName;\n    }\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n   *\n   * Creates BufferGeometries from primitives.\n   *\n   * @param {Array<GLTF.Primitive>} primitives\n   * @return {Promise<Array<BufferGeometry>>}\n   */\n  loadGeometries(primitives) {\n    const parser = this;\n    const extensions = this.extensions;\n    const cache = this.primitiveCache;\n    function createDracoPrimitive(primitive) {\n      return extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(primitive, parser).then(function (geometry) {\n        return addPrimitiveAttributes(geometry, primitive, parser);\n      });\n    }\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const primitive = primitives[i];\n      const cacheKey = createPrimitiveKey(primitive);\n\n      // See if we've already created this geometry\n      const cached = cache[cacheKey];\n      if (cached) {\n        // Use the cached geometry if it exists\n        pending.push(cached.promise);\n      } else {\n        let geometryPromise;\n        if (primitive.extensions && primitive.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]) {\n          // Use DRACO geometry if available\n          geometryPromise = createDracoPrimitive(primitive);\n        } else {\n          // Otherwise create a new geometry\n          geometryPromise = addPrimitiveAttributes(new BufferGeometry(), primitive, parser);\n        }\n\n        // Cache this geometry\n        cache[cacheKey] = {\n          primitive: primitive,\n          promise: geometryPromise\n        };\n        pending.push(geometryPromise);\n      }\n    }\n    return Promise.all(pending);\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n   * @param {number} meshIndex\n   * @return {Promise<Group|Mesh|SkinnedMesh>}\n   */\n  loadMesh(meshIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const meshDef = json.meshes[meshIndex];\n    const primitives = meshDef.primitives;\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const material = primitives[i].material === undefined ? createDefaultMaterial(this.cache) : this.getDependency('material', primitives[i].material);\n      pending.push(material);\n    }\n    pending.push(parser.loadGeometries(primitives));\n    return Promise.all(pending).then(function (results) {\n      const materials = results.slice(0, results.length - 1);\n      const geometries = results[results.length - 1];\n      const meshes = [];\n      for (let i = 0, il = geometries.length; i < il; i++) {\n        const geometry = geometries[i];\n        const primitive = primitives[i];\n\n        // 1. create Mesh\n\n        let mesh;\n        const material = materials[i];\n        if (primitive.mode === WEBGL_CONSTANTS.TRIANGLES || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN || primitive.mode === undefined) {\n          // .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n          mesh = meshDef.isSkinnedMesh === true ? new SkinnedMesh(geometry, material) : new Mesh(geometry, material);\n          if (mesh.isSkinnedMesh === true) {\n            // normalize skin weights to fix malformed assets (see #15319)\n            mesh.normalizeSkinWeights();\n          }\n          if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleStripDrawMode);\n          } else if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleFanDrawMode);\n          }\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINES) {\n          mesh = new LineSegments(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_STRIP) {\n          mesh = new Line(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_LOOP) {\n          mesh = new LineLoop(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.POINTS) {\n          mesh = new Points(geometry, material);\n        } else {\n          throw new Error('THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode);\n        }\n        if (Object.keys(mesh.geometry.morphAttributes).length > 0) {\n          updateMorphTargets(mesh, meshDef);\n        }\n        mesh.name = parser.createUniqueName(meshDef.name || 'mesh_' + meshIndex);\n        assignExtrasToUserData(mesh, meshDef);\n        if (primitive.extensions) addUnknownExtensionsToUserData(extensions, mesh, primitive);\n        parser.assignFinalMaterial(mesh);\n        meshes.push(mesh);\n      }\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        parser.associations.set(meshes[i], {\n          meshes: meshIndex,\n          primitives: i\n        });\n      }\n      if (meshes.length === 1) {\n        if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, meshes[0], meshDef);\n        return meshes[0];\n      }\n      const group = new Group();\n      if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, group, meshDef);\n      parser.associations.set(group, {\n        meshes: meshIndex\n      });\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        group.add(meshes[i]);\n      }\n      return group;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n   * @param {number} cameraIndex\n   * @return {Promise<THREE.Camera>}\n   */\n  loadCamera(cameraIndex) {\n    let camera;\n    const cameraDef = this.json.cameras[cameraIndex];\n    const params = cameraDef[cameraDef.type];\n    if (!params) {\n      console.warn('THREE.GLTFLoader: Missing camera parameters.');\n      return;\n    }\n    if (cameraDef.type === 'perspective') {\n      camera = new PerspectiveCamera(MathUtils.radToDeg(params.yfov), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6);\n    } else if (cameraDef.type === 'orthographic') {\n      camera = new OrthographicCamera(-params.xmag, params.xmag, params.ymag, -params.ymag, params.znear, params.zfar);\n    }\n    if (cameraDef.name) camera.name = this.createUniqueName(cameraDef.name);\n    assignExtrasToUserData(camera, cameraDef);\n    return Promise.resolve(camera);\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n   * @param {number} skinIndex\n   * @return {Promise<Skeleton>}\n   */\n  loadSkin(skinIndex) {\n    const skinDef = this.json.skins[skinIndex];\n    const pending = [];\n    for (let i = 0, il = skinDef.joints.length; i < il; i++) {\n      pending.push(this._loadNodeShallow(skinDef.joints[i]));\n    }\n    if (skinDef.inverseBindMatrices !== undefined) {\n      pending.push(this.getDependency('accessor', skinDef.inverseBindMatrices));\n    } else {\n      pending.push(null);\n    }\n    return Promise.all(pending).then(function (results) {\n      const inverseBindMatrices = results.pop();\n      const jointNodes = results;\n\n      // Note that bones (joint nodes) may or may not be in the\n      // scene graph at this time.\n\n      const bones = [];\n      const boneInverses = [];\n      for (let i = 0, il = jointNodes.length; i < il; i++) {\n        const jointNode = jointNodes[i];\n        if (jointNode) {\n          bones.push(jointNode);\n          const mat = new Matrix4();\n          if (inverseBindMatrices !== null) {\n            mat.fromArray(inverseBindMatrices.array, i * 16);\n          }\n          boneInverses.push(mat);\n        } else {\n          console.warn('THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[i]);\n        }\n      }\n      return new Skeleton(bones, boneInverses);\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n   * @param {number} animationIndex\n   * @return {Promise<AnimationClip>}\n   */\n  loadAnimation(animationIndex) {\n    const json = this.json;\n    const parser = this;\n    const animationDef = json.animations[animationIndex];\n    const animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex;\n    const pendingNodes = [];\n    const pendingInputAccessors = [];\n    const pendingOutputAccessors = [];\n    const pendingSamplers = [];\n    const pendingTargets = [];\n    for (let i = 0, il = animationDef.channels.length; i < il; i++) {\n      const channel = animationDef.channels[i];\n      const sampler = animationDef.samplers[channel.sampler];\n      const target = channel.target;\n      const name = target.node;\n      const input = animationDef.parameters !== undefined ? animationDef.parameters[sampler.input] : sampler.input;\n      const output = animationDef.parameters !== undefined ? animationDef.parameters[sampler.output] : sampler.output;\n      if (target.node === undefined) continue;\n      pendingNodes.push(this.getDependency('node', name));\n      pendingInputAccessors.push(this.getDependency('accessor', input));\n      pendingOutputAccessors.push(this.getDependency('accessor', output));\n      pendingSamplers.push(sampler);\n      pendingTargets.push(target);\n    }\n    return Promise.all([Promise.all(pendingNodes), Promise.all(pendingInputAccessors), Promise.all(pendingOutputAccessors), Promise.all(pendingSamplers), Promise.all(pendingTargets)]).then(function (dependencies) {\n      const nodes = dependencies[0];\n      const inputAccessors = dependencies[1];\n      const outputAccessors = dependencies[2];\n      const samplers = dependencies[3];\n      const targets = dependencies[4];\n      const tracks = [];\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        const node = nodes[i];\n        const inputAccessor = inputAccessors[i];\n        const outputAccessor = outputAccessors[i];\n        const sampler = samplers[i];\n        const target = targets[i];\n        if (node === undefined) continue;\n        if (node.updateMatrix) {\n          node.updateMatrix();\n          node.matrixAutoUpdate = true;\n        }\n        const createdTracks = parser._createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target);\n        if (createdTracks) {\n          for (let k = 0; k < createdTracks.length; k++) {\n            tracks.push(createdTracks[k]);\n          }\n        }\n      }\n      return new AnimationClip(animationName, undefined, tracks);\n    });\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    if (nodeDef.mesh === undefined) return null;\n    return parser.getDependency('mesh', nodeDef.mesh).then(function (mesh) {\n      const node = parser._getNodeRef(parser.meshCache, nodeDef.mesh, mesh);\n\n      // if weights are provided on the node, override weights on the mesh.\n      if (nodeDef.weights !== undefined) {\n        node.traverse(function (o) {\n          if (!o.isMesh) return;\n          for (let i = 0, il = nodeDef.weights.length; i < il; i++) {\n            o.morphTargetInfluences[i] = nodeDef.weights[i];\n          }\n        });\n      }\n      return node;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n   * @param {number} nodeIndex\n   * @return {Promise<Object3D>}\n   */\n  loadNode(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    const nodePending = parser._loadNodeShallow(nodeIndex);\n    const childPending = [];\n    const childrenDef = nodeDef.children || [];\n    for (let i = 0, il = childrenDef.length; i < il; i++) {\n      childPending.push(parser.getDependency('node', childrenDef[i]));\n    }\n    const skeletonPending = nodeDef.skin === undefined ? Promise.resolve(null) : parser.getDependency('skin', nodeDef.skin);\n    return Promise.all([nodePending, Promise.all(childPending), skeletonPending]).then(function (results) {\n      const node = results[0];\n      const children = results[1];\n      const skeleton = results[2];\n      if (skeleton !== null) {\n        // This full traverse should be fine because\n        // child glTF nodes have not been added to this node yet.\n        node.traverse(function (mesh) {\n          if (!mesh.isSkinnedMesh) return;\n          mesh.bind(skeleton, _identityMatrix);\n        });\n      }\n      for (let i = 0, il = children.length; i < il; i++) {\n        node.add(children[i]);\n      }\n      return node;\n    });\n  }\n\n  // ._loadNodeShallow() parses a single node.\n  // skin and child nodes are created and added in .loadNode() (no '_' prefix).\n  _loadNodeShallow(nodeIndex) {\n    const json = this.json;\n    const extensions = this.extensions;\n    const parser = this;\n\n    // This method is called from .loadNode() and .loadSkin().\n    // Cache a node to avoid duplication.\n\n    if (this.nodeCache[nodeIndex] !== undefined) {\n      return this.nodeCache[nodeIndex];\n    }\n    const nodeDef = json.nodes[nodeIndex];\n\n    // reserve node's name before its dependencies, so the root has the intended name.\n    const nodeName = nodeDef.name ? parser.createUniqueName(nodeDef.name) : '';\n    const pending = [];\n    const meshPromise = parser._invokeOne(function (ext) {\n      return ext.createNodeMesh && ext.createNodeMesh(nodeIndex);\n    });\n    if (meshPromise) {\n      pending.push(meshPromise);\n    }\n    if (nodeDef.camera !== undefined) {\n      pending.push(parser.getDependency('camera', nodeDef.camera).then(function (camera) {\n        return parser._getNodeRef(parser.cameraCache, nodeDef.camera, camera);\n      }));\n    }\n    parser._invokeAll(function (ext) {\n      return ext.createNodeAttachment && ext.createNodeAttachment(nodeIndex);\n    }).forEach(function (promise) {\n      pending.push(promise);\n    });\n    this.nodeCache[nodeIndex] = Promise.all(pending).then(function (objects) {\n      let node;\n\n      // .isBone isn't in glTF spec. See ._markDefs\n      if (nodeDef.isBone === true) {\n        node = new Bone();\n      } else if (objects.length > 1) {\n        node = new Group();\n      } else if (objects.length === 1) {\n        node = objects[0];\n      } else {\n        node = new Object3D();\n      }\n      if (node !== objects[0]) {\n        for (let i = 0, il = objects.length; i < il; i++) {\n          node.add(objects[i]);\n        }\n      }\n      if (nodeDef.name) {\n        node.userData.name = nodeDef.name;\n        node.name = nodeName;\n      }\n      assignExtrasToUserData(node, nodeDef);\n      if (nodeDef.extensions) addUnknownExtensionsToUserData(extensions, node, nodeDef);\n      if (nodeDef.matrix !== undefined) {\n        const matrix = new Matrix4();\n        matrix.fromArray(nodeDef.matrix);\n        node.applyMatrix4(matrix);\n      } else {\n        if (nodeDef.translation !== undefined) {\n          node.position.fromArray(nodeDef.translation);\n        }\n        if (nodeDef.rotation !== undefined) {\n          node.quaternion.fromArray(nodeDef.rotation);\n        }\n        if (nodeDef.scale !== undefined) {\n          node.scale.fromArray(nodeDef.scale);\n        }\n      }\n      if (!parser.associations.has(node)) {\n        parser.associations.set(node, {});\n      }\n      parser.associations.get(node).nodes = nodeIndex;\n      return node;\n    });\n    return this.nodeCache[nodeIndex];\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n   * @param {number} sceneIndex\n   * @return {Promise<Group>}\n   */\n  loadScene(sceneIndex) {\n    const extensions = this.extensions;\n    const sceneDef = this.json.scenes[sceneIndex];\n    const parser = this;\n\n    // Loader returns Group, not Scene.\n    // See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n    const scene = new Group();\n    if (sceneDef.name) scene.name = parser.createUniqueName(sceneDef.name);\n    assignExtrasToUserData(scene, sceneDef);\n    if (sceneDef.extensions) addUnknownExtensionsToUserData(extensions, scene, sceneDef);\n    const nodeIds = sceneDef.nodes || [];\n    const pending = [];\n    for (let i = 0, il = nodeIds.length; i < il; i++) {\n      pending.push(parser.getDependency('node', nodeIds[i]));\n    }\n    return Promise.all(pending).then(function (nodes) {\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        scene.add(nodes[i]);\n      }\n\n      // Removes dangling associations, associations that reference a node that\n      // didn't make it into the scene.\n      const reduceAssociations = node => {\n        const reducedAssociations = new Map();\n        for (const [key, value] of parser.associations) {\n          if (key instanceof Material || key instanceof Texture) {\n            reducedAssociations.set(key, value);\n          }\n        }\n        node.traverse(node => {\n          const mappings = parser.associations.get(node);\n          if (mappings != null) {\n            reducedAssociations.set(node, mappings);\n          }\n        });\n        return reducedAssociations;\n      };\n      parser.associations = reduceAssociations(scene);\n      return scene;\n    });\n  }\n  _createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target) {\n    const tracks = [];\n    const targetName = node.name ? node.name : node.uuid;\n    const targetNames = [];\n    if (PATH_PROPERTIES[target.path] === PATH_PROPERTIES.weights) {\n      node.traverse(function (object) {\n        if (object.morphTargetInfluences) {\n          targetNames.push(object.name ? object.name : object.uuid);\n        }\n      });\n    } else {\n      targetNames.push(targetName);\n    }\n    let TypedKeyframeTrack;\n    switch (PATH_PROPERTIES[target.path]) {\n      case PATH_PROPERTIES.weights:\n        TypedKeyframeTrack = NumberKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.rotation:\n        TypedKeyframeTrack = QuaternionKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.position:\n      case PATH_PROPERTIES.scale:\n      default:\n        switch (outputAccessor.itemSize) {\n          case 1:\n            TypedKeyframeTrack = NumberKeyframeTrack;\n            break;\n          case 2:\n          case 3:\n            TypedKeyframeTrack = VectorKeyframeTrack;\n            break;\n        }\n        break;\n    }\n    const interpolation = sampler.interpolation !== undefined ? INTERPOLATION[sampler.interpolation] : InterpolateLinear;\n    const outputArray = this._getArrayFromAccessor(outputAccessor);\n    for (let j = 0, jl = targetNames.length; j < jl; j++) {\n      const track = new TypedKeyframeTrack(targetNames[j] + '.' + PATH_PROPERTIES[target.path], inputAccessor.array, outputArray, interpolation);\n\n      // Override interpolation with custom factory method.\n      if (interpolation === 'CUBICSPLINE') {\n        this._createCubicSplineTrackInterpolant(track);\n      }\n      tracks.push(track);\n    }\n    return tracks;\n  }\n  _getArrayFromAccessor(accessor) {\n    let outputArray = accessor.array;\n    if (accessor.normalized) {\n      const scale = getNormalizedComponentScale(outputArray.constructor);\n      const scaled = new Float32Array(outputArray.length);\n      for (let j = 0, jl = outputArray.length; j < jl; j++) {\n        scaled[j] = outputArray[j] * scale;\n      }\n      outputArray = scaled;\n    }\n    return outputArray;\n  }\n  _createCubicSplineTrackInterpolant(track) {\n    track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline(result) {\n      // A CUBICSPLINE keyframe in glTF has three output values for each input value,\n      // representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n      // must be divided by three to get the interpolant's sampleSize argument.\n\n      const interpolantType = this instanceof QuaternionKeyframeTrack ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;\n      return new interpolantType(this.times, this.values, this.getValueSize() / 3, result);\n    };\n\n    // Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n    track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;\n  }\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const box = new Box3();\n  if (attributes.POSITION !== undefined) {\n    const accessor = parser.json.accessors[attributes.POSITION];\n    const min = accessor.min;\n    const max = accessor.max;\n\n    // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n    if (min !== undefined && max !== undefined) {\n      box.set(new Vector3(min[0], min[1], min[2]), new Vector3(max[0], max[1], max[2]));\n      if (accessor.normalized) {\n        const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n        box.min.multiplyScalar(boxScale);\n        box.max.multiplyScalar(boxScale);\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.');\n      return;\n    }\n  } else {\n    return;\n  }\n  const targets = primitiveDef.targets;\n  if (targets !== undefined) {\n    const maxDisplacement = new Vector3();\n    const vector = new Vector3();\n    for (let i = 0, il = targets.length; i < il; i++) {\n      const target = targets[i];\n      if (target.POSITION !== undefined) {\n        const accessor = parser.json.accessors[target.POSITION];\n        const min = accessor.min;\n        const max = accessor.max;\n\n        // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n        if (min !== undefined && max !== undefined) {\n          // we need to get max of absolute components because target weight is [-1,1]\n          vector.setX(Math.max(Math.abs(min[0]), Math.abs(max[0])));\n          vector.setY(Math.max(Math.abs(min[1]), Math.abs(max[1])));\n          vector.setZ(Math.max(Math.abs(min[2]), Math.abs(max[2])));\n          if (accessor.normalized) {\n            const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n            vector.multiplyScalar(boxScale);\n          }\n\n          // Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n          // to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n          // are used to implement key-frame animations and as such only two are active at a time - this results in very large\n          // boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n          maxDisplacement.max(vector);\n        } else {\n          console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.');\n        }\n      }\n    }\n\n    // As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n    box.expandByVector(maxDisplacement);\n  }\n  geometry.boundingBox = box;\n  const sphere = new Sphere();\n  box.getCenter(sphere.center);\n  sphere.radius = box.min.distanceTo(box.max) / 2;\n  geometry.boundingSphere = sphere;\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const pending = [];\n  function assignAttributeAccessor(accessorIndex, attributeName) {\n    return parser.getDependency('accessor', accessorIndex).then(function (accessor) {\n      geometry.setAttribute(attributeName, accessor);\n    });\n  }\n  for (const gltfAttributeName in attributes) {\n    const threeAttributeName = ATTRIBUTES[gltfAttributeName] || gltfAttributeName.toLowerCase();\n\n    // Skip attributes already provided by e.g. Draco extension.\n    if (threeAttributeName in geometry.attributes) continue;\n    pending.push(assignAttributeAccessor(attributes[gltfAttributeName], threeAttributeName));\n  }\n  if (primitiveDef.indices !== undefined && !geometry.index) {\n    const accessor = parser.getDependency('accessor', primitiveDef.indices).then(function (accessor) {\n      geometry.setIndex(accessor);\n    });\n    pending.push(accessor);\n  }\n  assignExtrasToUserData(geometry, primitiveDef);\n  computeBounds(geometry, primitiveDef, parser);\n  return Promise.all(pending).then(function () {\n    return primitiveDef.targets !== undefined ? addMorphTargets(geometry, primitiveDef.targets, parser) : geometry;\n  });\n}\nexport { GLTFLoader };", "map": {"version": 3, "names": ["AnimationClip", "Bone", "Box3", "BufferAttribute", "BufferGeometry", "ClampToEdgeWrapping", "Color", "DirectionalLight", "DoubleSide", "<PERSON><PERSON><PERSON><PERSON>", "FrontSide", "Group", "ImageBitmapLoader", "In<PERSON>d<PERSON>esh", "InterleavedBuffer", "InterleavedBufferAttribute", "Interpolant", "InterpolateDiscrete", "InterpolateLinear", "Line", "LineBasicMaterial", "LineLoop", "LineSegments", "LinearFilter", "LinearMipmapLinearFilter", "LinearMipmapNearestFilter", "Loader", "LoaderUtils", "Material", "MathUtils", "Matrix4", "<PERSON><PERSON>", "MeshBasicMaterial", "MeshPhysicalMaterial", "MeshStandardMaterial", "MirroredRepeatWrapping", "NearestFilter", "NearestMipmapLinearFilter", "NearestMipmapNearestFilter", "NumberKeyframeTrack", "Object3D", "OrthographicCamera", "PerspectiveCamera", "PointLight", "Points", "PointsMaterial", "PropertyBinding", "Quaternion", "QuaternionKeyframeTrack", "RepeatWrapping", "Skeleton", "<PERSON><PERSON><PERSON><PERSON>", "Sphere", "SpotLight", "Texture", "TextureLoader", "TriangleFanDrawMode", "TriangleStripDrawMode", "Vector2", "Vector3", "VectorKeyframeTrack", "SRGBColorSpace", "toTrianglesDrawMode", "GLTFLoader", "constructor", "manager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ktx2Loader", "meshoptDecoder", "pluginCallbacks", "register", "parser", "GLTFMaterialsClearcoatExtension", "GLTFTextureBasisUExtension", "GLTFTextureWebPExtension", "GLTFTextureAVIFExtension", "GLTFMaterialsSheenExtension", "GLTFMaterialsTransmissionExtension", "GLTFMaterialsVolumeExtension", "GLTFMaterialsIorExtension", "GLTFMaterialsEmissiveStrengthExtension", "GLTFMaterialsSpecularExtension", "GLTFMaterialsIridescenceExtension", "GLTFMaterialsAnisotropyExtension", "GLTFLightsExtension", "GLTFMeshoptCompression", "GLTFMeshGpuInstancing", "load", "url", "onLoad", "onProgress", "onError", "scope", "resourcePath", "path", "extractUrlBase", "itemStart", "_onError", "e", "console", "error", "itemError", "itemEnd", "loader", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "data", "parse", "gltf", "setDRACOLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "setKTX2Loader", "setMeshoptDecoder", "callback", "indexOf", "push", "unregister", "splice", "json", "extensions", "plugins", "textDecoder", "TextDecoder", "JSON", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magic", "decode", "Uint8Array", "BINARY_EXTENSION_HEADER_MAGIC", "EXTENSIONS", "KHR_BINARY_GLTF", "GLTFBinaryExtension", "content", "asset", "undefined", "version", "GLTFParser", "crossOrigin", "fileLoader", "i", "length", "plugin", "name", "extensionsUsed", "extensionName", "extensionsRequired", "KHR_MATERIALS_UNLIT", "GLTFMaterialsUnlitExtension", "KHR_DRACO_MESH_COMPRESSION", "GLTFDracoMeshCompressionExtension", "KHR_TEXTURE_TRANSFORM", "GLTFTextureTransformExtension", "KHR_MESH_QUANTIZATION", "GLTFMeshQuantizationExtension", "warn", "setExtensions", "setPlugins", "parseAsync", "Promise", "resolve", "reject", "GLTFRegistry", "objects", "get", "key", "add", "object", "remove", "removeAll", "KHR_LIGHTS_PUNCTUAL", "KHR_MATERIALS_CLEARCOAT", "KHR_MATERIALS_IOR", "KHR_MATERIALS_SHEEN", "KHR_MATERIALS_SPECULAR", "KHR_MATERIALS_TRANSMISSION", "KHR_MATERIALS_IRIDESCENCE", "KHR_MATERIALS_ANISOTROPY", "KHR_MATERIALS_VOLUME", "KHR_TEXTURE_BASISU", "KHR_MATERIALS_EMISSIVE_STRENGTH", "EXT_TEXTURE_WEBP", "EXT_TEXTURE_AVIF", "EXT_MESHOPT_COMPRESSION", "EXT_MESH_GPU_INSTANCING", "cache", "refs", "uses", "_markDefs", "nodeDefs", "nodes", "nodeIndex", "node<PERSON><PERSON><PERSON>", "nodeDef", "light", "_addNodeRef", "_loadLight", "lightIndex", "cache<PERSON>ey", "dependency", "lightDefs", "lights", "lightDef", "lightNode", "color", "fromArray", "range", "type", "target", "position", "set", "distance", "spot", "innerConeAngle", "outerConeAngle", "Math", "PI", "angle", "penumbra", "decay", "assignExtrasToUserData", "intensity", "createUniqueName", "getDependency", "index", "createNodeAttachment", "self", "then", "_getNodeRef", "getMaterialType", "extendParams", "materialParams", "materialDef", "pending", "opacity", "metallicRoughness", "pbrMetallicRoughness", "Array", "isArray", "baseColorFactor", "array", "baseColorTexture", "assignTexture", "all", "extendMaterialParams", "materialIndex", "materials", "emissiveStrength", "emissiveIntensity", "extension", "clearcoatFactor", "clearcoat", "clearcoatTexture", "clearcoatRoughnessFactor", "clearcoatRoughness", "clearcoatRoughnessTexture", "clearcoatNormalTexture", "scale", "clearcoatNormalScale", "iridescenceFactor", "iridescence", "iridescenceTexture", "iridescenceIor", "iridescenceIOR", "iridescenceThicknessRange", "iridescenceThicknessMinimum", "iridescenceThicknessMaximum", "iridescenceThicknessTexture", "sheenColor", "sheenRoughness", "sheen", "sheenColorFactor", "sheenRoughnessFactor", "sheenColorTexture", "sheenRoughnessTexture", "transmissionFactor", "transmission", "transmissionTexture", "thickness", "thicknessFactor", "thicknessTexture", "attenuationDistance", "Infinity", "colorArray", "attenuationColor", "ior", "specularIntensity", "specularFactor", "specularTexture", "specularColorFactor", "specularColor", "specularColorTexture", "anisotropyStrength", "anisotropy", "anisotropyRotation", "anisotropyTexture", "loadTexture", "textureIndex", "textureDef", "textures", "options", "loadTextureImage", "source", "isSupported", "images", "textureLoader", "uri", "handler", "<PERSON><PERSON><PERSON><PERSON>", "detectSupport", "image", "Image", "src", "onload", "onerror", "height", "loadBufferView", "bufferView", "bufferViews", "extensionDef", "buffer", "decoder", "supported", "res", "byteOffset", "byteLength", "count", "stride", "byteStride", "decodeGltfBufferAsync", "mode", "filter", "ready", "result", "decodeGltfBuffer", "createNodeMesh", "mesh", "meshDef", "meshes", "primitive", "primitives", "WEBGL_CONSTANTS", "TRIANGLES", "TRIANGLE_STRIP", "TRIANGLE_FAN", "attributesDef", "attributes", "accessor", "results", "nodeObject", "pop", "isGroup", "children", "instanced<PERSON><PERSON><PERSON>", "m", "p", "q", "s", "instanced<PERSON><PERSON>", "geometry", "material", "TRANSLATION", "fromBufferAttribute", "ROTATION", "SCALE", "setMatrixAt", "compose", "attributeName", "setAttribute", "prototype", "copy", "call", "assignFinalMaterial", "clear", "BINARY_EXTENSION_HEADER_LENGTH", "BINARY_EXTENSION_CHUNK_TYPES", "BIN", "body", "headerView", "DataView", "header", "slice", "getUint32", "chunkContentsLength", "chunkView", "chunkIndex", "chunkLength", "chunkType", "contentArray", "preload", "decodePrimitive", "bufferViewIndex", "gltfAttributeMap", "threeAttributeMap", "attributeNormalizedMap", "attributeTypeMap", "threeAttributeName", "ATTRIBUTES", "toLowerCase", "accessorDef", "accessors", "componentType", "WEBGL_COMPONENT_TYPES", "normalized", "decodeDracoFile", "attribute", "extendTexture", "texture", "transform", "texCoord", "channel", "offset", "rotation", "clone", "repeat", "needsUpdate", "GLTFCubicSplineInterpolant", "parameterPositions", "sampleValues", "sampleSize", "result<PERSON><PERSON><PERSON>", "copySampleValue_", "values", "valueSize", "interpolate_", "i1", "t0", "t", "t1", "stride2", "stride3", "td", "pp", "ppp", "offset1", "offset0", "s2", "s3", "s0", "s1", "p0", "m0", "p1", "m1", "_q", "GLTFCubicSplineQuaternionInterpolant", "normalize", "toArray", "FLOAT", "FLOAT_MAT3", "FLOAT_MAT4", "FLOAT_VEC2", "FLOAT_VEC3", "FLOAT_VEC4", "LINEAR", "REPEAT", "SAMPLER_2D", "POINTS", "LINES", "LINE_LOOP", "LINE_STRIP", "UNSIGNED_BYTE", "UNSIGNED_SHORT", "Int8Array", "Int16Array", "Uint16Array", "Uint32Array", "Float32Array", "WEBGL_FILTERS", "WEBGL_WRAPPINGS", "WEBGL_TYPE_SIZES", "POSITION", "NORMAL", "TANGENT", "TEXCOORD_0", "TEXCOORD_1", "TEXCOORD_2", "TEXCOORD_3", "COLOR_0", "WEIGHTS_0", "JOINTS_0", "PATH_PROPERTIES", "translation", "weights", "INTERPOLATION", "CUBICSPLINE", "STEP", "ALPHA_MODES", "OPAQUE", "MASK", "BLEND", "createDefaultMaterial", "emissive", "metalness", "roughness", "transparent", "depthTest", "side", "addUnknownExtensionsToUserData", "knownExtensions", "objectDef", "userData", "gltfExtensions", "gltfDef", "extras", "Object", "assign", "addMorphTargets", "targets", "hasMorphPosition", "hasMorphNormal", "hasMorphColor", "il", "pendingPositionAccessors", "pendingNormalAccessors", "pendingColorAccessors", "pendingAccessor", "normal", "morphPositions", "morphNormals", "morphColors", "morphAttributes", "morphTargetsRelative", "updateMorphTargets", "morphTargetInfluences", "targetNames", "morphTargetDictionary", "createPrimitiveKey", "primitiveDef", "geometry<PERSON>ey", "dracoExtension", "indices", "createAttributesKey", "<PERSON><PERSON><PERSON>", "keys", "sort", "getNormalizedComponentScale", "getImageURIMimeType", "search", "_identityMatrix", "associations", "Map", "primitiveCache", "nodeCache", "meshCache", "cameraCache", "lightCache", "sourceCache", "textureCache", "nodeNamesUsed", "<PERSON><PERSON><PERSON><PERSON>", "isFirefox", "firefoxVersion", "navigator", "test", "userAgent", "match", "createImageBitmap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_invokeAll", "ext", "beforeRoot", "getDependencies", "dependencies", "scene", "scenes", "animations", "cameras", "afterRoot", "catch", "skinDefs", "skins", "meshDefs", "skinIndex", "<PERSON><PERSON><PERSON><PERSON>", "joints", "isBone", "skin", "isSkinnedMesh", "camera", "ref", "updateMappings", "original", "mappings", "child", "entries", "_invokeOne", "func", "unshift", "loadScene", "loadNode", "loadMesh", "loadAccessor", "loadBuffer", "loadMaterial", "loadSkin", "loadAnimation", "loadCamera", "defs", "map", "def", "bufferIndex", "bufferDef", "buffers", "resolveURL", "bufferViewDef", "accessorIndex", "sparse", "itemSize", "TypedArray", "pendingBufferViews", "elementBytes", "BYTES_PER_ELEMENT", "itemBytes", "bufferAttribute", "ibSlice", "floor", "ib<PERSON><PERSON><PERSON><PERSON>", "ib", "itemSizeIndices", "SCALAR", "TypedArrayIndices", "byteOffsetIndices", "byteOffsetValues", "sparseIndices", "sparseValues", "setX", "setY", "setZ", "setW", "sourceIndex", "sourceDef", "sampler", "promise", "loadImageSource", "flipY", "startsWith", "samplers", "magFilter", "minFilter", "wrapS", "wrapT", "URL", "webkitURL", "sourceURI", "isObjectURL", "blob", "Blob", "mimeType", "createObjectURL", "isImageBitmapLoader", "imageBitmap", "revokeObjectURL", "mapName", "mapDef", "colorSpace", "gltfReference", "useDerivativeTangents", "tangent", "useVertexColors", "useFlatShading", "isPoints", "uuid", "pointsMaterial", "sizeAttenuation", "isLine", "lineMaterial", "cachedMaterial", "vertexColors", "flatShading", "normalScale", "y", "materialType", "materialExtensions", "kmuExtension", "metallicFactor", "roughnessFactor", "metallicRoughnessTexture", "doubleSided", "alphaMode", "depthWrite", "alphaTest", "<PERSON><PERSON><PERSON><PERSON>", "normalTexture", "occlusionTexture", "strength", "aoMapIntensity", "emissiveFactor", "emissiveTexture", "originalName", "sanitizedName", "sanitizeNodeName", "loadGeometries", "createDracoPrimitive", "addPrimitiveAttributes", "cached", "geometryPromise", "meshIndex", "geometries", "normalizeSkinWeights", "group", "cameraIndex", "cameraDef", "params", "radToDeg", "yfov", "aspectRatio", "znear", "zfar", "xmag", "ymag", "skinDef", "_loadNodeShallow", "inverseBindMatrices", "jointNodes", "bones", "boneInverses", "jointNode", "mat", "animationIndex", "animationDef", "animationName", "pendingNodes", "pendingInputAccessors", "pendingOutputAccessors", "pendingSamplers", "pendingTargets", "channels", "node", "input", "parameters", "output", "inputAccessors", "outputAccessors", "tracks", "inputAccessor", "outputAccessor", "updateMatrix", "matrixAutoUpdate", "createdTracks", "_createAnimationTracks", "k", "traverse", "o", "<PERSON><PERSON><PERSON>", "nodePending", "childPending", "childrenDef", "skeletonPending", "skeleton", "bind", "nodeName", "meshPromise", "for<PERSON>ach", "matrix", "applyMatrix4", "quaternion", "has", "sceneIndex", "sceneDef", "nodeIds", "reduceAssociations", "reducedAssociations", "value", "targetName", "TypedKeyframeTrack", "interpolation", "outputArray", "_getArrayFromAccessor", "j", "jl", "track", "_createCubicSplineTrackInterpolant", "scaled", "createInterpolant", "InterpolantFactoryMethodGLTFCubicSpline", "interpolantType", "times", "getValueSize", "isInterpolantFactoryMethodGLTFCubicSpline", "computeBounds", "box", "min", "max", "boxScale", "multiplyScalar", "maxDisplacement", "vector", "abs", "expandByVector", "boundingBox", "sphere", "getCenter", "center", "radius", "distanceTo", "boundingSphere", "assignAttributeAccessor", "gltfAttributeName", "setIndex"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three/examples/jsm/loaders/GLTFLoader.js"], "sourcesContent": ["import {\n\tAnimationClip,\n\tBone,\n\tBox3,\n\tBufferAttribute,\n\tBufferGeometry,\n\tClampToEdgeWrapping,\n\tColor,\n\tDirectionalLight,\n\tDoubleSide,\n\tFileLoader,\n\tFrontSide,\n\tGroup,\n\tImageBitmapLoader,\n\tInstancedMesh,\n\tInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tInterpolant,\n\tInterpolateDiscrete,\n\tInterpolateLinear,\n\tLine,\n\tLineBasicMaterial,\n\tLineLoop,\n\tLineSegments,\n\tLinearFilter,\n\tLinearMipmapLinearFilter,\n\tLinearMipmapNearestFilter,\n\tLoader,\n\tLoaderUtils,\n\tMaterial,\n\tMathUtils,\n\tMatrix4,\n\tMesh,\n\tMeshBasicMaterial,\n\tMeshPhysicalMaterial,\n\tMeshStandardMaterial,\n\tMirroredRepeatWrapping,\n\tNearestFilter,\n\tNearestMipmapLinearFilter,\n\tNearestMipmapNearestFilter,\n\tNumberKeyframeTrack,\n\tObject3D,\n\tOrthographicCamera,\n\tPerspectiveCamera,\n\tPointLight,\n\tPoints,\n\tPointsMaterial,\n\tPropertyBinding,\n\tQuaternion,\n\tQuaternionKeyframeTrack,\n\tRepeatWrapping,\n\tSkeleton,\n\tSkinnedMesh,\n\tSphere,\n\tSpotLight,\n\tTexture,\n\tTextureLoader,\n\tTriangleFanDrawMode,\n\tTriangleStripDrawMode,\n\tVector2,\n\tVector3,\n\tVectorKeyframeTrack,\n\tSRGBColorSpace\n} from 'three';\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils.js';\n\nclass GLTFLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.dracoLoader = null;\n\t\tthis.ktx2Loader = null;\n\t\tthis.meshoptDecoder = null;\n\n\t\tthis.pluginCallbacks = [];\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsClearcoatExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureBasisUExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureWebPExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureAVIFExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsSheenExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsTransmissionExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsVolumeExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsIorExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsEmissiveStrengthExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsSpecularExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsIridescenceExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsAnisotropyExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFLightsExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMeshoptCompression( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMeshGpuInstancing( parser );\n\n\t\t} );\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tlet resourcePath;\n\n\t\tif ( this.resourcePath !== '' ) {\n\n\t\t\tresourcePath = this.resourcePath;\n\n\t\t} else if ( this.path !== '' ) {\n\n\t\t\tresourcePath = this.path;\n\n\t\t} else {\n\n\t\t\tresourcePath = LoaderUtils.extractUrlBase( url );\n\n\t\t}\n\n\t\t// Tells the LoadingManager to track an extra item, which resolves after\n\t\t// the model is fully loaded. This means the count of items loaded will\n\t\t// be incorrect, but ensures manager.onLoad() does not fire early.\n\t\tthis.manager.itemStart( url );\n\n\t\tconst _onError = function ( e ) {\n\n\t\t\tif ( onError ) {\n\n\t\t\t\tonError( e );\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( e );\n\n\t\t\t}\n\n\t\t\tscope.manager.itemError( url );\n\t\t\tscope.manager.itemEnd( url );\n\n\t\t};\n\n\t\tconst loader = new FileLoader( this.manager );\n\n\t\tloader.setPath( this.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\n\t\tloader.load( url, function ( data ) {\n\n\t\t\ttry {\n\n\t\t\t\tscope.parse( data, resourcePath, function ( gltf ) {\n\n\t\t\t\t\tonLoad( gltf );\n\n\t\t\t\t\tscope.manager.itemEnd( url );\n\n\t\t\t\t}, _onError );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\t_onError( e );\n\n\t\t\t}\n\n\t\t}, onProgress, _onError );\n\n\t}\n\n\tsetDRACOLoader( dracoLoader ) {\n\n\t\tthis.dracoLoader = dracoLoader;\n\t\treturn this;\n\n\t}\n\n\tsetDDSLoader() {\n\n\t\tthrow new Error(\n\n\t\t\t'THREE.GLTFLoader: \"MSFT_texture_dds\" no longer supported. Please update to \"KHR_texture_basisu\".'\n\n\t\t);\n\n\t}\n\n\tsetKTX2Loader( ktx2Loader ) {\n\n\t\tthis.ktx2Loader = ktx2Loader;\n\t\treturn this;\n\n\t}\n\n\tsetMeshoptDecoder( meshoptDecoder ) {\n\n\t\tthis.meshoptDecoder = meshoptDecoder;\n\t\treturn this;\n\n\t}\n\n\tregister( callback ) {\n\n\t\tif ( this.pluginCallbacks.indexOf( callback ) === - 1 ) {\n\n\t\t\tthis.pluginCallbacks.push( callback );\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n\tunregister( callback ) {\n\n\t\tif ( this.pluginCallbacks.indexOf( callback ) !== - 1 ) {\n\n\t\t\tthis.pluginCallbacks.splice( this.pluginCallbacks.indexOf( callback ), 1 );\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n\tparse( data, path, onLoad, onError ) {\n\n\t\tlet json;\n\t\tconst extensions = {};\n\t\tconst plugins = {};\n\t\tconst textDecoder = new TextDecoder();\n\n\t\tif ( typeof data === 'string' ) {\n\n\t\t\tjson = JSON.parse( data );\n\n\t\t} else if ( data instanceof ArrayBuffer ) {\n\n\t\t\tconst magic = textDecoder.decode( new Uint8Array( data, 0, 4 ) );\n\n\t\t\tif ( magic === BINARY_EXTENSION_HEADER_MAGIC ) {\n\n\t\t\t\ttry {\n\n\t\t\t\t\textensions[ EXTENSIONS.KHR_BINARY_GLTF ] = new GLTFBinaryExtension( data );\n\n\t\t\t\t} catch ( error ) {\n\n\t\t\t\t\tif ( onError ) onError( error );\n\t\t\t\t\treturn;\n\n\t\t\t\t}\n\n\t\t\t\tjson = JSON.parse( extensions[ EXTENSIONS.KHR_BINARY_GLTF ].content );\n\n\t\t\t} else {\n\n\t\t\t\tjson = JSON.parse( textDecoder.decode( data ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tjson = data;\n\n\t\t}\n\n\t\tif ( json.asset === undefined || json.asset.version[ 0 ] < 2 ) {\n\n\t\t\tif ( onError ) onError( new Error( 'THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.' ) );\n\t\t\treturn;\n\n\t\t}\n\n\t\tconst parser = new GLTFParser( json, {\n\n\t\t\tpath: path || this.resourcePath || '',\n\t\t\tcrossOrigin: this.crossOrigin,\n\t\t\trequestHeader: this.requestHeader,\n\t\t\tmanager: this.manager,\n\t\t\tktx2Loader: this.ktx2Loader,\n\t\t\tmeshoptDecoder: this.meshoptDecoder\n\n\t\t} );\n\n\t\tparser.fileLoader.setRequestHeader( this.requestHeader );\n\n\t\tfor ( let i = 0; i < this.pluginCallbacks.length; i ++ ) {\n\n\t\t\tconst plugin = this.pluginCallbacks[ i ]( parser );\n\t\t\tplugins[ plugin.name ] = plugin;\n\n\t\t\t// Workaround to avoid determining as unknown extension\n\t\t\t// in addUnknownExtensionsToUserData().\n\t\t\t// Remove this workaround if we move all the existing\n\t\t\t// extension handlers to plugin system\n\t\t\textensions[ plugin.name ] = true;\n\n\t\t}\n\n\t\tif ( json.extensionsUsed ) {\n\n\t\t\tfor ( let i = 0; i < json.extensionsUsed.length; ++ i ) {\n\n\t\t\t\tconst extensionName = json.extensionsUsed[ i ];\n\t\t\t\tconst extensionsRequired = json.extensionsRequired || [];\n\n\t\t\t\tswitch ( extensionName ) {\n\n\t\t\t\t\tcase EXTENSIONS.KHR_MATERIALS_UNLIT:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFMaterialsUnlitExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFDracoMeshCompressionExtension( json, this.dracoLoader );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFTextureTransformExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_MESH_QUANTIZATION:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFMeshQuantizationExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\n\t\t\t\t\t\tif ( extensionsRequired.indexOf( extensionName ) >= 0 && plugins[ extensionName ] === undefined ) {\n\n\t\t\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".' );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tparser.setExtensions( extensions );\n\t\tparser.setPlugins( plugins );\n\t\tparser.parse( onLoad, onError );\n\n\t}\n\n\tparseAsync( data, path ) {\n\n\t\tconst scope = this;\n\n\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\tscope.parse( data, path, resolve, reject );\n\n\t\t} );\n\n\t}\n\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n\n\tlet objects = {};\n\n\treturn\t{\n\n\t\tget: function ( key ) {\n\n\t\t\treturn objects[ key ];\n\n\t\t},\n\n\t\tadd: function ( key, object ) {\n\n\t\t\tobjects[ key ] = object;\n\n\t\t},\n\n\t\tremove: function ( key ) {\n\n\t\t\tdelete objects[ key ];\n\n\t\t},\n\n\t\tremoveAll: function () {\n\n\t\t\tobjects = {};\n\n\t\t}\n\n\t};\n\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n\tKHR_BINARY_GLTF: 'KHR_binary_glTF',\n\tKHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n\tKHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n\tKHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n\tKHR_MATERIALS_IOR: 'KHR_materials_ior',\n\tKHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n\tKHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n\tKHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n\tKHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n\tKHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n\tKHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n\tKHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n\tKHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n\tKHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n\tKHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n\tKHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n\tEXT_TEXTURE_WEBP: 'EXT_texture_webp',\n\tEXT_TEXTURE_AVIF: 'EXT_texture_avif',\n\tEXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n\tEXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing'\n};\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n */\nclass GLTFLightsExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;\n\n\t\t// Object3D instance caches\n\t\tthis.cache = { refs: {}, uses: {} };\n\n\t}\n\n\t_markDefs() {\n\n\t\tconst parser = this.parser;\n\t\tconst nodeDefs = this.parser.json.nodes || [];\n\n\t\tfor ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {\n\n\t\t\tconst nodeDef = nodeDefs[ nodeIndex ];\n\n\t\t\tif ( nodeDef.extensions\n\t\t\t\t\t&& nodeDef.extensions[ this.name ]\n\t\t\t\t\t&& nodeDef.extensions[ this.name ].light !== undefined ) {\n\n\t\t\t\tparser._addNodeRef( this.cache, nodeDef.extensions[ this.name ].light );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t_loadLight( lightIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst cacheKey = 'light:' + lightIndex;\n\t\tlet dependency = parser.cache.get( cacheKey );\n\n\t\tif ( dependency ) return dependency;\n\n\t\tconst json = parser.json;\n\t\tconst extensions = ( json.extensions && json.extensions[ this.name ] ) || {};\n\t\tconst lightDefs = extensions.lights || [];\n\t\tconst lightDef = lightDefs[ lightIndex ];\n\t\tlet lightNode;\n\n\t\tconst color = new Color( 0xffffff );\n\n\t\tif ( lightDef.color !== undefined ) color.fromArray( lightDef.color );\n\n\t\tconst range = lightDef.range !== undefined ? lightDef.range : 0;\n\n\t\tswitch ( lightDef.type ) {\n\n\t\t\tcase 'directional':\n\t\t\t\tlightNode = new DirectionalLight( color );\n\t\t\t\tlightNode.target.position.set( 0, 0, - 1 );\n\t\t\t\tlightNode.add( lightNode.target );\n\t\t\t\tbreak;\n\n\t\t\tcase 'point':\n\t\t\t\tlightNode = new PointLight( color );\n\t\t\t\tlightNode.distance = range;\n\t\t\t\tbreak;\n\n\t\t\tcase 'spot':\n\t\t\t\tlightNode = new SpotLight( color );\n\t\t\t\tlightNode.distance = range;\n\t\t\t\t// Handle spotlight properties.\n\t\t\t\tlightDef.spot = lightDef.spot || {};\n\t\t\t\tlightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0;\n\t\t\t\tlightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0;\n\t\t\t\tlightNode.angle = lightDef.spot.outerConeAngle;\n\t\t\t\tlightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;\n\t\t\t\tlightNode.target.position.set( 0, 0, - 1 );\n\t\t\t\tlightNode.add( lightNode.target );\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: Unexpected light type: ' + lightDef.type );\n\n\t\t}\n\n\t\t// Some lights (e.g. spot) default to a position other than the origin. Reset the position\n\t\t// here, because node-level parsing will only override position if explicitly specified.\n\t\tlightNode.position.set( 0, 0, 0 );\n\n\t\tlightNode.decay = 2;\n\n\t\tassignExtrasToUserData( lightNode, lightDef );\n\n\t\tif ( lightDef.intensity !== undefined ) lightNode.intensity = lightDef.intensity;\n\n\t\tlightNode.name = parser.createUniqueName( lightDef.name || ( 'light_' + lightIndex ) );\n\n\t\tdependency = Promise.resolve( lightNode );\n\n\t\tparser.cache.add( cacheKey, dependency );\n\n\t\treturn dependency;\n\n\t}\n\n\tgetDependency( type, index ) {\n\n\t\tif ( type !== 'light' ) return;\n\n\t\treturn this._loadLight( index );\n\n\t}\n\n\tcreateNodeAttachment( nodeIndex ) {\n\n\t\tconst self = this;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\t\tconst lightDef = ( nodeDef.extensions && nodeDef.extensions[ this.name ] ) || {};\n\t\tconst lightIndex = lightDef.light;\n\n\t\tif ( lightIndex === undefined ) return null;\n\n\t\treturn this._loadLight( lightIndex ).then( function ( light ) {\n\n\t\t\treturn parser._getNodeRef( self.cache, lightIndex, light );\n\n\t\t} );\n\n\t}\n\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n */\nclass GLTFMaterialsUnlitExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_UNLIT;\n\n\t}\n\n\tgetMaterialType() {\n\n\t\treturn MeshBasicMaterial;\n\n\t}\n\n\textendParams( materialParams, materialDef, parser ) {\n\n\t\tconst pending = [];\n\n\t\tmaterialParams.color = new Color( 1.0, 1.0, 1.0 );\n\t\tmaterialParams.opacity = 1.0;\n\n\t\tconst metallicRoughness = materialDef.pbrMetallicRoughness;\n\n\t\tif ( metallicRoughness ) {\n\n\t\t\tif ( Array.isArray( metallicRoughness.baseColorFactor ) ) {\n\n\t\t\t\tconst array = metallicRoughness.baseColorFactor;\n\n\t\t\t\tmaterialParams.color.fromArray( array );\n\t\t\t\tmaterialParams.opacity = array[ 3 ];\n\n\t\t\t}\n\n\t\t\tif ( metallicRoughness.baseColorTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace ) );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst emissiveStrength = materialDef.extensions[ this.name ].emissiveStrength;\n\n\t\tif ( emissiveStrength !== undefined ) {\n\n\t\t\tmaterialParams.emissiveIntensity = emissiveStrength;\n\n\t\t}\n\n\t\treturn Promise.resolve();\n\n\t}\n\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n */\nclass GLTFMaterialsClearcoatExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.clearcoatFactor !== undefined ) {\n\n\t\t\tmaterialParams.clearcoat = extension.clearcoatFactor;\n\n\t\t}\n\n\t\tif ( extension.clearcoatTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatMap', extension.clearcoatTexture ) );\n\n\t\t}\n\n\t\tif ( extension.clearcoatRoughnessFactor !== undefined ) {\n\n\t\t\tmaterialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;\n\n\t\t}\n\n\t\tif ( extension.clearcoatRoughnessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture ) );\n\n\t\t}\n\n\t\tif ( extension.clearcoatNormalTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture ) );\n\n\t\t\tif ( extension.clearcoatNormalTexture.scale !== undefined ) {\n\n\t\t\t\tconst scale = extension.clearcoatNormalTexture.scale;\n\n\t\t\t\tmaterialParams.clearcoatNormalScale = new Vector2( scale, scale );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n */\nclass GLTFMaterialsIridescenceExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.iridescenceFactor !== undefined ) {\n\n\t\t\tmaterialParams.iridescence = extension.iridescenceFactor;\n\n\t\t}\n\n\t\tif ( extension.iridescenceTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'iridescenceMap', extension.iridescenceTexture ) );\n\n\t\t}\n\n\t\tif ( extension.iridescenceIor !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceIOR = extension.iridescenceIor;\n\n\t\t}\n\n\t\tif ( materialParams.iridescenceThicknessRange === undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange = [ 100, 400 ];\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessMinimum !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange[ 0 ] = extension.iridescenceThicknessMinimum;\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessMaximum !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange[ 1 ] = extension.iridescenceThicknessMaximum;\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n */\nclass GLTFMaterialsSheenExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_SHEEN;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tmaterialParams.sheenColor = new Color( 0, 0, 0 );\n\t\tmaterialParams.sheenRoughness = 0;\n\t\tmaterialParams.sheen = 1;\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.sheenColorFactor !== undefined ) {\n\n\t\t\tmaterialParams.sheenColor.fromArray( extension.sheenColorFactor );\n\n\t\t}\n\n\t\tif ( extension.sheenRoughnessFactor !== undefined ) {\n\n\t\t\tmaterialParams.sheenRoughness = extension.sheenRoughnessFactor;\n\n\t\t}\n\n\t\tif ( extension.sheenColorTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\tif ( extension.sheenRoughnessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n */\nclass GLTFMaterialsTransmissionExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.transmissionFactor !== undefined ) {\n\n\t\t\tmaterialParams.transmission = extension.transmissionFactor;\n\n\t\t}\n\n\t\tif ( extension.transmissionTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'transmissionMap', extension.transmissionTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n */\nclass GLTFMaterialsVolumeExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_VOLUME;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0;\n\n\t\tif ( extension.thicknessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'thicknessMap', extension.thicknessTexture ) );\n\n\t\t}\n\n\t\tmaterialParams.attenuationDistance = extension.attenuationDistance || Infinity;\n\n\t\tconst colorArray = extension.attenuationColor || [ 1, 1, 1 ];\n\t\tmaterialParams.attenuationColor = new Color( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ] );\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n */\nclass GLTFMaterialsIorExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_IOR;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.ior = extension.ior !== undefined ? extension.ior : 1.5;\n\n\t\treturn Promise.resolve();\n\n\t}\n\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n */\nclass GLTFMaterialsSpecularExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0;\n\n\t\tif ( extension.specularTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'specularIntensityMap', extension.specularTexture ) );\n\n\t\t}\n\n\t\tconst colorArray = extension.specularColorFactor || [ 1, 1, 1 ];\n\t\tmaterialParams.specularColor = new Color( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ] );\n\n\t\tif ( extension.specularColorTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n */\nclass GLTFMaterialsAnisotropyExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.anisotropyStrength !== undefined ) {\n\n\t\t\tmaterialParams.anisotropy = extension.anisotropyStrength;\n\n\t\t}\n\n\t\tif ( extension.anisotropyRotation !== undefined ) {\n\n\t\t\tmaterialParams.anisotropyRotation = extension.anisotropyRotation;\n\n\t\t}\n\n\t\tif ( extension.anisotropyTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'anisotropyMap', extension.anisotropyTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n */\nclass GLTFTextureBasisUExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_TEXTURE_BASISU;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ this.name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ this.name ];\n\t\tconst loader = parser.options.ktx2Loader;\n\n\t\tif ( ! loader ) {\n\n\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures' );\n\n\t\t\t} else {\n\n\t\t\t\t// Assumes that the extension is optional and that a fallback texture is present\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t}\n\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n */\nclass GLTFTextureWebPExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_TEXTURE_WEBP;\n\t\tthis.isSupported = null;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst name = this.name;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ name ];\n\t\tconst source = json.images[ extension.source ];\n\n\t\tlet loader = parser.textureLoader;\n\t\tif ( source.uri ) {\n\n\t\t\tconst handler = parser.options.manager.getHandler( source.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn this.detectSupport().then( function ( isSupported ) {\n\n\t\t\tif ( isSupported ) return parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( name ) >= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: WebP required by asset but unsupported.' );\n\n\t\t\t}\n\n\t\t\t// Fall back to PNG or JPEG.\n\t\t\treturn parser.loadTexture( textureIndex );\n\n\t\t} );\n\n\t}\n\n\tdetectSupport() {\n\n\t\tif ( ! this.isSupported ) {\n\n\t\t\tthis.isSupported = new Promise( function ( resolve ) {\n\n\t\t\t\tconst image = new Image();\n\n\t\t\t\t// Lossy test image. Support for lossy images doesn't guarantee support for all\n\t\t\t\t// WebP images, unfortunately.\n\t\t\t\timage.src = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';\n\n\t\t\t\timage.onload = image.onerror = function () {\n\n\t\t\t\t\tresolve( image.height === 1 );\n\n\t\t\t\t};\n\n\t\t\t} );\n\n\t\t}\n\n\t\treturn this.isSupported;\n\n\t}\n\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n */\nclass GLTFTextureAVIFExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_TEXTURE_AVIF;\n\t\tthis.isSupported = null;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst name = this.name;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ name ];\n\t\tconst source = json.images[ extension.source ];\n\n\t\tlet loader = parser.textureLoader;\n\t\tif ( source.uri ) {\n\n\t\t\tconst handler = parser.options.manager.getHandler( source.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn this.detectSupport().then( function ( isSupported ) {\n\n\t\t\tif ( isSupported ) return parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( name ) >= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: AVIF required by asset but unsupported.' );\n\n\t\t\t}\n\n\t\t\t// Fall back to PNG or JPEG.\n\t\t\treturn parser.loadTexture( textureIndex );\n\n\t\t} );\n\n\t}\n\n\tdetectSupport() {\n\n\t\tif ( ! this.isSupported ) {\n\n\t\t\tthis.isSupported = new Promise( function ( resolve ) {\n\n\t\t\t\tconst image = new Image();\n\n\t\t\t\t// Lossy test image.\n\t\t\t\timage.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI=';\n\t\t\t\timage.onload = image.onerror = function () {\n\n\t\t\t\t\tresolve( image.height === 1 );\n\n\t\t\t\t};\n\n\t\t\t} );\n\n\t\t}\n\n\t\treturn this.isSupported;\n\n\t}\n\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n */\nclass GLTFMeshoptCompression {\n\n\tconstructor( parser ) {\n\n\t\tthis.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;\n\t\tthis.parser = parser;\n\n\t}\n\n\tloadBufferView( index ) {\n\n\t\tconst json = this.parser.json;\n\t\tconst bufferView = json.bufferViews[ index ];\n\n\t\tif ( bufferView.extensions && bufferView.extensions[ this.name ] ) {\n\n\t\t\tconst extensionDef = bufferView.extensions[ this.name ];\n\n\t\t\tconst buffer = this.parser.getDependency( 'buffer', extensionDef.buffer );\n\t\t\tconst decoder = this.parser.options.meshoptDecoder;\n\n\t\t\tif ( ! decoder || ! decoder.supported ) {\n\n\t\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {\n\n\t\t\t\t\tthrow new Error( 'THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files' );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Assumes that the extension is optional and that fallback buffer data is present\n\t\t\t\t\treturn null;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn buffer.then( function ( res ) {\n\n\t\t\t\tconst byteOffset = extensionDef.byteOffset || 0;\n\t\t\t\tconst byteLength = extensionDef.byteLength || 0;\n\n\t\t\t\tconst count = extensionDef.count;\n\t\t\t\tconst stride = extensionDef.byteStride;\n\n\t\t\t\tconst source = new Uint8Array( res, byteOffset, byteLength );\n\n\t\t\t\tif ( decoder.decodeGltfBufferAsync ) {\n\n\t\t\t\t\treturn decoder.decodeGltfBufferAsync( count, stride, source, extensionDef.mode, extensionDef.filter ).then( function ( res ) {\n\n\t\t\t\t\t\treturn res.buffer;\n\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n\t\t\t\t\treturn decoder.ready.then( function () {\n\n\t\t\t\t\t\tconst result = new ArrayBuffer( count * stride );\n\t\t\t\t\t\tdecoder.decodeGltfBuffer( new Uint8Array( result ), count, stride, source, extensionDef.mode, extensionDef.filter );\n\t\t\t\t\t\treturn result;\n\n\t\t\t\t\t} );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} else {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n */\nclass GLTFMeshGpuInstancing {\n\n\tconstructor( parser ) {\n\n\t\tthis.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING;\n\t\tthis.parser = parser;\n\n\t}\n\n\tcreateNodeMesh( nodeIndex ) {\n\n\t\tconst json = this.parser.json;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tif ( ! nodeDef.extensions || ! nodeDef.extensions[ this.name ] ||\n\t\t\tnodeDef.mesh === undefined ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst meshDef = json.meshes[ nodeDef.mesh ];\n\n\t\t// No Points or Lines + Instancing support yet\n\n\t\tfor ( const primitive of meshDef.primitives ) {\n\n\t\t\tif ( primitive.mode !== WEBGL_CONSTANTS.TRIANGLES &&\n\t\t\t\t primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP &&\n\t\t\t\t primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN &&\n\t\t\t\t primitive.mode !== undefined ) {\n\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst extensionDef = nodeDef.extensions[ this.name ];\n\t\tconst attributesDef = extensionDef.attributes;\n\n\t\t// @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n\t\tconst pending = [];\n\t\tconst attributes = {};\n\n\t\tfor ( const key in attributesDef ) {\n\n\t\t\tpending.push( this.parser.getDependency( 'accessor', attributesDef[ key ] ).then( accessor => {\n\n\t\t\t\tattributes[ key ] = accessor;\n\t\t\t\treturn attributes[ key ];\n\n\t\t\t} ) );\n\n\t\t}\n\n\t\tif ( pending.length < 1 ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tpending.push( this.parser.createNodeMesh( nodeIndex ) );\n\n\t\treturn Promise.all( pending ).then( results => {\n\n\t\t\tconst nodeObject = results.pop();\n\t\t\tconst meshes = nodeObject.isGroup ? nodeObject.children : [ nodeObject ];\n\t\t\tconst count = results[ 0 ].count; // All attribute counts should be same\n\t\t\tconst instancedMeshes = [];\n\n\t\t\tfor ( const mesh of meshes ) {\n\n\t\t\t\t// Temporal variables\n\t\t\t\tconst m = new Matrix4();\n\t\t\t\tconst p = new Vector3();\n\t\t\t\tconst q = new Quaternion();\n\t\t\t\tconst s = new Vector3( 1, 1, 1 );\n\n\t\t\t\tconst instancedMesh = new InstancedMesh( mesh.geometry, mesh.material, count );\n\n\t\t\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\t\t\tif ( attributes.TRANSLATION ) {\n\n\t\t\t\t\t\tp.fromBufferAttribute( attributes.TRANSLATION, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( attributes.ROTATION ) {\n\n\t\t\t\t\t\tq.fromBufferAttribute( attributes.ROTATION, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( attributes.SCALE ) {\n\n\t\t\t\t\t\ts.fromBufferAttribute( attributes.SCALE, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tinstancedMesh.setMatrixAt( i, m.compose( p, q, s ) );\n\n\t\t\t\t}\n\n\t\t\t\t// Add instance attributes to the geometry, excluding TRS.\n\t\t\t\tfor ( const attributeName in attributes ) {\n\n\t\t\t\t\tif ( attributeName !== 'TRANSLATION' &&\n\t\t\t\t\t\t attributeName !== 'ROTATION' &&\n\t\t\t\t\t\t attributeName !== 'SCALE' ) {\n\n\t\t\t\t\t\tmesh.geometry.setAttribute( attributeName, attributes[ attributeName ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// Just in case\n\t\t\t\tObject3D.prototype.copy.call( instancedMesh, mesh );\n\n\t\t\t\tthis.parser.assignFinalMaterial( instancedMesh );\n\n\t\t\t\tinstancedMeshes.push( instancedMesh );\n\n\t\t\t}\n\n\t\t\tif ( nodeObject.isGroup ) {\n\n\t\t\t\tnodeObject.clear();\n\n\t\t\t\tnodeObject.add( ... instancedMeshes );\n\n\t\t\t\treturn nodeObject;\n\n\t\t\t}\n\n\t\t\treturn instancedMeshes[ 0 ];\n\n\t\t} );\n\n\t}\n\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF';\nconst BINARY_EXTENSION_HEADER_LENGTH = 12;\nconst BINARY_EXTENSION_CHUNK_TYPES = { JSON: 0x4E4F534A, BIN: 0x004E4942 };\n\nclass GLTFBinaryExtension {\n\n\tconstructor( data ) {\n\n\t\tthis.name = EXTENSIONS.KHR_BINARY_GLTF;\n\t\tthis.content = null;\n\t\tthis.body = null;\n\n\t\tconst headerView = new DataView( data, 0, BINARY_EXTENSION_HEADER_LENGTH );\n\t\tconst textDecoder = new TextDecoder();\n\n\t\tthis.header = {\n\t\t\tmagic: textDecoder.decode( new Uint8Array( data.slice( 0, 4 ) ) ),\n\t\t\tversion: headerView.getUint32( 4, true ),\n\t\t\tlength: headerView.getUint32( 8, true )\n\t\t};\n\n\t\tif ( this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Unsupported glTF-Binary header.' );\n\n\t\t} else if ( this.header.version < 2.0 ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Legacy binary file detected.' );\n\n\t\t}\n\n\t\tconst chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;\n\t\tconst chunkView = new DataView( data, BINARY_EXTENSION_HEADER_LENGTH );\n\t\tlet chunkIndex = 0;\n\n\t\twhile ( chunkIndex < chunkContentsLength ) {\n\n\t\t\tconst chunkLength = chunkView.getUint32( chunkIndex, true );\n\t\t\tchunkIndex += 4;\n\n\t\t\tconst chunkType = chunkView.getUint32( chunkIndex, true );\n\t\t\tchunkIndex += 4;\n\n\t\t\tif ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON ) {\n\n\t\t\t\tconst contentArray = new Uint8Array( data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength );\n\t\t\t\tthis.content = textDecoder.decode( contentArray );\n\n\t\t\t} else if ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN ) {\n\n\t\t\t\tconst byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;\n\t\t\t\tthis.body = data.slice( byteOffset, byteOffset + chunkLength );\n\n\t\t\t}\n\n\t\t\t// Clients must ignore chunks with unknown types.\n\n\t\t\tchunkIndex += chunkLength;\n\n\t\t}\n\n\t\tif ( this.content === null ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: JSON content not found.' );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n */\nclass GLTFDracoMeshCompressionExtension {\n\n\tconstructor( json, dracoLoader ) {\n\n\t\tif ( ! dracoLoader ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: No DRACOLoader instance provided.' );\n\n\t\t}\n\n\t\tthis.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;\n\t\tthis.json = json;\n\t\tthis.dracoLoader = dracoLoader;\n\t\tthis.dracoLoader.preload();\n\n\t}\n\n\tdecodePrimitive( primitive, parser ) {\n\n\t\tconst json = this.json;\n\t\tconst dracoLoader = this.dracoLoader;\n\t\tconst bufferViewIndex = primitive.extensions[ this.name ].bufferView;\n\t\tconst gltfAttributeMap = primitive.extensions[ this.name ].attributes;\n\t\tconst threeAttributeMap = {};\n\t\tconst attributeNormalizedMap = {};\n\t\tconst attributeTypeMap = {};\n\n\t\tfor ( const attributeName in gltfAttributeMap ) {\n\n\t\t\tconst threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();\n\n\t\t\tthreeAttributeMap[ threeAttributeName ] = gltfAttributeMap[ attributeName ];\n\n\t\t}\n\n\t\tfor ( const attributeName in primitive.attributes ) {\n\n\t\t\tconst threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();\n\n\t\t\tif ( gltfAttributeMap[ attributeName ] !== undefined ) {\n\n\t\t\t\tconst accessorDef = json.accessors[ primitive.attributes[ attributeName ] ];\n\t\t\t\tconst componentType = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\n\t\t\t\tattributeTypeMap[ threeAttributeName ] = componentType.name;\n\t\t\t\tattributeNormalizedMap[ threeAttributeName ] = accessorDef.normalized === true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn parser.getDependency( 'bufferView', bufferViewIndex ).then( function ( bufferView ) {\n\n\t\t\treturn new Promise( function ( resolve ) {\n\n\t\t\t\tdracoLoader.decodeDracoFile( bufferView, function ( geometry ) {\n\n\t\t\t\t\tfor ( const attributeName in geometry.attributes ) {\n\n\t\t\t\t\t\tconst attribute = geometry.attributes[ attributeName ];\n\t\t\t\t\t\tconst normalized = attributeNormalizedMap[ attributeName ];\n\n\t\t\t\t\t\tif ( normalized !== undefined ) attribute.normalized = normalized;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tresolve( geometry );\n\n\t\t\t\t}, threeAttributeMap, attributeTypeMap );\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n */\nclass GLTFTextureTransformExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;\n\n\t}\n\n\textendTexture( texture, transform ) {\n\n\t\tif ( ( transform.texCoord === undefined || transform.texCoord === texture.channel )\n\t\t\t&& transform.offset === undefined\n\t\t\t&& transform.rotation === undefined\n\t\t\t&& transform.scale === undefined ) {\n\n\t\t\t// See https://github.com/mrdoob/three.js/issues/21819.\n\t\t\treturn texture;\n\n\t\t}\n\n\t\ttexture = texture.clone();\n\n\t\tif ( transform.texCoord !== undefined ) {\n\n\t\t\ttexture.channel = transform.texCoord;\n\n\t\t}\n\n\t\tif ( transform.offset !== undefined ) {\n\n\t\t\ttexture.offset.fromArray( transform.offset );\n\n\t\t}\n\n\t\tif ( transform.rotation !== undefined ) {\n\n\t\t\ttexture.rotation = transform.rotation;\n\n\t\t}\n\n\t\tif ( transform.scale !== undefined ) {\n\n\t\t\ttexture.repeat.fromArray( transform.scale );\n\n\t\t}\n\n\t\ttexture.needsUpdate = true;\n\n\t\treturn texture;\n\n\t}\n\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n */\nclass GLTFMeshQuantizationExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_MESH_QUANTIZATION;\n\n\t}\n\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n\n\tconstructor( parameterPositions, sampleValues, sampleSize, resultBuffer ) {\n\n\t\tsuper( parameterPositions, sampleValues, sampleSize, resultBuffer );\n\n\t}\n\n\tcopySampleValue_( index ) {\n\n\t\t// Copies a sample value to the result buffer. See description of glTF\n\t\t// CUBICSPLINE values layout in interpolate_() function below.\n\n\t\tconst result = this.resultBuffer,\n\t\t\tvalues = this.sampleValues,\n\t\t\tvalueSize = this.valueSize,\n\t\t\toffset = index * valueSize * 3 + valueSize;\n\n\t\tfor ( let i = 0; i !== valueSize; i ++ ) {\n\n\t\t\tresult[ i ] = values[ offset + i ];\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tinterpolate_( i1, t0, t, t1 ) {\n\n\t\tconst result = this.resultBuffer;\n\t\tconst values = this.sampleValues;\n\t\tconst stride = this.valueSize;\n\n\t\tconst stride2 = stride * 2;\n\t\tconst stride3 = stride * 3;\n\n\t\tconst td = t1 - t0;\n\n\t\tconst p = ( t - t0 ) / td;\n\t\tconst pp = p * p;\n\t\tconst ppp = pp * p;\n\n\t\tconst offset1 = i1 * stride3;\n\t\tconst offset0 = offset1 - stride3;\n\n\t\tconst s2 = - 2 * ppp + 3 * pp;\n\t\tconst s3 = ppp - pp;\n\t\tconst s0 = 1 - s2;\n\t\tconst s1 = s3 - pp + p;\n\n\t\t// Layout of keyframe output values for CUBICSPLINE animations:\n\t\t//   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n\t\tfor ( let i = 0; i !== stride; i ++ ) {\n\n\t\t\tconst p0 = values[ offset0 + i + stride ]; // splineVertex_k\n\t\t\tconst m0 = values[ offset0 + i + stride2 ] * td; // outTangent_k * (t_k+1 - t_k)\n\t\t\tconst p1 = values[ offset1 + i + stride ]; // splineVertex_k+1\n\t\t\tconst m1 = values[ offset1 + i ] * td; // inTangent_k+1 * (t_k+1 - t_k)\n\n\t\t\tresult[ i ] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n}\n\nconst _q = new Quaternion();\n\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n\n\tinterpolate_( i1, t0, t, t1 ) {\n\n\t\tconst result = super.interpolate_( i1, t0, t, t1 );\n\n\t\t_q.fromArray( result ).normalize().toArray( result );\n\n\t\treturn result;\n\n\t}\n\n}\n\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n\tFLOAT: 5126,\n\t//FLOAT_MAT2: 35674,\n\tFLOAT_MAT3: 35675,\n\tFLOAT_MAT4: 35676,\n\tFLOAT_VEC2: 35664,\n\tFLOAT_VEC3: 35665,\n\tFLOAT_VEC4: 35666,\n\tLINEAR: 9729,\n\tREPEAT: 10497,\n\tSAMPLER_2D: 35678,\n\tPOINTS: 0,\n\tLINES: 1,\n\tLINE_LOOP: 2,\n\tLINE_STRIP: 3,\n\tTRIANGLES: 4,\n\tTRIANGLE_STRIP: 5,\n\tTRIANGLE_FAN: 6,\n\tUNSIGNED_BYTE: 5121,\n\tUNSIGNED_SHORT: 5123\n};\n\nconst WEBGL_COMPONENT_TYPES = {\n\t5120: Int8Array,\n\t5121: Uint8Array,\n\t5122: Int16Array,\n\t5123: Uint16Array,\n\t5125: Uint32Array,\n\t5126: Float32Array\n};\n\nconst WEBGL_FILTERS = {\n\t9728: NearestFilter,\n\t9729: LinearFilter,\n\t9984: NearestMipmapNearestFilter,\n\t9985: LinearMipmapNearestFilter,\n\t9986: NearestMipmapLinearFilter,\n\t9987: LinearMipmapLinearFilter\n};\n\nconst WEBGL_WRAPPINGS = {\n\t33071: ClampToEdgeWrapping,\n\t33648: MirroredRepeatWrapping,\n\t10497: RepeatWrapping\n};\n\nconst WEBGL_TYPE_SIZES = {\n\t'SCALAR': 1,\n\t'VEC2': 2,\n\t'VEC3': 3,\n\t'VEC4': 4,\n\t'MAT2': 4,\n\t'MAT3': 9,\n\t'MAT4': 16\n};\n\nconst ATTRIBUTES = {\n\tPOSITION: 'position',\n\tNORMAL: 'normal',\n\tTANGENT: 'tangent',\n\tTEXCOORD_0: 'uv',\n\tTEXCOORD_1: 'uv1',\n\tTEXCOORD_2: 'uv2',\n\tTEXCOORD_3: 'uv3',\n\tCOLOR_0: 'color',\n\tWEIGHTS_0: 'skinWeight',\n\tJOINTS_0: 'skinIndex',\n};\n\nconst PATH_PROPERTIES = {\n\tscale: 'scale',\n\ttranslation: 'position',\n\trotation: 'quaternion',\n\tweights: 'morphTargetInfluences'\n};\n\nconst INTERPOLATION = {\n\tCUBICSPLINE: undefined, // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n\t\t                        // keyframe track will be initialized with a default interpolation type, then modified.\n\tLINEAR: InterpolateLinear,\n\tSTEP: InterpolateDiscrete\n};\n\nconst ALPHA_MODES = {\n\tOPAQUE: 'OPAQUE',\n\tMASK: 'MASK',\n\tBLEND: 'BLEND'\n};\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n */\nfunction createDefaultMaterial( cache ) {\n\n\tif ( cache[ 'DefaultMaterial' ] === undefined ) {\n\n\t\tcache[ 'DefaultMaterial' ] = new MeshStandardMaterial( {\n\t\t\tcolor: 0xFFFFFF,\n\t\t\temissive: 0x000000,\n\t\t\tmetalness: 1,\n\t\t\troughness: 1,\n\t\t\ttransparent: false,\n\t\t\tdepthTest: true,\n\t\t\tside: FrontSide\n\t\t} );\n\n\t}\n\n\treturn cache[ 'DefaultMaterial' ];\n\n}\n\nfunction addUnknownExtensionsToUserData( knownExtensions, object, objectDef ) {\n\n\t// Add unknown glTF extensions to an object's userData.\n\n\tfor ( const name in objectDef.extensions ) {\n\n\t\tif ( knownExtensions[ name ] === undefined ) {\n\n\t\t\tobject.userData.gltfExtensions = object.userData.gltfExtensions || {};\n\t\t\tobject.userData.gltfExtensions[ name ] = objectDef.extensions[ name ];\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * @param {Object3D|Material|BufferGeometry} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData( object, gltfDef ) {\n\n\tif ( gltfDef.extras !== undefined ) {\n\n\t\tif ( typeof gltfDef.extras === 'object' ) {\n\n\t\t\tObject.assign( object.userData, gltfDef.extras );\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets( geometry, targets, parser ) {\n\n\tlet hasMorphPosition = false;\n\tlet hasMorphNormal = false;\n\tlet hasMorphColor = false;\n\n\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\tconst target = targets[ i ];\n\n\t\tif ( target.POSITION !== undefined ) hasMorphPosition = true;\n\t\tif ( target.NORMAL !== undefined ) hasMorphNormal = true;\n\t\tif ( target.COLOR_0 !== undefined ) hasMorphColor = true;\n\n\t\tif ( hasMorphPosition && hasMorphNormal && hasMorphColor ) break;\n\n\t}\n\n\tif ( ! hasMorphPosition && ! hasMorphNormal && ! hasMorphColor ) return Promise.resolve( geometry );\n\n\tconst pendingPositionAccessors = [];\n\tconst pendingNormalAccessors = [];\n\tconst pendingColorAccessors = [];\n\n\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\tconst target = targets[ i ];\n\n\t\tif ( hasMorphPosition ) {\n\n\t\t\tconst pendingAccessor = target.POSITION !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.POSITION )\n\t\t\t\t: geometry.attributes.position;\n\n\t\t\tpendingPositionAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t\tif ( hasMorphNormal ) {\n\n\t\t\tconst pendingAccessor = target.NORMAL !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.NORMAL )\n\t\t\t\t: geometry.attributes.normal;\n\n\t\t\tpendingNormalAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t\tif ( hasMorphColor ) {\n\n\t\t\tconst pendingAccessor = target.COLOR_0 !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.COLOR_0 )\n\t\t\t\t: geometry.attributes.color;\n\n\t\t\tpendingColorAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t}\n\n\treturn Promise.all( [\n\t\tPromise.all( pendingPositionAccessors ),\n\t\tPromise.all( pendingNormalAccessors ),\n\t\tPromise.all( pendingColorAccessors )\n\t] ).then( function ( accessors ) {\n\n\t\tconst morphPositions = accessors[ 0 ];\n\t\tconst morphNormals = accessors[ 1 ];\n\t\tconst morphColors = accessors[ 2 ];\n\n\t\tif ( hasMorphPosition ) geometry.morphAttributes.position = morphPositions;\n\t\tif ( hasMorphNormal ) geometry.morphAttributes.normal = morphNormals;\n\t\tif ( hasMorphColor ) geometry.morphAttributes.color = morphColors;\n\t\tgeometry.morphTargetsRelative = true;\n\n\t\treturn geometry;\n\n\t} );\n\n}\n\n/**\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets( mesh, meshDef ) {\n\n\tmesh.updateMorphTargets();\n\n\tif ( meshDef.weights !== undefined ) {\n\n\t\tfor ( let i = 0, il = meshDef.weights.length; i < il; i ++ ) {\n\n\t\t\tmesh.morphTargetInfluences[ i ] = meshDef.weights[ i ];\n\n\t\t}\n\n\t}\n\n\t// .extras has user-defined data, so check that .extras.targetNames is an array.\n\tif ( meshDef.extras && Array.isArray( meshDef.extras.targetNames ) ) {\n\n\t\tconst targetNames = meshDef.extras.targetNames;\n\n\t\tif ( mesh.morphTargetInfluences.length === targetNames.length ) {\n\n\t\t\tmesh.morphTargetDictionary = {};\n\n\t\t\tfor ( let i = 0, il = targetNames.length; i < il; i ++ ) {\n\n\t\t\t\tmesh.morphTargetDictionary[ targetNames[ i ] ] = i;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.' );\n\n\t\t}\n\n\t}\n\n}\n\nfunction createPrimitiveKey( primitiveDef ) {\n\n\tlet geometryKey;\n\n\tconst dracoExtension = primitiveDef.extensions && primitiveDef.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ];\n\n\tif ( dracoExtension ) {\n\n\t\tgeometryKey = 'draco:' + dracoExtension.bufferView\n\t\t\t\t+ ':' + dracoExtension.indices\n\t\t\t\t+ ':' + createAttributesKey( dracoExtension.attributes );\n\n\t} else {\n\n\t\tgeometryKey = primitiveDef.indices + ':' + createAttributesKey( primitiveDef.attributes ) + ':' + primitiveDef.mode;\n\n\t}\n\n\tif ( primitiveDef.targets !== undefined ) {\n\n\t\tfor ( let i = 0, il = primitiveDef.targets.length; i < il; i ++ ) {\n\n\t\t\tgeometryKey += ':' + createAttributesKey( primitiveDef.targets[ i ] );\n\n\t\t}\n\n\t}\n\n\treturn geometryKey;\n\n}\n\nfunction createAttributesKey( attributes ) {\n\n\tlet attributesKey = '';\n\n\tconst keys = Object.keys( attributes ).sort();\n\n\tfor ( let i = 0, il = keys.length; i < il; i ++ ) {\n\n\t\tattributesKey += keys[ i ] + ':' + attributes[ keys[ i ] ] + ';';\n\n\t}\n\n\treturn attributesKey;\n\n}\n\nfunction getNormalizedComponentScale( constructor ) {\n\n\t// Reference:\n\t// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n\tswitch ( constructor ) {\n\n\t\tcase Int8Array:\n\t\t\treturn 1 / 127;\n\n\t\tcase Uint8Array:\n\t\t\treturn 1 / 255;\n\n\t\tcase Int16Array:\n\t\t\treturn 1 / 32767;\n\n\t\tcase Uint16Array:\n\t\t\treturn 1 / 65535;\n\n\t\tdefault:\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Unsupported normalized accessor component type.' );\n\n\t}\n\n}\n\nfunction getImageURIMimeType( uri ) {\n\n\tif ( uri.search( /\\.jpe?g($|\\?)/i ) > 0 || uri.search( /^data\\:image\\/jpeg/ ) === 0 ) return 'image/jpeg';\n\tif ( uri.search( /\\.webp($|\\?)/i ) > 0 || uri.search( /^data\\:image\\/webp/ ) === 0 ) return 'image/webp';\n\n\treturn 'image/png';\n\n}\n\nconst _identityMatrix = new Matrix4();\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n\n\tconstructor( json = {}, options = {} ) {\n\n\t\tthis.json = json;\n\t\tthis.extensions = {};\n\t\tthis.plugins = {};\n\t\tthis.options = options;\n\n\t\t// loader object cache\n\t\tthis.cache = new GLTFRegistry();\n\n\t\t// associations between Three.js objects and glTF elements\n\t\tthis.associations = new Map();\n\n\t\t// BufferGeometry caching\n\t\tthis.primitiveCache = {};\n\n\t\t// Node cache\n\t\tthis.nodeCache = {};\n\n\t\t// Object3D instance caches\n\t\tthis.meshCache = { refs: {}, uses: {} };\n\t\tthis.cameraCache = { refs: {}, uses: {} };\n\t\tthis.lightCache = { refs: {}, uses: {} };\n\n\t\tthis.sourceCache = {};\n\t\tthis.textureCache = {};\n\n\t\t// Track node names, to ensure no duplicates\n\t\tthis.nodeNamesUsed = {};\n\n\t\t// Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n\t\t// expensive work of uploading a texture to the GPU off the main thread.\n\n\t\tlet isSafari = false;\n\t\tlet isFirefox = false;\n\t\tlet firefoxVersion = - 1;\n\n\t\tif ( typeof navigator !== 'undefined' ) {\n\n\t\t\tisSafari = /^((?!chrome|android).)*safari/i.test( navigator.userAgent ) === true;\n\t\t\tisFirefox = navigator.userAgent.indexOf( 'Firefox' ) > - 1;\n\t\t\tfirefoxVersion = isFirefox ? navigator.userAgent.match( /Firefox\\/([0-9]+)\\./ )[ 1 ] : - 1;\n\n\t\t}\n\n\t\tif ( typeof createImageBitmap === 'undefined' || isSafari || ( isFirefox && firefoxVersion < 98 ) ) {\n\n\t\t\tthis.textureLoader = new TextureLoader( this.options.manager );\n\n\t\t} else {\n\n\t\t\tthis.textureLoader = new ImageBitmapLoader( this.options.manager );\n\n\t\t}\n\n\t\tthis.textureLoader.setCrossOrigin( this.options.crossOrigin );\n\t\tthis.textureLoader.setRequestHeader( this.options.requestHeader );\n\n\t\tthis.fileLoader = new FileLoader( this.options.manager );\n\t\tthis.fileLoader.setResponseType( 'arraybuffer' );\n\n\t\tif ( this.options.crossOrigin === 'use-credentials' ) {\n\n\t\t\tthis.fileLoader.setWithCredentials( true );\n\n\t\t}\n\n\t}\n\n\tsetExtensions( extensions ) {\n\n\t\tthis.extensions = extensions;\n\n\t}\n\n\tsetPlugins( plugins ) {\n\n\t\tthis.plugins = plugins;\n\n\t}\n\n\tparse( onLoad, onError ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\n\t\t// Clear the loader cache\n\t\tthis.cache.removeAll();\n\t\tthis.nodeCache = {};\n\n\t\t// Mark the special nodes/meshes in json for efficient parse\n\t\tthis._invokeAll( function ( ext ) {\n\n\t\t\treturn ext._markDefs && ext._markDefs();\n\n\t\t} );\n\n\t\tPromise.all( this._invokeAll( function ( ext ) {\n\n\t\t\treturn ext.beforeRoot && ext.beforeRoot();\n\n\t\t} ) ).then( function () {\n\n\t\t\treturn Promise.all( [\n\n\t\t\t\tparser.getDependencies( 'scene' ),\n\t\t\t\tparser.getDependencies( 'animation' ),\n\t\t\t\tparser.getDependencies( 'camera' ),\n\n\t\t\t] );\n\n\t\t} ).then( function ( dependencies ) {\n\n\t\t\tconst result = {\n\t\t\t\tscene: dependencies[ 0 ][ json.scene || 0 ],\n\t\t\t\tscenes: dependencies[ 0 ],\n\t\t\t\tanimations: dependencies[ 1 ],\n\t\t\t\tcameras: dependencies[ 2 ],\n\t\t\t\tasset: json.asset,\n\t\t\t\tparser: parser,\n\t\t\t\tuserData: {}\n\t\t\t};\n\n\t\t\taddUnknownExtensionsToUserData( extensions, result, json );\n\n\t\t\tassignExtrasToUserData( result, json );\n\n\t\t\tPromise.all( parser._invokeAll( function ( ext ) {\n\n\t\t\t\treturn ext.afterRoot && ext.afterRoot( result );\n\n\t\t\t} ) ).then( function () {\n\n\t\t\t\tonLoad( result );\n\n\t\t\t} );\n\n\t\t} ).catch( onError );\n\n\t}\n\n\t/**\n\t * Marks the special nodes/meshes in json for efficient parse.\n\t */\n\t_markDefs() {\n\n\t\tconst nodeDefs = this.json.nodes || [];\n\t\tconst skinDefs = this.json.skins || [];\n\t\tconst meshDefs = this.json.meshes || [];\n\n\t\t// Nothing in the node definition indicates whether it is a Bone or an\n\t\t// Object3D. Use the skins' joint references to mark bones.\n\t\tfor ( let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex ++ ) {\n\n\t\t\tconst joints = skinDefs[ skinIndex ].joints;\n\n\t\t\tfor ( let i = 0, il = joints.length; i < il; i ++ ) {\n\n\t\t\t\tnodeDefs[ joints[ i ] ].isBone = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Iterate over all nodes, marking references to shared resources,\n\t\t// as well as skeleton joints.\n\t\tfor ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {\n\n\t\t\tconst nodeDef = nodeDefs[ nodeIndex ];\n\n\t\t\tif ( nodeDef.mesh !== undefined ) {\n\n\t\t\t\tthis._addNodeRef( this.meshCache, nodeDef.mesh );\n\n\t\t\t\t// Nothing in the mesh definition indicates whether it is\n\t\t\t\t// a SkinnedMesh or Mesh. Use the node's mesh reference\n\t\t\t\t// to mark SkinnedMesh if node has skin.\n\t\t\t\tif ( nodeDef.skin !== undefined ) {\n\n\t\t\t\t\tmeshDefs[ nodeDef.mesh ].isSkinnedMesh = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( nodeDef.camera !== undefined ) {\n\n\t\t\t\tthis._addNodeRef( this.cameraCache, nodeDef.camera );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Counts references to shared node / Object3D resources. These resources\n\t * can be reused, or \"instantiated\", at multiple nodes in the scene\n\t * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n\t * be marked. Non-scenegraph resources (like Materials, Geometries, and\n\t * Textures) can be reused directly and are not marked here.\n\t *\n\t * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n\t */\n\t_addNodeRef( cache, index ) {\n\n\t\tif ( index === undefined ) return;\n\n\t\tif ( cache.refs[ index ] === undefined ) {\n\n\t\t\tcache.refs[ index ] = cache.uses[ index ] = 0;\n\n\t\t}\n\n\t\tcache.refs[ index ] ++;\n\n\t}\n\n\t/** Returns a reference to a shared resource, cloning it if necessary. */\n\t_getNodeRef( cache, index, object ) {\n\n\t\tif ( cache.refs[ index ] <= 1 ) return object;\n\n\t\tconst ref = object.clone();\n\n\t\t// Propagates mappings to the cloned object, prevents mappings on the\n\t\t// original object from being lost.\n\t\tconst updateMappings = ( original, clone ) => {\n\n\t\t\tconst mappings = this.associations.get( original );\n\t\t\tif ( mappings != null ) {\n\n\t\t\t\tthis.associations.set( clone, mappings );\n\n\t\t\t}\n\n\t\t\tfor ( const [ i, child ] of original.children.entries() ) {\n\n\t\t\t\tupdateMappings( child, clone.children[ i ] );\n\n\t\t\t}\n\n\t\t};\n\n\t\tupdateMappings( object, ref );\n\n\t\tref.name += '_instance_' + ( cache.uses[ index ] ++ );\n\n\t\treturn ref;\n\n\t}\n\n\t_invokeOne( func ) {\n\n\t\tconst extensions = Object.values( this.plugins );\n\t\textensions.push( this );\n\n\t\tfor ( let i = 0; i < extensions.length; i ++ ) {\n\n\t\t\tconst result = func( extensions[ i ] );\n\n\t\t\tif ( result ) return result;\n\n\t\t}\n\n\t\treturn null;\n\n\t}\n\n\t_invokeAll( func ) {\n\n\t\tconst extensions = Object.values( this.plugins );\n\t\textensions.unshift( this );\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0; i < extensions.length; i ++ ) {\n\n\t\t\tconst result = func( extensions[ i ] );\n\n\t\t\tif ( result ) pending.push( result );\n\n\t\t}\n\n\t\treturn pending;\n\n\t}\n\n\t/**\n\t * Requests the specified dependency asynchronously, with caching.\n\t * @param {string} type\n\t * @param {number} index\n\t * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n\t */\n\tgetDependency( type, index ) {\n\n\t\tconst cacheKey = type + ':' + index;\n\t\tlet dependency = this.cache.get( cacheKey );\n\n\t\tif ( ! dependency ) {\n\n\t\t\tswitch ( type ) {\n\n\t\t\t\tcase 'scene':\n\t\t\t\t\tdependency = this.loadScene( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'node':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadNode && ext.loadNode( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'mesh':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadMesh && ext.loadMesh( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'accessor':\n\t\t\t\t\tdependency = this.loadAccessor( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'bufferView':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadBufferView && ext.loadBufferView( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'buffer':\n\t\t\t\t\tdependency = this.loadBuffer( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'material':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadMaterial && ext.loadMaterial( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'texture':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadTexture && ext.loadTexture( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'skin':\n\t\t\t\t\tdependency = this.loadSkin( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'animation':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadAnimation && ext.loadAnimation( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'camera':\n\t\t\t\t\tdependency = this.loadCamera( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext != this && ext.getDependency && ext.getDependency( type, index );\n\n\t\t\t\t\t} );\n\n\t\t\t\t\tif ( ! dependency ) {\n\n\t\t\t\t\t\tthrow new Error( 'Unknown type: ' + type );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tthis.cache.add( cacheKey, dependency );\n\n\t\t}\n\n\t\treturn dependency;\n\n\t}\n\n\t/**\n\t * Requests all dependencies of the specified type asynchronously, with caching.\n\t * @param {string} type\n\t * @return {Promise<Array<Object>>}\n\t */\n\tgetDependencies( type ) {\n\n\t\tlet dependencies = this.cache.get( type );\n\n\t\tif ( ! dependencies ) {\n\n\t\t\tconst parser = this;\n\t\t\tconst defs = this.json[ type + ( type === 'mesh' ? 'es' : 's' ) ] || [];\n\n\t\t\tdependencies = Promise.all( defs.map( function ( def, index ) {\n\n\t\t\t\treturn parser.getDependency( type, index );\n\n\t\t\t} ) );\n\n\t\t\tthis.cache.add( type, dependencies );\n\n\t\t}\n\n\t\treturn dependencies;\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n\t * @param {number} bufferIndex\n\t * @return {Promise<ArrayBuffer>}\n\t */\n\tloadBuffer( bufferIndex ) {\n\n\t\tconst bufferDef = this.json.buffers[ bufferIndex ];\n\t\tconst loader = this.fileLoader;\n\n\t\tif ( bufferDef.type && bufferDef.type !== 'arraybuffer' ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.' );\n\n\t\t}\n\n\t\t// If present, GLB container is required to be the first buffer.\n\t\tif ( bufferDef.uri === undefined && bufferIndex === 0 ) {\n\n\t\t\treturn Promise.resolve( this.extensions[ EXTENSIONS.KHR_BINARY_GLTF ].body );\n\n\t\t}\n\n\t\tconst options = this.options;\n\n\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\tloader.load( LoaderUtils.resolveURL( bufferDef.uri, options.path ), resolve, undefined, function () {\n\n\t\t\t\treject( new Error( 'THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".' ) );\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n\t * @param {number} bufferViewIndex\n\t * @return {Promise<ArrayBuffer>}\n\t */\n\tloadBufferView( bufferViewIndex ) {\n\n\t\tconst bufferViewDef = this.json.bufferViews[ bufferViewIndex ];\n\n\t\treturn this.getDependency( 'buffer', bufferViewDef.buffer ).then( function ( buffer ) {\n\n\t\t\tconst byteLength = bufferViewDef.byteLength || 0;\n\t\t\tconst byteOffset = bufferViewDef.byteOffset || 0;\n\t\t\treturn buffer.slice( byteOffset, byteOffset + byteLength );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n\t * @param {number} accessorIndex\n\t * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n\t */\n\tloadAccessor( accessorIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\n\t\tconst accessorDef = this.json.accessors[ accessorIndex ];\n\n\t\tif ( accessorDef.bufferView === undefined && accessorDef.sparse === undefined ) {\n\n\t\t\tconst itemSize = WEBGL_TYPE_SIZES[ accessorDef.type ];\n\t\t\tconst TypedArray = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\t\t\tconst normalized = accessorDef.normalized === true;\n\n\t\t\tconst array = new TypedArray( accessorDef.count * itemSize );\n\t\t\treturn Promise.resolve( new BufferAttribute( array, itemSize, normalized ) );\n\n\t\t}\n\n\t\tconst pendingBufferViews = [];\n\n\t\tif ( accessorDef.bufferView !== undefined ) {\n\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.bufferView ) );\n\n\t\t} else {\n\n\t\t\tpendingBufferViews.push( null );\n\n\t\t}\n\n\t\tif ( accessorDef.sparse !== undefined ) {\n\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.indices.bufferView ) );\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.values.bufferView ) );\n\n\t\t}\n\n\t\treturn Promise.all( pendingBufferViews ).then( function ( bufferViews ) {\n\n\t\t\tconst bufferView = bufferViews[ 0 ];\n\n\t\t\tconst itemSize = WEBGL_TYPE_SIZES[ accessorDef.type ];\n\t\t\tconst TypedArray = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\n\t\t\t// For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n\t\t\tconst elementBytes = TypedArray.BYTES_PER_ELEMENT;\n\t\t\tconst itemBytes = elementBytes * itemSize;\n\t\t\tconst byteOffset = accessorDef.byteOffset || 0;\n\t\t\tconst byteStride = accessorDef.bufferView !== undefined ? json.bufferViews[ accessorDef.bufferView ].byteStride : undefined;\n\t\t\tconst normalized = accessorDef.normalized === true;\n\t\t\tlet array, bufferAttribute;\n\n\t\t\t// The buffer is not interleaved if the stride is the item size in bytes.\n\t\t\tif ( byteStride && byteStride !== itemBytes ) {\n\n\t\t\t\t// Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n\t\t\t\t// This makes sure that IBA.count reflects accessor.count properly\n\t\t\t\tconst ibSlice = Math.floor( byteOffset / byteStride );\n\t\t\t\tconst ibCacheKey = 'InterleavedBuffer:' + accessorDef.bufferView + ':' + accessorDef.componentType + ':' + ibSlice + ':' + accessorDef.count;\n\t\t\t\tlet ib = parser.cache.get( ibCacheKey );\n\n\t\t\t\tif ( ! ib ) {\n\n\t\t\t\t\tarray = new TypedArray( bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes );\n\n\t\t\t\t\t// Integer parameters to IB/IBA are in array elements, not bytes.\n\t\t\t\t\tib = new InterleavedBuffer( array, byteStride / elementBytes );\n\n\t\t\t\t\tparser.cache.add( ibCacheKey, ib );\n\n\t\t\t\t}\n\n\t\t\t\tbufferAttribute = new InterleavedBufferAttribute( ib, itemSize, ( byteOffset % byteStride ) / elementBytes, normalized );\n\n\t\t\t} else {\n\n\t\t\t\tif ( bufferView === null ) {\n\n\t\t\t\t\tarray = new TypedArray( accessorDef.count * itemSize );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tarray = new TypedArray( bufferView, byteOffset, accessorDef.count * itemSize );\n\n\t\t\t\t}\n\n\t\t\t\tbufferAttribute = new BufferAttribute( array, itemSize, normalized );\n\n\t\t\t}\n\n\t\t\t// https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n\t\t\tif ( accessorDef.sparse !== undefined ) {\n\n\t\t\t\tconst itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;\n\t\t\t\tconst TypedArrayIndices = WEBGL_COMPONENT_TYPES[ accessorDef.sparse.indices.componentType ];\n\n\t\t\t\tconst byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;\n\t\t\t\tconst byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;\n\n\t\t\t\tconst sparseIndices = new TypedArrayIndices( bufferViews[ 1 ], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices );\n\t\t\t\tconst sparseValues = new TypedArray( bufferViews[ 2 ], byteOffsetValues, accessorDef.sparse.count * itemSize );\n\n\t\t\t\tif ( bufferView !== null ) {\n\n\t\t\t\t\t// Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n\t\t\t\t\tbufferAttribute = new BufferAttribute( bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized );\n\n\t\t\t\t}\n\n\t\t\t\tfor ( let i = 0, il = sparseIndices.length; i < il; i ++ ) {\n\n\t\t\t\t\tconst index = sparseIndices[ i ];\n\n\t\t\t\t\tbufferAttribute.setX( index, sparseValues[ i * itemSize ] );\n\t\t\t\t\tif ( itemSize >= 2 ) bufferAttribute.setY( index, sparseValues[ i * itemSize + 1 ] );\n\t\t\t\t\tif ( itemSize >= 3 ) bufferAttribute.setZ( index, sparseValues[ i * itemSize + 2 ] );\n\t\t\t\t\tif ( itemSize >= 4 ) bufferAttribute.setW( index, sparseValues[ i * itemSize + 3 ] );\n\t\t\t\t\tif ( itemSize >= 5 ) throw new Error( 'THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.' );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn bufferAttribute;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n\t * @param {number} textureIndex\n\t * @return {Promise<THREE.Texture|null>}\n\t */\n\tloadTexture( textureIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst options = this.options;\n\t\tconst textureDef = json.textures[ textureIndex ];\n\t\tconst sourceIndex = textureDef.source;\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tlet loader = this.textureLoader;\n\n\t\tif ( sourceDef.uri ) {\n\n\t\t\tconst handler = options.manager.getHandler( sourceDef.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn this.loadTextureImage( textureIndex, sourceIndex, loader );\n\n\t}\n\n\tloadTextureImage( textureIndex, sourceIndex, loader ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tconst cacheKey = ( sourceDef.uri || sourceDef.bufferView ) + ':' + textureDef.sampler;\n\n\t\tif ( this.textureCache[ cacheKey ] ) {\n\n\t\t\t// See https://github.com/mrdoob/three.js/issues/21559.\n\t\t\treturn this.textureCache[ cacheKey ];\n\n\t\t}\n\n\t\tconst promise = this.loadImageSource( sourceIndex, loader ).then( function ( texture ) {\n\n\t\t\ttexture.flipY = false;\n\n\t\t\ttexture.name = textureDef.name || sourceDef.name || '';\n\n\t\t\tif ( texture.name === '' && typeof sourceDef.uri === 'string' && sourceDef.uri.startsWith( 'data:image/' ) === false ) {\n\n\t\t\t\ttexture.name = sourceDef.uri;\n\n\t\t\t}\n\n\t\t\tconst samplers = json.samplers || {};\n\t\t\tconst sampler = samplers[ textureDef.sampler ] || {};\n\n\t\t\ttexture.magFilter = WEBGL_FILTERS[ sampler.magFilter ] || LinearFilter;\n\t\t\ttexture.minFilter = WEBGL_FILTERS[ sampler.minFilter ] || LinearMipmapLinearFilter;\n\t\t\ttexture.wrapS = WEBGL_WRAPPINGS[ sampler.wrapS ] || RepeatWrapping;\n\t\t\ttexture.wrapT = WEBGL_WRAPPINGS[ sampler.wrapT ] || RepeatWrapping;\n\n\t\t\tparser.associations.set( texture, { textures: textureIndex } );\n\n\t\t\treturn texture;\n\n\t\t} ).catch( function () {\n\n\t\t\treturn null;\n\n\t\t} );\n\n\t\tthis.textureCache[ cacheKey ] = promise;\n\n\t\treturn promise;\n\n\t}\n\n\tloadImageSource( sourceIndex, loader ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst options = this.options;\n\n\t\tif ( this.sourceCache[ sourceIndex ] !== undefined ) {\n\n\t\t\treturn this.sourceCache[ sourceIndex ].then( ( texture ) => texture.clone() );\n\n\t\t}\n\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tconst URL = self.URL || self.webkitURL;\n\n\t\tlet sourceURI = sourceDef.uri || '';\n\t\tlet isObjectURL = false;\n\n\t\tif ( sourceDef.bufferView !== undefined ) {\n\n\t\t\t// Load binary image data from bufferView, if provided.\n\n\t\t\tsourceURI = parser.getDependency( 'bufferView', sourceDef.bufferView ).then( function ( bufferView ) {\n\n\t\t\t\tisObjectURL = true;\n\t\t\t\tconst blob = new Blob( [ bufferView ], { type: sourceDef.mimeType } );\n\t\t\t\tsourceURI = URL.createObjectURL( blob );\n\t\t\t\treturn sourceURI;\n\n\t\t\t} );\n\n\t\t} else if ( sourceDef.uri === undefined ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView' );\n\n\t\t}\n\n\t\tconst promise = Promise.resolve( sourceURI ).then( function ( sourceURI ) {\n\n\t\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\t\tlet onLoad = resolve;\n\n\t\t\t\tif ( loader.isImageBitmapLoader === true ) {\n\n\t\t\t\t\tonLoad = function ( imageBitmap ) {\n\n\t\t\t\t\t\tconst texture = new Texture( imageBitmap );\n\t\t\t\t\t\ttexture.needsUpdate = true;\n\n\t\t\t\t\t\tresolve( texture );\n\n\t\t\t\t\t};\n\n\t\t\t\t}\n\n\t\t\t\tloader.load( LoaderUtils.resolveURL( sourceURI, options.path ), onLoad, undefined, reject );\n\n\t\t\t} );\n\n\t\t} ).then( function ( texture ) {\n\n\t\t\t// Clean up resources and configure Texture.\n\n\t\t\tif ( isObjectURL === true ) {\n\n\t\t\t\tURL.revokeObjectURL( sourceURI );\n\n\t\t\t}\n\n\t\t\ttexture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType( sourceDef.uri );\n\n\t\t\treturn texture;\n\n\t\t} ).catch( function ( error ) {\n\n\t\t\tconsole.error( 'THREE.GLTFLoader: Couldn\\'t load texture', sourceURI );\n\t\t\tthrow error;\n\n\t\t} );\n\n\t\tthis.sourceCache[ sourceIndex ] = promise;\n\t\treturn promise;\n\n\t}\n\n\t/**\n\t * Asynchronously assigns a texture to the given material parameters.\n\t * @param {Object} materialParams\n\t * @param {string} mapName\n\t * @param {Object} mapDef\n\t * @return {Promise<Texture>}\n\t */\n\tassignTexture( materialParams, mapName, mapDef, colorSpace ) {\n\n\t\tconst parser = this;\n\n\t\treturn this.getDependency( 'texture', mapDef.index ).then( function ( texture ) {\n\n\t\t\tif ( ! texture ) return null;\n\n\t\t\tif ( mapDef.texCoord !== undefined && mapDef.texCoord > 0 ) {\n\n\t\t\t\ttexture = texture.clone();\n\t\t\t\ttexture.channel = mapDef.texCoord;\n\n\t\t\t}\n\n\t\t\tif ( parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] ) {\n\n\t\t\t\tconst transform = mapDef.extensions !== undefined ? mapDef.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] : undefined;\n\n\t\t\t\tif ( transform ) {\n\n\t\t\t\t\tconst gltfReference = parser.associations.get( texture );\n\t\t\t\t\ttexture = parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ].extendTexture( texture, transform );\n\t\t\t\t\tparser.associations.set( texture, gltfReference );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( colorSpace !== undefined ) {\n\n\t\t\t\ttexture.colorSpace = colorSpace;\n\n\t\t\t}\n\n\t\t\tmaterialParams[ mapName ] = texture;\n\n\t\t\treturn texture;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Assigns final material to a Mesh, Line, or Points instance. The instance\n\t * already has a material (generated from the glTF material options alone)\n\t * but reuse of the same glTF material may require multiple threejs materials\n\t * to accommodate different primitive types, defines, etc. New materials will\n\t * be created if necessary, and reused from a cache.\n\t * @param  {Object3D} mesh Mesh, Line, or Points instance.\n\t */\n\tassignFinalMaterial( mesh ) {\n\n\t\tconst geometry = mesh.geometry;\n\t\tlet material = mesh.material;\n\n\t\tconst useDerivativeTangents = geometry.attributes.tangent === undefined;\n\t\tconst useVertexColors = geometry.attributes.color !== undefined;\n\t\tconst useFlatShading = geometry.attributes.normal === undefined;\n\n\t\tif ( mesh.isPoints ) {\n\n\t\t\tconst cacheKey = 'PointsMaterial:' + material.uuid;\n\n\t\t\tlet pointsMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! pointsMaterial ) {\n\n\t\t\t\tpointsMaterial = new PointsMaterial();\n\t\t\t\tMaterial.prototype.copy.call( pointsMaterial, material );\n\t\t\t\tpointsMaterial.color.copy( material.color );\n\t\t\t\tpointsMaterial.map = material.map;\n\t\t\t\tpointsMaterial.sizeAttenuation = false; // glTF spec says points should be 1px\n\n\t\t\t\tthis.cache.add( cacheKey, pointsMaterial );\n\n\t\t\t}\n\n\t\t\tmaterial = pointsMaterial;\n\n\t\t} else if ( mesh.isLine ) {\n\n\t\t\tconst cacheKey = 'LineBasicMaterial:' + material.uuid;\n\n\t\t\tlet lineMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! lineMaterial ) {\n\n\t\t\t\tlineMaterial = new LineBasicMaterial();\n\t\t\t\tMaterial.prototype.copy.call( lineMaterial, material );\n\t\t\t\tlineMaterial.color.copy( material.color );\n\t\t\t\tlineMaterial.map = material.map;\n\n\t\t\t\tthis.cache.add( cacheKey, lineMaterial );\n\n\t\t\t}\n\n\t\t\tmaterial = lineMaterial;\n\n\t\t}\n\n\t\t// Clone the material if it will be modified\n\t\tif ( useDerivativeTangents || useVertexColors || useFlatShading ) {\n\n\t\t\tlet cacheKey = 'ClonedMaterial:' + material.uuid + ':';\n\n\t\t\tif ( useDerivativeTangents ) cacheKey += 'derivative-tangents:';\n\t\t\tif ( useVertexColors ) cacheKey += 'vertex-colors:';\n\t\t\tif ( useFlatShading ) cacheKey += 'flat-shading:';\n\n\t\t\tlet cachedMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! cachedMaterial ) {\n\n\t\t\t\tcachedMaterial = material.clone();\n\n\t\t\t\tif ( useVertexColors ) cachedMaterial.vertexColors = true;\n\t\t\t\tif ( useFlatShading ) cachedMaterial.flatShading = true;\n\n\t\t\t\tif ( useDerivativeTangents ) {\n\n\t\t\t\t\t// https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n\t\t\t\t\tif ( cachedMaterial.normalScale ) cachedMaterial.normalScale.y *= - 1;\n\t\t\t\t\tif ( cachedMaterial.clearcoatNormalScale ) cachedMaterial.clearcoatNormalScale.y *= - 1;\n\n\t\t\t\t}\n\n\t\t\t\tthis.cache.add( cacheKey, cachedMaterial );\n\n\t\t\t\tthis.associations.set( cachedMaterial, this.associations.get( material ) );\n\n\t\t\t}\n\n\t\t\tmaterial = cachedMaterial;\n\n\t\t}\n\n\t\tmesh.material = material;\n\n\t}\n\n\tgetMaterialType( /* materialIndex */ ) {\n\n\t\treturn MeshStandardMaterial;\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n\t * @param {number} materialIndex\n\t * @return {Promise<Material>}\n\t */\n\tloadMaterial( materialIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\t\tconst materialDef = json.materials[ materialIndex ];\n\n\t\tlet materialType;\n\t\tconst materialParams = {};\n\t\tconst materialExtensions = materialDef.extensions || {};\n\n\t\tconst pending = [];\n\n\t\tif ( materialExtensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ] ) {\n\n\t\t\tconst kmuExtension = extensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ];\n\t\t\tmaterialType = kmuExtension.getMaterialType();\n\t\t\tpending.push( kmuExtension.extendParams( materialParams, materialDef, parser ) );\n\n\t\t} else {\n\n\t\t\t// Specification:\n\t\t\t// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n\t\t\tconst metallicRoughness = materialDef.pbrMetallicRoughness || {};\n\n\t\t\tmaterialParams.color = new Color( 1.0, 1.0, 1.0 );\n\t\t\tmaterialParams.opacity = 1.0;\n\n\t\t\tif ( Array.isArray( metallicRoughness.baseColorFactor ) ) {\n\n\t\t\t\tconst array = metallicRoughness.baseColorFactor;\n\n\t\t\t\tmaterialParams.color.fromArray( array );\n\t\t\t\tmaterialParams.opacity = array[ 3 ];\n\n\t\t\t}\n\n\t\t\tif ( metallicRoughness.baseColorTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace ) );\n\n\t\t\t}\n\n\t\t\tmaterialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0;\n\t\t\tmaterialParams.roughness = metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0;\n\n\t\t\tif ( metallicRoughness.metallicRoughnessTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture ) );\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture ) );\n\n\t\t\t}\n\n\t\t\tmaterialType = this._invokeOne( function ( ext ) {\n\n\t\t\t\treturn ext.getMaterialType && ext.getMaterialType( materialIndex );\n\n\t\t\t} );\n\n\t\t\tpending.push( Promise.all( this._invokeAll( function ( ext ) {\n\n\t\t\t\treturn ext.extendMaterialParams && ext.extendMaterialParams( materialIndex, materialParams );\n\n\t\t\t} ) ) );\n\n\t\t}\n\n\t\tif ( materialDef.doubleSided === true ) {\n\n\t\t\tmaterialParams.side = DoubleSide;\n\n\t\t}\n\n\t\tconst alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;\n\n\t\tif ( alphaMode === ALPHA_MODES.BLEND ) {\n\n\t\t\tmaterialParams.transparent = true;\n\n\t\t\t// See: https://github.com/mrdoob/three.js/issues/17706\n\t\t\tmaterialParams.depthWrite = false;\n\n\t\t} else {\n\n\t\t\tmaterialParams.transparent = false;\n\n\t\t\tif ( alphaMode === ALPHA_MODES.MASK ) {\n\n\t\t\t\tmaterialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'normalMap', materialDef.normalTexture ) );\n\n\t\t\tmaterialParams.normalScale = new Vector2( 1, 1 );\n\n\t\t\tif ( materialDef.normalTexture.scale !== undefined ) {\n\n\t\t\t\tconst scale = materialDef.normalTexture.scale;\n\n\t\t\t\tmaterialParams.normalScale.set( scale, scale );\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'aoMap', materialDef.occlusionTexture ) );\n\n\t\t\tif ( materialDef.occlusionTexture.strength !== undefined ) {\n\n\t\t\t\tmaterialParams.aoMapIntensity = materialDef.occlusionTexture.strength;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tmaterialParams.emissive = new Color().fromArray( materialDef.emissiveFactor );\n\n\t\t}\n\n\t\tif ( materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function () {\n\n\t\t\tconst material = new materialType( materialParams );\n\n\t\t\tif ( materialDef.name ) material.name = materialDef.name;\n\n\t\t\tassignExtrasToUserData( material, materialDef );\n\n\t\t\tparser.associations.set( material, { materials: materialIndex } );\n\n\t\t\tif ( materialDef.extensions ) addUnknownExtensionsToUserData( extensions, material, materialDef );\n\n\t\t\treturn material;\n\n\t\t} );\n\n\t}\n\n\t/** When Object3D instances are targeted by animation, they need unique names. */\n\tcreateUniqueName( originalName ) {\n\n\t\tconst sanitizedName = PropertyBinding.sanitizeNodeName( originalName || '' );\n\n\t\tif ( sanitizedName in this.nodeNamesUsed ) {\n\n\t\t\treturn sanitizedName + '_' + ( ++ this.nodeNamesUsed[ sanitizedName ] );\n\n\t\t} else {\n\n\t\t\tthis.nodeNamesUsed[ sanitizedName ] = 0;\n\n\t\t\treturn sanitizedName;\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n\t *\n\t * Creates BufferGeometries from primitives.\n\t *\n\t * @param {Array<GLTF.Primitive>} primitives\n\t * @return {Promise<Array<BufferGeometry>>}\n\t */\n\tloadGeometries( primitives ) {\n\n\t\tconst parser = this;\n\t\tconst extensions = this.extensions;\n\t\tconst cache = this.primitiveCache;\n\n\t\tfunction createDracoPrimitive( primitive ) {\n\n\t\t\treturn extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ]\n\t\t\t\t.decodePrimitive( primitive, parser )\n\t\t\t\t.then( function ( geometry ) {\n\n\t\t\t\t\treturn addPrimitiveAttributes( geometry, primitive, parser );\n\n\t\t\t\t} );\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = primitives.length; i < il; i ++ ) {\n\n\t\t\tconst primitive = primitives[ i ];\n\t\t\tconst cacheKey = createPrimitiveKey( primitive );\n\n\t\t\t// See if we've already created this geometry\n\t\t\tconst cached = cache[ cacheKey ];\n\n\t\t\tif ( cached ) {\n\n\t\t\t\t// Use the cached geometry if it exists\n\t\t\t\tpending.push( cached.promise );\n\n\t\t\t} else {\n\n\t\t\t\tlet geometryPromise;\n\n\t\t\t\tif ( primitive.extensions && primitive.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ] ) {\n\n\t\t\t\t\t// Use DRACO geometry if available\n\t\t\t\t\tgeometryPromise = createDracoPrimitive( primitive );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Otherwise create a new geometry\n\t\t\t\t\tgeometryPromise = addPrimitiveAttributes( new BufferGeometry(), primitive, parser );\n\n\t\t\t\t}\n\n\t\t\t\t// Cache this geometry\n\t\t\t\tcache[ cacheKey ] = { primitive: primitive, promise: geometryPromise };\n\n\t\t\t\tpending.push( geometryPromise );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n\t * @param {number} meshIndex\n\t * @return {Promise<Group|Mesh|SkinnedMesh>}\n\t */\n\tloadMesh( meshIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\n\t\tconst meshDef = json.meshes[ meshIndex ];\n\t\tconst primitives = meshDef.primitives;\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = primitives.length; i < il; i ++ ) {\n\n\t\t\tconst material = primitives[ i ].material === undefined\n\t\t\t\t? createDefaultMaterial( this.cache )\n\t\t\t\t: this.getDependency( 'material', primitives[ i ].material );\n\n\t\t\tpending.push( material );\n\n\t\t}\n\n\t\tpending.push( parser.loadGeometries( primitives ) );\n\n\t\treturn Promise.all( pending ).then( function ( results ) {\n\n\t\t\tconst materials = results.slice( 0, results.length - 1 );\n\t\t\tconst geometries = results[ results.length - 1 ];\n\n\t\t\tconst meshes = [];\n\n\t\t\tfor ( let i = 0, il = geometries.length; i < il; i ++ ) {\n\n\t\t\t\tconst geometry = geometries[ i ];\n\t\t\t\tconst primitive = primitives[ i ];\n\n\t\t\t\t// 1. create Mesh\n\n\t\t\t\tlet mesh;\n\n\t\t\t\tconst material = materials[ i ];\n\n\t\t\t\tif ( primitive.mode === WEBGL_CONSTANTS.TRIANGLES ||\n\t\t\t\t\t\tprimitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ||\n\t\t\t\t\t\tprimitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ||\n\t\t\t\t\t\tprimitive.mode === undefined ) {\n\n\t\t\t\t\t// .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n\t\t\t\t\tmesh = meshDef.isSkinnedMesh === true\n\t\t\t\t\t\t? new SkinnedMesh( geometry, material )\n\t\t\t\t\t\t: new Mesh( geometry, material );\n\n\t\t\t\t\tif ( mesh.isSkinnedMesh === true ) {\n\n\t\t\t\t\t\t// normalize skin weights to fix malformed assets (see #15319)\n\t\t\t\t\t\tmesh.normalizeSkinWeights();\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ) {\n\n\t\t\t\t\t\tmesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleStripDrawMode );\n\n\t\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ) {\n\n\t\t\t\t\t\tmesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleFanDrawMode );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINES ) {\n\n\t\t\t\t\tmesh = new LineSegments( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_STRIP ) {\n\n\t\t\t\t\tmesh = new Line( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_LOOP ) {\n\n\t\t\t\t\tmesh = new LineLoop( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.POINTS ) {\n\n\t\t\t\t\tmesh = new Points( geometry, material );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tthrow new Error( 'THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode );\n\n\t\t\t\t}\n\n\t\t\t\tif ( Object.keys( mesh.geometry.morphAttributes ).length > 0 ) {\n\n\t\t\t\t\tupdateMorphTargets( mesh, meshDef );\n\n\t\t\t\t}\n\n\t\t\t\tmesh.name = parser.createUniqueName( meshDef.name || ( 'mesh_' + meshIndex ) );\n\n\t\t\t\tassignExtrasToUserData( mesh, meshDef );\n\n\t\t\t\tif ( primitive.extensions ) addUnknownExtensionsToUserData( extensions, mesh, primitive );\n\n\t\t\t\tparser.assignFinalMaterial( mesh );\n\n\t\t\t\tmeshes.push( mesh );\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, il = meshes.length; i < il; i ++ ) {\n\n\t\t\t\tparser.associations.set( meshes[ i ], {\n\t\t\t\t\tmeshes: meshIndex,\n\t\t\t\t\tprimitives: i\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tif ( meshes.length === 1 ) {\n\n\t\t\t\tif ( meshDef.extensions ) addUnknownExtensionsToUserData( extensions, meshes[ 0 ], meshDef );\n\n\t\t\t\treturn meshes[ 0 ];\n\n\t\t\t}\n\n\t\t\tconst group = new Group();\n\n\t\t\tif ( meshDef.extensions ) addUnknownExtensionsToUserData( extensions, group, meshDef );\n\n\t\t\tparser.associations.set( group, { meshes: meshIndex } );\n\n\t\t\tfor ( let i = 0, il = meshes.length; i < il; i ++ ) {\n\n\t\t\t\tgroup.add( meshes[ i ] );\n\n\t\t\t}\n\n\t\t\treturn group;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n\t * @param {number} cameraIndex\n\t * @return {Promise<THREE.Camera>}\n\t */\n\tloadCamera( cameraIndex ) {\n\n\t\tlet camera;\n\t\tconst cameraDef = this.json.cameras[ cameraIndex ];\n\t\tconst params = cameraDef[ cameraDef.type ];\n\n\t\tif ( ! params ) {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing camera parameters.' );\n\t\t\treturn;\n\n\t\t}\n\n\t\tif ( cameraDef.type === 'perspective' ) {\n\n\t\t\tcamera = new PerspectiveCamera( MathUtils.radToDeg( params.yfov ), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6 );\n\n\t\t} else if ( cameraDef.type === 'orthographic' ) {\n\n\t\t\tcamera = new OrthographicCamera( - params.xmag, params.xmag, params.ymag, - params.ymag, params.znear, params.zfar );\n\n\t\t}\n\n\t\tif ( cameraDef.name ) camera.name = this.createUniqueName( cameraDef.name );\n\n\t\tassignExtrasToUserData( camera, cameraDef );\n\n\t\treturn Promise.resolve( camera );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n\t * @param {number} skinIndex\n\t * @return {Promise<Skeleton>}\n\t */\n\tloadSkin( skinIndex ) {\n\n\t\tconst skinDef = this.json.skins[ skinIndex ];\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = skinDef.joints.length; i < il; i ++ ) {\n\n\t\t\tpending.push( this._loadNodeShallow( skinDef.joints[ i ] ) );\n\n\t\t}\n\n\t\tif ( skinDef.inverseBindMatrices !== undefined ) {\n\n\t\t\tpending.push( this.getDependency( 'accessor', skinDef.inverseBindMatrices ) );\n\n\t\t} else {\n\n\t\t\tpending.push( null );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function ( results ) {\n\n\t\t\tconst inverseBindMatrices = results.pop();\n\t\t\tconst jointNodes = results;\n\n\t\t\t// Note that bones (joint nodes) may or may not be in the\n\t\t\t// scene graph at this time.\n\n\t\t\tconst bones = [];\n\t\t\tconst boneInverses = [];\n\n\t\t\tfor ( let i = 0, il = jointNodes.length; i < il; i ++ ) {\n\n\t\t\t\tconst jointNode = jointNodes[ i ];\n\n\t\t\t\tif ( jointNode ) {\n\n\t\t\t\t\tbones.push( jointNode );\n\n\t\t\t\t\tconst mat = new Matrix4();\n\n\t\t\t\t\tif ( inverseBindMatrices !== null ) {\n\n\t\t\t\t\t\tmat.fromArray( inverseBindMatrices.array, i * 16 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tboneInverses.push( mat );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn new Skeleton( bones, boneInverses );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n\t * @param {number} animationIndex\n\t * @return {Promise<AnimationClip>}\n\t */\n\tloadAnimation( animationIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\n\t\tconst animationDef = json.animations[ animationIndex ];\n\t\tconst animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex;\n\n\t\tconst pendingNodes = [];\n\t\tconst pendingInputAccessors = [];\n\t\tconst pendingOutputAccessors = [];\n\t\tconst pendingSamplers = [];\n\t\tconst pendingTargets = [];\n\n\t\tfor ( let i = 0, il = animationDef.channels.length; i < il; i ++ ) {\n\n\t\t\tconst channel = animationDef.channels[ i ];\n\t\t\tconst sampler = animationDef.samplers[ channel.sampler ];\n\t\t\tconst target = channel.target;\n\t\t\tconst name = target.node;\n\t\t\tconst input = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.input ] : sampler.input;\n\t\t\tconst output = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.output ] : sampler.output;\n\n\t\t\tif ( target.node === undefined ) continue;\n\n\t\t\tpendingNodes.push( this.getDependency( 'node', name ) );\n\t\t\tpendingInputAccessors.push( this.getDependency( 'accessor', input ) );\n\t\t\tpendingOutputAccessors.push( this.getDependency( 'accessor', output ) );\n\t\t\tpendingSamplers.push( sampler );\n\t\t\tpendingTargets.push( target );\n\n\t\t}\n\n\t\treturn Promise.all( [\n\n\t\t\tPromise.all( pendingNodes ),\n\t\t\tPromise.all( pendingInputAccessors ),\n\t\t\tPromise.all( pendingOutputAccessors ),\n\t\t\tPromise.all( pendingSamplers ),\n\t\t\tPromise.all( pendingTargets )\n\n\t\t] ).then( function ( dependencies ) {\n\n\t\t\tconst nodes = dependencies[ 0 ];\n\t\t\tconst inputAccessors = dependencies[ 1 ];\n\t\t\tconst outputAccessors = dependencies[ 2 ];\n\t\t\tconst samplers = dependencies[ 3 ];\n\t\t\tconst targets = dependencies[ 4 ];\n\n\t\t\tconst tracks = [];\n\n\t\t\tfor ( let i = 0, il = nodes.length; i < il; i ++ ) {\n\n\t\t\t\tconst node = nodes[ i ];\n\t\t\t\tconst inputAccessor = inputAccessors[ i ];\n\t\t\t\tconst outputAccessor = outputAccessors[ i ];\n\t\t\t\tconst sampler = samplers[ i ];\n\t\t\t\tconst target = targets[ i ];\n\n\t\t\t\tif ( node === undefined ) continue;\n\n\t\t\t\tif ( node.updateMatrix ) {\n\n\t\t\t\t\tnode.updateMatrix();\n\t\t\t\t\tnode.matrixAutoUpdate = true;\n\n\t\t\t\t}\n\n\t\t\t\tconst createdTracks = parser._createAnimationTracks( node, inputAccessor, outputAccessor, sampler, target );\n\n\t\t\t\tif ( createdTracks ) {\n\n\t\t\t\t\tfor ( let k = 0; k < createdTracks.length; k ++ ) {\n\n\t\t\t\t\t\ttracks.push( createdTracks[ k ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn new AnimationClip( animationName, undefined, tracks );\n\n\t\t} );\n\n\t}\n\n\tcreateNodeMesh( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tif ( nodeDef.mesh === undefined ) return null;\n\n\t\treturn parser.getDependency( 'mesh', nodeDef.mesh ).then( function ( mesh ) {\n\n\t\t\tconst node = parser._getNodeRef( parser.meshCache, nodeDef.mesh, mesh );\n\n\t\t\t// if weights are provided on the node, override weights on the mesh.\n\t\t\tif ( nodeDef.weights !== undefined ) {\n\n\t\t\t\tnode.traverse( function ( o ) {\n\n\t\t\t\t\tif ( ! o.isMesh ) return;\n\n\t\t\t\t\tfor ( let i = 0, il = nodeDef.weights.length; i < il; i ++ ) {\n\n\t\t\t\t\t\to.morphTargetInfluences[ i ] = nodeDef.weights[ i ];\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n\t * @param {number} nodeIndex\n\t * @return {Promise<Object3D>}\n\t */\n\tloadNode( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tconst nodePending = parser._loadNodeShallow( nodeIndex );\n\n\t\tconst childPending = [];\n\t\tconst childrenDef = nodeDef.children || [];\n\n\t\tfor ( let i = 0, il = childrenDef.length; i < il; i ++ ) {\n\n\t\t\tchildPending.push( parser.getDependency( 'node', childrenDef[ i ] ) );\n\n\t\t}\n\n\t\tconst skeletonPending = nodeDef.skin === undefined\n\t\t\t? Promise.resolve( null )\n\t\t\t: parser.getDependency( 'skin', nodeDef.skin );\n\n\t\treturn Promise.all( [\n\t\t\tnodePending,\n\t\t\tPromise.all( childPending ),\n\t\t\tskeletonPending\n\t\t] ).then( function ( results ) {\n\n\t\t\tconst node = results[ 0 ];\n\t\t\tconst children = results[ 1 ];\n\t\t\tconst skeleton = results[ 2 ];\n\n\t\t\tif ( skeleton !== null ) {\n\n\t\t\t\t// This full traverse should be fine because\n\t\t\t\t// child glTF nodes have not been added to this node yet.\n\t\t\t\tnode.traverse( function ( mesh ) {\n\n\t\t\t\t\tif ( ! mesh.isSkinnedMesh ) return;\n\n\t\t\t\t\tmesh.bind( skeleton, _identityMatrix );\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, il = children.length; i < il; i ++ ) {\n\n\t\t\t\tnode.add( children[ i ] );\n\n\t\t\t}\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t}\n\n\t// ._loadNodeShallow() parses a single node.\n\t// skin and child nodes are created and added in .loadNode() (no '_' prefix).\n\t_loadNodeShallow( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\t\tconst parser = this;\n\n\t\t// This method is called from .loadNode() and .loadSkin().\n\t\t// Cache a node to avoid duplication.\n\n\t\tif ( this.nodeCache[ nodeIndex ] !== undefined ) {\n\n\t\t\treturn this.nodeCache[ nodeIndex ];\n\n\t\t}\n\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\t// reserve node's name before its dependencies, so the root has the intended name.\n\t\tconst nodeName = nodeDef.name ? parser.createUniqueName( nodeDef.name ) : '';\n\n\t\tconst pending = [];\n\n\t\tconst meshPromise = parser._invokeOne( function ( ext ) {\n\n\t\t\treturn ext.createNodeMesh && ext.createNodeMesh( nodeIndex );\n\n\t\t} );\n\n\t\tif ( meshPromise ) {\n\n\t\t\tpending.push( meshPromise );\n\n\t\t}\n\n\t\tif ( nodeDef.camera !== undefined ) {\n\n\t\t\tpending.push( parser.getDependency( 'camera', nodeDef.camera ).then( function ( camera ) {\n\n\t\t\t\treturn parser._getNodeRef( parser.cameraCache, nodeDef.camera, camera );\n\n\t\t\t} ) );\n\n\t\t}\n\n\t\tparser._invokeAll( function ( ext ) {\n\n\t\t\treturn ext.createNodeAttachment && ext.createNodeAttachment( nodeIndex );\n\n\t\t} ).forEach( function ( promise ) {\n\n\t\t\tpending.push( promise );\n\n\t\t} );\n\n\t\tthis.nodeCache[ nodeIndex ] = Promise.all( pending ).then( function ( objects ) {\n\n\t\t\tlet node;\n\n\t\t\t// .isBone isn't in glTF spec. See ._markDefs\n\t\t\tif ( nodeDef.isBone === true ) {\n\n\t\t\t\tnode = new Bone();\n\n\t\t\t} else if ( objects.length > 1 ) {\n\n\t\t\t\tnode = new Group();\n\n\t\t\t} else if ( objects.length === 1 ) {\n\n\t\t\t\tnode = objects[ 0 ];\n\n\t\t\t} else {\n\n\t\t\t\tnode = new Object3D();\n\n\t\t\t}\n\n\t\t\tif ( node !== objects[ 0 ] ) {\n\n\t\t\t\tfor ( let i = 0, il = objects.length; i < il; i ++ ) {\n\n\t\t\t\t\tnode.add( objects[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( nodeDef.name ) {\n\n\t\t\t\tnode.userData.name = nodeDef.name;\n\t\t\t\tnode.name = nodeName;\n\n\t\t\t}\n\n\t\t\tassignExtrasToUserData( node, nodeDef );\n\n\t\t\tif ( nodeDef.extensions ) addUnknownExtensionsToUserData( extensions, node, nodeDef );\n\n\t\t\tif ( nodeDef.matrix !== undefined ) {\n\n\t\t\t\tconst matrix = new Matrix4();\n\t\t\t\tmatrix.fromArray( nodeDef.matrix );\n\t\t\t\tnode.applyMatrix4( matrix );\n\n\t\t\t} else {\n\n\t\t\t\tif ( nodeDef.translation !== undefined ) {\n\n\t\t\t\t\tnode.position.fromArray( nodeDef.translation );\n\n\t\t\t\t}\n\n\t\t\t\tif ( nodeDef.rotation !== undefined ) {\n\n\t\t\t\t\tnode.quaternion.fromArray( nodeDef.rotation );\n\n\t\t\t\t}\n\n\t\t\t\tif ( nodeDef.scale !== undefined ) {\n\n\t\t\t\t\tnode.scale.fromArray( nodeDef.scale );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( ! parser.associations.has( node ) ) {\n\n\t\t\t\tparser.associations.set( node, {} );\n\n\t\t\t}\n\n\t\t\tparser.associations.get( node ).nodes = nodeIndex;\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t\treturn this.nodeCache[ nodeIndex ];\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n\t * @param {number} sceneIndex\n\t * @return {Promise<Group>}\n\t */\n\tloadScene( sceneIndex ) {\n\n\t\tconst extensions = this.extensions;\n\t\tconst sceneDef = this.json.scenes[ sceneIndex ];\n\t\tconst parser = this;\n\n\t\t// Loader returns Group, not Scene.\n\t\t// See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n\t\tconst scene = new Group();\n\t\tif ( sceneDef.name ) scene.name = parser.createUniqueName( sceneDef.name );\n\n\t\tassignExtrasToUserData( scene, sceneDef );\n\n\t\tif ( sceneDef.extensions ) addUnknownExtensionsToUserData( extensions, scene, sceneDef );\n\n\t\tconst nodeIds = sceneDef.nodes || [];\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = nodeIds.length; i < il; i ++ ) {\n\n\t\t\tpending.push( parser.getDependency( 'node', nodeIds[ i ] ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function ( nodes ) {\n\n\t\t\tfor ( let i = 0, il = nodes.length; i < il; i ++ ) {\n\n\t\t\t\tscene.add( nodes[ i ] );\n\n\t\t\t}\n\n\t\t\t// Removes dangling associations, associations that reference a node that\n\t\t\t// didn't make it into the scene.\n\t\t\tconst reduceAssociations = ( node ) => {\n\n\t\t\t\tconst reducedAssociations = new Map();\n\n\t\t\t\tfor ( const [ key, value ] of parser.associations ) {\n\n\t\t\t\t\tif ( key instanceof Material || key instanceof Texture ) {\n\n\t\t\t\t\t\treducedAssociations.set( key, value );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tnode.traverse( ( node ) => {\n\n\t\t\t\t\tconst mappings = parser.associations.get( node );\n\n\t\t\t\t\tif ( mappings != null ) {\n\n\t\t\t\t\t\treducedAssociations.set( node, mappings );\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\treturn reducedAssociations;\n\n\t\t\t};\n\n\t\t\tparser.associations = reduceAssociations( scene );\n\n\t\t\treturn scene;\n\n\t\t} );\n\n\t}\n\n\t_createAnimationTracks( node, inputAccessor, outputAccessor, sampler, target ) {\n\n\t\tconst tracks = [];\n\n\t\tconst targetName = node.name ? node.name : node.uuid;\n\n\t\tconst targetNames = [];\n\n\t\tif ( PATH_PROPERTIES[ target.path ] === PATH_PROPERTIES.weights ) {\n\n\t\t\tnode.traverse( function ( object ) {\n\n\t\t\t\tif ( object.morphTargetInfluences ) {\n\n\t\t\t\t\ttargetNames.push( object.name ? object.name : object.uuid );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} else {\n\n\t\t\ttargetNames.push( targetName );\n\n\t\t}\n\n\t\tlet TypedKeyframeTrack;\n\n\t\tswitch ( PATH_PROPERTIES[ target.path ] ) {\n\n\t\t\tcase PATH_PROPERTIES.weights:\n\n\t\t\t\tTypedKeyframeTrack = NumberKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tcase PATH_PROPERTIES.rotation:\n\n\t\t\t\tTypedKeyframeTrack = QuaternionKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tcase PATH_PROPERTIES.position:\n\t\t\tcase PATH_PROPERTIES.scale:\n\t\t\tdefault:\n\t\t\t\tswitch ( outputAccessor.itemSize ) {\n\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\tTypedKeyframeTrack = NumberKeyframeTrack;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 2:\n\t\t\t\t\tcase 3:\n\t\t\t\t\t\tTypedKeyframeTrack = VectorKeyframeTrack;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\tconst interpolation = sampler.interpolation !== undefined ? INTERPOLATION[ sampler.interpolation ] : InterpolateLinear;\n\n\t\tconst outputArray = this._getArrayFromAccessor( outputAccessor );\n\n\t\tfor ( let j = 0, jl = targetNames.length; j < jl; j ++ ) {\n\n\t\t\tconst track = new TypedKeyframeTrack(\n\t\t\t\ttargetNames[ j ] + '.' + PATH_PROPERTIES[ target.path ],\n\t\t\t\tinputAccessor.array,\n\t\t\t\toutputArray,\n\t\t\t\tinterpolation\n\t\t\t);\n\n\t\t\t// Override interpolation with custom factory method.\n\t\t\tif ( interpolation === 'CUBICSPLINE' ) {\n\n\t\t\t\tthis._createCubicSplineTrackInterpolant( track );\n\n\t\t\t}\n\n\t\t\ttracks.push( track );\n\n\t\t}\n\n\t\treturn tracks;\n\n\t}\n\n\t_getArrayFromAccessor( accessor ) {\n\n\t\tlet outputArray = accessor.array;\n\n\t\tif ( accessor.normalized ) {\n\n\t\t\tconst scale = getNormalizedComponentScale( outputArray.constructor );\n\t\t\tconst scaled = new Float32Array( outputArray.length );\n\n\t\t\tfor ( let j = 0, jl = outputArray.length; j < jl; j ++ ) {\n\n\t\t\t\tscaled[ j ] = outputArray[ j ] * scale;\n\n\t\t\t}\n\n\t\t\toutputArray = scaled;\n\n\t\t}\n\n\t\treturn outputArray;\n\n\t}\n\n\t_createCubicSplineTrackInterpolant( track ) {\n\n\t\ttrack.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline( result ) {\n\n\t\t\t// A CUBICSPLINE keyframe in glTF has three output values for each input value,\n\t\t\t// representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n\t\t\t// must be divided by three to get the interpolant's sampleSize argument.\n\n\t\t\tconst interpolantType = ( this instanceof QuaternionKeyframeTrack ) ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;\n\n\t\t\treturn new interpolantType( this.times, this.values, this.getValueSize() / 3, result );\n\n\t\t};\n\n\t\t// Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n\t\ttrack.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;\n\n\t}\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds( geometry, primitiveDef, parser ) {\n\n\tconst attributes = primitiveDef.attributes;\n\n\tconst box = new Box3();\n\n\tif ( attributes.POSITION !== undefined ) {\n\n\t\tconst accessor = parser.json.accessors[ attributes.POSITION ];\n\n\t\tconst min = accessor.min;\n\t\tconst max = accessor.max;\n\n\t\t// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n\t\tif ( min !== undefined && max !== undefined ) {\n\n\t\t\tbox.set(\n\t\t\t\tnew Vector3( min[ 0 ], min[ 1 ], min[ 2 ] ),\n\t\t\t\tnew Vector3( max[ 0 ], max[ 1 ], max[ 2 ] )\n\t\t\t);\n\n\t\t\tif ( accessor.normalized ) {\n\n\t\t\t\tconst boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );\n\t\t\t\tbox.min.multiplyScalar( boxScale );\n\t\t\t\tbox.max.multiplyScalar( boxScale );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );\n\n\t\t\treturn;\n\n\t\t}\n\n\t} else {\n\n\t\treturn;\n\n\t}\n\n\tconst targets = primitiveDef.targets;\n\n\tif ( targets !== undefined ) {\n\n\t\tconst maxDisplacement = new Vector3();\n\t\tconst vector = new Vector3();\n\n\t\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\t\tconst target = targets[ i ];\n\n\t\t\tif ( target.POSITION !== undefined ) {\n\n\t\t\t\tconst accessor = parser.json.accessors[ target.POSITION ];\n\t\t\t\tconst min = accessor.min;\n\t\t\t\tconst max = accessor.max;\n\n\t\t\t\t// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n\t\t\t\tif ( min !== undefined && max !== undefined ) {\n\n\t\t\t\t\t// we need to get max of absolute components because target weight is [-1,1]\n\t\t\t\t\tvector.setX( Math.max( Math.abs( min[ 0 ] ), Math.abs( max[ 0 ] ) ) );\n\t\t\t\t\tvector.setY( Math.max( Math.abs( min[ 1 ] ), Math.abs( max[ 1 ] ) ) );\n\t\t\t\t\tvector.setZ( Math.max( Math.abs( min[ 2 ] ), Math.abs( max[ 2 ] ) ) );\n\n\n\t\t\t\t\tif ( accessor.normalized ) {\n\n\t\t\t\t\t\tconst boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );\n\t\t\t\t\t\tvector.multiplyScalar( boxScale );\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n\t\t\t\t\t// to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n\t\t\t\t\t// are used to implement key-frame animations and as such only two are active at a time - this results in very large\n\t\t\t\t\t// boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n\t\t\t\t\tmaxDisplacement.max( vector );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n\t\tbox.expandByVector( maxDisplacement );\n\n\t}\n\n\tgeometry.boundingBox = box;\n\n\tconst sphere = new Sphere();\n\n\tbox.getCenter( sphere.center );\n\tsphere.radius = box.min.distanceTo( box.max ) / 2;\n\n\tgeometry.boundingSphere = sphere;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes( geometry, primitiveDef, parser ) {\n\n\tconst attributes = primitiveDef.attributes;\n\n\tconst pending = [];\n\n\tfunction assignAttributeAccessor( accessorIndex, attributeName ) {\n\n\t\treturn parser.getDependency( 'accessor', accessorIndex )\n\t\t\t.then( function ( accessor ) {\n\n\t\t\t\tgeometry.setAttribute( attributeName, accessor );\n\n\t\t\t} );\n\n\t}\n\n\tfor ( const gltfAttributeName in attributes ) {\n\n\t\tconst threeAttributeName = ATTRIBUTES[ gltfAttributeName ] || gltfAttributeName.toLowerCase();\n\n\t\t// Skip attributes already provided by e.g. Draco extension.\n\t\tif ( threeAttributeName in geometry.attributes ) continue;\n\n\t\tpending.push( assignAttributeAccessor( attributes[ gltfAttributeName ], threeAttributeName ) );\n\n\t}\n\n\tif ( primitiveDef.indices !== undefined && ! geometry.index ) {\n\n\t\tconst accessor = parser.getDependency( 'accessor', primitiveDef.indices ).then( function ( accessor ) {\n\n\t\t\tgeometry.setIndex( accessor );\n\n\t\t} );\n\n\t\tpending.push( accessor );\n\n\t}\n\n\tassignExtrasToUserData( geometry, primitiveDef );\n\n\tcomputeBounds( geometry, primitiveDef, parser );\n\n\treturn Promise.all( pending ).then( function () {\n\n\t\treturn primitiveDef.targets !== undefined\n\t\t\t? addMorphTargets( geometry, primitiveDef.targets, parser )\n\t\t\t: geometry;\n\n\t} );\n\n}\n\nexport { GLTFLoader };\n"], "mappings": "AAAA,SACCA,aAAa,EACbC,IAAI,EACJC,IAAI,EACJC,eAAe,EACfC,cAAc,EACdC,mBAAmB,EACnBC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,iBAAiB,EACjBC,aAAa,EACbC,iBAAiB,EACjBC,0BAA0B,EAC1BC,WAAW,EACXC,mBAAmB,EACnBC,iBAAiB,EACjBC,IAAI,EACJC,iBAAiB,EACjBC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,wBAAwB,EACxBC,yBAAyB,EACzBC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,IAAI,EACJC,iBAAiB,EACjBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,aAAa,EACbC,yBAAyB,EACzBC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,kBAAkB,EAClBC,iBAAiB,EACjBC,UAAU,EACVC,MAAM,EACNC,cAAc,EACdC,eAAe,EACfC,UAAU,EACVC,uBAAuB,EACvBC,cAAc,EACdC,QAAQ,EACRC,WAAW,EACXC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,qBAAqB,EACrBC,OAAO,EACPC,OAAO,EACPC,mBAAmB,EACnBC,cAAc,QACR,OAAO;AACd,SAASC,mBAAmB,QAAQ,iCAAiC;AAErE,MAAMC,UAAU,SAASrC,MAAM,CAAC;EAE/BsC,WAAWA,CAAEC,OAAO,EAAG;IAEtB,KAAK,CAAEA,OAAQ,CAAC;IAEhB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACC,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACC,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIC,+BAA+B,CAAED,MAAO,CAAC;IAErD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIE,0BAA0B,CAAEF,MAAO,CAAC;IAEhD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIG,wBAAwB,CAAEH,MAAO,CAAC;IAE9C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAII,wBAAwB,CAAEJ,MAAO,CAAC;IAE9C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIK,2BAA2B,CAAEL,MAAO,CAAC;IAEjD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIM,kCAAkC,CAAEN,MAAO,CAAC;IAExD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIO,4BAA4B,CAAEP,MAAO,CAAC;IAElD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIQ,yBAAyB,CAAER,MAAO,CAAC;IAE/C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIS,sCAAsC,CAAET,MAAO,CAAC;IAE5D,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIU,8BAA8B,CAAEV,MAAO,CAAC;IAEpD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIW,iCAAiC,CAAEX,MAAO,CAAC;IAEvD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIY,gCAAgC,CAAEZ,MAAO,CAAC;IAEtD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIa,mBAAmB,CAAEb,MAAO,CAAC;IAEzC,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIc,sBAAsB,CAAEd,MAAO,CAAC;IAE5C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIe,qBAAqB,CAAEf,MAAO,CAAC;IAE3C,CAAE,CAAC;EAEJ;EAEAgB,IAAIA,CAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAG;IAExC,MAAMC,KAAK,GAAG,IAAI;IAElB,IAAIC,YAAY;IAEhB,IAAK,IAAI,CAACA,YAAY,KAAK,EAAE,EAAG;MAE/BA,YAAY,GAAG,IAAI,CAACA,YAAY;IAEjC,CAAC,MAAM,IAAK,IAAI,CAACC,IAAI,KAAK,EAAE,EAAG;MAE9BD,YAAY,GAAG,IAAI,CAACC,IAAI;IAEzB,CAAC,MAAM;MAEND,YAAY,GAAGlE,WAAW,CAACoE,cAAc,CAAEP,GAAI,CAAC;IAEjD;;IAEA;IACA;IACA;IACA,IAAI,CAACvB,OAAO,CAAC+B,SAAS,CAAER,GAAI,CAAC;IAE7B,MAAMS,QAAQ,GAAG,SAAAA,CAAWC,CAAC,EAAG;MAE/B,IAAKP,OAAO,EAAG;QAEdA,OAAO,CAAEO,CAAE,CAAC;MAEb,CAAC,MAAM;QAENC,OAAO,CAACC,KAAK,CAAEF,CAAE,CAAC;MAEnB;MAEAN,KAAK,CAAC3B,OAAO,CAACoC,SAAS,CAAEb,GAAI,CAAC;MAC9BI,KAAK,CAAC3B,OAAO,CAACqC,OAAO,CAAEd,GAAI,CAAC;IAE7B,CAAC;IAED,MAAMe,MAAM,GAAG,IAAI9F,UAAU,CAAE,IAAI,CAACwD,OAAQ,CAAC;IAE7CsC,MAAM,CAACC,OAAO,CAAE,IAAI,CAACV,IAAK,CAAC;IAC3BS,MAAM,CAACE,eAAe,CAAE,aAAc,CAAC;IACvCF,MAAM,CAACG,gBAAgB,CAAE,IAAI,CAACC,aAAc,CAAC;IAC7CJ,MAAM,CAACK,kBAAkB,CAAE,IAAI,CAACC,eAAgB,CAAC;IAEjDN,MAAM,CAAChB,IAAI,CAAEC,GAAG,EAAE,UAAWsB,IAAI,EAAG;MAEnC,IAAI;QAEHlB,KAAK,CAACmB,KAAK,CAAED,IAAI,EAAEjB,YAAY,EAAE,UAAWmB,IAAI,EAAG;UAElDvB,MAAM,CAAEuB,IAAK,CAAC;UAEdpB,KAAK,CAAC3B,OAAO,CAACqC,OAAO,CAAEd,GAAI,CAAC;QAE7B,CAAC,EAAES,QAAS,CAAC;MAEd,CAAC,CAAC,OAAQC,CAAC,EAAG;QAEbD,QAAQ,CAAEC,CAAE,CAAC;MAEd;IAED,CAAC,EAAER,UAAU,EAAEO,QAAS,CAAC;EAE1B;EAEAgB,cAAcA,CAAE/C,WAAW,EAAG;IAE7B,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,OAAO,IAAI;EAEZ;EAEAgD,YAAYA,CAAA,EAAG;IAEd,MAAM,IAAIC,KAAK,CAEd,kGAED,CAAC;EAEF;EAEAC,aAAaA,CAAEjD,UAAU,EAAG;IAE3B,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,OAAO,IAAI;EAEZ;EAEAkD,iBAAiBA,CAAEjD,cAAc,EAAG;IAEnC,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC,OAAO,IAAI;EAEZ;EAEAE,QAAQA,CAAEgD,QAAQ,EAAG;IAEpB,IAAK,IAAI,CAACjD,eAAe,CAACkD,OAAO,CAAED,QAAS,CAAC,KAAK,CAAE,CAAC,EAAG;MAEvD,IAAI,CAACjD,eAAe,CAACmD,IAAI,CAAEF,QAAS,CAAC;IAEtC;IAEA,OAAO,IAAI;EAEZ;EAEAG,UAAUA,CAAEH,QAAQ,EAAG;IAEtB,IAAK,IAAI,CAACjD,eAAe,CAACkD,OAAO,CAAED,QAAS,CAAC,KAAK,CAAE,CAAC,EAAG;MAEvD,IAAI,CAACjD,eAAe,CAACqD,MAAM,CAAE,IAAI,CAACrD,eAAe,CAACkD,OAAO,CAAED,QAAS,CAAC,EAAE,CAAE,CAAC;IAE3E;IAEA,OAAO,IAAI;EAEZ;EAEAP,KAAKA,CAAED,IAAI,EAAEhB,IAAI,EAAEL,MAAM,EAAEE,OAAO,EAAG;IAEpC,IAAIgC,IAAI;IACR,MAAMC,UAAU,GAAG,CAAC,CAAC;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IAErC,IAAK,OAAOjB,IAAI,KAAK,QAAQ,EAAG;MAE/Ba,IAAI,GAAGK,IAAI,CAACjB,KAAK,CAAED,IAAK,CAAC;IAE1B,CAAC,MAAM,IAAKA,IAAI,YAAYmB,WAAW,EAAG;MAEzC,MAAMC,KAAK,GAAGJ,WAAW,CAACK,MAAM,CAAE,IAAIC,UAAU,CAAEtB,IAAI,EAAE,CAAC,EAAE,CAAE,CAAE,CAAC;MAEhE,IAAKoB,KAAK,KAAKG,6BAA6B,EAAG;QAE9C,IAAI;UAEHT,UAAU,CAAEU,UAAU,CAACC,eAAe,CAAE,GAAG,IAAIC,mBAAmB,CAAE1B,IAAK,CAAC;QAE3E,CAAC,CAAC,OAAQV,KAAK,EAAG;UAEjB,IAAKT,OAAO,EAAGA,OAAO,CAAES,KAAM,CAAC;UAC/B;QAED;QAEAuB,IAAI,GAAGK,IAAI,CAACjB,KAAK,CAAEa,UAAU,CAAEU,UAAU,CAACC,eAAe,CAAE,CAACE,OAAQ,CAAC;MAEtE,CAAC,MAAM;QAENd,IAAI,GAAGK,IAAI,CAACjB,KAAK,CAAEe,WAAW,CAACK,MAAM,CAAErB,IAAK,CAAE,CAAC;MAEhD;IAED,CAAC,MAAM;MAENa,IAAI,GAAGb,IAAI;IAEZ;IAEA,IAAKa,IAAI,CAACe,KAAK,KAAKC,SAAS,IAAIhB,IAAI,CAACe,KAAK,CAACE,OAAO,CAAE,CAAC,CAAE,GAAG,CAAC,EAAG;MAE9D,IAAKjD,OAAO,EAAGA,OAAO,CAAE,IAAIwB,KAAK,CAAE,yEAA0E,CAAE,CAAC;MAChH;IAED;IAEA,MAAM5C,MAAM,GAAG,IAAIsE,UAAU,CAAElB,IAAI,EAAE;MAEpC7B,IAAI,EAAEA,IAAI,IAAI,IAAI,CAACD,YAAY,IAAI,EAAE;MACrCiD,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BnC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC1C,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBE,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,cAAc,EAAE,IAAI,CAACA;IAEtB,CAAE,CAAC;IAEHG,MAAM,CAACwE,UAAU,CAACrC,gBAAgB,CAAE,IAAI,CAACC,aAAc,CAAC;IAExD,KAAM,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3E,eAAe,CAAC4E,MAAM,EAAED,CAAC,EAAG,EAAG;MAExD,MAAME,MAAM,GAAG,IAAI,CAAC7E,eAAe,CAAE2E,CAAC,CAAE,CAAEzE,MAAO,CAAC;MAClDsD,OAAO,CAAEqB,MAAM,CAACC,IAAI,CAAE,GAAGD,MAAM;;MAE/B;MACA;MACA;MACA;MACAtB,UAAU,CAAEsB,MAAM,CAACC,IAAI,CAAE,GAAG,IAAI;IAEjC;IAEA,IAAKxB,IAAI,CAACyB,cAAc,EAAG;MAE1B,KAAM,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,IAAI,CAACyB,cAAc,CAACH,MAAM,EAAE,EAAGD,CAAC,EAAG;QAEvD,MAAMK,aAAa,GAAG1B,IAAI,CAACyB,cAAc,CAAEJ,CAAC,CAAE;QAC9C,MAAMM,kBAAkB,GAAG3B,IAAI,CAAC2B,kBAAkB,IAAI,EAAE;QAExD,QAASD,aAAa;UAErB,KAAKf,UAAU,CAACiB,mBAAmB;YAClC3B,UAAU,CAAEyB,aAAa,CAAE,GAAG,IAAIG,2BAA2B,CAAC,CAAC;YAC/D;UAED,KAAKlB,UAAU,CAACmB,0BAA0B;YACzC7B,UAAU,CAAEyB,aAAa,CAAE,GAAG,IAAIK,iCAAiC,CAAE/B,IAAI,EAAE,IAAI,CAACzD,WAAY,CAAC;YAC7F;UAED,KAAKoE,UAAU,CAACqB,qBAAqB;YACpC/B,UAAU,CAAEyB,aAAa,CAAE,GAAG,IAAIO,6BAA6B,CAAC,CAAC;YACjE;UAED,KAAKtB,UAAU,CAACuB,qBAAqB;YACpCjC,UAAU,CAAEyB,aAAa,CAAE,GAAG,IAAIS,6BAA6B,CAAC,CAAC;YACjE;UAED;YAEC,IAAKR,kBAAkB,CAAC/B,OAAO,CAAE8B,aAAc,CAAC,IAAI,CAAC,IAAIxB,OAAO,CAAEwB,aAAa,CAAE,KAAKV,SAAS,EAAG;cAEjGxC,OAAO,CAAC4D,IAAI,CAAE,uCAAuC,GAAGV,aAAa,GAAG,IAAK,CAAC;YAE/E;QAEF;MAED;IAED;IAEA9E,MAAM,CAACyF,aAAa,CAAEpC,UAAW,CAAC;IAClCrD,MAAM,CAAC0F,UAAU,CAAEpC,OAAQ,CAAC;IAC5BtD,MAAM,CAACwC,KAAK,CAAEtB,MAAM,EAAEE,OAAQ,CAAC;EAEhC;EAEAuE,UAAUA,CAAEpD,IAAI,EAAEhB,IAAI,EAAG;IAExB,MAAMF,KAAK,GAAG,IAAI;IAElB,OAAO,IAAIuE,OAAO,CAAE,UAAWC,OAAO,EAAEC,MAAM,EAAG;MAEhDzE,KAAK,CAACmB,KAAK,CAAED,IAAI,EAAEhB,IAAI,EAAEsE,OAAO,EAAEC,MAAO,CAAC;IAE3C,CAAE,CAAC;EAEJ;AAED;;AAEA;;AAEA,SAASC,YAAYA,CAAA,EAAG;EAEvB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAEhB,OAAO;IAENC,GAAG,EAAE,SAAAA,CAAWC,GAAG,EAAG;MAErB,OAAOF,OAAO,CAAEE,GAAG,CAAE;IAEtB,CAAC;IAEDC,GAAG,EAAE,SAAAA,CAAWD,GAAG,EAAEE,MAAM,EAAG;MAE7BJ,OAAO,CAAEE,GAAG,CAAE,GAAGE,MAAM;IAExB,CAAC;IAEDC,MAAM,EAAE,SAAAA,CAAWH,GAAG,EAAG;MAExB,OAAOF,OAAO,CAAEE,GAAG,CAAE;IAEtB,CAAC;IAEDI,SAAS,EAAE,SAAAA,CAAA,EAAY;MAEtBN,OAAO,GAAG,CAAC,CAAC;IAEb;EAED,CAAC;AAEF;;AAEA;AACA;AACA;;AAEA,MAAMjC,UAAU,GAAG;EAClBC,eAAe,EAAE,iBAAiB;EAClCkB,0BAA0B,EAAE,4BAA4B;EACxDqB,mBAAmB,EAAE,qBAAqB;EAC1CC,uBAAuB,EAAE,yBAAyB;EAClDC,iBAAiB,EAAE,mBAAmB;EACtCC,mBAAmB,EAAE,qBAAqB;EAC1CC,sBAAsB,EAAE,wBAAwB;EAChDC,0BAA0B,EAAE,4BAA4B;EACxDC,yBAAyB,EAAE,2BAA2B;EACtDC,wBAAwB,EAAE,0BAA0B;EACpD9B,mBAAmB,EAAE,qBAAqB;EAC1C+B,oBAAoB,EAAE,sBAAsB;EAC5CC,kBAAkB,EAAE,oBAAoB;EACxC5B,qBAAqB,EAAE,uBAAuB;EAC9CE,qBAAqB,EAAE,uBAAuB;EAC9C2B,+BAA+B,EAAE,iCAAiC;EAClEC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,uBAAuB,EAAE,yBAAyB;EAClDC,uBAAuB,EAAE;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMxG,mBAAmB,CAAC;EAEzBpB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACwC,mBAAmB;;IAE1C;IACA,IAAI,CAACe,KAAK,GAAG;MAAEC,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;EAEpC;EAEAC,SAASA,CAAA,EAAG;IAEX,MAAMzH,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAM0H,QAAQ,GAAG,IAAI,CAAC1H,MAAM,CAACoD,IAAI,CAACuE,KAAK,IAAI,EAAE;IAE7C,KAAM,IAAIC,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAGH,QAAQ,CAAChD,MAAM,EAAEkD,SAAS,GAAGC,UAAU,EAAED,SAAS,EAAG,EAAG;MAE7F,MAAME,OAAO,GAAGJ,QAAQ,CAAEE,SAAS,CAAE;MAErC,IAAKE,OAAO,CAACzE,UAAU,IAClByE,OAAO,CAACzE,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,IAC/BkD,OAAO,CAACzE,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,CAACmD,KAAK,KAAK3D,SAAS,EAAG;QAE1DpE,MAAM,CAACgI,WAAW,CAAE,IAAI,CAACV,KAAK,EAAEQ,OAAO,CAACzE,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,CAACmD,KAAM,CAAC;MAExE;IAED;EAED;EAEAE,UAAUA,CAAEC,UAAU,EAAG;IAExB,MAAMlI,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMmI,QAAQ,GAAG,QAAQ,GAAGD,UAAU;IACtC,IAAIE,UAAU,GAAGpI,MAAM,CAACsH,KAAK,CAACrB,GAAG,CAAEkC,QAAS,CAAC;IAE7C,IAAKC,UAAU,EAAG,OAAOA,UAAU;IAEnC,MAAMhF,IAAI,GAAGpD,MAAM,CAACoD,IAAI;IACxB,MAAMC,UAAU,GAAKD,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,IAAM,CAAC,CAAC;IAC5E,MAAMyD,SAAS,GAAGhF,UAAU,CAACiF,MAAM,IAAI,EAAE;IACzC,MAAMC,QAAQ,GAAGF,SAAS,CAAEH,UAAU,CAAE;IACxC,IAAIM,SAAS;IAEb,MAAMC,KAAK,GAAG,IAAI1M,KAAK,CAAE,QAAS,CAAC;IAEnC,IAAKwM,QAAQ,CAACE,KAAK,KAAKrE,SAAS,EAAGqE,KAAK,CAACC,SAAS,CAAEH,QAAQ,CAACE,KAAM,CAAC;IAErE,MAAME,KAAK,GAAGJ,QAAQ,CAACI,KAAK,KAAKvE,SAAS,GAAGmE,QAAQ,CAACI,KAAK,GAAG,CAAC;IAE/D,QAASJ,QAAQ,CAACK,IAAI;MAErB,KAAK,aAAa;QACjBJ,SAAS,GAAG,IAAIxM,gBAAgB,CAAEyM,KAAM,CAAC;QACzCD,SAAS,CAACK,MAAM,CAACC,QAAQ,CAACC,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAE,CAAC;QAC1CP,SAAS,CAACrC,GAAG,CAAEqC,SAAS,CAACK,MAAO,CAAC;QACjC;MAED,KAAK,OAAO;QACXL,SAAS,GAAG,IAAIpK,UAAU,CAAEqK,KAAM,CAAC;QACnCD,SAAS,CAACQ,QAAQ,GAAGL,KAAK;QAC1B;MAED,KAAK,MAAM;QACVH,SAAS,GAAG,IAAI1J,SAAS,CAAE2J,KAAM,CAAC;QAClCD,SAAS,CAACQ,QAAQ,GAAGL,KAAK;QAC1B;QACAJ,QAAQ,CAACU,IAAI,GAAGV,QAAQ,CAACU,IAAI,IAAI,CAAC,CAAC;QACnCV,QAAQ,CAACU,IAAI,CAACC,cAAc,GAAGX,QAAQ,CAACU,IAAI,CAACC,cAAc,KAAK9E,SAAS,GAAGmE,QAAQ,CAACU,IAAI,CAACC,cAAc,GAAG,CAAC;QAC5GX,QAAQ,CAACU,IAAI,CAACE,cAAc,GAAGZ,QAAQ,CAACU,IAAI,CAACE,cAAc,KAAK/E,SAAS,GAAGmE,QAAQ,CAACU,IAAI,CAACE,cAAc,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;QACxHb,SAAS,CAACc,KAAK,GAAGf,QAAQ,CAACU,IAAI,CAACE,cAAc;QAC9CX,SAAS,CAACe,QAAQ,GAAG,GAAG,GAAGhB,QAAQ,CAACU,IAAI,CAACC,cAAc,GAAGX,QAAQ,CAACU,IAAI,CAACE,cAAc;QACtFX,SAAS,CAACK,MAAM,CAACC,QAAQ,CAACC,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAE,CAAC;QAC1CP,SAAS,CAACrC,GAAG,CAAEqC,SAAS,CAACK,MAAO,CAAC;QACjC;MAED;QACC,MAAM,IAAIjG,KAAK,CAAE,2CAA2C,GAAG2F,QAAQ,CAACK,IAAK,CAAC;IAEhF;;IAEA;IACA;IACAJ,SAAS,CAACM,QAAQ,CAACC,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;IAEjCP,SAAS,CAACgB,KAAK,GAAG,CAAC;IAEnBC,sBAAsB,CAAEjB,SAAS,EAAED,QAAS,CAAC;IAE7C,IAAKA,QAAQ,CAACmB,SAAS,KAAKtF,SAAS,EAAGoE,SAAS,CAACkB,SAAS,GAAGnB,QAAQ,CAACmB,SAAS;IAEhFlB,SAAS,CAAC5D,IAAI,GAAG5E,MAAM,CAAC2J,gBAAgB,CAAEpB,QAAQ,CAAC3D,IAAI,IAAM,QAAQ,GAAGsD,UAAa,CAAC;IAEtFE,UAAU,GAAGxC,OAAO,CAACC,OAAO,CAAE2C,SAAU,CAAC;IAEzCxI,MAAM,CAACsH,KAAK,CAACnB,GAAG,CAAEgC,QAAQ,EAAEC,UAAW,CAAC;IAExC,OAAOA,UAAU;EAElB;EAEAwB,aAAaA,CAAEhB,IAAI,EAAEiB,KAAK,EAAG;IAE5B,IAAKjB,IAAI,KAAK,OAAO,EAAG;IAExB,OAAO,IAAI,CAACX,UAAU,CAAE4B,KAAM,CAAC;EAEhC;EAEAC,oBAAoBA,CAAElC,SAAS,EAAG;IAEjC,MAAMmC,IAAI,GAAG,IAAI;IACjB,MAAM/J,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMoD,IAAI,GAAGpD,MAAM,CAACoD,IAAI;IACxB,MAAM0E,OAAO,GAAG1E,IAAI,CAACuE,KAAK,CAAEC,SAAS,CAAE;IACvC,MAAMW,QAAQ,GAAKT,OAAO,CAACzE,UAAU,IAAIyE,OAAO,CAACzE,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,IAAM,CAAC,CAAC;IAChF,MAAMsD,UAAU,GAAGK,QAAQ,CAACR,KAAK;IAEjC,IAAKG,UAAU,KAAK9D,SAAS,EAAG,OAAO,IAAI;IAE3C,OAAO,IAAI,CAAC6D,UAAU,CAAEC,UAAW,CAAC,CAAC8B,IAAI,CAAE,UAAWjC,KAAK,EAAG;MAE7D,OAAO/H,MAAM,CAACiK,WAAW,CAAEF,IAAI,CAACzC,KAAK,EAAEY,UAAU,EAAEH,KAAM,CAAC;IAE3D,CAAE,CAAC;EAEJ;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM9C,2BAA2B,CAAC;EAEjCxF,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACmF,IAAI,GAAGb,UAAU,CAACiB,mBAAmB;EAE3C;EAEAkF,eAAeA,CAAA,EAAG;IAEjB,OAAOzM,iBAAiB;EAEzB;EAEA0M,YAAYA,CAAEC,cAAc,EAAEC,WAAW,EAAErK,MAAM,EAAG;IAEnD,MAAMsK,OAAO,GAAG,EAAE;IAElBF,cAAc,CAAC3B,KAAK,GAAG,IAAI1M,KAAK,CAAE,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;IACjDqO,cAAc,CAACG,OAAO,GAAG,GAAG;IAE5B,MAAMC,iBAAiB,GAAGH,WAAW,CAACI,oBAAoB;IAE1D,IAAKD,iBAAiB,EAAG;MAExB,IAAKE,KAAK,CAACC,OAAO,CAAEH,iBAAiB,CAACI,eAAgB,CAAC,EAAG;QAEzD,MAAMC,KAAK,GAAGL,iBAAiB,CAACI,eAAe;QAE/CR,cAAc,CAAC3B,KAAK,CAACC,SAAS,CAAEmC,KAAM,CAAC;QACvCT,cAAc,CAACG,OAAO,GAAGM,KAAK,CAAE,CAAC,CAAE;MAEpC;MAEA,IAAKL,iBAAiB,CAACM,gBAAgB,KAAK1G,SAAS,EAAG;QAEvDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,KAAK,EAAEI,iBAAiB,CAACM,gBAAgB,EAAExL,cAAe,CAAE,CAAC;MAElH;IAED;IAEA,OAAOsG,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM7J,sCAAsC,CAAC;EAE5ChB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACkD,+BAA+B;EAEvD;EAEAgE,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMuF,gBAAgB,GAAGf,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,CAACwG,gBAAgB;IAE7E,IAAKA,gBAAgB,KAAKhH,SAAS,EAAG;MAErCgG,cAAc,CAACiB,iBAAiB,GAAGD,gBAAgB;IAEpD;IAEA,OAAOxF,OAAO,CAACC,OAAO,CAAC,CAAC;EAEzB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM5F,+BAA+B,CAAC;EAErCR,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACyC,uBAAuB;EAE/C;EAEA0D,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyE,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErD,IAAK0G,SAAS,CAACC,eAAe,KAAKnH,SAAS,EAAG;MAE9CgG,cAAc,CAACoB,SAAS,GAAGF,SAAS,CAACC,eAAe;IAErD;IAEA,IAAKD,SAAS,CAACG,gBAAgB,KAAKrH,SAAS,EAAG;MAE/CkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEkB,SAAS,CAACG,gBAAiB,CAAE,CAAC;IAEnG;IAEA,IAAKH,SAAS,CAACI,wBAAwB,KAAKtH,SAAS,EAAG;MAEvDgG,cAAc,CAACuB,kBAAkB,GAAGL,SAAS,CAACI,wBAAwB;IAEvE;IAEA,IAAKJ,SAAS,CAACM,yBAAyB,KAAKxH,SAAS,EAAG;MAExDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,uBAAuB,EAAEkB,SAAS,CAACM,yBAA0B,CAAE,CAAC;IAErH;IAEA,IAAKN,SAAS,CAACO,sBAAsB,KAAKzH,SAAS,EAAG;MAErDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,oBAAoB,EAAEkB,SAAS,CAACO,sBAAuB,CAAE,CAAC;MAE9G,IAAKP,SAAS,CAACO,sBAAsB,CAACC,KAAK,KAAK1H,SAAS,EAAG;QAE3D,MAAM0H,KAAK,GAAGR,SAAS,CAACO,sBAAsB,CAACC,KAAK;QAEpD1B,cAAc,CAAC2B,oBAAoB,GAAG,IAAI5M,OAAO,CAAE2M,KAAK,EAAEA,KAAM,CAAC;MAElE;IAED;IAEA,OAAOlG,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM3J,iCAAiC,CAAC;EAEvClB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAAC8C,yBAAyB;EAEjD;EAEAqD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyE,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErD,IAAK0G,SAAS,CAACU,iBAAiB,KAAK5H,SAAS,EAAG;MAEhDgG,cAAc,CAAC6B,WAAW,GAAGX,SAAS,CAACU,iBAAiB;IAEzD;IAEA,IAAKV,SAAS,CAACY,kBAAkB,KAAK9H,SAAS,EAAG;MAEjDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,gBAAgB,EAAEkB,SAAS,CAACY,kBAAmB,CAAE,CAAC;IAEvG;IAEA,IAAKZ,SAAS,CAACa,cAAc,KAAK/H,SAAS,EAAG;MAE7CgG,cAAc,CAACgC,cAAc,GAAGd,SAAS,CAACa,cAAc;IAEzD;IAEA,IAAK/B,cAAc,CAACiC,yBAAyB,KAAKjI,SAAS,EAAG;MAE7DgG,cAAc,CAACiC,yBAAyB,GAAG,CAAE,GAAG,EAAE,GAAG,CAAE;IAExD;IAEA,IAAKf,SAAS,CAACgB,2BAA2B,KAAKlI,SAAS,EAAG;MAE1DgG,cAAc,CAACiC,yBAAyB,CAAE,CAAC,CAAE,GAAGf,SAAS,CAACgB,2BAA2B;IAEtF;IAEA,IAAKhB,SAAS,CAACiB,2BAA2B,KAAKnI,SAAS,EAAG;MAE1DgG,cAAc,CAACiC,yBAAyB,CAAE,CAAC,CAAE,GAAGf,SAAS,CAACiB,2BAA2B;IAEtF;IAEA,IAAKjB,SAAS,CAACkB,2BAA2B,KAAKpI,SAAS,EAAG;MAE1DkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,yBAAyB,EAAEkB,SAAS,CAACkB,2BAA4B,CAAE,CAAC;IAEzH;IAEA,OAAO5G,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMjK,2BAA2B,CAAC;EAEjCZ,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAAC2C,mBAAmB;EAE3C;EAEAwD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyE,OAAO,GAAG,EAAE;IAElBF,cAAc,CAACqC,UAAU,GAAG,IAAI1Q,KAAK,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;IAChDqO,cAAc,CAACsC,cAAc,GAAG,CAAC;IACjCtC,cAAc,CAACuC,KAAK,GAAG,CAAC;IAExB,MAAMrB,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErD,IAAK0G,SAAS,CAACsB,gBAAgB,KAAKxI,SAAS,EAAG;MAE/CgG,cAAc,CAACqC,UAAU,CAAC/D,SAAS,CAAE4C,SAAS,CAACsB,gBAAiB,CAAC;IAElE;IAEA,IAAKtB,SAAS,CAACuB,oBAAoB,KAAKzI,SAAS,EAAG;MAEnDgG,cAAc,CAACsC,cAAc,GAAGpB,SAAS,CAACuB,oBAAoB;IAE/D;IAEA,IAAKvB,SAAS,CAACwB,iBAAiB,KAAK1I,SAAS,EAAG;MAEhDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,eAAe,EAAEkB,SAAS,CAACwB,iBAAiB,EAAExN,cAAe,CAAE,CAAC;IAErH;IAEA,IAAKgM,SAAS,CAACyB,qBAAqB,KAAK3I,SAAS,EAAG;MAEpDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,mBAAmB,EAAEkB,SAAS,CAACyB,qBAAsB,CAAE,CAAC;IAE7G;IAEA,OAAOnH,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhK,kCAAkC,CAAC;EAExCb,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAAC6C,0BAA0B;EAElD;EAEAsD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyE,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErD,IAAK0G,SAAS,CAAC0B,kBAAkB,KAAK5I,SAAS,EAAG;MAEjDgG,cAAc,CAAC6C,YAAY,GAAG3B,SAAS,CAAC0B,kBAAkB;IAE3D;IAEA,IAAK1B,SAAS,CAAC4B,mBAAmB,KAAK9I,SAAS,EAAG;MAElDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,iBAAiB,EAAEkB,SAAS,CAAC4B,mBAAoB,CAAE,CAAC;IAEzG;IAEA,OAAOtH,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM/J,4BAA4B,CAAC;EAElCd,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACgD,oBAAoB;EAE5C;EAEAmD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyE,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErDwF,cAAc,CAAC+C,SAAS,GAAG7B,SAAS,CAAC8B,eAAe,KAAKhJ,SAAS,GAAGkH,SAAS,CAAC8B,eAAe,GAAG,CAAC;IAElG,IAAK9B,SAAS,CAAC+B,gBAAgB,KAAKjJ,SAAS,EAAG;MAE/CkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEkB,SAAS,CAAC+B,gBAAiB,CAAE,CAAC;IAEnG;IAEAjD,cAAc,CAACkD,mBAAmB,GAAGhC,SAAS,CAACgC,mBAAmB,IAAIC,QAAQ;IAE9E,MAAMC,UAAU,GAAGlC,SAAS,CAACmC,gBAAgB,IAAI,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;IAC5DrD,cAAc,CAACqD,gBAAgB,GAAG,IAAI1R,KAAK,CAAEyR,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAG,CAAC;IAEhG,OAAO5H,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM9J,yBAAyB,CAAC;EAE/Bf,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAAC0C,iBAAiB;EAEzC;EAEAyD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyF,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErDwF,cAAc,CAACsD,GAAG,GAAGpC,SAAS,CAACoC,GAAG,KAAKtJ,SAAS,GAAGkH,SAAS,CAACoC,GAAG,GAAG,GAAG;IAEtE,OAAO9H,OAAO,CAACC,OAAO,CAAC,CAAC;EAEzB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMnF,8BAA8B,CAAC;EAEpCjB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAAC4C,sBAAsB;EAE9C;EAEAuD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyE,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErDwF,cAAc,CAACuD,iBAAiB,GAAGrC,SAAS,CAACsC,cAAc,KAAKxJ,SAAS,GAAGkH,SAAS,CAACsC,cAAc,GAAG,GAAG;IAE1G,IAAKtC,SAAS,CAACuC,eAAe,KAAKzJ,SAAS,EAAG;MAE9CkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,sBAAsB,EAAEkB,SAAS,CAACuC,eAAgB,CAAE,CAAC;IAE1G;IAEA,MAAML,UAAU,GAAGlC,SAAS,CAACwC,mBAAmB,IAAI,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;IAC/D1D,cAAc,CAAC2D,aAAa,GAAG,IAAIhS,KAAK,CAAEyR,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAG,CAAC;IAE7F,IAAKlC,SAAS,CAAC0C,oBAAoB,KAAK5J,SAAS,EAAG;MAEnDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,kBAAkB,EAAEkB,SAAS,CAAC0C,oBAAoB,EAAE1O,cAAe,CAAE,CAAC;IAE3H;IAEA,OAAOsG,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM1J,gCAAgC,CAAC;EAEtCnB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAAC+C,wBAAwB;EAEhD;EAEAoD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMlL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOlH,oBAAoB;EAE5B;EAEAuN,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMpK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMqK,WAAW,GAAGrK,MAAM,CAACoD,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAChH,UAAU,IAAI,CAAEgH,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMyE,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAChH,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IAErD,IAAK0G,SAAS,CAAC2C,kBAAkB,KAAK7J,SAAS,EAAG;MAEjDgG,cAAc,CAAC8D,UAAU,GAAG5C,SAAS,CAAC2C,kBAAkB;IAEzD;IAEA,IAAK3C,SAAS,CAAC6C,kBAAkB,KAAK/J,SAAS,EAAG;MAEjDgG,cAAc,CAAC+D,kBAAkB,GAAG7C,SAAS,CAAC6C,kBAAkB;IAEjE;IAEA,IAAK7C,SAAS,CAAC8C,iBAAiB,KAAKhK,SAAS,EAAG;MAEhDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,eAAe,EAAEkB,SAAS,CAAC8C,iBAAkB,CAAE,CAAC;IAErG;IAEA,OAAOxI,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMpK,0BAA0B,CAAC;EAEhCT,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACiD,kBAAkB;EAE1C;EAEAqH,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAMtO,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMoD,IAAI,GAAGpD,MAAM,CAACoD,IAAI;IAExB,MAAMmL,UAAU,GAAGnL,IAAI,CAACoL,QAAQ,CAAEF,YAAY,CAAE;IAEhD,IAAK,CAAEC,UAAU,CAAClL,UAAU,IAAI,CAAEkL,UAAU,CAAClL,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAEtE,OAAO,IAAI;IAEZ;IAEA,MAAM0G,SAAS,GAAGiD,UAAU,CAAClL,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IACpD,MAAM5C,MAAM,GAAGhC,MAAM,CAACyO,OAAO,CAAC7O,UAAU;IAExC,IAAK,CAAEoC,MAAM,EAAG;MAEf,IAAKoB,IAAI,CAAC2B,kBAAkB,IAAI3B,IAAI,CAAC2B,kBAAkB,CAAC/B,OAAO,CAAE,IAAI,CAAC4B,IAAK,CAAC,IAAI,CAAC,EAAG;QAEnF,MAAM,IAAIhC,KAAK,CAAE,6EAA8E,CAAC;MAEjG,CAAC,MAAM;QAEN;QACA,OAAO,IAAI;MAEZ;IAED;IAEA,OAAO5C,MAAM,CAAC0O,gBAAgB,CAAEJ,YAAY,EAAEhD,SAAS,CAACqD,MAAM,EAAE3M,MAAO,CAAC;EAEzE;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM7B,wBAAwB,CAAC;EAE9BV,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACmD,gBAAgB;IACvC,IAAI,CAAC0H,WAAW,GAAG,IAAI;EAExB;EAEAP,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAM1J,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM5E,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMoD,IAAI,GAAGpD,MAAM,CAACoD,IAAI;IAExB,MAAMmL,UAAU,GAAGnL,IAAI,CAACoL,QAAQ,CAAEF,YAAY,CAAE;IAEhD,IAAK,CAAEC,UAAU,CAAClL,UAAU,IAAI,CAAEkL,UAAU,CAAClL,UAAU,CAAEuB,IAAI,CAAE,EAAG;MAEjE,OAAO,IAAI;IAEZ;IAEA,MAAM0G,SAAS,GAAGiD,UAAU,CAAClL,UAAU,CAAEuB,IAAI,CAAE;IAC/C,MAAM+J,MAAM,GAAGvL,IAAI,CAACyL,MAAM,CAAEvD,SAAS,CAACqD,MAAM,CAAE;IAE9C,IAAI3M,MAAM,GAAGhC,MAAM,CAAC8O,aAAa;IACjC,IAAKH,MAAM,CAACI,GAAG,EAAG;MAEjB,MAAMC,OAAO,GAAGhP,MAAM,CAACyO,OAAO,CAAC/O,OAAO,CAACuP,UAAU,CAAEN,MAAM,CAACI,GAAI,CAAC;MAC/D,IAAKC,OAAO,KAAK,IAAI,EAAGhN,MAAM,GAAGgN,OAAO;IAEzC;IAEA,OAAO,IAAI,CAACE,aAAa,CAAC,CAAC,CAAClF,IAAI,CAAE,UAAW4E,WAAW,EAAG;MAE1D,IAAKA,WAAW,EAAG,OAAO5O,MAAM,CAAC0O,gBAAgB,CAAEJ,YAAY,EAAEhD,SAAS,CAACqD,MAAM,EAAE3M,MAAO,CAAC;MAE3F,IAAKoB,IAAI,CAAC2B,kBAAkB,IAAI3B,IAAI,CAAC2B,kBAAkB,CAAC/B,OAAO,CAAE4B,IAAK,CAAC,IAAI,CAAC,EAAG;QAE9E,MAAM,IAAIhC,KAAK,CAAE,2DAA4D,CAAC;MAE/E;;MAEA;MACA,OAAO5C,MAAM,CAACqO,WAAW,CAAEC,YAAa,CAAC;IAE1C,CAAE,CAAC;EAEJ;EAEAY,aAAaA,CAAA,EAAG;IAEf,IAAK,CAAE,IAAI,CAACN,WAAW,EAAG;MAEzB,IAAI,CAACA,WAAW,GAAG,IAAIhJ,OAAO,CAAE,UAAWC,OAAO,EAAG;QAEpD,MAAMsJ,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;;QAEzB;QACA;QACAD,KAAK,CAACE,GAAG,GAAG,iFAAiF;QAE7FF,KAAK,CAACG,MAAM,GAAGH,KAAK,CAACI,OAAO,GAAG,YAAY;UAE1C1J,OAAO,CAAEsJ,KAAK,CAACK,MAAM,KAAK,CAAE,CAAC;QAE9B,CAAC;MAEF,CAAE,CAAC;IAEJ;IAEA,OAAO,IAAI,CAACZ,WAAW;EAExB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMxO,wBAAwB,CAAC;EAE9BX,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACoD,gBAAgB;IACvC,IAAI,CAACyH,WAAW,GAAG,IAAI;EAExB;EAEAP,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAM1J,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM5E,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMoD,IAAI,GAAGpD,MAAM,CAACoD,IAAI;IAExB,MAAMmL,UAAU,GAAGnL,IAAI,CAACoL,QAAQ,CAAEF,YAAY,CAAE;IAEhD,IAAK,CAAEC,UAAU,CAAClL,UAAU,IAAI,CAAEkL,UAAU,CAAClL,UAAU,CAAEuB,IAAI,CAAE,EAAG;MAEjE,OAAO,IAAI;IAEZ;IAEA,MAAM0G,SAAS,GAAGiD,UAAU,CAAClL,UAAU,CAAEuB,IAAI,CAAE;IAC/C,MAAM+J,MAAM,GAAGvL,IAAI,CAACyL,MAAM,CAAEvD,SAAS,CAACqD,MAAM,CAAE;IAE9C,IAAI3M,MAAM,GAAGhC,MAAM,CAAC8O,aAAa;IACjC,IAAKH,MAAM,CAACI,GAAG,EAAG;MAEjB,MAAMC,OAAO,GAAGhP,MAAM,CAACyO,OAAO,CAAC/O,OAAO,CAACuP,UAAU,CAAEN,MAAM,CAACI,GAAI,CAAC;MAC/D,IAAKC,OAAO,KAAK,IAAI,EAAGhN,MAAM,GAAGgN,OAAO;IAEzC;IAEA,OAAO,IAAI,CAACE,aAAa,CAAC,CAAC,CAAClF,IAAI,CAAE,UAAW4E,WAAW,EAAG;MAE1D,IAAKA,WAAW,EAAG,OAAO5O,MAAM,CAAC0O,gBAAgB,CAAEJ,YAAY,EAAEhD,SAAS,CAACqD,MAAM,EAAE3M,MAAO,CAAC;MAE3F,IAAKoB,IAAI,CAAC2B,kBAAkB,IAAI3B,IAAI,CAAC2B,kBAAkB,CAAC/B,OAAO,CAAE4B,IAAK,CAAC,IAAI,CAAC,EAAG;QAE9E,MAAM,IAAIhC,KAAK,CAAE,2DAA4D,CAAC;MAE/E;;MAEA;MACA,OAAO5C,MAAM,CAACqO,WAAW,CAAEC,YAAa,CAAC;IAE1C,CAAE,CAAC;EAEJ;EAEAY,aAAaA,CAAA,EAAG;IAEf,IAAK,CAAE,IAAI,CAACN,WAAW,EAAG;MAEzB,IAAI,CAACA,WAAW,GAAG,IAAIhJ,OAAO,CAAE,UAAWC,OAAO,EAAG;QAEpD,MAAMsJ,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;;QAEzB;QACAD,KAAK,CAACE,GAAG,GAAG,ibAAib;QAC7bF,KAAK,CAACG,MAAM,GAAGH,KAAK,CAACI,OAAO,GAAG,YAAY;UAE1C1J,OAAO,CAAEsJ,KAAK,CAACK,MAAM,KAAK,CAAE,CAAC;QAE9B,CAAC;MAEF,CAAE,CAAC;IAEJ;IAEA,OAAO,IAAI,CAACZ,WAAW;EAExB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM9N,sBAAsB,CAAC;EAE5BrB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACqD,uBAAuB;IAC9C,IAAI,CAACpH,MAAM,GAAGA,MAAM;EAErB;EAEAyP,cAAcA,CAAE5F,KAAK,EAAG;IAEvB,MAAMzG,IAAI,GAAG,IAAI,CAACpD,MAAM,CAACoD,IAAI;IAC7B,MAAMsM,UAAU,GAAGtM,IAAI,CAACuM,WAAW,CAAE9F,KAAK,CAAE;IAE5C,IAAK6F,UAAU,CAACrM,UAAU,IAAIqM,UAAU,CAACrM,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,EAAG;MAElE,MAAMgL,YAAY,GAAGF,UAAU,CAACrM,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;MAEvD,MAAMiL,MAAM,GAAG,IAAI,CAAC7P,MAAM,CAAC4J,aAAa,CAAE,QAAQ,EAAEgG,YAAY,CAACC,MAAO,CAAC;MACzE,MAAMC,OAAO,GAAG,IAAI,CAAC9P,MAAM,CAACyO,OAAO,CAAC5O,cAAc;MAElD,IAAK,CAAEiQ,OAAO,IAAI,CAAEA,OAAO,CAACC,SAAS,EAAG;QAEvC,IAAK3M,IAAI,CAAC2B,kBAAkB,IAAI3B,IAAI,CAAC2B,kBAAkB,CAAC/B,OAAO,CAAE,IAAI,CAAC4B,IAAK,CAAC,IAAI,CAAC,EAAG;UAEnF,MAAM,IAAIhC,KAAK,CAAE,oFAAqF,CAAC;QAExG,CAAC,MAAM;UAEN;UACA,OAAO,IAAI;QAEZ;MAED;MAEA,OAAOiN,MAAM,CAAC7F,IAAI,CAAE,UAAWgG,GAAG,EAAG;QAEpC,MAAMC,UAAU,GAAGL,YAAY,CAACK,UAAU,IAAI,CAAC;QAC/C,MAAMC,UAAU,GAAGN,YAAY,CAACM,UAAU,IAAI,CAAC;QAE/C,MAAMC,KAAK,GAAGP,YAAY,CAACO,KAAK;QAChC,MAAMC,MAAM,GAAGR,YAAY,CAACS,UAAU;QAEtC,MAAM1B,MAAM,GAAG,IAAI9K,UAAU,CAAEmM,GAAG,EAAEC,UAAU,EAAEC,UAAW,CAAC;QAE5D,IAAKJ,OAAO,CAACQ,qBAAqB,EAAG;UAEpC,OAAOR,OAAO,CAACQ,qBAAqB,CAAEH,KAAK,EAAEC,MAAM,EAAEzB,MAAM,EAAEiB,YAAY,CAACW,IAAI,EAAEX,YAAY,CAACY,MAAO,CAAC,CAACxG,IAAI,CAAE,UAAWgG,GAAG,EAAG;YAE5H,OAAOA,GAAG,CAACH,MAAM;UAElB,CAAE,CAAC;QAEJ,CAAC,MAAM;UAEN;UACA,OAAOC,OAAO,CAACW,KAAK,CAACzG,IAAI,CAAE,YAAY;YAEtC,MAAM0G,MAAM,GAAG,IAAIhN,WAAW,CAAEyM,KAAK,GAAGC,MAAO,CAAC;YAChDN,OAAO,CAACa,gBAAgB,CAAE,IAAI9M,UAAU,CAAE6M,MAAO,CAAC,EAAEP,KAAK,EAAEC,MAAM,EAAEzB,MAAM,EAAEiB,YAAY,CAACW,IAAI,EAAEX,YAAY,CAACY,MAAO,CAAC;YACnH,OAAOE,MAAM;UAEd,CAAE,CAAC;QAEJ;MAED,CAAE,CAAC;IAEJ,CAAC,MAAM;MAEN,OAAO,IAAI;IAEZ;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM3P,qBAAqB,CAAC;EAE3BtB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAAC4E,IAAI,GAAGb,UAAU,CAACsD,uBAAuB;IAC9C,IAAI,CAACrH,MAAM,GAAGA,MAAM;EAErB;EAEA4Q,cAAcA,CAAEhJ,SAAS,EAAG;IAE3B,MAAMxE,IAAI,GAAG,IAAI,CAACpD,MAAM,CAACoD,IAAI;IAC7B,MAAM0E,OAAO,GAAG1E,IAAI,CAACuE,KAAK,CAAEC,SAAS,CAAE;IAEvC,IAAK,CAAEE,OAAO,CAACzE,UAAU,IAAI,CAAEyE,OAAO,CAACzE,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,IAC7DkD,OAAO,CAAC+I,IAAI,KAAKzM,SAAS,EAAG;MAE7B,OAAO,IAAI;IAEZ;IAEA,MAAM0M,OAAO,GAAG1N,IAAI,CAAC2N,MAAM,CAAEjJ,OAAO,CAAC+I,IAAI,CAAE;;IAE3C;;IAEA,KAAM,MAAMG,SAAS,IAAIF,OAAO,CAACG,UAAU,EAAG;MAE7C,IAAKD,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACC,SAAS,IAC/CH,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACE,cAAc,IACjDJ,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACG,YAAY,IAC/CL,SAAS,CAACT,IAAI,KAAKnM,SAAS,EAAG;QAEhC,OAAO,IAAI;MAEZ;IAED;IAEA,MAAMwL,YAAY,GAAG9H,OAAO,CAACzE,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE;IACpD,MAAM0M,aAAa,GAAG1B,YAAY,CAAC2B,UAAU;;IAE7C;;IAEA,MAAMjH,OAAO,GAAG,EAAE;IAClB,MAAMiH,UAAU,GAAG,CAAC,CAAC;IAErB,KAAM,MAAMrL,GAAG,IAAIoL,aAAa,EAAG;MAElChH,OAAO,CAACrH,IAAI,CAAE,IAAI,CAACjD,MAAM,CAAC4J,aAAa,CAAE,UAAU,EAAE0H,aAAa,CAAEpL,GAAG,CAAG,CAAC,CAAC8D,IAAI,CAAEwH,QAAQ,IAAI;QAE7FD,UAAU,CAAErL,GAAG,CAAE,GAAGsL,QAAQ;QAC5B,OAAOD,UAAU,CAAErL,GAAG,CAAE;MAEzB,CAAE,CAAE,CAAC;IAEN;IAEA,IAAKoE,OAAO,CAAC5F,MAAM,GAAG,CAAC,EAAG;MAEzB,OAAO,IAAI;IAEZ;IAEA4F,OAAO,CAACrH,IAAI,CAAE,IAAI,CAACjD,MAAM,CAAC4Q,cAAc,CAAEhJ,SAAU,CAAE,CAAC;IAEvD,OAAOhC,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAEyH,OAAO,IAAI;MAE9C,MAAMC,UAAU,GAAGD,OAAO,CAACE,GAAG,CAAC,CAAC;MAChC,MAAMZ,MAAM,GAAGW,UAAU,CAACE,OAAO,GAAGF,UAAU,CAACG,QAAQ,GAAG,CAAEH,UAAU,CAAE;MACxE,MAAMvB,KAAK,GAAGsB,OAAO,CAAE,CAAC,CAAE,CAACtB,KAAK,CAAC,CAAC;MAClC,MAAM2B,eAAe,GAAG,EAAE;MAE1B,KAAM,MAAMjB,IAAI,IAAIE,MAAM,EAAG;QAE5B;QACA,MAAMgB,CAAC,GAAG,IAAIxU,OAAO,CAAC,CAAC;QACvB,MAAMyU,CAAC,GAAG,IAAI5S,OAAO,CAAC,CAAC;QACvB,MAAM6S,CAAC,GAAG,IAAIzT,UAAU,CAAC,CAAC;QAC1B,MAAM0T,CAAC,GAAG,IAAI9S,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;QAEhC,MAAM+S,aAAa,GAAG,IAAI7V,aAAa,CAAEuU,IAAI,CAACuB,QAAQ,EAAEvB,IAAI,CAACwB,QAAQ,EAAElC,KAAM,CAAC;QAE9E,KAAM,IAAI1L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0L,KAAK,EAAE1L,CAAC,EAAG,EAAG;UAElC,IAAK8M,UAAU,CAACe,WAAW,EAAG;YAE7BN,CAAC,CAACO,mBAAmB,CAAEhB,UAAU,CAACe,WAAW,EAAE7N,CAAE,CAAC;UAEnD;UAEA,IAAK8M,UAAU,CAACiB,QAAQ,EAAG;YAE1BP,CAAC,CAACM,mBAAmB,CAAEhB,UAAU,CAACiB,QAAQ,EAAE/N,CAAE,CAAC;UAEhD;UAEA,IAAK8M,UAAU,CAACkB,KAAK,EAAG;YAEvBP,CAAC,CAACK,mBAAmB,CAAEhB,UAAU,CAACkB,KAAK,EAAEhO,CAAE,CAAC;UAE7C;UAEA0N,aAAa,CAACO,WAAW,CAAEjO,CAAC,EAAEsN,CAAC,CAACY,OAAO,CAAEX,CAAC,EAAEC,CAAC,EAAEC,CAAE,CAAE,CAAC;QAErD;;QAEA;QACA,KAAM,MAAMU,aAAa,IAAIrB,UAAU,EAAG;UAEzC,IAAKqB,aAAa,KAAK,aAAa,IAClCA,aAAa,KAAK,UAAU,IAC5BA,aAAa,KAAK,OAAO,EAAG;YAE7B/B,IAAI,CAACuB,QAAQ,CAACS,YAAY,CAAED,aAAa,EAAErB,UAAU,CAAEqB,aAAa,CAAG,CAAC;UAEzE;QAED;;QAEA;QACA3U,QAAQ,CAAC6U,SAAS,CAACC,IAAI,CAACC,IAAI,CAAEb,aAAa,EAAEtB,IAAK,CAAC;QAEnD,IAAI,CAAC7Q,MAAM,CAACiT,mBAAmB,CAAEd,aAAc,CAAC;QAEhDL,eAAe,CAAC7O,IAAI,CAAEkP,aAAc,CAAC;MAEtC;MAEA,IAAKT,UAAU,CAACE,OAAO,EAAG;QAEzBF,UAAU,CAACwB,KAAK,CAAC,CAAC;QAElBxB,UAAU,CAACvL,GAAG,CAAE,GAAI2L,eAAgB,CAAC;QAErC,OAAOJ,UAAU;MAElB;MAEA,OAAOI,eAAe,CAAE,CAAC,CAAE;IAE5B,CAAE,CAAC;EAEJ;AAED;;AAEA;AACA,MAAMhO,6BAA6B,GAAG,MAAM;AAC5C,MAAMqP,8BAA8B,GAAG,EAAE;AACzC,MAAMC,4BAA4B,GAAG;EAAE3P,IAAI,EAAE,UAAU;EAAE4P,GAAG,EAAE;AAAW,CAAC;AAE1E,MAAMpP,mBAAmB,CAAC;EAEzBxE,WAAWA,CAAE8C,IAAI,EAAG;IAEnB,IAAI,CAACqC,IAAI,GAAGb,UAAU,CAACC,eAAe;IACtC,IAAI,CAACE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACoP,IAAI,GAAG,IAAI;IAEhB,MAAMC,UAAU,GAAG,IAAIC,QAAQ,CAAEjR,IAAI,EAAE,CAAC,EAAE4Q,8BAA+B,CAAC;IAC1E,MAAM5P,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IAErC,IAAI,CAACiQ,MAAM,GAAG;MACb9P,KAAK,EAAEJ,WAAW,CAACK,MAAM,CAAE,IAAIC,UAAU,CAAEtB,IAAI,CAACmR,KAAK,CAAE,CAAC,EAAE,CAAE,CAAE,CAAE,CAAC;MACjErP,OAAO,EAAEkP,UAAU,CAACI,SAAS,CAAE,CAAC,EAAE,IAAK,CAAC;MACxCjP,MAAM,EAAE6O,UAAU,CAACI,SAAS,CAAE,CAAC,EAAE,IAAK;IACvC,CAAC;IAED,IAAK,IAAI,CAACF,MAAM,CAAC9P,KAAK,KAAKG,6BAA6B,EAAG;MAE1D,MAAM,IAAIlB,KAAK,CAAE,mDAAoD,CAAC;IAEvE,CAAC,MAAM,IAAK,IAAI,CAAC6Q,MAAM,CAACpP,OAAO,GAAG,GAAG,EAAG;MAEvC,MAAM,IAAIzB,KAAK,CAAE,gDAAiD,CAAC;IAEpE;IAEA,MAAMgR,mBAAmB,GAAG,IAAI,CAACH,MAAM,CAAC/O,MAAM,GAAGyO,8BAA8B;IAC/E,MAAMU,SAAS,GAAG,IAAIL,QAAQ,CAAEjR,IAAI,EAAE4Q,8BAA+B,CAAC;IACtE,IAAIW,UAAU,GAAG,CAAC;IAElB,OAAQA,UAAU,GAAGF,mBAAmB,EAAG;MAE1C,MAAMG,WAAW,GAAGF,SAAS,CAACF,SAAS,CAAEG,UAAU,EAAE,IAAK,CAAC;MAC3DA,UAAU,IAAI,CAAC;MAEf,MAAME,SAAS,GAAGH,SAAS,CAACF,SAAS,CAAEG,UAAU,EAAE,IAAK,CAAC;MACzDA,UAAU,IAAI,CAAC;MAEf,IAAKE,SAAS,KAAKZ,4BAA4B,CAAC3P,IAAI,EAAG;QAEtD,MAAMwQ,YAAY,GAAG,IAAIpQ,UAAU,CAAEtB,IAAI,EAAE4Q,8BAA8B,GAAGW,UAAU,EAAEC,WAAY,CAAC;QACrG,IAAI,CAAC7P,OAAO,GAAGX,WAAW,CAACK,MAAM,CAAEqQ,YAAa,CAAC;MAElD,CAAC,MAAM,IAAKD,SAAS,KAAKZ,4BAA4B,CAACC,GAAG,EAAG;QAE5D,MAAMpD,UAAU,GAAGkD,8BAA8B,GAAGW,UAAU;QAC9D,IAAI,CAACR,IAAI,GAAG/Q,IAAI,CAACmR,KAAK,CAAEzD,UAAU,EAAEA,UAAU,GAAG8D,WAAY,CAAC;MAE/D;;MAEA;;MAEAD,UAAU,IAAIC,WAAW;IAE1B;IAEA,IAAK,IAAI,CAAC7P,OAAO,KAAK,IAAI,EAAG;MAE5B,MAAM,IAAItB,KAAK,CAAE,2CAA4C,CAAC;IAE/D;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMuC,iCAAiC,CAAC;EAEvC1F,WAAWA,CAAE2D,IAAI,EAAEzD,WAAW,EAAG;IAEhC,IAAK,CAAEA,WAAW,EAAG;MAEpB,MAAM,IAAIiD,KAAK,CAAE,qDAAsD,CAAC;IAEzE;IAEA,IAAI,CAACgC,IAAI,GAAGb,UAAU,CAACmB,0BAA0B;IACjD,IAAI,CAAC9B,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACzD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACA,WAAW,CAACuU,OAAO,CAAC,CAAC;EAE3B;EAEAC,eAAeA,CAAEnD,SAAS,EAAEhR,MAAM,EAAG;IAEpC,MAAMoD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMzD,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAMyU,eAAe,GAAGpD,SAAS,CAAC3N,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,CAAC8K,UAAU;IACpE,MAAM2E,gBAAgB,GAAGrD,SAAS,CAAC3N,UAAU,CAAE,IAAI,CAACuB,IAAI,CAAE,CAAC2M,UAAU;IACrE,MAAM+C,iBAAiB,GAAG,CAAC,CAAC;IAC5B,MAAMC,sBAAsB,GAAG,CAAC,CAAC;IACjC,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAE3B,KAAM,MAAM5B,aAAa,IAAIyB,gBAAgB,EAAG;MAE/C,MAAMI,kBAAkB,GAAGC,UAAU,CAAE9B,aAAa,CAAE,IAAIA,aAAa,CAAC+B,WAAW,CAAC,CAAC;MAErFL,iBAAiB,CAAEG,kBAAkB,CAAE,GAAGJ,gBAAgB,CAAEzB,aAAa,CAAE;IAE5E;IAEA,KAAM,MAAMA,aAAa,IAAI5B,SAAS,CAACO,UAAU,EAAG;MAEnD,MAAMkD,kBAAkB,GAAGC,UAAU,CAAE9B,aAAa,CAAE,IAAIA,aAAa,CAAC+B,WAAW,CAAC,CAAC;MAErF,IAAKN,gBAAgB,CAAEzB,aAAa,CAAE,KAAKxO,SAAS,EAAG;QAEtD,MAAMwQ,WAAW,GAAGxR,IAAI,CAACyR,SAAS,CAAE7D,SAAS,CAACO,UAAU,CAAEqB,aAAa,CAAE,CAAE;QAC3E,MAAMkC,aAAa,GAAGC,qBAAqB,CAAEH,WAAW,CAACE,aAAa,CAAE;QAExEN,gBAAgB,CAAEC,kBAAkB,CAAE,GAAGK,aAAa,CAAClQ,IAAI;QAC3D2P,sBAAsB,CAAEE,kBAAkB,CAAE,GAAGG,WAAW,CAACI,UAAU,KAAK,IAAI;MAE/E;IAED;IAEA,OAAOhV,MAAM,CAAC4J,aAAa,CAAE,YAAY,EAAEwK,eAAgB,CAAC,CAACpK,IAAI,CAAE,UAAW0F,UAAU,EAAG;MAE1F,OAAO,IAAI9J,OAAO,CAAE,UAAWC,OAAO,EAAG;QAExClG,WAAW,CAACsV,eAAe,CAAEvF,UAAU,EAAE,UAAW0C,QAAQ,EAAG;UAE9D,KAAM,MAAMQ,aAAa,IAAIR,QAAQ,CAACb,UAAU,EAAG;YAElD,MAAM2D,SAAS,GAAG9C,QAAQ,CAACb,UAAU,CAAEqB,aAAa,CAAE;YACtD,MAAMoC,UAAU,GAAGT,sBAAsB,CAAE3B,aAAa,CAAE;YAE1D,IAAKoC,UAAU,KAAK5Q,SAAS,EAAG8Q,SAAS,CAACF,UAAU,GAAGA,UAAU;UAElE;UAEAnP,OAAO,CAAEuM,QAAS,CAAC;QAEpB,CAAC,EAAEkC,iBAAiB,EAAEE,gBAAiB,CAAC;MAEzC,CAAE,CAAC;IAEJ,CAAE,CAAC;EAEJ;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMnP,6BAA6B,CAAC;EAEnC5F,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACmF,IAAI,GAAGb,UAAU,CAACqB,qBAAqB;EAE7C;EAEA+P,aAAaA,CAAEC,OAAO,EAAEC,SAAS,EAAG;IAEnC,IAAK,CAAEA,SAAS,CAACC,QAAQ,KAAKlR,SAAS,IAAIiR,SAAS,CAACC,QAAQ,KAAKF,OAAO,CAACG,OAAO,KAC7EF,SAAS,CAACG,MAAM,KAAKpR,SAAS,IAC9BiR,SAAS,CAACI,QAAQ,KAAKrR,SAAS,IAChCiR,SAAS,CAACvJ,KAAK,KAAK1H,SAAS,EAAG;MAEnC;MACA,OAAOgR,OAAO;IAEf;IAEAA,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC,CAAC;IAEzB,IAAKL,SAAS,CAACC,QAAQ,KAAKlR,SAAS,EAAG;MAEvCgR,OAAO,CAACG,OAAO,GAAGF,SAAS,CAACC,QAAQ;IAErC;IAEA,IAAKD,SAAS,CAACG,MAAM,KAAKpR,SAAS,EAAG;MAErCgR,OAAO,CAACI,MAAM,CAAC9M,SAAS,CAAE2M,SAAS,CAACG,MAAO,CAAC;IAE7C;IAEA,IAAKH,SAAS,CAACI,QAAQ,KAAKrR,SAAS,EAAG;MAEvCgR,OAAO,CAACK,QAAQ,GAAGJ,SAAS,CAACI,QAAQ;IAEtC;IAEA,IAAKJ,SAAS,CAACvJ,KAAK,KAAK1H,SAAS,EAAG;MAEpCgR,OAAO,CAACO,MAAM,CAACjN,SAAS,CAAE2M,SAAS,CAACvJ,KAAM,CAAC;IAE5C;IAEAsJ,OAAO,CAACQ,WAAW,GAAG,IAAI;IAE1B,OAAOR,OAAO;EAEf;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM7P,6BAA6B,CAAC;EAEnC9F,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACmF,IAAI,GAAGb,UAAU,CAACuB,qBAAqB;EAE7C;AAED;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAMuQ,0BAA0B,SAASpZ,WAAW,CAAC;EAEpDgD,WAAWA,CAAEqW,kBAAkB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAG;IAEzE,KAAK,CAAEH,kBAAkB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,YAAa,CAAC;EAEpE;EAEAC,gBAAgBA,CAAErM,KAAK,EAAG;IAEzB;IACA;;IAEA,MAAM6G,MAAM,GAAG,IAAI,CAACuF,YAAY;MAC/BE,MAAM,GAAG,IAAI,CAACJ,YAAY;MAC1BK,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1BZ,MAAM,GAAG3L,KAAK,GAAGuM,SAAS,GAAG,CAAC,GAAGA,SAAS;IAE3C,KAAM,IAAI3R,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAK2R,SAAS,EAAE3R,CAAC,EAAG,EAAG;MAExCiM,MAAM,CAAEjM,CAAC,CAAE,GAAG0R,MAAM,CAAEX,MAAM,GAAG/Q,CAAC,CAAE;IAEnC;IAEA,OAAOiM,MAAM;EAEd;EAEA2F,YAAYA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAG;IAE7B,MAAM/F,MAAM,GAAG,IAAI,CAACuF,YAAY;IAChC,MAAME,MAAM,GAAG,IAAI,CAACJ,YAAY;IAChC,MAAM3F,MAAM,GAAG,IAAI,CAACgG,SAAS;IAE7B,MAAMM,OAAO,GAAGtG,MAAM,GAAG,CAAC;IAC1B,MAAMuG,OAAO,GAAGvG,MAAM,GAAG,CAAC;IAE1B,MAAMwG,EAAE,GAAGH,EAAE,GAAGF,EAAE;IAElB,MAAMvE,CAAC,GAAG,CAAEwE,CAAC,GAAGD,EAAE,IAAKK,EAAE;IACzB,MAAMC,EAAE,GAAG7E,CAAC,GAAGA,CAAC;IAChB,MAAM8E,GAAG,GAAGD,EAAE,GAAG7E,CAAC;IAElB,MAAM+E,OAAO,GAAGT,EAAE,GAAGK,OAAO;IAC5B,MAAMK,OAAO,GAAGD,OAAO,GAAGJ,OAAO;IAEjC,MAAMM,EAAE,GAAG,CAAE,CAAC,GAAGH,GAAG,GAAG,CAAC,GAAGD,EAAE;IAC7B,MAAMK,EAAE,GAAGJ,GAAG,GAAGD,EAAE;IACnB,MAAMM,EAAE,GAAG,CAAC,GAAGF,EAAE;IACjB,MAAMG,EAAE,GAAGF,EAAE,GAAGL,EAAE,GAAG7E,CAAC;;IAEtB;IACA;IACA,KAAM,IAAIvN,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAK2L,MAAM,EAAE3L,CAAC,EAAG,EAAG;MAErC,MAAM4S,EAAE,GAAGlB,MAAM,CAAEa,OAAO,GAAGvS,CAAC,GAAG2L,MAAM,CAAE,CAAC,CAAC;MAC3C,MAAMkH,EAAE,GAAGnB,MAAM,CAAEa,OAAO,GAAGvS,CAAC,GAAGiS,OAAO,CAAE,GAAGE,EAAE,CAAC,CAAC;MACjD,MAAMW,EAAE,GAAGpB,MAAM,CAAEY,OAAO,GAAGtS,CAAC,GAAG2L,MAAM,CAAE,CAAC,CAAC;MAC3C,MAAMoH,EAAE,GAAGrB,MAAM,CAAEY,OAAO,GAAGtS,CAAC,CAAE,GAAGmS,EAAE,CAAC,CAAC;;MAEvClG,MAAM,CAAEjM,CAAC,CAAE,GAAG0S,EAAE,GAAGE,EAAE,GAAGD,EAAE,GAAGE,EAAE,GAAGL,EAAE,GAAGM,EAAE,GAAGL,EAAE,GAAGM,EAAE;IAEpD;IAEA,OAAO9G,MAAM;EAEd;AAED;AAEA,MAAM+G,EAAE,GAAG,IAAIjZ,UAAU,CAAC,CAAC;AAE3B,MAAMkZ,oCAAoC,SAAS7B,0BAA0B,CAAC;EAE7EQ,YAAYA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAG;IAE7B,MAAM/F,MAAM,GAAG,KAAK,CAAC2F,YAAY,CAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAG,CAAC;IAElDgB,EAAE,CAAC/O,SAAS,CAAEgI,MAAO,CAAC,CAACiH,SAAS,CAAC,CAAC,CAACC,OAAO,CAAElH,MAAO,CAAC;IAEpD,OAAOA,MAAM;EAEd;AAED;;AAGA;AACA;AACA;;AAEA;;AAEA,MAAMQ,eAAe,GAAG;EACvB2G,KAAK,EAAE,IAAI;EACX;EACAC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE,CAAC;EACbtH,SAAS,EAAE,CAAC;EACZC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfqH,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE;AACjB,CAAC;AAED,MAAM5D,qBAAqB,GAAG;EAC7B,IAAI,EAAE6D,SAAS;EACf,IAAI,EAAE/U,UAAU;EAChB,IAAI,EAAEgV,UAAU;EAChB,IAAI,EAAEC,WAAW;EACjB,IAAI,EAAEC,WAAW;EACjB,IAAI,EAAEC;AACP,CAAC;AAED,MAAMC,aAAa,GAAG;EACrB,IAAI,EAAEpb,aAAa;EACnB,IAAI,EAAEb,YAAY;EAClB,IAAI,EAAEe,0BAA0B;EAChC,IAAI,EAAEb,yBAAyB;EAC/B,IAAI,EAAEY,yBAAyB;EAC/B,IAAI,EAAEb;AACP,CAAC;AAED,MAAMic,eAAe,GAAG;EACvB,KAAK,EAAEpd,mBAAmB;EAC1B,KAAK,EAAE8B,sBAAsB;EAC7B,KAAK,EAAEc;AACR,CAAC;AAED,MAAMya,gBAAgB,GAAG;EACxB,QAAQ,EAAE,CAAC;EACX,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE;AACT,CAAC;AAED,MAAMzE,UAAU,GAAG;EAClB0E,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE;AACX,CAAC;AAED,MAAMC,eAAe,GAAG;EACvBhO,KAAK,EAAE,OAAO;EACdiO,WAAW,EAAE,UAAU;EACvBtE,QAAQ,EAAE,YAAY;EACtBuE,OAAO,EAAE;AACV,CAAC;AAED,MAAMC,aAAa,GAAG;EACrBC,WAAW,EAAE9V,SAAS;EAAE;EACC;EACzB+T,MAAM,EAAExb,iBAAiB;EACzBwd,IAAI,EAAEzd;AACP,CAAC;AAED,MAAM0d,WAAW,GAAG;EACnBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA,SAASC,qBAAqBA,CAAElT,KAAK,EAAG;EAEvC,IAAKA,KAAK,CAAE,iBAAiB,CAAE,KAAKlD,SAAS,EAAG;IAE/CkD,KAAK,CAAE,iBAAiB,CAAE,GAAG,IAAI3J,oBAAoB,CAAE;MACtD8K,KAAK,EAAE,QAAQ;MACfgS,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE3e;IACP,CAAE,CAAC;EAEJ;EAEA,OAAOmL,KAAK,CAAE,iBAAiB,CAAE;AAElC;AAEA,SAASyT,8BAA8BA,CAAEC,eAAe,EAAE5U,MAAM,EAAE6U,SAAS,EAAG;EAE7E;;EAEA,KAAM,MAAMrW,IAAI,IAAIqW,SAAS,CAAC5X,UAAU,EAAG;IAE1C,IAAK2X,eAAe,CAAEpW,IAAI,CAAE,KAAKR,SAAS,EAAG;MAE5CgC,MAAM,CAAC8U,QAAQ,CAACC,cAAc,GAAG/U,MAAM,CAAC8U,QAAQ,CAACC,cAAc,IAAI,CAAC,CAAC;MACrE/U,MAAM,CAAC8U,QAAQ,CAACC,cAAc,CAAEvW,IAAI,CAAE,GAAGqW,SAAS,CAAC5X,UAAU,CAAEuB,IAAI,CAAE;IAEtE;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA,SAAS6E,sBAAsBA,CAAErD,MAAM,EAAEgV,OAAO,EAAG;EAElD,IAAKA,OAAO,CAACC,MAAM,KAAKjX,SAAS,EAAG;IAEnC,IAAK,OAAOgX,OAAO,CAACC,MAAM,KAAK,QAAQ,EAAG;MAEzCC,MAAM,CAACC,MAAM,CAAEnV,MAAM,CAAC8U,QAAQ,EAAEE,OAAO,CAACC,MAAO,CAAC;IAEjD,CAAC,MAAM;MAENzZ,OAAO,CAAC4D,IAAI,CAAE,qDAAqD,GAAG4V,OAAO,CAACC,MAAO,CAAC;IAEvF;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAAEpJ,QAAQ,EAAEqJ,OAAO,EAAEzb,MAAM,EAAG;EAErD,IAAI0b,gBAAgB,GAAG,KAAK;EAC5B,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,aAAa,GAAG,KAAK;EAEzB,KAAM,IAAInX,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGJ,OAAO,CAAC/W,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;IAEpD,MAAMoE,MAAM,GAAG4S,OAAO,CAAEhX,CAAC,CAAE;IAE3B,IAAKoE,MAAM,CAACuQ,QAAQ,KAAKhV,SAAS,EAAGsX,gBAAgB,GAAG,IAAI;IAC5D,IAAK7S,MAAM,CAACwQ,MAAM,KAAKjV,SAAS,EAAGuX,cAAc,GAAG,IAAI;IACxD,IAAK9S,MAAM,CAAC8Q,OAAO,KAAKvV,SAAS,EAAGwX,aAAa,GAAG,IAAI;IAExD,IAAKF,gBAAgB,IAAIC,cAAc,IAAIC,aAAa,EAAG;EAE5D;EAEA,IAAK,CAAEF,gBAAgB,IAAI,CAAEC,cAAc,IAAI,CAAEC,aAAa,EAAG,OAAOhW,OAAO,CAACC,OAAO,CAAEuM,QAAS,CAAC;EAEnG,MAAM0J,wBAAwB,GAAG,EAAE;EACnC,MAAMC,sBAAsB,GAAG,EAAE;EACjC,MAAMC,qBAAqB,GAAG,EAAE;EAEhC,KAAM,IAAIvX,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGJ,OAAO,CAAC/W,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;IAEpD,MAAMoE,MAAM,GAAG4S,OAAO,CAAEhX,CAAC,CAAE;IAE3B,IAAKiX,gBAAgB,EAAG;MAEvB,MAAMO,eAAe,GAAGpT,MAAM,CAACuQ,QAAQ,KAAKhV,SAAS,GAClDpE,MAAM,CAAC4J,aAAa,CAAE,UAAU,EAAEf,MAAM,CAACuQ,QAAS,CAAC,GACnDhH,QAAQ,CAACb,UAAU,CAACzI,QAAQ;MAE/BgT,wBAAwB,CAAC7Y,IAAI,CAAEgZ,eAAgB,CAAC;IAEjD;IAEA,IAAKN,cAAc,EAAG;MAErB,MAAMM,eAAe,GAAGpT,MAAM,CAACwQ,MAAM,KAAKjV,SAAS,GAChDpE,MAAM,CAAC4J,aAAa,CAAE,UAAU,EAAEf,MAAM,CAACwQ,MAAO,CAAC,GACjDjH,QAAQ,CAACb,UAAU,CAAC2K,MAAM;MAE7BH,sBAAsB,CAAC9Y,IAAI,CAAEgZ,eAAgB,CAAC;IAE/C;IAEA,IAAKL,aAAa,EAAG;MAEpB,MAAMK,eAAe,GAAGpT,MAAM,CAAC8Q,OAAO,KAAKvV,SAAS,GACjDpE,MAAM,CAAC4J,aAAa,CAAE,UAAU,EAAEf,MAAM,CAAC8Q,OAAQ,CAAC,GAClDvH,QAAQ,CAACb,UAAU,CAAC9I,KAAK;MAE5BuT,qBAAqB,CAAC/Y,IAAI,CAAEgZ,eAAgB,CAAC;IAE9C;EAED;EAEA,OAAOrW,OAAO,CAACoF,GAAG,CAAE,CACnBpF,OAAO,CAACoF,GAAG,CAAE8Q,wBAAyB,CAAC,EACvClW,OAAO,CAACoF,GAAG,CAAE+Q,sBAAuB,CAAC,EACrCnW,OAAO,CAACoF,GAAG,CAAEgR,qBAAsB,CAAC,CACnC,CAAC,CAAChS,IAAI,CAAE,UAAW6K,SAAS,EAAG;IAEhC,MAAMsH,cAAc,GAAGtH,SAAS,CAAE,CAAC,CAAE;IACrC,MAAMuH,YAAY,GAAGvH,SAAS,CAAE,CAAC,CAAE;IACnC,MAAMwH,WAAW,GAAGxH,SAAS,CAAE,CAAC,CAAE;IAElC,IAAK6G,gBAAgB,EAAGtJ,QAAQ,CAACkK,eAAe,CAACxT,QAAQ,GAAGqT,cAAc;IAC1E,IAAKR,cAAc,EAAGvJ,QAAQ,CAACkK,eAAe,CAACJ,MAAM,GAAGE,YAAY;IACpE,IAAKR,aAAa,EAAGxJ,QAAQ,CAACkK,eAAe,CAAC7T,KAAK,GAAG4T,WAAW;IACjEjK,QAAQ,CAACmK,oBAAoB,GAAG,IAAI;IAEpC,OAAOnK,QAAQ;EAEhB,CAAE,CAAC;AAEJ;;AAEA;AACA;AACA;AACA;AACA,SAASoK,kBAAkBA,CAAE3L,IAAI,EAAEC,OAAO,EAAG;EAE5CD,IAAI,CAAC2L,kBAAkB,CAAC,CAAC;EAEzB,IAAK1L,OAAO,CAACkJ,OAAO,KAAK5V,SAAS,EAAG;IAEpC,KAAM,IAAIK,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG/K,OAAO,CAACkJ,OAAO,CAACtV,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAE5DoM,IAAI,CAAC4L,qBAAqB,CAAEhY,CAAC,CAAE,GAAGqM,OAAO,CAACkJ,OAAO,CAAEvV,CAAC,CAAE;IAEvD;EAED;;EAEA;EACA,IAAKqM,OAAO,CAACuK,MAAM,IAAI3Q,KAAK,CAACC,OAAO,CAAEmG,OAAO,CAACuK,MAAM,CAACqB,WAAY,CAAC,EAAG;IAEpE,MAAMA,WAAW,GAAG5L,OAAO,CAACuK,MAAM,CAACqB,WAAW;IAE9C,IAAK7L,IAAI,CAAC4L,qBAAqB,CAAC/X,MAAM,KAAKgY,WAAW,CAAChY,MAAM,EAAG;MAE/DmM,IAAI,CAAC8L,qBAAqB,GAAG,CAAC,CAAC;MAE/B,KAAM,IAAIlY,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGa,WAAW,CAAChY,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAExDoM,IAAI,CAAC8L,qBAAqB,CAAED,WAAW,CAAEjY,CAAC,CAAE,CAAE,GAAGA,CAAC;MAEnD;IAED,CAAC,MAAM;MAEN7C,OAAO,CAAC4D,IAAI,CAAE,sEAAuE,CAAC;IAEvF;EAED;AAED;AAEA,SAASoX,kBAAkBA,CAAEC,YAAY,EAAG;EAE3C,IAAIC,WAAW;EAEf,MAAMC,cAAc,GAAGF,YAAY,CAACxZ,UAAU,IAAIwZ,YAAY,CAACxZ,UAAU,CAAEU,UAAU,CAACmB,0BAA0B,CAAE;EAElH,IAAK6X,cAAc,EAAG;IAErBD,WAAW,GAAG,QAAQ,GAAGC,cAAc,CAACrN,UAAU,GAC9C,GAAG,GAAGqN,cAAc,CAACC,OAAO,GAC5B,GAAG,GAAGC,mBAAmB,CAAEF,cAAc,CAACxL,UAAW,CAAC;EAE3D,CAAC,MAAM;IAENuL,WAAW,GAAGD,YAAY,CAACG,OAAO,GAAG,GAAG,GAAGC,mBAAmB,CAAEJ,YAAY,CAACtL,UAAW,CAAC,GAAG,GAAG,GAAGsL,YAAY,CAACtM,IAAI;EAEpH;EAEA,IAAKsM,YAAY,CAACpB,OAAO,KAAKrX,SAAS,EAAG;IAEzC,KAAM,IAAIK,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGgB,YAAY,CAACpB,OAAO,CAAC/W,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAEjEqY,WAAW,IAAI,GAAG,GAAGG,mBAAmB,CAAEJ,YAAY,CAACpB,OAAO,CAAEhX,CAAC,CAAG,CAAC;IAEtE;EAED;EAEA,OAAOqY,WAAW;AAEnB;AAEA,SAASG,mBAAmBA,CAAE1L,UAAU,EAAG;EAE1C,IAAI2L,aAAa,GAAG,EAAE;EAEtB,MAAMC,IAAI,GAAG7B,MAAM,CAAC6B,IAAI,CAAE5L,UAAW,CAAC,CAAC6L,IAAI,CAAC,CAAC;EAE7C,KAAM,IAAI3Y,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGsB,IAAI,CAACzY,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;IAEjDyY,aAAa,IAAIC,IAAI,CAAE1Y,CAAC,CAAE,GAAG,GAAG,GAAG8M,UAAU,CAAE4L,IAAI,CAAE1Y,CAAC,CAAE,CAAE,GAAG,GAAG;EAEjE;EAEA,OAAOyY,aAAa;AAErB;AAEA,SAASG,2BAA2BA,CAAE5d,WAAW,EAAG;EAEnD;EACA;;EAEA,QAASA,WAAW;IAEnB,KAAKmZ,SAAS;MACb,OAAO,CAAC,GAAG,GAAG;IAEf,KAAK/U,UAAU;MACd,OAAO,CAAC,GAAG,GAAG;IAEf,KAAKgV,UAAU;MACd,OAAO,CAAC,GAAG,KAAK;IAEjB,KAAKC,WAAW;MACf,OAAO,CAAC,GAAG,KAAK;IAEjB;MACC,MAAM,IAAIlW,KAAK,CAAE,mEAAoE,CAAC;EAExF;AAED;AAEA,SAAS0a,mBAAmBA,CAAEvO,GAAG,EAAG;EAEnC,IAAKA,GAAG,CAACwO,MAAM,CAAE,gBAAiB,CAAC,GAAG,CAAC,IAAIxO,GAAG,CAACwO,MAAM,CAAE,oBAAqB,CAAC,KAAK,CAAC,EAAG,OAAO,YAAY;EACzG,IAAKxO,GAAG,CAACwO,MAAM,CAAE,eAAgB,CAAC,GAAG,CAAC,IAAIxO,GAAG,CAACwO,MAAM,CAAE,oBAAqB,CAAC,KAAK,CAAC,EAAG,OAAO,YAAY;EAExG,OAAO,WAAW;AAEnB;AAEA,MAAMC,eAAe,GAAG,IAAIjgB,OAAO,CAAC,CAAC;;AAErC;;AAEA,MAAM+G,UAAU,CAAC;EAEhB7E,WAAWA,CAAE2D,IAAI,GAAG,CAAC,CAAC,EAAEqL,OAAO,GAAG,CAAC,CAAC,EAAG;IAEtC,IAAI,CAACrL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACmL,OAAO,GAAGA,OAAO;;IAEtB;IACA,IAAI,CAACnH,KAAK,GAAG,IAAIvB,YAAY,CAAC,CAAC;;IAE/B;IACA,IAAI,CAAC0X,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;;IAE7B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;;IAExB;IACA,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,SAAS,GAAG;MAAEtW,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;IACvC,IAAI,CAACsW,WAAW,GAAG;MAAEvW,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;IACzC,IAAI,CAACuW,UAAU,GAAG;MAAExW,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;IAExC,IAAI,CAACwW,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;;IAEvB;IACA;;IAEA,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,cAAc,GAAG,CAAE,CAAC;IAExB,IAAK,OAAOC,SAAS,KAAK,WAAW,EAAG;MAEvCH,QAAQ,GAAG,gCAAgC,CAACI,IAAI,CAAED,SAAS,CAACE,SAAU,CAAC,KAAK,IAAI;MAChFJ,SAAS,GAAGE,SAAS,CAACE,SAAS,CAACxb,OAAO,CAAE,SAAU,CAAC,GAAG,CAAE,CAAC;MAC1Dqb,cAAc,GAAGD,SAAS,GAAGE,SAAS,CAACE,SAAS,CAACC,KAAK,CAAE,qBAAsB,CAAC,CAAE,CAAC,CAAE,GAAG,CAAE,CAAC;IAE3F;IAEA,IAAK,OAAOC,iBAAiB,KAAK,WAAW,IAAIP,QAAQ,IAAMC,SAAS,IAAIC,cAAc,GAAG,EAAI,EAAG;MAEnG,IAAI,CAACvP,aAAa,GAAG,IAAI9P,aAAa,CAAE,IAAI,CAACyP,OAAO,CAAC/O,OAAQ,CAAC;IAE/D,CAAC,MAAM;MAEN,IAAI,CAACoP,aAAa,GAAG,IAAIzS,iBAAiB,CAAE,IAAI,CAACoS,OAAO,CAAC/O,OAAQ,CAAC;IAEnE;IAEA,IAAI,CAACoP,aAAa,CAAC6P,cAAc,CAAE,IAAI,CAAClQ,OAAO,CAAClK,WAAY,CAAC;IAC7D,IAAI,CAACuK,aAAa,CAAC3M,gBAAgB,CAAE,IAAI,CAACsM,OAAO,CAACrM,aAAc,CAAC;IAEjE,IAAI,CAACoC,UAAU,GAAG,IAAItI,UAAU,CAAE,IAAI,CAACuS,OAAO,CAAC/O,OAAQ,CAAC;IACxD,IAAI,CAAC8E,UAAU,CAACtC,eAAe,CAAE,aAAc,CAAC;IAEhD,IAAK,IAAI,CAACuM,OAAO,CAAClK,WAAW,KAAK,iBAAiB,EAAG;MAErD,IAAI,CAACC,UAAU,CAACnC,kBAAkB,CAAE,IAAK,CAAC;IAE3C;EAED;EAEAoD,aAAaA,CAAEpC,UAAU,EAAG;IAE3B,IAAI,CAACA,UAAU,GAAGA,UAAU;EAE7B;EAEAqC,UAAUA,CAAEpC,OAAO,EAAG;IAErB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAEvB;EAEAd,KAAKA,CAAEtB,MAAM,EAAEE,OAAO,EAAG;IAExB,MAAMpB,MAAM,GAAG,IAAI;IACnB,MAAMoD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;;IAElC;IACA,IAAI,CAACiE,KAAK,CAAChB,SAAS,CAAC,CAAC;IACtB,IAAI,CAACsX,SAAS,GAAG,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACgB,UAAU,CAAE,UAAWC,GAAG,EAAG;MAEjC,OAAOA,GAAG,CAACpX,SAAS,IAAIoX,GAAG,CAACpX,SAAS,CAAC,CAAC;IAExC,CAAE,CAAC;IAEH7B,OAAO,CAACoF,GAAG,CAAE,IAAI,CAAC4T,UAAU,CAAE,UAAWC,GAAG,EAAG;MAE9C,OAAOA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACC,UAAU,CAAC,CAAC;IAE1C,CAAE,CAAE,CAAC,CAAC9U,IAAI,CAAE,YAAY;MAEvB,OAAOpE,OAAO,CAACoF,GAAG,CAAE,CAEnBhL,MAAM,CAAC+e,eAAe,CAAE,OAAQ,CAAC,EACjC/e,MAAM,CAAC+e,eAAe,CAAE,WAAY,CAAC,EACrC/e,MAAM,CAAC+e,eAAe,CAAE,QAAS,CAAC,CAEjC,CAAC;IAEJ,CAAE,CAAC,CAAC/U,IAAI,CAAE,UAAWgV,YAAY,EAAG;MAEnC,MAAMtO,MAAM,GAAG;QACduO,KAAK,EAAED,YAAY,CAAE,CAAC,CAAE,CAAE5b,IAAI,CAAC6b,KAAK,IAAI,CAAC,CAAE;QAC3CC,MAAM,EAAEF,YAAY,CAAE,CAAC,CAAE;QACzBG,UAAU,EAAEH,YAAY,CAAE,CAAC,CAAE;QAC7BI,OAAO,EAAEJ,YAAY,CAAE,CAAC,CAAE;QAC1B7a,KAAK,EAAEf,IAAI,CAACe,KAAK;QACjBnE,MAAM,EAAEA,MAAM;QACdkb,QAAQ,EAAE,CAAC;MACZ,CAAC;MAEDH,8BAA8B,CAAE1X,UAAU,EAAEqN,MAAM,EAAEtN,IAAK,CAAC;MAE1DqG,sBAAsB,CAAEiH,MAAM,EAAEtN,IAAK,CAAC;MAEtCwC,OAAO,CAACoF,GAAG,CAAEhL,MAAM,CAAC4e,UAAU,CAAE,UAAWC,GAAG,EAAG;QAEhD,OAAOA,GAAG,CAACQ,SAAS,IAAIR,GAAG,CAACQ,SAAS,CAAE3O,MAAO,CAAC;MAEhD,CAAE,CAAE,CAAC,CAAC1G,IAAI,CAAE,YAAY;QAEvB9I,MAAM,CAAEwP,MAAO,CAAC;MAEjB,CAAE,CAAC;IAEJ,CAAE,CAAC,CAAC4O,KAAK,CAAEle,OAAQ,CAAC;EAErB;;EAEA;AACD;AACA;EACCqG,SAASA,CAAA,EAAG;IAEX,MAAMC,QAAQ,GAAG,IAAI,CAACtE,IAAI,CAACuE,KAAK,IAAI,EAAE;IACtC,MAAM4X,QAAQ,GAAG,IAAI,CAACnc,IAAI,CAACoc,KAAK,IAAI,EAAE;IACtC,MAAMC,QAAQ,GAAG,IAAI,CAACrc,IAAI,CAAC2N,MAAM,IAAI,EAAE;;IAEvC;IACA;IACA,KAAM,IAAI2O,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAGJ,QAAQ,CAAC7a,MAAM,EAAEgb,SAAS,GAAGC,UAAU,EAAED,SAAS,EAAG,EAAG;MAE7F,MAAME,MAAM,GAAGL,QAAQ,CAAEG,SAAS,CAAE,CAACE,MAAM;MAE3C,KAAM,IAAInb,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG+D,MAAM,CAAClb,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAEnDiD,QAAQ,CAAEkY,MAAM,CAAEnb,CAAC,CAAE,CAAE,CAACob,MAAM,GAAG,IAAI;MAEtC;IAED;;IAEA;IACA;IACA,KAAM,IAAIjY,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAGH,QAAQ,CAAChD,MAAM,EAAEkD,SAAS,GAAGC,UAAU,EAAED,SAAS,EAAG,EAAG;MAE7F,MAAME,OAAO,GAAGJ,QAAQ,CAAEE,SAAS,CAAE;MAErC,IAAKE,OAAO,CAAC+I,IAAI,KAAKzM,SAAS,EAAG;QAEjC,IAAI,CAAC4D,WAAW,CAAE,IAAI,CAAC6V,SAAS,EAAE/V,OAAO,CAAC+I,IAAK,CAAC;;QAEhD;QACA;QACA;QACA,IAAK/I,OAAO,CAACgY,IAAI,KAAK1b,SAAS,EAAG;UAEjCqb,QAAQ,CAAE3X,OAAO,CAAC+I,IAAI,CAAE,CAACkP,aAAa,GAAG,IAAI;QAE9C;MAED;MAEA,IAAKjY,OAAO,CAACkY,MAAM,KAAK5b,SAAS,EAAG;QAEnC,IAAI,CAAC4D,WAAW,CAAE,IAAI,CAAC8V,WAAW,EAAEhW,OAAO,CAACkY,MAAO,CAAC;MAErD;IAED;EAED;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACChY,WAAWA,CAAEV,KAAK,EAAEuC,KAAK,EAAG;IAE3B,IAAKA,KAAK,KAAKzF,SAAS,EAAG;IAE3B,IAAKkD,KAAK,CAACC,IAAI,CAAEsC,KAAK,CAAE,KAAKzF,SAAS,EAAG;MAExCkD,KAAK,CAACC,IAAI,CAAEsC,KAAK,CAAE,GAAGvC,KAAK,CAACE,IAAI,CAAEqC,KAAK,CAAE,GAAG,CAAC;IAE9C;IAEAvC,KAAK,CAACC,IAAI,CAAEsC,KAAK,CAAE,EAAG;EAEvB;;EAEA;EACAI,WAAWA,CAAE3C,KAAK,EAAEuC,KAAK,EAAEzD,MAAM,EAAG;IAEnC,IAAKkB,KAAK,CAACC,IAAI,CAAEsC,KAAK,CAAE,IAAI,CAAC,EAAG,OAAOzD,MAAM;IAE7C,MAAM6Z,GAAG,GAAG7Z,MAAM,CAACsP,KAAK,CAAC,CAAC;;IAE1B;IACA;IACA,MAAMwK,cAAc,GAAGA,CAAEC,QAAQ,EAAEzK,KAAK,KAAM;MAE7C,MAAM0K,QAAQ,GAAG,IAAI,CAAC3C,YAAY,CAACxX,GAAG,CAAEka,QAAS,CAAC;MAClD,IAAKC,QAAQ,IAAI,IAAI,EAAG;QAEvB,IAAI,CAAC3C,YAAY,CAAC1U,GAAG,CAAE2M,KAAK,EAAE0K,QAAS,CAAC;MAEzC;MAEA,KAAM,MAAM,CAAE3b,CAAC,EAAE4b,KAAK,CAAE,IAAIF,QAAQ,CAACtO,QAAQ,CAACyO,OAAO,CAAC,CAAC,EAAG;QAEzDJ,cAAc,CAAEG,KAAK,EAAE3K,KAAK,CAAC7D,QAAQ,CAAEpN,CAAC,CAAG,CAAC;MAE7C;IAED,CAAC;IAEDyb,cAAc,CAAE9Z,MAAM,EAAE6Z,GAAI,CAAC;IAE7BA,GAAG,CAACrb,IAAI,IAAI,YAAY,GAAK0C,KAAK,CAACE,IAAI,CAAEqC,KAAK,CAAE,EAAK;IAErD,OAAOoW,GAAG;EAEX;EAEAM,UAAUA,CAAEC,IAAI,EAAG;IAElB,MAAMnd,UAAU,GAAGiY,MAAM,CAACnF,MAAM,CAAE,IAAI,CAAC7S,OAAQ,CAAC;IAChDD,UAAU,CAACJ,IAAI,CAAE,IAAK,CAAC;IAEvB,KAAM,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,UAAU,CAACqB,MAAM,EAAED,CAAC,EAAG,EAAG;MAE9C,MAAMiM,MAAM,GAAG8P,IAAI,CAAEnd,UAAU,CAAEoB,CAAC,CAAG,CAAC;MAEtC,IAAKiM,MAAM,EAAG,OAAOA,MAAM;IAE5B;IAEA,OAAO,IAAI;EAEZ;EAEAkO,UAAUA,CAAE4B,IAAI,EAAG;IAElB,MAAMnd,UAAU,GAAGiY,MAAM,CAACnF,MAAM,CAAE,IAAI,CAAC7S,OAAQ,CAAC;IAChDD,UAAU,CAACod,OAAO,CAAE,IAAK,CAAC;IAE1B,MAAMnW,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI7F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,UAAU,CAACqB,MAAM,EAAED,CAAC,EAAG,EAAG;MAE9C,MAAMiM,MAAM,GAAG8P,IAAI,CAAEnd,UAAU,CAAEoB,CAAC,CAAG,CAAC;MAEtC,IAAKiM,MAAM,EAAGpG,OAAO,CAACrH,IAAI,CAAEyN,MAAO,CAAC;IAErC;IAEA,OAAOpG,OAAO;EAEf;;EAEA;AACD;AACA;AACA;AACA;AACA;EACCV,aAAaA,CAAEhB,IAAI,EAAEiB,KAAK,EAAG;IAE5B,MAAM1B,QAAQ,GAAGS,IAAI,GAAG,GAAG,GAAGiB,KAAK;IACnC,IAAIzB,UAAU,GAAG,IAAI,CAACd,KAAK,CAACrB,GAAG,CAAEkC,QAAS,CAAC;IAE3C,IAAK,CAAEC,UAAU,EAAG;MAEnB,QAASQ,IAAI;QAEZ,KAAK,OAAO;UACXR,UAAU,GAAG,IAAI,CAACsY,SAAS,CAAE7W,KAAM,CAAC;UACpC;QAED,KAAK,MAAM;UACVzB,UAAU,GAAG,IAAI,CAACmY,UAAU,CAAE,UAAW1B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAAC8B,QAAQ,IAAI9B,GAAG,CAAC8B,QAAQ,CAAE9W,KAAM,CAAC;UAE7C,CAAE,CAAC;UACH;QAED,KAAK,MAAM;UACVzB,UAAU,GAAG,IAAI,CAACmY,UAAU,CAAE,UAAW1B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAAE/W,KAAM,CAAC;UAE7C,CAAE,CAAC;UACH;QAED,KAAK,UAAU;UACdzB,UAAU,GAAG,IAAI,CAACyY,YAAY,CAAEhX,KAAM,CAAC;UACvC;QAED,KAAK,YAAY;UAChBzB,UAAU,GAAG,IAAI,CAACmY,UAAU,CAAE,UAAW1B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACpP,cAAc,IAAIoP,GAAG,CAACpP,cAAc,CAAE5F,KAAM,CAAC;UAEzD,CAAE,CAAC;UACH;QAED,KAAK,QAAQ;UACZzB,UAAU,GAAG,IAAI,CAAC0Y,UAAU,CAAEjX,KAAM,CAAC;UACrC;QAED,KAAK,UAAU;UACdzB,UAAU,GAAG,IAAI,CAACmY,UAAU,CAAE,UAAW1B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACkC,YAAY,IAAIlC,GAAG,CAACkC,YAAY,CAAElX,KAAM,CAAC;UAErD,CAAE,CAAC;UACH;QAED,KAAK,SAAS;UACbzB,UAAU,GAAG,IAAI,CAACmY,UAAU,CAAE,UAAW1B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACxQ,WAAW,IAAIwQ,GAAG,CAACxQ,WAAW,CAAExE,KAAM,CAAC;UAEnD,CAAE,CAAC;UACH;QAED,KAAK,MAAM;UACVzB,UAAU,GAAG,IAAI,CAAC4Y,QAAQ,CAAEnX,KAAM,CAAC;UACnC;QAED,KAAK,WAAW;UACfzB,UAAU,GAAG,IAAI,CAACmY,UAAU,CAAE,UAAW1B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACoC,aAAa,IAAIpC,GAAG,CAACoC,aAAa,CAAEpX,KAAM,CAAC;UAEvD,CAAE,CAAC;UACH;QAED,KAAK,QAAQ;UACZzB,UAAU,GAAG,IAAI,CAAC8Y,UAAU,CAAErX,KAAM,CAAC;UACrC;QAED;UACCzB,UAAU,GAAG,IAAI,CAACmY,UAAU,CAAE,UAAW1B,GAAG,EAAG;YAE9C,OAAOA,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACjV,aAAa,IAAIiV,GAAG,CAACjV,aAAa,CAAEhB,IAAI,EAAEiB,KAAM,CAAC;UAE5E,CAAE,CAAC;UAEH,IAAK,CAAEzB,UAAU,EAAG;YAEnB,MAAM,IAAIxF,KAAK,CAAE,gBAAgB,GAAGgG,IAAK,CAAC;UAE3C;UAEA;MAEF;MAEA,IAAI,CAACtB,KAAK,CAACnB,GAAG,CAAEgC,QAAQ,EAAEC,UAAW,CAAC;IAEvC;IAEA,OAAOA,UAAU;EAElB;;EAEA;AACD;AACA;AACA;AACA;EACC2W,eAAeA,CAAEnW,IAAI,EAAG;IAEvB,IAAIoW,YAAY,GAAG,IAAI,CAAC1X,KAAK,CAACrB,GAAG,CAAE2C,IAAK,CAAC;IAEzC,IAAK,CAAEoW,YAAY,EAAG;MAErB,MAAMhf,MAAM,GAAG,IAAI;MACnB,MAAMmhB,IAAI,GAAG,IAAI,CAAC/d,IAAI,CAAEwF,IAAI,IAAKA,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG,GAAG,CAAE,CAAE,IAAI,EAAE;MAEvEoW,YAAY,GAAGpZ,OAAO,CAACoF,GAAG,CAAEmW,IAAI,CAACC,GAAG,CAAE,UAAWC,GAAG,EAAExX,KAAK,EAAG;QAE7D,OAAO7J,MAAM,CAAC4J,aAAa,CAAEhB,IAAI,EAAEiB,KAAM,CAAC;MAE3C,CAAE,CAAE,CAAC;MAEL,IAAI,CAACvC,KAAK,CAACnB,GAAG,CAAEyC,IAAI,EAAEoW,YAAa,CAAC;IAErC;IAEA,OAAOA,YAAY;EAEpB;;EAEA;AACD;AACA;AACA;AACA;EACC8B,UAAUA,CAAEQ,WAAW,EAAG;IAEzB,MAAMC,SAAS,GAAG,IAAI,CAACne,IAAI,CAACoe,OAAO,CAAEF,WAAW,CAAE;IAClD,MAAMtf,MAAM,GAAG,IAAI,CAACwC,UAAU;IAE9B,IAAK+c,SAAS,CAAC3Y,IAAI,IAAI2Y,SAAS,CAAC3Y,IAAI,KAAK,aAAa,EAAG;MAEzD,MAAM,IAAIhG,KAAK,CAAE,oBAAoB,GAAG2e,SAAS,CAAC3Y,IAAI,GAAG,gCAAiC,CAAC;IAE5F;;IAEA;IACA,IAAK2Y,SAAS,CAACxS,GAAG,KAAK3K,SAAS,IAAIkd,WAAW,KAAK,CAAC,EAAG;MAEvD,OAAO1b,OAAO,CAACC,OAAO,CAAE,IAAI,CAACxC,UAAU,CAAEU,UAAU,CAACC,eAAe,CAAE,CAACsP,IAAK,CAAC;IAE7E;IAEA,MAAM7E,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,OAAO,IAAI7I,OAAO,CAAE,UAAWC,OAAO,EAAEC,MAAM,EAAG;MAEhD9D,MAAM,CAAChB,IAAI,CAAE5D,WAAW,CAACqkB,UAAU,CAAEF,SAAS,CAACxS,GAAG,EAAEN,OAAO,CAAClN,IAAK,CAAC,EAAEsE,OAAO,EAAEzB,SAAS,EAAE,YAAY;QAEnG0B,MAAM,CAAE,IAAIlD,KAAK,CAAE,2CAA2C,GAAG2e,SAAS,CAACxS,GAAG,GAAG,IAAK,CAAE,CAAC;MAE1F,CAAE,CAAC;IAEJ,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;EACCU,cAAcA,CAAE2E,eAAe,EAAG;IAEjC,MAAMsN,aAAa,GAAG,IAAI,CAACte,IAAI,CAACuM,WAAW,CAAEyE,eAAe,CAAE;IAE9D,OAAO,IAAI,CAACxK,aAAa,CAAE,QAAQ,EAAE8X,aAAa,CAAC7R,MAAO,CAAC,CAAC7F,IAAI,CAAE,UAAW6F,MAAM,EAAG;MAErF,MAAMK,UAAU,GAAGwR,aAAa,CAACxR,UAAU,IAAI,CAAC;MAChD,MAAMD,UAAU,GAAGyR,aAAa,CAACzR,UAAU,IAAI,CAAC;MAChD,OAAOJ,MAAM,CAAC6D,KAAK,CAAEzD,UAAU,EAAEA,UAAU,GAAGC,UAAW,CAAC;IAE3D,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;EACC2Q,YAAYA,CAAEc,aAAa,EAAG;IAE7B,MAAM3hB,MAAM,GAAG,IAAI;IACnB,MAAMoD,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,MAAMwR,WAAW,GAAG,IAAI,CAACxR,IAAI,CAACyR,SAAS,CAAE8M,aAAa,CAAE;IAExD,IAAK/M,WAAW,CAAClF,UAAU,KAAKtL,SAAS,IAAIwQ,WAAW,CAACgN,MAAM,KAAKxd,SAAS,EAAG;MAE/E,MAAMyd,QAAQ,GAAG1I,gBAAgB,CAAEvE,WAAW,CAAChM,IAAI,CAAE;MACrD,MAAMkZ,UAAU,GAAG/M,qBAAqB,CAAEH,WAAW,CAACE,aAAa,CAAE;MACrE,MAAME,UAAU,GAAGJ,WAAW,CAACI,UAAU,KAAK,IAAI;MAElD,MAAMnK,KAAK,GAAG,IAAIiX,UAAU,CAAElN,WAAW,CAACzE,KAAK,GAAG0R,QAAS,CAAC;MAC5D,OAAOjc,OAAO,CAACC,OAAO,CAAE,IAAIjK,eAAe,CAAEiP,KAAK,EAAEgX,QAAQ,EAAE7M,UAAW,CAAE,CAAC;IAE7E;IAEA,MAAM+M,kBAAkB,GAAG,EAAE;IAE7B,IAAKnN,WAAW,CAAClF,UAAU,KAAKtL,SAAS,EAAG;MAE3C2d,kBAAkB,CAAC9e,IAAI,CAAE,IAAI,CAAC2G,aAAa,CAAE,YAAY,EAAEgL,WAAW,CAAClF,UAAW,CAAE,CAAC;IAEtF,CAAC,MAAM;MAENqS,kBAAkB,CAAC9e,IAAI,CAAE,IAAK,CAAC;IAEhC;IAEA,IAAK2R,WAAW,CAACgN,MAAM,KAAKxd,SAAS,EAAG;MAEvC2d,kBAAkB,CAAC9e,IAAI,CAAE,IAAI,CAAC2G,aAAa,CAAE,YAAY,EAAEgL,WAAW,CAACgN,MAAM,CAAC5E,OAAO,CAACtN,UAAW,CAAE,CAAC;MACpGqS,kBAAkB,CAAC9e,IAAI,CAAE,IAAI,CAAC2G,aAAa,CAAE,YAAY,EAAEgL,WAAW,CAACgN,MAAM,CAACzL,MAAM,CAACzG,UAAW,CAAE,CAAC;IAEpG;IAEA,OAAO9J,OAAO,CAACoF,GAAG,CAAE+W,kBAAmB,CAAC,CAAC/X,IAAI,CAAE,UAAW2F,WAAW,EAAG;MAEvE,MAAMD,UAAU,GAAGC,WAAW,CAAE,CAAC,CAAE;MAEnC,MAAMkS,QAAQ,GAAG1I,gBAAgB,CAAEvE,WAAW,CAAChM,IAAI,CAAE;MACrD,MAAMkZ,UAAU,GAAG/M,qBAAqB,CAAEH,WAAW,CAACE,aAAa,CAAE;;MAErE;MACA,MAAMkN,YAAY,GAAGF,UAAU,CAACG,iBAAiB;MACjD,MAAMC,SAAS,GAAGF,YAAY,GAAGH,QAAQ;MACzC,MAAM5R,UAAU,GAAG2E,WAAW,CAAC3E,UAAU,IAAI,CAAC;MAC9C,MAAMI,UAAU,GAAGuE,WAAW,CAAClF,UAAU,KAAKtL,SAAS,GAAGhB,IAAI,CAACuM,WAAW,CAAEiF,WAAW,CAAClF,UAAU,CAAE,CAACW,UAAU,GAAGjM,SAAS;MAC3H,MAAM4Q,UAAU,GAAGJ,WAAW,CAACI,UAAU,KAAK,IAAI;MAClD,IAAInK,KAAK,EAAEsX,eAAe;;MAE1B;MACA,IAAK9R,UAAU,IAAIA,UAAU,KAAK6R,SAAS,EAAG;QAE7C;QACA;QACA,MAAME,OAAO,GAAGhZ,IAAI,CAACiZ,KAAK,CAAEpS,UAAU,GAAGI,UAAW,CAAC;QACrD,MAAMiS,UAAU,GAAG,oBAAoB,GAAG1N,WAAW,CAAClF,UAAU,GAAG,GAAG,GAAGkF,WAAW,CAACE,aAAa,GAAG,GAAG,GAAGsN,OAAO,GAAG,GAAG,GAAGxN,WAAW,CAACzE,KAAK;QAC5I,IAAIoS,EAAE,GAAGviB,MAAM,CAACsH,KAAK,CAACrB,GAAG,CAAEqc,UAAW,CAAC;QAEvC,IAAK,CAAEC,EAAE,EAAG;UAEX1X,KAAK,GAAG,IAAIiX,UAAU,CAAEpS,UAAU,EAAE0S,OAAO,GAAG/R,UAAU,EAAEuE,WAAW,CAACzE,KAAK,GAAGE,UAAU,GAAG2R,YAAa,CAAC;;UAEzG;UACAO,EAAE,GAAG,IAAIhmB,iBAAiB,CAAEsO,KAAK,EAAEwF,UAAU,GAAG2R,YAAa,CAAC;UAE9DhiB,MAAM,CAACsH,KAAK,CAACnB,GAAG,CAAEmc,UAAU,EAAEC,EAAG,CAAC;QAEnC;QAEAJ,eAAe,GAAG,IAAI3lB,0BAA0B,CAAE+lB,EAAE,EAAEV,QAAQ,EAAI5R,UAAU,GAAGI,UAAU,GAAK2R,YAAY,EAAEhN,UAAW,CAAC;MAEzH,CAAC,MAAM;QAEN,IAAKtF,UAAU,KAAK,IAAI,EAAG;UAE1B7E,KAAK,GAAG,IAAIiX,UAAU,CAAElN,WAAW,CAACzE,KAAK,GAAG0R,QAAS,CAAC;QAEvD,CAAC,MAAM;UAENhX,KAAK,GAAG,IAAIiX,UAAU,CAAEpS,UAAU,EAAEO,UAAU,EAAE2E,WAAW,CAACzE,KAAK,GAAG0R,QAAS,CAAC;QAE/E;QAEAM,eAAe,GAAG,IAAIvmB,eAAe,CAAEiP,KAAK,EAAEgX,QAAQ,EAAE7M,UAAW,CAAC;MAErE;;MAEA;MACA,IAAKJ,WAAW,CAACgN,MAAM,KAAKxd,SAAS,EAAG;QAEvC,MAAMoe,eAAe,GAAGrJ,gBAAgB,CAACsJ,MAAM;QAC/C,MAAMC,iBAAiB,GAAG3N,qBAAqB,CAAEH,WAAW,CAACgN,MAAM,CAAC5E,OAAO,CAAClI,aAAa,CAAE;QAE3F,MAAM6N,iBAAiB,GAAG/N,WAAW,CAACgN,MAAM,CAAC5E,OAAO,CAAC/M,UAAU,IAAI,CAAC;QACpE,MAAM2S,gBAAgB,GAAGhO,WAAW,CAACgN,MAAM,CAACzL,MAAM,CAAClG,UAAU,IAAI,CAAC;QAElE,MAAM4S,aAAa,GAAG,IAAIH,iBAAiB,CAAE/S,WAAW,CAAE,CAAC,CAAE,EAAEgT,iBAAiB,EAAE/N,WAAW,CAACgN,MAAM,CAACzR,KAAK,GAAGqS,eAAgB,CAAC;QAC9H,MAAMM,YAAY,GAAG,IAAIhB,UAAU,CAAEnS,WAAW,CAAE,CAAC,CAAE,EAAEiT,gBAAgB,EAAEhO,WAAW,CAACgN,MAAM,CAACzR,KAAK,GAAG0R,QAAS,CAAC;QAE9G,IAAKnS,UAAU,KAAK,IAAI,EAAG;UAE1B;UACAyS,eAAe,GAAG,IAAIvmB,eAAe,CAAEumB,eAAe,CAACtX,KAAK,CAAC6I,KAAK,CAAC,CAAC,EAAEyO,eAAe,CAACN,QAAQ,EAAEM,eAAe,CAACnN,UAAW,CAAC;QAE7H;QAEA,KAAM,IAAIvQ,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGgH,aAAa,CAACne,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;UAE1D,MAAMoF,KAAK,GAAGgZ,aAAa,CAAEpe,CAAC,CAAE;UAEhC0d,eAAe,CAACY,IAAI,CAAElZ,KAAK,EAAEiZ,YAAY,CAAEre,CAAC,GAAGod,QAAQ,CAAG,CAAC;UAC3D,IAAKA,QAAQ,IAAI,CAAC,EAAGM,eAAe,CAACa,IAAI,CAAEnZ,KAAK,EAAEiZ,YAAY,CAAEre,CAAC,GAAGod,QAAQ,GAAG,CAAC,CAAG,CAAC;UACpF,IAAKA,QAAQ,IAAI,CAAC,EAAGM,eAAe,CAACc,IAAI,CAAEpZ,KAAK,EAAEiZ,YAAY,CAAEre,CAAC,GAAGod,QAAQ,GAAG,CAAC,CAAG,CAAC;UACpF,IAAKA,QAAQ,IAAI,CAAC,EAAGM,eAAe,CAACe,IAAI,CAAErZ,KAAK,EAAEiZ,YAAY,CAAEre,CAAC,GAAGod,QAAQ,GAAG,CAAC,CAAG,CAAC;UACpF,IAAKA,QAAQ,IAAI,CAAC,EAAG,MAAM,IAAIjf,KAAK,CAAE,mEAAoE,CAAC;QAE5G;MAED;MAEA,OAAOuf,eAAe;IAEvB,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;EACC9T,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAMlL,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMqL,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMF,UAAU,GAAGnL,IAAI,CAACoL,QAAQ,CAAEF,YAAY,CAAE;IAChD,MAAM6U,WAAW,GAAG5U,UAAU,CAACI,MAAM;IACrC,MAAMyU,SAAS,GAAGhgB,IAAI,CAACyL,MAAM,CAAEsU,WAAW,CAAE;IAE5C,IAAInhB,MAAM,GAAG,IAAI,CAAC8M,aAAa;IAE/B,IAAKsU,SAAS,CAACrU,GAAG,EAAG;MAEpB,MAAMC,OAAO,GAAGP,OAAO,CAAC/O,OAAO,CAACuP,UAAU,CAAEmU,SAAS,CAACrU,GAAI,CAAC;MAC3D,IAAKC,OAAO,KAAK,IAAI,EAAGhN,MAAM,GAAGgN,OAAO;IAEzC;IAEA,OAAO,IAAI,CAACN,gBAAgB,CAAEJ,YAAY,EAAE6U,WAAW,EAAEnhB,MAAO,CAAC;EAElE;EAEA0M,gBAAgBA,CAAEJ,YAAY,EAAE6U,WAAW,EAAEnhB,MAAM,EAAG;IAErD,MAAMhC,MAAM,GAAG,IAAI;IACnB,MAAMoD,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,MAAMmL,UAAU,GAAGnL,IAAI,CAACoL,QAAQ,CAAEF,YAAY,CAAE;IAChD,MAAM8U,SAAS,GAAGhgB,IAAI,CAACyL,MAAM,CAAEsU,WAAW,CAAE;IAE5C,MAAMhb,QAAQ,GAAG,CAAEib,SAAS,CAACrU,GAAG,IAAIqU,SAAS,CAAC1T,UAAU,IAAK,GAAG,GAAGnB,UAAU,CAAC8U,OAAO;IAErF,IAAK,IAAI,CAACpF,YAAY,CAAE9V,QAAQ,CAAE,EAAG;MAEpC;MACA,OAAO,IAAI,CAAC8V,YAAY,CAAE9V,QAAQ,CAAE;IAErC;IAEA,MAAMmb,OAAO,GAAG,IAAI,CAACC,eAAe,CAAEJ,WAAW,EAAEnhB,MAAO,CAAC,CAACgI,IAAI,CAAE,UAAWoL,OAAO,EAAG;MAEtFA,OAAO,CAACoO,KAAK,GAAG,KAAK;MAErBpO,OAAO,CAACxQ,IAAI,GAAG2J,UAAU,CAAC3J,IAAI,IAAIwe,SAAS,CAACxe,IAAI,IAAI,EAAE;MAEtD,IAAKwQ,OAAO,CAACxQ,IAAI,KAAK,EAAE,IAAI,OAAOwe,SAAS,CAACrU,GAAG,KAAK,QAAQ,IAAIqU,SAAS,CAACrU,GAAG,CAAC0U,UAAU,CAAE,aAAc,CAAC,KAAK,KAAK,EAAG;QAEtHrO,OAAO,CAACxQ,IAAI,GAAGwe,SAAS,CAACrU,GAAG;MAE7B;MAEA,MAAM2U,QAAQ,GAAGtgB,IAAI,CAACsgB,QAAQ,IAAI,CAAC,CAAC;MACpC,MAAML,OAAO,GAAGK,QAAQ,CAAEnV,UAAU,CAAC8U,OAAO,CAAE,IAAI,CAAC,CAAC;MAEpDjO,OAAO,CAACuO,SAAS,GAAG1K,aAAa,CAAEoK,OAAO,CAACM,SAAS,CAAE,IAAI3mB,YAAY;MACtEoY,OAAO,CAACwO,SAAS,GAAG3K,aAAa,CAAEoK,OAAO,CAACO,SAAS,CAAE,IAAI3mB,wBAAwB;MAClFmY,OAAO,CAACyO,KAAK,GAAG3K,eAAe,CAAEmK,OAAO,CAACQ,KAAK,CAAE,IAAInlB,cAAc;MAClE0W,OAAO,CAAC0O,KAAK,GAAG5K,eAAe,CAAEmK,OAAO,CAACS,KAAK,CAAE,IAAIplB,cAAc;MAElEsB,MAAM,CAACyd,YAAY,CAAC1U,GAAG,CAAEqM,OAAO,EAAE;QAAE5G,QAAQ,EAAEF;MAAa,CAAE,CAAC;MAE9D,OAAO8G,OAAO;IAEf,CAAE,CAAC,CAACkK,KAAK,CAAE,YAAY;MAEtB,OAAO,IAAI;IAEZ,CAAE,CAAC;IAEH,IAAI,CAACrB,YAAY,CAAE9V,QAAQ,CAAE,GAAGmb,OAAO;IAEvC,OAAOA,OAAO;EAEf;EAEAC,eAAeA,CAAEJ,WAAW,EAAEnhB,MAAM,EAAG;IAEtC,MAAMhC,MAAM,GAAG,IAAI;IACnB,MAAMoD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMqL,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,IAAK,IAAI,CAACuP,WAAW,CAAEmF,WAAW,CAAE,KAAK/e,SAAS,EAAG;MAEpD,OAAO,IAAI,CAAC4Z,WAAW,CAAEmF,WAAW,CAAE,CAACnZ,IAAI,CAAIoL,OAAO,IAAMA,OAAO,CAACM,KAAK,CAAC,CAAE,CAAC;IAE9E;IAEA,MAAM0N,SAAS,GAAGhgB,IAAI,CAACyL,MAAM,CAAEsU,WAAW,CAAE;IAE5C,MAAMY,GAAG,GAAGha,IAAI,CAACga,GAAG,IAAIha,IAAI,CAACia,SAAS;IAEtC,IAAIC,SAAS,GAAGb,SAAS,CAACrU,GAAG,IAAI,EAAE;IACnC,IAAImV,WAAW,GAAG,KAAK;IAEvB,IAAKd,SAAS,CAAC1T,UAAU,KAAKtL,SAAS,EAAG;MAEzC;;MAEA6f,SAAS,GAAGjkB,MAAM,CAAC4J,aAAa,CAAE,YAAY,EAAEwZ,SAAS,CAAC1T,UAAW,CAAC,CAAC1F,IAAI,CAAE,UAAW0F,UAAU,EAAG;QAEpGwU,WAAW,GAAG,IAAI;QAClB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAE,CAAE1U,UAAU,CAAE,EAAE;UAAE9G,IAAI,EAAEwa,SAAS,CAACiB;QAAS,CAAE,CAAC;QACrEJ,SAAS,GAAGF,GAAG,CAACO,eAAe,CAAEH,IAAK,CAAC;QACvC,OAAOF,SAAS;MAEjB,CAAE,CAAC;IAEJ,CAAC,MAAM,IAAKb,SAAS,CAACrU,GAAG,KAAK3K,SAAS,EAAG;MAEzC,MAAM,IAAIxB,KAAK,CAAE,0BAA0B,GAAGugB,WAAW,GAAG,gCAAiC,CAAC;IAE/F;IAEA,MAAMG,OAAO,GAAG1d,OAAO,CAACC,OAAO,CAAEoe,SAAU,CAAC,CAACja,IAAI,CAAE,UAAWia,SAAS,EAAG;MAEzE,OAAO,IAAIre,OAAO,CAAE,UAAWC,OAAO,EAAEC,MAAM,EAAG;QAEhD,IAAI5E,MAAM,GAAG2E,OAAO;QAEpB,IAAK7D,MAAM,CAACuiB,mBAAmB,KAAK,IAAI,EAAG;UAE1CrjB,MAAM,GAAG,SAAAA,CAAWsjB,WAAW,EAAG;YAEjC,MAAMpP,OAAO,GAAG,IAAIrW,OAAO,CAAEylB,WAAY,CAAC;YAC1CpP,OAAO,CAACQ,WAAW,GAAG,IAAI;YAE1B/P,OAAO,CAAEuP,OAAQ,CAAC;UAEnB,CAAC;QAEF;QAEApT,MAAM,CAAChB,IAAI,CAAE5D,WAAW,CAACqkB,UAAU,CAAEwC,SAAS,EAAExV,OAAO,CAAClN,IAAK,CAAC,EAAEL,MAAM,EAAEkD,SAAS,EAAE0B,MAAO,CAAC;MAE5F,CAAE,CAAC;IAEJ,CAAE,CAAC,CAACkE,IAAI,CAAE,UAAWoL,OAAO,EAAG;MAE9B;;MAEA,IAAK8O,WAAW,KAAK,IAAI,EAAG;QAE3BH,GAAG,CAACU,eAAe,CAAER,SAAU,CAAC;MAEjC;MAEA7O,OAAO,CAAC8F,QAAQ,CAACmJ,QAAQ,GAAGjB,SAAS,CAACiB,QAAQ,IAAI/G,mBAAmB,CAAE8F,SAAS,CAACrU,GAAI,CAAC;MAEtF,OAAOqG,OAAO;IAEf,CAAE,CAAC,CAACkK,KAAK,CAAE,UAAWzd,KAAK,EAAG;MAE7BD,OAAO,CAACC,KAAK,CAAE,0CAA0C,EAAEoiB,SAAU,CAAC;MACtE,MAAMpiB,KAAK;IAEZ,CAAE,CAAC;IAEH,IAAI,CAACmc,WAAW,CAAEmF,WAAW,CAAE,GAAGG,OAAO;IACzC,OAAOA,OAAO;EAEf;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCvY,aAAaA,CAAEX,cAAc,EAAEsa,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAG;IAE5D,MAAM5kB,MAAM,GAAG,IAAI;IAEnB,OAAO,IAAI,CAAC4J,aAAa,CAAE,SAAS,EAAE+a,MAAM,CAAC9a,KAAM,CAAC,CAACG,IAAI,CAAE,UAAWoL,OAAO,EAAG;MAE/E,IAAK,CAAEA,OAAO,EAAG,OAAO,IAAI;MAE5B,IAAKuP,MAAM,CAACrP,QAAQ,KAAKlR,SAAS,IAAIugB,MAAM,CAACrP,QAAQ,GAAG,CAAC,EAAG;QAE3DF,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC,CAAC;QACzBN,OAAO,CAACG,OAAO,GAAGoP,MAAM,CAACrP,QAAQ;MAElC;MAEA,IAAKtV,MAAM,CAACqD,UAAU,CAAEU,UAAU,CAACqB,qBAAqB,CAAE,EAAG;QAE5D,MAAMiQ,SAAS,GAAGsP,MAAM,CAACthB,UAAU,KAAKe,SAAS,GAAGugB,MAAM,CAACthB,UAAU,CAAEU,UAAU,CAACqB,qBAAqB,CAAE,GAAGhB,SAAS;QAErH,IAAKiR,SAAS,EAAG;UAEhB,MAAMwP,aAAa,GAAG7kB,MAAM,CAACyd,YAAY,CAACxX,GAAG,CAAEmP,OAAQ,CAAC;UACxDA,OAAO,GAAGpV,MAAM,CAACqD,UAAU,CAAEU,UAAU,CAACqB,qBAAqB,CAAE,CAAC+P,aAAa,CAAEC,OAAO,EAAEC,SAAU,CAAC;UACnGrV,MAAM,CAACyd,YAAY,CAAC1U,GAAG,CAAEqM,OAAO,EAAEyP,aAAc,CAAC;QAElD;MAED;MAEA,IAAKD,UAAU,KAAKxgB,SAAS,EAAG;QAE/BgR,OAAO,CAACwP,UAAU,GAAGA,UAAU;MAEhC;MAEAxa,cAAc,CAAEsa,OAAO,CAAE,GAAGtP,OAAO;MAEnC,OAAOA,OAAO;IAEf,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCnC,mBAAmBA,CAAEpC,IAAI,EAAG;IAE3B,MAAMuB,QAAQ,GAAGvB,IAAI,CAACuB,QAAQ;IAC9B,IAAIC,QAAQ,GAAGxB,IAAI,CAACwB,QAAQ;IAE5B,MAAMyS,qBAAqB,GAAG1S,QAAQ,CAACb,UAAU,CAACwT,OAAO,KAAK3gB,SAAS;IACvE,MAAM4gB,eAAe,GAAG5S,QAAQ,CAACb,UAAU,CAAC9I,KAAK,KAAKrE,SAAS;IAC/D,MAAM6gB,cAAc,GAAG7S,QAAQ,CAACb,UAAU,CAAC2K,MAAM,KAAK9X,SAAS;IAE/D,IAAKyM,IAAI,CAACqU,QAAQ,EAAG;MAEpB,MAAM/c,QAAQ,GAAG,iBAAiB,GAAGkK,QAAQ,CAAC8S,IAAI;MAElD,IAAIC,cAAc,GAAG,IAAI,CAAC9d,KAAK,CAACrB,GAAG,CAAEkC,QAAS,CAAC;MAE/C,IAAK,CAAEid,cAAc,EAAG;QAEvBA,cAAc,GAAG,IAAI9mB,cAAc,CAAC,CAAC;QACrCjB,QAAQ,CAACyV,SAAS,CAACC,IAAI,CAACC,IAAI,CAAEoS,cAAc,EAAE/S,QAAS,CAAC;QACxD+S,cAAc,CAAC3c,KAAK,CAACsK,IAAI,CAAEV,QAAQ,CAAC5J,KAAM,CAAC;QAC3C2c,cAAc,CAAChE,GAAG,GAAG/O,QAAQ,CAAC+O,GAAG;QACjCgE,cAAc,CAACC,eAAe,GAAG,KAAK,CAAC,CAAC;;QAExC,IAAI,CAAC/d,KAAK,CAACnB,GAAG,CAAEgC,QAAQ,EAAEid,cAAe,CAAC;MAE3C;MAEA/S,QAAQ,GAAG+S,cAAc;IAE1B,CAAC,MAAM,IAAKvU,IAAI,CAACyU,MAAM,EAAG;MAEzB,MAAMnd,QAAQ,GAAG,oBAAoB,GAAGkK,QAAQ,CAAC8S,IAAI;MAErD,IAAII,YAAY,GAAG,IAAI,CAACje,KAAK,CAACrB,GAAG,CAAEkC,QAAS,CAAC;MAE7C,IAAK,CAAEod,YAAY,EAAG;QAErBA,YAAY,GAAG,IAAI1oB,iBAAiB,CAAC,CAAC;QACtCQ,QAAQ,CAACyV,SAAS,CAACC,IAAI,CAACC,IAAI,CAAEuS,YAAY,EAAElT,QAAS,CAAC;QACtDkT,YAAY,CAAC9c,KAAK,CAACsK,IAAI,CAAEV,QAAQ,CAAC5J,KAAM,CAAC;QACzC8c,YAAY,CAACnE,GAAG,GAAG/O,QAAQ,CAAC+O,GAAG;QAE/B,IAAI,CAAC9Z,KAAK,CAACnB,GAAG,CAAEgC,QAAQ,EAAEod,YAAa,CAAC;MAEzC;MAEAlT,QAAQ,GAAGkT,YAAY;IAExB;;IAEA;IACA,IAAKT,qBAAqB,IAAIE,eAAe,IAAIC,cAAc,EAAG;MAEjE,IAAI9c,QAAQ,GAAG,iBAAiB,GAAGkK,QAAQ,CAAC8S,IAAI,GAAG,GAAG;MAEtD,IAAKL,qBAAqB,EAAG3c,QAAQ,IAAI,sBAAsB;MAC/D,IAAK6c,eAAe,EAAG7c,QAAQ,IAAI,gBAAgB;MACnD,IAAK8c,cAAc,EAAG9c,QAAQ,IAAI,eAAe;MAEjD,IAAIqd,cAAc,GAAG,IAAI,CAACle,KAAK,CAACrB,GAAG,CAAEkC,QAAS,CAAC;MAE/C,IAAK,CAAEqd,cAAc,EAAG;QAEvBA,cAAc,GAAGnT,QAAQ,CAACqD,KAAK,CAAC,CAAC;QAEjC,IAAKsP,eAAe,EAAGQ,cAAc,CAACC,YAAY,GAAG,IAAI;QACzD,IAAKR,cAAc,EAAGO,cAAc,CAACE,WAAW,GAAG,IAAI;QAEvD,IAAKZ,qBAAqB,EAAG;UAE5B;UACA,IAAKU,cAAc,CAACG,WAAW,EAAGH,cAAc,CAACG,WAAW,CAACC,CAAC,IAAI,CAAE,CAAC;UACrE,IAAKJ,cAAc,CAACzZ,oBAAoB,EAAGyZ,cAAc,CAACzZ,oBAAoB,CAAC6Z,CAAC,IAAI,CAAE,CAAC;QAExF;QAEA,IAAI,CAACte,KAAK,CAACnB,GAAG,CAAEgC,QAAQ,EAAEqd,cAAe,CAAC;QAE1C,IAAI,CAAC/H,YAAY,CAAC1U,GAAG,CAAEyc,cAAc,EAAE,IAAI,CAAC/H,YAAY,CAACxX,GAAG,CAAEoM,QAAS,CAAE,CAAC;MAE3E;MAEAA,QAAQ,GAAGmT,cAAc;IAE1B;IAEA3U,IAAI,CAACwB,QAAQ,GAAGA,QAAQ;EAEzB;EAEAnI,eAAeA,CAAE;EAAA,EAAsB;IAEtC,OAAOvM,oBAAoB;EAE5B;;EAEA;AACD;AACA;AACA;AACA;EACCojB,YAAYA,CAAE7V,aAAa,EAAG;IAE7B,MAAMlL,MAAM,GAAG,IAAI;IACnB,MAAMoD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMgH,WAAW,GAAGjH,IAAI,CAAC+H,SAAS,CAAED,aAAa,CAAE;IAEnD,IAAI2a,YAAY;IAChB,MAAMzb,cAAc,GAAG,CAAC,CAAC;IACzB,MAAM0b,kBAAkB,GAAGzb,WAAW,CAAChH,UAAU,IAAI,CAAC,CAAC;IAEvD,MAAMiH,OAAO,GAAG,EAAE;IAElB,IAAKwb,kBAAkB,CAAE/hB,UAAU,CAACiB,mBAAmB,CAAE,EAAG;MAE3D,MAAM+gB,YAAY,GAAG1iB,UAAU,CAAEU,UAAU,CAACiB,mBAAmB,CAAE;MACjE6gB,YAAY,GAAGE,YAAY,CAAC7b,eAAe,CAAC,CAAC;MAC7CI,OAAO,CAACrH,IAAI,CAAE8iB,YAAY,CAAC5b,YAAY,CAAEC,cAAc,EAAEC,WAAW,EAAErK,MAAO,CAAE,CAAC;IAEjF,CAAC,MAAM;MAEN;MACA;;MAEA,MAAMwK,iBAAiB,GAAGH,WAAW,CAACI,oBAAoB,IAAI,CAAC,CAAC;MAEhEL,cAAc,CAAC3B,KAAK,GAAG,IAAI1M,KAAK,CAAE,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;MACjDqO,cAAc,CAACG,OAAO,GAAG,GAAG;MAE5B,IAAKG,KAAK,CAACC,OAAO,CAAEH,iBAAiB,CAACI,eAAgB,CAAC,EAAG;QAEzD,MAAMC,KAAK,GAAGL,iBAAiB,CAACI,eAAe;QAE/CR,cAAc,CAAC3B,KAAK,CAACC,SAAS,CAAEmC,KAAM,CAAC;QACvCT,cAAc,CAACG,OAAO,GAAGM,KAAK,CAAE,CAAC,CAAE;MAEpC;MAEA,IAAKL,iBAAiB,CAACM,gBAAgB,KAAK1G,SAAS,EAAG;QAEvDkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,KAAK,EAAEI,iBAAiB,CAACM,gBAAgB,EAAExL,cAAe,CAAE,CAAC;MAElH;MAEA8K,cAAc,CAACsQ,SAAS,GAAGlQ,iBAAiB,CAACwb,cAAc,KAAK5hB,SAAS,GAAGoG,iBAAiB,CAACwb,cAAc,GAAG,GAAG;MAClH5b,cAAc,CAACuQ,SAAS,GAAGnQ,iBAAiB,CAACyb,eAAe,KAAK7hB,SAAS,GAAGoG,iBAAiB,CAACyb,eAAe,GAAG,GAAG;MAEpH,IAAKzb,iBAAiB,CAAC0b,wBAAwB,KAAK9hB,SAAS,EAAG;QAE/DkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEI,iBAAiB,CAAC0b,wBAAyB,CAAE,CAAC;QAClH5b,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEI,iBAAiB,CAAC0b,wBAAyB,CAAE,CAAC;MAEnH;MAEAL,YAAY,GAAG,IAAI,CAACtF,UAAU,CAAE,UAAW1B,GAAG,EAAG;QAEhD,OAAOA,GAAG,CAAC3U,eAAe,IAAI2U,GAAG,CAAC3U,eAAe,CAAEgB,aAAc,CAAC;MAEnE,CAAE,CAAC;MAEHZ,OAAO,CAACrH,IAAI,CAAE2C,OAAO,CAACoF,GAAG,CAAE,IAAI,CAAC4T,UAAU,CAAE,UAAWC,GAAG,EAAG;QAE5D,OAAOA,GAAG,CAAC5T,oBAAoB,IAAI4T,GAAG,CAAC5T,oBAAoB,CAAEC,aAAa,EAAEd,cAAe,CAAC;MAE7F,CAAE,CAAE,CAAE,CAAC;IAER;IAEA,IAAKC,WAAW,CAAC8b,WAAW,KAAK,IAAI,EAAG;MAEvC/b,cAAc,CAAC0Q,IAAI,GAAG7e,UAAU;IAEjC;IAEA,MAAMmqB,SAAS,GAAG/b,WAAW,CAAC+b,SAAS,IAAIhM,WAAW,CAACC,MAAM;IAE7D,IAAK+L,SAAS,KAAKhM,WAAW,CAACG,KAAK,EAAG;MAEtCnQ,cAAc,CAACwQ,WAAW,GAAG,IAAI;;MAEjC;MACAxQ,cAAc,CAACic,UAAU,GAAG,KAAK;IAElC,CAAC,MAAM;MAENjc,cAAc,CAACwQ,WAAW,GAAG,KAAK;MAElC,IAAKwL,SAAS,KAAKhM,WAAW,CAACE,IAAI,EAAG;QAErClQ,cAAc,CAACkc,SAAS,GAAGjc,WAAW,CAACkc,WAAW,KAAKniB,SAAS,GAAGiG,WAAW,CAACkc,WAAW,GAAG,GAAG;MAEjG;IAED;IAEA,IAAKlc,WAAW,CAACmc,aAAa,KAAKpiB,SAAS,IAAIyhB,YAAY,KAAKpoB,iBAAiB,EAAG;MAEpF6M,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,WAAW,EAAEC,WAAW,CAACmc,aAAc,CAAE,CAAC;MAE9Fpc,cAAc,CAACub,WAAW,GAAG,IAAIxmB,OAAO,CAAE,CAAC,EAAE,CAAE,CAAC;MAEhD,IAAKkL,WAAW,CAACmc,aAAa,CAAC1a,KAAK,KAAK1H,SAAS,EAAG;QAEpD,MAAM0H,KAAK,GAAGzB,WAAW,CAACmc,aAAa,CAAC1a,KAAK;QAE7C1B,cAAc,CAACub,WAAW,CAAC5c,GAAG,CAAE+C,KAAK,EAAEA,KAAM,CAAC;MAE/C;IAED;IAEA,IAAKzB,WAAW,CAACoc,gBAAgB,KAAKriB,SAAS,IAAIyhB,YAAY,KAAKpoB,iBAAiB,EAAG;MAEvF6M,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,OAAO,EAAEC,WAAW,CAACoc,gBAAiB,CAAE,CAAC;MAE7F,IAAKpc,WAAW,CAACoc,gBAAgB,CAACC,QAAQ,KAAKtiB,SAAS,EAAG;QAE1DgG,cAAc,CAACuc,cAAc,GAAGtc,WAAW,CAACoc,gBAAgB,CAACC,QAAQ;MAEtE;IAED;IAEA,IAAKrc,WAAW,CAACuc,cAAc,KAAKxiB,SAAS,IAAIyhB,YAAY,KAAKpoB,iBAAiB,EAAG;MAErF2M,cAAc,CAACqQ,QAAQ,GAAG,IAAI1e,KAAK,CAAC,CAAC,CAAC2M,SAAS,CAAE2B,WAAW,CAACuc,cAAe,CAAC;IAE9E;IAEA,IAAKvc,WAAW,CAACwc,eAAe,KAAKziB,SAAS,IAAIyhB,YAAY,KAAKpoB,iBAAiB,EAAG;MAEtF6M,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC+K,aAAa,CAAEX,cAAc,EAAE,aAAa,EAAEC,WAAW,CAACwc,eAAe,EAAEvnB,cAAe,CAAE,CAAC;IAEnH;IAEA,OAAOsG,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,YAAY;MAE/C,MAAMqI,QAAQ,GAAG,IAAIwT,YAAY,CAAEzb,cAAe,CAAC;MAEnD,IAAKC,WAAW,CAACzF,IAAI,EAAGyN,QAAQ,CAACzN,IAAI,GAAGyF,WAAW,CAACzF,IAAI;MAExD6E,sBAAsB,CAAE4I,QAAQ,EAAEhI,WAAY,CAAC;MAE/CrK,MAAM,CAACyd,YAAY,CAAC1U,GAAG,CAAEsJ,QAAQ,EAAE;QAAElH,SAAS,EAAED;MAAc,CAAE,CAAC;MAEjE,IAAKb,WAAW,CAAChH,UAAU,EAAG0X,8BAA8B,CAAE1X,UAAU,EAAEgP,QAAQ,EAAEhI,WAAY,CAAC;MAEjG,OAAOgI,QAAQ;IAEhB,CAAE,CAAC;EAEJ;;EAEA;EACA1I,gBAAgBA,CAAEmd,YAAY,EAAG;IAEhC,MAAMC,aAAa,GAAGxoB,eAAe,CAACyoB,gBAAgB,CAAEF,YAAY,IAAI,EAAG,CAAC;IAE5E,IAAKC,aAAa,IAAI,IAAI,CAAC7I,aAAa,EAAG;MAE1C,OAAO6I,aAAa,GAAG,GAAG,GAAK,EAAG,IAAI,CAAC7I,aAAa,CAAE6I,aAAa,CAAI;IAExE,CAAC,MAAM;MAEN,IAAI,CAAC7I,aAAa,CAAE6I,aAAa,CAAE,GAAG,CAAC;MAEvC,OAAOA,aAAa;IAErB;EAED;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCE,cAAcA,CAAEhW,UAAU,EAAG;IAE5B,MAAMjR,MAAM,GAAG,IAAI;IACnB,MAAMqD,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMiE,KAAK,GAAG,IAAI,CAACqW,cAAc;IAEjC,SAASuJ,oBAAoBA,CAAElW,SAAS,EAAG;MAE1C,OAAO3N,UAAU,CAAEU,UAAU,CAACmB,0BAA0B,CAAE,CACxDiP,eAAe,CAAEnD,SAAS,EAAEhR,MAAO,CAAC,CACpCgK,IAAI,CAAE,UAAWoI,QAAQ,EAAG;QAE5B,OAAO+U,sBAAsB,CAAE/U,QAAQ,EAAEpB,SAAS,EAAEhR,MAAO,CAAC;MAE7D,CAAE,CAAC;IAEL;IAEA,MAAMsK,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI7F,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG5K,UAAU,CAACvM,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAEvD,MAAMuM,SAAS,GAAGC,UAAU,CAAExM,CAAC,CAAE;MACjC,MAAM0D,QAAQ,GAAGyU,kBAAkB,CAAE5L,SAAU,CAAC;;MAEhD;MACA,MAAMoW,MAAM,GAAG9f,KAAK,CAAEa,QAAQ,CAAE;MAEhC,IAAKif,MAAM,EAAG;QAEb;QACA9c,OAAO,CAACrH,IAAI,CAAEmkB,MAAM,CAAC9D,OAAQ,CAAC;MAE/B,CAAC,MAAM;QAEN,IAAI+D,eAAe;QAEnB,IAAKrW,SAAS,CAAC3N,UAAU,IAAI2N,SAAS,CAAC3N,UAAU,CAAEU,UAAU,CAACmB,0BAA0B,CAAE,EAAG;UAE5F;UACAmiB,eAAe,GAAGH,oBAAoB,CAAElW,SAAU,CAAC;QAEpD,CAAC,MAAM;UAEN;UACAqW,eAAe,GAAGF,sBAAsB,CAAE,IAAItrB,cAAc,CAAC,CAAC,EAAEmV,SAAS,EAAEhR,MAAO,CAAC;QAEpF;;QAEA;QACAsH,KAAK,CAAEa,QAAQ,CAAE,GAAG;UAAE6I,SAAS,EAAEA,SAAS;UAAEsS,OAAO,EAAE+D;QAAgB,CAAC;QAEtE/c,OAAO,CAACrH,IAAI,CAAEokB,eAAgB,CAAC;MAEhC;IAED;IAEA,OAAOzhB,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;;EAEA;AACD;AACA;AACA;AACA;EACCsW,QAAQA,CAAE0G,SAAS,EAAG;IAErB,MAAMtnB,MAAM,GAAG,IAAI;IACnB,MAAMoD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;IAElC,MAAMyN,OAAO,GAAG1N,IAAI,CAAC2N,MAAM,CAAEuW,SAAS,CAAE;IACxC,MAAMrW,UAAU,GAAGH,OAAO,CAACG,UAAU;IAErC,MAAM3G,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI7F,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG5K,UAAU,CAACvM,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAEvD,MAAM4N,QAAQ,GAAGpB,UAAU,CAAExM,CAAC,CAAE,CAAC4N,QAAQ,KAAKjO,SAAS,GACpDoW,qBAAqB,CAAE,IAAI,CAAClT,KAAM,CAAC,GACnC,IAAI,CAACsC,aAAa,CAAE,UAAU,EAAEqH,UAAU,CAAExM,CAAC,CAAE,CAAC4N,QAAS,CAAC;MAE7D/H,OAAO,CAACrH,IAAI,CAAEoP,QAAS,CAAC;IAEzB;IAEA/H,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAACinB,cAAc,CAAEhW,UAAW,CAAE,CAAC;IAEnD,OAAOrL,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWyH,OAAO,EAAG;MAExD,MAAMtG,SAAS,GAAGsG,OAAO,CAACiC,KAAK,CAAE,CAAC,EAAEjC,OAAO,CAAC/M,MAAM,GAAG,CAAE,CAAC;MACxD,MAAM6iB,UAAU,GAAG9V,OAAO,CAAEA,OAAO,CAAC/M,MAAM,GAAG,CAAC,CAAE;MAEhD,MAAMqM,MAAM,GAAG,EAAE;MAEjB,KAAM,IAAItM,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG0L,UAAU,CAAC7iB,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAEvD,MAAM2N,QAAQ,GAAGmV,UAAU,CAAE9iB,CAAC,CAAE;QAChC,MAAMuM,SAAS,GAAGC,UAAU,CAAExM,CAAC,CAAE;;QAEjC;;QAEA,IAAIoM,IAAI;QAER,MAAMwB,QAAQ,GAAGlH,SAAS,CAAE1G,CAAC,CAAE;QAE/B,IAAKuM,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACC,SAAS,IAC/CH,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACE,cAAc,IACjDJ,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACG,YAAY,IAC/CL,SAAS,CAACT,IAAI,KAAKnM,SAAS,EAAG;UAEhC;UACAyM,IAAI,GAAGC,OAAO,CAACiP,aAAa,KAAK,IAAI,GAClC,IAAInhB,WAAW,CAAEwT,QAAQ,EAAEC,QAAS,CAAC,GACrC,IAAI7U,IAAI,CAAE4U,QAAQ,EAAEC,QAAS,CAAC;UAEjC,IAAKxB,IAAI,CAACkP,aAAa,KAAK,IAAI,EAAG;YAElC;YACAlP,IAAI,CAAC2W,oBAAoB,CAAC,CAAC;UAE5B;UAEA,IAAKxW,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACE,cAAc,EAAG;YAExDP,IAAI,CAACuB,QAAQ,GAAG7S,mBAAmB,CAAEsR,IAAI,CAACuB,QAAQ,EAAElT,qBAAsB,CAAC;UAE5E,CAAC,MAAM,IAAK8R,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACG,YAAY,EAAG;YAE7DR,IAAI,CAACuB,QAAQ,GAAG7S,mBAAmB,CAAEsR,IAAI,CAACuB,QAAQ,EAAEnT,mBAAoB,CAAC;UAE1E;QAED,CAAC,MAAM,IAAK+R,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACqH,KAAK,EAAG;UAEtD1H,IAAI,GAAG,IAAI9T,YAAY,CAAEqV,QAAQ,EAAEC,QAAS,CAAC;QAE9C,CAAC,MAAM,IAAKrB,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACuH,UAAU,EAAG;UAE3D5H,IAAI,GAAG,IAAIjU,IAAI,CAAEwV,QAAQ,EAAEC,QAAS,CAAC;QAEtC,CAAC,MAAM,IAAKrB,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACsH,SAAS,EAAG;UAE1D3H,IAAI,GAAG,IAAI/T,QAAQ,CAAEsV,QAAQ,EAAEC,QAAS,CAAC;QAE1C,CAAC,MAAM,IAAKrB,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACoH,MAAM,EAAG;UAEvDzH,IAAI,GAAG,IAAIxS,MAAM,CAAE+T,QAAQ,EAAEC,QAAS,CAAC;QAExC,CAAC,MAAM;UAEN,MAAM,IAAIzP,KAAK,CAAE,gDAAgD,GAAGoO,SAAS,CAACT,IAAK,CAAC;QAErF;QAEA,IAAK+K,MAAM,CAAC6B,IAAI,CAAEtM,IAAI,CAACuB,QAAQ,CAACkK,eAAgB,CAAC,CAAC5X,MAAM,GAAG,CAAC,EAAG;UAE9D8X,kBAAkB,CAAE3L,IAAI,EAAEC,OAAQ,CAAC;QAEpC;QAEAD,IAAI,CAACjM,IAAI,GAAG5E,MAAM,CAAC2J,gBAAgB,CAAEmH,OAAO,CAAClM,IAAI,IAAM,OAAO,GAAG0iB,SAAY,CAAC;QAE9E7d,sBAAsB,CAAEoH,IAAI,EAAEC,OAAQ,CAAC;QAEvC,IAAKE,SAAS,CAAC3N,UAAU,EAAG0X,8BAA8B,CAAE1X,UAAU,EAAEwN,IAAI,EAAEG,SAAU,CAAC;QAEzFhR,MAAM,CAACiT,mBAAmB,CAAEpC,IAAK,CAAC;QAElCE,MAAM,CAAC9N,IAAI,CAAE4N,IAAK,CAAC;MAEpB;MAEA,KAAM,IAAIpM,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG9K,MAAM,CAACrM,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAEnDzE,MAAM,CAACyd,YAAY,CAAC1U,GAAG,CAAEgI,MAAM,CAAEtM,CAAC,CAAE,EAAE;UACrCsM,MAAM,EAAEuW,SAAS;UACjBrW,UAAU,EAAExM;QACb,CAAE,CAAC;MAEJ;MAEA,IAAKsM,MAAM,CAACrM,MAAM,KAAK,CAAC,EAAG;QAE1B,IAAKoM,OAAO,CAACzN,UAAU,EAAG0X,8BAA8B,CAAE1X,UAAU,EAAE0N,MAAM,CAAE,CAAC,CAAE,EAAED,OAAQ,CAAC;QAE5F,OAAOC,MAAM,CAAE,CAAC,CAAE;MAEnB;MAEA,MAAM0W,KAAK,GAAG,IAAIrrB,KAAK,CAAC,CAAC;MAEzB,IAAK0U,OAAO,CAACzN,UAAU,EAAG0X,8BAA8B,CAAE1X,UAAU,EAAEokB,KAAK,EAAE3W,OAAQ,CAAC;MAEtF9Q,MAAM,CAACyd,YAAY,CAAC1U,GAAG,CAAE0e,KAAK,EAAE;QAAE1W,MAAM,EAAEuW;MAAU,CAAE,CAAC;MAEvD,KAAM,IAAI7iB,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG9K,MAAM,CAACrM,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAEnDgjB,KAAK,CAACthB,GAAG,CAAE4K,MAAM,CAAEtM,CAAC,CAAG,CAAC;MAEzB;MAEA,OAAOgjB,KAAK;IAEb,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;EACCvG,UAAUA,CAAEwG,WAAW,EAAG;IAEzB,IAAI1H,MAAM;IACV,MAAM2H,SAAS,GAAG,IAAI,CAACvkB,IAAI,CAACgc,OAAO,CAAEsI,WAAW,CAAE;IAClD,MAAME,MAAM,GAAGD,SAAS,CAAEA,SAAS,CAAC/e,IAAI,CAAE;IAE1C,IAAK,CAAEgf,MAAM,EAAG;MAEfhmB,OAAO,CAAC4D,IAAI,CAAE,8CAA+C,CAAC;MAC9D;IAED;IAEA,IAAKmiB,SAAS,CAAC/e,IAAI,KAAK,aAAa,EAAG;MAEvCoX,MAAM,GAAG,IAAI7hB,iBAAiB,CAAEb,SAAS,CAACuqB,QAAQ,CAAED,MAAM,CAACE,IAAK,CAAC,EAAEF,MAAM,CAACG,WAAW,IAAI,CAAC,EAAEH,MAAM,CAACI,KAAK,IAAI,CAAC,EAAEJ,MAAM,CAACK,IAAI,IAAI,GAAI,CAAC;IAEpI,CAAC,MAAM,IAAKN,SAAS,CAAC/e,IAAI,KAAK,cAAc,EAAG;MAE/CoX,MAAM,GAAG,IAAI9hB,kBAAkB,CAAE,CAAE0pB,MAAM,CAACM,IAAI,EAAEN,MAAM,CAACM,IAAI,EAAEN,MAAM,CAACO,IAAI,EAAE,CAAEP,MAAM,CAACO,IAAI,EAAEP,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,IAAK,CAAC;IAErH;IAEA,IAAKN,SAAS,CAAC/iB,IAAI,EAAGob,MAAM,CAACpb,IAAI,GAAG,IAAI,CAAC+E,gBAAgB,CAAEge,SAAS,CAAC/iB,IAAK,CAAC;IAE3E6E,sBAAsB,CAAEuW,MAAM,EAAE2H,SAAU,CAAC;IAE3C,OAAO/hB,OAAO,CAACC,OAAO,CAAEma,MAAO,CAAC;EAEjC;;EAEA;AACD;AACA;AACA;AACA;EACCgB,QAAQA,CAAEtB,SAAS,EAAG;IAErB,MAAM0I,OAAO,GAAG,IAAI,CAAChlB,IAAI,CAACoc,KAAK,CAAEE,SAAS,CAAE;IAE5C,MAAMpV,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI7F,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGuM,OAAO,CAACxI,MAAM,CAAClb,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAE3D6F,OAAO,CAACrH,IAAI,CAAE,IAAI,CAAColB,gBAAgB,CAAED,OAAO,CAACxI,MAAM,CAAEnb,CAAC,CAAG,CAAE,CAAC;IAE7D;IAEA,IAAK2jB,OAAO,CAACE,mBAAmB,KAAKlkB,SAAS,EAAG;MAEhDkG,OAAO,CAACrH,IAAI,CAAE,IAAI,CAAC2G,aAAa,CAAE,UAAU,EAAEwe,OAAO,CAACE,mBAAoB,CAAE,CAAC;IAE9E,CAAC,MAAM;MAENhe,OAAO,CAACrH,IAAI,CAAE,IAAK,CAAC;IAErB;IAEA,OAAO2C,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWyH,OAAO,EAAG;MAExD,MAAM6W,mBAAmB,GAAG7W,OAAO,CAACE,GAAG,CAAC,CAAC;MACzC,MAAM4W,UAAU,GAAG9W,OAAO;;MAE1B;MACA;;MAEA,MAAM+W,KAAK,GAAG,EAAE;MAChB,MAAMC,YAAY,GAAG,EAAE;MAEvB,KAAM,IAAIhkB,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG0M,UAAU,CAAC7jB,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAEvD,MAAMikB,SAAS,GAAGH,UAAU,CAAE9jB,CAAC,CAAE;QAEjC,IAAKikB,SAAS,EAAG;UAEhBF,KAAK,CAACvlB,IAAI,CAAEylB,SAAU,CAAC;UAEvB,MAAMC,GAAG,GAAG,IAAIprB,OAAO,CAAC,CAAC;UAEzB,IAAK+qB,mBAAmB,KAAK,IAAI,EAAG;YAEnCK,GAAG,CAACjgB,SAAS,CAAE4f,mBAAmB,CAACzd,KAAK,EAAEpG,CAAC,GAAG,EAAG,CAAC;UAEnD;UAEAgkB,YAAY,CAACxlB,IAAI,CAAE0lB,GAAI,CAAC;QAEzB,CAAC,MAAM;UAEN/mB,OAAO,CAAC4D,IAAI,CAAE,kDAAkD,EAAE4iB,OAAO,CAACxI,MAAM,CAAEnb,CAAC,CAAG,CAAC;QAExF;MAED;MAEA,OAAO,IAAI9F,QAAQ,CAAE6pB,KAAK,EAAEC,YAAa,CAAC;IAE3C,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;EACCxH,aAAaA,CAAE2H,cAAc,EAAG;IAE/B,MAAMxlB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMpD,MAAM,GAAG,IAAI;IAEnB,MAAM6oB,YAAY,GAAGzlB,IAAI,CAAC+b,UAAU,CAAEyJ,cAAc,CAAE;IACtD,MAAME,aAAa,GAAGD,YAAY,CAACjkB,IAAI,GAAGikB,YAAY,CAACjkB,IAAI,GAAG,YAAY,GAAGgkB,cAAc;IAE3F,MAAMG,YAAY,GAAG,EAAE;IACvB,MAAMC,qBAAqB,GAAG,EAAE;IAChC,MAAMC,sBAAsB,GAAG,EAAE;IACjC,MAAMC,eAAe,GAAG,EAAE;IAC1B,MAAMC,cAAc,GAAG,EAAE;IAEzB,KAAM,IAAI1kB,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGgN,YAAY,CAACO,QAAQ,CAAC1kB,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAElE,MAAM8Q,OAAO,GAAGsT,YAAY,CAACO,QAAQ,CAAE3kB,CAAC,CAAE;MAC1C,MAAM4e,OAAO,GAAGwF,YAAY,CAACnF,QAAQ,CAAEnO,OAAO,CAAC8N,OAAO,CAAE;MACxD,MAAMxa,MAAM,GAAG0M,OAAO,CAAC1M,MAAM;MAC7B,MAAMjE,IAAI,GAAGiE,MAAM,CAACwgB,IAAI;MACxB,MAAMC,KAAK,GAAGT,YAAY,CAACU,UAAU,KAAKnlB,SAAS,GAAGykB,YAAY,CAACU,UAAU,CAAElG,OAAO,CAACiG,KAAK,CAAE,GAAGjG,OAAO,CAACiG,KAAK;MAC9G,MAAME,MAAM,GAAGX,YAAY,CAACU,UAAU,KAAKnlB,SAAS,GAAGykB,YAAY,CAACU,UAAU,CAAElG,OAAO,CAACmG,MAAM,CAAE,GAAGnG,OAAO,CAACmG,MAAM;MAEjH,IAAK3gB,MAAM,CAACwgB,IAAI,KAAKjlB,SAAS,EAAG;MAEjC2kB,YAAY,CAAC9lB,IAAI,CAAE,IAAI,CAAC2G,aAAa,CAAE,MAAM,EAAEhF,IAAK,CAAE,CAAC;MACvDokB,qBAAqB,CAAC/lB,IAAI,CAAE,IAAI,CAAC2G,aAAa,CAAE,UAAU,EAAE0f,KAAM,CAAE,CAAC;MACrEL,sBAAsB,CAAChmB,IAAI,CAAE,IAAI,CAAC2G,aAAa,CAAE,UAAU,EAAE4f,MAAO,CAAE,CAAC;MACvEN,eAAe,CAACjmB,IAAI,CAAEogB,OAAQ,CAAC;MAC/B8F,cAAc,CAAClmB,IAAI,CAAE4F,MAAO,CAAC;IAE9B;IAEA,OAAOjD,OAAO,CAACoF,GAAG,CAAE,CAEnBpF,OAAO,CAACoF,GAAG,CAAE+d,YAAa,CAAC,EAC3BnjB,OAAO,CAACoF,GAAG,CAAEge,qBAAsB,CAAC,EACpCpjB,OAAO,CAACoF,GAAG,CAAEie,sBAAuB,CAAC,EACrCrjB,OAAO,CAACoF,GAAG,CAAEke,eAAgB,CAAC,EAC9BtjB,OAAO,CAACoF,GAAG,CAAEme,cAAe,CAAC,CAE5B,CAAC,CAACnf,IAAI,CAAE,UAAWgV,YAAY,EAAG;MAEnC,MAAMrX,KAAK,GAAGqX,YAAY,CAAE,CAAC,CAAE;MAC/B,MAAMyK,cAAc,GAAGzK,YAAY,CAAE,CAAC,CAAE;MACxC,MAAM0K,eAAe,GAAG1K,YAAY,CAAE,CAAC,CAAE;MACzC,MAAM0E,QAAQ,GAAG1E,YAAY,CAAE,CAAC,CAAE;MAClC,MAAMvD,OAAO,GAAGuD,YAAY,CAAE,CAAC,CAAE;MAEjC,MAAM2K,MAAM,GAAG,EAAE;MAEjB,KAAM,IAAIllB,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGlU,KAAK,CAACjD,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAElD,MAAM4kB,IAAI,GAAG1hB,KAAK,CAAElD,CAAC,CAAE;QACvB,MAAMmlB,aAAa,GAAGH,cAAc,CAAEhlB,CAAC,CAAE;QACzC,MAAMolB,cAAc,GAAGH,eAAe,CAAEjlB,CAAC,CAAE;QAC3C,MAAM4e,OAAO,GAAGK,QAAQ,CAAEjf,CAAC,CAAE;QAC7B,MAAMoE,MAAM,GAAG4S,OAAO,CAAEhX,CAAC,CAAE;QAE3B,IAAK4kB,IAAI,KAAKjlB,SAAS,EAAG;QAE1B,IAAKilB,IAAI,CAACS,YAAY,EAAG;UAExBT,IAAI,CAACS,YAAY,CAAC,CAAC;UACnBT,IAAI,CAACU,gBAAgB,GAAG,IAAI;QAE7B;QAEA,MAAMC,aAAa,GAAGhqB,MAAM,CAACiqB,sBAAsB,CAAEZ,IAAI,EAAEO,aAAa,EAAEC,cAAc,EAAExG,OAAO,EAAExa,MAAO,CAAC;QAE3G,IAAKmhB,aAAa,EAAG;UAEpB,KAAM,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAACtlB,MAAM,EAAEwlB,CAAC,EAAG,EAAG;YAEjDP,MAAM,CAAC1mB,IAAI,CAAE+mB,aAAa,CAAEE,CAAC,CAAG,CAAC;UAElC;QAED;MAED;MAEA,OAAO,IAAIzuB,aAAa,CAAEqtB,aAAa,EAAE1kB,SAAS,EAAEulB,MAAO,CAAC;IAE7D,CAAE,CAAC;EAEJ;EAEA/Y,cAAcA,CAAEhJ,SAAS,EAAG;IAE3B,MAAMxE,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMpD,MAAM,GAAG,IAAI;IACnB,MAAM8H,OAAO,GAAG1E,IAAI,CAACuE,KAAK,CAAEC,SAAS,CAAE;IAEvC,IAAKE,OAAO,CAAC+I,IAAI,KAAKzM,SAAS,EAAG,OAAO,IAAI;IAE7C,OAAOpE,MAAM,CAAC4J,aAAa,CAAE,MAAM,EAAE9B,OAAO,CAAC+I,IAAK,CAAC,CAAC7G,IAAI,CAAE,UAAW6G,IAAI,EAAG;MAE3E,MAAMwY,IAAI,GAAGrpB,MAAM,CAACiK,WAAW,CAAEjK,MAAM,CAAC6d,SAAS,EAAE/V,OAAO,CAAC+I,IAAI,EAAEA,IAAK,CAAC;;MAEvE;MACA,IAAK/I,OAAO,CAACkS,OAAO,KAAK5V,SAAS,EAAG;QAEpCilB,IAAI,CAACc,QAAQ,CAAE,UAAWC,CAAC,EAAG;UAE7B,IAAK,CAAEA,CAAC,CAACC,MAAM,EAAG;UAElB,KAAM,IAAI5lB,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG/T,OAAO,CAACkS,OAAO,CAACtV,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;YAE5D2lB,CAAC,CAAC3N,qBAAqB,CAAEhY,CAAC,CAAE,GAAGqD,OAAO,CAACkS,OAAO,CAAEvV,CAAC,CAAE;UAEpD;QAED,CAAE,CAAC;MAEJ;MAEA,OAAO4kB,IAAI;IAEZ,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;EACC1I,QAAQA,CAAE/Y,SAAS,EAAG;IAErB,MAAMxE,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMpD,MAAM,GAAG,IAAI;IAEnB,MAAM8H,OAAO,GAAG1E,IAAI,CAACuE,KAAK,CAAEC,SAAS,CAAE;IAEvC,MAAM0iB,WAAW,GAAGtqB,MAAM,CAACqoB,gBAAgB,CAAEzgB,SAAU,CAAC;IAExD,MAAM2iB,YAAY,GAAG,EAAE;IACvB,MAAMC,WAAW,GAAG1iB,OAAO,CAAC+J,QAAQ,IAAI,EAAE;IAE1C,KAAM,IAAIpN,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG2O,WAAW,CAAC9lB,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAExD8lB,YAAY,CAACtnB,IAAI,CAAEjD,MAAM,CAAC4J,aAAa,CAAE,MAAM,EAAE4gB,WAAW,CAAE/lB,CAAC,CAAG,CAAE,CAAC;IAEtE;IAEA,MAAMgmB,eAAe,GAAG3iB,OAAO,CAACgY,IAAI,KAAK1b,SAAS,GAC/CwB,OAAO,CAACC,OAAO,CAAE,IAAK,CAAC,GACvB7F,MAAM,CAAC4J,aAAa,CAAE,MAAM,EAAE9B,OAAO,CAACgY,IAAK,CAAC;IAE/C,OAAOla,OAAO,CAACoF,GAAG,CAAE,CACnBsf,WAAW,EACX1kB,OAAO,CAACoF,GAAG,CAAEuf,YAAa,CAAC,EAC3BE,eAAe,CACd,CAAC,CAACzgB,IAAI,CAAE,UAAWyH,OAAO,EAAG;MAE9B,MAAM4X,IAAI,GAAG5X,OAAO,CAAE,CAAC,CAAE;MACzB,MAAMI,QAAQ,GAAGJ,OAAO,CAAE,CAAC,CAAE;MAC7B,MAAMiZ,QAAQ,GAAGjZ,OAAO,CAAE,CAAC,CAAE;MAE7B,IAAKiZ,QAAQ,KAAK,IAAI,EAAG;QAExB;QACA;QACArB,IAAI,CAACc,QAAQ,CAAE,UAAWtZ,IAAI,EAAG;UAEhC,IAAK,CAAEA,IAAI,CAACkP,aAAa,EAAG;UAE5BlP,IAAI,CAAC8Z,IAAI,CAAED,QAAQ,EAAElN,eAAgB,CAAC;QAEvC,CAAE,CAAC;MAEJ;MAEA,KAAM,IAAI/Y,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGhK,QAAQ,CAACnN,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAErD4kB,IAAI,CAACljB,GAAG,CAAE0L,QAAQ,CAAEpN,CAAC,CAAG,CAAC;MAE1B;MAEA,OAAO4kB,IAAI;IAEZ,CAAE,CAAC;EAEJ;;EAEA;EACA;EACAhB,gBAAgBA,CAAEzgB,SAAS,EAAG;IAE7B,MAAMxE,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMrD,MAAM,GAAG,IAAI;;IAEnB;IACA;;IAEA,IAAK,IAAI,CAAC4d,SAAS,CAAEhW,SAAS,CAAE,KAAKxD,SAAS,EAAG;MAEhD,OAAO,IAAI,CAACwZ,SAAS,CAAEhW,SAAS,CAAE;IAEnC;IAEA,MAAME,OAAO,GAAG1E,IAAI,CAACuE,KAAK,CAAEC,SAAS,CAAE;;IAEvC;IACA,MAAMgjB,QAAQ,GAAG9iB,OAAO,CAAClD,IAAI,GAAG5E,MAAM,CAAC2J,gBAAgB,CAAE7B,OAAO,CAAClD,IAAK,CAAC,GAAG,EAAE;IAE5E,MAAM0F,OAAO,GAAG,EAAE;IAElB,MAAMugB,WAAW,GAAG7qB,MAAM,CAACugB,UAAU,CAAE,UAAW1B,GAAG,EAAG;MAEvD,OAAOA,GAAG,CAACjO,cAAc,IAAIiO,GAAG,CAACjO,cAAc,CAAEhJ,SAAU,CAAC;IAE7D,CAAE,CAAC;IAEH,IAAKijB,WAAW,EAAG;MAElBvgB,OAAO,CAACrH,IAAI,CAAE4nB,WAAY,CAAC;IAE5B;IAEA,IAAK/iB,OAAO,CAACkY,MAAM,KAAK5b,SAAS,EAAG;MAEnCkG,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC4J,aAAa,CAAE,QAAQ,EAAE9B,OAAO,CAACkY,MAAO,CAAC,CAAChW,IAAI,CAAE,UAAWgW,MAAM,EAAG;QAExF,OAAOhgB,MAAM,CAACiK,WAAW,CAAEjK,MAAM,CAAC8d,WAAW,EAAEhW,OAAO,CAACkY,MAAM,EAAEA,MAAO,CAAC;MAExE,CAAE,CAAE,CAAC;IAEN;IAEAhgB,MAAM,CAAC4e,UAAU,CAAE,UAAWC,GAAG,EAAG;MAEnC,OAAOA,GAAG,CAAC/U,oBAAoB,IAAI+U,GAAG,CAAC/U,oBAAoB,CAAElC,SAAU,CAAC;IAEzE,CAAE,CAAC,CAACkjB,OAAO,CAAE,UAAWxH,OAAO,EAAG;MAEjChZ,OAAO,CAACrH,IAAI,CAAEqgB,OAAQ,CAAC;IAExB,CAAE,CAAC;IAEH,IAAI,CAAC1F,SAAS,CAAEhW,SAAS,CAAE,GAAGhC,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWhE,OAAO,EAAG;MAE/E,IAAIqjB,IAAI;;MAER;MACA,IAAKvhB,OAAO,CAAC+X,MAAM,KAAK,IAAI,EAAG;QAE9BwJ,IAAI,GAAG,IAAI3tB,IAAI,CAAC,CAAC;MAElB,CAAC,MAAM,IAAKsK,OAAO,CAACtB,MAAM,GAAG,CAAC,EAAG;QAEhC2kB,IAAI,GAAG,IAAIjtB,KAAK,CAAC,CAAC;MAEnB,CAAC,MAAM,IAAK4J,OAAO,CAACtB,MAAM,KAAK,CAAC,EAAG;QAElC2kB,IAAI,GAAGrjB,OAAO,CAAE,CAAC,CAAE;MAEpB,CAAC,MAAM;QAENqjB,IAAI,GAAG,IAAIprB,QAAQ,CAAC,CAAC;MAEtB;MAEA,IAAKorB,IAAI,KAAKrjB,OAAO,CAAE,CAAC,CAAE,EAAG;QAE5B,KAAM,IAAIvB,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAG7V,OAAO,CAACtB,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;UAEpD4kB,IAAI,CAACljB,GAAG,CAAEH,OAAO,CAAEvB,CAAC,CAAG,CAAC;QAEzB;MAED;MAEA,IAAKqD,OAAO,CAAClD,IAAI,EAAG;QAEnBykB,IAAI,CAACnO,QAAQ,CAACtW,IAAI,GAAGkD,OAAO,CAAClD,IAAI;QACjCykB,IAAI,CAACzkB,IAAI,GAAGgmB,QAAQ;MAErB;MAEAnhB,sBAAsB,CAAE4f,IAAI,EAAEvhB,OAAQ,CAAC;MAEvC,IAAKA,OAAO,CAACzE,UAAU,EAAG0X,8BAA8B,CAAE1X,UAAU,EAAEgmB,IAAI,EAAEvhB,OAAQ,CAAC;MAErF,IAAKA,OAAO,CAACijB,MAAM,KAAK3mB,SAAS,EAAG;QAEnC,MAAM2mB,MAAM,GAAG,IAAIxtB,OAAO,CAAC,CAAC;QAC5BwtB,MAAM,CAACriB,SAAS,CAAEZ,OAAO,CAACijB,MAAO,CAAC;QAClC1B,IAAI,CAAC2B,YAAY,CAAED,MAAO,CAAC;MAE5B,CAAC,MAAM;QAEN,IAAKjjB,OAAO,CAACiS,WAAW,KAAK3V,SAAS,EAAG;UAExCilB,IAAI,CAACvgB,QAAQ,CAACJ,SAAS,CAAEZ,OAAO,CAACiS,WAAY,CAAC;QAE/C;QAEA,IAAKjS,OAAO,CAAC2N,QAAQ,KAAKrR,SAAS,EAAG;UAErCilB,IAAI,CAAC4B,UAAU,CAACviB,SAAS,CAAEZ,OAAO,CAAC2N,QAAS,CAAC;QAE9C;QAEA,IAAK3N,OAAO,CAACgE,KAAK,KAAK1H,SAAS,EAAG;UAElCilB,IAAI,CAACvd,KAAK,CAACpD,SAAS,CAAEZ,OAAO,CAACgE,KAAM,CAAC;QAEtC;MAED;MAEA,IAAK,CAAE9L,MAAM,CAACyd,YAAY,CAACyN,GAAG,CAAE7B,IAAK,CAAC,EAAG;QAExCrpB,MAAM,CAACyd,YAAY,CAAC1U,GAAG,CAAEsgB,IAAI,EAAE,CAAC,CAAE,CAAC;MAEpC;MAEArpB,MAAM,CAACyd,YAAY,CAACxX,GAAG,CAAEojB,IAAK,CAAC,CAAC1hB,KAAK,GAAGC,SAAS;MAEjD,OAAOyhB,IAAI;IAEZ,CAAE,CAAC;IAEH,OAAO,IAAI,CAACzL,SAAS,CAAEhW,SAAS,CAAE;EAEnC;;EAEA;AACD;AACA;AACA;AACA;EACC8Y,SAASA,CAAEyK,UAAU,EAAG;IAEvB,MAAM9nB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAM+nB,QAAQ,GAAG,IAAI,CAAChoB,IAAI,CAAC8b,MAAM,CAAEiM,UAAU,CAAE;IAC/C,MAAMnrB,MAAM,GAAG,IAAI;;IAEnB;IACA;IACA,MAAMif,KAAK,GAAG,IAAI7iB,KAAK,CAAC,CAAC;IACzB,IAAKgvB,QAAQ,CAACxmB,IAAI,EAAGqa,KAAK,CAACra,IAAI,GAAG5E,MAAM,CAAC2J,gBAAgB,CAAEyhB,QAAQ,CAACxmB,IAAK,CAAC;IAE1E6E,sBAAsB,CAAEwV,KAAK,EAAEmM,QAAS,CAAC;IAEzC,IAAKA,QAAQ,CAAC/nB,UAAU,EAAG0X,8BAA8B,CAAE1X,UAAU,EAAE4b,KAAK,EAAEmM,QAAS,CAAC;IAExF,MAAMC,OAAO,GAAGD,QAAQ,CAACzjB,KAAK,IAAI,EAAE;IAEpC,MAAM2C,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI7F,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGwP,OAAO,CAAC3mB,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAEpD6F,OAAO,CAACrH,IAAI,CAAEjD,MAAM,CAAC4J,aAAa,CAAE,MAAM,EAAEyhB,OAAO,CAAE5mB,CAAC,CAAG,CAAE,CAAC;IAE7D;IAEA,OAAOmB,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWrC,KAAK,EAAG;MAEtD,KAAM,IAAIlD,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGlU,KAAK,CAACjD,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;QAElDwa,KAAK,CAAC9Y,GAAG,CAAEwB,KAAK,CAAElD,CAAC,CAAG,CAAC;MAExB;;MAEA;MACA;MACA,MAAM6mB,kBAAkB,GAAKjC,IAAI,IAAM;QAEtC,MAAMkC,mBAAmB,GAAG,IAAI7N,GAAG,CAAC,CAAC;QAErC,KAAM,MAAM,CAAExX,GAAG,EAAEslB,KAAK,CAAE,IAAIxrB,MAAM,CAACyd,YAAY,EAAG;UAEnD,IAAKvX,GAAG,YAAY7I,QAAQ,IAAI6I,GAAG,YAAYnH,OAAO,EAAG;YAExDwsB,mBAAmB,CAACxiB,GAAG,CAAE7C,GAAG,EAAEslB,KAAM,CAAC;UAEtC;QAED;QAEAnC,IAAI,CAACc,QAAQ,CAAId,IAAI,IAAM;UAE1B,MAAMjJ,QAAQ,GAAGpgB,MAAM,CAACyd,YAAY,CAACxX,GAAG,CAAEojB,IAAK,CAAC;UAEhD,IAAKjJ,QAAQ,IAAI,IAAI,EAAG;YAEvBmL,mBAAmB,CAACxiB,GAAG,CAAEsgB,IAAI,EAAEjJ,QAAS,CAAC;UAE1C;QAED,CAAE,CAAC;QAEH,OAAOmL,mBAAmB;MAE3B,CAAC;MAEDvrB,MAAM,CAACyd,YAAY,GAAG6N,kBAAkB,CAAErM,KAAM,CAAC;MAEjD,OAAOA,KAAK;IAEb,CAAE,CAAC;EAEJ;EAEAgL,sBAAsBA,CAAEZ,IAAI,EAAEO,aAAa,EAAEC,cAAc,EAAExG,OAAO,EAAExa,MAAM,EAAG;IAE9E,MAAM8gB,MAAM,GAAG,EAAE;IAEjB,MAAM8B,UAAU,GAAGpC,IAAI,CAACzkB,IAAI,GAAGykB,IAAI,CAACzkB,IAAI,GAAGykB,IAAI,CAAClE,IAAI;IAEpD,MAAMzI,WAAW,GAAG,EAAE;IAEtB,IAAK5C,eAAe,CAAEjR,MAAM,CAACtH,IAAI,CAAE,KAAKuY,eAAe,CAACE,OAAO,EAAG;MAEjEqP,IAAI,CAACc,QAAQ,CAAE,UAAW/jB,MAAM,EAAG;QAElC,IAAKA,MAAM,CAACqW,qBAAqB,EAAG;UAEnCC,WAAW,CAACzZ,IAAI,CAAEmD,MAAM,CAACxB,IAAI,GAAGwB,MAAM,CAACxB,IAAI,GAAGwB,MAAM,CAAC+e,IAAK,CAAC;QAE5D;MAED,CAAE,CAAC;IAEJ,CAAC,MAAM;MAENzI,WAAW,CAACzZ,IAAI,CAAEwoB,UAAW,CAAC;IAE/B;IAEA,IAAIC,kBAAkB;IAEtB,QAAS5R,eAAe,CAAEjR,MAAM,CAACtH,IAAI,CAAE;MAEtC,KAAKuY,eAAe,CAACE,OAAO;QAE3B0R,kBAAkB,GAAG1tB,mBAAmB;QACxC;MAED,KAAK8b,eAAe,CAACrE,QAAQ;QAE5BiW,kBAAkB,GAAGjtB,uBAAuB;QAC5C;MAED,KAAKqb,eAAe,CAAChR,QAAQ;MAC7B,KAAKgR,eAAe,CAAChO,KAAK;MAC1B;QACC,QAAS+d,cAAc,CAAChI,QAAQ;UAE/B,KAAK,CAAC;YACL6J,kBAAkB,GAAG1tB,mBAAmB;YACxC;UACD,KAAK,CAAC;UACN,KAAK,CAAC;YACL0tB,kBAAkB,GAAGrsB,mBAAmB;YACxC;QAEF;QAEA;IAEF;IAEA,MAAMssB,aAAa,GAAGtI,OAAO,CAACsI,aAAa,KAAKvnB,SAAS,GAAG6V,aAAa,CAAEoJ,OAAO,CAACsI,aAAa,CAAE,GAAGhvB,iBAAiB;IAEtH,MAAMivB,WAAW,GAAG,IAAI,CAACC,qBAAqB,CAAEhC,cAAe,CAAC;IAEhE,KAAM,IAAIiC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGrP,WAAW,CAAChY,MAAM,EAAEonB,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;MAExD,MAAME,KAAK,GAAG,IAAIN,kBAAkB,CACnChP,WAAW,CAAEoP,CAAC,CAAE,GAAG,GAAG,GAAGhS,eAAe,CAAEjR,MAAM,CAACtH,IAAI,CAAE,EACvDqoB,aAAa,CAAC/e,KAAK,EACnB+gB,WAAW,EACXD,aACD,CAAC;;MAED;MACA,IAAKA,aAAa,KAAK,aAAa,EAAG;QAEtC,IAAI,CAACM,kCAAkC,CAAED,KAAM,CAAC;MAEjD;MAEArC,MAAM,CAAC1mB,IAAI,CAAE+oB,KAAM,CAAC;IAErB;IAEA,OAAOrC,MAAM;EAEd;EAEAkC,qBAAqBA,CAAEra,QAAQ,EAAG;IAEjC,IAAIoa,WAAW,GAAGpa,QAAQ,CAAC3G,KAAK;IAEhC,IAAK2G,QAAQ,CAACwD,UAAU,EAAG;MAE1B,MAAMlJ,KAAK,GAAGuR,2BAA2B,CAAEuO,WAAW,CAACnsB,WAAY,CAAC;MACpE,MAAMysB,MAAM,GAAG,IAAIlT,YAAY,CAAE4S,WAAW,CAAClnB,MAAO,CAAC;MAErD,KAAM,IAAIonB,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGH,WAAW,CAAClnB,MAAM,EAAEonB,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;QAExDI,MAAM,CAAEJ,CAAC,CAAE,GAAGF,WAAW,CAAEE,CAAC,CAAE,GAAGhgB,KAAK;MAEvC;MAEA8f,WAAW,GAAGM,MAAM;IAErB;IAEA,OAAON,WAAW;EAEnB;EAEAK,kCAAkCA,CAAED,KAAK,EAAG;IAE3CA,KAAK,CAACG,iBAAiB,GAAG,SAASC,uCAAuCA,CAAE1b,MAAM,EAAG;MAEpF;MACA;MACA;;MAEA,MAAM2b,eAAe,GAAK,IAAI,YAAY5tB,uBAAuB,GAAKiZ,oCAAoC,GAAG7B,0BAA0B;MAEvI,OAAO,IAAIwW,eAAe,CAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACnW,MAAM,EAAE,IAAI,CAACoW,YAAY,CAAC,CAAC,GAAG,CAAC,EAAE7b,MAAO,CAAC;IAEvF,CAAC;;IAED;IACAsb,KAAK,CAACG,iBAAiB,CAACK,yCAAyC,GAAG,IAAI;EAEzE;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAEra,QAAQ,EAAEyK,YAAY,EAAE7c,MAAM,EAAG;EAExD,MAAMuR,UAAU,GAAGsL,YAAY,CAACtL,UAAU;EAE1C,MAAMmb,GAAG,GAAG,IAAI/wB,IAAI,CAAC,CAAC;EAEtB,IAAK4V,UAAU,CAAC6H,QAAQ,KAAKhV,SAAS,EAAG;IAExC,MAAMoN,QAAQ,GAAGxR,MAAM,CAACoD,IAAI,CAACyR,SAAS,CAAEtD,UAAU,CAAC6H,QAAQ,CAAE;IAE7D,MAAMuT,GAAG,GAAGnb,QAAQ,CAACmb,GAAG;IACxB,MAAMC,GAAG,GAAGpb,QAAQ,CAACob,GAAG;;IAExB;;IAEA,IAAKD,GAAG,KAAKvoB,SAAS,IAAIwoB,GAAG,KAAKxoB,SAAS,EAAG;MAE7CsoB,GAAG,CAAC3jB,GAAG,CACN,IAAI3J,OAAO,CAAEutB,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAG,CAAC,EAC3C,IAAIvtB,OAAO,CAAEwtB,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAG,CAC3C,CAAC;MAED,IAAKpb,QAAQ,CAACwD,UAAU,EAAG;QAE1B,MAAM6X,QAAQ,GAAGxP,2BAA2B,CAAEtI,qBAAqB,CAAEvD,QAAQ,CAACsD,aAAa,CAAG,CAAC;QAC/F4X,GAAG,CAACC,GAAG,CAACG,cAAc,CAAED,QAAS,CAAC;QAClCH,GAAG,CAACE,GAAG,CAACE,cAAc,CAAED,QAAS,CAAC;MAEnC;IAED,CAAC,MAAM;MAENjrB,OAAO,CAAC4D,IAAI,CAAE,qEAAsE,CAAC;MAErF;IAED;EAED,CAAC,MAAM;IAEN;EAED;EAEA,MAAMiW,OAAO,GAAGoB,YAAY,CAACpB,OAAO;EAEpC,IAAKA,OAAO,KAAKrX,SAAS,EAAG;IAE5B,MAAM2oB,eAAe,GAAG,IAAI3tB,OAAO,CAAC,CAAC;IACrC,MAAM4tB,MAAM,GAAG,IAAI5tB,OAAO,CAAC,CAAC;IAE5B,KAAM,IAAIqF,CAAC,GAAG,CAAC,EAAEoX,EAAE,GAAGJ,OAAO,CAAC/W,MAAM,EAAED,CAAC,GAAGoX,EAAE,EAAEpX,CAAC,EAAG,EAAG;MAEpD,MAAMoE,MAAM,GAAG4S,OAAO,CAAEhX,CAAC,CAAE;MAE3B,IAAKoE,MAAM,CAACuQ,QAAQ,KAAKhV,SAAS,EAAG;QAEpC,MAAMoN,QAAQ,GAAGxR,MAAM,CAACoD,IAAI,CAACyR,SAAS,CAAEhM,MAAM,CAACuQ,QAAQ,CAAE;QACzD,MAAMuT,GAAG,GAAGnb,QAAQ,CAACmb,GAAG;QACxB,MAAMC,GAAG,GAAGpb,QAAQ,CAACob,GAAG;;QAExB;;QAEA,IAAKD,GAAG,KAAKvoB,SAAS,IAAIwoB,GAAG,KAAKxoB,SAAS,EAAG;UAE7C;UACA4oB,MAAM,CAACjK,IAAI,CAAE3Z,IAAI,CAACwjB,GAAG,CAAExjB,IAAI,CAAC6jB,GAAG,CAAEN,GAAG,CAAE,CAAC,CAAG,CAAC,EAAEvjB,IAAI,CAAC6jB,GAAG,CAAEL,GAAG,CAAE,CAAC,CAAG,CAAE,CAAE,CAAC;UACrEI,MAAM,CAAChK,IAAI,CAAE5Z,IAAI,CAACwjB,GAAG,CAAExjB,IAAI,CAAC6jB,GAAG,CAAEN,GAAG,CAAE,CAAC,CAAG,CAAC,EAAEvjB,IAAI,CAAC6jB,GAAG,CAAEL,GAAG,CAAE,CAAC,CAAG,CAAE,CAAE,CAAC;UACrEI,MAAM,CAAC/J,IAAI,CAAE7Z,IAAI,CAACwjB,GAAG,CAAExjB,IAAI,CAAC6jB,GAAG,CAAEN,GAAG,CAAE,CAAC,CAAG,CAAC,EAAEvjB,IAAI,CAAC6jB,GAAG,CAAEL,GAAG,CAAE,CAAC,CAAG,CAAE,CAAE,CAAC;UAGrE,IAAKpb,QAAQ,CAACwD,UAAU,EAAG;YAE1B,MAAM6X,QAAQ,GAAGxP,2BAA2B,CAAEtI,qBAAqB,CAAEvD,QAAQ,CAACsD,aAAa,CAAG,CAAC;YAC/FkY,MAAM,CAACF,cAAc,CAAED,QAAS,CAAC;UAElC;;UAEA;UACA;UACA;UACA;UACAE,eAAe,CAACH,GAAG,CAAEI,MAAO,CAAC;QAE9B,CAAC,MAAM;UAENprB,OAAO,CAAC4D,IAAI,CAAE,qEAAsE,CAAC;QAEtF;MAED;IAED;;IAEA;IACAknB,GAAG,CAACQ,cAAc,CAAEH,eAAgB,CAAC;EAEtC;EAEA3a,QAAQ,CAAC+a,WAAW,GAAGT,GAAG;EAE1B,MAAMU,MAAM,GAAG,IAAIvuB,MAAM,CAAC,CAAC;EAE3B6tB,GAAG,CAACW,SAAS,CAAED,MAAM,CAACE,MAAO,CAAC;EAC9BF,MAAM,CAACG,MAAM,GAAGb,GAAG,CAACC,GAAG,CAACa,UAAU,CAAEd,GAAG,CAACE,GAAI,CAAC,GAAG,CAAC;EAEjDxa,QAAQ,CAACqb,cAAc,GAAGL,MAAM;AAEjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjG,sBAAsBA,CAAE/U,QAAQ,EAAEyK,YAAY,EAAE7c,MAAM,EAAG;EAEjE,MAAMuR,UAAU,GAAGsL,YAAY,CAACtL,UAAU;EAE1C,MAAMjH,OAAO,GAAG,EAAE;EAElB,SAASojB,uBAAuBA,CAAE/L,aAAa,EAAE/O,aAAa,EAAG;IAEhE,OAAO5S,MAAM,CAAC4J,aAAa,CAAE,UAAU,EAAE+X,aAAc,CAAC,CACtD3X,IAAI,CAAE,UAAWwH,QAAQ,EAAG;MAE5BY,QAAQ,CAACS,YAAY,CAAED,aAAa,EAAEpB,QAAS,CAAC;IAEjD,CAAE,CAAC;EAEL;EAEA,KAAM,MAAMmc,iBAAiB,IAAIpc,UAAU,EAAG;IAE7C,MAAMkD,kBAAkB,GAAGC,UAAU,CAAEiZ,iBAAiB,CAAE,IAAIA,iBAAiB,CAAChZ,WAAW,CAAC,CAAC;;IAE7F;IACA,IAAKF,kBAAkB,IAAIrC,QAAQ,CAACb,UAAU,EAAG;IAEjDjH,OAAO,CAACrH,IAAI,CAAEyqB,uBAAuB,CAAEnc,UAAU,CAAEoc,iBAAiB,CAAE,EAAElZ,kBAAmB,CAAE,CAAC;EAE/F;EAEA,IAAKoI,YAAY,CAACG,OAAO,KAAK5Y,SAAS,IAAI,CAAEgO,QAAQ,CAACvI,KAAK,EAAG;IAE7D,MAAM2H,QAAQ,GAAGxR,MAAM,CAAC4J,aAAa,CAAE,UAAU,EAAEiT,YAAY,CAACG,OAAQ,CAAC,CAAChT,IAAI,CAAE,UAAWwH,QAAQ,EAAG;MAErGY,QAAQ,CAACwb,QAAQ,CAAEpc,QAAS,CAAC;IAE9B,CAAE,CAAC;IAEHlH,OAAO,CAACrH,IAAI,CAAEuO,QAAS,CAAC;EAEzB;EAEA/H,sBAAsB,CAAE2I,QAAQ,EAAEyK,YAAa,CAAC;EAEhD4P,aAAa,CAAEra,QAAQ,EAAEyK,YAAY,EAAE7c,MAAO,CAAC;EAE/C,OAAO4F,OAAO,CAACoF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,YAAY;IAE/C,OAAO6S,YAAY,CAACpB,OAAO,KAAKrX,SAAS,GACtCoX,eAAe,CAAEpJ,QAAQ,EAAEyK,YAAY,CAACpB,OAAO,EAAEzb,MAAO,CAAC,GACzDoS,QAAQ;EAEZ,CAAE,CAAC;AAEJ;AAEA,SAAS5S,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}