{"ast": null, "code": "import { LineSegments, BufferGeometry, Float32BufferAttribute, LineBasicMaterial, Vector3 } from \"three\";\nconst _v1 = /* @__PURE__ */new Vector3();\nconst _v2 = /* @__PURE__ */new Vector3();\nclass VertexTangentsHelper extends LineSegments {\n  constructor(object, size = 1, color = 65535) {\n    const geometry = new BufferGeometry();\n    const nTangents = object.geometry.attributes.tangent.count;\n    const positions = new Float32BufferAttribute(nTangents * 2 * 3, 3);\n    geometry.setAttribute(\"position\", positions);\n    super(geometry, new LineBasicMaterial({\n      color,\n      toneMapped: false\n    }));\n    this.object = object;\n    this.size = size;\n    this.type = \"VertexTangentsHelper\";\n    this.matrixAutoUpdate = false;\n    this.update();\n  }\n  update() {\n    this.object.updateMatrixWorld(true);\n    const matrixWorld = this.object.matrixWorld;\n    const position = this.geometry.attributes.position;\n    const objGeometry = this.object.geometry;\n    const objPos = objGeometry.attributes.position;\n    const objTan = objGeometry.attributes.tangent;\n    let idx = 0;\n    for (let j = 0, jl = objPos.count; j < jl; j++) {\n      _v1.fromBufferAttribute(objPos, j).applyMatrix4(matrixWorld);\n      _v2.fromBufferAttribute(objTan, j);\n      _v2.transformDirection(matrixWorld).multiplyScalar(this.size).add(_v1);\n      position.setXYZ(idx, _v1.x, _v1.y, _v1.z);\n      idx = idx + 1;\n      position.setXYZ(idx, _v2.x, _v2.y, _v2.z);\n      idx = idx + 1;\n    }\n    position.needsUpdate = true;\n  }\n  dispose() {\n    this.geometry.dispose();\n    this.material.dispose();\n  }\n}\nexport { VertexTangentsHelper };", "map": {"version": 3, "names": ["_v1", "Vector3", "_v2", "VertexTangentsHelper", "LineSegments", "constructor", "object", "size", "color", "geometry", "BufferGeometry", "nTangents", "attributes", "tangent", "count", "positions", "Float32BufferAttribute", "setAttribute", "LineBasicMaterial", "toneMapped", "type", "matrixAutoUpdate", "update", "updateMatrixWorld", "matrixWorld", "position", "objGeometry", "objPos", "objTan", "idx", "j", "jl", "fromBufferAttribute", "applyMatrix4", "transformDirection", "multiplyScalar", "add", "setXYZ", "x", "y", "z", "needsUpdate", "dispose", "material"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\helpers\\VertexTangentsHelper.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute, LineSegments, LineBasicMaterial, Vector3 } from 'three'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\n\nclass VertexTangentsHelper extends LineSegments {\n  constructor(object, size = 1, color = 0x00ffff) {\n    const geometry = new BufferGeometry()\n\n    const nTangents = object.geometry.attributes.tangent.count\n    const positions = new Float32BufferAttribute(nTangents * 2 * 3, 3)\n\n    geometry.setAttribute('position', positions)\n\n    super(geometry, new LineBasicMaterial({ color, toneMapped: false }))\n\n    this.object = object\n    this.size = size\n    this.type = 'VertexTangentsHelper'\n\n    //\n\n    this.matrixAutoUpdate = false\n\n    this.update()\n  }\n\n  update() {\n    this.object.updateMatrixWorld(true)\n\n    const matrixWorld = this.object.matrixWorld\n\n    const position = this.geometry.attributes.position\n\n    //\n\n    const objGeometry = this.object.geometry\n\n    const objPos = objGeometry.attributes.position\n\n    const objTan = objGeometry.attributes.tangent\n\n    let idx = 0\n\n    // for simplicity, ignore index and drawcalls, and render every tangent\n\n    for (let j = 0, jl = objPos.count; j < jl; j++) {\n      _v1.fromBufferAttribute(objPos, j).applyMatrix4(matrixWorld)\n\n      _v2.fromBufferAttribute(objTan, j)\n\n      _v2.transformDirection(matrixWorld).multiplyScalar(this.size).add(_v1)\n\n      position.setXYZ(idx, _v1.x, _v1.y, _v1.z)\n\n      idx = idx + 1\n\n      position.setXYZ(idx, _v2.x, _v2.y, _v2.z)\n\n      idx = idx + 1\n    }\n\n    position.needsUpdate = true\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n  }\n}\n\nexport { VertexTangentsHelper }\n"], "mappings": ";AAEA,MAAMA,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AACzC,MAAMC,GAAA,GAAsB,mBAAID,OAAA,CAAS;AAEzC,MAAME,oBAAA,SAA6BC,YAAA,CAAa;EAC9CC,YAAYC,MAAA,EAAQC,IAAA,GAAO,GAAGC,KAAA,GAAQ,OAAU;IAC9C,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAgB;IAErC,MAAMC,SAAA,GAAYL,MAAA,CAAOG,QAAA,CAASG,UAAA,CAAWC,OAAA,CAAQC,KAAA;IACrD,MAAMC,SAAA,GAAY,IAAIC,sBAAA,CAAuBL,SAAA,GAAY,IAAI,GAAG,CAAC;IAEjEF,QAAA,CAASQ,YAAA,CAAa,YAAYF,SAAS;IAE3C,MAAMN,QAAA,EAAU,IAAIS,iBAAA,CAAkB;MAAEV,KAAA;MAAOW,UAAA,EAAY;IAAK,CAAE,CAAC;IAEnE,KAAKb,MAAA,GAASA,MAAA;IACd,KAAKC,IAAA,GAAOA,IAAA;IACZ,KAAKa,IAAA,GAAO;IAIZ,KAAKC,gBAAA,GAAmB;IAExB,KAAKC,MAAA,CAAQ;EACd;EAEDA,OAAA,EAAS;IACP,KAAKhB,MAAA,CAAOiB,iBAAA,CAAkB,IAAI;IAElC,MAAMC,WAAA,GAAc,KAAKlB,MAAA,CAAOkB,WAAA;IAEhC,MAAMC,QAAA,GAAW,KAAKhB,QAAA,CAASG,UAAA,CAAWa,QAAA;IAI1C,MAAMC,WAAA,GAAc,KAAKpB,MAAA,CAAOG,QAAA;IAEhC,MAAMkB,MAAA,GAASD,WAAA,CAAYd,UAAA,CAAWa,QAAA;IAEtC,MAAMG,MAAA,GAASF,WAAA,CAAYd,UAAA,CAAWC,OAAA;IAEtC,IAAIgB,GAAA,GAAM;IAIV,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKJ,MAAA,CAAOb,KAAA,EAAOgB,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC9C9B,GAAA,CAAIgC,mBAAA,CAAoBL,MAAA,EAAQG,CAAC,EAAEG,YAAA,CAAaT,WAAW;MAE3DtB,GAAA,CAAI8B,mBAAA,CAAoBJ,MAAA,EAAQE,CAAC;MAEjC5B,GAAA,CAAIgC,kBAAA,CAAmBV,WAAW,EAAEW,cAAA,CAAe,KAAK5B,IAAI,EAAE6B,GAAA,CAAIpC,GAAG;MAErEyB,QAAA,CAASY,MAAA,CAAOR,GAAA,EAAK7B,GAAA,CAAIsC,CAAA,EAAGtC,GAAA,CAAIuC,CAAA,EAAGvC,GAAA,CAAIwC,CAAC;MAExCX,GAAA,GAAMA,GAAA,GAAM;MAEZJ,QAAA,CAASY,MAAA,CAAOR,GAAA,EAAK3B,GAAA,CAAIoC,CAAA,EAAGpC,GAAA,CAAIqC,CAAA,EAAGrC,GAAA,CAAIsC,CAAC;MAExCX,GAAA,GAAMA,GAAA,GAAM;IACb;IAEDJ,QAAA,CAASgB,WAAA,GAAc;EACxB;EAEDC,QAAA,EAAU;IACR,KAAKjC,QAAA,CAASiC,OAAA,CAAS;IACvB,KAAKC,QAAA,CAASD,OAAA,CAAS;EACxB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}