{"ast": null, "code": "import { Vector3, Vector2, Triangle, DoubleSide, BackSide } from 'three';\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst vA = /* @__PURE__ */new Vector3();\nconst vB = /* @__PURE__ */new Vector3();\nconst vC = /* @__PURE__ */new Vector3();\nconst uvA = /* @__PURE__ */new Vector2();\nconst uvB = /* @__PURE__ */new Vector2();\nconst uvC = /* @__PURE__ */new Vector2();\nconst intersectionPoint = /* @__PURE__ */new Vector3();\nfunction checkIntersection(ray, pA, pB, pC, point, side) {\n  let intersect;\n  if (side === BackSide) {\n    intersect = ray.intersectTriangle(pC, pB, pA, true, point);\n  } else {\n    intersect = ray.intersectTriangle(pA, pB, pC, side !== DoubleSide, point);\n  }\n  if (intersect === null) return null;\n  const distance = ray.origin.distanceTo(point);\n  return {\n    distance: distance,\n    point: point.clone()\n  };\n}\nfunction checkBufferGeometryIntersection(ray, position, uv, a, b, c, side) {\n  vA.fromBufferAttribute(position, a);\n  vB.fromBufferAttribute(position, b);\n  vC.fromBufferAttribute(position, c);\n  const intersection = checkIntersection(ray, vA, vB, vC, intersectionPoint, side);\n  if (intersection) {\n    if (uv) {\n      uvA.fromBufferAttribute(uv, a);\n      uvB.fromBufferAttribute(uv, b);\n      uvC.fromBufferAttribute(uv, c);\n      intersection.uv = Triangle.getUV(intersectionPoint, vA, vB, vC, uvA, uvB, uvC, new Vector2());\n    }\n    const face = {\n      a: a,\n      b: b,\n      c: c,\n      normal: new Vector3(),\n      materialIndex: 0\n    };\n    Triangle.getNormal(vA, vB, vC, face.normal);\n    intersection.face = face;\n    intersection.faceIndex = a;\n  }\n  return intersection;\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri(geo, side, ray, tri, intersections) {\n  const triOffset = tri * 3;\n  const a = geo.index.getX(triOffset);\n  const b = geo.index.getX(triOffset + 1);\n  const c = geo.index.getX(triOffset + 2);\n  const intersection = checkBufferGeometryIntersection(ray, geo.attributes.position, geo.attributes.uv, a, b, c, side);\n  if (intersection) {\n    intersection.faceIndex = tri;\n    if (intersections) intersections.push(intersection);\n    return intersection;\n  }\n  return null;\n}\nexport { intersectTri };", "map": {"version": 3, "names": ["Vector3", "Vector2", "Triangle", "DoubleSide", "BackSide", "vA", "vB", "vC", "uvA", "uvB", "uvC", "intersectionPoint", "checkIntersection", "ray", "pA", "pB", "pC", "point", "side", "intersect", "intersectTriangle", "distance", "origin", "distanceTo", "clone", "checkBufferGeometryIntersection", "position", "uv", "a", "b", "c", "fromBufferAttribute", "intersection", "getUV", "face", "normal", "materialIndex", "getNormal", "faceIndex", "intersectTri", "geo", "tri", "intersections", "triOffset", "index", "getX", "attributes", "push"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/utils/ThreeRayIntersectUtilities.js"], "sourcesContent": ["import { Vector3, Vector2, Triangle, DoubleSide, BackSide } from 'three';\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst vA = /* @__PURE__ */ new Vector3();\nconst vB = /* @__PURE__ */ new Vector3();\nconst vC = /* @__PURE__ */ new Vector3();\n\nconst uvA = /* @__PURE__ */ new Vector2();\nconst uvB = /* @__PURE__ */ new Vector2();\nconst uvC = /* @__PURE__ */ new Vector2();\n\nconst intersectionPoint = /* @__PURE__ */ new Vector3();\nfunction checkIntersection( ray, pA, pB, pC, point, side ) {\n\n\tlet intersect;\n\tif ( side === BackSide ) {\n\n\t\tintersect = ray.intersectTriangle( pC, pB, pA, true, point );\n\n\t} else {\n\n\t\tintersect = ray.intersectTriangle( pA, pB, pC, side !== DoubleSide, point );\n\n\t}\n\n\tif ( intersect === null ) return null;\n\n\tconst distance = ray.origin.distanceTo( point );\n\n\treturn {\n\n\t\tdistance: distance,\n\t\tpoint: point.clone(),\n\n\t};\n\n}\n\nfunction checkBufferGeometryIntersection( ray, position, uv, a, b, c, side ) {\n\n\tvA.fromBufferAttribute( position, a );\n\tvB.fromBufferAttribute( position, b );\n\tvC.fromBufferAttribute( position, c );\n\n\tconst intersection = checkIntersection( ray, vA, vB, vC, intersectionPoint, side );\n\n\tif ( intersection ) {\n\n\t\tif ( uv ) {\n\n\t\t\tuvA.fromBufferAttribute( uv, a );\n\t\t\tuvB.fromBufferAttribute( uv, b );\n\t\t\tuvC.fromBufferAttribute( uv, c );\n\n\t\t\tintersection.uv = Triangle.getUV( intersectionPoint, vA, vB, vC, uvA, uvB, uvC, new Vector2( ) );\n\n\t\t}\n\n\t\tconst face = {\n\t\t\ta: a,\n\t\t\tb: b,\n\t\t\tc: c,\n\t\t\tnormal: new Vector3(),\n\t\t\tmaterialIndex: 0\n\t\t};\n\n\t\tTriangle.getNormal( vA, vB, vC, face.normal );\n\n\t\tintersection.face = face;\n\t\tintersection.faceIndex = a;\n\n\t}\n\n\treturn intersection;\n\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri( geo, side, ray, tri, intersections ) {\n\n\tconst triOffset = tri * 3;\n\tconst a = geo.index.getX( triOffset );\n\tconst b = geo.index.getX( triOffset + 1 );\n\tconst c = geo.index.getX( triOffset + 2 );\n\n\tconst intersection = checkBufferGeometryIntersection( ray, geo.attributes.position, geo.attributes.uv, a, b, c, side );\n\n\tif ( intersection ) {\n\n\t\tintersection.faceIndex = tri;\n\t\tif ( intersections ) intersections.push( intersection );\n\t\treturn intersection;\n\n\t}\n\n\treturn null;\n\n}\n\nexport { intersectTri };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;;AAExE;AACA;AACA,MAAMC,EAAE,GAAG,eAAgB,IAAIL,OAAO,CAAC,CAAC;AACxC,MAAMM,EAAE,GAAG,eAAgB,IAAIN,OAAO,CAAC,CAAC;AACxC,MAAMO,EAAE,GAAG,eAAgB,IAAIP,OAAO,CAAC,CAAC;AAExC,MAAMQ,GAAG,GAAG,eAAgB,IAAIP,OAAO,CAAC,CAAC;AACzC,MAAMQ,GAAG,GAAG,eAAgB,IAAIR,OAAO,CAAC,CAAC;AACzC,MAAMS,GAAG,GAAG,eAAgB,IAAIT,OAAO,CAAC,CAAC;AAEzC,MAAMU,iBAAiB,GAAG,eAAgB,IAAIX,OAAO,CAAC,CAAC;AACvD,SAASY,iBAAiBA,CAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,IAAI,EAAG;EAE1D,IAAIC,SAAS;EACb,IAAKD,IAAI,KAAKd,QAAQ,EAAG;IAExBe,SAAS,GAAGN,GAAG,CAACO,iBAAiB,CAAEJ,EAAE,EAAED,EAAE,EAAED,EAAE,EAAE,IAAI,EAAEG,KAAM,CAAC;EAE7D,CAAC,MAAM;IAENE,SAAS,GAAGN,GAAG,CAACO,iBAAiB,CAAEN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,IAAI,KAAKf,UAAU,EAAEc,KAAM,CAAC;EAE5E;EAEA,IAAKE,SAAS,KAAK,IAAI,EAAG,OAAO,IAAI;EAErC,MAAME,QAAQ,GAAGR,GAAG,CAACS,MAAM,CAACC,UAAU,CAAEN,KAAM,CAAC;EAE/C,OAAO;IAENI,QAAQ,EAAEA,QAAQ;IAClBJ,KAAK,EAAEA,KAAK,CAACO,KAAK,CAAC;EAEpB,CAAC;AAEF;AAEA,SAASC,+BAA+BA,CAAEZ,GAAG,EAAEa,QAAQ,EAAEC,EAAE,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEZ,IAAI,EAAG;EAE5Eb,EAAE,CAAC0B,mBAAmB,CAAEL,QAAQ,EAAEE,CAAE,CAAC;EACrCtB,EAAE,CAACyB,mBAAmB,CAAEL,QAAQ,EAAEG,CAAE,CAAC;EACrCtB,EAAE,CAACwB,mBAAmB,CAAEL,QAAQ,EAAEI,CAAE,CAAC;EAErC,MAAME,YAAY,GAAGpB,iBAAiB,CAAEC,GAAG,EAAER,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEI,iBAAiB,EAAEO,IAAK,CAAC;EAElF,IAAKc,YAAY,EAAG;IAEnB,IAAKL,EAAE,EAAG;MAETnB,GAAG,CAACuB,mBAAmB,CAAEJ,EAAE,EAAEC,CAAE,CAAC;MAChCnB,GAAG,CAACsB,mBAAmB,CAAEJ,EAAE,EAAEE,CAAE,CAAC;MAChCnB,GAAG,CAACqB,mBAAmB,CAAEJ,EAAE,EAAEG,CAAE,CAAC;MAEhCE,YAAY,CAACL,EAAE,GAAGzB,QAAQ,CAAC+B,KAAK,CAAEtB,iBAAiB,EAAEN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE,IAAIT,OAAO,CAAE,CAAE,CAAC;IAEjG;IAEA,MAAMiC,IAAI,GAAG;MACZN,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJK,MAAM,EAAE,IAAInC,OAAO,CAAC,CAAC;MACrBoC,aAAa,EAAE;IAChB,CAAC;IAEDlC,QAAQ,CAACmC,SAAS,CAAEhC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE2B,IAAI,CAACC,MAAO,CAAC;IAE7CH,YAAY,CAACE,IAAI,GAAGA,IAAI;IACxBF,YAAY,CAACM,SAAS,GAAGV,CAAC;EAE3B;EAEA,OAAOI,YAAY;AAEpB;;AAEA;AACA,SAASO,YAAYA,CAAEC,GAAG,EAAEtB,IAAI,EAAEL,GAAG,EAAE4B,GAAG,EAAEC,aAAa,EAAG;EAE3D,MAAMC,SAAS,GAAGF,GAAG,GAAG,CAAC;EACzB,MAAMb,CAAC,GAAGY,GAAG,CAACI,KAAK,CAACC,IAAI,CAAEF,SAAU,CAAC;EACrC,MAAMd,CAAC,GAAGW,GAAG,CAACI,KAAK,CAACC,IAAI,CAAEF,SAAS,GAAG,CAAE,CAAC;EACzC,MAAMb,CAAC,GAAGU,GAAG,CAACI,KAAK,CAACC,IAAI,CAAEF,SAAS,GAAG,CAAE,CAAC;EAEzC,MAAMX,YAAY,GAAGP,+BAA+B,CAAEZ,GAAG,EAAE2B,GAAG,CAACM,UAAU,CAACpB,QAAQ,EAAEc,GAAG,CAACM,UAAU,CAACnB,EAAE,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEZ,IAAK,CAAC;EAEtH,IAAKc,YAAY,EAAG;IAEnBA,YAAY,CAACM,SAAS,GAAGG,GAAG;IAC5B,IAAKC,aAAa,EAAGA,aAAa,CAACK,IAAI,CAAEf,YAAa,CAAC;IACvD,OAAOA,YAAY;EAEpB;EAEA,OAAO,IAAI;AAEZ;AAEA,SAASO,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}