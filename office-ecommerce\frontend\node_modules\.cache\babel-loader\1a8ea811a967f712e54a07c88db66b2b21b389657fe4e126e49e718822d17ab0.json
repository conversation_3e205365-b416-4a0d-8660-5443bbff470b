{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n/*  Integration and compilation: @N8Programs\n    Inspired by:\n     https://github.com/mrdoob/three.js/blob/dev/examples/webgl_shadowmap_pcss.html\n     https://developer.nvidia.com/gpugems/gpugems2/part-ii-shading-lighting-and-shadows/chapter-17-efficient-soft-edged-shadows-using\n     https://developer.download.nvidia.com/whitepapers/2008/PCSS_Integration.pdf\n     https://github.com/mrdoob/three.js/blob/master/examples/webgl_shadowmap_pcss.html [spidersharma03]\n     https://spline.design/\n   Concept:\n     https://www.gamedev.net/tutorials/programming/graphics/contact-hardening-soft-shadows-made-fast-r4906/\n   Vogel Disk Implementation:\n     https://www.shadertoy.com/view/4l3yRM [ashalah]\n   High-Frequency Noise Implementation:\n     https://www.shadertoy.com/view/tt3fDH [spawner64]\n*/\n\nconst pcss = ({\n  focus = 0,\n  size = 25,\n  samples = 10\n} = {}) => `\n#define PENUMBRA_FILTER_SIZE float(${size})\n#define RGB_NOISE_FUNCTION(uv) (randRGB(uv))\nvec3 randRGB(vec2 uv) {\n  return vec3(\n    fract(sin(dot(uv, vec2(12.75613, 38.12123))) * 13234.76575),\n    fract(sin(dot(uv, vec2(19.45531, 58.46547))) * 43678.23431),\n    fract(sin(dot(uv, vec2(23.67817, 78.23121))) * 93567.23423)\n  );\n}\n\nvec3 lowPassRandRGB(vec2 uv) {\n  // 3x3 convolution (average)\n  // can be implemented as separable with an extra buffer for a total of 6 samples instead of 9\n  vec3 result = vec3(0);\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, +1.0));\n  result *= 0.111111111; // 1.0 / 9.0\n  return result;\n}\nvec3 highPassRandRGB(vec2 uv) {\n  // by subtracting the low-pass signal from the original signal, we're being left with the high-pass signal\n  // hp(x) = x - lp(x)\n  return RGB_NOISE_FUNCTION(uv) - lowPassRandRGB(uv) + 0.5;\n}\n\n\nvec2 vogelDiskSample(int sampleIndex, int sampleCount, float angle) {\n  const float goldenAngle = 2.399963f; // radians\n  float r = sqrt(float(sampleIndex) + 0.5f) / sqrt(float(sampleCount));\n  float theta = float(sampleIndex) * goldenAngle + angle;\n  float sine = sin(theta);\n  float cosine = cos(theta);\n  return vec2(cosine, sine) * r;\n}\nfloat penumbraSize( const in float zReceiver, const in float zBlocker ) { // Parallel plane estimation\n  return (zReceiver - zBlocker) / zBlocker;\n}\nfloat findBlocker(sampler2D shadowMap, vec2 uv, float compare, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float blockerDepthSum = float(${focus});\n  float blockers = 0.0;\n\n  int j = 0;\n  vec2 offset = vec2(0.);\n  float depth = 0.;\n\n  #pragma unroll_loop_start\n  for(int i = 0; i < ${samples}; i ++) {\n    offset = (vogelDiskSample(j, ${samples}, angle) * texelSize) * 2.0 * PENUMBRA_FILTER_SIZE;\n    depth = unpackRGBAToDepth( texture2D( shadowMap, uv + offset));\n    if (depth < compare) {\n      blockerDepthSum += depth;\n      blockers++;\n    }\n    j++;\n  }\n  #pragma unroll_loop_end\n\n  if (blockers > 0.0) {\n    return blockerDepthSum / blockers;\n  }\n  return -1.0;\n}\n\n        \nfloat vogelFilter(sampler2D shadowMap, vec2 uv, float zReceiver, float filterRadius, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float shadow = 0.0f;\n  int j = 0;\n  vec2 vogelSample = vec2(0.0);\n  vec2 offset = vec2(0.0);\n  #pragma unroll_loop_start\n  for (int i = 0; i < ${samples}; i++) {\n    vogelSample = vogelDiskSample(j, ${samples}, angle) * texelSize;\n    offset = vogelSample * (1.0 + filterRadius * float(${size}));\n    shadow += step( zReceiver, unpackRGBAToDepth( texture2D( shadowMap, uv + offset ) ) );\n    j++;\n  }\n  #pragma unroll_loop_end\n  return shadow * 1.0 / ${samples}.0;\n}\n\nfloat PCSS (sampler2D shadowMap, vec4 coords) {\n  vec2 uv = coords.xy;\n  float zReceiver = coords.z; // Assumed to be eye-space z in this code\n  float angle = highPassRandRGB(gl_FragCoord.xy).r * PI2;\n  float avgBlockerDepth = findBlocker(shadowMap, uv, zReceiver, angle);\n  if (avgBlockerDepth == -1.0) {\n    return 1.0;\n  }\n  float penumbraRatio = penumbraSize(zReceiver, avgBlockerDepth);\n  return vogelFilter(shadowMap, uv, zReceiver, 1.25 * penumbraRatio, angle);\n}`;\nfunction reset(gl, scene, camera) {\n  scene.traverse(object => {\n    if (object.material) {\n      gl.properties.remove(object.material);\n      object.material.dispose == null ? void 0 : object.material.dispose();\n    }\n  });\n  gl.info.programs.length = 0;\n  gl.compile(scene, camera);\n}\nfunction SoftShadows({\n  focus = 0,\n  samples = 10,\n  size = 25\n}) {\n  const gl = useThree(state => state.gl);\n  const scene = useThree(state => state.scene);\n  const camera = useThree(state => state.camera);\n  React.useEffect(() => {\n    const original = THREE.ShaderChunk.shadowmap_pars_fragment;\n    THREE.ShaderChunk.shadowmap_pars_fragment = THREE.ShaderChunk.shadowmap_pars_fragment.replace('#ifdef USE_SHADOWMAP', '#ifdef USE_SHADOWMAP\\n' + pcss({\n      size,\n      samples,\n      focus\n    })).replace('#if defined( SHADOWMAP_TYPE_PCF )', '\\nreturn PCSS(shadowMap, shadowCoord);\\n#if defined( SHADOWMAP_TYPE_PCF )');\n    reset(gl, scene, camera);\n    return () => {\n      THREE.ShaderChunk.shadowmap_pars_fragment = original;\n      reset(gl, scene, camera);\n    };\n  }, [focus, size, samples]);\n  return null;\n}\nexport { SoftShadows };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "pcss", "focus", "size", "samples", "reset", "gl", "scene", "camera", "traverse", "object", "material", "properties", "remove", "dispose", "info", "programs", "length", "compile", "SoftShadows", "state", "useEffect", "original", "ShaderChunk", "shadowmap_pars_fragment", "replace"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/softShadows.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n/*  Integration and compilation: @N8Programs\n    Inspired by:\n     https://github.com/mrdoob/three.js/blob/dev/examples/webgl_shadowmap_pcss.html\n     https://developer.nvidia.com/gpugems/gpugems2/part-ii-shading-lighting-and-shadows/chapter-17-efficient-soft-edged-shadows-using\n     https://developer.download.nvidia.com/whitepapers/2008/PCSS_Integration.pdf\n     https://github.com/mrdoob/three.js/blob/master/examples/webgl_shadowmap_pcss.html [spidersharma03]\n     https://spline.design/\n   Concept:\n     https://www.gamedev.net/tutorials/programming/graphics/contact-hardening-soft-shadows-made-fast-r4906/\n   Vogel Disk Implementation:\n     https://www.shadertoy.com/view/4l3yRM [ashalah]\n   High-Frequency Noise Implementation:\n     https://www.shadertoy.com/view/tt3fDH [spawner64]\n*/\n\nconst pcss = ({\n  focus = 0,\n  size = 25,\n  samples = 10\n} = {}) => `\n#define PENUMBRA_FILTER_SIZE float(${size})\n#define RGB_NOISE_FUNCTION(uv) (randRGB(uv))\nvec3 randRGB(vec2 uv) {\n  return vec3(\n    fract(sin(dot(uv, vec2(12.75613, 38.12123))) * 13234.76575),\n    fract(sin(dot(uv, vec2(19.45531, 58.46547))) * 43678.23431),\n    fract(sin(dot(uv, vec2(23.67817, 78.23121))) * 93567.23423)\n  );\n}\n\nvec3 lowPassRandRGB(vec2 uv) {\n  // 3x3 convolution (average)\n  // can be implemented as separable with an extra buffer for a total of 6 samples instead of 9\n  vec3 result = vec3(0);\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, +1.0));\n  result *= 0.111111111; // 1.0 / 9.0\n  return result;\n}\nvec3 highPassRandRGB(vec2 uv) {\n  // by subtracting the low-pass signal from the original signal, we're being left with the high-pass signal\n  // hp(x) = x - lp(x)\n  return RGB_NOISE_FUNCTION(uv) - lowPassRandRGB(uv) + 0.5;\n}\n\n\nvec2 vogelDiskSample(int sampleIndex, int sampleCount, float angle) {\n  const float goldenAngle = 2.399963f; // radians\n  float r = sqrt(float(sampleIndex) + 0.5f) / sqrt(float(sampleCount));\n  float theta = float(sampleIndex) * goldenAngle + angle;\n  float sine = sin(theta);\n  float cosine = cos(theta);\n  return vec2(cosine, sine) * r;\n}\nfloat penumbraSize( const in float zReceiver, const in float zBlocker ) { // Parallel plane estimation\n  return (zReceiver - zBlocker) / zBlocker;\n}\nfloat findBlocker(sampler2D shadowMap, vec2 uv, float compare, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float blockerDepthSum = float(${focus});\n  float blockers = 0.0;\n\n  int j = 0;\n  vec2 offset = vec2(0.);\n  float depth = 0.;\n\n  #pragma unroll_loop_start\n  for(int i = 0; i < ${samples}; i ++) {\n    offset = (vogelDiskSample(j, ${samples}, angle) * texelSize) * 2.0 * PENUMBRA_FILTER_SIZE;\n    depth = unpackRGBAToDepth( texture2D( shadowMap, uv + offset));\n    if (depth < compare) {\n      blockerDepthSum += depth;\n      blockers++;\n    }\n    j++;\n  }\n  #pragma unroll_loop_end\n\n  if (blockers > 0.0) {\n    return blockerDepthSum / blockers;\n  }\n  return -1.0;\n}\n\n        \nfloat vogelFilter(sampler2D shadowMap, vec2 uv, float zReceiver, float filterRadius, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float shadow = 0.0f;\n  int j = 0;\n  vec2 vogelSample = vec2(0.0);\n  vec2 offset = vec2(0.0);\n  #pragma unroll_loop_start\n  for (int i = 0; i < ${samples}; i++) {\n    vogelSample = vogelDiskSample(j, ${samples}, angle) * texelSize;\n    offset = vogelSample * (1.0 + filterRadius * float(${size}));\n    shadow += step( zReceiver, unpackRGBAToDepth( texture2D( shadowMap, uv + offset ) ) );\n    j++;\n  }\n  #pragma unroll_loop_end\n  return shadow * 1.0 / ${samples}.0;\n}\n\nfloat PCSS (sampler2D shadowMap, vec4 coords) {\n  vec2 uv = coords.xy;\n  float zReceiver = coords.z; // Assumed to be eye-space z in this code\n  float angle = highPassRandRGB(gl_FragCoord.xy).r * PI2;\n  float avgBlockerDepth = findBlocker(shadowMap, uv, zReceiver, angle);\n  if (avgBlockerDepth == -1.0) {\n    return 1.0;\n  }\n  float penumbraRatio = penumbraSize(zReceiver, avgBlockerDepth);\n  return vogelFilter(shadowMap, uv, zReceiver, 1.25 * penumbraRatio, angle);\n}`;\n\nfunction reset(gl, scene, camera) {\n  scene.traverse(object => {\n    if (object.material) {\n      gl.properties.remove(object.material);\n      object.material.dispose == null ? void 0 : object.material.dispose();\n    }\n  });\n  gl.info.programs.length = 0;\n  gl.compile(scene, camera);\n}\n\nfunction SoftShadows({\n  focus = 0,\n  samples = 10,\n  size = 25\n}) {\n  const gl = useThree(state => state.gl);\n  const scene = useThree(state => state.scene);\n  const camera = useThree(state => state.camera);\n  React.useEffect(() => {\n    const original = THREE.ShaderChunk.shadowmap_pars_fragment;\n    THREE.ShaderChunk.shadowmap_pars_fragment = THREE.ShaderChunk.shadowmap_pars_fragment.replace('#ifdef USE_SHADOWMAP', '#ifdef USE_SHADOWMAP\\n' + pcss({\n      size,\n      samples,\n      focus\n    })).replace('#if defined( SHADOWMAP_TYPE_PCF )', '\\nreturn PCSS(shadowMap, shadowCoord);\\n#if defined( SHADOWMAP_TYPE_PCF )');\n    reset(gl, scene, camera);\n    return () => {\n      THREE.ShaderChunk.shadowmap_pars_fragment = original;\n      reset(gl, scene, camera);\n    };\n  }, [focus, size, samples]);\n  return null;\n}\n\nexport { SoftShadows };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,IAAI,GAAGA,CAAC;EACZC,KAAK,GAAG,CAAC;EACTC,IAAI,GAAG,EAAE;EACTC,OAAO,GAAG;AACZ,CAAC,GAAG,CAAC,CAAC,KAAK;AACX,qCAAqCD,IAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkCD,KAAK;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBE,OAAO;AAC9B,mCAAmCA,OAAO;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBA,OAAO;AAC/B,uCAAuCA,OAAO;AAC9C,yDAAyDD,IAAI;AAC7D;AACA;AACA;AACA;AACA,0BAA0BC,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AAEF,SAASC,KAAKA,CAACC,EAAE,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAChCD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;IACvB,IAAIA,MAAM,CAACC,QAAQ,EAAE;MACnBL,EAAE,CAACM,UAAU,CAACC,MAAM,CAACH,MAAM,CAACC,QAAQ,CAAC;MACrCD,MAAM,CAACC,QAAQ,CAACG,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,MAAM,CAACC,QAAQ,CAACG,OAAO,CAAC,CAAC;IACtE;EACF,CAAC,CAAC;EACFR,EAAE,CAACS,IAAI,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC;EAC3BX,EAAE,CAACY,OAAO,CAACX,KAAK,EAAEC,MAAM,CAAC;AAC3B;AAEA,SAASW,WAAWA,CAAC;EACnBjB,KAAK,GAAG,CAAC;EACTE,OAAO,GAAG,EAAE;EACZD,IAAI,GAAG;AACT,CAAC,EAAE;EACD,MAAMG,EAAE,GAAGN,QAAQ,CAACoB,KAAK,IAAIA,KAAK,CAACd,EAAE,CAAC;EACtC,MAAMC,KAAK,GAAGP,QAAQ,CAACoB,KAAK,IAAIA,KAAK,CAACb,KAAK,CAAC;EAC5C,MAAMC,MAAM,GAAGR,QAAQ,CAACoB,KAAK,IAAIA,KAAK,CAACZ,MAAM,CAAC;EAC9CV,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAGvB,KAAK,CAACwB,WAAW,CAACC,uBAAuB;IAC1DzB,KAAK,CAACwB,WAAW,CAACC,uBAAuB,GAAGzB,KAAK,CAACwB,WAAW,CAACC,uBAAuB,CAACC,OAAO,CAAC,sBAAsB,EAAE,wBAAwB,GAAGxB,IAAI,CAAC;MACpJE,IAAI;MACJC,OAAO;MACPF;IACF,CAAC,CAAC,CAAC,CAACuB,OAAO,CAAC,mCAAmC,EAAE,2EAA2E,CAAC;IAC7HpB,KAAK,CAACC,EAAE,EAAEC,KAAK,EAAEC,MAAM,CAAC;IACxB,OAAO,MAAM;MACXT,KAAK,CAACwB,WAAW,CAACC,uBAAuB,GAAGF,QAAQ;MACpDjB,KAAK,CAACC,EAAE,EAAEC,KAAK,EAAEC,MAAM,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACN,KAAK,EAAEC,IAAI,EAAEC,OAAO,CAAC,CAAC;EAC1B,OAAO,IAAI;AACb;AAEA,SAASe,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}