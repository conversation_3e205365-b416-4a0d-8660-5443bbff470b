{"ast": null, "code": "import { Vector3, Vector2 } from 'three';\n\n/**\n * Helpers for converting buffers to and from Three.js objects\n */\n\n/**\n * Convents passed buffer of passed stride to an array of vectors with the correct length.\n *\n * @param buffer\n * @param stride\n * @returns\n */\nfunction bufferToVectors(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var p = [];\n  for (var i = 0, j = 0; i < buffer.length; i += stride, j++) {\n    if (stride === 3) {\n      p[j] = new Vector3(buffer[i], buffer[i + 1], buffer[i + 2]);\n    } else {\n      p[j] = new Vector2(buffer[i], buffer[i + 1]);\n    }\n  }\n  return p;\n}\n/**\n * Transforms a passed Vector2 or Vector3 array to a points buffer\n *\n * @param vectorArray\n * @returns\n */\n\nfunction vectorsToBuffer(vectorArray) {\n  var l = vectorArray.length;\n  var stride = vectorArray[0].hasOwnProperty(\"z\") ? 3 : 2;\n  var buffer = new Float32Array(l * stride);\n  for (var i = 0; i < l; i++) {\n    var j = i * stride;\n    buffer[j] = vectorArray[i].x;\n    buffer[j + 1] = vectorArray[i].y;\n    if (stride === 3) {\n      buffer[j + 2] = vectorArray[i].z;\n    }\n  }\n  return buffer;\n}\nvar three = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  bufferToVectors: bufferToVectors,\n  vectorsToBuffer: vectorsToBuffer\n});\nexport { bufferToVectors as b, three as t, vectorsToBuffer as v };", "map": {"version": 3, "names": ["Vector3", "Vector2", "bufferToVectors", "buffer", "stride", "arguments", "length", "undefined", "p", "i", "j", "vectors<PERSON><PERSON><PERSON>uffer", "vectorArray", "l", "hasOwnProperty", "Float32Array", "x", "y", "z", "three", "Object", "freeze", "__proto__", "b", "t", "v"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/maath/dist/three-eb2ad8c0.esm.js"], "sourcesContent": ["import { Vector3, Vector2 } from 'three';\n\n/**\n * Helpers for converting buffers to and from Three.js objects\n */\n\n/**\n * Convents passed buffer of passed stride to an array of vectors with the correct length.\n *\n * @param buffer\n * @param stride\n * @returns\n */\nfunction bufferToVectors(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var p = [];\n\n  for (var i = 0, j = 0; i < buffer.length; i += stride, j++) {\n    if (stride === 3) {\n      p[j] = new Vector3(buffer[i], buffer[i + 1], buffer[i + 2]);\n    } else {\n      p[j] = new Vector2(buffer[i], buffer[i + 1]);\n    }\n  }\n\n  return p;\n}\n/**\n * Transforms a passed Vector2 or Vector3 array to a points buffer\n *\n * @param vectorArray\n * @returns\n */\n\nfunction vectorsToBuffer(vectorArray) {\n  var l = vectorArray.length;\n  var stride = vectorArray[0].hasOwnProperty(\"z\") ? 3 : 2;\n  var buffer = new Float32Array(l * stride);\n\n  for (var i = 0; i < l; i++) {\n    var j = i * stride;\n    buffer[j] = vectorArray[i].x;\n    buffer[j + 1] = vectorArray[i].y;\n\n    if (stride === 3) {\n      buffer[j + 2] = vectorArray[i].z;\n    }\n  }\n\n  return buffer;\n}\n\nvar three = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  bufferToVectors: bufferToVectors,\n  vectorsToBuffer: vectorsToBuffer\n});\n\nexport { bufferToVectors as b, three as t, vectorsToBuffer as v };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,OAAO;;AAExC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF,IAAIG,CAAC,GAAG,EAAE;EAEV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGN,MAAM,CAACG,MAAM,EAAEG,CAAC,IAAIL,MAAM,EAAEM,CAAC,EAAE,EAAE;IAC1D,IAAIN,MAAM,KAAK,CAAC,EAAE;MAChBI,CAAC,CAACE,CAAC,CAAC,GAAG,IAAIV,OAAO,CAACG,MAAM,CAACM,CAAC,CAAC,EAAEN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,EAAEN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLD,CAAC,CAACE,CAAC,CAAC,GAAG,IAAIT,OAAO,CAACE,MAAM,CAACM,CAAC,CAAC,EAAEN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9C;EACF;EAEA,OAAOD,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASG,eAAeA,CAACC,WAAW,EAAE;EACpC,IAAIC,CAAC,GAAGD,WAAW,CAACN,MAAM;EAC1B,IAAIF,MAAM,GAAGQ,WAAW,CAAC,CAAC,CAAC,CAACE,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACvD,IAAIX,MAAM,GAAG,IAAIY,YAAY,CAACF,CAAC,GAAGT,MAAM,CAAC;EAEzC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,CAAC,EAAEJ,CAAC,EAAE,EAAE;IAC1B,IAAIC,CAAC,GAAGD,CAAC,GAAGL,MAAM;IAClBD,MAAM,CAACO,CAAC,CAAC,GAAGE,WAAW,CAACH,CAAC,CAAC,CAACO,CAAC;IAC5Bb,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,GAAGE,WAAW,CAACH,CAAC,CAAC,CAACQ,CAAC;IAEhC,IAAIb,MAAM,KAAK,CAAC,EAAE;MAChBD,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,GAAGE,WAAW,CAACH,CAAC,CAAC,CAACS,CAAC;IAClC;EACF;EAEA,OAAOf,MAAM;AACf;AAEA,IAAIgB,KAAK,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EACrCC,SAAS,EAAE,IAAI;EACfpB,eAAe,EAAEA,eAAe;EAChCS,eAAe,EAAEA;AACnB,CAAC,CAAC;AAEF,SAAST,eAAe,IAAIqB,CAAC,EAAEJ,KAAK,IAAIK,CAAC,EAAEb,eAAe,IAAIc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}