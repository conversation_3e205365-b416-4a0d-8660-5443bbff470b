{"ast": null, "code": "import { ShaderChunk, UniformsUtils, ShaderLib, Color } from \"three\";\nlet _SubsurfaceScatteringShader;\nfunction get() {\n  if (_SubsurfaceScatteringShader) return _SubsurfaceScatteringShader;\n  const meshphong_frag_head = ShaderChunk[\"meshphong_frag\"].slice(0, ShaderChunk[\"meshphong_frag\"].indexOf(\"void main() {\"));\n  const meshphong_frag_body = ShaderChunk[\"meshphong_frag\"].slice(ShaderChunk[\"meshphong_frag\"].indexOf(\"void main() {\"));\n  _SubsurfaceScatteringShader = {\n    uniforms: UniformsUtils.merge([ShaderLib[\"phong\"].uniforms, {\n      thicknessMap: {\n        value: null\n      },\n      thicknessColor: {\n        value: new Color(16777215)\n      },\n      thicknessDistortion: {\n        value: 0.1\n      },\n      thicknessAmbient: {\n        value: 0\n      },\n      thicknessAttenuation: {\n        value: 0.1\n      },\n      thicknessPower: {\n        value: 2\n      },\n      thicknessScale: {\n        value: 10\n      }\n    }]),\n    vertexShader: (/* glsl */\n    `\n    #define USE_UV\n    ${ShaderChunk[\"meshphong_vert\"]}\n  `),\n    fragmentShader: (/* glsl */\n    `\n    #define USE_UV',\n    #define SUBSURFACE',\n\n    ${meshphong_frag_head}\n\n    uniform sampler2D thicknessMap;\n    uniform float thicknessPower;\n    uniform float thicknessScale;\n    uniform float thicknessDistortion;\n    uniform float thicknessAmbient;\n    uniform float thicknessAttenuation;\n    uniform vec3 thicknessColor;\n\n    void RE_Direct_Scattering(const in IncidentLight directLight, const in vec2 uv, const in GeometricContext geometry, inout ReflectedLight reflectedLight) {\n    \tvec3 thickness = thicknessColor * texture2D(thicknessMap, uv).r;\n    \tvec3 scatteringHalf = normalize(directLight.direction + (geometry.normal * thicknessDistortion));\n    \tfloat scatteringDot = pow(saturate(dot(geometry.viewDir, -scatteringHalf)), thicknessPower) * thicknessScale;\n    \tvec3 scatteringIllu = (scatteringDot + thicknessAmbient) * thickness;\n    \treflectedLight.directDiffuse += scatteringIllu * thicknessAttenuation * directLight.color;\n    }\n\n    ${meshphong_frag_body.replace(\"#include <lights_fragment_begin>\", ShaderChunk[\"lights_fragment_begin\"].replace(/RE_Direct\\( directLight, geometry, material, reflectedLight \\);/g, /* glsl */\n    `\n        RE_Direct( directLight, geometry, material, reflectedLight );\n\n        #if defined( SUBSURFACE ) && defined( USE_UV )\n          RE_Direct_Scattering(directLight, vUv, geometry, reflectedLight);\n        #endif\n      `))}\n  `)\n  };\n  return _SubsurfaceScatteringShader;\n}\nconst SubsurfaceScatteringShader = {\n  get uniforms() {\n    return get().uniforms;\n  },\n  set uniforms(value) {\n    get().uniforms = value;\n  },\n  get vertexShader() {\n    return get().vertexShader;\n  },\n  set vertexShader(value) {\n    get().vertexShader = value;\n  },\n  get fragmentShader() {\n    return get().vertexShader;\n  },\n  set fragmentShader(value) {\n    get().vertexShader = value;\n  }\n};\nexport { SubsurfaceScatteringShader };", "map": {"version": 3, "names": ["_SubsurfaceScatteringShader", "get", "meshphong_frag_head", "ShaderChunk", "slice", "indexOf", "meshphong_frag_body", "uniforms", "UniformsUtils", "merge", "ShaderLib", "thicknessMap", "value", "thicknessColor", "Color", "thicknessDistortion", "thicknessAmbient", "thicknessAttenuation", "thicknessPower", "thicknessScale", "vertexShader", "fragmentShader", "replace", "SubsurfaceScatteringShader"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\shaders\\SubsurfaceScatteringShader.ts"], "sourcesContent": ["import { Color, ShaderChunk, ShaderLib, UniformsUtils } from 'three'\n\n/**\n * ------------------------------------------------------------------------------------------\n * Subsurface Scattering shader\n * Based on GDC 2011 – Approximating Translucency for a Fast, Cheap and Convincing Subsurface Scattering Look\n * https://colinbarrebrisebois.com/2011/03/07/gdc-2011-approximating-translucency-for-a-fast-cheap-and-convincing-subsurface-scattering-look/\n *------------------------------------------------------------------------------------------\n */\n\nlet _SubsurfaceScatteringShader: any\n\nfunction get() {\n  if (_SubsurfaceScatteringShader) return _SubsurfaceScatteringShader\n\n  const meshphong_frag_head = ShaderChunk['meshphong_frag'].slice(\n    0,\n    ShaderChunk['meshphong_frag'].indexOf('void main() {'),\n  )\n  const meshphong_frag_body = ShaderChunk['meshphong_frag'].slice(\n    ShaderChunk['meshphong_frag'].indexOf('void main() {'),\n  )\n\n  _SubsurfaceScatteringShader = {\n    uniforms: UniformsUtils.merge([\n      ShaderLib['phong'].uniforms,\n      {\n        thicknessMap: { value: null },\n        thicknessColor: { value: new Color(0xffffff) },\n        thicknessDistortion: { value: 0.1 },\n        thicknessAmbient: { value: 0.0 },\n        thicknessAttenuation: { value: 0.1 },\n        thicknessPower: { value: 2.0 },\n        thicknessScale: { value: 10.0 },\n      },\n    ]),\n\n    vertexShader: /* glsl */ `\n    #define USE_UV\n    ${ShaderChunk['meshphong_vert']}\n  `,\n    fragmentShader: /* glsl */ `\n    #define USE_UV',\n    #define SUBSURFACE',\n\n    ${meshphong_frag_head}\n\n    uniform sampler2D thicknessMap;\n    uniform float thicknessPower;\n    uniform float thicknessScale;\n    uniform float thicknessDistortion;\n    uniform float thicknessAmbient;\n    uniform float thicknessAttenuation;\n    uniform vec3 thicknessColor;\n\n    void RE_Direct_Scattering(const in IncidentLight directLight, const in vec2 uv, const in GeometricContext geometry, inout ReflectedLight reflectedLight) {\n    \tvec3 thickness = thicknessColor * texture2D(thicknessMap, uv).r;\n    \tvec3 scatteringHalf = normalize(directLight.direction + (geometry.normal * thicknessDistortion));\n    \tfloat scatteringDot = pow(saturate(dot(geometry.viewDir, -scatteringHalf)), thicknessPower) * thicknessScale;\n    \tvec3 scatteringIllu = (scatteringDot + thicknessAmbient) * thickness;\n    \treflectedLight.directDiffuse += scatteringIllu * thicknessAttenuation * directLight.color;\n    }\n\n    ${meshphong_frag_body.replace(\n      '#include <lights_fragment_begin>',\n      ShaderChunk['lights_fragment_begin'].replace(\n        /RE_Direct\\( directLight, geometry, material, reflectedLight \\);/g,\n        /* glsl */ `\n        RE_Direct( directLight, geometry, material, reflectedLight );\n\n        #if defined( SUBSURFACE ) && defined( USE_UV )\n          RE_Direct_Scattering(directLight, vUv, geometry, reflectedLight);\n        #endif\n      `,\n      ),\n    )}\n  `,\n  }\n\n  return _SubsurfaceScatteringShader\n}\n\nexport const SubsurfaceScatteringShader = {\n  get uniforms() {\n    return get().uniforms\n  },\n  set uniforms(value) {\n    get().uniforms = value\n  },\n  get vertexShader() {\n    return get().vertexShader\n  },\n  set vertexShader(value) {\n    get().vertexShader = value\n  },\n  get fragmentShader() {\n    return get().vertexShader\n  },\n  set fragmentShader(value) {\n    get().vertexShader = value\n  },\n}\n"], "mappings": ";AAUA,IAAIA,2BAAA;AAEJ,SAASC,IAAA,EAAM;EACT,IAAAD,2BAAA,EAAoC,OAAAA,2BAAA;EAElC,MAAAE,mBAAA,GAAsBC,WAAA,CAAY,gBAAgB,EAAEC,KAAA,CACxD,GACAD,WAAA,CAAY,gBAAgB,EAAEE,OAAA,CAAQ,eAAe;EAEjD,MAAAC,mBAAA,GAAsBH,WAAA,CAAY,gBAAgB,EAAEC,KAAA,CACxDD,WAAA,CAAY,gBAAgB,EAAEE,OAAA,CAAQ,eAAe;EAGzBL,2BAAA;IAC5BO,QAAA,EAAUC,aAAA,CAAcC,KAAA,CAAM,CAC5BC,SAAA,CAAU,OAAO,EAAEH,QAAA,EACnB;MACEI,YAAA,EAAc;QAAEC,KAAA,EAAO;MAAK;MAC5BC,cAAA,EAAgB;QAAED,KAAA,EAAO,IAAIE,KAAA,CAAM,QAAQ;MAAE;MAC7CC,mBAAA,EAAqB;QAAEH,KAAA,EAAO;MAAI;MAClCI,gBAAA,EAAkB;QAAEJ,KAAA,EAAO;MAAI;MAC/BK,oBAAA,EAAsB;QAAEL,KAAA,EAAO;MAAI;MACnCM,cAAA,EAAgB;QAAEN,KAAA,EAAO;MAAI;MAC7BO,cAAA,EAAgB;QAAEP,KAAA,EAAO;MAAK;IAChC,EACD;IAEDQ,YAAA;IAAyB;AAAA;AAAA,MAEvBjB,WAAA,CAAY,gBAAgB;AAAA;IAE9BkB,cAAA;IAA2B;AAAA;AAAA;AAAA;AAAA,MAIzBnB,mBAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBAI,mBAAA,CAAoBgB,OAAA,CACpB,oCACAnB,WAAA,CAAY,uBAAuB,EAAEmB,OAAA,CACnC;IACW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAOb;AAAA;EACF;EAIK,OAAAtB,2BAAA;AACT;AAEO,MAAMuB,0BAAA,GAA6B;EACxC,IAAIhB,SAAA,EAAW;IACb,OAAON,GAAA,CAAM,EAAAM,QAAA;EACf;EACA,IAAIA,SAASK,KAAA,EAAO;IAClBX,GAAA,GAAMM,QAAA,GAAWK,KAAA;EACnB;EACA,IAAIQ,aAAA,EAAe;IACjB,OAAOnB,GAAA,CAAM,EAAAmB,YAAA;EACf;EACA,IAAIA,aAAaR,KAAA,EAAO;IACtBX,GAAA,GAAMmB,YAAA,GAAeR,KAAA;EACvB;EACA,IAAIS,eAAA,EAAiB;IACnB,OAAOpB,GAAA,CAAM,EAAAmB,YAAA;EACf;EACA,IAAIC,eAAeT,KAAA,EAAO;IACxBX,GAAA,GAAMmB,YAAA,GAAeR,KAAA;EACvB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}