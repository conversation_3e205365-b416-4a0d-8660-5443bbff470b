{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\n\n/**\n * Wraps children in a billboarded group. Sample usage:\n *\n * ```js\n * <Billboard>\n *   <Text>hi</Text>\n * </Billboard>\n * ```\n */\nconst Billboard = /*#__PURE__*/React.forwardRef(function Billboard({\n  follow = true,\n  lockX = false,\n  lockY = false,\n  lockZ = false,\n  ...props\n}, ref) {\n  const localRef = React.useRef();\n  useFrame(({\n    camera\n  }) => {\n    if (!follow || !localRef.current) return; // save previous rotation in case we're locking an axis\n\n    const prevRotation = localRef.current.rotation.clone(); // always face the camera\n\n    camera.getWorldQuaternion(localRef.current.quaternion); // readjust any axis that is locked\n\n    if (lockX) localRef.current.rotation.x = prevRotation.x;\n    if (lockY) localRef.current.rotation.y = prevRotation.y;\n    if (lockZ) localRef.current.rotation.z = prevRotation.z;\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([localRef, ref])\n  }, props));\n});\nexport { Billboard };", "map": {"version": 3, "names": ["_extends", "React", "useFrame", "mergeRefs", "Billboard", "forwardRef", "follow", "lockX", "lockY", "lockZ", "props", "ref", "localRef", "useRef", "camera", "current", "prevRotation", "rotation", "clone", "getWorldQuaternion", "quaternion", "x", "y", "z", "createElement"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Billboard.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\n\n/**\n * Wraps children in a billboarded group. Sample usage:\n *\n * ```js\n * <Billboard>\n *   <Text>hi</Text>\n * </Billboard>\n * ```\n */\nconst Billboard = /*#__PURE__*/React.forwardRef(function Billboard({\n  follow = true,\n  lockX = false,\n  lockY = false,\n  lockZ = false,\n  ...props\n}, ref) {\n  const localRef = React.useRef();\n  useFrame(({\n    camera\n  }) => {\n    if (!follow || !localRef.current) return; // save previous rotation in case we're locking an axis\n\n    const prevRotation = localRef.current.rotation.clone(); // always face the camera\n\n    camera.getWorldQuaternion(localRef.current.quaternion); // readjust any axis that is locked\n\n    if (lockX) localRef.current.rotation.x = prevRotation.x;\n    if (lockY) localRef.current.rotation.y = prevRotation.y;\n    if (lockZ) localRef.current.rotation.z = prevRotation.z;\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([localRef, ref])\n  }, props));\n});\n\nexport { Billboard };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,kBAAkB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,SAASD,SAASA,CAAC;EACjEE,MAAM,GAAG,IAAI;EACbC,KAAK,GAAG,KAAK;EACbC,KAAK,GAAG,KAAK;EACbC,KAAK,GAAG,KAAK;EACb,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMC,QAAQ,GAAGX,KAAK,CAACY,MAAM,CAAC,CAAC;EAC/BX,QAAQ,CAAC,CAAC;IACRY;EACF,CAAC,KAAK;IACJ,IAAI,CAACR,MAAM,IAAI,CAACM,QAAQ,CAACG,OAAO,EAAE,OAAO,CAAC;;IAE1C,MAAMC,YAAY,GAAGJ,QAAQ,CAACG,OAAO,CAACE,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAExDJ,MAAM,CAACK,kBAAkB,CAACP,QAAQ,CAACG,OAAO,CAACK,UAAU,CAAC,CAAC,CAAC;;IAExD,IAAIb,KAAK,EAAEK,QAAQ,CAACG,OAAO,CAACE,QAAQ,CAACI,CAAC,GAAGL,YAAY,CAACK,CAAC;IACvD,IAAIb,KAAK,EAAEI,QAAQ,CAACG,OAAO,CAACE,QAAQ,CAACK,CAAC,GAAGN,YAAY,CAACM,CAAC;IACvD,IAAIb,KAAK,EAAEG,QAAQ,CAACG,OAAO,CAACE,QAAQ,CAACM,CAAC,GAAGP,YAAY,CAACO,CAAC;EACzD,CAAC,CAAC;EACF,OAAO,aAAatB,KAAK,CAACuB,aAAa,CAAC,OAAO,EAAExB,QAAQ,CAAC;IACxDW,GAAG,EAAER,SAAS,CAAC,CAACS,QAAQ,EAAED,GAAG,CAAC;EAChC,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}