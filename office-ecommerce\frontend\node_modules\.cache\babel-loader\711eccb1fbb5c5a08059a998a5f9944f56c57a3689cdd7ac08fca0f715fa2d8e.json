{"ast": null, "code": "import * as THREE from 'three';\nfunction shaderMaterial(uniforms, vertexShader, fragmentShader, onInit) {\n  const material = class material extends THREE.ShaderMaterial {\n    constructor(parameters = {}) {\n      const entries = Object.entries(uniforms); // Create unforms and shaders\n\n      super({\n        uniforms: entries.reduce((acc, [name, value]) => {\n          const uniform = THREE.UniformsUtils.clone({\n            [name]: {\n              value\n            }\n          });\n          return {\n            ...acc,\n            ...uniform\n          };\n        }, {}),\n        vertexShader,\n        fragmentShader\n      }); // Create getter/setters\n\n      this.key = '';\n      entries.forEach(([name]) => Object.defineProperty(this, name, {\n        get: () => this.uniforms[name].value,\n        set: v => this.uniforms[name].value = v\n      })); // Assign parameters, this might include uniforms\n\n      Object.assign(this, parameters); // Call onInit\n\n      if (onInit) onInit(this);\n    }\n  };\n  material.key = THREE.MathUtils.generateUUID();\n  return material;\n}\nexport { shaderMaterial };", "map": {"version": 3, "names": ["THREE", "shaderMaterial", "uniforms", "vertexShader", "fragmentShader", "onInit", "material", "ShaderMaterial", "constructor", "parameters", "entries", "Object", "reduce", "acc", "name", "value", "uniform", "UniformsUtils", "clone", "key", "for<PERSON>ach", "defineProperty", "get", "set", "v", "assign", "MathUtils", "generateUUID"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/shaderMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\n\nfunction shaderMaterial(uniforms, vertexShader, fragmentShader, onInit) {\n  const material = class material extends THREE.ShaderMaterial {\n    constructor(parameters = {}) {\n      const entries = Object.entries(uniforms); // Create unforms and shaders\n\n      super({\n        uniforms: entries.reduce((acc, [name, value]) => {\n          const uniform = THREE.UniformsUtils.clone({\n            [name]: {\n              value\n            }\n          });\n          return { ...acc,\n            ...uniform\n          };\n        }, {}),\n        vertexShader,\n        fragmentShader\n      }); // Create getter/setters\n\n      this.key = '';\n      entries.forEach(([name]) => Object.defineProperty(this, name, {\n        get: () => this.uniforms[name].value,\n        set: v => this.uniforms[name].value = v\n      })); // Assign parameters, this might include uniforms\n\n      Object.assign(this, parameters); // Call onInit\n\n      if (onInit) onInit(this);\n    }\n\n  };\n  material.key = THREE.MathUtils.generateUUID();\n  return material;\n}\n\nexport { shaderMaterial };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,cAAcA,CAACC,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAE;EACtE,MAAMC,QAAQ,GAAG,MAAMA,QAAQ,SAASN,KAAK,CAACO,cAAc,CAAC;IAC3DC,WAAWA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;MAC3B,MAAMC,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACR,QAAQ,CAAC,CAAC,CAAC;;MAE1C,KAAK,CAAC;QACJA,QAAQ,EAAEQ,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,IAAI,EAAEC,KAAK,CAAC,KAAK;UAC/C,MAAMC,OAAO,GAAGhB,KAAK,CAACiB,aAAa,CAACC,KAAK,CAAC;YACxC,CAACJ,IAAI,GAAG;cACNC;YACF;UACF,CAAC,CAAC;UACF,OAAO;YAAE,GAAGF,GAAG;YACb,GAAGG;UACL,CAAC;QACH,CAAC,EAAE,CAAC,CAAC,CAAC;QACNb,YAAY;QACZC;MACF,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAI,CAACe,GAAG,GAAG,EAAE;MACbT,OAAO,CAACU,OAAO,CAAC,CAAC,CAACN,IAAI,CAAC,KAAKH,MAAM,CAACU,cAAc,CAAC,IAAI,EAAEP,IAAI,EAAE;QAC5DQ,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACpB,QAAQ,CAACY,IAAI,CAAC,CAACC,KAAK;QACpCQ,GAAG,EAAEC,CAAC,IAAI,IAAI,CAACtB,QAAQ,CAACY,IAAI,CAAC,CAACC,KAAK,GAAGS;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAELb,MAAM,CAACc,MAAM,CAAC,IAAI,EAAEhB,UAAU,CAAC,CAAC,CAAC;;MAEjC,IAAIJ,MAAM,EAAEA,MAAM,CAAC,IAAI,CAAC;IAC1B;EAEF,CAAC;EACDC,QAAQ,CAACa,GAAG,GAAGnB,KAAK,CAAC0B,SAAS,CAACC,YAAY,CAAC,CAAC;EAC7C,OAAOrB,QAAQ;AACjB;AAEA,SAASL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}