{"ast": null, "code": "import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { Object3D, Matrix4, Quaternion, Vector3 } from 'three';\nimport { OrthographicCamera } from './OrthographicCamera.js';\nimport { Hud } from './Hud.js';\nconst Context = /*#__PURE__*/React.createContext({});\nconst useGizmoContext = () => {\n  return React.useContext(Context);\n};\nconst turnRate = 2 * Math.PI; // turn rate in angles per second\n\nconst dummy = new Object3D();\nconst matrix = new Matrix4();\nconst [q1, q2] = [new Quaternion(), new Quaternion()];\nconst target = new Vector3();\nconst targetPosition = new Vector3();\nconst isOrbitControls = controls => {\n  return 'minPolarAngle' in controls;\n};\nconst GizmoHelper = ({\n  alignment = 'bottom-right',\n  margin = [80, 80],\n  renderPriority = 1,\n  onUpdate,\n  onTarget,\n  children\n}) => {\n  const size = useThree(state => state.size);\n  const mainCamera = useThree(state => state.camera); // @ts-ignore\n\n  const defaultControls = useThree(state => state.controls);\n  const invalidate = useThree(state => state.invalidate);\n  const gizmoRef = React.useRef();\n  const virtualCam = React.useRef(null);\n  const animating = React.useRef(false);\n  const radius = React.useRef(0);\n  const focusPoint = React.useRef(new Vector3(0, 0, 0));\n  const defaultUp = React.useRef(new Vector3(0, 0, 0));\n  React.useEffect(() => {\n    defaultUp.current.copy(mainCamera.up);\n  }, [mainCamera]);\n  const tweenCamera = React.useCallback(direction => {\n    animating.current = true;\n    if (defaultControls || onTarget) focusPoint.current = (defaultControls == null ? void 0 : defaultControls.target) || (onTarget == null ? void 0 : onTarget());\n    radius.current = mainCamera.position.distanceTo(target); // Rotate from current camera orientation\n\n    q1.copy(mainCamera.quaternion); // To new current camera orientation\n\n    targetPosition.copy(direction).multiplyScalar(radius.current).add(target);\n    dummy.lookAt(targetPosition);\n    dummy.up.copy(mainCamera.up);\n    q2.copy(dummy.quaternion);\n    invalidate();\n  }, [defaultControls, mainCamera, onTarget, invalidate]);\n  useFrame((_, delta) => {\n    if (virtualCam.current && gizmoRef.current) {\n      var _gizmoRef$current;\n\n      // Animate step\n      if (animating.current) {\n        if (q1.angleTo(q2) < 0.01) {\n          animating.current = false; // Orbit controls uses UP vector as the orbit axes,\n          // so we need to reset it after the animation is done\n          // moving it around for the controls to work correctly\n\n          if (isOrbitControls(defaultControls)) {\n            mainCamera.up.copy(defaultUp.current);\n          }\n        } else {\n          const step = delta * turnRate; // animate position by doing a slerp and then scaling the position on the unit sphere\n\n          q1.rotateTowards(q2, step); // animate orientation\n\n          mainCamera.position.set(0, 0, 1).applyQuaternion(q1).multiplyScalar(radius.current).add(focusPoint.current);\n          mainCamera.up.set(0, 1, 0).applyQuaternion(q1).normalize();\n          mainCamera.quaternion.copy(q1);\n          if (onUpdate) onUpdate();else if (defaultControls) defaultControls.update();\n          invalidate();\n        }\n      } // Sync Gizmo with main camera orientation\n\n      matrix.copy(mainCamera.matrix).invert();\n      (_gizmoRef$current = gizmoRef.current) == null ? void 0 : _gizmoRef$current.quaternion.setFromRotationMatrix(matrix);\n    }\n  });\n  const gizmoHelperContext = React.useMemo(() => ({\n    tweenCamera\n  }), [tweenCamera]); // Position gizmo component within scene\n\n  const [marginX, marginY] = margin;\n  const x = alignment.endsWith('-center') ? 0 : alignment.endsWith('-left') ? -size.width / 2 + marginX : size.width / 2 - marginX;\n  const y = alignment.startsWith('center-') ? 0 : alignment.startsWith('top-') ? size.height / 2 - marginY : -size.height / 2 + marginY;\n  return /*#__PURE__*/React.createElement(Hud, {\n    renderPriority: renderPriority\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: gizmoHelperContext\n  }, /*#__PURE__*/React.createElement(OrthographicCamera, {\n    makeDefault: true,\n    ref: virtualCam,\n    position: [0, 0, 200]\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: gizmoRef,\n    position: [x, y, 0]\n  }, children)));\n};\nexport { GizmoHelper, useGizmoContext };", "map": {"version": 3, "names": ["React", "useThree", "useFrame", "Object3D", "Matrix4", "Quaternion", "Vector3", "OrthographicCamera", "<PERSON><PERSON>", "Context", "createContext", "useGizmoContext", "useContext", "turnRate", "Math", "PI", "dummy", "matrix", "q1", "q2", "target", "targetPosition", "isOrbitControls", "controls", "GizmoHelper", "alignment", "margin", "renderPriority", "onUpdate", "onTarget", "children", "size", "state", "mainCamera", "camera", "defaultControls", "invalidate", "gizmoRef", "useRef", "virtualCam", "animating", "radius", "focusPoint", "defaultUp", "useEffect", "current", "copy", "up", "tweenCamera", "useCallback", "direction", "position", "distanceTo", "quaternion", "multiplyScalar", "add", "lookAt", "_", "delta", "_gizmoRef$current", "angleTo", "step", "rotateTowards", "set", "applyQuaternion", "normalize", "update", "invert", "setFromRotationMatrix", "gizmoHelperContext", "useMemo", "marginX", "marginY", "x", "endsWith", "width", "y", "startsWith", "height", "createElement", "Provider", "value", "makeDefault", "ref"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/GizmoHelper.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { Object3D, Matrix4, Quaternion, Vector3 } from 'three';\nimport { OrthographicCamera } from './OrthographicCamera.js';\nimport { Hud } from './Hud.js';\n\nconst Context = /*#__PURE__*/React.createContext({});\nconst useGizmoContext = () => {\n  return React.useContext(Context);\n};\nconst turnRate = 2 * Math.PI; // turn rate in angles per second\n\nconst dummy = new Object3D();\nconst matrix = new Matrix4();\nconst [q1, q2] = [new Quaternion(), new Quaternion()];\nconst target = new Vector3();\nconst targetPosition = new Vector3();\n\nconst isOrbitControls = controls => {\n  return 'minPolarAngle' in controls;\n};\n\nconst GizmoHelper = ({\n  alignment = 'bottom-right',\n  margin = [80, 80],\n  renderPriority = 1,\n  onUpdate,\n  onTarget,\n  children\n}) => {\n  const size = useThree(state => state.size);\n  const mainCamera = useThree(state => state.camera); // @ts-ignore\n\n  const defaultControls = useThree(state => state.controls);\n  const invalidate = useThree(state => state.invalidate);\n  const gizmoRef = React.useRef();\n  const virtualCam = React.useRef(null);\n  const animating = React.useRef(false);\n  const radius = React.useRef(0);\n  const focusPoint = React.useRef(new Vector3(0, 0, 0));\n  const defaultUp = React.useRef(new Vector3(0, 0, 0));\n  React.useEffect(() => {\n    defaultUp.current.copy(mainCamera.up);\n  }, [mainCamera]);\n  const tweenCamera = React.useCallback(direction => {\n    animating.current = true;\n    if (defaultControls || onTarget) focusPoint.current = (defaultControls == null ? void 0 : defaultControls.target) || (onTarget == null ? void 0 : onTarget());\n    radius.current = mainCamera.position.distanceTo(target); // Rotate from current camera orientation\n\n    q1.copy(mainCamera.quaternion); // To new current camera orientation\n\n    targetPosition.copy(direction).multiplyScalar(radius.current).add(target);\n    dummy.lookAt(targetPosition);\n    dummy.up.copy(mainCamera.up);\n    q2.copy(dummy.quaternion);\n    invalidate();\n  }, [defaultControls, mainCamera, onTarget, invalidate]);\n  useFrame((_, delta) => {\n    if (virtualCam.current && gizmoRef.current) {\n      var _gizmoRef$current;\n\n      // Animate step\n      if (animating.current) {\n        if (q1.angleTo(q2) < 0.01) {\n          animating.current = false; // Orbit controls uses UP vector as the orbit axes,\n          // so we need to reset it after the animation is done\n          // moving it around for the controls to work correctly\n\n          if (isOrbitControls(defaultControls)) {\n            mainCamera.up.copy(defaultUp.current);\n          }\n        } else {\n          const step = delta * turnRate; // animate position by doing a slerp and then scaling the position on the unit sphere\n\n          q1.rotateTowards(q2, step); // animate orientation\n\n          mainCamera.position.set(0, 0, 1).applyQuaternion(q1).multiplyScalar(radius.current).add(focusPoint.current);\n          mainCamera.up.set(0, 1, 0).applyQuaternion(q1).normalize();\n          mainCamera.quaternion.copy(q1);\n          if (onUpdate) onUpdate();else if (defaultControls) defaultControls.update();\n          invalidate();\n        }\n      } // Sync Gizmo with main camera orientation\n\n\n      matrix.copy(mainCamera.matrix).invert();\n      (_gizmoRef$current = gizmoRef.current) == null ? void 0 : _gizmoRef$current.quaternion.setFromRotationMatrix(matrix);\n    }\n  });\n  const gizmoHelperContext = React.useMemo(() => ({\n    tweenCamera\n  }), [tweenCamera]); // Position gizmo component within scene\n\n  const [marginX, marginY] = margin;\n  const x = alignment.endsWith('-center') ? 0 : alignment.endsWith('-left') ? -size.width / 2 + marginX : size.width / 2 - marginX;\n  const y = alignment.startsWith('center-') ? 0 : alignment.startsWith('top-') ? size.height / 2 - marginY : -size.height / 2 + marginY;\n  return /*#__PURE__*/React.createElement(Hud, {\n    renderPriority: renderPriority\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: gizmoHelperContext\n  }, /*#__PURE__*/React.createElement(OrthographicCamera, {\n    makeDefault: true,\n    ref: virtualCam,\n    position: [0, 0, 200]\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: gizmoRef,\n    position: [x, y, 0]\n  }, children)));\n};\n\nexport { GizmoHelper, useGizmoContext };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC9D,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,GAAG,QAAQ,UAAU;AAE9B,MAAMC,OAAO,GAAG,aAAaT,KAAK,CAACU,aAAa,CAAC,CAAC,CAAC,CAAC;AACpD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,OAAOX,KAAK,CAACY,UAAU,CAACH,OAAO,CAAC;AAClC,CAAC;AACD,MAAMI,QAAQ,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;;AAE9B,MAAMC,KAAK,GAAG,IAAIb,QAAQ,CAAC,CAAC;AAC5B,MAAMc,MAAM,GAAG,IAAIb,OAAO,CAAC,CAAC;AAC5B,MAAM,CAACc,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAC,IAAId,UAAU,CAAC,CAAC,EAAE,IAAIA,UAAU,CAAC,CAAC,CAAC;AACrD,MAAMe,MAAM,GAAG,IAAId,OAAO,CAAC,CAAC;AAC5B,MAAMe,cAAc,GAAG,IAAIf,OAAO,CAAC,CAAC;AAEpC,MAAMgB,eAAe,GAAGC,QAAQ,IAAI;EAClC,OAAO,eAAe,IAAIA,QAAQ;AACpC,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAC;EACnBC,SAAS,GAAG,cAAc;EAC1BC,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;EACjBC,cAAc,GAAG,CAAC;EAClBC,QAAQ;EACRC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,IAAI,GAAG9B,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,UAAU,GAAGhC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC;;EAEpD,MAAMC,eAAe,GAAGlC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACT,QAAQ,CAAC;EACzD,MAAMa,UAAU,GAAGnC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACI,UAAU,CAAC;EACtD,MAAMC,QAAQ,GAAGrC,KAAK,CAACsC,MAAM,CAAC,CAAC;EAC/B,MAAMC,UAAU,GAAGvC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,SAAS,GAAGxC,KAAK,CAACsC,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMG,MAAM,GAAGzC,KAAK,CAACsC,MAAM,CAAC,CAAC,CAAC;EAC9B,MAAMI,UAAU,GAAG1C,KAAK,CAACsC,MAAM,CAAC,IAAIhC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACrD,MAAMqC,SAAS,GAAG3C,KAAK,CAACsC,MAAM,CAAC,IAAIhC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACpDN,KAAK,CAAC4C,SAAS,CAAC,MAAM;IACpBD,SAAS,CAACE,OAAO,CAACC,IAAI,CAACb,UAAU,CAACc,EAAE,CAAC;EACvC,CAAC,EAAE,CAACd,UAAU,CAAC,CAAC;EAChB,MAAMe,WAAW,GAAGhD,KAAK,CAACiD,WAAW,CAACC,SAAS,IAAI;IACjDV,SAAS,CAACK,OAAO,GAAG,IAAI;IACxB,IAAIV,eAAe,IAAIN,QAAQ,EAAEa,UAAU,CAACG,OAAO,GAAG,CAACV,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACf,MAAM,MAAMS,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;IAC7JY,MAAM,CAACI,OAAO,GAAGZ,UAAU,CAACkB,QAAQ,CAACC,UAAU,CAAChC,MAAM,CAAC,CAAC,CAAC;;IAEzDF,EAAE,CAAC4B,IAAI,CAACb,UAAU,CAACoB,UAAU,CAAC,CAAC,CAAC;;IAEhChC,cAAc,CAACyB,IAAI,CAACI,SAAS,CAAC,CAACI,cAAc,CAACb,MAAM,CAACI,OAAO,CAAC,CAACU,GAAG,CAACnC,MAAM,CAAC;IACzEJ,KAAK,CAACwC,MAAM,CAACnC,cAAc,CAAC;IAC5BL,KAAK,CAAC+B,EAAE,CAACD,IAAI,CAACb,UAAU,CAACc,EAAE,CAAC;IAC5B5B,EAAE,CAAC2B,IAAI,CAAC9B,KAAK,CAACqC,UAAU,CAAC;IACzBjB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACD,eAAe,EAAEF,UAAU,EAAEJ,QAAQ,EAAEO,UAAU,CAAC,CAAC;EACvDlC,QAAQ,CAAC,CAACuD,CAAC,EAAEC,KAAK,KAAK;IACrB,IAAInB,UAAU,CAACM,OAAO,IAAIR,QAAQ,CAACQ,OAAO,EAAE;MAC1C,IAAIc,iBAAiB;;MAErB;MACA,IAAInB,SAAS,CAACK,OAAO,EAAE;QACrB,IAAI3B,EAAE,CAAC0C,OAAO,CAACzC,EAAE,CAAC,GAAG,IAAI,EAAE;UACzBqB,SAAS,CAACK,OAAO,GAAG,KAAK,CAAC,CAAC;UAC3B;UACA;;UAEA,IAAIvB,eAAe,CAACa,eAAe,CAAC,EAAE;YACpCF,UAAU,CAACc,EAAE,CAACD,IAAI,CAACH,SAAS,CAACE,OAAO,CAAC;UACvC;QACF,CAAC,MAAM;UACL,MAAMgB,IAAI,GAAGH,KAAK,GAAG7C,QAAQ,CAAC,CAAC;;UAE/BK,EAAE,CAAC4C,aAAa,CAAC3C,EAAE,EAAE0C,IAAI,CAAC,CAAC,CAAC;;UAE5B5B,UAAU,CAACkB,QAAQ,CAACY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,eAAe,CAAC9C,EAAE,CAAC,CAACoC,cAAc,CAACb,MAAM,CAACI,OAAO,CAAC,CAACU,GAAG,CAACb,UAAU,CAACG,OAAO,CAAC;UAC3GZ,UAAU,CAACc,EAAE,CAACgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,eAAe,CAAC9C,EAAE,CAAC,CAAC+C,SAAS,CAAC,CAAC;UAC1DhC,UAAU,CAACoB,UAAU,CAACP,IAAI,CAAC5B,EAAE,CAAC;UAC9B,IAAIU,QAAQ,EAAEA,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAIO,eAAe,EAAEA,eAAe,CAAC+B,MAAM,CAAC,CAAC;UAC3E9B,UAAU,CAAC,CAAC;QACd;MACF,CAAC,CAAC;;MAGFnB,MAAM,CAAC6B,IAAI,CAACb,UAAU,CAAChB,MAAM,CAAC,CAACkD,MAAM,CAAC,CAAC;MACvC,CAACR,iBAAiB,GAAGtB,QAAQ,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGc,iBAAiB,CAACN,UAAU,CAACe,qBAAqB,CAACnD,MAAM,CAAC;IACtH;EACF,CAAC,CAAC;EACF,MAAMoD,kBAAkB,GAAGrE,KAAK,CAACsE,OAAO,CAAC,OAAO;IAC9CtB;EACF,CAAC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEpB,MAAM,CAACuB,OAAO,EAAEC,OAAO,CAAC,GAAG9C,MAAM;EACjC,MAAM+C,CAAC,GAAGhD,SAAS,CAACiD,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,GAAGjD,SAAS,CAACiD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC3C,IAAI,CAAC4C,KAAK,GAAG,CAAC,GAAGJ,OAAO,GAAGxC,IAAI,CAAC4C,KAAK,GAAG,CAAC,GAAGJ,OAAO;EAChI,MAAMK,CAAC,GAAGnD,SAAS,CAACoD,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAGpD,SAAS,CAACoD,UAAU,CAAC,MAAM,CAAC,GAAG9C,IAAI,CAAC+C,MAAM,GAAG,CAAC,GAAGN,OAAO,GAAG,CAACzC,IAAI,CAAC+C,MAAM,GAAG,CAAC,GAAGN,OAAO;EACrI,OAAO,aAAaxE,KAAK,CAAC+E,aAAa,CAACvE,GAAG,EAAE;IAC3CmB,cAAc,EAAEA;EAClB,CAAC,EAAE,aAAa3B,KAAK,CAAC+E,aAAa,CAACtE,OAAO,CAACuE,QAAQ,EAAE;IACpDC,KAAK,EAAEZ;EACT,CAAC,EAAE,aAAarE,KAAK,CAAC+E,aAAa,CAACxE,kBAAkB,EAAE;IACtD2E,WAAW,EAAE,IAAI;IACjBC,GAAG,EAAE5C,UAAU;IACfY,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG;EACtB,CAAC,CAAC,EAAE,aAAanD,KAAK,CAAC+E,aAAa,CAAC,OAAO,EAAE;IAC5CI,GAAG,EAAE9C,QAAQ;IACbc,QAAQ,EAAE,CAACsB,CAAC,EAAEG,CAAC,EAAE,CAAC;EACpB,CAAC,EAAE9C,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC;AAED,SAASN,WAAW,EAAEb,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}