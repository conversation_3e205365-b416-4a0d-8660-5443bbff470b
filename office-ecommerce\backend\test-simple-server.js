const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5003;

// Enable CORS
app.use(cors());
app.use(express.json());

// Simple health endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Simple server is working' });
});

// Simple login endpoint
app.post('/api/auth/login', (req, res) => {
  console.log('Login request received:', req.body);
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      token: 'test-token',
      user: {
        id: 1,
        email: req.body.email,
        role: 'Admin'
      }
    }
  });
});

// Start server
app.listen(PORT, '127.0.0.1', () => {
  console.log(`Simple test server running on http://127.0.0.1:${PORT}`);
});

// Error handling
app.on('error', (error) => {
  console.error('Server error:', error);
});
