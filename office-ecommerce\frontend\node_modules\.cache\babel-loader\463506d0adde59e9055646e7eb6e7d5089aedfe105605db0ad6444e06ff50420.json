{"ast": null, "code": "import { MeshStandardMaterial } from 'three';\nclass MeshReflectorMaterial extends MeshStandardMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this._tDepth = {\n      value: null\n    };\n    this._distortionMap = {\n      value: null\n    };\n    this._tDiffuse = {\n      value: null\n    };\n    this._tDiffuseBlur = {\n      value: null\n    };\n    this._textureMatrix = {\n      value: null\n    };\n    this._hasBlur = {\n      value: false\n    };\n    this._mirror = {\n      value: 0.0\n    };\n    this._mixBlur = {\n      value: 0.0\n    };\n    this._blurStrength = {\n      value: 0.5\n    };\n    this._minDepthThreshold = {\n      value: 0.9\n    };\n    this._maxDepthThreshold = {\n      value: 1\n    };\n    this._depthScale = {\n      value: 0\n    };\n    this._depthToBlurRatioBias = {\n      value: 0.25\n    };\n    this._distortion = {\n      value: 1\n    };\n    this._mixContrast = {\n      value: 1.0\n    };\n    this.setValues(parameters);\n  }\n  onBeforeCompile(shader) {\n    var _shader$defines;\n    if (!((_shader$defines = shader.defines) != null && _shader$defines.USE_UV)) {\n      shader.defines.USE_UV = '';\n    }\n    shader.uniforms.hasBlur = this._hasBlur;\n    shader.uniforms.tDiffuse = this._tDiffuse;\n    shader.uniforms.tDepth = this._tDepth;\n    shader.uniforms.distortionMap = this._distortionMap;\n    shader.uniforms.tDiffuseBlur = this._tDiffuseBlur;\n    shader.uniforms.textureMatrix = this._textureMatrix;\n    shader.uniforms.mirror = this._mirror;\n    shader.uniforms.mixBlur = this._mixBlur;\n    shader.uniforms.mixStrength = this._blurStrength;\n    shader.uniforms.minDepthThreshold = this._minDepthThreshold;\n    shader.uniforms.maxDepthThreshold = this._maxDepthThreshold;\n    shader.uniforms.depthScale = this._depthScale;\n    shader.uniforms.depthToBlurRatioBias = this._depthToBlurRatioBias;\n    shader.uniforms.distortion = this._distortion;\n    shader.uniforms.mixContrast = this._mixContrast;\n    shader.vertexShader = `\n        uniform mat4 textureMatrix;\n        varying vec4 my_vUv;\n      ${shader.vertexShader}`;\n    shader.vertexShader = shader.vertexShader.replace('#include <project_vertex>', `#include <project_vertex>\n        my_vUv = textureMatrix * vec4( position, 1.0 );\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );`);\n    shader.fragmentShader = `\n        uniform sampler2D tDiffuse;\n        uniform sampler2D tDiffuseBlur;\n        uniform sampler2D tDepth;\n        uniform sampler2D distortionMap;\n        uniform float distortion;\n        uniform float cameraNear;\n\t\t\t  uniform float cameraFar;\n        uniform bool hasBlur;\n        uniform float mixBlur;\n        uniform float mirror;\n        uniform float mixStrength;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float mixContrast;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec4 my_vUv;\n        ${shader.fragmentShader}`;\n    shader.fragmentShader = shader.fragmentShader.replace('#include <emissivemap_fragment>', `#include <emissivemap_fragment>\n\n      float distortionFactor = 0.0;\n      #ifdef USE_DISTORTION\n        distortionFactor = texture2D(distortionMap, vUv).r * distortion;\n      #endif\n\n      vec4 new_vUv = my_vUv;\n      new_vUv.x += distortionFactor;\n      new_vUv.y += distortionFactor;\n\n      vec4 base = texture2DProj(tDiffuse, new_vUv);\n      vec4 blur = texture2DProj(tDiffuseBlur, new_vUv);\n\n      vec4 merge = base;\n\n      #ifdef USE_NORMALMAP\n        vec2 normal_uv = vec2(0.0);\n        vec4 normalColor = texture2D(normalMap, vUv * normalScale);\n        vec3 my_normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n        vec3 coord = new_vUv.xyz / new_vUv.w;\n        normal_uv = coord.xy + coord.z * my_normal.xz * 0.05;\n        vec4 base_normal = texture2D(tDiffuse, normal_uv);\n        vec4 blur_normal = texture2D(tDiffuseBlur, normal_uv);\n        merge = base_normal;\n        blur = blur_normal;\n      #endif\n\n      float depthFactor = 0.0001;\n      float blurFactor = 0.0;\n\n      #ifdef USE_DEPTH\n        vec4 depth = texture2DProj(tDepth, new_vUv);\n        depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n        depthFactor *= depthScale;\n        depthFactor = max(0.0001, min(1.0, depthFactor));\n\n        #ifdef USE_BLUR\n          blur = blur * min(1.0, depthFactor + depthToBlurRatioBias);\n          merge = merge * min(1.0, depthFactor + 0.5);\n        #else\n          merge = merge * depthFactor;\n        #endif\n\n      #endif\n\n      float reflectorRoughnessFactor = roughness;\n      #ifdef USE_ROUGHNESSMAP\n        vec4 reflectorTexelRoughness = texture2D( roughnessMap, vUv );\n        reflectorRoughnessFactor *= reflectorTexelRoughness.g;\n      #endif\n\n      #ifdef USE_BLUR\n        blurFactor = min(1.0, mixBlur * reflectorRoughnessFactor);\n        merge = mix(merge, blur, blurFactor);\n      #endif\n\n      vec4 newMerge = vec4(0.0, 0.0, 0.0, 1.0);\n      newMerge.r = (merge.r - 0.5) * mixContrast + 0.5;\n      newMerge.g = (merge.g - 0.5) * mixContrast + 0.5;\n      newMerge.b = (merge.b - 0.5) * mixContrast + 0.5;\n\n      diffuseColor.rgb = diffuseColor.rgb * ((1.0 - min(1.0, mirror)) + newMerge.rgb * mixStrength);\n      `);\n  }\n  get tDiffuse() {\n    return this._tDiffuse.value;\n  }\n  set tDiffuse(v) {\n    this._tDiffuse.value = v;\n  }\n  get tDepth() {\n    return this._tDepth.value;\n  }\n  set tDepth(v) {\n    this._tDepth.value = v;\n  }\n  get distortionMap() {\n    return this._distortionMap.value;\n  }\n  set distortionMap(v) {\n    this._distortionMap.value = v;\n  }\n  get tDiffuseBlur() {\n    return this._tDiffuseBlur.value;\n  }\n  set tDiffuseBlur(v) {\n    this._tDiffuseBlur.value = v;\n  }\n  get textureMatrix() {\n    return this._textureMatrix.value;\n  }\n  set textureMatrix(v) {\n    this._textureMatrix.value = v;\n  }\n  get hasBlur() {\n    return this._hasBlur.value;\n  }\n  set hasBlur(v) {\n    this._hasBlur.value = v;\n  }\n  get mirror() {\n    return this._mirror.value;\n  }\n  set mirror(v) {\n    this._mirror.value = v;\n  }\n  get mixBlur() {\n    return this._mixBlur.value;\n  }\n  set mixBlur(v) {\n    this._mixBlur.value = v;\n  }\n  get mixStrength() {\n    return this._blurStrength.value;\n  }\n  set mixStrength(v) {\n    this._blurStrength.value = v;\n  }\n  get minDepthThreshold() {\n    return this._minDepthThreshold.value;\n  }\n  set minDepthThreshold(v) {\n    this._minDepthThreshold.value = v;\n  }\n  get maxDepthThreshold() {\n    return this._maxDepthThreshold.value;\n  }\n  set maxDepthThreshold(v) {\n    this._maxDepthThreshold.value = v;\n  }\n  get depthScale() {\n    return this._depthScale.value;\n  }\n  set depthScale(v) {\n    this._depthScale.value = v;\n  }\n  get depthToBlurRatioBias() {\n    return this._depthToBlurRatioBias.value;\n  }\n  set depthToBlurRatioBias(v) {\n    this._depthToBlurRatioBias.value = v;\n  }\n  get distortion() {\n    return this._distortion.value;\n  }\n  set distortion(v) {\n    this._distortion.value = v;\n  }\n  get mixContrast() {\n    return this._mixContrast.value;\n  }\n  set mixContrast(v) {\n    this._mixContrast.value = v;\n  }\n}\nexport { MeshReflectorMaterial };", "map": {"version": 3, "names": ["MeshStandardMaterial", "MeshReflectorMaterial", "constructor", "parameters", "_tDepth", "value", "_distortionMap", "_tDiffuse", "_tDiffuseBlur", "_textureMatrix", "_hasBlur", "_mirror", "_mixBlur", "_blurStrength", "_minDepth<PERSON><PERSON><PERSON>old", "_maxD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_depthScale", "_depthToBlurRatioBias", "_distortion", "_mixContrast", "set<PERSON><PERSON><PERSON>", "onBeforeCompile", "shader", "_shader$defines", "defines", "USE_UV", "uniforms", "<PERSON><PERSON><PERSON><PERSON>", "tDiffuse", "tD<PERSON>h", "distortionMap", "tDiffuseBlur", "textureMatrix", "mirror", "mixBlur", "mixStrength", "minDepthThr<PERSON>old", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depthScale", "depthToBlurRatioBias", "distortion", "mixContrast", "vertexShader", "replace", "fragmentShader", "v"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/materials/MeshReflectorMaterial.js"], "sourcesContent": ["import { MeshStandardMaterial } from 'three';\n\nclass MeshReflectorMaterial extends MeshStandardMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this._tDepth = {\n      value: null\n    };\n    this._distortionMap = {\n      value: null\n    };\n    this._tDiffuse = {\n      value: null\n    };\n    this._tDiffuseBlur = {\n      value: null\n    };\n    this._textureMatrix = {\n      value: null\n    };\n    this._hasBlur = {\n      value: false\n    };\n    this._mirror = {\n      value: 0.0\n    };\n    this._mixBlur = {\n      value: 0.0\n    };\n    this._blurStrength = {\n      value: 0.5\n    };\n    this._minDepthThreshold = {\n      value: 0.9\n    };\n    this._maxDepthThreshold = {\n      value: 1\n    };\n    this._depthScale = {\n      value: 0\n    };\n    this._depthToBlurRatioBias = {\n      value: 0.25\n    };\n    this._distortion = {\n      value: 1\n    };\n    this._mixContrast = {\n      value: 1.0\n    };\n    this.setValues(parameters);\n  }\n\n  onBeforeCompile(shader) {\n    var _shader$defines;\n\n    if (!((_shader$defines = shader.defines) != null && _shader$defines.USE_UV)) {\n      shader.defines.USE_UV = '';\n    }\n\n    shader.uniforms.hasBlur = this._hasBlur;\n    shader.uniforms.tDiffuse = this._tDiffuse;\n    shader.uniforms.tDepth = this._tDepth;\n    shader.uniforms.distortionMap = this._distortionMap;\n    shader.uniforms.tDiffuseBlur = this._tDiffuseBlur;\n    shader.uniforms.textureMatrix = this._textureMatrix;\n    shader.uniforms.mirror = this._mirror;\n    shader.uniforms.mixBlur = this._mixBlur;\n    shader.uniforms.mixStrength = this._blurStrength;\n    shader.uniforms.minDepthThreshold = this._minDepthThreshold;\n    shader.uniforms.maxDepthThreshold = this._maxDepthThreshold;\n    shader.uniforms.depthScale = this._depthScale;\n    shader.uniforms.depthToBlurRatioBias = this._depthToBlurRatioBias;\n    shader.uniforms.distortion = this._distortion;\n    shader.uniforms.mixContrast = this._mixContrast;\n    shader.vertexShader = `\n        uniform mat4 textureMatrix;\n        varying vec4 my_vUv;\n      ${shader.vertexShader}`;\n    shader.vertexShader = shader.vertexShader.replace('#include <project_vertex>', `#include <project_vertex>\n        my_vUv = textureMatrix * vec4( position, 1.0 );\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );`);\n    shader.fragmentShader = `\n        uniform sampler2D tDiffuse;\n        uniform sampler2D tDiffuseBlur;\n        uniform sampler2D tDepth;\n        uniform sampler2D distortionMap;\n        uniform float distortion;\n        uniform float cameraNear;\n\t\t\t  uniform float cameraFar;\n        uniform bool hasBlur;\n        uniform float mixBlur;\n        uniform float mirror;\n        uniform float mixStrength;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float mixContrast;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec4 my_vUv;\n        ${shader.fragmentShader}`;\n    shader.fragmentShader = shader.fragmentShader.replace('#include <emissivemap_fragment>', `#include <emissivemap_fragment>\n\n      float distortionFactor = 0.0;\n      #ifdef USE_DISTORTION\n        distortionFactor = texture2D(distortionMap, vUv).r * distortion;\n      #endif\n\n      vec4 new_vUv = my_vUv;\n      new_vUv.x += distortionFactor;\n      new_vUv.y += distortionFactor;\n\n      vec4 base = texture2DProj(tDiffuse, new_vUv);\n      vec4 blur = texture2DProj(tDiffuseBlur, new_vUv);\n\n      vec4 merge = base;\n\n      #ifdef USE_NORMALMAP\n        vec2 normal_uv = vec2(0.0);\n        vec4 normalColor = texture2D(normalMap, vUv * normalScale);\n        vec3 my_normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n        vec3 coord = new_vUv.xyz / new_vUv.w;\n        normal_uv = coord.xy + coord.z * my_normal.xz * 0.05;\n        vec4 base_normal = texture2D(tDiffuse, normal_uv);\n        vec4 blur_normal = texture2D(tDiffuseBlur, normal_uv);\n        merge = base_normal;\n        blur = blur_normal;\n      #endif\n\n      float depthFactor = 0.0001;\n      float blurFactor = 0.0;\n\n      #ifdef USE_DEPTH\n        vec4 depth = texture2DProj(tDepth, new_vUv);\n        depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n        depthFactor *= depthScale;\n        depthFactor = max(0.0001, min(1.0, depthFactor));\n\n        #ifdef USE_BLUR\n          blur = blur * min(1.0, depthFactor + depthToBlurRatioBias);\n          merge = merge * min(1.0, depthFactor + 0.5);\n        #else\n          merge = merge * depthFactor;\n        #endif\n\n      #endif\n\n      float reflectorRoughnessFactor = roughness;\n      #ifdef USE_ROUGHNESSMAP\n        vec4 reflectorTexelRoughness = texture2D( roughnessMap, vUv );\n        reflectorRoughnessFactor *= reflectorTexelRoughness.g;\n      #endif\n\n      #ifdef USE_BLUR\n        blurFactor = min(1.0, mixBlur * reflectorRoughnessFactor);\n        merge = mix(merge, blur, blurFactor);\n      #endif\n\n      vec4 newMerge = vec4(0.0, 0.0, 0.0, 1.0);\n      newMerge.r = (merge.r - 0.5) * mixContrast + 0.5;\n      newMerge.g = (merge.g - 0.5) * mixContrast + 0.5;\n      newMerge.b = (merge.b - 0.5) * mixContrast + 0.5;\n\n      diffuseColor.rgb = diffuseColor.rgb * ((1.0 - min(1.0, mirror)) + newMerge.rgb * mixStrength);\n      `);\n  }\n\n  get tDiffuse() {\n    return this._tDiffuse.value;\n  }\n\n  set tDiffuse(v) {\n    this._tDiffuse.value = v;\n  }\n\n  get tDepth() {\n    return this._tDepth.value;\n  }\n\n  set tDepth(v) {\n    this._tDepth.value = v;\n  }\n\n  get distortionMap() {\n    return this._distortionMap.value;\n  }\n\n  set distortionMap(v) {\n    this._distortionMap.value = v;\n  }\n\n  get tDiffuseBlur() {\n    return this._tDiffuseBlur.value;\n  }\n\n  set tDiffuseBlur(v) {\n    this._tDiffuseBlur.value = v;\n  }\n\n  get textureMatrix() {\n    return this._textureMatrix.value;\n  }\n\n  set textureMatrix(v) {\n    this._textureMatrix.value = v;\n  }\n\n  get hasBlur() {\n    return this._hasBlur.value;\n  }\n\n  set hasBlur(v) {\n    this._hasBlur.value = v;\n  }\n\n  get mirror() {\n    return this._mirror.value;\n  }\n\n  set mirror(v) {\n    this._mirror.value = v;\n  }\n\n  get mixBlur() {\n    return this._mixBlur.value;\n  }\n\n  set mixBlur(v) {\n    this._mixBlur.value = v;\n  }\n\n  get mixStrength() {\n    return this._blurStrength.value;\n  }\n\n  set mixStrength(v) {\n    this._blurStrength.value = v;\n  }\n\n  get minDepthThreshold() {\n    return this._minDepthThreshold.value;\n  }\n\n  set minDepthThreshold(v) {\n    this._minDepthThreshold.value = v;\n  }\n\n  get maxDepthThreshold() {\n    return this._maxDepthThreshold.value;\n  }\n\n  set maxDepthThreshold(v) {\n    this._maxDepthThreshold.value = v;\n  }\n\n  get depthScale() {\n    return this._depthScale.value;\n  }\n\n  set depthScale(v) {\n    this._depthScale.value = v;\n  }\n\n  get depthToBlurRatioBias() {\n    return this._depthToBlurRatioBias.value;\n  }\n\n  set depthToBlurRatioBias(v) {\n    this._depthToBlurRatioBias.value = v;\n  }\n\n  get distortion() {\n    return this._distortion.value;\n  }\n\n  set distortion(v) {\n    this._distortion.value = v;\n  }\n\n  get mixContrast() {\n    return this._mixContrast.value;\n  }\n\n  set mixContrast(v) {\n    this._mixContrast.value = v;\n  }\n\n}\n\nexport { MeshReflectorMaterial };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,OAAO;AAE5C,MAAMC,qBAAqB,SAASD,oBAAoB,CAAC;EACvDE,WAAWA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;IAC3B,KAAK,CAACA,UAAU,CAAC;IACjB,IAAI,CAACC,OAAO,GAAG;MACbC,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACC,cAAc,GAAG;MACpBD,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACE,SAAS,GAAG;MACfF,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACG,aAAa,GAAG;MACnBH,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACI,cAAc,GAAG;MACpBJ,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACK,QAAQ,GAAG;MACdL,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACM,OAAO,GAAG;MACbN,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACO,QAAQ,GAAG;MACdP,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACQ,aAAa,GAAG;MACnBR,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACS,kBAAkB,GAAG;MACxBT,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACU,kBAAkB,GAAG;MACxBV,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACW,WAAW,GAAG;MACjBX,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACY,qBAAqB,GAAG;MAC3BZ,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACa,WAAW,GAAG;MACjBb,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACc,YAAY,GAAG;MAClBd,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACe,SAAS,CAACjB,UAAU,CAAC;EAC5B;EAEAkB,eAAeA,CAACC,MAAM,EAAE;IACtB,IAAIC,eAAe;IAEnB,IAAI,EAAE,CAACA,eAAe,GAAGD,MAAM,CAACE,OAAO,KAAK,IAAI,IAAID,eAAe,CAACE,MAAM,CAAC,EAAE;MAC3EH,MAAM,CAACE,OAAO,CAACC,MAAM,GAAG,EAAE;IAC5B;IAEAH,MAAM,CAACI,QAAQ,CAACC,OAAO,GAAG,IAAI,CAACjB,QAAQ;IACvCY,MAAM,CAACI,QAAQ,CAACE,QAAQ,GAAG,IAAI,CAACrB,SAAS;IACzCe,MAAM,CAACI,QAAQ,CAACG,MAAM,GAAG,IAAI,CAACzB,OAAO;IACrCkB,MAAM,CAACI,QAAQ,CAACI,aAAa,GAAG,IAAI,CAACxB,cAAc;IACnDgB,MAAM,CAACI,QAAQ,CAACK,YAAY,GAAG,IAAI,CAACvB,aAAa;IACjDc,MAAM,CAACI,QAAQ,CAACM,aAAa,GAAG,IAAI,CAACvB,cAAc;IACnDa,MAAM,CAACI,QAAQ,CAACO,MAAM,GAAG,IAAI,CAACtB,OAAO;IACrCW,MAAM,CAACI,QAAQ,CAACQ,OAAO,GAAG,IAAI,CAACtB,QAAQ;IACvCU,MAAM,CAACI,QAAQ,CAACS,WAAW,GAAG,IAAI,CAACtB,aAAa;IAChDS,MAAM,CAACI,QAAQ,CAACU,iBAAiB,GAAG,IAAI,CAACtB,kBAAkB;IAC3DQ,MAAM,CAACI,QAAQ,CAACW,iBAAiB,GAAG,IAAI,CAACtB,kBAAkB;IAC3DO,MAAM,CAACI,QAAQ,CAACY,UAAU,GAAG,IAAI,CAACtB,WAAW;IAC7CM,MAAM,CAACI,QAAQ,CAACa,oBAAoB,GAAG,IAAI,CAACtB,qBAAqB;IACjEK,MAAM,CAACI,QAAQ,CAACc,UAAU,GAAG,IAAI,CAACtB,WAAW;IAC7CI,MAAM,CAACI,QAAQ,CAACe,WAAW,GAAG,IAAI,CAACtB,YAAY;IAC/CG,MAAM,CAACoB,YAAY,GAAG;AAC1B;AACA;AACA,QAAQpB,MAAM,CAACoB,YAAY,EAAE;IACzBpB,MAAM,CAACoB,YAAY,GAAGpB,MAAM,CAACoB,YAAY,CAACC,OAAO,CAAC,2BAA2B,EAAE;AACnF;AACA,kFAAkF,CAAC;IAC/ErB,MAAM,CAACsB,cAAc,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUtB,MAAM,CAACsB,cAAc,EAAE;IAC7BtB,MAAM,CAACsB,cAAc,GAAGtB,MAAM,CAACsB,cAAc,CAACD,OAAO,CAAC,iCAAiC,EAAE;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC;EACN;EAEA,IAAIf,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrB,SAAS,CAACF,KAAK;EAC7B;EAEA,IAAIuB,QAAQA,CAACiB,CAAC,EAAE;IACd,IAAI,CAACtC,SAAS,CAACF,KAAK,GAAGwC,CAAC;EAC1B;EAEA,IAAIhB,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACzB,OAAO,CAACC,KAAK;EAC3B;EAEA,IAAIwB,MAAMA,CAACgB,CAAC,EAAE;IACZ,IAAI,CAACzC,OAAO,CAACC,KAAK,GAAGwC,CAAC;EACxB;EAEA,IAAIf,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACxB,cAAc,CAACD,KAAK;EAClC;EAEA,IAAIyB,aAAaA,CAACe,CAAC,EAAE;IACnB,IAAI,CAACvC,cAAc,CAACD,KAAK,GAAGwC,CAAC;EAC/B;EAEA,IAAId,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACvB,aAAa,CAACH,KAAK;EACjC;EAEA,IAAI0B,YAAYA,CAACc,CAAC,EAAE;IAClB,IAAI,CAACrC,aAAa,CAACH,KAAK,GAAGwC,CAAC;EAC9B;EAEA,IAAIb,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACvB,cAAc,CAACJ,KAAK;EAClC;EAEA,IAAI2B,aAAaA,CAACa,CAAC,EAAE;IACnB,IAAI,CAACpC,cAAc,CAACJ,KAAK,GAAGwC,CAAC;EAC/B;EAEA,IAAIlB,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,QAAQ,CAACL,KAAK;EAC5B;EAEA,IAAIsB,OAAOA,CAACkB,CAAC,EAAE;IACb,IAAI,CAACnC,QAAQ,CAACL,KAAK,GAAGwC,CAAC;EACzB;EAEA,IAAIZ,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtB,OAAO,CAACN,KAAK;EAC3B;EAEA,IAAI4B,MAAMA,CAACY,CAAC,EAAE;IACZ,IAAI,CAAClC,OAAO,CAACN,KAAK,GAAGwC,CAAC;EACxB;EAEA,IAAIX,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtB,QAAQ,CAACP,KAAK;EAC5B;EAEA,IAAI6B,OAAOA,CAACW,CAAC,EAAE;IACb,IAAI,CAACjC,QAAQ,CAACP,KAAK,GAAGwC,CAAC;EACzB;EAEA,IAAIV,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACtB,aAAa,CAACR,KAAK;EACjC;EAEA,IAAI8B,WAAWA,CAACU,CAAC,EAAE;IACjB,IAAI,CAAChC,aAAa,CAACR,KAAK,GAAGwC,CAAC;EAC9B;EAEA,IAAIT,iBAAiBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACtB,kBAAkB,CAACT,KAAK;EACtC;EAEA,IAAI+B,iBAAiBA,CAACS,CAAC,EAAE;IACvB,IAAI,CAAC/B,kBAAkB,CAACT,KAAK,GAAGwC,CAAC;EACnC;EAEA,IAAIR,iBAAiBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACtB,kBAAkB,CAACV,KAAK;EACtC;EAEA,IAAIgC,iBAAiBA,CAACQ,CAAC,EAAE;IACvB,IAAI,CAAC9B,kBAAkB,CAACV,KAAK,GAAGwC,CAAC;EACnC;EAEA,IAAIP,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACtB,WAAW,CAACX,KAAK;EAC/B;EAEA,IAAIiC,UAAUA,CAACO,CAAC,EAAE;IAChB,IAAI,CAAC7B,WAAW,CAACX,KAAK,GAAGwC,CAAC;EAC5B;EAEA,IAAIN,oBAAoBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACtB,qBAAqB,CAACZ,KAAK;EACzC;EAEA,IAAIkC,oBAAoBA,CAACM,CAAC,EAAE;IAC1B,IAAI,CAAC5B,qBAAqB,CAACZ,KAAK,GAAGwC,CAAC;EACtC;EAEA,IAAIL,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACtB,WAAW,CAACb,KAAK;EAC/B;EAEA,IAAImC,UAAUA,CAACK,CAAC,EAAE;IAChB,IAAI,CAAC3B,WAAW,CAACb,KAAK,GAAGwC,CAAC;EAC5B;EAEA,IAAIJ,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACtB,YAAY,CAACd,KAAK;EAChC;EAEA,IAAIoC,WAAWA,CAACI,CAAC,EAAE;IACjB,IAAI,CAAC1B,YAAY,CAACd,KAAK,GAAGwC,CAAC;EAC7B;AAEF;AAEA,SAAS5C,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}