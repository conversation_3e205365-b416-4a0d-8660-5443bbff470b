{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CatmullRomCurve3, Color } from 'three';\nimport { Line } from './Line.js';\nconst CatmullRomLine = /*#__PURE__*/React.forwardRef(function CatmullRomLine({\n  points,\n  closed = false,\n  curveType = 'centripetal',\n  tension = 0.5,\n  segments = 20,\n  vertexColors,\n  ...rest\n}, ref) {\n  const curve = React.useMemo(() => {\n    const mappedPoints = points.map(pt => pt instanceof Vector3 ? pt : new Vector3(...pt));\n    return new CatmullRomCurve3(mappedPoints, closed, curveType, tension);\n  }, [points, closed, curveType, tension]);\n  const segmentedPoints = React.useMemo(() => curve.getPoints(segments), [curve, segments]);\n  const interpolatedVertexColors = React.useMemo(() => {\n    if (!vertexColors || vertexColors.length < 2) return undefined;\n    if (vertexColors.length === segments + 1) return vertexColors;\n    const mappedColors = vertexColors.map(color => color instanceof Color ? color : new Color(...color));\n    if (closed) mappedColors.push(mappedColors[0].clone());\n    const iColors = [mappedColors[0]];\n    const divisions = segments / (mappedColors.length - 1);\n    for (let i = 1; i < segments; i++) {\n      const alpha = i % divisions / divisions;\n      const colorIndex = Math.floor(i / divisions);\n      iColors.push(mappedColors[colorIndex].clone().lerp(mappedColors[colorIndex + 1], alpha));\n    }\n    iColors.push(mappedColors[mappedColors.length - 1]);\n    return iColors;\n  }, [vertexColors, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: ref,\n    points: segmentedPoints,\n    vertexColors: interpolatedVertexColors\n  }, rest));\n});\nexport { CatmullRomLine };", "map": {"version": 3, "names": ["_extends", "React", "Vector3", "CatmullRomCurve3", "Color", "Line", "CatmullRomLine", "forwardRef", "points", "closed", "curveType", "tension", "segments", "vertexColors", "rest", "ref", "curve", "useMemo", "mappedPoints", "map", "pt", "segmentedPoints", "getPoints", "interpolatedVertexColors", "length", "undefined", "mappedColors", "color", "push", "clone", "iColors", "divisions", "i", "alpha", "colorIndex", "Math", "floor", "lerp", "createElement"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/CatmullRomLine.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CatmullRomCurve3, Color } from 'three';\nimport { Line } from './Line.js';\n\nconst CatmullRomLine = /*#__PURE__*/React.forwardRef(function CatmullRomLine({\n  points,\n  closed = false,\n  curveType = 'centripetal',\n  tension = 0.5,\n  segments = 20,\n  vertexColors,\n  ...rest\n}, ref) {\n  const curve = React.useMemo(() => {\n    const mappedPoints = points.map(pt => pt instanceof Vector3 ? pt : new Vector3(...pt));\n    return new CatmullRomCurve3(mappedPoints, closed, curveType, tension);\n  }, [points, closed, curveType, tension]);\n  const segmentedPoints = React.useMemo(() => curve.getPoints(segments), [curve, segments]);\n  const interpolatedVertexColors = React.useMemo(() => {\n    if (!vertexColors || vertexColors.length < 2) return undefined;\n    if (vertexColors.length === segments + 1) return vertexColors;\n    const mappedColors = vertexColors.map(color => color instanceof Color ? color : new Color(...color));\n    if (closed) mappedColors.push(mappedColors[0].clone());\n    const iColors = [mappedColors[0]];\n    const divisions = segments / (mappedColors.length - 1);\n\n    for (let i = 1; i < segments; i++) {\n      const alpha = i % divisions / divisions;\n      const colorIndex = Math.floor(i / divisions);\n      iColors.push(mappedColors[colorIndex].clone().lerp(mappedColors[colorIndex + 1], alpha));\n    }\n\n    iColors.push(mappedColors[mappedColors.length - 1]);\n    return iColors;\n  }, [vertexColors, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: ref,\n    points: segmentedPoints,\n    vertexColors: interpolatedVertexColors\n  }, rest));\n});\n\nexport { CatmullRomLine };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,KAAK,QAAQ,OAAO;AACxD,SAASC,IAAI,QAAQ,WAAW;AAEhC,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,SAASD,cAAcA,CAAC;EAC3EE,MAAM;EACNC,MAAM,GAAG,KAAK;EACdC,SAAS,GAAG,aAAa;EACzBC,OAAO,GAAG,GAAG;EACbC,QAAQ,GAAG,EAAE;EACbC,YAAY;EACZ,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMC,KAAK,GAAGf,KAAK,CAACgB,OAAO,CAAC,MAAM;IAChC,MAAMC,YAAY,GAAGV,MAAM,CAACW,GAAG,CAACC,EAAE,IAAIA,EAAE,YAAYlB,OAAO,GAAGkB,EAAE,GAAG,IAAIlB,OAAO,CAAC,GAAGkB,EAAE,CAAC,CAAC;IACtF,OAAO,IAAIjB,gBAAgB,CAACe,YAAY,EAAET,MAAM,EAAEC,SAAS,EAAEC,OAAO,CAAC;EACvE,CAAC,EAAE,CAACH,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,CAAC,CAAC;EACxC,MAAMU,eAAe,GAAGpB,KAAK,CAACgB,OAAO,CAAC,MAAMD,KAAK,CAACM,SAAS,CAACV,QAAQ,CAAC,EAAE,CAACI,KAAK,EAAEJ,QAAQ,CAAC,CAAC;EACzF,MAAMW,wBAAwB,GAAGtB,KAAK,CAACgB,OAAO,CAAC,MAAM;IACnD,IAAI,CAACJ,YAAY,IAAIA,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE,OAAOC,SAAS;IAC9D,IAAIZ,YAAY,CAACW,MAAM,KAAKZ,QAAQ,GAAG,CAAC,EAAE,OAAOC,YAAY;IAC7D,MAAMa,YAAY,GAAGb,YAAY,CAACM,GAAG,CAACQ,KAAK,IAAIA,KAAK,YAAYvB,KAAK,GAAGuB,KAAK,GAAG,IAAIvB,KAAK,CAAC,GAAGuB,KAAK,CAAC,CAAC;IACpG,IAAIlB,MAAM,EAAEiB,YAAY,CAACE,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;IACtD,MAAMC,OAAO,GAAG,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC;IACjC,MAAMK,SAAS,GAAGnB,QAAQ,IAAIc,YAAY,CAACF,MAAM,GAAG,CAAC,CAAC;IAEtD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,QAAQ,EAAEoB,CAAC,EAAE,EAAE;MACjC,MAAMC,KAAK,GAAGD,CAAC,GAAGD,SAAS,GAAGA,SAAS;MACvC,MAAMG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACJ,CAAC,GAAGD,SAAS,CAAC;MAC5CD,OAAO,CAACF,IAAI,CAACF,YAAY,CAACQ,UAAU,CAAC,CAACL,KAAK,CAAC,CAAC,CAACQ,IAAI,CAACX,YAAY,CAACQ,UAAU,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC,CAAC;IAC1F;IAEAH,OAAO,CAACF,IAAI,CAACF,YAAY,CAACA,YAAY,CAACF,MAAM,GAAG,CAAC,CAAC,CAAC;IACnD,OAAOM,OAAO;EAChB,CAAC,EAAE,CAACjB,YAAY,EAAED,QAAQ,CAAC,CAAC;EAC5B,OAAO,aAAaX,KAAK,CAACqC,aAAa,CAACjC,IAAI,EAAEL,QAAQ,CAAC;IACrDe,GAAG,EAAEA,GAAG;IACRP,MAAM,EAAEa,eAAe;IACvBR,YAAY,EAAEU;EAChB,CAAC,EAAET,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAASR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}