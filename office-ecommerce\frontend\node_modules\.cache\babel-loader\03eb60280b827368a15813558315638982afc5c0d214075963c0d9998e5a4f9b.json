{"ast": null, "code": "import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { useCubeCamera } from './useCubeCamera.js';\nfunction CubeCamera({\n  children,\n  frames = Infinity,\n  resolution,\n  near,\n  far,\n  envMap,\n  fog,\n  ...props\n}) {\n  const ref = React.useRef();\n  const {\n    fbo,\n    camera,\n    update\n  } = useCubeCamera({\n    resolution,\n    near,\n    far,\n    envMap,\n    fog\n  });\n  let count = 0;\n  useFrame(() => {\n    if (ref.current && (frames === Infinity || count < frames)) {\n      ref.current.visible = false;\n      update();\n      ref.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: camera\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, children(fbo.texture)));\n}\nexport { CubeCamera };", "map": {"version": 3, "names": ["React", "useFrame", "useCubeCamera", "CubeCamera", "children", "frames", "Infinity", "resolution", "near", "far", "envMap", "fog", "props", "ref", "useRef", "fbo", "camera", "update", "count", "current", "visible", "createElement", "object", "texture"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/CubeCamera.js"], "sourcesContent": ["import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { useCubeCamera } from './useCubeCamera.js';\n\nfunction CubeCamera({\n  children,\n  frames = Infinity,\n  resolution,\n  near,\n  far,\n  envMap,\n  fog,\n  ...props\n}) {\n  const ref = React.useRef();\n  const {\n    fbo,\n    camera,\n    update\n  } = useCubeCamera({\n    resolution,\n    near,\n    far,\n    envMap,\n    fog\n  });\n  let count = 0;\n  useFrame(() => {\n    if (ref.current && (frames === Infinity || count < frames)) {\n      ref.current.visible = false;\n      update();\n      ref.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: camera\n  }), /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, children(fbo.texture)));\n}\n\nexport { CubeCamera };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,aAAa,QAAQ,oBAAoB;AAElD,SAASC,UAAUA,CAAC;EAClBC,QAAQ;EACRC,MAAM,GAAGC,QAAQ;EACjBC,UAAU;EACVC,IAAI;EACJC,GAAG;EACHC,MAAM;EACNC,GAAG;EACH,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGb,KAAK,CAACc,MAAM,CAAC,CAAC;EAC1B,MAAM;IACJC,GAAG;IACHC,MAAM;IACNC;EACF,CAAC,GAAGf,aAAa,CAAC;IAChBK,UAAU;IACVC,IAAI;IACJC,GAAG;IACHC,MAAM;IACNC;EACF,CAAC,CAAC;EACF,IAAIO,KAAK,GAAG,CAAC;EACbjB,QAAQ,CAAC,MAAM;IACb,IAAIY,GAAG,CAACM,OAAO,KAAKd,MAAM,KAAKC,QAAQ,IAAIY,KAAK,GAAGb,MAAM,CAAC,EAAE;MAC1DQ,GAAG,CAACM,OAAO,CAACC,OAAO,GAAG,KAAK;MAC3BH,MAAM,CAAC,CAAC;MACRJ,GAAG,CAACM,OAAO,CAACC,OAAO,GAAG,IAAI;MAC1BF,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAO,aAAalB,KAAK,CAACqB,aAAa,CAAC,OAAO,EAAET,KAAK,EAAE,aAAaZ,KAAK,CAACqB,aAAa,CAAC,WAAW,EAAE;IACpGC,MAAM,EAAEN;EACV,CAAC,CAAC,EAAE,aAAahB,KAAK,CAACqB,aAAa,CAAC,OAAO,EAAE;IAC5CR,GAAG,EAAEA;EACP,CAAC,EAAET,QAAQ,CAACW,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC;AAC5B;AAEA,SAASpB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}