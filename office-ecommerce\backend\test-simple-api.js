const axios = require('axios');

async function testSimpleAPI() {
  try {
    console.log('🧪 Testing Simple API on port 5003...');

    // Test health endpoint
    console.log('\n🔍 Testing /health endpoint...');
    try {
      const healthResponse = await axios.get('http://127.0.0.1:5003/health');
      console.log('✅ Health endpoint working');
      console.log('Response:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Health endpoint failed:', error.code, error.message);
    }

    // Test login endpoint
    console.log('\n🔍 Testing login endpoint...');
    try {
      const loginResponse = await axios.post('http://127.0.0.1:5003/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      console.log('✅ Login endpoint working');
      console.log('Response:', JSON.stringify(loginResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Login endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n✅ Simple API test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSimpleAPI();
