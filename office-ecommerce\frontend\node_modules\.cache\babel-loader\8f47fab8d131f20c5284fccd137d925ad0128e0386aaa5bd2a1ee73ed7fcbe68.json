{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\admin\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useWebSocket } from '../../hooks/useWebSocket';\nimport { useAuth } from '../../hooks/useAuth';\nimport { usePermissions } from '../../hooks/usePermissions';\nimport './AdminDashboard.css';\n\n// Import admin components\nimport DashboardOverview from '../../components/admin/DashboardOverview';\nimport InventoryManagement from '../../components/admin/InventoryManagement';\nimport ProductManagementNew from '../../components/admin/ProductManagementNew';\nimport OrderManagement from '../../components/admin/OrderManagement';\nimport SupplierManagement from '../../components/admin/SupplierManagement';\nimport UserManagement from '../../components/admin/UserManagement';\nimport Analytics from '../../components/admin/Analytics';\nimport ActivityLogs from '../../components/admin/ActivityLogs';\n\n// Import modern SVG icons and logo\nimport { DashboardIcon, InventoryIcon, ProductsIcon, OrdersIcon, SuppliersIcon, UsersIcon, AnalyticsIcon, ActivityIcon, LogoutIcon, ConnectedIcon, DisconnectedIcon } from '../../components/admin/icons/AdminIcons';\nimport AdminLogo from '../../components/common/AdminLogo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('overview');\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    canAccessAdminSection,\n    getAccessibleAdminSections,\n    isAdmin,\n    isEmployee\n  } = usePermissions();\n  const {\n    isConnected,\n    connectionStatus,\n    notifications,\n    removeNotification,\n    clearAllNotifications\n  } = useWebSocket();\n\n  // Get accessible sections for current user\n  const accessibleSections = getAccessibleAdminSections();\n\n  // Check for URL parameters to set active tab\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const tabFromUrl = searchParams.get('tab');\n    if (tabFromUrl && accessibleSections.includes(tabFromUrl)) {\n      setActiveTab(tabFromUrl);\n    } else if (accessibleSections.length > 0 && !accessibleSections.includes(activeTab)) {\n      setActiveTab(accessibleSections[0]);\n    }\n  }, [accessibleSections, activeTab, location.search]);\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  // Check if user can access a specific tab\n  const canAccessTab = tabName => {\n    return canAccessAdminSection(tabName);\n  };\n\n  // Handle tab change with permission check\n  const handleTabChange = tabName => {\n    if (canAccessTab(tabName)) {\n      setActiveTab(tabName);\n      // Update URL to reflect current tab\n      navigate(`/admin?tab=${tabName}`, {\n        replace: true\n      });\n    }\n  };\n\n  // Define all menu items with their permission requirements\n  const allMenuItems = [{\n    id: 'overview',\n    label: 'Dashboard',\n    icon: DashboardIcon,\n    permission: 'access_admin_dashboard'\n  }, {\n    id: 'inventory',\n    label: 'Inventory',\n    icon: InventoryIcon,\n    permission: 'manage_inventory'\n  }, {\n    id: 'products',\n    label: 'Products',\n    icon: ProductsIcon,\n    permission: 'manage_products'\n  }, {\n    id: 'orders',\n    label: 'Orders',\n    icon: OrdersIcon,\n    permission: 'view_all_orders'\n  }, {\n    id: 'suppliers',\n    label: 'Suppliers',\n    icon: SuppliersIcon,\n    permission: 'manage_suppliers'\n  }, {\n    id: 'users',\n    label: 'Users',\n    icon: UsersIcon,\n    permission: 'manage_users'\n  }, {\n    id: 'analytics',\n    label: 'Analytics',\n    icon: AnalyticsIcon,\n    permission: 'view_analytics'\n  }, {\n    id: 'activity-logs',\n    label: 'Activity Logs',\n    icon: ActivityIcon,\n    permission: 'access_admin_dashboard'\n  }];\n\n  // Filter menu items based on user permissions\n  const menuItems = allMenuItems.filter(item => canAccessAdminSection(item.id));\n  const renderContent = () => {\n    // Check if user has permission to access current tab\n    if (!canAccessAdminSection(activeTab)) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"access-denied\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You don't have permission to access this section.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Your role: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: user.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this);\n    }\n    switch (activeTab) {\n      case 'overview':\n        return /*#__PURE__*/_jsxDEV(DashboardOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 16\n        }, this);\n      case 'inventory':\n        return /*#__PURE__*/_jsxDEV(InventoryManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 16\n        }, this);\n      case 'products':\n        return /*#__PURE__*/_jsxDEV(ProductManagementNew, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 16\n        }, this);\n      case 'orders':\n        return /*#__PURE__*/_jsxDEV(OrderManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 16\n        }, this);\n      case 'suppliers':\n        return /*#__PURE__*/_jsxDEV(SupplierManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 16\n        }, this);\n      case 'users':\n        return /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 16\n        }, this);\n      case 'analytics':\n        return /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 16\n        }, this);\n      case 'activity-logs':\n        return /*#__PURE__*/_jsxDEV(ActivityLogs, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(DashboardOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-logo\",\n          children: /*#__PURE__*/_jsxDEV(AdminLogo, {\n            size: \"small\",\n            className: \"admin-logo-component\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"connection-status\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-indicator\",\n            children: isConnected ? /*#__PURE__*/_jsxDEV(ConnectedIcon, {\n              className: \"status-icon connected\",\n              color: \"#10B981\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(DisconnectedIcon, {\n              className: \"status-icon disconnected\",\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-text\",\n            children: isConnected ? 'Live Updates' : 'Offline'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-user-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-name\",\n            children: [user.firstName, \" \", user.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-role\",\n            children: user.role\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"admin-nav\",\n        children: menuItems.map(item => {\n          const IconComponent = item.icon;\n          const isActive = activeTab === item.id;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-item ${isActive ? 'active' : ''}`,\n            onClick: () => handleTabChange(item.id),\n            title: `${item.label} - ${item.permission}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                className: \"nav-icon-svg\",\n                color: isActive ? '#F0B21B' : 'currentColor'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-label\",\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"logout-btn\",\n          onClick: handleLogout,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-icon\",\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {\n              className: \"nav-icon-svg\",\n              color: \"#e74c3c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-label\",\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-content\",\n        children: renderContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notifications-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notifications-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Live Updates (\", notifications.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: clearAllNotifications,\n          className: \"clear-all-btn\",\n          children: \"Clear All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"notifications-list\",\n        children: notifications.slice(0, 5).map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `notification ${notification.type}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: notification.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: notification.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"notification-time\",\n              children: new Date(notification.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => removeNotification(notification.id),\n            className: \"notification-close\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 17\n          }, this)]\n        }, notification.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"jAoZArQlg9ilQ3NG3qXp2G4Kyt0=\", false, function () {\n  return [useNavigate, useLocation, useAuth, usePermissions, useWebSocket];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "useWebSocket", "useAuth", "usePermissions", "DashboardOverview", "InventoryManagement", "ProductManagementNew", "OrderManagement", "SupplierManagement", "UserManagement", "Analytics", "ActivityLogs", "DashboardIcon", "InventoryIcon", "ProductsIcon", "OrdersIcon", "SuppliersIcon", "UsersIcon", "AnalyticsIcon", "ActivityIcon", "LogoutIcon", "ConnectedIcon", "DisconnectedIcon", "AdminLogo", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "activeTab", "setActiveTab", "navigate", "location", "user", "logout", "canAccessAdminSection", "getAccessibleAdminSections", "isAdmin", "isEmployee", "isConnected", "connectionStatus", "notifications", "removeNotification", "clearAllNotifications", "accessibleSections", "searchParams", "URLSearchParams", "search", "tabFromUrl", "get", "includes", "length", "handleLogout", "canAccessTab", "tabName", "handleTabChange", "replace", "allMenuItems", "id", "label", "icon", "permission", "menuItems", "filter", "item", "renderContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "size", "color", "firstName", "lastName", "map", "IconComponent", "isActive", "onClick", "title", "slice", "notification", "type", "message", "Date", "timestamp", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/admin/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useWebSocket } from '../../hooks/useWebSocket';\nimport { useAuth } from '../../hooks/useAuth';\nimport { usePermissions } from '../../hooks/usePermissions';\nimport './AdminDashboard.css';\n\n// Import admin components\nimport DashboardOverview from '../../components/admin/DashboardOverview';\nimport InventoryManagement from '../../components/admin/InventoryManagement';\nimport ProductManagementNew from '../../components/admin/ProductManagementNew';\nimport OrderManagement from '../../components/admin/OrderManagement';\nimport SupplierManagement from '../../components/admin/SupplierManagement';\nimport UserManagement from '../../components/admin/UserManagement';\nimport Analytics from '../../components/admin/Analytics';\nimport ActivityLogs from '../../components/admin/ActivityLogs';\n\n// Import modern SVG icons and logo\nimport {\n  DashboardIcon,\n  InventoryIcon,\n  ProductsIcon,\n  OrdersIcon,\n  SuppliersIcon,\n  UsersIcon,\n  AnalyticsIcon,\n  ActivityIcon,\n  LogoutIcon,\n  ConnectedIcon,\n  DisconnectedIcon\n} from '../../components/admin/icons/AdminIcons';\nimport AdminLogo from '../../components/common/AdminLogo';\n\nconst AdminDashboard = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout } = useAuth();\n  const { canAccessAdminSection, getAccessibleAdminSections, isAdmin, isEmployee } = usePermissions();\n  const { isConnected, connectionStatus, notifications, removeNotification, clearAllNotifications } = useWebSocket();\n\n  // Get accessible sections for current user\n  const accessibleSections = getAccessibleAdminSections();\n\n  // Check for URL parameters to set active tab\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const tabFromUrl = searchParams.get('tab');\n\n    if (tabFromUrl && accessibleSections.includes(tabFromUrl)) {\n      setActiveTab(tabFromUrl);\n    } else if (accessibleSections.length > 0 && !accessibleSections.includes(activeTab)) {\n      setActiveTab(accessibleSections[0]);\n    }\n  }, [accessibleSections, activeTab, location.search]);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  // Check if user can access a specific tab\n  const canAccessTab = (tabName) => {\n    return canAccessAdminSection(tabName);\n  };\n\n  // Handle tab change with permission check\n  const handleTabChange = (tabName) => {\n    if (canAccessTab(tabName)) {\n      setActiveTab(tabName);\n      // Update URL to reflect current tab\n      navigate(`/admin?tab=${tabName}`, { replace: true });\n    }\n  };\n\n  // Define all menu items with their permission requirements\n  const allMenuItems = [\n    { id: 'overview', label: 'Dashboard', icon: DashboardIcon, permission: 'access_admin_dashboard' },\n    { id: 'inventory', label: 'Inventory', icon: InventoryIcon, permission: 'manage_inventory' },\n    { id: 'products', label: 'Products', icon: ProductsIcon, permission: 'manage_products' },\n    { id: 'orders', label: 'Orders', icon: OrdersIcon, permission: 'view_all_orders' },\n    { id: 'suppliers', label: 'Suppliers', icon: SuppliersIcon, permission: 'manage_suppliers' },\n    { id: 'users', label: 'Users', icon: UsersIcon, permission: 'manage_users' },\n    { id: 'analytics', label: 'Analytics', icon: AnalyticsIcon, permission: 'view_analytics' },\n    { id: 'activity-logs', label: 'Activity Logs', icon: ActivityIcon, permission: 'access_admin_dashboard' }\n  ];\n\n  // Filter menu items based on user permissions\n  const menuItems = allMenuItems.filter(item => canAccessAdminSection(item.id));\n\n  const renderContent = () => {\n    // Check if user has permission to access current tab\n    if (!canAccessAdminSection(activeTab)) {\n      return (\n        <div className=\"access-denied\">\n          <h2>Access Denied</h2>\n          <p>You don't have permission to access this section.</p>\n          <p>Your role: <strong>{user.role}</strong></p>\n        </div>\n      );\n    }\n\n    switch (activeTab) {\n      case 'overview':\n        return <DashboardOverview />;\n      case 'inventory':\n        return <InventoryManagement />;\n      case 'products':\n        return <ProductManagementNew />;\n      case 'orders':\n        return <OrderManagement />;\n      case 'suppliers':\n        return <SupplierManagement />;\n      case 'users':\n        return <UserManagement />;\n      case 'analytics':\n        return <Analytics />;\n      case 'activity-logs':\n        return <ActivityLogs />;\n      default:\n        return <DashboardOverview />;\n    }\n  };\n\n  if (!user) {\n    return (\n      <div className=\"admin-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"admin-dashboard\">\n      {/* Sidebar */}\n      <div className=\"admin-sidebar\">\n        <div className=\"admin-header\">\n          <div className=\"admin-logo\">\n            <AdminLogo size=\"small\" className=\"admin-logo-component\" />\n          </div>\n          <h2>Admin Panel</h2>\n          <div className=\"connection-status\">\n            <div className=\"status-indicator\">\n              {isConnected ? (\n                <ConnectedIcon className=\"status-icon connected\" color=\"#10B981\" />\n              ) : (\n                <DisconnectedIcon className=\"status-icon disconnected\" color=\"#EF4444\" />\n              )}\n            </div>\n            <span className=\"status-text\">\n              {isConnected ? 'Live Updates' : 'Offline'}\n            </span>\n          </div>\n          <div className=\"admin-user-info\">\n            <span className=\"user-name\">{user.firstName} {user.lastName}</span>\n            <span className=\"user-role\">{user.role}</span>\n          </div>\n        </div>\n\n        <nav className=\"admin-nav\">\n          {menuItems.map(item => {\n            const IconComponent = item.icon;\n            const isActive = activeTab === item.id;\n\n            return (\n              <button\n                key={item.id}\n                className={`nav-item ${isActive ? 'active' : ''}`}\n                onClick={() => handleTabChange(item.id)}\n                title={`${item.label} - ${item.permission}`}\n              >\n                <span className=\"nav-icon\">\n                  <IconComponent\n                    className=\"nav-icon-svg\"\n                    color={isActive ? '#F0B21B' : 'currentColor'}\n                  />\n                </span>\n                <span className=\"nav-label\">{item.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n\n        <div className=\"admin-footer\">\n          <button className=\"logout-btn\" onClick={handleLogout}>\n            <span className=\"nav-icon\">\n              <LogoutIcon className=\"nav-icon-svg\" color=\"#e74c3c\" />\n            </span>\n            <span className=\"nav-label\">Logout</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"admin-main\">\n        <div className=\"admin-content\">\n          {renderContent()}\n        </div>\n      </div>\n\n      {/* Notifications Panel */}\n      {notifications.length > 0 && (\n        <div className=\"notifications-panel\">\n          <div className=\"notifications-header\">\n            <h3>Live Updates ({notifications.length})</h3>\n            <button onClick={clearAllNotifications} className=\"clear-all-btn\">\n              Clear All\n            </button>\n          </div>\n          <div className=\"notifications-list\">\n            {notifications.slice(0, 5).map(notification => (\n              <div key={notification.id} className={`notification ${notification.type}`}>\n                <div className=\"notification-content\">\n                  <h4>{notification.title}</h4>\n                  <p>{notification.message}</p>\n                  <span className=\"notification-time\">\n                    {new Date(notification.timestamp).toLocaleTimeString()}\n                  </span>\n                </div>\n                <button\n                  onClick={() => removeNotification(notification.id)}\n                  className=\"notification-close\"\n                >\n                  ×\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAO,sBAAsB;;AAE7B;AACA,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,kBAAkB,MAAM,2CAA2C;AAC1E,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,YAAY,MAAM,qCAAqC;;AAE9D;AACA,SACEC,aAAa,EACbC,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,SAAS,EACTC,aAAa,EACbC,YAAY,EACZC,UAAU,EACVC,aAAa,EACbC,gBAAgB,QACX,yCAAyC;AAChD,OAAOC,SAAS,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAMiC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgC,IAAI;IAAEC;EAAO,CAAC,GAAG/B,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEgC,qBAAqB;IAAEC,0BAA0B;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGlC,cAAc,CAAC,CAAC;EACnG,MAAM;IAAEmC,WAAW;IAAEC,gBAAgB;IAAEC,aAAa;IAAEC,kBAAkB;IAAEC;EAAsB,CAAC,GAAGzC,YAAY,CAAC,CAAC;;EAElH;EACA,MAAM0C,kBAAkB,GAAGR,0BAA0B,CAAC,CAAC;;EAEvD;EACArC,SAAS,CAAC,MAAM;IACd,MAAM8C,YAAY,GAAG,IAAIC,eAAe,CAACd,QAAQ,CAACe,MAAM,CAAC;IACzD,MAAMC,UAAU,GAAGH,YAAY,CAACI,GAAG,CAAC,KAAK,CAAC;IAE1C,IAAID,UAAU,IAAIJ,kBAAkB,CAACM,QAAQ,CAACF,UAAU,CAAC,EAAE;MACzDlB,YAAY,CAACkB,UAAU,CAAC;IAC1B,CAAC,MAAM,IAAIJ,kBAAkB,CAACO,MAAM,GAAG,CAAC,IAAI,CAACP,kBAAkB,CAACM,QAAQ,CAACrB,SAAS,CAAC,EAAE;MACnFC,YAAY,CAACc,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACrC;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEf,SAAS,EAAEG,QAAQ,CAACe,MAAM,CAAC,CAAC;EAEpD,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBlB,MAAM,CAAC,CAAC;IACRH,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;;EAED;EACA,MAAMsB,YAAY,GAAIC,OAAO,IAAK;IAChC,OAAOnB,qBAAqB,CAACmB,OAAO,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAID,OAAO,IAAK;IACnC,IAAID,YAAY,CAACC,OAAO,CAAC,EAAE;MACzBxB,YAAY,CAACwB,OAAO,CAAC;MACrB;MACAvB,QAAQ,CAAC,cAAcuB,OAAO,EAAE,EAAE;QAAEE,OAAO,EAAE;MAAK,CAAC,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE/C,aAAa;IAAEgD,UAAU,EAAE;EAAyB,CAAC,EACjG;IAAEH,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE9C,aAAa;IAAE+C,UAAU,EAAE;EAAmB,CAAC,EAC5F;IAAEH,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE7C,YAAY;IAAE8C,UAAU,EAAE;EAAkB,CAAC,EACxF;IAAEH,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE5C,UAAU;IAAE6C,UAAU,EAAE;EAAkB,CAAC,EAClF;IAAEH,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE3C,aAAa;IAAE4C,UAAU,EAAE;EAAmB,CAAC,EAC5F;IAAEH,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE1C,SAAS;IAAE2C,UAAU,EAAE;EAAe,CAAC,EAC5E;IAAEH,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAEzC,aAAa;IAAE0C,UAAU,EAAE;EAAiB,CAAC,EAC1F;IAAEH,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAExC,YAAY;IAAEyC,UAAU,EAAE;EAAyB,CAAC,CAC1G;;EAED;EACA,MAAMC,SAAS,GAAGL,YAAY,CAACM,MAAM,CAACC,IAAI,IAAI7B,qBAAqB,CAAC6B,IAAI,CAACN,EAAE,CAAC,CAAC;EAE7E,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAI,CAAC9B,qBAAqB,CAACN,SAAS,CAAC,EAAE;MACrC,oBACEH,OAAA;QAAKwC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzC,OAAA;UAAAyC,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtB7C,OAAA;UAAAyC,QAAA,EAAG;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxD7C,OAAA;UAAAyC,QAAA,GAAG,aAAW,eAAAzC,OAAA;YAAAyC,QAAA,EAASlC,IAAI,CAACuC;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAEV;IAEA,QAAQ1C,SAAS;MACf,KAAK,UAAU;QACb,oBAAOH,OAAA,CAACrB,iBAAiB;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9B,KAAK,WAAW;QACd,oBAAO7C,OAAA,CAACpB,mBAAmB;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChC,KAAK,UAAU;QACb,oBAAO7C,OAAA,CAACnB,oBAAoB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,QAAQ;QACX,oBAAO7C,OAAA,CAAClB,eAAe;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B,KAAK,WAAW;QACd,oBAAO7C,OAAA,CAACjB,kBAAkB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,OAAO;QACV,oBAAO7C,OAAA,CAAChB,cAAc;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,WAAW;QACd,oBAAO7C,OAAA,CAACf,SAAS;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,eAAe;QAClB,oBAAO7C,OAAA,CAACd,YAAY;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB;QACE,oBAAO7C,OAAA,CAACrB,iBAAiB;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAChC;EACF,CAAC;EAED,IAAI,CAACtC,IAAI,EAAE;IACT,oBACEP,OAAA;MAAKwC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzC,OAAA;QAAKwC,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC7C,OAAA;QAAAyC,QAAA,EAAG;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEV;EAEA,oBACE7C,OAAA;IAAKwC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE9BzC,OAAA;MAAKwC,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzC,OAAA;QAAKwC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzC,OAAA;UAAKwC,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBzC,OAAA,CAACF,SAAS;YAACiD,IAAI,EAAC,OAAO;YAACP,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACN7C,OAAA;UAAAyC,QAAA,EAAI;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpB7C,OAAA;UAAKwC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzC,OAAA;YAAKwC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B5B,WAAW,gBACVb,OAAA,CAACJ,aAAa;cAAC4C,SAAS,EAAC,uBAAuB;cAACQ,KAAK,EAAC;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEnE7C,OAAA,CAACH,gBAAgB;cAAC2C,SAAS,EAAC,0BAA0B;cAACQ,KAAK,EAAC;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACzE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN7C,OAAA;YAAMwC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAC1B5B,WAAW,GAAG,cAAc,GAAG;UAAS;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7C,OAAA;UAAKwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzC,OAAA;YAAMwC,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAElC,IAAI,CAAC0C,SAAS,EAAC,GAAC,EAAC1C,IAAI,CAAC2C,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnE7C,OAAA;YAAMwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAElC,IAAI,CAACuC;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBL,SAAS,CAACe,GAAG,CAACb,IAAI,IAAI;UACrB,MAAMc,aAAa,GAAGd,IAAI,CAACJ,IAAI;UAC/B,MAAMmB,QAAQ,GAAGlD,SAAS,KAAKmC,IAAI,CAACN,EAAE;UAEtC,oBACEhC,OAAA;YAEEwC,SAAS,EAAE,YAAYa,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAClDC,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACS,IAAI,CAACN,EAAE,CAAE;YACxCuB,KAAK,EAAE,GAAGjB,IAAI,CAACL,KAAK,MAAMK,IAAI,CAACH,UAAU,EAAG;YAAAM,QAAA,gBAE5CzC,OAAA;cAAMwC,SAAS,EAAC,UAAU;cAAAC,QAAA,eACxBzC,OAAA,CAACoD,aAAa;gBACZZ,SAAS,EAAC,cAAc;gBACxBQ,KAAK,EAAEK,QAAQ,GAAG,SAAS,GAAG;cAAe;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP7C,OAAA;cAAMwC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEH,IAAI,CAACL;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAX1CP,IAAI,CAACN,EAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYN,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7C,OAAA;QAAKwC,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzC,OAAA;UAAQwC,SAAS,EAAC,YAAY;UAACc,OAAO,EAAE5B,YAAa;UAAAe,QAAA,gBACnDzC,OAAA;YAAMwC,SAAS,EAAC,UAAU;YAAAC,QAAA,eACxBzC,OAAA,CAACL,UAAU;cAAC6C,SAAS,EAAC,cAAc;cAACQ,KAAK,EAAC;YAAS;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACP7C,OAAA;YAAMwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA;MAAKwC,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBzC,OAAA;QAAKwC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BF,aAAa,CAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9B,aAAa,CAACU,MAAM,GAAG,CAAC,iBACvBzB,OAAA;MAAKwC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCzC,OAAA;QAAKwC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCzC,OAAA;UAAAyC,QAAA,GAAI,gBAAc,EAAC1B,aAAa,CAACU,MAAM,EAAC,GAAC;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9C7C,OAAA;UAAQsD,OAAO,EAAErC,qBAAsB;UAACuB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN7C,OAAA;QAAKwC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChC1B,aAAa,CAACyC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACL,GAAG,CAACM,YAAY,iBACzCzD,OAAA;UAA2BwC,SAAS,EAAE,gBAAgBiB,YAAY,CAACC,IAAI,EAAG;UAAAjB,QAAA,gBACxEzC,OAAA;YAAKwC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCzC,OAAA;cAAAyC,QAAA,EAAKgB,YAAY,CAACF;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7B7C,OAAA;cAAAyC,QAAA,EAAIgB,YAAY,CAACE;YAAO;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B7C,OAAA;cAAMwC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAChC,IAAImB,IAAI,CAACH,YAAY,CAACI,SAAS,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7C,OAAA;YACEsD,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACyC,YAAY,CAACzB,EAAE,CAAE;YACnDQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAC/B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAbDY,YAAY,CAACzB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcpB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAxMID,cAAc;EAAA,QAED3B,WAAW,EACXC,WAAW,EACHE,OAAO,EACmDC,cAAc,EACGF,YAAY;AAAA;AAAAuF,EAAA,GAN5G9D,cAAc;AA0MpB,eAAeA,cAAc;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}