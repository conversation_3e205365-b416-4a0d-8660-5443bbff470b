{"ast": null, "code": "import { FBXLoader } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\nfunction useFBX(path) {\n  return useLoader(FBXLoader, path);\n}\nuseFBX.preload = path => useLoader.preload(FBXLoader, path);\nuseFBX.clear = input => useLoader.clear(FBXLoader, input);\nexport { useFBX };", "map": {"version": 3, "names": ["FBXLoader", "useLoader", "useFBX", "path", "preload", "clear", "input"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useFBX.js"], "sourcesContent": ["import { FBXLoader } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\n\nfunction useFBX(path) {\n  return useLoader(FBXLoader, path);\n}\n\nuseFBX.preload = path => useLoader.preload(FBXLoader, path);\n\nuseFBX.clear = input => useLoader.clear(FBXLoader, input);\n\nexport { useFBX };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,SAAS,QAAQ,oBAAoB;AAE9C,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,OAAOF,SAAS,CAACD,SAAS,EAAEG,IAAI,CAAC;AACnC;AAEAD,MAAM,CAACE,OAAO,GAAGD,IAAI,IAAIF,SAAS,CAACG,OAAO,CAACJ,SAAS,EAAEG,IAAI,CAAC;AAE3DD,MAAM,CAACG,KAAK,GAAGC,KAAK,IAAIL,SAAS,CAACI,KAAK,CAACL,SAAS,EAAEM,KAAK,CAAC;AAEzD,SAASJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}