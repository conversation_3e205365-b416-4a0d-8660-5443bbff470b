{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\contexts\\\\CartContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Cart action types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CART_ACTIONS = {\n  ADD_ITEM: 'ADD_ITEM',\n  REMOVE_ITEM: 'REMOVE_ITEM',\n  UPDATE_QUANTITY: 'UPDATE_QUANTITY',\n  CLEAR_CART: 'CLEAR_CART',\n  LOAD_CART: 'LOAD_CART'\n};\n\n// Cart reducer\nconst cartReducer = (state, action) => {\n  switch (action.type) {\n    case CART_ACTIONS.ADD_ITEM:\n      {\n        const {\n          product,\n          quantity = 1,\n          customization = {}\n        } = action.payload;\n        const existingItemIndex = state.items.findIndex(item => item.product.id === product.id && JSON.stringify(item.customization) === JSON.stringify(customization));\n        if (existingItemIndex >= 0) {\n          // Update existing item quantity\n          const updatedItems = [...state.items];\n          updatedItems[existingItemIndex].quantity += quantity;\n          return {\n            ...state,\n            items: updatedItems\n          };\n        } else {\n          // Add new item\n          const newItem = {\n            id: `${product.id}-${Date.now()}`,\n            product,\n            quantity,\n            customization,\n            price: product.discountPrice || product.price\n          };\n          return {\n            ...state,\n            items: [...state.items, newItem]\n          };\n        }\n      }\n    case CART_ACTIONS.REMOVE_ITEM:\n      {\n        return {\n          ...state,\n          items: state.items.filter(item => item.id !== action.payload.itemId)\n        };\n      }\n    case CART_ACTIONS.UPDATE_QUANTITY:\n      {\n        const {\n          itemId,\n          quantity\n        } = action.payload;\n        if (quantity <= 0) {\n          return {\n            ...state,\n            items: state.items.filter(item => item.id !== itemId)\n          };\n        }\n        return {\n          ...state,\n          items: state.items.map(item => item.id === itemId ? {\n            ...item,\n            quantity\n          } : item)\n        };\n      }\n    case CART_ACTIONS.CLEAR_CART:\n      {\n        return {\n          ...state,\n          items: []\n        };\n      }\n    case CART_ACTIONS.LOAD_CART:\n      {\n        return {\n          ...state,\n          items: action.payload.items || []\n        };\n      }\n    default:\n      return state;\n  }\n};\n\n// Initial cart state\nconst initialState = {\n  items: [],\n  isOpen: false\n};\n\n// Create cart context\nconst CartContext = /*#__PURE__*/createContext();\n\n// Cart provider component\nexport const CartProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(cartReducer, initialState);\n\n  // Load cart from localStorage on mount\n  useEffect(() => {\n    const savedCart = localStorage.getItem('shopping-cart');\n    if (savedCart) {\n      try {\n        const cartData = JSON.parse(savedCart);\n        dispatch({\n          type: CART_ACTIONS.LOAD_CART,\n          payload: cartData\n        });\n      } catch (error) {\n        console.error('Error loading cart from localStorage:', error);\n      }\n    }\n  }, []);\n\n  // Save cart to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('shopping-cart', JSON.stringify(state));\n  }, [state]);\n\n  // Cart actions\n  const addToCart = (product, quantity = 1, customization = {}) => {\n    dispatch({\n      type: CART_ACTIONS.ADD_ITEM,\n      payload: {\n        product,\n        quantity,\n        customization\n      }\n    });\n  };\n  const removeFromCart = itemId => {\n    dispatch({\n      type: CART_ACTIONS.REMOVE_ITEM,\n      payload: {\n        itemId\n      }\n    });\n  };\n  const updateQuantity = (itemId, quantity) => {\n    dispatch({\n      type: CART_ACTIONS.UPDATE_QUANTITY,\n      payload: {\n        itemId,\n        quantity\n      }\n    });\n  };\n  const clearCart = () => {\n    dispatch({\n      type: CART_ACTIONS.CLEAR_CART\n    });\n  };\n\n  // Cart calculations\n  const getItemCount = () => {\n    return state.items.reduce((total, item) => total + item.quantity, 0);\n  };\n  const getSubtotal = () => {\n    return state.items.reduce((total, item) => total + item.price * item.quantity, 0);\n  };\n  const getTax = subtotal => {\n    return subtotal * 0.08; // 8% tax rate\n  };\n  const getShipping = subtotal => {\n    return subtotal > 1000 ? 0 : 50; // Free shipping over $1000\n  };\n  const getTotal = () => {\n    const subtotal = getSubtotal();\n    const tax = getTax(subtotal);\n    const shipping = getShipping(subtotal);\n    return subtotal + tax + shipping;\n  };\n  const value = {\n    // State\n    items: state.items,\n    isOpen: state.isOpen,\n    // Actions\n    addToCart,\n    removeFromCart,\n    updateQuantity,\n    clearCart,\n    // Calculations\n    getItemCount,\n    getSubtotal,\n    getTax,\n    getShipping,\n    getTotal\n  };\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 9\n  }, this);\n};\n\n// Custom hook to use cart context\n_s(CartProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = CartProvider;\nexport const useCart = () => {\n  _s2();\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n_s2(useCart, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default CartContext;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "jsxDEV", "_jsxDEV", "CART_ACTIONS", "ADD_ITEM", "REMOVE_ITEM", "UPDATE_QUANTITY", "CLEAR_CART", "LOAD_CART", "cartReducer", "state", "action", "type", "product", "quantity", "customization", "payload", "existingItemIndex", "items", "findIndex", "item", "id", "JSON", "stringify", "updatedItems", "newItem", "Date", "now", "price", "discountPrice", "filter", "itemId", "map", "initialState", "isOpen", "CartContext", "CartProvider", "children", "_s", "dispatch", "savedCart", "localStorage", "getItem", "cartData", "parse", "error", "console", "setItem", "addToCart", "removeFromCart", "updateQuantity", "clearCart", "getItemCount", "reduce", "total", "getSubtotal", "getTax", "subtotal", "getShipping", "getTotal", "tax", "shipping", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useCart", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/contexts/CartContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Cart action types\nconst CART_ACTIONS = {\n    ADD_ITEM: 'ADD_ITEM',\n    REMOVE_ITEM: 'REMOVE_ITEM',\n    UPDATE_QUANTITY: 'UPDATE_QUANTITY',\n    CLEAR_CART: 'CLEAR_CART',\n    LOAD_CART: 'LOAD_CART'\n};\n\n// Cart reducer\nconst cartReducer = (state, action) => {\n    switch (action.type) {\n        case CART_ACTIONS.ADD_ITEM: {\n            const { product, quantity = 1, customization = {} } = action.payload;\n            const existingItemIndex = state.items.findIndex(item => \n                item.product.id === product.id && \n                JSON.stringify(item.customization) === JSON.stringify(customization)\n            );\n\n            if (existingItemIndex >= 0) {\n                // Update existing item quantity\n                const updatedItems = [...state.items];\n                updatedItems[existingItemIndex].quantity += quantity;\n                return {\n                    ...state,\n                    items: updatedItems\n                };\n            } else {\n                // Add new item\n                const newItem = {\n                    id: `${product.id}-${Date.now()}`,\n                    product,\n                    quantity,\n                    customization,\n                    price: product.discountPrice || product.price\n                };\n                return {\n                    ...state,\n                    items: [...state.items, newItem]\n                };\n            }\n        }\n\n        case CART_ACTIONS.REMOVE_ITEM: {\n            return {\n                ...state,\n                items: state.items.filter(item => item.id !== action.payload.itemId)\n            };\n        }\n\n        case CART_ACTIONS.UPDATE_QUANTITY: {\n            const { itemId, quantity } = action.payload;\n            if (quantity <= 0) {\n                return {\n                    ...state,\n                    items: state.items.filter(item => item.id !== itemId)\n                };\n            }\n            return {\n                ...state,\n                items: state.items.map(item =>\n                    item.id === itemId ? { ...item, quantity } : item\n                )\n            };\n        }\n\n        case CART_ACTIONS.CLEAR_CART: {\n            return {\n                ...state,\n                items: []\n            };\n        }\n\n        case CART_ACTIONS.LOAD_CART: {\n            return {\n                ...state,\n                items: action.payload.items || []\n            };\n        }\n\n        default:\n            return state;\n    }\n};\n\n// Initial cart state\nconst initialState = {\n    items: [],\n    isOpen: false\n};\n\n// Create cart context\nconst CartContext = createContext();\n\n// Cart provider component\nexport const CartProvider = ({ children }) => {\n    const [state, dispatch] = useReducer(cartReducer, initialState);\n\n    // Load cart from localStorage on mount\n    useEffect(() => {\n        const savedCart = localStorage.getItem('shopping-cart');\n        if (savedCart) {\n            try {\n                const cartData = JSON.parse(savedCart);\n                dispatch({ type: CART_ACTIONS.LOAD_CART, payload: cartData });\n            } catch (error) {\n                console.error('Error loading cart from localStorage:', error);\n            }\n        }\n    }, []);\n\n    // Save cart to localStorage whenever it changes\n    useEffect(() => {\n        localStorage.setItem('shopping-cart', JSON.stringify(state));\n    }, [state]);\n\n    // Cart actions\n    const addToCart = (product, quantity = 1, customization = {}) => {\n        dispatch({\n            type: CART_ACTIONS.ADD_ITEM,\n            payload: { product, quantity, customization }\n        });\n    };\n\n    const removeFromCart = (itemId) => {\n        dispatch({\n            type: CART_ACTIONS.REMOVE_ITEM,\n            payload: { itemId }\n        });\n    };\n\n    const updateQuantity = (itemId, quantity) => {\n        dispatch({\n            type: CART_ACTIONS.UPDATE_QUANTITY,\n            payload: { itemId, quantity }\n        });\n    };\n\n    const clearCart = () => {\n        dispatch({ type: CART_ACTIONS.CLEAR_CART });\n    };\n\n    // Cart calculations\n    const getItemCount = () => {\n        return state.items.reduce((total, item) => total + item.quantity, 0);\n    };\n\n    const getSubtotal = () => {\n        return state.items.reduce((total, item) => total + (item.price * item.quantity), 0);\n    };\n\n    const getTax = (subtotal) => {\n        return subtotal * 0.08; // 8% tax rate\n    };\n\n    const getShipping = (subtotal) => {\n        return subtotal > 1000 ? 0 : 50; // Free shipping over $1000\n    };\n\n    const getTotal = () => {\n        const subtotal = getSubtotal();\n        const tax = getTax(subtotal);\n        const shipping = getShipping(subtotal);\n        return subtotal + tax + shipping;\n    };\n\n    const value = {\n        // State\n        items: state.items,\n        isOpen: state.isOpen,\n        \n        // Actions\n        addToCart,\n        removeFromCart,\n        updateQuantity,\n        clearCart,\n        \n        // Calculations\n        getItemCount,\n        getSubtotal,\n        getTax,\n        getShipping,\n        getTotal\n    };\n\n    return (\n        <CartContext.Provider value={value}>\n            {children}\n        </CartContext.Provider>\n    );\n};\n\n// Custom hook to use cart context\nexport const useCart = () => {\n    const context = useContext(CartContext);\n    if (!context) {\n        throw new Error('useCart must be used within a CartProvider');\n    }\n    return context;\n};\n\nexport default CartContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;;AAE/E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACjBC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,aAAa;EAC1BC,eAAe,EAAE,iBAAiB;EAClCC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACnC,QAAQA,MAAM,CAACC,IAAI;IACf,KAAKT,YAAY,CAACC,QAAQ;MAAE;QACxB,MAAM;UAAES,OAAO;UAAEC,QAAQ,GAAG,CAAC;UAAEC,aAAa,GAAG,CAAC;QAAE,CAAC,GAAGJ,MAAM,CAACK,OAAO;QACpE,MAAMC,iBAAiB,GAAGP,KAAK,CAACQ,KAAK,CAACC,SAAS,CAACC,IAAI,IAChDA,IAAI,CAACP,OAAO,CAACQ,EAAE,KAAKR,OAAO,CAACQ,EAAE,IAC9BC,IAAI,CAACC,SAAS,CAACH,IAAI,CAACL,aAAa,CAAC,KAAKO,IAAI,CAACC,SAAS,CAACR,aAAa,CACvE,CAAC;QAED,IAAIE,iBAAiB,IAAI,CAAC,EAAE;UACxB;UACA,MAAMO,YAAY,GAAG,CAAC,GAAGd,KAAK,CAACQ,KAAK,CAAC;UACrCM,YAAY,CAACP,iBAAiB,CAAC,CAACH,QAAQ,IAAIA,QAAQ;UACpD,OAAO;YACH,GAAGJ,KAAK;YACRQ,KAAK,EAAEM;UACX,CAAC;QACL,CAAC,MAAM;UACH;UACA,MAAMC,OAAO,GAAG;YACZJ,EAAE,EAAE,GAAGR,OAAO,CAACQ,EAAE,IAAIK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;YACjCd,OAAO;YACPC,QAAQ;YACRC,aAAa;YACba,KAAK,EAAEf,OAAO,CAACgB,aAAa,IAAIhB,OAAO,CAACe;UAC5C,CAAC;UACD,OAAO;YACH,GAAGlB,KAAK;YACRQ,KAAK,EAAE,CAAC,GAAGR,KAAK,CAACQ,KAAK,EAAEO,OAAO;UACnC,CAAC;QACL;MACJ;IAEA,KAAKtB,YAAY,CAACE,WAAW;MAAE;QAC3B,OAAO;UACH,GAAGK,KAAK;UACRQ,KAAK,EAAER,KAAK,CAACQ,KAAK,CAACY,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKV,MAAM,CAACK,OAAO,CAACe,MAAM;QACvE,CAAC;MACL;IAEA,KAAK5B,YAAY,CAACG,eAAe;MAAE;QAC/B,MAAM;UAAEyB,MAAM;UAAEjB;QAAS,CAAC,GAAGH,MAAM,CAACK,OAAO;QAC3C,IAAIF,QAAQ,IAAI,CAAC,EAAE;UACf,OAAO;YACH,GAAGJ,KAAK;YACRQ,KAAK,EAAER,KAAK,CAACQ,KAAK,CAACY,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKU,MAAM;UACxD,CAAC;QACL;QACA,OAAO;UACH,GAAGrB,KAAK;UACRQ,KAAK,EAAER,KAAK,CAACQ,KAAK,CAACc,GAAG,CAACZ,IAAI,IACvBA,IAAI,CAACC,EAAE,KAAKU,MAAM,GAAG;YAAE,GAAGX,IAAI;YAAEN;UAAS,CAAC,GAAGM,IACjD;QACJ,CAAC;MACL;IAEA,KAAKjB,YAAY,CAACI,UAAU;MAAE;QAC1B,OAAO;UACH,GAAGG,KAAK;UACRQ,KAAK,EAAE;QACX,CAAC;MACL;IAEA,KAAKf,YAAY,CAACK,SAAS;MAAE;QACzB,OAAO;UACH,GAAGE,KAAK;UACRQ,KAAK,EAAEP,MAAM,CAACK,OAAO,CAACE,KAAK,IAAI;QACnC,CAAC;MACL;IAEA;MACI,OAAOR,KAAK;EACpB;AACJ,CAAC;;AAED;AACA,MAAMuB,YAAY,GAAG;EACjBf,KAAK,EAAE,EAAE;EACTgB,MAAM,EAAE;AACZ,CAAC;;AAED;AACA,MAAMC,WAAW,gBAAGtC,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMuC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAAC5B,KAAK,EAAE6B,QAAQ,CAAC,GAAGxC,UAAU,CAACU,WAAW,EAAEwB,YAAY,CAAC;;EAE/D;EACAjC,SAAS,CAAC,MAAM;IACZ,MAAMwC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACvD,IAAIF,SAAS,EAAE;MACX,IAAI;QACA,MAAMG,QAAQ,GAAGrB,IAAI,CAACsB,KAAK,CAACJ,SAAS,CAAC;QACtCD,QAAQ,CAAC;UAAE3B,IAAI,EAAET,YAAY,CAACK,SAAS;UAAEQ,OAAO,EAAE2B;QAAS,CAAC,CAAC;MACjE,CAAC,CAAC,OAAOE,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MACjE;IACJ;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACZyC,YAAY,CAACM,OAAO,CAAC,eAAe,EAAEzB,IAAI,CAACC,SAAS,CAACb,KAAK,CAAC,CAAC;EAChE,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,MAAMsC,SAAS,GAAGA,CAACnC,OAAO,EAAEC,QAAQ,GAAG,CAAC,EAAEC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC7DwB,QAAQ,CAAC;MACL3B,IAAI,EAAET,YAAY,CAACC,QAAQ;MAC3BY,OAAO,EAAE;QAAEH,OAAO;QAAEC,QAAQ;QAAEC;MAAc;IAChD,CAAC,CAAC;EACN,CAAC;EAED,MAAMkC,cAAc,GAAIlB,MAAM,IAAK;IAC/BQ,QAAQ,CAAC;MACL3B,IAAI,EAAET,YAAY,CAACE,WAAW;MAC9BW,OAAO,EAAE;QAAEe;MAAO;IACtB,CAAC,CAAC;EACN,CAAC;EAED,MAAMmB,cAAc,GAAGA,CAACnB,MAAM,EAAEjB,QAAQ,KAAK;IACzCyB,QAAQ,CAAC;MACL3B,IAAI,EAAET,YAAY,CAACG,eAAe;MAClCU,OAAO,EAAE;QAAEe,MAAM;QAAEjB;MAAS;IAChC,CAAC,CAAC;EACN,CAAC;EAED,MAAMqC,SAAS,GAAGA,CAAA,KAAM;IACpBZ,QAAQ,CAAC;MAAE3B,IAAI,EAAET,YAAY,CAACI;IAAW,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM6C,YAAY,GAAGA,CAAA,KAAM;IACvB,OAAO1C,KAAK,CAACQ,KAAK,CAACmC,MAAM,CAAC,CAACC,KAAK,EAAElC,IAAI,KAAKkC,KAAK,GAAGlC,IAAI,CAACN,QAAQ,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,MAAMyC,WAAW,GAAGA,CAAA,KAAM;IACtB,OAAO7C,KAAK,CAACQ,KAAK,CAACmC,MAAM,CAAC,CAACC,KAAK,EAAElC,IAAI,KAAKkC,KAAK,GAAIlC,IAAI,CAACQ,KAAK,GAAGR,IAAI,CAACN,QAAS,EAAE,CAAC,CAAC;EACvF,CAAC;EAED,MAAM0C,MAAM,GAAIC,QAAQ,IAAK;IACzB,OAAOA,QAAQ,GAAG,IAAI,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMC,WAAW,GAAID,QAAQ,IAAK;IAC9B,OAAOA,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;EACrC,CAAC;EAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACnB,MAAMF,QAAQ,GAAGF,WAAW,CAAC,CAAC;IAC9B,MAAMK,GAAG,GAAGJ,MAAM,CAACC,QAAQ,CAAC;IAC5B,MAAMI,QAAQ,GAAGH,WAAW,CAACD,QAAQ,CAAC;IACtC,OAAOA,QAAQ,GAAGG,GAAG,GAAGC,QAAQ;EACpC,CAAC;EAED,MAAMC,KAAK,GAAG;IACV;IACA5C,KAAK,EAAER,KAAK,CAACQ,KAAK;IAClBgB,MAAM,EAAExB,KAAK,CAACwB,MAAM;IAEpB;IACAc,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,SAAS;IAET;IACAC,YAAY;IACZG,WAAW;IACXC,MAAM;IACNE,WAAW;IACXC;EACJ,CAAC;EAED,oBACIzD,OAAA,CAACiC,WAAW,CAAC4B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzB,QAAA,EAC9BA;EAAQ;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAE/B,CAAC;;AAED;AAAA7B,EAAA,CAjGaF,YAAY;AAAAgC,EAAA,GAAZhC,YAAY;AAkGzB,OAAO,MAAMiC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzB,MAAMC,OAAO,GAAGzE,UAAU,CAACqC,WAAW,CAAC;EACvC,IAAI,CAACoC,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EACjE;EACA,OAAOD,OAAO;AAClB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAelC,WAAW;AAAC,IAAAiC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}