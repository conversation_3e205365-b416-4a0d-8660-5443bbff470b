{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Vector2, WebGLRenderTarget, NoBlending, Clock, LinearFilter, RGBAFormat } from \"three\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nimport { ShaderPass } from \"./ShaderPass.js\";\nimport { MaskPass, ClearMaskPass } from \"./MaskPass.js\";\nclass EffectComposer {\n  constructor(renderer, renderTarget) {\n    __publicField(this, \"renderer\");\n    __publicField(this, \"_pixelRatio\");\n    __publicField(this, \"_width\");\n    __publicField(this, \"_height\");\n    __publicField(this, \"renderTarget1\");\n    __publicField(this, \"renderTarget2\");\n    __publicField(this, \"writeBuffer\");\n    __publicField(this, \"readBuffer\");\n    __publicField(this, \"renderToScreen\");\n    __publicField(this, \"passes\", []);\n    __publicField(this, \"copyPass\");\n    __publicField(this, \"clock\");\n    this.renderer = renderer;\n    if (renderTarget === void 0) {\n      const parameters = {\n        minFilter: LinearFilter,\n        magFilter: LinearFilter,\n        format: RGBAFormat\n      };\n      const size = renderer.getSize(new Vector2());\n      this._pixelRatio = renderer.getPixelRatio();\n      this._width = size.width;\n      this._height = size.height;\n      renderTarget = new WebGLRenderTarget(this._width * this._pixelRatio, this._height * this._pixelRatio, parameters);\n      renderTarget.texture.name = \"EffectComposer.rt1\";\n    } else {\n      this._pixelRatio = 1;\n      this._width = renderTarget.width;\n      this._height = renderTarget.height;\n    }\n    this.renderTarget1 = renderTarget;\n    this.renderTarget2 = renderTarget.clone();\n    this.renderTarget2.texture.name = \"EffectComposer.rt2\";\n    this.writeBuffer = this.renderTarget1;\n    this.readBuffer = this.renderTarget2;\n    this.renderToScreen = true;\n    if (CopyShader === void 0) {\n      console.error(\"THREE.EffectComposer relies on CopyShader\");\n    }\n    if (ShaderPass === void 0) {\n      console.error(\"THREE.EffectComposer relies on ShaderPass\");\n    }\n    this.copyPass = new ShaderPass(CopyShader);\n    this.copyPass.material.blending = NoBlending;\n    this.clock = new Clock();\n  }\n  swapBuffers() {\n    const tmp = this.readBuffer;\n    this.readBuffer = this.writeBuffer;\n    this.writeBuffer = tmp;\n  }\n  addPass(pass) {\n    this.passes.push(pass);\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio);\n  }\n  insertPass(pass, index) {\n    this.passes.splice(index, 0, pass);\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio);\n  }\n  removePass(pass) {\n    const index = this.passes.indexOf(pass);\n    if (index !== -1) {\n      this.passes.splice(index, 1);\n    }\n  }\n  isLastEnabledPass(passIndex) {\n    for (let i = passIndex + 1; i < this.passes.length; i++) {\n      if (this.passes[i].enabled) {\n        return false;\n      }\n    }\n    return true;\n  }\n  render(deltaTime) {\n    if (deltaTime === void 0) {\n      deltaTime = this.clock.getDelta();\n    }\n    const currentRenderTarget = this.renderer.getRenderTarget();\n    let maskActive = false;\n    const il = this.passes.length;\n    for (let i = 0; i < il; i++) {\n      const pass = this.passes[i];\n      if (pass.enabled === false) continue;\n      pass.renderToScreen = this.renderToScreen && this.isLastEnabledPass(i);\n      pass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime, maskActive);\n      if (pass.needsSwap) {\n        if (maskActive) {\n          const context = this.renderer.getContext();\n          const stencil = this.renderer.state.buffers.stencil;\n          stencil.setFunc(context.NOTEQUAL, 1, 4294967295);\n          this.copyPass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime);\n          stencil.setFunc(context.EQUAL, 1, 4294967295);\n        }\n        this.swapBuffers();\n      }\n      if (MaskPass !== void 0) {\n        if (pass instanceof MaskPass) {\n          maskActive = true;\n        } else if (pass instanceof ClearMaskPass) {\n          maskActive = false;\n        }\n      }\n    }\n    this.renderer.setRenderTarget(currentRenderTarget);\n  }\n  reset(renderTarget) {\n    if (renderTarget === void 0) {\n      const size = this.renderer.getSize(new Vector2());\n      this._pixelRatio = this.renderer.getPixelRatio();\n      this._width = size.width;\n      this._height = size.height;\n      renderTarget = this.renderTarget1.clone();\n      renderTarget.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio);\n    }\n    this.renderTarget1.dispose();\n    this.renderTarget2.dispose();\n    this.renderTarget1 = renderTarget;\n    this.renderTarget2 = renderTarget.clone();\n    this.writeBuffer = this.renderTarget1;\n    this.readBuffer = this.renderTarget2;\n  }\n  setSize(width, height) {\n    this._width = width;\n    this._height = height;\n    const effectiveWidth = this._width * this._pixelRatio;\n    const effectiveHeight = this._height * this._pixelRatio;\n    this.renderTarget1.setSize(effectiveWidth, effectiveHeight);\n    this.renderTarget2.setSize(effectiveWidth, effectiveHeight);\n    for (let i = 0; i < this.passes.length; i++) {\n      this.passes[i].setSize(effectiveWidth, effectiveHeight);\n    }\n  }\n  setPixelRatio(pixelRatio) {\n    this._pixelRatio = pixelRatio;\n    this.setSize(this._width, this._height);\n  }\n  dispose() {\n    this.renderTarget1.dispose();\n    this.renderTarget2.dispose();\n    this.copyPass.dispose();\n  }\n}\nexport { EffectComposer };", "map": {"version": 3, "names": ["EffectComposer", "constructor", "renderer", "renderTarget", "__publicField", "parameters", "minFilter", "LinearFilter", "magFilter", "format", "RGBAFormat", "size", "getSize", "Vector2", "_pixelRatio", "getPixelRatio", "_width", "width", "_height", "height", "WebGLRenderTarget", "texture", "name", "renderTarget1", "renderTarget2", "clone", "writeBuffer", "readBuffer", "renderToScreen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "error", "<PERSON><PERSON><PERSON><PERSON>", "copyPass", "material", "blending", "NoBlending", "clock", "Clock", "swapBuffers", "tmp", "addPass", "pass", "passes", "push", "setSize", "insertPass", "index", "splice", "removePass", "indexOf", "isLastEnabledPass", "passIndex", "i", "length", "enabled", "render", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "maskActive", "il", "needsSwap", "context", "getContext", "stencil", "state", "buffers", "setFunc", "NOTEQUAL", "EQUAL", "<PERSON><PERSON><PERSON>", "ClearMaskPass", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "dispose", "effectiveWidth", "effectiveHeight", "setPixelRatio", "pixelRatio"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\EffectComposer.ts"], "sourcesContent": ["import { Clock, LinearFilter, RGBAFormat, NoBlending, Vector2, WebG<PERSON>enderer, WebGLRenderTarget } from 'three'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { ShaderPass } from './ShaderPass'\nimport { MaskPass, ClearMaskPass } from './MaskPass'\nimport { Pass } from './Pass'\n\nclass EffectComposer<TRenderTarget extends WebGLRenderTarget = WebGLRenderTarget> {\n  public renderer: WebGLRenderer\n  private _pixelRatio: number\n  private _width: number\n  private _height: number\n  public renderTarget1: WebGLRenderTarget\n  public renderTarget2: WebGLRenderTarget\n  public writeBuffer: WebGLRenderTarget\n  public readBuffer: WebGLRenderTarget\n  public renderToScreen: boolean\n  public passes: Pass[] = []\n  public copyPass: Pass\n  public clock: Clock\n\n  constructor(renderer: WebGLRenderer, renderTarget?: TRenderTarget) {\n    this.renderer = renderer\n\n    if (renderTarget === undefined) {\n      const parameters = {\n        minFilter: LinearFilter,\n        magFilter: LinearFilter,\n        format: RGBAFormat,\n      }\n\n      const size = renderer.getSize(new Vector2())\n      this._pixelRatio = renderer.getPixelRatio()\n      this._width = size.width\n      this._height = size.height\n\n      renderTarget = new WebGLRenderTarget(\n        this._width * this._pixelRatio,\n        this._height * this._pixelRatio,\n        parameters,\n      ) as TRenderTarget\n      renderTarget.texture.name = 'EffectComposer.rt1'\n    } else {\n      this._pixelRatio = 1\n      this._width = renderTarget.width\n      this._height = renderTarget.height\n    }\n\n    this.renderTarget1 = renderTarget\n    this.renderTarget2 = renderTarget.clone()\n    this.renderTarget2.texture.name = 'EffectComposer.rt2'\n\n    this.writeBuffer = this.renderTarget1\n    this.readBuffer = this.renderTarget2\n\n    this.renderToScreen = true\n\n    // dependencies\n\n    if (CopyShader === undefined) {\n      console.error('THREE.EffectComposer relies on CopyShader')\n    }\n\n    if (ShaderPass === undefined) {\n      console.error('THREE.EffectComposer relies on ShaderPass')\n    }\n\n    this.copyPass = new ShaderPass(CopyShader)\n    // @ts-ignore\n    this.copyPass.material.blending = NoBlending\n\n    this.clock = new Clock()\n  }\n\n  public swapBuffers(): void {\n    const tmp = this.readBuffer\n    this.readBuffer = this.writeBuffer\n    this.writeBuffer = tmp\n  }\n\n  public addPass(pass: Pass): void {\n    this.passes.push(pass)\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n  }\n\n  public insertPass(pass: Pass, index: number): void {\n    this.passes.splice(index, 0, pass)\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n  }\n\n  public removePass(pass: Pass): void {\n    const index = this.passes.indexOf(pass)\n\n    if (index !== -1) {\n      this.passes.splice(index, 1)\n    }\n  }\n\n  public isLastEnabledPass(passIndex: number): boolean {\n    for (let i = passIndex + 1; i < this.passes.length; i++) {\n      if (this.passes[i].enabled) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  public render(deltaTime?: number): void {\n    // deltaTime value is in seconds\n\n    if (deltaTime === undefined) {\n      deltaTime = this.clock.getDelta()\n    }\n\n    const currentRenderTarget = this.renderer.getRenderTarget()\n\n    let maskActive = false\n\n    const il = this.passes.length\n\n    for (let i = 0; i < il; i++) {\n      const pass = this.passes[i]\n\n      if (pass.enabled === false) continue\n\n      pass.renderToScreen = this.renderToScreen && this.isLastEnabledPass(i)\n      pass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime, maskActive)\n\n      if (pass.needsSwap) {\n        if (maskActive) {\n          const context = this.renderer.getContext()\n          const stencil = this.renderer.state.buffers.stencil\n\n          //context.stencilFunc( context.NOTEQUAL, 1, 0xffffffff );\n          stencil.setFunc(context.NOTEQUAL, 1, 0xffffffff)\n\n          this.copyPass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime)\n\n          //context.stencilFunc( context.EQUAL, 1, 0xffffffff );\n          stencil.setFunc(context.EQUAL, 1, 0xffffffff)\n        }\n\n        this.swapBuffers()\n      }\n\n      if (MaskPass !== undefined) {\n        if (pass instanceof MaskPass) {\n          maskActive = true\n        } else if (pass instanceof ClearMaskPass) {\n          maskActive = false\n        }\n      }\n    }\n\n    this.renderer.setRenderTarget(currentRenderTarget)\n  }\n\n  public reset(renderTarget: WebGLRenderTarget): void {\n    if (renderTarget === undefined) {\n      const size = this.renderer.getSize(new Vector2())\n      this._pixelRatio = this.renderer.getPixelRatio()\n      this._width = size.width\n      this._height = size.height\n\n      renderTarget = this.renderTarget1.clone()\n      renderTarget.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n    }\n\n    this.renderTarget1.dispose()\n    this.renderTarget2.dispose()\n    this.renderTarget1 = renderTarget\n    this.renderTarget2 = renderTarget.clone()\n\n    this.writeBuffer = this.renderTarget1\n    this.readBuffer = this.renderTarget2\n  }\n\n  public setSize(width: number, height: number): void {\n    this._width = width\n    this._height = height\n\n    const effectiveWidth = this._width * this._pixelRatio\n    const effectiveHeight = this._height * this._pixelRatio\n\n    this.renderTarget1.setSize(effectiveWidth, effectiveHeight)\n    this.renderTarget2.setSize(effectiveWidth, effectiveHeight)\n\n    for (let i = 0; i < this.passes.length; i++) {\n      this.passes[i].setSize(effectiveWidth, effectiveHeight)\n    }\n  }\n\n  public setPixelRatio(pixelRatio: number): void {\n    this._pixelRatio = pixelRatio\n\n    this.setSize(this._width, this._height)\n  }\n\n  public dispose() {\n    this.renderTarget1.dispose()\n    this.renderTarget2.dispose()\n\n    this.copyPass.dispose()\n  }\n}\n\nexport { EffectComposer }\n"], "mappings": ";;;;;;;;;;;;;;;AAMA,MAAMA,cAAA,CAA4E;EAchFC,YAAYC,QAAA,EAAyBC,YAAA,EAA8B;IAb5DC,aAAA;IACCA,aAAA;IACAA,aAAA;IACAA,aAAA;IACDA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA,iBAAiB;IACjBA,aAAA;IACAA,aAAA;IAGL,KAAKF,QAAA,GAAWA,QAAA;IAEhB,IAAIC,YAAA,KAAiB,QAAW;MAC9B,MAAME,UAAA,GAAa;QACjBC,SAAA,EAAWC,YAAA;QACXC,SAAA,EAAWD,YAAA;QACXE,MAAA,EAAQC;MAAA;MAGV,MAAMC,IAAA,GAAOT,QAAA,CAASU,OAAA,CAAQ,IAAIC,OAAA,CAAS;MACtC,KAAAC,WAAA,GAAcZ,QAAA,CAASa,aAAA;MAC5B,KAAKC,MAAA,GAASL,IAAA,CAAKM,KAAA;MACnB,KAAKC,OAAA,GAAUP,IAAA,CAAKQ,MAAA;MAEpBhB,YAAA,GAAe,IAAIiB,iBAAA,CACjB,KAAKJ,MAAA,GAAS,KAAKF,WAAA,EACnB,KAAKI,OAAA,GAAU,KAAKJ,WAAA,EACpBT,UAAA;MAEFF,YAAA,CAAakB,OAAA,CAAQC,IAAA,GAAO;IAAA,OACvB;MACL,KAAKR,WAAA,GAAc;MACnB,KAAKE,MAAA,GAASb,YAAA,CAAac,KAAA;MAC3B,KAAKC,OAAA,GAAUf,YAAA,CAAagB,MAAA;IAC9B;IAEA,KAAKI,aAAA,GAAgBpB,YAAA;IAChB,KAAAqB,aAAA,GAAgBrB,YAAA,CAAasB,KAAA;IAC7B,KAAAD,aAAA,CAAcH,OAAA,CAAQC,IAAA,GAAO;IAElC,KAAKI,WAAA,GAAc,KAAKH,aAAA;IACxB,KAAKI,UAAA,GAAa,KAAKH,aAAA;IAEvB,KAAKI,cAAA,GAAiB;IAItB,IAAIC,UAAA,KAAe,QAAW;MAC5BC,OAAA,CAAQC,KAAA,CAAM,2CAA2C;IAC3D;IAEA,IAAIC,UAAA,KAAe,QAAW;MAC5BF,OAAA,CAAQC,KAAA,CAAM,2CAA2C;IAC3D;IAEK,KAAAE,QAAA,GAAW,IAAID,UAAA,CAAWH,UAAU;IAEpC,KAAAI,QAAA,CAASC,QAAA,CAASC,QAAA,GAAWC,UAAA;IAE7B,KAAAC,KAAA,GAAQ,IAAIC,KAAA;EACnB;EAEOC,YAAA,EAAoB;IACzB,MAAMC,GAAA,GAAM,KAAKb,UAAA;IACjB,KAAKA,UAAA,GAAa,KAAKD,WAAA;IACvB,KAAKA,WAAA,GAAcc,GAAA;EACrB;EAEOC,QAAQC,IAAA,EAAkB;IAC1B,KAAAC,MAAA,CAAOC,IAAA,CAAKF,IAAI;IAChBA,IAAA,CAAAG,OAAA,CAAQ,KAAK7B,MAAA,GAAS,KAAKF,WAAA,EAAa,KAAKI,OAAA,GAAU,KAAKJ,WAAW;EAC9E;EAEOgC,WAAWJ,IAAA,EAAYK,KAAA,EAAqB;IACjD,KAAKJ,MAAA,CAAOK,MAAA,CAAOD,KAAA,EAAO,GAAGL,IAAI;IAC5BA,IAAA,CAAAG,OAAA,CAAQ,KAAK7B,MAAA,GAAS,KAAKF,WAAA,EAAa,KAAKI,OAAA,GAAU,KAAKJ,WAAW;EAC9E;EAEOmC,WAAWP,IAAA,EAAkB;IAClC,MAAMK,KAAA,GAAQ,KAAKJ,MAAA,CAAOO,OAAA,CAAQR,IAAI;IAEtC,IAAIK,KAAA,KAAU,IAAI;MACX,KAAAJ,MAAA,CAAOK,MAAA,CAAOD,KAAA,EAAO,CAAC;IAC7B;EACF;EAEOI,kBAAkBC,SAAA,EAA4B;IACnD,SAASC,CAAA,GAAID,SAAA,GAAY,GAAGC,CAAA,GAAI,KAAKV,MAAA,CAAOW,MAAA,EAAQD,CAAA,IAAK;MACvD,IAAI,KAAKV,MAAA,CAAOU,CAAC,EAAEE,OAAA,EAAS;QACnB;MACT;IACF;IAEO;EACT;EAEOC,OAAOC,SAAA,EAA0B;IAGtC,IAAIA,SAAA,KAAc,QAAW;MACfA,SAAA,QAAKpB,KAAA,CAAMqB,QAAA;IACzB;IAEM,MAAAC,mBAAA,GAAsB,KAAKzD,QAAA,CAAS0D,eAAA,CAAgB;IAE1D,IAAIC,UAAA,GAAa;IAEX,MAAAC,EAAA,GAAK,KAAKnB,MAAA,CAAOW,MAAA;IAEvB,SAASD,CAAA,GAAI,GAAGA,CAAA,GAAIS,EAAA,EAAIT,CAAA,IAAK;MACrB,MAAAX,IAAA,GAAO,KAAKC,MAAA,CAAOU,CAAC;MAE1B,IAAIX,IAAA,CAAKa,OAAA,KAAY,OAAO;MAE5Bb,IAAA,CAAKd,cAAA,GAAiB,KAAKA,cAAA,IAAkB,KAAKuB,iBAAA,CAAkBE,CAAC;MAChEX,IAAA,CAAAc,MAAA,CAAO,KAAKtD,QAAA,EAAU,KAAKwB,WAAA,EAAa,KAAKC,UAAA,EAAY8B,SAAA,EAAWI,UAAU;MAEnF,IAAInB,IAAA,CAAKqB,SAAA,EAAW;QAClB,IAAIF,UAAA,EAAY;UACR,MAAAG,OAAA,GAAU,KAAK9D,QAAA,CAAS+D,UAAA,CAAW;UACzC,MAAMC,OAAA,GAAU,KAAKhE,QAAA,CAASiE,KAAA,CAAMC,OAAA,CAAQF,OAAA;UAG5CA,OAAA,CAAQG,OAAA,CAAQL,OAAA,CAAQM,QAAA,EAAU,GAAG,UAAU;UAE1C,KAAArC,QAAA,CAASuB,MAAA,CAAO,KAAKtD,QAAA,EAAU,KAAKwB,WAAA,EAAa,KAAKC,UAAA,EAAY8B,SAAS;UAGhFS,OAAA,CAAQG,OAAA,CAAQL,OAAA,CAAQO,KAAA,EAAO,GAAG,UAAU;QAC9C;QAEA,KAAKhC,WAAA,CAAY;MACnB;MAEA,IAAIiC,QAAA,KAAa,QAAW;QAC1B,IAAI9B,IAAA,YAAgB8B,QAAA,EAAU;UACfX,UAAA;QAAA,WACJnB,IAAA,YAAgB+B,aAAA,EAAe;UAC3BZ,UAAA;QACf;MACF;IACF;IAEK,KAAA3D,QAAA,CAASwE,eAAA,CAAgBf,mBAAmB;EACnD;EAEOgB,MAAMxE,YAAA,EAAuC;IAClD,IAAIA,YAAA,KAAiB,QAAW;MAC9B,MAAMQ,IAAA,GAAO,KAAKT,QAAA,CAASU,OAAA,CAAQ,IAAIC,OAAA,EAAS;MAC3C,KAAAC,WAAA,GAAc,KAAKZ,QAAA,CAASa,aAAA,CAAc;MAC/C,KAAKC,MAAA,GAASL,IAAA,CAAKM,KAAA;MACnB,KAAKC,OAAA,GAAUP,IAAA,CAAKQ,MAAA;MAELhB,YAAA,QAAKoB,aAAA,CAAcE,KAAA;MACrBtB,YAAA,CAAA0C,OAAA,CAAQ,KAAK7B,MAAA,GAAS,KAAKF,WAAA,EAAa,KAAKI,OAAA,GAAU,KAAKJ,WAAW;IACtF;IAEA,KAAKS,aAAA,CAAcqD,OAAA;IACnB,KAAKpD,aAAA,CAAcoD,OAAA;IACnB,KAAKrD,aAAA,GAAgBpB,YAAA;IAChB,KAAAqB,aAAA,GAAgBrB,YAAA,CAAasB,KAAA;IAElC,KAAKC,WAAA,GAAc,KAAKH,aAAA;IACxB,KAAKI,UAAA,GAAa,KAAKH,aAAA;EACzB;EAEOqB,QAAQ5B,KAAA,EAAeE,MAAA,EAAsB;IAClD,KAAKH,MAAA,GAASC,KAAA;IACd,KAAKC,OAAA,GAAUC,MAAA;IAET,MAAA0D,cAAA,GAAiB,KAAK7D,MAAA,GAAS,KAAKF,WAAA;IACpC,MAAAgE,eAAA,GAAkB,KAAK5D,OAAA,GAAU,KAAKJ,WAAA;IAEvC,KAAAS,aAAA,CAAcsB,OAAA,CAAQgC,cAAA,EAAgBC,eAAe;IACrD,KAAAtD,aAAA,CAAcqB,OAAA,CAAQgC,cAAA,EAAgBC,eAAe;IAE1D,SAASzB,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKV,MAAA,CAAOW,MAAA,EAAQD,CAAA,IAAK;MAC3C,KAAKV,MAAA,CAAOU,CAAC,EAAER,OAAA,CAAQgC,cAAA,EAAgBC,eAAe;IACxD;EACF;EAEOC,cAAcC,UAAA,EAA0B;IAC7C,KAAKlE,WAAA,GAAckE,UAAA;IAEnB,KAAKnC,OAAA,CAAQ,KAAK7B,MAAA,EAAQ,KAAKE,OAAO;EACxC;EAEO0D,QAAA,EAAU;IACf,KAAKrD,aAAA,CAAcqD,OAAA;IACnB,KAAKpD,aAAA,CAAcoD,OAAA;IAEnB,KAAK3C,QAAA,CAAS2C,OAAA;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}