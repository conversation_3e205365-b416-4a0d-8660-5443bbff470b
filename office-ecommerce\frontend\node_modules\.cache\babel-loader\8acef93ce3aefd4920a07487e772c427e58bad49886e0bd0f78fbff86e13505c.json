{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\components\\\\ThreeJSPreview.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useRef, useEffect, useState, Suspense } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { OrbitControls, ContactShadows, useGLTF } from '@react-three/drei';\nimport * as THREE from 'three';\nimport './ThreeJSPreview.css';\n\n// Loading component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"preview-loading\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-spinner\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Loading 3D model...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 9,\n  columnNumber: 3\n}, this);\n\n// Error component\n_c = LoadingSpinner;\nconst ErrorDisplay = ({\n  error\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"preview-error\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-icon\",\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n    children: \"Failed to load 3D model\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    children: error.message || 'Unknown error occurred'\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 17,\n  columnNumber: 3\n}, this);\n\n// Model component for file-based loading\n_c2 = ErrorDisplay;\nconst Model = ({\n  modelFile,\n  onLoad,\n  onError\n}) => {\n  _s();\n  const meshRef = useRef();\n  const [modelUrl, setModelUrl] = useState(null);\n  useEffect(() => {\n    if (!modelFile) return;\n\n    // Create object URL from file\n    const url = URL.createObjectURL(modelFile);\n    setModelUrl(url);\n    return () => {\n      URL.revokeObjectURL(url);\n      setModelUrl(null);\n    };\n  }, [modelFile]);\n  useFrame(state => {\n    if (meshRef.current) {\n      // Gentle rotation animation\n      meshRef.current.rotation.y += 0.005;\n    }\n  });\n  if (!modelUrl) return null;\n  return /*#__PURE__*/_jsxDEV(ModelLoader, {\n    url: modelUrl,\n    meshRef: meshRef,\n    onLoad: onLoad,\n    onError: onError\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 10\n  }, this);\n};\n\n// Separate component for GLTF loading\n_s(Model, \"ihaw9Ci3uCelEjMoP0lDqAstyPc=\", false, function () {\n  return [useFrame];\n});\n_c3 = Model;\nconst ModelLoader = ({\n  url,\n  meshRef,\n  onLoad,\n  onError\n}) => {\n  _s2();\n  try {\n    const {\n      scene\n    } = useGLTF(url);\n    useEffect(() => {\n      if (scene && onLoad) {\n        onLoad({\n          scene\n        });\n      }\n    }, [scene, onLoad]);\n    return /*#__PURE__*/_jsxDEV(\"primitive\", {\n      ref: meshRef,\n      object: scene.clone(),\n      scale: 1,\n      position: [0, 0, 0]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this);\n  } catch (error) {\n    useEffect(() => {\n      if (onError) {\n        onError(error);\n      }\n    }, [error, onError]);\n    return null;\n  }\n};\n\n// Camera controller\n_s2(ModelLoader, \"YISrnhKGyFrTU2KpQC2gL94815M=\", false, function () {\n  return [useGLTF];\n});\n_c4 = ModelLoader;\nconst CameraController = () => {\n  _s3();\n  useFrame(state => {\n    // Smooth camera movement\n    state.camera.lookAt(0, 0, 0);\n  });\n  return null;\n};\n_s3(CameraController, \"xC67171NPRcCAzsbrenetil66NI=\", false, function () {\n  return [useFrame];\n});\n_c5 = CameraController;\nconst ThreeJSPreview = ({\n  modelFile,\n  className = ''\n}) => {\n  _s4();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [modelInfo, setModelInfo] = useState(null);\n  const handleModelLoad = gltf => {\n    setLoading(false);\n    setError(null);\n\n    // Extract model information\n    const scene = gltf.scene;\n    const animations = gltf.animations;\n\n    // Calculate bounding box\n    const box = new THREE.Box3().setFromObject(scene);\n    const size = box.getSize(new THREE.Vector3());\n    setModelInfo({\n      triangles: 0,\n      // Would need to traverse geometry to count\n      materials: scene.traverse(child => {\n        if (child.isMesh) return child.material;\n      }),\n      animations: animations.length,\n      size: {\n        width: size.x.toFixed(2),\n        height: size.y.toFixed(2),\n        depth: size.z.toFixed(2)\n      }\n    });\n  };\n  const handleModelError = error => {\n    setLoading(false);\n    setError(error);\n  };\n  if (!modelFile) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `threejs-preview ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-placeholder\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"placeholder-icon\",\n          children: \"\\uD83C\\uDFAF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No 3D Model Selected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Upload a GLB or GLTF file to see the 3D preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `threejs-preview ${className}`,\n      children: /*#__PURE__*/_jsxDEV(ErrorDisplay, {\n        error: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `threejs-preview ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-container\",\n      children: [loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Canvas, {\n        camera: {\n          position: [5, 5, 5],\n          fov: 50\n        },\n        style: {\n          background: 'linear-gradient(135deg, #f8f9fa 0%, #F0B21B20 100%)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Suspense, {\n          fallback: null,\n          children: [/*#__PURE__*/_jsxDEV(\"ambientLight\", {\n            intensity: 0.6\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"directionalLight\", {\n            position: [10, 10, 5],\n            intensity: 1.2,\n            castShadow: true,\n            \"shadow-mapSize-width\": 2048,\n            \"shadow-mapSize-height\": 2048\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"directionalLight\", {\n            position: [-10, 5, -5],\n            intensity: 0.5\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"pointLight\", {\n            position: [-10, -10, -10],\n            intensity: 0.4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"pointLight\", {\n            position: [10, 10, 10],\n            intensity: 0.3,\n            color: \"#F0B21B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Model, {\n            modelFile: modelFile,\n            onLoad: handleModelLoad,\n            onError: handleModelError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ContactShadows, {\n            position: [0, -1, 0],\n            opacity: 0.4,\n            scale: 10,\n            blur: 2,\n            far: 4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(OrbitControls, {\n            enablePan: true,\n            enableZoom: true,\n            enableRotate: true,\n            minDistance: 2,\n            maxDistance: 20,\n            autoRotate: false,\n            autoRotateSpeed: 0.5\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CameraController, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), modelInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"model-info-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Model Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"File:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: modelFile.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"Size:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: [(modelFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"Dimensions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: [modelInfo.size.width, \" \\xD7 \", modelInfo.size.height, \" \\xD7 \", modelInfo.size.depth]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"Animations:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: modelInfo.animations\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-controls\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"control-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"control-label\",\n          children: \"Controls:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"control-hints\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDDB1\\uFE0F Drag to rotate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD0D Scroll to zoom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2328\\uFE0F Right-click + drag to pan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s4(ThreeJSPreview, \"iuUVogmQrFDrONtqXWJt7C5UxYE=\");\n_c6 = ThreeJSPreview;\nexport default ThreeJSPreview;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"LoadingSpinner\");\n$RefreshReg$(_c2, \"ErrorDisplay\");\n$RefreshReg$(_c3, \"Model\");\n$RefreshReg$(_c4, \"ModelLoader\");\n$RefreshReg$(_c5, \"CameraController\");\n$RefreshReg$(_c6, \"ThreeJSPreview\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "Suspense", "<PERSON><PERSON>", "useFrame", "OrbitControls", "ContactShadows", "useGLTF", "THREE", "jsxDEV", "_jsxDEV", "LoadingSpinner", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ErrorDisplay", "error", "message", "_c2", "Model", "modelFile", "onLoad", "onError", "_s", "meshRef", "modelUrl", "setModelUrl", "url", "URL", "createObjectURL", "revokeObjectURL", "state", "current", "rotation", "y", "<PERSON><PERSON><PERSON><PERSON>", "_c3", "_s2", "scene", "ref", "object", "clone", "scale", "position", "_c4", "CameraController", "_s3", "camera", "lookAt", "_c5", "ThreeJSPreview", "_s4", "loading", "setLoading", "setError", "modelInfo", "setModelInfo", "handleModelLoad", "gltf", "animations", "box", "Box3", "setFromObject", "size", "getSize", "Vector3", "triangles", "materials", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "length", "width", "x", "toFixed", "height", "depth", "z", "handleModelError", "fov", "style", "background", "fallback", "intensity", "<PERSON><PERSON><PERSON><PERSON>", "color", "opacity", "blur", "far", "enablePan", "enableZoom", "enableRotate", "minDistance", "maxDistance", "autoRotate", "autoRotateSpeed", "name", "_c6", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/components/ThreeJSPreview.js"], "sourcesContent": ["import React, { useRef, useEffect, useState, Suspense } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { OrbitControls, ContactShadows, useGLTF } from '@react-three/drei';\nimport * as THREE from 'three';\nimport './ThreeJSPreview.css';\n\n// Loading component\nconst LoadingSpinner = () => (\n  <div className=\"preview-loading\">\n    <div className=\"loading-spinner\"></div>\n    <p>Loading 3D model...</p>\n  </div>\n);\n\n// Error component\nconst ErrorDisplay = ({ error }) => (\n  <div className=\"preview-error\">\n    <div className=\"error-icon\">⚠️</div>\n    <h3>Failed to load 3D model</h3>\n    <p>{error.message || 'Unknown error occurred'}</p>\n  </div>\n);\n\n// Model component for file-based loading\nconst Model = ({ modelFile, onLoad, onError }) => {\n  const meshRef = useRef();\n  const [modelUrl, setModelUrl] = useState(null);\n\n  useEffect(() => {\n    if (!modelFile) return;\n\n    // Create object URL from file\n    const url = URL.createObjectURL(modelFile);\n    setModelUrl(url);\n\n    return () => {\n      URL.revokeObjectURL(url);\n      setModelUrl(null);\n    };\n  }, [modelFile]);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      // Gentle rotation animation\n      meshRef.current.rotation.y += 0.005;\n    }\n  });\n\n  if (!modelUrl) return null;\n\n  return <ModelLoader url={modelUrl} meshRef={meshRef} onLoad={onLoad} onError={onError} />;\n};\n\n// Separate component for GLTF loading\nconst ModelLoader = ({ url, meshRef, onLoad, onError }) => {\n  try {\n    const { scene } = useGLTF(url);\n\n    useEffect(() => {\n      if (scene && onLoad) {\n        onLoad({ scene });\n      }\n    }, [scene, onLoad]);\n\n    return (\n      <primitive\n        ref={meshRef}\n        object={scene.clone()}\n        scale={1}\n        position={[0, 0, 0]}\n      />\n    );\n  } catch (error) {\n    useEffect(() => {\n      if (onError) {\n        onError(error);\n      }\n    }, [error, onError]);\n\n    return null;\n  }\n};\n\n// Camera controller\nconst CameraController = () => {\n  useFrame((state) => {\n    // Smooth camera movement\n    state.camera.lookAt(0, 0, 0);\n  });\n  return null;\n};\n\nconst ThreeJSPreview = ({ modelFile, className = '' }) => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [modelInfo, setModelInfo] = useState(null);\n\n  const handleModelLoad = (gltf) => {\n    setLoading(false);\n    setError(null);\n    \n    // Extract model information\n    const scene = gltf.scene;\n    const animations = gltf.animations;\n    \n    // Calculate bounding box\n    const box = new THREE.Box3().setFromObject(scene);\n    const size = box.getSize(new THREE.Vector3());\n    \n    setModelInfo({\n      triangles: 0, // Would need to traverse geometry to count\n      materials: scene.traverse((child) => {\n        if (child.isMesh) return child.material;\n      }),\n      animations: animations.length,\n      size: {\n        width: size.x.toFixed(2),\n        height: size.y.toFixed(2),\n        depth: size.z.toFixed(2)\n      }\n    });\n  };\n\n  const handleModelError = (error) => {\n    setLoading(false);\n    setError(error);\n  };\n\n  if (!modelFile) {\n    return (\n      <div className={`threejs-preview ${className}`}>\n        <div className=\"preview-placeholder\">\n          <div className=\"placeholder-icon\">🎯</div>\n          <h3>No 3D Model Selected</h3>\n          <p>Upload a GLB or GLTF file to see the 3D preview</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className={`threejs-preview ${className}`}>\n        <ErrorDisplay error={error} />\n      </div>\n    );\n  }\n\n  return (\n    <div className={`threejs-preview ${className}`}>\n      <div className=\"preview-container\">\n        {loading && <LoadingSpinner />}\n        \n        <Canvas\n          camera={{ position: [5, 5, 5], fov: 50 }}\n          style={{ background: 'linear-gradient(135deg, #f8f9fa 0%, #F0B21B20 100%)' }}\n        >\n          <Suspense fallback={null}>\n            {/* Lighting */}\n            <ambientLight intensity={0.6} />\n            <directionalLight\n              position={[10, 10, 5]}\n              intensity={1.2}\n              castShadow\n              shadow-mapSize-width={2048}\n              shadow-mapSize-height={2048}\n            />\n            <directionalLight\n              position={[-10, 5, -5]}\n              intensity={0.5}\n            />\n            <pointLight position={[-10, -10, -10]} intensity={0.4} />\n            <pointLight position={[10, 10, 10]} intensity={0.3} color=\"#F0B21B\" />\n            \n            {/* Model */}\n            <Model \n              modelFile={modelFile}\n              onLoad={handleModelLoad}\n              onError={handleModelError}\n            />\n            \n            {/* Ground shadow */}\n            <ContactShadows \n              position={[0, -1, 0]} \n              opacity={0.4} \n              scale={10} \n              blur={2} \n              far={4} \n            />\n            \n            {/* Controls */}\n            <OrbitControls \n              enablePan={true}\n              enableZoom={true}\n              enableRotate={true}\n              minDistance={2}\n              maxDistance={20}\n              autoRotate={false}\n              autoRotateSpeed={0.5}\n            />\n            \n            {/* Camera controller */}\n            <CameraController />\n          </Suspense>\n        </Canvas>\n      </div>\n\n      {/* Model Information Panel */}\n      {modelInfo && (\n        <div className=\"model-info-panel\">\n          <h4>Model Information</h4>\n          <div className=\"info-grid\">\n            <div className=\"info-item\">\n              <span className=\"info-label\">File:</span>\n              <span className=\"info-value\">{modelFile.name}</span>\n            </div>\n            <div className=\"info-item\">\n              <span className=\"info-label\">Size:</span>\n              <span className=\"info-value\">{(modelFile.size / 1024 / 1024).toFixed(2)} MB</span>\n            </div>\n            <div className=\"info-item\">\n              <span className=\"info-label\">Dimensions:</span>\n              <span className=\"info-value\">\n                {modelInfo.size.width} × {modelInfo.size.height} × {modelInfo.size.depth}\n              </span>\n            </div>\n            <div className=\"info-item\">\n              <span className=\"info-label\">Animations:</span>\n              <span className=\"info-value\">{modelInfo.animations}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Controls Panel */}\n      <div className=\"preview-controls\">\n        <div className=\"control-group\">\n          <span className=\"control-label\">Controls:</span>\n          <div className=\"control-hints\">\n            <span>🖱️ Drag to rotate</span>\n            <span>🔍 Scroll to zoom</span>\n            <span>⌨️ Right-click + drag to pan</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ThreeJSPreview;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,OAAO;AACpE,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,aAAa,EAAEC,cAAc,EAAEC,OAAO,QAAQ,mBAAmB;AAC1E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAAA,kBACrBD,OAAA;EAAKE,SAAS,EAAC,iBAAiB;EAAAC,QAAA,gBAC9BH,OAAA;IAAKE,SAAS,EAAC;EAAiB;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eACvCP,OAAA;IAAAG,QAAA,EAAG;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACvB,CACN;;AAED;AAAAC,EAAA,GAPMP,cAAc;AAQpB,MAAMQ,YAAY,GAAGA,CAAC;EAAEC;AAAM,CAAC,kBAC7BV,OAAA;EAAKE,SAAS,EAAC,eAAe;EAAAC,QAAA,gBAC5BH,OAAA;IAAKE,SAAS,EAAC,YAAY;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eACpCP,OAAA;IAAAG,QAAA,EAAI;EAAuB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eAChCP,OAAA;IAAAG,QAAA,EAAIO,KAAK,CAACC,OAAO,IAAI;EAAwB;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC/C,CACN;;AAED;AAAAK,GAAA,GARMH,YAAY;AASlB,MAAMI,KAAK,GAAGA,CAAC;EAAEC,SAAS;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAMC,OAAO,GAAG7B,MAAM,CAAC,CAAC;EACxB,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,SAAS,EAAE;;IAEhB;IACA,MAAMO,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACT,SAAS,CAAC;IAC1CM,WAAW,CAACC,GAAG,CAAC;IAEhB,OAAO,MAAM;MACXC,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;MACxBD,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,CAACN,SAAS,CAAC,CAAC;EAEfpB,QAAQ,CAAE+B,KAAK,IAAK;IAClB,IAAIP,OAAO,CAACQ,OAAO,EAAE;MACnB;MACAR,OAAO,CAACQ,OAAO,CAACC,QAAQ,CAACC,CAAC,IAAI,KAAK;IACrC;EACF,CAAC,CAAC;EAEF,IAAI,CAACT,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBAAOnB,OAAA,CAAC6B,WAAW;IAACR,GAAG,EAAEF,QAAS;IAACD,OAAO,EAAEA,OAAQ;IAACH,MAAM,EAAEA,MAAO;IAACC,OAAO,EAAEA;EAAQ;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3F,CAAC;;AAED;AAAAU,EAAA,CA7BMJ,KAAK;EAAA,QAiBTnB,QAAQ;AAAA;AAAAoC,GAAA,GAjBJjB,KAAK;AA8BX,MAAMgB,WAAW,GAAGA,CAAC;EAAER,GAAG;EAAEH,OAAO;EAAEH,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAe,GAAA;EACzD,IAAI;IACF,MAAM;MAAEC;IAAM,CAAC,GAAGnC,OAAO,CAACwB,GAAG,CAAC;IAE9B/B,SAAS,CAAC,MAAM;MACd,IAAI0C,KAAK,IAAIjB,MAAM,EAAE;QACnBA,MAAM,CAAC;UAAEiB;QAAM,CAAC,CAAC;MACnB;IACF,CAAC,EAAE,CAACA,KAAK,EAAEjB,MAAM,CAAC,CAAC;IAEnB,oBACEf,OAAA;MACEiC,GAAG,EAAEf,OAAQ;MACbgB,MAAM,EAAEF,KAAK,CAACG,KAAK,CAAC,CAAE;MACtBC,KAAK,EAAE,CAAE;MACTC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAAE;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAEN,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdpB,SAAS,CAAC,MAAM;MACd,IAAI0B,OAAO,EAAE;QACXA,OAAO,CAACN,KAAK,CAAC;MAChB;IACF,CAAC,EAAE,CAACA,KAAK,EAAEM,OAAO,CAAC,CAAC;IAEpB,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AAAAe,GAAA,CA7BMF,WAAW;EAAA,QAEKhC,OAAO;AAAA;AAAAyC,GAAA,GAFvBT,WAAW;AA8BjB,MAAMU,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B9C,QAAQ,CAAE+B,KAAK,IAAK;IAClB;IACAA,KAAK,CAACgB,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC;AAACF,GAAA,CANID,gBAAgB;EAAA,QACpB7C,QAAQ;AAAA;AAAAiD,GAAA,GADJJ,gBAAgB;AAQtB,MAAMK,cAAc,GAAGA,CAAC;EAAE9B,SAAS;EAAEZ,SAAS,GAAG;AAAG,CAAC,KAAK;EAAA2C,GAAA;EACxD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEsC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAM4D,eAAe,GAAIC,IAAI,IAAK;IAChCL,UAAU,CAAC,KAAK,CAAC;IACjBC,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMhB,KAAK,GAAGoB,IAAI,CAACpB,KAAK;IACxB,MAAMqB,UAAU,GAAGD,IAAI,CAACC,UAAU;;IAElC;IACA,MAAMC,GAAG,GAAG,IAAIxD,KAAK,CAACyD,IAAI,CAAC,CAAC,CAACC,aAAa,CAACxB,KAAK,CAAC;IACjD,MAAMyB,IAAI,GAAGH,GAAG,CAACI,OAAO,CAAC,IAAI5D,KAAK,CAAC6D,OAAO,CAAC,CAAC,CAAC;IAE7CT,YAAY,CAAC;MACXU,SAAS,EAAE,CAAC;MAAE;MACdC,SAAS,EAAE7B,KAAK,CAAC8B,QAAQ,CAAEC,KAAK,IAAK;QACnC,IAAIA,KAAK,CAACC,MAAM,EAAE,OAAOD,KAAK,CAACE,QAAQ;MACzC,CAAC,CAAC;MACFZ,UAAU,EAAEA,UAAU,CAACa,MAAM;MAC7BT,IAAI,EAAE;QACJU,KAAK,EAAEV,IAAI,CAACW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;QACxBC,MAAM,EAAEb,IAAI,CAAC7B,CAAC,CAACyC,OAAO,CAAC,CAAC,CAAC;QACzBE,KAAK,EAAEd,IAAI,CAACe,CAAC,CAACH,OAAO,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,gBAAgB,GAAI/D,KAAK,IAAK;IAClCqC,UAAU,CAAC,KAAK,CAAC;IACjBC,QAAQ,CAACtC,KAAK,CAAC;EACjB,CAAC;EAED,IAAI,CAACI,SAAS,EAAE;IACd,oBACEd,OAAA;MAAKE,SAAS,EAAE,mBAAmBA,SAAS,EAAG;MAAAC,QAAA,eAC7CH,OAAA;QAAKE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCH,OAAA;UAAKE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1CP,OAAA;UAAAG,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BP,OAAA;UAAAG,QAAA,EAAG;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIG,KAAK,EAAE;IACT,oBACEV,OAAA;MAAKE,SAAS,EAAE,mBAAmBA,SAAS,EAAG;MAAAC,QAAA,eAC7CH,OAAA,CAACS,YAAY;QAACC,KAAK,EAAEA;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,oBACEP,OAAA;IAAKE,SAAS,EAAE,mBAAmBA,SAAS,EAAG;IAAAC,QAAA,gBAC7CH,OAAA;MAAKE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,GAC/B2C,OAAO,iBAAI9C,OAAA,CAACC,cAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE9BP,OAAA,CAACP,MAAM;QACLgD,MAAM,EAAE;UAAEJ,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAEqC,GAAG,EAAE;QAAG,CAAE;QACzCC,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAsD,CAAE;QAAAzE,QAAA,eAE7EH,OAAA,CAACR,QAAQ;UAACqF,QAAQ,EAAE,IAAK;UAAA1E,QAAA,gBAEvBH,OAAA;YAAc8E,SAAS,EAAE;UAAI;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCP,OAAA;YACEqC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAE;YACtByC,SAAS,EAAE,GAAI;YACfC,UAAU;YACV,wBAAsB,IAAK;YAC3B,yBAAuB;UAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACFP,OAAA;YACEqC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE;YACvByC,SAAS,EAAE;UAAI;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFP,OAAA;YAAYqC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAE;YAACyC,SAAS,EAAE;UAAI;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDP,OAAA;YAAYqC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;YAACyC,SAAS,EAAE,GAAI;YAACE,KAAK,EAAC;UAAS;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGtEP,OAAA,CAACa,KAAK;YACJC,SAAS,EAAEA,SAAU;YACrBC,MAAM,EAAEoC,eAAgB;YACxBnC,OAAO,EAAEyD;UAAiB;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFP,OAAA,CAACJ,cAAc;YACbyC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YACrB4C,OAAO,EAAE,GAAI;YACb7C,KAAK,EAAE,EAAG;YACV8C,IAAI,EAAE,CAAE;YACRC,GAAG,EAAE;UAAE;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGFP,OAAA,CAACL,aAAa;YACZyF,SAAS,EAAE,IAAK;YAChBC,UAAU,EAAE,IAAK;YACjBC,YAAY,EAAE,IAAK;YACnBC,WAAW,EAAE,CAAE;YACfC,WAAW,EAAE,EAAG;YAChBC,UAAU,EAAE,KAAM;YAClBC,eAAe,EAAE;UAAI;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAGFP,OAAA,CAACuC,gBAAgB;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL0C,SAAS,iBACRjD,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BH,OAAA;QAAAG,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BP,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEW,SAAS,CAAC6E;UAAI;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAE,CAACW,SAAS,CAAC2C,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEY,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,GACzB8C,SAAS,CAACQ,IAAI,CAACU,KAAK,EAAC,QAAG,EAAClB,SAAS,CAACQ,IAAI,CAACa,MAAM,EAAC,QAAG,EAACrB,SAAS,CAACQ,IAAI,CAACc,KAAK;UAAA;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAE8C,SAAS,CAACI;UAAU;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDP,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAME,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChDP,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA;YAAAG,QAAA,EAAM;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BP,OAAA;YAAAG,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9BP,OAAA;YAAAG,QAAA,EAAM;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsC,GAAA,CA3JID,cAAc;AAAAgD,GAAA,GAAdhD,cAAc;AA6JpB,eAAeA,cAAc;AAAC,IAAApC,EAAA,EAAAI,GAAA,EAAAkB,GAAA,EAAAQ,GAAA,EAAAK,GAAA,EAAAiD,GAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}