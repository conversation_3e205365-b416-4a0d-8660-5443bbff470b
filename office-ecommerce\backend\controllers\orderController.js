const OrderService = require('../services/orderService');
const logger = require('../utils/logger');

/**
 * Order Controller
 * Handles HTTP requests and responses for order-related operations
 */
class OrderController {

  constructor() {
    this.orderService = new OrderService();
  }

  /**
   * Get all orders with pagination and filters
   * @route GET /api/orders
   * @access Private (Admin/Employee)
   */
  async getOrders(req, res) {
    try {
      const filters = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        paymentStatus: req.query.paymentStatus,
        search: req.query.search,
        customerId: req.query.customerId,
        startDate: req.query.startDate,
        endDate: req.query.endDate
      };

      const result = await this.orderService.getOrders(filters);

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('OrderController.getOrders error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch orders',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Create a new order
   * @route POST /api/orders
   * @access Private (Authenticated users)
   */
  async createOrder(req, res) {
    try {
      const {
        customerEmail,
        customerName,
        customerPhone,
        shippingAddress,
        billingAddress,
        items,
        subTotal,
        taxAmount = 0,
        shippingAmount = 0,
        discountAmount = 0,
        totalAmount,
        currency = 'PHP',
        notes
      } = req.body;

      // Prepare order data
      const orderData = {
        CustomerEmail: customerEmail,
        CustomerName: customerName,
        CustomerPhone: customerPhone,
        ShippingAddress: shippingAddress,
        BillingAddress: billingAddress || shippingAddress,
        SubTotal: subTotal,
        TaxAmount: taxAmount,
        ShippingAmount: shippingAmount,
        DiscountAmount: discountAmount,
        TotalAmount: totalAmount,
        Currency: currency,
        Notes: notes
      };

      const result = await this.orderService.createOrder(orderData, items, req.user);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.status(201).json({
        success: true,
        message: 'Order created successfully',
        data: result.data
      });

    } catch (error) {
      logger.error('OrderController.createOrder error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create order',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Update order status
   * @route PATCH /api/orders/:id/status
   * @access Private (Admin/Employee)
   */
  async updateOrderStatus(req, res) {
    try {
      const { id } = req.params;
      const { status, notes } = req.body;

      const result = await this.orderService.updateOrderStatus(id, status, notes, req.user);

      if (!result.success) {
        const statusCode = result.code === 'INVALID_ORDER_ID' ? 400 : 404;
        return res.status(statusCode).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.json({
        success: true,
        message: 'Order status updated successfully',
        data: result.data
      });

    } catch (error) {
      logger.error('OrderController.updateOrderStatus error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update order status',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Cancel an order
   * @route DELETE /api/orders/:id
   * @access Private (Admin/Employee)
   */
  async cancelOrder(req, res) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const result = await this.orderService.cancelOrder(id, reason, req.user);

      if (!result.success) {
        const statusCode = result.code === 'INVALID_ORDER_ID' ? 400 : 404;
        return res.status(statusCode).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.json({
        success: true,
        message: 'Order cancelled successfully',
        data: result.data
      });

    } catch (error) {
      logger.error('OrderController.cancelOrder error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel order',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get orders for a specific user
   * @route GET /api/orders/user/:userId
   * @access Private (Admin/Employee or order owner)
   */
  async getUserOrders(req, res) {
    try {
      const { userId } = req.params;
      const filters = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status
      };

      // Check if user can access these orders
      if (req.user.role !== 'Admin' && req.user.role !== 'Employee' && req.user.userId !== userId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. You can only view your own orders.'
        });
      }

      const result = await this.orderService.getUserOrders(userId, filters);

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('OrderController.getUserOrders error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch user orders',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get a single order by ID
   * @route GET /api/orders/:id
   * @access Private (Admin/Employee or order owner)
   */
  async getOrderById(req, res) {
    try {
      const { id } = req.params;

      // This would need to be implemented in the service
      // const result = await orderService.getOrderById(id, req.user);

      // For now, return a placeholder response
      res.status(501).json({
        success: false,
        message: 'Get order by ID not yet implemented'
      });

    } catch (error) {
      logger.error('OrderController.getOrderById error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch order',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Update order details
   * @route PUT /api/orders/:id
   * @access Private (Admin/Employee)
   */
  async updateOrder(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // This would need to be implemented in the service
      // const result = await orderService.updateOrder(id, updateData, req.user);

      // For now, return a placeholder response
      res.status(501).json({
        success: false,
        message: 'Update order not yet implemented'
      });

    } catch (error) {
      logger.error('OrderController.updateOrder error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update order',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = OrderController;
