{"ast": null, "code": "// stats.js - http://github.com/mrdoob/stats.js\n(function (f, e) {\n  \"object\" === typeof exports && \"undefined\" !== typeof module ? module.exports = e() : \"function\" === typeof define && define.amd ? define(e) : f.Stats = e();\n})(this, function () {\n  var f = function () {\n    function e(a) {\n      c.appendChild(a.dom);\n      return a;\n    }\n    function u(a) {\n      for (var d = 0; d < c.children.length; d++) c.children[d].style.display = d === a ? \"block\" : \"none\";\n      l = a;\n    }\n    var l = 0,\n      c = document.createElement(\"div\");\n    c.style.cssText = \"position:fixed;top:0;left:0;cursor:pointer;opacity:0.9;z-index:10000\";\n    c.addEventListener(\"click\", function (a) {\n      a.preventDefault();\n      u(++l % c.children.length);\n    }, !1);\n    var k = (performance || Date).now(),\n      g = k,\n      a = 0,\n      r = e(new f.Panel(\"FPS\", \"#0ff\", \"#002\")),\n      h = e(new f.Panel(\"MS\", \"#0f0\", \"#020\"));\n    if (self.performance && self.performance.memory) var t = e(new f.Panel(\"MB\", \"#f08\", \"#201\"));\n    u(0);\n    return {\n      REVISION: 16,\n      dom: c,\n      addPanel: e,\n      showPanel: u,\n      begin: function () {\n        k = (performance || Date).now();\n      },\n      end: function () {\n        a++;\n        var c = (performance || Date).now();\n        h.update(c - k, 200);\n        if (c > g + 1E3 && (r.update(1E3 * a / (c - g), 100), g = c, a = 0, t)) {\n          var d = performance.memory;\n          t.update(d.usedJSHeapSize / 1048576, d.jsHeapSizeLimit / 1048576);\n        }\n        return c;\n      },\n      update: function () {\n        k = this.end();\n      },\n      domElement: c,\n      setMode: u\n    };\n  };\n  f.Panel = function (e, f, l) {\n    var c = Infinity,\n      k = 0,\n      g = Math.round,\n      a = g(window.devicePixelRatio || 1),\n      r = 80 * a,\n      h = 48 * a,\n      t = 3 * a,\n      v = 2 * a,\n      d = 3 * a,\n      m = 15 * a,\n      n = 74 * a,\n      p = 30 * a,\n      q = document.createElement(\"canvas\");\n    q.width = r;\n    q.height = h;\n    q.style.cssText = \"width:80px;height:48px\";\n    var b = q.getContext(\"2d\");\n    b.font = \"bold \" + 9 * a + \"px Helvetica,Arial,sans-serif\";\n    b.textBaseline = \"top\";\n    b.fillStyle = l;\n    b.fillRect(0, 0, r, h);\n    b.fillStyle = f;\n    b.fillText(e, t, v);\n    b.fillRect(d, m, n, p);\n    b.fillStyle = l;\n    b.globalAlpha = .9;\n    b.fillRect(d, m, n, p);\n    return {\n      dom: q,\n      update: function (h, w) {\n        c = Math.min(c, h);\n        k = Math.max(k, h);\n        b.fillStyle = l;\n        b.globalAlpha = 1;\n        b.fillRect(0, 0, r, m);\n        b.fillStyle = f;\n        b.fillText(g(h) + \" \" + e + \" (\" + g(c) + \"-\" + g(k) + \")\", t, v);\n        b.drawImage(q, d + a, m, n - a, p, d, m, n - a, p);\n        b.fillRect(d + n - a, m, a, p);\n        b.fillStyle = l;\n        b.globalAlpha = .9;\n        b.fillRect(d + n - a, m, a, g((1 - h / w) * p));\n      }\n    };\n  };\n  return f;\n});", "map": {"version": 3, "names": ["f", "e", "exports", "module", "define", "amd", "Stats", "a", "c", "append<PERSON><PERSON><PERSON>", "dom", "u", "d", "children", "length", "style", "display", "l", "document", "createElement", "cssText", "addEventListener", "preventDefault", "k", "performance", "Date", "now", "g", "r", "Panel", "h", "self", "memory", "t", "REVISION", "addPanel", "showPanel", "begin", "end", "update", "usedJSHeapSize", "jsHeapSizeLimit", "dom<PERSON>lement", "setMode", "Infinity", "Math", "round", "window", "devicePixelRatio", "v", "m", "n", "p", "q", "width", "height", "b", "getContext", "font", "textBaseline", "fillStyle", "fillRect", "fillText", "globalAlpha", "w", "min", "max", "drawImage"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/stats.js/build/stats.min.js"], "sourcesContent": ["// stats.js - http://github.com/mrdoob/stats.js\n(function(f,e){\"object\"===typeof exports&&\"undefined\"!==typeof module?module.exports=e():\"function\"===typeof define&&define.amd?define(e):f.Stats=e()})(this,function(){var f=function(){function e(a){c.appendChild(a.dom);return a}function u(a){for(var d=0;d<c.children.length;d++)c.children[d].style.display=d===a?\"block\":\"none\";l=a}var l=0,c=document.createElement(\"div\");c.style.cssText=\"position:fixed;top:0;left:0;cursor:pointer;opacity:0.9;z-index:10000\";c.addEventListener(\"click\",function(a){a.preventDefault();\nu(++l%c.children.length)},!1);var k=(performance||Date).now(),g=k,a=0,r=e(new f.Panel(\"FPS\",\"#0ff\",\"#002\")),h=e(new f.Panel(\"MS\",\"#0f0\",\"#020\"));if(self.performance&&self.performance.memory)var t=e(new f.Panel(\"MB\",\"#f08\",\"#201\"));u(0);return{REVISION:16,dom:c,addPanel:e,showPanel:u,begin:function(){k=(performance||Date).now()},end:function(){a++;var c=(performance||Date).now();h.update(c-k,200);if(c>g+1E3&&(r.update(1E3*a/(c-g),100),g=c,a=0,t)){var d=performance.memory;t.update(d.usedJSHeapSize/\n1048576,d.jsHeapSizeLimit/1048576)}return c},update:function(){k=this.end()},domElement:c,setMode:u}};f.Panel=function(e,f,l){var c=Infinity,k=0,g=Math.round,a=g(window.devicePixelRatio||1),r=80*a,h=48*a,t=3*a,v=2*a,d=3*a,m=15*a,n=74*a,p=30*a,q=document.createElement(\"canvas\");q.width=r;q.height=h;q.style.cssText=\"width:80px;height:48px\";var b=q.getContext(\"2d\");b.font=\"bold \"+9*a+\"px Helvetica,Arial,sans-serif\";b.textBaseline=\"top\";b.fillStyle=l;b.fillRect(0,0,r,h);b.fillStyle=f;b.fillText(e,t,v);\nb.fillRect(d,m,n,p);b.fillStyle=l;b.globalAlpha=.9;b.fillRect(d,m,n,p);return{dom:q,update:function(h,w){c=Math.min(c,h);k=Math.max(k,h);b.fillStyle=l;b.globalAlpha=1;b.fillRect(0,0,r,m);b.fillStyle=f;b.fillText(g(h)+\" \"+e+\" (\"+g(c)+\"-\"+g(k)+\")\",t,v);b.drawImage(q,d+a,m,n-a,p,d,m,n-a,p);b.fillRect(d+n-a,m,a,p);b.fillStyle=l;b.globalAlpha=.9;b.fillRect(d+n-a,m,a,g((1-h/w)*p))}}};return f});\n"], "mappings": "AAAA;AACA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,KAAG,OAAOC,OAAO,IAAE,WAAW,KAAG,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,KAAG,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAACD,CAAC,CAACM,KAAK,GAACL,CAAC,CAAC,CAAC;AAAA,CAAC,EAAE,IAAI,EAAC,YAAU;EAAC,IAAID,CAAC,GAAC,SAAAA,CAAA,EAAU;IAAC,SAASC,CAACA,CAACM,CAAC,EAAC;MAACC,CAAC,CAACC,WAAW,CAACF,CAAC,CAACG,GAAG,CAAC;MAAC,OAAOH,CAAC;IAAA;IAAC,SAASI,CAACA,CAACJ,CAAC,EAAC;MAAC,KAAI,IAAIK,CAAC,GAAC,CAAC,EAACA,CAAC,GAACJ,CAAC,CAACK,QAAQ,CAACC,MAAM,EAACF,CAAC,EAAE,EAACJ,CAAC,CAACK,QAAQ,CAACD,CAAC,CAAC,CAACG,KAAK,CAACC,OAAO,GAACJ,CAAC,KAAGL,CAAC,GAAC,OAAO,GAAC,MAAM;MAACU,CAAC,GAACV,CAAC;IAAA;IAAC,IAAIU,CAAC,GAAC,CAAC;MAACT,CAAC,GAACU,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAACX,CAAC,CAACO,KAAK,CAACK,OAAO,GAAC,sEAAsE;IAACZ,CAAC,CAACa,gBAAgB,CAAC,OAAO,EAAC,UAASd,CAAC,EAAC;MAACA,CAAC,CAACe,cAAc,CAAC,CAAC;MACpgBX,CAAC,CAAC,EAAEM,CAAC,GAACT,CAAC,CAACK,QAAQ,CAACC,MAAM,CAAC;IAAA,CAAC,EAAC,CAAC,CAAC,CAAC;IAAC,IAAIS,CAAC,GAAC,CAACC,WAAW,IAAEC,IAAI,EAAEC,GAAG,CAAC,CAAC;MAACC,CAAC,GAACJ,CAAC;MAAChB,CAAC,GAAC,CAAC;MAACqB,CAAC,GAAC3B,CAAC,CAAC,IAAID,CAAC,CAAC6B,KAAK,CAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC;MAACC,CAAC,GAAC7B,CAAC,CAAC,IAAID,CAAC,CAAC6B,KAAK,CAAC,IAAI,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC;IAAC,IAAGE,IAAI,CAACP,WAAW,IAAEO,IAAI,CAACP,WAAW,CAACQ,MAAM,EAAC,IAAIC,CAAC,GAAChC,CAAC,CAAC,IAAID,CAAC,CAAC6B,KAAK,CAAC,IAAI,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC;IAAClB,CAAC,CAAC,CAAC,CAAC;IAAC,OAAM;MAACuB,QAAQ,EAAC,EAAE;MAACxB,GAAG,EAACF,CAAC;MAAC2B,QAAQ,EAAClC,CAAC;MAACmC,SAAS,EAACzB,CAAC;MAAC0B,KAAK,EAAC,SAAAA,CAAA,EAAU;QAACd,CAAC,GAAC,CAACC,WAAW,IAAEC,IAAI,EAAEC,GAAG,CAAC,CAAC;MAAA,CAAC;MAACY,GAAG,EAAC,SAAAA,CAAA,EAAU;QAAC/B,CAAC,EAAE;QAAC,IAAIC,CAAC,GAAC,CAACgB,WAAW,IAAEC,IAAI,EAAEC,GAAG,CAAC,CAAC;QAACI,CAAC,CAACS,MAAM,CAAC/B,CAAC,GAACe,CAAC,EAAC,GAAG,CAAC;QAAC,IAAGf,CAAC,GAACmB,CAAC,GAAC,GAAG,KAAGC,CAAC,CAACW,MAAM,CAAC,GAAG,GAAChC,CAAC,IAAEC,CAAC,GAACmB,CAAC,CAAC,EAAC,GAAG,CAAC,EAACA,CAAC,GAACnB,CAAC,EAACD,CAAC,GAAC,CAAC,EAAC0B,CAAC,CAAC,EAAC;UAAC,IAAIrB,CAAC,GAACY,WAAW,CAACQ,MAAM;UAACC,CAAC,CAACM,MAAM,CAAC3B,CAAC,CAAC4B,cAAc,GACpf,OAAO,EAAC5B,CAAC,CAAC6B,eAAe,GAAC,OAAO,CAAC;QAAA;QAAC,OAAOjC,CAAC;MAAA,CAAC;MAAC+B,MAAM,EAAC,SAAAA,CAAA,EAAU;QAAChB,CAAC,GAAC,IAAI,CAACe,GAAG,CAAC,CAAC;MAAA,CAAC;MAACI,UAAU,EAAClC,CAAC;MAACmC,OAAO,EAAChC;IAAC,CAAC;EAAA,CAAC;EAACX,CAAC,CAAC6B,KAAK,GAAC,UAAS5B,CAAC,EAACD,CAAC,EAACiB,CAAC,EAAC;IAAC,IAAIT,CAAC,GAACoC,QAAQ;MAACrB,CAAC,GAAC,CAAC;MAACI,CAAC,GAACkB,IAAI,CAACC,KAAK;MAACvC,CAAC,GAACoB,CAAC,CAACoB,MAAM,CAACC,gBAAgB,IAAE,CAAC,CAAC;MAACpB,CAAC,GAAC,EAAE,GAACrB,CAAC;MAACuB,CAAC,GAAC,EAAE,GAACvB,CAAC;MAAC0B,CAAC,GAAC,CAAC,GAAC1B,CAAC;MAAC0C,CAAC,GAAC,CAAC,GAAC1C,CAAC;MAACK,CAAC,GAAC,CAAC,GAACL,CAAC;MAAC2C,CAAC,GAAC,EAAE,GAAC3C,CAAC;MAAC4C,CAAC,GAAC,EAAE,GAAC5C,CAAC;MAAC6C,CAAC,GAAC,EAAE,GAAC7C,CAAC;MAAC8C,CAAC,GAACnC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAACkC,CAAC,CAACC,KAAK,GAAC1B,CAAC;IAACyB,CAAC,CAACE,MAAM,GAACzB,CAAC;IAACuB,CAAC,CAACtC,KAAK,CAACK,OAAO,GAAC,wBAAwB;IAAC,IAAIoC,CAAC,GAACH,CAAC,CAACI,UAAU,CAAC,IAAI,CAAC;IAACD,CAAC,CAACE,IAAI,GAAC,OAAO,GAAC,CAAC,GAACnD,CAAC,GAAC,+BAA+B;IAACiD,CAAC,CAACG,YAAY,GAAC,KAAK;IAACH,CAAC,CAACI,SAAS,GAAC3C,CAAC;IAACuC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAC,CAAC,EAACjC,CAAC,EAACE,CAAC,CAAC;IAAC0B,CAAC,CAACI,SAAS,GAAC5D,CAAC;IAACwD,CAAC,CAACM,QAAQ,CAAC7D,CAAC,EAACgC,CAAC,EAACgB,CAAC,CAAC;IACtfO,CAAC,CAACK,QAAQ,CAACjD,CAAC,EAACsC,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC;IAACI,CAAC,CAACI,SAAS,GAAC3C,CAAC;IAACuC,CAAC,CAACO,WAAW,GAAC,EAAE;IAACP,CAAC,CAACK,QAAQ,CAACjD,CAAC,EAACsC,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC;IAAC,OAAM;MAAC1C,GAAG,EAAC2C,CAAC;MAACd,MAAM,EAAC,SAAAA,CAAST,CAAC,EAACkC,CAAC,EAAC;QAACxD,CAAC,GAACqC,IAAI,CAACoB,GAAG,CAACzD,CAAC,EAACsB,CAAC,CAAC;QAACP,CAAC,GAACsB,IAAI,CAACqB,GAAG,CAAC3C,CAAC,EAACO,CAAC,CAAC;QAAC0B,CAAC,CAACI,SAAS,GAAC3C,CAAC;QAACuC,CAAC,CAACO,WAAW,GAAC,CAAC;QAACP,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAC,CAAC,EAACjC,CAAC,EAACsB,CAAC,CAAC;QAACM,CAAC,CAACI,SAAS,GAAC5D,CAAC;QAACwD,CAAC,CAACM,QAAQ,CAACnC,CAAC,CAACG,CAAC,CAAC,GAAC,GAAG,GAAC7B,CAAC,GAAC,IAAI,GAAC0B,CAAC,CAACnB,CAAC,CAAC,GAAC,GAAG,GAACmB,CAAC,CAACJ,CAAC,CAAC,GAAC,GAAG,EAACU,CAAC,EAACgB,CAAC,CAAC;QAACO,CAAC,CAACW,SAAS,CAACd,CAAC,EAACzC,CAAC,GAACL,CAAC,EAAC2C,CAAC,EAACC,CAAC,GAAC5C,CAAC,EAAC6C,CAAC,EAACxC,CAAC,EAACsC,CAAC,EAACC,CAAC,GAAC5C,CAAC,EAAC6C,CAAC,CAAC;QAACI,CAAC,CAACK,QAAQ,CAACjD,CAAC,GAACuC,CAAC,GAAC5C,CAAC,EAAC2C,CAAC,EAAC3C,CAAC,EAAC6C,CAAC,CAAC;QAACI,CAAC,CAACI,SAAS,GAAC3C,CAAC;QAACuC,CAAC,CAACO,WAAW,GAAC,EAAE;QAACP,CAAC,CAACK,QAAQ,CAACjD,CAAC,GAACuC,CAAC,GAAC5C,CAAC,EAAC2C,CAAC,EAAC3C,CAAC,EAACoB,CAAC,CAAC,CAAC,CAAC,GAACG,CAAC,GAACkC,CAAC,IAAEZ,CAAC,CAAC,CAAC;MAAA;IAAC,CAAC;EAAA,CAAC;EAAC,OAAOpD,CAAC;AAAA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}