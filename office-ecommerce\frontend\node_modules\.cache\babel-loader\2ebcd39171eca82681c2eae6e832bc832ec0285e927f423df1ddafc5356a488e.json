{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Color, DoubleSide } from 'three';\nconst Shadow = /*#__PURE__*/React.forwardRef(({\n  fog = false,\n  renderOrder,\n  depthWrite = false,\n  colorStop = 0.0,\n  color = 'black',\n  opacity = 0.5,\n  ...props\n}, ref) => {\n  const canvas = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    canvas.width = 128;\n    canvas.height = 128;\n    const context = canvas.getContext('2d');\n    const gradient = context.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, canvas.width / 2);\n    gradient.addColorStop(colorStop, new Color(color).getStyle());\n    gradient.addColorStop(1, 'rgba(0,0,0,0)');\n    context.fillStyle = gradient;\n    context.fillRect(0, 0, canvas.width, canvas.height);\n    return canvas;\n  }, [color, colorStop]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    renderOrder: renderOrder,\n    ref: ref,\n    \"rotation-x\": -Math.PI / 2\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    opacity: opacity,\n    fog: fog,\n    depthWrite: depthWrite,\n    side: DoubleSide\n  }, /*#__PURE__*/React.createElement(\"canvasTexture\", {\n    attach: \"map\",\n    args: [canvas]\n  })));\n});\nexport { Shadow };", "map": {"version": 3, "names": ["_extends", "React", "Color", "DoubleSide", "Shadow", "forwardRef", "fog", "renderOrder", "depthWrite", "colorStop", "color", "opacity", "props", "ref", "canvas", "useMemo", "document", "createElement", "width", "height", "context", "getContext", "gradient", "createRadialGradient", "addColorStop", "getStyle", "fillStyle", "fillRect", "Math", "PI", "transparent", "side", "attach", "args"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Shadow.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Color, DoubleSide } from 'three';\n\nconst Shadow = /*#__PURE__*/React.forwardRef(({\n  fog = false,\n  renderOrder,\n  depthWrite = false,\n  colorStop = 0.0,\n  color = 'black',\n  opacity = 0.5,\n  ...props\n}, ref) => {\n  const canvas = React.useMemo(() => {\n    const canvas = document.createElement('canvas');\n    canvas.width = 128;\n    canvas.height = 128;\n    const context = canvas.getContext('2d');\n    const gradient = context.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, canvas.width / 2);\n    gradient.addColorStop(colorStop, new Color(color).getStyle());\n    gradient.addColorStop(1, 'rgba(0,0,0,0)');\n    context.fillStyle = gradient;\n    context.fillRect(0, 0, canvas.width, canvas.height);\n    return canvas;\n  }, [color, colorStop]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    renderOrder: renderOrder,\n    ref: ref,\n    \"rotation-x\": -Math.PI / 2\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    opacity: opacity,\n    fog: fog,\n    depthWrite: depthWrite,\n    side: DoubleSide\n  }, /*#__PURE__*/React.createElement(\"canvasTexture\", {\n    attach: \"map\",\n    args: [canvas]\n  })));\n});\n\nexport { Shadow };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,UAAU,QAAQ,OAAO;AAEzC,MAAMC,MAAM,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAAC;EAC5CC,GAAG,GAAG,KAAK;EACXC,WAAW;EACXC,UAAU,GAAG,KAAK;EAClBC,SAAS,GAAG,GAAG;EACfC,KAAK,GAAG,OAAO;EACfC,OAAO,GAAG,GAAG;EACb,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGb,KAAK,CAACc,OAAO,CAAC,MAAM;IACjC,MAAMD,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,KAAK,GAAG,GAAG;IAClBJ,MAAM,CAACK,MAAM,GAAG,GAAG;IACnB,MAAMC,OAAO,GAAGN,MAAM,CAACO,UAAU,CAAC,IAAI,CAAC;IACvC,MAAMC,QAAQ,GAAGF,OAAO,CAACG,oBAAoB,CAACT,MAAM,CAACI,KAAK,GAAG,CAAC,EAAEJ,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE,CAAC,EAAEL,MAAM,CAACI,KAAK,GAAG,CAAC,EAAEJ,MAAM,CAACK,MAAM,GAAG,CAAC,EAAEL,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC;IAC5II,QAAQ,CAACE,YAAY,CAACf,SAAS,EAAE,IAAIP,KAAK,CAACQ,KAAK,CAAC,CAACe,QAAQ,CAAC,CAAC,CAAC;IAC7DH,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,eAAe,CAAC;IACzCJ,OAAO,CAACM,SAAS,GAAGJ,QAAQ;IAC5BF,OAAO,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEb,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,MAAM,CAAC;IACnD,OAAOL,MAAM;EACf,CAAC,EAAE,CAACJ,KAAK,EAAED,SAAS,CAAC,CAAC;EACtB,OAAO,aAAaR,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAEjB,QAAQ,CAAC;IACvDO,WAAW,EAAEA,WAAW;IACxBM,GAAG,EAAEA,GAAG;IACR,YAAY,EAAE,CAACe,IAAI,CAACC,EAAE,GAAG;EAC3B,CAAC,EAAEjB,KAAK,CAAC,EAAE,aAAaX,KAAK,CAACgB,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAahB,KAAK,CAACgB,aAAa,CAAC,mBAAmB,EAAE;IACxHa,WAAW,EAAE,IAAI;IACjBnB,OAAO,EAAEA,OAAO;IAChBL,GAAG,EAAEA,GAAG;IACRE,UAAU,EAAEA,UAAU;IACtBuB,IAAI,EAAE5B;EACR,CAAC,EAAE,aAAaF,KAAK,CAACgB,aAAa,CAAC,eAAe,EAAE;IACnDe,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,CAACnB,MAAM;EACf,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,SAASV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}