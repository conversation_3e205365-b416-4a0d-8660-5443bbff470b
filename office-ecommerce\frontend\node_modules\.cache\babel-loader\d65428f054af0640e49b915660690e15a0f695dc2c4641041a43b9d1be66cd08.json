{"ast": null, "code": "import apiClient from './apiClient';\nexport const authService = {\n  async login(email, password) {\n    try {\n      const response = await apiClient.post('/api/auth/login', {\n        email,\n        password\n      });\n      return response;\n    } catch (error) {\n      // Fallback to mock authentication if backend is not accessible\n      console.warn('Backend not accessible, using mock authentication:', error.message);\n\n      // Store that we're in mock mode\n      localStorage.setItem('mockMode', 'true');\n\n      // Mock authentication logic\n      if (email === '<EMAIL>' && password === 'admin123') {\n        return {\n          success: true,\n          message: 'Login successful (mock)',\n          data: {\n            token: 'mock-jwt-token-admin',\n            user: {\n              id: 1,\n              firstName: 'Admin',\n              lastName: 'User',\n              email: email,\n              role: 'Admin'\n            }\n          }\n        };\n      } else if (email === '<EMAIL>' && password === 'admin123') {\n        return {\n          success: true,\n          message: 'Login successful (mock)',\n          data: {\n            token: 'mock-jwt-token-manager',\n            user: {\n              id: 2,\n              firstName: 'Manager',\n              lastName: 'User',\n              email: email,\n              role: 'Employee'\n            }\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Invalid credentials',\n          error: 'Invalid email or password'\n        };\n      }\n    }\n  },\n  async register(userData) {\n    // TODO: Implement register endpoint in backend\n    throw new Error('Registration not implemented yet');\n  },\n  async getProfile() {\n    // Mock profile data\n    return {\n      success: true,\n      data: {\n        id: 1,\n        name: 'Demo User',\n        email: '<EMAIL>',\n        phone: '(*************',\n        address: '123 Demo Street, Demo City, DC 12345'\n      }\n    };\n  },\n  async updateProfile(profileData) {\n    // Mock profile update (always succeeds)\n    return {\n      success: true,\n      data: {\n        ...profileData,\n        id: 1\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["apiClient", "authService", "login", "email", "password", "response", "post", "error", "console", "warn", "message", "localStorage", "setItem", "success", "data", "token", "user", "id", "firstName", "lastName", "role", "register", "userData", "Error", "getProfile", "name", "phone", "address", "updateProfile", "profileData"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/auth.js"], "sourcesContent": ["import apiClient from './apiClient';\n\nexport const authService = {\n    async login(email, password) {\n        try {\n            const response = await apiClient.post('/api/auth/login', { email, password });\n            return response;\n        } catch (error) {\n            // Fallback to mock authentication if backend is not accessible\n            console.warn('Backend not accessible, using mock authentication:', error.message);\n\n            // Store that we're in mock mode\n            localStorage.setItem('mockMode', 'true');\n\n            // Mock authentication logic\n            if (email === '<EMAIL>' && password === 'admin123') {\n                return {\n                    success: true,\n                    message: 'Login successful (mock)',\n                    data: {\n                        token: 'mock-jwt-token-admin',\n                        user: {\n                            id: 1,\n                            firstName: 'Admin',\n                            lastName: 'User',\n                            email: email,\n                            role: 'Admin'\n                        }\n                    }\n                };\n            } else if (email === '<EMAIL>' && password === 'admin123') {\n                return {\n                    success: true,\n                    message: 'Login successful (mock)',\n                    data: {\n                        token: 'mock-jwt-token-manager',\n                        user: {\n                            id: 2,\n                            firstName: 'Manager',\n                            lastName: 'User',\n                            email: email,\n                            role: 'Employee'\n                        }\n                    }\n                };\n            } else {\n                return {\n                    success: false,\n                    message: 'Invalid credentials',\n                    error: 'Invalid email or password'\n                };\n            }\n        }\n    },\n\n    async register(userData) {\n        // TODO: Implement register endpoint in backend\n        throw new Error('Registration not implemented yet');\n    },\n\n    async getProfile() {\n        // Mock profile data\n        return {\n            success: true,\n            data: {\n                id: 1,\n                name: 'Demo User',\n                email: '<EMAIL>',\n                phone: '(*************',\n                address: '123 Demo Street, Demo City, DC 12345'\n            }\n        };\n    },\n\n    async updateProfile(profileData) {\n        // Mock profile update (always succeeds)\n        return {\n            success: true,\n            data: {\n                ...profileData,\n                id: 1\n            }\n        };\n    }\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AAEnC,OAAO,MAAMC,WAAW,GAAG;EACvB,MAAMC,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACzB,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAML,SAAS,CAACM,IAAI,CAAC,iBAAiB,EAAE;QAAEH,KAAK;QAAEC;MAAS,CAAC,CAAC;MAC7E,OAAOC,QAAQ;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZ;MACAC,OAAO,CAACC,IAAI,CAAC,oDAAoD,EAAEF,KAAK,CAACG,OAAO,CAAC;;MAEjF;MACAC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;;MAExC;MACA,IAAIT,KAAK,KAAK,sBAAsB,IAAIC,QAAQ,KAAK,UAAU,EAAE;QAC7D,OAAO;UACHS,OAAO,EAAE,IAAI;UACbH,OAAO,EAAE,yBAAyB;UAClCI,IAAI,EAAE;YACFC,KAAK,EAAE,sBAAsB;YAC7BC,IAAI,EAAE;cACFC,EAAE,EAAE,CAAC;cACLC,SAAS,EAAE,OAAO;cAClBC,QAAQ,EAAE,MAAM;cAChBhB,KAAK,EAAEA,KAAK;cACZiB,IAAI,EAAE;YACV;UACJ;QACJ,CAAC;MACL,CAAC,MAAM,IAAIjB,KAAK,KAAK,wBAAwB,IAAIC,QAAQ,KAAK,UAAU,EAAE;QACtE,OAAO;UACHS,OAAO,EAAE,IAAI;UACbH,OAAO,EAAE,yBAAyB;UAClCI,IAAI,EAAE;YACFC,KAAK,EAAE,wBAAwB;YAC/BC,IAAI,EAAE;cACFC,EAAE,EAAE,CAAC;cACLC,SAAS,EAAE,SAAS;cACpBC,QAAQ,EAAE,MAAM;cAChBhB,KAAK,EAAEA,KAAK;cACZiB,IAAI,EAAE;YACV;UACJ;QACJ,CAAC;MACL,CAAC,MAAM;QACH,OAAO;UACHP,OAAO,EAAE,KAAK;UACdH,OAAO,EAAE,qBAAqB;UAC9BH,KAAK,EAAE;QACX,CAAC;MACL;IACJ;EACJ,CAAC;EAED,MAAMc,QAAQA,CAACC,QAAQ,EAAE;IACrB;IACA,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;EACvD,CAAC;EAED,MAAMC,UAAUA,CAAA,EAAG;IACf;IACA,OAAO;MACHX,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACFG,EAAE,EAAE,CAAC;QACLQ,IAAI,EAAE,WAAW;QACjBtB,KAAK,EAAE,kBAAkB;QACzBuB,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE;MACb;IACJ,CAAC;EACL,CAAC;EAED,MAAMC,aAAaA,CAACC,WAAW,EAAE;IAC7B;IACA,OAAO;MACHhB,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACF,GAAGe,WAAW;QACdZ,EAAE,EAAE;MACR;IACJ,CAAC;EACL;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}