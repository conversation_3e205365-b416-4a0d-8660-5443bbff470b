{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\account\\\\OrderHistory.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrderHistory = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [filter, setFilter] = useState('all');\n  useEffect(() => {\n    fetchOrders();\n  }, []);\n  const fetchOrders = async () => {\n    setLoading(true);\n    try {\n      // Mock order data - replace with actual API call\n      const mockOrders = [{\n        id: 'ORD-001',\n        orderNumber: 'ORD-2024-001',\n        date: '2024-01-15',\n        status: 'delivered',\n        total: 1299.99,\n        currency: 'PHP',\n        items: [{\n          id: 1,\n          name: 'Executive Office Chair',\n          quantity: 1,\n          price: 899.99,\n          image: '/images/chair-executive.jpg'\n        }, {\n          id: 2,\n          name: 'Desk Organizer Set',\n          quantity: 2,\n          price: 200.00,\n          image: '/images/organizer.jpg'\n        }],\n        shippingAddress: {\n          street: '123 Business Ave',\n          city: 'Manila',\n          state: 'Metro Manila',\n          zipCode: '1000',\n          country: 'Philippines'\n        },\n        tracking: {\n          number: 'TRK123456789',\n          carrier: 'LBC Express',\n          status: 'delivered',\n          estimatedDelivery: '2024-01-20'\n        }\n      }, {\n        id: 'ORD-002',\n        orderNumber: 'ORD-2024-002',\n        date: '2024-01-10',\n        status: 'processing',\n        total: 2499.99,\n        currency: 'PHP',\n        items: [{\n          id: 3,\n          name: 'Standing Desk',\n          quantity: 1,\n          price: 2499.99,\n          image: '/images/desk-standing.jpg'\n        }],\n        shippingAddress: {\n          street: '456 Corporate St',\n          city: 'Quezon City',\n          state: 'Metro Manila',\n          zipCode: '1100',\n          country: 'Philippines'\n        },\n        tracking: {\n          number: 'TRK987654321',\n          carrier: 'J&T Express',\n          status: 'in_transit',\n          estimatedDelivery: '2024-01-25'\n        }\n      }];\n      setOrders(mockOrders);\n    } catch (error) {\n      console.error('Failed to fetch orders:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'pending':\n        return '#f39c12';\n      case 'processing':\n        return '#3498db';\n      case 'shipped':\n        return '#9b59b6';\n      case 'delivered':\n        return '#27ae60';\n      case 'cancelled':\n        return '#e74c3c';\n      default:\n        return '#95a5a6';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n            points: \"12,6 12,12 16,14\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 21\n        }, this);\n      case 'shipped':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n            x: \"1\",\n            y: \"3\",\n            width: \"15\",\n            height: \"13\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"polygon\", {\n            points: \"16,8 20,8 23,11 23,16 16,16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"5.5\",\n            cy: \"18.5\",\n            r: \"2.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"18.5\",\n            cy: \"18.5\",\n            r: \"2.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 21\n        }, this);\n      case 'delivered':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n            points: \"20,6 9,17 4,12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 21\n        }, this);\n      case 'cancelled':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"18\",\n            y1: \"6\",\n            x2: \"6\",\n            y2: \"18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"6\",\n            y1: \"6\",\n            x2: \"18\",\n            y2: \"18\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 21\n        }, this);\n    }\n  };\n  const filteredOrders = orders.filter(order => {\n    if (filter === 'all') return true;\n    return order.status === filter;\n  });\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatCurrency = (amount, currency = 'PHP') => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: currency\n    }).format(amount);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"order-history\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"Order History\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"section-subtitle\",\n            children: \"Track your orders and view purchase history\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"spinner\",\n          width: \"32\",\n          height: \"32\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"currentColor\",\n            strokeWidth: \"4\",\n            fill: \"none\",\n            strokeDasharray: \"32\",\n            strokeDashoffset: \"32\",\n            children: /*#__PURE__*/_jsxDEV(\"animate\", {\n              attributeName: \"stroke-dashoffset\",\n              dur: \"1s\",\n              values: \"32;0;32\",\n              repeatCount: \"indefinite\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading your orders...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"order-history\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Order History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Track your orders and view purchase history\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-filters\",\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"filter-select\",\n          value: filter,\n          onChange: e => setFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"pending\",\n            children: \"Pending\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"processing\",\n            children: \"Processing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"shipped\",\n            children: \"Shipped\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"delivered\",\n            children: \"Delivered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"cancelled\",\n            children: \"Cancelled\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 13\n    }, this), filteredOrders.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"64\",\n        height: \"64\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"1\",\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n          x: \"8\",\n          y: \"2\",\n          width: \"8\",\n          height: \"4\",\n          rx: \"1\",\n          ry: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No orders found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"You haven't placed any orders yet or no orders match your filter.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"orders-list\",\n      children: filteredOrders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"order-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"order-number\",\n              children: order.orderNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"order-date\",\n              children: formatDate(order.date)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-badge\",\n              style: {\n                backgroundColor: `${getStatusColor(order.status)}20`,\n                color: getStatusColor(order.status),\n                border: `1px solid ${getStatusColor(order.status)}40`\n              },\n              children: [getStatusIcon(order.status), order.status.charAt(0).toUpperCase() + order.status.slice(1)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-items\",\n          children: order.items.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-image\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"placeholder-image\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: \"3\",\n                    y: \"3\",\n                    width: \"18\",\n                    height: \"18\",\n                    rx: \"2\",\n                    ry: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"8.5\",\n                    cy: \"8.5\",\n                    r: \"1.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"21,15 16,10 5,21\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"item-name\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"item-quantity\",\n                children: [\"Qty: \", item.quantity]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-price\",\n              children: formatCurrency(item.price, order.currency)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 41\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 37\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"order-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-total\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [\"Total: \", formatCurrency(order.total, order.currency)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"order-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-secondary\",\n              onClick: () => setSelectedOrder(order),\n              children: \"View Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 37\n            }, this), order.status === 'delivered' && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-primary\",\n              children: \"Reorder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 29\n        }, this)]\n      }, order.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 25\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 9\n  }, this);\n};\n_s(OrderHistory, \"mHy/jXIhxuHAYv4T7rYwIE/KKJM=\", false, function () {\n  return [useAuth];\n});\n_c = OrderHistory;\nexport default OrderHistory;\nvar _c;\n$RefreshReg$(_c, \"OrderHistory\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "OrderHistory", "_s", "user", "orders", "setOrders", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "filter", "setFilter", "fetchOrders", "mockOrders", "id", "orderNumber", "date", "status", "total", "currency", "items", "name", "quantity", "price", "image", "shippingAddress", "street", "city", "state", "zipCode", "country", "tracking", "number", "carrier", "estimatedDelivery", "error", "console", "getStatusColor", "getStatusIcon", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "children", "cx", "cy", "r", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "points", "d", "x", "y", "x1", "y1", "x2", "y2", "filteredOrders", "order", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "format", "className", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "attributeName", "dur", "values", "repeatCount", "value", "onChange", "e", "target", "length", "rx", "ry", "map", "backgroundColor", "color", "border", "char<PERSON>t", "toUpperCase", "slice", "item", "onClick", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/account/OrderHistory.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst OrderHistory = () => {\n    const { user } = useAuth();\n    const [orders, setOrders] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [selectedOrder, setSelectedOrder] = useState(null);\n    const [filter, setFilter] = useState('all');\n\n    useEffect(() => {\n        fetchOrders();\n    }, []);\n\n    const fetchOrders = async () => {\n        setLoading(true);\n        try {\n            // Mock order data - replace with actual API call\n            const mockOrders = [\n                {\n                    id: 'ORD-001',\n                    orderNumber: 'ORD-2024-001',\n                    date: '2024-01-15',\n                    status: 'delivered',\n                    total: 1299.99,\n                    currency: 'PHP',\n                    items: [\n                        {\n                            id: 1,\n                            name: 'Executive Office Chair',\n                            quantity: 1,\n                            price: 899.99,\n                            image: '/images/chair-executive.jpg'\n                        },\n                        {\n                            id: 2,\n                            name: 'Desk Organizer Set',\n                            quantity: 2,\n                            price: 200.00,\n                            image: '/images/organizer.jpg'\n                        }\n                    ],\n                    shippingAddress: {\n                        street: '123 Business Ave',\n                        city: 'Manila',\n                        state: 'Metro Manila',\n                        zipCode: '1000',\n                        country: 'Philippines'\n                    },\n                    tracking: {\n                        number: 'TRK123456789',\n                        carrier: 'LBC Express',\n                        status: 'delivered',\n                        estimatedDelivery: '2024-01-20'\n                    }\n                },\n                {\n                    id: 'ORD-002',\n                    orderNumber: 'ORD-2024-002',\n                    date: '2024-01-10',\n                    status: 'processing',\n                    total: 2499.99,\n                    currency: 'PHP',\n                    items: [\n                        {\n                            id: 3,\n                            name: 'Standing Desk',\n                            quantity: 1,\n                            price: 2499.99,\n                            image: '/images/desk-standing.jpg'\n                        }\n                    ],\n                    shippingAddress: {\n                        street: '456 Corporate St',\n                        city: 'Quezon City',\n                        state: 'Metro Manila',\n                        zipCode: '1100',\n                        country: 'Philippines'\n                    },\n                    tracking: {\n                        number: 'TRK987654321',\n                        carrier: 'J&T Express',\n                        status: 'in_transit',\n                        estimatedDelivery: '2024-01-25'\n                    }\n                }\n            ];\n            setOrders(mockOrders);\n        } catch (error) {\n            console.error('Failed to fetch orders:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const getStatusColor = (status) => {\n        switch (status) {\n            case 'pending':\n                return '#f39c12';\n            case 'processing':\n                return '#3498db';\n            case 'shipped':\n                return '#9b59b6';\n            case 'delivered':\n                return '#27ae60';\n            case 'cancelled':\n                return '#e74c3c';\n            default:\n                return '#95a5a6';\n        }\n    };\n\n    const getStatusIcon = (status) => {\n        switch (status) {\n            case 'pending':\n                return (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n                        <polyline points=\"12,6 12,12 16,14\"/>\n                    </svg>\n                );\n            case 'processing':\n                return (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <path d=\"M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"/>\n                    </svg>\n                );\n            case 'shipped':\n                return (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <rect x=\"1\" y=\"3\" width=\"15\" height=\"13\"/>\n                        <polygon points=\"16,8 20,8 23,11 23,16 16,16\"/>\n                        <circle cx=\"5.5\" cy=\"18.5\" r=\"2.5\"/>\n                        <circle cx=\"18.5\" cy=\"18.5\" r=\"2.5\"/>\n                    </svg>\n                );\n            case 'delivered':\n                return (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <polyline points=\"20,6 9,17 4,12\"/>\n                    </svg>\n                );\n            case 'cancelled':\n                return (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"/>\n                        <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"/>\n                    </svg>\n                );\n            default:\n                return (\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n                    </svg>\n                );\n        }\n    };\n\n    const filteredOrders = orders.filter(order => {\n        if (filter === 'all') return true;\n        return order.status === filter;\n    });\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatCurrency = (amount, currency = 'PHP') => {\n        return new Intl.NumberFormat('en-PH', {\n            style: 'currency',\n            currency: currency\n        }).format(amount);\n    };\n\n    if (loading) {\n        return (\n            <div className=\"order-history\">\n                <div className=\"section-header\">\n                    <div>\n                        <h2 className=\"section-title\">Order History</h2>\n                        <p className=\"section-subtitle\">Track your orders and view purchase history</p>\n                    </div>\n                </div>\n                <div className=\"loading-state\">\n                    <svg className=\"spinner\" width=\"32\" height=\"32\" viewBox=\"0 0 24 24\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" fill=\"none\" strokeDasharray=\"32\" strokeDashoffset=\"32\">\n                            <animate attributeName=\"stroke-dashoffset\" dur=\"1s\" values=\"32;0;32\" repeatCount=\"indefinite\"/>\n                        </circle>\n                    </svg>\n                    <p>Loading your orders...</p>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"order-history\">\n            <div className=\"section-header\">\n                <div>\n                    <h2 className=\"section-title\">Order History</h2>\n                    <p className=\"section-subtitle\">Track your orders and view purchase history</p>\n                </div>\n                <div className=\"order-filters\">\n                    <select\n                        className=\"filter-select\"\n                        value={filter}\n                        onChange={(e) => setFilter(e.target.value)}\n                    >\n                        <option value=\"all\">All Orders</option>\n                        <option value=\"pending\">Pending</option>\n                        <option value=\"processing\">Processing</option>\n                        <option value=\"shipped\">Shipped</option>\n                        <option value=\"delivered\">Delivered</option>\n                        <option value=\"cancelled\">Cancelled</option>\n                    </select>\n                </div>\n            </div>\n\n            {filteredOrders.length === 0 ? (\n                <div className=\"empty-state\">\n                    <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\n                        <path d=\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"/>\n                        <rect x=\"8\" y=\"2\" width=\"8\" height=\"4\" rx=\"1\" ry=\"1\"/>\n                    </svg>\n                    <h3>No orders found</h3>\n                    <p>You haven't placed any orders yet or no orders match your filter.</p>\n                </div>\n            ) : (\n                <div className=\"orders-list\">\n                    {filteredOrders.map(order => (\n                        <div key={order.id} className=\"order-card\">\n                            <div className=\"order-header\">\n                                <div className=\"order-info\">\n                                    <h3 className=\"order-number\">{order.orderNumber}</h3>\n                                    <p className=\"order-date\">{formatDate(order.date)}</p>\n                                </div>\n                                <div className=\"order-status\">\n                                    <span \n                                        className=\"status-badge\"\n                                        style={{ \n                                            backgroundColor: `${getStatusColor(order.status)}20`,\n                                            color: getStatusColor(order.status),\n                                            border: `1px solid ${getStatusColor(order.status)}40`\n                                        }}\n                                    >\n                                        {getStatusIcon(order.status)}\n                                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\n                                    </span>\n                                </div>\n                            </div>\n\n                            <div className=\"order-items\">\n                                {order.items.map(item => (\n                                    <div key={item.id} className=\"order-item\">\n                                        <div className=\"item-image\">\n                                            <div className=\"placeholder-image\">\n                                                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                                    <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\"/>\n                                                    <circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\"/>\n                                                    <polyline points=\"21,15 16,10 5,21\"/>\n                                                </svg>\n                                            </div>\n                                        </div>\n                                        <div className=\"item-details\">\n                                            <h4 className=\"item-name\">{item.name}</h4>\n                                            <p className=\"item-quantity\">Qty: {item.quantity}</p>\n                                        </div>\n                                        <div className=\"item-price\">\n                                            {formatCurrency(item.price, order.currency)}\n                                        </div>\n                                    </div>\n                                ))}\n                            </div>\n\n                            <div className=\"order-footer\">\n                                <div className=\"order-total\">\n                                    <strong>Total: {formatCurrency(order.total, order.currency)}</strong>\n                                </div>\n                                <div className=\"order-actions\">\n                                    <button\n                                        className=\"btn-secondary\"\n                                        onClick={() => setSelectedOrder(order)}\n                                    >\n                                        View Details\n                                    </button>\n                                    {order.status === 'delivered' && (\n                                        <button className=\"btn-primary\">\n                                            Reorder\n                                        </button>\n                                    )}\n                                </div>\n                            </div>\n                        </div>\n                    ))}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default OrderHistory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACZe,WAAW,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5BL,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA;MACA,MAAMM,UAAU,GAAG,CACf;QACIC,EAAE,EAAE,SAAS;QACbC,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,CACH;UACIN,EAAE,EAAE,CAAC;UACLO,IAAI,EAAE,wBAAwB;UAC9BC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE;QACX,CAAC,EACD;UACIV,EAAE,EAAE,CAAC;UACLO,IAAI,EAAE,oBAAoB;UAC1BC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE,MAAM;UACbC,KAAK,EAAE;QACX,CAAC,CACJ;QACDC,eAAe,EAAE;UACbC,MAAM,EAAE,kBAAkB;UAC1BC,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAE,cAAc;UACrBC,OAAO,EAAE,MAAM;UACfC,OAAO,EAAE;QACb,CAAC;QACDC,QAAQ,EAAE;UACNC,MAAM,EAAE,cAAc;UACtBC,OAAO,EAAE,aAAa;UACtBhB,MAAM,EAAE,WAAW;UACnBiB,iBAAiB,EAAE;QACvB;MACJ,CAAC,EACD;QACIpB,EAAE,EAAE,SAAS;QACbC,WAAW,EAAE,cAAc;QAC3BC,IAAI,EAAE,YAAY;QAClBC,MAAM,EAAE,YAAY;QACpBC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,CACH;UACIN,EAAE,EAAE,CAAC;UACLO,IAAI,EAAE,eAAe;UACrBC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE,OAAO;UACdC,KAAK,EAAE;QACX,CAAC,CACJ;QACDC,eAAe,EAAE;UACbC,MAAM,EAAE,kBAAkB;UAC1BC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,cAAc;UACrBC,OAAO,EAAE,MAAM;UACfC,OAAO,EAAE;QACb,CAAC;QACDC,QAAQ,EAAE;UACNC,MAAM,EAAE,cAAc;UACtBC,OAAO,EAAE,aAAa;UACtBhB,MAAM,EAAE,YAAY;UACpBiB,iBAAiB,EAAE;QACvB;MACJ,CAAC,CACJ;MACD7B,SAAS,CAACQ,UAAU,CAAC;IACzB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACN5B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM8B,cAAc,GAAIpB,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACV,KAAK,SAAS;QACV,OAAO,SAAS;MACpB,KAAK,YAAY;QACb,OAAO,SAAS;MACpB,KAAK,SAAS;QACV,OAAO,SAAS;MACpB,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB;QACI,OAAO,SAAS;IACxB;EACJ,CAAC;EAED,MAAMqB,aAAa,GAAIrB,MAAM,IAAK;IAC9B,QAAQA,MAAM;MACV,KAAK,SAAS;QACV,oBACIjB,OAAA;UAAKuC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAC,QAAA,gBAC7F7C,OAAA;YAAQ8C,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAChCpD,OAAA;YAAUqD,MAAM,EAAC;UAAkB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAEd,KAAK,YAAY;QACb,oBACIpD,OAAA;UAAKuC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAC,QAAA,eAC7F7C,OAAA;YAAMsD,CAAC,EAAC;UAA2D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAEd,KAAK,SAAS;QACV,oBACIpD,OAAA;UAAKuC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAC,QAAA,gBAC7F7C,OAAA;YAAMuD,CAAC,EAAC,GAAG;YAACC,CAAC,EAAC,GAAG;YAACjB,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC1CpD,OAAA;YAASqD,MAAM,EAAC;UAA6B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC/CpD,OAAA;YAAQ8C,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,MAAM;YAACC,CAAC,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACpCpD,OAAA;YAAQ8C,EAAE,EAAC,MAAM;YAACC,EAAE,EAAC,MAAM;YAACC,CAAC,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAEd,KAAK,WAAW;QACZ,oBACIpD,OAAA;UAAKuC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAC,QAAA,eAC7F7C,OAAA;YAAUqD,MAAM,EAAC;UAAgB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAEd,KAAK,WAAW;QACZ,oBACIpD,OAAA;UAAKuC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAC,QAAA,gBAC7F7C,OAAA;YAAMyD,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACrCpD,OAAA;YAAMyD,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAEd;QACI,oBACIpD,OAAA;UAAKuC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAC,QAAA,eAC7F7C,OAAA;YAAQ8C,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;IAElB;EACJ,CAAC;EAED,MAAMS,cAAc,GAAGzD,MAAM,CAACM,MAAM,CAACoD,KAAK,IAAI;IAC1C,IAAIpD,MAAM,KAAK,KAAK,EAAE,OAAO,IAAI;IACjC,OAAOoD,KAAK,CAAC7C,MAAM,KAAKP,MAAM;EAClC,CAAC,CAAC;EAEF,MAAMqD,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEpD,QAAQ,GAAG,KAAK,KAAK;IACjD,OAAO,IAAIqD,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBvD,QAAQ,EAAEA;IACd,CAAC,CAAC,CAACwD,MAAM,CAACJ,MAAM,CAAC;EACrB,CAAC;EAED,IAAIjE,OAAO,EAAE;IACT,oBACIN,OAAA;MAAK4E,SAAS,EAAC,eAAe;MAAA/B,QAAA,gBAC1B7C,OAAA;QAAK4E,SAAS,EAAC,gBAAgB;QAAA/B,QAAA,eAC3B7C,OAAA;UAAA6C,QAAA,gBACI7C,OAAA;YAAI4E,SAAS,EAAC,eAAe;YAAA/B,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDpD,OAAA;YAAG4E,SAAS,EAAC,kBAAkB;YAAA/B,QAAA,EAAC;UAA2C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNpD,OAAA;QAAK4E,SAAS,EAAC,eAAe;QAAA/B,QAAA,gBAC1B7C,OAAA;UAAK4E,SAAS,EAAC,SAAS;UAACrC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAAAI,QAAA,eAC/D7C,OAAA;YAAQ8C,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACL,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACF,IAAI,EAAC,MAAM;YAACmC,eAAe,EAAC,IAAI;YAACC,gBAAgB,EAAC,IAAI;YAAAjC,QAAA,eACvH7C,OAAA;cAAS+E,aAAa,EAAC,mBAAmB;cAACC,GAAG,EAAC,IAAI;cAACC,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC;YAAY;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACNpD,OAAA;UAAA6C,QAAA,EAAG;QAAsB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIpD,OAAA;IAAK4E,SAAS,EAAC,eAAe;IAAA/B,QAAA,gBAC1B7C,OAAA;MAAK4E,SAAS,EAAC,gBAAgB;MAAA/B,QAAA,gBAC3B7C,OAAA;QAAA6C,QAAA,gBACI7C,OAAA;UAAI4E,SAAS,EAAC,eAAe;UAAA/B,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDpD,OAAA;UAAG4E,SAAS,EAAC,kBAAkB;UAAA/B,QAAA,EAAC;QAA2C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eACNpD,OAAA;QAAK4E,SAAS,EAAC,eAAe;QAAA/B,QAAA,eAC1B7C,OAAA;UACI4E,SAAS,EAAC,eAAe;UACzBO,KAAK,EAAEzE,MAAO;UACd0E,QAAQ,EAAGC,CAAC,IAAK1E,SAAS,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAtC,QAAA,gBAE3C7C,OAAA;YAAQmF,KAAK,EAAC,KAAK;YAAAtC,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvCpD,OAAA;YAAQmF,KAAK,EAAC,SAAS;YAAAtC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCpD,OAAA;YAAQmF,KAAK,EAAC,YAAY;YAAAtC,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9CpD,OAAA;YAAQmF,KAAK,EAAC,SAAS;YAAAtC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCpD,OAAA;YAAQmF,KAAK,EAAC,WAAW;YAAAtC,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CpD,OAAA;YAAQmF,KAAK,EAAC,WAAW;YAAAtC,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELS,cAAc,CAAC0B,MAAM,KAAK,CAAC,gBACxBvF,OAAA;MAAK4E,SAAS,EAAC,aAAa;MAAA/B,QAAA,gBACxB7C,OAAA;QAAKuC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAAAC,QAAA,gBAC7F7C,OAAA;UAAMsD,CAAC,EAAC;QAA0E;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACpFpD,OAAA;UAAMuD,CAAC,EAAC,GAAG;UAACC,CAAC,EAAC,GAAG;UAACjB,KAAK,EAAC,GAAG;UAACC,MAAM,EAAC,GAAG;UAACgD,EAAE,EAAC,GAAG;UAACC,EAAE,EAAC;QAAG;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACNpD,OAAA;QAAA6C,QAAA,EAAI;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBpD,OAAA;QAAA6C,QAAA,EAAG;MAAiE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAAC,gBAENpD,OAAA;MAAK4E,SAAS,EAAC,aAAa;MAAA/B,QAAA,EACvBgB,cAAc,CAAC6B,GAAG,CAAC5B,KAAK,iBACrB9D,OAAA;QAAoB4E,SAAS,EAAC,YAAY;QAAA/B,QAAA,gBACtC7C,OAAA;UAAK4E,SAAS,EAAC,cAAc;UAAA/B,QAAA,gBACzB7C,OAAA;YAAK4E,SAAS,EAAC,YAAY;YAAA/B,QAAA,gBACvB7C,OAAA;cAAI4E,SAAS,EAAC,cAAc;cAAA/B,QAAA,EAAEiB,KAAK,CAAC/C;YAAW;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDpD,OAAA;cAAG4E,SAAS,EAAC,YAAY;cAAA/B,QAAA,EAAEkB,UAAU,CAACD,KAAK,CAAC9C,IAAI;YAAC;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNpD,OAAA;YAAK4E,SAAS,EAAC,cAAc;YAAA/B,QAAA,eACzB7C,OAAA;cACI4E,SAAS,EAAC,cAAc;cACxBF,KAAK,EAAE;gBACHiB,eAAe,EAAE,GAAGtD,cAAc,CAACyB,KAAK,CAAC7C,MAAM,CAAC,IAAI;gBACpD2E,KAAK,EAAEvD,cAAc,CAACyB,KAAK,CAAC7C,MAAM,CAAC;gBACnC4E,MAAM,EAAE,aAAaxD,cAAc,CAACyB,KAAK,CAAC7C,MAAM,CAAC;cACrD,CAAE;cAAA4B,QAAA,GAEDP,aAAa,CAACwB,KAAK,CAAC7C,MAAM,CAAC,EAC3B6C,KAAK,CAAC7C,MAAM,CAAC6E,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGjC,KAAK,CAAC7C,MAAM,CAAC+E,KAAK,CAAC,CAAC,CAAC;YAAA;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENpD,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAA/B,QAAA,EACvBiB,KAAK,CAAC1C,KAAK,CAACsE,GAAG,CAACO,IAAI,iBACjBjG,OAAA;YAAmB4E,SAAS,EAAC,YAAY;YAAA/B,QAAA,gBACrC7C,OAAA;cAAK4E,SAAS,EAAC,YAAY;cAAA/B,QAAA,eACvB7C,OAAA;gBAAK4E,SAAS,EAAC,mBAAmB;gBAAA/B,QAAA,eAC9B7C,OAAA;kBAAKuC,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAC,QAAA,gBAC7F7C,OAAA;oBAAMuD,CAAC,EAAC,GAAG;oBAACC,CAAC,EAAC,GAAG;oBAACjB,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACgD,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC;kBAAG;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACxDpD,OAAA;oBAAQ8C,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,KAAK;oBAACC,CAAC,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACnCpD,OAAA;oBAAUqD,MAAM,EAAC;kBAAkB;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNpD,OAAA;cAAK4E,SAAS,EAAC,cAAc;cAAA/B,QAAA,gBACzB7C,OAAA;gBAAI4E,SAAS,EAAC,WAAW;gBAAA/B,QAAA,EAAEoD,IAAI,CAAC5E;cAAI;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1CpD,OAAA;gBAAG4E,SAAS,EAAC,eAAe;gBAAA/B,QAAA,GAAC,OAAK,EAACoD,IAAI,CAAC3E,QAAQ;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNpD,OAAA;cAAK4E,SAAS,EAAC,YAAY;cAAA/B,QAAA,EACtByB,cAAc,CAAC2B,IAAI,CAAC1E,KAAK,EAAEuC,KAAK,CAAC3C,QAAQ;YAAC;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA,GAhBA6C,IAAI,CAACnF,EAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBZ,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENpD,OAAA;UAAK4E,SAAS,EAAC,cAAc;UAAA/B,QAAA,gBACzB7C,OAAA;YAAK4E,SAAS,EAAC,aAAa;YAAA/B,QAAA,eACxB7C,OAAA;cAAA6C,QAAA,GAAQ,SAAO,EAACyB,cAAc,CAACR,KAAK,CAAC5C,KAAK,EAAE4C,KAAK,CAAC3C,QAAQ,CAAC;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACNpD,OAAA;YAAK4E,SAAS,EAAC,eAAe;YAAA/B,QAAA,gBAC1B7C,OAAA;cACI4E,SAAS,EAAC,eAAe;cACzBsB,OAAO,EAAEA,CAAA,KAAMzF,gBAAgB,CAACqD,KAAK,CAAE;cAAAjB,QAAA,EAC1C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRU,KAAK,CAAC7C,MAAM,KAAK,WAAW,iBACzBjB,OAAA;cAAQ4E,SAAS,EAAC,aAAa;cAAA/B,QAAA,EAAC;YAEhC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA,GA7DAU,KAAK,CAAChD,EAAE;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8Db,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAClD,EAAA,CA3SID,YAAY;EAAA,QACGH,OAAO;AAAA;AAAAqG,EAAA,GADtBlG,YAAY;AA6SlB,eAAeA,YAAY;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}