{"ast": null, "code": "export { MeshBVH } from './core/MeshBVH.js';\nexport { MeshBVHVisualizer } from './objects/MeshBVHVisualizer.js';\nexport { CENTER, AVERAGE, SAH, NOT_INTERSECTED, INTERSECTED, CONTAINED } from './core/Constants.js';\nexport { getBVHExtremes, estimateMemoryInBytes, getJSONStructure, validateBounds } from './debug/Debug.js';\nexport { acceleratedRaycast, computeBoundsTree, disposeBoundsTree } from './utils/ExtensionUtilities.js';\nexport { getTriangleHitPointInfo } from './utils/TriangleUtilities.js';\nexport * from './math/ExtendedTriangle.js';\nexport * from './math/OrientedBox.js';\nexport * from './gpu/MeshBVHUniformStruct.js';\nexport * from './gpu/shaderFunctions.js';\nexport * from './gpu/VertexAttributeTexture.js';\nexport * from './utils/StaticGeometryGenerator.js';", "map": {"version": 3, "names": ["MeshBVH", "MeshBVHVisualizer", "CENTER", "AVERAGE", "SAH", "NOT_INTERSECTED", "INTERSECTED", "CONTAINED", "getBVHExtremes", "estimateMemoryInBytes", "getJSONStructure", "validateBounds", "acceleratedRaycast", "computeBoundsTree", "disposeBoundsTree", "getTriangleHitPointInfo"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/index.js"], "sourcesContent": ["export { MeshBVH } from './core/MeshBVH.js';\nexport { MeshBVHVisualizer } from './objects/MeshBVHVisualizer.js';\nexport { CENTER, AVERAGE, SAH, NOT_INTERSECTED, INTERSECTED, CONTAINED } from './core/Constants.js';\nexport { getBVHExtremes, estimateMemoryInBytes, getJSONStructure, validateBounds } from './debug/Debug.js';\nexport { acceleratedRaycast, computeBoundsTree, disposeBoundsTree } from './utils/ExtensionUtilities.js';\nexport { getTriangleHitPointInfo } from './utils/TriangleUtilities.js';\nexport * from './math/ExtendedTriangle.js';\nexport * from './math/OrientedBox.js';\nexport * from './gpu/MeshBVHUniformStruct.js';\nexport * from './gpu/shaderFunctions.js';\nexport * from './gpu/VertexAttributeTexture.js';\nexport * from './utils/StaticGeometryGenerator.js';\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,QAAQ,qBAAqB;AACnG,SAASC,cAAc,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,kBAAkB;AAC1G,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,+BAA+B;AACxG,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,cAAc,4BAA4B;AAC1C,cAAc,uBAAuB;AACrC,cAAc,+BAA+B;AAC7C,cAAc,0BAA0B;AACxC,cAAc,iCAAiC;AAC/C,cAAc,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}