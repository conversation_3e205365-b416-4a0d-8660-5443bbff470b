{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nconst isOrthographic = def => def && def.isOrthographicCamera;\nconst isBox3 = def => def && def.isBox3;\nconst context = /*#__PURE__*/React.createContext(null);\nfunction Bounds({\n  children,\n  damping = 6,\n  fit,\n  clip,\n  observe,\n  margin = 1.2,\n  eps = 0.01,\n  onFit\n}) {\n  const ref = React.useRef(null);\n  const {\n    camera,\n    invalidate,\n    size,\n    controls: controlsImpl\n  } = useThree();\n  const controls = controlsImpl;\n  const onFitRef = React.useRef(onFit);\n  onFitRef.current = onFit;\n  function equals(a, b) {\n    return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n  }\n  function damp(v, t, lambda, delta) {\n    v.x = THREE.MathUtils.damp(v.x, t.x, lambda, delta);\n    v.y = THREE.MathUtils.damp(v.y, t.y, lambda, delta);\n    v.z = THREE.MathUtils.damp(v.z, t.z, lambda, delta);\n  }\n  const [current] = React.useState(() => ({\n    animating: false,\n    focus: new THREE.Vector3(),\n    camera: new THREE.Vector3(),\n    zoom: 1\n  }));\n  const [goal] = React.useState(() => ({\n    focus: new THREE.Vector3(),\n    camera: new THREE.Vector3(),\n    zoom: 1\n  }));\n  const [box] = React.useState(() => new THREE.Box3());\n  const api = React.useMemo(() => {\n    function getSize() {\n      const size = box.getSize(new THREE.Vector3());\n      const center = box.getCenter(new THREE.Vector3());\n      const maxSize = Math.max(size.x, size.y, size.z);\n      const fitHeightDistance = isOrthographic(camera) ? maxSize * 4 : maxSize / (2 * Math.atan(Math.PI * camera.fov / 360));\n      const fitWidthDistance = isOrthographic(camera) ? maxSize * 4 : fitHeightDistance / camera.aspect;\n      const distance = margin * Math.max(fitHeightDistance, fitWidthDistance);\n      return {\n        box,\n        size,\n        center,\n        distance\n      };\n    }\n    return {\n      getSize,\n      refresh(object) {\n        if (isBox3(object)) box.copy(object);else {\n          const target = object || ref.current;\n          target.updateWorldMatrix(true, true);\n          box.setFromObject(target);\n        }\n        if (box.isEmpty()) {\n          const max = camera.position.length() || 10;\n          box.setFromCenterAndSize(new THREE.Vector3(), new THREE.Vector3(max, max, max));\n        }\n        if ((controls == null ? void 0 : controls.constructor.name) === 'OrthographicTrackballControls') {\n          // Put camera on a sphere along which it should move\n          const {\n            distance\n          } = getSize();\n          const direction = camera.position.clone().sub(controls.target).normalize().multiplyScalar(distance);\n          const newPos = controls.target.clone().add(direction);\n          camera.position.copy(newPos);\n        }\n        return this;\n      },\n      clip() {\n        const {\n          distance\n        } = getSize();\n        if (controls) controls.maxDistance = distance * 10;\n        camera.near = distance / 100;\n        camera.far = distance * 100;\n        camera.updateProjectionMatrix();\n        if (controls) controls.update();\n        invalidate();\n        return this;\n      },\n      to({\n        position,\n        target\n      }) {\n        current.camera.copy(camera.position);\n        const {\n          center\n        } = getSize();\n        goal.camera.set(...position);\n        if (target) {\n          goal.focus.set(...target);\n        } else {\n          goal.focus.copy(center);\n        }\n        if (damping) {\n          current.animating = true;\n        } else {\n          camera.position.set(...position);\n        }\n        return this;\n      },\n      fit() {\n        current.camera.copy(camera.position);\n        if (controls) current.focus.copy(controls.target);\n        const {\n          center,\n          distance\n        } = getSize();\n        const direction = center.clone().sub(camera.position).normalize().multiplyScalar(distance);\n        goal.camera.copy(center).sub(direction);\n        goal.focus.copy(center);\n        if (isOrthographic(camera)) {\n          current.zoom = camera.zoom;\n          let maxHeight = 0,\n            maxWidth = 0;\n          const vertices = [new THREE.Vector3(box.min.x, box.min.y, box.min.z), new THREE.Vector3(box.min.x, box.max.y, box.min.z), new THREE.Vector3(box.min.x, box.min.y, box.max.z), new THREE.Vector3(box.min.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.min.z), new THREE.Vector3(box.max.x, box.min.y, box.max.z), new THREE.Vector3(box.max.x, box.min.y, box.min.z)]; // Transform the center and each corner to camera space\n\n          center.applyMatrix4(camera.matrixWorldInverse);\n          for (const v of vertices) {\n            v.applyMatrix4(camera.matrixWorldInverse);\n            maxHeight = Math.max(maxHeight, Math.abs(v.y - center.y));\n            maxWidth = Math.max(maxWidth, Math.abs(v.x - center.x));\n          }\n          maxHeight *= 2;\n          maxWidth *= 2;\n          const zoomForHeight = (camera.top - camera.bottom) / maxHeight;\n          const zoomForWidth = (camera.right - camera.left) / maxWidth;\n          goal.zoom = Math.min(zoomForHeight, zoomForWidth) / margin;\n          if (!damping) {\n            camera.zoom = goal.zoom;\n            camera.updateProjectionMatrix();\n          }\n        }\n        if (damping) {\n          current.animating = true;\n        } else {\n          camera.position.copy(goal.camera);\n          camera.lookAt(goal.focus);\n          if (controls) {\n            controls.target.copy(goal.focus);\n            controls.update();\n          }\n        }\n        if (onFitRef.current) onFitRef.current(this.getSize());\n        invalidate();\n        return this;\n      }\n    };\n  }, [box, camera, controls, margin, damping, invalidate]);\n  React.useLayoutEffect(() => {\n    if (controls) {\n      // Try to prevent drag hijacking\n      const callback = () => current.animating = false;\n      controls.addEventListener('start', callback);\n      return () => controls.removeEventListener('start', callback);\n    }\n  }, [controls]); // Scale pointer on window resize\n\n  const count = React.useRef(0);\n  React.useLayoutEffect(() => {\n    if (observe || count.current++ === 0) {\n      api.refresh();\n      if (fit) api.fit();\n      if (clip) api.clip();\n    }\n  }, [size, clip, fit, observe, camera, controls]);\n  useFrame((state, delta) => {\n    if (current.animating) {\n      damp(current.focus, goal.focus, damping, delta);\n      damp(current.camera, goal.camera, damping, delta);\n      current.zoom = THREE.MathUtils.damp(current.zoom, goal.zoom, damping, delta);\n      camera.position.copy(current.camera);\n      if (isOrthographic(camera)) {\n        camera.zoom = current.zoom;\n        camera.updateProjectionMatrix();\n      }\n      if (!controls) {\n        camera.lookAt(current.focus);\n      } else {\n        controls.target.copy(current.focus);\n        controls.update();\n      }\n      invalidate();\n      if (isOrthographic(camera) && !(Math.abs(current.zoom - goal.zoom) < eps)) return;\n      if (!isOrthographic(camera) && !equals(current.camera, goal.camera)) return;\n      if (controls && !equals(current.focus, goal.focus)) return;\n      current.animating = false;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n}\nfunction useBounds() {\n  return React.useContext(context);\n}\nexport { Bounds, useBounds };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "useFrame", "isOrthographic", "def", "isOrthographicCamera", "isBox3", "context", "createContext", "Bounds", "children", "damping", "fit", "clip", "observe", "margin", "eps", "onFit", "ref", "useRef", "camera", "invalidate", "size", "controls", "controlsImpl", "onFitRef", "current", "equals", "a", "b", "Math", "abs", "x", "y", "z", "damp", "v", "t", "lambda", "delta", "MathUtils", "useState", "animating", "focus", "Vector3", "zoom", "goal", "box", "Box3", "api", "useMemo", "getSize", "center", "getCenter", "maxSize", "max", "fitHeightDistance", "atan", "PI", "fov", "fitWidthDistance", "aspect", "distance", "refresh", "object", "copy", "target", "updateWorldMatrix", "setFromObject", "isEmpty", "position", "length", "setFromCenterAndSize", "constructor", "name", "direction", "clone", "sub", "normalize", "multiplyScalar", "newPos", "add", "maxDistance", "near", "far", "updateProjectionMatrix", "update", "to", "set", "maxHeight", "max<PERSON><PERSON><PERSON>", "vertices", "min", "applyMatrix4", "matrixWorldInverse", "zoomForHeight", "top", "bottom", "zoomFor<PERSON><PERSON><PERSON>", "right", "left", "lookAt", "useLayoutEffect", "callback", "addEventListener", "removeEventListener", "count", "state", "createElement", "Provider", "value", "useBounds", "useContext"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Bounds.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst isOrthographic = def => def && def.isOrthographicCamera;\n\nconst isBox3 = def => def && def.isBox3;\n\nconst context = /*#__PURE__*/React.createContext(null);\nfunction Bounds({\n  children,\n  damping = 6,\n  fit,\n  clip,\n  observe,\n  margin = 1.2,\n  eps = 0.01,\n  onFit\n}) {\n  const ref = React.useRef(null);\n  const {\n    camera,\n    invalidate,\n    size,\n    controls: controlsImpl\n  } = useThree();\n  const controls = controlsImpl;\n  const onFitRef = React.useRef(onFit);\n  onFitRef.current = onFit;\n\n  function equals(a, b) {\n    return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n  }\n\n  function damp(v, t, lambda, delta) {\n    v.x = THREE.MathUtils.damp(v.x, t.x, lambda, delta);\n    v.y = THREE.MathUtils.damp(v.y, t.y, lambda, delta);\n    v.z = THREE.MathUtils.damp(v.z, t.z, lambda, delta);\n  }\n\n  const [current] = React.useState(() => ({\n    animating: false,\n    focus: new THREE.Vector3(),\n    camera: new THREE.Vector3(),\n    zoom: 1\n  }));\n  const [goal] = React.useState(() => ({\n    focus: new THREE.Vector3(),\n    camera: new THREE.Vector3(),\n    zoom: 1\n  }));\n  const [box] = React.useState(() => new THREE.Box3());\n  const api = React.useMemo(() => {\n    function getSize() {\n      const size = box.getSize(new THREE.Vector3());\n      const center = box.getCenter(new THREE.Vector3());\n      const maxSize = Math.max(size.x, size.y, size.z);\n      const fitHeightDistance = isOrthographic(camera) ? maxSize * 4 : maxSize / (2 * Math.atan(Math.PI * camera.fov / 360));\n      const fitWidthDistance = isOrthographic(camera) ? maxSize * 4 : fitHeightDistance / camera.aspect;\n      const distance = margin * Math.max(fitHeightDistance, fitWidthDistance);\n      return {\n        box,\n        size,\n        center,\n        distance\n      };\n    }\n\n    return {\n      getSize,\n\n      refresh(object) {\n        if (isBox3(object)) box.copy(object);else {\n          const target = object || ref.current;\n          target.updateWorldMatrix(true, true);\n          box.setFromObject(target);\n        }\n\n        if (box.isEmpty()) {\n          const max = camera.position.length() || 10;\n          box.setFromCenterAndSize(new THREE.Vector3(), new THREE.Vector3(max, max, max));\n        }\n\n        if ((controls == null ? void 0 : controls.constructor.name) === 'OrthographicTrackballControls') {\n          // Put camera on a sphere along which it should move\n          const {\n            distance\n          } = getSize();\n          const direction = camera.position.clone().sub(controls.target).normalize().multiplyScalar(distance);\n          const newPos = controls.target.clone().add(direction);\n          camera.position.copy(newPos);\n        }\n\n        return this;\n      },\n\n      clip() {\n        const {\n          distance\n        } = getSize();\n        if (controls) controls.maxDistance = distance * 10;\n        camera.near = distance / 100;\n        camera.far = distance * 100;\n        camera.updateProjectionMatrix();\n        if (controls) controls.update();\n        invalidate();\n        return this;\n      },\n\n      to({\n        position,\n        target\n      }) {\n        current.camera.copy(camera.position);\n        const {\n          center\n        } = getSize();\n        goal.camera.set(...position);\n\n        if (target) {\n          goal.focus.set(...target);\n        } else {\n          goal.focus.copy(center);\n        }\n\n        if (damping) {\n          current.animating = true;\n        } else {\n          camera.position.set(...position);\n        }\n\n        return this;\n      },\n\n      fit() {\n        current.camera.copy(camera.position);\n        if (controls) current.focus.copy(controls.target);\n        const {\n          center,\n          distance\n        } = getSize();\n        const direction = center.clone().sub(camera.position).normalize().multiplyScalar(distance);\n        goal.camera.copy(center).sub(direction);\n        goal.focus.copy(center);\n\n        if (isOrthographic(camera)) {\n          current.zoom = camera.zoom;\n          let maxHeight = 0,\n              maxWidth = 0;\n          const vertices = [new THREE.Vector3(box.min.x, box.min.y, box.min.z), new THREE.Vector3(box.min.x, box.max.y, box.min.z), new THREE.Vector3(box.min.x, box.min.y, box.max.z), new THREE.Vector3(box.min.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.max.z), new THREE.Vector3(box.max.x, box.max.y, box.min.z), new THREE.Vector3(box.max.x, box.min.y, box.max.z), new THREE.Vector3(box.max.x, box.min.y, box.min.z)]; // Transform the center and each corner to camera space\n\n          center.applyMatrix4(camera.matrixWorldInverse);\n\n          for (const v of vertices) {\n            v.applyMatrix4(camera.matrixWorldInverse);\n            maxHeight = Math.max(maxHeight, Math.abs(v.y - center.y));\n            maxWidth = Math.max(maxWidth, Math.abs(v.x - center.x));\n          }\n\n          maxHeight *= 2;\n          maxWidth *= 2;\n          const zoomForHeight = (camera.top - camera.bottom) / maxHeight;\n          const zoomForWidth = (camera.right - camera.left) / maxWidth;\n          goal.zoom = Math.min(zoomForHeight, zoomForWidth) / margin;\n\n          if (!damping) {\n            camera.zoom = goal.zoom;\n            camera.updateProjectionMatrix();\n          }\n        }\n\n        if (damping) {\n          current.animating = true;\n        } else {\n          camera.position.copy(goal.camera);\n          camera.lookAt(goal.focus);\n\n          if (controls) {\n            controls.target.copy(goal.focus);\n            controls.update();\n          }\n        }\n\n        if (onFitRef.current) onFitRef.current(this.getSize());\n        invalidate();\n        return this;\n      }\n\n    };\n  }, [box, camera, controls, margin, damping, invalidate]);\n  React.useLayoutEffect(() => {\n    if (controls) {\n      // Try to prevent drag hijacking\n      const callback = () => current.animating = false;\n\n      controls.addEventListener('start', callback);\n      return () => controls.removeEventListener('start', callback);\n    }\n  }, [controls]); // Scale pointer on window resize\n\n  const count = React.useRef(0);\n  React.useLayoutEffect(() => {\n    if (observe || count.current++ === 0) {\n      api.refresh();\n      if (fit) api.fit();\n      if (clip) api.clip();\n    }\n  }, [size, clip, fit, observe, camera, controls]);\n  useFrame((state, delta) => {\n    if (current.animating) {\n      damp(current.focus, goal.focus, damping, delta);\n      damp(current.camera, goal.camera, damping, delta);\n      current.zoom = THREE.MathUtils.damp(current.zoom, goal.zoom, damping, delta);\n      camera.position.copy(current.camera);\n\n      if (isOrthographic(camera)) {\n        camera.zoom = current.zoom;\n        camera.updateProjectionMatrix();\n      }\n\n      if (!controls) {\n        camera.lookAt(current.focus);\n      } else {\n        controls.target.copy(current.focus);\n        controls.update();\n      }\n\n      invalidate();\n      if (isOrthographic(camera) && !(Math.abs(current.zoom - goal.zoom) < eps)) return;\n      if (!isOrthographic(camera) && !equals(current.camera, goal.camera)) return;\n      if (controls && !equals(current.focus, goal.focus)) return;\n      current.animating = false;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children));\n}\nfunction useBounds() {\n  return React.useContext(context);\n}\n\nexport { Bounds, useBounds };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAEvD,MAAMC,cAAc,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,oBAAoB;AAE7D,MAAMC,MAAM,GAAGF,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACE,MAAM;AAEvC,MAAMC,OAAO,GAAG,aAAaR,KAAK,CAACS,aAAa,CAAC,IAAI,CAAC;AACtD,SAASC,MAAMA,CAAC;EACdC,QAAQ;EACRC,OAAO,GAAG,CAAC;EACXC,GAAG;EACHC,IAAI;EACJC,OAAO;EACPC,MAAM,GAAG,GAAG;EACZC,GAAG,GAAG,IAAI;EACVC;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGnB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM;IACJC,MAAM;IACNC,UAAU;IACVC,IAAI;IACJC,QAAQ,EAAEC;EACZ,CAAC,GAAGvB,QAAQ,CAAC,CAAC;EACd,MAAMsB,QAAQ,GAAGC,YAAY;EAC7B,MAAMC,QAAQ,GAAG1B,KAAK,CAACoB,MAAM,CAACF,KAAK,CAAC;EACpCQ,QAAQ,CAACC,OAAO,GAAGT,KAAK;EAExB,SAASU,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACpB,OAAOC,IAAI,CAACC,GAAG,CAACH,CAAC,CAACI,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC,GAAGhB,GAAG,IAAIc,IAAI,CAACC,GAAG,CAACH,CAAC,CAACK,CAAC,GAAGJ,CAAC,CAACI,CAAC,CAAC,GAAGjB,GAAG,IAAIc,IAAI,CAACC,GAAG,CAACH,CAAC,CAACM,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC,GAAGlB,GAAG;EAC5F;EAEA,SAASmB,IAAIA,CAACC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACjCH,CAAC,CAACJ,CAAC,GAAGhC,KAAK,CAACwC,SAAS,CAACL,IAAI,CAACC,CAAC,CAACJ,CAAC,EAAEK,CAAC,CAACL,CAAC,EAAEM,MAAM,EAAEC,KAAK,CAAC;IACnDH,CAAC,CAACH,CAAC,GAAGjC,KAAK,CAACwC,SAAS,CAACL,IAAI,CAACC,CAAC,CAACH,CAAC,EAAEI,CAAC,CAACJ,CAAC,EAAEK,MAAM,EAAEC,KAAK,CAAC;IACnDH,CAAC,CAACF,CAAC,GAAGlC,KAAK,CAACwC,SAAS,CAACL,IAAI,CAACC,CAAC,CAACF,CAAC,EAAEG,CAAC,CAACH,CAAC,EAAEI,MAAM,EAAEC,KAAK,CAAC;EACrD;EAEA,MAAM,CAACb,OAAO,CAAC,GAAG3B,KAAK,CAAC0C,QAAQ,CAAC,OAAO;IACtCC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE,IAAI3C,KAAK,CAAC4C,OAAO,CAAC,CAAC;IAC1BxB,MAAM,EAAE,IAAIpB,KAAK,CAAC4C,OAAO,CAAC,CAAC;IAC3BC,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;EACH,MAAM,CAACC,IAAI,CAAC,GAAG/C,KAAK,CAAC0C,QAAQ,CAAC,OAAO;IACnCE,KAAK,EAAE,IAAI3C,KAAK,CAAC4C,OAAO,CAAC,CAAC;IAC1BxB,MAAM,EAAE,IAAIpB,KAAK,CAAC4C,OAAO,CAAC,CAAC;IAC3BC,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;EACH,MAAM,CAACE,GAAG,CAAC,GAAGhD,KAAK,CAAC0C,QAAQ,CAAC,MAAM,IAAIzC,KAAK,CAACgD,IAAI,CAAC,CAAC,CAAC;EACpD,MAAMC,GAAG,GAAGlD,KAAK,CAACmD,OAAO,CAAC,MAAM;IAC9B,SAASC,OAAOA,CAAA,EAAG;MACjB,MAAM7B,IAAI,GAAGyB,GAAG,CAACI,OAAO,CAAC,IAAInD,KAAK,CAAC4C,OAAO,CAAC,CAAC,CAAC;MAC7C,MAAMQ,MAAM,GAAGL,GAAG,CAACM,SAAS,CAAC,IAAIrD,KAAK,CAAC4C,OAAO,CAAC,CAAC,CAAC;MACjD,MAAMU,OAAO,GAAGxB,IAAI,CAACyB,GAAG,CAACjC,IAAI,CAACU,CAAC,EAAEV,IAAI,CAACW,CAAC,EAAEX,IAAI,CAACY,CAAC,CAAC;MAChD,MAAMsB,iBAAiB,GAAGrD,cAAc,CAACiB,MAAM,CAAC,GAAGkC,OAAO,GAAG,CAAC,GAAGA,OAAO,IAAI,CAAC,GAAGxB,IAAI,CAAC2B,IAAI,CAAC3B,IAAI,CAAC4B,EAAE,GAAGtC,MAAM,CAACuC,GAAG,GAAG,GAAG,CAAC,CAAC;MACtH,MAAMC,gBAAgB,GAAGzD,cAAc,CAACiB,MAAM,CAAC,GAAGkC,OAAO,GAAG,CAAC,GAAGE,iBAAiB,GAAGpC,MAAM,CAACyC,MAAM;MACjG,MAAMC,QAAQ,GAAG/C,MAAM,GAAGe,IAAI,CAACyB,GAAG,CAACC,iBAAiB,EAAEI,gBAAgB,CAAC;MACvE,OAAO;QACLb,GAAG;QACHzB,IAAI;QACJ8B,MAAM;QACNU;MACF,CAAC;IACH;IAEA,OAAO;MACLX,OAAO;MAEPY,OAAOA,CAACC,MAAM,EAAE;QACd,IAAI1D,MAAM,CAAC0D,MAAM,CAAC,EAAEjB,GAAG,CAACkB,IAAI,CAACD,MAAM,CAAC,CAAC,KAAK;UACxC,MAAME,MAAM,GAAGF,MAAM,IAAI9C,GAAG,CAACQ,OAAO;UACpCwC,MAAM,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;UACpCpB,GAAG,CAACqB,aAAa,CAACF,MAAM,CAAC;QAC3B;QAEA,IAAInB,GAAG,CAACsB,OAAO,CAAC,CAAC,EAAE;UACjB,MAAMd,GAAG,GAAGnC,MAAM,CAACkD,QAAQ,CAACC,MAAM,CAAC,CAAC,IAAI,EAAE;UAC1CxB,GAAG,CAACyB,oBAAoB,CAAC,IAAIxE,KAAK,CAAC4C,OAAO,CAAC,CAAC,EAAE,IAAI5C,KAAK,CAAC4C,OAAO,CAACW,GAAG,EAAEA,GAAG,EAAEA,GAAG,CAAC,CAAC;QACjF;QAEA,IAAI,CAAChC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACkD,WAAW,CAACC,IAAI,MAAM,+BAA+B,EAAE;UAC/F;UACA,MAAM;YACJZ;UACF,CAAC,GAAGX,OAAO,CAAC,CAAC;UACb,MAAMwB,SAAS,GAAGvD,MAAM,CAACkD,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACC,GAAG,CAACtD,QAAQ,CAAC2C,MAAM,CAAC,CAACY,SAAS,CAAC,CAAC,CAACC,cAAc,CAACjB,QAAQ,CAAC;UACnG,MAAMkB,MAAM,GAAGzD,QAAQ,CAAC2C,MAAM,CAACU,KAAK,CAAC,CAAC,CAACK,GAAG,CAACN,SAAS,CAAC;UACrDvD,MAAM,CAACkD,QAAQ,CAACL,IAAI,CAACe,MAAM,CAAC;QAC9B;QAEA,OAAO,IAAI;MACb,CAAC;MAEDnE,IAAIA,CAAA,EAAG;QACL,MAAM;UACJiD;QACF,CAAC,GAAGX,OAAO,CAAC,CAAC;QACb,IAAI5B,QAAQ,EAAEA,QAAQ,CAAC2D,WAAW,GAAGpB,QAAQ,GAAG,EAAE;QAClD1C,MAAM,CAAC+D,IAAI,GAAGrB,QAAQ,GAAG,GAAG;QAC5B1C,MAAM,CAACgE,GAAG,GAAGtB,QAAQ,GAAG,GAAG;QAC3B1C,MAAM,CAACiE,sBAAsB,CAAC,CAAC;QAC/B,IAAI9D,QAAQ,EAAEA,QAAQ,CAAC+D,MAAM,CAAC,CAAC;QAC/BjE,UAAU,CAAC,CAAC;QACZ,OAAO,IAAI;MACb,CAAC;MAEDkE,EAAEA,CAAC;QACDjB,QAAQ;QACRJ;MACF,CAAC,EAAE;QACDxC,OAAO,CAACN,MAAM,CAAC6C,IAAI,CAAC7C,MAAM,CAACkD,QAAQ,CAAC;QACpC,MAAM;UACJlB;QACF,CAAC,GAAGD,OAAO,CAAC,CAAC;QACbL,IAAI,CAAC1B,MAAM,CAACoE,GAAG,CAAC,GAAGlB,QAAQ,CAAC;QAE5B,IAAIJ,MAAM,EAAE;UACVpB,IAAI,CAACH,KAAK,CAAC6C,GAAG,CAAC,GAAGtB,MAAM,CAAC;QAC3B,CAAC,MAAM;UACLpB,IAAI,CAACH,KAAK,CAACsB,IAAI,CAACb,MAAM,CAAC;QACzB;QAEA,IAAIzC,OAAO,EAAE;UACXe,OAAO,CAACgB,SAAS,GAAG,IAAI;QAC1B,CAAC,MAAM;UACLtB,MAAM,CAACkD,QAAQ,CAACkB,GAAG,CAAC,GAAGlB,QAAQ,CAAC;QAClC;QAEA,OAAO,IAAI;MACb,CAAC;MAED1D,GAAGA,CAAA,EAAG;QACJc,OAAO,CAACN,MAAM,CAAC6C,IAAI,CAAC7C,MAAM,CAACkD,QAAQ,CAAC;QACpC,IAAI/C,QAAQ,EAAEG,OAAO,CAACiB,KAAK,CAACsB,IAAI,CAAC1C,QAAQ,CAAC2C,MAAM,CAAC;QACjD,MAAM;UACJd,MAAM;UACNU;QACF,CAAC,GAAGX,OAAO,CAAC,CAAC;QACb,MAAMwB,SAAS,GAAGvB,MAAM,CAACwB,KAAK,CAAC,CAAC,CAACC,GAAG,CAACzD,MAAM,CAACkD,QAAQ,CAAC,CAACQ,SAAS,CAAC,CAAC,CAACC,cAAc,CAACjB,QAAQ,CAAC;QAC1FhB,IAAI,CAAC1B,MAAM,CAAC6C,IAAI,CAACb,MAAM,CAAC,CAACyB,GAAG,CAACF,SAAS,CAAC;QACvC7B,IAAI,CAACH,KAAK,CAACsB,IAAI,CAACb,MAAM,CAAC;QAEvB,IAAIjD,cAAc,CAACiB,MAAM,CAAC,EAAE;UAC1BM,OAAO,CAACmB,IAAI,GAAGzB,MAAM,CAACyB,IAAI;UAC1B,IAAI4C,SAAS,GAAG,CAAC;YACbC,QAAQ,GAAG,CAAC;UAChB,MAAMC,QAAQ,GAAG,CAAC,IAAI3F,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAAC6C,GAAG,CAAC5D,CAAC,EAAEe,GAAG,CAAC6C,GAAG,CAAC3D,CAAC,EAAEc,GAAG,CAAC6C,GAAG,CAAC1D,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAAC6C,GAAG,CAAC5D,CAAC,EAAEe,GAAG,CAACQ,GAAG,CAACtB,CAAC,EAAEc,GAAG,CAAC6C,GAAG,CAAC1D,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAAC6C,GAAG,CAAC5D,CAAC,EAAEe,GAAG,CAAC6C,GAAG,CAAC3D,CAAC,EAAEc,GAAG,CAACQ,GAAG,CAACrB,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAAC6C,GAAG,CAAC5D,CAAC,EAAEe,GAAG,CAACQ,GAAG,CAACtB,CAAC,EAAEc,GAAG,CAACQ,GAAG,CAACrB,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAACQ,GAAG,CAACvB,CAAC,EAAEe,GAAG,CAACQ,GAAG,CAACtB,CAAC,EAAEc,GAAG,CAACQ,GAAG,CAACrB,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAACQ,GAAG,CAACvB,CAAC,EAAEe,GAAG,CAACQ,GAAG,CAACtB,CAAC,EAAEc,GAAG,CAAC6C,GAAG,CAAC1D,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAACQ,GAAG,CAACvB,CAAC,EAAEe,GAAG,CAAC6C,GAAG,CAAC3D,CAAC,EAAEc,GAAG,CAACQ,GAAG,CAACrB,CAAC,CAAC,EAAE,IAAIlC,KAAK,CAAC4C,OAAO,CAACG,GAAG,CAACQ,GAAG,CAACvB,CAAC,EAAEe,GAAG,CAAC6C,GAAG,CAAC3D,CAAC,EAAEc,GAAG,CAAC6C,GAAG,CAAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnbkB,MAAM,CAACyC,YAAY,CAACzE,MAAM,CAAC0E,kBAAkB,CAAC;UAE9C,KAAK,MAAM1D,CAAC,IAAIuD,QAAQ,EAAE;YACxBvD,CAAC,CAACyD,YAAY,CAACzE,MAAM,CAAC0E,kBAAkB,CAAC;YACzCL,SAAS,GAAG3D,IAAI,CAACyB,GAAG,CAACkC,SAAS,EAAE3D,IAAI,CAACC,GAAG,CAACK,CAAC,CAACH,CAAC,GAAGmB,MAAM,CAACnB,CAAC,CAAC,CAAC;YACzDyD,QAAQ,GAAG5D,IAAI,CAACyB,GAAG,CAACmC,QAAQ,EAAE5D,IAAI,CAACC,GAAG,CAACK,CAAC,CAACJ,CAAC,GAAGoB,MAAM,CAACpB,CAAC,CAAC,CAAC;UACzD;UAEAyD,SAAS,IAAI,CAAC;UACdC,QAAQ,IAAI,CAAC;UACb,MAAMK,aAAa,GAAG,CAAC3E,MAAM,CAAC4E,GAAG,GAAG5E,MAAM,CAAC6E,MAAM,IAAIR,SAAS;UAC9D,MAAMS,YAAY,GAAG,CAAC9E,MAAM,CAAC+E,KAAK,GAAG/E,MAAM,CAACgF,IAAI,IAAIV,QAAQ;UAC5D5C,IAAI,CAACD,IAAI,GAAGf,IAAI,CAAC8D,GAAG,CAACG,aAAa,EAAEG,YAAY,CAAC,GAAGnF,MAAM;UAE1D,IAAI,CAACJ,OAAO,EAAE;YACZS,MAAM,CAACyB,IAAI,GAAGC,IAAI,CAACD,IAAI;YACvBzB,MAAM,CAACiE,sBAAsB,CAAC,CAAC;UACjC;QACF;QAEA,IAAI1E,OAAO,EAAE;UACXe,OAAO,CAACgB,SAAS,GAAG,IAAI;QAC1B,CAAC,MAAM;UACLtB,MAAM,CAACkD,QAAQ,CAACL,IAAI,CAACnB,IAAI,CAAC1B,MAAM,CAAC;UACjCA,MAAM,CAACiF,MAAM,CAACvD,IAAI,CAACH,KAAK,CAAC;UAEzB,IAAIpB,QAAQ,EAAE;YACZA,QAAQ,CAAC2C,MAAM,CAACD,IAAI,CAACnB,IAAI,CAACH,KAAK,CAAC;YAChCpB,QAAQ,CAAC+D,MAAM,CAAC,CAAC;UACnB;QACF;QAEA,IAAI7D,QAAQ,CAACC,OAAO,EAAED,QAAQ,CAACC,OAAO,CAAC,IAAI,CAACyB,OAAO,CAAC,CAAC,CAAC;QACtD9B,UAAU,CAAC,CAAC;QACZ,OAAO,IAAI;MACb;IAEF,CAAC;EACH,CAAC,EAAE,CAAC0B,GAAG,EAAE3B,MAAM,EAAEG,QAAQ,EAAER,MAAM,EAAEJ,OAAO,EAAEU,UAAU,CAAC,CAAC;EACxDtB,KAAK,CAACuG,eAAe,CAAC,MAAM;IAC1B,IAAI/E,QAAQ,EAAE;MACZ;MACA,MAAMgF,QAAQ,GAAGA,CAAA,KAAM7E,OAAO,CAACgB,SAAS,GAAG,KAAK;MAEhDnB,QAAQ,CAACiF,gBAAgB,CAAC,OAAO,EAAED,QAAQ,CAAC;MAC5C,OAAO,MAAMhF,QAAQ,CAACkF,mBAAmB,CAAC,OAAO,EAAEF,QAAQ,CAAC;IAC9D;EACF,CAAC,EAAE,CAAChF,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhB,MAAMmF,KAAK,GAAG3G,KAAK,CAACoB,MAAM,CAAC,CAAC,CAAC;EAC7BpB,KAAK,CAACuG,eAAe,CAAC,MAAM;IAC1B,IAAIxF,OAAO,IAAI4F,KAAK,CAAChF,OAAO,EAAE,KAAK,CAAC,EAAE;MACpCuB,GAAG,CAACc,OAAO,CAAC,CAAC;MACb,IAAInD,GAAG,EAAEqC,GAAG,CAACrC,GAAG,CAAC,CAAC;MAClB,IAAIC,IAAI,EAAEoC,GAAG,CAACpC,IAAI,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACS,IAAI,EAAET,IAAI,EAAED,GAAG,EAAEE,OAAO,EAAEM,MAAM,EAAEG,QAAQ,CAAC,CAAC;EAChDrB,QAAQ,CAAC,CAACyG,KAAK,EAAEpE,KAAK,KAAK;IACzB,IAAIb,OAAO,CAACgB,SAAS,EAAE;MACrBP,IAAI,CAACT,OAAO,CAACiB,KAAK,EAAEG,IAAI,CAACH,KAAK,EAAEhC,OAAO,EAAE4B,KAAK,CAAC;MAC/CJ,IAAI,CAACT,OAAO,CAACN,MAAM,EAAE0B,IAAI,CAAC1B,MAAM,EAAET,OAAO,EAAE4B,KAAK,CAAC;MACjDb,OAAO,CAACmB,IAAI,GAAG7C,KAAK,CAACwC,SAAS,CAACL,IAAI,CAACT,OAAO,CAACmB,IAAI,EAAEC,IAAI,CAACD,IAAI,EAAElC,OAAO,EAAE4B,KAAK,CAAC;MAC5EnB,MAAM,CAACkD,QAAQ,CAACL,IAAI,CAACvC,OAAO,CAACN,MAAM,CAAC;MAEpC,IAAIjB,cAAc,CAACiB,MAAM,CAAC,EAAE;QAC1BA,MAAM,CAACyB,IAAI,GAAGnB,OAAO,CAACmB,IAAI;QAC1BzB,MAAM,CAACiE,sBAAsB,CAAC,CAAC;MACjC;MAEA,IAAI,CAAC9D,QAAQ,EAAE;QACbH,MAAM,CAACiF,MAAM,CAAC3E,OAAO,CAACiB,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLpB,QAAQ,CAAC2C,MAAM,CAACD,IAAI,CAACvC,OAAO,CAACiB,KAAK,CAAC;QACnCpB,QAAQ,CAAC+D,MAAM,CAAC,CAAC;MACnB;MAEAjE,UAAU,CAAC,CAAC;MACZ,IAAIlB,cAAc,CAACiB,MAAM,CAAC,IAAI,EAAEU,IAAI,CAACC,GAAG,CAACL,OAAO,CAACmB,IAAI,GAAGC,IAAI,CAACD,IAAI,CAAC,GAAG7B,GAAG,CAAC,EAAE;MAC3E,IAAI,CAACb,cAAc,CAACiB,MAAM,CAAC,IAAI,CAACO,MAAM,CAACD,OAAO,CAACN,MAAM,EAAE0B,IAAI,CAAC1B,MAAM,CAAC,EAAE;MACrE,IAAIG,QAAQ,IAAI,CAACI,MAAM,CAACD,OAAO,CAACiB,KAAK,EAAEG,IAAI,CAACH,KAAK,CAAC,EAAE;MACpDjB,OAAO,CAACgB,SAAS,GAAG,KAAK;IAC3B;EACF,CAAC,CAAC;EACF,OAAO,aAAa3C,KAAK,CAAC6G,aAAa,CAAC,OAAO,EAAE;IAC/C1F,GAAG,EAAEA;EACP,CAAC,EAAE,aAAanB,KAAK,CAAC6G,aAAa,CAACrG,OAAO,CAACsG,QAAQ,EAAE;IACpDC,KAAK,EAAE7D;EACT,CAAC,EAAEvC,QAAQ,CAAC,CAAC;AACf;AACA,SAASqG,SAASA,CAAA,EAAG;EACnB,OAAOhH,KAAK,CAACiH,UAAU,CAACzG,OAAO,CAAC;AAClC;AAEA,SAASE,MAAM,EAAEsG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}