const axios = require('axios');
const ActivityLogService = require('./services/activityLogService');
const { connectDB } = require('./config/database');

class ActivityLogsTestSuite {
  constructor() {
    this.baseURL = 'http://localhost:8000';
    this.token = null;
    this.testResults = {
      database: { passed: 0, failed: 0, tests: [] },
      api: { passed: 0, failed: 0, tests: [] },
      middleware: { passed: 0, failed: 0, tests: [] },
      integration: { passed: 0, failed: 0, tests: [] }
    };
  }

  async runAllTests() {
    console.log('🧪 Starting Comprehensive Activity Logs Test Suite');
    console.log('=' .repeat(60));

    try {
      // 1. Database Tests
      await this.testDatabaseConnectivity();
      await this.testActivityLogService();

      // 2. Authentication (required for API tests)
      await this.authenticate();

      // 3. API Endpoint Tests
      await this.testActivityLogsAPI();
      await this.testActivityStatsAPI();
      await this.testFilterOptionsAPI();

      // 4. Middleware Integration Tests
      await this.testMiddlewareIntegration();

      // 5. Integration Tests
      await this.testCompleteFlow();

      // Print summary
      this.printTestSummary();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    }
  }

  // Database Tests
  async testDatabaseConnectivity() {
    console.log('\n📊 Testing Database Connectivity...');
    
    try {
      await connectDB();
      this.recordTest('database', 'Database Connection', true, 'Connected successfully');
    } catch (error) {
      this.recordTest('database', 'Database Connection', false, error.message);
    }
  }

  async testActivityLogService() {
    console.log('\n🔧 Testing ActivityLogService...');

    // Test logging activity
    try {
      await ActivityLogService.logActivity({
        userEmail: '<EMAIL>',
        userName: 'Test User',
        userRole: 'Admin',
        action: 'TEST',
        entityType: 'System',
        description: 'Test activity log entry',
        severity: 'INFO'
      });
      this.recordTest('database', 'Log Activity', true, 'Activity logged successfully');
    } catch (error) {
      this.recordTest('database', 'Log Activity', false, error.message);
    }

    // Test getting activity logs
    try {
      const result = await ActivityLogService.getActivityLogs({ limit: 5 });
      const hasLogs = result.logs && result.logs.length > 0;
      this.recordTest('database', 'Get Activity Logs', hasLogs, 
        hasLogs ? `Retrieved ${result.logs.length} logs` : 'No logs found');
    } catch (error) {
      this.recordTest('database', 'Get Activity Logs', false, error.message);
    }

    // Test getting statistics
    try {
      const stats = await ActivityLogService.getActivityStats();
      const hasStats = stats && stats.overview;
      this.recordTest('database', 'Get Activity Stats', hasStats, 
        hasStats ? `Stats retrieved: ${stats.overview.totalActivities} total activities` : 'No stats found');
    } catch (error) {
      this.recordTest('database', 'Get Activity Stats', false, error.message);
    }
  }

  // Authentication
  async authenticate() {
    console.log('\n🔐 Authenticating for API tests...');
    
    try {
      const response = await axios.post(`${this.baseURL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });

      if (response.data.success) {
        this.token = response.data.data.token;
        this.recordTest('api', 'Authentication', true, 'Login successful');
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      this.recordTest('api', 'Authentication', false, error.response?.data?.message || error.message);
      throw error; // Stop tests if authentication fails
    }
  }

  // API Tests
  async testActivityLogsAPI() {
    console.log('\n📋 Testing Activity Logs API...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };

    // Test basic endpoint
    try {
      const response = await axios.get(`${this.baseURL}/api/admin/activity-logs`, { headers });
      const success = response.data.success && response.data.data.logs;
      this.recordTest('api', 'GET /api/admin/activity-logs', success, 
        success ? `Retrieved ${response.data.data.logs.length} logs` : 'Invalid response structure');
    } catch (error) {
      this.recordTest('api', 'GET /api/admin/activity-logs', false, error.response?.data?.message || error.message);
    }

    // Test with filters
    try {
      const response = await axios.get(`${this.baseURL}/api/admin/activity-logs?action=LOGIN&limit=10`, { headers });
      const success = response.data.success;
      this.recordTest('api', 'Activity Logs with Filters', success, 
        success ? 'Filtering works correctly' : 'Filter failed');
    } catch (error) {
      this.recordTest('api', 'Activity Logs with Filters', false, error.response?.data?.message || error.message);
    }

    // Test pagination
    try {
      const response = await axios.get(`${this.baseURL}/api/admin/activity-logs?page=1&limit=5`, { headers });
      const success = response.data.success && response.data.data.pagination;
      this.recordTest('api', 'Activity Logs Pagination', success, 
        success ? `Page ${response.data.data.pagination.currentPage} of ${response.data.data.pagination.totalPages}` : 'Pagination failed');
    } catch (error) {
      this.recordTest('api', 'Activity Logs Pagination', false, error.response?.data?.message || error.message);
    }
  }

  async testActivityStatsAPI() {
    console.log('\n📊 Testing Activity Stats API...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };

    try {
      const response = await axios.get(`${this.baseURL}/api/admin/activity-logs/stats`, { headers });
      const success = response.data.success && response.data.data.overview;
      this.recordTest('api', 'GET /api/admin/activity-logs/stats', success, 
        success ? `Total activities: ${response.data.data.overview.totalActivities}` : 'Invalid stats structure');
    } catch (error) {
      this.recordTest('api', 'GET /api/admin/activity-logs/stats', false, error.response?.data?.message || error.message);
    }
  }

  async testFilterOptionsAPI() {
    console.log('\n🎯 Testing Filter Options APIs...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };

    // Test actions endpoint
    try {
      const response = await axios.get(`${this.baseURL}/api/admin/activity-logs/actions`, { headers });
      const success = response.data.success && Array.isArray(response.data.data);
      this.recordTest('api', 'GET /api/admin/activity-logs/actions', success, 
        success ? `${response.data.data.length} actions available` : 'Invalid actions response');
    } catch (error) {
      this.recordTest('api', 'GET /api/admin/activity-logs/actions', false, error.response?.data?.message || error.message);
    }

    // Test entity types endpoint
    try {
      const response = await axios.get(`${this.baseURL}/api/admin/activity-logs/entity-types`, { headers });
      const success = response.data.success && Array.isArray(response.data.data);
      this.recordTest('api', 'GET /api/admin/activity-logs/entity-types', success, 
        success ? `${response.data.data.length} entity types available` : 'Invalid entity types response');
    } catch (error) {
      this.recordTest('api', 'GET /api/admin/activity-logs/entity-types', false, error.response?.data?.message || error.message);
    }
  }

  // Middleware Integration Tests
  async testMiddlewareIntegration() {
    console.log('\n🔗 Testing Middleware Integration...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };

    // Test that login creates activity log
    try {
      // Get current log count
      const beforeResponse = await axios.get(`${this.baseURL}/api/admin/activity-logs?action=LOGIN&limit=1`, { headers });
      const beforeCount = beforeResponse.data.data.totalItems || 0;

      // Perform login (this should create an activity log)
      await axios.post(`${this.baseURL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });

      // Check if new log was created
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for async logging
      const afterResponse = await axios.get(`${this.baseURL}/api/admin/activity-logs?action=LOGIN&limit=1`, { headers });
      const afterCount = afterResponse.data.data.totalItems || 0;

      const success = afterCount > beforeCount;
      this.recordTest('middleware', 'Login Activity Logging', success, 
        success ? 'Login activity logged successfully' : 'Login activity not logged');
    } catch (error) {
      this.recordTest('middleware', 'Login Activity Logging', false, error.message);
    }

    // Test dashboard access logging
    try {
      const beforeResponse = await axios.get(`${this.baseURL}/api/admin/activity-logs?action=VIEW&entityType=Dashboard&limit=1`, { headers });
      const beforeCount = beforeResponse.data.data.totalItems || 0;

      // Access dashboard
      await axios.get(`${this.baseURL}/api/admin/dashboard`, { headers });

      await new Promise(resolve => setTimeout(resolve, 1000));
      const afterResponse = await axios.get(`${this.baseURL}/api/admin/activity-logs?action=VIEW&entityType=Dashboard&limit=1`, { headers });
      const afterCount = afterResponse.data.data.totalItems || 0;

      const success = afterCount > beforeCount;
      this.recordTest('middleware', 'Dashboard Access Logging', success, 
        success ? 'Dashboard access logged successfully' : 'Dashboard access not logged');
    } catch (error) {
      this.recordTest('middleware', 'Dashboard Access Logging', false, error.message);
    }
  }

  // Integration Tests
  async testCompleteFlow() {
    console.log('\n🔄 Testing Complete Integration Flow...');
    
    const headers = { 'Authorization': `Bearer ${this.token}` };

    try {
      // 1. Perform an action that should be logged
      await axios.get(`${this.baseURL}/api/admin/dashboard`, { headers });

      // 2. Wait for async logging
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 3. Retrieve logs and verify the action appears
      const response = await axios.get(`${this.baseURL}/api/admin/activity-logs?limit=10`, { headers });
      
      if (response.data.success) {
        const recentLogs = response.data.data.logs;
        const dashboardLog = recentLogs.find(log => 
          log.Action === 'VIEW' && log.EntityType === 'Dashboard'
        );

        const success = !!dashboardLog;
        this.recordTest('integration', 'Complete Flow Test', success, 
          success ? 'Action → Log → Retrieve flow working' : 'Complete flow failed');
      } else {
        this.recordTest('integration', 'Complete Flow Test', false, 'Failed to retrieve logs');
      }
    } catch (error) {
      this.recordTest('integration', 'Complete Flow Test', false, error.message);
    }
  }

  // Helper methods
  recordTest(category, testName, passed, message) {
    const result = { testName, passed, message };
    this.testResults[category].tests.push(result);
    
    if (passed) {
      this.testResults[category].passed++;
      console.log(`  ✅ ${testName}: ${message}`);
    } else {
      this.testResults[category].failed++;
      console.log(`  ❌ ${testName}: ${message}`);
    }
  }

  printTestSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));

    let totalPassed = 0;
    let totalFailed = 0;

    Object.keys(this.testResults).forEach(category => {
      const results = this.testResults[category];
      totalPassed += results.passed;
      totalFailed += results.failed;

      console.log(`\n${category.toUpperCase()}:`);
      console.log(`  ✅ Passed: ${results.passed}`);
      console.log(`  ❌ Failed: ${results.failed}`);
      console.log(`  📊 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
    });

    console.log('\n' + '-'.repeat(40));
    console.log(`OVERALL RESULTS:`);
    console.log(`✅ Total Passed: ${totalPassed}`);
    console.log(`❌ Total Failed: ${totalFailed}`);
    console.log(`📊 Overall Success Rate: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
    
    if (totalFailed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! Activity Logs system is fully operational.');
    } else {
      console.log(`\n⚠️  ${totalFailed} test(s) failed. Please review the issues above.`);
    }
  }
}

// Run the test suite
const testSuite = new ActivityLogsTestSuite();
testSuite.runAllTests().catch(console.error);
