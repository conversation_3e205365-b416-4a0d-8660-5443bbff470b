[{"C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js": "1", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js": "2", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js": "3", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js": "4", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js": "5", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js": "6", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js": "7", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js": "8", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js": "9", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js": "10", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js": "11", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js": "12", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js": "13", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "14", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js": "15", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js": "16", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js": "17", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js": "18", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js": "19", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js": "20", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js": "21", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js": "22", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js": "23", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js": "24", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js": "25", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js": "26", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js": "27", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js": "28", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js": "29", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js": "30", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js": "31", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js": "32", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js": "33", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js": "34", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js": "35", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js": "36", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js": "37", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js": "38", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js": "39", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js": "40", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js": "41", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js": "42", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js": "43", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js": "44", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js": "45", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js": "46", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js": "47", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js": "48", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js": "49", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js": "50", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js": "51", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js": "52", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js": "53", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js": "54", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js": "55", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js": "56", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js": "57", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js": "58", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js": "59", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js": "60", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js": "61", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js": "62", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js": "63", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductDetailsModal.js": "64", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductFormModal.js": "65", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js": "66", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js": "67", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js": "68", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js": "69", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js": "70", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js": "71", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ActivityLogs.js": "72", "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\ActivityLogIcons.js": "73", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js": "74", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js": "75", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js": "76", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js": "77", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js": "78", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js": "79", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js": "80", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js": "81", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js": "82", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js": "83", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js": "84", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js": "85", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js": "86", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js": "87", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js": "88", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js": "89", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js": "90", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js": "91", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js": "92", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js": "93", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js": "94", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js": "95", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js": "96", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js": "97", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js": "98", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js": "99", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js": "100", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js": "101", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js": "102", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js": "103", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js": "104", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js": "105", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js": "106", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js": "107", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js": "108", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js": "109", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js": "110", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js": "111", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js": "112", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js": "113", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js": "114", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js": "115", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js": "116", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js": "117", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js": "118", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js": "119", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js": "120", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js": "121", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js": "122", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ActivityLogs.js": "123", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js": "124", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js": "125", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js": "126", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js": "127", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js": "128", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js": "129", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js": "130", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js": "131", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js": "132", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js": "133", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js": "134", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js": "135", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\ActivityLogIcons.js": "136", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js": "137", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js": "138", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js": "139", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js": "140", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js": "141", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js": "142", "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js": "143"}, {"size": 535, "mtime": 1748532508000, "results": "144", "hashOfConfig": "145"}, {"size": 362, "mtime": 1748532510000, "results": "146", "hashOfConfig": "145"}, {"size": 7288, "mtime": 1752550163170, "results": "147", "hashOfConfig": "145"}, {"size": 5553, "mtime": 1748536576000, "results": "148", "hashOfConfig": "145"}, {"size": 4139, "mtime": 1751076622000, "results": "149", "hashOfConfig": "145"}, {"size": 6574, "mtime": 1751098548000, "results": "150", "hashOfConfig": "145"}, {"size": 22019, "mtime": 1751370338000, "results": "151", "hashOfConfig": "145"}, {"size": 9763, "mtime": 1751248890000, "results": "152", "hashOfConfig": "145"}, {"size": 4954, "mtime": 1752549460528, "results": "153", "hashOfConfig": "145"}, {"size": 18118, "mtime": 1751248890000, "results": "154", "hashOfConfig": "145"}, {"size": 14768, "mtime": 1751099198000, "results": "155", "hashOfConfig": "145"}, {"size": 7100, "mtime": 1750777438000, "results": "156", "hashOfConfig": "145"}, {"size": 13424, "mtime": 1748592600000, "results": "157", "hashOfConfig": "145"}, {"size": 23206, "mtime": 1751291576000, "results": "158", "hashOfConfig": "145"}, {"size": 5296, "mtime": 1751375904000, "results": "159", "hashOfConfig": "145"}, {"size": 11069, "mtime": 1751278342000, "results": "160", "hashOfConfig": "145"}, {"size": 9980, "mtime": 1751289364000, "results": "161", "hashOfConfig": "145"}, {"size": 11059, "mtime": 1750776056000, "results": "162", "hashOfConfig": "145"}, {"size": 8538, "mtime": 1752551742429, "results": "163", "hashOfConfig": "145"}, {"size": 8266, "mtime": 1748588852000, "results": "164", "hashOfConfig": "145"}, {"size": 5949, "mtime": 1748749522000, "results": "165", "hashOfConfig": "145"}, {"size": 19148, "mtime": 1751447640000, "results": "166", "hashOfConfig": "145"}, {"size": 1304, "mtime": 1751274452000, "results": "167", "hashOfConfig": "145"}, {"size": 1283, "mtime": 1751274444000, "results": "168", "hashOfConfig": "145"}, {"size": 809, "mtime": 1751274436000, "results": "169", "hashOfConfig": "145"}, {"size": 4827, "mtime": 1751274510000, "results": "170", "hashOfConfig": "145"}, {"size": 5314, "mtime": 1751292028000, "results": "171", "hashOfConfig": "145"}, {"size": 53614, "mtime": 1751251970000, "results": "172", "hashOfConfig": "145"}, {"size": 7098, "mtime": 1751277960000, "results": "173", "hashOfConfig": "145"}, {"size": 11351, "mtime": 1751114552000, "results": "174", "hashOfConfig": "145"}, {"size": 3375, "mtime": 1751249990000, "results": "175", "hashOfConfig": "145"}, {"size": 2764, "mtime": 1752550266258, "results": "176", "hashOfConfig": "145"}, {"size": 5687, "mtime": 1752551704438, "results": "177", "hashOfConfig": "145"}, {"size": 2754, "mtime": 1751275216000, "results": "178", "hashOfConfig": "145"}, {"size": 9289, "mtime": 1751076836000, "results": "179", "hashOfConfig": "145"}, {"size": 10084, "mtime": 1751440302000, "results": "180", "hashOfConfig": "145"}, {"size": 5674, "mtime": 1748533678000, "results": "181", "hashOfConfig": "145"}, {"size": 10237, "mtime": 1751375976000, "results": "182", "hashOfConfig": "145"}, {"size": 13243, "mtime": 1751376032000, "results": "183", "hashOfConfig": "145"}, {"size": 20686, "mtime": 1751376374000, "results": "184", "hashOfConfig": "145"}, {"size": 19762, "mtime": 1751376110000, "results": "185", "hashOfConfig": "145"}, {"size": 13935, "mtime": 1751376296000, "results": "186", "hashOfConfig": "145"}, {"size": 3302, "mtime": 1751251038000, "results": "187", "hashOfConfig": "145"}, {"size": 23502, "mtime": 1751440302000, "results": "188", "hashOfConfig": "145"}, {"size": 17469, "mtime": 1751459722103, "results": "189", "hashOfConfig": "190"}, {"size": 8949, "mtime": 1751275712000, "results": "191", "hashOfConfig": "145"}, {"size": 2536, "mtime": 1751268692000, "results": "192", "hashOfConfig": "145"}, {"size": 8611, "mtime": 1751273494000, "results": "193", "hashOfConfig": "145"}, {"size": 2632, "mtime": 1749573724000, "results": "194", "hashOfConfig": "145"}, {"size": 3344, "mtime": 1751274470000, "results": "195", "hashOfConfig": "145"}, {"size": 2001, "mtime": 1751448750000, "results": "196", "hashOfConfig": "145"}, {"size": 5719, "mtime": 1751076762000, "results": "197", "hashOfConfig": "145"}, {"size": 11918, "mtime": 1751270770000, "results": "198", "hashOfConfig": "145"}, {"size": 3006, "mtime": 1751268708000, "results": "199", "hashOfConfig": "145"}, {"size": 12092, "mtime": 1752550729643, "results": "200", "hashOfConfig": "145"}, {"size": 9556, "mtime": 1751175720000, "results": "201", "hashOfConfig": "145"}, {"size": 3823, "mtime": 1751272136000, "results": "202", "hashOfConfig": "145"}, {"size": 6242, "mtime": 1751387088000, "results": "203", "hashOfConfig": "145"}, {"size": 6705, "mtime": 1751460330000, "results": "204", "hashOfConfig": "145"}, {"size": 2725, "mtime": 1751076778000, "results": "205", "hashOfConfig": "145"}, {"size": 7053, "mtime": 1752551721433, "results": "206", "hashOfConfig": "145"}, {"size": 5487, "mtime": 1748536714000, "results": "207", "hashOfConfig": "145"}, {"size": 4182, "mtime": 1751175562000, "results": "208", "hashOfConfig": "145"}, {"size": 14750, "mtime": 1751454906562, "results": "209", "hashOfConfig": "190"}, {"size": 16346, "mtime": 1751460354209, "results": "210", "hashOfConfig": "190"}, {"size": 6346, "mtime": 1751454744000, "results": "211", "hashOfConfig": "145"}, {"size": 6908, "mtime": 1751459962000, "results": "212", "hashOfConfig": "145"}, {"size": 10679, "mtime": 1751460404000, "results": "213", "hashOfConfig": "145"}, {"size": 6209, "mtime": 1751460516000, "results": "214", "hashOfConfig": "145"}, {"size": 17111, "mtime": 1752036839812, "results": "215", "hashOfConfig": "145"}, {"size": 5819, "mtime": 1752036805302, "results": "216", "hashOfConfig": "145"}, {"size": 15202, "mtime": 1752550644631, "results": "217", "hashOfConfig": "145"}, {"size": 2891, "mtime": 1752550632655, "results": "218", "hashOfConfig": "145"}, {"size": 535, "mtime": 1748532508000, "results": "219", "hashOfConfig": "220"}, {"size": 7288, "mtime": 1752550163170, "results": "221", "hashOfConfig": "220"}, {"size": 362, "mtime": 1748532510000, "results": "222", "hashOfConfig": "220"}, {"size": 5553, "mtime": 1748536576000, "results": "223", "hashOfConfig": "220"}, {"size": 4139, "mtime": 1751076622000, "results": "224", "hashOfConfig": "220"}, {"size": 4954, "mtime": 1752549460528, "results": "225", "hashOfConfig": "220"}, {"size": 6574, "mtime": 1751098548000, "results": "226", "hashOfConfig": "220"}, {"size": 22019, "mtime": 1751370338000, "results": "227", "hashOfConfig": "220"}, {"size": 11069, "mtime": 1751278342000, "results": "228", "hashOfConfig": "220"}, {"size": 13424, "mtime": 1748592600000, "results": "229", "hashOfConfig": "220"}, {"size": 18118, "mtime": 1751248890000, "results": "230", "hashOfConfig": "220"}, {"size": 11059, "mtime": 1750776056000, "results": "231", "hashOfConfig": "220"}, {"size": 5296, "mtime": 1751375904000, "results": "232", "hashOfConfig": "220"}, {"size": 14768, "mtime": 1751099198000, "results": "233", "hashOfConfig": "220"}, {"size": 9763, "mtime": 1751248890000, "results": "234", "hashOfConfig": "220"}, {"size": 9980, "mtime": 1751289364000, "results": "235", "hashOfConfig": "220"}, {"size": 23206, "mtime": 1751291576000, "results": "236", "hashOfConfig": "220"}, {"size": 7100, "mtime": 1750777438000, "results": "237", "hashOfConfig": "220"}, {"size": 8538, "mtime": 1752551742429, "results": "238", "hashOfConfig": "220"}, {"size": 19148, "mtime": 1751447640000, "results": "239", "hashOfConfig": "220"}, {"size": 5949, "mtime": 1748749522000, "results": "240", "hashOfConfig": "220"}, {"size": 8266, "mtime": 1748588852000, "results": "241", "hashOfConfig": "220"}, {"size": 809, "mtime": 1751274436000, "results": "242", "hashOfConfig": "220"}, {"size": 1283, "mtime": 1751274444000, "results": "243", "hashOfConfig": "220"}, {"size": 1304, "mtime": 1751274452000, "results": "244", "hashOfConfig": "220"}, {"size": 4827, "mtime": 1751274510000, "results": "245", "hashOfConfig": "220"}, {"size": 11351, "mtime": 1751114552000, "results": "246", "hashOfConfig": "220"}, {"size": 7098, "mtime": 1751277960000, "results": "247", "hashOfConfig": "220"}, {"size": 2764, "mtime": 1752550266258, "results": "248", "hashOfConfig": "220"}, {"size": 5674, "mtime": 1748533678000, "results": "249", "hashOfConfig": "220"}, {"size": 9289, "mtime": 1751076836000, "results": "250", "hashOfConfig": "220"}, {"size": 5687, "mtime": 1752551704438, "results": "251", "hashOfConfig": "220"}, {"size": 2754, "mtime": 1751275216000, "results": "252", "hashOfConfig": "220"}, {"size": 13243, "mtime": 1751376032000, "results": "253", "hashOfConfig": "220"}, {"size": 10237, "mtime": 1751375976000, "results": "254", "hashOfConfig": "220"}, {"size": 13935, "mtime": 1751376296000, "results": "255", "hashOfConfig": "220"}, {"size": 3302, "mtime": 1751251038000, "results": "256", "hashOfConfig": "220"}, {"size": 20686, "mtime": 1751376374000, "results": "257", "hashOfConfig": "220"}, {"size": 3375, "mtime": 1751249990000, "results": "258", "hashOfConfig": "220"}, {"size": 19762, "mtime": 1751376110000, "results": "259", "hashOfConfig": "220"}, {"size": 53614, "mtime": 1751251970000, "results": "260", "hashOfConfig": "220"}, {"size": 5314, "mtime": 1751292028000, "results": "261", "hashOfConfig": "220"}, {"size": 23502, "mtime": 1751440302000, "results": "262", "hashOfConfig": "220"}, {"size": 10084, "mtime": 1751440302000, "results": "263", "hashOfConfig": "220"}, {"size": 8611, "mtime": 1751273494000, "results": "264", "hashOfConfig": "220"}, {"size": 3006, "mtime": 1751268708000, "results": "265", "hashOfConfig": "220"}, {"size": 2536, "mtime": 1751268692000, "results": "266", "hashOfConfig": "220"}, {"size": 10679, "mtime": 1751460404000, "results": "267", "hashOfConfig": "220"}, {"size": 8949, "mtime": 1751275712000, "results": "268", "hashOfConfig": "220"}, {"size": 15202, "mtime": 1752550644631, "results": "269", "hashOfConfig": "220"}, {"size": 11918, "mtime": 1751270770000, "results": "270", "hashOfConfig": "220"}, {"size": 2001, "mtime": 1751448750000, "results": "271", "hashOfConfig": "220"}, {"size": 5719, "mtime": 1751076762000, "results": "272", "hashOfConfig": "220"}, {"size": 2632, "mtime": 1749573724000, "results": "273", "hashOfConfig": "220"}, {"size": 3344, "mtime": 1751274470000, "results": "274", "hashOfConfig": "220"}, {"size": 12092, "mtime": 1752550729643, "results": "275", "hashOfConfig": "220"}, {"size": 9556, "mtime": 1751175720000, "results": "276", "hashOfConfig": "220"}, {"size": 7053, "mtime": 1752551721433, "results": "277", "hashOfConfig": "220"}, {"size": 6705, "mtime": 1751460330000, "results": "278", "hashOfConfig": "220"}, {"size": 6242, "mtime": 1751387088000, "results": "279", "hashOfConfig": "220"}, {"size": 3823, "mtime": 1751272136000, "results": "280", "hashOfConfig": "220"}, {"size": 2725, "mtime": 1751076778000, "results": "281", "hashOfConfig": "220"}, {"size": 2891, "mtime": 1752550632655, "results": "282", "hashOfConfig": "220"}, {"size": 5487, "mtime": 1748536714000, "results": "283", "hashOfConfig": "220"}, {"size": 4182, "mtime": 1751175562000, "results": "284", "hashOfConfig": "220"}, {"size": 5819, "mtime": 1752036805302, "results": "285", "hashOfConfig": "220"}, {"size": 6209, "mtime": 1751460516000, "results": "286", "hashOfConfig": "220"}, {"size": 17111, "mtime": 1752036839812, "results": "287", "hashOfConfig": "220"}, {"size": 6908, "mtime": 1751459962000, "results": "288", "hashOfConfig": "220"}, {"size": 6346, "mtime": 1751454744000, "results": "289", "hashOfConfig": "220"}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l36c5a", {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hbkk32", {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1craelc", {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductDetailsModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\modals\\ProductFormModal.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ActivityLogs.js", [], [], "C:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\ActivityLogIcons.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\index.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\App.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\reportWebVitals.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CartContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\CurrencyContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useAuth.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\contexts\\LanguageContext.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Login.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\OrderSuccessPage.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Payment.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductCatalog.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\About.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Account.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\ProductDetail.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Home.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Cart.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\CheckoutPage.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\Gallery.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\pages\\admin\\AdminDashboard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Header.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\demo\\ProductCardDemo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Footer.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\RoleBasedRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\AdminRoute.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePermissions.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\products.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\paymentService.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\auth.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductFilter.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\product\\ProductCard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\useWebSocket.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\AdminLogo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\OrderHistory.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\ProfileManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AccountPreferences.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\modals\\ConfirmationModal.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\SecuritySettings.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CheckoutModal.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\account\\AddressBook.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\3d\\3DConfigurator.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartItem.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\OrderManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\payment\\PayMongoCheckout.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\UserManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\SupplierManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ProductManagementNew.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\Analytics.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\ActivityLogs.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\InventoryManagement.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartIcon.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\CurrencyLanguageSelector.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\common\\Logo.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\auth\\UnauthorizedAccess.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\AdminIcons.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchInput.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\websocketService.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\api.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiClient.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\services\\apiConfig.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\hooks\\usePrice.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\icons\\ActivityLogIcons.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\cart\\CartSidebar.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\search\\SearchSuggestions.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\SearchFilters.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductCard.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ProductForm.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\ThreeJSPreview.js", [], [], "c:\\DesignXcel\\office-ecommerce\\frontend\\src\\components\\admin\\components\\FileUploadZone.js", [], []]