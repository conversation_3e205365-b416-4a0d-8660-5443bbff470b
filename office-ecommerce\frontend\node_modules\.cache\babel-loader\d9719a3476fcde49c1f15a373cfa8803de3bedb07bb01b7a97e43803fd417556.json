{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Shape } from 'three';\nimport { toCreasedNormals } from 'three-stdlib';\nconst eps = 0.00001;\nfunction createShape(width, height, radius0) {\n  const shape = new Shape();\n  const radius = radius0 - eps;\n  shape.absarc(eps, eps, eps, -Math.PI / 2, -Math.PI, true);\n  shape.absarc(eps, height - radius * 2, eps, Math.PI, Math.PI / 2, true);\n  shape.absarc(width - radius * 2, height - radius * 2, eps, Math.PI / 2, 0, true);\n  shape.absarc(width - radius * 2, eps, eps, 0, -Math.PI / 2, true);\n  return shape;\n}\nconst RoundedBox = /*#__PURE__*/React.forwardRef(function RoundedBox({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  creaseAngle = 0.4,\n  children,\n  ...rest\n}, ref) {\n  const shape = React.useMemo(() => createShape(width, height, radius), [width, height, radius]);\n  const params = React.useMemo(() => ({\n    depth: depth - radius * 2,\n    bevelEnabled: true,\n    bevelSegments: smoothness * 2,\n    steps,\n    bevelSize: radius - eps,\n    bevelThickness: radius,\n    curveSegments: smoothness\n  }), [depth, radius, smoothness]);\n  const geomRef = React.useRef();\n  React.useLayoutEffect(() => {\n    if (geomRef.current) {\n      geomRef.current.center();\n      toCreasedNormals(geomRef.current, creaseAngle);\n    }\n  }, [shape, params]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(\"extrudeGeometry\", {\n    ref: geomRef,\n    args: [shape, params]\n  }), children);\n});\nexport { RoundedBox };", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON>", "toCreasedNormals", "eps", "createShape", "width", "height", "radius0", "shape", "radius", "absarc", "Math", "PI", "RoundedBox", "forwardRef", "args", "depth", "steps", "smoothness", "creaseAngle", "children", "rest", "ref", "useMemo", "params", "bevelEnabled", "bevelSegments", "bevelSize", "bevelThickness", "curveSegments", "geomRef", "useRef", "useLayoutEffect", "current", "center", "createElement"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/RoundedBox.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Shape } from 'three';\nimport { toCreasedNormals } from 'three-stdlib';\n\nconst eps = 0.00001;\n\nfunction createShape(width, height, radius0) {\n  const shape = new Shape();\n  const radius = radius0 - eps;\n  shape.absarc(eps, eps, eps, -Math.PI / 2, -Math.PI, true);\n  shape.absarc(eps, height - radius * 2, eps, Math.PI, Math.PI / 2, true);\n  shape.absarc(width - radius * 2, height - radius * 2, eps, Math.PI / 2, 0, true);\n  shape.absarc(width - radius * 2, eps, eps, 0, -Math.PI / 2, true);\n  return shape;\n}\n\nconst RoundedBox = /*#__PURE__*/React.forwardRef(function RoundedBox({\n  args: [width = 1, height = 1, depth = 1] = [],\n  radius = 0.05,\n  steps = 1,\n  smoothness = 4,\n  creaseAngle = 0.4,\n  children,\n  ...rest\n}, ref) {\n  const shape = React.useMemo(() => createShape(width, height, radius), [width, height, radius]);\n  const params = React.useMemo(() => ({\n    depth: depth - radius * 2,\n    bevelEnabled: true,\n    bevelSegments: smoothness * 2,\n    steps,\n    bevelSize: radius - eps,\n    bevelThickness: radius,\n    curveSegments: smoothness\n  }), [depth, radius, smoothness]);\n  const geomRef = React.useRef();\n  React.useLayoutEffect(() => {\n    if (geomRef.current) {\n      geomRef.current.center();\n      toCreasedNormals(geomRef.current, creaseAngle);\n    }\n  }, [shape, params]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(\"extrudeGeometry\", {\n    ref: geomRef,\n    args: [shape, params]\n  }), children);\n});\n\nexport { RoundedBox };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,QAAQ,OAAO;AAC7B,SAASC,gBAAgB,QAAQ,cAAc;AAE/C,MAAMC,GAAG,GAAG,OAAO;AAEnB,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAG,IAAIP,KAAK,CAAC,CAAC;EACzB,MAAMQ,MAAM,GAAGF,OAAO,GAAGJ,GAAG;EAC5BK,KAAK,CAACE,MAAM,CAACP,GAAG,EAAEA,GAAG,EAAEA,GAAG,EAAE,CAACQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,CAACD,IAAI,CAACC,EAAE,EAAE,IAAI,CAAC;EACzDJ,KAAK,CAACE,MAAM,CAACP,GAAG,EAAEG,MAAM,GAAGG,MAAM,GAAG,CAAC,EAAEN,GAAG,EAAEQ,IAAI,CAACC,EAAE,EAAED,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;EACvEJ,KAAK,CAACE,MAAM,CAACL,KAAK,GAAGI,MAAM,GAAG,CAAC,EAAEH,MAAM,GAAGG,MAAM,GAAG,CAAC,EAAEN,GAAG,EAAEQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAChFJ,KAAK,CAACE,MAAM,CAACL,KAAK,GAAGI,MAAM,GAAG,CAAC,EAAEN,GAAG,EAAEA,GAAG,EAAE,CAAC,EAAE,CAACQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;EACjE,OAAOJ,KAAK;AACd;AAEA,MAAMK,UAAU,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,SAASD,UAAUA,CAAC;EACnEE,IAAI,EAAE,CAACV,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAEU,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE;EAC7CP,MAAM,GAAG,IAAI;EACbQ,KAAK,GAAG,CAAC;EACTC,UAAU,GAAG,CAAC;EACdC,WAAW,GAAG,GAAG;EACjBC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMd,KAAK,GAAGR,KAAK,CAACuB,OAAO,CAAC,MAAMnB,WAAW,CAACC,KAAK,EAAEC,MAAM,EAAEG,MAAM,CAAC,EAAE,CAACJ,KAAK,EAAEC,MAAM,EAAEG,MAAM,CAAC,CAAC;EAC9F,MAAMe,MAAM,GAAGxB,KAAK,CAACuB,OAAO,CAAC,OAAO;IAClCP,KAAK,EAAEA,KAAK,GAAGP,MAAM,GAAG,CAAC;IACzBgB,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAER,UAAU,GAAG,CAAC;IAC7BD,KAAK;IACLU,SAAS,EAAElB,MAAM,GAAGN,GAAG;IACvByB,cAAc,EAAEnB,MAAM;IACtBoB,aAAa,EAAEX;EACjB,CAAC,CAAC,EAAE,CAACF,KAAK,EAAEP,MAAM,EAAES,UAAU,CAAC,CAAC;EAChC,MAAMY,OAAO,GAAG9B,KAAK,CAAC+B,MAAM,CAAC,CAAC;EAC9B/B,KAAK,CAACgC,eAAe,CAAC,MAAM;IAC1B,IAAIF,OAAO,CAACG,OAAO,EAAE;MACnBH,OAAO,CAACG,OAAO,CAACC,MAAM,CAAC,CAAC;MACxBhC,gBAAgB,CAAC4B,OAAO,CAACG,OAAO,EAAEd,WAAW,CAAC;IAChD;EACF,CAAC,EAAE,CAACX,KAAK,EAAEgB,MAAM,CAAC,CAAC;EACnB,OAAO,aAAaxB,KAAK,CAACmC,aAAa,CAAC,MAAM,EAAEpC,QAAQ,CAAC;IACvDuB,GAAG,EAAEA;EACP,CAAC,EAAED,IAAI,CAAC,EAAE,aAAarB,KAAK,CAACmC,aAAa,CAAC,iBAAiB,EAAE;IAC5Db,GAAG,EAAEQ,OAAO;IACZf,IAAI,EAAE,CAACP,KAAK,EAAEgB,MAAM;EACtB,CAAC,CAAC,EAAEJ,QAAQ,CAAC;AACf,CAAC,CAAC;AAEF,SAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}