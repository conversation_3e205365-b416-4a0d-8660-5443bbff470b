{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\search\\\\SearchSuggestions.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SearchSuggestions = ({\n  suggestions,\n  selectedIndex,\n  onSelect,\n  onClose,\n  formatPrice,\n  isLoading\n}) => {\n  _s();\n  const {\n    t\n  } = useLanguage();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-suggestions\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"suggestions-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [t('loading'), \"...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this);\n  }\n  if (suggestions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-suggestions\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-suggestions\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: t('noProductsFound')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-suggestions\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"suggestions-list\",\n      children: suggestions.map((product, index) => {\n        var _product$images;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `suggestion-item ${index === selectedIndex ? 'selected' : ''}`,\n          onClick: () => onSelect(product),\n          onMouseEnter: () => {\n            // Optional: Update selected index on hover\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"suggestion-image\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: ((_product$images = product.images) === null || _product$images === void 0 ? void 0 : _product$images[0]) || '/placeholder-image.jpg',\n              alt: product.name,\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"suggestion-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"suggestion-name\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"suggestion-category\",\n              children: product.categoryName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"suggestion-pricing\",\n              children: product.discountPrice && product.discountPrice < product.price ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"suggestion-price current\",\n                  children: formatPrice(product.discountPrice)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"suggestion-price original\",\n                  children: formatPrice(product.price)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"suggestion-price current\",\n                children: formatPrice(product.price)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"suggestion-arrow\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M9 18L15 12L9 6\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 25\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"suggestions-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"keyboard-hint\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Use \\u2191\\u2193 to navigate, Enter to select, Esc to close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 9\n  }, this);\n};\n_s(SearchSuggestions, \"ot2YhC7pP10gRrIouBKIa40vomw=\", false, function () {\n  return [useLanguage];\n});\n_c = SearchSuggestions;\nexport default SearchSuggestions;\nvar _c;\n$RefreshReg$(_c, \"SearchSuggestions\");", "map": {"version": 3, "names": ["React", "useLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SearchSuggestions", "suggestions", "selectedIndex", "onSelect", "onClose", "formatPrice", "isLoading", "_s", "t", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "product", "index", "_product$images", "onClick", "onMouseEnter", "src", "images", "alt", "name", "loading", "categoryName", "discountPrice", "price", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "id", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/search/SearchSuggestions.js"], "sourcesContent": ["import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SearchSuggestions = ({ \n    suggestions, \n    selectedIndex, \n    onSelect, \n    onClose, \n    formatPrice, \n    isLoading \n}) => {\n    const { t } = useLanguage();\n\n    if (isLoading) {\n        return (\n            <div className=\"search-suggestions\">\n                <div className=\"suggestions-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <span>{t('loading')}...</span>\n                </div>\n            </div>\n        );\n    }\n\n    if (suggestions.length === 0) {\n        return (\n            <div className=\"search-suggestions\">\n                <div className=\"no-suggestions\">\n                    <span>{t('noProductsFound')}</span>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"search-suggestions\">\n            \n            <div className=\"suggestions-list\">\n                {suggestions.map((product, index) => (\n                    <div\n                        key={product.id}\n                        className={`suggestion-item ${index === selectedIndex ? 'selected' : ''}`}\n                        onClick={() => onSelect(product)}\n                        onMouseEnter={() => {\n                            // Optional: Update selected index on hover\n                        }}\n                    >\n                        <div className=\"suggestion-image\">\n                            <img\n                                src={product.images?.[0] || '/placeholder-image.jpg'}\n                                alt={product.name}\n                                loading=\"lazy\"\n                            />\n                        </div>\n                        \n                        <div className=\"suggestion-content\">\n                            <h4 className=\"suggestion-name\">{product.name}</h4>\n                            <div className=\"suggestion-category\">{product.categoryName}</div>\n\n                            <div className=\"suggestion-pricing\">\n                                {product.discountPrice && product.discountPrice < product.price ? (\n                                    <>\n                                        <span className=\"suggestion-price current\">\n                                            {formatPrice(product.discountPrice)}\n                                        </span>\n                                        <span className=\"suggestion-price original\">\n                                            {formatPrice(product.price)}\n                                        </span>\n                                    </>\n                                ) : (\n                                    <span className=\"suggestion-price current\">\n                                        {formatPrice(product.price)}\n                                    </span>\n                                )}\n                            </div>\n                        </div>\n                        \n                        <div className=\"suggestion-arrow\">\n                            <svg\n                                width=\"16\"\n                                height=\"16\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"none\"\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                            >\n                                <path\n                                    d=\"M9 18L15 12L9 6\"\n                                    stroke=\"currentColor\"\n                                    strokeWidth=\"2\"\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                />\n                            </svg>\n                        </div>\n                    </div>\n                ))}\n            </div>\n            \n            <div className=\"suggestions-footer\">\n                <div className=\"keyboard-hint\">\n                    <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default SearchSuggestions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,iBAAiB,GAAGA,CAAC;EACvBC,WAAW;EACXC,aAAa;EACbC,QAAQ;EACRC,OAAO;EACPC,WAAW;EACXC;AACJ,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM;IAAEC;EAAE,CAAC,GAAGb,WAAW,CAAC,CAAC;EAE3B,IAAIW,SAAS,EAAE;IACX,oBACIT,OAAA;MAAKY,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eAC/Bb,OAAA;QAAKY,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCb,OAAA;UAAKY,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCjB,OAAA;UAAAa,QAAA,GAAOF,CAAC,CAAC,SAAS,CAAC,EAAC,KAAG;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIb,WAAW,CAACc,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACIlB,OAAA;MAAKY,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eAC/Bb,OAAA;QAAKY,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC3Bb,OAAA;UAAAa,QAAA,EAAOF,CAAC,CAAC,iBAAiB;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIjB,OAAA;IAAKY,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAE/Bb,OAAA;MAAKY,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC5BT,WAAW,CAACe,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;QAAA,IAAAC,eAAA;QAAA,oBAC5BtB,OAAA;UAEIY,SAAS,EAAE,mBAAmBS,KAAK,KAAKhB,aAAa,GAAG,UAAU,GAAG,EAAE,EAAG;UAC1EkB,OAAO,EAAEA,CAAA,KAAMjB,QAAQ,CAACc,OAAO,CAAE;UACjCI,YAAY,EAAEA,CAAA,KAAM;YAChB;UAAA,CACF;UAAAX,QAAA,gBAEFb,OAAA;YAAKY,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7Bb,OAAA;cACIyB,GAAG,EAAE,EAAAH,eAAA,GAAAF,OAAO,CAACM,MAAM,cAAAJ,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC,KAAI,wBAAyB;cACrDK,GAAG,EAAEP,OAAO,CAACQ,IAAK;cAClBC,OAAO,EAAC;YAAM;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENjB,OAAA;YAAKY,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAC/Bb,OAAA;cAAIY,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEO,OAAO,CAACQ;YAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDjB,OAAA;cAAKY,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEO,OAAO,CAACU;YAAY;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEjEjB,OAAA;cAAKY,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAC9BO,OAAO,CAACW,aAAa,IAAIX,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACY,KAAK,gBAC3DhC,OAAA,CAAAE,SAAA;gBAAAW,QAAA,gBACIb,OAAA;kBAAMY,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACrCL,WAAW,CAACY,OAAO,CAACW,aAAa;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACPjB,OAAA;kBAAMY,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACtCL,WAAW,CAACY,OAAO,CAACY,KAAK;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA,eACT,CAAC,gBAEHjB,OAAA;gBAAMY,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrCL,WAAW,CAACY,OAAO,CAACY,KAAK;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YACT;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENjB,OAAA;YAAKY,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7Bb,OAAA;cACIiC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,4BAA4B;cAAAxB,QAAA,eAElCb,OAAA;gBACIsC,CAAC,EAAC,iBAAiB;gBACnBC,MAAM,EAAC,cAAc;gBACrBC,WAAW,EAAC,GAAG;gBACfC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,GArDDG,OAAO,CAACuB,EAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsDd,CAAC;MAAA,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENjB,OAAA;MAAKY,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eAC/Bb,OAAA;QAAKY,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1Bb,OAAA;UAAAa,QAAA,EAAM;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACP,EAAA,CAtGIP,iBAAiB;EAAA,QAQLL,WAAW;AAAA;AAAA8C,EAAA,GARvBzC,iBAAiB;AAwGvB,eAAeA,iBAAiB;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}