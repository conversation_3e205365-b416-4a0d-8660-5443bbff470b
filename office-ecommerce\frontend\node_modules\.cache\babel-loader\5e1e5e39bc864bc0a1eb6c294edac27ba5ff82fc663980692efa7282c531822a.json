{"ast": null, "code": "const isPromise = promise => typeof promise === 'object' && typeof promise.then === 'function';\nconst globalCache = [];\nfunction shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n  return true;\n}\nfunction query(fn, keys = null, preload = false, config = {}) {\n  // If no keys were given, the function is the key\n  if (keys === null) keys = [fn];\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) {\n        if (config.lifespan && config.lifespan > 0) {\n          if (entry.timeout) clearTimeout(entry.timeout);\n          entry.timeout = setTimeout(entry.remove, config.lifespan);\n        }\n        return entry.response;\n      } // If the promise is still unresolved, throw\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    remove: () => {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    },\n    promise:\n    // Execute the promise\n    (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value\n    ).then(response => {\n      entry.response = response; // Remove the entry in time if a lifespan was given\n\n      if (config.lifespan && config.lifespan > 0) {\n        entry.timeout = setTimeout(entry.remove, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\nconst peek = keys => {\n  var _globalCache$find;\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) entry.remove();\n  }\n};\nexport { clear, peek, preload, suspend };", "map": {"version": 3, "names": ["isPromise", "promise", "then", "globalCache", "shallowEqualArrays", "arrA", "arrB", "equal", "a", "b", "len", "length", "i", "query", "fn", "keys", "preload", "config", "entry", "undefined", "Object", "prototype", "hasOwnProperty", "call", "error", "lifespan", "timeout", "clearTimeout", "setTimeout", "remove", "response", "index", "indexOf", "splice", "catch", "push", "suspend", "peek", "_globalCache$find", "find", "clear"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/suspend-react/index.js"], "sourcesContent": ["const isPromise = promise => typeof promise === 'object' && typeof promise.then === 'function';\n\nconst globalCache = [];\n\nfunction shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n\n  return true;\n}\n\nfunction query(fn, keys = null, preload = false, config = {}) {\n  // If no keys were given, the function is the key\n  if (keys === null) keys = [fn];\n\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) {\n        if (config.lifespan && config.lifespan > 0) {\n          if (entry.timeout) clearTimeout(entry.timeout);\n          entry.timeout = setTimeout(entry.remove, config.lifespan);\n        }\n\n        return entry.response;\n      } // If the promise is still unresolved, throw\n\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    remove: () => {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    },\n    promise: // Execute the promise\n    (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value\n    ).then(response => {\n      entry.response = response; // Remove the entry in time if a lifespan was given\n\n      if (config.lifespan && config.lifespan > 0) {\n        entry.timeout = setTimeout(entry.remove, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\n\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\n\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\n\nconst peek = keys => {\n  var _globalCache$find;\n\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\n\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) entry.remove();\n  }\n};\n\nexport { clear, peek, preload, suspend };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAGC,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,CAACC,IAAI,KAAK,UAAU;AAE9F,MAAMC,WAAW,GAAG,EAAE;AAEtB,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,EAAE;EACjE,IAAIJ,IAAI,KAAKC,IAAI,EAAE,OAAO,IAAI;EAC9B,IAAI,CAACD,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;EAChC,MAAMI,GAAG,GAAGL,IAAI,CAACM,MAAM;EACvB,IAAIL,IAAI,CAACK,MAAM,KAAKD,GAAG,EAAE,OAAO,KAAK;EAErC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE,IAAI,CAACL,KAAK,CAACF,IAAI,CAACO,CAAC,CAAC,EAAEN,IAAI,CAACM,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;EAExE,OAAO,IAAI;AACb;AAEA,SAASC,KAAKA,CAACC,EAAE,EAAEC,IAAI,GAAG,IAAI,EAAEC,OAAO,GAAG,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;EAC5D;EACA,IAAIF,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,CAACD,EAAE,CAAC;EAE9B,KAAK,MAAMI,KAAK,IAAIf,WAAW,EAAE;IAC/B;IACA,IAAIC,kBAAkB,CAACW,IAAI,EAAEG,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACX,KAAK,CAAC,EAAE;MACrD;MACA,IAAIS,OAAO,EAAE,OAAOG,SAAS,CAAC,CAAC;;MAE/B,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,KAAK,EAAE,OAAO,CAAC,EAAE,MAAMA,KAAK,CAACM,KAAK,CAAC,CAAC;;MAE7E,IAAIJ,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,KAAK,EAAE,UAAU,CAAC,EAAE;QAC3D,IAAID,MAAM,CAACQ,QAAQ,IAAIR,MAAM,CAACQ,QAAQ,GAAG,CAAC,EAAE;UAC1C,IAAIP,KAAK,CAACQ,OAAO,EAAEC,YAAY,CAACT,KAAK,CAACQ,OAAO,CAAC;UAC9CR,KAAK,CAACQ,OAAO,GAAGE,UAAU,CAACV,KAAK,CAACW,MAAM,EAAEZ,MAAM,CAACQ,QAAQ,CAAC;QAC3D;QAEA,OAAOP,KAAK,CAACY,QAAQ;MACvB,CAAC,CAAC;;MAGF,IAAI,CAACd,OAAO,EAAE,MAAME,KAAK,CAACjB,OAAO;IACnC;EACF,CAAC,CAAC;;EAGF,MAAMiB,KAAK,GAAG;IACZH,IAAI;IACJR,KAAK,EAAEU,MAAM,CAACV,KAAK;IACnBsB,MAAM,EAAEA,CAAA,KAAM;MACZ,MAAME,KAAK,GAAG5B,WAAW,CAAC6B,OAAO,CAACd,KAAK,CAAC;MACxC,IAAIa,KAAK,KAAK,CAAC,CAAC,EAAE5B,WAAW,CAAC8B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAChD,CAAC;IACD9B,OAAO;IAAE;IACT,CAACD,SAAS,CAACc,EAAE,CAAC,GAAGA,EAAE,GAAGA,EAAE,CAAC,GAAGC,IAAI,CAAC,CAAC;IAAA,EAChCb,IAAI,CAAC4B,QAAQ,IAAI;MACjBZ,KAAK,CAACY,QAAQ,GAAGA,QAAQ,CAAC,CAAC;;MAE3B,IAAIb,MAAM,CAACQ,QAAQ,IAAIR,MAAM,CAACQ,QAAQ,GAAG,CAAC,EAAE;QAC1CP,KAAK,CAACQ,OAAO,GAAGE,UAAU,CAACV,KAAK,CAACW,MAAM,EAAEZ,MAAM,CAACQ,QAAQ,CAAC;MAC3D;IACF,CAAC,CAAC,CAAC;IAAA,CACFS,KAAK,CAACV,KAAK,IAAIN,KAAK,CAACM,KAAK,GAAGA,KAAK;EACrC,CAAC,CAAC,CAAC;;EAEHrB,WAAW,CAACgC,IAAI,CAACjB,KAAK,CAAC,CAAC,CAAC;;EAEzB,IAAI,CAACF,OAAO,EAAE,MAAME,KAAK,CAACjB,OAAO;EACjC,OAAOkB,SAAS;AAClB;AAEA,MAAMiB,OAAO,GAAGA,CAACtB,EAAE,EAAEC,IAAI,EAAEE,MAAM,KAAKJ,KAAK,CAACC,EAAE,EAAEC,IAAI,EAAE,KAAK,EAAEE,MAAM,CAAC;AAEpE,MAAMD,OAAO,GAAGA,CAACF,EAAE,EAAEC,IAAI,EAAEE,MAAM,KAAK,KAAKJ,KAAK,CAACC,EAAE,EAAEC,IAAI,EAAE,IAAI,EAAEE,MAAM,CAAC;AAExE,MAAMoB,IAAI,GAAGtB,IAAI,IAAI;EACnB,IAAIuB,iBAAiB;EAErB,OAAO,CAACA,iBAAiB,GAAGnC,WAAW,CAACoC,IAAI,CAACrB,KAAK,IAAId,kBAAkB,CAACW,IAAI,EAAEG,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACX,KAAK,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+B,iBAAiB,CAACR,QAAQ;AACzJ,CAAC;AAED,MAAMU,KAAK,GAAGzB,IAAI,IAAI;EACpB,IAAIA,IAAI,KAAKI,SAAS,IAAIJ,IAAI,CAACJ,MAAM,KAAK,CAAC,EAAER,WAAW,CAAC8B,MAAM,CAAC,CAAC,EAAE9B,WAAW,CAACQ,MAAM,CAAC,CAAC,KAAK;IAC1F,MAAMO,KAAK,GAAGf,WAAW,CAACoC,IAAI,CAACrB,KAAK,IAAId,kBAAkB,CAACW,IAAI,EAAEG,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACX,KAAK,CAAC,CAAC;IAC1F,IAAIW,KAAK,EAAEA,KAAK,CAACW,MAAM,CAAC,CAAC;EAC3B;AACF,CAAC;AAED,SAASW,KAAK,EAAEH,IAAI,EAAErB,OAAO,EAAEoB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}