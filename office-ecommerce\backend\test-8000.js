const axios = require('axios');

async function test8000() {
  try {
    console.log('🏥 Testing Backend on localhost:8000...');

    // Test basic health endpoint
    console.log('\n🔍 Testing /health endpoint...');
    try {
      const healthResponse = await axios.get('http://localhost:8000/health');
      console.log('✅ Health endpoint working');
      console.log('Response:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Health endpoint failed:', error.code, error.message);
    }

    // Test API health endpoint
    console.log('\n🔍 Testing /api/health endpoint...');
    try {
      const apiHealthResponse = await axios.get('http://localhost:8000/api/health');
      console.log('✅ API health endpoint working');
      console.log('Response:', JSON.stringify(apiHealthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ API health endpoint failed:', error.code, error.message);
    }

    // Test login endpoint
    console.log('\n🔍 Testing login endpoint...');
    try {
      const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      console.log('✅ Login endpoint working');
      console.log('Response:', JSON.stringify(loginResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Login endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n✅ Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
test8000();
