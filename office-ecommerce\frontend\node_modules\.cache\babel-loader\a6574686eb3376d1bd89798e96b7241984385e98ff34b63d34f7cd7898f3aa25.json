{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, Vector2, Color } from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { LineSegments2, Line2, LineMaterial, LineSegmentsGeometry, LineGeometry } from 'three-stdlib';\nconst Line = /*#__PURE__*/React.forwardRef(function Line({\n  points,\n  color = 'black',\n  vertexColors,\n  linewidth,\n  lineWidth,\n  segments,\n  dashed,\n  ...rest\n}, ref) {\n  const size = useThree(state => state.size);\n  const line2 = React.useMemo(() => segments ? new LineSegments2() : new Line2(), [segments]);\n  const [lineMaterial] = React.useState(() => new LineMaterial());\n  const lineGeom = React.useMemo(() => {\n    const geom = segments ? new LineSegmentsGeometry() : new LineGeometry();\n    const pValues = points.map(p => {\n      const isArray = Array.isArray(p);\n      return p instanceof Vector3 ? [p.x, p.y, p.z] : p instanceof Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n    });\n    geom.setPositions(pValues.flat());\n    if (vertexColors) {\n      const cValues = vertexColors.map(c => c instanceof Color ? c.toArray() : c);\n      geom.setColors(cValues.flat());\n    }\n    return geom;\n  }, [points, segments, vertexColors]);\n  React.useLayoutEffect(() => {\n    line2.computeLineDistances();\n  }, [points, line2]);\n  React.useLayoutEffect(() => {\n    if (dashed) {\n      lineMaterial.defines.USE_DASH = '';\n    } else {\n      // Setting lineMaterial.defines.USE_DASH to undefined is apparently not sufficient.\n      delete lineMaterial.defines.USE_DASH;\n    }\n    lineMaterial.needsUpdate = true;\n  }, [dashed, lineMaterial]);\n  React.useEffect(() => {\n    return () => lineGeom.dispose();\n  }, [lineGeom]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: line2,\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(\"primitive\", {\n    object: lineGeom,\n    attach: \"geometry\"\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: lineMaterial,\n    attach: \"material\",\n    color: color,\n    vertexColors: Boolean(vertexColors),\n    resolution: [size.width, size.height],\n    linewidth: linewidth !== null && linewidth !== void 0 ? linewidth : lineWidth,\n    dashed: dashed\n  }, rest)));\n});\nexport { Line };", "map": {"version": 3, "names": ["_extends", "React", "Vector3", "Vector2", "Color", "useThree", "LineSegments2", "Line2", "LineMaterial", "LineSegmentsGeometry", "LineGeometry", "Line", "forwardRef", "points", "color", "vertexColors", "linewidth", "lineWidth", "segments", "dashed", "rest", "ref", "size", "state", "line2", "useMemo", "lineMaterial", "useState", "lineGeom", "geom", "p<PERSON><PERSON><PERSON>", "map", "p", "isArray", "Array", "x", "y", "z", "length", "setPositions", "flat", "c<PERSON><PERSON><PERSON>", "c", "toArray", "setColors", "useLayoutEffect", "computeLineDistances", "defines", "USE_DASH", "needsUpdate", "useEffect", "dispose", "createElement", "object", "attach", "Boolean", "resolution", "width", "height"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Line.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, Vector2, Color } from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { LineSegments2, Line2, LineMaterial, LineSegmentsGeometry, LineGeometry } from 'three-stdlib';\n\nconst Line = /*#__PURE__*/React.forwardRef(function Line({\n  points,\n  color = 'black',\n  vertexColors,\n  linewidth,\n  lineWidth,\n  segments,\n  dashed,\n  ...rest\n}, ref) {\n  const size = useThree(state => state.size);\n  const line2 = React.useMemo(() => segments ? new LineSegments2() : new Line2(), [segments]);\n  const [lineMaterial] = React.useState(() => new LineMaterial());\n  const lineGeom = React.useMemo(() => {\n    const geom = segments ? new LineSegmentsGeometry() : new LineGeometry();\n    const pValues = points.map(p => {\n      const isArray = Array.isArray(p);\n      return p instanceof Vector3 ? [p.x, p.y, p.z] : p instanceof Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n    });\n    geom.setPositions(pValues.flat());\n\n    if (vertexColors) {\n      const cValues = vertexColors.map(c => c instanceof Color ? c.toArray() : c);\n      geom.setColors(cValues.flat());\n    }\n\n    return geom;\n  }, [points, segments, vertexColors]);\n  React.useLayoutEffect(() => {\n    line2.computeLineDistances();\n  }, [points, line2]);\n  React.useLayoutEffect(() => {\n    if (dashed) {\n      lineMaterial.defines.USE_DASH = '';\n    } else {\n      // Setting lineMaterial.defines.USE_DASH to undefined is apparently not sufficient.\n      delete lineMaterial.defines.USE_DASH;\n    }\n\n    lineMaterial.needsUpdate = true;\n  }, [dashed, lineMaterial]);\n  React.useEffect(() => {\n    return () => lineGeom.dispose();\n  }, [lineGeom]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: line2,\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(\"primitive\", {\n    object: lineGeom,\n    attach: \"geometry\"\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: lineMaterial,\n    attach: \"material\",\n    color: color,\n    vertexColors: Boolean(vertexColors),\n    resolution: [size.width, size.height],\n    linewidth: linewidth !== null && linewidth !== void 0 ? linewidth : lineWidth,\n    dashed: dashed\n  }, rest)));\n});\n\nexport { Line };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,OAAO;AAC/C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,aAAa,EAAEC,KAAK,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,YAAY,QAAQ,cAAc;AAErG,MAAMC,IAAI,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,SAASD,IAAIA,CAAC;EACvDE,MAAM;EACNC,KAAK,GAAG,OAAO;EACfC,YAAY;EACZC,SAAS;EACTC,SAAS;EACTC,QAAQ;EACRC,MAAM;EACN,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMC,IAAI,GAAGjB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,KAAK,GAAGvB,KAAK,CAACwB,OAAO,CAAC,MAAMP,QAAQ,GAAG,IAAIZ,aAAa,CAAC,CAAC,GAAG,IAAIC,KAAK,CAAC,CAAC,EAAE,CAACW,QAAQ,CAAC,CAAC;EAC3F,MAAM,CAACQ,YAAY,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,MAAM,IAAInB,YAAY,CAAC,CAAC,CAAC;EAC/D,MAAMoB,QAAQ,GAAG3B,KAAK,CAACwB,OAAO,CAAC,MAAM;IACnC,MAAMI,IAAI,GAAGX,QAAQ,GAAG,IAAIT,oBAAoB,CAAC,CAAC,GAAG,IAAIC,YAAY,CAAC,CAAC;IACvE,MAAMoB,OAAO,GAAGjB,MAAM,CAACkB,GAAG,CAACC,CAAC,IAAI;MAC9B,MAAMC,OAAO,GAAGC,KAAK,CAACD,OAAO,CAACD,CAAC,CAAC;MAChC,OAAOA,CAAC,YAAY9B,OAAO,GAAG,CAAC8B,CAAC,CAACG,CAAC,EAAEH,CAAC,CAACI,CAAC,EAAEJ,CAAC,CAACK,CAAC,CAAC,GAAGL,CAAC,YAAY7B,OAAO,GAAG,CAAC6B,CAAC,CAACG,CAAC,EAAEH,CAAC,CAACI,CAAC,EAAE,CAAC,CAAC,GAAGH,OAAO,IAAID,CAAC,CAACM,MAAM,KAAK,CAAC,GAAG,CAACN,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,OAAO,IAAID,CAAC,CAACM,MAAM,KAAK,CAAC,GAAG,CAACN,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC;IACzL,CAAC,CAAC;IACFH,IAAI,CAACU,YAAY,CAACT,OAAO,CAACU,IAAI,CAAC,CAAC,CAAC;IAEjC,IAAIzB,YAAY,EAAE;MAChB,MAAM0B,OAAO,GAAG1B,YAAY,CAACgB,GAAG,CAACW,CAAC,IAAIA,CAAC,YAAYtC,KAAK,GAAGsC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGD,CAAC,CAAC;MAC3Eb,IAAI,CAACe,SAAS,CAACH,OAAO,CAACD,IAAI,CAAC,CAAC,CAAC;IAChC;IAEA,OAAOX,IAAI;EACb,CAAC,EAAE,CAAChB,MAAM,EAAEK,QAAQ,EAAEH,YAAY,CAAC,CAAC;EACpCd,KAAK,CAAC4C,eAAe,CAAC,MAAM;IAC1BrB,KAAK,CAACsB,oBAAoB,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACjC,MAAM,EAAEW,KAAK,CAAC,CAAC;EACnBvB,KAAK,CAAC4C,eAAe,CAAC,MAAM;IAC1B,IAAI1B,MAAM,EAAE;MACVO,YAAY,CAACqB,OAAO,CAACC,QAAQ,GAAG,EAAE;IACpC,CAAC,MAAM;MACL;MACA,OAAOtB,YAAY,CAACqB,OAAO,CAACC,QAAQ;IACtC;IAEAtB,YAAY,CAACuB,WAAW,GAAG,IAAI;EACjC,CAAC,EAAE,CAAC9B,MAAM,EAAEO,YAAY,CAAC,CAAC;EAC1BzB,KAAK,CAACiD,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMtB,QAAQ,CAACuB,OAAO,CAAC,CAAC;EACjC,CAAC,EAAE,CAACvB,QAAQ,CAAC,CAAC;EACd,OAAO,aAAa3B,KAAK,CAACmD,aAAa,CAAC,WAAW,EAAEpD,QAAQ,CAAC;IAC5DqD,MAAM,EAAE7B,KAAK;IACbH,GAAG,EAAEA;EACP,CAAC,EAAED,IAAI,CAAC,EAAE,aAAanB,KAAK,CAACmD,aAAa,CAAC,WAAW,EAAE;IACtDC,MAAM,EAAEzB,QAAQ;IAChB0B,MAAM,EAAE;EACV,CAAC,CAAC,EAAE,aAAarD,KAAK,CAACmD,aAAa,CAAC,WAAW,EAAEpD,QAAQ,CAAC;IACzDqD,MAAM,EAAE3B,YAAY;IACpB4B,MAAM,EAAE,UAAU;IAClBxC,KAAK,EAAEA,KAAK;IACZC,YAAY,EAAEwC,OAAO,CAACxC,YAAY,CAAC;IACnCyC,UAAU,EAAE,CAAClC,IAAI,CAACmC,KAAK,EAAEnC,IAAI,CAACoC,MAAM,CAAC;IACrC1C,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGC,SAAS;IAC7EE,MAAM,EAAEA;EACV,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAAST,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}