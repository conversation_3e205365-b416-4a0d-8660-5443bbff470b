{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\account\\\\AddressBook.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddressBook = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [addresses, setAddresses] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingAddress, setEditingAddress] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    type: 'shipping',\n    firstName: '',\n    lastName: '',\n    company: '',\n    street: '',\n    city: '',\n    state: '',\n    zipCode: '',\n    country: 'Philippines',\n    phone: '',\n    isDefault: false\n  });\n  useEffect(() => {\n    fetchAddresses();\n  }, []);\n  const fetchAddresses = async () => {\n    setLoading(true);\n    try {\n      // Mock address data - replace with actual API call\n      const mockAddresses = [{\n        id: 1,\n        type: 'shipping',\n        firstName: 'John',\n        lastName: 'Doe',\n        company: 'DesignXcel Corp',\n        street: '123 Business Avenue, Suite 100',\n        city: 'Manila',\n        state: 'Metro Manila',\n        zipCode: '1000',\n        country: 'Philippines',\n        phone: '+63 ************',\n        isDefault: true\n      }, {\n        id: 2,\n        type: 'billing',\n        firstName: 'John',\n        lastName: 'Doe',\n        company: '',\n        street: '456 Home Street',\n        city: 'Quezon City',\n        state: 'Metro Manila',\n        zipCode: '1100',\n        country: 'Philippines',\n        phone: '+63 ************',\n        isDefault: false\n      }];\n      setAddresses(mockAddresses);\n    } catch (error) {\n      console.error('Failed to fetch addresses:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      if (editingAddress) {\n        // Update existing address\n        setAddresses(prev => prev.map(addr => addr.id === editingAddress.id ? {\n          ...formData,\n          id: editingAddress.id\n        } : addr));\n      } else {\n        // Add new address\n        const newAddress = {\n          ...formData,\n          id: Date.now() // Mock ID generation\n        };\n        setAddresses(prev => [...prev, newAddress]);\n      }\n      resetForm();\n    } catch (error) {\n      console.error('Failed to save address:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEdit = address => {\n    setEditingAddress(address);\n    setFormData(address);\n    setShowAddForm(true);\n  };\n  const handleDelete = async addressId => {\n    if (window.confirm('Are you sure you want to delete this address?')) {\n      setAddresses(prev => prev.filter(addr => addr.id !== addressId));\n    }\n  };\n  const handleSetDefault = async addressId => {\n    setAddresses(prev => prev.map(addr => ({\n      ...addr,\n      isDefault: addr.id === addressId\n    })));\n  };\n  const resetForm = () => {\n    setFormData({\n      type: 'shipping',\n      firstName: '',\n      lastName: '',\n      company: '',\n      street: '',\n      city: '',\n      state: '',\n      zipCode: '',\n      country: 'Philippines',\n      phone: '',\n      isDefault: false\n    });\n    setEditingAddress(null);\n    setShowAddForm(false);\n  };\n  const formatAddress = address => {\n    const parts = [address.street, address.city, address.state, address.zipCode, address.country].filter(Boolean);\n    return parts.join(', ');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"address-book\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Address Book\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Manage your shipping and billing addresses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn-primary\",\n        onClick: () => setShowAddForm(true),\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"16\",\n          height: \"16\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          children: [/*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"12\",\n            y1: \"5\",\n            x2: \"12\",\n            y2: \"19\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"5\",\n            y1: \"12\",\n            x2: \"19\",\n            y2: \"12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 21\n        }, this), \"Add New Address\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"address-form-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: editingAddress ? 'Edit Address' : 'Add New Address'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-btn\",\n          onClick: resetForm,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"18\",\n              y1: \"6\",\n              x2: \"6\",\n              y2: \"18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"6\",\n              y1: \"6\",\n              x2: \"18\",\n              y2: \"18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"address-form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Address Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"type\",\n            className: \"form-input\",\n            value: formData.type,\n            onChange: handleChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"shipping\",\n              children: \"Shipping Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"billing\",\n              children: \"Billing Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"First Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"firstName\",\n              className: \"form-input\",\n              value: formData.firstName,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Last Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"lastName\",\n              className: \"form-input\",\n              value: formData.lastName,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Company (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"company\",\n            className: \"form-input\",\n            value: formData.company,\n            onChange: handleChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Street Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"street\",\n            className: \"form-input\",\n            value: formData.street,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"City\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"city\",\n              className: \"form-input\",\n              value: formData.city,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"State/Province\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"state\",\n              className: \"form-input\",\n              value: formData.state,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"ZIP/Postal Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"zipCode\",\n              className: \"form-input\",\n              value: formData.zipCode,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Country\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"country\",\n              className: \"form-input\",\n              value: formData.country,\n              onChange: handleChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Philippines\",\n                children: \"Philippines\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"United States\",\n                children: \"United States\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Canada\",\n                children: \"Canada\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"United Kingdom\",\n                children: \"United Kingdom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Australia\",\n                children: \"Australia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Phone Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            name: \"phone\",\n            className: \"form-input\",\n            value: formData.phone,\n            onChange: handleChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"checkbox-label\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              name: \"isDefault\",\n              checked: formData.isDefault,\n              onChange: handleChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"checkmark\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 33\n            }, this), \"Set as default address\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn-primary\",\n            disabled: loading,\n            children: loading ? 'Saving...' : editingAddress ? 'Update Address' : 'Add Address'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn-secondary\",\n            onClick: resetForm,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"addresses-list\",\n      children: addresses.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"64\",\n          height: \"64\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"1\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"10\",\n            r: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No addresses saved\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Add your first address to make checkout faster and easier.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"address-grid\",\n        children: addresses.map(address => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"address-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"address-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"address-type\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `type-badge ${address.type}`,\n                children: [address.type === 'shipping' ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: \"1\",\n                    y: \"3\",\n                    width: \"15\",\n                    height: \"13\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"polygon\", {\n                    points: \"16,8 20,8 23,11 23,16 16,16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"5.5\",\n                    cy: \"18.5\",\n                    r: \"2.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"18.5\",\n                    cy: \"18.5\",\n                    r: \"2.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 49\n                }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: \"1\",\n                    y: \"4\",\n                    width: \"22\",\n                    height: \"16\",\n                    rx: \"2\",\n                    ry: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"1\",\n                    y1: \"10\",\n                    x2: \"23\",\n                    y2: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 49\n                }, this), address.type.charAt(0).toUpperCase() + address.type.slice(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 37\n            }, this), address.isDefault && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"default-badge\",\n              children: \"Default\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"address-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"address-name\",\n              children: [address.firstName, \" \", address.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 37\n            }, this), address.company && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"address-company\",\n              children: address.company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"address-text\",\n              children: formatAddress(address)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"address-phone\",\n              children: address.phone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"address-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-btn edit\",\n              onClick: () => handleEdit(address),\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 41\n              }, this), \"Edit\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 37\n            }, this), !address.isDefault && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-btn default\",\n              onClick: () => handleSetDefault(address.id),\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                children: /*#__PURE__*/_jsxDEV(\"polygon\", {\n                  points: \"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 45\n              }, this), \"Set Default\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-btn delete\",\n              onClick: () => handleDelete(address.id),\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                children: [/*#__PURE__*/_jsxDEV(\"polyline\", {\n                  points: \"3,6 5,6 21,6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 41\n              }, this), \"Delete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 33\n          }, this)]\n        }, address.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 29\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 9\n  }, this);\n};\n_s(AddressBook, \"rE3ag2Xz/6qecnqBBbxyxx64rzw=\", false, function () {\n  return [useAuth];\n});\n_c = AddressBook;\nexport default AddressBook;\nvar _c;\n$RefreshReg$(_c, \"AddressBook\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "AddressBook", "_s", "user", "addresses", "setAdd<PERSON>", "showAddForm", "setShowAddForm", "<PERSON><PERSON><PERSON><PERSON>", "setE<PERSON>ing<PERSON>dd<PERSON>", "loading", "setLoading", "formData", "setFormData", "type", "firstName", "lastName", "company", "street", "city", "state", "zipCode", "country", "phone", "isDefault", "fetchAddresses", "mockAddresses", "id", "error", "console", "handleChange", "e", "name", "value", "checked", "target", "prev", "handleSubmit", "preventDefault", "map", "addr", "<PERSON><PERSON><PERSON><PERSON>", "Date", "now", "resetForm", "handleEdit", "address", "handleDelete", "addressId", "window", "confirm", "filter", "handleSetDefault", "formatAddress", "parts", "Boolean", "join", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "onSubmit", "onChange", "required", "disabled", "length", "d", "cx", "cy", "r", "x", "y", "points", "rx", "ry", "char<PERSON>t", "toUpperCase", "slice", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/account/AddressBook.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../hooks/useAuth';\n\nconst AddressBook = () => {\n    const { user } = useAuth();\n    const [addresses, setAddresses] = useState([]);\n    const [showAddForm, setShowAddForm] = useState(false);\n    const [editingAddress, setEditingAddress] = useState(null);\n    const [loading, setLoading] = useState(false);\n    const [formData, setFormData] = useState({\n        type: 'shipping',\n        firstName: '',\n        lastName: '',\n        company: '',\n        street: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        country: 'Philippines',\n        phone: '',\n        isDefault: false\n    });\n\n    useEffect(() => {\n        fetchAddresses();\n    }, []);\n\n    const fetchAddresses = async () => {\n        setLoading(true);\n        try {\n            // Mock address data - replace with actual API call\n            const mockAddresses = [\n                {\n                    id: 1,\n                    type: 'shipping',\n                    firstName: 'John',\n                    lastName: 'Doe',\n                    company: 'DesignXcel Corp',\n                    street: '123 Business Avenue, Suite 100',\n                    city: 'Manila',\n                    state: 'Metro Manila',\n                    zipCode: '1000',\n                    country: 'Philippines',\n                    phone: '+63 ************',\n                    isDefault: true\n                },\n                {\n                    id: 2,\n                    type: 'billing',\n                    firstName: 'John',\n                    lastName: 'Doe',\n                    company: '',\n                    street: '456 Home Street',\n                    city: 'Quezon City',\n                    state: 'Metro Manila',\n                    zipCode: '1100',\n                    country: 'Philippines',\n                    phone: '+63 ************',\n                    isDefault: false\n                }\n            ];\n            setAddresses(mockAddresses);\n        } catch (error) {\n            console.error('Failed to fetch addresses:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleChange = (e) => {\n        const { name, value, type, checked } = e.target;\n        setFormData(prev => ({\n            ...prev,\n            [name]: type === 'checkbox' ? checked : value\n        }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setLoading(true);\n\n        try {\n            if (editingAddress) {\n                // Update existing address\n                setAddresses(prev => prev.map(addr => \n                    addr.id === editingAddress.id \n                        ? { ...formData, id: editingAddress.id }\n                        : addr\n                ));\n            } else {\n                // Add new address\n                const newAddress = {\n                    ...formData,\n                    id: Date.now() // Mock ID generation\n                };\n                setAddresses(prev => [...prev, newAddress]);\n            }\n\n            resetForm();\n        } catch (error) {\n            console.error('Failed to save address:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleEdit = (address) => {\n        setEditingAddress(address);\n        setFormData(address);\n        setShowAddForm(true);\n    };\n\n    const handleDelete = async (addressId) => {\n        if (window.confirm('Are you sure you want to delete this address?')) {\n            setAddresses(prev => prev.filter(addr => addr.id !== addressId));\n        }\n    };\n\n    const handleSetDefault = async (addressId) => {\n        setAddresses(prev => prev.map(addr => ({\n            ...addr,\n            isDefault: addr.id === addressId\n        })));\n    };\n\n    const resetForm = () => {\n        setFormData({\n            type: 'shipping',\n            firstName: '',\n            lastName: '',\n            company: '',\n            street: '',\n            city: '',\n            state: '',\n            zipCode: '',\n            country: 'Philippines',\n            phone: '',\n            isDefault: false\n        });\n        setEditingAddress(null);\n        setShowAddForm(false);\n    };\n\n    const formatAddress = (address) => {\n        const parts = [\n            address.street,\n            address.city,\n            address.state,\n            address.zipCode,\n            address.country\n        ].filter(Boolean);\n        return parts.join(', ');\n    };\n\n    return (\n        <div className=\"address-book\">\n            <div className=\"section-header\">\n                <div>\n                    <h2 className=\"section-title\">Address Book</h2>\n                    <p className=\"section-subtitle\">\n                        Manage your shipping and billing addresses\n                    </p>\n                </div>\n                <button\n                    className=\"btn-primary\"\n                    onClick={() => setShowAddForm(true)}\n                >\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                        <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"/>\n                        <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"/>\n                    </svg>\n                    Add New Address\n                </button>\n            </div>\n\n            {showAddForm && (\n                <div className=\"address-form-container\">\n                    <div className=\"form-header\">\n                        <h3>{editingAddress ? 'Edit Address' : 'Add New Address'}</h3>\n                        <button\n                            className=\"close-btn\"\n                            onClick={resetForm}\n                        >\n                            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"/>\n                                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"/>\n                            </svg>\n                        </button>\n                    </div>\n\n                    <form className=\"address-form\" onSubmit={handleSubmit}>\n                        <div className=\"form-group\">\n                            <label className=\"form-label\">Address Type</label>\n                            <select\n                                name=\"type\"\n                                className=\"form-input\"\n                                value={formData.type}\n                                onChange={handleChange}\n                                required\n                            >\n                                <option value=\"shipping\">Shipping Address</option>\n                                <option value=\"billing\">Billing Address</option>\n                            </select>\n                        </div>\n\n                        <div className=\"form-row\">\n                            <div className=\"form-group\">\n                                <label className=\"form-label\">First Name</label>\n                                <input\n                                    type=\"text\"\n                                    name=\"firstName\"\n                                    className=\"form-input\"\n                                    value={formData.firstName}\n                                    onChange={handleChange}\n                                    required\n                                />\n                            </div>\n                            <div className=\"form-group\">\n                                <label className=\"form-label\">Last Name</label>\n                                <input\n                                    type=\"text\"\n                                    name=\"lastName\"\n                                    className=\"form-input\"\n                                    value={formData.lastName}\n                                    onChange={handleChange}\n                                    required\n                                />\n                            </div>\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label className=\"form-label\">Company (Optional)</label>\n                            <input\n                                type=\"text\"\n                                name=\"company\"\n                                className=\"form-input\"\n                                value={formData.company}\n                                onChange={handleChange}\n                            />\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label className=\"form-label\">Street Address</label>\n                            <input\n                                type=\"text\"\n                                name=\"street\"\n                                className=\"form-input\"\n                                value={formData.street}\n                                onChange={handleChange}\n                                required\n                            />\n                        </div>\n\n                        <div className=\"form-row\">\n                            <div className=\"form-group\">\n                                <label className=\"form-label\">City</label>\n                                <input\n                                    type=\"text\"\n                                    name=\"city\"\n                                    className=\"form-input\"\n                                    value={formData.city}\n                                    onChange={handleChange}\n                                    required\n                                />\n                            </div>\n                            <div className=\"form-group\">\n                                <label className=\"form-label\">State/Province</label>\n                                <input\n                                    type=\"text\"\n                                    name=\"state\"\n                                    className=\"form-input\"\n                                    value={formData.state}\n                                    onChange={handleChange}\n                                    required\n                                />\n                            </div>\n                        </div>\n\n                        <div className=\"form-row\">\n                            <div className=\"form-group\">\n                                <label className=\"form-label\">ZIP/Postal Code</label>\n                                <input\n                                    type=\"text\"\n                                    name=\"zipCode\"\n                                    className=\"form-input\"\n                                    value={formData.zipCode}\n                                    onChange={handleChange}\n                                    required\n                                />\n                            </div>\n                            <div className=\"form-group\">\n                                <label className=\"form-label\">Country</label>\n                                <select\n                                    name=\"country\"\n                                    className=\"form-input\"\n                                    value={formData.country}\n                                    onChange={handleChange}\n                                    required\n                                >\n                                    <option value=\"Philippines\">Philippines</option>\n                                    <option value=\"United States\">United States</option>\n                                    <option value=\"Canada\">Canada</option>\n                                    <option value=\"United Kingdom\">United Kingdom</option>\n                                    <option value=\"Australia\">Australia</option>\n                                </select>\n                            </div>\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label className=\"form-label\">Phone Number</label>\n                            <input\n                                type=\"tel\"\n                                name=\"phone\"\n                                className=\"form-input\"\n                                value={formData.phone}\n                                onChange={handleChange}\n                                required\n                            />\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label className=\"checkbox-label\">\n                                <input\n                                    type=\"checkbox\"\n                                    name=\"isDefault\"\n                                    checked={formData.isDefault}\n                                    onChange={handleChange}\n                                />\n                                <span className=\"checkmark\"></span>\n                                Set as default address\n                            </label>\n                        </div>\n\n                        <div className=\"form-actions\">\n                            <button\n                                type=\"submit\"\n                                className=\"btn-primary\"\n                                disabled={loading}\n                            >\n                                {loading ? 'Saving...' : (editingAddress ? 'Update Address' : 'Add Address')}\n                            </button>\n                            <button\n                                type=\"button\"\n                                className=\"btn-secondary\"\n                                onClick={resetForm}\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </form>\n                </div>\n            )}\n\n            <div className=\"addresses-list\">\n                {addresses.length === 0 ? (\n                    <div className=\"empty-state\">\n                        <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\">\n                            <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"/>\n                            <circle cx=\"12\" cy=\"10\" r=\"3\"/>\n                        </svg>\n                        <h3>No addresses saved</h3>\n                        <p>Add your first address to make checkout faster and easier.</p>\n                    </div>\n                ) : (\n                    <div className=\"address-grid\">\n                        {addresses.map(address => (\n                            <div key={address.id} className=\"address-card\">\n                                <div className=\"address-header\">\n                                    <div className=\"address-type\">\n                                        <span className={`type-badge ${address.type}`}>\n                                            {address.type === 'shipping' ? (\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                                    <rect x=\"1\" y=\"3\" width=\"15\" height=\"13\"/>\n                                                    <polygon points=\"16,8 20,8 23,11 23,16 16,16\"/>\n                                                    <circle cx=\"5.5\" cy=\"18.5\" r=\"2.5\"/>\n                                                    <circle cx=\"18.5\" cy=\"18.5\" r=\"2.5\"/>\n                                                </svg>\n                                            ) : (\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                                    <rect x=\"1\" y=\"4\" width=\"22\" height=\"16\" rx=\"2\" ry=\"2\"/>\n                                                    <line x1=\"1\" y1=\"10\" x2=\"23\" y2=\"10\"/>\n                                                </svg>\n                                            )}\n                                            {address.type.charAt(0).toUpperCase() + address.type.slice(1)}\n                                        </span>\n                                    </div>\n                                    {address.isDefault && (\n                                        <span className=\"default-badge\">Default</span>\n                                    )}\n                                </div>\n\n                                <div className=\"address-content\">\n                                    <h4 className=\"address-name\">\n                                        {address.firstName} {address.lastName}\n                                    </h4>\n                                    {address.company && (\n                                        <p className=\"address-company\">{address.company}</p>\n                                    )}\n                                    <p className=\"address-text\">{formatAddress(address)}</p>\n                                    <p className=\"address-phone\">{address.phone}</p>\n                                </div>\n\n                                <div className=\"address-actions\">\n                                    <button\n                                        className=\"action-btn edit\"\n                                        onClick={() => handleEdit(address)}\n                                    >\n                                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                            <path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"/>\n                                            <path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"/>\n                                        </svg>\n                                        Edit\n                                    </button>\n                                    {!address.isDefault && (\n                                        <button\n                                            className=\"action-btn default\"\n                                            onClick={() => handleSetDefault(address.id)}\n                                        >\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                                <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\"/>\n                                            </svg>\n                                            Set Default\n                                        </button>\n                                    )}\n                                    <button\n                                        className=\"action-btn delete\"\n                                        onClick={() => handleDelete(address.id)}\n                                    >\n                                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\n                                            <polyline points=\"3,6 5,6 21,6\"/>\n                                            <path d=\"M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2\"/>\n                                        </svg>\n                                        Delete\n                                    </button>\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n                )}\n            </div>\n        </div>\n    );\n};\n\nexport default AddressBook;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACrCkB,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE;EACf,CAAC,CAAC;EAEF3B,SAAS,CAAC,MAAM;IACZ4B,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/Bd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA;MACA,MAAMe,aAAa,GAAG,CAClB;QACIC,EAAE,EAAE,CAAC;QACLb,IAAI,EAAE,UAAU;QAChBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,iBAAiB;QAC1BC,MAAM,EAAE,gCAAgC;QACxCC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,cAAc;QACrBC,OAAO,EAAE,MAAM;QACfC,OAAO,EAAE,aAAa;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,SAAS,EAAE;MACf,CAAC,EACD;QACIG,EAAE,EAAE,CAAC;QACLb,IAAI,EAAE,SAAS;QACfC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,cAAc;QACrBC,OAAO,EAAE,MAAM;QACfC,OAAO,EAAE,aAAa;QACtBC,KAAK,EAAE,kBAAkB;QACzBC,SAAS,EAAE;MACf,CAAC,CACJ;MACDnB,YAAY,CAACqB,aAAa,CAAC;IAC/B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACNjB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEnB,IAAI;MAAEoB;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/CtB,WAAW,CAACuB,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGlB,IAAI,KAAK,UAAU,GAAGoB,OAAO,GAAGD;IAC5C,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMI,YAAY,GAAG,MAAON,CAAC,IAAK;IAC9BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACA,IAAIH,cAAc,EAAE;QAChB;QACAH,YAAY,CAAC+B,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,IAAI,IAC9BA,IAAI,CAACb,EAAE,KAAKnB,cAAc,CAACmB,EAAE,GACvB;UAAE,GAAGf,QAAQ;UAAEe,EAAE,EAAEnB,cAAc,CAACmB;QAAG,CAAC,GACtCa,IACV,CAAC,CAAC;MACN,CAAC,MAAM;QACH;QACA,MAAMC,UAAU,GAAG;UACf,GAAG7B,QAAQ;UACXe,EAAE,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;QACnB,CAAC;QACDtC,YAAY,CAAC+B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEK,UAAU,CAAC,CAAC;MAC/C;MAEAG,SAAS,CAAC,CAAC;IACf,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACNjB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkC,UAAU,GAAIC,OAAO,IAAK;IAC5BrC,iBAAiB,CAACqC,OAAO,CAAC;IAC1BjC,WAAW,CAACiC,OAAO,CAAC;IACpBvC,cAAc,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMwC,YAAY,GAAG,MAAOC,SAAS,IAAK;IACtC,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACjE7C,YAAY,CAAC+B,IAAI,IAAIA,IAAI,CAACe,MAAM,CAACX,IAAI,IAAIA,IAAI,CAACb,EAAE,KAAKqB,SAAS,CAAC,CAAC;IACpE;EACJ,CAAC;EAED,MAAMI,gBAAgB,GAAG,MAAOJ,SAAS,IAAK;IAC1C3C,YAAY,CAAC+B,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,IAAI,KAAK;MACnC,GAAGA,IAAI;MACPhB,SAAS,EAAEgB,IAAI,CAACb,EAAE,KAAKqB;IAC3B,CAAC,CAAC,CAAC,CAAC;EACR,CAAC;EAED,MAAMJ,SAAS,GAAGA,CAAA,KAAM;IACpB/B,WAAW,CAAC;MACRC,IAAI,EAAE,UAAU;MAChBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,aAAa;MACtBC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE;IACf,CAAC,CAAC;IACFf,iBAAiB,CAAC,IAAI,CAAC;IACvBF,cAAc,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAM8C,aAAa,GAAIP,OAAO,IAAK;IAC/B,MAAMQ,KAAK,GAAG,CACVR,OAAO,CAAC5B,MAAM,EACd4B,OAAO,CAAC3B,IAAI,EACZ2B,OAAO,CAAC1B,KAAK,EACb0B,OAAO,CAACzB,OAAO,EACfyB,OAAO,CAACxB,OAAO,CAClB,CAAC6B,MAAM,CAACI,OAAO,CAAC;IACjB,OAAOD,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,oBACIxD,OAAA;IAAKyD,SAAS,EAAC,cAAc;IAAAC,QAAA,gBACzB1D,OAAA;MAAKyD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3B1D,OAAA;QAAA0D,QAAA,gBACI1D,OAAA;UAAIyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/C9D,OAAA;UAAGyD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN9D,OAAA;QACIyD,SAAS,EAAC,aAAa;QACvBM,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAAC,IAAI,CAAE;QAAAmD,QAAA,gBAEpC1D,OAAA;UAAKgE,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAX,QAAA,gBAC7F1D,OAAA;YAAMsE,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACtC9D,OAAA;YAAMsE,EAAE,EAAC,GAAG;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,mBAEV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAELxD,WAAW,iBACRN,OAAA;MAAKyD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACnC1D,OAAA;QAAKyD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB1D,OAAA;UAAA0D,QAAA,EAAKlD,cAAc,GAAG,cAAc,GAAG;QAAiB;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9D9D,OAAA;UACIyD,SAAS,EAAC,WAAW;UACrBM,OAAO,EAAEnB,SAAU;UAAAc,QAAA,eAEnB1D,OAAA;YAAKgE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAAAX,QAAA,gBAC7F1D,OAAA;cAAMsE,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACrC9D,OAAA;cAAMsE,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEN9D,OAAA;QAAMyD,SAAS,EAAC,cAAc;QAACiB,QAAQ,EAAErC,YAAa;QAAAqB,QAAA,gBAClD1D,OAAA;UAAKyD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB1D,OAAA;YAAOyD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD9D,OAAA;YACIgC,IAAI,EAAC,MAAM;YACXyB,SAAS,EAAC,YAAY;YACtBxB,KAAK,EAAErB,QAAQ,CAACE,IAAK;YACrB6D,QAAQ,EAAE7C,YAAa;YACvB8C,QAAQ;YAAAlB,QAAA,gBAER1D,OAAA;cAAQiC,KAAK,EAAC,UAAU;cAAAyB,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClD9D,OAAA;cAAQiC,KAAK,EAAC,SAAS;cAAAyB,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACrB1D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChD9D,OAAA;cACIc,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC,WAAW;cAChByB,SAAS,EAAC,YAAY;cACtBxB,KAAK,EAAErB,QAAQ,CAACG,SAAU;cAC1B4D,QAAQ,EAAE7C,YAAa;cACvB8C,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/C9D,OAAA;cACIc,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC,UAAU;cACfyB,SAAS,EAAC,YAAY;cACtBxB,KAAK,EAAErB,QAAQ,CAACI,QAAS;cACzB2D,QAAQ,EAAE7C,YAAa;cACvB8C,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB1D,OAAA;YAAOyD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxD9D,OAAA;YACIc,IAAI,EAAC,MAAM;YACXkB,IAAI,EAAC,SAAS;YACdyB,SAAS,EAAC,YAAY;YACtBxB,KAAK,EAAErB,QAAQ,CAACK,OAAQ;YACxB0D,QAAQ,EAAE7C;UAAa;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB1D,OAAA;YAAOyD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpD9D,OAAA;YACIc,IAAI,EAAC,MAAM;YACXkB,IAAI,EAAC,QAAQ;YACbyB,SAAS,EAAC,YAAY;YACtBxB,KAAK,EAAErB,QAAQ,CAACM,MAAO;YACvByD,QAAQ,EAAE7C,YAAa;YACvB8C,QAAQ;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACrB1D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C9D,OAAA;cACIc,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC,MAAM;cACXyB,SAAS,EAAC,YAAY;cACtBxB,KAAK,EAAErB,QAAQ,CAACO,IAAK;cACrBwD,QAAQ,EAAE7C,YAAa;cACvB8C,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpD9D,OAAA;cACIc,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC,OAAO;cACZyB,SAAS,EAAC,YAAY;cACtBxB,KAAK,EAAErB,QAAQ,CAACQ,KAAM;cACtBuD,QAAQ,EAAE7C,YAAa;cACvB8C,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACrB1D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrD9D,OAAA;cACIc,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC,SAAS;cACdyB,SAAS,EAAC,YAAY;cACtBxB,KAAK,EAAErB,QAAQ,CAACS,OAAQ;cACxBsD,QAAQ,EAAE7C,YAAa;cACvB8C,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB1D,OAAA;cAAOyD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7C9D,OAAA;cACIgC,IAAI,EAAC,SAAS;cACdyB,SAAS,EAAC,YAAY;cACtBxB,KAAK,EAAErB,QAAQ,CAACU,OAAQ;cACxBqD,QAAQ,EAAE7C,YAAa;cACvB8C,QAAQ;cAAAlB,QAAA,gBAER1D,OAAA;gBAAQiC,KAAK,EAAC,aAAa;gBAAAyB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD9D,OAAA;gBAAQiC,KAAK,EAAC,eAAe;gBAAAyB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpD9D,OAAA;gBAAQiC,KAAK,EAAC,QAAQ;gBAAAyB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC9D,OAAA;gBAAQiC,KAAK,EAAC,gBAAgB;gBAAAyB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtD9D,OAAA;gBAAQiC,KAAK,EAAC,WAAW;gBAAAyB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB1D,OAAA;YAAOyD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD9D,OAAA;YACIc,IAAI,EAAC,KAAK;YACVkB,IAAI,EAAC,OAAO;YACZyB,SAAS,EAAC,YAAY;YACtBxB,KAAK,EAAErB,QAAQ,CAACW,KAAM;YACtBoD,QAAQ,EAAE7C,YAAa;YACvB8C,QAAQ;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvB1D,OAAA;YAAOyD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B1D,OAAA;cACIc,IAAI,EAAC,UAAU;cACfkB,IAAI,EAAC,WAAW;cAChBE,OAAO,EAAEtB,QAAQ,CAACY,SAAU;cAC5BmD,QAAQ,EAAE7C;YAAa;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACF9D,OAAA;cAAMyD,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,0BAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzB1D,OAAA;YACIc,IAAI,EAAC,QAAQ;YACb2C,SAAS,EAAC,aAAa;YACvBoB,QAAQ,EAAEnE,OAAQ;YAAAgD,QAAA,EAEjBhD,OAAO,GAAG,WAAW,GAAIF,cAAc,GAAG,gBAAgB,GAAG;UAAc;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACT9D,OAAA;YACIc,IAAI,EAAC,QAAQ;YACb2C,SAAS,EAAC,eAAe;YACzBM,OAAO,EAAEnB,SAAU;YAAAc,QAAA,EACtB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACR,eAED9D,OAAA;MAAKyD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC1BtD,SAAS,CAAC0E,MAAM,KAAK,CAAC,gBACnB9E,OAAA;QAAKyD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB1D,OAAA;UAAKgE,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAAAX,QAAA,gBAC7F1D,OAAA;YAAM+E,CAAC,EAAC;UAAgD;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC1D9D,OAAA;YAAQgF,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACN9D,OAAA;UAAA0D,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B9D,OAAA;UAAA0D,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,gBAEN9D,OAAA;QAAKyD,SAAS,EAAC,cAAc;QAAAC,QAAA,EACxBtD,SAAS,CAACmC,GAAG,CAACO,OAAO,iBAClB9C,OAAA;UAAsByD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC1C1D,OAAA;YAAKyD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B1D,OAAA;cAAKyD,SAAS,EAAC,cAAc;cAAAC,QAAA,eACzB1D,OAAA;gBAAMyD,SAAS,EAAE,cAAcX,OAAO,CAAChC,IAAI,EAAG;gBAAA4C,QAAA,GACzCZ,OAAO,CAAChC,IAAI,KAAK,UAAU,gBACxBd,OAAA;kBAAKgE,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAX,QAAA,gBAC7F1D,OAAA;oBAAMmF,CAAC,EAAC,GAAG;oBAACC,CAAC,EAAC,GAAG;oBAACpB,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC;kBAAI;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC1C9D,OAAA;oBAASqF,MAAM,EAAC;kBAA6B;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/C9D,OAAA;oBAAQgF,EAAE,EAAC,KAAK;oBAACC,EAAE,EAAC,MAAM;oBAACC,CAAC,EAAC;kBAAK;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACpC9D,OAAA;oBAAQgF,EAAE,EAAC,MAAM;oBAACC,EAAE,EAAC,MAAM;oBAACC,CAAC,EAAC;kBAAK;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,gBAEN9D,OAAA;kBAAKgE,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAAAX,QAAA,gBAC7F1D,OAAA;oBAAMmF,CAAC,EAAC,GAAG;oBAACC,CAAC,EAAC,GAAG;oBAACpB,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACqB,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACxD9D,OAAA;oBAAMsE,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC;kBAAI;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CACR,EACAhB,OAAO,CAAChC,IAAI,CAAC0E,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG3C,OAAO,CAAChC,IAAI,CAAC4E,KAAK,CAAC,CAAC,CAAC;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLhB,OAAO,CAACtB,SAAS,iBACdxB,OAAA;cAAMyD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEN9D,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B1D,OAAA;cAAIyD,SAAS,EAAC,cAAc;cAAAC,QAAA,GACvBZ,OAAO,CAAC/B,SAAS,EAAC,GAAC,EAAC+B,OAAO,CAAC9B,QAAQ;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,EACJhB,OAAO,CAAC7B,OAAO,iBACZjB,OAAA;cAAGyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEZ,OAAO,CAAC7B;YAAO;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACtD,eACD9D,OAAA;cAAGyD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEL,aAAa,CAACP,OAAO;YAAC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD9D,OAAA;cAAGyD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEZ,OAAO,CAACvB;YAAK;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAEN9D,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B1D,OAAA;cACIyD,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAACC,OAAO,CAAE;cAAAY,QAAA,gBAEnC1D,OAAA;gBAAKgE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAAAX,QAAA,gBAC7F1D,OAAA;kBAAM+E,CAAC,EAAC;gBAA4D;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACtE9D,OAAA;kBAAM+E,CAAC,EAAC;gBAAyD;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,QAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACR,CAAChB,OAAO,CAACtB,SAAS,iBACfxB,OAAA;cACIyD,SAAS,EAAC,oBAAoB;cAC9BM,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACN,OAAO,CAACnB,EAAE,CAAE;cAAA+B,QAAA,gBAE5C1D,OAAA;gBAAKgE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAAAX,QAAA,eAC7F1D,OAAA;kBAASqF,MAAM,EAAC;gBAA2F;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5G,CAAC,eAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX,eACD9D,OAAA;cACIyD,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAMhB,YAAY,CAACD,OAAO,CAACnB,EAAE,CAAE;cAAA+B,QAAA,gBAExC1D,OAAA;gBAAKgE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAAAX,QAAA,gBAC7F1D,OAAA;kBAAUqF,MAAM,EAAC;gBAAc;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACjC9D,OAAA;kBAAM+E,CAAC,EAAC;gBAAgF;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC,UAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GApEAhB,OAAO,CAACnB,EAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqEf,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC5D,EAAA,CAvbID,WAAW;EAAA,QACIH,OAAO;AAAA;AAAA6F,EAAA,GADtB1F,WAAW;AAybjB,eAAeA,WAAW;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}