{"ast": null, "code": "// Centralized API Client\n// This service provides a configured axios instance for all API calls\n\nimport axios from 'axios';\nimport apiConfig from './apiConfig';\nclass ApiClient {\n  constructor() {\n    // Create axios instance with base configuration\n    this.client = axios.create({\n      baseURL: apiConfig.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      }\n    });\n\n    // Setup request interceptor for authentication\n    this.client.interceptors.request.use(config => {\n      // Add auth token if available\n      const token = this.getAuthToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n\n      // Add request timestamp for debugging\n      if (apiConfig.debugMode) {\n        var _config$method;\n        config.metadata = {\n          startTime: new Date()\n        };\n        console.log(`🚀 API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n      }\n      return config;\n    }, error => {\n      console.error('❌ Request Error:', error);\n      return Promise.reject(error);\n    });\n\n    // Setup response interceptor for error handling\n    this.client.interceptors.response.use(response => {\n      // Log response time in debug mode\n      if (apiConfig.debugMode && response.config.metadata) {\n        var _response$config$meth;\n        const duration = new Date() - response.config.metadata.startTime;\n        console.log(`✅ API Response: ${(_response$config$meth = response.config.method) === null || _response$config$meth === void 0 ? void 0 : _response$config$meth.toUpperCase()} ${response.config.url} (${duration}ms)`);\n      }\n      return response;\n    }, error => {\n      // Handle different error types\n      if (error.response) {\n        // Server responded with error status\n        const {\n          status,\n          data\n        } = error.response;\n        if (status === 401) {\n          // Unauthorized - clear auth token and redirect to login\n          this.clearAuthToken();\n          if (window.location.pathname !== '/login') {\n            window.location.href = '/login';\n          }\n        } else if (status === 403) {\n          // Forbidden - check if it's an invalid token issue\n          if (data.message && (data.message.includes('token') || data.message.includes('expired') || data.message.includes('invalid'))) {\n            // Invalid/expired token - clear auth and redirect to login\n            this.clearAuthToken();\n            if (window.location.pathname !== '/login') {\n              window.location.href = '/login';\n            }\n          } else {\n            // Insufficient permissions - show access denied message\n            console.error('❌ Access Denied:', data.message || 'Insufficient permissions');\n          }\n        } else if (status >= 500) {\n          // Server error\n          console.error('❌ Server Error:', data.message || 'Internal server error');\n        }\n\n        // Log error details in debug mode\n        if (apiConfig.debugMode) {\n          var _error$config, _error$config$method, _error$config2;\n          console.error(`❌ API Error: ${(_error$config = error.config) === null || _error$config === void 0 ? void 0 : (_error$config$method = _error$config.method) === null || _error$config$method === void 0 ? void 0 : _error$config$method.toUpperCase()} ${(_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url}`, {\n            status,\n            data,\n            headers: error.response.headers\n          });\n        }\n      } else if (error.request) {\n        // Network error\n        console.error('❌ Network Error:', error.message);\n      } else {\n        // Other error\n        console.error('❌ Request Setup Error:', error.message);\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // Get authentication token from localStorage\n  getAuthToken() {\n    return localStorage.getItem('token') || localStorage.getItem('authToken');\n  }\n\n  // Clear authentication token\n  clearAuthToken() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('user');\n  }\n\n  // Generic GET request\n  async get(url, config = {}) {\n    try {\n      const response = await this.client.get(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic POST request\n  async post(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.post(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PUT request\n  async put(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.put(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PATCH request\n  async patch(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.patch(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic DELETE request\n  async delete(url, config = {}) {\n    try {\n      const response = await this.client.delete(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Handle and format errors\n  handleError(error) {\n    if (error.response) {\n      var _error$response$data, _error$response$data2;\n      // Server responded with error\n      const message = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.error) || 'Server error occurred';\n      return new Error(message);\n    } else if (error.request) {\n      // Network error\n      return new Error('Network error - please check your connection');\n    } else {\n      // Other error\n      return new Error(error.message || 'An unexpected error occurred');\n    }\n  }\n\n  // Health check\n  async healthCheck() {\n    try {\n      const response = await this.get('/health');\n      return response;\n    } catch (error) {\n      console.error('❌ Backend health check failed:', error.message);\n      return null;\n    }\n  }\n\n  // Test connection to backend\n  async testConnection() {\n    try {\n      const health = await this.healthCheck();\n      if (health) {\n        console.log('✅ Backend connection successful');\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('❌ Backend connection failed:', error.message);\n      return false;\n    }\n  }\n}\n\n// Create singleton instance\nconst apiClient = new ApiClient();\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "apiConfig", "ApiClient", "constructor", "client", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "getAuthToken", "Authorization", "debugMode", "_config$method", "metadata", "startTime", "Date", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "_response$config$meth", "duration", "status", "data", "clearAuthToken", "window", "location", "pathname", "href", "message", "includes", "_error$config", "_error$config$method", "_error$config2", "localStorage", "getItem", "removeItem", "get", "handleError", "post", "put", "patch", "delete", "_error$response$data", "_error$response$data2", "Error", "healthCheck", "testConnection", "health", "apiClient"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/services/apiClient.js"], "sourcesContent": ["// Centralized API Client\n// This service provides a configured axios instance for all API calls\n\nimport axios from 'axios';\nimport apiConfig from './apiConfig';\n\nclass ApiClient {\n  constructor() {\n    // Create axios instance with base configuration\n    this.client = axios.create({\n      baseURL: apiConfig.baseURL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      }\n    });\n\n    // Setup request interceptor for authentication\n    this.client.interceptors.request.use(\n      (config) => {\n        // Add auth token if available\n        const token = this.getAuthToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n\n        // Add request timestamp for debugging\n        if (apiConfig.debugMode) {\n          config.metadata = { startTime: new Date() };\n          console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n        }\n\n        return config;\n      },\n      (error) => {\n        console.error('❌ Request Error:', error);\n        return Promise.reject(error);\n      }\n    );\n\n    // Setup response interceptor for error handling\n    this.client.interceptors.response.use(\n      (response) => {\n        // Log response time in debug mode\n        if (apiConfig.debugMode && response.config.metadata) {\n          const duration = new Date() - response.config.metadata.startTime;\n          console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);\n        }\n\n        return response;\n      },\n      (error) => {\n        // Handle different error types\n        if (error.response) {\n          // Server responded with error status\n          const { status, data } = error.response;\n          \n          if (status === 401) {\n            // Unauthorized - clear auth token and redirect to login\n            this.clearAuthToken();\n            if (window.location.pathname !== '/login') {\n              window.location.href = '/login';\n            }\n          } else if (status === 403) {\n            // Forbidden - check if it's an invalid token issue\n            if (data.message && (data.message.includes('token') || data.message.includes('expired') || data.message.includes('invalid'))) {\n              // Invalid/expired token - clear auth and redirect to login\n              this.clearAuthToken();\n              if (window.location.pathname !== '/login') {\n                window.location.href = '/login';\n              }\n            } else {\n              // Insufficient permissions - show access denied message\n              console.error('❌ Access Denied:', data.message || 'Insufficient permissions');\n            }\n          } else if (status >= 500) {\n            // Server error\n            console.error('❌ Server Error:', data.message || 'Internal server error');\n          }\n\n          // Log error details in debug mode\n          if (apiConfig.debugMode) {\n            console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {\n              status,\n              data,\n              headers: error.response.headers\n            });\n          }\n        } else if (error.request) {\n          // Network error\n          console.error('❌ Network Error:', error.message);\n        } else {\n          // Other error\n          console.error('❌ Request Setup Error:', error.message);\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Get authentication token from localStorage\n  getAuthToken() {\n    return localStorage.getItem('token') || localStorage.getItem('authToken');\n  }\n\n  // Clear authentication token\n  clearAuthToken() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('user');\n  }\n\n  // Generic GET request\n  async get(url, config = {}) {\n    try {\n      const response = await this.client.get(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic POST request\n  async post(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.post(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PUT request\n  async put(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.put(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic PATCH request\n  async patch(url, data = {}, config = {}) {\n    try {\n      const response = await this.client.patch(url, data, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Generic DELETE request\n  async delete(url, config = {}) {\n    try {\n      const response = await this.client.delete(url, config);\n      return response.data;\n    } catch (error) {\n      throw this.handleError(error);\n    }\n  }\n\n  // Handle and format errors\n  handleError(error) {\n    if (error.response) {\n      // Server responded with error\n      const message = error.response.data?.message || error.response.data?.error || 'Server error occurred';\n      return new Error(message);\n    } else if (error.request) {\n      // Network error\n      return new Error('Network error - please check your connection');\n    } else {\n      // Other error\n      return new Error(error.message || 'An unexpected error occurred');\n    }\n  }\n\n  // Health check\n  async healthCheck() {\n    try {\n      const response = await this.get('/health');\n      return response;\n    } catch (error) {\n      console.error('❌ Backend health check failed:', error.message);\n      return null;\n    }\n  }\n\n  // Test connection to backend\n  async testConnection() {\n    try {\n      const health = await this.healthCheck();\n      if (health) {\n        console.log('✅ Backend connection successful');\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('❌ Backend connection failed:', error.message);\n      return false;\n    }\n  }\n}\n\n// Create singleton instance\nconst apiClient = new ApiClient();\n\nexport default apiClient;\n"], "mappings": "AAAA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,aAAa;AAEnC,MAAMC,SAAS,CAAC;EACdC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,MAAM,GAAGJ,KAAK,CAACK,MAAM,CAAC;MACzBC,OAAO,EAAEL,SAAS,CAACK,OAAO;MAC1BC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACJ,MAAM,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;MACV;MACA,MAAMC,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MACjC,IAAID,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACO,aAAa,GAAG,UAAUF,KAAK,EAAE;MAClD;;MAEA;MACA,IAAIZ,SAAS,CAACe,SAAS,EAAE;QAAA,IAAAC,cAAA;QACvBL,MAAM,CAACM,QAAQ,GAAG;UAAEC,SAAS,EAAE,IAAIC,IAAI,CAAC;QAAE,CAAC;QAC3CC,OAAO,CAACC,GAAG,CAAC,oBAAAL,cAAA,GAAmBL,MAAM,CAACW,MAAM,cAAAN,cAAA,uBAAbA,cAAA,CAAeO,WAAW,CAAC,CAAC,IAAIZ,MAAM,CAACa,GAAG,EAAE,CAAC;MAC9E;MAEA,OAAOb,MAAM;IACf,CAAC,EACAc,KAAK,IAAK;MACTL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACtB,MAAM,CAACK,YAAY,CAACoB,QAAQ,CAAClB,GAAG,CAClCkB,QAAQ,IAAK;MACZ;MACA,IAAI5B,SAAS,CAACe,SAAS,IAAIa,QAAQ,CAACjB,MAAM,CAACM,QAAQ,EAAE;QAAA,IAAAY,qBAAA;QACnD,MAAMC,QAAQ,GAAG,IAAIX,IAAI,CAAC,CAAC,GAAGS,QAAQ,CAACjB,MAAM,CAACM,QAAQ,CAACC,SAAS;QAChEE,OAAO,CAACC,GAAG,CAAC,oBAAAQ,qBAAA,GAAmBD,QAAQ,CAACjB,MAAM,CAACW,MAAM,cAAAO,qBAAA,uBAAtBA,qBAAA,CAAwBN,WAAW,CAAC,CAAC,IAAIK,QAAQ,CAACjB,MAAM,CAACa,GAAG,KAAKM,QAAQ,KAAK,CAAC;MAChH;MAEA,OAAOF,QAAQ;IACjB,CAAC,EACAH,KAAK,IAAK;MACT;MACA,IAAIA,KAAK,CAACG,QAAQ,EAAE;QAClB;QACA,MAAM;UAAEG,MAAM;UAAEC;QAAK,CAAC,GAAGP,KAAK,CAACG,QAAQ;QAEvC,IAAIG,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAI,CAACE,cAAc,CAAC,CAAC;UACrB,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;YACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;UACjC;QACF,CAAC,MAAM,IAAIN,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAIC,IAAI,CAACM,OAAO,KAAKN,IAAI,CAACM,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIP,IAAI,CAACM,OAAO,CAACC,QAAQ,CAAC,SAAS,CAAC,IAAIP,IAAI,CAACM,OAAO,CAACC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE;YAC5H;YACA,IAAI,CAACN,cAAc,CAAC,CAAC;YACrB,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;cACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;YACjC;UACF,CAAC,MAAM;YACL;YACAjB,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEO,IAAI,CAACM,OAAO,IAAI,0BAA0B,CAAC;UAC/E;QACF,CAAC,MAAM,IAAIP,MAAM,IAAI,GAAG,EAAE;UACxB;UACAX,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAEO,IAAI,CAACM,OAAO,IAAI,uBAAuB,CAAC;QAC3E;;QAEA;QACA,IAAItC,SAAS,CAACe,SAAS,EAAE;UAAA,IAAAyB,aAAA,EAAAC,oBAAA,EAAAC,cAAA;UACvBtB,OAAO,CAACK,KAAK,CAAC,iBAAAe,aAAA,GAAgBf,KAAK,CAACd,MAAM,cAAA6B,aAAA,wBAAAC,oBAAA,GAAZD,aAAA,CAAclB,MAAM,cAAAmB,oBAAA,uBAApBA,oBAAA,CAAsBlB,WAAW,CAAC,CAAC,KAAAmB,cAAA,GAAIjB,KAAK,CAACd,MAAM,cAAA+B,cAAA,uBAAZA,cAAA,CAAclB,GAAG,EAAE,EAAE;YACxFO,MAAM;YACNC,IAAI;YACJzB,OAAO,EAAEkB,KAAK,CAACG,QAAQ,CAACrB;UAC1B,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAIkB,KAAK,CAAChB,OAAO,EAAE;QACxB;QACAW,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACa,OAAO,CAAC;MAClD,CAAC,MAAM;QACL;QACAlB,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACa,OAAO,CAAC;MACxD;MAEA,OAAOZ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACAZ,YAAYA,CAAA,EAAG;IACb,OAAO8B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC3E;;EAEA;EACAX,cAAcA,CAAA,EAAG;IACfU,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;IAChCF,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC;IACpCF,YAAY,CAACE,UAAU,CAAC,MAAM,CAAC;EACjC;;EAEA;EACA,MAAMC,GAAGA,CAACtB,GAAG,EAAEb,MAAM,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAAC2C,GAAG,CAACtB,GAAG,EAAEb,MAAM,CAAC;MACnD,OAAOiB,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAM,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAMuB,IAAIA,CAACxB,GAAG,EAAEQ,IAAI,GAAG,CAAC,CAAC,EAAErB,MAAM,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAAC6C,IAAI,CAACxB,GAAG,EAAEQ,IAAI,EAAErB,MAAM,CAAC;MAC1D,OAAOiB,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAM,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAMwB,GAAGA,CAACzB,GAAG,EAAEQ,IAAI,GAAG,CAAC,CAAC,EAAErB,MAAM,GAAG,CAAC,CAAC,EAAE;IACrC,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAAC8C,GAAG,CAACzB,GAAG,EAAEQ,IAAI,EAAErB,MAAM,CAAC;MACzD,OAAOiB,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAM,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAMyB,KAAKA,CAAC1B,GAAG,EAAEQ,IAAI,GAAG,CAAC,CAAC,EAAErB,MAAM,GAAG,CAAC,CAAC,EAAE;IACvC,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAAC+C,KAAK,CAAC1B,GAAG,EAAEQ,IAAI,EAAErB,MAAM,CAAC;MAC3D,OAAOiB,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAM,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC;IAC/B;EACF;;EAEA;EACA,MAAM0B,MAAMA,CAAC3B,GAAG,EAAEb,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7B,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM,IAAI,CAACzB,MAAM,CAACgD,MAAM,CAAC3B,GAAG,EAAEb,MAAM,CAAC;MACtD,OAAOiB,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd,MAAM,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC;IAC/B;EACF;;EAEA;EACAsB,WAAWA,CAACtB,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACG,QAAQ,EAAE;MAAA,IAAAwB,oBAAA,EAAAC,qBAAA;MAClB;MACA,MAAMf,OAAO,GAAG,EAAAc,oBAAA,GAAA3B,KAAK,CAACG,QAAQ,CAACI,IAAI,cAAAoB,oBAAA,uBAAnBA,oBAAA,CAAqBd,OAAO,OAAAe,qBAAA,GAAI5B,KAAK,CAACG,QAAQ,CAACI,IAAI,cAAAqB,qBAAA,uBAAnBA,qBAAA,CAAqB5B,KAAK,KAAI,uBAAuB;MACrG,OAAO,IAAI6B,KAAK,CAAChB,OAAO,CAAC;IAC3B,CAAC,MAAM,IAAIb,KAAK,CAAChB,OAAO,EAAE;MACxB;MACA,OAAO,IAAI6C,KAAK,CAAC,8CAA8C,CAAC;IAClE,CAAC,MAAM;MACL;MACA,OAAO,IAAIA,KAAK,CAAC7B,KAAK,CAACa,OAAO,IAAI,8BAA8B,CAAC;IACnE;EACF;;EAEA;EACA,MAAMiB,WAAWA,CAAA,EAAG;IAClB,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAM,IAAI,CAACkB,GAAG,CAAC,SAAS,CAAC;MAC1C,OAAOlB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAACa,OAAO,CAAC;MAC9D,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAMkB,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACF,WAAW,CAAC,CAAC;MACvC,IAAIE,MAAM,EAAE;QACVrC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAACa,OAAO,CAAC;MAC5D,OAAO,KAAK;IACd;EACF;AACF;;AAEA;AACA,MAAMoB,SAAS,GAAG,IAAIzD,SAAS,CAAC,CAAC;AAEjC,eAAeyD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}