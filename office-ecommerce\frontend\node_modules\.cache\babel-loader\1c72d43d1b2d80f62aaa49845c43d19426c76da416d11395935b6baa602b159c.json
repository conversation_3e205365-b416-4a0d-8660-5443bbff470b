{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\SupplierManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SupplierManagement = () => {\n  _s();\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Mock data for now\n    setTimeout(() => {\n      setSuppliers([{\n        id: '1',\n        name: 'Office Furniture Co.',\n        contact: 'John <PERSON>',\n        email: '<EMAIL>',\n        phone: '+63 ************',\n        rating: 4.5,\n        status: 'Active'\n      }, {\n        id: '2',\n        name: 'Desk Solutions Inc.',\n        contact: '<PERSON>',\n        email: '<EMAIL>',\n        phone: '+63 ************',\n        rating: 4.2,\n        status: 'Active'\n      }]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading suppliers...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"supplier-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"admin-card-title\",\n        children: \"Supplier Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"admin-btn admin-btn-primary\",\n        children: \"Add New Supplier\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"admin-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Supplier Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Contact Person\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Rating\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: supplier.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: supplier.contact\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: supplier.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: supplier.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [\"\\u2B50 \", supplier.rating]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: '#27ae60'\n                  },\n                  children: supplier.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"admin-btn admin-btn-secondary btn-small\",\n                  children: \"Edit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this)]\n            }, supplier.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(SupplierManagement, \"HmwpFQWUDH2x5griNVnHBdNV6+Y=\");\n_c = SupplierManagement;\nexport default SupplierManagement;\nvar _c;\n$RefreshReg$(_c, \"SupplierManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "SupplierManagement", "_s", "suppliers", "setSuppliers", "loading", "setLoading", "setTimeout", "id", "name", "contact", "email", "phone", "rating", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "supplier", "style", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/SupplierManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst SupplierManagement = () => {\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Mock data for now\n    setTimeout(() => {\n      setSuppliers([\n        {\n          id: '1',\n          name: 'Office Furniture Co.',\n          contact: 'John Manager',\n          email: '<EMAIL>',\n          phone: '+63 ************',\n          rating: 4.5,\n          status: 'Active'\n        },\n        {\n          id: '2',\n          name: 'Desk Solutions Inc.',\n          contact: '<PERSON>',\n          email: '<EMAIL>',\n          phone: '+63 ************',\n          rating: 4.2,\n          status: 'Active'\n        }\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"admin-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading suppliers...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"supplier-management\">\n      <div className=\"admin-card-header\">\n        <h1 className=\"admin-card-title\">Supplier Management</h1>\n        <button className=\"admin-btn admin-btn-primary\">Add New Supplier</button>\n      </div>\n\n      <div className=\"admin-card\">\n        <div className=\"table-container\">\n          <table className=\"admin-table\">\n            <thead>\n              <tr>\n                <th>Supplier Name</th>\n                <th>Contact Person</th>\n                <th>Email</th>\n                <th>Phone</th>\n                <th>Rating</th>\n                <th>Status</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {suppliers.map(supplier => (\n                <tr key={supplier.id}>\n                  <td>{supplier.name}</td>\n                  <td>{supplier.contact}</td>\n                  <td>{supplier.email}</td>\n                  <td>{supplier.phone}</td>\n                  <td>⭐ {supplier.rating}</td>\n                  <td>\n                    <span className=\"status-badge\" style={{ backgroundColor: '#27ae60' }}>\n                      {supplier.status}\n                    </span>\n                  </td>\n                  <td>\n                    <button className=\"admin-btn admin-btn-secondary btn-small\">Edit</button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SupplierManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACAS,UAAU,CAAC,MAAM;MACfH,YAAY,CAAC,CACX;QACEI,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,sBAAsB;QAC5BC,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,0BAA0B;QACjCC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,GAAG;QACXC,MAAM,EAAE;MACV,CAAC,EACD;QACEN,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,qBAAqB;QAC3BC,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,yBAAyB;QAChCC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,GAAG;QACXC,MAAM,EAAE;MACV,CAAC,CACF,CAAC;MACFR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BhB,OAAA;QAAKe,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCpB,OAAA;QAAAgB,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEV;EAEA,oBACEpB,OAAA;IAAKe,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClChB,OAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChB,OAAA;QAAIe,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDpB,OAAA;QAAQe,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBhB,OAAA;QAAKe,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhB,OAAA;UAAOe,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC5BhB,OAAA;YAAAgB,QAAA,eACEhB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAAgB,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBpB,OAAA;gBAAAgB,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBpB,OAAA;gBAAAgB,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdpB,OAAA;gBAAAgB,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdpB,OAAA;gBAAAgB,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfpB,OAAA;gBAAAgB,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfpB,OAAA;gBAAAgB,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpB,OAAA;YAAAgB,QAAA,EACGb,SAAS,CAACkB,GAAG,CAACC,QAAQ,iBACrBtB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAAgB,QAAA,EAAKM,QAAQ,CAACb;cAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxBpB,OAAA;gBAAAgB,QAAA,EAAKM,QAAQ,CAACZ;cAAO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3BpB,OAAA;gBAAAgB,QAAA,EAAKM,QAAQ,CAACX;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzBpB,OAAA;gBAAAgB,QAAA,EAAKM,QAAQ,CAACV;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzBpB,OAAA;gBAAAgB,QAAA,GAAI,SAAE,EAACM,QAAQ,CAACT,MAAM;cAAA;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BpB,OAAA;gBAAAgB,QAAA,eACEhB,OAAA;kBAAMe,SAAS,EAAC,cAAc;kBAACQ,KAAK,EAAE;oBAAEC,eAAe,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EAClEM,QAAQ,CAACR;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLpB,OAAA;gBAAAgB,QAAA,eACEhB,OAAA;kBAAQe,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA,GAbEE,QAAQ,CAACd,EAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAchB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CArFID,kBAAkB;AAAAwB,EAAA,GAAlBxB,kBAAkB;AAuFxB,eAAeA,kBAAkB;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}