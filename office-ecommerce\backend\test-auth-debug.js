const axios = require('axios');

async function testAuth() {
  try {
    console.log('🔐 Testing authentication...');
    
    const response = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('Error status:', error.response?.status);
    console.error('Error data:', error.response?.data);
    console.error('Error message:', error.message);
  }
}

testAuth();
