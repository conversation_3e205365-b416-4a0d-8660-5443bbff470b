{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\modals\\\\ConfirmationModal.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmationModal = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title = \"Confirm Action\",\n  message = \"Are you sure you want to proceed?\",\n  confirmText = \"Confirm\",\n  cancelText = \"Cancel\",\n  type = \"warning\" // warning, danger, info\n}) => {\n  if (!isOpen) return null;\n  const handleConfirm = () => {\n    onConfirm();\n    onClose();\n  };\n  const getIconByType = () => {\n    switch (type) {\n      case 'danger':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"48\",\n          height: \"48\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            fill: \"#fee2e2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M15 9L9 15M9 9L15 15\",\n            stroke: \"#dc2626\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"48\",\n          height: \"48\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            fill: \"#dbeafe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 16V12M12 8H12.01\",\n            stroke: \"#2563eb\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this);\n      default:\n        // warning\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"48\",\n          height: \"48\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            fill: \"#fef3c7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M12 9V13M12 17H12.01\",\n            stroke: \"#d97706\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"confirmation-modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"confirmation-modal\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"confirmation-modal-close\",\n        onClick: onClose,\n        \"aria-label\": \"Close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"confirmation-modal-icon\",\n        children: getIconByType()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"confirmation-modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"confirmation-modal-title\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"confirmation-modal-message\",\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"confirmation-modal-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"confirmation-btn confirmation-btn-cancel\",\n          onClick: onClose,\n          children: cancelText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `confirmation-btn confirmation-btn-confirm confirmation-btn-${type}`,\n          onClick: handleConfirm,\n          children: confirmText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 9\n  }, this);\n};\n_c = ConfirmationModal;\nexport default ConfirmationModal;\nvar _c;\n$RefreshReg$(_c, \"ConfirmationModal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ConfirmationModal", "isOpen", "onClose", "onConfirm", "title", "message", "confirmText", "cancelText", "type", "handleConfirm", "getIconByType", "width", "height", "viewBox", "fill", "xmlns", "children", "cx", "cy", "r", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "className", "onClick", "e", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/modals/ConfirmationModal.js"], "sourcesContent": ["import React from 'react';\n\nconst ConfirmationModal = ({ \n    isOpen, \n    onClose, \n    onConfirm, \n    title = \"Confirm Action\",\n    message = \"Are you sure you want to proceed?\",\n    confirmText = \"Confirm\",\n    cancelText = \"Cancel\",\n    type = \"warning\" // warning, danger, info\n}) => {\n    if (!isOpen) return null;\n\n    const handleConfirm = () => {\n        onConfirm();\n        onClose();\n    };\n\n    const getIconByType = () => {\n        switch (type) {\n            case 'danger':\n                return (\n                    <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#fee2e2\"/>\n                        <path d=\"M15 9L9 15M9 9L15 15\" stroke=\"#dc2626\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                );\n            case 'info':\n                return (\n                    <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#dbeafe\"/>\n                        <path d=\"M12 16V12M12 8H12.01\" stroke=\"#2563eb\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                );\n            default: // warning\n                return (\n                    <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#fef3c7\"/>\n                        <path d=\"M12 9V13M12 17H12.01\" stroke=\"#d97706\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                );\n        }\n    };\n\n    return (\n        <div className=\"confirmation-modal-overlay\" onClick={onClose}>\n            <div className=\"confirmation-modal\" onClick={(e) => e.stopPropagation()}>\n                {/* Close Button */}\n                <button \n                    className=\"confirmation-modal-close\" \n                    onClick={onClose}\n                    aria-label=\"Close\"\n                >\n                    ×\n                </button>\n\n                {/* Icon */}\n                <div className=\"confirmation-modal-icon\">\n                    {getIconByType()}\n                </div>\n\n                {/* Content */}\n                <div className=\"confirmation-modal-content\">\n                    <h3 className=\"confirmation-modal-title\">{title}</h3>\n                    <p className=\"confirmation-modal-message\">{message}</p>\n                </div>\n\n                {/* Actions */}\n                <div className=\"confirmation-modal-actions\">\n                    <button \n                        className=\"confirmation-btn confirmation-btn-cancel\"\n                        onClick={onClose}\n                    >\n                        {cancelText}\n                    </button>\n                    <button \n                        className={`confirmation-btn confirmation-btn-confirm confirmation-btn-${type}`}\n                        onClick={handleConfirm}\n                    >\n                        {confirmText}\n                    </button>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default ConfirmationModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAAiB,GAAGA,CAAC;EACvBC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC,KAAK,GAAG,gBAAgB;EACxBC,OAAO,GAAG,mCAAmC;EAC7CC,WAAW,GAAG,SAAS;EACvBC,UAAU,GAAG,QAAQ;EACrBC,IAAI,GAAG,SAAS,CAAC;AACrB,CAAC,KAAK;EACF,IAAI,CAACP,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IACxBN,SAAS,CAAC,CAAC;IACXD,OAAO,CAAC,CAAC;EACb,CAAC;EAED,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IACxB,QAAQF,IAAI;MACR,KAAK,QAAQ;QACT,oBACIT,OAAA;UAAKY,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAAAC,QAAA,gBAC1FjB,OAAA;YAAQkB,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACL,IAAI,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC/CxB,OAAA;YAAMyB,CAAC,EAAC,sBAAsB;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7G,CAAC;MAEd,KAAK,MAAM;QACP,oBACIxB,OAAA;UAAKY,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAAAC,QAAA,gBAC1FjB,OAAA;YAAQkB,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACL,IAAI,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC/CxB,OAAA;YAAMyB,CAAC,EAAC,sBAAsB;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7G,CAAC;MAEd;QAAS;QACL,oBACIxB,OAAA;UAAKY,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,4BAA4B;UAAAC,QAAA,gBAC1FjB,OAAA;YAAQkB,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACL,IAAI,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eAC/CxB,OAAA;YAAMyB,CAAC,EAAC,sBAAsB;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7G,CAAC;IAElB;EACJ,CAAC;EAED,oBACIxB,OAAA;IAAK8B,SAAS,EAAC,4BAA4B;IAACC,OAAO,EAAE5B,OAAQ;IAAAc,QAAA,eACzDjB,OAAA;MAAK8B,SAAS,EAAC,oBAAoB;MAACC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAhB,QAAA,gBAEpEjB,OAAA;QACI8B,SAAS,EAAC,0BAA0B;QACpCC,OAAO,EAAE5B,OAAQ;QACjB,cAAW,OAAO;QAAAc,QAAA,EACrB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGTxB,OAAA;QAAK8B,SAAS,EAAC,yBAAyB;QAAAb,QAAA,EACnCN,aAAa,CAAC;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGNxB,OAAA;QAAK8B,SAAS,EAAC,4BAA4B;QAAAb,QAAA,gBACvCjB,OAAA;UAAI8B,SAAS,EAAC,0BAA0B;UAAAb,QAAA,EAAEZ;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrDxB,OAAA;UAAG8B,SAAS,EAAC,4BAA4B;UAAAb,QAAA,EAAEX;QAAO;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAGNxB,OAAA;QAAK8B,SAAS,EAAC,4BAA4B;QAAAb,QAAA,gBACvCjB,OAAA;UACI8B,SAAS,EAAC,0CAA0C;UACpDC,OAAO,EAAE5B,OAAQ;UAAAc,QAAA,EAEhBT;QAAU;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACTxB,OAAA;UACI8B,SAAS,EAAE,8DAA8DrB,IAAI,EAAG;UAChFsB,OAAO,EAAErB,aAAc;UAAAO,QAAA,EAEtBV;QAAW;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACU,EAAA,GApFIjC,iBAAiB;AAsFvB,eAAeA,iBAAiB;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}