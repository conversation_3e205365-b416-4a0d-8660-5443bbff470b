{"ast": null, "code": "import { Vector3, <PERSON>3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>3, <PERSON><PERSON>, <PERSON> } from \"three\";\nconst a = {\n  c: null,\n  // center\n  u: [/* @__PURE__ */new Vector3(), /* @__PURE__ */new Vector3(), /* @__PURE__ */new Vector3()],\n  // basis vectors\n  e: []\n  // half width\n};\nconst b = {\n  c: null,\n  // center\n  u: [/* @__PURE__ */new Vector3(), /* @__PURE__ */new Vector3(), /* @__PURE__ */new Vector3()],\n  // basis vectors\n  e: []\n  // half width\n};\nconst R = [[], [], []];\nconst AbsR = [[], [], []];\nconst t = [];\nconst xAxis = /* @__PURE__ */new Vector3();\nconst yAxis = /* @__PURE__ */new Vector3();\nconst zAxis = /* @__PURE__ */new Vector3();\nconst v1 = /* @__PURE__ */new Vector3();\nconst size = /* @__PURE__ */new Vector3();\nconst closestPoint = /* @__PURE__ */new Vector3();\nconst rotationMatrix = /* @__PURE__ */new Matrix3();\nconst aabb = /* @__PURE__ */new Box3();\nconst matrix = /* @__PURE__ */new Matrix4();\nconst inverse = /* @__PURE__ */new Matrix4();\nconst localRay = /* @__PURE__ */new Ray();\nclass OBB {\n  constructor(center = new Vector3(), halfSize = new Vector3(), rotation = new Matrix3()) {\n    this.center = center;\n    this.halfSize = halfSize;\n    this.rotation = rotation;\n  }\n  set(center, halfSize, rotation) {\n    this.center = center;\n    this.halfSize = halfSize;\n    this.rotation = rotation;\n    return this;\n  }\n  copy(obb2) {\n    this.center.copy(obb2.center);\n    this.halfSize.copy(obb2.halfSize);\n    this.rotation.copy(obb2.rotation);\n    return this;\n  }\n  clone() {\n    return new this.constructor().copy(this);\n  }\n  getSize(result) {\n    return result.copy(this.halfSize).multiplyScalar(2);\n  }\n  /**\n   * Reference: Closest Point on OBB to Point in Real-Time Collision Detection\n   * by Christer Ericson (chapter 5.1.4)\n   */\n  clampPoint(point, result) {\n    const halfSize = this.halfSize;\n    v1.subVectors(point, this.center);\n    this.rotation.extractBasis(xAxis, yAxis, zAxis);\n    result.copy(this.center);\n    const x = MathUtils.clamp(v1.dot(xAxis), -halfSize.x, halfSize.x);\n    result.add(xAxis.multiplyScalar(x));\n    const y = MathUtils.clamp(v1.dot(yAxis), -halfSize.y, halfSize.y);\n    result.add(yAxis.multiplyScalar(y));\n    const z = MathUtils.clamp(v1.dot(zAxis), -halfSize.z, halfSize.z);\n    result.add(zAxis.multiplyScalar(z));\n    return result;\n  }\n  containsPoint(point) {\n    v1.subVectors(point, this.center);\n    this.rotation.extractBasis(xAxis, yAxis, zAxis);\n    return Math.abs(v1.dot(xAxis)) <= this.halfSize.x && Math.abs(v1.dot(yAxis)) <= this.halfSize.y && Math.abs(v1.dot(zAxis)) <= this.halfSize.z;\n  }\n  intersectsBox3(box3) {\n    return this.intersectsOBB(obb.fromBox3(box3));\n  }\n  intersectsSphere(sphere) {\n    this.clampPoint(sphere.center, closestPoint);\n    return closestPoint.distanceToSquared(sphere.center) <= sphere.radius * sphere.radius;\n  }\n  /**\n   * Reference: OBB-OBB Intersection in Real-Time Collision Detection\n   * by Christer Ericson (chapter 4.4.1)\n   *\n   */\n  intersectsOBB(obb2, epsilon = Number.EPSILON) {\n    a.c = this.center;\n    a.e[0] = this.halfSize.x;\n    a.e[1] = this.halfSize.y;\n    a.e[2] = this.halfSize.z;\n    this.rotation.extractBasis(a.u[0], a.u[1], a.u[2]);\n    b.c = obb2.center;\n    b.e[0] = obb2.halfSize.x;\n    b.e[1] = obb2.halfSize.y;\n    b.e[2] = obb2.halfSize.z;\n    obb2.rotation.extractBasis(b.u[0], b.u[1], b.u[2]);\n    for (let i = 0; i < 3; i++) {\n      for (let j = 0; j < 3; j++) {\n        R[i][j] = a.u[i].dot(b.u[j]);\n      }\n    }\n    v1.subVectors(b.c, a.c);\n    t[0] = v1.dot(a.u[0]);\n    t[1] = v1.dot(a.u[1]);\n    t[2] = v1.dot(a.u[2]);\n    for (let i = 0; i < 3; i++) {\n      for (let j = 0; j < 3; j++) {\n        AbsR[i][j] = Math.abs(R[i][j]) + epsilon;\n      }\n    }\n    let ra, rb;\n    for (let i = 0; i < 3; i++) {\n      ra = a.e[i];\n      rb = b.e[0] * AbsR[i][0] + b.e[1] * AbsR[i][1] + b.e[2] * AbsR[i][2];\n      if (Math.abs(t[i]) > ra + rb) return false;\n    }\n    for (let i = 0; i < 3; i++) {\n      ra = a.e[0] * AbsR[0][i] + a.e[1] * AbsR[1][i] + a.e[2] * AbsR[2][i];\n      rb = b.e[i];\n      if (Math.abs(t[0] * R[0][i] + t[1] * R[1][i] + t[2] * R[2][i]) > ra + rb) return false;\n    }\n    ra = a.e[1] * AbsR[2][0] + a.e[2] * AbsR[1][0];\n    rb = b.e[1] * AbsR[0][2] + b.e[2] * AbsR[0][1];\n    if (Math.abs(t[2] * R[1][0] - t[1] * R[2][0]) > ra + rb) return false;\n    ra = a.e[1] * AbsR[2][1] + a.e[2] * AbsR[1][1];\n    rb = b.e[0] * AbsR[0][2] + b.e[2] * AbsR[0][0];\n    if (Math.abs(t[2] * R[1][1] - t[1] * R[2][1]) > ra + rb) return false;\n    ra = a.e[1] * AbsR[2][2] + a.e[2] * AbsR[1][2];\n    rb = b.e[0] * AbsR[0][1] + b.e[1] * AbsR[0][0];\n    if (Math.abs(t[2] * R[1][2] - t[1] * R[2][2]) > ra + rb) return false;\n    ra = a.e[0] * AbsR[2][0] + a.e[2] * AbsR[0][0];\n    rb = b.e[1] * AbsR[1][2] + b.e[2] * AbsR[1][1];\n    if (Math.abs(t[0] * R[2][0] - t[2] * R[0][0]) > ra + rb) return false;\n    ra = a.e[0] * AbsR[2][1] + a.e[2] * AbsR[0][1];\n    rb = b.e[0] * AbsR[1][2] + b.e[2] * AbsR[1][0];\n    if (Math.abs(t[0] * R[2][1] - t[2] * R[0][1]) > ra + rb) return false;\n    ra = a.e[0] * AbsR[2][2] + a.e[2] * AbsR[0][2];\n    rb = b.e[0] * AbsR[1][1] + b.e[1] * AbsR[1][0];\n    if (Math.abs(t[0] * R[2][2] - t[2] * R[0][2]) > ra + rb) return false;\n    ra = a.e[0] * AbsR[1][0] + a.e[1] * AbsR[0][0];\n    rb = b.e[1] * AbsR[2][2] + b.e[2] * AbsR[2][1];\n    if (Math.abs(t[1] * R[0][0] - t[0] * R[1][0]) > ra + rb) return false;\n    ra = a.e[0] * AbsR[1][1] + a.e[1] * AbsR[0][1];\n    rb = b.e[0] * AbsR[2][2] + b.e[2] * AbsR[2][0];\n    if (Math.abs(t[1] * R[0][1] - t[0] * R[1][1]) > ra + rb) return false;\n    ra = a.e[0] * AbsR[1][2] + a.e[1] * AbsR[0][2];\n    rb = b.e[0] * AbsR[2][1] + b.e[1] * AbsR[2][0];\n    if (Math.abs(t[1] * R[0][2] - t[0] * R[1][2]) > ra + rb) return false;\n    return true;\n  }\n  /**\n   * Reference: Testing Box Against Plane in Real-Time Collision Detection\n   * by Christer Ericson (chapter 5.2.3)\n   */\n  intersectsPlane(plane) {\n    this.rotation.extractBasis(xAxis, yAxis, zAxis);\n    const r = this.halfSize.x * Math.abs(plane.normal.dot(xAxis)) + this.halfSize.y * Math.abs(plane.normal.dot(yAxis)) + this.halfSize.z * Math.abs(plane.normal.dot(zAxis));\n    const d = plane.normal.dot(this.center) - plane.constant;\n    return Math.abs(d) <= r;\n  }\n  /**\n   * Performs a ray/OBB intersection test and stores the intersection point\n   * to the given 3D vector. If no intersection is detected, *null* is returned.\n   */\n  intersectRay(ray, result) {\n    this.getSize(size);\n    aabb.setFromCenterAndSize(v1.set(0, 0, 0), size);\n    matrix.setFromMatrix3(this.rotation);\n    matrix.setPosition(this.center);\n    inverse.copy(matrix).invert();\n    localRay.copy(ray).applyMatrix4(inverse);\n    if (localRay.intersectBox(aabb, result)) {\n      return result.applyMatrix4(matrix);\n    } else {\n      return null;\n    }\n  }\n  /**\n   * Performs a ray/OBB intersection test. Returns either true or false if\n   * there is a intersection or not.\n   */\n  intersectsRay(ray) {\n    return this.intersectRay(ray, v1) !== null;\n  }\n  fromBox3(box3) {\n    box3.getCenter(this.center);\n    box3.getSize(this.halfSize).multiplyScalar(0.5);\n    this.rotation.identity();\n    return this;\n  }\n  equals(obb2) {\n    return obb2.center.equals(this.center) && obb2.halfSize.equals(this.halfSize) && obb2.rotation.equals(this.rotation);\n  }\n  applyMatrix4(matrix2) {\n    const e = matrix2.elements;\n    let sx = v1.set(e[0], e[1], e[2]).length();\n    const sy = v1.set(e[4], e[5], e[6]).length();\n    const sz = v1.set(e[8], e[9], e[10]).length();\n    const det = matrix2.determinant();\n    if (det < 0) sx = -sx;\n    rotationMatrix.setFromMatrix4(matrix2);\n    const invSX = 1 / sx;\n    const invSY = 1 / sy;\n    const invSZ = 1 / sz;\n    rotationMatrix.elements[0] *= invSX;\n    rotationMatrix.elements[1] *= invSX;\n    rotationMatrix.elements[2] *= invSX;\n    rotationMatrix.elements[3] *= invSY;\n    rotationMatrix.elements[4] *= invSY;\n    rotationMatrix.elements[5] *= invSY;\n    rotationMatrix.elements[6] *= invSZ;\n    rotationMatrix.elements[7] *= invSZ;\n    rotationMatrix.elements[8] *= invSZ;\n    this.rotation.multiply(rotationMatrix);\n    this.halfSize.x *= sx;\n    this.halfSize.y *= sy;\n    this.halfSize.z *= sz;\n    v1.setFromMatrixPosition(matrix2);\n    this.center.add(v1);\n    return this;\n  }\n}\nconst obb = /* @__PURE__ */new OBB();\nexport { OBB };", "map": {"version": 3, "names": ["a", "c", "u", "Vector3", "e", "b", "R", "AbsR", "t", "xAxis", "yAxis", "zAxis", "v1", "size", "closestPoint", "rotationMatrix", "Matrix3", "aabb", "Box3", "matrix", "Matrix4", "inverse", "localRay", "<PERSON>", "OBB", "constructor", "center", "halfSize", "rotation", "set", "copy", "obb2", "clone", "getSize", "result", "multiplyScalar", "clampPoint", "point", "subVectors", "extractBasis", "x", "MathUtils", "clamp", "dot", "add", "y", "z", "containsPoint", "Math", "abs", "intersectsBox3", "box3", "intersectsOBB", "obb", "fromBox3", "intersectsSphere", "sphere", "distanceToSquared", "radius", "epsilon", "Number", "EPSILON", "i", "j", "ra", "rb", "intersects<PERSON><PERSON>", "plane", "r", "normal", "d", "constant", "intersectRay", "ray", "setFromCenterAndSize", "setFromMatrix3", "setPosition", "invert", "applyMatrix4", "intersectBox", "intersectsRay", "getCenter", "identity", "equals", "matrix2", "elements", "sx", "length", "sy", "sz", "det", "determinant", "setFromMatrix4", "invSX", "invSY", "invSZ", "multiply", "setFromMatrixPosition"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\math\\OBB.js"], "sourcesContent": ["import { Box3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>4, <PERSON>3, <PERSON>, Vector3 } from 'three'\n\n// module scope helper variables\n\nconst a = {\n  c: null, // center\n  u: [/* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3()], // basis vectors\n  e: [], // half width\n}\n\nconst b = {\n  c: null, // center\n  u: [/* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3(), /* @__PURE__ */ new Vector3()], // basis vectors\n  e: [], // half width\n}\n\nconst R = [[], [], []]\nconst AbsR = [[], [], []]\nconst t = []\n\nconst xAxis = /* @__PURE__ */ new Vector3()\nconst yAxis = /* @__PURE__ */ new Vector3()\nconst zAxis = /* @__PURE__ */ new Vector3()\nconst v1 = /* @__PURE__ */ new Vector3()\nconst size = /* @__PURE__ */ new Vector3()\nconst closestPoint = /* @__PURE__ */ new Vector3()\nconst rotationMatrix = /* @__PURE__ */ new Matrix3()\nconst aabb = /* @__PURE__ */ new Box3()\nconst matrix = /* @__PURE__ */ new Matrix4()\nconst inverse = /* @__PURE__ */ new Matrix4()\nconst localRay = /* @__PURE__ */ new Ray()\n\n// OBB\n\nclass OBB {\n  constructor(center = new Vector3(), halfSize = new Vector3(), rotation = new Matrix3()) {\n    this.center = center\n    this.halfSize = halfSize\n    this.rotation = rotation\n  }\n\n  set(center, halfSize, rotation) {\n    this.center = center\n    this.halfSize = halfSize\n    this.rotation = rotation\n\n    return this\n  }\n\n  copy(obb) {\n    this.center.copy(obb.center)\n    this.halfSize.copy(obb.halfSize)\n    this.rotation.copy(obb.rotation)\n\n    return this\n  }\n\n  clone() {\n    return new this.constructor().copy(this)\n  }\n\n  getSize(result) {\n    return result.copy(this.halfSize).multiplyScalar(2)\n  }\n\n  /**\n   * Reference: Closest Point on OBB to Point in Real-Time Collision Detection\n   * by Christer Ericson (chapter 5.1.4)\n   */\n  clampPoint(point, result) {\n    const halfSize = this.halfSize\n\n    v1.subVectors(point, this.center)\n    this.rotation.extractBasis(xAxis, yAxis, zAxis)\n\n    // start at the center position of the OBB\n\n    result.copy(this.center)\n\n    // project the target onto the OBB axes and walk towards that point\n\n    const x = MathUtils.clamp(v1.dot(xAxis), -halfSize.x, halfSize.x)\n    result.add(xAxis.multiplyScalar(x))\n\n    const y = MathUtils.clamp(v1.dot(yAxis), -halfSize.y, halfSize.y)\n    result.add(yAxis.multiplyScalar(y))\n\n    const z = MathUtils.clamp(v1.dot(zAxis), -halfSize.z, halfSize.z)\n    result.add(zAxis.multiplyScalar(z))\n\n    return result\n  }\n\n  containsPoint(point) {\n    v1.subVectors(point, this.center)\n    this.rotation.extractBasis(xAxis, yAxis, zAxis)\n\n    // project v1 onto each axis and check if these points lie inside the OBB\n\n    return (\n      Math.abs(v1.dot(xAxis)) <= this.halfSize.x &&\n      Math.abs(v1.dot(yAxis)) <= this.halfSize.y &&\n      Math.abs(v1.dot(zAxis)) <= this.halfSize.z\n    )\n  }\n\n  intersectsBox3(box3) {\n    return this.intersectsOBB(obb.fromBox3(box3))\n  }\n\n  intersectsSphere(sphere) {\n    // find the point on the OBB closest to the sphere center\n\n    this.clampPoint(sphere.center, closestPoint)\n\n    // if that point is inside the sphere, the OBB and sphere intersect\n\n    return closestPoint.distanceToSquared(sphere.center) <= sphere.radius * sphere.radius\n  }\n\n  /**\n   * Reference: OBB-OBB Intersection in Real-Time Collision Detection\n   * by Christer Ericson (chapter 4.4.1)\n   *\n   */\n  intersectsOBB(obb, epsilon = Number.EPSILON) {\n    // prepare data structures (the code uses the same nomenclature like the reference)\n\n    a.c = this.center\n    a.e[0] = this.halfSize.x\n    a.e[1] = this.halfSize.y\n    a.e[2] = this.halfSize.z\n    this.rotation.extractBasis(a.u[0], a.u[1], a.u[2])\n\n    b.c = obb.center\n    b.e[0] = obb.halfSize.x\n    b.e[1] = obb.halfSize.y\n    b.e[2] = obb.halfSize.z\n    obb.rotation.extractBasis(b.u[0], b.u[1], b.u[2])\n\n    // compute rotation matrix expressing b in a's coordinate frame\n\n    for (let i = 0; i < 3; i++) {\n      for (let j = 0; j < 3; j++) {\n        R[i][j] = a.u[i].dot(b.u[j])\n      }\n    }\n\n    // compute translation vector\n\n    v1.subVectors(b.c, a.c)\n\n    // bring translation into a's coordinate frame\n\n    t[0] = v1.dot(a.u[0])\n    t[1] = v1.dot(a.u[1])\n    t[2] = v1.dot(a.u[2])\n\n    // compute common subexpressions. Add in an epsilon term to\n    // counteract arithmetic errors when two edges are parallel and\n    // their cross product is (near) null\n\n    for (let i = 0; i < 3; i++) {\n      for (let j = 0; j < 3; j++) {\n        AbsR[i][j] = Math.abs(R[i][j]) + epsilon\n      }\n    }\n\n    let ra, rb\n\n    // test axes L = A0, L = A1, L = A2\n\n    for (let i = 0; i < 3; i++) {\n      ra = a.e[i]\n      rb = b.e[0] * AbsR[i][0] + b.e[1] * AbsR[i][1] + b.e[2] * AbsR[i][2]\n      if (Math.abs(t[i]) > ra + rb) return false\n    }\n\n    // test axes L = B0, L = B1, L = B2\n\n    for (let i = 0; i < 3; i++) {\n      ra = a.e[0] * AbsR[0][i] + a.e[1] * AbsR[1][i] + a.e[2] * AbsR[2][i]\n      rb = b.e[i]\n      if (Math.abs(t[0] * R[0][i] + t[1] * R[1][i] + t[2] * R[2][i]) > ra + rb) return false\n    }\n\n    // test axis L = A0 x B0\n\n    ra = a.e[1] * AbsR[2][0] + a.e[2] * AbsR[1][0]\n    rb = b.e[1] * AbsR[0][2] + b.e[2] * AbsR[0][1]\n    if (Math.abs(t[2] * R[1][0] - t[1] * R[2][0]) > ra + rb) return false\n\n    // test axis L = A0 x B1\n\n    ra = a.e[1] * AbsR[2][1] + a.e[2] * AbsR[1][1]\n    rb = b.e[0] * AbsR[0][2] + b.e[2] * AbsR[0][0]\n    if (Math.abs(t[2] * R[1][1] - t[1] * R[2][1]) > ra + rb) return false\n\n    // test axis L = A0 x B2\n\n    ra = a.e[1] * AbsR[2][2] + a.e[2] * AbsR[1][2]\n    rb = b.e[0] * AbsR[0][1] + b.e[1] * AbsR[0][0]\n    if (Math.abs(t[2] * R[1][2] - t[1] * R[2][2]) > ra + rb) return false\n\n    // test axis L = A1 x B0\n\n    ra = a.e[0] * AbsR[2][0] + a.e[2] * AbsR[0][0]\n    rb = b.e[1] * AbsR[1][2] + b.e[2] * AbsR[1][1]\n    if (Math.abs(t[0] * R[2][0] - t[2] * R[0][0]) > ra + rb) return false\n\n    // test axis L = A1 x B1\n\n    ra = a.e[0] * AbsR[2][1] + a.e[2] * AbsR[0][1]\n    rb = b.e[0] * AbsR[1][2] + b.e[2] * AbsR[1][0]\n    if (Math.abs(t[0] * R[2][1] - t[2] * R[0][1]) > ra + rb) return false\n\n    // test axis L = A1 x B2\n\n    ra = a.e[0] * AbsR[2][2] + a.e[2] * AbsR[0][2]\n    rb = b.e[0] * AbsR[1][1] + b.e[1] * AbsR[1][0]\n    if (Math.abs(t[0] * R[2][2] - t[2] * R[0][2]) > ra + rb) return false\n\n    // test axis L = A2 x B0\n\n    ra = a.e[0] * AbsR[1][0] + a.e[1] * AbsR[0][0]\n    rb = b.e[1] * AbsR[2][2] + b.e[2] * AbsR[2][1]\n    if (Math.abs(t[1] * R[0][0] - t[0] * R[1][0]) > ra + rb) return false\n\n    // test axis L = A2 x B1\n\n    ra = a.e[0] * AbsR[1][1] + a.e[1] * AbsR[0][1]\n    rb = b.e[0] * AbsR[2][2] + b.e[2] * AbsR[2][0]\n    if (Math.abs(t[1] * R[0][1] - t[0] * R[1][1]) > ra + rb) return false\n\n    // test axis L = A2 x B2\n\n    ra = a.e[0] * AbsR[1][2] + a.e[1] * AbsR[0][2]\n    rb = b.e[0] * AbsR[2][1] + b.e[1] * AbsR[2][0]\n    if (Math.abs(t[1] * R[0][2] - t[0] * R[1][2]) > ra + rb) return false\n\n    // since no separating axis is found, the OBBs must be intersecting\n\n    return true\n  }\n\n  /**\n   * Reference: Testing Box Against Plane in Real-Time Collision Detection\n   * by Christer Ericson (chapter 5.2.3)\n   */\n  intersectsPlane(plane) {\n    this.rotation.extractBasis(xAxis, yAxis, zAxis)\n\n    // compute the projection interval radius of this OBB onto L(t) = this->center + t * p.normal;\n\n    const r =\n      this.halfSize.x * Math.abs(plane.normal.dot(xAxis)) +\n      this.halfSize.y * Math.abs(plane.normal.dot(yAxis)) +\n      this.halfSize.z * Math.abs(plane.normal.dot(zAxis))\n\n    // compute distance of the OBB's center from the plane\n\n    const d = plane.normal.dot(this.center) - plane.constant\n\n    // Intersection occurs when distance d falls within [-r,+r] interval\n\n    return Math.abs(d) <= r\n  }\n\n  /**\n   * Performs a ray/OBB intersection test and stores the intersection point\n   * to the given 3D vector. If no intersection is detected, *null* is returned.\n   */\n  intersectRay(ray, result) {\n    // the idea is to perform the intersection test in the local space\n    // of the OBB.\n\n    this.getSize(size)\n    aabb.setFromCenterAndSize(v1.set(0, 0, 0), size)\n\n    // create a 4x4 transformation matrix\n\n    matrix.setFromMatrix3(this.rotation)\n    matrix.setPosition(this.center)\n\n    // transform ray to the local space of the OBB\n\n    inverse.copy(matrix).invert()\n    localRay.copy(ray).applyMatrix4(inverse)\n\n    // perform ray <-> AABB intersection test\n\n    if (localRay.intersectBox(aabb, result)) {\n      // transform the intersection point back to world space\n\n      return result.applyMatrix4(matrix)\n    } else {\n      return null\n    }\n  }\n\n  /**\n   * Performs a ray/OBB intersection test. Returns either true or false if\n   * there is a intersection or not.\n   */\n  intersectsRay(ray) {\n    return this.intersectRay(ray, v1) !== null\n  }\n\n  fromBox3(box3) {\n    box3.getCenter(this.center)\n\n    box3.getSize(this.halfSize).multiplyScalar(0.5)\n\n    this.rotation.identity()\n\n    return this\n  }\n\n  equals(obb) {\n    return obb.center.equals(this.center) && obb.halfSize.equals(this.halfSize) && obb.rotation.equals(this.rotation)\n  }\n\n  applyMatrix4(matrix) {\n    const e = matrix.elements\n\n    let sx = v1.set(e[0], e[1], e[2]).length()\n    const sy = v1.set(e[4], e[5], e[6]).length()\n    const sz = v1.set(e[8], e[9], e[10]).length()\n\n    const det = matrix.determinant()\n    if (det < 0) sx = -sx\n\n    rotationMatrix.setFromMatrix4(matrix)\n\n    const invSX = 1 / sx\n    const invSY = 1 / sy\n    const invSZ = 1 / sz\n\n    rotationMatrix.elements[0] *= invSX\n    rotationMatrix.elements[1] *= invSX\n    rotationMatrix.elements[2] *= invSX\n\n    rotationMatrix.elements[3] *= invSY\n    rotationMatrix.elements[4] *= invSY\n    rotationMatrix.elements[5] *= invSY\n\n    rotationMatrix.elements[6] *= invSZ\n    rotationMatrix.elements[7] *= invSZ\n    rotationMatrix.elements[8] *= invSZ\n\n    this.rotation.multiply(rotationMatrix)\n\n    this.halfSize.x *= sx\n    this.halfSize.y *= sy\n    this.halfSize.z *= sz\n\n    v1.setFromMatrixPosition(matrix)\n    this.center.add(v1)\n\n    return this\n  }\n}\n\nconst obb = /* @__PURE__ */ new OBB()\n\nexport { OBB }\n"], "mappings": ";AAIA,MAAMA,CAAA,GAAI;EACRC,CAAA,EAAG;EAAA;EACHC,CAAA,EAAG,CAAiB,mBAAIC,OAAA,CAAS,GAAkB,mBAAIA,OAAA,CAAS,GAAkB,mBAAIA,OAAA,EAAS;EAAA;EAC/FC,CAAA,EAAG;EAAE;AACP;AAEA,MAAMC,CAAA,GAAI;EACRJ,CAAA,EAAG;EAAA;EACHC,CAAA,EAAG,CAAiB,mBAAIC,OAAA,CAAS,GAAkB,mBAAIA,OAAA,CAAS,GAAkB,mBAAIA,OAAA,EAAS;EAAA;EAC/FC,CAAA,EAAG;EAAE;AACP;AAEA,MAAME,CAAA,GAAI,CAAC,IAAI,EAAE,EAAE,EAAE;AACrB,MAAMC,IAAA,GAAO,CAAC,IAAI,EAAE,EAAE,EAAE;AACxB,MAAMC,CAAA,GAAI,EAAE;AAEZ,MAAMC,KAAA,GAAwB,mBAAIN,OAAA,CAAS;AAC3C,MAAMO,KAAA,GAAwB,mBAAIP,OAAA,CAAS;AAC3C,MAAMQ,KAAA,GAAwB,mBAAIR,OAAA,CAAS;AAC3C,MAAMS,EAAA,GAAqB,mBAAIT,OAAA,CAAS;AACxC,MAAMU,IAAA,GAAuB,mBAAIV,OAAA,CAAS;AAC1C,MAAMW,YAAA,GAA+B,mBAAIX,OAAA,CAAS;AAClD,MAAMY,cAAA,GAAiC,mBAAIC,OAAA,CAAS;AACpD,MAAMC,IAAA,GAAuB,mBAAIC,IAAA,CAAM;AACvC,MAAMC,MAAA,GAAyB,mBAAIC,OAAA,CAAS;AAC5C,MAAMC,OAAA,GAA0B,mBAAID,OAAA,CAAS;AAC7C,MAAME,QAAA,GAA2B,mBAAIC,GAAA,CAAK;AAI1C,MAAMC,GAAA,CAAI;EACRC,YAAYC,MAAA,GAAS,IAAIvB,OAAA,IAAWwB,QAAA,GAAW,IAAIxB,OAAA,IAAWyB,QAAA,GAAW,IAAIZ,OAAA,IAAW;IACtF,KAAKU,MAAA,GAASA,MAAA;IACd,KAAKC,QAAA,GAAWA,QAAA;IAChB,KAAKC,QAAA,GAAWA,QAAA;EACjB;EAEDC,IAAIH,MAAA,EAAQC,QAAA,EAAUC,QAAA,EAAU;IAC9B,KAAKF,MAAA,GAASA,MAAA;IACd,KAAKC,QAAA,GAAWA,QAAA;IAChB,KAAKC,QAAA,GAAWA,QAAA;IAEhB,OAAO;EACR;EAEDE,KAAKC,IAAA,EAAK;IACR,KAAKL,MAAA,CAAOI,IAAA,CAAKC,IAAA,CAAIL,MAAM;IAC3B,KAAKC,QAAA,CAASG,IAAA,CAAKC,IAAA,CAAIJ,QAAQ;IAC/B,KAAKC,QAAA,CAASE,IAAA,CAAKC,IAAA,CAAIH,QAAQ;IAE/B,OAAO;EACR;EAEDI,MAAA,EAAQ;IACN,OAAO,IAAI,KAAKP,WAAA,GAAcK,IAAA,CAAK,IAAI;EACxC;EAEDG,QAAQC,MAAA,EAAQ;IACd,OAAOA,MAAA,CAAOJ,IAAA,CAAK,KAAKH,QAAQ,EAAEQ,cAAA,CAAe,CAAC;EACnD;EAAA;AAAA;AAAA;AAAA;EAMDC,WAAWC,KAAA,EAAOH,MAAA,EAAQ;IACxB,MAAMP,QAAA,GAAW,KAAKA,QAAA;IAEtBf,EAAA,CAAG0B,UAAA,CAAWD,KAAA,EAAO,KAAKX,MAAM;IAChC,KAAKE,QAAA,CAASW,YAAA,CAAa9B,KAAA,EAAOC,KAAA,EAAOC,KAAK;IAI9CuB,MAAA,CAAOJ,IAAA,CAAK,KAAKJ,MAAM;IAIvB,MAAMc,CAAA,GAAIC,SAAA,CAAUC,KAAA,CAAM9B,EAAA,CAAG+B,GAAA,CAAIlC,KAAK,GAAG,CAACkB,QAAA,CAASa,CAAA,EAAGb,QAAA,CAASa,CAAC;IAChEN,MAAA,CAAOU,GAAA,CAAInC,KAAA,CAAM0B,cAAA,CAAeK,CAAC,CAAC;IAElC,MAAMK,CAAA,GAAIJ,SAAA,CAAUC,KAAA,CAAM9B,EAAA,CAAG+B,GAAA,CAAIjC,KAAK,GAAG,CAACiB,QAAA,CAASkB,CAAA,EAAGlB,QAAA,CAASkB,CAAC;IAChEX,MAAA,CAAOU,GAAA,CAAIlC,KAAA,CAAMyB,cAAA,CAAeU,CAAC,CAAC;IAElC,MAAMC,CAAA,GAAIL,SAAA,CAAUC,KAAA,CAAM9B,EAAA,CAAG+B,GAAA,CAAIhC,KAAK,GAAG,CAACgB,QAAA,CAASmB,CAAA,EAAGnB,QAAA,CAASmB,CAAC;IAChEZ,MAAA,CAAOU,GAAA,CAAIjC,KAAA,CAAMwB,cAAA,CAAeW,CAAC,CAAC;IAElC,OAAOZ,MAAA;EACR;EAEDa,cAAcV,KAAA,EAAO;IACnBzB,EAAA,CAAG0B,UAAA,CAAWD,KAAA,EAAO,KAAKX,MAAM;IAChC,KAAKE,QAAA,CAASW,YAAA,CAAa9B,KAAA,EAAOC,KAAA,EAAOC,KAAK;IAI9C,OACEqC,IAAA,CAAKC,GAAA,CAAIrC,EAAA,CAAG+B,GAAA,CAAIlC,KAAK,CAAC,KAAK,KAAKkB,QAAA,CAASa,CAAA,IACzCQ,IAAA,CAAKC,GAAA,CAAIrC,EAAA,CAAG+B,GAAA,CAAIjC,KAAK,CAAC,KAAK,KAAKiB,QAAA,CAASkB,CAAA,IACzCG,IAAA,CAAKC,GAAA,CAAIrC,EAAA,CAAG+B,GAAA,CAAIhC,KAAK,CAAC,KAAK,KAAKgB,QAAA,CAASmB,CAAA;EAE5C;EAEDI,eAAeC,IAAA,EAAM;IACnB,OAAO,KAAKC,aAAA,CAAcC,GAAA,CAAIC,QAAA,CAASH,IAAI,CAAC;EAC7C;EAEDI,iBAAiBC,MAAA,EAAQ;IAGvB,KAAKpB,UAAA,CAAWoB,MAAA,CAAO9B,MAAA,EAAQZ,YAAY;IAI3C,OAAOA,YAAA,CAAa2C,iBAAA,CAAkBD,MAAA,CAAO9B,MAAM,KAAK8B,MAAA,CAAOE,MAAA,GAASF,MAAA,CAAOE,MAAA;EAChF;EAAA;AAAA;AAAA;AAAA;AAAA;EAODN,cAAcrB,IAAA,EAAK4B,OAAA,GAAUC,MAAA,CAAOC,OAAA,EAAS;IAG3C7D,CAAA,CAAEC,CAAA,GAAI,KAAKyB,MAAA;IACX1B,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAI,KAAKuB,QAAA,CAASa,CAAA;IACvBxC,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAI,KAAKuB,QAAA,CAASkB,CAAA;IACvB7C,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAI,KAAKuB,QAAA,CAASmB,CAAA;IACvB,KAAKlB,QAAA,CAASW,YAAA,CAAavC,CAAA,CAAEE,CAAA,CAAE,CAAC,GAAGF,CAAA,CAAEE,CAAA,CAAE,CAAC,GAAGF,CAAA,CAAEE,CAAA,CAAE,CAAC,CAAC;IAEjDG,CAAA,CAAEJ,CAAA,GAAI8B,IAAA,CAAIL,MAAA;IACVrB,CAAA,CAAED,CAAA,CAAE,CAAC,IAAI2B,IAAA,CAAIJ,QAAA,CAASa,CAAA;IACtBnC,CAAA,CAAED,CAAA,CAAE,CAAC,IAAI2B,IAAA,CAAIJ,QAAA,CAASkB,CAAA;IACtBxC,CAAA,CAAED,CAAA,CAAE,CAAC,IAAI2B,IAAA,CAAIJ,QAAA,CAASmB,CAAA;IACtBf,IAAA,CAAIH,QAAA,CAASW,YAAA,CAAalC,CAAA,CAAEH,CAAA,CAAE,CAAC,GAAGG,CAAA,CAAEH,CAAA,CAAE,CAAC,GAAGG,CAAA,CAAEH,CAAA,CAAE,CAAC,CAAC;IAIhD,SAAS4D,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1BzD,CAAA,CAAEwD,CAAC,EAAEC,CAAC,IAAI/D,CAAA,CAAEE,CAAA,CAAE4D,CAAC,EAAEnB,GAAA,CAAItC,CAAA,CAAEH,CAAA,CAAE6D,CAAC,CAAC;MAC5B;IACF;IAIDnD,EAAA,CAAG0B,UAAA,CAAWjC,CAAA,CAAEJ,CAAA,EAAGD,CAAA,CAAEC,CAAC;IAItBO,CAAA,CAAE,CAAC,IAAII,EAAA,CAAG+B,GAAA,CAAI3C,CAAA,CAAEE,CAAA,CAAE,CAAC,CAAC;IACpBM,CAAA,CAAE,CAAC,IAAII,EAAA,CAAG+B,GAAA,CAAI3C,CAAA,CAAEE,CAAA,CAAE,CAAC,CAAC;IACpBM,CAAA,CAAE,CAAC,IAAII,EAAA,CAAG+B,GAAA,CAAI3C,CAAA,CAAEE,CAAA,CAAE,CAAC,CAAC;IAMpB,SAAS4D,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1BxD,IAAA,CAAKuD,CAAC,EAAEC,CAAC,IAAIf,IAAA,CAAKC,GAAA,CAAI3C,CAAA,CAAEwD,CAAC,EAAEC,CAAC,CAAC,IAAIJ,OAAA;MAClC;IACF;IAED,IAAIK,EAAA,EAAIC,EAAA;IAIR,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1BE,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE0D,CAAC;MACVG,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAKuD,CAAC,EAAE,CAAC,IAAIzD,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAKuD,CAAC,EAAE,CAAC,IAAIzD,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAKuD,CAAC,EAAE,CAAC;MACnE,IAAId,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAEsD,CAAC,CAAC,IAAIE,EAAA,GAAKC,EAAA,EAAI,OAAO;IACtC;IAID,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1BE,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAEuD,CAAC,IAAI9D,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAEuD,CAAC,IAAI9D,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAEuD,CAAC;MACnEG,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE0D,CAAC;MACV,IAAId,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAEwD,CAAC,IAAItD,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAEwD,CAAC,IAAItD,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAEwD,CAAC,CAAC,IAAIE,EAAA,GAAKC,EAAA,EAAI,OAAO;IAClF;IAIDD,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhED,EAAA,GAAKhE,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIP,CAAA,CAAEI,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C0D,EAAA,GAAK5D,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC,IAAIF,CAAA,CAAED,CAAA,CAAE,CAAC,IAAIG,IAAA,CAAK,CAAC,EAAE,CAAC;IAC7C,IAAIyC,IAAA,CAAKC,GAAA,CAAIzC,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,IAAIE,CAAA,CAAE,CAAC,IAAIF,CAAA,CAAE,CAAC,EAAE,CAAC,CAAC,IAAI0D,EAAA,GAAKC,EAAA,EAAI,OAAO;IAIhE,OAAO;EACR;EAAA;AAAA;AAAA;AAAA;EAMDC,gBAAgBC,KAAA,EAAO;IACrB,KAAKvC,QAAA,CAASW,YAAA,CAAa9B,KAAA,EAAOC,KAAA,EAAOC,KAAK;IAI9C,MAAMyD,CAAA,GACJ,KAAKzC,QAAA,CAASa,CAAA,GAAIQ,IAAA,CAAKC,GAAA,CAAIkB,KAAA,CAAME,MAAA,CAAO1B,GAAA,CAAIlC,KAAK,CAAC,IAClD,KAAKkB,QAAA,CAASkB,CAAA,GAAIG,IAAA,CAAKC,GAAA,CAAIkB,KAAA,CAAME,MAAA,CAAO1B,GAAA,CAAIjC,KAAK,CAAC,IAClD,KAAKiB,QAAA,CAASmB,CAAA,GAAIE,IAAA,CAAKC,GAAA,CAAIkB,KAAA,CAAME,MAAA,CAAO1B,GAAA,CAAIhC,KAAK,CAAC;IAIpD,MAAM2D,CAAA,GAAIH,KAAA,CAAME,MAAA,CAAO1B,GAAA,CAAI,KAAKjB,MAAM,IAAIyC,KAAA,CAAMI,QAAA;IAIhD,OAAOvB,IAAA,CAAKC,GAAA,CAAIqB,CAAC,KAAKF,CAAA;EACvB;EAAA;AAAA;AAAA;AAAA;EAMDI,aAAaC,GAAA,EAAKvC,MAAA,EAAQ;IAIxB,KAAKD,OAAA,CAAQpB,IAAI;IACjBI,IAAA,CAAKyD,oBAAA,CAAqB9D,EAAA,CAAGiB,GAAA,CAAI,GAAG,GAAG,CAAC,GAAGhB,IAAI;IAI/CM,MAAA,CAAOwD,cAAA,CAAe,KAAK/C,QAAQ;IACnCT,MAAA,CAAOyD,WAAA,CAAY,KAAKlD,MAAM;IAI9BL,OAAA,CAAQS,IAAA,CAAKX,MAAM,EAAE0D,MAAA,CAAQ;IAC7BvD,QAAA,CAASQ,IAAA,CAAK2C,GAAG,EAAEK,YAAA,CAAazD,OAAO;IAIvC,IAAIC,QAAA,CAASyD,YAAA,CAAa9D,IAAA,EAAMiB,MAAM,GAAG;MAGvC,OAAOA,MAAA,CAAO4C,YAAA,CAAa3D,MAAM;IACvC,OAAW;MACL,OAAO;IACR;EACF;EAAA;AAAA;AAAA;AAAA;EAMD6D,cAAcP,GAAA,EAAK;IACjB,OAAO,KAAKD,YAAA,CAAaC,GAAA,EAAK7D,EAAE,MAAM;EACvC;EAED0C,SAASH,IAAA,EAAM;IACbA,IAAA,CAAK8B,SAAA,CAAU,KAAKvD,MAAM;IAE1ByB,IAAA,CAAKlB,OAAA,CAAQ,KAAKN,QAAQ,EAAEQ,cAAA,CAAe,GAAG;IAE9C,KAAKP,QAAA,CAASsD,QAAA,CAAU;IAExB,OAAO;EACR;EAEDC,OAAOpD,IAAA,EAAK;IACV,OAAOA,IAAA,CAAIL,MAAA,CAAOyD,MAAA,CAAO,KAAKzD,MAAM,KAAKK,IAAA,CAAIJ,QAAA,CAASwD,MAAA,CAAO,KAAKxD,QAAQ,KAAKI,IAAA,CAAIH,QAAA,CAASuD,MAAA,CAAO,KAAKvD,QAAQ;EACjH;EAEDkD,aAAaM,OAAA,EAAQ;IACnB,MAAMhF,CAAA,GAAIgF,OAAA,CAAOC,QAAA;IAEjB,IAAIC,EAAA,GAAK1E,EAAA,CAAGiB,GAAA,CAAIzB,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,CAAC,EAAEmF,MAAA,CAAQ;IAC1C,MAAMC,EAAA,GAAK5E,EAAA,CAAGiB,GAAA,CAAIzB,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,CAAC,EAAEmF,MAAA,CAAQ;IAC5C,MAAME,EAAA,GAAK7E,EAAA,CAAGiB,GAAA,CAAIzB,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,CAAC,GAAGA,CAAA,CAAE,EAAE,CAAC,EAAEmF,MAAA,CAAQ;IAE7C,MAAMG,GAAA,GAAMN,OAAA,CAAOO,WAAA,CAAa;IAChC,IAAID,GAAA,GAAM,GAAGJ,EAAA,GAAK,CAACA,EAAA;IAEnBvE,cAAA,CAAe6E,cAAA,CAAeR,OAAM;IAEpC,MAAMS,KAAA,GAAQ,IAAIP,EAAA;IAClB,MAAMQ,KAAA,GAAQ,IAAIN,EAAA;IAClB,MAAMO,KAAA,GAAQ,IAAIN,EAAA;IAElB1E,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKQ,KAAA;IAC9B9E,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKQ,KAAA;IAC9B9E,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKQ,KAAA;IAE9B9E,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKS,KAAA;IAC9B/E,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKS,KAAA;IAC9B/E,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKS,KAAA;IAE9B/E,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKU,KAAA;IAC9BhF,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKU,KAAA;IAC9BhF,cAAA,CAAesE,QAAA,CAAS,CAAC,KAAKU,KAAA;IAE9B,KAAKnE,QAAA,CAASoE,QAAA,CAASjF,cAAc;IAErC,KAAKY,QAAA,CAASa,CAAA,IAAK8C,EAAA;IACnB,KAAK3D,QAAA,CAASkB,CAAA,IAAK2C,EAAA;IACnB,KAAK7D,QAAA,CAASmB,CAAA,IAAK2C,EAAA;IAEnB7E,EAAA,CAAGqF,qBAAA,CAAsBb,OAAM;IAC/B,KAAK1D,MAAA,CAAOkB,GAAA,CAAIhC,EAAE;IAElB,OAAO;EACR;AACH;AAEA,MAAMyC,GAAA,GAAsB,mBAAI7B,GAAA,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}