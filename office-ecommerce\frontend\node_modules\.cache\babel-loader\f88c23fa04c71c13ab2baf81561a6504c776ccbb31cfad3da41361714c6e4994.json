{"ast": null, "code": "/**\n * @license React\n * react-reconciler-constants.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    var SyncLane = /*                        */\n    1;\n    var InputContinuousLane = /*            */\n    4;\n    var DefaultLane = /*                    */\n    16;\n    var IdleLane = /*                       */\n    536870912;\n    var DiscreteEventPriority = SyncLane;\n    var ContinuousEventPriority = InputContinuousLane;\n    var DefaultEventPriority = DefaultLane;\n    var IdleEventPriority = IdleLane;\n    var LegacyRoot = 0;\n    var ConcurrentRoot = 1;\n    exports.ConcurrentRoot = ConcurrentRoot;\n    exports.ContinuousEventPriority = ContinuousEventPriority;\n    exports.DefaultEventPriority = DefaultEventPriority;\n    exports.DiscreteEventPriority = DiscreteEventPriority;\n    exports.IdleEventPriority = IdleEventPriority;\n    exports.LegacyRoot = LegacyRoot;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "SyncLane", "InputContinuousLane", "DefaultLane", "IdleLane", "DiscreteEventPriority", "ContinuousEventPriority", "DefaultEventPriority", "IdleEventPriority", "LegacyRoot", "ConcurrentRoot", "exports"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/react-reconciler/cjs/react-reconciler-constants.development.js"], "sourcesContent": ["/**\n * @license React\n * react-reconciler-constants.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar SyncLane =\n/*                        */\n1;\nvar InputContinuousLane =\n/*            */\n4;\nvar DefaultLane =\n/*                    */\n16;\nvar IdleLane =\n/*                       */\n536870912;\n\nvar DiscreteEventPriority = SyncLane;\nvar ContinuousEventPriority = InputContinuousLane;\nvar DefaultEventPriority = DefaultLane;\nvar IdleEventPriority = IdleLane;\n\nvar LegacyRoot = 0;\nvar ConcurrentRoot = 1;\n\nexports.ConcurrentRoot = ConcurrentRoot;\nexports.ContinuousEventPriority = ContinuousEventPriority;\nexports.DefaultEventPriority = DefaultEventPriority;\nexports.DiscreteEventPriority = DiscreteEventPriority;\nexports.IdleEventPriority = IdleEventPriority;\nexports.LegacyRoot = LegacyRoot;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IACd,YAAY;;IAEZ,IAAIC,QAAQ,GACZ;IACA,CAAC;IACD,IAAIC,mBAAmB,GACvB;IACA,CAAC;IACD,IAAIC,WAAW,GACf;IACA,EAAE;IACF,IAAIC,QAAQ,GACZ;IACA,SAAS;IAET,IAAIC,qBAAqB,GAAGJ,QAAQ;IACpC,IAAIK,uBAAuB,GAAGJ,mBAAmB;IACjD,IAAIK,oBAAoB,GAAGJ,WAAW;IACtC,IAAIK,iBAAiB,GAAGJ,QAAQ;IAEhC,IAAIK,UAAU,GAAG,CAAC;IAClB,IAAIC,cAAc,GAAG,CAAC;IAEtBC,OAAO,CAACD,cAAc,GAAGA,cAAc;IACvCC,OAAO,CAACL,uBAAuB,GAAGA,uBAAuB;IACzDK,OAAO,CAACJ,oBAAoB,GAAGA,oBAAoB;IACnDI,OAAO,CAACN,qBAAqB,GAAGA,qBAAqB;IACrDM,OAAO,CAACH,iBAAiB,GAAGA,iBAAiB;IAC7CG,OAAO,CAACF,UAAU,GAAGA,UAAU;EAC7B,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}