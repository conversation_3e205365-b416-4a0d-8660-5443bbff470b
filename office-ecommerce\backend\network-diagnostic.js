const net = require('net');
const http = require('http');

console.log('🔍 Network Diagnostic Tool');
console.log('==========================\n');

// Test 1: Check if port is available
function checkPortAvailable(port) {
    return new Promise((resolve) => {
        const server = net.createServer();
        
        server.listen(port, () => {
            server.close(() => {
                resolve(true); // Port is available
            });
        });
        
        server.on('error', () => {
            resolve(false); // Port is in use
        });
    });
}

// Test 2: Try to connect to a port
function testConnection(port) {
    return new Promise((resolve) => {
        const client = new net.Socket();
        
        client.connect(port, 'localhost', () => {
            client.destroy();
            resolve(true);
        });
        
        client.on('error', () => {
            resolve(false);
        });
        
        client.setTimeout(3000, () => {
            client.destroy();
            resolve(false);
        });
    });
}

// Test 3: Create a simple HTTP server and test it
function createTestServer(port) {
    return new Promise((resolve, reject) => {
        const server = http.createServer((req, res) => {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ status: 'OK', message: 'Test server working' }));
        });
        
        server.listen(port, '0.0.0.0', () => {
            console.log(`✅ Test server started on port ${port}`);
            
            // Test the server immediately
            const testReq = http.request({
                hostname: 'localhost',
                port: port,
                path: '/',
                method: 'GET'
            }, (res) => {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => {
                    console.log(`✅ Test server response: ${data}`);
                    server.close(() => {
                        resolve(true);
                    });
                });
            });
            
            testReq.on('error', (error) => {
                console.log(`❌ Test server request failed: ${error.message}`);
                server.close(() => {
                    resolve(false);
                });
            });
            
            testReq.end();
        });
        
        server.on('error', (error) => {
            console.log(`❌ Test server failed to start: ${error.message}`);
            resolve(false);
        });
    });
}

async function runDiagnostics() {
    const testPorts = [8000, 8001, 3001, 5000, 5001];
    
    console.log('1. Checking port availability...');
    for (const port of testPorts) {
        const available = await checkPortAvailable(port);
        console.log(`   Port ${port}: ${available ? '✅ Available' : '❌ In use'}`);
    }
    
    console.log('\n2. Testing connections to ports...');
    for (const port of testPorts) {
        const connected = await testConnection(port);
        console.log(`   Port ${port}: ${connected ? '✅ Connectable' : '❌ Not connectable'}`);
    }
    
    console.log('\n3. Testing HTTP server creation...');
    const testPort = 8001;
    const serverWorked = await createTestServer(testPort);
    console.log(`   HTTP server test: ${serverWorked ? '✅ Success' : '❌ Failed'}`);
    
    console.log('\n4. Environment information...');
    console.log(`   Node.js version: ${process.version}`);
    console.log(`   Platform: ${process.platform}`);
    console.log(`   Architecture: ${process.arch}`);
    console.log(`   Current working directory: ${process.cwd()}`);
    
    console.log('\n5. Network interface information...');
    const os = require('os');
    const interfaces = os.networkInterfaces();
    Object.keys(interfaces).forEach(name => {
        interfaces[name].forEach(iface => {
            if (iface.family === 'IPv4') {
                console.log(`   ${name}: ${iface.address} (${iface.internal ? 'internal' : 'external'})`);
            }
        });
    });
    
    console.log('\n🏁 Diagnostic complete!');
}

runDiagnostics().catch(console.error);
