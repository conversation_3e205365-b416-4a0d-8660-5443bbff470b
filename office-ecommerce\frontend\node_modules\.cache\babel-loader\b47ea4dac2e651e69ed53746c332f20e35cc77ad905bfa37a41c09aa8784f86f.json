{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\nconst RenderTexture = /*#__PURE__*/React.forwardRef(({\n  children,\n  compute,\n  width,\n  height,\n  samples = 8,\n  renderPriority = 0,\n  eventPriority = 0,\n  frames = Infinity,\n  stencilBuffer = false,\n  depthBuffer = true,\n  generateMipmaps = false,\n  ...props\n}, forwardRef) => {\n  const {\n    size,\n    viewport\n  } = useThree();\n  const fbo = useFBO((width || size.width) * viewport.dpr, (height || size.height) * viewport.dpr, {\n    samples,\n    stencilBuffer,\n    depthBuffer,\n    generateMipmaps\n  });\n  const [vScene] = React.useState(() => new THREE.Scene());\n  const uvCompute = React.useCallback((event, state, previous) => {\n    var _fbo$texture, _previous$previousRoo;\n\n    // Since this is only a texture it does not have an easy way to obtain the parent, which we\n    // need to transform event coordinates to local coordinates. We use r3f internals to find the\n    // next Object3D.\n    let parent = (_fbo$texture = fbo.texture) == null ? void 0 : _fbo$texture.__r3f.parent;\n    while (parent && !(parent instanceof THREE.Object3D)) {\n      parent = parent.__r3f.parent;\n    }\n    if (!parent) return false; // First we call the previous state-onion-layers compute, this is what makes it possible to nest portals\n\n    if (!previous.raycaster.camera) previous.events.compute(event, previous, (_previous$previousRoo = previous.previousRoot) == null ? void 0 : _previous$previousRoo.getState()); // We run a quick check against the parent, if it isn't hit there's no need to raycast at all\n\n    const [intersection] = previous.raycaster.intersectObject(parent);\n    if (!intersection) return false; // We take that hits uv coords, set up this layers raycaster, et voilà, we have raycasting on arbitrary surfaces\n\n    const uv = intersection.uv;\n    if (!uv) return false;\n    state.raycaster.setFromCamera(state.pointer.set(uv.x * 2 - 1, uv.y * 2 - 1), state.camera);\n  }, []);\n  React.useImperativeHandle(forwardRef, () => fbo.texture, [fbo]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(Container, {\n    renderPriority: renderPriority,\n    frames: frames,\n    fbo: fbo\n  }, children), vScene, {\n    events: {\n      compute: compute || uvCompute,\n      priority: eventPriority\n    }\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: fbo.texture\n  }, props)));\n}); // The container component has to be separate, it can not be inlined because \"useFrame(state\" when run inside createPortal will return\n// the portals own state which includes user-land overrides (custom cameras etc), but if it is executed in <RenderTexture>'s render function\n// it would return the default state.\n\nfunction Container({\n  frames,\n  renderPriority,\n  children,\n  fbo\n}) {\n  let count = 0;\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      state.gl.setRenderTarget(fbo);\n      state.gl.render(state.scene, state.camera);\n      state.gl.setRenderTarget(null);\n      count++;\n    }\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\nexport { RenderTexture };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "useThree", "createPortal", "useFrame", "useFBO", "RenderTexture", "forwardRef", "children", "compute", "width", "height", "samples", "renderPriority", "eventPriority", "frames", "Infinity", "stencil<PERSON>uffer", "depthBuffer", "generateMipmaps", "props", "size", "viewport", "fbo", "dpr", "vScene", "useState", "Scene", "uvCompute", "useCallback", "event", "state", "previous", "_fbo$texture", "_previous$previousRoo", "parent", "texture", "__r3f", "Object3D", "raycaster", "camera", "events", "previousRoot", "getState", "intersection", "intersectObject", "uv", "setFromCamera", "pointer", "set", "x", "y", "useImperativeHandle", "createElement", "Fragment", "Container", "priority", "object", "count", "gl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "scene"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/RenderTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\n\nconst RenderTexture = /*#__PURE__*/React.forwardRef(({\n  children,\n  compute,\n  width,\n  height,\n  samples = 8,\n  renderPriority = 0,\n  eventPriority = 0,\n  frames = Infinity,\n  stencilBuffer = false,\n  depthBuffer = true,\n  generateMipmaps = false,\n  ...props\n}, forwardRef) => {\n  const {\n    size,\n    viewport\n  } = useThree();\n  const fbo = useFBO((width || size.width) * viewport.dpr, (height || size.height) * viewport.dpr, {\n    samples,\n    stencilBuffer,\n    depthBuffer,\n    generateMipmaps\n  });\n  const [vScene] = React.useState(() => new THREE.Scene());\n  const uvCompute = React.useCallback((event, state, previous) => {\n    var _fbo$texture, _previous$previousRoo;\n\n    // Since this is only a texture it does not have an easy way to obtain the parent, which we\n    // need to transform event coordinates to local coordinates. We use r3f internals to find the\n    // next Object3D.\n    let parent = (_fbo$texture = fbo.texture) == null ? void 0 : _fbo$texture.__r3f.parent;\n\n    while (parent && !(parent instanceof THREE.Object3D)) {\n      parent = parent.__r3f.parent;\n    }\n\n    if (!parent) return false; // First we call the previous state-onion-layers compute, this is what makes it possible to nest portals\n\n    if (!previous.raycaster.camera) previous.events.compute(event, previous, (_previous$previousRoo = previous.previousRoot) == null ? void 0 : _previous$previousRoo.getState()); // We run a quick check against the parent, if it isn't hit there's no need to raycast at all\n\n    const [intersection] = previous.raycaster.intersectObject(parent);\n    if (!intersection) return false; // We take that hits uv coords, set up this layers raycaster, et voilà, we have raycasting on arbitrary surfaces\n\n    const uv = intersection.uv;\n    if (!uv) return false;\n    state.raycaster.setFromCamera(state.pointer.set(uv.x * 2 - 1, uv.y * 2 - 1), state.camera);\n  }, []);\n  React.useImperativeHandle(forwardRef, () => fbo.texture, [fbo]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal( /*#__PURE__*/React.createElement(Container, {\n    renderPriority: renderPriority,\n    frames: frames,\n    fbo: fbo\n  }, children), vScene, {\n    events: {\n      compute: compute || uvCompute,\n      priority: eventPriority\n    }\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: fbo.texture\n  }, props)));\n}); // The container component has to be separate, it can not be inlined because \"useFrame(state\" when run inside createPortal will return\n// the portals own state which includes user-land overrides (custom cameras etc), but if it is executed in <RenderTexture>'s render function\n// it would return the default state.\n\nfunction Container({\n  frames,\n  renderPriority,\n  children,\n  fbo\n}) {\n  let count = 0;\n  useFrame(state => {\n    if (frames === Infinity || count < frames) {\n      state.gl.setRenderTarget(fbo);\n      state.gl.render(state.scene, state.camera);\n      state.gl.setRenderTarget(null);\n      count++;\n    }\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\n\nexport { RenderTexture };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,oBAAoB;AACrE,SAASC,MAAM,QAAQ,aAAa;AAEpC,MAAMC,aAAa,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACnDC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACLC,MAAM;EACNC,OAAO,GAAG,CAAC;EACXC,cAAc,GAAG,CAAC;EAClBC,aAAa,GAAG,CAAC;EACjBC,MAAM,GAAGC,QAAQ;EACjBC,aAAa,GAAG,KAAK;EACrBC,WAAW,GAAG,IAAI;EAClBC,eAAe,GAAG,KAAK;EACvB,GAAGC;AACL,CAAC,EAAEb,UAAU,KAAK;EAChB,MAAM;IACJc,IAAI;IACJC;EACF,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EACd,MAAMqB,GAAG,GAAGlB,MAAM,CAAC,CAACK,KAAK,IAAIW,IAAI,CAACX,KAAK,IAAIY,QAAQ,CAACE,GAAG,EAAE,CAACb,MAAM,IAAIU,IAAI,CAACV,MAAM,IAAIW,QAAQ,CAACE,GAAG,EAAE;IAC/FZ,OAAO;IACPK,aAAa;IACbC,WAAW;IACXC;EACF,CAAC,CAAC;EACF,MAAM,CAACM,MAAM,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAAC2B,KAAK,CAAC,CAAC,CAAC;EACxD,MAAMC,SAAS,GAAG3B,KAAK,CAAC4B,WAAW,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,KAAK;IAC9D,IAAIC,YAAY,EAAEC,qBAAqB;;IAEvC;IACA;IACA;IACA,IAAIC,MAAM,GAAG,CAACF,YAAY,GAAGV,GAAG,CAACa,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,YAAY,CAACI,KAAK,CAACF,MAAM;IAEtF,OAAOA,MAAM,IAAI,EAAEA,MAAM,YAAYnC,KAAK,CAACsC,QAAQ,CAAC,EAAE;MACpDH,MAAM,GAAGA,MAAM,CAACE,KAAK,CAACF,MAAM;IAC9B;IAEA,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK,CAAC,CAAC;;IAE3B,IAAI,CAACH,QAAQ,CAACO,SAAS,CAACC,MAAM,EAAER,QAAQ,CAACS,MAAM,CAAChC,OAAO,CAACqB,KAAK,EAAEE,QAAQ,EAAE,CAACE,qBAAqB,GAAGF,QAAQ,CAACU,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGR,qBAAqB,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE/K,MAAM,CAACC,YAAY,CAAC,GAAGZ,QAAQ,CAACO,SAAS,CAACM,eAAe,CAACV,MAAM,CAAC;IACjE,IAAI,CAACS,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC;;IAEjC,MAAME,EAAE,GAAGF,YAAY,CAACE,EAAE;IAC1B,IAAI,CAACA,EAAE,EAAE,OAAO,KAAK;IACrBf,KAAK,CAACQ,SAAS,CAACQ,aAAa,CAAChB,KAAK,CAACiB,OAAO,CAACC,GAAG,CAACH,EAAE,CAACI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEJ,EAAE,CAACK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEpB,KAAK,CAACS,MAAM,CAAC;EAC5F,CAAC,EAAE,EAAE,CAAC;EACNvC,KAAK,CAACmD,mBAAmB,CAAC7C,UAAU,EAAE,MAAMgB,GAAG,CAACa,OAAO,EAAE,CAACb,GAAG,CAAC,CAAC;EAC/D,OAAO,aAAatB,KAAK,CAACoD,aAAa,CAACpD,KAAK,CAACqD,QAAQ,EAAE,IAAI,EAAEnD,YAAY,CAAE,aAAaF,KAAK,CAACoD,aAAa,CAACE,SAAS,EAAE;IACtH1C,cAAc,EAAEA,cAAc;IAC9BE,MAAM,EAAEA,MAAM;IACdQ,GAAG,EAAEA;EACP,CAAC,EAAEf,QAAQ,CAAC,EAAEiB,MAAM,EAAE;IACpBgB,MAAM,EAAE;MACNhC,OAAO,EAAEA,OAAO,IAAImB,SAAS;MAC7B4B,QAAQ,EAAE1C;IACZ;EACF,CAAC,CAAC,EAAE,aAAab,KAAK,CAACoD,aAAa,CAAC,WAAW,EAAEtD,QAAQ,CAAC;IACzD0D,MAAM,EAAElC,GAAG,CAACa;EACd,CAAC,EAAEhB,KAAK,CAAC,CAAC,CAAC;AACb,CAAC,CAAC,CAAC,CAAC;AACJ;AACA;;AAEA,SAASmC,SAASA,CAAC;EACjBxC,MAAM;EACNF,cAAc;EACdL,QAAQ;EACRe;AACF,CAAC,EAAE;EACD,IAAImC,KAAK,GAAG,CAAC;EACbtD,QAAQ,CAAC2B,KAAK,IAAI;IAChB,IAAIhB,MAAM,KAAKC,QAAQ,IAAI0C,KAAK,GAAG3C,MAAM,EAAE;MACzCgB,KAAK,CAAC4B,EAAE,CAACC,eAAe,CAACrC,GAAG,CAAC;MAC7BQ,KAAK,CAAC4B,EAAE,CAACE,MAAM,CAAC9B,KAAK,CAAC+B,KAAK,EAAE/B,KAAK,CAACS,MAAM,CAAC;MAC1CT,KAAK,CAAC4B,EAAE,CAACC,eAAe,CAAC,IAAI,CAAC;MAC9BF,KAAK,EAAE;IACT;EACF,CAAC,EAAE7C,cAAc,CAAC;EAClB,OAAO,aAAaZ,KAAK,CAACoD,aAAa,CAACpD,KAAK,CAACqD,QAAQ,EAAE,IAAI,EAAE9C,QAAQ,CAAC;AACzE;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}