{"ast": null, "code": "import { registerAction, dragAction, pinchAction, wheelAction, scrollAction, moveAction, hoverAction } from '@use-gesture/core/actions';\nexport * from '@use-gesture/core/actions';\nimport React from 'react';\nimport { Controller, parseMergedHandlers } from '@use-gesture/core';\nexport * from '@use-gesture/core/utils';\nexport * from '@use-gesture/core/types';\nfunction useRecognizers(handlers, config = {}, gestureKey, nativeHandlers) {\n  const ctrl = React.useMemo(() => new Controller(handlers), []);\n  ctrl.applyHandlers(handlers, nativeHandlers);\n  ctrl.applyConfig(config, gestureKey);\n  React.useEffect(ctrl.effect.bind(ctrl));\n  React.useEffect(() => {\n    return ctrl.clean.bind(ctrl);\n  }, []);\n  if (config.target === undefined) {\n    return ctrl.bind.bind(ctrl);\n  }\n  return undefined;\n}\nfunction useDrag(handler, config) {\n  registerAction(dragAction);\n  return useRecognizers({\n    drag: handler\n  }, config || {}, 'drag');\n}\nfunction usePinch(handler, config) {\n  registerAction(pinchAction);\n  return useRecognizers({\n    pinch: handler\n  }, config || {}, 'pinch');\n}\nfunction useWheel(handler, config) {\n  registerAction(wheelAction);\n  return useRecognizers({\n    wheel: handler\n  }, config || {}, 'wheel');\n}\nfunction useScroll(handler, config) {\n  registerAction(scrollAction);\n  return useRecognizers({\n    scroll: handler\n  }, config || {}, 'scroll');\n}\nfunction useMove(handler, config) {\n  registerAction(moveAction);\n  return useRecognizers({\n    move: handler\n  }, config || {}, 'move');\n}\nfunction useHover(handler, config) {\n  registerAction(hoverAction);\n  return useRecognizers({\n    hover: handler\n  }, config || {}, 'hover');\n}\nfunction createUseGesture(actions) {\n  actions.forEach(registerAction);\n  return function useGesture(_handlers, _config) {\n    const {\n      handlers,\n      nativeHandlers,\n      config\n    } = parseMergedHandlers(_handlers, _config || {});\n    return useRecognizers(handlers, config, undefined, nativeHandlers);\n  };\n}\nfunction useGesture(handlers, config) {\n  const hook = createUseGesture([dragAction, pinchAction, scrollAction, wheelAction, moveAction, hoverAction]);\n  return hook(handlers, config || {});\n}\nexport { createUseGesture, useDrag, useGesture, useHover, useMove, usePinch, useScroll, useWheel };", "map": {"version": 3, "names": ["registerAction", "dragAction", "pinchAction", "wheelAction", "scrollAction", "moveAction", "hoverAction", "React", "Controller", "parseMergedHandlers", "useRecognizers", "handlers", "config", "<PERSON><PERSON><PERSON>", "nativeHandlers", "ctrl", "useMemo", "applyHandlers", "applyConfig", "useEffect", "effect", "bind", "clean", "target", "undefined", "useDrag", "handler", "drag", "usePinch", "pinch", "useWheel", "wheel", "useScroll", "scroll", "useMove", "move", "useHover", "hover", "createUseGesture", "actions", "for<PERSON>ach", "useGesture", "_handlers", "_config", "hook"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@use-gesture/react/dist/use-gesture-react.esm.js"], "sourcesContent": ["import { registerAction, dragAction, pinchAction, wheelAction, scrollAction, moveAction, hoverAction } from '@use-gesture/core/actions';\nexport * from '@use-gesture/core/actions';\nimport React from 'react';\nimport { Controller, parseMergedHandlers } from '@use-gesture/core';\nexport * from '@use-gesture/core/utils';\nexport * from '@use-gesture/core/types';\n\nfunction useRecognizers(handlers, config = {}, gestureKey, nativeHandlers) {\n  const ctrl = React.useMemo(() => new Controller(handlers), []);\n  ctrl.applyHandlers(handlers, nativeHandlers);\n  ctrl.applyConfig(config, gestureKey);\n  React.useEffect(ctrl.effect.bind(ctrl));\n  React.useEffect(() => {\n    return ctrl.clean.bind(ctrl);\n  }, []);\n  if (config.target === undefined) {\n    return ctrl.bind.bind(ctrl);\n  }\n  return undefined;\n}\n\nfunction useDrag(handler, config) {\n  registerAction(dragAction);\n  return useRecognizers({\n    drag: handler\n  }, config || {}, 'drag');\n}\n\nfunction usePinch(handler, config) {\n  registerAction(pinchAction);\n  return useRecognizers({\n    pinch: handler\n  }, config || {}, 'pinch');\n}\n\nfunction useWheel(handler, config) {\n  registerAction(wheelAction);\n  return useRecognizers({\n    wheel: handler\n  }, config || {}, 'wheel');\n}\n\nfunction useScroll(handler, config) {\n  registerAction(scrollAction);\n  return useRecognizers({\n    scroll: handler\n  }, config || {}, 'scroll');\n}\n\nfunction useMove(handler, config) {\n  registerAction(moveAction);\n  return useRecognizers({\n    move: handler\n  }, config || {}, 'move');\n}\n\nfunction useHover(handler, config) {\n  registerAction(hoverAction);\n  return useRecognizers({\n    hover: handler\n  }, config || {}, 'hover');\n}\n\nfunction createUseGesture(actions) {\n  actions.forEach(registerAction);\n  return function useGesture(_handlers, _config) {\n    const {\n      handlers,\n      nativeHandlers,\n      config\n    } = parseMergedHandlers(_handlers, _config || {});\n    return useRecognizers(handlers, config, undefined, nativeHandlers);\n  };\n}\n\nfunction useGesture(handlers, config) {\n  const hook = createUseGesture([dragAction, pinchAction, scrollAction, wheelAction, moveAction, hoverAction]);\n  return hook(handlers, config || {});\n}\n\nexport { createUseGesture, useDrag, useGesture, useHover, useMove, usePinch, useScroll, useWheel };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,QAAQ,2BAA2B;AACvI,cAAc,2BAA2B;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,mBAAmB,QAAQ,mBAAmB;AACnE,cAAc,yBAAyB;AACvC,cAAc,yBAAyB;AAEvC,SAASC,cAAcA,CAACC,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAEC,UAAU,EAAEC,cAAc,EAAE;EACzE,MAAMC,IAAI,GAAGR,KAAK,CAACS,OAAO,CAAC,MAAM,IAAIR,UAAU,CAACG,QAAQ,CAAC,EAAE,EAAE,CAAC;EAC9DI,IAAI,CAACE,aAAa,CAACN,QAAQ,EAAEG,cAAc,CAAC;EAC5CC,IAAI,CAACG,WAAW,CAACN,MAAM,EAAEC,UAAU,CAAC;EACpCN,KAAK,CAACY,SAAS,CAACJ,IAAI,CAACK,MAAM,CAACC,IAAI,CAACN,IAAI,CAAC,CAAC;EACvCR,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,OAAOJ,IAAI,CAACO,KAAK,CAACD,IAAI,CAACN,IAAI,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;EACN,IAAIH,MAAM,CAACW,MAAM,KAAKC,SAAS,EAAE;IAC/B,OAAOT,IAAI,CAACM,IAAI,CAACA,IAAI,CAACN,IAAI,CAAC;EAC7B;EACA,OAAOS,SAAS;AAClB;AAEA,SAASC,OAAOA,CAACC,OAAO,EAAEd,MAAM,EAAE;EAChCZ,cAAc,CAACC,UAAU,CAAC;EAC1B,OAAOS,cAAc,CAAC;IACpBiB,IAAI,EAAED;EACR,CAAC,EAAEd,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;AAC1B;AAEA,SAASgB,QAAQA,CAACF,OAAO,EAAEd,MAAM,EAAE;EACjCZ,cAAc,CAACE,WAAW,CAAC;EAC3B,OAAOQ,cAAc,CAAC;IACpBmB,KAAK,EAAEH;EACT,CAAC,EAAEd,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;AAC3B;AAEA,SAASkB,QAAQA,CAACJ,OAAO,EAAEd,MAAM,EAAE;EACjCZ,cAAc,CAACG,WAAW,CAAC;EAC3B,OAAOO,cAAc,CAAC;IACpBqB,KAAK,EAAEL;EACT,CAAC,EAAEd,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;AAC3B;AAEA,SAASoB,SAASA,CAACN,OAAO,EAAEd,MAAM,EAAE;EAClCZ,cAAc,CAACI,YAAY,CAAC;EAC5B,OAAOM,cAAc,CAAC;IACpBuB,MAAM,EAAEP;EACV,CAAC,EAAEd,MAAM,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC;AAC5B;AAEA,SAASsB,OAAOA,CAACR,OAAO,EAAEd,MAAM,EAAE;EAChCZ,cAAc,CAACK,UAAU,CAAC;EAC1B,OAAOK,cAAc,CAAC;IACpByB,IAAI,EAAET;EACR,CAAC,EAAEd,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC;AAC1B;AAEA,SAASwB,QAAQA,CAACV,OAAO,EAAEd,MAAM,EAAE;EACjCZ,cAAc,CAACM,WAAW,CAAC;EAC3B,OAAOI,cAAc,CAAC;IACpB2B,KAAK,EAAEX;EACT,CAAC,EAAEd,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;AAC3B;AAEA,SAAS0B,gBAAgBA,CAACC,OAAO,EAAE;EACjCA,OAAO,CAACC,OAAO,CAACxC,cAAc,CAAC;EAC/B,OAAO,SAASyC,UAAUA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC7C,MAAM;MACJhC,QAAQ;MACRG,cAAc;MACdF;IACF,CAAC,GAAGH,mBAAmB,CAACiC,SAAS,EAAEC,OAAO,IAAI,CAAC,CAAC,CAAC;IACjD,OAAOjC,cAAc,CAACC,QAAQ,EAAEC,MAAM,EAAEY,SAAS,EAAEV,cAAc,CAAC;EACpE,CAAC;AACH;AAEA,SAAS2B,UAAUA,CAAC9B,QAAQ,EAAEC,MAAM,EAAE;EACpC,MAAMgC,IAAI,GAAGN,gBAAgB,CAAC,CAACrC,UAAU,EAAEC,WAAW,EAAEE,YAAY,EAAED,WAAW,EAAEE,UAAU,EAAEC,WAAW,CAAC,CAAC;EAC5G,OAAOsC,IAAI,CAACjC,QAAQ,EAAEC,MAAM,IAAI,CAAC,CAAC,CAAC;AACrC;AAEA,SAAS0B,gBAAgB,EAAEb,OAAO,EAAEgB,UAAU,EAAEL,QAAQ,EAAEF,OAAO,EAAEN,QAAQ,EAAEI,SAAS,EAAEF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}