{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>adingManager, Group } from \"three\";\nimport { ColladaLoader } from \"./ColladaLoader.js\";\nimport { unzipSync } from \"fflate\";\nclass KMZLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data) {\n    function findFile(url) {\n      for (const path in zip) {\n        if (path.substr(-url.length) === url) {\n          return zip[path];\n        }\n      }\n    }\n    const manager = new LoadingManager();\n    manager.setURLModifier(function (url) {\n      const image = findFile(url);\n      if (image) {\n        console.log(\"Loading\", url);\n        const blob = new Blob([image.buffer], {\n          type: \"application/octet-stream\"\n        });\n        return URL.createObjectURL(blob);\n      }\n      return url;\n    });\n    const zip = unzipSync(new Uint8Array(data));\n    if (zip[\"doc.kml\"]) {\n      const xml = new DOMParser().parseFromString(fflate.strFromU8(zip[\"doc.kml\"]), \"application/xml\");\n      const model = xml.querySelector(\"Placemark Model Link href\");\n      if (model) {\n        const loader = new ColladaLoader(manager);\n        return loader.parse(fflate.strFromU8(zip[model.textContent]));\n      }\n    } else {\n      console.warn(\"KMZLoader: Missing doc.kml file.\");\n      for (const path in zip) {\n        const extension = path.split(\".\").pop().toLowerCase();\n        if (extension === \"dae\") {\n          const loader = new ColladaLoader(manager);\n          return loader.parse(fflate.strFromU8(zip[path]));\n        }\n      }\n    }\n    console.error(\"KMZLoader: Couldn't find .dae file.\");\n    return {\n      scene: new Group()\n    };\n  }\n}\nexport { KMZLoader };", "map": {"version": 3, "names": ["KMZLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "data", "findFile", "zip", "substr", "length", "LoadingManager", "setURLModifier", "image", "log", "blob", "Blob", "buffer", "type", "URL", "createObjectURL", "unzipSync", "Uint8Array", "xml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "fflate", "strFromU8", "model", "querySelector", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "warn", "extension", "split", "pop", "toLowerCase", "scene", "Group"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\KMZLoader.js"], "sourcesContent": ["import { FileLoader, Group, Loader, LoadingManager } from 'three'\nimport { ColladaLoader } from '../loaders/ColladaLoader'\nimport { unzipSync } from 'fflate'\n\nclass KMZLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    function findFile(url) {\n      for (const path in zip) {\n        if (path.substr(-url.length) === url) {\n          return zip[path]\n        }\n      }\n    }\n\n    const manager = new LoadingManager()\n    manager.setURLModifier(function (url) {\n      const image = findFile(url)\n\n      if (image) {\n        console.log('Loading', url)\n\n        const blob = new Blob([image.buffer], { type: 'application/octet-stream' })\n        return URL.createObjectURL(blob)\n      }\n\n      return url\n    })\n\n    //\n\n    const zip = unzipSync(new Uint8Array(data))\n\n    if (zip['doc.kml']) {\n      const xml = new DOMParser().parseFromString(fflate.strFromU8(zip['doc.kml']), 'application/xml')\n      const model = xml.querySelector('Placemark Model Link href')\n\n      if (model) {\n        const loader = new ColladaLoader(manager)\n        return loader.parse(fflate.strFromU8(zip[model.textContent]))\n      }\n    } else {\n      console.warn('KMZLoader: Missing doc.kml file.')\n\n      for (const path in zip) {\n        const extension = path.split('.').pop().toLowerCase()\n\n        if (extension === 'dae') {\n          const loader = new ColladaLoader(manager)\n          return loader.parse(fflate.strFromU8(zip[path]))\n        }\n      }\n    }\n\n    console.error(\"KMZLoader: Couldn't find .dae file.\")\n    return { scene: new Group() }\n  }\n}\n\nexport { KMZLoader }\n"], "mappings": ";;;AAIA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMN,OAAO;IAC3CO,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;IAC/CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMN,OAAA,CAAQqB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMK,IAAA,EAAM;IACV,SAASC,SAASrB,GAAA,EAAK;MACrB,WAAWQ,IAAA,IAAQc,GAAA,EAAK;QACtB,IAAId,IAAA,CAAKe,MAAA,CAAO,CAACvB,GAAA,CAAIwB,MAAM,MAAMxB,GAAA,EAAK;UACpC,OAAOsB,GAAA,CAAId,IAAI;QAChB;MACF;IACF;IAED,MAAMV,OAAA,GAAU,IAAI2B,cAAA,CAAgB;IACpC3B,OAAA,CAAQ4B,cAAA,CAAe,UAAU1B,GAAA,EAAK;MACpC,MAAM2B,KAAA,GAAQN,QAAA,CAASrB,GAAG;MAE1B,IAAI2B,KAAA,EAAO;QACTV,OAAA,CAAQW,GAAA,CAAI,WAAW5B,GAAG;QAE1B,MAAM6B,IAAA,GAAO,IAAIC,IAAA,CAAK,CAACH,KAAA,CAAMI,MAAM,GAAG;UAAEC,IAAA,EAAM;QAAA,CAA4B;QAC1E,OAAOC,GAAA,CAAIC,eAAA,CAAgBL,IAAI;MAChC;MAED,OAAO7B,GAAA;IACb,CAAK;IAID,MAAMsB,GAAA,GAAMa,SAAA,CAAU,IAAIC,UAAA,CAAWhB,IAAI,CAAC;IAE1C,IAAIE,GAAA,CAAI,SAAS,GAAG;MAClB,MAAMe,GAAA,GAAM,IAAIC,SAAA,CAAW,EAACC,eAAA,CAAgBC,MAAA,CAAOC,SAAA,CAAUnB,GAAA,CAAI,SAAS,CAAC,GAAG,iBAAiB;MAC/F,MAAMoB,KAAA,GAAQL,GAAA,CAAIM,aAAA,CAAc,2BAA2B;MAE3D,IAAID,KAAA,EAAO;QACT,MAAMrC,MAAA,GAAS,IAAIuC,aAAA,CAAc9C,OAAO;QACxC,OAAOO,MAAA,CAAOU,KAAA,CAAMyB,MAAA,CAAOC,SAAA,CAAUnB,GAAA,CAAIoB,KAAA,CAAMG,WAAW,CAAC,CAAC;MAC7D;IACP,OAAW;MACL5B,OAAA,CAAQ6B,IAAA,CAAK,kCAAkC;MAE/C,WAAWtC,IAAA,IAAQc,GAAA,EAAK;QACtB,MAAMyB,SAAA,GAAYvC,IAAA,CAAKwC,KAAA,CAAM,GAAG,EAAEC,GAAA,CAAK,EAACC,WAAA,CAAa;QAErD,IAAIH,SAAA,KAAc,OAAO;UACvB,MAAM1C,MAAA,GAAS,IAAIuC,aAAA,CAAc9C,OAAO;UACxC,OAAOO,MAAA,CAAOU,KAAA,CAAMyB,MAAA,CAAOC,SAAA,CAAUnB,GAAA,CAAId,IAAI,CAAC,CAAC;QAChD;MACF;IACF;IAEDS,OAAA,CAAQC,KAAA,CAAM,qCAAqC;IACnD,OAAO;MAAEiC,KAAA,EAAO,IAAIC,KAAA;IAAS;EAC9B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}