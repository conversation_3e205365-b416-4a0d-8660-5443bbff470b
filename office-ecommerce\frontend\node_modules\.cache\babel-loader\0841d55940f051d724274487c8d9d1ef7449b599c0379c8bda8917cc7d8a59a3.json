{"ast": null, "code": "var SpotlightShadowShader = \"#define GLSLIFY 1\\nvarying vec2 vUv;uniform sampler2D uShadowMap;uniform float uTime;void main(){vec3 color=texture2D(uShadowMap,vUv).xyz;gl_FragColor=vec4(color,1.);}\"; // eslint-disable-line\n\nexport { SpotlightShadowShader as default };", "map": {"version": 3, "names": ["SpotlightShadowShader", "default"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/helpers/glsl/DefaultSpotlightShadowShadows.glsl.js"], "sourcesContent": ["var SpotlightShadowShader = \"#define GLSLIFY 1\\nvarying vec2 vUv;uniform sampler2D uShadowMap;uniform float uTime;void main(){vec3 color=texture2D(uShadowMap,vUv).xyz;gl_FragColor=vec4(color,1.);}\"; // eslint-disable-line\n\nexport { SpotlightShadowShader as default };\n"], "mappings": "AAAA,IAAIA,qBAAqB,GAAG,yKAAyK,CAAC,CAAC;;AAEvM,SAASA,qBAAqB,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}