{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport Composer from 'react-composer';\nconst _instanceLocalMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst _instanceWorldMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst _instanceIntersects = [];\nconst _mesh = /*@__PURE__*/new THREE.Mesh();\nclass PositionMesh extends THREE.Group {\n  constructor() {\n    super();\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  } // This will allow the virtual instance have bounds\n\n  get geometry() {\n    var _this$instance$curren;\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  } // And this will allow the virtual instance to receive events\n\n  raycast(raycaster, intersects) {\n    const parent = this.instance.current;\n    if (!parent) return;\n    if (!parent.geometry || !parent.material) return;\n    _mesh.geometry = parent.geometry;\n    const matrixWorld = parent.matrixWorld;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey); // If the instance wasn't found or exceeds the parents draw range, bail out\n\n    if (instanceId === -1 || instanceId > parent.count) return; // calculate the world matrix for each instance\n\n    parent.getMatrixAt(instanceId, _instanceLocalMatrix);\n    _instanceWorldMatrix.multiplyMatrices(matrixWorld, _instanceLocalMatrix); // the mesh represents this single instance\n\n    _mesh.matrixWorld = _instanceWorldMatrix; // raycast side according to instance material\n\n    if (parent.material instanceof THREE.Material) _mesh.material.side = parent.material.side;else _mesh.material.side = parent.material[0].side;\n    _mesh.raycast(raycaster, _instanceIntersects); // process the result of raycast\n\n    for (let i = 0, l = _instanceIntersects.length; i < l; i++) {\n      const intersect = _instanceIntersects[i];\n      intersect.instanceId = instanceId;\n      intersect.object = this;\n      intersects.push(intersect);\n    }\n    _instanceIntersects.length = 0;\n  }\n}\nconst globalContext = /*@__PURE__*/React.createContext(null);\nconst parentMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst instanceMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst tempMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst translation = /*@__PURE__*/new THREE.Vector3();\nconst rotation = /*@__PURE__*/new THREE.Quaternion();\nconst scale = /*@__PURE__*/new THREE.Vector3();\nconst Instance = /*#__PURE__*/React.forwardRef(({\n  context,\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionMesh\n  }), []);\n  const group = React.useRef();\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context || globalContext);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionMesh\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: mergeRefs([ref, group])\n  }, props), children);\n});\nconst Instances = /*#__PURE__*/React.forwardRef(({\n  children,\n  range,\n  limit = 1000,\n  frames = Infinity,\n  ...props\n}, ref) => {\n  const [{\n    context,\n    instance\n  }] = React.useState(() => {\n    const context = /*#__PURE__*/React.createContext(null);\n    return {\n      context,\n      instance: /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(Instance, _extends({\n        context: context\n      }, props, {\n        ref: ref\n      })))\n    };\n  });\n  const parentRef = React.useRef(null);\n  const [instances, setInstances] = React.useState([]);\n  const [[matrices, colors]] = React.useState(() => {\n    const mArray = new Float32Array(limit * 16);\n    for (let i = 0; i < limit; i++) tempMatrix.identity().toArray(mArray, i * 16);\n    return [mArray, new Float32Array([...new Array(limit * 3)].map(() => 1))];\n  });\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.instanceMatrix.needsUpdate = true;\n  });\n  let count = 0;\n  let updateRange = 0;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      parentRef.current.updateMatrix();\n      parentRef.current.updateMatrixWorld();\n      parentMatrix.copy(parentRef.current.matrixWorld).invert();\n      updateRange = Math.min(limit, range !== undefined ? range : limit, instances.length);\n      parentRef.current.count = updateRange;\n      parentRef.current.instanceMatrix.updateRange.count = updateRange * 16;\n      parentRef.current.instanceColor.updateRange.count = updateRange * 3;\n      for (let i = 0; i < instances.length; i++) {\n        const instance = instances[i].current; // Multiply the inverse of the InstancedMesh world matrix or else\n        // Instances will be double-transformed if <Instances> isn't at identity\n\n        instance.matrixWorld.decompose(translation, rotation, scale);\n        instanceMatrix.compose(translation, rotation, scale).premultiply(parentMatrix);\n        instanceMatrix.toArray(matrices, i * 16);\n        parentRef.current.instanceMatrix.needsUpdate = true;\n        instance.color.toArray(colors, i * 3);\n        parentRef.current.instanceColor.needsUpdate = true;\n      }\n      count++;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setInstances(instances => [...instances, ref]);\n      return () => setInstances(instances => instances.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"instancedMesh\", _extends({\n    userData: {\n      instances\n    },\n    matrixAutoUpdate: false,\n    ref: mergeRefs([ref, parentRef]),\n    args: [null, null, 0],\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceMatrix\",\n    count: matrices.length / 16,\n    array: matrices,\n    itemSize: 16,\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceColor\",\n    count: colors.length / 3,\n    array: colors,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), typeof children === 'function' ? /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children(instance)) : /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children));\n});\nconst Merged = /*#__PURE__*/React.forwardRef(function Merged({\n  meshes,\n  children,\n  ...props\n}, ref) {\n  const isArray = Array.isArray(meshes); // Filter out meshes from collections, which may contain non-meshes\n\n  if (!isArray) for (const key of Object.keys(meshes)) if (!meshes[key].isMesh) delete meshes[key];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(Composer, {\n    components: (isArray ? meshes : Object.values(meshes)).map(({\n      geometry,\n      material\n    }) => /*#__PURE__*/React.createElement(Instances, _extends({\n      key: geometry.uuid,\n      geometry: geometry,\n      material: material\n    }, props)))\n  }, args => isArray ? children(...args) : children(Object.keys(meshes).filter(key => meshes[key].isMesh).reduce((acc, key, i) => ({\n    ...acc,\n    [key]: args[i]\n  }), {}))));\n});\nexport { Instance, Instances, Merged };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useFrame", "mergeRefs", "Composer", "_instanceLocalMatrix", "Matrix4", "_instanceWorldMatrix", "_instanceIntersects", "_mesh", "<PERSON><PERSON>", "PositionMesh", "Group", "constructor", "color", "Color", "instance", "current", "undefined", "<PERSON><PERSON><PERSON>", "geometry", "_this$instance$curren", "raycast", "raycaster", "intersects", "parent", "material", "matrixWorld", "instanceId", "userData", "instances", "indexOf", "count", "getMatrixAt", "multiplyMatrices", "Material", "side", "i", "l", "length", "intersect", "object", "push", "globalContext", "createContext", "parentMatrix", "instanceMatrix", "tempMatrix", "translation", "Vector3", "rotation", "Quaternion", "scale", "Instance", "forwardRef", "context", "children", "props", "ref", "useMemo", "group", "useRef", "subscribe", "getParent", "useContext", "useLayoutEffect", "createElement", "Instances", "range", "limit", "frames", "Infinity", "useState", "parentRef", "setInstances", "matrices", "colors", "m<PERSON>rray", "Float32Array", "identity", "toArray", "Array", "map", "useEffect", "needsUpdate", "updateRange", "updateMatrix", "updateMatrixWorld", "copy", "invert", "Math", "min", "instanceColor", "decompose", "compose", "premultiply", "api", "filter", "item", "matrixAutoUpdate", "args", "attach", "array", "itemSize", "usage", "DynamicDrawUsage", "Provider", "value", "<PERSON>rged", "meshes", "isArray", "key", "Object", "keys", "<PERSON><PERSON><PERSON>", "components", "values", "uuid", "reduce", "acc"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Instances.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport Composer from 'react-composer';\n\nconst _instanceLocalMatrix = /*@__PURE__*/new THREE.Matrix4();\n\nconst _instanceWorldMatrix = /*@__PURE__*/new THREE.Matrix4();\n\nconst _instanceIntersects = [];\n\nconst _mesh = /*@__PURE__*/new THREE.Mesh();\n\nclass PositionMesh extends THREE.Group {\n  constructor() {\n    super();\n    this.color = new THREE.Color('white');\n    this.instance = {\n      current: undefined\n    };\n    this.instanceKey = {\n      current: undefined\n    };\n  } // This will allow the virtual instance have bounds\n\n\n  get geometry() {\n    var _this$instance$curren;\n\n    return (_this$instance$curren = this.instance.current) == null ? void 0 : _this$instance$curren.geometry;\n  } // And this will allow the virtual instance to receive events\n\n\n  raycast(raycaster, intersects) {\n    const parent = this.instance.current;\n    if (!parent) return;\n    if (!parent.geometry || !parent.material) return;\n    _mesh.geometry = parent.geometry;\n    const matrixWorld = parent.matrixWorld;\n    const instanceId = parent.userData.instances.indexOf(this.instanceKey); // If the instance wasn't found or exceeds the parents draw range, bail out\n\n    if (instanceId === -1 || instanceId > parent.count) return; // calculate the world matrix for each instance\n\n    parent.getMatrixAt(instanceId, _instanceLocalMatrix);\n\n    _instanceWorldMatrix.multiplyMatrices(matrixWorld, _instanceLocalMatrix); // the mesh represents this single instance\n\n\n    _mesh.matrixWorld = _instanceWorldMatrix; // raycast side according to instance material\n\n    if (parent.material instanceof THREE.Material) _mesh.material.side = parent.material.side;else _mesh.material.side = parent.material[0].side;\n\n    _mesh.raycast(raycaster, _instanceIntersects); // process the result of raycast\n\n\n    for (let i = 0, l = _instanceIntersects.length; i < l; i++) {\n      const intersect = _instanceIntersects[i];\n      intersect.instanceId = instanceId;\n      intersect.object = this;\n      intersects.push(intersect);\n    }\n\n    _instanceIntersects.length = 0;\n  }\n\n}\n\nconst globalContext = /*@__PURE__*/React.createContext(null);\nconst parentMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst instanceMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst tempMatrix = /*@__PURE__*/new THREE.Matrix4();\nconst translation = /*@__PURE__*/new THREE.Vector3();\nconst rotation = /*@__PURE__*/new THREE.Quaternion();\nconst scale = /*@__PURE__*/new THREE.Vector3();\nconst Instance = /*#__PURE__*/React.forwardRef(({\n  context,\n  children,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    PositionMesh\n  }), []);\n  const group = React.useRef();\n  const {\n    subscribe,\n    getParent\n  } = React.useContext(context || globalContext);\n  React.useLayoutEffect(() => subscribe(group), []);\n  return /*#__PURE__*/React.createElement(\"positionMesh\", _extends({\n    instance: getParent(),\n    instanceKey: group,\n    ref: mergeRefs([ref, group])\n  }, props), children);\n});\nconst Instances = /*#__PURE__*/React.forwardRef(({\n  children,\n  range,\n  limit = 1000,\n  frames = Infinity,\n  ...props\n}, ref) => {\n  const [{\n    context,\n    instance\n  }] = React.useState(() => {\n    const context = /*#__PURE__*/React.createContext(null);\n    return {\n      context,\n      instance: /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(Instance, _extends({\n        context: context\n      }, props, {\n        ref: ref\n      })))\n    };\n  });\n  const parentRef = React.useRef(null);\n  const [instances, setInstances] = React.useState([]);\n  const [[matrices, colors]] = React.useState(() => {\n    const mArray = new Float32Array(limit * 16);\n\n    for (let i = 0; i < limit; i++) tempMatrix.identity().toArray(mArray, i * 16);\n\n    return [mArray, new Float32Array([...new Array(limit * 3)].map(() => 1))];\n  });\n  React.useEffect(() => {\n    // We might be a frame too late? 🤷‍♂️\n    parentRef.current.instanceMatrix.needsUpdate = true;\n  });\n  let count = 0;\n  let updateRange = 0;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      parentRef.current.updateMatrix();\n      parentRef.current.updateMatrixWorld();\n      parentMatrix.copy(parentRef.current.matrixWorld).invert();\n      updateRange = Math.min(limit, range !== undefined ? range : limit, instances.length);\n      parentRef.current.count = updateRange;\n      parentRef.current.instanceMatrix.updateRange.count = updateRange * 16;\n      parentRef.current.instanceColor.updateRange.count = updateRange * 3;\n\n      for (let i = 0; i < instances.length; i++) {\n        const instance = instances[i].current; // Multiply the inverse of the InstancedMesh world matrix or else\n        // Instances will be double-transformed if <Instances> isn't at identity\n\n        instance.matrixWorld.decompose(translation, rotation, scale);\n        instanceMatrix.compose(translation, rotation, scale).premultiply(parentMatrix);\n        instanceMatrix.toArray(matrices, i * 16);\n        parentRef.current.instanceMatrix.needsUpdate = true;\n        instance.color.toArray(colors, i * 3);\n        parentRef.current.instanceColor.needsUpdate = true;\n      }\n\n      count++;\n    }\n  });\n  const api = React.useMemo(() => ({\n    getParent: () => parentRef,\n    subscribe: ref => {\n      setInstances(instances => [...instances, ref]);\n      return () => setInstances(instances => instances.filter(item => item.current !== ref.current));\n    }\n  }), []);\n  return /*#__PURE__*/React.createElement(\"instancedMesh\", _extends({\n    userData: {\n      instances\n    },\n    matrixAutoUpdate: false,\n    ref: mergeRefs([ref, parentRef]),\n    args: [null, null, 0],\n    raycast: () => null\n  }, props), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceMatrix\",\n    count: matrices.length / 16,\n    array: matrices,\n    itemSize: 16,\n    usage: THREE.DynamicDrawUsage\n  }), /*#__PURE__*/React.createElement(\"instancedBufferAttribute\", {\n    attach: \"instanceColor\",\n    count: colors.length / 3,\n    array: colors,\n    itemSize: 3,\n    usage: THREE.DynamicDrawUsage\n  }), typeof children === 'function' ? /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children(instance)) : /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children));\n});\nconst Merged = /*#__PURE__*/React.forwardRef(function Merged({\n  meshes,\n  children,\n  ...props\n}, ref) {\n  const isArray = Array.isArray(meshes); // Filter out meshes from collections, which may contain non-meshes\n\n  if (!isArray) for (const key of Object.keys(meshes)) if (!meshes[key].isMesh) delete meshes[key];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, /*#__PURE__*/React.createElement(Composer, {\n    components: (isArray ? meshes : Object.values(meshes)).map(({\n      geometry,\n      material\n    }) => /*#__PURE__*/React.createElement(Instances, _extends({\n      key: geometry.uuid,\n      geometry: geometry,\n      material: material\n    }, props)))\n  }, args => isArray ? children(...args) : children(Object.keys(meshes).filter(key => meshes[key].isMesh).reduce((acc, key, i) => ({ ...acc,\n    [key]: args[i]\n  }), {}))));\n});\n\nexport { Instance, Instances, Merged };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,QAAQ,MAAM,gBAAgB;AAErC,MAAMC,oBAAoB,GAAG,aAAa,IAAIN,KAAK,CAACO,OAAO,CAAC,CAAC;AAE7D,MAAMC,oBAAoB,GAAG,aAAa,IAAIR,KAAK,CAACO,OAAO,CAAC,CAAC;AAE7D,MAAME,mBAAmB,GAAG,EAAE;AAE9B,MAAMC,KAAK,GAAG,aAAa,IAAIV,KAAK,CAACW,IAAI,CAAC,CAAC;AAE3C,MAAMC,YAAY,SAASZ,KAAK,CAACa,KAAK,CAAC;EACrCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,IAAIf,KAAK,CAACgB,KAAK,CAAC,OAAO,CAAC;IACrC,IAAI,CAACC,QAAQ,GAAG;MACdC,OAAO,EAAEC;IACX,CAAC;IACD,IAAI,CAACC,WAAW,GAAG;MACjBF,OAAO,EAAEC;IACX,CAAC;EACH,CAAC,CAAC;;EAGF,IAAIE,QAAQA,CAAA,EAAG;IACb,IAAIC,qBAAqB;IAEzB,OAAO,CAACA,qBAAqB,GAAG,IAAI,CAACL,QAAQ,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,qBAAqB,CAACD,QAAQ;EAC1G,CAAC,CAAC;;EAGFE,OAAOA,CAACC,SAAS,EAAEC,UAAU,EAAE;IAC7B,MAAMC,MAAM,GAAG,IAAI,CAACT,QAAQ,CAACC,OAAO;IACpC,IAAI,CAACQ,MAAM,EAAE;IACb,IAAI,CAACA,MAAM,CAACL,QAAQ,IAAI,CAACK,MAAM,CAACC,QAAQ,EAAE;IAC1CjB,KAAK,CAACW,QAAQ,GAAGK,MAAM,CAACL,QAAQ;IAChC,MAAMO,WAAW,GAAGF,MAAM,CAACE,WAAW;IACtC,MAAMC,UAAU,GAAGH,MAAM,CAACI,QAAQ,CAACC,SAAS,CAACC,OAAO,CAAC,IAAI,CAACZ,WAAW,CAAC,CAAC,CAAC;;IAExE,IAAIS,UAAU,KAAK,CAAC,CAAC,IAAIA,UAAU,GAAGH,MAAM,CAACO,KAAK,EAAE,OAAO,CAAC;;IAE5DP,MAAM,CAACQ,WAAW,CAACL,UAAU,EAAEvB,oBAAoB,CAAC;IAEpDE,oBAAoB,CAAC2B,gBAAgB,CAACP,WAAW,EAAEtB,oBAAoB,CAAC,CAAC,CAAC;;IAG1EI,KAAK,CAACkB,WAAW,GAAGpB,oBAAoB,CAAC,CAAC;;IAE1C,IAAIkB,MAAM,CAACC,QAAQ,YAAY3B,KAAK,CAACoC,QAAQ,EAAE1B,KAAK,CAACiB,QAAQ,CAACU,IAAI,GAAGX,MAAM,CAACC,QAAQ,CAACU,IAAI,CAAC,KAAK3B,KAAK,CAACiB,QAAQ,CAACU,IAAI,GAAGX,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACU,IAAI;IAE5I3B,KAAK,CAACa,OAAO,CAACC,SAAS,EAAEf,mBAAmB,CAAC,CAAC,CAAC;;IAG/C,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG9B,mBAAmB,CAAC+B,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC1D,MAAMG,SAAS,GAAGhC,mBAAmB,CAAC6B,CAAC,CAAC;MACxCG,SAAS,CAACZ,UAAU,GAAGA,UAAU;MACjCY,SAAS,CAACC,MAAM,GAAG,IAAI;MACvBjB,UAAU,CAACkB,IAAI,CAACF,SAAS,CAAC;IAC5B;IAEAhC,mBAAmB,CAAC+B,MAAM,GAAG,CAAC;EAChC;AAEF;AAEA,MAAMI,aAAa,GAAG,aAAa3C,KAAK,CAAC4C,aAAa,CAAC,IAAI,CAAC;AAC5D,MAAMC,YAAY,GAAG,aAAa,IAAI9C,KAAK,CAACO,OAAO,CAAC,CAAC;AACrD,MAAMwC,cAAc,GAAG,aAAa,IAAI/C,KAAK,CAACO,OAAO,CAAC,CAAC;AACvD,MAAMyC,UAAU,GAAG,aAAa,IAAIhD,KAAK,CAACO,OAAO,CAAC,CAAC;AACnD,MAAM0C,WAAW,GAAG,aAAa,IAAIjD,KAAK,CAACkD,OAAO,CAAC,CAAC;AACpD,MAAMC,QAAQ,GAAG,aAAa,IAAInD,KAAK,CAACoD,UAAU,CAAC,CAAC;AACpD,MAAMC,KAAK,GAAG,aAAa,IAAIrD,KAAK,CAACkD,OAAO,CAAC,CAAC;AAC9C,MAAMI,QAAQ,GAAG,aAAarD,KAAK,CAACsD,UAAU,CAAC,CAAC;EAC9CC,OAAO;EACPC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT1D,KAAK,CAAC2D,OAAO,CAAC,MAAM1D,MAAM,CAAC;IACzBU;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAMiD,KAAK,GAAG5D,KAAK,CAAC6D,MAAM,CAAC,CAAC;EAC5B,MAAM;IACJC,SAAS;IACTC;EACF,CAAC,GAAG/D,KAAK,CAACgE,UAAU,CAACT,OAAO,IAAIZ,aAAa,CAAC;EAC9C3C,KAAK,CAACiE,eAAe,CAAC,MAAMH,SAAS,CAACF,KAAK,CAAC,EAAE,EAAE,CAAC;EACjD,OAAO,aAAa5D,KAAK,CAACkE,aAAa,CAAC,cAAc,EAAEpE,QAAQ,CAAC;IAC/DkB,QAAQ,EAAE+C,SAAS,CAAC,CAAC;IACrB5C,WAAW,EAAEyC,KAAK;IAClBF,GAAG,EAAEvD,SAAS,CAAC,CAACuD,GAAG,EAAEE,KAAK,CAAC;EAC7B,CAAC,EAAEH,KAAK,CAAC,EAAED,QAAQ,CAAC;AACtB,CAAC,CAAC;AACF,MAAMW,SAAS,GAAG,aAAanE,KAAK,CAACsD,UAAU,CAAC,CAAC;EAC/CE,QAAQ;EACRY,KAAK;EACLC,KAAK,GAAG,IAAI;EACZC,MAAM,GAAGC,QAAQ;EACjB,GAAGd;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM,CAAC;IACLH,OAAO;IACPvC;EACF,CAAC,CAAC,GAAGhB,KAAK,CAACwE,QAAQ,CAAC,MAAM;IACxB,MAAMjB,OAAO,GAAG,aAAavD,KAAK,CAAC4C,aAAa,CAAC,IAAI,CAAC;IACtD,OAAO;MACLW,OAAO;MACPvC,QAAQ,EAAE,aAAahB,KAAK,CAACsD,UAAU,CAAC,CAACG,KAAK,EAAEC,GAAG,KAAK,aAAa1D,KAAK,CAACkE,aAAa,CAACb,QAAQ,EAAEvD,QAAQ,CAAC;QAC1GyD,OAAO,EAAEA;MACX,CAAC,EAAEE,KAAK,EAAE;QACRC,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;IACL,CAAC;EACH,CAAC,CAAC;EACF,MAAMe,SAAS,GAAGzE,KAAK,CAAC6D,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAAC/B,SAAS,EAAE4C,YAAY,CAAC,GAAG1E,KAAK,CAACwE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC,CAACG,QAAQ,EAAEC,MAAM,CAAC,CAAC,GAAG5E,KAAK,CAACwE,QAAQ,CAAC,MAAM;IAChD,MAAMK,MAAM,GAAG,IAAIC,YAAY,CAACT,KAAK,GAAG,EAAE,CAAC;IAE3C,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAEU,UAAU,CAACgC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAACH,MAAM,EAAExC,CAAC,GAAG,EAAE,CAAC;IAE7E,OAAO,CAACwC,MAAM,EAAE,IAAIC,YAAY,CAAC,CAAC,GAAG,IAAIG,KAAK,CAACZ,KAAK,GAAG,CAAC,CAAC,CAAC,CAACa,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3E,CAAC,CAAC;EACFlF,KAAK,CAACmF,SAAS,CAAC,MAAM;IACpB;IACAV,SAAS,CAACxD,OAAO,CAAC6B,cAAc,CAACsC,WAAW,GAAG,IAAI;EACrD,CAAC,CAAC;EACF,IAAIpD,KAAK,GAAG,CAAC;EACb,IAAIqD,WAAW,GAAG,CAAC;EACnBnF,QAAQ,CAAC,MAAM;IACb,IAAIoE,MAAM,KAAKC,QAAQ,IAAIvC,KAAK,GAAGsC,MAAM,EAAE;MACzCG,SAAS,CAACxD,OAAO,CAACqE,YAAY,CAAC,CAAC;MAChCb,SAAS,CAACxD,OAAO,CAACsE,iBAAiB,CAAC,CAAC;MACrC1C,YAAY,CAAC2C,IAAI,CAACf,SAAS,CAACxD,OAAO,CAACU,WAAW,CAAC,CAAC8D,MAAM,CAAC,CAAC;MACzDJ,WAAW,GAAGK,IAAI,CAACC,GAAG,CAACtB,KAAK,EAAED,KAAK,KAAKlD,SAAS,GAAGkD,KAAK,GAAGC,KAAK,EAAEvC,SAAS,CAACS,MAAM,CAAC;MACpFkC,SAAS,CAACxD,OAAO,CAACe,KAAK,GAAGqD,WAAW;MACrCZ,SAAS,CAACxD,OAAO,CAAC6B,cAAc,CAACuC,WAAW,CAACrD,KAAK,GAAGqD,WAAW,GAAG,EAAE;MACrEZ,SAAS,CAACxD,OAAO,CAAC2E,aAAa,CAACP,WAAW,CAACrD,KAAK,GAAGqD,WAAW,GAAG,CAAC;MAEnE,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,SAAS,CAACS,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzC,MAAMrB,QAAQ,GAAGc,SAAS,CAACO,CAAC,CAAC,CAACpB,OAAO,CAAC,CAAC;QACvC;;QAEAD,QAAQ,CAACW,WAAW,CAACkE,SAAS,CAAC7C,WAAW,EAAEE,QAAQ,EAAEE,KAAK,CAAC;QAC5DN,cAAc,CAACgD,OAAO,CAAC9C,WAAW,EAAEE,QAAQ,EAAEE,KAAK,CAAC,CAAC2C,WAAW,CAAClD,YAAY,CAAC;QAC9EC,cAAc,CAACkC,OAAO,CAACL,QAAQ,EAAEtC,CAAC,GAAG,EAAE,CAAC;QACxCoC,SAAS,CAACxD,OAAO,CAAC6B,cAAc,CAACsC,WAAW,GAAG,IAAI;QACnDpE,QAAQ,CAACF,KAAK,CAACkE,OAAO,CAACJ,MAAM,EAAEvC,CAAC,GAAG,CAAC,CAAC;QACrCoC,SAAS,CAACxD,OAAO,CAAC2E,aAAa,CAACR,WAAW,GAAG,IAAI;MACpD;MAEApD,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,MAAMgE,GAAG,GAAGhG,KAAK,CAAC2D,OAAO,CAAC,OAAO;IAC/BI,SAAS,EAAEA,CAAA,KAAMU,SAAS;IAC1BX,SAAS,EAAEJ,GAAG,IAAI;MAChBgB,YAAY,CAAC5C,SAAS,IAAI,CAAC,GAAGA,SAAS,EAAE4B,GAAG,CAAC,CAAC;MAC9C,OAAO,MAAMgB,YAAY,CAAC5C,SAAS,IAAIA,SAAS,CAACmE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjF,OAAO,KAAKyC,GAAG,CAACzC,OAAO,CAAC,CAAC;IAChG;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAO,aAAajB,KAAK,CAACkE,aAAa,CAAC,eAAe,EAAEpE,QAAQ,CAAC;IAChE+B,QAAQ,EAAE;MACRC;IACF,CAAC;IACDqE,gBAAgB,EAAE,KAAK;IACvBzC,GAAG,EAAEvD,SAAS,CAAC,CAACuD,GAAG,EAAEe,SAAS,CAAC,CAAC;IAChC2B,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IACrB9E,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAEmC,KAAK,CAAC,EAAE,aAAazD,KAAK,CAACkE,aAAa,CAAC,0BAA0B,EAAE;IACtEmC,MAAM,EAAE,gBAAgB;IACxBrE,KAAK,EAAE2C,QAAQ,CAACpC,MAAM,GAAG,EAAE;IAC3B+D,KAAK,EAAE3B,QAAQ;IACf4B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAEzG,KAAK,CAAC0G;EACf,CAAC,CAAC,EAAE,aAAazG,KAAK,CAACkE,aAAa,CAAC,0BAA0B,EAAE;IAC/DmC,MAAM,EAAE,eAAe;IACvBrE,KAAK,EAAE4C,MAAM,CAACrC,MAAM,GAAG,CAAC;IACxB+D,KAAK,EAAE1B,MAAM;IACb2B,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAEzG,KAAK,CAAC0G;EACf,CAAC,CAAC,EAAE,OAAOjD,QAAQ,KAAK,UAAU,GAAG,aAAaxD,KAAK,CAACkE,aAAa,CAACX,OAAO,CAACmD,QAAQ,EAAE;IACtFC,KAAK,EAAEX;EACT,CAAC,EAAExC,QAAQ,CAACxC,QAAQ,CAAC,CAAC,GAAG,aAAahB,KAAK,CAACkE,aAAa,CAACvB,aAAa,CAAC+D,QAAQ,EAAE;IAChFC,KAAK,EAAEX;EACT,CAAC,EAAExC,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAMoD,MAAM,GAAG,aAAa5G,KAAK,CAACsD,UAAU,CAAC,SAASsD,MAAMA,CAAC;EAC3DC,MAAM;EACNrD,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMoD,OAAO,GAAG7B,KAAK,CAAC6B,OAAO,CAACD,MAAM,CAAC,CAAC,CAAC;;EAEvC,IAAI,CAACC,OAAO,EAAE,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,EAAE,IAAI,CAACA,MAAM,CAACE,GAAG,CAAC,CAACG,MAAM,EAAE,OAAOL,MAAM,CAACE,GAAG,CAAC;EAChG,OAAO,aAAa/G,KAAK,CAACkE,aAAa,CAAC,OAAO,EAAE;IAC/CR,GAAG,EAAEA;EACP,CAAC,EAAE,aAAa1D,KAAK,CAACkE,aAAa,CAAC9D,QAAQ,EAAE;IAC5C+G,UAAU,EAAE,CAACL,OAAO,GAAGD,MAAM,GAAGG,MAAM,CAACI,MAAM,CAACP,MAAM,CAAC,EAAE3B,GAAG,CAAC,CAAC;MAC1D9D,QAAQ;MACRM;IACF,CAAC,KAAK,aAAa1B,KAAK,CAACkE,aAAa,CAACC,SAAS,EAAErE,QAAQ,CAAC;MACzDiH,GAAG,EAAE3F,QAAQ,CAACiG,IAAI;MAClBjG,QAAQ,EAAEA,QAAQ;MAClBM,QAAQ,EAAEA;IACZ,CAAC,EAAE+B,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE2C,IAAI,IAAIU,OAAO,GAAGtD,QAAQ,CAAC,GAAG4C,IAAI,CAAC,GAAG5C,QAAQ,CAACwD,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACZ,MAAM,CAACc,GAAG,IAAIF,MAAM,CAACE,GAAG,CAAC,CAACG,MAAM,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAER,GAAG,EAAE1E,CAAC,MAAM;IAAE,GAAGkF,GAAG;IACvI,CAACR,GAAG,GAAGX,IAAI,CAAC/D,CAAC;EACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASgB,QAAQ,EAAEc,SAAS,EAAEyC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}