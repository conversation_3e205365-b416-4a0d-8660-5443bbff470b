{"ast": null, "code": "import * as THREE from \"three\";\nfunction createText(message, height) {\n  const canvas = document.createElement(\"canvas\");\n  const context = canvas.getContext(\"2d\");\n  let metrics = null;\n  const textHeight = 100;\n  context.font = \"normal \" + textHeight + \"px Arial\";\n  metrics = context.measureText(message);\n  const textWidth = metrics.width;\n  canvas.width = textWidth;\n  canvas.height = textHeight;\n  context.font = \"normal \" + textHeight + \"px Arial\";\n  context.textAlign = \"center\";\n  context.textBaseline = \"middle\";\n  context.fillStyle = \"#ffffff\";\n  context.fillText(message, textWidth / 2, textHeight / 2);\n  const texture = new THREE.Texture(canvas);\n  texture.needsUpdate = true;\n  const material = new THREE.MeshBasicMaterial({\n    color: 16777215,\n    side: THREE.DoubleSide,\n    map: texture,\n    transparent: true\n  });\n  const geometry = new THREE.PlaneGeometry(height * textWidth / textHeight, height);\n  const plane = new THREE.Mesh(geometry, material);\n  return plane;\n}\nexport { createText };", "map": {"version": 3, "names": ["createText", "message", "height", "canvas", "document", "createElement", "context", "getContext", "metrics", "textHeight", "font", "measureText", "textWidth", "width", "textAlign", "textBaseline", "fillStyle", "fillText", "texture", "THREE", "Texture", "needsUpdate", "material", "MeshBasicMaterial", "color", "side", "DoubleSide", "map", "transparent", "geometry", "PlaneGeometry", "plane", "<PERSON><PERSON>"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\webxr\\Text2D.js"], "sourcesContent": ["import * as THREE from 'three'\n\nfunction createText(message, height) {\n  const canvas = document.createElement('canvas')\n  const context = canvas.getContext('2d')\n  let metrics = null\n  const textHeight = 100\n  context.font = 'normal ' + textHeight + 'px Arial'\n  metrics = context.measureText(message)\n  const textWidth = metrics.width\n  canvas.width = textWidth\n  canvas.height = textHeight\n  context.font = 'normal ' + textHeight + 'px Arial'\n  context.textAlign = 'center'\n  context.textBaseline = 'middle'\n  context.fillStyle = '#ffffff'\n  context.fillText(message, textWidth / 2, textHeight / 2)\n\n  const texture = new THREE.Texture(canvas)\n  texture.needsUpdate = true\n  //var spriteAlignment = new THREE.Vector2(0,0) ;\n  const material = new THREE.MeshBasicMaterial({\n    color: 0xffffff,\n    side: THREE.DoubleSide,\n    map: texture,\n    transparent: true,\n  })\n  const geometry = new THREE.PlaneGeometry((height * textWidth) / textHeight, height)\n  const plane = new THREE.Mesh(geometry, material)\n  return plane\n}\n\nexport { createText }\n"], "mappings": ";AAEA,SAASA,WAAWC,OAAA,EAASC,MAAA,EAAQ;EACnC,MAAMC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;EAC9C,MAAMC,OAAA,GAAUH,MAAA,CAAOI,UAAA,CAAW,IAAI;EACtC,IAAIC,OAAA,GAAU;EACd,MAAMC,UAAA,GAAa;EACnBH,OAAA,CAAQI,IAAA,GAAO,YAAYD,UAAA,GAAa;EACxCD,OAAA,GAAUF,OAAA,CAAQK,WAAA,CAAYV,OAAO;EACrC,MAAMW,SAAA,GAAYJ,OAAA,CAAQK,KAAA;EAC1BV,MAAA,CAAOU,KAAA,GAAQD,SAAA;EACfT,MAAA,CAAOD,MAAA,GAASO,UAAA;EAChBH,OAAA,CAAQI,IAAA,GAAO,YAAYD,UAAA,GAAa;EACxCH,OAAA,CAAQQ,SAAA,GAAY;EACpBR,OAAA,CAAQS,YAAA,GAAe;EACvBT,OAAA,CAAQU,SAAA,GAAY;EACpBV,OAAA,CAAQW,QAAA,CAAShB,OAAA,EAASW,SAAA,GAAY,GAAGH,UAAA,GAAa,CAAC;EAEvD,MAAMS,OAAA,GAAU,IAAIC,KAAA,CAAMC,OAAA,CAAQjB,MAAM;EACxCe,OAAA,CAAQG,WAAA,GAAc;EAEtB,MAAMC,QAAA,GAAW,IAAIH,KAAA,CAAMI,iBAAA,CAAkB;IAC3CC,KAAA,EAAO;IACPC,IAAA,EAAMN,KAAA,CAAMO,UAAA;IACZC,GAAA,EAAKT,OAAA;IACLU,WAAA,EAAa;EACjB,CAAG;EACD,MAAMC,QAAA,GAAW,IAAIV,KAAA,CAAMW,aAAA,CAAe5B,MAAA,GAASU,SAAA,GAAaH,UAAA,EAAYP,MAAM;EAClF,MAAM6B,KAAA,GAAQ,IAAIZ,KAAA,CAAMa,IAAA,CAAKH,QAAA,EAAUP,QAAQ;EAC/C,OAAOS,KAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}