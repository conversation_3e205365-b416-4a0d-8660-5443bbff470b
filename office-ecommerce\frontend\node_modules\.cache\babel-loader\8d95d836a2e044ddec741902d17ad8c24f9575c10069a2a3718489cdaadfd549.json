{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\DashboardOverview.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './DashboardOverview.css';\nimport './AdminComponents.css';\nimport { InventoryIcon, OrdersIcon, UsersIcon, WarningIcon, InfoIcon, AnalyticsIcon } from '../admin/icons/AdminIcons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardOverview = () => {\n  _s();\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      // Mock data for now - will be replaced with API call\n      const mockData = {\n        overview: {\n          totalProducts: 150,\n          totalOrders: 1250,\n          totalRevenue: 125000.00,\n          totalCustomers: 450,\n          lowStockItems: 8,\n          pendingOrders: 25\n        },\n        recentOrders: [{\n          id: 'ORD001',\n          orderNumber: 'ORD-2024-001',\n          customerName: 'John Doe',\n          total: 696.98,\n          status: 'Processing',\n          date: new Date().toISOString()\n        }, {\n          id: 'ORD002',\n          orderNumber: 'ORD-2024-002',\n          customerName: 'Jane Smith',\n          total: 1299.99,\n          status: 'Shipped',\n          date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()\n        }],\n        topProducts: [{\n          id: 'PROD001',\n          name: 'Executive Office Chair',\n          sales: 45,\n          revenue: 13499.55\n        }, {\n          id: 'PROD002',\n          name: 'Standing Desk',\n          sales: 32,\n          revenue: 19199.68\n        }],\n        inventoryAlerts: [{\n          productName: 'Standing Desk',\n          currentStock: 8,\n          reorderLevel: 15,\n          status: 'Low Stock'\n        }, {\n          productName: 'Conference Table',\n          currentStock: 3,\n          reorderLevel: 5,\n          status: 'Critical'\n        }]\n      };\n      setDashboardData(mockData);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-PH', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'processing':\n        return '#f39c12';\n      case 'shipped':\n        return '#3498db';\n      case 'delivered':\n        return '#27ae60';\n      case 'cancelled':\n        return '#e74c3c';\n      case 'low stock':\n        return '#f39c12';\n      case 'critical':\n        return '#e74c3c';\n      default:\n        return '#95a5a6';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this);\n  }\n  if (!dashboardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-error\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Failed to load dashboard data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-overview\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Dashboard Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Welcome to your inventory management dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"metrics-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(InventoryIcon, {\n            color: \"#F0B21B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.overview.totalProducts\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(OrdersIcon, {\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.overview.totalOrders\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n            color: \"#10B981\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: formatCurrency(dashboardData.overview.totalRevenue)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Revenue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(UsersIcon, {\n            color: \"#8B5CF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.overview.totalCustomers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total Customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"#F59E0B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.overview.lowStockItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Low Stock Items\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metric-card pending\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-icon\",\n          children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"#6B7280\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: dashboardData.overview.pendingOrders\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Pending Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"admin-card-title\",\n            children: \"Recent Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-primary\",\n            children: \"View All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"admin-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Order Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Customer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Total\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: dashboardData.recentOrders.map(order => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: order.orderNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: order.customerName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: formatCurrency(order.total)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"status-badge\",\n                    style: {\n                      backgroundColor: getStatusColor(order.status)\n                    },\n                    children: order.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: formatDate(order.date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this)]\n              }, order.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"admin-card-title\",\n            children: \"Top Selling Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-list\",\n          children: dashboardData.topProducts.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [product.sales, \" units sold\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-revenue\",\n              children: formatCurrency(product.revenue)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"admin-card-title\",\n            children: \"Inventory Alerts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary\",\n            children: \"Manage Inventory\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alerts-list\",\n          children: dashboardData.inventoryAlerts.map((alert, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: alert.productName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Current Stock: \", alert.currentStock, \" | Reorder Level: \", alert.reorderLevel]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-badge\",\n              style: {\n                backgroundColor: getStatusColor(alert.status)\n              },\n              children: alert.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(DashboardOverview, \"z1DTpCBdm98Lma1s3czcX10QOec=\");\n_c = DashboardOverview;\nexport default DashboardOverview;\nvar _c;\n$RefreshReg$(_c, \"DashboardOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "InventoryIcon", "OrdersIcon", "UsersIcon", "WarningIcon", "InfoIcon", "AnalyticsIcon", "jsxDEV", "_jsxDEV", "DashboardOverview", "_s", "dashboardData", "setDashboardData", "loading", "setLoading", "fetchDashboardData", "mockData", "overview", "totalProducts", "totalOrders", "totalRevenue", "totalCustomers", "lowStockItems", "pendingOrders", "recentOrders", "id", "orderNumber", "customerName", "total", "status", "date", "Date", "toISOString", "now", "topProducts", "name", "sales", "revenue", "inventoryAlerts", "productName", "currentStock", "reorderLevel", "error", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getStatusColor", "toLowerCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "map", "order", "backgroundColor", "product", "alert", "index", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/DashboardOverview.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './DashboardOverview.css';\nimport './AdminComponents.css';\nimport {\n  InventoryIcon,\n  OrdersIcon,\n  UsersIcon,\n  WarningIcon,\n  InfoIcon,\n  AnalyticsIcon\n} from '../admin/icons/AdminIcons';\n\nconst DashboardOverview = () => {\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      // Mock data for now - will be replaced with API call\n      const mockData = {\n        overview: {\n          totalProducts: 150,\n          totalOrders: 1250,\n          totalRevenue: 125000.00,\n          totalCustomers: 450,\n          lowStockItems: 8,\n          pendingOrders: 25\n        },\n        recentOrders: [\n          {\n            id: 'ORD001',\n            orderNumber: 'ORD-2024-001',\n            customerName: '<PERSON>',\n            total: 696.98,\n            status: 'Processing',\n            date: new Date().toISOString()\n          },\n          {\n            id: 'ORD002',\n            orderNumber: 'ORD-2024-002',\n            customerName: '<PERSON>',\n            total: 1299.99,\n            status: 'Shipped',\n            date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()\n          }\n        ],\n        topProducts: [\n          {\n            id: 'PROD001',\n            name: 'Executive Office Chair',\n            sales: 45,\n            revenue: 13499.55\n          },\n          {\n            id: 'PROD002',\n            name: 'Standing Desk',\n            sales: 32,\n            revenue: 19199.68\n          }\n        ],\n        inventoryAlerts: [\n          {\n            productName: 'Standing Desk',\n            currentStock: 8,\n            reorderLevel: 15,\n            status: 'Low Stock'\n          },\n          {\n            productName: 'Conference Table',\n            currentStock: 3,\n            reorderLevel: 5,\n            status: 'Critical'\n          }\n        ]\n      };\n\n      setDashboardData(mockData);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-PH', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'processing':\n        return '#f39c12';\n      case 'shipped':\n        return '#3498db';\n      case 'delivered':\n        return '#27ae60';\n      case 'cancelled':\n        return '#e74c3c';\n      case 'low stock':\n        return '#f39c12';\n      case 'critical':\n        return '#e74c3c';\n      default:\n        return '#95a5a6';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dashboard-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading dashboard...</p>\n      </div>\n    );\n  }\n\n  if (!dashboardData) {\n    return (\n      <div className=\"dashboard-error\">\n        <p>Failed to load dashboard data</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"dashboard-overview\">\n      <div className=\"dashboard-header\">\n        <h1>Dashboard Overview</h1>\n        <p>Welcome to your inventory management dashboard</p>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"metrics-grid\">\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">\n            <InventoryIcon color=\"#F0B21B\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{dashboardData.overview.totalProducts}</h3>\n            <p>Total Products</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">\n            <OrdersIcon color=\"#3B82F6\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{dashboardData.overview.totalOrders}</h3>\n            <p>Total Orders</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">\n            <AnalyticsIcon color=\"#10B981\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{formatCurrency(dashboardData.overview.totalRevenue)}</h3>\n            <p>Total Revenue</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card\">\n          <div className=\"metric-icon\">\n            <UsersIcon color=\"#8B5CF6\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{dashboardData.overview.totalCustomers}</h3>\n            <p>Total Customers</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card alert\">\n          <div className=\"metric-icon\">\n            <WarningIcon color=\"#F59E0B\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{dashboardData.overview.lowStockItems}</h3>\n            <p>Low Stock Items</p>\n          </div>\n        </div>\n\n        <div className=\"metric-card pending\">\n          <div className=\"metric-icon\">\n            <InfoIcon color=\"#6B7280\" />\n          </div>\n          <div className=\"metric-content\">\n            <h3>{dashboardData.overview.pendingOrders}</h3>\n            <p>Pending Orders</p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"dashboard-content\">\n        {/* Recent Orders */}\n        <div className=\"admin-card\">\n          <div className=\"admin-card-header\">\n            <h2 className=\"admin-card-title\">Recent Orders</h2>\n            <button className=\"admin-btn admin-btn-primary\">View All</button>\n          </div>\n          <div className=\"table-container\">\n            <table className=\"admin-table\">\n              <thead>\n                <tr>\n                  <th>Order Number</th>\n                  <th>Customer</th>\n                  <th>Total</th>\n                  <th>Status</th>\n                  <th>Date</th>\n                </tr>\n              </thead>\n              <tbody>\n                {dashboardData.recentOrders.map(order => (\n                  <tr key={order.id}>\n                    <td>{order.orderNumber}</td>\n                    <td>{order.customerName}</td>\n                    <td>{formatCurrency(order.total)}</td>\n                    <td>\n                      <span \n                        className=\"status-badge\"\n                        style={{ backgroundColor: getStatusColor(order.status) }}\n                      >\n                        {order.status}\n                      </span>\n                    </td>\n                    <td>{formatDate(order.date)}</td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Top Products */}\n        <div className=\"admin-card\">\n          <div className=\"admin-card-header\">\n            <h2 className=\"admin-card-title\">Top Selling Products</h2>\n          </div>\n          <div className=\"products-list\">\n            {dashboardData.topProducts.map(product => (\n              <div key={product.id} className=\"product-item\">\n                <div className=\"product-info\">\n                  <h4>{product.name}</h4>\n                  <p>{product.sales} units sold</p>\n                </div>\n                <div className=\"product-revenue\">\n                  {formatCurrency(product.revenue)}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Inventory Alerts */}\n        <div className=\"admin-card\">\n          <div className=\"admin-card-header\">\n            <h2 className=\"admin-card-title\">Inventory Alerts</h2>\n            <button className=\"admin-btn admin-btn-secondary\">Manage Inventory</button>\n          </div>\n          <div className=\"alerts-list\">\n            {dashboardData.inventoryAlerts.map((alert, index) => (\n              <div key={index} className=\"alert-item\">\n                <div className=\"alert-info\">\n                  <h4>{alert.productName}</h4>\n                  <p>Current Stock: {alert.currentStock} | Reorder Level: {alert.reorderLevel}</p>\n                </div>\n                <span \n                  className=\"status-badge\"\n                  style={{ backgroundColor: getStatusColor(alert.status) }}\n                >\n                  {alert.status}\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardOverview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,yBAAyB;AAChC,OAAO,uBAAuB;AAC9B,SACEC,aAAa,EACbC,UAAU,EACVC,SAAS,EACTC,WAAW,EACXC,QAAQ,EACRC,aAAa,QACR,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACde,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG;QACfC,QAAQ,EAAE;UACRC,aAAa,EAAE,GAAG;UAClBC,WAAW,EAAE,IAAI;UACjBC,YAAY,EAAE,SAAS;UACvBC,cAAc,EAAE,GAAG;UACnBC,aAAa,EAAE,CAAC;UAChBC,aAAa,EAAE;QACjB,CAAC;QACDC,YAAY,EAAE,CACZ;UACEC,EAAE,EAAE,QAAQ;UACZC,WAAW,EAAE,cAAc;UAC3BC,YAAY,EAAE,UAAU;UACxBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,YAAY;UACpBC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QAC/B,CAAC,EACD;UACEP,EAAE,EAAE,QAAQ;UACZC,WAAW,EAAE,cAAc;UAC3BC,YAAY,EAAE,YAAY;UAC1BC,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,SAAS;UACjBC,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC;QAC/D,CAAC,CACF;QACDE,WAAW,EAAE,CACX;UACET,EAAE,EAAE,SAAS;UACbU,IAAI,EAAE,wBAAwB;UAC9BC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE;QACX,CAAC,EACD;UACEZ,EAAE,EAAE,SAAS;UACbU,IAAI,EAAE,eAAe;UACrBC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE;QACX,CAAC,CACF;QACDC,eAAe,EAAE,CACf;UACEC,WAAW,EAAE,eAAe;UAC5BC,YAAY,EAAE,CAAC;UACfC,YAAY,EAAE,EAAE;UAChBZ,MAAM,EAAE;QACV,CAAC,EACD;UACEU,WAAW,EAAE,kBAAkB;UAC/BC,YAAY,EAAE,CAAC;UACfC,YAAY,EAAE,CAAC;UACfZ,MAAM,EAAE;QACV,CAAC;MAEL,CAAC;MAEDjB,gBAAgB,CAACI,QAAQ,CAAC;MAC1BF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIrB,IAAI,CAACqB,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAI5B,MAAM,IAAK;IACjC,QAAQA,MAAM,CAAC6B,WAAW,CAAC,CAAC;MAC1B,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,IAAI7C,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKmD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpD,OAAA;QAAKmD,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCxD,OAAA;QAAAoD,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAEV;EAEA,IAAI,CAACrD,aAAa,EAAE;IAClB,oBACEH,OAAA;MAAKmD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BpD,OAAA;QAAAoD,QAAA,EAAG;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAEV;EAEA,oBACExD,OAAA;IAAKmD,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCpD,OAAA;MAAKmD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BpD,OAAA;QAAAoD,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BxD,OAAA;QAAAoD,QAAA,EAAG;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAGNxD,OAAA;MAAKmD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BpD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpD,OAAA,CAACP,aAAa;YAACgE,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAKjD,aAAa,CAACM,QAAQ,CAACC;UAAa;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CxD,OAAA;YAAAoD,QAAA,EAAG;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpD,OAAA,CAACN,UAAU;YAAC+D,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAKjD,aAAa,CAACM,QAAQ,CAACE;UAAW;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7CxD,OAAA;YAAAoD,QAAA,EAAG;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpD,OAAA,CAACF,aAAa;YAAC2D,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAKhB,cAAc,CAACjC,aAAa,CAACM,QAAQ,CAACG,YAAY;UAAC;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9DxD,OAAA;YAAAoD,QAAA,EAAG;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpD,OAAA,CAACL,SAAS;YAAC8D,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAKjD,aAAa,CAACM,QAAQ,CAACI;UAAc;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDxD,OAAA;YAAAoD,QAAA,EAAG;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCpD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpD,OAAA,CAACJ,WAAW;YAAC6D,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAKjD,aAAa,CAACM,QAAQ,CAACK;UAAa;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CxD,OAAA;YAAAoD,QAAA,EAAG;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCpD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BpD,OAAA,CAACH,QAAQ;YAAC4D,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,EAAKjD,aAAa,CAACM,QAAQ,CAACM;UAAa;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CxD,OAAA;YAAAoD,QAAA,EAAG;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxD,OAAA;MAAKmD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCpD,OAAA;QAAKmD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBpD,OAAA;UAAKmD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCpD,OAAA;YAAImD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDxD,OAAA;YAAQmD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BpD,OAAA;YAAOmD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC5BpD,OAAA;cAAAoD,QAAA,eACEpD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAAoD,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrBxD,OAAA;kBAAAoD,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBxD,OAAA;kBAAAoD,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdxD,OAAA;kBAAAoD,QAAA,EAAI;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfxD,OAAA;kBAAAoD,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRxD,OAAA;cAAAoD,QAAA,EACGjD,aAAa,CAACa,YAAY,CAAC0C,GAAG,CAACC,KAAK,iBACnC3D,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAAoD,QAAA,EAAKO,KAAK,CAACzC;gBAAW;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5BxD,OAAA;kBAAAoD,QAAA,EAAKO,KAAK,CAACxC;gBAAY;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7BxD,OAAA;kBAAAoD,QAAA,EAAKhB,cAAc,CAACuB,KAAK,CAACvC,KAAK;gBAAC;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtCxD,OAAA;kBAAAoD,QAAA,eACEpD,OAAA;oBACEmD,SAAS,EAAC,cAAc;oBACxBX,KAAK,EAAE;sBAAEoB,eAAe,EAAEX,cAAc,CAACU,KAAK,CAACtC,MAAM;oBAAE,CAAE;oBAAA+B,QAAA,EAExDO,KAAK,CAACtC;kBAAM;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLxD,OAAA;kBAAAoD,QAAA,EAAKT,UAAU,CAACgB,KAAK,CAACrC,IAAI;gBAAC;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAZ1BG,KAAK,CAAC1C,EAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAab,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAKmD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBpD,OAAA;UAAKmD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCpD,OAAA;YAAImD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BjD,aAAa,CAACuB,WAAW,CAACgC,GAAG,CAACG,OAAO,iBACpC7D,OAAA;YAAsBmD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC5CpD,OAAA;cAAKmD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BpD,OAAA;gBAAAoD,QAAA,EAAKS,OAAO,CAAClC;cAAI;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBxD,OAAA;gBAAAoD,QAAA,GAAIS,OAAO,CAACjC,KAAK,EAAC,aAAW;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7BhB,cAAc,CAACyB,OAAO,CAAChC,OAAO;YAAC;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA,GAPEK,OAAO,CAAC5C,EAAE;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAKmD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBpD,OAAA;UAAKmD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCpD,OAAA;YAAImD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDxD,OAAA;YAAQmD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eACNxD,OAAA;UAAKmD,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBjD,aAAa,CAAC2B,eAAe,CAAC4B,GAAG,CAAC,CAACI,KAAK,EAAEC,KAAK,kBAC9C/D,OAAA;YAAiBmD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACrCpD,OAAA;cAAKmD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpD,OAAA;gBAAAoD,QAAA,EAAKU,KAAK,CAAC/B;cAAW;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BxD,OAAA;gBAAAoD,QAAA,GAAG,iBAAe,EAACU,KAAK,CAAC9B,YAAY,EAAC,oBAAkB,EAAC8B,KAAK,CAAC7B,YAAY;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACNxD,OAAA;cACEmD,SAAS,EAAC,cAAc;cACxBX,KAAK,EAAE;gBAAEoB,eAAe,EAAEX,cAAc,CAACa,KAAK,CAACzC,MAAM;cAAE,CAAE;cAAA+B,QAAA,EAExDU,KAAK,CAACzC;YAAM;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,GAVCO,KAAK;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA3RID,iBAAiB;AAAA+D,EAAA,GAAjB/D,iBAAiB;AA6RvB,eAAeA,iBAAiB;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}