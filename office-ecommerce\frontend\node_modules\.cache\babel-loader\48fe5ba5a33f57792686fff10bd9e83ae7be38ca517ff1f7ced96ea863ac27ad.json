{"ast": null, "code": "import { Vector3 } from \"three\";\nconst hilbert2D = (center = new Vector3(0, 0, 0), size = 10, iterations = 1, v0 = 0, v1 = 1, v2 = 2, v3 = 3) => {\n  const half = size / 2;\n  const vec_s = [new Vector3(center.x - half, center.y, center.z - half), new Vector3(center.x - half, center.y, center.z + half), new Vector3(center.x + half, center.y, center.z + half), new Vector3(center.x + half, center.y, center.z - half)];\n  const vec = [vec_s[v0], vec_s[v1], vec_s[v2], vec_s[v3]];\n  if (0 <= --iterations) {\n    const tmp = [];\n    Array.prototype.push.apply(tmp, hilbert2D(vec[0], half, iterations, v0, v3, v2, v1));\n    Array.prototype.push.apply(tmp, hilbert2D(vec[1], half, iterations, v0, v1, v2, v3));\n    Array.prototype.push.apply(tmp, hilbert2D(vec[2], half, iterations, v0, v1, v2, v3));\n    Array.prototype.push.apply(tmp, hilbert2D(vec[3], half, iterations, v2, v1, v0, v3));\n    return tmp;\n  }\n  return vec;\n};\nconst hilbert3D = (center = new Vector3(0, 0, 0), size = 10, iterations = 1, v0 = 0, v1 = 1, v2 = 2, v3 = 3, v4 = 4, v5 = 5, v6 = 6, v7 = 7) => {\n  const half = size / 2;\n  const vec_s = [new Vector3(center.x - half, center.y + half, center.z - half), new Vector3(center.x - half, center.y + half, center.z + half), new Vector3(center.x - half, center.y - half, center.z + half), new Vector3(center.x - half, center.y - half, center.z - half), new Vector3(center.x + half, center.y - half, center.z - half), new Vector3(center.x + half, center.y - half, center.z + half), new Vector3(center.x + half, center.y + half, center.z + half), new Vector3(center.x + half, center.y + half, center.z - half)];\n  const vec = [vec_s[v0], vec_s[v1], vec_s[v2], vec_s[v3], vec_s[v4], vec_s[v5], vec_s[v6], vec_s[v7]];\n  if (--iterations >= 0) {\n    const tmp = [];\n    Array.prototype.push.apply(tmp, hilbert3D(vec[0], half, iterations, v0, v3, v4, v7, v6, v5, v2, v1));\n    Array.prototype.push.apply(tmp, hilbert3D(vec[1], half, iterations, v0, v7, v6, v1, v2, v5, v4, v3));\n    Array.prototype.push.apply(tmp, hilbert3D(vec[2], half, iterations, v0, v7, v6, v1, v2, v5, v4, v3));\n    Array.prototype.push.apply(tmp, hilbert3D(vec[3], half, iterations, v2, v3, v0, v1, v6, v7, v4, v5));\n    Array.prototype.push.apply(tmp, hilbert3D(vec[4], half, iterations, v2, v3, v0, v1, v6, v7, v4, v5));\n    Array.prototype.push.apply(tmp, hilbert3D(vec[5], half, iterations, v4, v3, v2, v5, v6, v1, v0, v7));\n    Array.prototype.push.apply(tmp, hilbert3D(vec[6], half, iterations, v4, v3, v2, v5, v6, v1, v0, v7));\n    Array.prototype.push.apply(tmp, hilbert3D(vec[7], half, iterations, v6, v5, v2, v1, v0, v3, v4, v7));\n    return tmp;\n  }\n  return vec;\n};\nconst gosper = (size = 1) => {\n  function fractalize(config) {\n    let output = \"\";\n    let input = config.axiom;\n    for (let i = 0, il = config.steps; 0 <= il ? i < il : i > il; 0 <= il ? i++ : i--) {\n      output = \"\";\n      for (let j = 0, jl = input.length; j < jl; j++) {\n        const char = input[j];\n        if (char in config.rules) {\n          output += config.rules[char];\n        } else {\n          output += char;\n        }\n      }\n      input = output;\n    }\n    return output;\n  }\n  function toPoints(config) {\n    let currX = 0;\n    let currY = 0;\n    let angle = 0;\n    const path = [0, 0, 0];\n    const fractal = config.fractal;\n    for (let i = 0, l = fractal.length; i < l; i++) {\n      const char = fractal[i];\n      if (char === \"+\") {\n        angle += config.angle;\n      } else if (char === \"-\") {\n        angle -= config.angle;\n      } else if (char === \"F\") {\n        currX += config.size * Math.cos(angle);\n        currY += -config.size * Math.sin(angle);\n        path.push(currX, currY, 0);\n      }\n    }\n    return path;\n  }\n  const gosper2 = fractalize({\n    axiom: \"A\",\n    steps: 4,\n    rules: {\n      A: \"A+BF++BF-FA--FAFA-BF+\",\n      B: \"-FA+BFBF++BF+FA--FA-B\"\n    }\n  });\n  const points = toPoints({\n    fractal: gosper2,\n    size,\n    angle: Math.PI / 3\n    // 60 degrees\n  });\n  return points;\n};\nconst GeometryUtils = {\n  hilbert3D,\n  gosper,\n  hilbert2D\n};\nexport { GeometryUtils };", "map": {"version": 3, "names": ["hilbert2D", "center", "Vector3", "size", "iterations", "v0", "v1", "v2", "v3", "half", "vec_s", "x", "y", "z", "vec", "tmp", "Array", "prototype", "push", "apply", "hilbert3D", "v4", "v5", "v6", "v7", "gosper", "fractalize", "config", "output", "input", "axiom", "i", "il", "steps", "j", "jl", "length", "char", "rules", "toPoints", "currX", "currY", "angle", "path", "fractal", "l", "Math", "cos", "sin", "gosper2", "A", "B", "points", "PI", "GeometryUtils"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\utils\\GeometryUtils.ts"], "sourcesContent": ["import { Vector3 } from 'three'\n\n/**\n * Generates 2D-Coordinates in a very fast way.\n *\n * Based on work by:\n * @link http://www.openprocessing.org/sketch/15493\n *\n * @param center     Center of Hilbert curve.\n * @param size       Total width of Hilbert curve.\n * @param iterations Number of subdivisions.\n * @param v0         Corner index -X, -Z.\n * @param v1         Corner index -X, +Z.\n * @param v2         Corner index +X, +Z.\n * @param v3         Corner index +X, -Z.\n */\nconst hilbert2D = (\n  center = new Vector3(0, 0, 0),\n  size = 10,\n  iterations = 1,\n  v0 = 0,\n  v1 = 1,\n  v2 = 2,\n  v3 = 3,\n): Vector3[] => {\n  // Default Vars\n  const half = size / 2\n  const vec_s = [\n    new Vector3(center.x - half, center.y, center.z - half),\n    new Vector3(center.x - half, center.y, center.z + half),\n    new Vector3(center.x + half, center.y, center.z + half),\n    new Vector3(center.x + half, center.y, center.z - half),\n  ]\n\n  const vec = [vec_s[v0], vec_s[v1], vec_s[v2], vec_s[v3]]\n\n  // Recurse iterations\n  if (0 <= --iterations) {\n    const tmp: Vector3[] = []\n\n    Array.prototype.push.apply(tmp, hilbert2D(vec[0], half, iterations, v0, v3, v2, v1))\n    Array.prototype.push.apply(tmp, hilbert2D(vec[1], half, iterations, v0, v1, v2, v3))\n    Array.prototype.push.apply(tmp, hilbert2D(vec[2], half, iterations, v0, v1, v2, v3))\n    Array.prototype.push.apply(tmp, hilbert2D(vec[3], half, iterations, v2, v1, v0, v3))\n\n    // Return recursive call\n    return tmp\n  }\n\n  // Return complete Hilbert Curve.\n  return vec\n}\n\n/**\n * Generates 3D-Coordinates in a very fast way.\n *\n * Based on work by:\n * @link http://www.openprocessing.org/visuals/?visualID=15599\n *\n * @param center     Center of Hilbert curve.\n * @param size       Total width of Hilbert curve.\n * @param iterations Number of subdivisions.\n * @param v0         Corner index -X, +Y, -Z.\n * @param v1         Corner index -X, +Y, +Z.\n * @param v2         Corner index -X, -Y, +Z.\n * @param v3         Corner index -X, -Y, -Z.\n * @param v4         Corner index +X, -Y, -Z.\n * @param v5         Corner index +X, -Y, +Z.\n * @param v6         Corner index +X, +Y, +Z.\n * @param v7         Corner index +X, +Y, -Z.\n */\nconst hilbert3D = (\n  center = new Vector3(0, 0, 0),\n  size = 10,\n  iterations = 1,\n  v0 = 0,\n  v1 = 1,\n  v2 = 2,\n  v3 = 3,\n  v4 = 4,\n  v5 = 5,\n  v6 = 6,\n  v7 = 7,\n): Vector3[] => {\n  // Default Vars\n  const half = size / 2\n  const vec_s = [\n    new Vector3(center.x - half, center.y + half, center.z - half),\n    new Vector3(center.x - half, center.y + half, center.z + half),\n    new Vector3(center.x - half, center.y - half, center.z + half),\n    new Vector3(center.x - half, center.y - half, center.z - half),\n    new Vector3(center.x + half, center.y - half, center.z - half),\n    new Vector3(center.x + half, center.y - half, center.z + half),\n    new Vector3(center.x + half, center.y + half, center.z + half),\n    new Vector3(center.x + half, center.y + half, center.z - half),\n  ]\n\n  const vec = [vec_s[v0], vec_s[v1], vec_s[v2], vec_s[v3], vec_s[v4], vec_s[v5], vec_s[v6], vec_s[v7]]\n\n  // Recurse iterations\n  if (--iterations >= 0) {\n    const tmp: Vector3[] = []\n\n    Array.prototype.push.apply(tmp, hilbert3D(vec[0], half, iterations, v0, v3, v4, v7, v6, v5, v2, v1))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[1], half, iterations, v0, v7, v6, v1, v2, v5, v4, v3))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[2], half, iterations, v0, v7, v6, v1, v2, v5, v4, v3))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[3], half, iterations, v2, v3, v0, v1, v6, v7, v4, v5))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[4], half, iterations, v2, v3, v0, v1, v6, v7, v4, v5))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[5], half, iterations, v4, v3, v2, v5, v6, v1, v0, v7))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[6], half, iterations, v4, v3, v2, v5, v6, v1, v0, v7))\n    Array.prototype.push.apply(tmp, hilbert3D(vec[7], half, iterations, v6, v5, v2, v1, v0, v3, v4, v7))\n\n    // Return recursive call\n    return tmp\n  }\n\n  // Return complete Hilbert Curve.\n  return vec\n}\n\n/**\n * Generates a Gosper curve (lying in the XY plane)\n *\n * https://gist.github.com/nitaku/6521802\n *\n * @param size The size of a single gosper island.\n */\nconst gosper = (size = 1): number[] => {\n  function fractalize(config: { axiom: string; steps: number; rules: Record<string, string> }): string {\n    let output = ''\n    let input = config.axiom\n\n    for (let i = 0, il = config.steps; 0 <= il ? i < il : i > il; 0 <= il ? i++ : i--) {\n      output = ''\n\n      for (let j = 0, jl = input.length; j < jl; j++) {\n        const char = input[j]\n\n        if (char in config.rules) {\n          output += config.rules[char]\n        } else {\n          output += char\n        }\n      }\n\n      input = output\n    }\n\n    return output\n  }\n\n  function toPoints(config: { fractal: string; size: number; angle: number }): number[] {\n    let currX = 0\n    let currY = 0\n    let angle = 0\n    const path = [0, 0, 0]\n    const fractal = config.fractal\n\n    for (let i = 0, l = fractal.length; i < l; i++) {\n      const char = fractal[i]\n\n      if (char === '+') {\n        angle += config.angle\n      } else if (char === '-') {\n        angle -= config.angle\n      } else if (char === 'F') {\n        currX += config.size * Math.cos(angle)\n        currY += -config.size * Math.sin(angle)\n        path.push(currX, currY, 0)\n      }\n    }\n\n    return path\n  }\n\n  //\n\n  const gosper = fractalize({\n    axiom: 'A',\n    steps: 4,\n    rules: {\n      A: 'A+BF++BF-FA--FAFA-BF+',\n      B: '-FA+BFBF++BF+FA--FA-B',\n    },\n  })\n\n  const points = toPoints({\n    fractal: gosper,\n    size: size,\n    angle: Math.PI / 3, // 60 degrees\n  })\n\n  return points\n}\n\nexport const GeometryUtils = {\n  hilbert3D,\n  gosper,\n  hilbert2D,\n}\n"], "mappings": ";AAgBA,MAAMA,SAAA,GAAYA,CAChBC,MAAA,GAAS,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC,GAC5BC,IAAA,GAAO,IACPC,UAAA,GAAa,GACbC,EAAA,GAAK,GACLC,EAAA,GAAK,GACLC,EAAA,GAAK,GACLC,EAAA,GAAK,MACS;EAEd,MAAMC,IAAA,GAAON,IAAA,GAAO;EACpB,MAAMO,KAAA,GAAQ,CACZ,IAAIR,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GACtD,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GACtD,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GACtD,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAA,GAAIJ,IAAI;EAGxD,MAAMK,GAAA,GAAM,CAACJ,KAAA,CAAML,EAAE,GAAGK,KAAA,CAAMJ,EAAE,GAAGI,KAAA,CAAMH,EAAE,GAAGG,KAAA,CAAMF,EAAE,CAAC;EAGnD,SAAK,EAAEJ,UAAA,EAAY;IACrB,MAAMW,GAAA,GAAiB;IAEvBC,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKf,SAAA,CAAUc,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYC,EAAA,EAAIG,EAAA,EAAID,EAAA,EAAID,EAAE,CAAC;IACnFU,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKf,SAAA,CAAUc,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAE,CAAC;IACnFQ,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKf,SAAA,CAAUc,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAE,CAAC;IACnFQ,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKf,SAAA,CAAUc,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYG,EAAA,EAAID,EAAA,EAAID,EAAA,EAAIG,EAAE,CAAC;IAG5E,OAAAO,GAAA;EACT;EAGO,OAAAD,GAAA;AACT;AAoBA,MAAMM,SAAA,GAAYA,CAChBnB,MAAA,GAAS,IAAIC,OAAA,CAAQ,GAAG,GAAG,CAAC,GAC5BC,IAAA,GAAO,IACPC,UAAA,GAAa,GACbC,EAAA,GAAK,GACLC,EAAA,GAAK,GACLC,EAAA,GAAK,GACLC,EAAA,GAAK,GACLa,EAAA,GAAK,GACLC,EAAA,GAAK,GACLC,EAAA,GAAK,GACLC,EAAA,GAAK,MACS;EAEd,MAAMf,IAAA,GAAON,IAAA,GAAO;EACpB,MAAMO,KAAA,GAAQ,CACZ,IAAIR,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GAC7D,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GAC7D,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GAC7D,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GAC7D,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GAC7D,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GAC7D,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI,GAC7D,IAAIP,OAAA,CAAQD,MAAA,CAAOU,CAAA,GAAIF,IAAA,EAAMR,MAAA,CAAOW,CAAA,GAAIH,IAAA,EAAMR,MAAA,CAAOY,CAAA,GAAIJ,IAAI;EAGzD,MAAAK,GAAA,GAAM,CAACJ,KAAA,CAAML,EAAE,GAAGK,KAAA,CAAMJ,EAAE,GAAGI,KAAA,CAAMH,EAAE,GAAGG,KAAA,CAAMF,EAAE,GAAGE,KAAA,CAAMW,EAAE,GAAGX,KAAA,CAAMY,EAAE,GAAGZ,KAAA,CAAMa,EAAE,GAAGb,KAAA,CAAMc,EAAE,CAAC;EAG/F,MAAEpB,UAAA,IAAc,GAAG;IACrB,MAAMW,GAAA,GAAiB;IAEvBC,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYC,EAAA,EAAIG,EAAA,EAAIa,EAAA,EAAIG,EAAA,EAAID,EAAA,EAAID,EAAA,EAAIf,EAAA,EAAID,EAAE,CAAC;IACnGU,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYC,EAAA,EAAImB,EAAA,EAAID,EAAA,EAAIjB,EAAA,EAAIC,EAAA,EAAIe,EAAA,EAAID,EAAA,EAAIb,EAAE,CAAC;IACnGQ,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYC,EAAA,EAAImB,EAAA,EAAID,EAAA,EAAIjB,EAAA,EAAIC,EAAA,EAAIe,EAAA,EAAID,EAAA,EAAIb,EAAE,CAAC;IACnGQ,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYG,EAAA,EAAIC,EAAA,EAAIH,EAAA,EAAIC,EAAA,EAAIiB,EAAA,EAAIC,EAAA,EAAIH,EAAA,EAAIC,EAAE,CAAC;IACnGN,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYG,EAAA,EAAIC,EAAA,EAAIH,EAAA,EAAIC,EAAA,EAAIiB,EAAA,EAAIC,EAAA,EAAIH,EAAA,EAAIC,EAAE,CAAC;IACnGN,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYiB,EAAA,EAAIb,EAAA,EAAID,EAAA,EAAIe,EAAA,EAAIC,EAAA,EAAIjB,EAAA,EAAID,EAAA,EAAImB,EAAE,CAAC;IACnGR,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYiB,EAAA,EAAIb,EAAA,EAAID,EAAA,EAAIe,EAAA,EAAIC,EAAA,EAAIjB,EAAA,EAAID,EAAA,EAAImB,EAAE,CAAC;IACnGR,KAAA,CAAMC,SAAA,CAAUC,IAAA,CAAKC,KAAA,CAAMJ,GAAA,EAAKK,SAAA,CAAUN,GAAA,CAAI,CAAC,GAAGL,IAAA,EAAML,UAAA,EAAYmB,EAAA,EAAID,EAAA,EAAIf,EAAA,EAAID,EAAA,EAAID,EAAA,EAAIG,EAAA,EAAIa,EAAA,EAAIG,EAAE,CAAC;IAG5F,OAAAT,GAAA;EACT;EAGO,OAAAD,GAAA;AACT;AASA,MAAMW,MAAA,GAASA,CAACtB,IAAA,GAAO,MAAgB;EACrC,SAASuB,WAAWC,MAAA,EAAiF;IACnG,IAAIC,MAAA,GAAS;IACb,IAAIC,KAAA,GAAQF,MAAA,CAAOG,KAAA;IAEnB,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKL,MAAA,CAAOM,KAAA,EAAO,KAAKD,EAAA,GAAKD,CAAA,GAAIC,EAAA,GAAKD,CAAA,GAAIC,EAAA,EAAI,KAAKA,EAAA,GAAKD,CAAA,KAAMA,CAAA,IAAK;MACxEH,MAAA;MAET,SAASM,CAAA,GAAI,GAAGC,EAAA,GAAKN,KAAA,CAAMO,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACxC,MAAAG,IAAA,GAAOR,KAAA,CAAMK,CAAC;QAEhB,IAAAG,IAAA,IAAQV,MAAA,CAAOW,KAAA,EAAO;UACdV,MAAA,IAAAD,MAAA,CAAOW,KAAA,CAAMD,IAAI;QAAA,OACtB;UACKT,MAAA,IAAAS,IAAA;QACZ;MACF;MAEQR,KAAA,GAAAD,MAAA;IACV;IAEO,OAAAA,MAAA;EACT;EAEA,SAASW,SAASZ,MAAA,EAAoE;IACpF,IAAIa,KAAA,GAAQ;IACZ,IAAIC,KAAA,GAAQ;IACZ,IAAIC,KAAA,GAAQ;IACZ,MAAMC,IAAA,GAAO,CAAC,GAAG,GAAG,CAAC;IACrB,MAAMC,OAAA,GAAUjB,MAAA,CAAOiB,OAAA;IAEvB,SAASb,CAAA,GAAI,GAAGc,CAAA,GAAID,OAAA,CAAQR,MAAA,EAAQL,CAAA,GAAIc,CAAA,EAAGd,CAAA,IAAK;MACxC,MAAAM,IAAA,GAAOO,OAAA,CAAQb,CAAC;MAEtB,IAAIM,IAAA,KAAS,KAAK;QAChBK,KAAA,IAASf,MAAA,CAAOe,KAAA;MAAA,WACPL,IAAA,KAAS,KAAK;QACvBK,KAAA,IAASf,MAAA,CAAOe,KAAA;MAAA,WACPL,IAAA,KAAS,KAAK;QACvBG,KAAA,IAASb,MAAA,CAAOxB,IAAA,GAAO2C,IAAA,CAAKC,GAAA,CAAIL,KAAK;QACrCD,KAAA,IAAS,CAACd,MAAA,CAAOxB,IAAA,GAAO2C,IAAA,CAAKE,GAAA,CAAIN,KAAK;QACjCC,IAAA,CAAAzB,IAAA,CAAKsB,KAAA,EAAOC,KAAA,EAAO,CAAC;MAC3B;IACF;IAEO,OAAAE,IAAA;EACT;EAIA,MAAMM,OAAA,GAASvB,UAAA,CAAW;IACxBI,KAAA,EAAO;IACPG,KAAA,EAAO;IACPK,KAAA,EAAO;MACLY,CAAA,EAAG;MACHC,CAAA,EAAG;IACL;EAAA,CACD;EAED,MAAMC,MAAA,GAASb,QAAA,CAAS;IACtBK,OAAA,EAASK,OAAA;IACT9C,IAAA;IACAuC,KAAA,EAAOI,IAAA,CAAKO,EAAA,GAAK;IAAA;EAAA,CAClB;EAEM,OAAAD,MAAA;AACT;AAEO,MAAME,aAAA,GAAgB;EAC3BlC,SAAA;EACAK,MAAA;EACAzB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}