const axios = require('axios');

async function testAdminAPI() {
  try {
    console.log('🧪 Testing Admin Dashboard API...');

    // Test activity logs stats endpoint
    console.log('\n📊 Testing activity logs stats...');
    try {
      const statsResponse = await axios.get('http://localhost:5001/api/admin/activity-logs/stats');
      console.log('✅ Activity stats endpoint working');
      console.log('Stats:', JSON.stringify(statsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Activity stats endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test activity logs endpoint
    console.log('\n📋 Testing activity logs list...');
    try {
      const logsResponse = await axios.get('http://localhost:5001/api/admin/activity-logs?limit=5');
      console.log('✅ Activity logs endpoint working');
      console.log(`Found ${logsResponse.data.data.logs.length} logs`);
      logsResponse.data.data.logs.forEach((log, index) => {
        console.log(`${index + 1}. [${log.Severity}] ${log.Action} ${log.EntityType} - ${log.Description}`);
      });
    } catch (error) {
      console.log('❌ Activity logs endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test actions endpoint
    console.log('\n🎯 Testing actions endpoint...');
    try {
      const actionsResponse = await axios.get('http://localhost:5001/api/admin/activity-logs/actions');
      console.log('✅ Actions endpoint working');
      console.log('Available actions:', actionsResponse.data.data);
    } catch (error) {
      console.log('❌ Actions endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test entity types endpoint
    console.log('\n🏷️ Testing entity types endpoint...');
    try {
      const entityTypesResponse = await axios.get('http://localhost:5001/api/admin/activity-logs/entity-types');
      console.log('✅ Entity types endpoint working');
      console.log('Available entity types:', entityTypesResponse.data.data);
    } catch (error) {
      console.log('❌ Entity types endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n✅ Admin API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAdminAPI();
