{"ast": null, "code": "import { TextureLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport { useLayoutEffect, useEffect } from 'react';\nconst IsObject = url => url === Object(url) && !Array.isArray(url) && typeof url !== 'function';\nfunction useTexture(input, onLoad) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(TextureLoader, IsObject(input) ? Object.values(input) : input);\n  useLayoutEffect(() => {\n    onLoad == null ? void 0 : onLoad(textures);\n  }, [onLoad]); // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n\n  useEffect(() => {\n    const array = Array.isArray(textures) ? textures : [textures];\n    array.forEach(gl.initTexture);\n  }, [gl, textures]);\n  if (IsObject(input)) {\n    const keys = Object.keys(input);\n    const keyed = {};\n    keys.forEach(key => Object.assign(keyed, {\n      [key]: textures[keys.indexOf(key)]\n    }));\n    return keyed;\n  } else {\n    return textures;\n  }\n}\nuseTexture.preload = url => useLoader.preload(TextureLoader, url);\nuseTexture.clear = input => useLoader.clear(TextureLoader, input);\nexport { IsObject, useTexture };", "map": {"version": 3, "names": ["TextureLoader", "useThree", "useLoader", "useLayoutEffect", "useEffect", "IsObject", "url", "Object", "Array", "isArray", "useTexture", "input", "onLoad", "gl", "state", "textures", "values", "array", "for<PERSON>ach", "initTexture", "keys", "keyed", "key", "assign", "indexOf", "preload", "clear"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useTexture.js"], "sourcesContent": ["import { TextureLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport { useLayoutEffect, useEffect } from 'react';\n\nconst IsObject = url => url === Object(url) && !Array.isArray(url) && typeof url !== 'function';\nfunction useTexture(input, onLoad) {\n  const gl = useThree(state => state.gl);\n  const textures = useLoader(TextureLoader, IsObject(input) ? Object.values(input) : input);\n  useLayoutEffect(() => {\n    onLoad == null ? void 0 : onLoad(textures);\n  }, [onLoad]); // https://github.com/mrdoob/three.js/issues/22696\n  // Upload the texture to the GPU immediately instead of waiting for the first render\n\n  useEffect(() => {\n    const array = Array.isArray(textures) ? textures : [textures];\n    array.forEach(gl.initTexture);\n  }, [gl, textures]);\n\n  if (IsObject(input)) {\n    const keys = Object.keys(input);\n    const keyed = {};\n    keys.forEach(key => Object.assign(keyed, {\n      [key]: textures[keys.indexOf(key)]\n    }));\n    return keyed;\n  } else {\n    return textures;\n  }\n}\n\nuseTexture.preload = url => useLoader.preload(TextureLoader, url);\n\nuseTexture.clear = input => useLoader.clear(TextureLoader, input);\n\nexport { IsObject, useTexture };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AACxD,SAASC,eAAe,EAAEC,SAAS,QAAQ,OAAO;AAElD,MAAMC,QAAQ,GAAGC,GAAG,IAAIA,GAAG,KAAKC,MAAM,CAACD,GAAG,CAAC,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,UAAU;AAC/F,SAASI,UAAUA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACjC,MAAMC,EAAE,GAAGZ,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,QAAQ,GAAGb,SAAS,CAACF,aAAa,EAAEK,QAAQ,CAACM,KAAK,CAAC,GAAGJ,MAAM,CAACS,MAAM,CAACL,KAAK,CAAC,GAAGA,KAAK,CAAC;EACzFR,eAAe,CAAC,MAAM;IACpBS,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,QAAQ,CAAC;EAC5C,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC;EACd;;EAEAR,SAAS,CAAC,MAAM;IACd,MAAMa,KAAK,GAAGT,KAAK,CAACC,OAAO,CAACM,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC7DE,KAAK,CAACC,OAAO,CAACL,EAAE,CAACM,WAAW,CAAC;EAC/B,CAAC,EAAE,CAACN,EAAE,EAAEE,QAAQ,CAAC,CAAC;EAElB,IAAIV,QAAQ,CAACM,KAAK,CAAC,EAAE;IACnB,MAAMS,IAAI,GAAGb,MAAM,CAACa,IAAI,CAACT,KAAK,CAAC;IAC/B,MAAMU,KAAK,GAAG,CAAC,CAAC;IAChBD,IAAI,CAACF,OAAO,CAACI,GAAG,IAAIf,MAAM,CAACgB,MAAM,CAACF,KAAK,EAAE;MACvC,CAACC,GAAG,GAAGP,QAAQ,CAACK,IAAI,CAACI,OAAO,CAACF,GAAG,CAAC;IACnC,CAAC,CAAC,CAAC;IACH,OAAOD,KAAK;EACd,CAAC,MAAM;IACL,OAAON,QAAQ;EACjB;AACF;AAEAL,UAAU,CAACe,OAAO,GAAGnB,GAAG,IAAIJ,SAAS,CAACuB,OAAO,CAACzB,aAAa,EAAEM,GAAG,CAAC;AAEjEI,UAAU,CAACgB,KAAK,GAAGf,KAAK,IAAIT,SAAS,CAACwB,KAAK,CAAC1B,aAAa,EAAEW,KAAK,CAAC;AAEjE,SAASN,QAAQ,EAAEK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}