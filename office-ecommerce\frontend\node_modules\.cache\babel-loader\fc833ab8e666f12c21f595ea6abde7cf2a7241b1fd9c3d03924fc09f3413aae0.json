{"ast": null, "code": "import * as THREE from 'three';\nimport { WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport * as React from 'react';\nimport { useMemo, useEffect } from 'react';\nimport { useThree } from '@react-three/fiber';\nfunction useCubeCamera({\n  resolution = 256,\n  near = 0.1,\n  far = 1000,\n  envMap,\n  fog\n} = {}) {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  const fbo = useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  const camera = useMemo(() => new THREE.CubeCamera(near, far, fbo), [near, far, fbo]);\n  let originalFog;\n  let originalBackground;\n  const update = React.useCallback(() => {\n    originalFog = scene.fog;\n    originalBackground = scene.background;\n    scene.background = envMap || originalBackground;\n    scene.fog = fog || originalFog;\n    camera.update(gl, scene);\n    scene.fog = originalFog;\n    scene.background = originalBackground;\n  }, [gl, scene, camera]);\n  return {\n    fbo,\n    camera,\n    update\n  };\n}\nexport { useCubeCamera };", "map": {"version": 3, "names": ["THREE", "WebGLCubeRenderTarget", "HalfFloatType", "React", "useMemo", "useEffect", "useThree", "useCubeCamera", "resolution", "near", "far", "envMap", "fog", "gl", "scene", "fbo", "texture", "type", "dispose", "camera", "CubeCamera", "originalFog", "originalBackground", "update", "useCallback", "background"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useCubeCamera.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport * as React from 'react';\nimport { useMemo, useEffect } from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction useCubeCamera({\n  resolution = 256,\n  near = 0.1,\n  far = 1000,\n  envMap,\n  fog\n} = {}) {\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  const fbo = useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  const camera = useMemo(() => new THREE.CubeCamera(near, far, fbo), [near, far, fbo]);\n  let originalFog;\n  let originalBackground;\n  const update = React.useCallback(() => {\n    originalFog = scene.fog;\n    originalBackground = scene.background;\n    scene.background = envMap || originalBackground;\n    scene.fog = fog || originalFog;\n    camera.update(gl, scene);\n    scene.fog = originalFog;\n    scene.background = originalBackground;\n  }, [gl, scene, camera]);\n  return {\n    fbo,\n    camera,\n    update\n  };\n}\n\nexport { useCubeCamera };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,OAAO;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,SAAS,QAAQ,OAAO;AAC1C,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,aAAaA,CAAC;EACrBC,UAAU,GAAG,GAAG;EAChBC,IAAI,GAAG,GAAG;EACVC,GAAG,GAAG,IAAI;EACVC,MAAM;EACNC;AACF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMC,EAAE,GAAGP,QAAQ,CAAC,CAAC;IACnBO;EACF,CAAC,KAAKA,EAAE,CAAC;EACT,MAAMC,KAAK,GAAGR,QAAQ,CAAC,CAAC;IACtBQ;EACF,CAAC,KAAKA,KAAK,CAAC;EACZ,MAAMC,GAAG,GAAGX,OAAO,CAAC,MAAM;IACxB,MAAMW,GAAG,GAAG,IAAId,qBAAqB,CAACO,UAAU,CAAC;IACjDO,GAAG,CAACC,OAAO,CAACC,IAAI,GAAGf,aAAa;IAChC,OAAOa,GAAG;EACZ,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAChBH,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXU,GAAG,CAACG,OAAO,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACH,GAAG,CAAC,CAAC;EACT,MAAMI,MAAM,GAAGf,OAAO,CAAC,MAAM,IAAIJ,KAAK,CAACoB,UAAU,CAACX,IAAI,EAAEC,GAAG,EAAEK,GAAG,CAAC,EAAE,CAACN,IAAI,EAAEC,GAAG,EAAEK,GAAG,CAAC,CAAC;EACpF,IAAIM,WAAW;EACf,IAAIC,kBAAkB;EACtB,MAAMC,MAAM,GAAGpB,KAAK,CAACqB,WAAW,CAAC,MAAM;IACrCH,WAAW,GAAGP,KAAK,CAACF,GAAG;IACvBU,kBAAkB,GAAGR,KAAK,CAACW,UAAU;IACrCX,KAAK,CAACW,UAAU,GAAGd,MAAM,IAAIW,kBAAkB;IAC/CR,KAAK,CAACF,GAAG,GAAGA,GAAG,IAAIS,WAAW;IAC9BF,MAAM,CAACI,MAAM,CAACV,EAAE,EAAEC,KAAK,CAAC;IACxBA,KAAK,CAACF,GAAG,GAAGS,WAAW;IACvBP,KAAK,CAACW,UAAU,GAAGH,kBAAkB;EACvC,CAAC,EAAE,CAACT,EAAE,EAAEC,KAAK,EAAEK,MAAM,CAAC,CAAC;EACvB,OAAO;IACLJ,GAAG;IACHI,MAAM;IACNI;EACF,CAAC;AACH;AAEA,SAAShB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}