{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\common\\\\Footer.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Get Touch in Excellence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Have questions about our premium office solutions? Our team is here to help you create the perfect workspace.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-methods\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-method\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"method-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: \"2\",\n                    y: \"4\",\n                    width: \"20\",\n                    height: \"16\",\n                    rx: \"2\",\n                    fill: \"white\",\n                    stroke: \"white\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 18,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M22 6L12 13L2 6\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 19,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: \"2\",\n                    y: \"4\",\n                    width: \"20\",\n                    height: \"16\",\n                    rx: \"2\",\n                    fill: \"none\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 20,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 17,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 16,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Email Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 25,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-method\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"method-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M22 16.92V19.92C22 20.51 21.39 21 20.8 21H19.82C16.75 20.67 13.79 19.61 11.19 17.93C8.77 16.39 6.72 14.34 5.18 11.92C3.5 9.32 2.44 6.36 2.11 3.29C2.05 2.7 2.49 2 3.08 2H6.08C6.67 2 7.18 2.51 7.18 3.1V3.72C7.31 4.68 7.54 5.62 7.88 6.53C8.01 6.89 7.94 7.28 7.69 7.58L6.42 8.85C7.85 11.35 9.93 13.43 12.43 14.86L13.7 13.59C14 13.34 14.39 13.27 14.75 13.4C15.66 13.74 16.6 13.97 17.56 14.1H17.56C18.15 14.1 18.66 14.61 18.66 15.2V18.2C18.66 18.79 18.15 19.3 17.56 19.3H16.92Z\",\n                    fill: \"white\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 32,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Call Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"+1 234 567 890\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-method\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"method-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61 3.95 5.32 5.64 3.64C7.32 1.95 9.61 1 12 1C14.39 1 16.68 1.95 18.36 3.64C20.05 5.32 21 7.61 21 10Z\",\n                    fill: \"white\",\n                    stroke: \"#F0B21B\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 44,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"10\",\n                    r: \"3\",\n                    fill: \"#F0B21B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 45,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Visit Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"123 Design Street, Creative City 12345\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-tagline\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Creating exceptional office environments through innovative furniture solutions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-form-header\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"contact-form-badge\",\n              children: \"Contact Form\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"footer-contact-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Enter your name\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                placeholder: \"Enter your email\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                placeholder: \"Message\",\n                rows: \"4\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"send-message-btn\",\n              children: \"Send Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-bottom\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Quick Links\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/about\",\n                    children: \"About Us\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/products\",\n                    children: \"Products\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/faq\",\n                    children: \"FAQ\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/privacy\",\n                    children: \"Privacy Policy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/terms\",\n                    children: \"Terms of Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"footer-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Follow Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"social-links\",\n                children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#facebook\",\n                  \"aria-label\": \"Facebook\",\n                  className: \"social-link\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M18 2H15C13.6739 2 12.4021 2.52678 11.4645 3.46447C10.5268 4.40215 10 5.67392 10 7V10H7V14H10V22H14V14H17L18 10H14V7C14 6.73478 14.1054 6.48043 14.2929 6.29289C14.4804 6.10536 14.7348 6 15 6H18V2Z\",\n                      fill: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 45\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#instagram\",\n                  \"aria-label\": \"Instagram\",\n                  className: \"social-link\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                      x: \"2\",\n                      y: \"2\",\n                      width: \"20\",\n                      height: \"20\",\n                      rx: \"5\",\n                      ry: \"5\",\n                      stroke: \"white\",\n                      strokeWidth: \"2\",\n                      fill: \"none\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 112,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M16 11.37C16.1234 12.2022 15.9813 13.0522 15.5938 13.799C15.2063 14.5458 14.5931 15.1514 13.8416 15.5297C13.0901 15.9079 12.2384 16.0396 11.4078 15.9059C10.5771 15.7723 9.80976 15.3801 9.21484 14.7852C8.61992 14.1902 8.22773 13.4229 8.09407 12.5922C7.9604 11.7615 8.09207 10.9099 8.47033 10.1584C8.84859 9.40685 9.45419 8.79374 10.201 8.40624C10.9478 8.01874 11.7978 7.87658 12.63 8C13.4789 8.12588 14.2649 8.52146 14.8717 9.1283C15.4785 9.73515 15.8741 10.5211 16 11.37Z\",\n                      stroke: \"white\",\n                      strokeWidth: \"2\",\n                      fill: \"none\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 113,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M17.5 6.5H17.51\",\n                      stroke: \"white\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 114,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "xmlns", "x", "y", "rx", "stroke", "strokeWidth", "d", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "type", "placeholder", "required", "rows", "to", "href", "ry", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/common/Footer.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Footer = () => {\n    return (\n        <footer className=\"footer\">\n            <div className=\"container\">\n                <div className=\"footer-content\">\n                    {/* Left Side - Contact Information */}\n                    <div className=\"footer-left\">\n                        <h2>Get Touch in Excellence</h2>\n                        <p>Have questions about our premium office solutions? Our team is here to help you create the perfect workspace.</p>\n\n                        <div className=\"contact-methods\">\n                            <div className=\"contact-method\">\n                                <div className=\"method-icon\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                        <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" fill=\"white\" stroke=\"white\" strokeWidth=\"2\"/>\n                                        <path d=\"M22 6L12 13L2 6\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                        <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" fill=\"none\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                                    </svg>\n                                </div>\n                                <div>\n                                    <h4>Email Us</h4>\n                                    <p><EMAIL></p>\n                                </div>\n                            </div>\n\n                            <div className=\"contact-method\">\n                                <div className=\"method-icon\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                        <path d=\"M22 16.92V19.92C22 20.51 21.39 21 20.8 21H19.82C16.75 20.67 13.79 19.61 11.19 17.93C8.77 16.39 6.72 14.34 5.18 11.92C3.5 9.32 2.44 6.36 2.11 3.29C2.05 2.7 2.49 2 3.08 2H6.08C6.67 2 7.18 2.51 7.18 3.1V3.72C7.31 4.68 7.54 5.62 7.88 6.53C8.01 6.89 7.94 7.28 7.69 7.58L6.42 8.85C7.85 11.35 9.93 13.43 12.43 14.86L13.7 13.59C14 13.34 14.39 13.27 14.75 13.4C15.66 13.74 16.6 13.97 17.56 14.1H17.56C18.15 14.1 18.66 14.61 18.66 15.2V18.2C18.66 18.79 18.15 19.3 17.56 19.3H16.92Z\" fill=\"white\" stroke=\"#F0B21B\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                </div>\n                                <div>\n                                    <h4>Call Us</h4>\n                                    <p>+1 234 567 890</p>\n                                </div>\n                            </div>\n\n                            <div className=\"contact-method\">\n                                <div className=\"method-icon\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                        <path d=\"M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61 3.95 5.32 5.64 3.64C7.32 1.95 9.61 1 12 1C14.39 1 16.68 1.95 18.36 3.64C20.05 5.32 21 7.61 21 10Z\" fill=\"white\" stroke=\"#F0B21B\" strokeWidth=\"2\"/>\n                                        <circle cx=\"12\" cy=\"10\" r=\"3\" fill=\"#F0B21B\"/>\n                                    </svg>\n                                </div>\n                                <div>\n                                    <h4>Visit Us</h4>\n                                    <p>123 Design Street, Creative City 12345</p>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"company-tagline\">\n                            <p>Creating exceptional office environments through innovative furniture solutions.</p>\n                        </div>\n                    </div>\n\n                    {/* Right Side - Contact Form */}\n                    <div className=\"footer-right\">\n                        <div className=\"contact-form-header\">\n                            <span className=\"contact-form-badge\">Contact Form</span>\n                        </div>\n\n                        <form className=\"footer-contact-form\">\n                            <div className=\"form-group\">\n                                <input type=\"text\" placeholder=\"Enter your name\" required />\n                            </div>\n                            <div className=\"form-group\">\n                                <input type=\"email\" placeholder=\"Enter your email\" required />\n                            </div>\n                            <div className=\"form-group\">\n                                <textarea placeholder=\"Message\" rows=\"4\" required></textarea>\n                            </div>\n                            <button type=\"submit\" className=\"send-message-btn\">Send Message</button>\n                        </form>\n                    </div>\n                </div>\n\n                {/* Bottom Section */}\n                <div className=\"footer-bottom\">\n                    <div className=\"footer-bottom-content\">\n                        <div className=\"footer-links\">\n                            <div className=\"footer-section\">\n                                <h4>Quick Links</h4>\n                                <ul>\n                                    <li><Link to=\"/about\">About Us</Link></li>\n                                    <li><Link to=\"/products\">Products</Link></li>\n                                </ul>\n                            </div>\n\n                            <div className=\"footer-section\">\n                                <h4>Support</h4>\n                                <ul>\n                                    <li><Link to=\"/faq\">FAQ</Link></li>\n                                    <li><Link to=\"/privacy\">Privacy Policy</Link></li>\n                                    <li><Link to=\"/terms\">Terms of Service</Link></li>\n                                </ul>\n                            </div>\n\n                            <div className=\"footer-section\">\n                                <h4>Follow Us</h4>\n                                <div className=\"social-links\">\n                                    <a href=\"#facebook\" aria-label=\"Facebook\" className=\"social-link\">\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                            <path d=\"M18 2H15C13.6739 2 12.4021 2.52678 11.4645 3.46447C10.5268 4.40215 10 5.67392 10 7V10H7V14H10V22H14V14H17L18 10H14V7C14 6.73478 14.1054 6.48043 14.2929 6.29289C14.4804 6.10536 14.7348 6 15 6H18V2Z\" fill=\"white\"/>\n                                        </svg>\n                                    </a>\n                                    <a href=\"#instagram\" aria-label=\"Instagram\" className=\"social-link\">\n                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                            <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" rx=\"5\" ry=\"5\" stroke=\"white\" strokeWidth=\"2\" fill=\"none\"/>\n                                            <path d=\"M16 11.37C16.1234 12.2022 15.9813 13.0522 15.5938 13.799C15.2063 14.5458 14.5931 15.1514 13.8416 15.5297C13.0901 15.9079 12.2384 16.0396 11.4078 15.9059C10.5771 15.7723 9.80976 15.3801 9.21484 14.7852C8.61992 14.1902 8.22773 13.4229 8.09407 12.5922C7.9604 11.7615 8.09207 10.9099 8.47033 10.1584C8.84859 9.40685 9.45419 8.79374 10.201 8.40624C10.9478 8.01874 11.7978 7.87658 12.63 8C13.4789 8.12588 14.2649 8.52146 14.8717 9.1283C15.4785 9.73515 15.8741 10.5211 16 11.37Z\" stroke=\"white\" strokeWidth=\"2\" fill=\"none\"/>\n                                            <path d=\"M17.5 6.5H17.51\" stroke=\"white\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                        </svg>\n                                    </a>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </footer>\n    );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACjB,oBACID,OAAA;IAAQE,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACtBH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAE3BH,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBH,OAAA;YAAAG,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCP,OAAA;YAAAG,QAAA,EAAG;UAA6G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEpHP,OAAA;YAAKE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BH,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BH,OAAA;gBAAKE,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACxBH,OAAA;kBAAKQ,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,KAAK,EAAC,4BAA4B;kBAAAT,QAAA,gBAC1FH,OAAA;oBAAMa,CAAC,EAAC,GAAG;oBAACC,CAAC,EAAC,GAAG;oBAACN,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACM,EAAE,EAAC,GAAG;oBAACJ,IAAI,EAAC,OAAO;oBAACK,MAAM,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC7FP,OAAA;oBAAMkB,CAAC,EAAC,iBAAiB;oBAACF,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACE,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACzGP,OAAA;oBAAMa,CAAC,EAAC,GAAG;oBAACC,CAAC,EAAC,GAAG;oBAACN,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACM,EAAE,EAAC,GAAG;oBAACJ,IAAI,EAAC,MAAM;oBAACK,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNP,OAAA;gBAAAG,QAAA,gBACIH,OAAA;kBAAAG,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBP,OAAA;kBAAAG,QAAA,EAAG;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BH,OAAA;gBAAKE,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACxBH,OAAA;kBAAKQ,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,KAAK,EAAC,4BAA4B;kBAAAT,QAAA,eAC1FH,OAAA;oBAAMkB,CAAC,EAAC,ydAAyd;oBAACP,IAAI,EAAC,OAAO;oBAACK,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC,GAAG;oBAACE,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7jB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNP,OAAA;gBAAAG,QAAA,gBACIH,OAAA;kBAAAG,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChBP,OAAA;kBAAAG,QAAA,EAAG;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BH,OAAA;gBAAKE,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACxBH,OAAA;kBAAKQ,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAACC,KAAK,EAAC,4BAA4B;kBAAAT,QAAA,gBAC1FH,OAAA;oBAAMkB,CAAC,EAAC,6IAA6I;oBAACP,IAAI,EAAC,OAAO;oBAACK,MAAM,EAAC,SAAS;oBAACC,WAAW,EAAC;kBAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eACrMP,OAAA;oBAAQqB,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACZ,IAAI,EAAC;kBAAS;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNP,OAAA;gBAAAG,QAAA,gBACIH,OAAA;kBAAAG,QAAA,EAAI;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjBP,OAAA;kBAAAG,QAAA,EAAG;gBAAsC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC5BH,OAAA;cAAAG,QAAA,EAAG;YAAgF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBH,OAAA;YAAKE,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAChCH,OAAA;cAAME,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAENP,OAAA;YAAME,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBACjCH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACvBH,OAAA;gBAAOwB,IAAI,EAAC,MAAM;gBAACC,WAAW,EAAC,iBAAiB;gBAACC,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNP,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACvBH,OAAA;gBAAOwB,IAAI,EAAC,OAAO;gBAACC,WAAW,EAAC,kBAAkB;gBAACC,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNP,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,eACvBH,OAAA;gBAAUyB,WAAW,EAAC,SAAS;gBAACE,IAAI,EAAC,GAAG;gBAACD,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNP,OAAA;cAAQwB,IAAI,EAAC,QAAQ;cAACtB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNP,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1BH,OAAA;UAAKE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAClCH,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BH,OAAA;gBAAAG,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBP,OAAA;gBAAAG,QAAA,gBACIH,OAAA;kBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;oBAAC8B,EAAE,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1CP,OAAA;kBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;oBAAC8B,EAAE,EAAC,WAAW;oBAAAzB,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BH,OAAA;gBAAAG,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBP,OAAA;gBAAAG,QAAA,gBACIH,OAAA;kBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;oBAAC8B,EAAE,EAAC,MAAM;oBAAAzB,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnCP,OAAA;kBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;oBAAC8B,EAAE,EAAC,UAAU;oBAAAzB,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClDP,OAAA;kBAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;oBAAC8B,EAAE,EAAC,QAAQ;oBAAAzB,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BH,OAAA;gBAAAG,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBP,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAG6B,IAAI,EAAC,WAAW;kBAAC,cAAW,UAAU;kBAAC3B,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC7DH,OAAA;oBAAKQ,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,KAAK,EAAC,4BAA4B;oBAAAT,QAAA,eAC1FH,OAAA;sBAAMkB,CAAC,EAAC,sMAAsM;sBAACP,IAAI,EAAC;oBAAO;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5N;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACJP,OAAA;kBAAG6B,IAAI,EAAC,YAAY;kBAAC,cAAW,WAAW;kBAAC3B,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC/DH,OAAA;oBAAKQ,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAACC,KAAK,EAAC,4BAA4B;oBAAAT,QAAA,gBAC1FH,OAAA;sBAAMa,CAAC,EAAC,GAAG;sBAACC,CAAC,EAAC,GAAG;sBAACN,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACM,EAAE,EAAC,GAAG;sBAACe,EAAE,EAAC,GAAG;sBAACd,MAAM,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACN,IAAI,EAAC;oBAAM;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACnGP,OAAA;sBAAMkB,CAAC,EAAC,ydAAyd;sBAACF,MAAM,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACN,IAAI,EAAC;oBAAM;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAC9gBP,OAAA;sBAAMkB,CAAC,EAAC,iBAAiB;sBAACF,MAAM,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEjB,CAAC;AAACwB,EAAA,GAzHI9B,MAAM;AA2HZ,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}