{"ast": null, "code": "import { useReducer, useRef, useDebugValue, useEffect, useLayoutEffect } from 'react';\nfunction createStore(createState) {\n  let state;\n  const listeners = /* @__PURE__ */new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (nextState !== state) {\n      const previousState = state;\n      state = replace ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach(listener => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const subscribeWithSelector = (listener, selector = getState, equalityFn = Object.is) => {\n    console.warn(\"[DEPRECATED] Please use `subscribeWithSelector` middleware\");\n    let currentSlice = selector(state);\n    function listenerToAdd() {\n      const nextSlice = selector(state);\n      if (!equalityFn(currentSlice, nextSlice)) {\n        const previousSlice = currentSlice;\n        listener(currentSlice = nextSlice, previousSlice);\n      }\n    }\n    listeners.add(listenerToAdd);\n    return () => listeners.delete(listenerToAdd);\n  };\n  const subscribe = (listener, selector, equalityFn) => {\n    if (selector || equalityFn) {\n      return subscribeWithSelector(listener, selector, equalityFn);\n    }\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => listeners.clear();\n  const api = {\n    setState,\n    getState,\n    subscribe,\n    destroy\n  };\n  state = createState(setState, getState, api);\n  return api;\n}\nconst isSSR = typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\nconst useIsomorphicLayoutEffect = isSSR ? useEffect : useLayoutEffect;\nfunction create(createState) {\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useStore = (selector = api.getState, equalityFn = Object.is) => {\n    const [, forceUpdate] = useReducer(c => c + 1, 0);\n    const state = api.getState();\n    const stateRef = useRef(state);\n    const selectorRef = useRef(selector);\n    const equalityFnRef = useRef(equalityFn);\n    const erroredRef = useRef(false);\n    const currentSliceRef = useRef();\n    if (currentSliceRef.current === void 0) {\n      currentSliceRef.current = selector(state);\n    }\n    let newStateSlice;\n    let hasNewStateSlice = false;\n    if (stateRef.current !== state || selectorRef.current !== selector || equalityFnRef.current !== equalityFn || erroredRef.current) {\n      newStateSlice = selector(state);\n      hasNewStateSlice = !equalityFn(currentSliceRef.current, newStateSlice);\n    }\n    useIsomorphicLayoutEffect(() => {\n      if (hasNewStateSlice) {\n        currentSliceRef.current = newStateSlice;\n      }\n      stateRef.current = state;\n      selectorRef.current = selector;\n      equalityFnRef.current = equalityFn;\n      erroredRef.current = false;\n    });\n    const stateBeforeSubscriptionRef = useRef(state);\n    useIsomorphicLayoutEffect(() => {\n      const listener = () => {\n        try {\n          const nextState = api.getState();\n          const nextStateSlice = selectorRef.current(nextState);\n          if (!equalityFnRef.current(currentSliceRef.current, nextStateSlice)) {\n            stateRef.current = nextState;\n            currentSliceRef.current = nextStateSlice;\n            forceUpdate();\n          }\n        } catch (error) {\n          erroredRef.current = true;\n          forceUpdate();\n        }\n      };\n      const unsubscribe = api.subscribe(listener);\n      if (api.getState() !== stateBeforeSubscriptionRef.current) {\n        listener();\n      }\n      return unsubscribe;\n    }, []);\n    const sliceToReturn = hasNewStateSlice ? newStateSlice : currentSliceRef.current;\n    useDebugValue(sliceToReturn);\n    return sliceToReturn;\n  };\n  Object.assign(useStore, api);\n  useStore[Symbol.iterator] = function () {\n    console.warn(\"[useStore, api] = create() is deprecated and will be removed in v4\");\n    const items = [useStore, api];\n    return {\n      next() {\n        const done = items.length <= 0;\n        return {\n          value: items.shift(),\n          done\n        };\n      }\n    };\n  };\n  return useStore;\n}\nexport { create as default };", "map": {"version": 3, "names": ["useReducer", "useRef", "useDebugValue", "useEffect", "useLayoutEffect", "createStore", "createState", "state", "listeners", "Set", "setState", "partial", "replace", "nextState", "previousState", "Object", "assign", "for<PERSON>ach", "listener", "getState", "subscribeWithSelector", "selector", "equalityFn", "is", "console", "warn", "currentSlice", "listenerToAdd", "nextSlice", "previousSlice", "add", "delete", "subscribe", "destroy", "clear", "api", "isSSR", "window", "navigator", "test", "userAgent", "useIsomorphicLayoutEffect", "create", "useStore", "forceUpdate", "c", "stateRef", "selectorRef", "equalityFnRef", "erroredRef", "currentSliceRef", "current", "newStateSlice", "hasNewStateSlice", "stateBeforeSubscriptionRef", "nextStateSlice", "error", "unsubscribe", "sliceToReturn", "Symbol", "iterator", "items", "next", "done", "length", "value", "shift", "default"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/zustand/esm/index.js"], "sourcesContent": ["import { useReducer, useRef, useDebugValue, useEffect, useLayoutEffect } from 'react';\n\nfunction createStore(createState) {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (nextState !== state) {\n      const previousState = state;\n      state = replace ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const subscribeWithSelector = (listener, selector = getState, equalityFn = Object.is) => {\n    console.warn(\"[DEPRECATED] Please use `subscribeWithSelector` middleware\");\n    let currentSlice = selector(state);\n    function listenerToAdd() {\n      const nextSlice = selector(state);\n      if (!equalityFn(currentSlice, nextSlice)) {\n        const previousSlice = currentSlice;\n        listener(currentSlice = nextSlice, previousSlice);\n      }\n    }\n    listeners.add(listenerToAdd);\n    return () => listeners.delete(listenerToAdd);\n  };\n  const subscribe = (listener, selector, equalityFn) => {\n    if (selector || equalityFn) {\n      return subscribeWithSelector(listener, selector, equalityFn);\n    }\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => listeners.clear();\n  const api = { setState, getState, subscribe, destroy };\n  state = createState(setState, getState, api);\n  return api;\n}\n\nconst isSSR = typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\nconst useIsomorphicLayoutEffect = isSSR ? useEffect : useLayoutEffect;\nfunction create(createState) {\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useStore = (selector = api.getState, equalityFn = Object.is) => {\n    const [, forceUpdate] = useReducer((c) => c + 1, 0);\n    const state = api.getState();\n    const stateRef = useRef(state);\n    const selectorRef = useRef(selector);\n    const equalityFnRef = useRef(equalityFn);\n    const erroredRef = useRef(false);\n    const currentSliceRef = useRef();\n    if (currentSliceRef.current === void 0) {\n      currentSliceRef.current = selector(state);\n    }\n    let newStateSlice;\n    let hasNewStateSlice = false;\n    if (stateRef.current !== state || selectorRef.current !== selector || equalityFnRef.current !== equalityFn || erroredRef.current) {\n      newStateSlice = selector(state);\n      hasNewStateSlice = !equalityFn(currentSliceRef.current, newStateSlice);\n    }\n    useIsomorphicLayoutEffect(() => {\n      if (hasNewStateSlice) {\n        currentSliceRef.current = newStateSlice;\n      }\n      stateRef.current = state;\n      selectorRef.current = selector;\n      equalityFnRef.current = equalityFn;\n      erroredRef.current = false;\n    });\n    const stateBeforeSubscriptionRef = useRef(state);\n    useIsomorphicLayoutEffect(() => {\n      const listener = () => {\n        try {\n          const nextState = api.getState();\n          const nextStateSlice = selectorRef.current(nextState);\n          if (!equalityFnRef.current(currentSliceRef.current, nextStateSlice)) {\n            stateRef.current = nextState;\n            currentSliceRef.current = nextStateSlice;\n            forceUpdate();\n          }\n        } catch (error) {\n          erroredRef.current = true;\n          forceUpdate();\n        }\n      };\n      const unsubscribe = api.subscribe(listener);\n      if (api.getState() !== stateBeforeSubscriptionRef.current) {\n        listener();\n      }\n      return unsubscribe;\n    }, []);\n    const sliceToReturn = hasNewStateSlice ? newStateSlice : currentSliceRef.current;\n    useDebugValue(sliceToReturn);\n    return sliceToReturn;\n  };\n  Object.assign(useStore, api);\n  useStore[Symbol.iterator] = function() {\n    console.warn(\"[useStore, api] = create() is deprecated and will be removed in v4\");\n    const items = [useStore, api];\n    return {\n      next() {\n        const done = items.length <= 0;\n        return { value: items.shift(), done };\n      }\n    };\n  };\n  return useStore;\n}\n\nexport { create as default };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,aAAa,EAAEC,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAErF,SAASC,WAAWA,CAACC,WAAW,EAAE;EAChC,IAAIC,KAAK;EACT,MAAMC,SAAS,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;EAC3C,MAAMC,QAAQ,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;IACrC,MAAMC,SAAS,GAAG,OAAOF,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACJ,KAAK,CAAC,GAAGI,OAAO;IAC1E,IAAIE,SAAS,KAAKN,KAAK,EAAE;MACvB,MAAMO,aAAa,GAAGP,KAAK;MAC3BA,KAAK,GAAGK,OAAO,GAAGC,SAAS,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAET,KAAK,EAAEM,SAAS,CAAC;MACjEL,SAAS,CAACS,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAACX,KAAK,EAAEO,aAAa,CAAC,CAAC;IACjE;EACF,CAAC;EACD,MAAMK,QAAQ,GAAGA,CAAA,KAAMZ,KAAK;EAC5B,MAAMa,qBAAqB,GAAGA,CAACF,QAAQ,EAAEG,QAAQ,GAAGF,QAAQ,EAAEG,UAAU,GAAGP,MAAM,CAACQ,EAAE,KAAK;IACvFC,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;IAC1E,IAAIC,YAAY,GAAGL,QAAQ,CAACd,KAAK,CAAC;IAClC,SAASoB,aAAaA,CAAA,EAAG;MACvB,MAAMC,SAAS,GAAGP,QAAQ,CAACd,KAAK,CAAC;MACjC,IAAI,CAACe,UAAU,CAACI,YAAY,EAAEE,SAAS,CAAC,EAAE;QACxC,MAAMC,aAAa,GAAGH,YAAY;QAClCR,QAAQ,CAACQ,YAAY,GAAGE,SAAS,EAAEC,aAAa,CAAC;MACnD;IACF;IACArB,SAAS,CAACsB,GAAG,CAACH,aAAa,CAAC;IAC5B,OAAO,MAAMnB,SAAS,CAACuB,MAAM,CAACJ,aAAa,CAAC;EAC9C,CAAC;EACD,MAAMK,SAAS,GAAGA,CAACd,QAAQ,EAAEG,QAAQ,EAAEC,UAAU,KAAK;IACpD,IAAID,QAAQ,IAAIC,UAAU,EAAE;MAC1B,OAAOF,qBAAqB,CAACF,QAAQ,EAAEG,QAAQ,EAAEC,UAAU,CAAC;IAC9D;IACAd,SAAS,CAACsB,GAAG,CAACZ,QAAQ,CAAC;IACvB,OAAO,MAAMV,SAAS,CAACuB,MAAM,CAACb,QAAQ,CAAC;EACzC,CAAC;EACD,MAAMe,OAAO,GAAGA,CAAA,KAAMzB,SAAS,CAAC0B,KAAK,CAAC,CAAC;EACvC,MAAMC,GAAG,GAAG;IAAEzB,QAAQ;IAAES,QAAQ;IAAEa,SAAS;IAAEC;EAAQ,CAAC;EACtD1B,KAAK,GAAGD,WAAW,CAACI,QAAQ,EAAES,QAAQ,EAAEgB,GAAG,CAAC;EAC5C,OAAOA,GAAG;AACZ;AAEA,MAAMC,KAAK,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,IAAI,6BAA6B,CAACC,IAAI,CAACF,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC;AAClI,MAAMC,yBAAyB,GAAGL,KAAK,GAAGjC,SAAS,GAAGC,eAAe;AACrE,SAASsC,MAAMA,CAACpC,WAAW,EAAE;EAC3B,MAAM6B,GAAG,GAAG,OAAO7B,WAAW,KAAK,UAAU,GAAGD,WAAW,CAACC,WAAW,CAAC,GAAGA,WAAW;EACtF,MAAMqC,QAAQ,GAAGA,CAACtB,QAAQ,GAAGc,GAAG,CAAChB,QAAQ,EAAEG,UAAU,GAAGP,MAAM,CAACQ,EAAE,KAAK;IACpE,MAAM,GAAGqB,WAAW,CAAC,GAAG5C,UAAU,CAAE6C,CAAC,IAAKA,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACnD,MAAMtC,KAAK,GAAG4B,GAAG,CAAChB,QAAQ,CAAC,CAAC;IAC5B,MAAM2B,QAAQ,GAAG7C,MAAM,CAACM,KAAK,CAAC;IAC9B,MAAMwC,WAAW,GAAG9C,MAAM,CAACoB,QAAQ,CAAC;IACpC,MAAM2B,aAAa,GAAG/C,MAAM,CAACqB,UAAU,CAAC;IACxC,MAAM2B,UAAU,GAAGhD,MAAM,CAAC,KAAK,CAAC;IAChC,MAAMiD,eAAe,GAAGjD,MAAM,CAAC,CAAC;IAChC,IAAIiD,eAAe,CAACC,OAAO,KAAK,KAAK,CAAC,EAAE;MACtCD,eAAe,CAACC,OAAO,GAAG9B,QAAQ,CAACd,KAAK,CAAC;IAC3C;IACA,IAAI6C,aAAa;IACjB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIP,QAAQ,CAACK,OAAO,KAAK5C,KAAK,IAAIwC,WAAW,CAACI,OAAO,KAAK9B,QAAQ,IAAI2B,aAAa,CAACG,OAAO,KAAK7B,UAAU,IAAI2B,UAAU,CAACE,OAAO,EAAE;MAChIC,aAAa,GAAG/B,QAAQ,CAACd,KAAK,CAAC;MAC/B8C,gBAAgB,GAAG,CAAC/B,UAAU,CAAC4B,eAAe,CAACC,OAAO,EAAEC,aAAa,CAAC;IACxE;IACAX,yBAAyB,CAAC,MAAM;MAC9B,IAAIY,gBAAgB,EAAE;QACpBH,eAAe,CAACC,OAAO,GAAGC,aAAa;MACzC;MACAN,QAAQ,CAACK,OAAO,GAAG5C,KAAK;MACxBwC,WAAW,CAACI,OAAO,GAAG9B,QAAQ;MAC9B2B,aAAa,CAACG,OAAO,GAAG7B,UAAU;MAClC2B,UAAU,CAACE,OAAO,GAAG,KAAK;IAC5B,CAAC,CAAC;IACF,MAAMG,0BAA0B,GAAGrD,MAAM,CAACM,KAAK,CAAC;IAChDkC,yBAAyB,CAAC,MAAM;MAC9B,MAAMvB,QAAQ,GAAGA,CAAA,KAAM;QACrB,IAAI;UACF,MAAML,SAAS,GAAGsB,GAAG,CAAChB,QAAQ,CAAC,CAAC;UAChC,MAAMoC,cAAc,GAAGR,WAAW,CAACI,OAAO,CAACtC,SAAS,CAAC;UACrD,IAAI,CAACmC,aAAa,CAACG,OAAO,CAACD,eAAe,CAACC,OAAO,EAAEI,cAAc,CAAC,EAAE;YACnET,QAAQ,CAACK,OAAO,GAAGtC,SAAS;YAC5BqC,eAAe,CAACC,OAAO,GAAGI,cAAc;YACxCX,WAAW,CAAC,CAAC;UACf;QACF,CAAC,CAAC,OAAOY,KAAK,EAAE;UACdP,UAAU,CAACE,OAAO,GAAG,IAAI;UACzBP,WAAW,CAAC,CAAC;QACf;MACF,CAAC;MACD,MAAMa,WAAW,GAAGtB,GAAG,CAACH,SAAS,CAACd,QAAQ,CAAC;MAC3C,IAAIiB,GAAG,CAAChB,QAAQ,CAAC,CAAC,KAAKmC,0BAA0B,CAACH,OAAO,EAAE;QACzDjC,QAAQ,CAAC,CAAC;MACZ;MACA,OAAOuC,WAAW;IACpB,CAAC,EAAE,EAAE,CAAC;IACN,MAAMC,aAAa,GAAGL,gBAAgB,GAAGD,aAAa,GAAGF,eAAe,CAACC,OAAO;IAChFjD,aAAa,CAACwD,aAAa,CAAC;IAC5B,OAAOA,aAAa;EACtB,CAAC;EACD3C,MAAM,CAACC,MAAM,CAAC2B,QAAQ,EAAER,GAAG,CAAC;EAC5BQ,QAAQ,CAACgB,MAAM,CAACC,QAAQ,CAAC,GAAG,YAAW;IACrCpC,OAAO,CAACC,IAAI,CAAC,oEAAoE,CAAC;IAClF,MAAMoC,KAAK,GAAG,CAAClB,QAAQ,EAAER,GAAG,CAAC;IAC7B,OAAO;MACL2B,IAAIA,CAAA,EAAG;QACL,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,IAAI,CAAC;QAC9B,OAAO;UAAEC,KAAK,EAAEJ,KAAK,CAACK,KAAK,CAAC,CAAC;UAAEH;QAAK,CAAC;MACvC;IACF,CAAC;EACH,CAAC;EACD,OAAOpB,QAAQ;AACjB;AAEA,SAASD,MAAM,IAAIyB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}