{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst col = new THREE.Color();\n/**\n * In `@react-three/fiber` after `v8.0.0` but prior to `v8.1.0`, `state.size` contained only dimension\n * information. After `v8.1.0`, position information (`top`, `left`) was added\n *\n * @todo remove this when drei supports v9 and up\n */\n\nfunction isNonLegacyCanvasSize(size) {\n  return 'top' in size;\n}\nfunction computeContainerPosition(canvasSize, trackRect) {\n  const {\n    right,\n    top,\n    left: trackLeft,\n    bottom: trackBottom,\n    width,\n    height\n  } = trackRect;\n  const isOffscreen = trackRect.bottom < 0 || top > canvasSize.height || right < 0 || trackRect.left > canvasSize.width;\n  if (isNonLegacyCanvasSize(canvasSize)) {\n    const canvasBottom = canvasSize.top + canvasSize.height;\n    const bottom = canvasBottom - trackBottom;\n    const left = trackLeft - canvasSize.left;\n    return {\n      position: {\n        width,\n        height,\n        left,\n        top,\n        bottom,\n        right\n      },\n      isOffscreen\n    };\n  } // Fall back on old behavior if r3f < 8.1.0\n\n  const bottom = canvasSize.height - trackBottom;\n  return {\n    position: {\n      width,\n      height,\n      top,\n      left: trackLeft,\n      bottom,\n      right\n    },\n    isOffscreen\n  };\n}\nfunction Container({\n  canvasSize,\n  scene,\n  index,\n  children,\n  frames,\n  rect,\n  track\n}) {\n  const get = useThree(state => state.get);\n  const camera = useThree(state => state.camera);\n  const virtualScene = useThree(state => state.scene);\n  const setEvents = useThree(state => state.setEvents);\n  let frameCount = 0;\n  useFrame(state => {\n    if (frames === Infinity || frameCount <= frames) {\n      var _track$current;\n      rect.current = (_track$current = track.current) == null ? void 0 : _track$current.getBoundingClientRect();\n      frameCount++;\n    }\n    if (rect.current) {\n      const {\n        position: {\n          left,\n          bottom,\n          width,\n          height\n        },\n        isOffscreen\n      } = computeContainerPosition(canvasSize, rect.current);\n      const aspect = width / height;\n      if (isOrthographicCamera(camera)) {\n        if (camera.left !== width / -2 || camera.right !== width / 2 || camera.top !== height / 2 || camera.bottom !== height / -2) {\n          Object.assign(camera, {\n            left: width / -2,\n            right: width / 2,\n            top: height / 2,\n            bottom: height / -2\n          });\n          camera.updateProjectionMatrix();\n        }\n      } else if (camera.aspect !== aspect) {\n        camera.aspect = aspect;\n        camera.updateProjectionMatrix();\n      }\n      state.gl.setViewport(left, bottom, width, height);\n      state.gl.setScissor(left, bottom, width, height);\n      state.gl.setScissorTest(true);\n      if (isOffscreen) {\n        state.gl.getClearColor(col);\n        state.gl.setClearColor(col, state.gl.getClearAlpha());\n        state.gl.clear(true, true);\n      } else {\n        // When children are present render the portalled scene, otherwise the default scene\n        state.gl.render(children ? virtualScene : scene, camera);\n      } // Restore the default state\n\n      state.gl.setScissorTest(true);\n    }\n  }, index);\n  React.useEffect(() => {\n    // Connect the event layer to the tracking element\n    const old = get().events.connected;\n    setEvents({\n      connected: track.current\n    });\n    return () => setEvents({\n      connected: old\n    });\n  }, []);\n  React.useEffect(() => {\n    if (isNonLegacyCanvasSize(canvasSize)) {\n      return;\n    }\n    console.warn('Detected @react-three/fiber canvas size does not include position information. <View /> may not work as expected. ' + 'Upgrade to @react-three/fiber ^8.1.0 for support.\\n See https://github.com/pmndrs/drei/issues/944');\n  }, []);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\nconst View = ({\n  track,\n  index = 1,\n  frames = Infinity,\n  children\n}) => {\n  var _rect$current, _rect$current2;\n  const rect = React.useRef(null);\n  const {\n    size,\n    scene\n  } = useThree();\n  const [virtualScene] = React.useState(() => new THREE.Scene());\n  const compute = React.useCallback((event, state) => {\n    if (rect.current && track.current && event.target === track.current) {\n      const {\n        width,\n        height,\n        left,\n        top\n      } = rect.current;\n      const x = event.clientX - left;\n      const y = event.clientY - top;\n      state.pointer.set(x / width * 2 - 1, -(y / height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    }\n  }, [rect, track]);\n  const [ready, toggle] = React.useReducer(() => true, false);\n  React.useEffect(() => {\n    var _track$current2;\n\n    // We need the tracking elements bounds beforehand in order to inject it into the portal\n    rect.current = (_track$current2 = track.current) == null ? void 0 : _track$current2.getBoundingClientRect(); // And now we can proceed\n\n    toggle();\n  }, [track]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, ready && createPortal(/*#__PURE__*/React.createElement(Container, {\n    canvasSize: size,\n    frames: frames,\n    scene: scene,\n    track: track,\n    rect: rect,\n    index: index\n  }, children), virtualScene, {\n    events: {\n      compute,\n      priority: index\n    },\n    size: {\n      width: (_rect$current = rect.current) == null ? void 0 : _rect$current.width,\n      height: (_rect$current2 = rect.current) == null ? void 0 : _rect$current2.height\n    }\n  }));\n};\nexport { View };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "createPortal", "useFrame", "isOrthographicCamera", "def", "col", "Color", "isNonLegacyCanvasSize", "size", "computeContainerPosition", "canvasSize", "trackRect", "right", "top", "left", "trackLeft", "bottom", "trackBottom", "width", "height", "isOffscreen", "canvasBottom", "position", "Container", "scene", "index", "children", "frames", "rect", "track", "get", "state", "camera", "virtualScene", "setEvents", "frameCount", "Infinity", "_track$current", "current", "getBoundingClientRect", "aspect", "Object", "assign", "updateProjectionMatrix", "gl", "setViewport", "set<PERSON><PERSON>sor", "setScissorTest", "getClearColor", "setClearColor", "getClearAlpha", "clear", "render", "useEffect", "old", "events", "connected", "console", "warn", "createElement", "Fragment", "View", "_rect$current", "_rect$current2", "useRef", "useState", "Scene", "compute", "useCallback", "event", "target", "x", "clientX", "y", "clientY", "pointer", "set", "raycaster", "setFromCamera", "ready", "toggle", "useReducer", "_track$current2", "priority"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/View.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\n\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\n\nconst col = new THREE.Color();\n/**\n * In `@react-three/fiber` after `v8.0.0` but prior to `v8.1.0`, `state.size` contained only dimension\n * information. After `v8.1.0`, position information (`top`, `left`) was added\n *\n * @todo remove this when drei supports v9 and up\n */\n\nfunction isNonLegacyCanvasSize(size) {\n  return 'top' in size;\n}\n\nfunction computeContainerPosition(canvasSize, trackRect) {\n  const {\n    right,\n    top,\n    left: trackLeft,\n    bottom: trackBottom,\n    width,\n    height\n  } = trackRect;\n  const isOffscreen = trackRect.bottom < 0 || top > canvasSize.height || right < 0 || trackRect.left > canvasSize.width;\n\n  if (isNonLegacyCanvasSize(canvasSize)) {\n    const canvasBottom = canvasSize.top + canvasSize.height;\n    const bottom = canvasBottom - trackBottom;\n    const left = trackLeft - canvasSize.left;\n    return {\n      position: {\n        width,\n        height,\n        left,\n        top,\n        bottom,\n        right\n      },\n      isOffscreen\n    };\n  } // Fall back on old behavior if r3f < 8.1.0\n\n\n  const bottom = canvasSize.height - trackBottom;\n  return {\n    position: {\n      width,\n      height,\n      top,\n      left: trackLeft,\n      bottom,\n      right\n    },\n    isOffscreen\n  };\n}\n\nfunction Container({\n  canvasSize,\n  scene,\n  index,\n  children,\n  frames,\n  rect,\n  track\n}) {\n  const get = useThree(state => state.get);\n  const camera = useThree(state => state.camera);\n  const virtualScene = useThree(state => state.scene);\n  const setEvents = useThree(state => state.setEvents);\n  let frameCount = 0;\n  useFrame(state => {\n    if (frames === Infinity || frameCount <= frames) {\n      var _track$current;\n\n      rect.current = (_track$current = track.current) == null ? void 0 : _track$current.getBoundingClientRect();\n      frameCount++;\n    }\n\n    if (rect.current) {\n      const {\n        position: {\n          left,\n          bottom,\n          width,\n          height\n        },\n        isOffscreen\n      } = computeContainerPosition(canvasSize, rect.current);\n      const aspect = width / height;\n\n      if (isOrthographicCamera(camera)) {\n        if (camera.left !== width / -2 || camera.right !== width / 2 || camera.top !== height / 2 || camera.bottom !== height / -2) {\n          Object.assign(camera, {\n            left: width / -2,\n            right: width / 2,\n            top: height / 2,\n            bottom: height / -2\n          });\n          camera.updateProjectionMatrix();\n        }\n      } else if (camera.aspect !== aspect) {\n        camera.aspect = aspect;\n        camera.updateProjectionMatrix();\n      }\n\n      state.gl.setViewport(left, bottom, width, height);\n      state.gl.setScissor(left, bottom, width, height);\n      state.gl.setScissorTest(true);\n\n      if (isOffscreen) {\n        state.gl.getClearColor(col);\n        state.gl.setClearColor(col, state.gl.getClearAlpha());\n        state.gl.clear(true, true);\n      } else {\n        // When children are present render the portalled scene, otherwise the default scene\n        state.gl.render(children ? virtualScene : scene, camera);\n      } // Restore the default state\n\n\n      state.gl.setScissorTest(true);\n    }\n  }, index);\n  React.useEffect(() => {\n    // Connect the event layer to the tracking element\n    const old = get().events.connected;\n    setEvents({\n      connected: track.current\n    });\n    return () => setEvents({\n      connected: old\n    });\n  }, []);\n  React.useEffect(() => {\n    if (isNonLegacyCanvasSize(canvasSize)) {\n      return;\n    }\n\n    console.warn('Detected @react-three/fiber canvas size does not include position information. <View /> may not work as expected. ' + 'Upgrade to @react-three/fiber ^8.1.0 for support.\\n See https://github.com/pmndrs/drei/issues/944');\n  }, []);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n}\n\nconst View = ({\n  track,\n  index = 1,\n  frames = Infinity,\n  children\n}) => {\n  var _rect$current, _rect$current2;\n\n  const rect = React.useRef(null);\n  const {\n    size,\n    scene\n  } = useThree();\n  const [virtualScene] = React.useState(() => new THREE.Scene());\n  const compute = React.useCallback((event, state) => {\n    if (rect.current && track.current && event.target === track.current) {\n      const {\n        width,\n        height,\n        left,\n        top\n      } = rect.current;\n      const x = event.clientX - left;\n      const y = event.clientY - top;\n      state.pointer.set(x / width * 2 - 1, -(y / height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    }\n  }, [rect, track]);\n  const [ready, toggle] = React.useReducer(() => true, false);\n  React.useEffect(() => {\n    var _track$current2;\n\n    // We need the tracking elements bounds beforehand in order to inject it into the portal\n    rect.current = (_track$current2 = track.current) == null ? void 0 : _track$current2.getBoundingClientRect(); // And now we can proceed\n\n    toggle();\n  }, [track]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, ready && createPortal( /*#__PURE__*/React.createElement(Container, {\n    canvasSize: size,\n    frames: frames,\n    scene: scene,\n    track: track,\n    rect: rect,\n    index: index\n  }, children), virtualScene, {\n    events: {\n      compute,\n      priority: index\n    },\n    size: {\n      width: (_rect$current = rect.current) == null ? void 0 : _rect$current.width,\n      height: (_rect$current2 = rect.current) == null ? void 0 : _rect$current2.height\n    }\n  }));\n};\n\nexport { View };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,oBAAoB;AAErE,MAAMC,oBAAoB,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACD,oBAAoB;AAEnE,MAAME,GAAG,GAAG,IAAIN,KAAK,CAACO,KAAK,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EACnC,OAAO,KAAK,IAAIA,IAAI;AACtB;AAEA,SAASC,wBAAwBA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACvD,MAAM;IACJC,KAAK;IACLC,GAAG;IACHC,IAAI,EAAEC,SAAS;IACfC,MAAM,EAAEC,WAAW;IACnBC,KAAK;IACLC;EACF,CAAC,GAAGR,SAAS;EACb,MAAMS,WAAW,GAAGT,SAAS,CAACK,MAAM,GAAG,CAAC,IAAIH,GAAG,GAAGH,UAAU,CAACS,MAAM,IAAIP,KAAK,GAAG,CAAC,IAAID,SAAS,CAACG,IAAI,GAAGJ,UAAU,CAACQ,KAAK;EAErH,IAAIX,qBAAqB,CAACG,UAAU,CAAC,EAAE;IACrC,MAAMW,YAAY,GAAGX,UAAU,CAACG,GAAG,GAAGH,UAAU,CAACS,MAAM;IACvD,MAAMH,MAAM,GAAGK,YAAY,GAAGJ,WAAW;IACzC,MAAMH,IAAI,GAAGC,SAAS,GAAGL,UAAU,CAACI,IAAI;IACxC,OAAO;MACLQ,QAAQ,EAAE;QACRJ,KAAK;QACLC,MAAM;QACNL,IAAI;QACJD,GAAG;QACHG,MAAM;QACNJ;MACF,CAAC;MACDQ;IACF,CAAC;EACH,CAAC,CAAC;;EAGF,MAAMJ,MAAM,GAAGN,UAAU,CAACS,MAAM,GAAGF,WAAW;EAC9C,OAAO;IACLK,QAAQ,EAAE;MACRJ,KAAK;MACLC,MAAM;MACNN,GAAG;MACHC,IAAI,EAAEC,SAAS;MACfC,MAAM;MACNJ;IACF,CAAC;IACDQ;EACF,CAAC;AACH;AAEA,SAASG,SAASA,CAAC;EACjBb,UAAU;EACVc,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRC,MAAM;EACNC,IAAI;EACJC;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAG9B,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACD,GAAG,CAAC;EACxC,MAAME,MAAM,GAAGhC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EAC9C,MAAMC,YAAY,GAAGjC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACP,KAAK,CAAC;EACnD,MAAMU,SAAS,GAAGlC,QAAQ,CAAC+B,KAAK,IAAIA,KAAK,CAACG,SAAS,CAAC;EACpD,IAAIC,UAAU,GAAG,CAAC;EAClBjC,QAAQ,CAAC6B,KAAK,IAAI;IAChB,IAAIJ,MAAM,KAAKS,QAAQ,IAAID,UAAU,IAAIR,MAAM,EAAE;MAC/C,IAAIU,cAAc;MAElBT,IAAI,CAACU,OAAO,GAAG,CAACD,cAAc,GAAGR,KAAK,CAACS,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,cAAc,CAACE,qBAAqB,CAAC,CAAC;MACzGJ,UAAU,EAAE;IACd;IAEA,IAAIP,IAAI,CAACU,OAAO,EAAE;MAChB,MAAM;QACJhB,QAAQ,EAAE;UACRR,IAAI;UACJE,MAAM;UACNE,KAAK;UACLC;QACF,CAAC;QACDC;MACF,CAAC,GAAGX,wBAAwB,CAACC,UAAU,EAAEkB,IAAI,CAACU,OAAO,CAAC;MACtD,MAAME,MAAM,GAAGtB,KAAK,GAAGC,MAAM;MAE7B,IAAIhB,oBAAoB,CAAC6B,MAAM,CAAC,EAAE;QAChC,IAAIA,MAAM,CAAClB,IAAI,KAAKI,KAAK,GAAG,CAAC,CAAC,IAAIc,MAAM,CAACpB,KAAK,KAAKM,KAAK,GAAG,CAAC,IAAIc,MAAM,CAACnB,GAAG,KAAKM,MAAM,GAAG,CAAC,IAAIa,MAAM,CAAChB,MAAM,KAAKG,MAAM,GAAG,CAAC,CAAC,EAAE;UAC1HsB,MAAM,CAACC,MAAM,CAACV,MAAM,EAAE;YACpBlB,IAAI,EAAEI,KAAK,GAAG,CAAC,CAAC;YAChBN,KAAK,EAAEM,KAAK,GAAG,CAAC;YAChBL,GAAG,EAAEM,MAAM,GAAG,CAAC;YACfH,MAAM,EAAEG,MAAM,GAAG,CAAC;UACpB,CAAC,CAAC;UACFa,MAAM,CAACW,sBAAsB,CAAC,CAAC;QACjC;MACF,CAAC,MAAM,IAAIX,MAAM,CAACQ,MAAM,KAAKA,MAAM,EAAE;QACnCR,MAAM,CAACQ,MAAM,GAAGA,MAAM;QACtBR,MAAM,CAACW,sBAAsB,CAAC,CAAC;MACjC;MAEAZ,KAAK,CAACa,EAAE,CAACC,WAAW,CAAC/B,IAAI,EAAEE,MAAM,EAAEE,KAAK,EAAEC,MAAM,CAAC;MACjDY,KAAK,CAACa,EAAE,CAACE,UAAU,CAAChC,IAAI,EAAEE,MAAM,EAAEE,KAAK,EAAEC,MAAM,CAAC;MAChDY,KAAK,CAACa,EAAE,CAACG,cAAc,CAAC,IAAI,CAAC;MAE7B,IAAI3B,WAAW,EAAE;QACfW,KAAK,CAACa,EAAE,CAACI,aAAa,CAAC3C,GAAG,CAAC;QAC3B0B,KAAK,CAACa,EAAE,CAACK,aAAa,CAAC5C,GAAG,EAAE0B,KAAK,CAACa,EAAE,CAACM,aAAa,CAAC,CAAC,CAAC;QACrDnB,KAAK,CAACa,EAAE,CAACO,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACApB,KAAK,CAACa,EAAE,CAACQ,MAAM,CAAC1B,QAAQ,GAAGO,YAAY,GAAGT,KAAK,EAAEQ,MAAM,CAAC;MAC1D,CAAC,CAAC;;MAGFD,KAAK,CAACa,EAAE,CAACG,cAAc,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC,EAAEtB,KAAK,CAAC;EACT3B,KAAK,CAACuD,SAAS,CAAC,MAAM;IACpB;IACA,MAAMC,GAAG,GAAGxB,GAAG,CAAC,CAAC,CAACyB,MAAM,CAACC,SAAS;IAClCtB,SAAS,CAAC;MACRsB,SAAS,EAAE3B,KAAK,CAACS;IACnB,CAAC,CAAC;IACF,OAAO,MAAMJ,SAAS,CAAC;MACrBsB,SAAS,EAAEF;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACNxD,KAAK,CAACuD,SAAS,CAAC,MAAM;IACpB,IAAI9C,qBAAqB,CAACG,UAAU,CAAC,EAAE;MACrC;IACF;IAEA+C,OAAO,CAACC,IAAI,CAAC,oHAAoH,GAAG,mGAAmG,CAAC;EAC1O,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa5D,KAAK,CAAC6D,aAAa,CAAC7D,KAAK,CAAC8D,QAAQ,EAAE,IAAI,EAAElC,QAAQ,CAAC;AACzE;AAEA,MAAMmC,IAAI,GAAGA,CAAC;EACZhC,KAAK;EACLJ,KAAK,GAAG,CAAC;EACTE,MAAM,GAAGS,QAAQ;EACjBV;AACF,CAAC,KAAK;EACJ,IAAIoC,aAAa,EAAEC,cAAc;EAEjC,MAAMnC,IAAI,GAAG9B,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM;IACJxD,IAAI;IACJgB;EACF,CAAC,GAAGxB,QAAQ,CAAC,CAAC;EACd,MAAM,CAACiC,YAAY,CAAC,GAAGnC,KAAK,CAACmE,QAAQ,CAAC,MAAM,IAAIlE,KAAK,CAACmE,KAAK,CAAC,CAAC,CAAC;EAC9D,MAAMC,OAAO,GAAGrE,KAAK,CAACsE,WAAW,CAAC,CAACC,KAAK,EAAEtC,KAAK,KAAK;IAClD,IAAIH,IAAI,CAACU,OAAO,IAAIT,KAAK,CAACS,OAAO,IAAI+B,KAAK,CAACC,MAAM,KAAKzC,KAAK,CAACS,OAAO,EAAE;MACnE,MAAM;QACJpB,KAAK;QACLC,MAAM;QACNL,IAAI;QACJD;MACF,CAAC,GAAGe,IAAI,CAACU,OAAO;MAChB,MAAMiC,CAAC,GAAGF,KAAK,CAACG,OAAO,GAAG1D,IAAI;MAC9B,MAAM2D,CAAC,GAAGJ,KAAK,CAACK,OAAO,GAAG7D,GAAG;MAC7BkB,KAAK,CAAC4C,OAAO,CAACC,GAAG,CAACL,CAAC,GAAGrD,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAEuD,CAAC,GAAGtD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC3DY,KAAK,CAAC8C,SAAS,CAACC,aAAa,CAAC/C,KAAK,CAAC4C,OAAO,EAAE5C,KAAK,CAACC,MAAM,CAAC;IAC5D;EACF,CAAC,EAAE,CAACJ,IAAI,EAAEC,KAAK,CAAC,CAAC;EACjB,MAAM,CAACkD,KAAK,EAAEC,MAAM,CAAC,GAAGlF,KAAK,CAACmF,UAAU,CAAC,MAAM,IAAI,EAAE,KAAK,CAAC;EAC3DnF,KAAK,CAACuD,SAAS,CAAC,MAAM;IACpB,IAAI6B,eAAe;;IAEnB;IACAtD,IAAI,CAACU,OAAO,GAAG,CAAC4C,eAAe,GAAGrD,KAAK,CAACS,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4C,eAAe,CAAC3C,qBAAqB,CAAC,CAAC,CAAC,CAAC;;IAE7GyC,MAAM,CAAC,CAAC;EACV,CAAC,EAAE,CAACnD,KAAK,CAAC,CAAC;EACX,OAAO,aAAa/B,KAAK,CAAC6D,aAAa,CAAC7D,KAAK,CAAC8D,QAAQ,EAAE,IAAI,EAAEmB,KAAK,IAAI9E,YAAY,CAAE,aAAaH,KAAK,CAAC6D,aAAa,CAACpC,SAAS,EAAE;IAC/Hb,UAAU,EAAEF,IAAI;IAChBmB,MAAM,EAAEA,MAAM;IACdH,KAAK,EAAEA,KAAK;IACZK,KAAK,EAAEA,KAAK;IACZD,IAAI,EAAEA,IAAI;IACVH,KAAK,EAAEA;EACT,CAAC,EAAEC,QAAQ,CAAC,EAAEO,YAAY,EAAE;IAC1BsB,MAAM,EAAE;MACNY,OAAO;MACPgB,QAAQ,EAAE1D;IACZ,CAAC;IACDjB,IAAI,EAAE;MACJU,KAAK,EAAE,CAAC4C,aAAa,GAAGlC,IAAI,CAACU,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwB,aAAa,CAAC5C,KAAK;MAC5EC,MAAM,EAAE,CAAC4C,cAAc,GAAGnC,IAAI,CAACU,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyB,cAAc,CAAC5C;IAC5E;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS0C,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}