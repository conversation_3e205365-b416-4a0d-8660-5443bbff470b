{"ast": null, "code": "let e = -1;\nconst t = t => {\n    addEventListener(\"pageshow\", n => {\n      n.persisted && (e = n.timeStamp, t(n));\n    }, !0);\n  },\n  n = (e, t, n, i) => {\n    let o, s;\n    return r => {\n      t.value >= 0 && (r || i) && (s = t.value - (o ?? 0), (s || void 0 === o) && (o = t.value, t.delta = s, t.rating = ((e, t) => e > t[1] ? \"poor\" : e > t[0] ? \"needs-improvement\" : \"good\")(t.value, n), e(t)));\n    };\n  },\n  i = e => {\n    requestAnimationFrame(() => requestAnimationFrame(() => e()));\n  },\n  o = () => {\n    const e = performance.getEntriesByType(\"navigation\")[0];\n    if (e && e.responseStart > 0 && e.responseStart < performance.now()) return e;\n  },\n  s = () => {\n    const e = o();\n    return e?.activationStart ?? 0;\n  },\n  r = (t, n = -1) => {\n    const i = o();\n    let r = \"navigate\";\n    e >= 0 ? r = \"back-forward-cache\" : i && (document.prerendering || s() > 0 ? r = \"prerender\" : document.wasDiscarded ? r = \"restore\" : i.type && (r = i.type.replace(/_/g, \"-\")));\n    return {\n      name: t,\n      value: n,\n      rating: \"good\",\n      delta: 0,\n      entries: [],\n      id: `v5-${Date.now()}-${Math.floor(8999999999999 * Math.random()) + 1e12}`,\n      navigationType: r\n    };\n  },\n  c = new WeakMap();\nfunction a(e, t) {\n  return c.get(e) || c.set(e, new t()), c.get(e);\n}\nclass d {\n  t;\n  i = 0;\n  o = [];\n  h(e) {\n    if (e.hadRecentInput) return;\n    const t = this.o[0],\n      n = this.o.at(-1);\n    this.i && t && n && e.startTime - n.startTime < 1e3 && e.startTime - t.startTime < 5e3 ? (this.i += e.value, this.o.push(e)) : (this.i = e.value, this.o = [e]), this.t?.(e);\n  }\n}\nconst h = (e, t, n = {}) => {\n    try {\n      if (PerformanceObserver.supportedEntryTypes.includes(e)) {\n        const i = new PerformanceObserver(e => {\n          Promise.resolve().then(() => {\n            t(e.getEntries());\n          });\n        });\n        return i.observe({\n          type: e,\n          buffered: !0,\n          ...n\n        }), i;\n      }\n    } catch {}\n  },\n  f = e => {\n    let t = !1;\n    return () => {\n      t || (e(), t = !0);\n    };\n  };\nlet u = -1;\nconst l = () => \"hidden\" !== document.visibilityState || document.prerendering ? 1 / 0 : 0,\n  m = e => {\n    \"hidden\" === document.visibilityState && u > -1 && (u = \"visibilitychange\" === e.type ? e.timeStamp : 0, v());\n  },\n  g = () => {\n    addEventListener(\"visibilitychange\", m, !0), addEventListener(\"prerenderingchange\", m, !0);\n  },\n  v = () => {\n    removeEventListener(\"visibilitychange\", m, !0), removeEventListener(\"prerenderingchange\", m, !0);\n  },\n  p = () => {\n    if (u < 0) {\n      const e = s(),\n        n = document.prerendering ? void 0 : globalThis.performance.getEntriesByType(\"visibility-state\").filter(t => \"hidden\" === t.name && t.startTime > e)[0]?.startTime;\n      u = n ?? l(), g(), t(() => {\n        setTimeout(() => {\n          u = l(), g();\n        });\n      });\n    }\n    return {\n      get firstHiddenTime() {\n        return u;\n      }\n    };\n  },\n  y = e => {\n    document.prerendering ? addEventListener(\"prerenderingchange\", () => e(), !0) : e();\n  },\n  b = [1800, 3e3],\n  P = (e, o = {}) => {\n    y(() => {\n      const c = p();\n      let a,\n        d = r(\"FCP\");\n      const f = h(\"paint\", e => {\n        for (const t of e) \"first-contentful-paint\" === t.name && (f.disconnect(), t.startTime < c.firstHiddenTime && (d.value = Math.max(t.startTime - s(), 0), d.entries.push(t), a(!0)));\n      });\n      f && (a = n(e, d, b, o.reportAllChanges), t(t => {\n        d = r(\"FCP\"), a = n(e, d, b, o.reportAllChanges), i(() => {\n          d.value = performance.now() - t.timeStamp, a(!0);\n        });\n      }));\n    });\n  },\n  T = [.1, .25],\n  E = (e, o = {}) => {\n    P(f(() => {\n      let s,\n        c = r(\"CLS\", 0);\n      const f = a(o, d),\n        u = e => {\n          for (const t of e) f.h(t);\n          f.i > c.value && (c.value = f.i, c.entries = f.o, s());\n        },\n        l = h(\"layout-shift\", u);\n      l && (s = n(e, c, T, o.reportAllChanges), document.addEventListener(\"visibilitychange\", () => {\n        \"hidden\" === document.visibilityState && (u(l.takeRecords()), s(!0));\n      }), t(() => {\n        f.i = 0, c = r(\"CLS\", 0), s = n(e, c, T, o.reportAllChanges), i(() => s());\n      }), setTimeout(s));\n    }));\n  };\nlet _ = 0,\n  L = 1 / 0,\n  M = 0;\nconst C = e => {\n  for (const t of e) t.interactionId && (L = Math.min(L, t.interactionId), M = Math.max(M, t.interactionId), _ = M ? (M - L) / 7 + 1 : 0);\n};\nlet I;\nconst w = () => I ? _ : performance.interactionCount ?? 0,\n  F = () => {\n    \"interactionCount\" in performance || I || (I = h(\"event\", C, {\n      type: \"event\",\n      buffered: !0,\n      durationThreshold: 0\n    }));\n  };\nlet k = 0;\nclass A {\n  u = [];\n  l = new Map();\n  m;\n  v;\n  p() {\n    k = w(), this.u.length = 0, this.l.clear();\n  }\n  P() {\n    const e = Math.min(this.u.length - 1, Math.floor((w() - k) / 50));\n    return this.u[e];\n  }\n  h(e) {\n    if (this.m?.(e), !e.interactionId && \"first-input\" !== e.entryType) return;\n    const t = this.u.at(-1);\n    let n = this.l.get(e.interactionId);\n    if (n || this.u.length < 10 || e.duration > t.T) {\n      if (n ? e.duration > n.T ? (n.entries = [e], n.T = e.duration) : e.duration === n.T && e.startTime === n.entries[0].startTime && n.entries.push(e) : (n = {\n        id: e.interactionId,\n        entries: [e],\n        T: e.duration\n      }, this.l.set(n.id, n), this.u.push(n)), this.u.sort((e, t) => t.T - e.T), this.u.length > 10) {\n        const e = this.u.splice(10);\n        for (const t of e) this.l.delete(t.id);\n      }\n      this.v?.(n);\n    }\n  }\n}\nconst B = e => {\n    const t = globalThis.requestIdleCallback || setTimeout;\n    \"hidden\" === document.visibilityState ? e() : (e = f(e), document.addEventListener(\"visibilitychange\", e, {\n      once: !0\n    }), t(() => {\n      e(), document.removeEventListener(\"visibilitychange\", e);\n    }));\n  },\n  N = [200, 500],\n  S = (e, i = {}) => {\n    globalThis.PerformanceEventTiming && \"interactionId\" in PerformanceEventTiming.prototype && y(() => {\n      F();\n      let o,\n        s = r(\"INP\");\n      const c = a(i, A),\n        d = e => {\n          B(() => {\n            for (const t of e) c.h(t);\n            const t = c.P();\n            t && t.T !== s.value && (s.value = t.T, s.entries = t.entries, o());\n          });\n        },\n        f = h(\"event\", d, {\n          durationThreshold: i.durationThreshold ?? 40\n        });\n      o = n(e, s, N, i.reportAllChanges), f && (f.observe({\n        type: \"first-input\",\n        buffered: !0\n      }), document.addEventListener(\"visibilitychange\", () => {\n        \"hidden\" === document.visibilityState && (d(f.takeRecords()), o(!0));\n      }), t(() => {\n        c.p(), s = r(\"INP\"), o = n(e, s, N, i.reportAllChanges);\n      }));\n    });\n  };\nclass q {\n  m;\n  h(e) {\n    this.m?.(e);\n  }\n}\nconst x = [2500, 4e3],\n  O = (e, o = {}) => {\n    y(() => {\n      const c = p();\n      let d,\n        u = r(\"LCP\");\n      const l = a(o, q),\n        m = e => {\n          o.reportAllChanges || (e = e.slice(-1));\n          for (const t of e) l.h(t), t.startTime < c.firstHiddenTime && (u.value = Math.max(t.startTime - s(), 0), u.entries = [t], d());\n        },\n        g = h(\"largest-contentful-paint\", m);\n      if (g) {\n        d = n(e, u, x, o.reportAllChanges);\n        const s = f(() => {\n          m(g.takeRecords()), g.disconnect(), d(!0);\n        });\n        for (const e of [\"keydown\", \"click\", \"visibilitychange\"]) addEventListener(e, () => B(s), {\n          capture: !0,\n          once: !0\n        });\n        t(t => {\n          u = r(\"LCP\"), d = n(e, u, x, o.reportAllChanges), i(() => {\n            u.value = performance.now() - t.timeStamp, d(!0);\n          });\n        });\n      }\n    });\n  },\n  $ = [800, 1800],\n  D = e => {\n    document.prerendering ? y(() => D(e)) : \"complete\" !== document.readyState ? addEventListener(\"load\", () => D(e), !0) : setTimeout(e);\n  },\n  H = (e, i = {}) => {\n    let c = r(\"TTFB\"),\n      a = n(e, c, $, i.reportAllChanges);\n    D(() => {\n      const d = o();\n      d && (c.value = Math.max(d.responseStart - s(), 0), c.entries = [d], a(!0), t(() => {\n        c = r(\"TTFB\", 0), a = n(e, c, $, i.reportAllChanges), a(!0);\n      }));\n    });\n  };\nexport { T as CLSThresholds, b as FCPThresholds, N as INPThresholds, x as LCPThresholds, $ as TTFBThresholds, E as onCLS, P as onFCP, S as onINP, O as onLCP, H as onTTFB };", "map": {"version": 3, "names": ["e", "t", "addEventListener", "n", "persisted", "timeStamp", "i", "o", "s", "r", "value", "delta", "rating", "requestAnimationFrame", "performance", "getEntriesByType", "responseStart", "now", "activationStart", "document", "prerendering", "wasDiscarded", "type", "replace", "name", "entries", "id", "Date", "Math", "floor", "random", "navigationType", "c", "WeakMap", "a", "get", "set", "d", "h", "hadRecentInput", "at", "startTime", "push", "PerformanceObserver", "supportedEntryTypes", "includes", "Promise", "resolve", "then", "getEntries", "observe", "buffered", "f", "u", "l", "visibilityState", "m", "v", "g", "removeEventListener", "p", "globalThis", "filter", "setTimeout", "firstHiddenTime", "y", "b", "P", "disconnect", "max", "reportAllChanges", "T", "E", "takeRecords", "_", "L", "M", "C", "interactionId", "min", "I", "w", "interactionCount", "F", "durationThreshold", "k", "A", "Map", "length", "clear", "entryType", "duration", "sort", "splice", "delete", "B", "requestIdleCallback", "once", "N", "S", "PerformanceEventTiming", "prototype", "q", "x", "O", "slice", "capture", "$", "D", "readyState", "H", "CLSThresholds", "FCPThresholds", "INPThresholds", "LCPThresholds", "TTFBThresholds", "onCLS", "onFCP", "onINP", "onLCP", "onTTFB"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/web-vitals/dist/web-vitals.js"], "sourcesContent": ["let e=-1;const t=t=>{addEventListener(\"pageshow\",(n=>{n.persisted&&(e=n.timeStamp,t(n))}),!0)},n=(e,t,n,i)=>{let o,s;return r=>{t.value>=0&&(r||i)&&(s=t.value-(o??0),(s||void 0===o)&&(o=t.value,t.delta=s,t.rating=((e,t)=>e>t[1]?\"poor\":e>t[0]?\"needs-improvement\":\"good\")(t.value,n),e(t)))}},i=e=>{requestAnimationFrame((()=>requestAnimationFrame((()=>e()))))},o=()=>{const e=performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},s=()=>{const e=o();return e?.activationStart??0},r=(t,n=-1)=>{const i=o();let r=\"navigate\";e>=0?r=\"back-forward-cache\":i&&(document.prerendering||s()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":i.type&&(r=i.type.replace(/_/g,\"-\")));return{name:t,value:n,rating:\"good\",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function a(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class d{t;i=0;o=[];h(e){if(e.hadRecentInput)return;const t=this.o[0],n=this.o.at(-1);this.i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}const h=(e,t,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const i=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return i.observe({type:e,buffered:!0,...n}),i}}catch{}},f=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let u=-1;const l=()=>\"hidden\"!==document.visibilityState||document.prerendering?1/0:0,m=e=>{\"hidden\"===document.visibilityState&&u>-1&&(u=\"visibilitychange\"===e.type?e.timeStamp:0,v())},g=()=>{addEventListener(\"visibilitychange\",m,!0),addEventListener(\"prerenderingchange\",m,!0)},v=()=>{removeEventListener(\"visibilitychange\",m,!0),removeEventListener(\"prerenderingchange\",m,!0)},p=()=>{if(u<0){const e=s(),n=document.prerendering?void 0:globalThis.performance.getEntriesByType(\"visibility-state\").filter((t=>\"hidden\"===t.name&&t.startTime>e))[0]?.startTime;u=n??l(),g(),t((()=>{setTimeout((()=>{u=l(),g()}))}))}return{get firstHiddenTime(){return u}}},y=e=>{document.prerendering?addEventListener(\"prerenderingchange\",(()=>e()),!0):e()},b=[1800,3e3],P=(e,o={})=>{y((()=>{const c=p();let a,d=r(\"FCP\");const f=h(\"paint\",(e=>{for(const t of e)\"first-contentful-paint\"===t.name&&(f.disconnect(),t.startTime<c.firstHiddenTime&&(d.value=Math.max(t.startTime-s(),0),d.entries.push(t),a(!0)))}));f&&(a=n(e,d,b,o.reportAllChanges),t((t=>{d=r(\"FCP\"),a=n(e,d,b,o.reportAllChanges),i((()=>{d.value=performance.now()-t.timeStamp,a(!0)}))})))}))},T=[.1,.25],E=(e,o={})=>{P(f((()=>{let s,c=r(\"CLS\",0);const f=a(o,d),u=e=>{for(const t of e)f.h(t);f.i>c.value&&(c.value=f.i,c.entries=f.o,s())},l=h(\"layout-shift\",u);l&&(s=n(e,c,T,o.reportAllChanges),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(u(l.takeRecords()),s(!0))})),t((()=>{f.i=0,c=r(\"CLS\",0),s=n(e,c,T,o.reportAllChanges),i((()=>s()))})),setTimeout(s))})))};let _=0,L=1/0,M=0;const C=e=>{for(const t of e)t.interactionId&&(L=Math.min(L,t.interactionId),M=Math.max(M,t.interactionId),_=M?(M-L)/7+1:0)};let I;const w=()=>I?_:performance.interactionCount??0,F=()=>{\"interactionCount\"in performance||I||(I=h(\"event\",C,{type:\"event\",buffered:!0,durationThreshold:0}))};let k=0;class A{u=[];l=new Map;m;v;p(){k=w(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((w()-k)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&\"first-input\"!==e.entryType)return;const t=this.u.at(-1);let n=this.l.get(e.interactionId);if(n||this.u.length<10||e.duration>t.T){if(n?e.duration>n.T?(n.entries=[e],n.T=e.duration):e.duration===n.T&&e.startTime===n.entries[0].startTime&&n.entries.push(e):(n={id:e.interactionId,entries:[e],T:e.duration},this.l.set(n.id,n),this.u.push(n)),this.u.sort(((e,t)=>t.T-e.T)),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}this.v?.(n)}}}const B=e=>{const t=globalThis.requestIdleCallback||setTimeout;\"hidden\"===document.visibilityState?e():(e=f(e),document.addEventListener(\"visibilitychange\",e,{once:!0}),t((()=>{e(),document.removeEventListener(\"visibilitychange\",e)})))},N=[200,500],S=(e,i={})=>{globalThis.PerformanceEventTiming&&\"interactionId\"in PerformanceEventTiming.prototype&&y((()=>{F();let o,s=r(\"INP\");const c=a(i,A),d=e=>{B((()=>{for(const t of e)c.h(t);const t=c.P();t&&t.T!==s.value&&(s.value=t.T,s.entries=t.entries,o())}))},f=h(\"event\",d,{durationThreshold:i.durationThreshold??40});o=n(e,s,N,i.reportAllChanges),f&&(f.observe({type:\"first-input\",buffered:!0}),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(d(f.takeRecords()),o(!0))})),t((()=>{c.p(),s=r(\"INP\"),o=n(e,s,N,i.reportAllChanges)})))}))};class q{m;h(e){this.m?.(e)}}const x=[2500,4e3],O=(e,o={})=>{y((()=>{const c=p();let d,u=r(\"LCP\");const l=a(o,q),m=e=>{o.reportAllChanges||(e=e.slice(-1));for(const t of e)l.h(t),t.startTime<c.firstHiddenTime&&(u.value=Math.max(t.startTime-s(),0),u.entries=[t],d())},g=h(\"largest-contentful-paint\",m);if(g){d=n(e,u,x,o.reportAllChanges);const s=f((()=>{m(g.takeRecords()),g.disconnect(),d(!0)}));for(const e of[\"keydown\",\"click\",\"visibilitychange\"])addEventListener(e,(()=>B(s)),{capture:!0,once:!0});t((t=>{u=r(\"LCP\"),d=n(e,u,x,o.reportAllChanges),i((()=>{u.value=performance.now()-t.timeStamp,d(!0)}))}))}}))},$=[800,1800],D=e=>{document.prerendering?y((()=>D(e))):\"complete\"!==document.readyState?addEventListener(\"load\",(()=>D(e)),!0):setTimeout(e)},H=(e,i={})=>{let c=r(\"TTFB\"),a=n(e,c,$,i.reportAllChanges);D((()=>{const d=o();d&&(c.value=Math.max(d.responseStart-s(),0),c.entries=[d],a(!0),t((()=>{c=r(\"TTFB\",0),a=n(e,c,$,i.reportAllChanges),a(!0)})))}))};export{T as CLSThresholds,b as FCPThresholds,N as INPThresholds,x as LCPThresholds,$ as TTFBThresholds,E as onCLS,P as onFCP,S as onINP,O as onLCP,H as onTTFB};\n"], "mappings": "AAAA,IAAIA,CAAC,GAAC,CAAC,CAAC;AAAC,MAAMC,CAAC,GAACA,CAAC,IAAE;IAACC,gBAAgB,CAAC,UAAU,EAAEC,CAAC,IAAE;MAACA,CAAC,CAACC,SAAS,KAAGJ,CAAC,GAACG,CAAC,CAACE,SAAS,EAACJ,CAAC,CAACE,CAAC,CAAC,CAAC;IAAA,CAAC,EAAE,CAAC,CAAC,CAAC;EAAA,CAAC;EAACA,CAAC,GAACA,CAACH,CAAC,EAACC,CAAC,EAACE,CAAC,EAACG,CAAC,KAAG;IAAC,IAAIC,CAAC,EAACC,CAAC;IAAC,OAAOC,CAAC,IAAE;MAACR,CAAC,CAACS,KAAK,IAAE,CAAC,KAAGD,CAAC,IAAEH,CAAC,CAAC,KAAGE,CAAC,GAACP,CAAC,CAACS,KAAK,IAAEH,CAAC,IAAE,CAAC,CAAC,EAAC,CAACC,CAAC,IAAE,KAAK,CAAC,KAAGD,CAAC,MAAIA,CAAC,GAACN,CAAC,CAACS,KAAK,EAACT,CAAC,CAACU,KAAK,GAACH,CAAC,EAACP,CAAC,CAACW,MAAM,GAAC,CAAC,CAACZ,CAAC,EAACC,CAAC,KAAGD,CAAC,GAACC,CAAC,CAAC,CAAC,CAAC,GAAC,MAAM,GAACD,CAAC,GAACC,CAAC,CAAC,CAAC,CAAC,GAAC,mBAAmB,GAAC,MAAM,EAAEA,CAAC,CAACS,KAAK,EAACP,CAAC,CAAC,EAACH,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;EAACK,CAAC,GAACN,CAAC,IAAE;IAACa,qBAAqB,CAAE,MAAIA,qBAAqB,CAAE,MAAIb,CAAC,CAAC,CAAE,CAAE,CAAC;EAAA,CAAC;EAACO,CAAC,GAACA,CAAA,KAAI;IAAC,MAAMP,CAAC,GAACc,WAAW,CAACC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAAC,IAAGf,CAAC,IAAEA,CAAC,CAACgB,aAAa,GAAC,CAAC,IAAEhB,CAAC,CAACgB,aAAa,GAACF,WAAW,CAACG,GAAG,CAAC,CAAC,EAAC,OAAOjB,CAAC;EAAA,CAAC;EAACQ,CAAC,GAACA,CAAA,KAAI;IAAC,MAAMR,CAAC,GAACO,CAAC,CAAC,CAAC;IAAC,OAAOP,CAAC,EAAEkB,eAAe,IAAE,CAAC;EAAA,CAAC;EAACT,CAAC,GAACA,CAACR,CAAC,EAACE,CAAC,GAAC,CAAC,CAAC,KAAG;IAAC,MAAMG,CAAC,GAACC,CAAC,CAAC,CAAC;IAAC,IAAIE,CAAC,GAAC,UAAU;IAACT,CAAC,IAAE,CAAC,GAACS,CAAC,GAAC,oBAAoB,GAACH,CAAC,KAAGa,QAAQ,CAACC,YAAY,IAAEZ,CAAC,CAAC,CAAC,GAAC,CAAC,GAACC,CAAC,GAAC,WAAW,GAACU,QAAQ,CAACE,YAAY,GAACZ,CAAC,GAAC,SAAS,GAACH,CAAC,CAACgB,IAAI,KAAGb,CAAC,GAACH,CAAC,CAACgB,IAAI,CAACC,OAAO,CAAC,IAAI,EAAC,GAAG,CAAC,CAAC,CAAC;IAAC,OAAM;MAACC,IAAI,EAACvB,CAAC;MAACS,KAAK,EAACP,CAAC;MAACS,MAAM,EAAC,MAAM;MAACD,KAAK,EAAC,CAAC;MAACc,OAAO,EAAC,EAAE;MAACC,EAAE,EAAC,MAAMC,IAAI,CAACV,GAAG,CAAC,CAAC,IAAIW,IAAI,CAACC,KAAK,CAAC,aAAa,GAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,GAAC,IAAI,EAAE;MAACC,cAAc,EAACtB;IAAC,CAAC;EAAA,CAAC;EAACuB,CAAC,GAAC,IAAIC,OAAO,CAAD,CAAC;AAAC,SAASC,CAACA,CAAClC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAO+B,CAAC,CAACG,GAAG,CAACnC,CAAC,CAAC,IAAEgC,CAAC,CAACI,GAAG,CAACpC,CAAC,EAAC,IAAIC,CAAC,CAAD,CAAC,CAAC,EAAC+B,CAAC,CAACG,GAAG,CAACnC,CAAC,CAAC;AAAA;AAAC,MAAMqC,CAAC;EAACpC,CAAC;EAACK,CAAC,GAAC,CAAC;EAACC,CAAC,GAAC,EAAE;EAAC+B,CAACA,CAACtC,CAAC,EAAC;IAAC,IAAGA,CAAC,CAACuC,cAAc,EAAC;IAAO,MAAMtC,CAAC,GAAC,IAAI,CAACM,CAAC,CAAC,CAAC,CAAC;MAACJ,CAAC,GAAC,IAAI,CAACI,CAAC,CAACiC,EAAE,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,CAAClC,CAAC,IAAEL,CAAC,IAAEE,CAAC,IAAEH,CAAC,CAACyC,SAAS,GAACtC,CAAC,CAACsC,SAAS,GAAC,GAAG,IAAEzC,CAAC,CAACyC,SAAS,GAACxC,CAAC,CAACwC,SAAS,GAAC,GAAG,IAAE,IAAI,CAACnC,CAAC,IAAEN,CAAC,CAACU,KAAK,EAAC,IAAI,CAACH,CAAC,CAACmC,IAAI,CAAC1C,CAAC,CAAC,KAAG,IAAI,CAACM,CAAC,GAACN,CAAC,CAACU,KAAK,EAAC,IAAI,CAACH,CAAC,GAAC,CAACP,CAAC,CAAC,CAAC,EAAC,IAAI,CAACC,CAAC,GAAGD,CAAC,CAAC;EAAA;AAAC;AAAC,MAAMsC,CAAC,GAACA,CAACtC,CAAC,EAACC,CAAC,EAACE,CAAC,GAAC,CAAC,CAAC,KAAG;IAAC,IAAG;MAAC,IAAGwC,mBAAmB,CAACC,mBAAmB,CAACC,QAAQ,CAAC7C,CAAC,CAAC,EAAC;QAAC,MAAMM,CAAC,GAAC,IAAIqC,mBAAmB,CAAE3C,CAAC,IAAE;UAAC8C,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAE,MAAI;YAAC/C,CAAC,CAACD,CAAC,CAACiD,UAAU,CAAC,CAAC,CAAC;UAAA,CAAE,CAAC;QAAA,CAAE,CAAC;QAAC,OAAO3C,CAAC,CAAC4C,OAAO,CAAC;UAAC5B,IAAI,EAACtB,CAAC;UAACmD,QAAQ,EAAC,CAAC,CAAC;UAAC,GAAGhD;QAAC,CAAC,CAAC,EAACG,CAAC;MAAA;IAAC,CAAC,OAAK,CAAC;EAAC,CAAC;EAAC8C,CAAC,GAACpD,CAAC,IAAE;IAAC,IAAIC,CAAC,GAAC,CAAC,CAAC;IAAC,OAAM,MAAI;MAACA,CAAC,KAAGD,CAAC,CAAC,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAC,IAAIoD,CAAC,GAAC,CAAC,CAAC;AAAC,MAAMC,CAAC,GAACA,CAAA,KAAI,QAAQ,KAAGnC,QAAQ,CAACoC,eAAe,IAAEpC,QAAQ,CAACC,YAAY,GAAC,CAAC,GAAC,CAAC,GAAC,CAAC;EAACoC,CAAC,GAACxD,CAAC,IAAE;IAAC,QAAQ,KAAGmB,QAAQ,CAACoC,eAAe,IAAEF,CAAC,GAAC,CAAC,CAAC,KAAGA,CAAC,GAAC,kBAAkB,KAAGrD,CAAC,CAACsB,IAAI,GAACtB,CAAC,CAACK,SAAS,GAAC,CAAC,EAACoD,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC;EAACC,CAAC,GAACA,CAAA,KAAI;IAACxD,gBAAgB,CAAC,kBAAkB,EAACsD,CAAC,EAAC,CAAC,CAAC,CAAC,EAACtD,gBAAgB,CAAC,oBAAoB,EAACsD,CAAC,EAAC,CAAC,CAAC,CAAC;EAAA,CAAC;EAACC,CAAC,GAACA,CAAA,KAAI;IAACE,mBAAmB,CAAC,kBAAkB,EAACH,CAAC,EAAC,CAAC,CAAC,CAAC,EAACG,mBAAmB,CAAC,oBAAoB,EAACH,CAAC,EAAC,CAAC,CAAC,CAAC;EAAA,CAAC;EAACI,CAAC,GAACA,CAAA,KAAI;IAAC,IAAGP,CAAC,GAAC,CAAC,EAAC;MAAC,MAAMrD,CAAC,GAACQ,CAAC,CAAC,CAAC;QAACL,CAAC,GAACgB,QAAQ,CAACC,YAAY,GAAC,KAAK,CAAC,GAACyC,UAAU,CAAC/C,WAAW,CAACC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC+C,MAAM,CAAE7D,CAAC,IAAE,QAAQ,KAAGA,CAAC,CAACuB,IAAI,IAAEvB,CAAC,CAACwC,SAAS,GAACzC,CAAE,CAAC,CAAC,CAAC,CAAC,EAAEyC,SAAS;MAACY,CAAC,GAAClD,CAAC,IAAEmD,CAAC,CAAC,CAAC,EAACI,CAAC,CAAC,CAAC,EAACzD,CAAC,CAAE,MAAI;QAAC8D,UAAU,CAAE,MAAI;UAACV,CAAC,GAACC,CAAC,CAAC,CAAC,EAACI,CAAC,CAAC,CAAC;QAAA,CAAE,CAAC;MAAA,CAAE,CAAC;IAAA;IAAC,OAAM;MAAC,IAAIM,eAAeA,CAAA,EAAE;QAAC,OAAOX,CAAC;MAAA;IAAC,CAAC;EAAA,CAAC;EAACY,CAAC,GAACjE,CAAC,IAAE;IAACmB,QAAQ,CAACC,YAAY,GAAClB,gBAAgB,CAAC,oBAAoB,EAAE,MAAIF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAACA,CAAC,CAAC,CAAC;EAAA,CAAC;EAACkE,CAAC,GAAC,CAAC,IAAI,EAAC,GAAG,CAAC;EAACC,CAAC,GAACA,CAACnE,CAAC,EAACO,CAAC,GAAC,CAAC,CAAC,KAAG;IAAC0D,CAAC,CAAE,MAAI;MAAC,MAAMjC,CAAC,GAAC4B,CAAC,CAAC,CAAC;MAAC,IAAI1B,CAAC;QAACG,CAAC,GAAC5B,CAAC,CAAC,KAAK,CAAC;MAAC,MAAM2C,CAAC,GAACd,CAAC,CAAC,OAAO,EAAEtC,CAAC,IAAE;QAAC,KAAI,MAAMC,CAAC,IAAID,CAAC,EAAC,wBAAwB,KAAGC,CAAC,CAACuB,IAAI,KAAG4B,CAAC,CAACgB,UAAU,CAAC,CAAC,EAACnE,CAAC,CAACwC,SAAS,GAACT,CAAC,CAACgC,eAAe,KAAG3B,CAAC,CAAC3B,KAAK,GAACkB,IAAI,CAACyC,GAAG,CAACpE,CAAC,CAACwC,SAAS,GAACjC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC6B,CAAC,CAACZ,OAAO,CAACiB,IAAI,CAACzC,CAAC,CAAC,EAACiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CAAE,CAAC;MAACkB,CAAC,KAAGlB,CAAC,GAAC/B,CAAC,CAACH,CAAC,EAACqC,CAAC,EAAC6B,CAAC,EAAC3D,CAAC,CAAC+D,gBAAgB,CAAC,EAACrE,CAAC,CAAEA,CAAC,IAAE;QAACoC,CAAC,GAAC5B,CAAC,CAAC,KAAK,CAAC,EAACyB,CAAC,GAAC/B,CAAC,CAACH,CAAC,EAACqC,CAAC,EAAC6B,CAAC,EAAC3D,CAAC,CAAC+D,gBAAgB,CAAC,EAAChE,CAAC,CAAE,MAAI;UAAC+B,CAAC,CAAC3B,KAAK,GAACI,WAAW,CAACG,GAAG,CAAC,CAAC,GAAChB,CAAC,CAACI,SAAS,EAAC6B,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAE,CAAC;MAAA,CAAE,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;EAACqC,CAAC,GAAC,CAAC,EAAE,EAAC,GAAG,CAAC;EAACC,CAAC,GAACA,CAACxE,CAAC,EAACO,CAAC,GAAC,CAAC,CAAC,KAAG;IAAC4D,CAAC,CAACf,CAAC,CAAE,MAAI;MAAC,IAAI5C,CAAC;QAACwB,CAAC,GAACvB,CAAC,CAAC,KAAK,EAAC,CAAC,CAAC;MAAC,MAAM2C,CAAC,GAAClB,CAAC,CAAC3B,CAAC,EAAC8B,CAAC,CAAC;QAACgB,CAAC,GAACrD,CAAC,IAAE;UAAC,KAAI,MAAMC,CAAC,IAAID,CAAC,EAACoD,CAAC,CAACd,CAAC,CAACrC,CAAC,CAAC;UAACmD,CAAC,CAAC9C,CAAC,GAAC0B,CAAC,CAACtB,KAAK,KAAGsB,CAAC,CAACtB,KAAK,GAAC0C,CAAC,CAAC9C,CAAC,EAAC0B,CAAC,CAACP,OAAO,GAAC2B,CAAC,CAAC7C,CAAC,EAACC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC;QAAC8C,CAAC,GAAChB,CAAC,CAAC,cAAc,EAACe,CAAC,CAAC;MAACC,CAAC,KAAG9C,CAAC,GAACL,CAAC,CAACH,CAAC,EAACgC,CAAC,EAACuC,CAAC,EAAChE,CAAC,CAAC+D,gBAAgB,CAAC,EAACnD,QAAQ,CAACjB,gBAAgB,CAAC,kBAAkB,EAAE,MAAI;QAAC,QAAQ,KAAGiB,QAAQ,CAACoC,eAAe,KAAGF,CAAC,CAACC,CAAC,CAACmB,WAAW,CAAC,CAAC,CAAC,EAACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CAAE,CAAC,EAACP,CAAC,CAAE,MAAI;QAACmD,CAAC,CAAC9C,CAAC,GAAC,CAAC,EAAC0B,CAAC,GAACvB,CAAC,CAAC,KAAK,EAAC,CAAC,CAAC,EAACD,CAAC,GAACL,CAAC,CAACH,CAAC,EAACgC,CAAC,EAACuC,CAAC,EAAChE,CAAC,CAAC+D,gBAAgB,CAAC,EAAChE,CAAC,CAAE,MAAIE,CAAC,CAAC,CAAE,CAAC;MAAA,CAAE,CAAC,EAACuD,UAAU,CAACvD,CAAC,CAAC,CAAC;IAAA,CAAE,CAAC,CAAC;EAAA,CAAC;AAAC,IAAIkE,CAAC,GAAC,CAAC;EAACC,CAAC,GAAC,CAAC,GAAC,CAAC;EAACC,CAAC,GAAC,CAAC;AAAC,MAAMC,CAAC,GAAC7E,CAAC,IAAE;EAAC,KAAI,MAAMC,CAAC,IAAID,CAAC,EAACC,CAAC,CAAC6E,aAAa,KAAGH,CAAC,GAAC/C,IAAI,CAACmD,GAAG,CAACJ,CAAC,EAAC1E,CAAC,CAAC6E,aAAa,CAAC,EAACF,CAAC,GAAChD,IAAI,CAACyC,GAAG,CAACO,CAAC,EAAC3E,CAAC,CAAC6E,aAAa,CAAC,EAACJ,CAAC,GAACE,CAAC,GAAC,CAACA,CAAC,GAACD,CAAC,IAAE,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;AAAA,CAAC;AAAC,IAAIK,CAAC;AAAC,MAAMC,CAAC,GAACA,CAAA,KAAID,CAAC,GAACN,CAAC,GAAC5D,WAAW,CAACoE,gBAAgB,IAAE,CAAC;EAACC,CAAC,GAACA,CAAA,KAAI;IAAC,kBAAkB,IAAGrE,WAAW,IAAEkE,CAAC,KAAGA,CAAC,GAAC1C,CAAC,CAAC,OAAO,EAACuC,CAAC,EAAC;MAACvD,IAAI,EAAC,OAAO;MAAC6B,QAAQ,EAAC,CAAC,CAAC;MAACiC,iBAAiB,EAAC;IAAC,CAAC,CAAC,CAAC;EAAA,CAAC;AAAC,IAAIC,CAAC,GAAC,CAAC;AAAC,MAAMC,CAAC;EAACjC,CAAC,GAAC,EAAE;EAACC,CAAC,GAAC,IAAIiC,GAAG,CAAD,CAAC;EAAC/B,CAAC;EAACC,CAAC;EAACG,CAACA,CAAA,EAAE;IAACyB,CAAC,GAACJ,CAAC,CAAC,CAAC,EAAC,IAAI,CAAC5B,CAAC,CAACmC,MAAM,GAAC,CAAC,EAAC,IAAI,CAAClC,CAAC,CAACmC,KAAK,CAAC,CAAC;EAAA;EAACtB,CAACA,CAAA,EAAE;IAAC,MAAMnE,CAAC,GAAC4B,IAAI,CAACmD,GAAG,CAAC,IAAI,CAAC1B,CAAC,CAACmC,MAAM,GAAC,CAAC,EAAC5D,IAAI,CAACC,KAAK,CAAC,CAACoD,CAAC,CAAC,CAAC,GAACI,CAAC,IAAE,EAAE,CAAC,CAAC;IAAC,OAAO,IAAI,CAAChC,CAAC,CAACrD,CAAC,CAAC;EAAA;EAACsC,CAACA,CAACtC,CAAC,EAAC;IAAC,IAAG,IAAI,CAACwD,CAAC,GAAGxD,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC8E,aAAa,IAAE,aAAa,KAAG9E,CAAC,CAAC0F,SAAS,EAAC;IAAO,MAAMzF,CAAC,GAAC,IAAI,CAACoD,CAAC,CAACb,EAAE,CAAC,CAAC,CAAC,CAAC;IAAC,IAAIrC,CAAC,GAAC,IAAI,CAACmD,CAAC,CAACnB,GAAG,CAACnC,CAAC,CAAC8E,aAAa,CAAC;IAAC,IAAG3E,CAAC,IAAE,IAAI,CAACkD,CAAC,CAACmC,MAAM,GAAC,EAAE,IAAExF,CAAC,CAAC2F,QAAQ,GAAC1F,CAAC,CAACsE,CAAC,EAAC;MAAC,IAAGpE,CAAC,GAACH,CAAC,CAAC2F,QAAQ,GAACxF,CAAC,CAACoE,CAAC,IAAEpE,CAAC,CAACsB,OAAO,GAAC,CAACzB,CAAC,CAAC,EAACG,CAAC,CAACoE,CAAC,GAACvE,CAAC,CAAC2F,QAAQ,IAAE3F,CAAC,CAAC2F,QAAQ,KAAGxF,CAAC,CAACoE,CAAC,IAAEvE,CAAC,CAACyC,SAAS,KAAGtC,CAAC,CAACsB,OAAO,CAAC,CAAC,CAAC,CAACgB,SAAS,IAAEtC,CAAC,CAACsB,OAAO,CAACiB,IAAI,CAAC1C,CAAC,CAAC,IAAEG,CAAC,GAAC;QAACuB,EAAE,EAAC1B,CAAC,CAAC8E,aAAa;QAACrD,OAAO,EAAC,CAACzB,CAAC,CAAC;QAACuE,CAAC,EAACvE,CAAC,CAAC2F;MAAQ,CAAC,EAAC,IAAI,CAACrC,CAAC,CAAClB,GAAG,CAACjC,CAAC,CAACuB,EAAE,EAACvB,CAAC,CAAC,EAAC,IAAI,CAACkD,CAAC,CAACX,IAAI,CAACvC,CAAC,CAAC,CAAC,EAAC,IAAI,CAACkD,CAAC,CAACuC,IAAI,CAAE,CAAC5F,CAAC,EAACC,CAAC,KAAGA,CAAC,CAACsE,CAAC,GAACvE,CAAC,CAACuE,CAAE,CAAC,EAAC,IAAI,CAAClB,CAAC,CAACmC,MAAM,GAAC,EAAE,EAAC;QAAC,MAAMxF,CAAC,GAAC,IAAI,CAACqD,CAAC,CAACwC,MAAM,CAAC,EAAE,CAAC;QAAC,KAAI,MAAM5F,CAAC,IAAID,CAAC,EAAC,IAAI,CAACsD,CAAC,CAACwC,MAAM,CAAC7F,CAAC,CAACyB,EAAE,CAAC;MAAA;MAAC,IAAI,CAAC+B,CAAC,GAAGtD,CAAC,CAAC;IAAA;EAAC;AAAC;AAAC,MAAM4F,CAAC,GAAC/F,CAAC,IAAE;IAAC,MAAMC,CAAC,GAAC4D,UAAU,CAACmC,mBAAmB,IAAEjC,UAAU;IAAC,QAAQ,KAAG5C,QAAQ,CAACoC,eAAe,GAACvD,CAAC,CAAC,CAAC,IAAEA,CAAC,GAACoD,CAAC,CAACpD,CAAC,CAAC,EAACmB,QAAQ,CAACjB,gBAAgB,CAAC,kBAAkB,EAACF,CAAC,EAAC;MAACiG,IAAI,EAAC,CAAC;IAAC,CAAC,CAAC,EAAChG,CAAC,CAAE,MAAI;MAACD,CAAC,CAAC,CAAC,EAACmB,QAAQ,CAACwC,mBAAmB,CAAC,kBAAkB,EAAC3D,CAAC,CAAC;IAAA,CAAE,CAAC,CAAC;EAAA,CAAC;EAACkG,CAAC,GAAC,CAAC,GAAG,EAAC,GAAG,CAAC;EAACC,CAAC,GAACA,CAACnG,CAAC,EAACM,CAAC,GAAC,CAAC,CAAC,KAAG;IAACuD,UAAU,CAACuC,sBAAsB,IAAE,eAAe,IAAGA,sBAAsB,CAACC,SAAS,IAAEpC,CAAC,CAAE,MAAI;MAACkB,CAAC,CAAC,CAAC;MAAC,IAAI5E,CAAC;QAACC,CAAC,GAACC,CAAC,CAAC,KAAK,CAAC;MAAC,MAAMuB,CAAC,GAACE,CAAC,CAAC5B,CAAC,EAACgF,CAAC,CAAC;QAACjD,CAAC,GAACrC,CAAC,IAAE;UAAC+F,CAAC,CAAE,MAAI;YAAC,KAAI,MAAM9F,CAAC,IAAID,CAAC,EAACgC,CAAC,CAACM,CAAC,CAACrC,CAAC,CAAC;YAAC,MAAMA,CAAC,GAAC+B,CAAC,CAACmC,CAAC,CAAC,CAAC;YAAClE,CAAC,IAAEA,CAAC,CAACsE,CAAC,KAAG/D,CAAC,CAACE,KAAK,KAAGF,CAAC,CAACE,KAAK,GAACT,CAAC,CAACsE,CAAC,EAAC/D,CAAC,CAACiB,OAAO,GAACxB,CAAC,CAACwB,OAAO,EAAClB,CAAC,CAAC,CAAC,CAAC;UAAA,CAAE,CAAC;QAAA,CAAC;QAAC6C,CAAC,GAACd,CAAC,CAAC,OAAO,EAACD,CAAC,EAAC;UAAC+C,iBAAiB,EAAC9E,CAAC,CAAC8E,iBAAiB,IAAE;QAAE,CAAC,CAAC;MAAC7E,CAAC,GAACJ,CAAC,CAACH,CAAC,EAACQ,CAAC,EAAC0F,CAAC,EAAC5F,CAAC,CAACgE,gBAAgB,CAAC,EAAClB,CAAC,KAAGA,CAAC,CAACF,OAAO,CAAC;QAAC5B,IAAI,EAAC,aAAa;QAAC6B,QAAQ,EAAC,CAAC;MAAC,CAAC,CAAC,EAAChC,QAAQ,CAACjB,gBAAgB,CAAC,kBAAkB,EAAE,MAAI;QAAC,QAAQ,KAAGiB,QAAQ,CAACoC,eAAe,KAAGlB,CAAC,CAACe,CAAC,CAACqB,WAAW,CAAC,CAAC,CAAC,EAAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CAAE,CAAC,EAACN,CAAC,CAAE,MAAI;QAAC+B,CAAC,CAAC4B,CAAC,CAAC,CAAC,EAACpD,CAAC,GAACC,CAAC,CAAC,KAAK,CAAC,EAACF,CAAC,GAACJ,CAAC,CAACH,CAAC,EAACQ,CAAC,EAAC0F,CAAC,EAAC5F,CAAC,CAACgE,gBAAgB,CAAC;MAAA,CAAE,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;AAAC,MAAMgC,CAAC;EAAC9C,CAAC;EAAClB,CAACA,CAACtC,CAAC,EAAC;IAAC,IAAI,CAACwD,CAAC,GAAGxD,CAAC,CAAC;EAAA;AAAC;AAAC,MAAMuG,CAAC,GAAC,CAAC,IAAI,EAAC,GAAG,CAAC;EAACC,CAAC,GAACA,CAACxG,CAAC,EAACO,CAAC,GAAC,CAAC,CAAC,KAAG;IAAC0D,CAAC,CAAE,MAAI;MAAC,MAAMjC,CAAC,GAAC4B,CAAC,CAAC,CAAC;MAAC,IAAIvB,CAAC;QAACgB,CAAC,GAAC5C,CAAC,CAAC,KAAK,CAAC;MAAC,MAAM6C,CAAC,GAACpB,CAAC,CAAC3B,CAAC,EAAC+F,CAAC,CAAC;QAAC9C,CAAC,GAACxD,CAAC,IAAE;UAACO,CAAC,CAAC+D,gBAAgB,KAAGtE,CAAC,GAACA,CAAC,CAACyG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAAC,KAAI,MAAMxG,CAAC,IAAID,CAAC,EAACsD,CAAC,CAAChB,CAAC,CAACrC,CAAC,CAAC,EAACA,CAAC,CAACwC,SAAS,GAACT,CAAC,CAACgC,eAAe,KAAGX,CAAC,CAAC3C,KAAK,GAACkB,IAAI,CAACyC,GAAG,CAACpE,CAAC,CAACwC,SAAS,GAACjC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC6C,CAAC,CAAC5B,OAAO,GAAC,CAACxB,CAAC,CAAC,EAACoC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC;QAACqB,CAAC,GAACpB,CAAC,CAAC,0BAA0B,EAACkB,CAAC,CAAC;MAAC,IAAGE,CAAC,EAAC;QAACrB,CAAC,GAAClC,CAAC,CAACH,CAAC,EAACqD,CAAC,EAACkD,CAAC,EAAChG,CAAC,CAAC+D,gBAAgB,CAAC;QAAC,MAAM9D,CAAC,GAAC4C,CAAC,CAAE,MAAI;UAACI,CAAC,CAACE,CAAC,CAACe,WAAW,CAAC,CAAC,CAAC,EAACf,CAAC,CAACU,UAAU,CAAC,CAAC,EAAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAE,CAAC;QAAC,KAAI,MAAMrC,CAAC,IAAG,CAAC,SAAS,EAAC,OAAO,EAAC,kBAAkB,CAAC,EAACE,gBAAgB,CAACF,CAAC,EAAE,MAAI+F,CAAC,CAACvF,CAAC,CAAC,EAAE;UAACkG,OAAO,EAAC,CAAC,CAAC;UAACT,IAAI,EAAC,CAAC;QAAC,CAAC,CAAC;QAAChG,CAAC,CAAEA,CAAC,IAAE;UAACoD,CAAC,GAAC5C,CAAC,CAAC,KAAK,CAAC,EAAC4B,CAAC,GAAClC,CAAC,CAACH,CAAC,EAACqD,CAAC,EAACkD,CAAC,EAAChG,CAAC,CAAC+D,gBAAgB,CAAC,EAAChE,CAAC,CAAE,MAAI;YAAC+C,CAAC,CAAC3C,KAAK,GAACI,WAAW,CAACG,GAAG,CAAC,CAAC,GAAChB,CAAC,CAACI,SAAS,EAACgC,CAAC,CAAC,CAAC,CAAC,CAAC;UAAA,CAAE,CAAC;QAAA,CAAE,CAAC;MAAA;IAAC,CAAE,CAAC;EAAA,CAAC;EAACsE,CAAC,GAAC,CAAC,GAAG,EAAC,IAAI,CAAC;EAACC,CAAC,GAAC5G,CAAC,IAAE;IAACmB,QAAQ,CAACC,YAAY,GAAC6C,CAAC,CAAE,MAAI2C,CAAC,CAAC5G,CAAC,CAAE,CAAC,GAAC,UAAU,KAAGmB,QAAQ,CAAC0F,UAAU,GAAC3G,gBAAgB,CAAC,MAAM,EAAE,MAAI0G,CAAC,CAAC5G,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAC+D,UAAU,CAAC/D,CAAC,CAAC;EAAA,CAAC;EAAC8G,CAAC,GAACA,CAAC9G,CAAC,EAACM,CAAC,GAAC,CAAC,CAAC,KAAG;IAAC,IAAI0B,CAAC,GAACvB,CAAC,CAAC,MAAM,CAAC;MAACyB,CAAC,GAAC/B,CAAC,CAACH,CAAC,EAACgC,CAAC,EAAC2E,CAAC,EAACrG,CAAC,CAACgE,gBAAgB,CAAC;IAACsC,CAAC,CAAE,MAAI;MAAC,MAAMvE,CAAC,GAAC9B,CAAC,CAAC,CAAC;MAAC8B,CAAC,KAAGL,CAAC,CAACtB,KAAK,GAACkB,IAAI,CAACyC,GAAG,CAAChC,CAAC,CAACrB,aAAa,GAACR,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACwB,CAAC,CAACP,OAAO,GAAC,CAACY,CAAC,CAAC,EAACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAACjC,CAAC,CAAE,MAAI;QAAC+B,CAAC,GAACvB,CAAC,CAAC,MAAM,EAAC,CAAC,CAAC,EAACyB,CAAC,GAAC/B,CAAC,CAACH,CAAC,EAACgC,CAAC,EAAC2E,CAAC,EAACrG,CAAC,CAACgE,gBAAgB,CAAC,EAACpC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CAAE,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;AAAC,SAAOqC,CAAC,IAAIwC,aAAa,EAAC7C,CAAC,IAAI8C,aAAa,EAACd,CAAC,IAAIe,aAAa,EAACV,CAAC,IAAIW,aAAa,EAACP,CAAC,IAAIQ,cAAc,EAAC3C,CAAC,IAAI4C,KAAK,EAACjD,CAAC,IAAIkD,KAAK,EAAClB,CAAC,IAAImB,KAAK,EAACd,CAAC,IAAIe,KAAK,EAACT,CAAC,IAAIU,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}