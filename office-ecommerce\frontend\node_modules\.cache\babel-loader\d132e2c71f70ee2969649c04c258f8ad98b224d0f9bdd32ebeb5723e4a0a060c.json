{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nconst Detailed = /*#__PURE__*/React.forwardRef(({\n  children,\n  hysteresis = 0,\n  distances,\n  ...props\n}, ref) => {\n  const lodRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const {\n      current: lod\n    } = lodRef;\n    lod.levels.length = 0;\n    lod.children.forEach((object, index) => lod.levels.push({\n      object,\n      hysteresis,\n      distance: distances[index]\n    }));\n  });\n  useFrame(state => {\n    var _lodRef$current;\n    return (_lodRef$current = lodRef.current) == null ? void 0 : _lodRef$current.update(state.camera);\n  });\n  return /*#__PURE__*/React.createElement(\"lOD\", _extends({\n    ref: mergeRefs([lodRef, ref])\n  }, props), children);\n});\nexport { Detailed };", "map": {"version": 3, "names": ["_extends", "React", "useFrame", "mergeRefs", "Detailed", "forwardRef", "children", "hysteresis", "distances", "props", "ref", "lodRef", "useRef", "useLayoutEffect", "current", "lod", "levels", "length", "for<PERSON>ach", "object", "index", "push", "distance", "state", "_lodRef$current", "update", "camera", "createElement"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Detailed.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\n\nconst Detailed = /*#__PURE__*/React.forwardRef(({\n  children,\n  hysteresis = 0,\n  distances,\n  ...props\n}, ref) => {\n  const lodRef = React.useRef(null);\n  React.useLayoutEffect(() => {\n    const {\n      current: lod\n    } = lodRef;\n    lod.levels.length = 0;\n    lod.children.forEach((object, index) => lod.levels.push({\n      object,\n      hysteresis,\n      distance: distances[index]\n    }));\n  });\n  useFrame(state => {\n    var _lodRef$current;\n\n    return (_lodRef$current = lodRef.current) == null ? void 0 : _lodRef$current.update(state.camera);\n  });\n  return /*#__PURE__*/React.createElement(\"lOD\", _extends({\n    ref: mergeRefs([lodRef, ref])\n  }, props), children);\n});\n\nexport { Detailed };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,kBAAkB;AAExC,MAAMC,QAAQ,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAAC;EAC9CC,QAAQ;EACRC,UAAU,GAAG,CAAC;EACdC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EACjCX,KAAK,CAACY,eAAe,CAAC,MAAM;IAC1B,MAAM;MACJC,OAAO,EAAEC;IACX,CAAC,GAAGJ,MAAM;IACVI,GAAG,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC;IACrBF,GAAG,CAACT,QAAQ,CAACY,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAKL,GAAG,CAACC,MAAM,CAACK,IAAI,CAAC;MACtDF,MAAM;MACNZ,UAAU;MACVe,QAAQ,EAAEd,SAAS,CAACY,KAAK;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACFlB,QAAQ,CAACqB,KAAK,IAAI;IAChB,IAAIC,eAAe;IAEnB,OAAO,CAACA,eAAe,GAAGb,MAAM,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGU,eAAe,CAACC,MAAM,CAACF,KAAK,CAACG,MAAM,CAAC;EACnG,CAAC,CAAC;EACF,OAAO,aAAazB,KAAK,CAAC0B,aAAa,CAAC,KAAK,EAAE3B,QAAQ,CAAC;IACtDU,GAAG,EAAEP,SAAS,CAAC,CAACQ,MAAM,EAAED,GAAG,CAAC;EAC9B,CAAC,EAAED,KAAK,CAAC,EAAEH,QAAQ,CAAC;AACtB,CAAC,CAAC;AAEF,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}