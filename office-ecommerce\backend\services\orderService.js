const logger = require('../utils/logger');
const Order = require('../models/Order');
const websocketService = require('./websocketService');

/**
 * Order Service
 * Handles all order-related business logic
 */
class OrderService {

  constructor() {
    this.orderModel = new Order();
  }
  
  /**
   * Create a new order with inventory management
   * @param {Object} orderData - Order information
   * @param {Array} orderItems - Array of order items
   * @param {Object} user - User creating the order
   * @returns {Object} Created order result
   */
  async createOrder(orderData, orderItems, user) {
    try {
      // Validate order data
      this.validateOrderData(orderData, orderItems);
      
      // Prepare order data with user context
      const preparedOrderData = {
        ...orderData,
        CustomerID: user.userId,
        OrderStatus: 'Pending',
        PaymentStatus: 'Pending'
      };

      // Prepare order items with validation
      const preparedOrderItems = orderItems.map(item => ({
        VariantID: item.variantId,
        Quantity: item.quantity,
        UnitPrice: item.unitPrice,
        TotalPrice: item.totalPrice,
        CustomConfiguration: item.customConfiguration ? JSON.stringify(item.customConfiguration) : null
      }));

      // Create order with inventory management
      const result = await this.orderModel.createOrderWithInventory(preparedOrderData, preparedOrderItems);

      // Emit real-time update to admins
      this.emitOrderCreatedEvent(result.order, orderData.CustomerName);

      logger.info(`Order created: ${result.order.OrderNumber} by user ${user.userId}`);

      return {
        success: true,
        data: {
          order: result.order,
          items: result.items
        }
      };

    } catch (error) {
      logger.error('OrderService.createOrder error:', error);
      
      // Handle specific inventory errors
      if (error.message.includes('Insufficient inventory')) {
        return {
          success: false,
          error: 'Insufficient inventory',
          message: error.message,
          code: 'INSUFFICIENT_INVENTORY'
        };
      }

      throw error;
    }
  }

  /**
   * Get orders with pagination and filters
   * @param {Object} filters - Filter parameters
   * @returns {Object} Paginated orders result
   */
  async getOrders(filters) {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        paymentStatus,
        search,
        customerId,
        startDate,
        endDate
      } = filters;

      // Build filters object for model
      const modelFilters = {
        page: parseInt(page),
        limit: parseInt(limit)
      };

      if (status) modelFilters.status = status;
      if (paymentStatus) modelFilters.paymentStatus = paymentStatus;
      if (search) modelFilters.search = search;
      if (customerId) modelFilters.customerId = customerId;
      if (startDate) modelFilters.startDate = startDate;
      if (endDate) modelFilters.endDate = endDate;

      const result = await this.orderModel.getOrdersWithPagination(modelFilters);

      return {
        success: true,
        data: {
          orders: result.orders,
          pagination: result.pagination
        }
      };

    } catch (error) {
      logger.error('OrderService.getOrders error:', error);
      throw error;
    }
  }

  /**
   * Update order status
   * @param {string} orderId - Order ID
   * @param {string} status - New status
   * @param {string} notes - Optional notes
   * @param {Object} user - User making the update
   * @returns {Object} Update result
   */
  async updateOrderStatus(orderId, status, notes, user) {
    try {
      // Validate UUID format
      if (!this.isValidUUID(orderId)) {
        return {
          success: false,
          error: 'Invalid order ID format',
          code: 'INVALID_ORDER_ID'
        };
      }

      const updatedOrder = await this.orderModel.updateOrderStatus(orderId, status, notes);

      if (!updatedOrder) {
        return {
          success: false,
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND'
        };
      }

      // Emit real-time update
      this.emitOrderStatusUpdatedEvent(orderId, status, user.email);

      logger.info(`Order ${orderId} status updated to ${status} by user ${user.userId}`);

      return {
        success: true,
        data: updatedOrder
      };

    } catch (error) {
      logger.error('OrderService.updateOrderStatus error:', error);
      throw error;
    }
  }

  /**
   * Cancel an order
   * @param {string} orderId - Order ID
   * @param {string} reason - Cancellation reason
   * @param {Object} user - User cancelling the order
   * @returns {Object} Cancellation result
   */
  async cancelOrder(orderId, reason, user) {
    try {
      // Validate UUID format
      if (!this.isValidUUID(orderId)) {
        return {
          success: false,
          error: 'Invalid order ID format',
          code: 'INVALID_ORDER_ID'
        };
      }

      const result = await this.orderModel.cancelOrder(orderId, reason);

      if (!result) {
        return {
          success: false,
          error: 'Order not found or cannot be cancelled',
          code: 'ORDER_NOT_FOUND_OR_CANNOT_CANCEL'
        };
      }

      // Emit real-time update
      this.emitOrderCancelledEvent(orderId, reason, user.email);

      logger.info(`Order ${orderId} cancelled by user ${user.userId}. Reason: ${reason}`);

      return {
        success: true,
        data: result
      };

    } catch (error) {
      logger.error('OrderService.cancelOrder error:', error);
      throw error;
    }
  }

  /**
   * Get orders for a specific user
   * @param {string} userId - User ID
   * @param {Object} filters - Filter parameters
   * @returns {Object} User orders result
   */
  async getUserOrders(userId, filters) {
    try {
      const {
        page = 1,
        limit = 10,
        status
      } = filters;

      const modelFilters = {
        userId,
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit)
      };

      if (status) modelFilters.status = status;

      const result = await this.orderModel.getUserOrders(modelFilters);

      return {
        success: true,
        data: {
          orders: result.orders,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(result.total / parseInt(limit)),
            totalItems: result.total,
            itemsPerPage: parseInt(limit)
          }
        }
      };

    } catch (error) {
      logger.error('OrderService.getUserOrders error:', error);
      throw error;
    }
  }

  /**
   * Validate order data
   * @private
   */
  validateOrderData(orderData, orderItems) {
    if (!orderData.CustomerEmail || !orderData.CustomerName) {
      throw new Error('Customer email and name are required');
    }

    if (!orderItems || orderItems.length === 0) {
      throw new Error('At least one order item is required');
    }

    // Validate each order item
    orderItems.forEach((item, index) => {
      if (!item.variantId || !item.quantity || !item.unitPrice) {
        throw new Error(`Invalid item at index ${index}: variantId, quantity, and unitPrice are required`);
      }
    });
  }

  /**
   * Validate UUID format
   * @private
   */
  isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Emit order created event
   * @private
   */
  emitOrderCreatedEvent(order, customerName) {
    websocketService.emitToAdmins('orderCreated', {
      orderId: order.OrderID,
      orderNumber: order.OrderNumber,
      customerName: customerName,
      totalAmount: order.TotalAmount,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Emit order status updated event
   * @private
   */
  emitOrderStatusUpdatedEvent(orderId, newStatus, updatedBy) {
    websocketService.emitToAdmins('orderStatusUpdated', {
      orderId: orderId,
      newStatus: newStatus,
      updatedBy: updatedBy,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Emit order cancelled event
   * @private
   */
  emitOrderCancelledEvent(orderId, reason, cancelledBy) {
    websocketService.emitToAdmins('orderCancelled', {
      orderId: orderId,
      reason: reason,
      cancelledBy: cancelledBy,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = new OrderService();
