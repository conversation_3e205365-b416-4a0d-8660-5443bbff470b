{"ast": null, "code": "import { intersectTri } from './ThreeRayIntersectUtilities.js';\nexport function intersectTris(geo, side, ray, offset, count, intersections) {\n  for (let i = offset, end = offset + count; i < end; i++) {\n    intersectTri(geo, side, ray, i, intersections);\n  }\n}\nexport function intersectClosestTri(geo, side, ray, offset, count) {\n  let dist = Infinity;\n  let res = null;\n  for (let i = offset, end = offset + count; i < end; i++) {\n    const intersection = intersectTri(geo, side, ray, i);\n    if (intersection && intersection.distance < dist) {\n      res = intersection;\n      dist = intersection.distance;\n    }\n  }\n  return res;\n}\n\n// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect(hit, object, raycaster) {\n  if (hit === null) {\n    return null;\n  }\n  hit.point.applyMatrix4(object.matrixWorld);\n  hit.distance = hit.point.distanceTo(raycaster.ray.origin);\n  hit.object = object;\n  if (hit.distance < raycaster.near || hit.distance > raycaster.far) {\n    return null;\n  } else {\n    return hit;\n  }\n}", "map": {"version": 3, "names": ["intersectTri", "intersectTris", "geo", "side", "ray", "offset", "count", "intersections", "i", "end", "intersectClosestTri", "dist", "Infinity", "res", "intersection", "distance", "convertRaycastIntersect", "hit", "object", "raycaster", "point", "applyMatrix4", "matrixWorld", "distanceTo", "origin", "near", "far"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/utils/GeometryRayIntersectUtilities.js"], "sourcesContent": ["import { intersectTri } from './ThreeRayIntersectUtilities.js';\n\nexport function intersectTris( geo, side, ray, offset, count, intersections ) {\n\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tintersectTri( geo, side, ray, i, intersections );\n\n\t}\n\n}\n\nexport function intersectClosestTri( geo, side, ray, offset, count ) {\n\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tconst intersection = intersectTri( geo, side, ray, i );\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\n// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect( hit, object, raycaster ) {\n\n\tif ( hit === null ) {\n\n\t\treturn null;\n\n\t}\n\n\thit.point.applyMatrix4( object.matrixWorld );\n\thit.distance = hit.point.distanceTo( raycaster.ray.origin );\n\thit.object = object;\n\n\tif ( hit.distance < raycaster.near || hit.distance > raycaster.far ) {\n\n\t\treturn null;\n\n\t} else {\n\n\t\treturn hit;\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iCAAiC;AAE9D,OAAO,SAASC,aAAaA,CAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,aAAa,EAAG;EAE7E,KAAM,IAAIC,CAAC,GAAGH,MAAM,EAAEI,GAAG,GAAGJ,MAAM,GAAGC,KAAK,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAG,EAAG;IAE3DR,YAAY,CAAEE,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEI,CAAC,EAAED,aAAc,CAAC;EAEjD;AAED;AAEA,OAAO,SAASG,mBAAmBA,CAAER,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAG;EAEpE,IAAIK,IAAI,GAAGC,QAAQ;EACnB,IAAIC,GAAG,GAAG,IAAI;EACd,KAAM,IAAIL,CAAC,GAAGH,MAAM,EAAEI,GAAG,GAAGJ,MAAM,GAAGC,KAAK,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAG,EAAG;IAE3D,MAAMM,YAAY,GAAGd,YAAY,CAAEE,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEI,CAAE,CAAC;IACtD,IAAKM,YAAY,IAAIA,YAAY,CAACC,QAAQ,GAAGJ,IAAI,EAAG;MAEnDE,GAAG,GAAGC,YAAY;MAClBH,IAAI,GAAGG,YAAY,CAACC,QAAQ;IAE7B;EAED;EAEA,OAAOF,GAAG;AAEX;;AAEA;AACA;AACA,OAAO,SAASG,uBAAuBA,CAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAG;EAEjE,IAAKF,GAAG,KAAK,IAAI,EAAG;IAEnB,OAAO,IAAI;EAEZ;EAEAA,GAAG,CAACG,KAAK,CAACC,YAAY,CAAEH,MAAM,CAACI,WAAY,CAAC;EAC5CL,GAAG,CAACF,QAAQ,GAAGE,GAAG,CAACG,KAAK,CAACG,UAAU,CAAEJ,SAAS,CAACf,GAAG,CAACoB,MAAO,CAAC;EAC3DP,GAAG,CAACC,MAAM,GAAGA,MAAM;EAEnB,IAAKD,GAAG,CAACF,QAAQ,GAAGI,SAAS,CAACM,IAAI,IAAIR,GAAG,CAACF,QAAQ,GAAGI,SAAS,CAACO,GAAG,EAAG;IAEpE,OAAO,IAAI;EAEZ,CAAC,MAAM;IAEN,OAAOT,GAAG;EAEX;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}