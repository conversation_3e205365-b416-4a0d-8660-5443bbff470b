{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\auth\\\\RoleBasedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport UnauthorizedAccess from './UnauthorizedAccess';\n\n/**\n * RoleBasedRoute Component\n * Protects routes based on user roles\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleBasedRoute = ({\n  children,\n  allowedRoles = [],\n  redirectTo = '/login',\n  showUnauthorized = true\n}) => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    loading\n  } = useAuth();\n  const location = useLocation();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Checking permissions...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Not authenticated - redirect to login\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: redirectTo,\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 16\n    }, this);\n  }\n\n  // Check if user role is allowed\n  const hasPermission = allowedRoles.length === 0 || allowedRoles.includes(user.role);\n  if (!hasPermission) {\n    if (showUnauthorized) {\n      return /*#__PURE__*/_jsxDEV(UnauthorizedAccess, {\n        userRole: user.role,\n        requiredRoles: allowedRoles\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 20\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 20\n      }, this);\n    }\n  }\n  return children;\n};\n_s(RoleBasedRoute, \"pvp8inAvQHEb0BVUe3eLqKvyMMQ=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = RoleBasedRoute;\nexport default RoleBasedRoute;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "UnauthorizedAccess", "jsxDEV", "_jsxDEV", "RoleBasedRoute", "children", "allowedRoles", "redirectTo", "showUnauthorized", "_s", "user", "isAuthenticated", "loading", "location", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "hasPermission", "length", "includes", "role", "userRole", "requiredRoles", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/auth/RoleBasedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport UnauthorizedAccess from './UnauthorizedAccess';\n\n/**\n * RoleBasedRoute Component\n * Protects routes based on user roles\n */\nconst RoleBasedRoute = ({ \n    children, \n    allowedRoles = [], \n    redirectTo = '/login',\n    showUnauthorized = true \n}) => {\n    const { user, isAuthenticated, loading } = useAuth();\n    const location = useLocation();\n\n    if (loading) {\n        return (\n            <div className=\"auth-loading\">\n                <div className=\"loading-spinner\"></div>\n                <p>Checking permissions...</p>\n            </div>\n        );\n    }\n\n    // Not authenticated - redirect to login\n    if (!isAuthenticated) {\n        return <Navigate to={redirectTo} state={{ from: location }} replace />;\n    }\n\n    // Check if user role is allowed\n    const hasPermission = allowedRoles.length === 0 || allowedRoles.includes(user.role);\n\n    if (!hasPermission) {\n        if (showUnauthorized) {\n            return <UnauthorizedAccess userRole={user.role} requiredRoles={allowedRoles} />;\n        } else {\n            return <Navigate to=\"/\" replace />;\n        }\n    }\n\n    return children;\n};\n\nexport default RoleBasedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAOC,kBAAkB,MAAM,sBAAsB;;AAErD;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,cAAc,GAAGA,CAAC;EACpBC,QAAQ;EACRC,YAAY,GAAG,EAAE;EACjBC,UAAU,GAAG,QAAQ;EACrBC,gBAAgB,GAAG;AACvB,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACpD,MAAMa,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,IAAIa,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKW,SAAS,EAAC,cAAc;MAAAT,QAAA,gBACzBF,OAAA;QAAKW,SAAS,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCf,OAAA;QAAAE,QAAA,EAAG;MAAuB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEd;;EAEA;EACA,IAAI,CAACP,eAAe,EAAE;IAClB,oBAAOR,OAAA,CAACL,QAAQ;MAACqB,EAAE,EAAEZ,UAAW;MAACa,KAAK,EAAE;QAAEC,IAAI,EAAER;MAAS,CAAE;MAACS,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1E;;EAEA;EACA,MAAMK,aAAa,GAAGjB,YAAY,CAACkB,MAAM,KAAK,CAAC,IAAIlB,YAAY,CAACmB,QAAQ,CAACf,IAAI,CAACgB,IAAI,CAAC;EAEnF,IAAI,CAACH,aAAa,EAAE;IAChB,IAAIf,gBAAgB,EAAE;MAClB,oBAAOL,OAAA,CAACF,kBAAkB;QAAC0B,QAAQ,EAAEjB,IAAI,CAACgB,IAAK;QAACE,aAAa,EAAEtB;MAAa;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACnF,CAAC,MAAM;MACH,oBAAOf,OAAA,CAACL,QAAQ;QAACqB,EAAE,EAAC,GAAG;QAACG,OAAO;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACtC;EACJ;EAEA,OAAOb,QAAQ;AACnB,CAAC;AAACI,EAAA,CAnCIL,cAAc;EAAA,QAM2BJ,OAAO,EACjCD,WAAW;AAAA;AAAA8B,EAAA,GAP1BzB,cAAc;AAqCpB,eAAeA,cAAc;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}