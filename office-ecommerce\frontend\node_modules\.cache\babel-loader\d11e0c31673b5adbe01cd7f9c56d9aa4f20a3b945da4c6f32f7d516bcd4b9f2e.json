{"ast": null, "code": "import { OrthographicCamera, Scene, UniformsUtils, ShaderMaterial, PlaneGeometry, Mesh, Texture, LinearFilter, MeshBasicMaterial, DoubleSide } from \"three\";\nimport { UnpackDepthRGBAShader } from \"../shaders/UnpackDepthRGBAShader.js\";\nclass ShadowMapViewer {\n  constructor(light) {\n    const scope = this;\n    const doRenderLabel = light.name !== void 0 && light.name !== \"\";\n    let userAutoClearSetting;\n    const frame = {\n      x: 10,\n      y: 10,\n      width: 256,\n      height: 256\n    };\n    const camera = new OrthographicCamera(window.innerWidth / -2, window.innerWidth / 2, window.innerHeight / 2, window.innerHeight / -2, 1, 10);\n    camera.position.set(0, 0, 2);\n    const scene = new Scene();\n    const shader = UnpackDepthRGBAShader;\n    const uniforms = UniformsUtils.clone(shader.uniforms);\n    const material = new ShaderMaterial({\n      uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader\n    });\n    const plane = new PlaneGeometry(frame.width, frame.height);\n    const mesh = new Mesh(plane, material);\n    scene.add(mesh);\n    let labelCanvas, labelMesh;\n    if (doRenderLabel) {\n      labelCanvas = document.createElement(\"canvas\");\n      const context = labelCanvas.getContext(\"2d\");\n      context.font = \"Bold 20px Arial\";\n      const labelWidth = context.measureText(light.name).width;\n      labelCanvas.width = labelWidth;\n      labelCanvas.height = 25;\n      context.font = \"Bold 20px Arial\";\n      context.fillStyle = \"rgba( 255, 0, 0, 1 )\";\n      context.fillText(light.name, 0, 20);\n      const labelTexture = new Texture(labelCanvas);\n      labelTexture.magFilter = LinearFilter;\n      labelTexture.minFilter = LinearFilter;\n      labelTexture.needsUpdate = true;\n      const labelMaterial = new MeshBasicMaterial({\n        map: labelTexture,\n        side: DoubleSide\n      });\n      labelMaterial.transparent = true;\n      const labelPlane = new PlaneGeometry(labelCanvas.width, labelCanvas.height);\n      labelMesh = new Mesh(labelPlane, labelMaterial);\n      scene.add(labelMesh);\n    }\n    function resetPosition() {\n      scope.position.set(scope.position.x, scope.position.y);\n    }\n    this.enabled = true;\n    this.size = {\n      width: frame.width,\n      height: frame.height,\n      set: function (width, height) {\n        this.width = width;\n        this.height = height;\n        mesh.scale.set(this.width / frame.width, this.height / frame.height, 1);\n        resetPosition();\n      }\n    };\n    this.position = {\n      x: frame.x,\n      y: frame.y,\n      set: function (x, y) {\n        this.x = x;\n        this.y = y;\n        const width = scope.size.width;\n        const height = scope.size.height;\n        mesh.position.set(-window.innerWidth / 2 + width / 2 + this.x, window.innerHeight / 2 - height / 2 - this.y, 0);\n        if (doRenderLabel) labelMesh.position.set(mesh.position.x, mesh.position.y - scope.size.height / 2 + labelCanvas.height / 2, 0);\n      }\n    };\n    this.render = function (renderer) {\n      if (this.enabled) {\n        uniforms.tDiffuse.value = light.shadow.map.texture;\n        userAutoClearSetting = renderer.autoClear;\n        renderer.autoClear = false;\n        renderer.clearDepth();\n        renderer.render(scene, camera);\n        renderer.autoClear = userAutoClearSetting;\n      }\n    };\n    this.updateForWindowResize = function () {\n      if (this.enabled) {\n        camera.left = window.innerWidth / -2;\n        camera.right = window.innerWidth / 2;\n        camera.top = window.innerHeight / 2;\n        camera.bottom = window.innerHeight / -2;\n        camera.updateProjectionMatrix();\n        this.update();\n      }\n    };\n    this.update = function () {\n      this.position.set(this.position.x, this.position.y);\n      this.size.set(this.size.width, this.size.height);\n    };\n    this.update();\n  }\n}\nexport { ShadowMapViewer };", "map": {"version": 3, "names": ["ShadowMapViewer", "constructor", "light", "scope", "doRenderLabel", "name", "userAutoClearSetting", "frame", "x", "y", "width", "height", "camera", "OrthographicCamera", "window", "innerWidth", "innerHeight", "position", "set", "scene", "Scene", "shader", "UnpackDepthRGBAShader", "uniforms", "UniformsUtils", "clone", "material", "ShaderMaterial", "vertexShader", "fragmentShader", "plane", "PlaneGeometry", "mesh", "<PERSON><PERSON>", "add", "labelCanvas", "labelMesh", "document", "createElement", "context", "getContext", "font", "labelWidth", "measureText", "fillStyle", "fillText", "labelTexture", "Texture", "magFilter", "LinearFilter", "minFilter", "needsUpdate", "labelMaterial", "MeshBasicMaterial", "map", "side", "DoubleSide", "transparent", "labelPlane", "resetPosition", "enabled", "size", "scale", "render", "renderer", "tDiffuse", "value", "shadow", "texture", "autoClear", "clear<PERSON><PERSON>h", "updateForWindowResize", "left", "right", "top", "bottom", "updateProjectionMatrix", "update"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\utils\\ShadowMapViewer.js"], "sourcesContent": ["import {\n  DoubleS<PERSON>,\n  LinearFilter,\n  Mesh,\n  MeshBasicMaterial,\n  OrthographicCamera,\n  PlaneGeometry,\n  Scene,\n  ShaderMaterial,\n  Texture,\n  UniformsUtils,\n} from 'three'\nimport { UnpackDepthRGBAShader } from '../shaders/UnpackDepthRGBAShader'\n\n/**\n * This is a helper for visualising a given light's shadow map.\n * It works for shadow casting lights: DirectionalLight and SpotLight.\n * It renders out the shadow map and displays it on a HUD.\n *\n * Example usage:\n *\t1) Import ShadowMapViewer into your app.\n *\n *\t2) Create a shadow casting light and name it optionally:\n *\t\tlet light = new DirectionalLight( 0xffffff, 1 );\n *\t\tlight.castShadow = true;\n *\t\tlight.name = 'Sun';\n *\n *\t3) Create a shadow map viewer for that light and set its size and position optionally:\n *\t\tlet shadowMapViewer = new ShadowMapViewer( light );\n *\t\tshadowMapViewer.size.set( 128, 128 );\t//width, height  default: 256, 256\n *\t\tshadowMapViewer.position.set( 10, 10 );\t//x, y in pixel\t default: 0, 0 (top left corner)\n *\n *\t4) Render the shadow map viewer in your render loop:\n *\t\tshadowMapViewer.render( renderer );\n *\n *\t5) Optionally: Update the shadow map viewer on window resize:\n *\t\tshadowMapViewer.updateForWindowResize();\n *\n *\t6) If you set the position or size members directly, you need to call shadowMapViewer.update();\n */\n\nclass ShadowMapViewer {\n  constructor(light) {\n    //- Internals\n    const scope = this\n    const doRenderLabel = light.name !== undefined && light.name !== ''\n    let userAutoClearSetting\n\n    //Holds the initial position and dimension of the HUD\n    const frame = {\n      x: 10,\n      y: 10,\n      width: 256,\n      height: 256,\n    }\n\n    const camera = new OrthographicCamera(\n      window.innerWidth / -2,\n      window.innerWidth / 2,\n      window.innerHeight / 2,\n      window.innerHeight / -2,\n      1,\n      10,\n    )\n    camera.position.set(0, 0, 2)\n    const scene = new Scene()\n\n    //HUD for shadow map\n    const shader = UnpackDepthRGBAShader\n\n    const uniforms = UniformsUtils.clone(shader.uniforms)\n    const material = new ShaderMaterial({\n      uniforms: uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n    })\n    const plane = new PlaneGeometry(frame.width, frame.height)\n    const mesh = new Mesh(plane, material)\n\n    scene.add(mesh)\n\n    //Label for light's name\n    let labelCanvas, labelMesh\n\n    if (doRenderLabel) {\n      labelCanvas = document.createElement('canvas')\n\n      const context = labelCanvas.getContext('2d')\n      context.font = 'Bold 20px Arial'\n\n      const labelWidth = context.measureText(light.name).width\n      labelCanvas.width = labelWidth\n      labelCanvas.height = 25 //25 to account for g, p, etc.\n\n      context.font = 'Bold 20px Arial'\n      context.fillStyle = 'rgba( 255, 0, 0, 1 )'\n      context.fillText(light.name, 0, 20)\n\n      const labelTexture = new Texture(labelCanvas)\n      labelTexture.magFilter = LinearFilter\n      labelTexture.minFilter = LinearFilter\n      labelTexture.needsUpdate = true\n\n      const labelMaterial = new MeshBasicMaterial({ map: labelTexture, side: DoubleSide })\n      labelMaterial.transparent = true\n\n      const labelPlane = new PlaneGeometry(labelCanvas.width, labelCanvas.height)\n      labelMesh = new Mesh(labelPlane, labelMaterial)\n\n      scene.add(labelMesh)\n    }\n\n    function resetPosition() {\n      scope.position.set(scope.position.x, scope.position.y)\n    }\n\n    //- API\n    // Set to false to disable displaying this shadow map\n    this.enabled = true\n\n    // Set the size of the displayed shadow map on the HUD\n    this.size = {\n      width: frame.width,\n      height: frame.height,\n      set: function (width, height) {\n        this.width = width\n        this.height = height\n\n        mesh.scale.set(this.width / frame.width, this.height / frame.height, 1)\n\n        //Reset the position as it is off when we scale stuff\n        resetPosition()\n      },\n    }\n\n    // Set the position of the displayed shadow map on the HUD\n    this.position = {\n      x: frame.x,\n      y: frame.y,\n      set: function (x, y) {\n        this.x = x\n        this.y = y\n\n        const width = scope.size.width\n        const height = scope.size.height\n\n        mesh.position.set(-window.innerWidth / 2 + width / 2 + this.x, window.innerHeight / 2 - height / 2 - this.y, 0)\n\n        if (doRenderLabel)\n          labelMesh.position.set(mesh.position.x, mesh.position.y - scope.size.height / 2 + labelCanvas.height / 2, 0)\n      },\n    }\n\n    this.render = function (renderer) {\n      if (this.enabled) {\n        //Because a light's .shadowMap is only initialised after the first render pass\n        //we have to make sure the correct map is sent into the shader, otherwise we\n        //always end up with the scene's first added shadow casting light's shadowMap\n        //in the shader\n        //See: https://github.com/mrdoob/three.js/issues/5932\n        uniforms.tDiffuse.value = light.shadow.map.texture\n\n        userAutoClearSetting = renderer.autoClear\n        renderer.autoClear = false // To allow render overlay\n        renderer.clearDepth()\n        renderer.render(scene, camera)\n        renderer.autoClear = userAutoClearSetting //Restore user's setting\n      }\n    }\n\n    this.updateForWindowResize = function () {\n      if (this.enabled) {\n        camera.left = window.innerWidth / -2\n        camera.right = window.innerWidth / 2\n        camera.top = window.innerHeight / 2\n        camera.bottom = window.innerHeight / -2\n        camera.updateProjectionMatrix()\n\n        this.update()\n      }\n    }\n\n    this.update = function () {\n      this.position.set(this.position.x, this.position.y)\n      this.size.set(this.size.width, this.size.height)\n    }\n\n    //Force an update to set position/size\n    this.update()\n  }\n}\n\nexport { ShadowMapViewer }\n"], "mappings": ";;AAyCA,MAAMA,eAAA,CAAgB;EACpBC,YAAYC,KAAA,EAAO;IAEjB,MAAMC,KAAA,GAAQ;IACd,MAAMC,aAAA,GAAgBF,KAAA,CAAMG,IAAA,KAAS,UAAaH,KAAA,CAAMG,IAAA,KAAS;IACjE,IAAIC,oBAAA;IAGJ,MAAMC,KAAA,GAAQ;MACZC,CAAA,EAAG;MACHC,CAAA,EAAG;MACHC,KAAA,EAAO;MACPC,MAAA,EAAQ;IACT;IAED,MAAMC,MAAA,GAAS,IAAIC,kBAAA,CACjBC,MAAA,CAAOC,UAAA,GAAa,IACpBD,MAAA,CAAOC,UAAA,GAAa,GACpBD,MAAA,CAAOE,WAAA,GAAc,GACrBF,MAAA,CAAOE,WAAA,GAAc,IACrB,GACA,EACD;IACDJ,MAAA,CAAOK,QAAA,CAASC,GAAA,CAAI,GAAG,GAAG,CAAC;IAC3B,MAAMC,KAAA,GAAQ,IAAIC,KAAA,CAAO;IAGzB,MAAMC,MAAA,GAASC,qBAAA;IAEf,MAAMC,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAMJ,MAAA,CAAOE,QAAQ;IACpD,MAAMG,QAAA,GAAW,IAAIC,cAAA,CAAe;MAClCJ,QAAA;MACAK,YAAA,EAAcP,MAAA,CAAOO,YAAA;MACrBC,cAAA,EAAgBR,MAAA,CAAOQ;IAC7B,CAAK;IACD,MAAMC,KAAA,GAAQ,IAAIC,aAAA,CAAcxB,KAAA,CAAMG,KAAA,EAAOH,KAAA,CAAMI,MAAM;IACzD,MAAMqB,IAAA,GAAO,IAAIC,IAAA,CAAKH,KAAA,EAAOJ,QAAQ;IAErCP,KAAA,CAAMe,GAAA,CAAIF,IAAI;IAGd,IAAIG,WAAA,EAAaC,SAAA;IAEjB,IAAIhC,aAAA,EAAe;MACjB+B,WAAA,GAAcE,QAAA,CAASC,aAAA,CAAc,QAAQ;MAE7C,MAAMC,OAAA,GAAUJ,WAAA,CAAYK,UAAA,CAAW,IAAI;MAC3CD,OAAA,CAAQE,IAAA,GAAO;MAEf,MAAMC,UAAA,GAAaH,OAAA,CAAQI,WAAA,CAAYzC,KAAA,CAAMG,IAAI,EAAEK,KAAA;MACnDyB,WAAA,CAAYzB,KAAA,GAAQgC,UAAA;MACpBP,WAAA,CAAYxB,MAAA,GAAS;MAErB4B,OAAA,CAAQE,IAAA,GAAO;MACfF,OAAA,CAAQK,SAAA,GAAY;MACpBL,OAAA,CAAQM,QAAA,CAAS3C,KAAA,CAAMG,IAAA,EAAM,GAAG,EAAE;MAElC,MAAMyC,YAAA,GAAe,IAAIC,OAAA,CAAQZ,WAAW;MAC5CW,YAAA,CAAaE,SAAA,GAAYC,YAAA;MACzBH,YAAA,CAAaI,SAAA,GAAYD,YAAA;MACzBH,YAAA,CAAaK,WAAA,GAAc;MAE3B,MAAMC,aAAA,GAAgB,IAAIC,iBAAA,CAAkB;QAAEC,GAAA,EAAKR,YAAA;QAAcS,IAAA,EAAMC;MAAA,CAAY;MACnFJ,aAAA,CAAcK,WAAA,GAAc;MAE5B,MAAMC,UAAA,GAAa,IAAI3B,aAAA,CAAcI,WAAA,CAAYzB,KAAA,EAAOyB,WAAA,CAAYxB,MAAM;MAC1EyB,SAAA,GAAY,IAAIH,IAAA,CAAKyB,UAAA,EAAYN,aAAa;MAE9CjC,KAAA,CAAMe,GAAA,CAAIE,SAAS;IACpB;IAED,SAASuB,cAAA,EAAgB;MACvBxD,KAAA,CAAMc,QAAA,CAASC,GAAA,CAAIf,KAAA,CAAMc,QAAA,CAAST,CAAA,EAAGL,KAAA,CAAMc,QAAA,CAASR,CAAC;IACtD;IAID,KAAKmD,OAAA,GAAU;IAGf,KAAKC,IAAA,GAAO;MACVnD,KAAA,EAAOH,KAAA,CAAMG,KAAA;MACbC,MAAA,EAAQJ,KAAA,CAAMI,MAAA;MACdO,GAAA,EAAK,SAAAA,CAAUR,KAAA,EAAOC,MAAA,EAAQ;QAC5B,KAAKD,KAAA,GAAQA,KAAA;QACb,KAAKC,MAAA,GAASA,MAAA;QAEdqB,IAAA,CAAK8B,KAAA,CAAM5C,GAAA,CAAI,KAAKR,KAAA,GAAQH,KAAA,CAAMG,KAAA,EAAO,KAAKC,MAAA,GAASJ,KAAA,CAAMI,MAAA,EAAQ,CAAC;QAGtEgD,aAAA,CAAe;MAChB;IACF;IAGD,KAAK1C,QAAA,GAAW;MACdT,CAAA,EAAGD,KAAA,CAAMC,CAAA;MACTC,CAAA,EAAGF,KAAA,CAAME,CAAA;MACTS,GAAA,EAAK,SAAAA,CAAUV,CAAA,EAAGC,CAAA,EAAG;QACnB,KAAKD,CAAA,GAAIA,CAAA;QACT,KAAKC,CAAA,GAAIA,CAAA;QAET,MAAMC,KAAA,GAAQP,KAAA,CAAM0D,IAAA,CAAKnD,KAAA;QACzB,MAAMC,MAAA,GAASR,KAAA,CAAM0D,IAAA,CAAKlD,MAAA;QAE1BqB,IAAA,CAAKf,QAAA,CAASC,GAAA,CAAI,CAACJ,MAAA,CAAOC,UAAA,GAAa,IAAIL,KAAA,GAAQ,IAAI,KAAKF,CAAA,EAAGM,MAAA,CAAOE,WAAA,GAAc,IAAIL,MAAA,GAAS,IAAI,KAAKF,CAAA,EAAG,CAAC;QAE9G,IAAIL,aAAA,EACFgC,SAAA,CAAUnB,QAAA,CAASC,GAAA,CAAIc,IAAA,CAAKf,QAAA,CAAST,CAAA,EAAGwB,IAAA,CAAKf,QAAA,CAASR,CAAA,GAAIN,KAAA,CAAM0D,IAAA,CAAKlD,MAAA,GAAS,IAAIwB,WAAA,CAAYxB,MAAA,GAAS,GAAG,CAAC;MAC9G;IACF;IAED,KAAKoD,MAAA,GAAS,UAAUC,QAAA,EAAU;MAChC,IAAI,KAAKJ,OAAA,EAAS;QAMhBrC,QAAA,CAAS0C,QAAA,CAASC,KAAA,GAAQhE,KAAA,CAAMiE,MAAA,CAAOb,GAAA,CAAIc,OAAA;QAE3C9D,oBAAA,GAAuB0D,QAAA,CAASK,SAAA;QAChCL,QAAA,CAASK,SAAA,GAAY;QACrBL,QAAA,CAASM,UAAA,CAAY;QACrBN,QAAA,CAASD,MAAA,CAAO5C,KAAA,EAAOP,MAAM;QAC7BoD,QAAA,CAASK,SAAA,GAAY/D,oBAAA;MACtB;IACF;IAED,KAAKiE,qBAAA,GAAwB,YAAY;MACvC,IAAI,KAAKX,OAAA,EAAS;QAChBhD,MAAA,CAAO4D,IAAA,GAAO1D,MAAA,CAAOC,UAAA,GAAa;QAClCH,MAAA,CAAO6D,KAAA,GAAQ3D,MAAA,CAAOC,UAAA,GAAa;QACnCH,MAAA,CAAO8D,GAAA,GAAM5D,MAAA,CAAOE,WAAA,GAAc;QAClCJ,MAAA,CAAO+D,MAAA,GAAS7D,MAAA,CAAOE,WAAA,GAAc;QACrCJ,MAAA,CAAOgE,sBAAA,CAAwB;QAE/B,KAAKC,MAAA,CAAQ;MACd;IACF;IAED,KAAKA,MAAA,GAAS,YAAY;MACxB,KAAK5D,QAAA,CAASC,GAAA,CAAI,KAAKD,QAAA,CAAST,CAAA,EAAG,KAAKS,QAAA,CAASR,CAAC;MAClD,KAAKoD,IAAA,CAAK3C,GAAA,CAAI,KAAK2C,IAAA,CAAKnD,KAAA,EAAO,KAAKmD,IAAA,CAAKlD,MAAM;IAChD;IAGD,KAAKkE,MAAA,CAAQ;EACd;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}