{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\UserManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserManagement = () => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Mock data for now\n    setTimeout(() => {\n      setUsers([{\n        id: '1',\n        firstName: 'Admin',\n        lastName: 'User',\n        email: '<EMAIL>',\n        role: 'Admin',\n        status: 'Active',\n        lastLogin: new Date().toISOString()\n      }, {\n        id: '2',\n        firstName: 'Manager',\n        lastName: 'User',\n        email: '<EMAIL>',\n        role: 'Employee',\n        status: 'Active',\n        lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()\n      }]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleString('en-PH');\n  };\n  const getRoleColor = role => {\n    switch (role.toLowerCase()) {\n      case 'admin':\n        return '#e74c3c';\n      case 'employee':\n        return '#3498db';\n      case 'customer':\n        return '#27ae60';\n      default:\n        return '#95a5a6';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading users...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"admin-card-title\",\n        children: \"User Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"admin-btn admin-btn-primary\",\n        children: \"Add New User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"admin-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Role\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Last Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [user.firstName, \" \", user.lastName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: getRoleColor(user.role)\n                  },\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: '#27ae60'\n                  },\n                  children: user.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDate(user.lastLogin)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"admin-btn admin-btn-secondary btn-small\",\n                  children: \"Edit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this)]\n            }, user.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagement, \"+quVjBMM9THpHvnUcBaphXhhZmo=\");\n_c = UserManagement;\nexport default UserManagement;\nvar _c;\n$RefreshReg$(_c, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "UserManagement", "_s", "users", "setUsers", "loading", "setLoading", "setTimeout", "id", "firstName", "lastName", "email", "role", "status", "lastLogin", "Date", "toISOString", "now", "formatDate", "dateString", "toLocaleString", "getRoleColor", "toLowerCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "user", "style", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/components/admin/UserManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst UserManagement = () => {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Mock data for now\n    setTimeout(() => {\n      setUsers([\n        {\n          id: '1',\n          firstName: 'Admin',\n          lastName: 'User',\n          email: '<EMAIL>',\n          role: 'Admin',\n          status: 'Active',\n          lastLogin: new Date().toISOString()\n        },\n        {\n          id: '2',\n          firstName: 'Manager',\n          lastName: 'User',\n          email: '<EMAIL>',\n          role: 'Employee',\n          status: 'Active',\n          lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()\n        }\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleString('en-PH');\n  };\n\n  const getRoleColor = (role) => {\n    switch (role.toLowerCase()) {\n      case 'admin': return '#e74c3c';\n      case 'employee': return '#3498db';\n      case 'customer': return '#27ae60';\n      default: return '#95a5a6';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"admin-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading users...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"user-management\">\n      <div className=\"admin-card-header\">\n        <h1 className=\"admin-card-title\">User Management</h1>\n        <button className=\"admin-btn admin-btn-primary\">Add New User</button>\n      </div>\n\n      <div className=\"admin-card\">\n        <div className=\"table-container\">\n          <table className=\"admin-table\">\n            <thead>\n              <tr>\n                <th>Name</th>\n                <th>Email</th>\n                <th>Role</th>\n                <th>Status</th>\n                <th>Last Login</th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {users.map(user => (\n                <tr key={user.id}>\n                  <td>{user.firstName} {user.lastName}</td>\n                  <td>{user.email}</td>\n                  <td>\n                    <span \n                      className=\"status-badge\"\n                      style={{ backgroundColor: getRoleColor(user.role) }}\n                    >\n                      {user.role}\n                    </span>\n                  </td>\n                  <td>\n                    <span className=\"status-badge\" style={{ backgroundColor: '#27ae60' }}>\n                      {user.status}\n                    </span>\n                  </td>\n                  <td>{formatDate(user.lastLogin)}</td>\n                  <td>\n                    <button className=\"admin-btn admin-btn-secondary btn-small\">Edit</button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACAS,UAAU,CAAC,MAAM;MACfH,QAAQ,CAAC,CACP;QACEI,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,OAAO;QAClBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,sBAAsB;QAC7BC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,SAAS,EAAE,SAAS;QACpBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,wBAAwB;QAC/BC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC;MACnE,CAAC,CACF,CAAC;MACFV,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIJ,IAAI,CAACI,UAAU,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;EACrD,CAAC;EAED,MAAMC,YAAY,GAAIT,IAAI,IAAK;IAC7B,QAAQA,IAAI,CAACU,WAAW,CAAC,CAAC;MACxB,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAIjB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKuB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxB,OAAA;QAAKuB,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC5B,OAAA;QAAAwB,QAAA,EAAG;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAEV;EAEA,oBACE5B,OAAA;IAAKuB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BxB,OAAA;MAAKuB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxB,OAAA;QAAIuB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrD5B,OAAA;QAAQuB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAEN5B,OAAA;MAAKuB,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBxB,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BxB,OAAA;UAAOuB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC5BxB,OAAA;YAAAwB,QAAA,eACExB,OAAA;cAAAwB,QAAA,gBACExB,OAAA;gBAAAwB,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb5B,OAAA;gBAAAwB,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd5B,OAAA;gBAAAwB,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb5B,OAAA;gBAAAwB,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf5B,OAAA;gBAAAwB,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB5B,OAAA;gBAAAwB,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR5B,OAAA;YAAAwB,QAAA,EACGrB,KAAK,CAAC0B,GAAG,CAACC,IAAI,iBACb9B,OAAA;cAAAwB,QAAA,gBACExB,OAAA;gBAAAwB,QAAA,GAAKM,IAAI,CAACrB,SAAS,EAAC,GAAC,EAACqB,IAAI,CAACpB,QAAQ;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzC5B,OAAA;gBAAAwB,QAAA,EAAKM,IAAI,CAACnB;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrB5B,OAAA;gBAAAwB,QAAA,eACExB,OAAA;kBACEuB,SAAS,EAAC,cAAc;kBACxBQ,KAAK,EAAE;oBAAEC,eAAe,EAAEX,YAAY,CAACS,IAAI,CAAClB,IAAI;kBAAE,CAAE;kBAAAY,QAAA,EAEnDM,IAAI,CAAClB;gBAAI;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL5B,OAAA;gBAAAwB,QAAA,eACExB,OAAA;kBAAMuB,SAAS,EAAC,cAAc;kBAACQ,KAAK,EAAE;oBAAEC,eAAe,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EAClEM,IAAI,CAACjB;gBAAM;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL5B,OAAA;gBAAAwB,QAAA,EAAKN,UAAU,CAACY,IAAI,CAAChB,SAAS;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrC5B,OAAA;gBAAAwB,QAAA,eACExB,OAAA;kBAAQuB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA,GAnBEE,IAAI,CAACtB,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAvGID,cAAc;AAAAgC,EAAA,GAAdhC,cAAc;AAyGpB,eAAeA,cAAc;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}