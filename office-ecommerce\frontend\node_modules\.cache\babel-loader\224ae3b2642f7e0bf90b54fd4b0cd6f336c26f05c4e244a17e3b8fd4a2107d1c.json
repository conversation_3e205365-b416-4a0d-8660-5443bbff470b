{"ast": null, "code": "import { S as SUPPORT, C as ConfigResolverMap, _ as _objectSpread2, a as _defineProperty, t as toDomEventType, i as isTouch, b as touchIds, E as EngineMap, c as chain, p as parseProp, d as toHandlerProp } from './actions-fe213e88.esm.js';\nimport './maths-0ab39ae9.esm.js';\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nconst sharedConfigResolver = {\n  target(value) {\n    if (value) {\n      return () => 'current' in value ? value.current : value;\n    }\n    return undefined;\n  },\n  enabled(value = true) {\n    return value;\n  },\n  window(value = SUPPORT.isBrowser ? window : undefined) {\n    return value;\n  },\n  eventOptions({\n    passive = true,\n    capture = false\n  } = {}) {\n    return {\n      passive,\n      capture\n    };\n  },\n  transform(value) {\n    return value;\n  }\n};\nconst _excluded = [\"target\", \"eventOptions\", \"window\", \"enabled\", \"transform\"];\nfunction resolveWith(config = {}, resolvers) {\n  const result = {};\n  for (const [key, resolver] of Object.entries(resolvers)) {\n    switch (typeof resolver) {\n      case 'function':\n        if (process.env.NODE_ENV === 'development') {\n          const r = resolver.call(result, config[key], key, config);\n          if (!Number.isNaN(r)) result[key] = r;\n        } else {\n          result[key] = resolver.call(result, config[key], key, config);\n        }\n        break;\n      case 'object':\n        result[key] = resolveWith(config[key], resolver);\n        break;\n      case 'boolean':\n        if (resolver) result[key] = config[key];\n        break;\n    }\n  }\n  return result;\n}\nfunction parse(newConfig, gestureKey, _config = {}) {\n  const _ref = newConfig,\n    {\n      target,\n      eventOptions,\n      window,\n      enabled,\n      transform\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  _config.shared = resolveWith({\n    target,\n    eventOptions,\n    window,\n    enabled,\n    transform\n  }, sharedConfigResolver);\n  if (gestureKey) {\n    const resolver = ConfigResolverMap.get(gestureKey);\n    _config[gestureKey] = resolveWith(_objectSpread2({\n      shared: _config.shared\n    }, rest), resolver);\n  } else {\n    for (const key in rest) {\n      const resolver = ConfigResolverMap.get(key);\n      if (resolver) {\n        _config[key] = resolveWith(_objectSpread2({\n          shared: _config.shared\n        }, rest[key]), resolver);\n      } else if (process.env.NODE_ENV === 'development') {\n        if (!['drag', 'pinch', 'scroll', 'wheel', 'move', 'hover'].includes(key)) {\n          if (key === 'domTarget') {\n            throw Error(`[@use-gesture]: \\`domTarget\\` option has been renamed to \\`target\\`.`);\n          }\n          console.warn(`[@use-gesture]: Unknown config key \\`${key}\\` was used. Please read the documentation for further information.`);\n        }\n      }\n    }\n  }\n  return _config;\n}\nclass EventStore {\n  constructor(ctrl, gestureKey) {\n    _defineProperty(this, \"_listeners\", new Set());\n    this._ctrl = ctrl;\n    this._gestureKey = gestureKey;\n  }\n  add(element, device, action, handler, options) {\n    const listeners = this._listeners;\n    const type = toDomEventType(device, action);\n    const _options = this._gestureKey ? this._ctrl.config[this._gestureKey].eventOptions : {};\n    const eventOptions = _objectSpread2(_objectSpread2({}, _options), options);\n    element.addEventListener(type, handler, eventOptions);\n    const remove = () => {\n      element.removeEventListener(type, handler, eventOptions);\n      listeners.delete(remove);\n    };\n    listeners.add(remove);\n    return remove;\n  }\n  clean() {\n    this._listeners.forEach(remove => remove());\n    this._listeners.clear();\n  }\n}\nclass TimeoutStore {\n  constructor() {\n    _defineProperty(this, \"_timeouts\", new Map());\n  }\n  add(key, callback, ms = 140, ...args) {\n    this.remove(key);\n    this._timeouts.set(key, window.setTimeout(callback, ms, ...args));\n  }\n  remove(key) {\n    const timeout = this._timeouts.get(key);\n    if (timeout) window.clearTimeout(timeout);\n  }\n  clean() {\n    this._timeouts.forEach(timeout => void window.clearTimeout(timeout));\n    this._timeouts.clear();\n  }\n}\nclass Controller {\n  constructor(handlers) {\n    _defineProperty(this, \"gestures\", new Set());\n    _defineProperty(this, \"_targetEventStore\", new EventStore(this));\n    _defineProperty(this, \"gestureEventStores\", {});\n    _defineProperty(this, \"gestureTimeoutStores\", {});\n    _defineProperty(this, \"handlers\", {});\n    _defineProperty(this, \"config\", {});\n    _defineProperty(this, \"pointerIds\", new Set());\n    _defineProperty(this, \"touchIds\", new Set());\n    _defineProperty(this, \"state\", {\n      shared: {\n        shiftKey: false,\n        metaKey: false,\n        ctrlKey: false,\n        altKey: false\n      }\n    });\n    resolveGestures(this, handlers);\n  }\n  setEventIds(event) {\n    if (isTouch(event)) {\n      this.touchIds = new Set(touchIds(event));\n      return this.touchIds;\n    } else if ('pointerId' in event) {\n      if (event.type === 'pointerup' || event.type === 'pointercancel') this.pointerIds.delete(event.pointerId);else if (event.type === 'pointerdown') this.pointerIds.add(event.pointerId);\n      return this.pointerIds;\n    }\n  }\n  applyHandlers(handlers, nativeHandlers) {\n    this.handlers = handlers;\n    this.nativeHandlers = nativeHandlers;\n  }\n  applyConfig(config, gestureKey) {\n    this.config = parse(config, gestureKey, this.config);\n  }\n  clean() {\n    this._targetEventStore.clean();\n    for (const key of this.gestures) {\n      this.gestureEventStores[key].clean();\n      this.gestureTimeoutStores[key].clean();\n    }\n  }\n  effect() {\n    if (this.config.shared.target) this.bind();\n    return () => this._targetEventStore.clean();\n  }\n  bind(...args) {\n    const sharedConfig = this.config.shared;\n    const props = {};\n    let target;\n    if (sharedConfig.target) {\n      target = sharedConfig.target();\n      if (!target) return;\n    }\n    if (sharedConfig.enabled) {\n      for (const gestureKey of this.gestures) {\n        const gestureConfig = this.config[gestureKey];\n        const bindFunction = bindToProps(props, gestureConfig.eventOptions, !!target);\n        if (gestureConfig.enabled) {\n          const Engine = EngineMap.get(gestureKey);\n          new Engine(this, args, gestureKey).bind(bindFunction);\n        }\n      }\n      const nativeBindFunction = bindToProps(props, sharedConfig.eventOptions, !!target);\n      for (const eventKey in this.nativeHandlers) {\n        nativeBindFunction(eventKey, '', event => this.nativeHandlers[eventKey](_objectSpread2(_objectSpread2({}, this.state.shared), {}, {\n          event,\n          args\n        })), undefined, true);\n      }\n    }\n    for (const handlerProp in props) {\n      props[handlerProp] = chain(...props[handlerProp]);\n    }\n    if (!target) return props;\n    for (const handlerProp in props) {\n      const {\n        device,\n        capture,\n        passive\n      } = parseProp(handlerProp);\n      this._targetEventStore.add(target, device, '', props[handlerProp], {\n        capture,\n        passive\n      });\n    }\n  }\n}\nfunction setupGesture(ctrl, gestureKey) {\n  ctrl.gestures.add(gestureKey);\n  ctrl.gestureEventStores[gestureKey] = new EventStore(ctrl, gestureKey);\n  ctrl.gestureTimeoutStores[gestureKey] = new TimeoutStore();\n}\nfunction resolveGestures(ctrl, internalHandlers) {\n  if (internalHandlers.drag) setupGesture(ctrl, 'drag');\n  if (internalHandlers.wheel) setupGesture(ctrl, 'wheel');\n  if (internalHandlers.scroll) setupGesture(ctrl, 'scroll');\n  if (internalHandlers.move) setupGesture(ctrl, 'move');\n  if (internalHandlers.pinch) setupGesture(ctrl, 'pinch');\n  if (internalHandlers.hover) setupGesture(ctrl, 'hover');\n}\nconst bindToProps = (props, eventOptions, withPassiveOption) => (device, action, handler, options = {}, isNative = false) => {\n  var _options$capture, _options$passive;\n  const capture = (_options$capture = options.capture) !== null && _options$capture !== void 0 ? _options$capture : eventOptions.capture;\n  const passive = (_options$passive = options.passive) !== null && _options$passive !== void 0 ? _options$passive : eventOptions.passive;\n  let handlerProp = isNative ? device : toHandlerProp(device, action, capture);\n  if (withPassiveOption && passive) handlerProp += 'Passive';\n  props[handlerProp] = props[handlerProp] || [];\n  props[handlerProp].push(handler);\n};\nconst RE_NOT_NATIVE = /^on(Drag|Wheel|Scroll|Move|Pinch|Hover)/;\nfunction sortHandlers(_handlers) {\n  const native = {};\n  const handlers = {};\n  const actions = new Set();\n  for (let key in _handlers) {\n    if (RE_NOT_NATIVE.test(key)) {\n      actions.add(RegExp.lastMatch);\n      handlers[key] = _handlers[key];\n    } else {\n      native[key] = _handlers[key];\n    }\n  }\n  return [handlers, native, actions];\n}\nfunction registerGesture(actions, handlers, handlerKey, key, internalHandlers, config) {\n  if (!actions.has(handlerKey)) return;\n  if (!EngineMap.has(key)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(`[@use-gesture]: You've created a custom handler that that uses the \\`${key}\\` gesture but isn't properly configured.\\n\\nPlease add \\`${key}Action\\` when creating your handler.`);\n    }\n    return;\n  }\n  const startKey = handlerKey + 'Start';\n  const endKey = handlerKey + 'End';\n  const fn = state => {\n    let memo = undefined;\n    if (state.first && startKey in handlers) handlers[startKey](state);\n    if (handlerKey in handlers) memo = handlers[handlerKey](state);\n    if (state.last && endKey in handlers) handlers[endKey](state);\n    return memo;\n  };\n  internalHandlers[key] = fn;\n  config[key] = config[key] || {};\n}\nfunction parseMergedHandlers(mergedHandlers, mergedConfig) {\n  const [handlers, nativeHandlers, actions] = sortHandlers(mergedHandlers);\n  const internalHandlers = {};\n  registerGesture(actions, handlers, 'onDrag', 'drag', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onWheel', 'wheel', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onScroll', 'scroll', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onPinch', 'pinch', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onMove', 'move', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onHover', 'hover', internalHandlers, mergedConfig);\n  return {\n    handlers: internalHandlers,\n    config: mergedConfig,\n    nativeHandlers\n  };\n}\nexport { Controller, parseMergedHandlers };", "map": {"version": 3, "names": ["S", "SUPPORT", "C", "ConfigResolverMap", "_", "_objectSpread2", "a", "_defineProperty", "t", "toDomEventType", "i", "is<PERSON><PERSON>ch", "b", "touchIds", "E", "EngineMap", "c", "chain", "p", "parseProp", "d", "toHandlerProp", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "length", "indexOf", "_objectWithoutProperties", "getOwnPropertySymbols", "sourceSymbolKeys", "prototype", "propertyIsEnumerable", "call", "sharedConfigResolver", "value", "current", "undefined", "enabled", "window", "<PERSON><PERSON><PERSON><PERSON>", "eventOptions", "passive", "capture", "transform", "_excluded", "resolveWith", "config", "resolvers", "result", "resolver", "entries", "process", "env", "NODE_ENV", "r", "Number", "isNaN", "parse", "newConfig", "<PERSON><PERSON><PERSON>", "_config", "_ref", "rest", "shared", "get", "includes", "Error", "console", "warn", "EventStore", "constructor", "ctrl", "Set", "_ctrl", "_gesture<PERSON><PERSON>", "add", "element", "device", "action", "handler", "options", "listeners", "_listeners", "type", "_options", "addEventListener", "remove", "removeEventListener", "delete", "clean", "for<PERSON>ach", "clear", "TimeoutStore", "Map", "callback", "ms", "args", "_timeouts", "set", "setTimeout", "timeout", "clearTimeout", "Controller", "handlers", "shift<PERSON>ey", "metaKey", "ctrl<PERSON>ey", "altKey", "resolveGestures", "setEventIds", "event", "pointerIds", "pointerId", "applyHandlers", "nativeHandlers", "applyConfig", "_targetEventStore", "gestures", "gestureEventStores", "gestureTimeoutStores", "effect", "bind", "sharedConfig", "props", "gestureConfig", "bindFunction", "bindToProps", "Engine", "nativeBindFunction", "eventKey", "state", "handlerProp", "setupGesture", "internalHandlers", "drag", "wheel", "scroll", "move", "pinch", "hover", "withPassiveOption", "isNative", "_options$capture", "_options$passive", "push", "RE_NOT_NATIVE", "sortHandlers", "_handlers", "native", "actions", "test", "RegExp", "lastMatch", "registerGesture", "handler<PERSON><PERSON>", "has", "startKey", "<PERSON><PERSON><PERSON>", "fn", "memo", "first", "last", "parseMergedHandlers", "mergedHandlers", "mergedConfig"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@use-gesture/core/dist/use-gesture-core.esm.js"], "sourcesContent": ["import { S as SUPPORT, C as ConfigResolverMap, _ as _objectSpread2, a as _defineProperty, t as toDomEventType, i as isTouch, b as touchIds, E as EngineMap, c as chain, p as parseProp, d as toHandlerProp } from './actions-fe213e88.esm.js';\nimport './maths-0ab39ae9.esm.js';\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n\nconst sharedConfigResolver = {\n  target(value) {\n    if (value) {\n      return () => 'current' in value ? value.current : value;\n    }\n    return undefined;\n  },\n  enabled(value = true) {\n    return value;\n  },\n  window(value = SUPPORT.isBrowser ? window : undefined) {\n    return value;\n  },\n  eventOptions({\n    passive = true,\n    capture = false\n  } = {}) {\n    return {\n      passive,\n      capture\n    };\n  },\n  transform(value) {\n    return value;\n  }\n};\n\nconst _excluded = [\"target\", \"eventOptions\", \"window\", \"enabled\", \"transform\"];\nfunction resolveWith(config = {}, resolvers) {\n  const result = {};\n  for (const [key, resolver] of Object.entries(resolvers)) {\n    switch (typeof resolver) {\n      case 'function':\n        if (process.env.NODE_ENV === 'development') {\n          const r = resolver.call(result, config[key], key, config);\n          if (!Number.isNaN(r)) result[key] = r;\n        } else {\n          result[key] = resolver.call(result, config[key], key, config);\n        }\n        break;\n      case 'object':\n        result[key] = resolveWith(config[key], resolver);\n        break;\n      case 'boolean':\n        if (resolver) result[key] = config[key];\n        break;\n    }\n  }\n  return result;\n}\nfunction parse(newConfig, gestureKey, _config = {}) {\n  const _ref = newConfig,\n    {\n      target,\n      eventOptions,\n      window,\n      enabled,\n      transform\n    } = _ref,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  _config.shared = resolveWith({\n    target,\n    eventOptions,\n    window,\n    enabled,\n    transform\n  }, sharedConfigResolver);\n  if (gestureKey) {\n    const resolver = ConfigResolverMap.get(gestureKey);\n    _config[gestureKey] = resolveWith(_objectSpread2({\n      shared: _config.shared\n    }, rest), resolver);\n  } else {\n    for (const key in rest) {\n      const resolver = ConfigResolverMap.get(key);\n      if (resolver) {\n        _config[key] = resolveWith(_objectSpread2({\n          shared: _config.shared\n        }, rest[key]), resolver);\n      } else if (process.env.NODE_ENV === 'development') {\n        if (!['drag', 'pinch', 'scroll', 'wheel', 'move', 'hover'].includes(key)) {\n          if (key === 'domTarget') {\n            throw Error(`[@use-gesture]: \\`domTarget\\` option has been renamed to \\`target\\`.`);\n          }\n          console.warn(`[@use-gesture]: Unknown config key \\`${key}\\` was used. Please read the documentation for further information.`);\n        }\n      }\n    }\n  }\n  return _config;\n}\n\nclass EventStore {\n  constructor(ctrl, gestureKey) {\n    _defineProperty(this, \"_listeners\", new Set());\n    this._ctrl = ctrl;\n    this._gestureKey = gestureKey;\n  }\n  add(element, device, action, handler, options) {\n    const listeners = this._listeners;\n    const type = toDomEventType(device, action);\n    const _options = this._gestureKey ? this._ctrl.config[this._gestureKey].eventOptions : {};\n    const eventOptions = _objectSpread2(_objectSpread2({}, _options), options);\n    element.addEventListener(type, handler, eventOptions);\n    const remove = () => {\n      element.removeEventListener(type, handler, eventOptions);\n      listeners.delete(remove);\n    };\n    listeners.add(remove);\n    return remove;\n  }\n  clean() {\n    this._listeners.forEach(remove => remove());\n    this._listeners.clear();\n  }\n}\n\nclass TimeoutStore {\n  constructor() {\n    _defineProperty(this, \"_timeouts\", new Map());\n  }\n  add(key, callback, ms = 140, ...args) {\n    this.remove(key);\n    this._timeouts.set(key, window.setTimeout(callback, ms, ...args));\n  }\n  remove(key) {\n    const timeout = this._timeouts.get(key);\n    if (timeout) window.clearTimeout(timeout);\n  }\n  clean() {\n    this._timeouts.forEach(timeout => void window.clearTimeout(timeout));\n    this._timeouts.clear();\n  }\n}\n\nclass Controller {\n  constructor(handlers) {\n    _defineProperty(this, \"gestures\", new Set());\n    _defineProperty(this, \"_targetEventStore\", new EventStore(this));\n    _defineProperty(this, \"gestureEventStores\", {});\n    _defineProperty(this, \"gestureTimeoutStores\", {});\n    _defineProperty(this, \"handlers\", {});\n    _defineProperty(this, \"config\", {});\n    _defineProperty(this, \"pointerIds\", new Set());\n    _defineProperty(this, \"touchIds\", new Set());\n    _defineProperty(this, \"state\", {\n      shared: {\n        shiftKey: false,\n        metaKey: false,\n        ctrlKey: false,\n        altKey: false\n      }\n    });\n    resolveGestures(this, handlers);\n  }\n  setEventIds(event) {\n    if (isTouch(event)) {\n      this.touchIds = new Set(touchIds(event));\n      return this.touchIds;\n    } else if ('pointerId' in event) {\n      if (event.type === 'pointerup' || event.type === 'pointercancel') this.pointerIds.delete(event.pointerId);else if (event.type === 'pointerdown') this.pointerIds.add(event.pointerId);\n      return this.pointerIds;\n    }\n  }\n  applyHandlers(handlers, nativeHandlers) {\n    this.handlers = handlers;\n    this.nativeHandlers = nativeHandlers;\n  }\n  applyConfig(config, gestureKey) {\n    this.config = parse(config, gestureKey, this.config);\n  }\n  clean() {\n    this._targetEventStore.clean();\n    for (const key of this.gestures) {\n      this.gestureEventStores[key].clean();\n      this.gestureTimeoutStores[key].clean();\n    }\n  }\n  effect() {\n    if (this.config.shared.target) this.bind();\n    return () => this._targetEventStore.clean();\n  }\n  bind(...args) {\n    const sharedConfig = this.config.shared;\n    const props = {};\n    let target;\n    if (sharedConfig.target) {\n      target = sharedConfig.target();\n      if (!target) return;\n    }\n    if (sharedConfig.enabled) {\n      for (const gestureKey of this.gestures) {\n        const gestureConfig = this.config[gestureKey];\n        const bindFunction = bindToProps(props, gestureConfig.eventOptions, !!target);\n        if (gestureConfig.enabled) {\n          const Engine = EngineMap.get(gestureKey);\n          new Engine(this, args, gestureKey).bind(bindFunction);\n        }\n      }\n      const nativeBindFunction = bindToProps(props, sharedConfig.eventOptions, !!target);\n      for (const eventKey in this.nativeHandlers) {\n        nativeBindFunction(eventKey, '', event => this.nativeHandlers[eventKey](_objectSpread2(_objectSpread2({}, this.state.shared), {}, {\n          event,\n          args\n        })), undefined, true);\n      }\n    }\n    for (const handlerProp in props) {\n      props[handlerProp] = chain(...props[handlerProp]);\n    }\n    if (!target) return props;\n    for (const handlerProp in props) {\n      const {\n        device,\n        capture,\n        passive\n      } = parseProp(handlerProp);\n      this._targetEventStore.add(target, device, '', props[handlerProp], {\n        capture,\n        passive\n      });\n    }\n  }\n}\nfunction setupGesture(ctrl, gestureKey) {\n  ctrl.gestures.add(gestureKey);\n  ctrl.gestureEventStores[gestureKey] = new EventStore(ctrl, gestureKey);\n  ctrl.gestureTimeoutStores[gestureKey] = new TimeoutStore();\n}\nfunction resolveGestures(ctrl, internalHandlers) {\n  if (internalHandlers.drag) setupGesture(ctrl, 'drag');\n  if (internalHandlers.wheel) setupGesture(ctrl, 'wheel');\n  if (internalHandlers.scroll) setupGesture(ctrl, 'scroll');\n  if (internalHandlers.move) setupGesture(ctrl, 'move');\n  if (internalHandlers.pinch) setupGesture(ctrl, 'pinch');\n  if (internalHandlers.hover) setupGesture(ctrl, 'hover');\n}\nconst bindToProps = (props, eventOptions, withPassiveOption) => (device, action, handler, options = {}, isNative = false) => {\n  var _options$capture, _options$passive;\n  const capture = (_options$capture = options.capture) !== null && _options$capture !== void 0 ? _options$capture : eventOptions.capture;\n  const passive = (_options$passive = options.passive) !== null && _options$passive !== void 0 ? _options$passive : eventOptions.passive;\n  let handlerProp = isNative ? device : toHandlerProp(device, action, capture);\n  if (withPassiveOption && passive) handlerProp += 'Passive';\n  props[handlerProp] = props[handlerProp] || [];\n  props[handlerProp].push(handler);\n};\n\nconst RE_NOT_NATIVE = /^on(Drag|Wheel|Scroll|Move|Pinch|Hover)/;\nfunction sortHandlers(_handlers) {\n  const native = {};\n  const handlers = {};\n  const actions = new Set();\n  for (let key in _handlers) {\n    if (RE_NOT_NATIVE.test(key)) {\n      actions.add(RegExp.lastMatch);\n      handlers[key] = _handlers[key];\n    } else {\n      native[key] = _handlers[key];\n    }\n  }\n  return [handlers, native, actions];\n}\nfunction registerGesture(actions, handlers, handlerKey, key, internalHandlers, config) {\n  if (!actions.has(handlerKey)) return;\n  if (!EngineMap.has(key)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(`[@use-gesture]: You've created a custom handler that that uses the \\`${key}\\` gesture but isn't properly configured.\\n\\nPlease add \\`${key}Action\\` when creating your handler.`);\n    }\n    return;\n  }\n  const startKey = handlerKey + 'Start';\n  const endKey = handlerKey + 'End';\n  const fn = state => {\n    let memo = undefined;\n    if (state.first && startKey in handlers) handlers[startKey](state);\n    if (handlerKey in handlers) memo = handlers[handlerKey](state);\n    if (state.last && endKey in handlers) handlers[endKey](state);\n    return memo;\n  };\n  internalHandlers[key] = fn;\n  config[key] = config[key] || {};\n}\nfunction parseMergedHandlers(mergedHandlers, mergedConfig) {\n  const [handlers, nativeHandlers, actions] = sortHandlers(mergedHandlers);\n  const internalHandlers = {};\n  registerGesture(actions, handlers, 'onDrag', 'drag', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onWheel', 'wheel', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onScroll', 'scroll', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onPinch', 'pinch', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onMove', 'move', internalHandlers, mergedConfig);\n  registerGesture(actions, handlers, 'onHover', 'hover', internalHandlers, mergedConfig);\n  return {\n    handlers: internalHandlers,\n    config: mergedConfig,\n    nativeHandlers\n  };\n}\n\nexport { Controller, parseMergedHandlers };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,aAAa,QAAQ,2BAA2B;AAC7O,OAAO,yBAAyB;AAEhC,SAASC,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACvD,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIE,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EACpC,IAAIM,GAAG,EAAEnB,CAAC;EACV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,UAAU,CAACI,MAAM,EAAEpB,CAAC,EAAE,EAAE;IACtCmB,GAAG,GAAGH,UAAU,CAAChB,CAAC,CAAC;IACnB,IAAIc,QAAQ,CAACO,OAAO,CAACF,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;EAC3B;EACA,OAAOJ,MAAM;AACf;AAEA,SAASO,wBAAwBA,CAACT,MAAM,EAAEC,QAAQ,EAAE;EAClD,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIE,MAAM,GAAGH,6BAA6B,CAACC,MAAM,EAAEC,QAAQ,CAAC;EAC5D,IAAIK,GAAG,EAAEnB,CAAC;EACV,IAAIiB,MAAM,CAACM,qBAAqB,EAAE;IAChC,IAAIC,gBAAgB,GAAGP,MAAM,CAACM,qBAAqB,CAACV,MAAM,CAAC;IAC3D,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,gBAAgB,CAACJ,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC5CmB,GAAG,GAAGK,gBAAgB,CAACxB,CAAC,CAAC;MACzB,IAAIc,QAAQ,CAACO,OAAO,CAACF,GAAG,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI,CAACF,MAAM,CAACQ,SAAS,CAACC,oBAAoB,CAACC,IAAI,CAACd,MAAM,EAAEM,GAAG,CAAC,EAAE;MAC9DJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;IAC3B;EACF;EACA,OAAOJ,MAAM;AACf;AAEA,MAAMa,oBAAoB,GAAG;EAC3Bb,MAAMA,CAACc,KAAK,EAAE;IACZ,IAAIA,KAAK,EAAE;MACT,OAAO,MAAM,SAAS,IAAIA,KAAK,GAAGA,KAAK,CAACC,OAAO,GAAGD,KAAK;IACzD;IACA,OAAOE,SAAS;EAClB,CAAC;EACDC,OAAOA,CAACH,KAAK,GAAG,IAAI,EAAE;IACpB,OAAOA,KAAK;EACd,CAAC;EACDI,MAAMA,CAACJ,KAAK,GAAGtC,OAAO,CAAC2C,SAAS,GAAGD,MAAM,GAAGF,SAAS,EAAE;IACrD,OAAOF,KAAK;EACd,CAAC;EACDM,YAAYA,CAAC;IACXC,OAAO,GAAG,IAAI;IACdC,OAAO,GAAG;EACZ,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,OAAO;MACLD,OAAO;MACPC;IACF,CAAC;EACH,CAAC;EACDC,SAASA,CAACT,KAAK,EAAE;IACf,OAAOA,KAAK;EACd;AACF,CAAC;AAED,MAAMU,SAAS,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;AAC9E,SAASC,WAAWA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEC,SAAS,EAAE;EAC3C,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAM,CAACxB,GAAG,EAAEyB,QAAQ,CAAC,IAAI3B,MAAM,CAAC4B,OAAO,CAACH,SAAS,CAAC,EAAE;IACvD,QAAQ,OAAOE,QAAQ;MACrB,KAAK,UAAU;QACb,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;UAC1C,MAAMC,CAAC,GAAGL,QAAQ,CAACjB,IAAI,CAACgB,MAAM,EAAEF,MAAM,CAACtB,GAAG,CAAC,EAAEA,GAAG,EAAEsB,MAAM,CAAC;UACzD,IAAI,CAACS,MAAM,CAACC,KAAK,CAACF,CAAC,CAAC,EAAEN,MAAM,CAACxB,GAAG,CAAC,GAAG8B,CAAC;QACvC,CAAC,MAAM;UACLN,MAAM,CAACxB,GAAG,CAAC,GAAGyB,QAAQ,CAACjB,IAAI,CAACgB,MAAM,EAAEF,MAAM,CAACtB,GAAG,CAAC,EAAEA,GAAG,EAAEsB,MAAM,CAAC;QAC/D;QACA;MACF,KAAK,QAAQ;QACXE,MAAM,CAACxB,GAAG,CAAC,GAAGqB,WAAW,CAACC,MAAM,CAACtB,GAAG,CAAC,EAAEyB,QAAQ,CAAC;QAChD;MACF,KAAK,SAAS;QACZ,IAAIA,QAAQ,EAAED,MAAM,CAACxB,GAAG,CAAC,GAAGsB,MAAM,CAACtB,GAAG,CAAC;QACvC;IACJ;EACF;EACA,OAAOwB,MAAM;AACf;AACA,SAASS,KAAKA,CAACC,SAAS,EAAEC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAClD,MAAMC,IAAI,GAAGH,SAAS;IACpB;MACEtC,MAAM;MACNoB,YAAY;MACZF,MAAM;MACND,OAAO;MACPM;IACF,CAAC,GAAGkB,IAAI;IACRC,IAAI,GAAGnC,wBAAwB,CAACkC,IAAI,EAAEjB,SAAS,CAAC;EAClDgB,OAAO,CAACG,MAAM,GAAGlB,WAAW,CAAC;IAC3BzB,MAAM;IACNoB,YAAY;IACZF,MAAM;IACND,OAAO;IACPM;EACF,CAAC,EAAEV,oBAAoB,CAAC;EACxB,IAAI0B,UAAU,EAAE;IACd,MAAMV,QAAQ,GAAGnD,iBAAiB,CAACkE,GAAG,CAACL,UAAU,CAAC;IAClDC,OAAO,CAACD,UAAU,CAAC,GAAGd,WAAW,CAAC7C,cAAc,CAAC;MAC/C+D,MAAM,EAAEH,OAAO,CAACG;IAClB,CAAC,EAAED,IAAI,CAAC,EAAEb,QAAQ,CAAC;EACrB,CAAC,MAAM;IACL,KAAK,MAAMzB,GAAG,IAAIsC,IAAI,EAAE;MACtB,MAAMb,QAAQ,GAAGnD,iBAAiB,CAACkE,GAAG,CAACxC,GAAG,CAAC;MAC3C,IAAIyB,QAAQ,EAAE;QACZW,OAAO,CAACpC,GAAG,CAAC,GAAGqB,WAAW,CAAC7C,cAAc,CAAC;UACxC+D,MAAM,EAAEH,OAAO,CAACG;QAClB,CAAC,EAAED,IAAI,CAACtC,GAAG,CAAC,CAAC,EAAEyB,QAAQ,CAAC;MAC1B,CAAC,MAAM,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QACjD,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAACY,QAAQ,CAACzC,GAAG,CAAC,EAAE;UACxE,IAAIA,GAAG,KAAK,WAAW,EAAE;YACvB,MAAM0C,KAAK,CAAC,sEAAsE,CAAC;UACrF;UACAC,OAAO,CAACC,IAAI,CAAC,wCAAwC5C,GAAG,qEAAqE,CAAC;QAChI;MACF;IACF;EACF;EACA,OAAOoC,OAAO;AAChB;AAEA,MAAMS,UAAU,CAAC;EACfC,WAAWA,CAACC,IAAI,EAAEZ,UAAU,EAAE;IAC5BzD,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,IAAIsE,GAAG,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACC,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACG,WAAW,GAAGf,UAAU;EAC/B;EACAgB,GAAGA,CAACC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC7C,MAAMC,SAAS,GAAG,IAAI,CAACC,UAAU;IACjC,MAAMC,IAAI,GAAG/E,cAAc,CAACyE,MAAM,EAAEC,MAAM,CAAC;IAC3C,MAAMM,QAAQ,GAAG,IAAI,CAACV,WAAW,GAAG,IAAI,CAACD,KAAK,CAAC3B,MAAM,CAAC,IAAI,CAAC4B,WAAW,CAAC,CAAClC,YAAY,GAAG,CAAC,CAAC;IACzF,MAAMA,YAAY,GAAGxC,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoF,QAAQ,CAAC,EAAEJ,OAAO,CAAC;IAC1EJ,OAAO,CAACS,gBAAgB,CAACF,IAAI,EAAEJ,OAAO,EAAEvC,YAAY,CAAC;IACrD,MAAM8C,MAAM,GAAGA,CAAA,KAAM;MACnBV,OAAO,CAACW,mBAAmB,CAACJ,IAAI,EAAEJ,OAAO,EAAEvC,YAAY,CAAC;MACxDyC,SAAS,CAACO,MAAM,CAACF,MAAM,CAAC;IAC1B,CAAC;IACDL,SAAS,CAACN,GAAG,CAACW,MAAM,CAAC;IACrB,OAAOA,MAAM;EACf;EACAG,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,UAAU,CAACQ,OAAO,CAACJ,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACJ,UAAU,CAACS,KAAK,CAAC,CAAC;EACzB;AACF;AAEA,MAAMC,YAAY,CAAC;EACjBtB,WAAWA,CAAA,EAAG;IACZpE,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI2F,GAAG,CAAC,CAAC,CAAC;EAC/C;EACAlB,GAAGA,CAACnD,GAAG,EAAEsE,QAAQ,EAAEC,EAAE,GAAG,GAAG,EAAE,GAAGC,IAAI,EAAE;IACpC,IAAI,CAACV,MAAM,CAAC9D,GAAG,CAAC;IAChB,IAAI,CAACyE,SAAS,CAACC,GAAG,CAAC1E,GAAG,EAAEc,MAAM,CAAC6D,UAAU,CAACL,QAAQ,EAAEC,EAAE,EAAE,GAAGC,IAAI,CAAC,CAAC;EACnE;EACAV,MAAMA,CAAC9D,GAAG,EAAE;IACV,MAAM4E,OAAO,GAAG,IAAI,CAACH,SAAS,CAACjC,GAAG,CAACxC,GAAG,CAAC;IACvC,IAAI4E,OAAO,EAAE9D,MAAM,CAAC+D,YAAY,CAACD,OAAO,CAAC;EAC3C;EACAX,KAAKA,CAAA,EAAG;IACN,IAAI,CAACQ,SAAS,CAACP,OAAO,CAACU,OAAO,IAAI,KAAK9D,MAAM,CAAC+D,YAAY,CAACD,OAAO,CAAC,CAAC;IACpE,IAAI,CAACH,SAAS,CAACN,KAAK,CAAC,CAAC;EACxB;AACF;AAEA,MAAMW,UAAU,CAAC;EACfhC,WAAWA,CAACiC,QAAQ,EAAE;IACpBrG,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAIsE,GAAG,CAAC,CAAC,CAAC;IAC5CtE,eAAe,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAImE,UAAU,CAAC,IAAI,CAAC,CAAC;IAChEnE,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;IAC/CA,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC,CAAC;IACjDA,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IACrCA,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACnCA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,IAAIsE,GAAG,CAAC,CAAC,CAAC;IAC9CtE,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAIsE,GAAG,CAAC,CAAC,CAAC;IAC5CtE,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;MAC7B6D,MAAM,EAAE;QACNyC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IACFC,eAAe,CAAC,IAAI,EAAEL,QAAQ,CAAC;EACjC;EACAM,WAAWA,CAACC,KAAK,EAAE;IACjB,IAAIxG,OAAO,CAACwG,KAAK,CAAC,EAAE;MAClB,IAAI,CAACtG,QAAQ,GAAG,IAAIgE,GAAG,CAAChE,QAAQ,CAACsG,KAAK,CAAC,CAAC;MACxC,OAAO,IAAI,CAACtG,QAAQ;IACtB,CAAC,MAAM,IAAI,WAAW,IAAIsG,KAAK,EAAE;MAC/B,IAAIA,KAAK,CAAC3B,IAAI,KAAK,WAAW,IAAI2B,KAAK,CAAC3B,IAAI,KAAK,eAAe,EAAE,IAAI,CAAC4B,UAAU,CAACvB,MAAM,CAACsB,KAAK,CAACE,SAAS,CAAC,CAAC,KAAK,IAAIF,KAAK,CAAC3B,IAAI,KAAK,aAAa,EAAE,IAAI,CAAC4B,UAAU,CAACpC,GAAG,CAACmC,KAAK,CAACE,SAAS,CAAC;MACrL,OAAO,IAAI,CAACD,UAAU;IACxB;EACF;EACAE,aAAaA,CAACV,QAAQ,EAAEW,cAAc,EAAE;IACtC,IAAI,CAACX,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACW,cAAc,GAAGA,cAAc;EACtC;EACAC,WAAWA,CAACrE,MAAM,EAAEa,UAAU,EAAE;IAC9B,IAAI,CAACb,MAAM,GAAGW,KAAK,CAACX,MAAM,EAAEa,UAAU,EAAE,IAAI,CAACb,MAAM,CAAC;EACtD;EACA2C,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC2B,iBAAiB,CAAC3B,KAAK,CAAC,CAAC;IAC9B,KAAK,MAAMjE,GAAG,IAAI,IAAI,CAAC6F,QAAQ,EAAE;MAC/B,IAAI,CAACC,kBAAkB,CAAC9F,GAAG,CAAC,CAACiE,KAAK,CAAC,CAAC;MACpC,IAAI,CAAC8B,oBAAoB,CAAC/F,GAAG,CAAC,CAACiE,KAAK,CAAC,CAAC;IACxC;EACF;EACA+B,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC1E,MAAM,CAACiB,MAAM,CAAC3C,MAAM,EAAE,IAAI,CAACqG,IAAI,CAAC,CAAC;IAC1C,OAAO,MAAM,IAAI,CAACL,iBAAiB,CAAC3B,KAAK,CAAC,CAAC;EAC7C;EACAgC,IAAIA,CAAC,GAAGzB,IAAI,EAAE;IACZ,MAAM0B,YAAY,GAAG,IAAI,CAAC5E,MAAM,CAACiB,MAAM;IACvC,MAAM4D,KAAK,GAAG,CAAC,CAAC;IAChB,IAAIvG,MAAM;IACV,IAAIsG,YAAY,CAACtG,MAAM,EAAE;MACvBA,MAAM,GAAGsG,YAAY,CAACtG,MAAM,CAAC,CAAC;MAC9B,IAAI,CAACA,MAAM,EAAE;IACf;IACA,IAAIsG,YAAY,CAACrF,OAAO,EAAE;MACxB,KAAK,MAAMsB,UAAU,IAAI,IAAI,CAAC0D,QAAQ,EAAE;QACtC,MAAMO,aAAa,GAAG,IAAI,CAAC9E,MAAM,CAACa,UAAU,CAAC;QAC7C,MAAMkE,YAAY,GAAGC,WAAW,CAACH,KAAK,EAAEC,aAAa,CAACpF,YAAY,EAAE,CAAC,CAACpB,MAAM,CAAC;QAC7E,IAAIwG,aAAa,CAACvF,OAAO,EAAE;UACzB,MAAM0F,MAAM,GAAGrH,SAAS,CAACsD,GAAG,CAACL,UAAU,CAAC;UACxC,IAAIoE,MAAM,CAAC,IAAI,EAAE/B,IAAI,EAAErC,UAAU,CAAC,CAAC8D,IAAI,CAACI,YAAY,CAAC;QACvD;MACF;MACA,MAAMG,kBAAkB,GAAGF,WAAW,CAACH,KAAK,EAAED,YAAY,CAAClF,YAAY,EAAE,CAAC,CAACpB,MAAM,CAAC;MAClF,KAAK,MAAM6G,QAAQ,IAAI,IAAI,CAACf,cAAc,EAAE;QAC1Cc,kBAAkB,CAACC,QAAQ,EAAE,EAAE,EAAEnB,KAAK,IAAI,IAAI,CAACI,cAAc,CAACe,QAAQ,CAAC,CAACjI,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAACkI,KAAK,CAACnE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;UAChI+C,KAAK;UACLd;QACF,CAAC,CAAC,CAAC,EAAE5D,SAAS,EAAE,IAAI,CAAC;MACvB;IACF;IACA,KAAK,MAAM+F,WAAW,IAAIR,KAAK,EAAE;MAC/BA,KAAK,CAACQ,WAAW,CAAC,GAAGvH,KAAK,CAAC,GAAG+G,KAAK,CAACQ,WAAW,CAAC,CAAC;IACnD;IACA,IAAI,CAAC/G,MAAM,EAAE,OAAOuG,KAAK;IACzB,KAAK,MAAMQ,WAAW,IAAIR,KAAK,EAAE;MAC/B,MAAM;QACJ9C,MAAM;QACNnC,OAAO;QACPD;MACF,CAAC,GAAG3B,SAAS,CAACqH,WAAW,CAAC;MAC1B,IAAI,CAACf,iBAAiB,CAACzC,GAAG,CAACvD,MAAM,EAAEyD,MAAM,EAAE,EAAE,EAAE8C,KAAK,CAACQ,WAAW,CAAC,EAAE;QACjEzF,OAAO;QACPD;MACF,CAAC,CAAC;IACJ;EACF;AACF;AACA,SAAS2F,YAAYA,CAAC7D,IAAI,EAAEZ,UAAU,EAAE;EACtCY,IAAI,CAAC8C,QAAQ,CAAC1C,GAAG,CAAChB,UAAU,CAAC;EAC7BY,IAAI,CAAC+C,kBAAkB,CAAC3D,UAAU,CAAC,GAAG,IAAIU,UAAU,CAACE,IAAI,EAAEZ,UAAU,CAAC;EACtEY,IAAI,CAACgD,oBAAoB,CAAC5D,UAAU,CAAC,GAAG,IAAIiC,YAAY,CAAC,CAAC;AAC5D;AACA,SAASgB,eAAeA,CAACrC,IAAI,EAAE8D,gBAAgB,EAAE;EAC/C,IAAIA,gBAAgB,CAACC,IAAI,EAAEF,YAAY,CAAC7D,IAAI,EAAE,MAAM,CAAC;EACrD,IAAI8D,gBAAgB,CAACE,KAAK,EAAEH,YAAY,CAAC7D,IAAI,EAAE,OAAO,CAAC;EACvD,IAAI8D,gBAAgB,CAACG,MAAM,EAAEJ,YAAY,CAAC7D,IAAI,EAAE,QAAQ,CAAC;EACzD,IAAI8D,gBAAgB,CAACI,IAAI,EAAEL,YAAY,CAAC7D,IAAI,EAAE,MAAM,CAAC;EACrD,IAAI8D,gBAAgB,CAACK,KAAK,EAAEN,YAAY,CAAC7D,IAAI,EAAE,OAAO,CAAC;EACvD,IAAI8D,gBAAgB,CAACM,KAAK,EAAEP,YAAY,CAAC7D,IAAI,EAAE,OAAO,CAAC;AACzD;AACA,MAAMuD,WAAW,GAAGA,CAACH,KAAK,EAAEnF,YAAY,EAAEoG,iBAAiB,KAAK,CAAC/D,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE6D,QAAQ,GAAG,KAAK,KAAK;EAC3H,IAAIC,gBAAgB,EAAEC,gBAAgB;EACtC,MAAMrG,OAAO,GAAG,CAACoG,gBAAgB,GAAG9D,OAAO,CAACtC,OAAO,MAAM,IAAI,IAAIoG,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGtG,YAAY,CAACE,OAAO;EACtI,MAAMD,OAAO,GAAG,CAACsG,gBAAgB,GAAG/D,OAAO,CAACvC,OAAO,MAAM,IAAI,IAAIsG,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGvG,YAAY,CAACC,OAAO;EACtI,IAAI0F,WAAW,GAAGU,QAAQ,GAAGhE,MAAM,GAAG7D,aAAa,CAAC6D,MAAM,EAAEC,MAAM,EAAEpC,OAAO,CAAC;EAC5E,IAAIkG,iBAAiB,IAAInG,OAAO,EAAE0F,WAAW,IAAI,SAAS;EAC1DR,KAAK,CAACQ,WAAW,CAAC,GAAGR,KAAK,CAACQ,WAAW,CAAC,IAAI,EAAE;EAC7CR,KAAK,CAACQ,WAAW,CAAC,CAACa,IAAI,CAACjE,OAAO,CAAC;AAClC,CAAC;AAED,MAAMkE,aAAa,GAAG,yCAAyC;AAC/D,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC/B,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAM7C,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAM8C,OAAO,GAAG,IAAI7E,GAAG,CAAC,CAAC;EACzB,KAAK,IAAIhD,GAAG,IAAI2H,SAAS,EAAE;IACzB,IAAIF,aAAa,CAACK,IAAI,CAAC9H,GAAG,CAAC,EAAE;MAC3B6H,OAAO,CAAC1E,GAAG,CAAC4E,MAAM,CAACC,SAAS,CAAC;MAC7BjD,QAAQ,CAAC/E,GAAG,CAAC,GAAG2H,SAAS,CAAC3H,GAAG,CAAC;IAChC,CAAC,MAAM;MACL4H,MAAM,CAAC5H,GAAG,CAAC,GAAG2H,SAAS,CAAC3H,GAAG,CAAC;IAC9B;EACF;EACA,OAAO,CAAC+E,QAAQ,EAAE6C,MAAM,EAAEC,OAAO,CAAC;AACpC;AACA,SAASI,eAAeA,CAACJ,OAAO,EAAE9C,QAAQ,EAAEmD,UAAU,EAAElI,GAAG,EAAE6G,gBAAgB,EAAEvF,MAAM,EAAE;EACrF,IAAI,CAACuG,OAAO,CAACM,GAAG,CAACD,UAAU,CAAC,EAAE;EAC9B,IAAI,CAAChJ,SAAS,CAACiJ,GAAG,CAACnI,GAAG,CAAC,EAAE;IACvB,IAAI2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1Cc,OAAO,CAACC,IAAI,CAAC,wEAAwE5C,GAAG,6DAA6DA,GAAG,sCAAsC,CAAC;IACjM;IACA;EACF;EACA,MAAMoI,QAAQ,GAAGF,UAAU,GAAG,OAAO;EACrC,MAAMG,MAAM,GAAGH,UAAU,GAAG,KAAK;EACjC,MAAMI,EAAE,GAAG5B,KAAK,IAAI;IAClB,IAAI6B,IAAI,GAAG3H,SAAS;IACpB,IAAI8F,KAAK,CAAC8B,KAAK,IAAIJ,QAAQ,IAAIrD,QAAQ,EAAEA,QAAQ,CAACqD,QAAQ,CAAC,CAAC1B,KAAK,CAAC;IAClE,IAAIwB,UAAU,IAAInD,QAAQ,EAAEwD,IAAI,GAAGxD,QAAQ,CAACmD,UAAU,CAAC,CAACxB,KAAK,CAAC;IAC9D,IAAIA,KAAK,CAAC+B,IAAI,IAAIJ,MAAM,IAAItD,QAAQ,EAAEA,QAAQ,CAACsD,MAAM,CAAC,CAAC3B,KAAK,CAAC;IAC7D,OAAO6B,IAAI;EACb,CAAC;EACD1B,gBAAgB,CAAC7G,GAAG,CAAC,GAAGsI,EAAE;EAC1BhH,MAAM,CAACtB,GAAG,CAAC,GAAGsB,MAAM,CAACtB,GAAG,CAAC,IAAI,CAAC,CAAC;AACjC;AACA,SAAS0I,mBAAmBA,CAACC,cAAc,EAAEC,YAAY,EAAE;EACzD,MAAM,CAAC7D,QAAQ,EAAEW,cAAc,EAAEmC,OAAO,CAAC,GAAGH,YAAY,CAACiB,cAAc,CAAC;EACxE,MAAM9B,gBAAgB,GAAG,CAAC,CAAC;EAC3BoB,eAAe,CAACJ,OAAO,EAAE9C,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE8B,gBAAgB,EAAE+B,YAAY,CAAC;EACpFX,eAAe,CAACJ,OAAO,EAAE9C,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE8B,gBAAgB,EAAE+B,YAAY,CAAC;EACtFX,eAAe,CAACJ,OAAO,EAAE9C,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE8B,gBAAgB,EAAE+B,YAAY,CAAC;EACxFX,eAAe,CAACJ,OAAO,EAAE9C,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE8B,gBAAgB,EAAE+B,YAAY,CAAC;EACtFX,eAAe,CAACJ,OAAO,EAAE9C,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE8B,gBAAgB,EAAE+B,YAAY,CAAC;EACpFX,eAAe,CAACJ,OAAO,EAAE9C,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE8B,gBAAgB,EAAE+B,YAAY,CAAC;EACtF,OAAO;IACL7D,QAAQ,EAAE8B,gBAAgB;IAC1BvF,MAAM,EAAEsH,YAAY;IACpBlD;EACF,CAAC;AACH;AAEA,SAASZ,UAAU,EAAE4D,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}