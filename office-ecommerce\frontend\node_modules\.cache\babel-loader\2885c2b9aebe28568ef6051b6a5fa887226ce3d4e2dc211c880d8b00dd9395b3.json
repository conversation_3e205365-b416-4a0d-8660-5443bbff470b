{"ast": null, "code": "import { Color, UniformsUtils, ShaderMaterial, AdditiveBlending, WebGLRenderTarget, HalfFloatType } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nclass SSAARenderPass extends Pass {\n  constructor(scene, camera, clearColor, clearAlpha) {\n    super();\n    this.scene = scene;\n    this.camera = camera;\n    this.sampleLevel = 4;\n    this.unbiased = true;\n    this.clearColor = clearColor !== void 0 ? clearColor : 0;\n    this.clearAlpha = clearAlpha !== void 0 ? clearAlpha : 0;\n    this._oldClearColor = new Color();\n    const copyShader = CopyShader;\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms);\n    this.copyMaterial = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      transparent: true,\n      depthTest: false,\n      depthWrite: false,\n      premultipliedAlpha: true,\n      blending: AdditiveBlending\n    });\n    this.fsQuad = new FullScreenQuad(this.copyMaterial);\n  }\n  dispose() {\n    if (this.sampleRenderTarget) {\n      this.sampleRenderTarget.dispose();\n      this.sampleRenderTarget = null;\n    }\n    this.copyMaterial.dispose();\n    this.fsQuad.dispose();\n  }\n  setSize(width, height) {\n    if (this.sampleRenderTarget) this.sampleRenderTarget.setSize(width, height);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    if (!this.sampleRenderTarget) {\n      this.sampleRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, {\n        type: HalfFloatType\n      });\n      this.sampleRenderTarget.texture.name = \"SSAARenderPass.sample\";\n    }\n    const jitterOffsets = _JitterVectors[Math.max(0, Math.min(this.sampleLevel, 5))];\n    const autoClear = renderer.autoClear;\n    renderer.autoClear = false;\n    renderer.getClearColor(this._oldClearColor);\n    const oldClearAlpha = renderer.getClearAlpha();\n    const baseSampleWeight = 1 / jitterOffsets.length;\n    const roundingRange = 1 / 32;\n    this.copyUniforms[\"tDiffuse\"].value = this.sampleRenderTarget.texture;\n    const viewOffset = {\n      fullWidth: readBuffer.width,\n      fullHeight: readBuffer.height,\n      offsetX: 0,\n      offsetY: 0,\n      width: readBuffer.width,\n      height: readBuffer.height\n    };\n    const originalViewOffset = Object.assign({}, this.camera.view);\n    if (originalViewOffset.enabled) Object.assign(viewOffset, originalViewOffset);\n    for (let i = 0; i < jitterOffsets.length; i++) {\n      const jitterOffset = jitterOffsets[i];\n      if (this.camera.setViewOffset) {\n        this.camera.setViewOffset(viewOffset.fullWidth, viewOffset.fullHeight, viewOffset.offsetX + jitterOffset[0] * 0.0625, viewOffset.offsetY + jitterOffset[1] * 0.0625,\n        // 0.0625 = 1 / 16\n        viewOffset.width, viewOffset.height);\n      }\n      let sampleWeight = baseSampleWeight;\n      if (this.unbiased) {\n        const uniformCenteredDistribution = -0.5 + (i + 0.5) / jitterOffsets.length;\n        sampleWeight += roundingRange * uniformCenteredDistribution;\n      }\n      this.copyUniforms[\"opacity\"].value = sampleWeight;\n      renderer.setClearColor(this.clearColor, this.clearAlpha);\n      renderer.setRenderTarget(this.sampleRenderTarget);\n      renderer.clear();\n      renderer.render(this.scene, this.camera);\n      renderer.setRenderTarget(this.renderToScreen ? null : writeBuffer);\n      if (i === 0) {\n        renderer.setClearColor(0, 0);\n        renderer.clear();\n      }\n      this.fsQuad.render(renderer);\n    }\n    if (this.camera.setViewOffset && originalViewOffset.enabled) {\n      this.camera.setViewOffset(originalViewOffset.fullWidth, originalViewOffset.fullHeight, originalViewOffset.offsetX, originalViewOffset.offsetY, originalViewOffset.width, originalViewOffset.height);\n    } else if (this.camera.clearViewOffset) {\n      this.camera.clearViewOffset();\n    }\n    renderer.autoClear = autoClear;\n    renderer.setClearColor(this._oldClearColor, oldClearAlpha);\n  }\n}\nconst _JitterVectors = [[[0, 0]], [[4, 4], [-4, -4]], [[-2, -6], [6, -2], [-6, 2], [2, 6]], [[1, -3], [-1, 3], [5, 1], [-3, -5], [-5, 5], [-7, -1], [3, 7], [7, -7]], [[1, 1], [-1, -3], [-3, 2], [4, -1], [-5, -2], [2, 5], [5, 3], [3, -5], [-2, 6], [0, -7], [-4, -6], [-6, 4], [-8, 0], [7, -4], [6, 7], [-7, -8]], [[-4, -7], [-7, -5], [-3, -5], [-5, -4], [-1, -4], [-2, -2], [-6, -1], [-4, 0], [-7, 1], [-1, 2], [-6, 3], [-3, 3], [-7, 6], [-3, 6], [-5, 7], [-1, 7], [5, -7], [1, -6], [6, -5], [4, -4], [2, -3], [7, -2], [1, -1], [4, -1], [2, 1], [6, 2], [0, 4], [4, 4], [2, 5], [7, 5], [5, 6], [3, 7]]];\nexport { SSAARenderPass };", "map": {"version": 3, "names": ["SSAARenderPass", "Pass", "constructor", "scene", "camera", "clearColor", "clearAlpha", "sampleLevel", "unbiased", "_oldClearColor", "Color", "copyShader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyUniforms", "UniformsUtils", "clone", "uniforms", "copyMaterial", "ShaderMaterial", "vertexShader", "fragmentShader", "transparent", "depthTest", "depthWrite", "premultipliedAlpha", "blending", "AdditiveBlending", "fsQuad", "FullScreenQuad", "dispose", "sampleRender<PERSON>arget", "setSize", "width", "height", "render", "renderer", "writeBuffer", "readBuffer", "WebGLRenderTarget", "type", "HalfFloatType", "texture", "name", "jitterOffsets", "_JitterVectors", "Math", "max", "min", "autoClear", "getClearColor", "oldClearAlpha", "getClearAlpha", "baseSampleWeight", "length", "roundingRange", "value", "viewOffset", "fullWidth", "fullHeight", "offsetX", "offsetY", "originalViewOffset", "Object", "assign", "view", "enabled", "i", "jitterOffset", "setViewOffset", "sampleWeight", "uniformCenteredDistribution", "setClearColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "renderToScreen", "clearViewOffset"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\SSAARenderPass.js"], "sourcesContent": ["import { AdditiveBlending, Color, HalfFloatType, ShaderMaterial, UniformsUtils, WebGLRenderTarget } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\n\n/**\n *\n * Supersample Anti-Aliasing Render Pass\n *\n * This manual approach to SSAA re-renders the scene ones for each sample with camera jitter and accumulates the results.\n *\n * References: https://en.wikipedia.org/wiki/Supersampling\n *\n */\n\nclass SSAARenderPass extends Pass {\n  constructor(scene, camera, clearColor, clearAlpha) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.sampleLevel = 4 // specified as n, where the number of samples is 2^n, so sampleLevel = 4, is 2^4 samples, 16.\n    this.unbiased = true\n\n    // as we need to clear the buffer in this pass, clearColor must be set to something, defaults to black.\n    this.clearColor = clearColor !== undefined ? clearColor : 0x000000\n    this.clearAlpha = clearAlpha !== undefined ? clearAlpha : 0\n    this._oldClearColor = new Color()\n\n    const copyShader = CopyShader\n    this.copyUniforms = UniformsUtils.clone(copyShader.uniforms)\n\n    this.copyMaterial = new ShaderMaterial({\n      uniforms: this.copyUniforms,\n      vertexShader: copyShader.vertexShader,\n      fragmentShader: copyShader.fragmentShader,\n      transparent: true,\n      depthTest: false,\n      depthWrite: false,\n      premultipliedAlpha: true,\n      blending: AdditiveBlending,\n    })\n\n    this.fsQuad = new FullScreenQuad(this.copyMaterial)\n  }\n\n  dispose() {\n    if (this.sampleRenderTarget) {\n      this.sampleRenderTarget.dispose()\n      this.sampleRenderTarget = null\n    }\n\n    this.copyMaterial.dispose()\n\n    this.fsQuad.dispose()\n  }\n\n  setSize(width, height) {\n    if (this.sampleRenderTarget) this.sampleRenderTarget.setSize(width, height)\n  }\n\n  render(renderer, writeBuffer, readBuffer) {\n    if (!this.sampleRenderTarget) {\n      this.sampleRenderTarget = new WebGLRenderTarget(readBuffer.width, readBuffer.height, { type: HalfFloatType })\n      this.sampleRenderTarget.texture.name = 'SSAARenderPass.sample'\n    }\n\n    const jitterOffsets = _JitterVectors[Math.max(0, Math.min(this.sampleLevel, 5))]\n\n    const autoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    renderer.getClearColor(this._oldClearColor)\n    const oldClearAlpha = renderer.getClearAlpha()\n\n    const baseSampleWeight = 1.0 / jitterOffsets.length\n    const roundingRange = 1 / 32\n    this.copyUniforms['tDiffuse'].value = this.sampleRenderTarget.texture\n\n    const viewOffset = {\n      fullWidth: readBuffer.width,\n      fullHeight: readBuffer.height,\n      offsetX: 0,\n      offsetY: 0,\n      width: readBuffer.width,\n      height: readBuffer.height,\n    }\n\n    const originalViewOffset = Object.assign({}, this.camera.view)\n\n    if (originalViewOffset.enabled) Object.assign(viewOffset, originalViewOffset)\n\n    // render the scene multiple times, each slightly jitter offset from the last and accumulate the results.\n    for (let i = 0; i < jitterOffsets.length; i++) {\n      const jitterOffset = jitterOffsets[i]\n\n      if (this.camera.setViewOffset) {\n        this.camera.setViewOffset(\n          viewOffset.fullWidth,\n          viewOffset.fullHeight,\n\n          viewOffset.offsetX + jitterOffset[0] * 0.0625,\n          viewOffset.offsetY + jitterOffset[1] * 0.0625, // 0.0625 = 1 / 16\n\n          viewOffset.width,\n          viewOffset.height,\n        )\n      }\n\n      let sampleWeight = baseSampleWeight\n\n      if (this.unbiased) {\n        // the theory is that equal weights for each sample lead to an accumulation of rounding errors.\n        // The following equation varies the sampleWeight per sample so that it is uniformly distributed\n        // across a range of values whose rounding errors cancel each other out.\n\n        const uniformCenteredDistribution = -0.5 + (i + 0.5) / jitterOffsets.length\n        sampleWeight += roundingRange * uniformCenteredDistribution\n      }\n\n      this.copyUniforms['opacity'].value = sampleWeight\n      renderer.setClearColor(this.clearColor, this.clearAlpha)\n      renderer.setRenderTarget(this.sampleRenderTarget)\n      renderer.clear()\n      renderer.render(this.scene, this.camera)\n\n      renderer.setRenderTarget(this.renderToScreen ? null : writeBuffer)\n\n      if (i === 0) {\n        renderer.setClearColor(0x000000, 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.render(renderer)\n    }\n\n    if (this.camera.setViewOffset && originalViewOffset.enabled) {\n      this.camera.setViewOffset(\n        originalViewOffset.fullWidth,\n        originalViewOffset.fullHeight,\n\n        originalViewOffset.offsetX,\n        originalViewOffset.offsetY,\n\n        originalViewOffset.width,\n        originalViewOffset.height,\n      )\n    } else if (this.camera.clearViewOffset) {\n      this.camera.clearViewOffset()\n    }\n\n    renderer.autoClear = autoClear\n    renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n  }\n}\n\n// These jitter vectors are specified in integers because it is easier.\n// I am assuming a [-8,8) integer grid, but it needs to be mapped onto [-0.5,0.5)\n// before being used, thus these integers need to be scaled by 1/16.\n//\n// Sample patterns reference: https://msdn.microsoft.com/en-us/library/windows/desktop/ff476218%28v=vs.85%29.aspx?f=255&MSPPError=-2147217396\n// prettier-ignore\nconst _JitterVectors = [\n\t[\n\t\t[ 0, 0 ]\n\t],\n\t[\n\t\t[ 4, 4 ], [ - 4, - 4 ]\n\t],\n\t[\n\t\t[ - 2, - 6 ], [ 6, - 2 ], [ - 6, 2 ], [ 2, 6 ]\n\t],\n\t[\n\t\t[ 1, - 3 ], [ - 1, 3 ], [ 5, 1 ], [ - 3, - 5 ],\n\t\t[ - 5, 5 ], [ - 7, - 1 ], [ 3, 7 ], [ 7, - 7 ]\n\t],\n\t[\n\t\t[ 1, 1 ], [ - 1, - 3 ], [ - 3, 2 ], [ 4, - 1 ],\n\t\t[ - 5, - 2 ], [ 2, 5 ], [ 5, 3 ], [ 3, - 5 ],\n\t\t[ - 2, 6 ], [ 0, - 7 ], [ - 4, - 6 ], [ - 6, 4 ],\n\t\t[ - 8, 0 ], [ 7, - 4 ], [ 6, 7 ], [ - 7, - 8 ]\n\t],\n\t[\n\t\t[ - 4, - 7 ], [ - 7, - 5 ], [ - 3, - 5 ], [ - 5, - 4 ],\n\t\t[ - 1, - 4 ], [ - 2, - 2 ], [ - 6, - 1 ], [ - 4, 0 ],\n\t\t[ - 7, 1 ], [ - 1, 2 ], [ - 6, 3 ], [ - 3, 3 ],\n\t\t[ - 7, 6 ], [ - 3, 6 ], [ - 5, 7 ], [ - 1, 7 ],\n\t\t[ 5, - 7 ], [ 1, - 6 ], [ 6, - 5 ], [ 4, - 4 ],\n\t\t[ 2, - 3 ], [ 7, - 2 ], [ 1, - 1 ], [ 4, - 1 ],\n\t\t[ 2, 1 ], [ 6, 2 ], [ 0, 4 ], [ 4, 4 ],\n\t\t[ 2, 5 ], [ 7, 5 ], [ 5, 6 ], [ 3, 7 ]\n\t]\n];\n\nexport { SSAARenderPass }\n"], "mappings": ";;;AAcA,MAAMA,cAAA,SAAuBC,IAAA,CAAK;EAChCC,YAAYC,KAAA,EAAOC,MAAA,EAAQC,UAAA,EAAYC,UAAA,EAAY;IACjD,MAAO;IAEP,KAAKH,KAAA,GAAQA,KAAA;IACb,KAAKC,MAAA,GAASA,MAAA;IAEd,KAAKG,WAAA,GAAc;IACnB,KAAKC,QAAA,GAAW;IAGhB,KAAKH,UAAA,GAAaA,UAAA,KAAe,SAAYA,UAAA,GAAa;IAC1D,KAAKC,UAAA,GAAaA,UAAA,KAAe,SAAYA,UAAA,GAAa;IAC1D,KAAKG,cAAA,GAAiB,IAAIC,KAAA,CAAO;IAEjC,MAAMC,UAAA,GAAaC,UAAA;IACnB,KAAKC,YAAA,GAAeC,aAAA,CAAcC,KAAA,CAAMJ,UAAA,CAAWK,QAAQ;IAE3D,KAAKC,YAAA,GAAe,IAAIC,cAAA,CAAe;MACrCF,QAAA,EAAU,KAAKH,YAAA;MACfM,YAAA,EAAcR,UAAA,CAAWQ,YAAA;MACzBC,cAAA,EAAgBT,UAAA,CAAWS,cAAA;MAC3BC,WAAA,EAAa;MACbC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,kBAAA,EAAoB;MACpBC,QAAA,EAAUC;IAChB,CAAK;IAED,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKX,YAAY;EACnD;EAEDY,QAAA,EAAU;IACR,IAAI,KAAKC,kBAAA,EAAoB;MAC3B,KAAKA,kBAAA,CAAmBD,OAAA,CAAS;MACjC,KAAKC,kBAAA,GAAqB;IAC3B;IAED,KAAKb,YAAA,CAAaY,OAAA,CAAS;IAE3B,KAAKF,MAAA,CAAOE,OAAA,CAAS;EACtB;EAEDE,QAAQC,KAAA,EAAOC,MAAA,EAAQ;IACrB,IAAI,KAAKH,kBAAA,EAAoB,KAAKA,kBAAA,CAAmBC,OAAA,CAAQC,KAAA,EAAOC,MAAM;EAC3E;EAEDC,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAY;IACxC,IAAI,CAAC,KAAKP,kBAAA,EAAoB;MAC5B,KAAKA,kBAAA,GAAqB,IAAIQ,iBAAA,CAAkBD,UAAA,CAAWL,KAAA,EAAOK,UAAA,CAAWJ,MAAA,EAAQ;QAAEM,IAAA,EAAMC;MAAA,CAAe;MAC5G,KAAKV,kBAAA,CAAmBW,OAAA,CAAQC,IAAA,GAAO;IACxC;IAED,MAAMC,aAAA,GAAgBC,cAAA,CAAeC,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKE,GAAA,CAAI,KAAKxC,WAAA,EAAa,CAAC,CAAC,CAAC;IAE/E,MAAMyC,SAAA,GAAYb,QAAA,CAASa,SAAA;IAC3Bb,QAAA,CAASa,SAAA,GAAY;IAErBb,QAAA,CAASc,aAAA,CAAc,KAAKxC,cAAc;IAC1C,MAAMyC,aAAA,GAAgBf,QAAA,CAASgB,aAAA,CAAe;IAE9C,MAAMC,gBAAA,GAAmB,IAAMT,aAAA,CAAcU,MAAA;IAC7C,MAAMC,aAAA,GAAgB,IAAI;IAC1B,KAAKzC,YAAA,CAAa,UAAU,EAAE0C,KAAA,GAAQ,KAAKzB,kBAAA,CAAmBW,OAAA;IAE9D,MAAMe,UAAA,GAAa;MACjBC,SAAA,EAAWpB,UAAA,CAAWL,KAAA;MACtB0B,UAAA,EAAYrB,UAAA,CAAWJ,MAAA;MACvB0B,OAAA,EAAS;MACTC,OAAA,EAAS;MACT5B,KAAA,EAAOK,UAAA,CAAWL,KAAA;MAClBC,MAAA,EAAQI,UAAA,CAAWJ;IACpB;IAED,MAAM4B,kBAAA,GAAqBC,MAAA,CAAOC,MAAA,CAAO,CAAE,GAAE,KAAK3D,MAAA,CAAO4D,IAAI;IAE7D,IAAIH,kBAAA,CAAmBI,OAAA,EAASH,MAAA,CAAOC,MAAA,CAAOP,UAAA,EAAYK,kBAAkB;IAG5E,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAIvB,aAAA,CAAcU,MAAA,EAAQa,CAAA,IAAK;MAC7C,MAAMC,YAAA,GAAexB,aAAA,CAAcuB,CAAC;MAEpC,IAAI,KAAK9D,MAAA,CAAOgE,aAAA,EAAe;QAC7B,KAAKhE,MAAA,CAAOgE,aAAA,CACVZ,UAAA,CAAWC,SAAA,EACXD,UAAA,CAAWE,UAAA,EAEXF,UAAA,CAAWG,OAAA,GAAUQ,YAAA,CAAa,CAAC,IAAI,QACvCX,UAAA,CAAWI,OAAA,GAAUO,YAAA,CAAa,CAAC,IAAI;QAAA;QAEvCX,UAAA,CAAWxB,KAAA,EACXwB,UAAA,CAAWvB,MACZ;MACF;MAED,IAAIoC,YAAA,GAAejB,gBAAA;MAEnB,IAAI,KAAK5C,QAAA,EAAU;QAKjB,MAAM8D,2BAAA,GAA8B,QAAQJ,CAAA,GAAI,OAAOvB,aAAA,CAAcU,MAAA;QACrEgB,YAAA,IAAgBf,aAAA,GAAgBgB,2BAAA;MACjC;MAED,KAAKzD,YAAA,CAAa,SAAS,EAAE0C,KAAA,GAAQc,YAAA;MACrClC,QAAA,CAASoC,aAAA,CAAc,KAAKlE,UAAA,EAAY,KAAKC,UAAU;MACvD6B,QAAA,CAASqC,eAAA,CAAgB,KAAK1C,kBAAkB;MAChDK,QAAA,CAASsC,KAAA,CAAO;MAChBtC,QAAA,CAASD,MAAA,CAAO,KAAK/B,KAAA,EAAO,KAAKC,MAAM;MAEvC+B,QAAA,CAASqC,eAAA,CAAgB,KAAKE,cAAA,GAAiB,OAAOtC,WAAW;MAEjE,IAAI8B,CAAA,KAAM,GAAG;QACX/B,QAAA,CAASoC,aAAA,CAAc,GAAU,CAAG;QACpCpC,QAAA,CAASsC,KAAA,CAAO;MACjB;MAED,KAAK9C,MAAA,CAAOO,MAAA,CAAOC,QAAQ;IAC5B;IAED,IAAI,KAAK/B,MAAA,CAAOgE,aAAA,IAAiBP,kBAAA,CAAmBI,OAAA,EAAS;MAC3D,KAAK7D,MAAA,CAAOgE,aAAA,CACVP,kBAAA,CAAmBJ,SAAA,EACnBI,kBAAA,CAAmBH,UAAA,EAEnBG,kBAAA,CAAmBF,OAAA,EACnBE,kBAAA,CAAmBD,OAAA,EAEnBC,kBAAA,CAAmB7B,KAAA,EACnB6B,kBAAA,CAAmB5B,MACpB;IACP,WAAe,KAAK7B,MAAA,CAAOuE,eAAA,EAAiB;MACtC,KAAKvE,MAAA,CAAOuE,eAAA,CAAiB;IAC9B;IAEDxC,QAAA,CAASa,SAAA,GAAYA,SAAA;IACrBb,QAAA,CAASoC,aAAA,CAAc,KAAK9D,cAAA,EAAgByC,aAAa;EAC1D;AACH;AAQA,MAAMN,cAAA,GAAiB,CACtB,CACC,CAAE,GAAG,CAAG,EACR,EACD,CACC,CAAE,GAAG,CAAG,GAAE,CAAE,IAAK,EAAK,EACtB,EACD,CACC,CAAE,IAAK,EAAG,GAAI,CAAE,GAAG,KAAO,CAAE,IAAK,CAAG,GAAE,CAAE,GAAG,CAAG,EAC9C,EACD,CACC,CAAE,GAAG,EAAK,GAAE,CAAE,IAAK,CAAC,GAAI,CAAE,GAAG,CAAG,GAAE,CAAE,IAAK,EAAK,GAC9C,CAAE,IAAK,CAAG,GAAE,CAAE,IAAK,KAAO,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,EAAK,EAC9C,EACD,CACC,CAAE,GAAG,CAAC,GAAI,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,CAAC,GAAI,CAAE,GAAG,EAAK,GAC9C,CAAE,IAAK,EAAK,GAAE,CAAE,GAAG,IAAK,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,EAAK,GAC5C,CAAE,IAAK,CAAG,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,CAAG,GAChD,CAAE,IAAK,CAAG,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,CAAG,GAAE,CAAE,IAAK,EAAK,EAC9C,EACD,CACC,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,EAAG,GAAI,CAAE,IAAK,EAAK,GACtD,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,EAAK,GAAE,CAAE,IAAK,CAAG,GACpD,CAAE,IAAK,CAAG,GAAE,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAG,GAC9C,CAAE,IAAK,CAAG,GAAE,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAC,GAAI,CAAE,IAAK,CAAG,GAC9C,CAAE,GAAG,EAAK,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAK,GAC9C,CAAE,GAAG,EAAK,GAAE,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAG,GAAI,CAAE,GAAG,EAAK,GAC9C,CAAE,GAAG,IAAK,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAG,GACtC,CAAE,GAAG,IAAK,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAC,GAAI,CAAE,GAAG,CAAG,EACtC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}