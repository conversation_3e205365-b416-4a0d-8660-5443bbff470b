{"ast": null, "code": "import { useMemo, useCallback } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Texture } from 'three';\n\n/**\n *  Adapted from https://github.com/brunoimbrizi/interactive-particles/blob/master/src/scripts/webgl/particles/TrailTexture.js\n *  Changes:\n *    * accepts config as constructor params\n *    * frame-rate independent aging\n *    * added option to interpolate between slow mouse events\n *    * added option for smoothing between values to avoid large jumps in force\n */\n\n// smooth new sample (measurement) based on previous sample (current)\nfunction smoothAverage(current, measurement, smoothing = 0.9) {\n  return measurement * smoothing + current * (1.0 - smoothing);\n} // default ease\n\nconst easeCircleOut = x => Math.sqrt(1 - Math.pow(x - 1, 2));\nclass TrailTexture {\n  constructor({\n    size = 256,\n    maxAge = 750,\n    radius = 0.3,\n    intensity = 0.2,\n    interpolate = 0,\n    smoothing = 0,\n    minForce = 0.3,\n    blend = 'screen',\n    // source-over is canvas default. Others are slower\n    ease = easeCircleOut\n  } = {}) {\n    this.size = size;\n    this.maxAge = maxAge;\n    this.radius = radius;\n    this.intensity = intensity;\n    this.ease = ease;\n    this.interpolate = interpolate;\n    this.smoothing = smoothing;\n    this.minForce = minForce;\n    this.blend = blend;\n    this.trail = [];\n    this.force = 0;\n    this.initTexture();\n  }\n  initTexture() {\n    this.canvas = document.createElement('canvas');\n    this.canvas.width = this.canvas.height = this.size;\n    this.ctx = this.canvas.getContext('2d');\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n    this.texture = new Texture(this.canvas);\n    this.canvas.id = 'touchTexture';\n    this.canvas.style.width = this.canvas.style.height = `${this.canvas.width}px`;\n  }\n  update(delta) {\n    this.clear(); // age points\n\n    this.trail.forEach((point, i) => {\n      point.age += delta * 1000; // remove old\n\n      if (point.age > this.maxAge) {\n        this.trail.splice(i, 1);\n      }\n    }); // reset force when empty (when smoothing)\n\n    if (!this.trail.length) this.force = 0;\n    this.trail.forEach(point => {\n      this.drawTouch(point);\n    });\n    this.texture.needsUpdate = true;\n  }\n  clear() {\n    this.ctx.globalCompositeOperation = 'source-over';\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n  }\n  addTouch(point) {\n    const last = this.trail[this.trail.length - 1];\n    if (last) {\n      const dx = last.x - point.x;\n      const dy = last.y - point.y;\n      const dd = dx * dx + dy * dy;\n      const force = Math.max(this.minForce, Math.min(dd * 10000, 1));\n      this.force = smoothAverage(force, this.force, this.smoothing);\n      if (!!this.interpolate) {\n        const lines = Math.ceil(dd / Math.pow(this.radius * 0.5 / this.interpolate, 2));\n        if (lines > 1) {\n          for (let i = 1; i < lines; i++) {\n            this.trail.push({\n              x: last.x - dx / lines * i,\n              y: last.y - dy / lines * i,\n              age: 0,\n              force\n            });\n          }\n        }\n      }\n    }\n    this.trail.push({\n      x: point.x,\n      y: point.y,\n      age: 0,\n      force: this.force\n    });\n  }\n  drawTouch(point) {\n    const pos = {\n      x: point.x * this.size,\n      y: (1 - point.y) * this.size\n    };\n    let intensity = 1;\n    if (point.age < this.maxAge * 0.3) {\n      intensity = this.ease(point.age / (this.maxAge * 0.3));\n    } else {\n      intensity = this.ease(1 - (point.age - this.maxAge * 0.3) / (this.maxAge * 0.7));\n    }\n    intensity *= point.force; // apply blending\n\n    this.ctx.globalCompositeOperation = this.blend;\n    const radius = this.size * this.radius * intensity;\n    const grd = this.ctx.createRadialGradient(pos.x, pos.y, Math.max(0, radius * 0.25), pos.x, pos.y, Math.max(0, radius));\n    grd.addColorStop(0, `rgba(255, 255, 255, ${this.intensity})`);\n    grd.addColorStop(1, `rgba(0, 0, 0, 0.0)`);\n    this.ctx.beginPath();\n    this.ctx.fillStyle = grd;\n    this.ctx.arc(pos.x, pos.y, Math.max(0, radius), 0, Math.PI * 2);\n    this.ctx.fill();\n  }\n}\nfunction useTrailTexture(config = {}) {\n  const {\n    size,\n    maxAge,\n    radius,\n    intensity,\n    interpolate,\n    smoothing,\n    minForce,\n    blend,\n    ease\n  } = config;\n  const trail = useMemo(() => new TrailTexture(config), [size, maxAge, radius, intensity, interpolate, smoothing, minForce, blend, ease]);\n  useFrame((_, delta) => void trail.update(delta));\n  const onMove = useCallback(e => trail.addTouch(e.uv), [trail]);\n  return [trail.texture, onMove];\n}\nexport { useTrailTexture };", "map": {"version": 3, "names": ["useMemo", "useCallback", "useFrame", "Texture", "smoothAverage", "current", "measurement", "smoothing", "easeCircleOut", "x", "Math", "sqrt", "pow", "TrailTexture", "constructor", "size", "maxAge", "radius", "intensity", "interpolate", "minForce", "blend", "ease", "trail", "force", "initTexture", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "fillStyle", "fillRect", "texture", "id", "style", "update", "delta", "clear", "for<PERSON>ach", "point", "i", "age", "splice", "length", "drawTouch", "needsUpdate", "globalCompositeOperation", "addTouch", "last", "dx", "dy", "y", "dd", "max", "min", "lines", "ceil", "push", "pos", "grd", "createRadialGradient", "addColorStop", "beginPath", "arc", "PI", "fill", "useTrailTexture", "config", "_", "onMove", "e", "uv"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useTrailTexture.js"], "sourcesContent": ["import { useMemo, useCallback } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Texture } from 'three';\n\n/**\n *  Adapted from https://github.com/brunoimbrizi/interactive-particles/blob/master/src/scripts/webgl/particles/TrailTexture.js\n *  Changes:\n *    * accepts config as constructor params\n *    * frame-rate independent aging\n *    * added option to interpolate between slow mouse events\n *    * added option for smoothing between values to avoid large jumps in force\n */\n\n// smooth new sample (measurement) based on previous sample (current)\nfunction smoothAverage(current, measurement, smoothing = 0.9) {\n  return measurement * smoothing + current * (1.0 - smoothing);\n} // default ease\n\n\nconst easeCircleOut = x => Math.sqrt(1 - Math.pow(x - 1, 2));\n\nclass TrailTexture {\n  constructor({\n    size = 256,\n    maxAge = 750,\n    radius = 0.3,\n    intensity = 0.2,\n    interpolate = 0,\n    smoothing = 0,\n    minForce = 0.3,\n    blend = 'screen',\n    // source-over is canvas default. Others are slower\n    ease = easeCircleOut\n  } = {}) {\n    this.size = size;\n    this.maxAge = maxAge;\n    this.radius = radius;\n    this.intensity = intensity;\n    this.ease = ease;\n    this.interpolate = interpolate;\n    this.smoothing = smoothing;\n    this.minForce = minForce;\n    this.blend = blend;\n    this.trail = [];\n    this.force = 0;\n    this.initTexture();\n  }\n\n  initTexture() {\n    this.canvas = document.createElement('canvas');\n    this.canvas.width = this.canvas.height = this.size;\n    this.ctx = this.canvas.getContext('2d');\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n    this.texture = new Texture(this.canvas);\n    this.canvas.id = 'touchTexture';\n    this.canvas.style.width = this.canvas.style.height = `${this.canvas.width}px`;\n  }\n\n  update(delta) {\n    this.clear(); // age points\n\n    this.trail.forEach((point, i) => {\n      point.age += delta * 1000; // remove old\n\n      if (point.age > this.maxAge) {\n        this.trail.splice(i, 1);\n      }\n    }); // reset force when empty (when smoothing)\n\n    if (!this.trail.length) this.force = 0;\n    this.trail.forEach(point => {\n      this.drawTouch(point);\n    });\n    this.texture.needsUpdate = true;\n  }\n\n  clear() {\n    this.ctx.globalCompositeOperation = 'source-over';\n    this.ctx.fillStyle = 'black';\n    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n  }\n\n  addTouch(point) {\n    const last = this.trail[this.trail.length - 1];\n\n    if (last) {\n      const dx = last.x - point.x;\n      const dy = last.y - point.y;\n      const dd = dx * dx + dy * dy;\n      const force = Math.max(this.minForce, Math.min(dd * 10000, 1));\n      this.force = smoothAverage(force, this.force, this.smoothing);\n\n      if (!!this.interpolate) {\n        const lines = Math.ceil(dd / Math.pow(this.radius * 0.5 / this.interpolate, 2));\n\n        if (lines > 1) {\n          for (let i = 1; i < lines; i++) {\n            this.trail.push({\n              x: last.x - dx / lines * i,\n              y: last.y - dy / lines * i,\n              age: 0,\n              force\n            });\n          }\n        }\n      }\n    }\n\n    this.trail.push({\n      x: point.x,\n      y: point.y,\n      age: 0,\n      force: this.force\n    });\n  }\n\n  drawTouch(point) {\n    const pos = {\n      x: point.x * this.size,\n      y: (1 - point.y) * this.size\n    };\n    let intensity = 1;\n\n    if (point.age < this.maxAge * 0.3) {\n      intensity = this.ease(point.age / (this.maxAge * 0.3));\n    } else {\n      intensity = this.ease(1 - (point.age - this.maxAge * 0.3) / (this.maxAge * 0.7));\n    }\n\n    intensity *= point.force; // apply blending\n\n    this.ctx.globalCompositeOperation = this.blend;\n    const radius = this.size * this.radius * intensity;\n    const grd = this.ctx.createRadialGradient(pos.x, pos.y, Math.max(0, radius * 0.25), pos.x, pos.y, Math.max(0, radius));\n    grd.addColorStop(0, `rgba(255, 255, 255, ${this.intensity})`);\n    grd.addColorStop(1, `rgba(0, 0, 0, 0.0)`);\n    this.ctx.beginPath();\n    this.ctx.fillStyle = grd;\n    this.ctx.arc(pos.x, pos.y, Math.max(0, radius), 0, Math.PI * 2);\n    this.ctx.fill();\n  }\n\n}\n\nfunction useTrailTexture(config = {}) {\n  const {\n    size,\n    maxAge,\n    radius,\n    intensity,\n    interpolate,\n    smoothing,\n    minForce,\n    blend,\n    ease\n  } = config;\n  const trail = useMemo(() => new TrailTexture(config), [size, maxAge, radius, intensity, interpolate, smoothing, minForce, blend, ease]);\n  useFrame((_, delta) => void trail.update(delta));\n  const onMove = useCallback(e => trail.addTouch(e.uv), [trail]);\n  return [trail.texture, onMove];\n}\n\nexport { useTrailTexture };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,WAAW,QAAQ,OAAO;AAC5C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,OAAO,QAAQ,OAAO;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASC,aAAaA,CAACC,OAAO,EAAEC,WAAW,EAAEC,SAAS,GAAG,GAAG,EAAE;EAC5D,OAAOD,WAAW,GAAGC,SAAS,GAAGF,OAAO,IAAI,GAAG,GAAGE,SAAS,CAAC;AAC9D,CAAC,CAAC;;AAGF,MAAMC,aAAa,GAAGC,CAAC,IAAIC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACH,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAE5D,MAAMI,YAAY,CAAC;EACjBC,WAAWA,CAAC;IACVC,IAAI,GAAG,GAAG;IACVC,MAAM,GAAG,GAAG;IACZC,MAAM,GAAG,GAAG;IACZC,SAAS,GAAG,GAAG;IACfC,WAAW,GAAG,CAAC;IACfZ,SAAS,GAAG,CAAC;IACba,QAAQ,GAAG,GAAG;IACdC,KAAK,GAAG,QAAQ;IAChB;IACAC,IAAI,GAAGd;EACT,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,IAAI,CAACO,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACZ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACa,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB;EAEAA,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC9C,IAAI,CAACF,MAAM,CAACG,KAAK,GAAG,IAAI,CAACH,MAAM,CAACI,MAAM,GAAG,IAAI,CAACf,IAAI;IAClD,IAAI,CAACgB,GAAG,GAAG,IAAI,CAACL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;IACvC,IAAI,CAACD,GAAG,CAACE,SAAS,GAAG,OAAO;IAC5B,IAAI,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACR,MAAM,CAACG,KAAK,EAAE,IAAI,CAACH,MAAM,CAACI,MAAM,CAAC;IAC9D,IAAI,CAACK,OAAO,GAAG,IAAIhC,OAAO,CAAC,IAAI,CAACuB,MAAM,CAAC;IACvC,IAAI,CAACA,MAAM,CAACU,EAAE,GAAG,cAAc;IAC/B,IAAI,CAACV,MAAM,CAACW,KAAK,CAACR,KAAK,GAAG,IAAI,CAACH,MAAM,CAACW,KAAK,CAACP,MAAM,GAAG,GAAG,IAAI,CAACJ,MAAM,CAACG,KAAK,IAAI;EAC/E;EAEAS,MAAMA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEd,IAAI,CAACjB,KAAK,CAACkB,OAAO,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;MAC/BD,KAAK,CAACE,GAAG,IAAIL,KAAK,GAAG,IAAI,CAAC,CAAC;;MAE3B,IAAIG,KAAK,CAACE,GAAG,GAAG,IAAI,CAAC5B,MAAM,EAAE;QAC3B,IAAI,CAACO,KAAK,CAACsB,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAI,CAAC,IAAI,CAACpB,KAAK,CAACuB,MAAM,EAAE,IAAI,CAACtB,KAAK,GAAG,CAAC;IACtC,IAAI,CAACD,KAAK,CAACkB,OAAO,CAACC,KAAK,IAAI;MAC1B,IAAI,CAACK,SAAS,CAACL,KAAK,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAACP,OAAO,CAACa,WAAW,GAAG,IAAI;EACjC;EAEAR,KAAKA,CAAA,EAAG;IACN,IAAI,CAACT,GAAG,CAACkB,wBAAwB,GAAG,aAAa;IACjD,IAAI,CAAClB,GAAG,CAACE,SAAS,GAAG,OAAO;IAC5B,IAAI,CAACF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACR,MAAM,CAACG,KAAK,EAAE,IAAI,CAACH,MAAM,CAACI,MAAM,CAAC;EAChE;EAEAoB,QAAQA,CAACR,KAAK,EAAE;IACd,MAAMS,IAAI,GAAG,IAAI,CAAC5B,KAAK,CAAC,IAAI,CAACA,KAAK,CAACuB,MAAM,GAAG,CAAC,CAAC;IAE9C,IAAIK,IAAI,EAAE;MACR,MAAMC,EAAE,GAAGD,IAAI,CAAC1C,CAAC,GAAGiC,KAAK,CAACjC,CAAC;MAC3B,MAAM4C,EAAE,GAAGF,IAAI,CAACG,CAAC,GAAGZ,KAAK,CAACY,CAAC;MAC3B,MAAMC,EAAE,GAAGH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;MAC5B,MAAM7B,KAAK,GAAGd,IAAI,CAAC8C,GAAG,CAAC,IAAI,CAACpC,QAAQ,EAAEV,IAAI,CAAC+C,GAAG,CAACF,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;MAC9D,IAAI,CAAC/B,KAAK,GAAGpB,aAAa,CAACoB,KAAK,EAAE,IAAI,CAACA,KAAK,EAAE,IAAI,CAACjB,SAAS,CAAC;MAE7D,IAAI,CAAC,CAAC,IAAI,CAACY,WAAW,EAAE;QACtB,MAAMuC,KAAK,GAAGhD,IAAI,CAACiD,IAAI,CAACJ,EAAE,GAAG7C,IAAI,CAACE,GAAG,CAAC,IAAI,CAACK,MAAM,GAAG,GAAG,GAAG,IAAI,CAACE,WAAW,EAAE,CAAC,CAAC,CAAC;QAE/E,IAAIuC,KAAK,GAAG,CAAC,EAAE;UACb,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,EAAEf,CAAC,EAAE,EAAE;YAC9B,IAAI,CAACpB,KAAK,CAACqC,IAAI,CAAC;cACdnD,CAAC,EAAE0C,IAAI,CAAC1C,CAAC,GAAG2C,EAAE,GAAGM,KAAK,GAAGf,CAAC;cAC1BW,CAAC,EAAEH,IAAI,CAACG,CAAC,GAAGD,EAAE,GAAGK,KAAK,GAAGf,CAAC;cAC1BC,GAAG,EAAE,CAAC;cACNpB;YACF,CAAC,CAAC;UACJ;QACF;MACF;IACF;IAEA,IAAI,CAACD,KAAK,CAACqC,IAAI,CAAC;MACdnD,CAAC,EAAEiC,KAAK,CAACjC,CAAC;MACV6C,CAAC,EAAEZ,KAAK,CAACY,CAAC;MACVV,GAAG,EAAE,CAAC;MACNpB,KAAK,EAAE,IAAI,CAACA;IACd,CAAC,CAAC;EACJ;EAEAuB,SAASA,CAACL,KAAK,EAAE;IACf,MAAMmB,GAAG,GAAG;MACVpD,CAAC,EAAEiC,KAAK,CAACjC,CAAC,GAAG,IAAI,CAACM,IAAI;MACtBuC,CAAC,EAAE,CAAC,CAAC,GAAGZ,KAAK,CAACY,CAAC,IAAI,IAAI,CAACvC;IAC1B,CAAC;IACD,IAAIG,SAAS,GAAG,CAAC;IAEjB,IAAIwB,KAAK,CAACE,GAAG,GAAG,IAAI,CAAC5B,MAAM,GAAG,GAAG,EAAE;MACjCE,SAAS,GAAG,IAAI,CAACI,IAAI,CAACoB,KAAK,CAACE,GAAG,IAAI,IAAI,CAAC5B,MAAM,GAAG,GAAG,CAAC,CAAC;IACxD,CAAC,MAAM;MACLE,SAAS,GAAG,IAAI,CAACI,IAAI,CAAC,CAAC,GAAG,CAACoB,KAAK,CAACE,GAAG,GAAG,IAAI,CAAC5B,MAAM,GAAG,GAAG,KAAK,IAAI,CAACA,MAAM,GAAG,GAAG,CAAC,CAAC;IAClF;IAEAE,SAAS,IAAIwB,KAAK,CAAClB,KAAK,CAAC,CAAC;;IAE1B,IAAI,CAACO,GAAG,CAACkB,wBAAwB,GAAG,IAAI,CAAC5B,KAAK;IAC9C,MAAMJ,MAAM,GAAG,IAAI,CAACF,IAAI,GAAG,IAAI,CAACE,MAAM,GAAGC,SAAS;IAClD,MAAM4C,GAAG,GAAG,IAAI,CAAC/B,GAAG,CAACgC,oBAAoB,CAACF,GAAG,CAACpD,CAAC,EAAEoD,GAAG,CAACP,CAAC,EAAE5C,IAAI,CAAC8C,GAAG,CAAC,CAAC,EAAEvC,MAAM,GAAG,IAAI,CAAC,EAAE4C,GAAG,CAACpD,CAAC,EAAEoD,GAAG,CAACP,CAAC,EAAE5C,IAAI,CAAC8C,GAAG,CAAC,CAAC,EAAEvC,MAAM,CAAC,CAAC;IACtH6C,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,uBAAuB,IAAI,CAAC9C,SAAS,GAAG,CAAC;IAC7D4C,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,oBAAoB,CAAC;IACzC,IAAI,CAACjC,GAAG,CAACkC,SAAS,CAAC,CAAC;IACpB,IAAI,CAAClC,GAAG,CAACE,SAAS,GAAG6B,GAAG;IACxB,IAAI,CAAC/B,GAAG,CAACmC,GAAG,CAACL,GAAG,CAACpD,CAAC,EAAEoD,GAAG,CAACP,CAAC,EAAE5C,IAAI,CAAC8C,GAAG,CAAC,CAAC,EAAEvC,MAAM,CAAC,EAAE,CAAC,EAAEP,IAAI,CAACyD,EAAE,GAAG,CAAC,CAAC;IAC/D,IAAI,CAACpC,GAAG,CAACqC,IAAI,CAAC,CAAC;EACjB;AAEF;AAEA,SAASC,eAAeA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EACpC,MAAM;IACJvD,IAAI;IACJC,MAAM;IACNC,MAAM;IACNC,SAAS;IACTC,WAAW;IACXZ,SAAS;IACTa,QAAQ;IACRC,KAAK;IACLC;EACF,CAAC,GAAGgD,MAAM;EACV,MAAM/C,KAAK,GAAGvB,OAAO,CAAC,MAAM,IAAIa,YAAY,CAACyD,MAAM,CAAC,EAAE,CAACvD,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEZ,SAAS,EAAEa,QAAQ,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAAC;EACvIpB,QAAQ,CAAC,CAACqE,CAAC,EAAEhC,KAAK,KAAK,KAAKhB,KAAK,CAACe,MAAM,CAACC,KAAK,CAAC,CAAC;EAChD,MAAMiC,MAAM,GAAGvE,WAAW,CAACwE,CAAC,IAAIlD,KAAK,CAAC2B,QAAQ,CAACuB,CAAC,CAACC,EAAE,CAAC,EAAE,CAACnD,KAAK,CAAC,CAAC;EAC9D,OAAO,CAACA,KAAK,CAACY,OAAO,EAAEqC,MAAM,CAAC;AAChC;AAEA,SAASH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}