{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from './Line.js';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst defaultLookAt = new THREE.Vector3(0, 0, -1);\nconst Facemesh = /*#__PURE__*/React.forwardRef(({\n  face = FacemeshDatas.SAMPLE_FACE,\n  width,\n  height,\n  depth = 1,\n  verticalTri = [159, 386, 200],\n  origin,\n  debug = false,\n  children,\n  ...props\n}, fref) => {\n  var _meshRef$current3, _meshRef$current3$geo, _meshRef$current4;\n  const outerRef = React.useRef(null);\n  const meshRef = React.useRef(null);\n  const [sightDir] = React.useState(() => new THREE.Vector3());\n  const [sightDirQuaternion] = React.useState(() => new THREE.Quaternion());\n  const {\n    invalidate\n  } = useThree();\n  React.useEffect(() => {\n    var _meshRef$current;\n    (_meshRef$current = meshRef.current) == null ? void 0 : _meshRef$current.geometry.setIndex(FacemeshDatas.TRIANGULATION);\n  }, []);\n  const [a] = React.useState(() => new THREE.Vector3());\n  const [b] = React.useState(() => new THREE.Vector3());\n  const [c] = React.useState(() => new THREE.Vector3());\n  const [ab] = React.useState(() => new THREE.Vector3());\n  const [ac] = React.useState(() => new THREE.Vector3());\n  const [bboxSize] = React.useState(() => new THREE.Vector3());\n  React.useEffect(() => {\n    var _meshRef$current2, _outerRef$current, _geometry$boundingBox;\n    const geometry = (_meshRef$current2 = meshRef.current) == null ? void 0 : _meshRef$current2.geometry;\n    if (!geometry) return;\n    geometry.setFromPoints(face.keypoints); //\n    // A. compute sightDir vector (normal to verticalTri)\n    //\n\n    a.copy(face.keypoints[verticalTri[0]]);\n    b.copy(face.keypoints[verticalTri[1]]);\n    c.copy(face.keypoints[verticalTri[2]]);\n    ab.copy(b).sub(a);\n    ac.copy(c).sub(a);\n    sightDir.crossVectors(ac, ab).normalize();\n    sightDirQuaternion.setFromUnitVectors(defaultLookAt, sightDir);\n    const sightDirQuaternionInverse = sightDirQuaternion.clone().invert(); //\n    // B. geometry (straightened)\n    //\n    // 1. center (before rotate back)\n\n    geometry.computeBoundingBox();\n    if (debug) invalidate(); // invalidate to force re-render for box3Helper (after .computeBoundingBox())\n\n    geometry.center(); // 2. rotate back + rotate outerRef (once 1.)\n\n    geometry.applyQuaternion(sightDirQuaternionInverse);\n    (_outerRef$current = outerRef.current) == null ? void 0 : _outerRef$current.setRotationFromQuaternion(sightDirQuaternion); // 3. origin: substract the geometry to that landmark coords (once 1.)\n\n    if (origin) {\n      const position = geometry.getAttribute('position');\n      geometry.translate(-position.getX(origin), -position.getY(origin), -position.getZ(origin));\n    } // 4. re-scale\n\n    (_geometry$boundingBox = geometry.boundingBox) == null ? void 0 : _geometry$boundingBox.getSize(bboxSize);\n    let scale = 1;\n    if (width) scale = width / bboxSize.x; // fit in width\n\n    if (height) scale = height / bboxSize.y; // fit in height\n\n    if (depth) scale = depth / bboxSize.z; // fit in depth\n\n    if (scale !== 1) geometry.scale(scale, scale, scale);\n    geometry.computeVertexNormals();\n    geometry.attributes.position.needsUpdate = true;\n  }, [face, width, height, depth, verticalTri, origin, debug, invalidate, sightDir, sightDirQuaternion, a, b, c, ab, ac, bboxSize]); //\n  // API\n  //\n\n  const api = React.useMemo(() => ({\n    meshRef,\n    outerRef\n  }), []);\n  React.useImperativeHandle(fref, () => api, [api]);\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    ref: outerRef\n  }, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: meshRef\n  }, children, debug ? /*#__PURE__*/React.createElement(React.Fragment, null, ((_meshRef$current3 = meshRef.current) == null ? void 0 : (_meshRef$current3$geo = _meshRef$current3.geometry) == null ? void 0 : _meshRef$current3$geo.boundingBox) && /*#__PURE__*/React.createElement(\"box3Helper\", {\n    args: [(_meshRef$current4 = meshRef.current) == null ? void 0 : _meshRef$current4.geometry.boundingBox]\n  }), /*#__PURE__*/React.createElement(Line, {\n    points: [[0, 0, 0], defaultLookAt],\n    color: 0x00ffff\n  })) : null)));\n});\nconst FacemeshDatas = {\n  // My face as default (captured with a 640x480 webcam)\n  // prettier-ignore\n  SAMPLE_FACE: {\n    \"keypoints\": [{\n      \"x\": 356.2804412841797,\n      \"y\": 295.1960563659668,\n      \"z\": -23.786449432373047,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 354.8859405517578,\n      \"y\": 264.69520568847656,\n      \"z\": -36.718435287475586\n    }, {\n      \"x\": 355.2180862426758,\n      \"y\": 275.3360366821289,\n      \"z\": -21.183712482452393\n    }, {\n      \"x\": 347.349853515625,\n      \"y\": 242.4400234222412,\n      \"z\": -25.093655586242676\n    }, {\n      \"x\": 354.40135955810547,\n      \"y\": 256.67933464050293,\n      \"z\": -38.23572635650635\n    }, {\n      \"x\": 353.7689971923828,\n      \"y\": 247.54886627197266,\n      \"z\": -34.5475435256958\n    }, {\n      \"x\": 352.1288299560547,\n      \"y\": 227.34312057495117,\n      \"z\": -13.095386028289795\n    }, {\n      \"x\": 303.5013198852539,\n      \"y\": 234.67002868652344,\n      \"z\": 12.500141859054565,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 351.09378814697266,\n      \"y\": 211.87547206878662,\n      \"z\": -6.413471698760986\n    }, {\n      \"x\": 350.7115936279297,\n      \"y\": 202.1251630783081,\n      \"z\": -6.413471698760986\n    }, {\n      \"x\": 348.33667755126953,\n      \"y\": 168.7741756439209,\n      \"z\": 6.483500003814697,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 356.4806365966797,\n      \"y\": 299.2995357513428,\n      \"z\": -23.144519329071045\n    }, {\n      \"x\": 356.5511703491211,\n      \"y\": 302.66146659851074,\n      \"z\": -21.020312309265137\n    }, {\n      \"x\": 356.6239547729492,\n      \"y\": 304.1536331176758,\n      \"z\": -18.137459754943848,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 356.5807342529297,\n      \"y\": 305.1840591430664,\n      \"z\": -18.767719268798828,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 356.8241500854492,\n      \"y\": 308.25711250305176,\n      \"z\": -20.16829490661621\n    }, {\n      \"x\": 357.113037109375,\n      \"y\": 312.26277351379395,\n      \"z\": -22.10575819015503\n    }, {\n      \"x\": 357.34962463378906,\n      \"y\": 317.1123218536377,\n      \"z\": -21.837315559387207,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 357.6658630371094,\n      \"y\": 325.51036834716797,\n      \"z\": -16.27002477645874\n    }, {\n      \"x\": 355.0201416015625,\n      \"y\": 269.36279296875,\n      \"z\": -33.73054027557373\n    }, {\n      \"x\": 348.5237503051758,\n      \"y\": 270.33411026000977,\n      \"z\": -24.93025302886963\n    }, {\n      \"x\": 279.97331619262695,\n      \"y\": 213.24176788330078,\n      \"z\": 47.759642601013184,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 322.66529083251953,\n      \"y\": 238.5027265548706,\n      \"z\": 5.535193085670471\n    }, {\n      \"x\": 316.0983657836914,\n      \"y\": 239.94489669799805,\n      \"z\": 5.777376294136047\n    }, {\n      \"x\": 309.9431610107422,\n      \"y\": 240.24518966674805,\n      \"z\": 7.510589361190796\n    }, {\n      \"x\": 301.31994247436523,\n      \"y\": 237.86138534545898,\n      \"z\": 13.118728399276733\n    }, {\n      \"x\": 328.14266204833984,\n      \"y\": 235.80496788024902,\n      \"z\": 6.646900177001953\n    }, {\n      \"x\": 313.7326431274414,\n      \"y\": 222.11161136627197,\n      \"z\": 3.9887237548828125\n    }, {\n      \"x\": 320.45196533203125,\n      \"y\": 221.87729358673096,\n      \"z\": 4.601476192474365\n    }, {\n      \"x\": 307.35679626464844,\n      \"y\": 223.63793849945068,\n      \"z\": 5.932023525238037\n    }, {\n      \"x\": 303.0031204223633,\n      \"y\": 226.3743782043457,\n      \"z\": 8.479321002960205\n    }, {\n      \"x\": 296.80023193359375,\n      \"y\": 242.94299125671387,\n      \"z\": 15.931552648544312\n    }, {\n      \"x\": 332.2352981567383,\n      \"y\": 340.77341079711914,\n      \"z\": -10.165848731994629\n    }, {\n      \"x\": 301.38587951660156,\n      \"y\": 233.46447944641113,\n      \"z\": 14.764405488967896,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 279.0147018432617,\n      \"y\": 244.37155723571777,\n      \"z\": 45.77549457550049\n    }, {\n      \"x\": 289.60548400878906,\n      \"y\": 239.1807460784912,\n      \"z\": 23.191204071044922\n    }, {\n      \"x\": 320.32257080078125,\n      \"y\": 267.1292781829834,\n      \"z\": -4.954537749290466\n    }, {\n      \"x\": 347.64583587646484,\n      \"y\": 294.4955062866211,\n      \"z\": -23.062820434570312,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 349.28138732910156,\n      \"y\": 303.1095886230469,\n      \"z\": -20.238323211669922\n    }, {\n      \"x\": 338.9453125,\n      \"y\": 298.19186210632324,\n      \"z\": -19.456336498260498,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 333.36788177490234,\n      \"y\": 302.6706790924072,\n      \"z\": -14.776077270507812,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 342.89188385009766,\n      \"y\": 304.3561363220215,\n      \"z\": -17.752301692962646\n    }, {\n      \"x\": 337.7375030517578,\n      \"y\": 306.0098361968994,\n      \"z\": -13.410515785217285\n    }, {\n      \"x\": 325.6159210205078,\n      \"y\": 316.22995376586914,\n      \"z\": -6.681914925575256\n    }, {\n      \"x\": 349.0104675292969,\n      \"y\": 264.9818515777588,\n      \"z\": -36.274919509887695\n    }, {\n      \"x\": 347.7138900756836,\n      \"y\": 257.5664806365967,\n      \"z\": -37.67549514770508\n    }, {\n      \"x\": 291.79357528686523,\n      \"y\": 218.88171672821045,\n      \"z\": 11.578094959259033,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 332.2689437866211,\n      \"y\": 247.56946563720703,\n      \"z\": -3.3730539679527283\n    }, {\n      \"x\": 332.0074462890625,\n      \"y\": 267.1201229095459,\n      \"z\": -19.969879388809204\n    }, {\n      \"x\": 331.27952575683594,\n      \"y\": 263.6967658996582,\n      \"z\": -17.47218608856201\n    }, {\n      \"x\": 301.04373931884766,\n      \"y\": 269.56552505493164,\n      \"z\": 3.61815482378006\n    }, {\n      \"x\": 347.4863815307617,\n      \"y\": 249.0706443786621,\n      \"z\": -32.633421421051025\n    }, {\n      \"x\": 307.26118087768555,\n      \"y\": 208.2646894454956,\n      \"z\": 1.1591226607561111,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 297.91919708251953,\n      \"y\": 212.22604751586914,\n      \"z\": 5.914516448974609,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 285.1651382446289,\n      \"y\": 197.98450469970703,\n      \"z\": 36.391637325286865,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 337.04097747802734,\n      \"y\": 211.25229835510254,\n      \"z\": -4.548954665660858\n    }, {\n      \"x\": 326.5912628173828,\n      \"y\": 223.16698551177979,\n      \"z\": 6.670243740081787\n    }, {\n      \"x\": 320.05664825439453,\n      \"y\": 309.5834255218506,\n      \"z\": -4.055835008621216\n    }, {\n      \"x\": 289.6866226196289,\n      \"y\": 314.617395401001,\n      \"z\": 53.875489234924316,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 337.4256896972656,\n      \"y\": 270.8755302429199,\n      \"z\": -17.67060160636902\n    }, {\n      \"x\": 343.69922637939453,\n      \"y\": 273.0000400543213,\n      \"z\": -18.756048679351807\n    }, {\n      \"x\": 327.4242401123047,\n      \"y\": 309.22399520874023,\n      \"z\": -4.703601002693176,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 330.37220001220703,\n      \"y\": 308.3323001861572,\n      \"z\": -6.442649960517883\n    }, {\n      \"x\": 293.87027740478516,\n      \"y\": 207.7961826324463,\n      \"z\": 9.821539521217346,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 332.11437225341797,\n      \"y\": 271.22812271118164,\n      \"z\": -16.64351224899292\n    }, {\n      \"x\": 320.1197814941406,\n      \"y\": 207.40366458892822,\n      \"z\": -2.48164564371109,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 318.59575271606445,\n      \"y\": 201.07443809509277,\n      \"z\": -3.110446035861969,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 310.72303771972656,\n      \"y\": 175.75075149536133,\n      \"z\": 13.328815698623657,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 289.67578887939453,\n      \"y\": 202.29835510253906,\n      \"z\": 21.370456218719482\n    }, {\n      \"x\": 315.30879974365234,\n      \"y\": 187.35260009765625,\n      \"z\": 5.0304025411605835\n    }, {\n      \"x\": 287.8936767578125,\n      \"y\": 216.54793739318848,\n      \"z\": 17.81065821647644,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 283.9391899108887,\n      \"y\": 215.01142501831055,\n      \"z\": 32.04984903335571\n    }, {\n      \"x\": 348.35330963134766,\n      \"y\": 299.4155788421631,\n      \"z\": -22.47924566268921\n    }, {\n      \"x\": 341.1790466308594,\n      \"y\": 301.8221855163574,\n      \"z\": -18.977805376052856\n    }, {\n      \"x\": 335.69713592529297,\n      \"y\": 304.4266891479492,\n      \"z\": -14.682706594467163\n    }, {\n      \"x\": 339.4615173339844,\n      \"y\": 272.3654365539551,\n      \"z\": -16.38674020767212\n    }, {\n      \"x\": 328.99600982666016,\n      \"y\": 308.86685371398926,\n      \"z\": -5.616893768310547\n    }, {\n      \"x\": 332.00313568115234,\n      \"y\": 309.1875743865967,\n      \"z\": -10.335084199905396\n    }, {\n      \"x\": 331.0068130493164,\n      \"y\": 307.9274368286133,\n      \"z\": -6.681914925575256,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 341.13792419433594,\n      \"y\": 266.4876937866211,\n      \"z\": -26.56425952911377\n    }, {\n      \"x\": 339.02950286865234,\n      \"y\": 305.6663703918457,\n      \"z\": -12.33674168586731,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 344.22935485839844,\n      \"y\": 304.9452781677246,\n      \"z\": -15.161235332489014,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 350.1844024658203,\n      \"y\": 304.374303817749,\n      \"z\": -17.5305438041687,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 348.52630615234375,\n      \"y\": 325.9562301635742,\n      \"z\": -16.164982318878174\n    }, {\n      \"x\": 348.6581802368164,\n      \"y\": 317.1624183654785,\n      \"z\": -21.510512828826904,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 348.9766311645508,\n      \"y\": 312.1923065185547,\n      \"z\": -21.708929538726807\n    }, {\n      \"x\": 349.2427444458008,\n      \"y\": 308.0660820007324,\n      \"z\": -19.643079042434692\n    }, {\n      \"x\": 349.67491149902344,\n      \"y\": 305.42747497558594,\n      \"z\": -18.16080331802368,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 337.95589447021484,\n      \"y\": 306.6535949707031,\n      \"z\": -12.803598642349243,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 337.06878662109375,\n      \"y\": 307.63169288635254,\n      \"z\": -14.274203777313232\n    }, {\n      \"x\": 335.77449798583984,\n      \"y\": 309.8449516296387,\n      \"z\": -15.698124170303345\n    }, {\n      \"x\": 334.6099090576172,\n      \"y\": 312.7997016906738,\n      \"z\": -14.764405488967896,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 327.2330856323242,\n      \"y\": 293.80866050720215,\n      \"z\": -11.864047050476074\n    }, {\n      \"x\": 280.97679138183594,\n      \"y\": 279.79928970336914,\n      \"z\": 68.90834331512451,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 355.13843536376953,\n      \"y\": 271.7875671386719,\n      \"z\": -25.350427627563477\n    }, {\n      \"x\": 334.7235870361328,\n      \"y\": 307.4656391143799,\n      \"z\": -9.302158951759338,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 333.5293960571289,\n      \"y\": 307.89782524108887,\n      \"z\": -10.200862884521484\n    }, {\n      \"x\": 346.29688262939453,\n      \"y\": 276.4256286621094,\n      \"z\": -19.748122692108154\n    }, {\n      \"x\": 335.16246795654297,\n      \"y\": 276.22097969055176,\n      \"z\": -12.313398122787476\n    }, {\n      \"x\": 345.09132385253906,\n      \"y\": 274.7082996368408,\n      \"z\": -19.304605722427368\n    }, {\n      \"x\": 325.4267883300781,\n      \"y\": 252.95130729675293,\n      \"z\": -1.6661019623279572\n    }, {\n      \"x\": 315.347843170166,\n      \"y\": 259.05200958251953,\n      \"z\": -0.25604281574487686\n    }, {\n      \"x\": 330.44933319091797,\n      \"y\": 267.7570152282715,\n      \"z\": -14.017432928085327\n    }, {\n      \"x\": 294.96768951416016,\n      \"y\": 185.26001930236816,\n      \"z\": 23.903164863586426,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 299.63531494140625,\n      \"y\": 192.7913761138916,\n      \"z\": 12.640198469161987\n    }, {\n      \"x\": 304.5452117919922,\n      \"y\": 202.4142837524414,\n      \"z\": 3.244667649269104,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 331.6915512084961,\n      \"y\": 320.0467872619629,\n      \"z\": -10.632705688476562\n    }, {\n      \"x\": 334.5911407470703,\n      \"y\": 201.27566814422607,\n      \"z\": -6.133356094360352,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 331.4815902709961,\n      \"y\": 185.44180870056152,\n      \"z\": 0.6627205014228821\n    }, {\n      \"x\": 328.05816650390625,\n      \"y\": 170.8385467529297,\n      \"z\": 7.358860373497009,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 304.49764251708984,\n      \"y\": 239.76297855377197,\n      \"z\": 10.387605428695679\n    }, {\n      \"x\": 290.6382179260254,\n      \"y\": 248.85257720947266,\n      \"z\": 19.03616428375244\n    }, {\n      \"x\": 331.5682601928711,\n      \"y\": 233.20727348327637,\n      \"z\": 7.837390303611755\n    }, {\n      \"x\": 295.5115509033203,\n      \"y\": 228.9834451675415,\n      \"z\": 14.41426157951355\n    }, {\n      \"x\": 336.94332122802734,\n      \"y\": 241.8259334564209,\n      \"z\": -5.27842104434967\n    }, {\n      \"x\": 336.2792205810547,\n      \"y\": 262.7049922943115,\n      \"z\": -26.12074375152588\n    }, {\n      \"x\": 284.4102478027344,\n      \"y\": 255.3262710571289,\n      \"z\": 25.467140674591064\n    }, {\n      \"x\": 295.1420593261719,\n      \"y\": 253.02655220031738,\n      \"z\": 12.430112361907959\n    }, {\n      \"x\": 303.5196113586426,\n      \"y\": 254.20703887939453,\n      \"z\": 6.139191389083862\n    }, {\n      \"x\": 315.73450088500977,\n      \"y\": 251.64799690246582,\n      \"z\": 3.3788898587226868\n    }, {\n      \"x\": 324.69661712646484,\n      \"y\": 247.56494522094727,\n      \"z\": 2.3328344523906708\n    }, {\n      \"x\": 331.57970428466797,\n      \"y\": 243.02241325378418,\n      \"z\": 1.1423448473215103\n    }, {\n      \"x\": 345.6210708618164,\n      \"y\": 229.9976634979248,\n      \"z\": -10.825285911560059\n    }, {\n      \"x\": 286.26644134521484,\n      \"y\": 270.37991523742676,\n      \"z\": 21.708929538726807\n    }, {\n      \"x\": 290.2525520324707,\n      \"y\": 228.4921360015869,\n      \"z\": 17.71728754043579\n    }, {\n      \"x\": 351.65367126464844,\n      \"y\": 269.3400764465332,\n      \"z\": -33.450424671173096\n    }, {\n      \"x\": 333.1378936767578,\n      \"y\": 253.88388633728027,\n      \"z\": -7.230473756790161\n    }, {\n      \"x\": 277.8318977355957,\n      \"y\": 246.95331573486328,\n      \"z\": 68.20805549621582,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 336.6680908203125,\n      \"y\": 238.10003757476807,\n      \"z\": 0.7688578963279724\n    }, {\n      \"x\": 329.95800018310547,\n      \"y\": 269.18323516845703,\n      \"z\": -7.207130789756775\n    }, {\n      \"x\": 299.17491912841797,\n      \"y\": 234.13324356079102,\n      \"z\": 15.95489501953125\n    }, {\n      \"x\": 335.61729431152344,\n      \"y\": 258.71752738952637,\n      \"z\": -23.016133308410645\n    }, {\n      \"x\": 284.1079330444336,\n      \"y\": 297.0343494415283,\n      \"z\": 63.25934886932373,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 331.44542694091797,\n      \"y\": 230.6892442703247,\n      \"z\": 9.92658257484436,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 341.41536712646484,\n      \"y\": 253.01264762878418,\n      \"z\": -29.038610458374023\n    }, {\n      \"x\": 303.5472869873047,\n      \"y\": 327.5896739959717,\n      \"z\": 16.725212335586548\n    }, {\n      \"x\": 304.7756576538086,\n      \"y\": 337.4389457702637,\n      \"z\": 27.38126277923584,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 280.80501556396484,\n      \"y\": 275.32050132751465,\n      \"z\": 45.0752067565918\n    }, {\n      \"x\": 295.43582916259766,\n      \"y\": 318.4501647949219,\n      \"z\": 26.2608003616333\n    }, {\n      \"x\": 281.4303207397461,\n      \"y\": 228.7355661392212,\n      \"z\": 40.94350814819336\n    }, {\n      \"x\": 331.2549591064453,\n      \"y\": 349.4216537475586,\n      \"z\": -7.376367449760437\n    }, {\n      \"x\": 352.4247741699219,\n      \"y\": 271.7330074310303,\n      \"z\": -24.953596591949463\n    }, {\n      \"x\": 327.5672912597656,\n      \"y\": 260.41900634765625,\n      \"z\": -5.456410646438599\n    }, {\n      \"x\": 284.5432472229004,\n      \"y\": 241.7647933959961,\n      \"z\": 29.668869972229004\n    }, {\n      \"x\": 310,\n      \"y\": 235.66174507141113,\n      \"z\": 8.502663969993591,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 315.7071113586426,\n      \"y\": 235.7572603225708,\n      \"z\": 6.938687562942505,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 330.41088104248047,\n      \"y\": 311.04143142700195,\n      \"z\": -9.325502514839172,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 288.5377502441406,\n      \"y\": 285.31983375549316,\n      \"z\": 21.837315559387207\n    }, {\n      \"x\": 344.55039978027344,\n      \"y\": 359.4300842285156,\n      \"z\": -6.705257892608643,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 323.41880798339844,\n      \"y\": 351.67362213134766,\n      \"z\": 7.802375555038452,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 314.64088439941406,\n      \"y\": 346.11894607543945,\n      \"z\": 16.36339783668518,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 349.4945526123047,\n      \"y\": 184.8434829711914,\n      \"z\": -0.21847527474164963\n    }, {\n      \"x\": 359.24694061279297,\n      \"y\": 359.8348903656006,\n      \"z\": -8.403456211090088,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 321.26182556152344,\n      \"y\": 234.64492321014404,\n      \"z\": 6.90950870513916,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 326.318359375,\n      \"y\": 232.90250301361084,\n      \"z\": 8.029969334602356,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 329.6211624145508,\n      \"y\": 231.6195774078369,\n      \"z\": 9.722331762313843,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 285.9398078918457,\n      \"y\": 228.2351303100586,\n      \"z\": 24.650139808654785\n    }, {\n      \"x\": 325.79288482666016,\n      \"y\": 227.88007736206055,\n      \"z\": 7.469738721847534,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 320.1699447631836,\n      \"y\": 227.5934886932373,\n      \"z\": 6.168370842933655,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 314.85408782958984,\n      \"y\": 227.85282611846924,\n      \"z\": 6.2675780057907104,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 309.3084907531738,\n      \"y\": 229.1516876220703,\n      \"z\": 7.7031683921813965,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 305.5621337890625,\n      \"y\": 230.92366218566895,\n      \"z\": 9.722331762313843,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 277.8681945800781,\n      \"y\": 228.5354232788086,\n      \"z\": 59.71122741699219,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 306.1444664001465,\n      \"y\": 235.1954698562622,\n      \"z\": 10.603528022766113,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 355.4478454589844,\n      \"y\": 281.96210861206055,\n      \"z\": -20.565123558044434\n    }, {\n      \"x\": 333.02661895751953,\n      \"y\": 288.0105400085449,\n      \"z\": -14.72939133644104\n    }, {\n      \"x\": 337.15728759765625,\n      \"y\": 269.2059516906738,\n      \"z\": -19.8414945602417\n    }, {\n      \"x\": 345.9898376464844,\n      \"y\": 283.5453128814697,\n      \"z\": -20.4834246635437\n    }, {\n      \"x\": 351.48963928222656,\n      \"y\": 219.98916149139404,\n      \"z\": -7.0378947257995605\n    }, {\n      \"x\": 312.39574432373047,\n      \"y\": 336.50628089904785,\n      \"z\": 8.671900033950806\n    }, {\n      \"x\": 321.32152557373047,\n      \"y\": 343.1755256652832,\n      \"z\": 0.9067271649837494\n    }, {\n      \"x\": 343.78379821777344,\n      \"y\": 353.2975959777832,\n      \"z\": -14.355905055999756\n    }, {\n      \"x\": 296.8791389465332,\n      \"y\": 327.91497230529785,\n      \"z\": 41.01353645324707,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 329.6939468383789,\n      \"y\": 229.27897453308105,\n      \"z\": 8.934508562088013,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 341.6905212402344,\n      \"y\": 241.4073657989502,\n      \"z\": -14.589333534240723\n    }, {\n      \"x\": 359.03079986572266,\n      \"y\": 353.48859786987305,\n      \"z\": -15.803166627883911\n    }, {\n      \"x\": 333.1861877441406,\n      \"y\": 356.43213272094727,\n      \"z\": -1.0234417766332626,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 283.97483825683594,\n      \"y\": 291.4318656921387,\n      \"z\": 41.94725513458252\n    }, {\n      \"x\": 343.33770751953125,\n      \"y\": 305.830135345459,\n      \"z\": -15.756480693817139,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 342.40283966064453,\n      \"y\": 307.7453899383545,\n      \"z\": -17.4021577835083\n    }, {\n      \"x\": 341.53621673583984,\n      \"y\": 311.0595703125,\n      \"z\": -19.047834873199463\n    }, {\n      \"x\": 340.9107208251953,\n      \"y\": 315.4837703704834,\n      \"z\": -18.5576331615448,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 339.1478729248047,\n      \"y\": 323.42233657836914,\n      \"z\": -14.367576837539673\n    }, {\n      \"x\": 333.3201599121094,\n      \"y\": 307.4406337738037,\n      \"z\": -9.617288708686829\n    }, {\n      \"x\": 331.2411117553711,\n      \"y\": 306.9811820983887,\n      \"z\": -9.669809937477112\n    }, {\n      \"x\": 329.23255920410156,\n      \"y\": 306.0508346557617,\n      \"z\": -9.582273960113525,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 322.4586486816406,\n      \"y\": 301.33323669433594,\n      \"z\": -7.720675468444824\n    }, {\n      \"x\": 297.1712112426758,\n      \"y\": 286.9552803039551,\n      \"z\": 8.240055441856384\n    }, {\n      \"x\": 341.3060760498047,\n      \"y\": 235.4432201385498,\n      \"z\": -7.504753470420837\n    }, {\n      \"x\": 336.9318389892578,\n      \"y\": 224.3451976776123,\n      \"z\": 5.829898118972778\n    }, {\n      \"x\": 332.65323638916016,\n      \"y\": 226.70403957366943,\n      \"z\": 8.105834126472473\n    }, {\n      \"x\": 334.67357635498047,\n      \"y\": 306.4397621154785,\n      \"z\": -8.981193900108337,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 297.4601936340332,\n      \"y\": 306.29210472106934,\n      \"z\": 15.476365089416504\n    }, {\n      \"x\": 342.9119110107422,\n      \"y\": 222.37077713012695,\n      \"z\": -2.754466235637665\n    }, {\n      \"x\": 335.4629898071289,\n      \"y\": 332.20250129699707,\n      \"z\": -11.823196411132812\n    }, {\n      \"x\": 353.2412338256836,\n      \"y\": 240.56339263916016,\n      \"z\": -27.147831916809082\n    }, {\n      \"x\": 346.3080596923828,\n      \"y\": 236.41446590423584,\n      \"z\": -18.452589511871338\n    }, {\n      \"x\": 352.6475143432617,\n      \"y\": 234.1420555114746,\n      \"z\": -19.748122692108154\n    }, {\n      \"x\": 337.3209762573242,\n      \"y\": 253.39937210083008,\n      \"z\": -16.024924516677856\n    }, {\n      \"x\": 358.6122131347656,\n      \"y\": 344.90861892700195,\n      \"z\": -18.592647314071655\n    }, {\n      \"x\": 358.1117248535156,\n      \"y\": 334.64990615844727,\n      \"z\": -17.49552845954895\n    }, {\n      \"x\": 346.4450454711914,\n      \"y\": 335.0321102142334,\n      \"z\": -16.32838249206543\n    }, {\n      \"x\": 319.17640686035156,\n      \"y\": 320.2833938598633,\n      \"z\": -3.276764452457428\n    }, {\n      \"x\": 325.2540588378906,\n      \"y\": 276.2369728088379,\n      \"z\": -6.460157036781311\n    }, {\n      \"x\": 326.7214584350586,\n      \"y\": 327.3939514160156,\n      \"z\": -7.417217493057251\n    }, {\n      \"x\": 310.7190132141113,\n      \"y\": 277.2265148162842,\n      \"z\": -3.5452082753181458\n    }, {\n      \"x\": 319.78355407714844,\n      \"y\": 284.8238182067871,\n      \"z\": -6.4543211460113525\n    }, {\n      \"x\": 305.773983001709,\n      \"y\": 290.83580017089844,\n      \"z\": 0.06907138042151928\n    }, {\n      \"x\": 344.4001770019531,\n      \"y\": 344.85408782958984,\n      \"z\": -16.946970224380493\n    }, {\n      \"x\": 333.1879425048828,\n      \"y\": 258.74256134033203,\n      \"z\": -11.90489649772644\n    }, {\n      \"x\": 313.80598068237305,\n      \"y\": 327.08919525146484,\n      \"z\": 2.2277912497520447\n    }, {\n      \"x\": 322.9637908935547,\n      \"y\": 334.6819496154785,\n      \"z\": -3.3643004298210144\n    }, {\n      \"x\": 313.4055519104004,\n      \"y\": 311.2166690826416,\n      \"z\": -1.1175429821014404\n    }, {\n      \"x\": 291.0865783691406,\n      \"y\": 298.2831001281738,\n      \"z\": 22.467575073242188\n    }, {\n      \"x\": 305.6580924987793,\n      \"y\": 313.3707904815674,\n      \"z\": 5.561453700065613\n    }, {\n      \"x\": 288.23760986328125,\n      \"y\": 305.9941864013672,\n      \"z\": 36.765122413635254\n    }, {\n      \"x\": 315.10692596435547,\n      \"y\": 296.26991271972656,\n      \"z\": -4.604393839836121\n    }, {\n      \"x\": 337.50518798828125,\n      \"y\": 247.5944423675537,\n      \"z\": -10.597691535949707\n    }, {\n      \"x\": 338.8450622558594,\n      \"y\": 265.47778129577637,\n      \"z\": -27.778091430664062\n    }, {\n      \"x\": 334.25254821777344,\n      \"y\": 269.0671920776367,\n      \"z\": -20.938611030578613\n    }, {\n      \"x\": 341.64512634277344,\n      \"y\": 259.6387195587158,\n      \"z\": -32.189905643463135\n    }, {\n      \"x\": 331.44081115722656,\n      \"y\": 219.0976095199585,\n      \"z\": 4.207563698291779\n    }, {\n      \"x\": 320.56339263916016,\n      \"y\": 216.49658203125,\n      \"z\": 2.930997312068939\n    }, {\n      \"x\": 311.21912002563477,\n      \"y\": 216.57853603363037,\n      \"z\": 2.9674705862998962\n    }, {\n      \"x\": 303.46256256103516,\n      \"y\": 218.54614734649658,\n      \"z\": 5.357203483581543\n    }, {\n      \"x\": 297.99999237060547,\n      \"y\": 222.505202293396,\n      \"z\": 9.325502514839172\n    }, {\n      \"x\": 294.93839263916016,\n      \"y\": 236.39654159545898,\n      \"z\": 18.534289598464966\n    }, {\n      \"x\": 278.87489318847656,\n      \"y\": 259.7095584869385,\n      \"z\": 45.68212032318115\n    }, {\n      \"x\": 300.3782653808594,\n      \"y\": 245.38593292236328,\n      \"z\": 12.278382778167725\n    }, {\n      \"x\": 307.06348419189453,\n      \"y\": 246.36857986450195,\n      \"z\": 8.164191246032715\n    }, {\n      \"x\": 315.5229187011719,\n      \"y\": 245.3949737548828,\n      \"z\": 5.503097176551819\n    }, {\n      \"x\": 323.71395111083984,\n      \"y\": 242.75178909301758,\n      \"z\": 4.6335723996162415\n    }, {\n      \"x\": 330.2785873413086,\n      \"y\": 239.34658527374268,\n      \"z\": 4.937030673027039\n    }, {\n      \"x\": 334.6982192993164,\n      \"y\": 236.0460376739502,\n      \"z\": 4.823233783245087\n    }, {\n      \"x\": 279.3412208557129,\n      \"y\": 263.5196113586426,\n      \"z\": 70.91583728790283,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 334.65972900390625,\n      \"y\": 271.6648578643799,\n      \"z\": -17.775644063949585\n    }, {\n      \"x\": 342.05677032470703,\n      \"y\": 246.99846267700195,\n      \"z\": -20.84523916244507\n    }, {\n      \"x\": 344.0357971191406,\n      \"y\": 264.5701503753662,\n      \"z\": -32.936880588531494\n    }, {\n      \"x\": 348.25531005859375,\n      \"y\": 268.6645030975342,\n      \"z\": -30.695960521697998\n    }, {\n      \"x\": 344.12227630615234,\n      \"y\": 266.34212493896484,\n      \"z\": -29.808926582336426\n    }, {\n      \"x\": 337.12318420410156,\n      \"y\": 274.2556858062744,\n      \"z\": -15.768152475357056\n    }, {\n      \"x\": 349.49047088623047,\n      \"y\": 269.071683883667,\n      \"z\": -32.51670837402344\n    }, {\n      \"x\": 350.1683044433594,\n      \"y\": 271.4691352844238,\n      \"z\": -24.93025302886963\n    }, {\n      \"x\": 333.9634704589844,\n      \"y\": 230.56639194488525,\n      \"z\": 8.89949381351471\n    }, {\n      \"x\": 338.2147979736328,\n      \"y\": 231.4807891845703,\n      \"z\": 4.6715047955513\n    }, {\n      \"x\": 340.4712677001953,\n      \"y\": 231.74463272094727,\n      \"z\": -0.34996166825294495\n    }, {\n      \"x\": 303.28975677490234,\n      \"y\": 232.24980354309082,\n      \"z\": 11.916568279266357,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 299.4649124145508,\n      \"y\": 229.53842639923096,\n      \"z\": 12.325069904327393\n    }, {\n      \"x\": 359.09618377685547,\n      \"y\": 241.77349090576172,\n      \"z\": -24.650139808654785\n    }, {\n      \"x\": 399.46216583251953,\n      \"y\": 229.89503860473633,\n      \"z\": 15.919880867004395,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 361.38919830322266,\n      \"y\": 269.6129894256592,\n      \"z\": -24.510080814361572\n    }, {\n      \"x\": 416.9973373413086,\n      \"y\": 206.0895538330078,\n      \"z\": 53.26857566833496,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 381.32179260253906,\n      \"y\": 235.5476474761963,\n      \"z\": 7.6214683055877686\n    }, {\n      \"x\": 387.8068542480469,\n      \"y\": 236.25958442687988,\n      \"z\": 8.345099091529846\n    }, {\n      \"x\": 393.95751953125,\n      \"y\": 235.8660364151001,\n      \"z\": 10.475142002105713\n    }, {\n      \"x\": 401.84600830078125,\n      \"y\": 232.77019500732422,\n      \"z\": 16.760226488113403\n    }, {\n      \"x\": 375.70568084716797,\n      \"y\": 233.48456382751465,\n      \"z\": 8.234220147132874\n    }, {\n      \"x\": 388.17752838134766,\n      \"y\": 218.94717693328857,\n      \"z\": 6.810300946235657\n    }, {\n      \"x\": 381.64928436279297,\n      \"y\": 219.2656660079956,\n      \"z\": 6.711093783378601\n    }, {\n      \"x\": 394.4760513305664,\n      \"y\": 219.66821193695068,\n      \"z\": 9.173773527145386\n    }, {\n      \"x\": 398.8843536376953,\n      \"y\": 221.8837022781372,\n      \"z\": 12.03328251838684\n    }, {\n      \"x\": 406.5454864501953,\n      \"y\": 237.12156772613525,\n      \"z\": 19.7131085395813\n    }, {\n      \"x\": 383.87447357177734,\n      \"y\": 337.6932907104492,\n      \"z\": -8.631049990653992\n    }, {\n      \"x\": 401.2682342529297,\n      \"y\": 228.5916566848755,\n      \"z\": 18.359217643737793,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 422.0449447631836,\n      \"y\": 236.73934936523438,\n      \"z\": 51.16771221160889\n    }, {\n      \"x\": 412.69153594970703,\n      \"y\": 232.80198097229004,\n      \"z\": 27.52131938934326\n    }, {\n      \"x\": 387.3497772216797,\n      \"y\": 263.298397064209,\n      \"z\": -2.8609684109687805\n    }, {\n      \"x\": 364.5124053955078,\n      \"y\": 293.39221000671387,\n      \"z\": -22.397546768188477,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 363.62987518310547,\n      \"y\": 302.1291446685791,\n      \"z\": -19.643079042434692\n    }, {\n      \"x\": 373.2334518432617,\n      \"y\": 295.8647060394287,\n      \"z\": -18.125789165496826,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 378.83365631103516,\n      \"y\": 299.5177745819092,\n      \"z\": -13.153743743896484,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 369.91477966308594,\n      \"y\": 302.5704002380371,\n      \"z\": -16.65518283843994\n    }, {\n      \"x\": 374.9167251586914,\n      \"y\": 303.5416603088379,\n      \"z\": -11.963253021240234\n    }, {\n      \"x\": 387.58888244628906,\n      \"y\": 312.2716999053955,\n      \"z\": -4.680258631706238\n    }, {\n      \"x\": 360.6635284423828,\n      \"y\": 264.31986808776855,\n      \"z\": -35.94811677932739\n    }, {\n      \"x\": 361.04564666748047,\n      \"y\": 256.8225860595703,\n      \"z\": -37.278664112091064\n    }, {\n      \"x\": 408.3855438232422,\n      \"y\": 213.52088928222656,\n      \"z\": 15.756480693817139,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 373.2946014404297,\n      \"y\": 245.38101196289062,\n      \"z\": -1.9316278398036957\n    }, {\n      \"x\": 376.83860778808594,\n      \"y\": 264.3721103668213,\n      \"z\": -18.510947227478027\n    }, {\n      \"x\": 376.9546127319336,\n      \"y\": 261.0010528564453,\n      \"z\": -15.989909172058105\n    }, {\n      \"x\": 406.1498260498047,\n      \"y\": 263.5030174255371,\n      \"z\": 7.072908878326416\n    }, {\n      \"x\": 360.07205963134766,\n      \"y\": 248.3631706237793,\n      \"z\": -32.16656446456909\n    }, {\n      \"x\": 393.11119079589844,\n      \"y\": 205.10473251342773,\n      \"z\": 3.7786373496055603,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 402.12791442871094,\n      \"y\": 207.89000988006592,\n      \"z\": 9.383859634399414,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 410.8693313598633,\n      \"y\": 191.6182279586792,\n      \"z\": 41.27030849456787,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 364.9509811401367,\n      \"y\": 210.40483474731445,\n      \"z\": -3.758212625980377\n    }, {\n      \"x\": 375.94444274902344,\n      \"y\": 221.1331844329834,\n      \"z\": 8.368442058563232\n    }, {\n      \"x\": 392.1904754638672,\n      \"y\": 305.0360298156738,\n      \"z\": -1.752179116010666\n    }, {\n      \"x\": 419.50225830078125,\n      \"y\": 307.25592613220215,\n      \"z\": 58.96425247192383,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 372.0027160644531,\n      \"y\": 268.7212657928467,\n      \"z\": -16.631840467453003\n    }, {\n      \"x\": 366.1614227294922,\n      \"y\": 271.6237449645996,\n      \"z\": -18.219159841537476\n    }, {\n      \"x\": 385.00938415527344,\n      \"y\": 305.3863334655762,\n      \"z\": -2.567722797393799\n    }, {\n      \"x\": 381.99771881103516,\n      \"y\": 304.9723720550537,\n      \"z\": -4.575215280056\n    }, {\n      \"x\": 405.078125,\n      \"y\": 203.21216583251953,\n      \"z\": 13.713973760604858,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 377.13207244873047,\n      \"y\": 268.4710121154785,\n      \"z\": -15.266278982162476\n    }, {\n      \"x\": 380.9713363647461,\n      \"y\": 205.36980628967285,\n      \"z\": -0.7250899076461792,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 381.7788314819336,\n      \"y\": 198.9268398284912,\n      \"z\": -1.184653863310814,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 385.5204772949219,\n      \"y\": 172.1484375,\n      \"z\": 16.04826807975769,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 407.94189453125,\n      \"y\": 196.76236152648926,\n      \"z\": 25.723915100097656\n    }, {\n      \"x\": 383.03890228271484,\n      \"y\": 184.5157527923584,\n      \"z\": 7.393874526023865\n    }, {\n      \"x\": 411.61781311035156,\n      \"y\": 210.79241752624512,\n      \"z\": 22.315845489501953,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 414.30870056152344,\n      \"y\": 208.4643030166626,\n      \"z\": 37.021894454956055\n    }, {\n      \"x\": 364.28722381591797,\n      \"y\": 298.35777282714844,\n      \"z\": -21.86065673828125\n    }, {\n      \"x\": 371.3682556152344,\n      \"y\": 299.78848457336426,\n      \"z\": -17.834001779556274\n    }, {\n      \"x\": 376.88201904296875,\n      \"y\": 301.6696071624756,\n      \"z\": -13.153743743896484\n    }, {\n      \"x\": 370.2193832397461,\n      \"y\": 270.49095153808594,\n      \"z\": -15.569736957550049\n    }, {\n      \"x\": 383.5081100463867,\n      \"y\": 305.2726364135742,\n      \"z\": -3.673594295978546\n    }, {\n      \"x\": 380.73760986328125,\n      \"y\": 305.96869468688965,\n      \"z\": -8.660228252410889\n    }, {\n      \"x\": 381.2334442138672,\n      \"y\": 304.63574409484863,\n      \"z\": -4.820316135883331,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 368.1698989868164,\n      \"y\": 264.8884963989258,\n      \"z\": -25.653886795043945\n    }, {\n      \"x\": 373.5087203979492,\n      \"y\": 303.4233856201172,\n      \"z\": -10.95950722694397,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 368.4544372558594,\n      \"y\": 303.29601287841797,\n      \"z\": -14.169161319732666,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 362.76554107666016,\n      \"y\": 303.5735607147217,\n      \"z\": -16.911956071853638,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 366.60980224609375,\n      \"y\": 324.8870658874512,\n      \"z\": -15.616422891616821\n    }, {\n      \"x\": 365.7067108154297,\n      \"y\": 315.95678329467773,\n      \"z\": -20.903596878051758,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 365.0083923339844,\n      \"y\": 311.2232208251953,\n      \"z\": -21.066999435424805\n    }, {\n      \"x\": 364.1508102416992,\n      \"y\": 307.0583438873291,\n      \"z\": -18.907777070999146\n    }, {\n      \"x\": 363.37512969970703,\n      \"y\": 304.5721435546875,\n      \"z\": -17.42550015449524,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 374.580078125,\n      \"y\": 304.3059539794922,\n      \"z\": -11.40302300453186,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 375.55362701416016,\n      \"y\": 305.0998020172119,\n      \"z\": -12.861957550048828\n    }, {\n      \"x\": 377.2437286376953,\n      \"y\": 307.1674346923828,\n      \"z\": -14.215847253799438\n    }, {\n      \"x\": 378.68587493896484,\n      \"y\": 309.9015712738037,\n      \"z\": -13.223772048950195,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 383.8992691040039,\n      \"y\": 290.29629707336426,\n      \"z\": -9.97326910495758\n    }, {\n      \"x\": 423.3871841430664,\n      \"y\": 271.91688537597656,\n      \"z\": 74.37058925628662,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 377.68043518066406,\n      \"y\": 304.62209701538086,\n      \"z\": -7.603961229324341,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 379.00428771972656,\n      \"y\": 304.9314594268799,\n      \"z\": -8.57852816581726\n    }, {\n      \"x\": 364.00279998779297,\n      \"y\": 275.2813911437988,\n      \"z\": -19.25792098045349\n    }, {\n      \"x\": 374.68231201171875,\n      \"y\": 273.82555961608887,\n      \"z\": -11.28047227859497\n    }, {\n      \"x\": 365.0354766845703,\n      \"y\": 273.4548568725586,\n      \"z\": -18.791062831878662\n    }, {\n      \"x\": 380.61901092529297,\n      \"y\": 249.8848056793213,\n      \"z\": 0.15501167625188828\n    }, {\n      \"x\": 391.14158630371094,\n      \"y\": 254.7934627532959,\n      \"z\": 2.0906515419483185\n    }, {\n      \"x\": 378.1761169433594,\n      \"y\": 264.9612236022949,\n      \"z\": -12.605184316635132\n    }, {\n      \"x\": 400.9540557861328,\n      \"y\": 179.99592304229736,\n      \"z\": 27.82477855682373,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 398.0038833618164,\n      \"y\": 188.50656509399414,\n      \"z\": 16.094952821731567\n    }, {\n      \"x\": 394.8717498779297,\n      \"y\": 199.0359592437744,\n      \"z\": 6.226727366447449,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 382.10926055908203,\n      \"y\": 316.83926582336426,\n      \"z\": -8.946179747581482\n    }, {\n      \"x\": 366.51588439941406,\n      \"y\": 200.32583713531494,\n      \"z\": -5.24632453918457,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 367.4893569946289,\n      \"y\": 183.87210845947266,\n      \"z\": 1.9039081037044525\n    }, {\n      \"x\": 368.6243438720703,\n      \"y\": 168.8127565383911,\n      \"z\": 8.736093044281006,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 398.96175384521484,\n      \"y\": 234.9675178527832,\n      \"z\": 13.713973760604858\n    }, {\n      \"x\": 412.9645538330078,\n      \"y\": 242.23042488098145,\n      \"z\": 23.272905349731445\n    }, {\n      \"x\": 372.05257415771484,\n      \"y\": 231.41919136047363,\n      \"z\": 9.226294755935669\n    }, {\n      \"x\": 406.0722351074219,\n      \"y\": 223.58965873718262,\n      \"z\": 18.370890617370605\n    }, {\n      \"x\": 368.27442169189453,\n      \"y\": 240.2039337158203,\n      \"z\": -4.166713654994965\n    }, {\n      \"x\": 372.3575210571289,\n      \"y\": 260.66442489624023,\n      \"z\": -24.976940155029297\n    }, {\n      \"x\": 419.2244338989258,\n      \"y\": 247.9079246520996,\n      \"z\": 30.299127101898193\n    }, {\n      \"x\": 409.43885803222656,\n      \"y\": 246.60913467407227,\n      \"z\": 16.398411989212036\n    }, {\n      \"x\": 401.69139862060547,\n      \"y\": 248.76328468322754,\n      \"z\": 9.395531415939331\n    }, {\n      \"x\": 389.7608184814453,\n      \"y\": 247.56915092468262,\n      \"z\": 5.841569304466248\n    }, {\n      \"x\": 380.5461883544922,\n      \"y\": 244.55984115600586,\n      \"z\": 4.263003468513489\n    }, {\n      \"x\": 373.25817108154297,\n      \"y\": 240.80214500427246,\n      \"z\": 2.5356262922286987\n    }, {\n      \"x\": 358.77086639404297,\n      \"y\": 229.35615062713623,\n      \"z\": -10.387605428695679\n    }, {\n      \"x\": 419.5793914794922,\n      \"y\": 262.8478717803955,\n      \"z\": 26.5175724029541\n    }, {\n      \"x\": 410.8808898925781,\n      \"y\": 222.51372814178467,\n      \"z\": 22.199130058288574\n    }, {\n      \"x\": 358.45714569091797,\n      \"y\": 268.91467094421387,\n      \"z\": -33.17030906677246\n    }, {\n      \"x\": 373.4129333496094,\n      \"y\": 251.6385841369629,\n      \"z\": -5.771540403366089\n    }, {\n      \"x\": 422.5408172607422,\n      \"y\": 239.23919677734375,\n      \"z\": 74.04378890991211,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 367.8171920776367,\n      \"y\": 236.58040523529053,\n      \"z\": 1.820748895406723\n    }, {\n      \"x\": 378.51959228515625,\n      \"y\": 266.2532329559326,\n      \"z\": -5.74819803237915\n    }, {\n      \"x\": 403.3472442626953,\n      \"y\": 229.05112266540527,\n      \"z\": 19.689764976501465\n    }, {\n      \"x\": 372.34840393066406,\n      \"y\": 256.6451168060303,\n      \"z\": -21.872329711914062\n    }, {\n      \"x\": 422.54566192626953,\n      \"y\": 289.1587829589844,\n      \"z\": 68.67491245269775,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 371.9297409057617,\n      \"y\": 228.90116214752197,\n      \"z\": 11.432201862335205,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 366.21360778808594,\n      \"y\": 251.6158962249756,\n      \"z\": -28.19826364517212\n    }, {\n      \"x\": 409.1571807861328,\n      \"y\": 321.3156223297119,\n      \"z\": 20.2266526222229\n    }, {\n      \"x\": 408.52943420410156,\n      \"y\": 331.44238471984863,\n      \"z\": 31.09278917312622,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 424.2788314819336,\n      \"y\": 267.1992301940918,\n      \"z\": 50.467424392700195\n    }, {\n      \"x\": 415.60352325439453,\n      \"y\": 311.6528606414795,\n      \"z\": 30.579242706298828\n    }, {\n      \"x\": 418.12793731689453,\n      \"y\": 221.59927368164062,\n      \"z\": 46.26569747924805\n    }, {\n      \"x\": 385.68286895751953,\n      \"y\": 346.0184955596924,\n      \"z\": -5.70151150226593\n    }, {\n      \"x\": 357.82936096191406,\n      \"y\": 271.3758373260498,\n      \"z\": -24.836881160736084\n    }, {\n      \"x\": 379.588623046875,\n      \"y\": 257.5071716308594,\n      \"z\": -3.755294680595398\n    }, {\n      \"x\": 417.4592590332031,\n      \"y\": 234.71948146820068,\n      \"z\": 34.5475435256958\n    }, {\n      \"x\": 393.4684371948242,\n      \"y\": 231.58967971801758,\n      \"z\": 11.408859491348267,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 387.8864288330078,\n      \"y\": 232.14245796203613,\n      \"z\": 9.51808214187622,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 382.4981689453125,\n      \"y\": 307.5654888153076,\n      \"z\": -7.522260546684265,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 419.00169372558594,\n      \"y\": 277.8332805633545,\n      \"z\": 26.424202919006348\n    }, {\n      \"x\": 373.62953186035156,\n      \"y\": 357.6375102996826,\n      \"z\": -5.75986921787262,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 392.8708267211914,\n      \"y\": 347.72446632385254,\n      \"z\": 10.154176950454712,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 400.3953552246094,\n      \"y\": 341.0005187988281,\n      \"z\": 19.39797878265381,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 382.25440979003906,\n      \"y\": 231.66935920715332,\n      \"z\": 8.998700976371765,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 377.14550018310547,\n      \"y\": 230.4228687286377,\n      \"z\": 9.804032444953918,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 373.8358688354492,\n      \"y\": 229.64950561523438,\n      \"z\": 11.292144060134888,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 414.5794677734375,\n      \"y\": 221.67891025543213,\n      \"z\": 29.412097930908203\n    }, {\n      \"x\": 377.00672149658203,\n      \"y\": 225.66201210021973,\n      \"z\": 9.360517263412476,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 382.29530334472656,\n      \"y\": 224.8431158065796,\n      \"z\": 8.32175612449646,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 387.5133514404297,\n      \"y\": 224.49507236480713,\n      \"z\": 8.917000889778137,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 393.15906524658203,\n      \"y\": 225.24795055389404,\n      \"z\": 10.737749338150024,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 397.05554962158203,\n      \"y\": 226.55359268188477,\n      \"z\": 13.002015352249146,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 420.5299377441406,\n      \"y\": 221.014666557312,\n      \"z\": 65.40690422058105,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 397.06920623779297,\n      \"y\": 230.6661558151245,\n      \"z\": 13.807345628738403,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 377.94647216796875,\n      \"y\": 285.1647090911865,\n      \"z\": -13.305472135543823\n    }, {\n      \"x\": 372.1118927001953,\n      \"y\": 267.1267318725586,\n      \"z\": -18.83774757385254\n    }, {\n      \"x\": 364.9968719482422,\n      \"y\": 282.24411964416504,\n      \"z\": -19.818150997161865\n    }, {\n      \"x\": 401.973876953125,\n      \"y\": 331.20131492614746,\n      \"z\": 11.566424369812012\n    }, {\n      \"x\": 394.3083190917969,\n      \"y\": 338.86693954467773,\n      \"z\": 3.142542541027069\n    }, {\n      \"x\": 373.9820861816406,\n      \"y\": 351.4504623413086,\n      \"z\": -13.50388765335083\n    }, {\n      \"x\": 414.3888854980469,\n      \"y\": 321.24735832214355,\n      \"z\": 45.51872253417969,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 373.44234466552734,\n      \"y\": 227.33163356781006,\n      \"z\": 10.626870393753052,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 364.0731430053711,\n      \"y\": 240.31539916992188,\n      \"z\": -13.807345628738403\n    }, {\n      \"x\": 384.2658233642578,\n      \"y\": 353.3793067932129,\n      \"z\": 0.7385850697755814,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 423.20526123046875,\n      \"y\": 283.5176181793213,\n      \"z\": 47.152724266052246\n    }, {\n      \"x\": 369.42798614501953,\n      \"y\": 304.0898895263672,\n      \"z\": -14.647691249847412,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 370.63812255859375,\n      \"y\": 305.90051651000977,\n      \"z\": -16.211668252944946\n    }, {\n      \"x\": 371.91192626953125,\n      \"y\": 309.0167713165283,\n      \"z\": -17.84567356109619\n    }, {\n      \"x\": 373.0583953857422,\n      \"y\": 313.3545398712158,\n      \"z\": -17.378815412521362,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 375.39905548095703,\n      \"y\": 321.09289169311523,\n      \"z\": -13.118728399276733\n    }, {\n      \"x\": 379.2567825317383,\n      \"y\": 304.3582534790039,\n      \"z\": -7.924926280975342\n    }, {\n      \"x\": 381.18797302246094,\n      \"y\": 303.7031364440918,\n      \"z\": -7.843226194381714\n    }, {\n      \"x\": 383.0918502807617,\n      \"y\": 302.4884605407715,\n      \"z\": -7.6506465673446655,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 389.09461975097656,\n      \"y\": 297.1475315093994,\n      \"z\": -5.5497825145721436\n    }, {\n      \"x\": 411.6408920288086,\n      \"y\": 280.24898529052734,\n      \"z\": 12.02161192893982\n    }, {\n      \"x\": 363.3110809326172,\n      \"y\": 234.27620887756348,\n      \"z\": -6.775286793708801\n    }, {\n      \"x\": 366.0474395751953,\n      \"y\": 223.29872131347656,\n      \"z\": 6.827808618545532\n    }, {\n      \"x\": 370.34427642822266,\n      \"y\": 225.1457118988037,\n      \"z\": 9.558931589126587\n    }, {\n      \"x\": 377.5371551513672,\n      \"y\": 303.60079765319824,\n      \"z\": -7.358860373497009,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 412.9557800292969,\n      \"y\": 299.53579902648926,\n      \"z\": 19.39797878265381\n    }, {\n      \"x\": 360.0810241699219,\n      \"y\": 221.72012329101562,\n      \"z\": -2.153385728597641\n    }, {\n      \"x\": 379.82784271240234,\n      \"y\": 329.47723388671875,\n      \"z\": -10.48097848892212\n    }, {\n      \"x\": 359.08477783203125,\n      \"y\": 235.7911491394043,\n      \"z\": -18.079102039337158\n    }, {\n      \"x\": 369.6688461303711,\n      \"y\": 251.5407943725586,\n      \"z\": -14.962821006774902\n    }, {\n      \"x\": 369.5555114746094,\n      \"y\": 333.5307312011719,\n      \"z\": -15.67478060722351\n    }, {\n      \"x\": 394.0193176269531,\n      \"y\": 315.6973171234131,\n      \"z\": -0.9920747578144073\n    }, {\n      \"x\": 383.78997802734375,\n      \"y\": 272.7268695831299,\n      \"z\": -4.689012169837952\n    }, {\n      \"x\": 387.67765045166016,\n      \"y\": 323.6722755432129,\n      \"z\": -5.640236139297485\n    }, {\n      \"x\": 397.8769302368164,\n      \"y\": 272.1331214904785,\n      \"z\": -0.9395531564950943\n    }, {\n      \"x\": 389.87476348876953,\n      \"y\": 280.5630111694336,\n      \"z\": -4.29218202829361\n    }, {\n      \"x\": 403.83888244628906,\n      \"y\": 285.1167869567871,\n      \"z\": 3.0229100584983826\n    }, {\n      \"x\": 372.5467300415039,\n      \"y\": 343.1070327758789,\n      \"z\": -16.153310537338257\n    }, {\n      \"x\": 374.1112518310547,\n      \"y\": 256.3721466064453,\n      \"z\": -10.574349164962769\n    }, {\n      \"x\": 399.73785400390625,\n      \"y\": 321.77515983581543,\n      \"z\": 4.849494695663452\n    }, {\n      \"x\": 392.03365325927734,\n      \"y\": 330.56447982788086,\n      \"z\": -1.3407598435878754\n    }, {\n      \"x\": 398.59134674072266,\n      \"y\": 305.93902587890625,\n      \"z\": 1.517290621995926\n    }, {\n      \"x\": 417.95997619628906,\n      \"y\": 290.9716987609863,\n      \"z\": 26.89105987548828\n    }, {\n      \"x\": 406.04541778564453,\n      \"y\": 307.35154151916504,\n      \"z\": 8.666064143180847\n    }, {\n      \"x\": 420.75328826904297,\n      \"y\": 298.40752601623535,\n      \"z\": 41.78385257720947\n    }, {\n      \"x\": 395.4522705078125,\n      \"y\": 291.4153575897217,\n      \"z\": -2.1752697229385376\n    }, {\n      \"x\": 368.6452102661133,\n      \"y\": 245.8882999420166,\n      \"z\": -9.453888535499573\n    }, {\n      \"x\": 370.34900665283203,\n      \"y\": 263.56690406799316,\n      \"z\": -26.75100326538086\n    }, {\n      \"x\": 374.98477935791016,\n      \"y\": 266.6126346588135,\n      \"z\": -19.77146625518799\n    }, {\n      \"x\": 366.99840545654297,\n      \"y\": 258.12140464782715,\n      \"z\": -31.372904777526855\n    }, {\n      \"x\": 371.00616455078125,\n      \"y\": 217.63479709625244,\n      \"z\": 5.60522198677063\n    }, {\n      \"x\": 381.30577087402344,\n      \"y\": 214.14087295532227,\n      \"z\": 4.983716309070587\n    }, {\n      \"x\": 390.1496124267578,\n      \"y\": 213.38221549987793,\n      \"z\": 5.593550801277161\n    }, {\n      \"x\": 397.7696990966797,\n      \"y\": 214.3659782409668,\n      \"z\": 8.57852816581726\n    }, {\n      \"x\": 403.1652069091797,\n      \"y\": 217.65509605407715,\n      \"z\": 13.013685941696167\n    }, {\n      \"x\": 407.3551940917969,\n      \"y\": 230.72525024414062,\n      \"z\": 22.444231510162354\n    }, {\n      \"x\": 424.0876770019531,\n      \"y\": 251.7839241027832,\n      \"z\": 51.16771221160889\n    }, {\n      \"x\": 403.50196838378906,\n      \"y\": 239.88757610321045,\n      \"z\": 15.803166627883911\n    }, {\n      \"x\": 397.31719970703125,\n      \"y\": 241.49806022644043,\n      \"z\": 11.233787536621094\n    }, {\n      \"x\": 388.99425506591797,\n      \"y\": 241.4366912841797,\n      \"z\": 7.948269248008728\n    }, {\n      \"x\": 380.7804489135742,\n      \"y\": 239.78078842163086,\n      \"z\": 6.600214838981628\n    }, {\n      \"x\": 374.01336669921875,\n      \"y\": 237.11946487426758,\n      \"z\": 6.349278092384338\n    }, {\n      \"x\": 369.39125061035156,\n      \"y\": 234.35351371765137,\n      \"z\": 5.987462401390076\n    }, {\n      \"x\": 422.9730987548828,\n      \"y\": 255.76455116271973,\n      \"z\": 76.61150932312012,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 374.73915100097656,\n      \"y\": 269.24214363098145,\n      \"z\": -16.608498096466064\n    }, {\n      \"x\": 364.61681365966797,\n      \"y\": 245.71088790893555,\n      \"z\": -20.02823829650879\n    }, {\n      \"x\": 365.3834533691406,\n      \"y\": 263.34174156188965,\n      \"z\": -32.32996463775635\n    }, {\n      \"x\": 361.58252716064453,\n      \"y\": 267.8273677825928,\n      \"z\": -30.345816612243652\n    }, {\n      \"x\": 365.37208557128906,\n      \"y\": 265.0249671936035,\n      \"z\": -29.178667068481445\n    }, {\n      \"x\": 372.72605895996094,\n      \"y\": 272.05135345458984,\n      \"z\": -14.834434986114502\n    }, {\n      \"x\": 360.48614501953125,\n      \"y\": 268.34827423095703,\n      \"z\": -32.189905643463135\n    }, {\n      \"x\": 359.9516296386719,\n      \"y\": 270.8049201965332,\n      \"z\": -24.650139808654785\n    }, {\n      \"x\": 369.5049285888672,\n      \"y\": 229.01945114135742,\n      \"z\": 10.107489824295044\n    }, {\n      \"x\": 365.5447769165039,\n      \"y\": 230.24096488952637,\n      \"z\": 5.593550801277161\n    }, {\n      \"x\": 363.50669860839844,\n      \"y\": 230.6208372116089,\n      \"z\": 0.43622106313705444\n    }, {\n      \"x\": 399.3529510498047,\n      \"y\": 227.65677452087402,\n      \"z\": 15.35965085029602,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 402.5693130493164,\n      \"y\": 224.60190296173096,\n      \"z\": 15.931552648544312\n    }],\n    \"box\": {\n      \"xMin\": 277.8318977355957,\n      \"yMin\": 168.7741756439209,\n      \"xMax\": 424.2788314819336,\n      \"yMax\": 359.8348903656006,\n      \"width\": 146.4469337463379,\n      \"height\": 191.0607147216797\n    }\n  },\n  // Extracted from: https://github.com/tensorflow/tfjs-models/blob/a8f500809f5afe38feea27870c77e7ba03a6ece4/face-landmarks-detection/demos/shared/triangulation.js\n  // prettier-ignore\n  TRIANGULATION: [127, 34, 139, 11, 0, 37, 232, 231, 120, 72, 37, 39, 128, 121, 47, 232, 121, 128, 104, 69, 67, 175, 171, 148, 157, 154, 155, 118, 50, 101, 73, 39, 40, 9, 151, 108, 48, 115, 131, 194, 204, 211, 74, 40, 185, 80, 42, 183, 40, 92, 186, 230, 229, 118, 202, 212, 214, 83, 18, 17, 76, 61, 146, 160, 29, 30, 56, 157, 173, 106, 204, 194, 135, 214, 192, 203, 165, 98, 21, 71, 68, 51, 45, 4, 144, 24, 23, 77, 146, 91, 205, 50, 187, 201, 200, 18, 91, 106, 182, 90, 91, 181, 85, 84, 17, 206, 203, 36, 148, 171, 140, 92, 40, 39, 193, 189, 244, 159, 158, 28, 247, 246, 161, 236, 3, 196, 54, 68, 104, 193, 168, 8, 117, 228, 31, 189, 193, 55, 98, 97, 99, 126, 47, 100, 166, 79, 218, 155, 154, 26, 209, 49, 131, 135, 136, 150, 47, 126, 217, 223, 52, 53, 45, 51, 134, 211, 170, 140, 67, 69, 108, 43, 106, 91, 230, 119, 120, 226, 130, 247, 63, 53, 52, 238, 20, 242, 46, 70, 156, 78, 62, 96, 46, 53, 63, 143, 34, 227, 173, 155, 133, 123, 117, 111, 44, 125, 19, 236, 134, 51, 216, 206, 205, 154, 153, 22, 39, 37, 167, 200, 201, 208, 36, 142, 100, 57, 212, 202, 20, 60, 99, 28, 158, 157, 35, 226, 113, 160, 159, 27, 204, 202, 210, 113, 225, 46, 43, 202, 204, 62, 76, 77, 137, 123, 116, 41, 38, 72, 203, 129, 142, 64, 98, 240, 49, 102, 64, 41, 73, 74, 212, 216, 207, 42, 74, 184, 169, 170, 211, 170, 149, 176, 105, 66, 69, 122, 6, 168, 123, 147, 187, 96, 77, 90, 65, 55, 107, 89, 90, 180, 101, 100, 120, 63, 105, 104, 93, 137, 227, 15, 86, 85, 129, 102, 49, 14, 87, 86, 55, 8, 9, 100, 47, 121, 145, 23, 22, 88, 89, 179, 6, 122, 196, 88, 95, 96, 138, 172, 136, 215, 58, 172, 115, 48, 219, 42, 80, 81, 195, 3, 51, 43, 146, 61, 171, 175, 199, 81, 82, 38, 53, 46, 225, 144, 163, 110, 246, 33, 7, 52, 65, 66, 229, 228, 117, 34, 127, 234, 107, 108, 69, 109, 108, 151, 48, 64, 235, 62, 78, 191, 129, 209, 126, 111, 35, 143, 163, 161, 246, 117, 123, 50, 222, 65, 52, 19, 125, 141, 221, 55, 65, 3, 195, 197, 25, 7, 33, 220, 237, 44, 70, 71, 139, 122, 193, 245, 247, 130, 33, 71, 21, 162, 153, 158, 159, 170, 169, 150, 188, 174, 196, 216, 186, 92, 144, 160, 161, 2, 97, 167, 141, 125, 241, 164, 167, 37, 72, 38, 12, 145, 159, 160, 38, 82, 13, 63, 68, 71, 226, 35, 111, 158, 153, 154, 101, 50, 205, 206, 92, 165, 209, 198, 217, 165, 167, 97, 220, 115, 218, 133, 112, 243, 239, 238, 241, 214, 135, 169, 190, 173, 133, 171, 208, 32, 125, 44, 237, 86, 87, 178, 85, 86, 179, 84, 85, 180, 83, 84, 181, 201, 83, 182, 137, 93, 132, 76, 62, 183, 61, 76, 184, 57, 61, 185, 212, 57, 186, 214, 207, 187, 34, 143, 156, 79, 239, 237, 123, 137, 177, 44, 1, 4, 201, 194, 32, 64, 102, 129, 213, 215, 138, 59, 166, 219, 242, 99, 97, 2, 94, 141, 75, 59, 235, 24, 110, 228, 25, 130, 226, 23, 24, 229, 22, 23, 230, 26, 22, 231, 112, 26, 232, 189, 190, 243, 221, 56, 190, 28, 56, 221, 27, 28, 222, 29, 27, 223, 30, 29, 224, 247, 30, 225, 238, 79, 20, 166, 59, 75, 60, 75, 240, 147, 177, 215, 20, 79, 166, 187, 147, 213, 112, 233, 244, 233, 128, 245, 128, 114, 188, 114, 217, 174, 131, 115, 220, 217, 198, 236, 198, 131, 134, 177, 132, 58, 143, 35, 124, 110, 163, 7, 228, 110, 25, 356, 389, 368, 11, 302, 267, 452, 350, 349, 302, 303, 269, 357, 343, 277, 452, 453, 357, 333, 332, 297, 175, 152, 377, 384, 398, 382, 347, 348, 330, 303, 304, 270, 9, 336, 337, 278, 279, 360, 418, 262, 431, 304, 408, 409, 310, 415, 407, 270, 409, 410, 450, 348, 347, 422, 430, 434, 313, 314, 17, 306, 307, 375, 387, 388, 260, 286, 414, 398, 335, 406, 418, 364, 367, 416, 423, 358, 327, 251, 284, 298, 281, 5, 4, 373, 374, 253, 307, 320, 321, 425, 427, 411, 421, 313, 18, 321, 405, 406, 320, 404, 405, 315, 16, 17, 426, 425, 266, 377, 400, 369, 322, 391, 269, 417, 465, 464, 386, 257, 258, 466, 260, 388, 456, 399, 419, 284, 332, 333, 417, 285, 8, 346, 340, 261, 413, 441, 285, 327, 460, 328, 355, 371, 329, 392, 439, 438, 382, 341, 256, 429, 420, 360, 364, 394, 379, 277, 343, 437, 443, 444, 283, 275, 440, 363, 431, 262, 369, 297, 338, 337, 273, 375, 321, 450, 451, 349, 446, 342, 467, 293, 334, 282, 458, 461, 462, 276, 353, 383, 308, 324, 325, 276, 300, 293, 372, 345, 447, 382, 398, 362, 352, 345, 340, 274, 1, 19, 456, 248, 281, 436, 427, 425, 381, 256, 252, 269, 391, 393, 200, 199, 428, 266, 330, 329, 287, 273, 422, 250, 462, 328, 258, 286, 384, 265, 353, 342, 387, 259, 257, 424, 431, 430, 342, 353, 276, 273, 335, 424, 292, 325, 307, 366, 447, 345, 271, 303, 302, 423, 266, 371, 294, 455, 460, 279, 278, 294, 271, 272, 304, 432, 434, 427, 272, 407, 408, 394, 430, 431, 395, 369, 400, 334, 333, 299, 351, 417, 168, 352, 280, 411, 325, 319, 320, 295, 296, 336, 319, 403, 404, 330, 348, 349, 293, 298, 333, 323, 454, 447, 15, 16, 315, 358, 429, 279, 14, 15, 316, 285, 336, 9, 329, 349, 350, 374, 380, 252, 318, 402, 403, 6, 197, 419, 318, 319, 325, 367, 364, 365, 435, 367, 397, 344, 438, 439, 272, 271, 311, 195, 5, 281, 273, 287, 291, 396, 428, 199, 311, 271, 268, 283, 444, 445, 373, 254, 339, 263, 466, 249, 282, 334, 296, 449, 347, 346, 264, 447, 454, 336, 296, 299, 338, 10, 151, 278, 439, 455, 292, 407, 415, 358, 371, 355, 340, 345, 372, 390, 249, 466, 346, 347, 280, 442, 443, 282, 19, 94, 370, 441, 442, 295, 248, 419, 197, 263, 255, 359, 440, 275, 274, 300, 383, 368, 351, 412, 465, 263, 467, 466, 301, 368, 389, 380, 374, 386, 395, 378, 379, 412, 351, 419, 436, 426, 322, 373, 390, 388, 2, 164, 393, 370, 462, 461, 164, 0, 267, 302, 11, 12, 374, 373, 387, 268, 12, 13, 293, 300, 301, 446, 261, 340, 385, 384, 381, 330, 266, 425, 426, 423, 391, 429, 355, 437, 391, 327, 326, 440, 457, 438, 341, 382, 362, 459, 457, 461, 434, 430, 394, 414, 463, 362, 396, 369, 262, 354, 461, 457, 316, 403, 402, 315, 404, 403, 314, 405, 404, 313, 406, 405, 421, 418, 406, 366, 401, 361, 306, 408, 407, 291, 409, 408, 287, 410, 409, 432, 436, 410, 434, 416, 411, 264, 368, 383, 309, 438, 457, 352, 376, 401, 274, 275, 4, 421, 428, 262, 294, 327, 358, 433, 416, 367, 289, 455, 439, 462, 370, 326, 2, 326, 370, 305, 460, 455, 254, 449, 448, 255, 261, 446, 253, 450, 449, 252, 451, 450, 256, 452, 451, 341, 453, 452, 413, 464, 463, 441, 413, 414, 258, 442, 441, 257, 443, 442, 259, 444, 443, 260, 445, 444, 467, 342, 445, 459, 458, 250, 289, 392, 290, 290, 328, 460, 376, 433, 435, 250, 290, 392, 411, 416, 433, 341, 463, 464, 453, 464, 465, 357, 465, 412, 343, 412, 399, 360, 363, 440, 437, 399, 456, 420, 456, 363, 401, 435, 288, 372, 383, 353, 339, 255, 249, 448, 261, 255, 133, 243, 190, 133, 155, 112, 33, 246, 247, 33, 130, 25, 398, 384, 286, 362, 398, 414, 362, 463, 341, 263, 359, 467, 263, 249, 255, 466, 467, 260, 75, 60, 166, 238, 239, 79, 162, 127, 139, 72, 11, 37, 121, 232, 120, 73, 72, 39, 114, 128, 47, 233, 232, 128, 103, 104, 67, 152, 175, 148, 173, 157, 155, 119, 118, 101, 74, 73, 40, 107, 9, 108, 49, 48, 131, 32, 194, 211, 184, 74, 185, 191, 80, 183, 185, 40, 186, 119, 230, 118, 210, 202, 214, 84, 83, 17, 77, 76, 146, 161, 160, 30, 190, 56, 173, 182, 106, 194, 138, 135, 192, 129, 203, 98, 54, 21, 68, 5, 51, 4, 145, 144, 23, 90, 77, 91, 207, 205, 187, 83, 201, 18, 181, 91, 182, 180, 90, 181, 16, 85, 17, 205, 206, 36, 176, 148, 140, 165, 92, 39, 245, 193, 244, 27, 159, 28, 30, 247, 161, 174, 236, 196, 103, 54, 104, 55, 193, 8, 111, 117, 31, 221, 189, 55, 240, 98, 99, 142, 126, 100, 219, 166, 218, 112, 155, 26, 198, 209, 131, 169, 135, 150, 114, 47, 217, 224, 223, 53, 220, 45, 134, 32, 211, 140, 109, 67, 108, 146, 43, 91, 231, 230, 120, 113, 226, 247, 105, 63, 52, 241, 238, 242, 124, 46, 156, 95, 78, 96, 70, 46, 63, 116, 143, 227, 116, 123, 111, 1, 44, 19, 3, 236, 51, 207, 216, 205, 26, 154, 22, 165, 39, 167, 199, 200, 208, 101, 36, 100, 43, 57, 202, 242, 20, 99, 56, 28, 157, 124, 35, 113, 29, 160, 27, 211, 204, 210, 124, 113, 46, 106, 43, 204, 96, 62, 77, 227, 137, 116, 73, 41, 72, 36, 203, 142, 235, 64, 240, 48, 49, 64, 42, 41, 74, 214, 212, 207, 183, 42, 184, 210, 169, 211, 140, 170, 176, 104, 105, 69, 193, 122, 168, 50, 123, 187, 89, 96, 90, 66, 65, 107, 179, 89, 180, 119, 101, 120, 68, 63, 104, 234, 93, 227, 16, 15, 85, 209, 129, 49, 15, 14, 86, 107, 55, 9, 120, 100, 121, 153, 145, 22, 178, 88, 179, 197, 6, 196, 89, 88, 96, 135, 138, 136, 138, 215, 172, 218, 115, 219, 41, 42, 81, 5, 195, 51, 57, 43, 61, 208, 171, 199, 41, 81, 38, 224, 53, 225, 24, 144, 110, 105, 52, 66, 118, 229, 117, 227, 34, 234, 66, 107, 69, 10, 109, 151, 219, 48, 235, 183, 62, 191, 142, 129, 126, 116, 111, 143, 7, 163, 246, 118, 117, 50, 223, 222, 52, 94, 19, 141, 222, 221, 65, 196, 3, 197, 45, 220, 44, 156, 70, 139, 188, 122, 245, 139, 71, 162, 145, 153, 159, 149, 170, 150, 122, 188, 196, 206, 216, 92, 163, 144, 161, 164, 2, 167, 242, 141, 241, 0, 164, 37, 11, 72, 12, 144, 145, 160, 12, 38, 13, 70, 63, 71, 31, 226, 111, 157, 158, 154, 36, 101, 205, 203, 206, 165, 126, 209, 217, 98, 165, 97, 237, 220, 218, 237, 239, 241, 210, 214, 169, 140, 171, 32, 241, 125, 237, 179, 86, 178, 180, 85, 179, 181, 84, 180, 182, 83, 181, 194, 201, 182, 177, 137, 132, 184, 76, 183, 185, 61, 184, 186, 57, 185, 216, 212, 186, 192, 214, 187, 139, 34, 156, 218, 79, 237, 147, 123, 177, 45, 44, 4, 208, 201, 32, 98, 64, 129, 192, 213, 138, 235, 59, 219, 141, 242, 97, 97, 2, 141, 240, 75, 235, 229, 24, 228, 31, 25, 226, 230, 23, 229, 231, 22, 230, 232, 26, 231, 233, 112, 232, 244, 189, 243, 189, 221, 190, 222, 28, 221, 223, 27, 222, 224, 29, 223, 225, 30, 224, 113, 247, 225, 99, 60, 240, 213, 147, 215, 60, 20, 166, 192, 187, 213, 243, 112, 244, 244, 233, 245, 245, 128, 188, 188, 114, 174, 134, 131, 220, 174, 217, 236, 236, 198, 134, 215, 177, 58, 156, 143, 124, 25, 110, 7, 31, 228, 25, 264, 356, 368, 0, 11, 267, 451, 452, 349, 267, 302, 269, 350, 357, 277, 350, 452, 357, 299, 333, 297, 396, 175, 377, 381, 384, 382, 280, 347, 330, 269, 303, 270, 151, 9, 337, 344, 278, 360, 424, 418, 431, 270, 304, 409, 272, 310, 407, 322, 270, 410, 449, 450, 347, 432, 422, 434, 18, 313, 17, 291, 306, 375, 259, 387, 260, 424, 335, 418, 434, 364, 416, 391, 423, 327, 301, 251, 298, 275, 281, 4, 254, 373, 253, 375, 307, 321, 280, 425, 411, 200, 421, 18, 335, 321, 406, 321, 320, 405, 314, 315, 17, 423, 426, 266, 396, 377, 369, 270, 322, 269, 413, 417, 464, 385, 386, 258, 248, 456, 419, 298, 284, 333, 168, 417, 8, 448, 346, 261, 417, 413, 285, 326, 327, 328, 277, 355, 329, 309, 392, 438, 381, 382, 256, 279, 429, 360, 365, 364, 379, 355, 277, 437, 282, 443, 283, 281, 275, 363, 395, 431, 369, 299, 297, 337, 335, 273, 321, 348, 450, 349, 359, 446, 467, 283, 293, 282, 250, 458, 462, 300, 276, 383, 292, 308, 325, 283, 276, 293, 264, 372, 447, 346, 352, 340, 354, 274, 19, 363, 456, 281, 426, 436, 425, 380, 381, 252, 267, 269, 393, 421, 200, 428, 371, 266, 329, 432, 287, 422, 290, 250, 328, 385, 258, 384, 446, 265, 342, 386, 387, 257, 422, 424, 430, 445, 342, 276, 422, 273, 424, 306, 292, 307, 352, 366, 345, 268, 271, 302, 358, 423, 371, 327, 294, 460, 331, 279, 294, 303, 271, 304, 436, 432, 427, 304, 272, 408, 395, 394, 431, 378, 395, 400, 296, 334, 299, 6, 351, 168, 376, 352, 411, 307, 325, 320, 285, 295, 336, 320, 319, 404, 329, 330, 349, 334, 293, 333, 366, 323, 447, 316, 15, 315, 331, 358, 279, 317, 14, 316, 8, 285, 9, 277, 329, 350, 253, 374, 252, 319, 318, 403, 351, 6, 419, 324, 318, 325, 397, 367, 365, 288, 435, 397, 278, 344, 439, 310, 272, 311, 248, 195, 281, 375, 273, 291, 175, 396, 199, 312, 311, 268, 276, 283, 445, 390, 373, 339, 295, 282, 296, 448, 449, 346, 356, 264, 454, 337, 336, 299, 337, 338, 151, 294, 278, 455, 308, 292, 415, 429, 358, 355, 265, 340, 372, 388, 390, 466, 352, 346, 280, 295, 442, 282, 354, 19, 370, 285, 441, 295, 195, 248, 197, 457, 440, 274, 301, 300, 368, 417, 351, 465, 251, 301, 389, 385, 380, 386, 394, 395, 379, 399, 412, 419, 410, 436, 322, 387, 373, 388, 326, 2, 393, 354, 370, 461, 393, 164, 267, 268, 302, 12, 386, 374, 387, 312, 268, 13, 298, 293, 301, 265, 446, 340, 380, 385, 381, 280, 330, 425, 322, 426, 391, 420, 429, 437, 393, 391, 326, 344, 440, 438, 458, 459, 461, 364, 434, 394, 428, 396, 262, 274, 354, 457, 317, 316, 402, 316, 315, 403, 315, 314, 404, 314, 313, 405, 313, 421, 406, 323, 366, 361, 292, 306, 407, 306, 291, 408, 291, 287, 409, 287, 432, 410, 427, 434, 411, 372, 264, 383, 459, 309, 457, 366, 352, 401, 1, 274, 4, 418, 421, 262, 331, 294, 358, 435, 433, 367, 392, 289, 439, 328, 462, 326, 94, 2, 370, 289, 305, 455, 339, 254, 448, 359, 255, 446, 254, 253, 449, 253, 252, 450, 252, 256, 451, 256, 341, 452, 414, 413, 463, 286, 441, 414, 286, 258, 441, 258, 257, 442, 257, 259, 443, 259, 260, 444, 260, 467, 445, 309, 459, 250, 305, 289, 290, 305, 290, 460, 401, 376, 435, 309, 250, 392, 376, 411, 433, 453, 341, 464, 357, 453, 465, 343, 357, 412, 437, 343, 399, 344, 360, 440, 420, 437, 456, 360, 420, 363, 361, 401, 288, 265, 372, 353, 390, 339, 249, 339, 448, 255]\n};\nexport { Facemesh, FacemeshDatas };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "Line", "defaultLookAt", "Vector3", "<PERSON><PERSON><PERSON>", "forwardRef", "face", "FacemeshDatas", "SAMPLE_FACE", "width", "height", "depth", "verticalTri", "origin", "debug", "children", "props", "fref", "_meshRef$current3", "_meshRef$current3$geo", "_meshRef$current4", "outerRef", "useRef", "meshRef", "sightDir", "useState", "sightDirQuaternion", "Quaternion", "invalidate", "useEffect", "_meshRef$current", "current", "geometry", "setIndex", "TRIANGULATION", "a", "b", "c", "ab", "ac", "bboxSize", "_meshRef$current2", "_outerRef$current", "_geometry$boundingBox", "setFromPoints", "keypoints", "copy", "sub", "crossVectors", "normalize", "setFromUnitVectors", "sightDirQuaternionInverse", "clone", "invert", "computeBoundingBox", "center", "applyQuaternion", "setRotationFromQuaternion", "position", "getAttribute", "translate", "getX", "getY", "getZ", "boundingBox", "getSize", "scale", "x", "y", "z", "computeVertexNormals", "attributes", "needsUpdate", "api", "useMemo", "useImperativeHandle", "createElement", "ref", "Fragment", "args", "points", "color"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Facemesh.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from './Line.js';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst defaultLookAt = new THREE.Vector3(0, 0, -1);\nconst Facemesh = /*#__PURE__*/React.forwardRef(({\n  face = FacemeshDatas.SAMPLE_FACE,\n  width,\n  height,\n  depth = 1,\n  verticalTri = [159, 386, 200],\n  origin,\n  debug = false,\n  children,\n  ...props\n}, fref) => {\n  var _meshRef$current3, _meshRef$current3$geo, _meshRef$current4;\n\n  const outerRef = React.useRef(null);\n  const meshRef = React.useRef(null);\n  const [sightDir] = React.useState(() => new THREE.Vector3());\n  const [sightDirQuaternion] = React.useState(() => new THREE.Quaternion());\n  const {\n    invalidate\n  } = useThree();\n  React.useEffect(() => {\n    var _meshRef$current;\n\n    (_meshRef$current = meshRef.current) == null ? void 0 : _meshRef$current.geometry.setIndex(FacemeshDatas.TRIANGULATION);\n  }, []);\n  const [a] = React.useState(() => new THREE.Vector3());\n  const [b] = React.useState(() => new THREE.Vector3());\n  const [c] = React.useState(() => new THREE.Vector3());\n  const [ab] = React.useState(() => new THREE.Vector3());\n  const [ac] = React.useState(() => new THREE.Vector3());\n  const [bboxSize] = React.useState(() => new THREE.Vector3());\n  React.useEffect(() => {\n    var _meshRef$current2, _outerRef$current, _geometry$boundingBox;\n\n    const geometry = (_meshRef$current2 = meshRef.current) == null ? void 0 : _meshRef$current2.geometry;\n    if (!geometry) return;\n    geometry.setFromPoints(face.keypoints); //\n    // A. compute sightDir vector (normal to verticalTri)\n    //\n\n    a.copy(face.keypoints[verticalTri[0]]);\n    b.copy(face.keypoints[verticalTri[1]]);\n    c.copy(face.keypoints[verticalTri[2]]);\n    ab.copy(b).sub(a);\n    ac.copy(c).sub(a);\n    sightDir.crossVectors(ac, ab).normalize();\n    sightDirQuaternion.setFromUnitVectors(defaultLookAt, sightDir);\n    const sightDirQuaternionInverse = sightDirQuaternion.clone().invert(); //\n    // B. geometry (straightened)\n    //\n    // 1. center (before rotate back)\n\n    geometry.computeBoundingBox();\n    if (debug) invalidate(); // invalidate to force re-render for box3Helper (after .computeBoundingBox())\n\n    geometry.center(); // 2. rotate back + rotate outerRef (once 1.)\n\n    geometry.applyQuaternion(sightDirQuaternionInverse);\n    (_outerRef$current = outerRef.current) == null ? void 0 : _outerRef$current.setRotationFromQuaternion(sightDirQuaternion); // 3. origin: substract the geometry to that landmark coords (once 1.)\n\n    if (origin) {\n      const position = geometry.getAttribute('position');\n      geometry.translate(-position.getX(origin), -position.getY(origin), -position.getZ(origin));\n    } // 4. re-scale\n\n\n    (_geometry$boundingBox = geometry.boundingBox) == null ? void 0 : _geometry$boundingBox.getSize(bboxSize);\n    let scale = 1;\n    if (width) scale = width / bboxSize.x; // fit in width\n\n    if (height) scale = height / bboxSize.y; // fit in height\n\n    if (depth) scale = depth / bboxSize.z; // fit in depth\n\n    if (scale !== 1) geometry.scale(scale, scale, scale);\n    geometry.computeVertexNormals();\n    geometry.attributes.position.needsUpdate = true;\n  }, [face, width, height, depth, verticalTri, origin, debug, invalidate, sightDir, sightDirQuaternion, a, b, c, ab, ac, bboxSize]); //\n  // API\n  //\n\n  const api = React.useMemo(() => ({\n    meshRef,\n    outerRef\n  }), []);\n  React.useImperativeHandle(fref, () => api, [api]);\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    ref: outerRef\n  }, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: meshRef\n  }, children, debug ? /*#__PURE__*/React.createElement(React.Fragment, null, ((_meshRef$current3 = meshRef.current) == null ? void 0 : (_meshRef$current3$geo = _meshRef$current3.geometry) == null ? void 0 : _meshRef$current3$geo.boundingBox) && /*#__PURE__*/React.createElement(\"box3Helper\", {\n    args: [(_meshRef$current4 = meshRef.current) == null ? void 0 : _meshRef$current4.geometry.boundingBox]\n  }), /*#__PURE__*/React.createElement(Line, {\n    points: [[0, 0, 0], defaultLookAt],\n    color: 0x00ffff\n  })) : null)));\n});\nconst FacemeshDatas = {\n  // My face as default (captured with a 640x480 webcam)\n  // prettier-ignore\n  SAMPLE_FACE: {\n    \"keypoints\": [{\n      \"x\": 356.2804412841797,\n      \"y\": 295.1960563659668,\n      \"z\": -23.786449432373047,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 354.8859405517578,\n      \"y\": 264.69520568847656,\n      \"z\": -36.718435287475586\n    }, {\n      \"x\": 355.2180862426758,\n      \"y\": 275.3360366821289,\n      \"z\": -21.183712482452393\n    }, {\n      \"x\": 347.349853515625,\n      \"y\": 242.4400234222412,\n      \"z\": -25.093655586242676\n    }, {\n      \"x\": 354.40135955810547,\n      \"y\": 256.67933464050293,\n      \"z\": -38.23572635650635\n    }, {\n      \"x\": 353.7689971923828,\n      \"y\": 247.54886627197266,\n      \"z\": -34.5475435256958\n    }, {\n      \"x\": 352.1288299560547,\n      \"y\": 227.34312057495117,\n      \"z\": -13.095386028289795\n    }, {\n      \"x\": 303.5013198852539,\n      \"y\": 234.67002868652344,\n      \"z\": 12.500141859054565,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 351.09378814697266,\n      \"y\": 211.87547206878662,\n      \"z\": -6.413471698760986\n    }, {\n      \"x\": 350.7115936279297,\n      \"y\": 202.1251630783081,\n      \"z\": -6.413471698760986\n    }, {\n      \"x\": 348.33667755126953,\n      \"y\": 168.7741756439209,\n      \"z\": 6.483500003814697,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 356.4806365966797,\n      \"y\": 299.2995357513428,\n      \"z\": -23.144519329071045\n    }, {\n      \"x\": 356.5511703491211,\n      \"y\": 302.66146659851074,\n      \"z\": -21.020312309265137\n    }, {\n      \"x\": 356.6239547729492,\n      \"y\": 304.1536331176758,\n      \"z\": -18.137459754943848,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 356.5807342529297,\n      \"y\": 305.1840591430664,\n      \"z\": -18.767719268798828,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 356.8241500854492,\n      \"y\": 308.25711250305176,\n      \"z\": -20.16829490661621\n    }, {\n      \"x\": 357.113037109375,\n      \"y\": 312.26277351379395,\n      \"z\": -22.10575819015503\n    }, {\n      \"x\": 357.34962463378906,\n      \"y\": 317.1123218536377,\n      \"z\": -21.837315559387207,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 357.6658630371094,\n      \"y\": 325.51036834716797,\n      \"z\": -16.27002477645874\n    }, {\n      \"x\": 355.0201416015625,\n      \"y\": 269.36279296875,\n      \"z\": -33.73054027557373\n    }, {\n      \"x\": 348.5237503051758,\n      \"y\": 270.33411026000977,\n      \"z\": -24.93025302886963\n    }, {\n      \"x\": 279.97331619262695,\n      \"y\": 213.24176788330078,\n      \"z\": 47.759642601013184,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 322.66529083251953,\n      \"y\": 238.5027265548706,\n      \"z\": 5.535193085670471\n    }, {\n      \"x\": 316.0983657836914,\n      \"y\": 239.94489669799805,\n      \"z\": 5.777376294136047\n    }, {\n      \"x\": 309.9431610107422,\n      \"y\": 240.24518966674805,\n      \"z\": 7.510589361190796\n    }, {\n      \"x\": 301.31994247436523,\n      \"y\": 237.86138534545898,\n      \"z\": 13.118728399276733\n    }, {\n      \"x\": 328.14266204833984,\n      \"y\": 235.80496788024902,\n      \"z\": 6.646900177001953\n    }, {\n      \"x\": 313.7326431274414,\n      \"y\": 222.11161136627197,\n      \"z\": 3.9887237548828125\n    }, {\n      \"x\": 320.45196533203125,\n      \"y\": 221.87729358673096,\n      \"z\": 4.601476192474365\n    }, {\n      \"x\": 307.35679626464844,\n      \"y\": 223.63793849945068,\n      \"z\": 5.932023525238037\n    }, {\n      \"x\": 303.0031204223633,\n      \"y\": 226.3743782043457,\n      \"z\": 8.479321002960205\n    }, {\n      \"x\": 296.80023193359375,\n      \"y\": 242.94299125671387,\n      \"z\": 15.931552648544312\n    }, {\n      \"x\": 332.2352981567383,\n      \"y\": 340.77341079711914,\n      \"z\": -10.165848731994629\n    }, {\n      \"x\": 301.38587951660156,\n      \"y\": 233.46447944641113,\n      \"z\": 14.764405488967896,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 279.0147018432617,\n      \"y\": 244.37155723571777,\n      \"z\": 45.77549457550049\n    }, {\n      \"x\": 289.60548400878906,\n      \"y\": 239.1807460784912,\n      \"z\": 23.191204071044922\n    }, {\n      \"x\": 320.32257080078125,\n      \"y\": 267.1292781829834,\n      \"z\": -4.954537749290466\n    }, {\n      \"x\": 347.64583587646484,\n      \"y\": 294.4955062866211,\n      \"z\": -23.062820434570312,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 349.28138732910156,\n      \"y\": 303.1095886230469,\n      \"z\": -20.238323211669922\n    }, {\n      \"x\": 338.9453125,\n      \"y\": 298.19186210632324,\n      \"z\": -19.456336498260498,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 333.36788177490234,\n      \"y\": 302.6706790924072,\n      \"z\": -14.776077270507812,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 342.89188385009766,\n      \"y\": 304.3561363220215,\n      \"z\": -17.752301692962646\n    }, {\n      \"x\": 337.7375030517578,\n      \"y\": 306.0098361968994,\n      \"z\": -13.410515785217285\n    }, {\n      \"x\": 325.6159210205078,\n      \"y\": 316.22995376586914,\n      \"z\": -6.681914925575256\n    }, {\n      \"x\": 349.0104675292969,\n      \"y\": 264.9818515777588,\n      \"z\": -36.274919509887695\n    }, {\n      \"x\": 347.7138900756836,\n      \"y\": 257.5664806365967,\n      \"z\": -37.67549514770508\n    }, {\n      \"x\": 291.79357528686523,\n      \"y\": 218.88171672821045,\n      \"z\": 11.578094959259033,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 332.2689437866211,\n      \"y\": 247.56946563720703,\n      \"z\": -3.3730539679527283\n    }, {\n      \"x\": 332.0074462890625,\n      \"y\": 267.1201229095459,\n      \"z\": -19.969879388809204\n    }, {\n      \"x\": 331.27952575683594,\n      \"y\": 263.6967658996582,\n      \"z\": -17.47218608856201\n    }, {\n      \"x\": 301.04373931884766,\n      \"y\": 269.56552505493164,\n      \"z\": 3.61815482378006\n    }, {\n      \"x\": 347.4863815307617,\n      \"y\": 249.0706443786621,\n      \"z\": -32.633421421051025\n    }, {\n      \"x\": 307.26118087768555,\n      \"y\": 208.2646894454956,\n      \"z\": 1.1591226607561111,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 297.91919708251953,\n      \"y\": 212.22604751586914,\n      \"z\": 5.914516448974609,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 285.1651382446289,\n      \"y\": 197.98450469970703,\n      \"z\": 36.391637325286865,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 337.04097747802734,\n      \"y\": 211.25229835510254,\n      \"z\": -4.548954665660858\n    }, {\n      \"x\": 326.5912628173828,\n      \"y\": 223.16698551177979,\n      \"z\": 6.670243740081787\n    }, {\n      \"x\": 320.05664825439453,\n      \"y\": 309.5834255218506,\n      \"z\": -4.055835008621216\n    }, {\n      \"x\": 289.6866226196289,\n      \"y\": 314.617395401001,\n      \"z\": 53.875489234924316,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 337.4256896972656,\n      \"y\": 270.8755302429199,\n      \"z\": -17.67060160636902\n    }, {\n      \"x\": 343.69922637939453,\n      \"y\": 273.0000400543213,\n      \"z\": -18.756048679351807\n    }, {\n      \"x\": 327.4242401123047,\n      \"y\": 309.22399520874023,\n      \"z\": -4.703601002693176,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 330.37220001220703,\n      \"y\": 308.3323001861572,\n      \"z\": -6.442649960517883\n    }, {\n      \"x\": 293.87027740478516,\n      \"y\": 207.7961826324463,\n      \"z\": 9.821539521217346,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 332.11437225341797,\n      \"y\": 271.22812271118164,\n      \"z\": -16.64351224899292\n    }, {\n      \"x\": 320.1197814941406,\n      \"y\": 207.40366458892822,\n      \"z\": -2.48164564371109,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 318.59575271606445,\n      \"y\": 201.07443809509277,\n      \"z\": -3.110446035861969,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 310.72303771972656,\n      \"y\": 175.75075149536133,\n      \"z\": 13.328815698623657,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 289.67578887939453,\n      \"y\": 202.29835510253906,\n      \"z\": 21.370456218719482\n    }, {\n      \"x\": 315.30879974365234,\n      \"y\": 187.35260009765625,\n      \"z\": 5.0304025411605835\n    }, {\n      \"x\": 287.8936767578125,\n      \"y\": 216.54793739318848,\n      \"z\": 17.81065821647644,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 283.9391899108887,\n      \"y\": 215.01142501831055,\n      \"z\": 32.04984903335571\n    }, {\n      \"x\": 348.35330963134766,\n      \"y\": 299.4155788421631,\n      \"z\": -22.47924566268921\n    }, {\n      \"x\": 341.1790466308594,\n      \"y\": 301.8221855163574,\n      \"z\": -18.977805376052856\n    }, {\n      \"x\": 335.69713592529297,\n      \"y\": 304.4266891479492,\n      \"z\": -14.682706594467163\n    }, {\n      \"x\": 339.4615173339844,\n      \"y\": 272.3654365539551,\n      \"z\": -16.38674020767212\n    }, {\n      \"x\": 328.99600982666016,\n      \"y\": 308.86685371398926,\n      \"z\": -5.616893768310547\n    }, {\n      \"x\": 332.00313568115234,\n      \"y\": 309.1875743865967,\n      \"z\": -10.335084199905396\n    }, {\n      \"x\": 331.0068130493164,\n      \"y\": 307.9274368286133,\n      \"z\": -6.681914925575256,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 341.13792419433594,\n      \"y\": 266.4876937866211,\n      \"z\": -26.56425952911377\n    }, {\n      \"x\": 339.02950286865234,\n      \"y\": 305.6663703918457,\n      \"z\": -12.33674168586731,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 344.22935485839844,\n      \"y\": 304.9452781677246,\n      \"z\": -15.161235332489014,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 350.1844024658203,\n      \"y\": 304.374303817749,\n      \"z\": -17.5305438041687,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 348.52630615234375,\n      \"y\": 325.9562301635742,\n      \"z\": -16.164982318878174\n    }, {\n      \"x\": 348.6581802368164,\n      \"y\": 317.1624183654785,\n      \"z\": -21.510512828826904,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 348.9766311645508,\n      \"y\": 312.1923065185547,\n      \"z\": -21.708929538726807\n    }, {\n      \"x\": 349.2427444458008,\n      \"y\": 308.0660820007324,\n      \"z\": -19.643079042434692\n    }, {\n      \"x\": 349.67491149902344,\n      \"y\": 305.42747497558594,\n      \"z\": -18.16080331802368,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 337.95589447021484,\n      \"y\": 306.6535949707031,\n      \"z\": -12.803598642349243,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 337.06878662109375,\n      \"y\": 307.63169288635254,\n      \"z\": -14.274203777313232\n    }, {\n      \"x\": 335.77449798583984,\n      \"y\": 309.8449516296387,\n      \"z\": -15.698124170303345\n    }, {\n      \"x\": 334.6099090576172,\n      \"y\": 312.7997016906738,\n      \"z\": -14.764405488967896,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 327.2330856323242,\n      \"y\": 293.80866050720215,\n      \"z\": -11.864047050476074\n    }, {\n      \"x\": 280.97679138183594,\n      \"y\": 279.79928970336914,\n      \"z\": 68.90834331512451,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 355.13843536376953,\n      \"y\": 271.7875671386719,\n      \"z\": -25.350427627563477\n    }, {\n      \"x\": 334.7235870361328,\n      \"y\": 307.4656391143799,\n      \"z\": -9.302158951759338,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 333.5293960571289,\n      \"y\": 307.89782524108887,\n      \"z\": -10.200862884521484\n    }, {\n      \"x\": 346.29688262939453,\n      \"y\": 276.4256286621094,\n      \"z\": -19.748122692108154\n    }, {\n      \"x\": 335.16246795654297,\n      \"y\": 276.22097969055176,\n      \"z\": -12.313398122787476\n    }, {\n      \"x\": 345.09132385253906,\n      \"y\": 274.7082996368408,\n      \"z\": -19.304605722427368\n    }, {\n      \"x\": 325.4267883300781,\n      \"y\": 252.95130729675293,\n      \"z\": -1.6661019623279572\n    }, {\n      \"x\": 315.347843170166,\n      \"y\": 259.05200958251953,\n      \"z\": -0.25604281574487686\n    }, {\n      \"x\": 330.44933319091797,\n      \"y\": 267.7570152282715,\n      \"z\": -14.017432928085327\n    }, {\n      \"x\": 294.96768951416016,\n      \"y\": 185.26001930236816,\n      \"z\": 23.903164863586426,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 299.63531494140625,\n      \"y\": 192.7913761138916,\n      \"z\": 12.640198469161987\n    }, {\n      \"x\": 304.5452117919922,\n      \"y\": 202.4142837524414,\n      \"z\": 3.244667649269104,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 331.6915512084961,\n      \"y\": 320.0467872619629,\n      \"z\": -10.632705688476562\n    }, {\n      \"x\": 334.5911407470703,\n      \"y\": 201.27566814422607,\n      \"z\": -6.133356094360352,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 331.4815902709961,\n      \"y\": 185.44180870056152,\n      \"z\": 0.6627205014228821\n    }, {\n      \"x\": 328.05816650390625,\n      \"y\": 170.8385467529297,\n      \"z\": 7.358860373497009,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 304.49764251708984,\n      \"y\": 239.76297855377197,\n      \"z\": 10.387605428695679\n    }, {\n      \"x\": 290.6382179260254,\n      \"y\": 248.85257720947266,\n      \"z\": 19.03616428375244\n    }, {\n      \"x\": 331.5682601928711,\n      \"y\": 233.20727348327637,\n      \"z\": 7.837390303611755\n    }, {\n      \"x\": 295.5115509033203,\n      \"y\": 228.9834451675415,\n      \"z\": 14.41426157951355\n    }, {\n      \"x\": 336.94332122802734,\n      \"y\": 241.8259334564209,\n      \"z\": -5.27842104434967\n    }, {\n      \"x\": 336.2792205810547,\n      \"y\": 262.7049922943115,\n      \"z\": -26.12074375152588\n    }, {\n      \"x\": 284.4102478027344,\n      \"y\": 255.3262710571289,\n      \"z\": 25.467140674591064\n    }, {\n      \"x\": 295.1420593261719,\n      \"y\": 253.02655220031738,\n      \"z\": 12.430112361907959\n    }, {\n      \"x\": 303.5196113586426,\n      \"y\": 254.20703887939453,\n      \"z\": 6.139191389083862\n    }, {\n      \"x\": 315.73450088500977,\n      \"y\": 251.64799690246582,\n      \"z\": 3.3788898587226868\n    }, {\n      \"x\": 324.69661712646484,\n      \"y\": 247.56494522094727,\n      \"z\": 2.3328344523906708\n    }, {\n      \"x\": 331.57970428466797,\n      \"y\": 243.02241325378418,\n      \"z\": 1.1423448473215103\n    }, {\n      \"x\": 345.6210708618164,\n      \"y\": 229.9976634979248,\n      \"z\": -10.825285911560059\n    }, {\n      \"x\": 286.26644134521484,\n      \"y\": 270.37991523742676,\n      \"z\": 21.708929538726807\n    }, {\n      \"x\": 290.2525520324707,\n      \"y\": 228.4921360015869,\n      \"z\": 17.71728754043579\n    }, {\n      \"x\": 351.65367126464844,\n      \"y\": 269.3400764465332,\n      \"z\": -33.450424671173096\n    }, {\n      \"x\": 333.1378936767578,\n      \"y\": 253.88388633728027,\n      \"z\": -7.230473756790161\n    }, {\n      \"x\": 277.8318977355957,\n      \"y\": 246.95331573486328,\n      \"z\": 68.20805549621582,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 336.6680908203125,\n      \"y\": 238.10003757476807,\n      \"z\": 0.7688578963279724\n    }, {\n      \"x\": 329.95800018310547,\n      \"y\": 269.18323516845703,\n      \"z\": -7.207130789756775\n    }, {\n      \"x\": 299.17491912841797,\n      \"y\": 234.13324356079102,\n      \"z\": 15.95489501953125\n    }, {\n      \"x\": 335.61729431152344,\n      \"y\": 258.71752738952637,\n      \"z\": -23.016133308410645\n    }, {\n      \"x\": 284.1079330444336,\n      \"y\": 297.0343494415283,\n      \"z\": 63.25934886932373,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 331.44542694091797,\n      \"y\": 230.6892442703247,\n      \"z\": 9.92658257484436,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 341.41536712646484,\n      \"y\": 253.01264762878418,\n      \"z\": -29.038610458374023\n    }, {\n      \"x\": 303.5472869873047,\n      \"y\": 327.5896739959717,\n      \"z\": 16.725212335586548\n    }, {\n      \"x\": 304.7756576538086,\n      \"y\": 337.4389457702637,\n      \"z\": 27.38126277923584,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 280.80501556396484,\n      \"y\": 275.32050132751465,\n      \"z\": 45.0752067565918\n    }, {\n      \"x\": 295.43582916259766,\n      \"y\": 318.4501647949219,\n      \"z\": 26.2608003616333\n    }, {\n      \"x\": 281.4303207397461,\n      \"y\": 228.7355661392212,\n      \"z\": 40.94350814819336\n    }, {\n      \"x\": 331.2549591064453,\n      \"y\": 349.4216537475586,\n      \"z\": -7.376367449760437\n    }, {\n      \"x\": 352.4247741699219,\n      \"y\": 271.7330074310303,\n      \"z\": -24.953596591949463\n    }, {\n      \"x\": 327.5672912597656,\n      \"y\": 260.41900634765625,\n      \"z\": -5.456410646438599\n    }, {\n      \"x\": 284.5432472229004,\n      \"y\": 241.7647933959961,\n      \"z\": 29.668869972229004\n    }, {\n      \"x\": 310,\n      \"y\": 235.66174507141113,\n      \"z\": 8.502663969993591,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 315.7071113586426,\n      \"y\": 235.7572603225708,\n      \"z\": 6.938687562942505,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 330.41088104248047,\n      \"y\": 311.04143142700195,\n      \"z\": -9.325502514839172,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 288.5377502441406,\n      \"y\": 285.31983375549316,\n      \"z\": 21.837315559387207\n    }, {\n      \"x\": 344.55039978027344,\n      \"y\": 359.4300842285156,\n      \"z\": -6.705257892608643,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 323.41880798339844,\n      \"y\": 351.67362213134766,\n      \"z\": 7.802375555038452,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 314.64088439941406,\n      \"y\": 346.11894607543945,\n      \"z\": 16.36339783668518,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 349.4945526123047,\n      \"y\": 184.8434829711914,\n      \"z\": -0.21847527474164963\n    }, {\n      \"x\": 359.24694061279297,\n      \"y\": 359.8348903656006,\n      \"z\": -8.403456211090088,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 321.26182556152344,\n      \"y\": 234.64492321014404,\n      \"z\": 6.90950870513916,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 326.318359375,\n      \"y\": 232.90250301361084,\n      \"z\": 8.029969334602356,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 329.6211624145508,\n      \"y\": 231.6195774078369,\n      \"z\": 9.722331762313843,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 285.9398078918457,\n      \"y\": 228.2351303100586,\n      \"z\": 24.650139808654785\n    }, {\n      \"x\": 325.79288482666016,\n      \"y\": 227.88007736206055,\n      \"z\": 7.469738721847534,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 320.1699447631836,\n      \"y\": 227.5934886932373,\n      \"z\": 6.168370842933655,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 314.85408782958984,\n      \"y\": 227.85282611846924,\n      \"z\": 6.2675780057907104,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 309.3084907531738,\n      \"y\": 229.1516876220703,\n      \"z\": 7.7031683921813965,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 305.5621337890625,\n      \"y\": 230.92366218566895,\n      \"z\": 9.722331762313843,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 277.8681945800781,\n      \"y\": 228.5354232788086,\n      \"z\": 59.71122741699219,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 306.1444664001465,\n      \"y\": 235.1954698562622,\n      \"z\": 10.603528022766113,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 355.4478454589844,\n      \"y\": 281.96210861206055,\n      \"z\": -20.565123558044434\n    }, {\n      \"x\": 333.02661895751953,\n      \"y\": 288.0105400085449,\n      \"z\": -14.72939133644104\n    }, {\n      \"x\": 337.15728759765625,\n      \"y\": 269.2059516906738,\n      \"z\": -19.8414945602417\n    }, {\n      \"x\": 345.9898376464844,\n      \"y\": 283.5453128814697,\n      \"z\": -20.4834246635437\n    }, {\n      \"x\": 351.48963928222656,\n      \"y\": 219.98916149139404,\n      \"z\": -7.0378947257995605\n    }, {\n      \"x\": 312.39574432373047,\n      \"y\": 336.50628089904785,\n      \"z\": 8.671900033950806\n    }, {\n      \"x\": 321.32152557373047,\n      \"y\": 343.1755256652832,\n      \"z\": 0.9067271649837494\n    }, {\n      \"x\": 343.78379821777344,\n      \"y\": 353.2975959777832,\n      \"z\": -14.355905055999756\n    }, {\n      \"x\": 296.8791389465332,\n      \"y\": 327.91497230529785,\n      \"z\": 41.01353645324707,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 329.6939468383789,\n      \"y\": 229.27897453308105,\n      \"z\": 8.934508562088013,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 341.6905212402344,\n      \"y\": 241.4073657989502,\n      \"z\": -14.589333534240723\n    }, {\n      \"x\": 359.03079986572266,\n      \"y\": 353.48859786987305,\n      \"z\": -15.803166627883911\n    }, {\n      \"x\": 333.1861877441406,\n      \"y\": 356.43213272094727,\n      \"z\": -1.0234417766332626,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 283.97483825683594,\n      \"y\": 291.4318656921387,\n      \"z\": 41.94725513458252\n    }, {\n      \"x\": 343.33770751953125,\n      \"y\": 305.830135345459,\n      \"z\": -15.756480693817139,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 342.40283966064453,\n      \"y\": 307.7453899383545,\n      \"z\": -17.4021577835083\n    }, {\n      \"x\": 341.53621673583984,\n      \"y\": 311.0595703125,\n      \"z\": -19.047834873199463\n    }, {\n      \"x\": 340.9107208251953,\n      \"y\": 315.4837703704834,\n      \"z\": -18.5576331615448,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 339.1478729248047,\n      \"y\": 323.42233657836914,\n      \"z\": -14.367576837539673\n    }, {\n      \"x\": 333.3201599121094,\n      \"y\": 307.4406337738037,\n      \"z\": -9.617288708686829\n    }, {\n      \"x\": 331.2411117553711,\n      \"y\": 306.9811820983887,\n      \"z\": -9.669809937477112\n    }, {\n      \"x\": 329.23255920410156,\n      \"y\": 306.0508346557617,\n      \"z\": -9.582273960113525,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 322.4586486816406,\n      \"y\": 301.33323669433594,\n      \"z\": -7.720675468444824\n    }, {\n      \"x\": 297.1712112426758,\n      \"y\": 286.9552803039551,\n      \"z\": 8.240055441856384\n    }, {\n      \"x\": 341.3060760498047,\n      \"y\": 235.4432201385498,\n      \"z\": -7.504753470420837\n    }, {\n      \"x\": 336.9318389892578,\n      \"y\": 224.3451976776123,\n      \"z\": 5.829898118972778\n    }, {\n      \"x\": 332.65323638916016,\n      \"y\": 226.70403957366943,\n      \"z\": 8.105834126472473\n    }, {\n      \"x\": 334.67357635498047,\n      \"y\": 306.4397621154785,\n      \"z\": -8.981193900108337,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 297.4601936340332,\n      \"y\": 306.29210472106934,\n      \"z\": 15.476365089416504\n    }, {\n      \"x\": 342.9119110107422,\n      \"y\": 222.37077713012695,\n      \"z\": -2.754466235637665\n    }, {\n      \"x\": 335.4629898071289,\n      \"y\": 332.20250129699707,\n      \"z\": -11.823196411132812\n    }, {\n      \"x\": 353.2412338256836,\n      \"y\": 240.56339263916016,\n      \"z\": -27.147831916809082\n    }, {\n      \"x\": 346.3080596923828,\n      \"y\": 236.41446590423584,\n      \"z\": -18.452589511871338\n    }, {\n      \"x\": 352.6475143432617,\n      \"y\": 234.1420555114746,\n      \"z\": -19.748122692108154\n    }, {\n      \"x\": 337.3209762573242,\n      \"y\": 253.39937210083008,\n      \"z\": -16.024924516677856\n    }, {\n      \"x\": 358.6122131347656,\n      \"y\": 344.90861892700195,\n      \"z\": -18.592647314071655\n    }, {\n      \"x\": 358.1117248535156,\n      \"y\": 334.64990615844727,\n      \"z\": -17.49552845954895\n    }, {\n      \"x\": 346.4450454711914,\n      \"y\": 335.0321102142334,\n      \"z\": -16.32838249206543\n    }, {\n      \"x\": 319.17640686035156,\n      \"y\": 320.2833938598633,\n      \"z\": -3.276764452457428\n    }, {\n      \"x\": 325.2540588378906,\n      \"y\": 276.2369728088379,\n      \"z\": -6.460157036781311\n    }, {\n      \"x\": 326.7214584350586,\n      \"y\": 327.3939514160156,\n      \"z\": -7.417217493057251\n    }, {\n      \"x\": 310.7190132141113,\n      \"y\": 277.2265148162842,\n      \"z\": -3.5452082753181458\n    }, {\n      \"x\": 319.78355407714844,\n      \"y\": 284.8238182067871,\n      \"z\": -6.4543211460113525\n    }, {\n      \"x\": 305.773983001709,\n      \"y\": 290.83580017089844,\n      \"z\": 0.06907138042151928\n    }, {\n      \"x\": 344.4001770019531,\n      \"y\": 344.85408782958984,\n      \"z\": -16.946970224380493\n    }, {\n      \"x\": 333.1879425048828,\n      \"y\": 258.74256134033203,\n      \"z\": -11.90489649772644\n    }, {\n      \"x\": 313.80598068237305,\n      \"y\": 327.08919525146484,\n      \"z\": 2.2277912497520447\n    }, {\n      \"x\": 322.9637908935547,\n      \"y\": 334.6819496154785,\n      \"z\": -3.3643004298210144\n    }, {\n      \"x\": 313.4055519104004,\n      \"y\": 311.2166690826416,\n      \"z\": -1.1175429821014404\n    }, {\n      \"x\": 291.0865783691406,\n      \"y\": 298.2831001281738,\n      \"z\": 22.467575073242188\n    }, {\n      \"x\": 305.6580924987793,\n      \"y\": 313.3707904815674,\n      \"z\": 5.561453700065613\n    }, {\n      \"x\": 288.23760986328125,\n      \"y\": 305.9941864013672,\n      \"z\": 36.765122413635254\n    }, {\n      \"x\": 315.10692596435547,\n      \"y\": 296.26991271972656,\n      \"z\": -4.604393839836121\n    }, {\n      \"x\": 337.50518798828125,\n      \"y\": 247.5944423675537,\n      \"z\": -10.597691535949707\n    }, {\n      \"x\": 338.8450622558594,\n      \"y\": 265.47778129577637,\n      \"z\": -27.778091430664062\n    }, {\n      \"x\": 334.25254821777344,\n      \"y\": 269.0671920776367,\n      \"z\": -20.938611030578613\n    }, {\n      \"x\": 341.64512634277344,\n      \"y\": 259.6387195587158,\n      \"z\": -32.189905643463135\n    }, {\n      \"x\": 331.44081115722656,\n      \"y\": 219.0976095199585,\n      \"z\": 4.207563698291779\n    }, {\n      \"x\": 320.56339263916016,\n      \"y\": 216.49658203125,\n      \"z\": 2.930997312068939\n    }, {\n      \"x\": 311.21912002563477,\n      \"y\": 216.57853603363037,\n      \"z\": 2.9674705862998962\n    }, {\n      \"x\": 303.46256256103516,\n      \"y\": 218.54614734649658,\n      \"z\": 5.357203483581543\n    }, {\n      \"x\": 297.99999237060547,\n      \"y\": 222.505202293396,\n      \"z\": 9.325502514839172\n    }, {\n      \"x\": 294.93839263916016,\n      \"y\": 236.39654159545898,\n      \"z\": 18.534289598464966\n    }, {\n      \"x\": 278.87489318847656,\n      \"y\": 259.7095584869385,\n      \"z\": 45.68212032318115\n    }, {\n      \"x\": 300.3782653808594,\n      \"y\": 245.38593292236328,\n      \"z\": 12.278382778167725\n    }, {\n      \"x\": 307.06348419189453,\n      \"y\": 246.36857986450195,\n      \"z\": 8.164191246032715\n    }, {\n      \"x\": 315.5229187011719,\n      \"y\": 245.3949737548828,\n      \"z\": 5.503097176551819\n    }, {\n      \"x\": 323.71395111083984,\n      \"y\": 242.75178909301758,\n      \"z\": 4.6335723996162415\n    }, {\n      \"x\": 330.2785873413086,\n      \"y\": 239.34658527374268,\n      \"z\": 4.937030673027039\n    }, {\n      \"x\": 334.6982192993164,\n      \"y\": 236.0460376739502,\n      \"z\": 4.823233783245087\n    }, {\n      \"x\": 279.3412208557129,\n      \"y\": 263.5196113586426,\n      \"z\": 70.91583728790283,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 334.65972900390625,\n      \"y\": 271.6648578643799,\n      \"z\": -17.775644063949585\n    }, {\n      \"x\": 342.05677032470703,\n      \"y\": 246.99846267700195,\n      \"z\": -20.84523916244507\n    }, {\n      \"x\": 344.0357971191406,\n      \"y\": 264.5701503753662,\n      \"z\": -32.936880588531494\n    }, {\n      \"x\": 348.25531005859375,\n      \"y\": 268.6645030975342,\n      \"z\": -30.695960521697998\n    }, {\n      \"x\": 344.12227630615234,\n      \"y\": 266.34212493896484,\n      \"z\": -29.808926582336426\n    }, {\n      \"x\": 337.12318420410156,\n      \"y\": 274.2556858062744,\n      \"z\": -15.768152475357056\n    }, {\n      \"x\": 349.49047088623047,\n      \"y\": 269.071683883667,\n      \"z\": -32.51670837402344\n    }, {\n      \"x\": 350.1683044433594,\n      \"y\": 271.4691352844238,\n      \"z\": -24.93025302886963\n    }, {\n      \"x\": 333.9634704589844,\n      \"y\": 230.56639194488525,\n      \"z\": 8.89949381351471\n    }, {\n      \"x\": 338.2147979736328,\n      \"y\": 231.4807891845703,\n      \"z\": 4.6715047955513\n    }, {\n      \"x\": 340.4712677001953,\n      \"y\": 231.74463272094727,\n      \"z\": -0.34996166825294495\n    }, {\n      \"x\": 303.28975677490234,\n      \"y\": 232.24980354309082,\n      \"z\": 11.916568279266357,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 299.4649124145508,\n      \"y\": 229.53842639923096,\n      \"z\": 12.325069904327393\n    }, {\n      \"x\": 359.09618377685547,\n      \"y\": 241.77349090576172,\n      \"z\": -24.650139808654785\n    }, {\n      \"x\": 399.46216583251953,\n      \"y\": 229.89503860473633,\n      \"z\": 15.919880867004395,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 361.38919830322266,\n      \"y\": 269.6129894256592,\n      \"z\": -24.510080814361572\n    }, {\n      \"x\": 416.9973373413086,\n      \"y\": 206.0895538330078,\n      \"z\": 53.26857566833496,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 381.32179260253906,\n      \"y\": 235.5476474761963,\n      \"z\": 7.6214683055877686\n    }, {\n      \"x\": 387.8068542480469,\n      \"y\": 236.25958442687988,\n      \"z\": 8.345099091529846\n    }, {\n      \"x\": 393.95751953125,\n      \"y\": 235.8660364151001,\n      \"z\": 10.475142002105713\n    }, {\n      \"x\": 401.84600830078125,\n      \"y\": 232.77019500732422,\n      \"z\": 16.760226488113403\n    }, {\n      \"x\": 375.70568084716797,\n      \"y\": 233.48456382751465,\n      \"z\": 8.234220147132874\n    }, {\n      \"x\": 388.17752838134766,\n      \"y\": 218.94717693328857,\n      \"z\": 6.810300946235657\n    }, {\n      \"x\": 381.64928436279297,\n      \"y\": 219.2656660079956,\n      \"z\": 6.711093783378601\n    }, {\n      \"x\": 394.4760513305664,\n      \"y\": 219.66821193695068,\n      \"z\": 9.173773527145386\n    }, {\n      \"x\": 398.8843536376953,\n      \"y\": 221.8837022781372,\n      \"z\": 12.03328251838684\n    }, {\n      \"x\": 406.5454864501953,\n      \"y\": 237.12156772613525,\n      \"z\": 19.7131085395813\n    }, {\n      \"x\": 383.87447357177734,\n      \"y\": 337.6932907104492,\n      \"z\": -8.631049990653992\n    }, {\n      \"x\": 401.2682342529297,\n      \"y\": 228.5916566848755,\n      \"z\": 18.359217643737793,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 422.0449447631836,\n      \"y\": 236.73934936523438,\n      \"z\": 51.16771221160889\n    }, {\n      \"x\": 412.69153594970703,\n      \"y\": 232.80198097229004,\n      \"z\": 27.52131938934326\n    }, {\n      \"x\": 387.3497772216797,\n      \"y\": 263.298397064209,\n      \"z\": -2.8609684109687805\n    }, {\n      \"x\": 364.5124053955078,\n      \"y\": 293.39221000671387,\n      \"z\": -22.397546768188477,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 363.62987518310547,\n      \"y\": 302.1291446685791,\n      \"z\": -19.643079042434692\n    }, {\n      \"x\": 373.2334518432617,\n      \"y\": 295.8647060394287,\n      \"z\": -18.125789165496826,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 378.83365631103516,\n      \"y\": 299.5177745819092,\n      \"z\": -13.153743743896484,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 369.91477966308594,\n      \"y\": 302.5704002380371,\n      \"z\": -16.65518283843994\n    }, {\n      \"x\": 374.9167251586914,\n      \"y\": 303.5416603088379,\n      \"z\": -11.963253021240234\n    }, {\n      \"x\": 387.58888244628906,\n      \"y\": 312.2716999053955,\n      \"z\": -4.680258631706238\n    }, {\n      \"x\": 360.6635284423828,\n      \"y\": 264.31986808776855,\n      \"z\": -35.94811677932739\n    }, {\n      \"x\": 361.04564666748047,\n      \"y\": 256.8225860595703,\n      \"z\": -37.278664112091064\n    }, {\n      \"x\": 408.3855438232422,\n      \"y\": 213.52088928222656,\n      \"z\": 15.756480693817139,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 373.2946014404297,\n      \"y\": 245.38101196289062,\n      \"z\": -1.9316278398036957\n    }, {\n      \"x\": 376.83860778808594,\n      \"y\": 264.3721103668213,\n      \"z\": -18.510947227478027\n    }, {\n      \"x\": 376.9546127319336,\n      \"y\": 261.0010528564453,\n      \"z\": -15.989909172058105\n    }, {\n      \"x\": 406.1498260498047,\n      \"y\": 263.5030174255371,\n      \"z\": 7.072908878326416\n    }, {\n      \"x\": 360.07205963134766,\n      \"y\": 248.3631706237793,\n      \"z\": -32.16656446456909\n    }, {\n      \"x\": 393.11119079589844,\n      \"y\": 205.10473251342773,\n      \"z\": 3.7786373496055603,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 402.12791442871094,\n      \"y\": 207.89000988006592,\n      \"z\": 9.383859634399414,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 410.8693313598633,\n      \"y\": 191.6182279586792,\n      \"z\": 41.27030849456787,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 364.9509811401367,\n      \"y\": 210.40483474731445,\n      \"z\": -3.758212625980377\n    }, {\n      \"x\": 375.94444274902344,\n      \"y\": 221.1331844329834,\n      \"z\": 8.368442058563232\n    }, {\n      \"x\": 392.1904754638672,\n      \"y\": 305.0360298156738,\n      \"z\": -1.752179116010666\n    }, {\n      \"x\": 419.50225830078125,\n      \"y\": 307.25592613220215,\n      \"z\": 58.96425247192383,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 372.0027160644531,\n      \"y\": 268.7212657928467,\n      \"z\": -16.631840467453003\n    }, {\n      \"x\": 366.1614227294922,\n      \"y\": 271.6237449645996,\n      \"z\": -18.219159841537476\n    }, {\n      \"x\": 385.00938415527344,\n      \"y\": 305.3863334655762,\n      \"z\": -2.567722797393799\n    }, {\n      \"x\": 381.99771881103516,\n      \"y\": 304.9723720550537,\n      \"z\": -4.575215280056\n    }, {\n      \"x\": 405.078125,\n      \"y\": 203.21216583251953,\n      \"z\": 13.713973760604858,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 377.13207244873047,\n      \"y\": 268.4710121154785,\n      \"z\": -15.266278982162476\n    }, {\n      \"x\": 380.9713363647461,\n      \"y\": 205.36980628967285,\n      \"z\": -0.7250899076461792,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 381.7788314819336,\n      \"y\": 198.9268398284912,\n      \"z\": -1.184653863310814,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 385.5204772949219,\n      \"y\": 172.1484375,\n      \"z\": 16.04826807975769,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 407.94189453125,\n      \"y\": 196.76236152648926,\n      \"z\": 25.723915100097656\n    }, {\n      \"x\": 383.03890228271484,\n      \"y\": 184.5157527923584,\n      \"z\": 7.393874526023865\n    }, {\n      \"x\": 411.61781311035156,\n      \"y\": 210.79241752624512,\n      \"z\": 22.315845489501953,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 414.30870056152344,\n      \"y\": 208.4643030166626,\n      \"z\": 37.021894454956055\n    }, {\n      \"x\": 364.28722381591797,\n      \"y\": 298.35777282714844,\n      \"z\": -21.86065673828125\n    }, {\n      \"x\": 371.3682556152344,\n      \"y\": 299.78848457336426,\n      \"z\": -17.834001779556274\n    }, {\n      \"x\": 376.88201904296875,\n      \"y\": 301.6696071624756,\n      \"z\": -13.153743743896484\n    }, {\n      \"x\": 370.2193832397461,\n      \"y\": 270.49095153808594,\n      \"z\": -15.569736957550049\n    }, {\n      \"x\": 383.5081100463867,\n      \"y\": 305.2726364135742,\n      \"z\": -3.673594295978546\n    }, {\n      \"x\": 380.73760986328125,\n      \"y\": 305.96869468688965,\n      \"z\": -8.660228252410889\n    }, {\n      \"x\": 381.2334442138672,\n      \"y\": 304.63574409484863,\n      \"z\": -4.820316135883331,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 368.1698989868164,\n      \"y\": 264.8884963989258,\n      \"z\": -25.653886795043945\n    }, {\n      \"x\": 373.5087203979492,\n      \"y\": 303.4233856201172,\n      \"z\": -10.95950722694397,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 368.4544372558594,\n      \"y\": 303.29601287841797,\n      \"z\": -14.169161319732666,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 362.76554107666016,\n      \"y\": 303.5735607147217,\n      \"z\": -16.911956071853638,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 366.60980224609375,\n      \"y\": 324.8870658874512,\n      \"z\": -15.616422891616821\n    }, {\n      \"x\": 365.7067108154297,\n      \"y\": 315.95678329467773,\n      \"z\": -20.903596878051758,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 365.0083923339844,\n      \"y\": 311.2232208251953,\n      \"z\": -21.066999435424805\n    }, {\n      \"x\": 364.1508102416992,\n      \"y\": 307.0583438873291,\n      \"z\": -18.907777070999146\n    }, {\n      \"x\": 363.37512969970703,\n      \"y\": 304.5721435546875,\n      \"z\": -17.42550015449524,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 374.580078125,\n      \"y\": 304.3059539794922,\n      \"z\": -11.40302300453186,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 375.55362701416016,\n      \"y\": 305.0998020172119,\n      \"z\": -12.861957550048828\n    }, {\n      \"x\": 377.2437286376953,\n      \"y\": 307.1674346923828,\n      \"z\": -14.215847253799438\n    }, {\n      \"x\": 378.68587493896484,\n      \"y\": 309.9015712738037,\n      \"z\": -13.223772048950195,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 383.8992691040039,\n      \"y\": 290.29629707336426,\n      \"z\": -9.97326910495758\n    }, {\n      \"x\": 423.3871841430664,\n      \"y\": 271.91688537597656,\n      \"z\": 74.37058925628662,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 377.68043518066406,\n      \"y\": 304.62209701538086,\n      \"z\": -7.603961229324341,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 379.00428771972656,\n      \"y\": 304.9314594268799,\n      \"z\": -8.57852816581726\n    }, {\n      \"x\": 364.00279998779297,\n      \"y\": 275.2813911437988,\n      \"z\": -19.25792098045349\n    }, {\n      \"x\": 374.68231201171875,\n      \"y\": 273.82555961608887,\n      \"z\": -11.28047227859497\n    }, {\n      \"x\": 365.0354766845703,\n      \"y\": 273.4548568725586,\n      \"z\": -18.791062831878662\n    }, {\n      \"x\": 380.61901092529297,\n      \"y\": 249.8848056793213,\n      \"z\": 0.15501167625188828\n    }, {\n      \"x\": 391.14158630371094,\n      \"y\": 254.7934627532959,\n      \"z\": 2.0906515419483185\n    }, {\n      \"x\": 378.1761169433594,\n      \"y\": 264.9612236022949,\n      \"z\": -12.605184316635132\n    }, {\n      \"x\": 400.9540557861328,\n      \"y\": 179.99592304229736,\n      \"z\": 27.82477855682373,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 398.0038833618164,\n      \"y\": 188.50656509399414,\n      \"z\": 16.094952821731567\n    }, {\n      \"x\": 394.8717498779297,\n      \"y\": 199.0359592437744,\n      \"z\": 6.226727366447449,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 382.10926055908203,\n      \"y\": 316.83926582336426,\n      \"z\": -8.946179747581482\n    }, {\n      \"x\": 366.51588439941406,\n      \"y\": 200.32583713531494,\n      \"z\": -5.24632453918457,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 367.4893569946289,\n      \"y\": 183.87210845947266,\n      \"z\": 1.9039081037044525\n    }, {\n      \"x\": 368.6243438720703,\n      \"y\": 168.8127565383911,\n      \"z\": 8.736093044281006,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 398.96175384521484,\n      \"y\": 234.9675178527832,\n      \"z\": 13.713973760604858\n    }, {\n      \"x\": 412.9645538330078,\n      \"y\": 242.23042488098145,\n      \"z\": 23.272905349731445\n    }, {\n      \"x\": 372.05257415771484,\n      \"y\": 231.41919136047363,\n      \"z\": 9.226294755935669\n    }, {\n      \"x\": 406.0722351074219,\n      \"y\": 223.58965873718262,\n      \"z\": 18.370890617370605\n    }, {\n      \"x\": 368.27442169189453,\n      \"y\": 240.2039337158203,\n      \"z\": -4.166713654994965\n    }, {\n      \"x\": 372.3575210571289,\n      \"y\": 260.66442489624023,\n      \"z\": -24.976940155029297\n    }, {\n      \"x\": 419.2244338989258,\n      \"y\": 247.9079246520996,\n      \"z\": 30.299127101898193\n    }, {\n      \"x\": 409.43885803222656,\n      \"y\": 246.60913467407227,\n      \"z\": 16.398411989212036\n    }, {\n      \"x\": 401.69139862060547,\n      \"y\": 248.76328468322754,\n      \"z\": 9.395531415939331\n    }, {\n      \"x\": 389.7608184814453,\n      \"y\": 247.56915092468262,\n      \"z\": 5.841569304466248\n    }, {\n      \"x\": 380.5461883544922,\n      \"y\": 244.55984115600586,\n      \"z\": 4.263003468513489\n    }, {\n      \"x\": 373.25817108154297,\n      \"y\": 240.80214500427246,\n      \"z\": 2.5356262922286987\n    }, {\n      \"x\": 358.77086639404297,\n      \"y\": 229.35615062713623,\n      \"z\": -10.387605428695679\n    }, {\n      \"x\": 419.5793914794922,\n      \"y\": 262.8478717803955,\n      \"z\": 26.5175724029541\n    }, {\n      \"x\": 410.8808898925781,\n      \"y\": 222.51372814178467,\n      \"z\": 22.199130058288574\n    }, {\n      \"x\": 358.45714569091797,\n      \"y\": 268.91467094421387,\n      \"z\": -33.17030906677246\n    }, {\n      \"x\": 373.4129333496094,\n      \"y\": 251.6385841369629,\n      \"z\": -5.771540403366089\n    }, {\n      \"x\": 422.5408172607422,\n      \"y\": 239.23919677734375,\n      \"z\": 74.04378890991211,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 367.8171920776367,\n      \"y\": 236.58040523529053,\n      \"z\": 1.820748895406723\n    }, {\n      \"x\": 378.51959228515625,\n      \"y\": 266.2532329559326,\n      \"z\": -5.74819803237915\n    }, {\n      \"x\": 403.3472442626953,\n      \"y\": 229.05112266540527,\n      \"z\": 19.689764976501465\n    }, {\n      \"x\": 372.34840393066406,\n      \"y\": 256.6451168060303,\n      \"z\": -21.872329711914062\n    }, {\n      \"x\": 422.54566192626953,\n      \"y\": 289.1587829589844,\n      \"z\": 68.67491245269775,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 371.9297409057617,\n      \"y\": 228.90116214752197,\n      \"z\": 11.432201862335205,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 366.21360778808594,\n      \"y\": 251.6158962249756,\n      \"z\": -28.19826364517212\n    }, {\n      \"x\": 409.1571807861328,\n      \"y\": 321.3156223297119,\n      \"z\": 20.2266526222229\n    }, {\n      \"x\": 408.52943420410156,\n      \"y\": 331.44238471984863,\n      \"z\": 31.09278917312622,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 424.2788314819336,\n      \"y\": 267.1992301940918,\n      \"z\": 50.467424392700195\n    }, {\n      \"x\": 415.60352325439453,\n      \"y\": 311.6528606414795,\n      \"z\": 30.579242706298828\n    }, {\n      \"x\": 418.12793731689453,\n      \"y\": 221.59927368164062,\n      \"z\": 46.26569747924805\n    }, {\n      \"x\": 385.68286895751953,\n      \"y\": 346.0184955596924,\n      \"z\": -5.70151150226593\n    }, {\n      \"x\": 357.82936096191406,\n      \"y\": 271.3758373260498,\n      \"z\": -24.836881160736084\n    }, {\n      \"x\": 379.588623046875,\n      \"y\": 257.5071716308594,\n      \"z\": -3.755294680595398\n    }, {\n      \"x\": 417.4592590332031,\n      \"y\": 234.71948146820068,\n      \"z\": 34.5475435256958\n    }, {\n      \"x\": 393.4684371948242,\n      \"y\": 231.58967971801758,\n      \"z\": 11.408859491348267,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 387.8864288330078,\n      \"y\": 232.14245796203613,\n      \"z\": 9.51808214187622,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 382.4981689453125,\n      \"y\": 307.5654888153076,\n      \"z\": -7.522260546684265,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 419.00169372558594,\n      \"y\": 277.8332805633545,\n      \"z\": 26.424202919006348\n    }, {\n      \"x\": 373.62953186035156,\n      \"y\": 357.6375102996826,\n      \"z\": -5.75986921787262,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 392.8708267211914,\n      \"y\": 347.72446632385254,\n      \"z\": 10.154176950454712,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 400.3953552246094,\n      \"y\": 341.0005187988281,\n      \"z\": 19.39797878265381,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 382.25440979003906,\n      \"y\": 231.66935920715332,\n      \"z\": 8.998700976371765,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 377.14550018310547,\n      \"y\": 230.4228687286377,\n      \"z\": 9.804032444953918,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 373.8358688354492,\n      \"y\": 229.64950561523438,\n      \"z\": 11.292144060134888,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 414.5794677734375,\n      \"y\": 221.67891025543213,\n      \"z\": 29.412097930908203\n    }, {\n      \"x\": 377.00672149658203,\n      \"y\": 225.66201210021973,\n      \"z\": 9.360517263412476,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 382.29530334472656,\n      \"y\": 224.8431158065796,\n      \"z\": 8.32175612449646,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 387.5133514404297,\n      \"y\": 224.49507236480713,\n      \"z\": 8.917000889778137,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 393.15906524658203,\n      \"y\": 225.24795055389404,\n      \"z\": 10.737749338150024,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 397.05554962158203,\n      \"y\": 226.55359268188477,\n      \"z\": 13.002015352249146,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 420.5299377441406,\n      \"y\": 221.014666557312,\n      \"z\": 65.40690422058105,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 397.06920623779297,\n      \"y\": 230.6661558151245,\n      \"z\": 13.807345628738403,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 377.94647216796875,\n      \"y\": 285.1647090911865,\n      \"z\": -13.305472135543823\n    }, {\n      \"x\": 372.1118927001953,\n      \"y\": 267.1267318725586,\n      \"z\": -18.83774757385254\n    }, {\n      \"x\": 364.9968719482422,\n      \"y\": 282.24411964416504,\n      \"z\": -19.818150997161865\n    }, {\n      \"x\": 401.973876953125,\n      \"y\": 331.20131492614746,\n      \"z\": 11.566424369812012\n    }, {\n      \"x\": 394.3083190917969,\n      \"y\": 338.86693954467773,\n      \"z\": 3.142542541027069\n    }, {\n      \"x\": 373.9820861816406,\n      \"y\": 351.4504623413086,\n      \"z\": -13.50388765335083\n    }, {\n      \"x\": 414.3888854980469,\n      \"y\": 321.24735832214355,\n      \"z\": 45.51872253417969,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 373.44234466552734,\n      \"y\": 227.33163356781006,\n      \"z\": 10.626870393753052,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 364.0731430053711,\n      \"y\": 240.31539916992188,\n      \"z\": -13.807345628738403\n    }, {\n      \"x\": 384.2658233642578,\n      \"y\": 353.3793067932129,\n      \"z\": 0.7385850697755814,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 423.20526123046875,\n      \"y\": 283.5176181793213,\n      \"z\": 47.152724266052246\n    }, {\n      \"x\": 369.42798614501953,\n      \"y\": 304.0898895263672,\n      \"z\": -14.647691249847412,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 370.63812255859375,\n      \"y\": 305.90051651000977,\n      \"z\": -16.211668252944946\n    }, {\n      \"x\": 371.91192626953125,\n      \"y\": 309.0167713165283,\n      \"z\": -17.84567356109619\n    }, {\n      \"x\": 373.0583953857422,\n      \"y\": 313.3545398712158,\n      \"z\": -17.378815412521362,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 375.39905548095703,\n      \"y\": 321.09289169311523,\n      \"z\": -13.118728399276733\n    }, {\n      \"x\": 379.2567825317383,\n      \"y\": 304.3582534790039,\n      \"z\": -7.924926280975342\n    }, {\n      \"x\": 381.18797302246094,\n      \"y\": 303.7031364440918,\n      \"z\": -7.843226194381714\n    }, {\n      \"x\": 383.0918502807617,\n      \"y\": 302.4884605407715,\n      \"z\": -7.6506465673446655,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 389.09461975097656,\n      \"y\": 297.1475315093994,\n      \"z\": -5.5497825145721436\n    }, {\n      \"x\": 411.6408920288086,\n      \"y\": 280.24898529052734,\n      \"z\": 12.02161192893982\n    }, {\n      \"x\": 363.3110809326172,\n      \"y\": 234.27620887756348,\n      \"z\": -6.775286793708801\n    }, {\n      \"x\": 366.0474395751953,\n      \"y\": 223.29872131347656,\n      \"z\": 6.827808618545532\n    }, {\n      \"x\": 370.34427642822266,\n      \"y\": 225.1457118988037,\n      \"z\": 9.558931589126587\n    }, {\n      \"x\": 377.5371551513672,\n      \"y\": 303.60079765319824,\n      \"z\": -7.358860373497009,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 412.9557800292969,\n      \"y\": 299.53579902648926,\n      \"z\": 19.39797878265381\n    }, {\n      \"x\": 360.0810241699219,\n      \"y\": 221.72012329101562,\n      \"z\": -2.153385728597641\n    }, {\n      \"x\": 379.82784271240234,\n      \"y\": 329.47723388671875,\n      \"z\": -10.48097848892212\n    }, {\n      \"x\": 359.08477783203125,\n      \"y\": 235.7911491394043,\n      \"z\": -18.079102039337158\n    }, {\n      \"x\": 369.6688461303711,\n      \"y\": 251.5407943725586,\n      \"z\": -14.962821006774902\n    }, {\n      \"x\": 369.5555114746094,\n      \"y\": 333.5307312011719,\n      \"z\": -15.67478060722351\n    }, {\n      \"x\": 394.0193176269531,\n      \"y\": 315.6973171234131,\n      \"z\": -0.9920747578144073\n    }, {\n      \"x\": 383.78997802734375,\n      \"y\": 272.7268695831299,\n      \"z\": -4.689012169837952\n    }, {\n      \"x\": 387.67765045166016,\n      \"y\": 323.6722755432129,\n      \"z\": -5.640236139297485\n    }, {\n      \"x\": 397.8769302368164,\n      \"y\": 272.1331214904785,\n      \"z\": -0.9395531564950943\n    }, {\n      \"x\": 389.87476348876953,\n      \"y\": 280.5630111694336,\n      \"z\": -4.29218202829361\n    }, {\n      \"x\": 403.83888244628906,\n      \"y\": 285.1167869567871,\n      \"z\": 3.0229100584983826\n    }, {\n      \"x\": 372.5467300415039,\n      \"y\": 343.1070327758789,\n      \"z\": -16.153310537338257\n    }, {\n      \"x\": 374.1112518310547,\n      \"y\": 256.3721466064453,\n      \"z\": -10.574349164962769\n    }, {\n      \"x\": 399.73785400390625,\n      \"y\": 321.77515983581543,\n      \"z\": 4.849494695663452\n    }, {\n      \"x\": 392.03365325927734,\n      \"y\": 330.56447982788086,\n      \"z\": -1.3407598435878754\n    }, {\n      \"x\": 398.59134674072266,\n      \"y\": 305.93902587890625,\n      \"z\": 1.517290621995926\n    }, {\n      \"x\": 417.95997619628906,\n      \"y\": 290.9716987609863,\n      \"z\": 26.89105987548828\n    }, {\n      \"x\": 406.04541778564453,\n      \"y\": 307.35154151916504,\n      \"z\": 8.666064143180847\n    }, {\n      \"x\": 420.75328826904297,\n      \"y\": 298.40752601623535,\n      \"z\": 41.78385257720947\n    }, {\n      \"x\": 395.4522705078125,\n      \"y\": 291.4153575897217,\n      \"z\": -2.1752697229385376\n    }, {\n      \"x\": 368.6452102661133,\n      \"y\": 245.8882999420166,\n      \"z\": -9.453888535499573\n    }, {\n      \"x\": 370.34900665283203,\n      \"y\": 263.56690406799316,\n      \"z\": -26.75100326538086\n    }, {\n      \"x\": 374.98477935791016,\n      \"y\": 266.6126346588135,\n      \"z\": -19.77146625518799\n    }, {\n      \"x\": 366.99840545654297,\n      \"y\": 258.12140464782715,\n      \"z\": -31.372904777526855\n    }, {\n      \"x\": 371.00616455078125,\n      \"y\": 217.63479709625244,\n      \"z\": 5.60522198677063\n    }, {\n      \"x\": 381.30577087402344,\n      \"y\": 214.14087295532227,\n      \"z\": 4.983716309070587\n    }, {\n      \"x\": 390.1496124267578,\n      \"y\": 213.38221549987793,\n      \"z\": 5.593550801277161\n    }, {\n      \"x\": 397.7696990966797,\n      \"y\": 214.3659782409668,\n      \"z\": 8.57852816581726\n    }, {\n      \"x\": 403.1652069091797,\n      \"y\": 217.65509605407715,\n      \"z\": 13.013685941696167\n    }, {\n      \"x\": 407.3551940917969,\n      \"y\": 230.72525024414062,\n      \"z\": 22.444231510162354\n    }, {\n      \"x\": 424.0876770019531,\n      \"y\": 251.7839241027832,\n      \"z\": 51.16771221160889\n    }, {\n      \"x\": 403.50196838378906,\n      \"y\": 239.88757610321045,\n      \"z\": 15.803166627883911\n    }, {\n      \"x\": 397.31719970703125,\n      \"y\": 241.49806022644043,\n      \"z\": 11.233787536621094\n    }, {\n      \"x\": 388.99425506591797,\n      \"y\": 241.4366912841797,\n      \"z\": 7.948269248008728\n    }, {\n      \"x\": 380.7804489135742,\n      \"y\": 239.78078842163086,\n      \"z\": 6.600214838981628\n    }, {\n      \"x\": 374.01336669921875,\n      \"y\": 237.11946487426758,\n      \"z\": 6.349278092384338\n    }, {\n      \"x\": 369.39125061035156,\n      \"y\": 234.35351371765137,\n      \"z\": 5.987462401390076\n    }, {\n      \"x\": 422.9730987548828,\n      \"y\": 255.76455116271973,\n      \"z\": 76.61150932312012,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 374.73915100097656,\n      \"y\": 269.24214363098145,\n      \"z\": -16.608498096466064\n    }, {\n      \"x\": 364.61681365966797,\n      \"y\": 245.71088790893555,\n      \"z\": -20.02823829650879\n    }, {\n      \"x\": 365.3834533691406,\n      \"y\": 263.34174156188965,\n      \"z\": -32.32996463775635\n    }, {\n      \"x\": 361.58252716064453,\n      \"y\": 267.8273677825928,\n      \"z\": -30.345816612243652\n    }, {\n      \"x\": 365.37208557128906,\n      \"y\": 265.0249671936035,\n      \"z\": -29.178667068481445\n    }, {\n      \"x\": 372.72605895996094,\n      \"y\": 272.05135345458984,\n      \"z\": -14.834434986114502\n    }, {\n      \"x\": 360.48614501953125,\n      \"y\": 268.34827423095703,\n      \"z\": -32.189905643463135\n    }, {\n      \"x\": 359.9516296386719,\n      \"y\": 270.8049201965332,\n      \"z\": -24.650139808654785\n    }, {\n      \"x\": 369.5049285888672,\n      \"y\": 229.01945114135742,\n      \"z\": 10.107489824295044\n    }, {\n      \"x\": 365.5447769165039,\n      \"y\": 230.24096488952637,\n      \"z\": 5.593550801277161\n    }, {\n      \"x\": 363.50669860839844,\n      \"y\": 230.6208372116089,\n      \"z\": 0.43622106313705444\n    }, {\n      \"x\": 399.3529510498047,\n      \"y\": 227.65677452087402,\n      \"z\": 15.35965085029602,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 402.5693130493164,\n      \"y\": 224.60190296173096,\n      \"z\": 15.931552648544312\n    }],\n    \"box\": {\n      \"xMin\": 277.8318977355957,\n      \"yMin\": 168.7741756439209,\n      \"xMax\": 424.2788314819336,\n      \"yMax\": 359.8348903656006,\n      \"width\": 146.4469337463379,\n      \"height\": 191.0607147216797\n    }\n  },\n  // Extracted from: https://github.com/tensorflow/tfjs-models/blob/a8f500809f5afe38feea27870c77e7ba03a6ece4/face-landmarks-detection/demos/shared/triangulation.js\n  // prettier-ignore\n  TRIANGULATION: [127, 34, 139, 11, 0, 37, 232, 231, 120, 72, 37, 39, 128, 121, 47, 232, 121, 128, 104, 69, 67, 175, 171, 148, 157, 154, 155, 118, 50, 101, 73, 39, 40, 9, 151, 108, 48, 115, 131, 194, 204, 211, 74, 40, 185, 80, 42, 183, 40, 92, 186, 230, 229, 118, 202, 212, 214, 83, 18, 17, 76, 61, 146, 160, 29, 30, 56, 157, 173, 106, 204, 194, 135, 214, 192, 203, 165, 98, 21, 71, 68, 51, 45, 4, 144, 24, 23, 77, 146, 91, 205, 50, 187, 201, 200, 18, 91, 106, 182, 90, 91, 181, 85, 84, 17, 206, 203, 36, 148, 171, 140, 92, 40, 39, 193, 189, 244, 159, 158, 28, 247, 246, 161, 236, 3, 196, 54, 68, 104, 193, 168, 8, 117, 228, 31, 189, 193, 55, 98, 97, 99, 126, 47, 100, 166, 79, 218, 155, 154, 26, 209, 49, 131, 135, 136, 150, 47, 126, 217, 223, 52, 53, 45, 51, 134, 211, 170, 140, 67, 69, 108, 43, 106, 91, 230, 119, 120, 226, 130, 247, 63, 53, 52, 238, 20, 242, 46, 70, 156, 78, 62, 96, 46, 53, 63, 143, 34, 227, 173, 155, 133, 123, 117, 111, 44, 125, 19, 236, 134, 51, 216, 206, 205, 154, 153, 22, 39, 37, 167, 200, 201, 208, 36, 142, 100, 57, 212, 202, 20, 60, 99, 28, 158, 157, 35, 226, 113, 160, 159, 27, 204, 202, 210, 113, 225, 46, 43, 202, 204, 62, 76, 77, 137, 123, 116, 41, 38, 72, 203, 129, 142, 64, 98, 240, 49, 102, 64, 41, 73, 74, 212, 216, 207, 42, 74, 184, 169, 170, 211, 170, 149, 176, 105, 66, 69, 122, 6, 168, 123, 147, 187, 96, 77, 90, 65, 55, 107, 89, 90, 180, 101, 100, 120, 63, 105, 104, 93, 137, 227, 15, 86, 85, 129, 102, 49, 14, 87, 86, 55, 8, 9, 100, 47, 121, 145, 23, 22, 88, 89, 179, 6, 122, 196, 88, 95, 96, 138, 172, 136, 215, 58, 172, 115, 48, 219, 42, 80, 81, 195, 3, 51, 43, 146, 61, 171, 175, 199, 81, 82, 38, 53, 46, 225, 144, 163, 110, 246, 33, 7, 52, 65, 66, 229, 228, 117, 34, 127, 234, 107, 108, 69, 109, 108, 151, 48, 64, 235, 62, 78, 191, 129, 209, 126, 111, 35, 143, 163, 161, 246, 117, 123, 50, 222, 65, 52, 19, 125, 141, 221, 55, 65, 3, 195, 197, 25, 7, 33, 220, 237, 44, 70, 71, 139, 122, 193, 245, 247, 130, 33, 71, 21, 162, 153, 158, 159, 170, 169, 150, 188, 174, 196, 216, 186, 92, 144, 160, 161, 2, 97, 167, 141, 125, 241, 164, 167, 37, 72, 38, 12, 145, 159, 160, 38, 82, 13, 63, 68, 71, 226, 35, 111, 158, 153, 154, 101, 50, 205, 206, 92, 165, 209, 198, 217, 165, 167, 97, 220, 115, 218, 133, 112, 243, 239, 238, 241, 214, 135, 169, 190, 173, 133, 171, 208, 32, 125, 44, 237, 86, 87, 178, 85, 86, 179, 84, 85, 180, 83, 84, 181, 201, 83, 182, 137, 93, 132, 76, 62, 183, 61, 76, 184, 57, 61, 185, 212, 57, 186, 214, 207, 187, 34, 143, 156, 79, 239, 237, 123, 137, 177, 44, 1, 4, 201, 194, 32, 64, 102, 129, 213, 215, 138, 59, 166, 219, 242, 99, 97, 2, 94, 141, 75, 59, 235, 24, 110, 228, 25, 130, 226, 23, 24, 229, 22, 23, 230, 26, 22, 231, 112, 26, 232, 189, 190, 243, 221, 56, 190, 28, 56, 221, 27, 28, 222, 29, 27, 223, 30, 29, 224, 247, 30, 225, 238, 79, 20, 166, 59, 75, 60, 75, 240, 147, 177, 215, 20, 79, 166, 187, 147, 213, 112, 233, 244, 233, 128, 245, 128, 114, 188, 114, 217, 174, 131, 115, 220, 217, 198, 236, 198, 131, 134, 177, 132, 58, 143, 35, 124, 110, 163, 7, 228, 110, 25, 356, 389, 368, 11, 302, 267, 452, 350, 349, 302, 303, 269, 357, 343, 277, 452, 453, 357, 333, 332, 297, 175, 152, 377, 384, 398, 382, 347, 348, 330, 303, 304, 270, 9, 336, 337, 278, 279, 360, 418, 262, 431, 304, 408, 409, 310, 415, 407, 270, 409, 410, 450, 348, 347, 422, 430, 434, 313, 314, 17, 306, 307, 375, 387, 388, 260, 286, 414, 398, 335, 406, 418, 364, 367, 416, 423, 358, 327, 251, 284, 298, 281, 5, 4, 373, 374, 253, 307, 320, 321, 425, 427, 411, 421, 313, 18, 321, 405, 406, 320, 404, 405, 315, 16, 17, 426, 425, 266, 377, 400, 369, 322, 391, 269, 417, 465, 464, 386, 257, 258, 466, 260, 388, 456, 399, 419, 284, 332, 333, 417, 285, 8, 346, 340, 261, 413, 441, 285, 327, 460, 328, 355, 371, 329, 392, 439, 438, 382, 341, 256, 429, 420, 360, 364, 394, 379, 277, 343, 437, 443, 444, 283, 275, 440, 363, 431, 262, 369, 297, 338, 337, 273, 375, 321, 450, 451, 349, 446, 342, 467, 293, 334, 282, 458, 461, 462, 276, 353, 383, 308, 324, 325, 276, 300, 293, 372, 345, 447, 382, 398, 362, 352, 345, 340, 274, 1, 19, 456, 248, 281, 436, 427, 425, 381, 256, 252, 269, 391, 393, 200, 199, 428, 266, 330, 329, 287, 273, 422, 250, 462, 328, 258, 286, 384, 265, 353, 342, 387, 259, 257, 424, 431, 430, 342, 353, 276, 273, 335, 424, 292, 325, 307, 366, 447, 345, 271, 303, 302, 423, 266, 371, 294, 455, 460, 279, 278, 294, 271, 272, 304, 432, 434, 427, 272, 407, 408, 394, 430, 431, 395, 369, 400, 334, 333, 299, 351, 417, 168, 352, 280, 411, 325, 319, 320, 295, 296, 336, 319, 403, 404, 330, 348, 349, 293, 298, 333, 323, 454, 447, 15, 16, 315, 358, 429, 279, 14, 15, 316, 285, 336, 9, 329, 349, 350, 374, 380, 252, 318, 402, 403, 6, 197, 419, 318, 319, 325, 367, 364, 365, 435, 367, 397, 344, 438, 439, 272, 271, 311, 195, 5, 281, 273, 287, 291, 396, 428, 199, 311, 271, 268, 283, 444, 445, 373, 254, 339, 263, 466, 249, 282, 334, 296, 449, 347, 346, 264, 447, 454, 336, 296, 299, 338, 10, 151, 278, 439, 455, 292, 407, 415, 358, 371, 355, 340, 345, 372, 390, 249, 466, 346, 347, 280, 442, 443, 282, 19, 94, 370, 441, 442, 295, 248, 419, 197, 263, 255, 359, 440, 275, 274, 300, 383, 368, 351, 412, 465, 263, 467, 466, 301, 368, 389, 380, 374, 386, 395, 378, 379, 412, 351, 419, 436, 426, 322, 373, 390, 388, 2, 164, 393, 370, 462, 461, 164, 0, 267, 302, 11, 12, 374, 373, 387, 268, 12, 13, 293, 300, 301, 446, 261, 340, 385, 384, 381, 330, 266, 425, 426, 423, 391, 429, 355, 437, 391, 327, 326, 440, 457, 438, 341, 382, 362, 459, 457, 461, 434, 430, 394, 414, 463, 362, 396, 369, 262, 354, 461, 457, 316, 403, 402, 315, 404, 403, 314, 405, 404, 313, 406, 405, 421, 418, 406, 366, 401, 361, 306, 408, 407, 291, 409, 408, 287, 410, 409, 432, 436, 410, 434, 416, 411, 264, 368, 383, 309, 438, 457, 352, 376, 401, 274, 275, 4, 421, 428, 262, 294, 327, 358, 433, 416, 367, 289, 455, 439, 462, 370, 326, 2, 326, 370, 305, 460, 455, 254, 449, 448, 255, 261, 446, 253, 450, 449, 252, 451, 450, 256, 452, 451, 341, 453, 452, 413, 464, 463, 441, 413, 414, 258, 442, 441, 257, 443, 442, 259, 444, 443, 260, 445, 444, 467, 342, 445, 459, 458, 250, 289, 392, 290, 290, 328, 460, 376, 433, 435, 250, 290, 392, 411, 416, 433, 341, 463, 464, 453, 464, 465, 357, 465, 412, 343, 412, 399, 360, 363, 440, 437, 399, 456, 420, 456, 363, 401, 435, 288, 372, 383, 353, 339, 255, 249, 448, 261, 255, 133, 243, 190, 133, 155, 112, 33, 246, 247, 33, 130, 25, 398, 384, 286, 362, 398, 414, 362, 463, 341, 263, 359, 467, 263, 249, 255, 466, 467, 260, 75, 60, 166, 238, 239, 79, 162, 127, 139, 72, 11, 37, 121, 232, 120, 73, 72, 39, 114, 128, 47, 233, 232, 128, 103, 104, 67, 152, 175, 148, 173, 157, 155, 119, 118, 101, 74, 73, 40, 107, 9, 108, 49, 48, 131, 32, 194, 211, 184, 74, 185, 191, 80, 183, 185, 40, 186, 119, 230, 118, 210, 202, 214, 84, 83, 17, 77, 76, 146, 161, 160, 30, 190, 56, 173, 182, 106, 194, 138, 135, 192, 129, 203, 98, 54, 21, 68, 5, 51, 4, 145, 144, 23, 90, 77, 91, 207, 205, 187, 83, 201, 18, 181, 91, 182, 180, 90, 181, 16, 85, 17, 205, 206, 36, 176, 148, 140, 165, 92, 39, 245, 193, 244, 27, 159, 28, 30, 247, 161, 174, 236, 196, 103, 54, 104, 55, 193, 8, 111, 117, 31, 221, 189, 55, 240, 98, 99, 142, 126, 100, 219, 166, 218, 112, 155, 26, 198, 209, 131, 169, 135, 150, 114, 47, 217, 224, 223, 53, 220, 45, 134, 32, 211, 140, 109, 67, 108, 146, 43, 91, 231, 230, 120, 113, 226, 247, 105, 63, 52, 241, 238, 242, 124, 46, 156, 95, 78, 96, 70, 46, 63, 116, 143, 227, 116, 123, 111, 1, 44, 19, 3, 236, 51, 207, 216, 205, 26, 154, 22, 165, 39, 167, 199, 200, 208, 101, 36, 100, 43, 57, 202, 242, 20, 99, 56, 28, 157, 124, 35, 113, 29, 160, 27, 211, 204, 210, 124, 113, 46, 106, 43, 204, 96, 62, 77, 227, 137, 116, 73, 41, 72, 36, 203, 142, 235, 64, 240, 48, 49, 64, 42, 41, 74, 214, 212, 207, 183, 42, 184, 210, 169, 211, 140, 170, 176, 104, 105, 69, 193, 122, 168, 50, 123, 187, 89, 96, 90, 66, 65, 107, 179, 89, 180, 119, 101, 120, 68, 63, 104, 234, 93, 227, 16, 15, 85, 209, 129, 49, 15, 14, 86, 107, 55, 9, 120, 100, 121, 153, 145, 22, 178, 88, 179, 197, 6, 196, 89, 88, 96, 135, 138, 136, 138, 215, 172, 218, 115, 219, 41, 42, 81, 5, 195, 51, 57, 43, 61, 208, 171, 199, 41, 81, 38, 224, 53, 225, 24, 144, 110, 105, 52, 66, 118, 229, 117, 227, 34, 234, 66, 107, 69, 10, 109, 151, 219, 48, 235, 183, 62, 191, 142, 129, 126, 116, 111, 143, 7, 163, 246, 118, 117, 50, 223, 222, 52, 94, 19, 141, 222, 221, 65, 196, 3, 197, 45, 220, 44, 156, 70, 139, 188, 122, 245, 139, 71, 162, 145, 153, 159, 149, 170, 150, 122, 188, 196, 206, 216, 92, 163, 144, 161, 164, 2, 167, 242, 141, 241, 0, 164, 37, 11, 72, 12, 144, 145, 160, 12, 38, 13, 70, 63, 71, 31, 226, 111, 157, 158, 154, 36, 101, 205, 203, 206, 165, 126, 209, 217, 98, 165, 97, 237, 220, 218, 237, 239, 241, 210, 214, 169, 140, 171, 32, 241, 125, 237, 179, 86, 178, 180, 85, 179, 181, 84, 180, 182, 83, 181, 194, 201, 182, 177, 137, 132, 184, 76, 183, 185, 61, 184, 186, 57, 185, 216, 212, 186, 192, 214, 187, 139, 34, 156, 218, 79, 237, 147, 123, 177, 45, 44, 4, 208, 201, 32, 98, 64, 129, 192, 213, 138, 235, 59, 219, 141, 242, 97, 97, 2, 141, 240, 75, 235, 229, 24, 228, 31, 25, 226, 230, 23, 229, 231, 22, 230, 232, 26, 231, 233, 112, 232, 244, 189, 243, 189, 221, 190, 222, 28, 221, 223, 27, 222, 224, 29, 223, 225, 30, 224, 113, 247, 225, 99, 60, 240, 213, 147, 215, 60, 20, 166, 192, 187, 213, 243, 112, 244, 244, 233, 245, 245, 128, 188, 188, 114, 174, 134, 131, 220, 174, 217, 236, 236, 198, 134, 215, 177, 58, 156, 143, 124, 25, 110, 7, 31, 228, 25, 264, 356, 368, 0, 11, 267, 451, 452, 349, 267, 302, 269, 350, 357, 277, 350, 452, 357, 299, 333, 297, 396, 175, 377, 381, 384, 382, 280, 347, 330, 269, 303, 270, 151, 9, 337, 344, 278, 360, 424, 418, 431, 270, 304, 409, 272, 310, 407, 322, 270, 410, 449, 450, 347, 432, 422, 434, 18, 313, 17, 291, 306, 375, 259, 387, 260, 424, 335, 418, 434, 364, 416, 391, 423, 327, 301, 251, 298, 275, 281, 4, 254, 373, 253, 375, 307, 321, 280, 425, 411, 200, 421, 18, 335, 321, 406, 321, 320, 405, 314, 315, 17, 423, 426, 266, 396, 377, 369, 270, 322, 269, 413, 417, 464, 385, 386, 258, 248, 456, 419, 298, 284, 333, 168, 417, 8, 448, 346, 261, 417, 413, 285, 326, 327, 328, 277, 355, 329, 309, 392, 438, 381, 382, 256, 279, 429, 360, 365, 364, 379, 355, 277, 437, 282, 443, 283, 281, 275, 363, 395, 431, 369, 299, 297, 337, 335, 273, 321, 348, 450, 349, 359, 446, 467, 283, 293, 282, 250, 458, 462, 300, 276, 383, 292, 308, 325, 283, 276, 293, 264, 372, 447, 346, 352, 340, 354, 274, 19, 363, 456, 281, 426, 436, 425, 380, 381, 252, 267, 269, 393, 421, 200, 428, 371, 266, 329, 432, 287, 422, 290, 250, 328, 385, 258, 384, 446, 265, 342, 386, 387, 257, 422, 424, 430, 445, 342, 276, 422, 273, 424, 306, 292, 307, 352, 366, 345, 268, 271, 302, 358, 423, 371, 327, 294, 460, 331, 279, 294, 303, 271, 304, 436, 432, 427, 304, 272, 408, 395, 394, 431, 378, 395, 400, 296, 334, 299, 6, 351, 168, 376, 352, 411, 307, 325, 320, 285, 295, 336, 320, 319, 404, 329, 330, 349, 334, 293, 333, 366, 323, 447, 316, 15, 315, 331, 358, 279, 317, 14, 316, 8, 285, 9, 277, 329, 350, 253, 374, 252, 319, 318, 403, 351, 6, 419, 324, 318, 325, 397, 367, 365, 288, 435, 397, 278, 344, 439, 310, 272, 311, 248, 195, 281, 375, 273, 291, 175, 396, 199, 312, 311, 268, 276, 283, 445, 390, 373, 339, 295, 282, 296, 448, 449, 346, 356, 264, 454, 337, 336, 299, 337, 338, 151, 294, 278, 455, 308, 292, 415, 429, 358, 355, 265, 340, 372, 388, 390, 466, 352, 346, 280, 295, 442, 282, 354, 19, 370, 285, 441, 295, 195, 248, 197, 457, 440, 274, 301, 300, 368, 417, 351, 465, 251, 301, 389, 385, 380, 386, 394, 395, 379, 399, 412, 419, 410, 436, 322, 387, 373, 388, 326, 2, 393, 354, 370, 461, 393, 164, 267, 268, 302, 12, 386, 374, 387, 312, 268, 13, 298, 293, 301, 265, 446, 340, 380, 385, 381, 280, 330, 425, 322, 426, 391, 420, 429, 437, 393, 391, 326, 344, 440, 438, 458, 459, 461, 364, 434, 394, 428, 396, 262, 274, 354, 457, 317, 316, 402, 316, 315, 403, 315, 314, 404, 314, 313, 405, 313, 421, 406, 323, 366, 361, 292, 306, 407, 306, 291, 408, 291, 287, 409, 287, 432, 410, 427, 434, 411, 372, 264, 383, 459, 309, 457, 366, 352, 401, 1, 274, 4, 418, 421, 262, 331, 294, 358, 435, 433, 367, 392, 289, 439, 328, 462, 326, 94, 2, 370, 289, 305, 455, 339, 254, 448, 359, 255, 446, 254, 253, 449, 253, 252, 450, 252, 256, 451, 256, 341, 452, 414, 413, 463, 286, 441, 414, 286, 258, 441, 258, 257, 442, 257, 259, 443, 259, 260, 444, 260, 467, 445, 309, 459, 250, 305, 289, 290, 305, 290, 460, 401, 376, 435, 309, 250, 392, 376, 411, 433, 453, 341, 464, 357, 453, 465, 343, 357, 412, 437, 343, 399, 344, 360, 440, 420, 437, 456, 360, 420, 363, 361, 401, 288, 265, 372, 353, 390, 339, 249, 339, 448, 255]\n};\n\nexport { Facemesh, FacemeshDatas };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,IAAI,QAAQ,WAAW;;AAEhC;AACA,MAAMC,aAAa,GAAG,IAAIH,KAAK,CAACI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,MAAMC,QAAQ,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAC9CC,IAAI,GAAGC,aAAa,CAACC,WAAW;EAChCC,KAAK;EACLC,MAAM;EACNC,KAAK,GAAG,CAAC;EACTC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7BC,MAAM;EACNC,KAAK,GAAG,KAAK;EACbC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,IAAIC,iBAAiB,EAAEC,qBAAqB,EAAEC,iBAAiB;EAE/D,MAAMC,QAAQ,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,OAAO,GAAGzB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACE,QAAQ,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACuB,kBAAkB,CAAC,GAAG5B,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAAC4B,UAAU,CAAC,CAAC,CAAC;EACzE,MAAM;IACJC;EACF,CAAC,GAAG5B,QAAQ,CAAC,CAAC;EACdF,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpB,IAAIC,gBAAgB;IAEpB,CAACA,gBAAgB,GAAGP,OAAO,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,gBAAgB,CAACE,QAAQ,CAACC,QAAQ,CAAC1B,aAAa,CAAC2B,aAAa,CAAC;EACzH,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACC,CAAC,CAAC,GAAGrC,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACrD,MAAM,CAACiC,CAAC,CAAC,GAAGtC,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACrD,MAAM,CAACkC,CAAC,CAAC,GAAGvC,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACrD,MAAM,CAACmC,EAAE,CAAC,GAAGxC,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACtD,MAAM,CAACoC,EAAE,CAAC,GAAGzC,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACtD,MAAM,CAACqC,QAAQ,CAAC,GAAG1C,KAAK,CAAC2B,QAAQ,CAAC,MAAM,IAAI1B,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EAC5DL,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpB,IAAIY,iBAAiB,EAAEC,iBAAiB,EAAEC,qBAAqB;IAE/D,MAAMX,QAAQ,GAAG,CAACS,iBAAiB,GAAGlB,OAAO,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGU,iBAAiB,CAACT,QAAQ;IACpG,IAAI,CAACA,QAAQ,EAAE;IACfA,QAAQ,CAACY,aAAa,CAACtC,IAAI,CAACuC,SAAS,CAAC,CAAC,CAAC;IACxC;IACA;;IAEAV,CAAC,CAACW,IAAI,CAACxC,IAAI,CAACuC,SAAS,CAACjC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACtCwB,CAAC,CAACU,IAAI,CAACxC,IAAI,CAACuC,SAAS,CAACjC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACtCyB,CAAC,CAACS,IAAI,CAACxC,IAAI,CAACuC,SAAS,CAACjC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC0B,EAAE,CAACQ,IAAI,CAACV,CAAC,CAAC,CAACW,GAAG,CAACZ,CAAC,CAAC;IACjBI,EAAE,CAACO,IAAI,CAACT,CAAC,CAAC,CAACU,GAAG,CAACZ,CAAC,CAAC;IACjBX,QAAQ,CAACwB,YAAY,CAACT,EAAE,EAAED,EAAE,CAAC,CAACW,SAAS,CAAC,CAAC;IACzCvB,kBAAkB,CAACwB,kBAAkB,CAAChD,aAAa,EAAEsB,QAAQ,CAAC;IAC9D,MAAM2B,yBAAyB,GAAGzB,kBAAkB,CAAC0B,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;IACvE;IACA;IACA;;IAEArB,QAAQ,CAACsB,kBAAkB,CAAC,CAAC;IAC7B,IAAIxC,KAAK,EAAEc,UAAU,CAAC,CAAC,CAAC,CAAC;;IAEzBI,QAAQ,CAACuB,MAAM,CAAC,CAAC,CAAC,CAAC;;IAEnBvB,QAAQ,CAACwB,eAAe,CAACL,yBAAyB,CAAC;IACnD,CAACT,iBAAiB,GAAGrB,QAAQ,CAACU,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,iBAAiB,CAACe,yBAAyB,CAAC/B,kBAAkB,CAAC,CAAC,CAAC;;IAE3H,IAAIb,MAAM,EAAE;MACV,MAAM6C,QAAQ,GAAG1B,QAAQ,CAAC2B,YAAY,CAAC,UAAU,CAAC;MAClD3B,QAAQ,CAAC4B,SAAS,CAAC,CAACF,QAAQ,CAACG,IAAI,CAAChD,MAAM,CAAC,EAAE,CAAC6C,QAAQ,CAACI,IAAI,CAACjD,MAAM,CAAC,EAAE,CAAC6C,QAAQ,CAACK,IAAI,CAAClD,MAAM,CAAC,CAAC;IAC5F,CAAC,CAAC;;IAGF,CAAC8B,qBAAqB,GAAGX,QAAQ,CAACgC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGrB,qBAAqB,CAACsB,OAAO,CAACzB,QAAQ,CAAC;IACzG,IAAI0B,KAAK,GAAG,CAAC;IACb,IAAIzD,KAAK,EAAEyD,KAAK,GAAGzD,KAAK,GAAG+B,QAAQ,CAAC2B,CAAC,CAAC,CAAC;;IAEvC,IAAIzD,MAAM,EAAEwD,KAAK,GAAGxD,MAAM,GAAG8B,QAAQ,CAAC4B,CAAC,CAAC,CAAC;;IAEzC,IAAIzD,KAAK,EAAEuD,KAAK,GAAGvD,KAAK,GAAG6B,QAAQ,CAAC6B,CAAC,CAAC,CAAC;;IAEvC,IAAIH,KAAK,KAAK,CAAC,EAAElC,QAAQ,CAACkC,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEA,KAAK,CAAC;IACpDlC,QAAQ,CAACsC,oBAAoB,CAAC,CAAC;IAC/BtC,QAAQ,CAACuC,UAAU,CAACb,QAAQ,CAACc,WAAW,GAAG,IAAI;EACjD,CAAC,EAAE,CAAClE,IAAI,EAAEG,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAEC,KAAK,EAAEc,UAAU,EAAEJ,QAAQ,EAAEE,kBAAkB,EAAES,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACnI;EACA;;EAEA,MAAMiC,GAAG,GAAG3E,KAAK,CAAC4E,OAAO,CAAC,OAAO;IAC/BnD,OAAO;IACPF;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACPvB,KAAK,CAAC6E,mBAAmB,CAAC1D,IAAI,EAAE,MAAMwD,GAAG,EAAE,CAACA,GAAG,CAAC,CAAC;EACjD,OAAO,aAAa3E,KAAK,CAAC8E,aAAa,CAAC,OAAO,EAAE5D,KAAK,EAAE,aAAalB,KAAK,CAAC8E,aAAa,CAAC,OAAO,EAAE;IAChGC,GAAG,EAAExD;EACP,CAAC,EAAE,aAAavB,KAAK,CAAC8E,aAAa,CAAC,MAAM,EAAE;IAC1CC,GAAG,EAAEtD;EACP,CAAC,EAAER,QAAQ,EAAED,KAAK,GAAG,aAAahB,KAAK,CAAC8E,aAAa,CAAC9E,KAAK,CAACgF,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC5D,iBAAiB,GAAGK,OAAO,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACZ,qBAAqB,GAAGD,iBAAiB,CAACc,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGb,qBAAqB,CAAC6C,WAAW,KAAK,aAAalE,KAAK,CAAC8E,aAAa,CAAC,YAAY,EAAE;IACjSG,IAAI,EAAE,CAAC,CAAC3D,iBAAiB,GAAGG,OAAO,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,iBAAiB,CAACY,QAAQ,CAACgC,WAAW;EACxG,CAAC,CAAC,EAAE,aAAalE,KAAK,CAAC8E,aAAa,CAAC3E,IAAI,EAAE;IACzC+E,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE9E,aAAa,CAAC;IAClC+E,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAM1E,aAAa,GAAG;EACpB;EACA;EACAC,WAAW,EAAE;IACX,WAAW,EAAE,CAAC;MACZ,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,eAAe;MACpB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,WAAW;MAChB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,gBAAgB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,CAAC,gBAAgB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,gBAAgB;MACrB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,gBAAgB;MACrB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,aAAa;MAClB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,cAAc;MACnB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,gBAAgB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,eAAe;MACpB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,eAAe;MACpB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,UAAU;MACf,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,WAAW;MAChB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,eAAe;MACpB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,aAAa;MAClB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,gBAAgB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,gBAAgB;MACrB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,gBAAgB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,gBAAgB;MACrB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,gBAAgB;MACrB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC,kBAAkB;MACxB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC,iBAAiB;MACvB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,CAAC;IACR,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE;IACP,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE,iBAAiB;MACtB,MAAM,EAAE;IACV,CAAC,EAAE;MACD,GAAG,EAAE,iBAAiB;MACtB,GAAG,EAAE,kBAAkB;MACvB,GAAG,EAAE;IACP,CAAC,CAAC;IACF,KAAK,EAAE;MACL,MAAM,EAAE,iBAAiB;MACzB,MAAM,EAAE,iBAAiB;MACzB,MAAM,EAAE,iBAAiB;MACzB,MAAM,EAAE,iBAAiB;MACzB,OAAO,EAAE,iBAAiB;MAC1B,QAAQ,EAAE;IACZ;EACF,CAAC;EACD;EACA;EACA0B,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAC1zY,CAAC;AAED,SAAS9B,QAAQ,EAAEG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}