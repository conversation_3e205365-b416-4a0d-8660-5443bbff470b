{"ast": null, "code": "import { Vector2 } from \"three\";\nconst DepthLimitedBlurShader = {\n  defines: {\n    KERNEL_RADIUS: 4,\n    DEPTH_PACKING: 1,\n    PERSPECTIVE_CAMERA: 1\n  },\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    size: {\n      value: /* @__PURE__ */new Vector2(512, 512)\n    },\n    sampleUvOffsets: {\n      value: [/* @__PURE__ */new Vector2(0, 0)]\n    },\n    sampleWeights: {\n      value: [1]\n    },\n    tDepth: {\n      value: null\n    },\n    cameraNear: {\n      value: 10\n    },\n    cameraFar: {\n      value: 1e3\n    },\n    depthCutoff: {\n      value: 10\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    #include <common>\n\n    uniform vec2 size;\n\n    varying vec2 vUv;\n    varying vec2 vInvSize;\n\n    void main() {\n    \tvUv = uv;\n    \tvInvSize = 1.0 / size;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    #include <common>\n    #include <packing>\n\n    uniform sampler2D tDiffuse;\n    uniform sampler2D tDepth;\n\n    uniform float cameraNear;\n    uniform float cameraFar;\n    uniform float depthCutoff;\n\n    uniform vec2 sampleUvOffsets[ KERNEL_RADIUS + 1 ];\n    uniform float sampleWeights[ KERNEL_RADIUS + 1 ];\n\n    varying vec2 vUv;\n    varying vec2 vInvSize;\n\n    float getDepth( const in vec2 screenPosition ) {\n    \t#if DEPTH_PACKING == 1\n    \treturn unpackRGBAToDepth( texture2D( tDepth, screenPosition ) );\n    \t#else\n    \treturn texture2D( tDepth, screenPosition ).x;\n    \t#endif\n    }\n\n    float getViewZ( const in float depth ) {\n    \t#if PERSPECTIVE_CAMERA == 1\n    \treturn perspectiveDepthToViewZ( depth, cameraNear, cameraFar );\n    \t#else\n    \treturn orthographicDepthToViewZ( depth, cameraNear, cameraFar );\n    \t#endif\n    }\n\n    void main() {\n    \tfloat depth = getDepth( vUv );\n    \tif( depth >= ( 1.0 - EPSILON ) ) {\n    \t\tdiscard;\n    \t}\n\n    \tfloat centerViewZ = -getViewZ( depth );\n    \tbool rBreak = false, lBreak = false;\n\n    \tfloat weightSum = sampleWeights[0];\n    \tvec4 diffuseSum = texture2D( tDiffuse, vUv ) * weightSum;\n\n    \tfor( int i = 1; i <= KERNEL_RADIUS; i ++ ) {\n\n    \t\tfloat sampleWeight = sampleWeights[i];\n    \t\tvec2 sampleUvOffset = sampleUvOffsets[i] * vInvSize;\n\n    \t\tvec2 sampleUv = vUv + sampleUvOffset;\n    \t\tfloat viewZ = -getViewZ( getDepth( sampleUv ) );\n\n    \t\tif( abs( viewZ - centerViewZ ) > depthCutoff ) rBreak = true;\n\n    \t\tif( ! rBreak ) {\n    \t\t\tdiffuseSum += texture2D( tDiffuse, sampleUv ) * sampleWeight;\n    \t\t\tweightSum += sampleWeight;\n    \t\t}\n\n    \t\tsampleUv = vUv - sampleUvOffset;\n    \t\tviewZ = -getViewZ( getDepth( sampleUv ) );\n\n    \t\tif( abs( viewZ - centerViewZ ) > depthCutoff ) lBreak = true;\n\n    \t\tif( ! lBreak ) {\n    \t\t\tdiffuseSum += texture2D( tDiffuse, sampleUv ) * sampleWeight;\n    \t\t\tweightSum += sampleWeight;\n    \t\t}\n\n    \t}\n\n    \tgl_FragColor = diffuseSum / weightSum;\n    }\n  `)\n};\nconst BlurShaderUtils = {\n  createSampleWeights: (kernelRadius, stdDev) => {\n    const gaussian = (x, stdDev2) => {\n      return Math.exp(-(x * x) / (2 * (stdDev2 * stdDev2))) / (Math.sqrt(2 * Math.PI) * stdDev2);\n    };\n    const weights = [];\n    for (let i = 0; i <= kernelRadius; i++) {\n      weights.push(gaussian(i, stdDev));\n    }\n    return weights;\n  },\n  createSampleOffsets: (kernelRadius, uvIncrement) => {\n    const offsets = [];\n    for (let i = 0; i <= kernelRadius; i++) {\n      offsets.push(uvIncrement.clone().multiplyScalar(i));\n    }\n    return offsets;\n  },\n  configure: (shader, kernelRadius, stdDev, uvIncrement) => {\n    shader.defines[\"KERNEL_RADIUS\"] = kernelRadius;\n    shader.uniforms[\"sampleUvOffsets\"].value = BlurShaderUtils.createSampleOffsets(kernelRadius, uvIncrement);\n    shader.uniforms[\"sampleWeights\"].value = BlurShaderUtils.createSampleWeights(kernelRadius, stdDev);\n    shader.needsUpdate = true;\n  }\n};\nexport { BlurShaderUtils, DepthLimitedBlurShader };", "map": {"version": 3, "names": ["DepthLimited<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defines", "KERNEL_RADIUS", "DEPTH_PACKING", "PERSPECTIVE_CAMERA", "uniforms", "tDiffuse", "value", "size", "Vector2", "sampleUvOffsets", "sampleWeights", "tD<PERSON>h", "cameraNear", "cameraFar", "<PERSON><PERSON><PERSON><PERSON>", "vertexShader", "fragmentShader", "<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createSampleWeights", "kernelRadius", "stdDev", "gaussian", "x", "stdDev2", "Math", "exp", "sqrt", "PI", "weights", "i", "push", "createSampleOffsets", "uvIncrement", "offsets", "clone", "multiplyScalar", "configure", "shader", "needsUpdate"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\shaders\\DepthLimitedBlurShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type DepthLimitedBlurShaderDefines = {\n  DEPTH_PACKING: number\n  KERNEL_RADIUS: number\n  PERSPECTIVE_CAMERA: number\n}\n\nexport type DepthLimitedBlurShaderUniforms = {\n  cameraFar: IUniform<number>\n  cameraNear: IUniform<number>\n  depthCutoff: IUniform<number>\n  sampleUvOffsets: IUniform<Vector2[]>\n  sampleWeights: IUniform<number[]>\n  size: IUniform<Vector2>\n  tDepth: IUniform<Texture | null>\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface IDepthLimitedBlurShader\n  extends IShader<DepthLimitedBlurShaderUniforms, DepthLimitedBlurShaderDefines> {\n  defines: DepthLimitedBlurShaderDefines\n  needsUpdate?: boolean\n}\n\nexport const DepthLimitedBlurShader: IDepthLimitedBlurShader = {\n  defines: {\n    KERNEL_RADIUS: 4,\n    DEPTH_PACKING: 1,\n    PERSPECTIVE_CAMERA: 1,\n  },\n  uniforms: {\n    tDiffuse: { value: null },\n    size: { value: /* @__PURE__ */ new Vector2(512, 512) },\n    sampleUvOffsets: { value: [/* @__PURE__ */ new Vector2(0, 0)] },\n    sampleWeights: { value: [1.0] },\n    tDepth: { value: null },\n    cameraNear: { value: 10 },\n    cameraFar: { value: 1000 },\n    depthCutoff: { value: 10 },\n  },\n  vertexShader: /* glsl */ `\n    #include <common>\n\n    uniform vec2 size;\n\n    varying vec2 vUv;\n    varying vec2 vInvSize;\n\n    void main() {\n    \tvUv = uv;\n    \tvInvSize = 1.0 / size;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n    }\n  `,\n  fragmentShader: /* glsl */ `\n    #include <common>\n    #include <packing>\n\n    uniform sampler2D tDiffuse;\n    uniform sampler2D tDepth;\n\n    uniform float cameraNear;\n    uniform float cameraFar;\n    uniform float depthCutoff;\n\n    uniform vec2 sampleUvOffsets[ KERNEL_RADIUS + 1 ];\n    uniform float sampleWeights[ KERNEL_RADIUS + 1 ];\n\n    varying vec2 vUv;\n    varying vec2 vInvSize;\n\n    float getDepth( const in vec2 screenPosition ) {\n    \t#if DEPTH_PACKING == 1\n    \treturn unpackRGBAToDepth( texture2D( tDepth, screenPosition ) );\n    \t#else\n    \treturn texture2D( tDepth, screenPosition ).x;\n    \t#endif\n    }\n\n    float getViewZ( const in float depth ) {\n    \t#if PERSPECTIVE_CAMERA == 1\n    \treturn perspectiveDepthToViewZ( depth, cameraNear, cameraFar );\n    \t#else\n    \treturn orthographicDepthToViewZ( depth, cameraNear, cameraFar );\n    \t#endif\n    }\n\n    void main() {\n    \tfloat depth = getDepth( vUv );\n    \tif( depth >= ( 1.0 - EPSILON ) ) {\n    \t\tdiscard;\n    \t}\n\n    \tfloat centerViewZ = -getViewZ( depth );\n    \tbool rBreak = false, lBreak = false;\n\n    \tfloat weightSum = sampleWeights[0];\n    \tvec4 diffuseSum = texture2D( tDiffuse, vUv ) * weightSum;\n\n    \tfor( int i = 1; i <= KERNEL_RADIUS; i ++ ) {\n\n    \t\tfloat sampleWeight = sampleWeights[i];\n    \t\tvec2 sampleUvOffset = sampleUvOffsets[i] * vInvSize;\n\n    \t\tvec2 sampleUv = vUv + sampleUvOffset;\n    \t\tfloat viewZ = -getViewZ( getDepth( sampleUv ) );\n\n    \t\tif( abs( viewZ - centerViewZ ) > depthCutoff ) rBreak = true;\n\n    \t\tif( ! rBreak ) {\n    \t\t\tdiffuseSum += texture2D( tDiffuse, sampleUv ) * sampleWeight;\n    \t\t\tweightSum += sampleWeight;\n    \t\t}\n\n    \t\tsampleUv = vUv - sampleUvOffset;\n    \t\tviewZ = -getViewZ( getDepth( sampleUv ) );\n\n    \t\tif( abs( viewZ - centerViewZ ) > depthCutoff ) lBreak = true;\n\n    \t\tif( ! lBreak ) {\n    \t\t\tdiffuseSum += texture2D( tDiffuse, sampleUv ) * sampleWeight;\n    \t\t\tweightSum += sampleWeight;\n    \t\t}\n\n    \t}\n\n    \tgl_FragColor = diffuseSum / weightSum;\n    }\n  `,\n}\n\nexport const BlurShaderUtils = {\n  createSampleWeights: (kernelRadius: number, stdDev: number): number[] => {\n    const gaussian = (x: number, stdDev: number): number => {\n      return Math.exp(-(x * x) / (2.0 * (stdDev * stdDev))) / (Math.sqrt(2.0 * Math.PI) * stdDev)\n    }\n\n    const weights: number[] = []\n\n    for (let i = 0; i <= kernelRadius; i++) {\n      weights.push(gaussian(i, stdDev))\n    }\n\n    return weights\n  },\n\n  createSampleOffsets: (kernelRadius: number, uvIncrement: Vector2): Vector2[] => {\n    const offsets: Vector2[] = []\n\n    for (let i = 0; i <= kernelRadius; i++) {\n      offsets.push(uvIncrement.clone().multiplyScalar(i))\n    }\n\n    return offsets\n  },\n\n  configure: (shader: IDepthLimitedBlurShader, kernelRadius: number, stdDev: number, uvIncrement: Vector2): void => {\n    shader.defines['KERNEL_RADIUS'] = kernelRadius\n    shader.uniforms['sampleUvOffsets'].value = BlurShaderUtils.createSampleOffsets(kernelRadius, uvIncrement)\n    shader.uniforms['sampleWeights'].value = BlurShaderUtils.createSampleWeights(kernelRadius, stdDev)\n    shader.needsUpdate = true\n  },\n}\n"], "mappings": ";AA2BO,MAAMA,sBAAA,GAAkD;EAC7DC,OAAA,EAAS;IACPC,aAAA,EAAe;IACfC,aAAA,EAAe;IACfC,kBAAA,EAAoB;EACtB;EACAC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,IAAA,EAAM;MAAED,KAAA,qBAA2BE,OAAA,CAAQ,KAAK,GAAG;IAAE;IACrDC,eAAA,EAAiB;MAAEH,KAAA,EAAO,oBAAqBE,OAAA,CAAQ,GAAG,CAAC,CAAC;IAAE;IAC9DE,aAAA,EAAe;MAAEJ,KAAA,EAAO,CAAC,CAAG;IAAE;IAC9BK,MAAA,EAAQ;MAAEL,KAAA,EAAO;IAAK;IACtBM,UAAA,EAAY;MAAEN,KAAA,EAAO;IAAG;IACxBO,SAAA,EAAW;MAAEP,KAAA,EAAO;IAAK;IACzBQ,WAAA,EAAa;MAAER,KAAA,EAAO;IAAG;EAC3B;EACAS,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAezBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2E7B;AAEO,MAAMC,eAAA,GAAkB;EAC7BC,mBAAA,EAAqBA,CAACC,YAAA,EAAsBC,MAAA,KAA6B;IACjE,MAAAC,QAAA,GAAWA,CAACC,CAAA,EAAWC,OAAA,KAA2B;MACtD,OAAOC,IAAA,CAAKC,GAAA,CAAI,EAAEH,CAAA,GAAIA,CAAA,KAAM,KAAOC,OAAA,GAASA,OAAA,EAAQ,KAAKC,IAAA,CAAKE,IAAA,CAAK,IAAMF,IAAA,CAAKG,EAAE,IAAIJ,OAAA;IAAA;IAGtF,MAAMK,OAAA,GAAoB;IAE1B,SAASC,CAAA,GAAI,GAAGA,CAAA,IAAKV,YAAA,EAAcU,CAAA,IAAK;MACtCD,OAAA,CAAQE,IAAA,CAAKT,QAAA,CAASQ,CAAA,EAAGT,MAAM,CAAC;IAClC;IAEO,OAAAQ,OAAA;EACT;EAEAG,mBAAA,EAAqBA,CAACZ,YAAA,EAAsBa,WAAA,KAAoC;IAC9E,MAAMC,OAAA,GAAqB;IAE3B,SAASJ,CAAA,GAAI,GAAGA,CAAA,IAAKV,YAAA,EAAcU,CAAA,IAAK;MACtCI,OAAA,CAAQH,IAAA,CAAKE,WAAA,CAAYE,KAAA,CAAQ,EAAAC,cAAA,CAAeN,CAAC,CAAC;IACpD;IAEO,OAAAI,OAAA;EACT;EAEAG,SAAA,EAAWA,CAACC,MAAA,EAAiClB,YAAA,EAAsBC,MAAA,EAAgBY,WAAA,KAA+B;IACzGK,MAAA,CAAArC,OAAA,CAAQ,eAAe,IAAImB,YAAA;IAClCkB,MAAA,CAAOjC,QAAA,CAAS,iBAAiB,EAAEE,KAAA,GAAQW,eAAA,CAAgBc,mBAAA,CAAoBZ,YAAA,EAAca,WAAW;IACxGK,MAAA,CAAOjC,QAAA,CAAS,eAAe,EAAEE,KAAA,GAAQW,eAAA,CAAgBC,mBAAA,CAAoBC,YAAA,EAAcC,MAAM;IACjGiB,MAAA,CAAOC,WAAA,GAAc;EACvB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}