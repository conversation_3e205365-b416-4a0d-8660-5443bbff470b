{"ast": null, "code": "import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\nfunction AdaptiveDpr({\n  pixelated\n}) {\n  const gl = useThree(state => state.gl);\n  const active = useThree(state => state.internal.active);\n  const current = useThree(state => state.performance.current);\n  const initialDpr = useThree(state => state.viewport.initialDpr);\n  const setDpr = useThree(state => state.setDpr); // Restore initial pixelratio on unmount\n\n  React.useEffect(() => {\n    const domElement = gl.domElement;\n    return () => {\n      if (active) setDpr(initialDpr);\n      if (pixelated && domElement) domElement.style.imageRendering = 'auto';\n    };\n  }, []); // Set adaptive pixelratio\n\n  React.useEffect(() => {\n    setDpr(current * initialDpr);\n    if (pixelated && gl.domElement) gl.domElement.style.imageRendering = current === 1 ? 'auto' : 'pixelated';\n  }, [current]);\n  return null;\n}\nexport { AdaptiveDpr };", "map": {"version": 3, "names": ["React", "useThree", "AdaptiveDpr", "pixelated", "gl", "state", "active", "internal", "current", "performance", "initialDpr", "viewport", "setDpr", "useEffect", "dom<PERSON>lement", "style", "imageRendering"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/AdaptiveDpr.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction AdaptiveDpr({\n  pixelated\n}) {\n  const gl = useThree(state => state.gl);\n  const active = useThree(state => state.internal.active);\n  const current = useThree(state => state.performance.current);\n  const initialDpr = useThree(state => state.viewport.initialDpr);\n  const setDpr = useThree(state => state.setDpr); // Restore initial pixelratio on unmount\n\n  React.useEffect(() => {\n    const domElement = gl.domElement;\n    return () => {\n      if (active) setDpr(initialDpr);\n      if (pixelated && domElement) domElement.style.imageRendering = 'auto';\n    };\n  }, []); // Set adaptive pixelratio\n\n  React.useEffect(() => {\n    setDpr(current * initialDpr);\n    if (pixelated && gl.domElement) gl.domElement.style.imageRendering = current === 1 ? 'auto' : 'pixelated';\n  }, [current]);\n  return null;\n}\n\nexport { AdaptiveDpr };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,WAAWA,CAAC;EACnBC;AACF,CAAC,EAAE;EACD,MAAMC,EAAE,GAAGH,QAAQ,CAACI,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,MAAM,GAAGL,QAAQ,CAACI,KAAK,IAAIA,KAAK,CAACE,QAAQ,CAACD,MAAM,CAAC;EACvD,MAAME,OAAO,GAAGP,QAAQ,CAACI,KAAK,IAAIA,KAAK,CAACI,WAAW,CAACD,OAAO,CAAC;EAC5D,MAAME,UAAU,GAAGT,QAAQ,CAACI,KAAK,IAAIA,KAAK,CAACM,QAAQ,CAACD,UAAU,CAAC;EAC/D,MAAME,MAAM,GAAGX,QAAQ,CAACI,KAAK,IAAIA,KAAK,CAACO,MAAM,CAAC,CAAC,CAAC;;EAEhDZ,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,MAAMC,UAAU,GAAGV,EAAE,CAACU,UAAU;IAChC,OAAO,MAAM;MACX,IAAIR,MAAM,EAAEM,MAAM,CAACF,UAAU,CAAC;MAC9B,IAAIP,SAAS,IAAIW,UAAU,EAAEA,UAAU,CAACC,KAAK,CAACC,cAAc,GAAG,MAAM;IACvE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERhB,KAAK,CAACa,SAAS,CAAC,MAAM;IACpBD,MAAM,CAACJ,OAAO,GAAGE,UAAU,CAAC;IAC5B,IAAIP,SAAS,IAAIC,EAAE,CAACU,UAAU,EAAEV,EAAE,CAACU,UAAU,CAACC,KAAK,CAACC,cAAc,GAAGR,OAAO,KAAK,CAAC,GAAG,MAAM,GAAG,WAAW;EAC3G,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAO,IAAI;AACb;AAEA,SAASN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}