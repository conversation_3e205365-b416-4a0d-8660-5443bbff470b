{"ast": null, "code": "import { UniformsUtils, ShaderMaterial } from \"three\";\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { CopyShader } from \"../shaders/CopyShader.js\";\nclass TexturePass extends Pass {\n  constructor(map, opacity) {\n    super();\n    const shader = CopyShader;\n    this.map = map;\n    this.opacity = opacity !== void 0 ? opacity : 1;\n    this.uniforms = UniformsUtils.clone(shader.uniforms);\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n      depthTest: false,\n      depthWrite: false,\n      premultipliedAlpha: true\n    });\n    this.needsSwap = false;\n    this.fsQuad = new FullScreenQuad(null);\n  }\n  render(renderer, writeBuffer, readBuffer) {\n    const oldAutoClear = renderer.autoClear;\n    renderer.autoClear = false;\n    this.fsQuad.material = this.material;\n    this.uniforms[\"opacity\"].value = this.opacity;\n    this.uniforms[\"tDiffuse\"].value = this.map;\n    this.material.transparent = this.opacity < 1;\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer);\n    if (this.clear) renderer.clear();\n    this.fsQuad.render(renderer);\n    renderer.autoClear = oldAutoClear;\n  }\n  dispose() {\n    this.material.dispose();\n    this.fsQuad.dispose();\n  }\n}\nexport { TexturePass };", "map": {"version": 3, "names": ["TexturePass", "Pass", "constructor", "map", "opacity", "shader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uniforms", "UniformsUtils", "clone", "material", "ShaderMaterial", "vertexShader", "fragmentShader", "depthTest", "depthWrite", "premultipliedAlpha", "needsSwap", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "oldAutoClear", "autoClear", "value", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderToScreen", "clear", "dispose"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\TexturePass.js"], "sourcesContent": ["import { ShaderMaterial, UniformsUtils } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\n\nclass TexturePass extends Pass {\n  constructor(map, opacity) {\n    super()\n\n    const shader = CopyShader\n\n    this.map = map\n    this.opacity = opacity !== undefined ? opacity : 1.0\n\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n      depthTest: false,\n      depthWrite: false,\n      premultipliedAlpha: true,\n    })\n\n    this.needsSwap = false\n\n    this.fsQuad = new FullScreenQuad(null)\n  }\n\n  render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */) {\n    const oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    this.fsQuad.material = this.material\n\n    this.uniforms['opacity'].value = this.opacity\n    this.uniforms['tDiffuse'].value = this.map\n    this.material.transparent = this.opacity < 1.0\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n    if (this.clear) renderer.clear()\n    this.fsQuad.render(renderer)\n\n    renderer.autoClear = oldAutoClear\n  }\n\n  dispose() {\n    this.material.dispose()\n\n    this.fsQuad.dispose()\n  }\n}\n\nexport { TexturePass }\n"], "mappings": ";;;AAIA,MAAMA,WAAA,SAAoBC,IAAA,CAAK;EAC7BC,YAAYC,GAAA,EAAKC,OAAA,EAAS;IACxB,MAAO;IAEP,MAAMC,MAAA,GAASC,UAAA;IAEf,KAAKH,GAAA,GAAMA,GAAA;IACX,KAAKC,OAAA,GAAUA,OAAA,KAAY,SAAYA,OAAA,GAAU;IAEjD,KAAKG,QAAA,GAAWC,aAAA,CAAcC,KAAA,CAAMJ,MAAA,CAAOE,QAAQ;IAEnD,KAAKG,QAAA,GAAW,IAAIC,cAAA,CAAe;MACjCJ,QAAA,EAAU,KAAKA,QAAA;MACfK,YAAA,EAAcP,MAAA,CAAOO,YAAA;MACrBC,cAAA,EAAgBR,MAAA,CAAOQ,cAAA;MACvBC,SAAA,EAAW;MACXC,UAAA,EAAY;MACZC,kBAAA,EAAoB;IAC1B,CAAK;IAED,KAAKC,SAAA,GAAY;IAEjB,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,IAAI;EACtC;EAEDC,OAAOC,QAAA,EAAUC,WAAA,EAAaC,UAAA,EAAyC;IACrE,MAAMC,YAAA,GAAeH,QAAA,CAASI,SAAA;IAC9BJ,QAAA,CAASI,SAAA,GAAY;IAErB,KAAKP,MAAA,CAAOR,QAAA,GAAW,KAAKA,QAAA;IAE5B,KAAKH,QAAA,CAAS,SAAS,EAAEmB,KAAA,GAAQ,KAAKtB,OAAA;IACtC,KAAKG,QAAA,CAAS,UAAU,EAAEmB,KAAA,GAAQ,KAAKvB,GAAA;IACvC,KAAKO,QAAA,CAASiB,WAAA,GAAc,KAAKvB,OAAA,GAAU;IAE3CiB,QAAA,CAASO,eAAA,CAAgB,KAAKC,cAAA,GAAiB,OAAON,UAAU;IAChE,IAAI,KAAKO,KAAA,EAAOT,QAAA,CAASS,KAAA,CAAO;IAChC,KAAKZ,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAE3BA,QAAA,CAASI,SAAA,GAAYD,YAAA;EACtB;EAEDO,QAAA,EAAU;IACR,KAAKrB,QAAA,CAASqB,OAAA,CAAS;IAEvB,KAAKb,MAAA,CAAOa,OAAA,CAAS;EACtB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}