const axios = require('axios');

async function testActivityAPI() {
  try {
    console.log('🧪 Testing Activity Logs API...');

    // First, test login to get a token
    console.log('\n🔐 Testing login...');
    let token = null;
    try {
      const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      if (loginResponse.data.success) {
        token = loginResponse.data.data.token;
        console.log('✅ Login successful, token obtained');
      } else {
        console.log('❌ Login failed:', loginResponse.data.message);
        return;
      }
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data?.message || error.message);
      return;
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test activity logs stats endpoint
    console.log('\n📊 Testing activity logs stats...');
    try {
      const statsResponse = await axios.get('http://localhost:8000/api/admin/activity-logs/stats', { headers });
      console.log('✅ Activity stats endpoint working');
      console.log('Stats overview:', JSON.stringify(statsResponse.data.data.overview, null, 2));
    } catch (error) {
      console.log('❌ Activity stats endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test activity logs list endpoint
    console.log('\n📋 Testing activity logs list...');
    try {
      const logsResponse = await axios.get('http://localhost:8000/api/admin/activity-logs?limit=5', { headers });
      console.log('✅ Activity logs endpoint working');
      console.log(`Found ${logsResponse.data.data.logs.length} logs`);
      logsResponse.data.data.logs.forEach((log, index) => {
        console.log(`${index + 1}. [${log.Severity}] ${log.Action} ${log.EntityType} - ${log.Description} (${log.CreatedAt})`);
      });
    } catch (error) {
      console.log('❌ Activity logs endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test actions endpoint
    console.log('\n🎯 Testing actions endpoint...');
    try {
      const actionsResponse = await axios.get('http://localhost:8000/api/admin/activity-logs/actions', { headers });
      console.log('✅ Actions endpoint working');
      console.log('Available actions:', actionsResponse.data.data);
    } catch (error) {
      console.log('❌ Actions endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test entity types endpoint
    console.log('\n🏷️ Testing entity types endpoint...');
    try {
      const entityTypesResponse = await axios.get('http://localhost:8000/api/admin/activity-logs/entity-types', { headers });
      console.log('✅ Entity types endpoint working');
      console.log('Available entity types:', entityTypesResponse.data.data);
    } catch (error) {
      console.log('❌ Entity types endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    // Test dashboard endpoint to see if activity stats are included
    console.log('\n🏠 Testing dashboard endpoint...');
    try {
      const dashboardResponse = await axios.get('http://localhost:8000/api/admin/dashboard', { headers });
      console.log('✅ Dashboard endpoint working');
      if (dashboardResponse.data.data.activityStats) {
        console.log('✅ Activity stats included in dashboard');
        console.log('Activity stats:', JSON.stringify(dashboardResponse.data.data.activityStats, null, 2));
      } else {
        console.log('⚠️ Activity stats not included in dashboard');
      }
    } catch (error) {
      console.log('❌ Dashboard endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n✅ Activity API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testActivityAPI();
