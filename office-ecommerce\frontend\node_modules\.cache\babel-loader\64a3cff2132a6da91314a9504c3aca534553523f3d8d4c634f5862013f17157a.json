{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nfunction create(type, effect) {\n  const El = type + 'Geometry';\n  return /*#__PURE__*/React.forwardRef(({\n    args,\n    children,\n    ...props\n  }, fref) => {\n    const ref = React.useRef(null);\n    React.useImperativeHandle(fref, () => ref.current);\n    React.useLayoutEffect(() => void (effect == null ? void 0 : effect(ref.current)));\n    return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      ref: ref\n    }, props), /*#__PURE__*/React.createElement(El, {\n      attach: \"geometry\",\n      args: args\n    }), children);\n  });\n}\nconst Box = create('box');\nconst Circle = create('circle');\nconst Cone = create('cone');\nconst Cylinder = create('cylinder');\nconst Sphere = create('sphere');\nconst Plane = create('plane');\nconst Tube = create('tube');\nconst Torus = create('torus');\nconst TorusKnot = create('torusKnot');\nconst Tetrahedron = create('tetrahedron');\nconst Ring = create('ring');\nconst Polyhedron = create('polyhedron');\nconst Icosahedron = create('icosahedron');\nconst Octahedron = create('octahedron');\nconst Dodecahedron = create('dodecahedron');\nconst Extrude = create('extrude');\nconst Lathe = create('lathe');\nconst Capsule = create('capsule');\nconst Shape = create('shape', ({\n  geometry\n}) => {\n  // Calculate UVs (by https://discourse.threejs.org/u/prisoner849)\n  // https://discourse.threejs.org/t/custom-shape-in-image-not-working/49348/10\n  const pos = geometry.attributes.position;\n  const b3 = new THREE.Box3().setFromBufferAttribute(pos);\n  const b3size = new THREE.Vector3();\n  b3.getSize(b3size);\n  const uv = [];\n  let x = 0,\n    y = 0,\n    u = 0,\n    v = 0;\n  for (let i = 0; i < pos.count; i++) {\n    x = pos.getX(i);\n    y = pos.getY(i);\n    u = (x - b3.min.x) / b3size.x;\n    v = (y - b3.min.y) / b3size.y;\n    uv.push(u, v);\n  }\n  geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uv, 2));\n});\nexport { Box, Capsule, Circle, Cone, Cylinder, Dodecahedron, Extrude, Icosahedron, Lathe, Octahedron, Plane, Polyhedron, Ring, Shape, Sphere, Tetrahedron, Torus, TorusKnot, Tube };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "create", "type", "effect", "El", "forwardRef", "args", "children", "props", "fref", "ref", "useRef", "useImperativeHandle", "current", "useLayoutEffect", "createElement", "attach", "Box", "Circle", "Cone", "<PERSON><PERSON><PERSON>", "Sphere", "Plane", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tetrahedron", "Ring", "Polyhedron", "Icosahedron", "Octahedron", "Dodecahedron", "Extrude", "Lathe", "Capsule", "<PERSON><PERSON><PERSON>", "geometry", "pos", "attributes", "position", "b3", "Box3", "setFromBufferAttribute", "b3size", "Vector3", "getSize", "uv", "x", "y", "u", "v", "i", "count", "getX", "getY", "min", "push", "setAttribute", "Float32BufferAttribute"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/shapes.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\nfunction create(type, effect) {\n  const El = type + 'Geometry';\n  return /*#__PURE__*/React.forwardRef(({\n    args,\n    children,\n    ...props\n  }, fref) => {\n    const ref = React.useRef(null);\n    React.useImperativeHandle(fref, () => ref.current);\n    React.useLayoutEffect(() => void (effect == null ? void 0 : effect(ref.current)));\n    return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      ref: ref\n    }, props), /*#__PURE__*/React.createElement(El, {\n      attach: \"geometry\",\n      args: args\n    }), children);\n  });\n}\n\nconst Box = create('box');\nconst Circle = create('circle');\nconst Cone = create('cone');\nconst Cylinder = create('cylinder');\nconst Sphere = create('sphere');\nconst Plane = create('plane');\nconst Tube = create('tube');\nconst Torus = create('torus');\nconst TorusKnot = create('torusKnot');\nconst Tetrahedron = create('tetrahedron');\nconst Ring = create('ring');\nconst Polyhedron = create('polyhedron');\nconst Icosahedron = create('icosahedron');\nconst Octahedron = create('octahedron');\nconst Dodecahedron = create('dodecahedron');\nconst Extrude = create('extrude');\nconst Lathe = create('lathe');\nconst Capsule = create('capsule');\nconst Shape = create('shape', ({\n  geometry\n}) => {\n  // Calculate UVs (by https://discourse.threejs.org/u/prisoner849)\n  // https://discourse.threejs.org/t/custom-shape-in-image-not-working/49348/10\n  const pos = geometry.attributes.position;\n  const b3 = new THREE.Box3().setFromBufferAttribute(pos);\n  const b3size = new THREE.Vector3();\n  b3.getSize(b3size);\n  const uv = [];\n  let x = 0,\n      y = 0,\n      u = 0,\n      v = 0;\n\n  for (let i = 0; i < pos.count; i++) {\n    x = pos.getX(i);\n    y = pos.getY(i);\n    u = (x - b3.min.x) / b3size.x;\n    v = (y - b3.min.y) / b3size.y;\n    uv.push(u, v);\n  }\n\n  geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uv, 2));\n});\n\nexport { Box, Capsule, Circle, Cone, Cylinder, Dodecahedron, Extrude, Icosahedron, Lathe, Octahedron, Plane, Polyhedron, Ring, Shape, Sphere, Tetrahedron, Torus, TorusKnot, Tube };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,MAAMA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC5B,MAAMC,EAAE,GAAGF,IAAI,GAAG,UAAU;EAC5B,OAAO,aAAaH,KAAK,CAACM,UAAU,CAAC,CAAC;IACpCC,IAAI;IACJC,QAAQ;IACR,GAAGC;EACL,CAAC,EAAEC,IAAI,KAAK;IACV,MAAMC,GAAG,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;IAC9BZ,KAAK,CAACa,mBAAmB,CAACH,IAAI,EAAE,MAAMC,GAAG,CAACG,OAAO,CAAC;IAClDd,KAAK,CAACe,eAAe,CAAC,MAAM,MAAMX,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,GAAG,CAACG,OAAO,CAAC,CAAC,CAAC;IACjF,OAAO,aAAad,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAEjB,QAAQ,CAAC;MACvDY,GAAG,EAAEA;IACP,CAAC,EAAEF,KAAK,CAAC,EAAE,aAAaT,KAAK,CAACgB,aAAa,CAACX,EAAE,EAAE;MAC9CY,MAAM,EAAE,UAAU;MAClBV,IAAI,EAAEA;IACR,CAAC,CAAC,EAAEC,QAAQ,CAAC;EACf,CAAC,CAAC;AACJ;AAEA,MAAMU,GAAG,GAAGhB,MAAM,CAAC,KAAK,CAAC;AACzB,MAAMiB,MAAM,GAAGjB,MAAM,CAAC,QAAQ,CAAC;AAC/B,MAAMkB,IAAI,GAAGlB,MAAM,CAAC,MAAM,CAAC;AAC3B,MAAMmB,QAAQ,GAAGnB,MAAM,CAAC,UAAU,CAAC;AACnC,MAAMoB,MAAM,GAAGpB,MAAM,CAAC,QAAQ,CAAC;AAC/B,MAAMqB,KAAK,GAAGrB,MAAM,CAAC,OAAO,CAAC;AAC7B,MAAMsB,IAAI,GAAGtB,MAAM,CAAC,MAAM,CAAC;AAC3B,MAAMuB,KAAK,GAAGvB,MAAM,CAAC,OAAO,CAAC;AAC7B,MAAMwB,SAAS,GAAGxB,MAAM,CAAC,WAAW,CAAC;AACrC,MAAMyB,WAAW,GAAGzB,MAAM,CAAC,aAAa,CAAC;AACzC,MAAM0B,IAAI,GAAG1B,MAAM,CAAC,MAAM,CAAC;AAC3B,MAAM2B,UAAU,GAAG3B,MAAM,CAAC,YAAY,CAAC;AACvC,MAAM4B,WAAW,GAAG5B,MAAM,CAAC,aAAa,CAAC;AACzC,MAAM6B,UAAU,GAAG7B,MAAM,CAAC,YAAY,CAAC;AACvC,MAAM8B,YAAY,GAAG9B,MAAM,CAAC,cAAc,CAAC;AAC3C,MAAM+B,OAAO,GAAG/B,MAAM,CAAC,SAAS,CAAC;AACjC,MAAMgC,KAAK,GAAGhC,MAAM,CAAC,OAAO,CAAC;AAC7B,MAAMiC,OAAO,GAAGjC,MAAM,CAAC,SAAS,CAAC;AACjC,MAAMkC,KAAK,GAAGlC,MAAM,CAAC,OAAO,EAAE,CAAC;EAC7BmC;AACF,CAAC,KAAK;EACJ;EACA;EACA,MAAMC,GAAG,GAAGD,QAAQ,CAACE,UAAU,CAACC,QAAQ;EACxC,MAAMC,EAAE,GAAG,IAAIxC,KAAK,CAACyC,IAAI,CAAC,CAAC,CAACC,sBAAsB,CAACL,GAAG,CAAC;EACvD,MAAMM,MAAM,GAAG,IAAI3C,KAAK,CAAC4C,OAAO,CAAC,CAAC;EAClCJ,EAAE,CAACK,OAAO,CAACF,MAAM,CAAC;EAClB,MAAMG,EAAE,GAAG,EAAE;EACb,IAAIC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC;EAET,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,GAAG,CAACe,KAAK,EAAED,CAAC,EAAE,EAAE;IAClCJ,CAAC,GAAGV,GAAG,CAACgB,IAAI,CAACF,CAAC,CAAC;IACfH,CAAC,GAAGX,GAAG,CAACiB,IAAI,CAACH,CAAC,CAAC;IACfF,CAAC,GAAG,CAACF,CAAC,GAAGP,EAAE,CAACe,GAAG,CAACR,CAAC,IAAIJ,MAAM,CAACI,CAAC;IAC7BG,CAAC,GAAG,CAACF,CAAC,GAAGR,EAAE,CAACe,GAAG,CAACP,CAAC,IAAIL,MAAM,CAACK,CAAC;IAC7BF,EAAE,CAACU,IAAI,CAACP,CAAC,EAAEC,CAAC,CAAC;EACf;EAEAd,QAAQ,CAACqB,YAAY,CAAC,IAAI,EAAE,IAAIzD,KAAK,CAAC0D,sBAAsB,CAACZ,EAAE,EAAE,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC;AAEF,SAAS7B,GAAG,EAAEiB,OAAO,EAAEhB,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEW,YAAY,EAAEC,OAAO,EAAEH,WAAW,EAAEI,KAAK,EAAEH,UAAU,EAAER,KAAK,EAAEM,UAAU,EAAED,IAAI,EAAEQ,KAAK,EAAEd,MAAM,EAAEK,WAAW,EAAEF,KAAK,EAAEC,SAAS,EAAEF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}