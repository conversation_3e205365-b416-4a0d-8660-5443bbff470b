{"ast": null, "code": "export class MeshBVHNode {\n  constructor() {\n\n    // internal nodes have boundingData, left, right, and splitAxis\n    // leaf nodes have offset and count (referring to primitives in the mesh geometry)\n  }\n}", "map": {"version": 3, "names": ["MeshBVHNode", "constructor"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/core/MeshBVHNode.js"], "sourcesContent": ["export class MeshBVHNode {\n\n\tconstructor() {\n\n\t\t// internal nodes have boundingData, left, right, and splitAxis\n\t\t// leaf nodes have offset and count (referring to primitives in the mesh geometry)\n\n\t}\n\n}\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,CAAC;EAExBC,WAAWA,CAAA,EAAG;;IAEb;IACA;EAAA;AAIF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}