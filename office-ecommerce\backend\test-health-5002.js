const axios = require('axios');

async function testHealth() {
  try {
    console.log('🏥 Testing Backend Health on port 5002...');

    // Test basic health endpoint
    console.log('\n🔍 Testing /health endpoint...');
    try {
      const healthResponse = await axios.get('http://127.0.0.1:5002/health');
      console.log('✅ Health endpoint working');
      console.log('Response:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Health endpoint failed:', error.code, error.message);
    }

    // Test API health endpoint
    console.log('\n🔍 Testing /api/health endpoint...');
    try {
      const apiHealthResponse = await axios.get('http://127.0.0.1:5002/api/health');
      console.log('✅ API health endpoint working');
      console.log('Response:', JSON.stringify(apiHealthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ API health endpoint failed:', error.code, error.message);
    }

    // Test login endpoint
    console.log('\n🔍 Testing login endpoint...');
    try {
      const loginResponse = await axios.post('http://127.0.0.1:5002/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      console.log('✅ Login endpoint working');
      console.log('Response:', JSON.stringify(loginResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Login endpoint failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n✅ Health check completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testHealth();
