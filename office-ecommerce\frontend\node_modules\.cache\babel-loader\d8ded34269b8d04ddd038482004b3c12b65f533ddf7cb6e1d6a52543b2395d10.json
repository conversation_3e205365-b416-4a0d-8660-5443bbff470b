{"ast": null, "code": "import { BufferAttribute, BufferGeometry, Float32BufferAttribute, InstancedBufferAttribute, InterleavedBuffer, InterleavedBufferAttribute, TriangleFanDrawMode, TriangleStripDrawMode, TrianglesDrawMode, Vector3 } from 'three';\nfunction computeMikkTSpaceTangents(geometry, MikkTSpace, negateSign = true) {\n  if (!MikkTSpace || !MikkTSpace.isReady) {\n    throw new Error('BufferGeometryUtils: Initialized MikkTSpace library required.');\n  }\n  if (!geometry.hasAttribute('position') || !geometry.hasAttribute('normal') || !geometry.hasAttribute('uv')) {\n    throw new Error('BufferGeometryUtils: Tangents require \"position\", \"normal\", and \"uv\" attributes.');\n  }\n  function getAttributeArray(attribute) {\n    if (attribute.normalized || attribute.isInterleavedBufferAttribute) {\n      const dstArray = new Float32Array(attribute.count * attribute.itemSize);\n      for (let i = 0, j = 0; i < attribute.count; i++) {\n        dstArray[j++] = attribute.getX(i);\n        dstArray[j++] = attribute.getY(i);\n        if (attribute.itemSize > 2) {\n          dstArray[j++] = attribute.getZ(i);\n        }\n      }\n      return dstArray;\n    }\n    if (attribute.array instanceof Float32Array) {\n      return attribute.array;\n    }\n    return new Float32Array(attribute.array);\n  }\n\n  // MikkTSpace algorithm requires non-indexed input.\n\n  const _geometry = geometry.index ? geometry.toNonIndexed() : geometry;\n\n  // Compute vertex tangents.\n\n  const tangents = MikkTSpace.generateTangents(getAttributeArray(_geometry.attributes.position), getAttributeArray(_geometry.attributes.normal), getAttributeArray(_geometry.attributes.uv));\n\n  // Texture coordinate convention of glTF differs from the apparent\n  // default of the MikkTSpace library; .w component must be flipped.\n\n  if (negateSign) {\n    for (let i = 3; i < tangents.length; i += 4) {\n      tangents[i] *= -1;\n    }\n  }\n\n  //\n\n  _geometry.setAttribute('tangent', new BufferAttribute(tangents, 4));\n  if (geometry !== _geometry) {\n    geometry.copy(_geometry);\n  }\n  return geometry;\n}\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nfunction mergeGeometries(geometries, useGroups = false) {\n  const isIndexed = geometries[0].index !== null;\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes));\n  const morphAttributesUsed = new Set(Object.keys(geometries[0].morphAttributes));\n  const attributes = {};\n  const morphAttributes = {};\n  const morphTargetsRelative = geometries[0].morphTargetsRelative;\n  const mergedGeometry = new BufferGeometry();\n  let offset = 0;\n  for (let i = 0; i < geometries.length; ++i) {\n    const geometry = geometries[i];\n    let attributesCount = 0;\n\n    // ensure that all geometries are indexed, or none\n\n    if (isIndexed !== (geometry.index !== null)) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.');\n      return null;\n    }\n\n    // gather attributes, exit early if they're different\n\n    for (const name in geometry.attributes) {\n      if (!attributesUsed.has(name)) {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.');\n        return null;\n      }\n      if (attributes[name] === undefined) attributes[name] = [];\n      attributes[name].push(geometry.attributes[name]);\n      attributesCount++;\n    }\n\n    // ensure geometries have the same number of attributes\n\n    if (attributesCount !== attributesUsed.size) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. Make sure all geometries have the same number of attributes.');\n      return null;\n    }\n\n    // gather morph attributes, exit early if they're different\n\n    if (morphTargetsRelative !== geometry.morphTargetsRelative) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. .morphTargetsRelative must be consistent throughout all geometries.');\n      return null;\n    }\n    for (const name in geometry.morphAttributes) {\n      if (!morphAttributesUsed.has(name)) {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '.  .morphAttributes must be consistent throughout all geometries.');\n        return null;\n      }\n      if (morphAttributes[name] === undefined) morphAttributes[name] = [];\n      morphAttributes[name].push(geometry.morphAttributes[name]);\n    }\n    if (useGroups) {\n      let count;\n      if (isIndexed) {\n        count = geometry.index.count;\n      } else if (geometry.attributes.position !== undefined) {\n        count = geometry.attributes.position.count;\n      } else {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. The geometry must have either an index or a position attribute');\n        return null;\n      }\n      mergedGeometry.addGroup(offset, count, i);\n      offset += count;\n    }\n  }\n\n  // merge indices\n\n  if (isIndexed) {\n    let indexOffset = 0;\n    const mergedIndex = [];\n    for (let i = 0; i < geometries.length; ++i) {\n      const index = geometries[i].index;\n      for (let j = 0; j < index.count; ++j) {\n        mergedIndex.push(index.getX(j) + indexOffset);\n      }\n      indexOffset += geometries[i].attributes.position.count;\n    }\n    mergedGeometry.setIndex(mergedIndex);\n  }\n\n  // merge attributes\n\n  for (const name in attributes) {\n    const mergedAttribute = mergeAttributes(attributes[name]);\n    if (!mergedAttribute) {\n      console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' attribute.');\n      return null;\n    }\n    mergedGeometry.setAttribute(name, mergedAttribute);\n  }\n\n  // merge morph attributes\n\n  for (const name in morphAttributes) {\n    const numMorphTargets = morphAttributes[name][0].length;\n    if (numMorphTargets === 0) break;\n    mergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {};\n    mergedGeometry.morphAttributes[name] = [];\n    for (let i = 0; i < numMorphTargets; ++i) {\n      const morphAttributesToMerge = [];\n      for (let j = 0; j < morphAttributes[name].length; ++j) {\n        morphAttributesToMerge.push(morphAttributes[name][j][i]);\n      }\n      const mergedMorphAttribute = mergeAttributes(morphAttributesToMerge);\n      if (!mergedMorphAttribute) {\n        console.error('THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' morphAttribute.');\n        return null;\n      }\n      mergedGeometry.morphAttributes[name].push(mergedMorphAttribute);\n    }\n  }\n  return mergedGeometry;\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nfunction mergeAttributes(attributes) {\n  let TypedArray;\n  let itemSize;\n  let normalized;\n  let gpuType = -1;\n  let arrayLength = 0;\n  for (let i = 0; i < attributes.length; ++i) {\n    const attribute = attributes[i];\n    if (attribute.isInterleavedBufferAttribute) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. InterleavedBufferAttributes are not supported.');\n      return null;\n    }\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor;\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.');\n      return null;\n    }\n    if (itemSize === undefined) itemSize = attribute.itemSize;\n    if (itemSize !== attribute.itemSize) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.');\n      return null;\n    }\n    if (normalized === undefined) normalized = attribute.normalized;\n    if (normalized !== attribute.normalized) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.');\n      return null;\n    }\n    if (gpuType === -1) gpuType = attribute.gpuType;\n    if (gpuType !== attribute.gpuType) {\n      console.error('THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.gpuType must be consistent across matching attributes.');\n      return null;\n    }\n    arrayLength += attribute.array.length;\n  }\n  const array = new TypedArray(arrayLength);\n  let offset = 0;\n  for (let i = 0; i < attributes.length; ++i) {\n    array.set(attributes[i].array, offset);\n    offset += attributes[i].array.length;\n  }\n  const result = new BufferAttribute(array, itemSize, normalized);\n  if (gpuType !== undefined) {\n    result.gpuType = gpuType;\n  }\n  return result;\n}\n\n/**\n * @param {BufferAttribute}\n * @return {BufferAttribute}\n */\nexport function deepCloneAttribute(attribute) {\n  if (attribute.isInstancedInterleavedBufferAttribute || attribute.isInterleavedBufferAttribute) {\n    return deinterleaveAttribute(attribute);\n  }\n  if (attribute.isInstancedBufferAttribute) {\n    return new InstancedBufferAttribute().copy(attribute);\n  }\n  return new BufferAttribute().copy(attribute);\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nfunction interleaveAttributes(attributes) {\n  // Interleaves the provided attributes into an InterleavedBuffer and returns\n  // a set of InterleavedBufferAttributes for each attribute\n  let TypedArray;\n  let arrayLength = 0;\n  let stride = 0;\n\n  // calculate the length and type of the interleavedBuffer\n  for (let i = 0, l = attributes.length; i < l; ++i) {\n    const attribute = attributes[i];\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor;\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('AttributeBuffers of different types cannot be interleaved');\n      return null;\n    }\n    arrayLength += attribute.array.length;\n    stride += attribute.itemSize;\n  }\n\n  // Create the set of buffer attributes\n  const interleavedBuffer = new InterleavedBuffer(new TypedArray(arrayLength), stride);\n  let offset = 0;\n  const res = [];\n  const getters = ['getX', 'getY', 'getZ', 'getW'];\n  const setters = ['setX', 'setY', 'setZ', 'setW'];\n  for (let j = 0, l = attributes.length; j < l; j++) {\n    const attribute = attributes[j];\n    const itemSize = attribute.itemSize;\n    const count = attribute.count;\n    const iba = new InterleavedBufferAttribute(interleavedBuffer, itemSize, offset, attribute.normalized);\n    res.push(iba);\n    offset += itemSize;\n\n    // Move the data for each attribute into the new interleavedBuffer\n    // at the appropriate offset\n    for (let c = 0; c < count; c++) {\n      for (let k = 0; k < itemSize; k++) {\n        iba[setters[k]](c, attribute[getters[k]](c));\n      }\n    }\n  }\n  return res;\n}\n\n// returns a new, non-interleaved version of the provided attribute\nexport function deinterleaveAttribute(attribute) {\n  const cons = attribute.data.array.constructor;\n  const count = attribute.count;\n  const itemSize = attribute.itemSize;\n  const normalized = attribute.normalized;\n  const array = new cons(count * itemSize);\n  let newAttribute;\n  if (attribute.isInstancedInterleavedBufferAttribute) {\n    newAttribute = new InstancedBufferAttribute(array, itemSize, normalized, attribute.meshPerAttribute);\n  } else {\n    newAttribute = new BufferAttribute(array, itemSize, normalized);\n  }\n  for (let i = 0; i < count; i++) {\n    newAttribute.setX(i, attribute.getX(i));\n    if (itemSize >= 2) {\n      newAttribute.setY(i, attribute.getY(i));\n    }\n    if (itemSize >= 3) {\n      newAttribute.setZ(i, attribute.getZ(i));\n    }\n    if (itemSize >= 4) {\n      newAttribute.setW(i, attribute.getW(i));\n    }\n  }\n  return newAttribute;\n}\n\n// deinterleaves all attributes on the geometry\nexport function deinterleaveGeometry(geometry) {\n  const attributes = geometry.attributes;\n  const morphTargets = geometry.morphTargets;\n  const attrMap = new Map();\n  for (const key in attributes) {\n    const attr = attributes[key];\n    if (attr.isInterleavedBufferAttribute) {\n      if (!attrMap.has(attr)) {\n        attrMap.set(attr, deinterleaveAttribute(attr));\n      }\n      attributes[key] = attrMap.get(attr);\n    }\n  }\n  for (const key in morphTargets) {\n    const attr = morphTargets[key];\n    if (attr.isInterleavedBufferAttribute) {\n      if (!attrMap.has(attr)) {\n        attrMap.set(attr, deinterleaveAttribute(attr));\n      }\n      morphTargets[key] = attrMap.get(attr);\n    }\n  }\n}\n\n/**\n * @param {Array<BufferGeometry>} geometry\n * @return {number}\n */\nfunction estimateBytesUsed(geometry) {\n  // Return the estimated memory used by this geometry in bytes\n  // Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n  // for InterleavedBufferAttributes.\n  let mem = 0;\n  for (const name in geometry.attributes) {\n    const attr = geometry.getAttribute(name);\n    mem += attr.count * attr.itemSize * attr.array.BYTES_PER_ELEMENT;\n  }\n  const indices = geometry.getIndex();\n  mem += indices ? indices.count * indices.itemSize * indices.array.BYTES_PER_ELEMENT : 0;\n  return mem;\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry}\n */\nfunction mergeVertices(geometry, tolerance = 1e-4) {\n  tolerance = Math.max(tolerance, Number.EPSILON);\n\n  // Generate an index buffer if the geometry doesn't have one, or optimize it\n  // if it's already available.\n  const hashToIndex = {};\n  const indices = geometry.getIndex();\n  const positions = geometry.getAttribute('position');\n  const vertexCount = indices ? indices.count : positions.count;\n\n  // next value for triangle indices\n  let nextIndex = 0;\n\n  // attributes and new attribute arrays\n  const attributeNames = Object.keys(geometry.attributes);\n  const tmpAttributes = {};\n  const tmpMorphAttributes = {};\n  const newIndices = [];\n  const getters = ['getX', 'getY', 'getZ', 'getW'];\n  const setters = ['setX', 'setY', 'setZ', 'setW'];\n\n  // Initialize the arrays, allocating space conservatively. Extra\n  // space will be trimmed in the last step.\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i];\n    const attr = geometry.attributes[name];\n    tmpAttributes[name] = new BufferAttribute(new attr.array.constructor(attr.count * attr.itemSize), attr.itemSize, attr.normalized);\n    const morphAttr = geometry.morphAttributes[name];\n    if (morphAttr) {\n      tmpMorphAttributes[name] = new BufferAttribute(new morphAttr.array.constructor(morphAttr.count * morphAttr.itemSize), morphAttr.itemSize, morphAttr.normalized);\n    }\n  }\n\n  // convert the error tolerance to an amount of decimal places to truncate to\n  const decimalShift = Math.log10(1 / tolerance);\n  const shiftMultiplier = Math.pow(10, decimalShift);\n  for (let i = 0; i < vertexCount; i++) {\n    const index = indices ? indices.getX(i) : i;\n\n    // Generate a hash for the vertex attributes at the current index 'i'\n    let hash = '';\n    for (let j = 0, l = attributeNames.length; j < l; j++) {\n      const name = attributeNames[j];\n      const attribute = geometry.getAttribute(name);\n      const itemSize = attribute.itemSize;\n      for (let k = 0; k < itemSize; k++) {\n        // double tilde truncates the decimal value\n        hash += `${~~(attribute[getters[k]](index) * shiftMultiplier)},`;\n      }\n    }\n\n    // Add another reference to the vertex if it's already\n    // used by another index\n    if (hash in hashToIndex) {\n      newIndices.push(hashToIndex[hash]);\n    } else {\n      // copy data to the new index in the temporary attributes\n      for (let j = 0, l = attributeNames.length; j < l; j++) {\n        const name = attributeNames[j];\n        const attribute = geometry.getAttribute(name);\n        const morphAttr = geometry.morphAttributes[name];\n        const itemSize = attribute.itemSize;\n        const newarray = tmpAttributes[name];\n        const newMorphArrays = tmpMorphAttributes[name];\n        for (let k = 0; k < itemSize; k++) {\n          const getterFunc = getters[k];\n          const setterFunc = setters[k];\n          newarray[setterFunc](nextIndex, attribute[getterFunc](index));\n          if (morphAttr) {\n            for (let m = 0, ml = morphAttr.length; m < ml; m++) {\n              newMorphArrays[m][setterFunc](nextIndex, morphAttr[m][getterFunc](index));\n            }\n          }\n        }\n      }\n      hashToIndex[hash] = nextIndex;\n      newIndices.push(nextIndex);\n      nextIndex++;\n    }\n  }\n\n  // generate result BufferGeometry\n  const result = geometry.clone();\n  for (const name in geometry.attributes) {\n    const tmpAttribute = tmpAttributes[name];\n    result.setAttribute(name, new BufferAttribute(tmpAttribute.array.slice(0, nextIndex * tmpAttribute.itemSize), tmpAttribute.itemSize, tmpAttribute.normalized));\n    if (!(name in tmpMorphAttributes)) continue;\n    for (let j = 0; j < tmpMorphAttributes[name].length; j++) {\n      const tmpMorphAttribute = tmpMorphAttributes[name][j];\n      result.morphAttributes[name][j] = new BufferAttribute(tmpMorphAttribute.array.slice(0, nextIndex * tmpMorphAttribute.itemSize), tmpMorphAttribute.itemSize, tmpMorphAttribute.normalized);\n    }\n  }\n\n  // indices\n\n  result.setIndex(newIndices);\n  return result;\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nfunction toTrianglesDrawMode(geometry, drawMode) {\n  if (drawMode === TrianglesDrawMode) {\n    console.warn('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.');\n    return geometry;\n  }\n  if (drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode) {\n    let index = geometry.getIndex();\n\n    // generate index if not present\n\n    if (index === null) {\n      const indices = [];\n      const position = geometry.getAttribute('position');\n      if (position !== undefined) {\n        for (let i = 0; i < position.count; i++) {\n          indices.push(i);\n        }\n        geometry.setIndex(indices);\n        index = geometry.getIndex();\n      } else {\n        console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.');\n        return geometry;\n      }\n    }\n\n    //\n\n    const numberOfTriangles = index.count - 2;\n    const newIndices = [];\n    if (drawMode === TriangleFanDrawMode) {\n      // gl.TRIANGLE_FAN\n\n      for (let i = 1; i <= numberOfTriangles; i++) {\n        newIndices.push(index.getX(0));\n        newIndices.push(index.getX(i));\n        newIndices.push(index.getX(i + 1));\n      }\n    } else {\n      // gl.TRIANGLE_STRIP\n\n      for (let i = 0; i < numberOfTriangles; i++) {\n        if (i % 2 === 0) {\n          newIndices.push(index.getX(i));\n          newIndices.push(index.getX(i + 1));\n          newIndices.push(index.getX(i + 2));\n        } else {\n          newIndices.push(index.getX(i + 2));\n          newIndices.push(index.getX(i + 1));\n          newIndices.push(index.getX(i));\n        }\n      }\n    }\n    if (newIndices.length / 3 !== numberOfTriangles) {\n      console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.');\n    }\n\n    // build final geometry\n\n    const newGeometry = geometry.clone();\n    newGeometry.setIndex(newIndices);\n    newGeometry.clearGroups();\n    return newGeometry;\n  } else {\n    console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode);\n    return geometry;\n  }\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nfunction computeMorphedAttributes(object) {\n  const _vA = new Vector3();\n  const _vB = new Vector3();\n  const _vC = new Vector3();\n  const _tempA = new Vector3();\n  const _tempB = new Vector3();\n  const _tempC = new Vector3();\n  const _morphA = new Vector3();\n  const _morphB = new Vector3();\n  const _morphC = new Vector3();\n  function _calculateMorphedAttributeData(object, attribute, morphAttribute, morphTargetsRelative, a, b, c, modifiedAttributeArray) {\n    _vA.fromBufferAttribute(attribute, a);\n    _vB.fromBufferAttribute(attribute, b);\n    _vC.fromBufferAttribute(attribute, c);\n    const morphInfluences = object.morphTargetInfluences;\n    if (morphAttribute && morphInfluences) {\n      _morphA.set(0, 0, 0);\n      _morphB.set(0, 0, 0);\n      _morphC.set(0, 0, 0);\n      for (let i = 0, il = morphAttribute.length; i < il; i++) {\n        const influence = morphInfluences[i];\n        const morph = morphAttribute[i];\n        if (influence === 0) continue;\n        _tempA.fromBufferAttribute(morph, a);\n        _tempB.fromBufferAttribute(morph, b);\n        _tempC.fromBufferAttribute(morph, c);\n        if (morphTargetsRelative) {\n          _morphA.addScaledVector(_tempA, influence);\n          _morphB.addScaledVector(_tempB, influence);\n          _morphC.addScaledVector(_tempC, influence);\n        } else {\n          _morphA.addScaledVector(_tempA.sub(_vA), influence);\n          _morphB.addScaledVector(_tempB.sub(_vB), influence);\n          _morphC.addScaledVector(_tempC.sub(_vC), influence);\n        }\n      }\n      _vA.add(_morphA);\n      _vB.add(_morphB);\n      _vC.add(_morphC);\n    }\n    if (object.isSkinnedMesh) {\n      object.applyBoneTransform(a, _vA);\n      object.applyBoneTransform(b, _vB);\n      object.applyBoneTransform(c, _vC);\n    }\n    modifiedAttributeArray[a * 3 + 0] = _vA.x;\n    modifiedAttributeArray[a * 3 + 1] = _vA.y;\n    modifiedAttributeArray[a * 3 + 2] = _vA.z;\n    modifiedAttributeArray[b * 3 + 0] = _vB.x;\n    modifiedAttributeArray[b * 3 + 1] = _vB.y;\n    modifiedAttributeArray[b * 3 + 2] = _vB.z;\n    modifiedAttributeArray[c * 3 + 0] = _vC.x;\n    modifiedAttributeArray[c * 3 + 1] = _vC.y;\n    modifiedAttributeArray[c * 3 + 2] = _vC.z;\n  }\n  const geometry = object.geometry;\n  const material = object.material;\n  let a, b, c;\n  const index = geometry.index;\n  const positionAttribute = geometry.attributes.position;\n  const morphPosition = geometry.morphAttributes.position;\n  const morphTargetsRelative = geometry.morphTargetsRelative;\n  const normalAttribute = geometry.attributes.normal;\n  const morphNormal = geometry.morphAttributes.position;\n  const groups = geometry.groups;\n  const drawRange = geometry.drawRange;\n  let i, j, il, jl;\n  let group;\n  let start, end;\n  const modifiedPosition = new Float32Array(positionAttribute.count * positionAttribute.itemSize);\n  const modifiedNormal = new Float32Array(normalAttribute.count * normalAttribute.itemSize);\n  if (index !== null) {\n    // indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i];\n        start = Math.max(group.start, drawRange.start);\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count);\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = index.getX(j);\n          b = index.getX(j + 1);\n          c = index.getX(j + 2);\n          _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n          _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start);\n      end = Math.min(index.count, drawRange.start + drawRange.count);\n      for (i = start, il = end; i < il; i += 3) {\n        a = index.getX(i);\n        b = index.getX(i + 1);\n        c = index.getX(i + 2);\n        _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n        _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n      }\n    }\n  } else {\n    // non-indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i];\n        start = Math.max(group.start, drawRange.start);\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count);\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = j;\n          b = j + 1;\n          c = j + 2;\n          _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n          _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start);\n      end = Math.min(positionAttribute.count, drawRange.start + drawRange.count);\n      for (i = start, il = end; i < il; i += 3) {\n        a = i;\n        b = i + 1;\n        c = i + 2;\n        _calculateMorphedAttributeData(object, positionAttribute, morphPosition, morphTargetsRelative, a, b, c, modifiedPosition);\n        _calculateMorphedAttributeData(object, normalAttribute, morphNormal, morphTargetsRelative, a, b, c, modifiedNormal);\n      }\n    }\n  }\n  const morphedPositionAttribute = new Float32BufferAttribute(modifiedPosition, 3);\n  const morphedNormalAttribute = new Float32BufferAttribute(modifiedNormal, 3);\n  return {\n    positionAttribute: positionAttribute,\n    normalAttribute: normalAttribute,\n    morphedPositionAttribute: morphedPositionAttribute,\n    morphedNormalAttribute: morphedNormalAttribute\n  };\n}\nfunction mergeGroups(geometry) {\n  if (geometry.groups.length === 0) {\n    console.warn('THREE.BufferGeometryUtils.mergeGroups(): No groups are defined. Nothing to merge.');\n    return geometry;\n  }\n  let groups = geometry.groups;\n\n  // sort groups by material index\n\n  groups = groups.sort((a, b) => {\n    if (a.materialIndex !== b.materialIndex) return a.materialIndex - b.materialIndex;\n    return a.start - b.start;\n  });\n\n  // create index for non-indexed geometries\n\n  if (geometry.getIndex() === null) {\n    const positionAttribute = geometry.getAttribute('position');\n    const indices = [];\n    for (let i = 0; i < positionAttribute.count; i += 3) {\n      indices.push(i, i + 1, i + 2);\n    }\n    geometry.setIndex(indices);\n  }\n\n  // sort index\n\n  const index = geometry.getIndex();\n  const newIndices = [];\n  for (let i = 0; i < groups.length; i++) {\n    const group = groups[i];\n    const groupStart = group.start;\n    const groupLength = groupStart + group.count;\n    for (let j = groupStart; j < groupLength; j++) {\n      newIndices.push(index.getX(j));\n    }\n  }\n  geometry.dispose(); // Required to force buffer recreation\n  geometry.setIndex(newIndices);\n\n  // update groups indices\n\n  let start = 0;\n  for (let i = 0; i < groups.length; i++) {\n    const group = groups[i];\n    group.start = start;\n    start += group.count;\n  }\n\n  // merge groups\n\n  let currentGroup = groups[0];\n  geometry.groups = [currentGroup];\n  for (let i = 1; i < groups.length; i++) {\n    const group = groups[i];\n    if (currentGroup.materialIndex === group.materialIndex) {\n      currentGroup.count += group.count;\n    } else {\n      currentGroup = group;\n      geometry.groups.push(currentGroup);\n    }\n  }\n  return geometry;\n}\n\n// Creates a new, non-indexed geometry with smooth normals everywhere except faces that meet at\n// an angle greater than the crease angle.\nfunction toCreasedNormals(geometry, creaseAngle = Math.PI / 3 /* 60 degrees */) {\n  const creaseDot = Math.cos(creaseAngle);\n  const hashMultiplier = (1 + 1e-10) * 1e2;\n\n  // reusable vertors\n  const verts = [new Vector3(), new Vector3(), new Vector3()];\n  const tempVec1 = new Vector3();\n  const tempVec2 = new Vector3();\n  const tempNorm = new Vector3();\n  const tempNorm2 = new Vector3();\n\n  // hashes a vector\n  function hashVertex(v) {\n    const x = ~~(v.x * hashMultiplier);\n    const y = ~~(v.y * hashMultiplier);\n    const z = ~~(v.z * hashMultiplier);\n    return `${x},${y},${z}`;\n  }\n  const resultGeometry = geometry.toNonIndexed();\n  const posAttr = resultGeometry.attributes.position;\n  const vertexMap = {};\n\n  // find all the normals shared by commonly located vertices\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i;\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0);\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1);\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2);\n    tempVec1.subVectors(c, b);\n    tempVec2.subVectors(a, b);\n\n    // add the normal to the map for all vertices\n    const normal = new Vector3().crossVectors(tempVec1, tempVec2).normalize();\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n];\n      const hash = hashVertex(vert);\n      if (!(hash in vertexMap)) {\n        vertexMap[hash] = [];\n      }\n      vertexMap[hash].push(normal);\n    }\n  }\n\n  // average normals from all vertices that share a common location if they are within the\n  // provided crease threshold\n  const normalArray = new Float32Array(posAttr.count * 3);\n  const normAttr = new BufferAttribute(normalArray, 3, false);\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    // get the face normal for this vertex\n    const i3 = 3 * i;\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0);\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1);\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2);\n    tempVec1.subVectors(c, b);\n    tempVec2.subVectors(a, b);\n    tempNorm.crossVectors(tempVec1, tempVec2).normalize();\n\n    // average all normals that meet the threshold and set the normal value\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n];\n      const hash = hashVertex(vert);\n      const otherNormals = vertexMap[hash];\n      tempNorm2.set(0, 0, 0);\n      for (let k = 0, lk = otherNormals.length; k < lk; k++) {\n        const otherNorm = otherNormals[k];\n        if (tempNorm.dot(otherNorm) > creaseDot) {\n          tempNorm2.add(otherNorm);\n        }\n      }\n      tempNorm2.normalize();\n      normAttr.setXYZ(i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z);\n    }\n  }\n  resultGeometry.setAttribute('normal', normAttr);\n  return resultGeometry;\n}\nfunction mergeBufferGeometries(geometries, useGroups = false) {\n  console.warn('THREE.BufferGeometryUtils: mergeBufferGeometries() has been renamed to mergeGeometries().'); // @deprecated, r151\n  return mergeGeometries(geometries, useGroups);\n}\nfunction mergeBufferAttributes(attributes) {\n  console.warn('THREE.BufferGeometryUtils: mergeBufferAttributes() has been renamed to mergeAttributes().'); // @deprecated, r151\n  return mergeAttributes(attributes);\n}\nexport { computeMikkTSpaceTangents, mergeGeometries, mergeBufferGeometries, mergeAttributes, mergeBufferAttributes, interleaveAttributes, estimateBytesUsed, mergeVertices, toTrianglesDrawMode, computeMorphedAttributes, mergeGroups, toCreasedNormals };", "map": {"version": 3, "names": ["BufferAttribute", "BufferGeometry", "Float32BufferAttribute", "InstancedBufferAttribute", "InterleavedBuffer", "InterleavedBufferAttribute", "TriangleFanDrawMode", "TriangleStripDrawMode", "TrianglesDrawMode", "Vector3", "computeMikkTSpaceTangents", "geometry", "MikkTSpace", "negateSign", "isReady", "Error", "hasAttribute", "getAttributeArray", "attribute", "normalized", "isInterleavedBufferAttribute", "dstArray", "Float32Array", "count", "itemSize", "i", "j", "getX", "getY", "getZ", "array", "_geometry", "index", "toNonIndexed", "tangents", "generateTangents", "attributes", "position", "normal", "uv", "length", "setAttribute", "copy", "mergeGeometries", "geometries", "useGroups", "isIndexed", "attributesUsed", "Set", "Object", "keys", "morphAttributesUsed", "morphAttributes", "morphTargetsRelative", "mergedGeometry", "offset", "attributesCount", "console", "error", "name", "has", "undefined", "push", "size", "addGroup", "indexOffset", "mergedIndex", "setIndex", "mergedAttribute", "mergeAttributes", "numMorphTargets", "morphAttributesToMerge", "mergedMorphAttribute", "TypedArray", "gpuType", "array<PERSON>ength", "constructor", "set", "result", "deepCloneAttribute", "isInstancedInterleavedBufferAttribute", "deinterleaveAttribute", "isInstancedBufferAttribute", "interleaveAttributes", "stride", "l", "interleavedBuffer", "res", "getters", "setters", "iba", "c", "k", "cons", "data", "newAttribute", "meshPerAttribute", "setX", "setY", "setZ", "setW", "getW", "deinterleaveGeometry", "morphTargets", "attrMap", "Map", "key", "attr", "get", "estimateBytesUsed", "mem", "getAttribute", "BYTES_PER_ELEMENT", "indices", "getIndex", "mergeVertices", "tolerance", "Math", "max", "Number", "EPSILON", "hashToIndex", "positions", "vertexCount", "nextIndex", "attributeNames", "tmpAttributes", "tmpMorphAttributes", "newIndices", "morphAttr", "decimalShift", "log10", "shiftMultiplier", "pow", "hash", "newarray", "newMorphArrays", "getterFunc", "set<PERSON><PERSON><PERSON><PERSON>", "m", "ml", "clone", "tmpAttribute", "slice", "tmpMorphAttribute", "toTrianglesDrawMode", "drawMode", "warn", "numberOfTriangles", "newGeometry", "clearGroups", "computeMorphedAttributes", "object", "_vA", "_vB", "_vC", "_tempA", "_tempB", "_tempC", "_morphA", "_morphB", "_morphC", "_calculateMorphedAttributeData", "morphAttribute", "a", "b", "modifiedAttributeArray", "fromBufferAttribute", "morphInfluences", "morphTargetInfluences", "il", "influence", "morph", "addScaledVector", "sub", "add", "isSkinnedMesh", "applyBoneTransform", "x", "y", "z", "material", "positionAttribute", "morphPosition", "normalAttribute", "morphNormal", "groups", "drawRange", "jl", "group", "start", "end", "modifiedPosition", "modifiedNormal", "Array", "isArray", "min", "morphedPositionAttribute", "morphedNormalAttribute", "mergeGroups", "sort", "materialIndex", "groupStart", "groupLength", "dispose", "currentGroup", "toCreasedNormals", "creaseAngle", "PI", "creaseDot", "cos", "hashMultiplier", "verts", "tempVec1", "tempVec2", "tempNorm", "tempNorm2", "hashVertex", "v", "resultGeometry", "posAttr", "vertexMap", "i3", "subVectors", "crossVectors", "normalize", "n", "vert", "normalArray", "norm<PERSON>ttr", "otherNormals", "lk", "otherNorm", "dot", "setXYZ", "mergeBufferGeometries", "mergeBufferAttributes"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/three/examples/jsm/utils/BufferGeometryUtils.js"], "sourcesContent": ["import {\n\tBufferAttribute,\n\tBufferGeometry,\n\tFloat32BufferAttribute,\n\tInstancedBufferAttribute,\n\tInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tTriangleFanDrawMode,\n\tTriangleStripDrawMode,\n\tTrianglesDrawMode,\n\tVector3,\n} from 'three';\n\nfunction computeMikkTSpaceTangents( geometry, MikkTSpace, negateSign = true ) {\n\n\tif ( ! MikkTSpace || ! MikkTSpace.isReady ) {\n\n\t\tthrow new Error( 'BufferGeometryUtils: Initialized MikkTSpace library required.' );\n\n\t}\n\n\tif ( ! geometry.hasAttribute( 'position' ) || ! geometry.hasAttribute( 'normal' ) || ! geometry.hasAttribute( 'uv' ) ) {\n\n\t\tthrow new Error( 'BufferGeometryUtils: Tangents require \"position\", \"normal\", and \"uv\" attributes.' );\n\n\t}\n\n\tfunction getAttributeArray( attribute ) {\n\n\t\tif ( attribute.normalized || attribute.isInterleavedBufferAttribute ) {\n\n\t\t\tconst dstArray = new Float32Array( attribute.count * attribute.itemSize );\n\n\t\t\tfor ( let i = 0, j = 0; i < attribute.count; i ++ ) {\n\n\t\t\t\tdstArray[ j ++ ] = attribute.getX( i );\n\t\t\t\tdstArray[ j ++ ] = attribute.getY( i );\n\n\t\t\t\tif ( attribute.itemSize > 2 ) {\n\n\t\t\t\t\tdstArray[ j ++ ] = attribute.getZ( i );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn dstArray;\n\n\t\t}\n\n\t\tif ( attribute.array instanceof Float32Array ) {\n\n\t\t\treturn attribute.array;\n\n\t\t}\n\n\t\treturn new Float32Array( attribute.array );\n\n\t}\n\n\t// MikkTSpace algorithm requires non-indexed input.\n\n\tconst _geometry = geometry.index ? geometry.toNonIndexed() : geometry;\n\n\t// Compute vertex tangents.\n\n\tconst tangents = MikkTSpace.generateTangents(\n\n\t\tgetAttributeArray( _geometry.attributes.position ),\n\t\tgetAttributeArray( _geometry.attributes.normal ),\n\t\tgetAttributeArray( _geometry.attributes.uv )\n\n\t);\n\n\t// Texture coordinate convention of glTF differs from the apparent\n\t// default of the MikkTSpace library; .w component must be flipped.\n\n\tif ( negateSign ) {\n\n\t\tfor ( let i = 3; i < tangents.length; i += 4 ) {\n\n\t\t\ttangents[ i ] *= - 1;\n\n\t\t}\n\n\t}\n\n\t//\n\n\t_geometry.setAttribute( 'tangent', new BufferAttribute( tangents, 4 ) );\n\n\tif ( geometry !== _geometry ) {\n\n\t\tgeometry.copy( _geometry );\n\n\t}\n\n\treturn geometry;\n\n}\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nfunction mergeGeometries( geometries, useGroups = false ) {\n\n\tconst isIndexed = geometries[ 0 ].index !== null;\n\n\tconst attributesUsed = new Set( Object.keys( geometries[ 0 ].attributes ) );\n\tconst morphAttributesUsed = new Set( Object.keys( geometries[ 0 ].morphAttributes ) );\n\n\tconst attributes = {};\n\tconst morphAttributes = {};\n\n\tconst morphTargetsRelative = geometries[ 0 ].morphTargetsRelative;\n\n\tconst mergedGeometry = new BufferGeometry();\n\n\tlet offset = 0;\n\n\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\tconst geometry = geometries[ i ];\n\t\tlet attributesCount = 0;\n\n\t\t// ensure that all geometries are indexed, or none\n\n\t\tif ( isIndexed !== ( geometry.index !== null ) ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// gather attributes, exit early if they're different\n\n\t\tfor ( const name in geometry.attributes ) {\n\n\t\t\tif ( ! attributesUsed.has( name ) ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tif ( attributes[ name ] === undefined ) attributes[ name ] = [];\n\n\t\t\tattributes[ name ].push( geometry.attributes[ name ] );\n\n\t\t\tattributesCount ++;\n\n\t\t}\n\n\t\t// ensure geometries have the same number of attributes\n\n\t\tif ( attributesCount !== attributesUsed.size ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. Make sure all geometries have the same number of attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// gather morph attributes, exit early if they're different\n\n\t\tif ( morphTargetsRelative !== geometry.morphTargetsRelative ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. .morphTargetsRelative must be consistent throughout all geometries.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tfor ( const name in geometry.morphAttributes ) {\n\n\t\t\tif ( ! morphAttributesUsed.has( name ) ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '.  .morphAttributes must be consistent throughout all geometries.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tif ( morphAttributes[ name ] === undefined ) morphAttributes[ name ] = [];\n\n\t\t\tmorphAttributes[ name ].push( geometry.morphAttributes[ name ] );\n\n\t\t}\n\n\t\tif ( useGroups ) {\n\n\t\t\tlet count;\n\n\t\t\tif ( isIndexed ) {\n\n\t\t\t\tcount = geometry.index.count;\n\n\t\t\t} else if ( geometry.attributes.position !== undefined ) {\n\n\t\t\t\tcount = geometry.attributes.position.count;\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. The geometry must have either an index or a position attribute' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tmergedGeometry.addGroup( offset, count, i );\n\n\t\t\toffset += count;\n\n\t\t}\n\n\t}\n\n\t// merge indices\n\n\tif ( isIndexed ) {\n\n\t\tlet indexOffset = 0;\n\t\tconst mergedIndex = [];\n\n\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\tconst index = geometries[ i ].index;\n\n\t\t\tfor ( let j = 0; j < index.count; ++ j ) {\n\n\t\t\t\tmergedIndex.push( index.getX( j ) + indexOffset );\n\n\t\t\t}\n\n\t\t\tindexOffset += geometries[ i ].attributes.position.count;\n\n\t\t}\n\n\t\tmergedGeometry.setIndex( mergedIndex );\n\n\t}\n\n\t// merge attributes\n\n\tfor ( const name in attributes ) {\n\n\t\tconst mergedAttribute = mergeAttributes( attributes[ name ] );\n\n\t\tif ( ! mergedAttribute ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' attribute.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tmergedGeometry.setAttribute( name, mergedAttribute );\n\n\t}\n\n\t// merge morph attributes\n\n\tfor ( const name in morphAttributes ) {\n\n\t\tconst numMorphTargets = morphAttributes[ name ][ 0 ].length;\n\n\t\tif ( numMorphTargets === 0 ) break;\n\n\t\tmergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {};\n\t\tmergedGeometry.morphAttributes[ name ] = [];\n\n\t\tfor ( let i = 0; i < numMorphTargets; ++ i ) {\n\n\t\t\tconst morphAttributesToMerge = [];\n\n\t\t\tfor ( let j = 0; j < morphAttributes[ name ].length; ++ j ) {\n\n\t\t\t\tmorphAttributesToMerge.push( morphAttributes[ name ][ j ][ i ] );\n\n\t\t\t}\n\n\t\t\tconst mergedMorphAttribute = mergeAttributes( morphAttributesToMerge );\n\n\t\t\tif ( ! mergedMorphAttribute ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' morphAttribute.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tmergedGeometry.morphAttributes[ name ].push( mergedMorphAttribute );\n\n\t\t}\n\n\t}\n\n\treturn mergedGeometry;\n\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nfunction mergeAttributes( attributes ) {\n\n\tlet TypedArray;\n\tlet itemSize;\n\tlet normalized;\n\tlet gpuType = - 1;\n\tlet arrayLength = 0;\n\n\tfor ( let i = 0; i < attributes.length; ++ i ) {\n\n\t\tconst attribute = attributes[ i ];\n\n\t\tif ( attribute.isInterleavedBufferAttribute ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. InterleavedBufferAttributes are not supported.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( TypedArray === undefined ) TypedArray = attribute.array.constructor;\n\t\tif ( TypedArray !== attribute.array.constructor ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( itemSize === undefined ) itemSize = attribute.itemSize;\n\t\tif ( itemSize !== attribute.itemSize ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( normalized === undefined ) normalized = attribute.normalized;\n\t\tif ( normalized !== attribute.normalized ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( gpuType === - 1 ) gpuType = attribute.gpuType;\n\t\tif ( gpuType !== attribute.gpuType ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.gpuType must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tarrayLength += attribute.array.length;\n\n\t}\n\n\tconst array = new TypedArray( arrayLength );\n\tlet offset = 0;\n\n\tfor ( let i = 0; i < attributes.length; ++ i ) {\n\n\t\tarray.set( attributes[ i ].array, offset );\n\n\t\toffset += attributes[ i ].array.length;\n\n\t}\n\n\tconst result = new BufferAttribute( array, itemSize, normalized );\n\tif ( gpuType !== undefined ) {\n\n\t\tresult.gpuType = gpuType;\n\n\t}\n\n\treturn result;\n\n}\n\n/**\n * @param {BufferAttribute}\n * @return {BufferAttribute}\n */\nexport function deepCloneAttribute( attribute ) {\n\n\tif ( attribute.isInstancedInterleavedBufferAttribute || attribute.isInterleavedBufferAttribute ) {\n\n\t\treturn deinterleaveAttribute( attribute );\n\n\t}\n\n\tif ( attribute.isInstancedBufferAttribute ) {\n\n\t\treturn new InstancedBufferAttribute().copy( attribute );\n\n\t}\n\n\treturn new BufferAttribute().copy( attribute );\n\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nfunction interleaveAttributes( attributes ) {\n\n\t// Interleaves the provided attributes into an InterleavedBuffer and returns\n\t// a set of InterleavedBufferAttributes for each attribute\n\tlet TypedArray;\n\tlet arrayLength = 0;\n\tlet stride = 0;\n\n\t// calculate the length and type of the interleavedBuffer\n\tfor ( let i = 0, l = attributes.length; i < l; ++ i ) {\n\n\t\tconst attribute = attributes[ i ];\n\n\t\tif ( TypedArray === undefined ) TypedArray = attribute.array.constructor;\n\t\tif ( TypedArray !== attribute.array.constructor ) {\n\n\t\t\tconsole.error( 'AttributeBuffers of different types cannot be interleaved' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tarrayLength += attribute.array.length;\n\t\tstride += attribute.itemSize;\n\n\t}\n\n\t// Create the set of buffer attributes\n\tconst interleavedBuffer = new InterleavedBuffer( new TypedArray( arrayLength ), stride );\n\tlet offset = 0;\n\tconst res = [];\n\tconst getters = [ 'getX', 'getY', 'getZ', 'getW' ];\n\tconst setters = [ 'setX', 'setY', 'setZ', 'setW' ];\n\n\tfor ( let j = 0, l = attributes.length; j < l; j ++ ) {\n\n\t\tconst attribute = attributes[ j ];\n\t\tconst itemSize = attribute.itemSize;\n\t\tconst count = attribute.count;\n\t\tconst iba = new InterleavedBufferAttribute( interleavedBuffer, itemSize, offset, attribute.normalized );\n\t\tres.push( iba );\n\n\t\toffset += itemSize;\n\n\t\t// Move the data for each attribute into the new interleavedBuffer\n\t\t// at the appropriate offset\n\t\tfor ( let c = 0; c < count; c ++ ) {\n\n\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\tiba[ setters[ k ] ]( c, attribute[ getters[ k ] ]( c ) );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\n// returns a new, non-interleaved version of the provided attribute\nexport function deinterleaveAttribute( attribute ) {\n\n\tconst cons = attribute.data.array.constructor;\n\tconst count = attribute.count;\n\tconst itemSize = attribute.itemSize;\n\tconst normalized = attribute.normalized;\n\n\tconst array = new cons( count * itemSize );\n\tlet newAttribute;\n\tif ( attribute.isInstancedInterleavedBufferAttribute ) {\n\n\t\tnewAttribute = new InstancedBufferAttribute( array, itemSize, normalized, attribute.meshPerAttribute );\n\n\t} else {\n\n\t\tnewAttribute = new BufferAttribute( array, itemSize, normalized );\n\n\t}\n\n\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\tnewAttribute.setX( i, attribute.getX( i ) );\n\n\t\tif ( itemSize >= 2 ) {\n\n\t\t\tnewAttribute.setY( i, attribute.getY( i ) );\n\n\t\t}\n\n\t\tif ( itemSize >= 3 ) {\n\n\t\t\tnewAttribute.setZ( i, attribute.getZ( i ) );\n\n\t\t}\n\n\t\tif ( itemSize >= 4 ) {\n\n\t\t\tnewAttribute.setW( i, attribute.getW( i ) );\n\n\t\t}\n\n\t}\n\n\treturn newAttribute;\n\n}\n\n// deinterleaves all attributes on the geometry\nexport function deinterleaveGeometry( geometry ) {\n\n\tconst attributes = geometry.attributes;\n\tconst morphTargets = geometry.morphTargets;\n\tconst attrMap = new Map();\n\n\tfor ( const key in attributes ) {\n\n\t\tconst attr = attributes[ key ];\n\t\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\t\tif ( ! attrMap.has( attr ) ) {\n\n\t\t\t\tattrMap.set( attr, deinterleaveAttribute( attr ) );\n\n\t\t\t}\n\n\t\t\tattributes[ key ] = attrMap.get( attr );\n\n\t\t}\n\n\t}\n\n\tfor ( const key in morphTargets ) {\n\n\t\tconst attr = morphTargets[ key ];\n\t\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\t\tif ( ! attrMap.has( attr ) ) {\n\n\t\t\t\tattrMap.set( attr, deinterleaveAttribute( attr ) );\n\n\t\t\t}\n\n\t\t\tmorphTargets[ key ] = attrMap.get( attr );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * @param {Array<BufferGeometry>} geometry\n * @return {number}\n */\nfunction estimateBytesUsed( geometry ) {\n\n\t// Return the estimated memory used by this geometry in bytes\n\t// Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n\t// for InterleavedBufferAttributes.\n\tlet mem = 0;\n\tfor ( const name in geometry.attributes ) {\n\n\t\tconst attr = geometry.getAttribute( name );\n\t\tmem += attr.count * attr.itemSize * attr.array.BYTES_PER_ELEMENT;\n\n\t}\n\n\tconst indices = geometry.getIndex();\n\tmem += indices ? indices.count * indices.itemSize * indices.array.BYTES_PER_ELEMENT : 0;\n\treturn mem;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry}\n */\nfunction mergeVertices( geometry, tolerance = 1e-4 ) {\n\n\ttolerance = Math.max( tolerance, Number.EPSILON );\n\n\t// Generate an index buffer if the geometry doesn't have one, or optimize it\n\t// if it's already available.\n\tconst hashToIndex = {};\n\tconst indices = geometry.getIndex();\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst vertexCount = indices ? indices.count : positions.count;\n\n\t// next value for triangle indices\n\tlet nextIndex = 0;\n\n\t// attributes and new attribute arrays\n\tconst attributeNames = Object.keys( geometry.attributes );\n\tconst tmpAttributes = {};\n\tconst tmpMorphAttributes = {};\n\tconst newIndices = [];\n\tconst getters = [ 'getX', 'getY', 'getZ', 'getW' ];\n\tconst setters = [ 'setX', 'setY', 'setZ', 'setW' ];\n\n\t// Initialize the arrays, allocating space conservatively. Extra\n\t// space will be trimmed in the last step.\n\tfor ( let i = 0, l = attributeNames.length; i < l; i ++ ) {\n\n\t\tconst name = attributeNames[ i ];\n\t\tconst attr = geometry.attributes[ name ];\n\n\t\ttmpAttributes[ name ] = new BufferAttribute(\n\t\t\tnew attr.array.constructor( attr.count * attr.itemSize ),\n\t\t\tattr.itemSize,\n\t\t\tattr.normalized\n\t\t);\n\n\t\tconst morphAttr = geometry.morphAttributes[ name ];\n\t\tif ( morphAttr ) {\n\n\t\t\ttmpMorphAttributes[ name ] = new BufferAttribute(\n\t\t\t\tnew morphAttr.array.constructor( morphAttr.count * morphAttr.itemSize ),\n\t\t\t\tmorphAttr.itemSize,\n\t\t\t\tmorphAttr.normalized\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\t// convert the error tolerance to an amount of decimal places to truncate to\n\tconst decimalShift = Math.log10( 1 / tolerance );\n\tconst shiftMultiplier = Math.pow( 10, decimalShift );\n\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\tconst index = indices ? indices.getX( i ) : i;\n\n\t\t// Generate a hash for the vertex attributes at the current index 'i'\n\t\tlet hash = '';\n\t\tfor ( let j = 0, l = attributeNames.length; j < l; j ++ ) {\n\n\t\t\tconst name = attributeNames[ j ];\n\t\t\tconst attribute = geometry.getAttribute( name );\n\t\t\tconst itemSize = attribute.itemSize;\n\n\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\t// double tilde truncates the decimal value\n\t\t\t\thash += `${ ~ ~ ( attribute[ getters[ k ] ]( index ) * shiftMultiplier ) },`;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Add another reference to the vertex if it's already\n\t\t// used by another index\n\t\tif ( hash in hashToIndex ) {\n\n\t\t\tnewIndices.push( hashToIndex[ hash ] );\n\n\t\t} else {\n\n\t\t\t// copy data to the new index in the temporary attributes\n\t\t\tfor ( let j = 0, l = attributeNames.length; j < l; j ++ ) {\n\n\t\t\t\tconst name = attributeNames[ j ];\n\t\t\t\tconst attribute = geometry.getAttribute( name );\n\t\t\t\tconst morphAttr = geometry.morphAttributes[ name ];\n\t\t\t\tconst itemSize = attribute.itemSize;\n\t\t\t\tconst newarray = tmpAttributes[ name ];\n\t\t\t\tconst newMorphArrays = tmpMorphAttributes[ name ];\n\n\t\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\t\tconst getterFunc = getters[ k ];\n\t\t\t\t\tconst setterFunc = setters[ k ];\n\t\t\t\t\tnewarray[ setterFunc ]( nextIndex, attribute[ getterFunc ]( index ) );\n\n\t\t\t\t\tif ( morphAttr ) {\n\n\t\t\t\t\t\tfor ( let m = 0, ml = morphAttr.length; m < ml; m ++ ) {\n\n\t\t\t\t\t\t\tnewMorphArrays[ m ][ setterFunc ]( nextIndex, morphAttr[ m ][ getterFunc ]( index ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\thashToIndex[ hash ] = nextIndex;\n\t\t\tnewIndices.push( nextIndex );\n\t\t\tnextIndex ++;\n\n\t\t}\n\n\t}\n\n\t// generate result BufferGeometry\n\tconst result = geometry.clone();\n\tfor ( const name in geometry.attributes ) {\n\n\t\tconst tmpAttribute = tmpAttributes[ name ];\n\n\t\tresult.setAttribute( name, new BufferAttribute(\n\t\t\ttmpAttribute.array.slice( 0, nextIndex * tmpAttribute.itemSize ),\n\t\t\ttmpAttribute.itemSize,\n\t\t\ttmpAttribute.normalized,\n\t\t) );\n\n\t\tif ( ! ( name in tmpMorphAttributes ) ) continue;\n\n\t\tfor ( let j = 0; j < tmpMorphAttributes[ name ].length; j ++ ) {\n\n\t\t\tconst tmpMorphAttribute = tmpMorphAttributes[ name ][ j ];\n\n\t\t\tresult.morphAttributes[ name ][ j ] = new BufferAttribute(\n\t\t\t\ttmpMorphAttribute.array.slice( 0, nextIndex * tmpMorphAttribute.itemSize ),\n\t\t\t\ttmpMorphAttribute.itemSize,\n\t\t\t\ttmpMorphAttribute.normalized,\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\t// indices\n\n\tresult.setIndex( newIndices );\n\n\treturn result;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nfunction toTrianglesDrawMode( geometry, drawMode ) {\n\n\tif ( drawMode === TrianglesDrawMode ) {\n\n\t\tconsole.warn( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.' );\n\t\treturn geometry;\n\n\t}\n\n\tif ( drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode ) {\n\n\t\tlet index = geometry.getIndex();\n\n\t\t// generate index if not present\n\n\t\tif ( index === null ) {\n\n\t\t\tconst indices = [];\n\n\t\t\tconst position = geometry.getAttribute( 'position' );\n\n\t\t\tif ( position !== undefined ) {\n\n\t\t\t\tfor ( let i = 0; i < position.count; i ++ ) {\n\n\t\t\t\t\tindices.push( i );\n\n\t\t\t\t}\n\n\t\t\t\tgeometry.setIndex( indices );\n\t\t\t\tindex = geometry.getIndex();\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.' );\n\t\t\t\treturn geometry;\n\n\t\t\t}\n\n\t\t}\n\n\t\t//\n\n\t\tconst numberOfTriangles = index.count - 2;\n\t\tconst newIndices = [];\n\n\t\tif ( drawMode === TriangleFanDrawMode ) {\n\n\t\t\t// gl.TRIANGLE_FAN\n\n\t\t\tfor ( let i = 1; i <= numberOfTriangles; i ++ ) {\n\n\t\t\t\tnewIndices.push( index.getX( 0 ) );\n\t\t\t\tnewIndices.push( index.getX( i ) );\n\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\t// gl.TRIANGLE_STRIP\n\n\t\t\tfor ( let i = 0; i < numberOfTriangles; i ++ ) {\n\n\t\t\t\tif ( i % 2 === 0 ) {\n\n\t\t\t\t\tnewIndices.push( index.getX( i ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 2 ) );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tnewIndices.push( index.getX( i + 2 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i ) );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( ( newIndices.length / 3 ) !== numberOfTriangles ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.' );\n\n\t\t}\n\n\t\t// build final geometry\n\n\t\tconst newGeometry = geometry.clone();\n\t\tnewGeometry.setIndex( newIndices );\n\t\tnewGeometry.clearGroups();\n\n\t\treturn newGeometry;\n\n\t} else {\n\n\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode );\n\t\treturn geometry;\n\n\t}\n\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nfunction computeMorphedAttributes( object ) {\n\n\tconst _vA = new Vector3();\n\tconst _vB = new Vector3();\n\tconst _vC = new Vector3();\n\n\tconst _tempA = new Vector3();\n\tconst _tempB = new Vector3();\n\tconst _tempC = new Vector3();\n\n\tconst _morphA = new Vector3();\n\tconst _morphB = new Vector3();\n\tconst _morphC = new Vector3();\n\n\tfunction _calculateMorphedAttributeData(\n\t\tobject,\n\t\tattribute,\n\t\tmorphAttribute,\n\t\tmorphTargetsRelative,\n\t\ta,\n\t\tb,\n\t\tc,\n\t\tmodifiedAttributeArray\n\t) {\n\n\t\t_vA.fromBufferAttribute( attribute, a );\n\t\t_vB.fromBufferAttribute( attribute, b );\n\t\t_vC.fromBufferAttribute( attribute, c );\n\n\t\tconst morphInfluences = object.morphTargetInfluences;\n\n\t\tif ( morphAttribute && morphInfluences ) {\n\n\t\t\t_morphA.set( 0, 0, 0 );\n\t\t\t_morphB.set( 0, 0, 0 );\n\t\t\t_morphC.set( 0, 0, 0 );\n\n\t\t\tfor ( let i = 0, il = morphAttribute.length; i < il; i ++ ) {\n\n\t\t\t\tconst influence = morphInfluences[ i ];\n\t\t\t\tconst morph = morphAttribute[ i ];\n\n\t\t\t\tif ( influence === 0 ) continue;\n\n\t\t\t\t_tempA.fromBufferAttribute( morph, a );\n\t\t\t\t_tempB.fromBufferAttribute( morph, b );\n\t\t\t\t_tempC.fromBufferAttribute( morph, c );\n\n\t\t\t\tif ( morphTargetsRelative ) {\n\n\t\t\t\t\t_morphA.addScaledVector( _tempA, influence );\n\t\t\t\t\t_morphB.addScaledVector( _tempB, influence );\n\t\t\t\t\t_morphC.addScaledVector( _tempC, influence );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t_morphA.addScaledVector( _tempA.sub( _vA ), influence );\n\t\t\t\t\t_morphB.addScaledVector( _tempB.sub( _vB ), influence );\n\t\t\t\t\t_morphC.addScaledVector( _tempC.sub( _vC ), influence );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t_vA.add( _morphA );\n\t\t\t_vB.add( _morphB );\n\t\t\t_vC.add( _morphC );\n\n\t\t}\n\n\t\tif ( object.isSkinnedMesh ) {\n\n\t\t\tobject.applyBoneTransform( a, _vA );\n\t\t\tobject.applyBoneTransform( b, _vB );\n\t\t\tobject.applyBoneTransform( c, _vC );\n\n\t\t}\n\n\t\tmodifiedAttributeArray[ a * 3 + 0 ] = _vA.x;\n\t\tmodifiedAttributeArray[ a * 3 + 1 ] = _vA.y;\n\t\tmodifiedAttributeArray[ a * 3 + 2 ] = _vA.z;\n\t\tmodifiedAttributeArray[ b * 3 + 0 ] = _vB.x;\n\t\tmodifiedAttributeArray[ b * 3 + 1 ] = _vB.y;\n\t\tmodifiedAttributeArray[ b * 3 + 2 ] = _vB.z;\n\t\tmodifiedAttributeArray[ c * 3 + 0 ] = _vC.x;\n\t\tmodifiedAttributeArray[ c * 3 + 1 ] = _vC.y;\n\t\tmodifiedAttributeArray[ c * 3 + 2 ] = _vC.z;\n\n\t}\n\n\tconst geometry = object.geometry;\n\tconst material = object.material;\n\n\tlet a, b, c;\n\tconst index = geometry.index;\n\tconst positionAttribute = geometry.attributes.position;\n\tconst morphPosition = geometry.morphAttributes.position;\n\tconst morphTargetsRelative = geometry.morphTargetsRelative;\n\tconst normalAttribute = geometry.attributes.normal;\n\tconst morphNormal = geometry.morphAttributes.position;\n\n\tconst groups = geometry.groups;\n\tconst drawRange = geometry.drawRange;\n\tlet i, j, il, jl;\n\tlet group;\n\tlet start, end;\n\n\tconst modifiedPosition = new Float32Array( positionAttribute.count * positionAttribute.itemSize );\n\tconst modifiedNormal = new Float32Array( normalAttribute.count * normalAttribute.itemSize );\n\n\tif ( index !== null ) {\n\n\t\t// indexed buffer geometry\n\n\t\tif ( Array.isArray( material ) ) {\n\n\t\t\tfor ( i = 0, il = groups.length; i < il; i ++ ) {\n\n\t\t\t\tgroup = groups[ i ];\n\n\t\t\t\tstart = Math.max( group.start, drawRange.start );\n\t\t\t\tend = Math.min( ( group.start + group.count ), ( drawRange.start + drawRange.count ) );\n\n\t\t\t\tfor ( j = start, jl = end; j < jl; j += 3 ) {\n\n\t\t\t\t\ta = index.getX( j );\n\t\t\t\t\tb = index.getX( j + 1 );\n\t\t\t\t\tc = index.getX( j + 2 );\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tpositionAttribute,\n\t\t\t\t\t\tmorphPosition,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedPosition\n\t\t\t\t\t);\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tnormalAttribute,\n\t\t\t\t\t\tmorphNormal,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedNormal\n\t\t\t\t\t);\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tstart = Math.max( 0, drawRange.start );\n\t\t\tend = Math.min( index.count, ( drawRange.start + drawRange.count ) );\n\n\t\t\tfor ( i = start, il = end; i < il; i += 3 ) {\n\n\t\t\t\ta = index.getX( i );\n\t\t\t\tb = index.getX( i + 1 );\n\t\t\t\tc = index.getX( i + 2 );\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tpositionAttribute,\n\t\t\t\t\tmorphPosition,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedPosition\n\t\t\t\t);\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tnormalAttribute,\n\t\t\t\t\tmorphNormal,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedNormal\n\t\t\t\t);\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\t// non-indexed buffer geometry\n\n\t\tif ( Array.isArray( material ) ) {\n\n\t\t\tfor ( i = 0, il = groups.length; i < il; i ++ ) {\n\n\t\t\t\tgroup = groups[ i ];\n\n\t\t\t\tstart = Math.max( group.start, drawRange.start );\n\t\t\t\tend = Math.min( ( group.start + group.count ), ( drawRange.start + drawRange.count ) );\n\n\t\t\t\tfor ( j = start, jl = end; j < jl; j += 3 ) {\n\n\t\t\t\t\ta = j;\n\t\t\t\t\tb = j + 1;\n\t\t\t\t\tc = j + 2;\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tpositionAttribute,\n\t\t\t\t\t\tmorphPosition,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedPosition\n\t\t\t\t\t);\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tnormalAttribute,\n\t\t\t\t\t\tmorphNormal,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedNormal\n\t\t\t\t\t);\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tstart = Math.max( 0, drawRange.start );\n\t\t\tend = Math.min( positionAttribute.count, ( drawRange.start + drawRange.count ) );\n\n\t\t\tfor ( i = start, il = end; i < il; i += 3 ) {\n\n\t\t\t\ta = i;\n\t\t\t\tb = i + 1;\n\t\t\t\tc = i + 2;\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tpositionAttribute,\n\t\t\t\t\tmorphPosition,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedPosition\n\t\t\t\t);\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tnormalAttribute,\n\t\t\t\t\tmorphNormal,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedNormal\n\t\t\t\t);\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\tconst morphedPositionAttribute = new Float32BufferAttribute( modifiedPosition, 3 );\n\tconst morphedNormalAttribute = new Float32BufferAttribute( modifiedNormal, 3 );\n\n\treturn {\n\n\t\tpositionAttribute: positionAttribute,\n\t\tnormalAttribute: normalAttribute,\n\t\tmorphedPositionAttribute: morphedPositionAttribute,\n\t\tmorphedNormalAttribute: morphedNormalAttribute\n\n\t};\n\n}\n\nfunction mergeGroups( geometry ) {\n\n\tif ( geometry.groups.length === 0 ) {\n\n\t\tconsole.warn( 'THREE.BufferGeometryUtils.mergeGroups(): No groups are defined. Nothing to merge.' );\n\t\treturn geometry;\n\n\t}\n\n\tlet groups = geometry.groups;\n\n\t// sort groups by material index\n\n\tgroups = groups.sort( ( a, b ) => {\n\n\t\tif ( a.materialIndex !== b.materialIndex ) return a.materialIndex - b.materialIndex;\n\n\t\treturn a.start - b.start;\n\n\t} );\n\n\t// create index for non-indexed geometries\n\n\tif ( geometry.getIndex() === null ) {\n\n\t\tconst positionAttribute = geometry.getAttribute( 'position' );\n\t\tconst indices = [];\n\n\t\tfor ( let i = 0; i < positionAttribute.count; i += 3 ) {\n\n\t\t\tindices.push( i, i + 1, i + 2 );\n\n\t\t}\n\n\t\tgeometry.setIndex( indices );\n\n\t}\n\n\t// sort index\n\n\tconst index = geometry.getIndex();\n\n\tconst newIndices = [];\n\n\tfor ( let i = 0; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tconst groupStart = group.start;\n\t\tconst groupLength = groupStart + group.count;\n\n\t\tfor ( let j = groupStart; j < groupLength; j ++ ) {\n\n\t\t\tnewIndices.push( index.getX( j ) );\n\n\t\t}\n\n\t}\n\n\tgeometry.dispose(); // Required to force buffer recreation\n\tgeometry.setIndex( newIndices );\n\n\t// update groups indices\n\n\tlet start = 0;\n\n\tfor ( let i = 0; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tgroup.start = start;\n\t\tstart += group.count;\n\n\t}\n\n\t// merge groups\n\n\tlet currentGroup = groups[ 0 ];\n\n\tgeometry.groups = [ currentGroup ];\n\n\tfor ( let i = 1; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tif ( currentGroup.materialIndex === group.materialIndex ) {\n\n\t\t\tcurrentGroup.count += group.count;\n\n\t\t} else {\n\n\t\t\tcurrentGroup = group;\n\t\t\tgeometry.groups.push( currentGroup );\n\n\t\t}\n\n\t}\n\n\treturn geometry;\n\n}\n\n\n// Creates a new, non-indexed geometry with smooth normals everywhere except faces that meet at\n// an angle greater than the crease angle.\nfunction toCreasedNormals( geometry, creaseAngle = Math.PI / 3 /* 60 degrees */ ) {\n\n\tconst creaseDot = Math.cos( creaseAngle );\n\tconst hashMultiplier = ( 1 + 1e-10 ) * 1e2;\n\n\t// reusable vertors\n\tconst verts = [ new Vector3(), new Vector3(), new Vector3() ];\n\tconst tempVec1 = new Vector3();\n\tconst tempVec2 = new Vector3();\n\tconst tempNorm = new Vector3();\n\tconst tempNorm2 = new Vector3();\n\n\t// hashes a vector\n\tfunction hashVertex( v ) {\n\n\t\tconst x = ~ ~ ( v.x * hashMultiplier );\n\t\tconst y = ~ ~ ( v.y * hashMultiplier );\n\t\tconst z = ~ ~ ( v.z * hashMultiplier );\n\t\treturn `${x},${y},${z}`;\n\n\t}\n\n\tconst resultGeometry = geometry.toNonIndexed();\n\tconst posAttr = resultGeometry.attributes.position;\n\tconst vertexMap = {};\n\n\t// find all the normals shared by commonly located vertices\n\tfor ( let i = 0, l = posAttr.count / 3; i < l; i ++ ) {\n\n\t\tconst i3 = 3 * i;\n\t\tconst a = verts[ 0 ].fromBufferAttribute( posAttr, i3 + 0 );\n\t\tconst b = verts[ 1 ].fromBufferAttribute( posAttr, i3 + 1 );\n\t\tconst c = verts[ 2 ].fromBufferAttribute( posAttr, i3 + 2 );\n\n\t\ttempVec1.subVectors( c, b );\n\t\ttempVec2.subVectors( a, b );\n\n\t\t// add the normal to the map for all vertices\n\t\tconst normal = new Vector3().crossVectors( tempVec1, tempVec2 ).normalize();\n\t\tfor ( let n = 0; n < 3; n ++ ) {\n\n\t\t\tconst vert = verts[ n ];\n\t\t\tconst hash = hashVertex( vert );\n\t\t\tif ( ! ( hash in vertexMap ) ) {\n\n\t\t\t\tvertexMap[ hash ] = [];\n\n\t\t\t}\n\n\t\t\tvertexMap[ hash ].push( normal );\n\n\t\t}\n\n\t}\n\n\t// average normals from all vertices that share a common location if they are within the\n\t// provided crease threshold\n\tconst normalArray = new Float32Array( posAttr.count * 3 );\n\tconst normAttr = new BufferAttribute( normalArray, 3, false );\n\tfor ( let i = 0, l = posAttr.count / 3; i < l; i ++ ) {\n\n\t\t// get the face normal for this vertex\n\t\tconst i3 = 3 * i;\n\t\tconst a = verts[ 0 ].fromBufferAttribute( posAttr, i3 + 0 );\n\t\tconst b = verts[ 1 ].fromBufferAttribute( posAttr, i3 + 1 );\n\t\tconst c = verts[ 2 ].fromBufferAttribute( posAttr, i3 + 2 );\n\n\t\ttempVec1.subVectors( c, b );\n\t\ttempVec2.subVectors( a, b );\n\n\t\ttempNorm.crossVectors( tempVec1, tempVec2 ).normalize();\n\n\t\t// average all normals that meet the threshold and set the normal value\n\t\tfor ( let n = 0; n < 3; n ++ ) {\n\n\t\t\tconst vert = verts[ n ];\n\t\t\tconst hash = hashVertex( vert );\n\t\t\tconst otherNormals = vertexMap[ hash ];\n\t\t\ttempNorm2.set( 0, 0, 0 );\n\n\t\t\tfor ( let k = 0, lk = otherNormals.length; k < lk; k ++ ) {\n\n\t\t\t\tconst otherNorm = otherNormals[ k ];\n\t\t\t\tif ( tempNorm.dot( otherNorm ) > creaseDot ) {\n\n\t\t\t\t\ttempNorm2.add( otherNorm );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\ttempNorm2.normalize();\n\t\t\tnormAttr.setXYZ( i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z );\n\n\t\t}\n\n\t}\n\n\tresultGeometry.setAttribute( 'normal', normAttr );\n\treturn resultGeometry;\n\n}\n\nfunction mergeBufferGeometries( geometries, useGroups = false ) {\n\n\tconsole.warn( 'THREE.BufferGeometryUtils: mergeBufferGeometries() has been renamed to mergeGeometries().' ); // @deprecated, r151\n\treturn mergeGeometries( geometries, useGroups );\n\n}\n\nfunction mergeBufferAttributes( attributes ) {\n\n\tconsole.warn( 'THREE.BufferGeometryUtils: mergeBufferAttributes() has been renamed to mergeAttributes().' ); // @deprecated, r151\n\treturn mergeAttributes( attributes );\n\n}\n\nexport {\n\tcomputeMikkTSpaceTangents,\n\tmergeGeometries,\n\tmergeBufferGeometries,\n\tmergeAttributes,\n\tmergeBufferAttributes,\n\tinterleaveAttributes,\n\testimateBytesUsed,\n\tmergeVertices,\n\ttoTrianglesDrawMode,\n\tcomputeMorphedAttributes,\n\tmergeGroups,\n\ttoCreasedNormals\n};\n"], "mappings": "AAAA,SACCA,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,wBAAwB,EACxBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,mBAAmB,EACnBC,qBAAqB,EACrBC,iBAAiB,EACjBC,OAAO,QACD,OAAO;AAEd,SAASC,yBAAyBA,CAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,GAAG,IAAI,EAAG;EAE7E,IAAK,CAAED,UAAU,IAAI,CAAEA,UAAU,CAACE,OAAO,EAAG;IAE3C,MAAM,IAAIC,KAAK,CAAE,+DAAgE,CAAC;EAEnF;EAEA,IAAK,CAAEJ,QAAQ,CAACK,YAAY,CAAE,UAAW,CAAC,IAAI,CAAEL,QAAQ,CAACK,YAAY,CAAE,QAAS,CAAC,IAAI,CAAEL,QAAQ,CAACK,YAAY,CAAE,IAAK,CAAC,EAAG;IAEtH,MAAM,IAAID,KAAK,CAAE,kFAAmF,CAAC;EAEtG;EAEA,SAASE,iBAAiBA,CAAEC,SAAS,EAAG;IAEvC,IAAKA,SAAS,CAACC,UAAU,IAAID,SAAS,CAACE,4BAA4B,EAAG;MAErE,MAAMC,QAAQ,GAAG,IAAIC,YAAY,CAAEJ,SAAS,CAACK,KAAK,GAAGL,SAAS,CAACM,QAAS,CAAC;MAEzE,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGP,SAAS,CAACK,KAAK,EAAEE,CAAC,EAAG,EAAG;QAEnDJ,QAAQ,CAAEK,CAAC,EAAG,CAAE,GAAGR,SAAS,CAACS,IAAI,CAAEF,CAAE,CAAC;QACtCJ,QAAQ,CAAEK,CAAC,EAAG,CAAE,GAAGR,SAAS,CAACU,IAAI,CAAEH,CAAE,CAAC;QAEtC,IAAKP,SAAS,CAACM,QAAQ,GAAG,CAAC,EAAG;UAE7BH,QAAQ,CAAEK,CAAC,EAAG,CAAE,GAAGR,SAAS,CAACW,IAAI,CAAEJ,CAAE,CAAC;QAEvC;MAED;MAEA,OAAOJ,QAAQ;IAEhB;IAEA,IAAKH,SAAS,CAACY,KAAK,YAAYR,YAAY,EAAG;MAE9C,OAAOJ,SAAS,CAACY,KAAK;IAEvB;IAEA,OAAO,IAAIR,YAAY,CAAEJ,SAAS,CAACY,KAAM,CAAC;EAE3C;;EAEA;;EAEA,MAAMC,SAAS,GAAGpB,QAAQ,CAACqB,KAAK,GAAGrB,QAAQ,CAACsB,YAAY,CAAC,CAAC,GAAGtB,QAAQ;;EAErE;;EAEA,MAAMuB,QAAQ,GAAGtB,UAAU,CAACuB,gBAAgB,CAE3ClB,iBAAiB,CAAEc,SAAS,CAACK,UAAU,CAACC,QAAS,CAAC,EAClDpB,iBAAiB,CAAEc,SAAS,CAACK,UAAU,CAACE,MAAO,CAAC,EAChDrB,iBAAiB,CAAEc,SAAS,CAACK,UAAU,CAACG,EAAG,CAE5C,CAAC;;EAED;EACA;;EAEA,IAAK1B,UAAU,EAAG;IAEjB,KAAM,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,QAAQ,CAACM,MAAM,EAAEf,CAAC,IAAI,CAAC,EAAG;MAE9CS,QAAQ,CAAET,CAAC,CAAE,IAAI,CAAE,CAAC;IAErB;EAED;;EAEA;;EAEAM,SAAS,CAACU,YAAY,CAAE,SAAS,EAAE,IAAIzC,eAAe,CAAEkC,QAAQ,EAAE,CAAE,CAAE,CAAC;EAEvE,IAAKvB,QAAQ,KAAKoB,SAAS,EAAG;IAE7BpB,QAAQ,CAAC+B,IAAI,CAAEX,SAAU,CAAC;EAE3B;EAEA,OAAOpB,QAAQ;AAEhB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASgC,eAAeA,CAAEC,UAAU,EAAEC,SAAS,GAAG,KAAK,EAAG;EAEzD,MAAMC,SAAS,GAAGF,UAAU,CAAE,CAAC,CAAE,CAACZ,KAAK,KAAK,IAAI;EAEhD,MAAMe,cAAc,GAAG,IAAIC,GAAG,CAAEC,MAAM,CAACC,IAAI,CAAEN,UAAU,CAAE,CAAC,CAAE,CAACR,UAAW,CAAE,CAAC;EAC3E,MAAMe,mBAAmB,GAAG,IAAIH,GAAG,CAAEC,MAAM,CAACC,IAAI,CAAEN,UAAU,CAAE,CAAC,CAAE,CAACQ,eAAgB,CAAE,CAAC;EAErF,MAAMhB,UAAU,GAAG,CAAC,CAAC;EACrB,MAAMgB,eAAe,GAAG,CAAC,CAAC;EAE1B,MAAMC,oBAAoB,GAAGT,UAAU,CAAE,CAAC,CAAE,CAACS,oBAAoB;EAEjE,MAAMC,cAAc,GAAG,IAAIrD,cAAc,CAAC,CAAC;EAE3C,IAAIsD,MAAM,GAAG,CAAC;EAEd,KAAM,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,UAAU,CAACJ,MAAM,EAAE,EAAGf,CAAC,EAAG;IAE9C,MAAMd,QAAQ,GAAGiC,UAAU,CAAEnB,CAAC,CAAE;IAChC,IAAI+B,eAAe,GAAG,CAAC;;IAEvB;;IAEA,IAAKV,SAAS,MAAOnC,QAAQ,CAACqB,KAAK,KAAK,IAAI,CAAE,EAAG;MAEhDyB,OAAO,CAACC,KAAK,CAAE,8EAA8E,GAAGjC,CAAC,GAAG,8HAA+H,CAAC;MACpO,OAAO,IAAI;IAEZ;;IAEA;;IAEA,KAAM,MAAMkC,IAAI,IAAIhD,QAAQ,CAACyB,UAAU,EAAG;MAEzC,IAAK,CAAEW,cAAc,CAACa,GAAG,CAAED,IAAK,CAAC,EAAG;QAEnCF,OAAO,CAACC,KAAK,CAAE,8EAA8E,GAAGjC,CAAC,GAAG,+DAA+D,GAAGkC,IAAI,GAAG,8DAA+D,CAAC;QAC7O,OAAO,IAAI;MAEZ;MAEA,IAAKvB,UAAU,CAAEuB,IAAI,CAAE,KAAKE,SAAS,EAAGzB,UAAU,CAAEuB,IAAI,CAAE,GAAG,EAAE;MAE/DvB,UAAU,CAAEuB,IAAI,CAAE,CAACG,IAAI,CAAEnD,QAAQ,CAACyB,UAAU,CAAEuB,IAAI,CAAG,CAAC;MAEtDH,eAAe,EAAG;IAEnB;;IAEA;;IAEA,IAAKA,eAAe,KAAKT,cAAc,CAACgB,IAAI,EAAG;MAE9CN,OAAO,CAACC,KAAK,CAAE,8EAA8E,GAAGjC,CAAC,GAAG,gEAAiE,CAAC;MACtK,OAAO,IAAI;IAEZ;;IAEA;;IAEA,IAAK4B,oBAAoB,KAAK1C,QAAQ,CAAC0C,oBAAoB,EAAG;MAE7DI,OAAO,CAACC,KAAK,CAAE,8EAA8E,GAAGjC,CAAC,GAAG,uEAAwE,CAAC;MAC7K,OAAO,IAAI;IAEZ;IAEA,KAAM,MAAMkC,IAAI,IAAIhD,QAAQ,CAACyC,eAAe,EAAG;MAE9C,IAAK,CAAED,mBAAmB,CAACS,GAAG,CAAED,IAAK,CAAC,EAAG;QAExCF,OAAO,CAACC,KAAK,CAAE,8EAA8E,GAAGjC,CAAC,GAAG,mEAAoE,CAAC;QACzK,OAAO,IAAI;MAEZ;MAEA,IAAK2B,eAAe,CAAEO,IAAI,CAAE,KAAKE,SAAS,EAAGT,eAAe,CAAEO,IAAI,CAAE,GAAG,EAAE;MAEzEP,eAAe,CAAEO,IAAI,CAAE,CAACG,IAAI,CAAEnD,QAAQ,CAACyC,eAAe,CAAEO,IAAI,CAAG,CAAC;IAEjE;IAEA,IAAKd,SAAS,EAAG;MAEhB,IAAItB,KAAK;MAET,IAAKuB,SAAS,EAAG;QAEhBvB,KAAK,GAAGZ,QAAQ,CAACqB,KAAK,CAACT,KAAK;MAE7B,CAAC,MAAM,IAAKZ,QAAQ,CAACyB,UAAU,CAACC,QAAQ,KAAKwB,SAAS,EAAG;QAExDtC,KAAK,GAAGZ,QAAQ,CAACyB,UAAU,CAACC,QAAQ,CAACd,KAAK;MAE3C,CAAC,MAAM;QAENkC,OAAO,CAACC,KAAK,CAAE,8EAA8E,GAAGjC,CAAC,GAAG,kEAAmE,CAAC;QACxK,OAAO,IAAI;MAEZ;MAEA6B,cAAc,CAACU,QAAQ,CAAET,MAAM,EAAEhC,KAAK,EAAEE,CAAE,CAAC;MAE3C8B,MAAM,IAAIhC,KAAK;IAEhB;EAED;;EAEA;;EAEA,IAAKuB,SAAS,EAAG;IAEhB,IAAImB,WAAW,GAAG,CAAC;IACnB,MAAMC,WAAW,GAAG,EAAE;IAEtB,KAAM,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,UAAU,CAACJ,MAAM,EAAE,EAAGf,CAAC,EAAG;MAE9C,MAAMO,KAAK,GAAGY,UAAU,CAAEnB,CAAC,CAAE,CAACO,KAAK;MAEnC,KAAM,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,KAAK,CAACT,KAAK,EAAE,EAAGG,CAAC,EAAG;QAExCwC,WAAW,CAACJ,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAED,CAAE,CAAC,GAAGuC,WAAY,CAAC;MAElD;MAEAA,WAAW,IAAIrB,UAAU,CAAEnB,CAAC,CAAE,CAACW,UAAU,CAACC,QAAQ,CAACd,KAAK;IAEzD;IAEA+B,cAAc,CAACa,QAAQ,CAAED,WAAY,CAAC;EAEvC;;EAEA;;EAEA,KAAM,MAAMP,IAAI,IAAIvB,UAAU,EAAG;IAEhC,MAAMgC,eAAe,GAAGC,eAAe,CAAEjC,UAAU,CAAEuB,IAAI,CAAG,CAAC;IAE7D,IAAK,CAAES,eAAe,EAAG;MAExBX,OAAO,CAACC,KAAK,CAAE,iFAAiF,GAAGC,IAAI,GAAG,aAAc,CAAC;MACzH,OAAO,IAAI;IAEZ;IAEAL,cAAc,CAACb,YAAY,CAAEkB,IAAI,EAAES,eAAgB,CAAC;EAErD;;EAEA;;EAEA,KAAM,MAAMT,IAAI,IAAIP,eAAe,EAAG;IAErC,MAAMkB,eAAe,GAAGlB,eAAe,CAAEO,IAAI,CAAE,CAAE,CAAC,CAAE,CAACnB,MAAM;IAE3D,IAAK8B,eAAe,KAAK,CAAC,EAAG;IAE7BhB,cAAc,CAACF,eAAe,GAAGE,cAAc,CAACF,eAAe,IAAI,CAAC,CAAC;IACrEE,cAAc,CAACF,eAAe,CAAEO,IAAI,CAAE,GAAG,EAAE;IAE3C,KAAM,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,eAAe,EAAE,EAAG7C,CAAC,EAAG;MAE5C,MAAM8C,sBAAsB,GAAG,EAAE;MAEjC,KAAM,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,eAAe,CAAEO,IAAI,CAAE,CAACnB,MAAM,EAAE,EAAGd,CAAC,EAAG;QAE3D6C,sBAAsB,CAACT,IAAI,CAAEV,eAAe,CAAEO,IAAI,CAAE,CAAEjC,CAAC,CAAE,CAAED,CAAC,CAAG,CAAC;MAEjE;MAEA,MAAM+C,oBAAoB,GAAGH,eAAe,CAAEE,sBAAuB,CAAC;MAEtE,IAAK,CAAEC,oBAAoB,EAAG;QAE7Bf,OAAO,CAACC,KAAK,CAAE,iFAAiF,GAAGC,IAAI,GAAG,kBAAmB,CAAC;QAC9H,OAAO,IAAI;MAEZ;MAEAL,cAAc,CAACF,eAAe,CAAEO,IAAI,CAAE,CAACG,IAAI,CAAEU,oBAAqB,CAAC;IAEpE;EAED;EAEA,OAAOlB,cAAc;AAEtB;;AAEA;AACA;AACA;AACA;AACA,SAASe,eAAeA,CAAEjC,UAAU,EAAG;EAEtC,IAAIqC,UAAU;EACd,IAAIjD,QAAQ;EACZ,IAAIL,UAAU;EACd,IAAIuD,OAAO,GAAG,CAAE,CAAC;EACjB,IAAIC,WAAW,GAAG,CAAC;EAEnB,KAAM,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,UAAU,CAACI,MAAM,EAAE,EAAGf,CAAC,EAAG;IAE9C,MAAMP,SAAS,GAAGkB,UAAU,CAAEX,CAAC,CAAE;IAEjC,IAAKP,SAAS,CAACE,4BAA4B,EAAG;MAE7CqC,OAAO,CAACC,KAAK,CAAE,sGAAuG,CAAC;MACvH,OAAO,IAAI;IAEZ;IAEA,IAAKe,UAAU,KAAKZ,SAAS,EAAGY,UAAU,GAAGvD,SAAS,CAACY,KAAK,CAAC8C,WAAW;IACxE,IAAKH,UAAU,KAAKvD,SAAS,CAACY,KAAK,CAAC8C,WAAW,EAAG;MAEjDnB,OAAO,CAACC,KAAK,CAAE,2IAA4I,CAAC;MAC5J,OAAO,IAAI;IAEZ;IAEA,IAAKlC,QAAQ,KAAKqC,SAAS,EAAGrC,QAAQ,GAAGN,SAAS,CAACM,QAAQ;IAC3D,IAAKA,QAAQ,KAAKN,SAAS,CAACM,QAAQ,EAAG;MAEtCiC,OAAO,CAACC,KAAK,CAAE,+HAAgI,CAAC;MAChJ,OAAO,IAAI;IAEZ;IAEA,IAAKvC,UAAU,KAAK0C,SAAS,EAAG1C,UAAU,GAAGD,SAAS,CAACC,UAAU;IACjE,IAAKA,UAAU,KAAKD,SAAS,CAACC,UAAU,EAAG;MAE1CsC,OAAO,CAACC,KAAK,CAAE,iIAAkI,CAAC;MAClJ,OAAO,IAAI;IAEZ;IAEA,IAAKgB,OAAO,KAAK,CAAE,CAAC,EAAGA,OAAO,GAAGxD,SAAS,CAACwD,OAAO;IAClD,IAAKA,OAAO,KAAKxD,SAAS,CAACwD,OAAO,EAAG;MAEpCjB,OAAO,CAACC,KAAK,CAAE,8HAA+H,CAAC;MAC/I,OAAO,IAAI;IAEZ;IAEAiB,WAAW,IAAIzD,SAAS,CAACY,KAAK,CAACU,MAAM;EAEtC;EAEA,MAAMV,KAAK,GAAG,IAAI2C,UAAU,CAAEE,WAAY,CAAC;EAC3C,IAAIpB,MAAM,GAAG,CAAC;EAEd,KAAM,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,UAAU,CAACI,MAAM,EAAE,EAAGf,CAAC,EAAG;IAE9CK,KAAK,CAAC+C,GAAG,CAAEzC,UAAU,CAAEX,CAAC,CAAE,CAACK,KAAK,EAAEyB,MAAO,CAAC;IAE1CA,MAAM,IAAInB,UAAU,CAAEX,CAAC,CAAE,CAACK,KAAK,CAACU,MAAM;EAEvC;EAEA,MAAMsC,MAAM,GAAG,IAAI9E,eAAe,CAAE8B,KAAK,EAAEN,QAAQ,EAAEL,UAAW,CAAC;EACjE,IAAKuD,OAAO,KAAKb,SAAS,EAAG;IAE5BiB,MAAM,CAACJ,OAAO,GAAGA,OAAO;EAEzB;EAEA,OAAOI,MAAM;AAEd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAAE7D,SAAS,EAAG;EAE/C,IAAKA,SAAS,CAAC8D,qCAAqC,IAAI9D,SAAS,CAACE,4BAA4B,EAAG;IAEhG,OAAO6D,qBAAqB,CAAE/D,SAAU,CAAC;EAE1C;EAEA,IAAKA,SAAS,CAACgE,0BAA0B,EAAG;IAE3C,OAAO,IAAI/E,wBAAwB,CAAC,CAAC,CAACuC,IAAI,CAAExB,SAAU,CAAC;EAExD;EAEA,OAAO,IAAIlB,eAAe,CAAC,CAAC,CAAC0C,IAAI,CAAExB,SAAU,CAAC;AAE/C;;AAEA;AACA;AACA;AACA;AACA,SAASiE,oBAAoBA,CAAE/C,UAAU,EAAG;EAE3C;EACA;EACA,IAAIqC,UAAU;EACd,IAAIE,WAAW,GAAG,CAAC;EACnB,IAAIS,MAAM,GAAG,CAAC;;EAEd;EACA,KAAM,IAAI3D,CAAC,GAAG,CAAC,EAAE4D,CAAC,GAAGjD,UAAU,CAACI,MAAM,EAAEf,CAAC,GAAG4D,CAAC,EAAE,EAAG5D,CAAC,EAAG;IAErD,MAAMP,SAAS,GAAGkB,UAAU,CAAEX,CAAC,CAAE;IAEjC,IAAKgD,UAAU,KAAKZ,SAAS,EAAGY,UAAU,GAAGvD,SAAS,CAACY,KAAK,CAAC8C,WAAW;IACxE,IAAKH,UAAU,KAAKvD,SAAS,CAACY,KAAK,CAAC8C,WAAW,EAAG;MAEjDnB,OAAO,CAACC,KAAK,CAAE,2DAA4D,CAAC;MAC5E,OAAO,IAAI;IAEZ;IAEAiB,WAAW,IAAIzD,SAAS,CAACY,KAAK,CAACU,MAAM;IACrC4C,MAAM,IAAIlE,SAAS,CAACM,QAAQ;EAE7B;;EAEA;EACA,MAAM8D,iBAAiB,GAAG,IAAIlF,iBAAiB,CAAE,IAAIqE,UAAU,CAAEE,WAAY,CAAC,EAAES,MAAO,CAAC;EACxF,IAAI7B,MAAM,GAAG,CAAC;EACd,MAAMgC,GAAG,GAAG,EAAE;EACd,MAAMC,OAAO,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAE;EAClD,MAAMC,OAAO,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAE;EAElD,KAAM,IAAI/D,CAAC,GAAG,CAAC,EAAE2D,CAAC,GAAGjD,UAAU,CAACI,MAAM,EAAEd,CAAC,GAAG2D,CAAC,EAAE3D,CAAC,EAAG,EAAG;IAErD,MAAMR,SAAS,GAAGkB,UAAU,CAAEV,CAAC,CAAE;IACjC,MAAMF,QAAQ,GAAGN,SAAS,CAACM,QAAQ;IACnC,MAAMD,KAAK,GAAGL,SAAS,CAACK,KAAK;IAC7B,MAAMmE,GAAG,GAAG,IAAIrF,0BAA0B,CAAEiF,iBAAiB,EAAE9D,QAAQ,EAAE+B,MAAM,EAAErC,SAAS,CAACC,UAAW,CAAC;IACvGoE,GAAG,CAACzB,IAAI,CAAE4B,GAAI,CAAC;IAEfnC,MAAM,IAAI/B,QAAQ;;IAElB;IACA;IACA,KAAM,IAAImE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpE,KAAK,EAAEoE,CAAC,EAAG,EAAG;MAElC,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpE,QAAQ,EAAEoE,CAAC,EAAG,EAAG;QAErCF,GAAG,CAAED,OAAO,CAAEG,CAAC,CAAE,CAAE,CAAED,CAAC,EAAEzE,SAAS,CAAEsE,OAAO,CAAEI,CAAC,CAAE,CAAE,CAAED,CAAE,CAAE,CAAC;MAEzD;IAED;EAED;EAEA,OAAOJ,GAAG;AAEX;;AAEA;AACA,OAAO,SAASN,qBAAqBA,CAAE/D,SAAS,EAAG;EAElD,MAAM2E,IAAI,GAAG3E,SAAS,CAAC4E,IAAI,CAAChE,KAAK,CAAC8C,WAAW;EAC7C,MAAMrD,KAAK,GAAGL,SAAS,CAACK,KAAK;EAC7B,MAAMC,QAAQ,GAAGN,SAAS,CAACM,QAAQ;EACnC,MAAML,UAAU,GAAGD,SAAS,CAACC,UAAU;EAEvC,MAAMW,KAAK,GAAG,IAAI+D,IAAI,CAAEtE,KAAK,GAAGC,QAAS,CAAC;EAC1C,IAAIuE,YAAY;EAChB,IAAK7E,SAAS,CAAC8D,qCAAqC,EAAG;IAEtDe,YAAY,GAAG,IAAI5F,wBAAwB,CAAE2B,KAAK,EAAEN,QAAQ,EAAEL,UAAU,EAAED,SAAS,CAAC8E,gBAAiB,CAAC;EAEvG,CAAC,MAAM;IAEND,YAAY,GAAG,IAAI/F,eAAe,CAAE8B,KAAK,EAAEN,QAAQ,EAAEL,UAAW,CAAC;EAElE;EAEA,KAAM,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAG,EAAG;IAElCsE,YAAY,CAACE,IAAI,CAAExE,CAAC,EAAEP,SAAS,CAACS,IAAI,CAAEF,CAAE,CAAE,CAAC;IAE3C,IAAKD,QAAQ,IAAI,CAAC,EAAG;MAEpBuE,YAAY,CAACG,IAAI,CAAEzE,CAAC,EAAEP,SAAS,CAACU,IAAI,CAAEH,CAAE,CAAE,CAAC;IAE5C;IAEA,IAAKD,QAAQ,IAAI,CAAC,EAAG;MAEpBuE,YAAY,CAACI,IAAI,CAAE1E,CAAC,EAAEP,SAAS,CAACW,IAAI,CAAEJ,CAAE,CAAE,CAAC;IAE5C;IAEA,IAAKD,QAAQ,IAAI,CAAC,EAAG;MAEpBuE,YAAY,CAACK,IAAI,CAAE3E,CAAC,EAAEP,SAAS,CAACmF,IAAI,CAAE5E,CAAE,CAAE,CAAC;IAE5C;EAED;EAEA,OAAOsE,YAAY;AAEpB;;AAEA;AACA,OAAO,SAASO,oBAAoBA,CAAE3F,QAAQ,EAAG;EAEhD,MAAMyB,UAAU,GAAGzB,QAAQ,CAACyB,UAAU;EACtC,MAAMmE,YAAY,GAAG5F,QAAQ,CAAC4F,YAAY;EAC1C,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;EAEzB,KAAM,MAAMC,GAAG,IAAItE,UAAU,EAAG;IAE/B,MAAMuE,IAAI,GAAGvE,UAAU,CAAEsE,GAAG,CAAE;IAC9B,IAAKC,IAAI,CAACvF,4BAA4B,EAAG;MAExC,IAAK,CAAEoF,OAAO,CAAC5C,GAAG,CAAE+C,IAAK,CAAC,EAAG;QAE5BH,OAAO,CAAC3B,GAAG,CAAE8B,IAAI,EAAE1B,qBAAqB,CAAE0B,IAAK,CAAE,CAAC;MAEnD;MAEAvE,UAAU,CAAEsE,GAAG,CAAE,GAAGF,OAAO,CAACI,GAAG,CAAED,IAAK,CAAC;IAExC;EAED;EAEA,KAAM,MAAMD,GAAG,IAAIH,YAAY,EAAG;IAEjC,MAAMI,IAAI,GAAGJ,YAAY,CAAEG,GAAG,CAAE;IAChC,IAAKC,IAAI,CAACvF,4BAA4B,EAAG;MAExC,IAAK,CAAEoF,OAAO,CAAC5C,GAAG,CAAE+C,IAAK,CAAC,EAAG;QAE5BH,OAAO,CAAC3B,GAAG,CAAE8B,IAAI,EAAE1B,qBAAqB,CAAE0B,IAAK,CAAE,CAAC;MAEnD;MAEAJ,YAAY,CAAEG,GAAG,CAAE,GAAGF,OAAO,CAACI,GAAG,CAAED,IAAK,CAAC;IAE1C;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAAElG,QAAQ,EAAG;EAEtC;EACA;EACA;EACA,IAAImG,GAAG,GAAG,CAAC;EACX,KAAM,MAAMnD,IAAI,IAAIhD,QAAQ,CAACyB,UAAU,EAAG;IAEzC,MAAMuE,IAAI,GAAGhG,QAAQ,CAACoG,YAAY,CAAEpD,IAAK,CAAC;IAC1CmD,GAAG,IAAIH,IAAI,CAACpF,KAAK,GAAGoF,IAAI,CAACnF,QAAQ,GAAGmF,IAAI,CAAC7E,KAAK,CAACkF,iBAAiB;EAEjE;EAEA,MAAMC,OAAO,GAAGtG,QAAQ,CAACuG,QAAQ,CAAC,CAAC;EACnCJ,GAAG,IAAIG,OAAO,GAAGA,OAAO,CAAC1F,KAAK,GAAG0F,OAAO,CAACzF,QAAQ,GAAGyF,OAAO,CAACnF,KAAK,CAACkF,iBAAiB,GAAG,CAAC;EACvF,OAAOF,GAAG;AAEX;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASK,aAAaA,CAAExG,QAAQ,EAAEyG,SAAS,GAAG,IAAI,EAAG;EAEpDA,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAEF,SAAS,EAAEG,MAAM,CAACC,OAAQ,CAAC;;EAEjD;EACA;EACA,MAAMC,WAAW,GAAG,CAAC,CAAC;EACtB,MAAMR,OAAO,GAAGtG,QAAQ,CAACuG,QAAQ,CAAC,CAAC;EACnC,MAAMQ,SAAS,GAAG/G,QAAQ,CAACoG,YAAY,CAAE,UAAW,CAAC;EACrD,MAAMY,WAAW,GAAGV,OAAO,GAAGA,OAAO,CAAC1F,KAAK,GAAGmG,SAAS,CAACnG,KAAK;;EAE7D;EACA,IAAIqG,SAAS,GAAG,CAAC;;EAEjB;EACA,MAAMC,cAAc,GAAG5E,MAAM,CAACC,IAAI,CAAEvC,QAAQ,CAACyB,UAAW,CAAC;EACzD,MAAM0F,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,kBAAkB,GAAG,CAAC,CAAC;EAC7B,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMxC,OAAO,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAE;EAClD,MAAMC,OAAO,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAE;;EAElD;EACA;EACA,KAAM,IAAIhE,CAAC,GAAG,CAAC,EAAE4D,CAAC,GAAGwC,cAAc,CAACrF,MAAM,EAAEf,CAAC,GAAG4D,CAAC,EAAE5D,CAAC,EAAG,EAAG;IAEzD,MAAMkC,IAAI,GAAGkE,cAAc,CAAEpG,CAAC,CAAE;IAChC,MAAMkF,IAAI,GAAGhG,QAAQ,CAACyB,UAAU,CAAEuB,IAAI,CAAE;IAExCmE,aAAa,CAAEnE,IAAI,CAAE,GAAG,IAAI3D,eAAe,CAC1C,IAAI2G,IAAI,CAAC7E,KAAK,CAAC8C,WAAW,CAAE+B,IAAI,CAACpF,KAAK,GAAGoF,IAAI,CAACnF,QAAS,CAAC,EACxDmF,IAAI,CAACnF,QAAQ,EACbmF,IAAI,CAACxF,UACN,CAAC;IAED,MAAM8G,SAAS,GAAGtH,QAAQ,CAACyC,eAAe,CAAEO,IAAI,CAAE;IAClD,IAAKsE,SAAS,EAAG;MAEhBF,kBAAkB,CAAEpE,IAAI,CAAE,GAAG,IAAI3D,eAAe,CAC/C,IAAIiI,SAAS,CAACnG,KAAK,CAAC8C,WAAW,CAAEqD,SAAS,CAAC1G,KAAK,GAAG0G,SAAS,CAACzG,QAAS,CAAC,EACvEyG,SAAS,CAACzG,QAAQ,EAClByG,SAAS,CAAC9G,UACX,CAAC;IAEF;EAED;;EAEA;EACA,MAAM+G,YAAY,GAAGb,IAAI,CAACc,KAAK,CAAE,CAAC,GAAGf,SAAU,CAAC;EAChD,MAAMgB,eAAe,GAAGf,IAAI,CAACgB,GAAG,CAAE,EAAE,EAAEH,YAAa,CAAC;EACpD,KAAM,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkG,WAAW,EAAElG,CAAC,EAAG,EAAG;IAExC,MAAMO,KAAK,GAAGiF,OAAO,GAAGA,OAAO,CAACtF,IAAI,CAAEF,CAAE,CAAC,GAAGA,CAAC;;IAE7C;IACA,IAAI6G,IAAI,GAAG,EAAE;IACb,KAAM,IAAI5G,CAAC,GAAG,CAAC,EAAE2D,CAAC,GAAGwC,cAAc,CAACrF,MAAM,EAAEd,CAAC,GAAG2D,CAAC,EAAE3D,CAAC,EAAG,EAAG;MAEzD,MAAMiC,IAAI,GAAGkE,cAAc,CAAEnG,CAAC,CAAE;MAChC,MAAMR,SAAS,GAAGP,QAAQ,CAACoG,YAAY,CAAEpD,IAAK,CAAC;MAC/C,MAAMnC,QAAQ,GAAGN,SAAS,CAACM,QAAQ;MAEnC,KAAM,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpE,QAAQ,EAAEoE,CAAC,EAAG,EAAG;QAErC;QACA0C,IAAI,IAAI,GAAI,CAAE,EAAIpH,SAAS,CAAEsE,OAAO,CAAEI,CAAC,CAAE,CAAE,CAAE5D,KAAM,CAAC,GAAGoG,eAAe,CAAE,GAAI;MAE7E;IAED;;IAEA;IACA;IACA,IAAKE,IAAI,IAAIb,WAAW,EAAG;MAE1BO,UAAU,CAAClE,IAAI,CAAE2D,WAAW,CAAEa,IAAI,CAAG,CAAC;IAEvC,CAAC,MAAM;MAEN;MACA,KAAM,IAAI5G,CAAC,GAAG,CAAC,EAAE2D,CAAC,GAAGwC,cAAc,CAACrF,MAAM,EAAEd,CAAC,GAAG2D,CAAC,EAAE3D,CAAC,EAAG,EAAG;QAEzD,MAAMiC,IAAI,GAAGkE,cAAc,CAAEnG,CAAC,CAAE;QAChC,MAAMR,SAAS,GAAGP,QAAQ,CAACoG,YAAY,CAAEpD,IAAK,CAAC;QAC/C,MAAMsE,SAAS,GAAGtH,QAAQ,CAACyC,eAAe,CAAEO,IAAI,CAAE;QAClD,MAAMnC,QAAQ,GAAGN,SAAS,CAACM,QAAQ;QACnC,MAAM+G,QAAQ,GAAGT,aAAa,CAAEnE,IAAI,CAAE;QACtC,MAAM6E,cAAc,GAAGT,kBAAkB,CAAEpE,IAAI,CAAE;QAEjD,KAAM,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpE,QAAQ,EAAEoE,CAAC,EAAG,EAAG;UAErC,MAAM6C,UAAU,GAAGjD,OAAO,CAAEI,CAAC,CAAE;UAC/B,MAAM8C,UAAU,GAAGjD,OAAO,CAAEG,CAAC,CAAE;UAC/B2C,QAAQ,CAAEG,UAAU,CAAE,CAAEd,SAAS,EAAE1G,SAAS,CAAEuH,UAAU,CAAE,CAAEzG,KAAM,CAAE,CAAC;UAErE,IAAKiG,SAAS,EAAG;YAEhB,KAAM,IAAIU,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGX,SAAS,CAACzF,MAAM,EAAEmG,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;cAEtDH,cAAc,CAAEG,CAAC,CAAE,CAAED,UAAU,CAAE,CAAEd,SAAS,EAAEK,SAAS,CAAEU,CAAC,CAAE,CAAEF,UAAU,CAAE,CAAEzG,KAAM,CAAE,CAAC;YAEtF;UAED;QAED;MAED;MAEAyF,WAAW,CAAEa,IAAI,CAAE,GAAGV,SAAS;MAC/BI,UAAU,CAAClE,IAAI,CAAE8D,SAAU,CAAC;MAC5BA,SAAS,EAAG;IAEb;EAED;;EAEA;EACA,MAAM9C,MAAM,GAAGnE,QAAQ,CAACkI,KAAK,CAAC,CAAC;EAC/B,KAAM,MAAMlF,IAAI,IAAIhD,QAAQ,CAACyB,UAAU,EAAG;IAEzC,MAAM0G,YAAY,GAAGhB,aAAa,CAAEnE,IAAI,CAAE;IAE1CmB,MAAM,CAACrC,YAAY,CAAEkB,IAAI,EAAE,IAAI3D,eAAe,CAC7C8I,YAAY,CAAChH,KAAK,CAACiH,KAAK,CAAE,CAAC,EAAEnB,SAAS,GAAGkB,YAAY,CAACtH,QAAS,CAAC,EAChEsH,YAAY,CAACtH,QAAQ,EACrBsH,YAAY,CAAC3H,UACd,CAAE,CAAC;IAEH,IAAK,EAAIwC,IAAI,IAAIoE,kBAAkB,CAAE,EAAG;IAExC,KAAM,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,kBAAkB,CAAEpE,IAAI,CAAE,CAACnB,MAAM,EAAEd,CAAC,EAAG,EAAG;MAE9D,MAAMsH,iBAAiB,GAAGjB,kBAAkB,CAAEpE,IAAI,CAAE,CAAEjC,CAAC,CAAE;MAEzDoD,MAAM,CAAC1B,eAAe,CAAEO,IAAI,CAAE,CAAEjC,CAAC,CAAE,GAAG,IAAI1B,eAAe,CACxDgJ,iBAAiB,CAAClH,KAAK,CAACiH,KAAK,CAAE,CAAC,EAAEnB,SAAS,GAAGoB,iBAAiB,CAACxH,QAAS,CAAC,EAC1EwH,iBAAiB,CAACxH,QAAQ,EAC1BwH,iBAAiB,CAAC7H,UACnB,CAAC;IAEF;EAED;;EAEA;;EAEA2D,MAAM,CAACX,QAAQ,CAAE6D,UAAW,CAAC;EAE7B,OAAOlD,MAAM;AAEd;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASmE,mBAAmBA,CAAEtI,QAAQ,EAAEuI,QAAQ,EAAG;EAElD,IAAKA,QAAQ,KAAK1I,iBAAiB,EAAG;IAErCiD,OAAO,CAAC0F,IAAI,CAAE,yFAA0F,CAAC;IACzG,OAAOxI,QAAQ;EAEhB;EAEA,IAAKuI,QAAQ,KAAK5I,mBAAmB,IAAI4I,QAAQ,KAAK3I,qBAAqB,EAAG;IAE7E,IAAIyB,KAAK,GAAGrB,QAAQ,CAACuG,QAAQ,CAAC,CAAC;;IAE/B;;IAEA,IAAKlF,KAAK,KAAK,IAAI,EAAG;MAErB,MAAMiF,OAAO,GAAG,EAAE;MAElB,MAAM5E,QAAQ,GAAG1B,QAAQ,CAACoG,YAAY,CAAE,UAAW,CAAC;MAEpD,IAAK1E,QAAQ,KAAKwB,SAAS,EAAG;QAE7B,KAAM,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,QAAQ,CAACd,KAAK,EAAEE,CAAC,EAAG,EAAG;UAE3CwF,OAAO,CAACnD,IAAI,CAAErC,CAAE,CAAC;QAElB;QAEAd,QAAQ,CAACwD,QAAQ,CAAE8C,OAAQ,CAAC;QAC5BjF,KAAK,GAAGrB,QAAQ,CAACuG,QAAQ,CAAC,CAAC;MAE5B,CAAC,MAAM;QAENzD,OAAO,CAACC,KAAK,CAAE,yGAA0G,CAAC;QAC1H,OAAO/C,QAAQ;MAEhB;IAED;;IAEA;;IAEA,MAAMyI,iBAAiB,GAAGpH,KAAK,CAACT,KAAK,GAAG,CAAC;IACzC,MAAMyG,UAAU,GAAG,EAAE;IAErB,IAAKkB,QAAQ,KAAK5I,mBAAmB,EAAG;MAEvC;;MAEA,KAAM,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI2H,iBAAiB,EAAE3H,CAAC,EAAG,EAAG;QAE/CuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAE,CAAE,CAAE,CAAC;QAClCqG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAE,CAAE,CAAC;QAClCuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAC,GAAG,CAAE,CAAE,CAAC;MAEvC;IAED,CAAC,MAAM;MAEN;;MAEA,KAAM,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2H,iBAAiB,EAAE3H,CAAC,EAAG,EAAG;QAE9C,IAAKA,CAAC,GAAG,CAAC,KAAK,CAAC,EAAG;UAElBuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAE,CAAE,CAAC;UAClCuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAC,GAAG,CAAE,CAAE,CAAC;UACtCuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAC,GAAG,CAAE,CAAE,CAAC;QAEvC,CAAC,MAAM;UAENuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAC,GAAG,CAAE,CAAE,CAAC;UACtCuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAC,GAAG,CAAE,CAAE,CAAC;UACtCuG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAEF,CAAE,CAAE,CAAC;QAEnC;MAED;IAED;IAEA,IAAOuG,UAAU,CAACxF,MAAM,GAAG,CAAC,KAAO4G,iBAAiB,EAAG;MAEtD3F,OAAO,CAACC,KAAK,CAAE,kGAAmG,CAAC;IAEpH;;IAEA;;IAEA,MAAM2F,WAAW,GAAG1I,QAAQ,CAACkI,KAAK,CAAC,CAAC;IACpCQ,WAAW,CAAClF,QAAQ,CAAE6D,UAAW,CAAC;IAClCqB,WAAW,CAACC,WAAW,CAAC,CAAC;IAEzB,OAAOD,WAAW;EAEnB,CAAC,MAAM;IAEN5F,OAAO,CAACC,KAAK,CAAE,qEAAqE,EAAEwF,QAAS,CAAC;IAChG,OAAOvI,QAAQ;EAEhB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4I,wBAAwBA,CAAEC,MAAM,EAAG;EAE3C,MAAMC,GAAG,GAAG,IAAIhJ,OAAO,CAAC,CAAC;EACzB,MAAMiJ,GAAG,GAAG,IAAIjJ,OAAO,CAAC,CAAC;EACzB,MAAMkJ,GAAG,GAAG,IAAIlJ,OAAO,CAAC,CAAC;EAEzB,MAAMmJ,MAAM,GAAG,IAAInJ,OAAO,CAAC,CAAC;EAC5B,MAAMoJ,MAAM,GAAG,IAAIpJ,OAAO,CAAC,CAAC;EAC5B,MAAMqJ,MAAM,GAAG,IAAIrJ,OAAO,CAAC,CAAC;EAE5B,MAAMsJ,OAAO,GAAG,IAAItJ,OAAO,CAAC,CAAC;EAC7B,MAAMuJ,OAAO,GAAG,IAAIvJ,OAAO,CAAC,CAAC;EAC7B,MAAMwJ,OAAO,GAAG,IAAIxJ,OAAO,CAAC,CAAC;EAE7B,SAASyJ,8BAA8BA,CACtCV,MAAM,EACNtI,SAAS,EACTiJ,cAAc,EACd9G,oBAAoB,EACpB+G,CAAC,EACDC,CAAC,EACD1E,CAAC,EACD2E,sBAAsB,EACrB;IAEDb,GAAG,CAACc,mBAAmB,CAAErJ,SAAS,EAAEkJ,CAAE,CAAC;IACvCV,GAAG,CAACa,mBAAmB,CAAErJ,SAAS,EAAEmJ,CAAE,CAAC;IACvCV,GAAG,CAACY,mBAAmB,CAAErJ,SAAS,EAAEyE,CAAE,CAAC;IAEvC,MAAM6E,eAAe,GAAGhB,MAAM,CAACiB,qBAAqB;IAEpD,IAAKN,cAAc,IAAIK,eAAe,EAAG;MAExCT,OAAO,CAAClF,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;MACtBmF,OAAO,CAACnF,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;MACtBoF,OAAO,CAACpF,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;MAEtB,KAAM,IAAIpD,CAAC,GAAG,CAAC,EAAEiJ,EAAE,GAAGP,cAAc,CAAC3H,MAAM,EAAEf,CAAC,GAAGiJ,EAAE,EAAEjJ,CAAC,EAAG,EAAG;QAE3D,MAAMkJ,SAAS,GAAGH,eAAe,CAAE/I,CAAC,CAAE;QACtC,MAAMmJ,KAAK,GAAGT,cAAc,CAAE1I,CAAC,CAAE;QAEjC,IAAKkJ,SAAS,KAAK,CAAC,EAAG;QAEvBf,MAAM,CAACW,mBAAmB,CAAEK,KAAK,EAAER,CAAE,CAAC;QACtCP,MAAM,CAACU,mBAAmB,CAAEK,KAAK,EAAEP,CAAE,CAAC;QACtCP,MAAM,CAACS,mBAAmB,CAAEK,KAAK,EAAEjF,CAAE,CAAC;QAEtC,IAAKtC,oBAAoB,EAAG;UAE3B0G,OAAO,CAACc,eAAe,CAAEjB,MAAM,EAAEe,SAAU,CAAC;UAC5CX,OAAO,CAACa,eAAe,CAAEhB,MAAM,EAAEc,SAAU,CAAC;UAC5CV,OAAO,CAACY,eAAe,CAAEf,MAAM,EAAEa,SAAU,CAAC;QAE7C,CAAC,MAAM;UAENZ,OAAO,CAACc,eAAe,CAAEjB,MAAM,CAACkB,GAAG,CAAErB,GAAI,CAAC,EAAEkB,SAAU,CAAC;UACvDX,OAAO,CAACa,eAAe,CAAEhB,MAAM,CAACiB,GAAG,CAAEpB,GAAI,CAAC,EAAEiB,SAAU,CAAC;UACvDV,OAAO,CAACY,eAAe,CAAEf,MAAM,CAACgB,GAAG,CAAEnB,GAAI,CAAC,EAAEgB,SAAU,CAAC;QAExD;MAED;MAEAlB,GAAG,CAACsB,GAAG,CAAEhB,OAAQ,CAAC;MAClBL,GAAG,CAACqB,GAAG,CAAEf,OAAQ,CAAC;MAClBL,GAAG,CAACoB,GAAG,CAAEd,OAAQ,CAAC;IAEnB;IAEA,IAAKT,MAAM,CAACwB,aAAa,EAAG;MAE3BxB,MAAM,CAACyB,kBAAkB,CAAEb,CAAC,EAAEX,GAAI,CAAC;MACnCD,MAAM,CAACyB,kBAAkB,CAAEZ,CAAC,EAAEX,GAAI,CAAC;MACnCF,MAAM,CAACyB,kBAAkB,CAAEtF,CAAC,EAAEgE,GAAI,CAAC;IAEpC;IAEAW,sBAAsB,CAAEF,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGX,GAAG,CAACyB,CAAC;IAC3CZ,sBAAsB,CAAEF,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGX,GAAG,CAAC0B,CAAC;IAC3Cb,sBAAsB,CAAEF,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGX,GAAG,CAAC2B,CAAC;IAC3Cd,sBAAsB,CAAED,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGX,GAAG,CAACwB,CAAC;IAC3CZ,sBAAsB,CAAED,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGX,GAAG,CAACyB,CAAC;IAC3Cb,sBAAsB,CAAED,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGX,GAAG,CAAC0B,CAAC;IAC3Cd,sBAAsB,CAAE3E,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGgE,GAAG,CAACuB,CAAC;IAC3CZ,sBAAsB,CAAE3E,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGgE,GAAG,CAACwB,CAAC;IAC3Cb,sBAAsB,CAAE3E,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGgE,GAAG,CAACyB,CAAC;EAE5C;EAEA,MAAMzK,QAAQ,GAAG6I,MAAM,CAAC7I,QAAQ;EAChC,MAAM0K,QAAQ,GAAG7B,MAAM,CAAC6B,QAAQ;EAEhC,IAAIjB,CAAC,EAAEC,CAAC,EAAE1E,CAAC;EACX,MAAM3D,KAAK,GAAGrB,QAAQ,CAACqB,KAAK;EAC5B,MAAMsJ,iBAAiB,GAAG3K,QAAQ,CAACyB,UAAU,CAACC,QAAQ;EACtD,MAAMkJ,aAAa,GAAG5K,QAAQ,CAACyC,eAAe,CAACf,QAAQ;EACvD,MAAMgB,oBAAoB,GAAG1C,QAAQ,CAAC0C,oBAAoB;EAC1D,MAAMmI,eAAe,GAAG7K,QAAQ,CAACyB,UAAU,CAACE,MAAM;EAClD,MAAMmJ,WAAW,GAAG9K,QAAQ,CAACyC,eAAe,CAACf,QAAQ;EAErD,MAAMqJ,MAAM,GAAG/K,QAAQ,CAAC+K,MAAM;EAC9B,MAAMC,SAAS,GAAGhL,QAAQ,CAACgL,SAAS;EACpC,IAAIlK,CAAC,EAAEC,CAAC,EAAEgJ,EAAE,EAAEkB,EAAE;EAChB,IAAIC,KAAK;EACT,IAAIC,KAAK,EAAEC,GAAG;EAEd,MAAMC,gBAAgB,GAAG,IAAI1K,YAAY,CAAEgK,iBAAiB,CAAC/J,KAAK,GAAG+J,iBAAiB,CAAC9J,QAAS,CAAC;EACjG,MAAMyK,cAAc,GAAG,IAAI3K,YAAY,CAAEkK,eAAe,CAACjK,KAAK,GAAGiK,eAAe,CAAChK,QAAS,CAAC;EAE3F,IAAKQ,KAAK,KAAK,IAAI,EAAG;IAErB;;IAEA,IAAKkK,KAAK,CAACC,OAAO,CAAEd,QAAS,CAAC,EAAG;MAEhC,KAAM5J,CAAC,GAAG,CAAC,EAAEiJ,EAAE,GAAGgB,MAAM,CAAClJ,MAAM,EAAEf,CAAC,GAAGiJ,EAAE,EAAEjJ,CAAC,EAAG,EAAG;QAE/CoK,KAAK,GAAGH,MAAM,CAAEjK,CAAC,CAAE;QAEnBqK,KAAK,GAAGzE,IAAI,CAACC,GAAG,CAAEuE,KAAK,CAACC,KAAK,EAAEH,SAAS,CAACG,KAAM,CAAC;QAChDC,GAAG,GAAG1E,IAAI,CAAC+E,GAAG,CAAIP,KAAK,CAACC,KAAK,GAAGD,KAAK,CAACtK,KAAK,EAAMoK,SAAS,CAACG,KAAK,GAAGH,SAAS,CAACpK,KAAQ,CAAC;QAEtF,KAAMG,CAAC,GAAGoK,KAAK,EAAEF,EAAE,GAAGG,GAAG,EAAErK,CAAC,GAAGkK,EAAE,EAAElK,CAAC,IAAI,CAAC,EAAG;UAE3C0I,CAAC,GAAGpI,KAAK,CAACL,IAAI,CAAED,CAAE,CAAC;UACnB2I,CAAC,GAAGrI,KAAK,CAACL,IAAI,CAAED,CAAC,GAAG,CAAE,CAAC;UACvBiE,CAAC,GAAG3D,KAAK,CAACL,IAAI,CAAED,CAAC,GAAG,CAAE,CAAC;UAEvBwI,8BAA8B,CAC7BV,MAAM,EACN8B,iBAAiB,EACjBC,aAAa,EACblI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPqG,gBACD,CAAC;UAED9B,8BAA8B,CAC7BV,MAAM,EACNgC,eAAe,EACfC,WAAW,EACXpI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPsG,cACD,CAAC;QAEF;MAED;IAED,CAAC,MAAM;MAENH,KAAK,GAAGzE,IAAI,CAACC,GAAG,CAAE,CAAC,EAAEqE,SAAS,CAACG,KAAM,CAAC;MACtCC,GAAG,GAAG1E,IAAI,CAAC+E,GAAG,CAAEpK,KAAK,CAACT,KAAK,EAAIoK,SAAS,CAACG,KAAK,GAAGH,SAAS,CAACpK,KAAQ,CAAC;MAEpE,KAAME,CAAC,GAAGqK,KAAK,EAAEpB,EAAE,GAAGqB,GAAG,EAAEtK,CAAC,GAAGiJ,EAAE,EAAEjJ,CAAC,IAAI,CAAC,EAAG;QAE3C2I,CAAC,GAAGpI,KAAK,CAACL,IAAI,CAAEF,CAAE,CAAC;QACnB4I,CAAC,GAAGrI,KAAK,CAACL,IAAI,CAAEF,CAAC,GAAG,CAAE,CAAC;QACvBkE,CAAC,GAAG3D,KAAK,CAACL,IAAI,CAAEF,CAAC,GAAG,CAAE,CAAC;QAEvByI,8BAA8B,CAC7BV,MAAM,EACN8B,iBAAiB,EACjBC,aAAa,EACblI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPqG,gBACD,CAAC;QAED9B,8BAA8B,CAC7BV,MAAM,EACNgC,eAAe,EACfC,WAAW,EACXpI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPsG,cACD,CAAC;MAEF;IAED;EAED,CAAC,MAAM;IAEN;;IAEA,IAAKC,KAAK,CAACC,OAAO,CAAEd,QAAS,CAAC,EAAG;MAEhC,KAAM5J,CAAC,GAAG,CAAC,EAAEiJ,EAAE,GAAGgB,MAAM,CAAClJ,MAAM,EAAEf,CAAC,GAAGiJ,EAAE,EAAEjJ,CAAC,EAAG,EAAG;QAE/CoK,KAAK,GAAGH,MAAM,CAAEjK,CAAC,CAAE;QAEnBqK,KAAK,GAAGzE,IAAI,CAACC,GAAG,CAAEuE,KAAK,CAACC,KAAK,EAAEH,SAAS,CAACG,KAAM,CAAC;QAChDC,GAAG,GAAG1E,IAAI,CAAC+E,GAAG,CAAIP,KAAK,CAACC,KAAK,GAAGD,KAAK,CAACtK,KAAK,EAAMoK,SAAS,CAACG,KAAK,GAAGH,SAAS,CAACpK,KAAQ,CAAC;QAEtF,KAAMG,CAAC,GAAGoK,KAAK,EAAEF,EAAE,GAAGG,GAAG,EAAErK,CAAC,GAAGkK,EAAE,EAAElK,CAAC,IAAI,CAAC,EAAG;UAE3C0I,CAAC,GAAG1I,CAAC;UACL2I,CAAC,GAAG3I,CAAC,GAAG,CAAC;UACTiE,CAAC,GAAGjE,CAAC,GAAG,CAAC;UAETwI,8BAA8B,CAC7BV,MAAM,EACN8B,iBAAiB,EACjBC,aAAa,EACblI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPqG,gBACD,CAAC;UAED9B,8BAA8B,CAC7BV,MAAM,EACNgC,eAAe,EACfC,WAAW,EACXpI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPsG,cACD,CAAC;QAEF;MAED;IAED,CAAC,MAAM;MAENH,KAAK,GAAGzE,IAAI,CAACC,GAAG,CAAE,CAAC,EAAEqE,SAAS,CAACG,KAAM,CAAC;MACtCC,GAAG,GAAG1E,IAAI,CAAC+E,GAAG,CAAEd,iBAAiB,CAAC/J,KAAK,EAAIoK,SAAS,CAACG,KAAK,GAAGH,SAAS,CAACpK,KAAQ,CAAC;MAEhF,KAAME,CAAC,GAAGqK,KAAK,EAAEpB,EAAE,GAAGqB,GAAG,EAAEtK,CAAC,GAAGiJ,EAAE,EAAEjJ,CAAC,IAAI,CAAC,EAAG;QAE3C2I,CAAC,GAAG3I,CAAC;QACL4I,CAAC,GAAG5I,CAAC,GAAG,CAAC;QACTkE,CAAC,GAAGlE,CAAC,GAAG,CAAC;QAETyI,8BAA8B,CAC7BV,MAAM,EACN8B,iBAAiB,EACjBC,aAAa,EACblI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPqG,gBACD,CAAC;QAED9B,8BAA8B,CAC7BV,MAAM,EACNgC,eAAe,EACfC,WAAW,EACXpI,oBAAoB,EACpB+G,CAAC,EAAEC,CAAC,EAAE1E,CAAC,EACPsG,cACD,CAAC;MAEF;IAED;EAED;EAEA,MAAMI,wBAAwB,GAAG,IAAInM,sBAAsB,CAAE8L,gBAAgB,EAAE,CAAE,CAAC;EAClF,MAAMM,sBAAsB,GAAG,IAAIpM,sBAAsB,CAAE+L,cAAc,EAAE,CAAE,CAAC;EAE9E,OAAO;IAENX,iBAAiB,EAAEA,iBAAiB;IACpCE,eAAe,EAAEA,eAAe;IAChCa,wBAAwB,EAAEA,wBAAwB;IAClDC,sBAAsB,EAAEA;EAEzB,CAAC;AAEF;AAEA,SAASC,WAAWA,CAAE5L,QAAQ,EAAG;EAEhC,IAAKA,QAAQ,CAAC+K,MAAM,CAAClJ,MAAM,KAAK,CAAC,EAAG;IAEnCiB,OAAO,CAAC0F,IAAI,CAAE,mFAAoF,CAAC;IACnG,OAAOxI,QAAQ;EAEhB;EAEA,IAAI+K,MAAM,GAAG/K,QAAQ,CAAC+K,MAAM;;EAE5B;;EAEAA,MAAM,GAAGA,MAAM,CAACc,IAAI,CAAE,CAAEpC,CAAC,EAAEC,CAAC,KAAM;IAEjC,IAAKD,CAAC,CAACqC,aAAa,KAAKpC,CAAC,CAACoC,aAAa,EAAG,OAAOrC,CAAC,CAACqC,aAAa,GAAGpC,CAAC,CAACoC,aAAa;IAEnF,OAAOrC,CAAC,CAAC0B,KAAK,GAAGzB,CAAC,CAACyB,KAAK;EAEzB,CAAE,CAAC;;EAEH;;EAEA,IAAKnL,QAAQ,CAACuG,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAG;IAEnC,MAAMoE,iBAAiB,GAAG3K,QAAQ,CAACoG,YAAY,CAAE,UAAW,CAAC;IAC7D,MAAME,OAAO,GAAG,EAAE;IAElB,KAAM,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6J,iBAAiB,CAAC/J,KAAK,EAAEE,CAAC,IAAI,CAAC,EAAG;MAEtDwF,OAAO,CAACnD,IAAI,CAAErC,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAE,CAAC;IAEhC;IAEAd,QAAQ,CAACwD,QAAQ,CAAE8C,OAAQ,CAAC;EAE7B;;EAEA;;EAEA,MAAMjF,KAAK,GAAGrB,QAAQ,CAACuG,QAAQ,CAAC,CAAC;EAEjC,MAAMc,UAAU,GAAG,EAAE;EAErB,KAAM,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiK,MAAM,CAAClJ,MAAM,EAAEf,CAAC,EAAG,EAAG;IAE1C,MAAMoK,KAAK,GAAGH,MAAM,CAAEjK,CAAC,CAAE;IAEzB,MAAMiL,UAAU,GAAGb,KAAK,CAACC,KAAK;IAC9B,MAAMa,WAAW,GAAGD,UAAU,GAAGb,KAAK,CAACtK,KAAK;IAE5C,KAAM,IAAIG,CAAC,GAAGgL,UAAU,EAAEhL,CAAC,GAAGiL,WAAW,EAAEjL,CAAC,EAAG,EAAG;MAEjDsG,UAAU,CAAClE,IAAI,CAAE9B,KAAK,CAACL,IAAI,CAAED,CAAE,CAAE,CAAC;IAEnC;EAED;EAEAf,QAAQ,CAACiM,OAAO,CAAC,CAAC,CAAC,CAAC;EACpBjM,QAAQ,CAACwD,QAAQ,CAAE6D,UAAW,CAAC;;EAE/B;;EAEA,IAAI8D,KAAK,GAAG,CAAC;EAEb,KAAM,IAAIrK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiK,MAAM,CAAClJ,MAAM,EAAEf,CAAC,EAAG,EAAG;IAE1C,MAAMoK,KAAK,GAAGH,MAAM,CAAEjK,CAAC,CAAE;IAEzBoK,KAAK,CAACC,KAAK,GAAGA,KAAK;IACnBA,KAAK,IAAID,KAAK,CAACtK,KAAK;EAErB;;EAEA;;EAEA,IAAIsL,YAAY,GAAGnB,MAAM,CAAE,CAAC,CAAE;EAE9B/K,QAAQ,CAAC+K,MAAM,GAAG,CAAEmB,YAAY,CAAE;EAElC,KAAM,IAAIpL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiK,MAAM,CAAClJ,MAAM,EAAEf,CAAC,EAAG,EAAG;IAE1C,MAAMoK,KAAK,GAAGH,MAAM,CAAEjK,CAAC,CAAE;IAEzB,IAAKoL,YAAY,CAACJ,aAAa,KAAKZ,KAAK,CAACY,aAAa,EAAG;MAEzDI,YAAY,CAACtL,KAAK,IAAIsK,KAAK,CAACtK,KAAK;IAElC,CAAC,MAAM;MAENsL,YAAY,GAAGhB,KAAK;MACpBlL,QAAQ,CAAC+K,MAAM,CAAC5H,IAAI,CAAE+I,YAAa,CAAC;IAErC;EAED;EAEA,OAAOlM,QAAQ;AAEhB;;AAGA;AACA;AACA,SAASmM,gBAAgBA,CAAEnM,QAAQ,EAAEoM,WAAW,GAAG1F,IAAI,CAAC2F,EAAE,GAAG,CAAC,CAAC,kBAAmB;EAEjF,MAAMC,SAAS,GAAG5F,IAAI,CAAC6F,GAAG,CAAEH,WAAY,CAAC;EACzC,MAAMI,cAAc,GAAG,CAAE,CAAC,GAAG,KAAK,IAAK,GAAG;;EAE1C;EACA,MAAMC,KAAK,GAAG,CAAE,IAAI3M,OAAO,CAAC,CAAC,EAAE,IAAIA,OAAO,CAAC,CAAC,EAAE,IAAIA,OAAO,CAAC,CAAC,CAAE;EAC7D,MAAM4M,QAAQ,GAAG,IAAI5M,OAAO,CAAC,CAAC;EAC9B,MAAM6M,QAAQ,GAAG,IAAI7M,OAAO,CAAC,CAAC;EAC9B,MAAM8M,QAAQ,GAAG,IAAI9M,OAAO,CAAC,CAAC;EAC9B,MAAM+M,SAAS,GAAG,IAAI/M,OAAO,CAAC,CAAC;;EAE/B;EACA,SAASgN,UAAUA,CAAEC,CAAC,EAAG;IAExB,MAAMxC,CAAC,GAAG,CAAE,EAAIwC,CAAC,CAACxC,CAAC,GAAGiC,cAAc,CAAE;IACtC,MAAMhC,CAAC,GAAG,CAAE,EAAIuC,CAAC,CAACvC,CAAC,GAAGgC,cAAc,CAAE;IACtC,MAAM/B,CAAC,GAAG,CAAE,EAAIsC,CAAC,CAACtC,CAAC,GAAG+B,cAAc,CAAE;IACtC,OAAO,GAAGjC,CAAC,IAAIC,CAAC,IAAIC,CAAC,EAAE;EAExB;EAEA,MAAMuC,cAAc,GAAGhN,QAAQ,CAACsB,YAAY,CAAC,CAAC;EAC9C,MAAM2L,OAAO,GAAGD,cAAc,CAACvL,UAAU,CAACC,QAAQ;EAClD,MAAMwL,SAAS,GAAG,CAAC,CAAC;;EAEpB;EACA,KAAM,IAAIpM,CAAC,GAAG,CAAC,EAAE4D,CAAC,GAAGuI,OAAO,CAACrM,KAAK,GAAG,CAAC,EAAEE,CAAC,GAAG4D,CAAC,EAAE5D,CAAC,EAAG,EAAG;IAErD,MAAMqM,EAAE,GAAG,CAAC,GAAGrM,CAAC;IAChB,MAAM2I,CAAC,GAAGgD,KAAK,CAAE,CAAC,CAAE,CAAC7C,mBAAmB,CAAEqD,OAAO,EAAEE,EAAE,GAAG,CAAE,CAAC;IAC3D,MAAMzD,CAAC,GAAG+C,KAAK,CAAE,CAAC,CAAE,CAAC7C,mBAAmB,CAAEqD,OAAO,EAAEE,EAAE,GAAG,CAAE,CAAC;IAC3D,MAAMnI,CAAC,GAAGyH,KAAK,CAAE,CAAC,CAAE,CAAC7C,mBAAmB,CAAEqD,OAAO,EAAEE,EAAE,GAAG,CAAE,CAAC;IAE3DT,QAAQ,CAACU,UAAU,CAAEpI,CAAC,EAAE0E,CAAE,CAAC;IAC3BiD,QAAQ,CAACS,UAAU,CAAE3D,CAAC,EAAEC,CAAE,CAAC;;IAE3B;IACA,MAAM/H,MAAM,GAAG,IAAI7B,OAAO,CAAC,CAAC,CAACuN,YAAY,CAAEX,QAAQ,EAAEC,QAAS,CAAC,CAACW,SAAS,CAAC,CAAC;IAC3E,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMC,IAAI,GAAGf,KAAK,CAAEc,CAAC,CAAE;MACvB,MAAM5F,IAAI,GAAGmF,UAAU,CAAEU,IAAK,CAAC;MAC/B,IAAK,EAAI7F,IAAI,IAAIuF,SAAS,CAAE,EAAG;QAE9BA,SAAS,CAAEvF,IAAI,CAAE,GAAG,EAAE;MAEvB;MAEAuF,SAAS,CAAEvF,IAAI,CAAE,CAACxE,IAAI,CAAExB,MAAO,CAAC;IAEjC;EAED;;EAEA;EACA;EACA,MAAM8L,WAAW,GAAG,IAAI9M,YAAY,CAAEsM,OAAO,CAACrM,KAAK,GAAG,CAAE,CAAC;EACzD,MAAM8M,QAAQ,GAAG,IAAIrO,eAAe,CAAEoO,WAAW,EAAE,CAAC,EAAE,KAAM,CAAC;EAC7D,KAAM,IAAI3M,CAAC,GAAG,CAAC,EAAE4D,CAAC,GAAGuI,OAAO,CAACrM,KAAK,GAAG,CAAC,EAAEE,CAAC,GAAG4D,CAAC,EAAE5D,CAAC,EAAG,EAAG;IAErD;IACA,MAAMqM,EAAE,GAAG,CAAC,GAAGrM,CAAC;IAChB,MAAM2I,CAAC,GAAGgD,KAAK,CAAE,CAAC,CAAE,CAAC7C,mBAAmB,CAAEqD,OAAO,EAAEE,EAAE,GAAG,CAAE,CAAC;IAC3D,MAAMzD,CAAC,GAAG+C,KAAK,CAAE,CAAC,CAAE,CAAC7C,mBAAmB,CAAEqD,OAAO,EAAEE,EAAE,GAAG,CAAE,CAAC;IAC3D,MAAMnI,CAAC,GAAGyH,KAAK,CAAE,CAAC,CAAE,CAAC7C,mBAAmB,CAAEqD,OAAO,EAAEE,EAAE,GAAG,CAAE,CAAC;IAE3DT,QAAQ,CAACU,UAAU,CAAEpI,CAAC,EAAE0E,CAAE,CAAC;IAC3BiD,QAAQ,CAACS,UAAU,CAAE3D,CAAC,EAAEC,CAAE,CAAC;IAE3BkD,QAAQ,CAACS,YAAY,CAAEX,QAAQ,EAAEC,QAAS,CAAC,CAACW,SAAS,CAAC,CAAC;;IAEvD;IACA,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMC,IAAI,GAAGf,KAAK,CAAEc,CAAC,CAAE;MACvB,MAAM5F,IAAI,GAAGmF,UAAU,CAAEU,IAAK,CAAC;MAC/B,MAAMG,YAAY,GAAGT,SAAS,CAAEvF,IAAI,CAAE;MACtCkF,SAAS,CAAC3I,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;MAExB,KAAM,IAAIe,CAAC,GAAG,CAAC,EAAE2I,EAAE,GAAGD,YAAY,CAAC9L,MAAM,EAAEoD,CAAC,GAAG2I,EAAE,EAAE3I,CAAC,EAAG,EAAG;QAEzD,MAAM4I,SAAS,GAAGF,YAAY,CAAE1I,CAAC,CAAE;QACnC,IAAK2H,QAAQ,CAACkB,GAAG,CAAED,SAAU,CAAC,GAAGvB,SAAS,EAAG;UAE5CO,SAAS,CAACzC,GAAG,CAAEyD,SAAU,CAAC;QAE3B;MAED;MAEAhB,SAAS,CAACS,SAAS,CAAC,CAAC;MACrBI,QAAQ,CAACK,MAAM,CAAEZ,EAAE,GAAGI,CAAC,EAAEV,SAAS,CAACtC,CAAC,EAAEsC,SAAS,CAACrC,CAAC,EAAEqC,SAAS,CAACpC,CAAE,CAAC;IAEjE;EAED;EAEAuC,cAAc,CAAClL,YAAY,CAAE,QAAQ,EAAE4L,QAAS,CAAC;EACjD,OAAOV,cAAc;AAEtB;AAEA,SAASgB,qBAAqBA,CAAE/L,UAAU,EAAEC,SAAS,GAAG,KAAK,EAAG;EAE/DY,OAAO,CAAC0F,IAAI,CAAE,2FAA4F,CAAC,CAAC,CAAC;EAC7G,OAAOxG,eAAe,CAAEC,UAAU,EAAEC,SAAU,CAAC;AAEhD;AAEA,SAAS+L,qBAAqBA,CAAExM,UAAU,EAAG;EAE5CqB,OAAO,CAAC0F,IAAI,CAAE,2FAA4F,CAAC,CAAC,CAAC;EAC7G,OAAO9E,eAAe,CAAEjC,UAAW,CAAC;AAErC;AAEA,SACC1B,yBAAyB,EACzBiC,eAAe,EACfgM,qBAAqB,EACrBtK,eAAe,EACfuK,qBAAqB,EACrBzJ,oBAAoB,EACpB0B,iBAAiB,EACjBM,aAAa,EACb8B,mBAAmB,EACnBM,wBAAwB,EACxBgD,WAAW,EACXO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}