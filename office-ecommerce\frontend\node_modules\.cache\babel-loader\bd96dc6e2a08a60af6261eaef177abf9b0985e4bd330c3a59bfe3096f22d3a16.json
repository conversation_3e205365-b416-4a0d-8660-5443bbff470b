{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { useTexture } from './useTexture.js';\nconst ImageMaterialImpl = shaderMaterial({\n  color: new THREE.Color('white'),\n  scale: [1, 1],\n  imageBounds: [1, 1],\n  map: null,\n  zoom: 1,\n  grayscale: 0,\n  opacity: 1\n}, /* glsl */\n`\n  varying vec2 vUv;\n  void main() {\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n    vUv = uv;\n  }\n`, /* glsl */\n`\n  // mostly from https://gist.github.com/statico/df64c5d167362ecf7b34fca0b1459a44\n  varying vec2 vUv;\n  uniform vec2 scale;\n  uniform vec2 imageBounds;\n  uniform vec3 color;\n  uniform sampler2D map;\n  uniform float zoom;\n  uniform float grayscale;\n  uniform float opacity;\n  const vec3 luma = vec3(.299, 0.587, 0.114);\n  vec4 toGrayscale(vec4 color, float intensity) {\n    return vec4(mix(color.rgb, vec3(dot(color.rgb, luma)), intensity), color.a);\n  }\n  vec2 aspect(vec2 size) {\n    return size / min(size.x, size.y);\n  }\n  void main() {\n    vec2 s = aspect(scale);\n    vec2 i = aspect(imageBounds);\n    float rs = s.x / s.y;\n    float ri = i.x / i.y;\n    vec2 new = rs < ri ? vec2(i.x * s.y / i.y, s.y) : vec2(s.x, i.y * s.x / i.x);\n    vec2 offset = (rs < ri ? vec2((new.x - s.x) / 2.0, 0.0) : vec2(0.0, (new.y - s.y) / 2.0)) / new;\n    vec2 uv = vUv * s / new + offset;\n    vec2 zUv = (uv - vec2(0.5, 0.5)) / zoom + vec2(0.5, 0.5);\n    gl_FragColor = toGrayscale(texture2D(map, zUv) * vec4(color, opacity), grayscale);\n    \n    #include <tonemapping_fragment>\n    #include <encodings_fragment>\n  }\n`);\nconst ImageBase = /*#__PURE__*/React.forwardRef(({\n  children,\n  color,\n  segments = 1,\n  scale = 1,\n  zoom = 1,\n  grayscale = 0,\n  opacity = 1,\n  texture,\n  toneMapped,\n  transparent,\n  ...props\n}, ref) => {\n  extend({\n    ImageMaterial: ImageMaterialImpl\n  });\n  const planeBounds = Array.isArray(scale) ? [scale[0], scale[1]] : [scale, scale];\n  const imageBounds = [texture.image.width, texture.image.height];\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    scale: Array.isArray(scale) ? [...scale, 1] : scale\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: [1, 1, segments, segments]\n  }), /*#__PURE__*/React.createElement(\"imageMaterial\", {\n    color: color,\n    map: texture,\n    zoom: zoom,\n    grayscale: grayscale,\n    opacity: opacity,\n    scale: planeBounds,\n    imageBounds: imageBounds,\n    toneMapped: toneMapped,\n    transparent: transparent\n  }), children);\n});\nconst ImageWithUrl = /*#__PURE__*/React.forwardRef(({\n  url,\n  ...props\n}, ref) => {\n  const texture = useTexture(url);\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    texture: texture,\n    ref: ref\n  }));\n});\nconst ImageWithTexture = /*#__PURE__*/React.forwardRef(({\n  url: _url,\n  ...props\n}, ref) => {\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    ref: ref\n  }));\n});\nconst Image = /*#__PURE__*/React.forwardRef((props, ref) => {\n  if (props.url) return /*#__PURE__*/React.createElement(ImageWithUrl, _extends({}, props, {\n    ref: ref\n  }));else if (props.texture) return /*#__PURE__*/React.createElement(ImageWithTexture, _extends({}, props, {\n    ref: ref\n  }));else throw new Error('<Image /> requires a url or texture');\n});\nexport { Image };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "extend", "shaderMaterial", "useTexture", "ImageMaterialImpl", "color", "Color", "scale", "imageBounds", "map", "zoom", "grayscale", "opacity", "ImageBase", "forwardRef", "children", "segments", "texture", "toneMapped", "transparent", "props", "ref", "ImageMaterial", "planeBounds", "Array", "isArray", "image", "width", "height", "createElement", "args", "ImageWithUrl", "url", "ImageWithTexture", "_url", "Image", "Error"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Image.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { useTexture } from './useTexture.js';\n\nconst ImageMaterialImpl = shaderMaterial({\n  color: new THREE.Color('white'),\n  scale: [1, 1],\n  imageBounds: [1, 1],\n  map: null,\n  zoom: 1,\n  grayscale: 0,\n  opacity: 1\n},\n/* glsl */\n`\n  varying vec2 vUv;\n  void main() {\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n    vUv = uv;\n  }\n`,\n/* glsl */\n`\n  // mostly from https://gist.github.com/statico/df64c5d167362ecf7b34fca0b1459a44\n  varying vec2 vUv;\n  uniform vec2 scale;\n  uniform vec2 imageBounds;\n  uniform vec3 color;\n  uniform sampler2D map;\n  uniform float zoom;\n  uniform float grayscale;\n  uniform float opacity;\n  const vec3 luma = vec3(.299, 0.587, 0.114);\n  vec4 toGrayscale(vec4 color, float intensity) {\n    return vec4(mix(color.rgb, vec3(dot(color.rgb, luma)), intensity), color.a);\n  }\n  vec2 aspect(vec2 size) {\n    return size / min(size.x, size.y);\n  }\n  void main() {\n    vec2 s = aspect(scale);\n    vec2 i = aspect(imageBounds);\n    float rs = s.x / s.y;\n    float ri = i.x / i.y;\n    vec2 new = rs < ri ? vec2(i.x * s.y / i.y, s.y) : vec2(s.x, i.y * s.x / i.x);\n    vec2 offset = (rs < ri ? vec2((new.x - s.x) / 2.0, 0.0) : vec2(0.0, (new.y - s.y) / 2.0)) / new;\n    vec2 uv = vUv * s / new + offset;\n    vec2 zUv = (uv - vec2(0.5, 0.5)) / zoom + vec2(0.5, 0.5);\n    gl_FragColor = toGrayscale(texture2D(map, zUv) * vec4(color, opacity), grayscale);\n    \n    #include <tonemapping_fragment>\n    #include <encodings_fragment>\n  }\n`);\nconst ImageBase = /*#__PURE__*/React.forwardRef(({\n  children,\n  color,\n  segments = 1,\n  scale = 1,\n  zoom = 1,\n  grayscale = 0,\n  opacity = 1,\n  texture,\n  toneMapped,\n  transparent,\n  ...props\n}, ref) => {\n  extend({\n    ImageMaterial: ImageMaterialImpl\n  });\n  const planeBounds = Array.isArray(scale) ? [scale[0], scale[1]] : [scale, scale];\n  const imageBounds = [texture.image.width, texture.image.height];\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    scale: Array.isArray(scale) ? [...scale, 1] : scale\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: [1, 1, segments, segments]\n  }), /*#__PURE__*/React.createElement(\"imageMaterial\", {\n    color: color,\n    map: texture,\n    zoom: zoom,\n    grayscale: grayscale,\n    opacity: opacity,\n    scale: planeBounds,\n    imageBounds: imageBounds,\n    toneMapped: toneMapped,\n    transparent: transparent\n  }), children);\n});\nconst ImageWithUrl = /*#__PURE__*/React.forwardRef(({\n  url,\n  ...props\n}, ref) => {\n  const texture = useTexture(url);\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    texture: texture,\n    ref: ref\n  }));\n});\nconst ImageWithTexture = /*#__PURE__*/React.forwardRef(({\n  url: _url,\n  ...props\n}, ref) => {\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    ref: ref\n  }));\n});\nconst Image = /*#__PURE__*/React.forwardRef((props, ref) => {\n  if (props.url) return /*#__PURE__*/React.createElement(ImageWithUrl, _extends({}, props, {\n    ref: ref\n  }));else if (props.texture) return /*#__PURE__*/React.createElement(ImageWithTexture, _extends({}, props, {\n    ref: ref\n  }));else throw new Error('<Image /> requires a url or texture');\n});\n\nexport { Image };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,UAAU,QAAQ,iBAAiB;AAE5C,MAAMC,iBAAiB,GAAGF,cAAc,CAAC;EACvCG,KAAK,EAAE,IAAIL,KAAK,CAACM,KAAK,CAAC,OAAO,CAAC;EAC/BC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACbC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACnBC,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,CAAC;EACPC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE;AACX,CAAC,EACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AACF,MAAMC,SAAS,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,CAAC;EAC/CC,QAAQ;EACRV,KAAK;EACLW,QAAQ,GAAG,CAAC;EACZT,KAAK,GAAG,CAAC;EACTG,IAAI,GAAG,CAAC;EACRC,SAAS,GAAG,CAAC;EACbC,OAAO,GAAG,CAAC;EACXK,OAAO;EACPC,UAAU;EACVC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTpB,MAAM,CAAC;IACLqB,aAAa,EAAElB;EACjB,CAAC,CAAC;EACF,MAAMmB,WAAW,GAAGC,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,KAAK,EAAEA,KAAK,CAAC;EAChF,MAAMC,WAAW,GAAG,CAACS,OAAO,CAACS,KAAK,CAACC,KAAK,EAAEV,OAAO,CAACS,KAAK,CAACE,MAAM,CAAC;EAC/D,OAAO,aAAa7B,KAAK,CAAC8B,aAAa,CAAC,MAAM,EAAE/B,QAAQ,CAAC;IACvDuB,GAAG,EAAEA,GAAG;IACRd,KAAK,EAAEiB,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,EAAE,CAAC,CAAC,GAAGA;EAChD,CAAC,EAAEa,KAAK,CAAC,EAAE,aAAarB,KAAK,CAAC8B,aAAa,CAAC,eAAe,EAAE;IAC3DC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEd,QAAQ,EAAEA,QAAQ;EACjC,CAAC,CAAC,EAAE,aAAajB,KAAK,CAAC8B,aAAa,CAAC,eAAe,EAAE;IACpDxB,KAAK,EAAEA,KAAK;IACZI,GAAG,EAAEQ,OAAO;IACZP,IAAI,EAAEA,IAAI;IACVC,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA,OAAO;IAChBL,KAAK,EAAEgB,WAAW;IAClBf,WAAW,EAAEA,WAAW;IACxBU,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA;EACf,CAAC,CAAC,EAAEJ,QAAQ,CAAC;AACf,CAAC,CAAC;AACF,MAAMgB,YAAY,GAAG,aAAahC,KAAK,CAACe,UAAU,CAAC,CAAC;EAClDkB,GAAG;EACH,GAAGZ;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMJ,OAAO,GAAGd,UAAU,CAAC6B,GAAG,CAAC;EAC/B,OAAO,aAAajC,KAAK,CAAC8B,aAAa,CAAChB,SAAS,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrEH,OAAO,EAAEA,OAAO;IAChBI,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMY,gBAAgB,GAAG,aAAalC,KAAK,CAACe,UAAU,CAAC,CAAC;EACtDkB,GAAG,EAAEE,IAAI;EACT,GAAGd;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,OAAO,aAAatB,KAAK,CAAC8B,aAAa,CAAChB,SAAS,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrEC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMc,KAAK,GAAG,aAAapC,KAAK,CAACe,UAAU,CAAC,CAACM,KAAK,EAAEC,GAAG,KAAK;EAC1D,IAAID,KAAK,CAACY,GAAG,EAAE,OAAO,aAAajC,KAAK,CAAC8B,aAAa,CAACE,YAAY,EAAEjC,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACvFC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC,KAAK,IAAID,KAAK,CAACH,OAAO,EAAE,OAAO,aAAalB,KAAK,CAAC8B,aAAa,CAACI,gBAAgB,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACxGC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAAIe,KAAK,CAAC,qCAAqC,CAAC;AACjE,CAAC,CAAC;AAEF,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}