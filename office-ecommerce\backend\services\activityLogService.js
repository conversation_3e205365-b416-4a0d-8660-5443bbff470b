const { getPool } = require('../config/database');
const logger = require('../utils/logger');

class ActivityLogService {
  /**
   * Log an activity to the database
   * @param {Object} logData - Activity log data
   * @param {string} logData.userID - User ID (optional)
   * @param {string} logData.userEmail - User email (optional)
   * @param {string} logData.userName - User name (optional)
   * @param {string} logData.userRole - User role (optional)
   * @param {string} logData.action - Action performed (CREATE, UPDATE, DELETE, etc.)
   * @param {string} logData.entityType - Type of entity (User, Product, Order, etc.)
   * @param {string} logData.entityID - Entity ID (optional)
   * @param {string} logData.entityName - Entity name (optional)
   * @param {string} logData.description - Description of the activity
   * @param {string} logData.ipAddress - IP address (optional)
   * @param {string} logData.userAgent - User agent (optional)
   * @param {string} logData.requestMethod - HTTP method (optional)
   * @param {string} logData.requestPath - Request path (optional)
   * @param {number} logData.statusCode - HTTP status code (optional)
   * @param {number} logData.duration - Request duration in ms (optional)
   * @param {Object} logData.oldValues - Old values for updates (optional)
   * @param {Object} logData.newValues - New values for updates (optional)
   * @param {Object} logData.metadata - Additional metadata (optional)
   * @param {string} logData.severity - Log severity (INFO, WARNING, ERROR, CRITICAL)
   */
  static async logActivity(logData) {
    try {
      const pool = getPool();
      const request = pool.request();

      const query = `
        INSERT INTO ActivityLogs (
          UserID, UserEmail, UserName, UserRole, Action, EntityType, EntityID, EntityName,
          Description, IPAddress, UserAgent, RequestMethod, RequestPath, StatusCode,
          Duration, OldValues, NewValues, Metadata, Severity
        ) VALUES (
          @userID, @userEmail, @userName, @userRole, @action, @entityType, @entityID, @entityName,
          @description, @ipAddress, @userAgent, @requestMethod, @requestPath, @statusCode,
          @duration, @oldValues, @newValues, @metadata, @severity
        )
      `;

      request.input('userID', logData.userID || null);
      request.input('userEmail', logData.userEmail || null);
      request.input('userName', logData.userName || null);
      request.input('userRole', logData.userRole || null);
      request.input('action', logData.action);
      request.input('entityType', logData.entityType);
      request.input('entityID', logData.entityID || null);
      request.input('entityName', logData.entityName || null);
      request.input('description', logData.description);
      request.input('ipAddress', logData.ipAddress || null);
      request.input('userAgent', logData.userAgent || null);
      request.input('requestMethod', logData.requestMethod || null);
      request.input('requestPath', logData.requestPath || null);
      request.input('statusCode', logData.statusCode || null);
      request.input('duration', logData.duration || null);
      request.input('oldValues', logData.oldValues ? JSON.stringify(logData.oldValues) : null);
      request.input('newValues', logData.newValues ? JSON.stringify(logData.newValues) : null);
      request.input('metadata', logData.metadata ? JSON.stringify(logData.metadata) : null);
      request.input('severity', logData.severity || 'INFO');

      await request.query(query);
      
      logger.debug('Activity logged successfully', { action: logData.action, entityType: logData.entityType });
    } catch (error) {
      logger.error('Failed to log activity:', error);
      // Don't throw error to avoid breaking the main functionality
    }
  }

  /**
   * Get activity logs with filtering and pagination
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (default: 1)
   * @param {number} options.limit - Items per page (default: 50)
   * @param {string} options.userID - Filter by user ID
   * @param {string} options.action - Filter by action
   * @param {string} options.entityType - Filter by entity type
   * @param {string} options.severity - Filter by severity
   * @param {Date} options.startDate - Filter from date
   * @param {Date} options.endDate - Filter to date
   * @param {string} options.search - Search in description, user name, or entity name
   */
  static async getActivityLogs(options = {}) {
    try {
      const pool = getPool();
      const request = pool.request();

      const {
        page = 1,
        limit = 50,
        userID,
        action,
        entityType,
        severity,
        startDate,
        endDate,
        search
      } = options;

      let whereConditions = [];
      let queryParams = {};

      // Build WHERE conditions
      if (userID) {
        whereConditions.push('UserID = @userID');
        queryParams.userID = userID;
      }

      if (action) {
        whereConditions.push('Action = @action');
        queryParams.action = action;
      }

      if (entityType) {
        whereConditions.push('EntityType = @entityType');
        queryParams.entityType = entityType;
      }

      if (severity) {
        whereConditions.push('Severity = @severity');
        queryParams.severity = severity;
      }

      if (startDate) {
        whereConditions.push('CreatedAt >= @startDate');
        queryParams.startDate = startDate;
      }

      if (endDate) {
        whereConditions.push('CreatedAt <= @endDate');
        queryParams.endDate = endDate;
      }

      if (search) {
        whereConditions.push(`(
          Description LIKE @search OR 
          UserName LIKE @search OR 
          EntityName LIKE @search OR
          UserEmail LIKE @search
        )`);
        queryParams.search = `%${search}%`;
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // Add parameters to request
      Object.keys(queryParams).forEach(key => {
        request.input(key, queryParams[key]);
      });

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM ActivityLogs
        ${whereClause}
      `;

      const countResult = await request.query(countQuery);
      const totalItems = countResult.recordset[0].total;

      // Get paginated results
      const offset = (page - 1) * limit;
      request.input('offset', offset);
      request.input('limit', limit);

      const dataQuery = `
        SELECT 
          LogID, UserID, UserEmail, UserName, UserRole, Action, EntityType, EntityID, EntityName,
          Description, IPAddress, UserAgent, RequestMethod, RequestPath, StatusCode,
          Duration, OldValues, NewValues, Metadata, Severity, CreatedAt
        FROM ActivityLogs
        ${whereClause}
        ORDER BY CreatedAt DESC
        OFFSET @offset ROWS
        FETCH NEXT @limit ROWS ONLY
      `;

      const dataResult = await request.query(dataQuery);

      // Parse JSON fields
      const logs = dataResult.recordset.map(log => ({
        ...log,
        oldValues: log.OldValues ? JSON.parse(log.OldValues) : null,
        newValues: log.NewValues ? JSON.parse(log.NewValues) : null,
        metadata: log.Metadata ? JSON.parse(log.Metadata) : null
      }));

      return {
        logs,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalItems / limit),
          totalItems,
          itemsPerPage: parseInt(limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get activity logs:', error);
      throw error;
    }
  }

  /**
   * Get activity statistics for dashboard
   */
  static async getActivityStats() {
    try {
      const pool = getPool();
      const request = pool.request();

      const query = `
        SELECT 
          COUNT(*) as totalActivities,
          COUNT(CASE WHEN CreatedAt >= DATEADD(day, -1, GETUTCDATE()) THEN 1 END) as last24Hours,
          COUNT(CASE WHEN CreatedAt >= DATEADD(day, -7, GETUTCDATE()) THEN 1 END) as last7Days,
          COUNT(CASE WHEN CreatedAt >= DATEADD(day, -30, GETUTCDATE()) THEN 1 END) as last30Days,
          COUNT(CASE WHEN Severity = 'ERROR' OR Severity = 'CRITICAL' THEN 1 END) as errorCount
        FROM ActivityLogs;

        SELECT Action, COUNT(*) as count
        FROM ActivityLogs
        WHERE CreatedAt >= DATEADD(day, -30, GETUTCDATE())
        GROUP BY Action
        ORDER BY count DESC;

        SELECT EntityType, COUNT(*) as count
        FROM ActivityLogs
        WHERE CreatedAt >= DATEADD(day, -30, GETUTCDATE())
        GROUP BY EntityType
        ORDER BY count DESC;

        SELECT 
          CAST(CreatedAt as DATE) as date,
          COUNT(*) as count
        FROM ActivityLogs
        WHERE CreatedAt >= DATEADD(day, -30, GETUTCDATE())
        GROUP BY CAST(CreatedAt as DATE)
        ORDER BY date DESC;
      `;

      const result = await request.query(query);

      return {
        overview: result.recordsets[0][0],
        actionStats: result.recordsets[1],
        entityStats: result.recordsets[2],
        dailyStats: result.recordsets[3]
      };
    } catch (error) {
      logger.error('Failed to get activity stats:', error);
      throw error;
    }
  }

  /**
   * Helper method to create activity log from Express request
   */
  static createLogFromRequest(req, action, entityType, entityData = {}) {
    const user = req.user || {};
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;

    return {
      userID: user.id,
      userEmail: user.email,
      userName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.email,
      userRole: user.role,
      action,
      entityType,
      entityID: entityData.id,
      entityName: entityData.name,
      description: entityData.description || `${action} ${entityType}`,
      ipAddress: clientIP,
      userAgent: req.get('User-Agent'),
      requestMethod: req.method,
      requestPath: req.originalUrl,
      metadata: {
        body: req.method !== 'GET' ? req.body : undefined,
        query: req.query,
        params: req.params
      }
    };
  }
}

module.exports = ActivityLogService;
