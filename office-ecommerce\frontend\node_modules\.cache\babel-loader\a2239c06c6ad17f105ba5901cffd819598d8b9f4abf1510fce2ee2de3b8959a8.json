{"ast": null, "code": "import { c as createPointerEvents, e as extend, u as useMutableCallback, a as useIsomorphicLayoutEffect, b as createRoot, i as isRef, E as ErrorBoundary, B as Block, d as unmountComponentAtNode } from './events-776716bd.esm.js';\nexport { t as ReactThreeFiber, z as _roots, x as act, p as addAfterEffect, o as addEffect, q as addTail, n as advance, k as applyProps, y as buildGraph, g as context, f as createEvents, c as createPointerEvents, h as createPortal, b as createRoot, l as dispose, c as events, e as extend, s as flushGlobalEffects, v as flushSync, w as getRootState, m as invalidate, j as reconciler, r as render, d as unmountComponentAtNode, F as useFrame, G as useGraph, A as useInstanceHandle, H as useLoader, C as useStore, D as useThree } from './events-776716bd.esm.js';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport useMeasure from 'react-use-measure';\nimport { FiberProvider, useContextBridge } from 'its-fine';\nimport { jsx } from 'react/jsx-runtime';\nimport 'react-reconciler/constants';\nimport 'zustand';\nimport 'suspend-react';\nimport 'react-reconciler';\nimport 'scheduler';\nconst CanvasImpl = /*#__PURE__*/React.forwardRef(function Canvas({\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = createPointerEvents,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}, forwardedRef) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  React.useMemo(() => extend(THREE), []);\n  const Bridge = useContextBridge();\n  const [containerRef, containerRect] = useMeasure({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = React.useRef(null);\n  const divRef = React.useRef(null);\n  React.useImperativeHandle(forwardedRef, () => canvasRef.current);\n  const handlePointerMissed = useMutableCallback(onPointerMissed);\n  const [block, setBlock] = React.useState(false);\n  const [error, setError] = React.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = React.useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = createRoot(canvas);\n      root.current.configure({\n        gl,\n        events,\n        shadows,\n        linear,\n        flat,\n        legacy,\n        orthographic,\n        frameloop,\n        dpr,\n        performance,\n        raycaster,\n        camera,\n        scene,\n        size: containerRect,\n        // Pass mutable reference to onPointerMissed so it's free to update\n        onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n        onCreated: state => {\n          // Connect to event source\n          state.events.connect == null ? void 0 : state.events.connect(eventSource ? isRef(eventSource) ? eventSource.current : eventSource : divRef.current);\n          // Set up compute function\n          if (eventPrefix) {\n            state.setEvents({\n              compute: (event, state) => {\n                const x = event[eventPrefix + 'X'];\n                const y = event[eventPrefix + 'Y'];\n                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                state.raycaster.setFromCamera(state.pointer, state.camera);\n              }\n            });\n          }\n          // Call onCreated callback\n          onCreated == null ? void 0 : onCreated(state);\n        }\n      });\n      root.current.render(/*#__PURE__*/jsx(Bridge, {\n        children: /*#__PURE__*/jsx(ErrorBoundary, {\n          set: setError,\n          children: /*#__PURE__*/jsx(React.Suspense, {\n            fallback: /*#__PURE__*/jsx(Block, {\n              set: setBlock\n            }),\n            children: children != null ? children : null\n          })\n        })\n      }));\n    }\n  });\n  React.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => unmountComponentAtNode(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/jsx(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/jsx(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n});\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nconst Canvas = /*#__PURE__*/React.forwardRef(function CanvasWrapper(props, ref) {\n  return /*#__PURE__*/jsx(FiberProvider, {\n    children: /*#__PURE__*/jsx(CanvasImpl, {\n      ...props,\n      ref: ref\n    })\n  });\n});\nexport { Canvas };", "map": {"version": 3, "names": ["c", "createPointerEvents", "e", "extend", "u", "useMutableCallback", "a", "useIsomorphicLayoutEffect", "b", "createRoot", "i", "isRef", "E", "Error<PERSON>ou<PERSON><PERSON>", "B", "Block", "d", "unmountComponentAtNode", "t", "ReactThreeFiber", "z", "_roots", "x", "act", "p", "addAfterEffect", "o", "addEffect", "q", "addTail", "n", "advance", "k", "applyProps", "y", "buildGraph", "g", "context", "f", "createEvents", "h", "createPortal", "l", "dispose", "events", "s", "flushGlobalEffects", "v", "flushSync", "w", "getRootState", "m", "invalidate", "j", "reconciler", "r", "render", "F", "useFrame", "G", "useGraph", "A", "useInstanceHandle", "H", "useLoader", "C", "useStore", "D", "useThree", "React", "THREE", "useMeasure", "FiberProvider", "useContextBridge", "jsx", "CanvasImpl", "forwardRef", "<PERSON><PERSON>", "children", "fallback", "resize", "style", "gl", "eventSource", "eventPrefix", "shadows", "linear", "flat", "legacy", "orthographic", "frameloop", "dpr", "performance", "raycaster", "camera", "scene", "onPointerMissed", "onCreated", "props", "forwardedRef", "useMemo", "Bridge", "containerRef", "containerRect", "scroll", "debounce", "canvasRef", "useRef", "divRef", "useImperativeHandle", "current", "handlePointerMissed", "block", "setBlock", "useState", "error", "setError", "root", "canvas", "width", "height", "configure", "size", "args", "state", "connect", "setEvents", "compute", "event", "pointer", "set", "setFromCamera", "Suspense", "useEffect", "pointerEvents", "ref", "position", "overflow", "display", "CanvasWrapper"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js"], "sourcesContent": ["import { c as createPointerEvents, e as extend, u as useMutableCallback, a as useIsomorphicLayoutEffect, b as createRoot, i as isRef, E as ErrorBoundary, B as Block, d as unmountComponentAtNode } from './events-776716bd.esm.js';\nexport { t as ReactThreeFiber, z as _roots, x as act, p as addAfterEffect, o as addEffect, q as addTail, n as advance, k as applyProps, y as buildGraph, g as context, f as createEvents, c as createPointerEvents, h as createPortal, b as createRoot, l as dispose, c as events, e as extend, s as flushGlobalEffects, v as flushSync, w as getRootState, m as invalidate, j as reconciler, r as render, d as unmountComponentAtNode, F as useFrame, G as useGraph, A as useInstanceHandle, H as useLoader, C as useStore, D as useThree } from './events-776716bd.esm.js';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport useMeasure from 'react-use-measure';\nimport { FiberProvider, useContextBridge } from 'its-fine';\nimport { jsx } from 'react/jsx-runtime';\nimport 'react-reconciler/constants';\nimport 'zustand';\nimport 'suspend-react';\nimport 'react-reconciler';\nimport 'scheduler';\n\nconst CanvasImpl = /*#__PURE__*/React.forwardRef(function Canvas({\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = createPointerEvents,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}, forwardedRef) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  React.useMemo(() => extend(THREE), []);\n  const Bridge = useContextBridge();\n  const [containerRef, containerRect] = useMeasure({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = React.useRef(null);\n  const divRef = React.useRef(null);\n  React.useImperativeHandle(forwardedRef, () => canvasRef.current);\n  const handlePointerMissed = useMutableCallback(onPointerMissed);\n  const [block, setBlock] = React.useState(false);\n  const [error, setError] = React.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = React.useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = createRoot(canvas);\n      root.current.configure({\n        gl,\n        events,\n        shadows,\n        linear,\n        flat,\n        legacy,\n        orthographic,\n        frameloop,\n        dpr,\n        performance,\n        raycaster,\n        camera,\n        scene,\n        size: containerRect,\n        // Pass mutable reference to onPointerMissed so it's free to update\n        onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n        onCreated: state => {\n          // Connect to event source\n          state.events.connect == null ? void 0 : state.events.connect(eventSource ? isRef(eventSource) ? eventSource.current : eventSource : divRef.current);\n          // Set up compute function\n          if (eventPrefix) {\n            state.setEvents({\n              compute: (event, state) => {\n                const x = event[eventPrefix + 'X'];\n                const y = event[eventPrefix + 'Y'];\n                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                state.raycaster.setFromCamera(state.pointer, state.camera);\n              }\n            });\n          }\n          // Call onCreated callback\n          onCreated == null ? void 0 : onCreated(state);\n        }\n      });\n      root.current.render( /*#__PURE__*/jsx(Bridge, {\n        children: /*#__PURE__*/jsx(ErrorBoundary, {\n          set: setError,\n          children: /*#__PURE__*/jsx(React.Suspense, {\n            fallback: /*#__PURE__*/jsx(Block, {\n              set: setBlock\n            }),\n            children: children != null ? children : null\n          })\n        })\n      }));\n    }\n  });\n  React.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => unmountComponentAtNode(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/jsx(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/jsx(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n});\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nconst Canvas = /*#__PURE__*/React.forwardRef(function CanvasWrapper(props, ref) {\n  return /*#__PURE__*/jsx(FiberProvider, {\n    children: /*#__PURE__*/jsx(CanvasImpl, {\n      ...props,\n      ref: ref\n    })\n  });\n});\n\nexport { Canvas };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,0BAA0B;AACnO,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,YAAY,EAAEvC,CAAC,IAAIC,mBAAmB,EAAEuC,CAAC,IAAIC,YAAY,EAAEjC,CAAC,IAAIC,UAAU,EAAEiC,CAAC,IAAIC,OAAO,EAAE3C,CAAC,IAAI4C,MAAM,EAAE1C,CAAC,IAAIC,MAAM,EAAE0C,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,MAAM,EAAExC,CAAC,IAAIC,sBAAsB,EAAEwC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,0BAA0B;AAC5iB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,UAAU;AAC1D,SAASC,GAAG,QAAQ,mBAAmB;AACvC,OAAO,4BAA4B;AACnC,OAAO,SAAS;AAChB,OAAO,eAAe;AACtB,OAAO,kBAAkB;AACzB,OAAO,WAAW;AAElB,MAAMC,UAAU,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,SAASC,MAAMA,CAAC;EAC/DC,QAAQ;EACRC,QAAQ;EACRC,MAAM;EACNC,KAAK;EACLC,EAAE;EACFtC,MAAM,GAAG3C,mBAAmB;EAC5BkF,WAAW;EACXC,WAAW;EACXC,OAAO;EACPC,MAAM;EACNC,IAAI;EACJC,MAAM;EACNC,YAAY;EACZC,SAAS;EACTC,GAAG;EACHC,WAAW;EACXC,SAAS;EACTC,MAAM;EACNC,KAAK;EACLC,eAAe;EACfC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,YAAY,EAAE;EACf;EACA;EACA;EACA9B,KAAK,CAAC+B,OAAO,CAAC,MAAMjG,MAAM,CAACmE,KAAK,CAAC,EAAE,EAAE,CAAC;EACtC,MAAM+B,MAAM,GAAG5B,gBAAgB,CAAC,CAAC;EACjC,MAAM,CAAC6B,YAAY,EAAEC,aAAa,CAAC,GAAGhC,UAAU,CAAC;IAC/CiC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;MACRD,MAAM,EAAE,EAAE;MACVxB,MAAM,EAAE;IACV,CAAC;IACD,GAAGA;EACL,CAAC,CAAC;EACF,MAAM0B,SAAS,GAAGrC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,MAAM,GAAGvC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EACjCtC,KAAK,CAACwC,mBAAmB,CAACV,YAAY,EAAE,MAAMO,SAAS,CAACI,OAAO,CAAC;EAChE,MAAMC,mBAAmB,GAAG1G,kBAAkB,CAAC2F,eAAe,CAAC;EAC/D,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,KAAK,CAAC6C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,KAAK,CAAC6C,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,IAAIF,KAAK,EAAE,MAAMA,KAAK;EACtB;EACA,IAAIG,KAAK,EAAE,MAAMA,KAAK;EACtB,MAAME,IAAI,GAAGhD,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EAC/BpG,yBAAyB,CAAC,MAAM;IAC9B,MAAM+G,MAAM,GAAGZ,SAAS,CAACI,OAAO;IAChC,IAAIP,aAAa,CAACgB,KAAK,GAAG,CAAC,IAAIhB,aAAa,CAACiB,MAAM,GAAG,CAAC,IAAIF,MAAM,EAAE;MACjE,IAAI,CAACD,IAAI,CAACP,OAAO,EAAEO,IAAI,CAACP,OAAO,GAAGrG,UAAU,CAAC6G,MAAM,CAAC;MACpDD,IAAI,CAACP,OAAO,CAACW,SAAS,CAAC;QACrBvC,EAAE;QACFtC,MAAM;QACNyC,OAAO;QACPC,MAAM;QACNC,IAAI;QACJC,MAAM;QACNC,YAAY;QACZC,SAAS;QACTC,GAAG;QACHC,WAAW;QACXC,SAAS;QACTC,MAAM;QACNC,KAAK;QACL2B,IAAI,EAAEnB,aAAa;QACnB;QACAP,eAAe,EAAEA,CAAC,GAAG2B,IAAI,KAAKZ,mBAAmB,CAACD,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGC,mBAAmB,CAACD,OAAO,CAAC,GAAGa,IAAI,CAAC;QACjH1B,SAAS,EAAE2B,KAAK,IAAI;UAClB;UACAA,KAAK,CAAChF,MAAM,CAACiF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGD,KAAK,CAAChF,MAAM,CAACiF,OAAO,CAAC1C,WAAW,GAAGxE,KAAK,CAACwE,WAAW,CAAC,GAAGA,WAAW,CAAC2B,OAAO,GAAG3B,WAAW,GAAGyB,MAAM,CAACE,OAAO,CAAC;UACnJ;UACA,IAAI1B,WAAW,EAAE;YACfwC,KAAK,CAACE,SAAS,CAAC;cACdC,OAAO,EAAEA,CAACC,KAAK,EAAEJ,KAAK,KAAK;gBACzB,MAAMtG,CAAC,GAAG0G,KAAK,CAAC5C,WAAW,GAAG,GAAG,CAAC;gBAClC,MAAMlD,CAAC,GAAG8F,KAAK,CAAC5C,WAAW,GAAG,GAAG,CAAC;gBAClCwC,KAAK,CAACK,OAAO,CAACC,GAAG,CAAC5G,CAAC,GAAGsG,KAAK,CAACF,IAAI,CAACH,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAErF,CAAC,GAAG0F,KAAK,CAACF,IAAI,CAACF,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACjFI,KAAK,CAAC/B,SAAS,CAACsC,aAAa,CAACP,KAAK,CAACK,OAAO,EAAEL,KAAK,CAAC9B,MAAM,CAAC;cAC5D;YACF,CAAC,CAAC;UACJ;UACA;UACAG,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC2B,KAAK,CAAC;QAC/C;MACF,CAAC,CAAC;MACFP,IAAI,CAACP,OAAO,CAACtD,MAAM,CAAE,aAAakB,GAAG,CAAC2B,MAAM,EAAE;QAC5CvB,QAAQ,EAAE,aAAaJ,GAAG,CAAC7D,aAAa,EAAE;UACxCqH,GAAG,EAAEd,QAAQ;UACbtC,QAAQ,EAAE,aAAaJ,GAAG,CAACL,KAAK,CAAC+D,QAAQ,EAAE;YACzCrD,QAAQ,EAAE,aAAaL,GAAG,CAAC3D,KAAK,EAAE;cAChCmH,GAAG,EAAEjB;YACP,CAAC,CAAC;YACFnC,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG;UAC1C,CAAC;QACH,CAAC;MACH,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;EACFT,KAAK,CAACgE,SAAS,CAAC,MAAM;IACpB,MAAMf,MAAM,GAAGZ,SAAS,CAACI,OAAO;IAChC,IAAIQ,MAAM,EAAE,OAAO,MAAMrG,sBAAsB,CAACqG,MAAM,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA,MAAMgB,aAAa,GAAGnD,WAAW,GAAG,MAAM,GAAG,MAAM;EACnD,OAAO,aAAaT,GAAG,CAAC,KAAK,EAAE;IAC7B6D,GAAG,EAAE3B,MAAM;IACX3B,KAAK,EAAE;MACLuD,QAAQ,EAAE,UAAU;MACpBjB,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdiB,QAAQ,EAAE,QAAQ;MAClBH,aAAa;MACb,GAAGrD;IACL,CAAC;IACD,GAAGiB,KAAK;IACRpB,QAAQ,EAAE,aAAaJ,GAAG,CAAC,KAAK,EAAE;MAChC6D,GAAG,EAAEjC,YAAY;MACjBrB,KAAK,EAAE;QACLsC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACV,CAAC;MACD1C,QAAQ,EAAE,aAAaJ,GAAG,CAAC,QAAQ,EAAE;QACnC6D,GAAG,EAAE7B,SAAS;QACdzB,KAAK,EAAE;UACLyD,OAAO,EAAE;QACX,CAAC;QACD5D,QAAQ,EAAEC;MACZ,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMF,MAAM,GAAG,aAAaR,KAAK,CAACO,UAAU,CAAC,SAAS+D,aAAaA,CAACzC,KAAK,EAAEqC,GAAG,EAAE;EAC9E,OAAO,aAAa7D,GAAG,CAACF,aAAa,EAAE;IACrCM,QAAQ,EAAE,aAAaJ,GAAG,CAACC,UAAU,EAAE;MACrC,GAAGuB,KAAK;MACRqC,GAAG,EAAEA;IACP,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,SAAS1D,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}