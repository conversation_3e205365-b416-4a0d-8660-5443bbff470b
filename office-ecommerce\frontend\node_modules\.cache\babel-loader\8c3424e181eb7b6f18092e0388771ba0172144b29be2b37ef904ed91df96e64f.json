{"ast": null, "code": "import * as React from 'react';\nimport { Object3D, AnimationMixer } from 'three';\nimport { useFrame } from '@react-three/fiber';\nfunction useAnimations(clips, root) {\n  const ref = React.useRef();\n  const [actualRef] = React.useState(() => root ? root instanceof Object3D ? {\n    current: root\n  } : root : ref); // eslint-disable-next-line prettier/prettier\n\n  const [mixer] = React.useState(() => new AnimationMixer(undefined));\n  React.useLayoutEffect(() => void (mixer._root = actualRef.current), [mixer, root]);\n  const lazyActions = React.useRef({});\n  const [api] = React.useState(() => {\n    const actions = {};\n    clips.forEach(clip => Object.defineProperty(actions, clip.name, {\n      enumerable: true,\n      get() {\n        if (actualRef.current) {\n          return lazyActions.current[clip.name] || (lazyActions.current[clip.name] = mixer.clipAction(clip, actualRef.current));\n        }\n      },\n      configurable: true\n    }));\n    return {\n      ref: actualRef,\n      clips,\n      actions,\n      names: clips.map(c => c.name),\n      mixer\n    };\n  });\n  useFrame((state, delta) => mixer.update(delta));\n  React.useEffect(() => {\n    const currentRoot = actualRef.current;\n    return () => {\n      // Clean up only when clips change, wipe out lazy actions and uncache clips\n      lazyActions.current = {};\n      Object.values(api.actions).forEach(action => {\n        if (currentRoot) {\n          mixer.uncacheAction(action, currentRoot);\n        }\n      });\n    };\n  }, [clips]);\n  React.useEffect(() => {\n    return () => {\n      mixer.stopAllAction();\n    };\n  }, [mixer]);\n  return api;\n}\nexport { useAnimations };", "map": {"version": 3, "names": ["React", "Object3D", "AnimationMixer", "useFrame", "useAnimations", "clips", "root", "ref", "useRef", "actualRef", "useState", "current", "mixer", "undefined", "useLayoutEffect", "_root", "lazyActions", "api", "actions", "for<PERSON>ach", "clip", "Object", "defineProperty", "name", "enumerable", "get", "clipAction", "configurable", "names", "map", "c", "state", "delta", "update", "useEffect", "currentRoot", "values", "action", "uncacheAction", "stopAllAction"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useAnimations.js"], "sourcesContent": ["import * as React from 'react';\nimport { Object3D, AnimationMixer } from 'three';\nimport { useFrame } from '@react-three/fiber';\n\nfunction useAnimations(clips, root) {\n  const ref = React.useRef();\n  const [actualRef] = React.useState(() => root ? root instanceof Object3D ? {\n    current: root\n  } : root : ref); // eslint-disable-next-line prettier/prettier\n\n  const [mixer] = React.useState(() => new AnimationMixer(undefined));\n  React.useLayoutEffect(() => void (mixer._root = actualRef.current), [mixer, root]);\n  const lazyActions = React.useRef({});\n  const [api] = React.useState(() => {\n    const actions = {};\n    clips.forEach(clip => Object.defineProperty(actions, clip.name, {\n      enumerable: true,\n\n      get() {\n        if (actualRef.current) {\n          return lazyActions.current[clip.name] || (lazyActions.current[clip.name] = mixer.clipAction(clip, actualRef.current));\n        }\n      },\n\n      configurable: true\n    }));\n    return {\n      ref: actualRef,\n      clips,\n      actions,\n      names: clips.map(c => c.name),\n      mixer\n    };\n  });\n  useFrame((state, delta) => mixer.update(delta));\n  React.useEffect(() => {\n    const currentRoot = actualRef.current;\n    return () => {\n      // Clean up only when clips change, wipe out lazy actions and uncache clips\n      lazyActions.current = {};\n      Object.values(api.actions).forEach(action => {\n        if (currentRoot) {\n          mixer.uncacheAction(action, currentRoot);\n        }\n      });\n    };\n  }, [clips]);\n  React.useEffect(() => {\n    return () => {\n      mixer.stopAllAction();\n    };\n  }, [mixer]);\n  return api;\n}\n\nexport { useAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAChD,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAClC,MAAMC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC;EAC1B,MAAM,CAACC,SAAS,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,MAAMJ,IAAI,GAAGA,IAAI,YAAYL,QAAQ,GAAG;IACzEU,OAAO,EAAEL;EACX,CAAC,GAAGA,IAAI,GAAGC,GAAG,CAAC,CAAC,CAAC;;EAEjB,MAAM,CAACK,KAAK,CAAC,GAAGZ,KAAK,CAACU,QAAQ,CAAC,MAAM,IAAIR,cAAc,CAACW,SAAS,CAAC,CAAC;EACnEb,KAAK,CAACc,eAAe,CAAC,MAAM,MAAMF,KAAK,CAACG,KAAK,GAAGN,SAAS,CAACE,OAAO,CAAC,EAAE,CAACC,KAAK,EAAEN,IAAI,CAAC,CAAC;EAClF,MAAMU,WAAW,GAAGhB,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM,CAACS,GAAG,CAAC,GAAGjB,KAAK,CAACU,QAAQ,CAAC,MAAM;IACjC,MAAMQ,OAAO,GAAG,CAAC,CAAC;IAClBb,KAAK,CAACc,OAAO,CAACC,IAAI,IAAIC,MAAM,CAACC,cAAc,CAACJ,OAAO,EAAEE,IAAI,CAACG,IAAI,EAAE;MAC9DC,UAAU,EAAE,IAAI;MAEhBC,GAAGA,CAAA,EAAG;QACJ,IAAIhB,SAAS,CAACE,OAAO,EAAE;UACrB,OAAOK,WAAW,CAACL,OAAO,CAACS,IAAI,CAACG,IAAI,CAAC,KAAKP,WAAW,CAACL,OAAO,CAACS,IAAI,CAACG,IAAI,CAAC,GAAGX,KAAK,CAACc,UAAU,CAACN,IAAI,EAAEX,SAAS,CAACE,OAAO,CAAC,CAAC;QACvH;MACF,CAAC;MAEDgB,YAAY,EAAE;IAChB,CAAC,CAAC,CAAC;IACH,OAAO;MACLpB,GAAG,EAAEE,SAAS;MACdJ,KAAK;MACLa,OAAO;MACPU,KAAK,EAAEvB,KAAK,CAACwB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACP,IAAI,CAAC;MAC7BX;IACF,CAAC;EACH,CAAC,CAAC;EACFT,QAAQ,CAAC,CAAC4B,KAAK,EAAEC,KAAK,KAAKpB,KAAK,CAACqB,MAAM,CAACD,KAAK,CAAC,CAAC;EAC/ChC,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAG1B,SAAS,CAACE,OAAO;IACrC,OAAO,MAAM;MACX;MACAK,WAAW,CAACL,OAAO,GAAG,CAAC,CAAC;MACxBU,MAAM,CAACe,MAAM,CAACnB,GAAG,CAACC,OAAO,CAAC,CAACC,OAAO,CAACkB,MAAM,IAAI;QAC3C,IAAIF,WAAW,EAAE;UACfvB,KAAK,CAAC0B,aAAa,CAACD,MAAM,EAAEF,WAAW,CAAC;QAC1C;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAC9B,KAAK,CAAC,CAAC;EACXL,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXtB,KAAK,CAAC2B,aAAa,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAAC3B,KAAK,CAAC,CAAC;EACX,OAAOK,GAAG;AACZ;AAEA,SAASb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}