{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useAuth } from './useAuth';\n\n/**\n * Role-based permissions hook\n * Provides utilities for checking user permissions\n */\nexport const usePermissions = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n\n  // Define role hierarchy (higher number = more permissions)\n  const roleHierarchy = {\n    'Customer': 1,\n    'Employee': 2,\n    'Admin': 3\n  };\n\n  // Define permissions for each role\n  const rolePermissions = {\n    'Customer': ['view_products', 'add_to_cart', 'place_orders', 'view_own_orders', 'update_profile'],\n    'Employee': ['view_products', 'add_to_cart', 'place_orders', 'view_own_orders', 'update_profile', 'access_admin_dashboard', 'manage_inventory', 'manage_products', 'view_all_orders', 'manage_orders'],\n    'Admin': ['view_products', 'add_to_cart', 'place_orders', 'view_own_orders', 'update_profile', 'access_admin_dashboard', 'manage_inventory', 'manage_products', 'view_all_orders', 'manage_orders', 'manage_suppliers', 'manage_users', 'view_analytics', 'system_settings']\n  };\n\n  // Admin dashboard sections and their required permissions\n  const adminSections = {\n    'overview': ['access_admin_dashboard'],\n    'inventory': ['manage_inventory'],\n    'products': ['manage_products'],\n    'orders': ['view_all_orders'],\n    'suppliers': ['manage_suppliers'],\n    'users': ['manage_users'],\n    'analytics': ['view_analytics']\n  };\n\n  /**\n   * Check if user has a specific permission\n   */\n  const hasPermission = permission => {\n    if (!isAuthenticated || !user) return false;\n    const userPermissions = rolePermissions[user.role] || [];\n    return userPermissions.includes(permission);\n  };\n\n  /**\n   * Check if user has any of the specified permissions\n   */\n  const hasAnyPermission = permissions => {\n    return permissions.some(permission => hasPermission(permission));\n  };\n\n  /**\n   * Check if user has all of the specified permissions\n   */\n  const hasAllPermissions = permissions => {\n    return permissions.every(permission => hasPermission(permission));\n  };\n\n  /**\n   * Check if user has a specific role\n   */\n  const hasRole = role => {\n    if (!isAuthenticated || !user) return false;\n    return user.role === role;\n  };\n\n  /**\n   * Check if user has role equal to or higher than specified role\n   */\n  const hasRoleOrHigher = role => {\n    if (!isAuthenticated || !user) return false;\n    const userLevel = roleHierarchy[user.role] || 0;\n    const requiredLevel = roleHierarchy[role] || 0;\n    return userLevel >= requiredLevel;\n  };\n\n  /**\n   * Check if user can access admin dashboard\n   */\n  const canAccessAdmin = () => {\n    return hasPermission('access_admin_dashboard');\n  };\n\n  /**\n   * Check if user can access specific admin section\n   */\n  const canAccessAdminSection = section => {\n    const requiredPermissions = adminSections[section] || [];\n    return hasAllPermissions(requiredPermissions);\n  };\n\n  /**\n   * Get all accessible admin sections for current user\n   */\n  const getAccessibleAdminSections = () => {\n    if (!canAccessAdmin()) return [];\n    return Object.keys(adminSections).filter(section => canAccessAdminSection(section));\n  };\n\n  /**\n   * Check if user is admin\n   */\n  const isAdmin = () => hasRole('Admin');\n\n  /**\n   * Check if user is employee or higher\n   */\n  const isEmployee = () => hasRoleOrHigher('Employee');\n\n  /**\n   * Check if user is customer\n   */\n  const isCustomer = () => hasRole('Customer');\n\n  /**\n   * Get user's permissions list\n   */\n  const getUserPermissions = () => {\n    if (!isAuthenticated || !user) return [];\n    return rolePermissions[user.role] || [];\n  };\n  return {\n    // Permission checks\n    hasPermission,\n    hasAnyPermission,\n    hasAllPermissions,\n    // Role checks\n    hasRole,\n    hasRoleOrHigher,\n    isAdmin,\n    isEmployee,\n    isCustomer,\n    // Admin specific\n    canAccessAdmin,\n    canAccessAdminSection,\n    getAccessibleAdminSections,\n    // Utilities\n    getUserPermissions,\n    roleHierarchy,\n    rolePermissions,\n    adminSections\n  };\n};\n_s(usePermissions, \"ZciNvz1w38ujgxJkc4IsNEDLTNc=\", false, function () {\n  return [useAuth];\n});\nexport default usePermissions;", "map": {"version": 3, "names": ["useAuth", "usePermissions", "_s", "user", "isAuthenticated", "roleHierarchy", "rolePermissions", "adminSections", "hasPermission", "permission", "userPermissions", "role", "includes", "hasAnyPermission", "permissions", "some", "hasAllPermissions", "every", "hasRole", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userLevel", "requiredLevel", "canAccessAdmin", "canAccessAdminSection", "section", "requiredPermissions", "getAccessibleAdminSections", "Object", "keys", "filter", "isAdmin", "isEmployee", "isCustomer", "getUserPermissions"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/hooks/usePermissions.js"], "sourcesContent": ["import { useAuth } from './useAuth';\n\n/**\n * Role-based permissions hook\n * Provides utilities for checking user permissions\n */\nexport const usePermissions = () => {\n    const { user, isAuthenticated } = useAuth();\n\n    // Define role hierarchy (higher number = more permissions)\n    const roleHierarchy = {\n        'Customer': 1,\n        'Employee': 2,\n        'Admin': 3\n    };\n\n    // Define permissions for each role\n    const rolePermissions = {\n        'Customer': [\n            'view_products',\n            'add_to_cart',\n            'place_orders',\n            'view_own_orders',\n            'update_profile'\n        ],\n        'Employee': [\n            'view_products',\n            'add_to_cart',\n            'place_orders',\n            'view_own_orders',\n            'update_profile',\n            'access_admin_dashboard',\n            'manage_inventory',\n            'manage_products',\n            'view_all_orders',\n            'manage_orders'\n        ],\n        'Admin': [\n            'view_products',\n            'add_to_cart',\n            'place_orders',\n            'view_own_orders',\n            'update_profile',\n            'access_admin_dashboard',\n            'manage_inventory',\n            'manage_products',\n            'view_all_orders',\n            'manage_orders',\n            'manage_suppliers',\n            'manage_users',\n            'view_analytics',\n            'system_settings'\n        ]\n    };\n\n    // Admin dashboard sections and their required permissions\n    const adminSections = {\n        'overview': ['access_admin_dashboard'],\n        'inventory': ['manage_inventory'],\n        'products': ['manage_products'],\n        'orders': ['view_all_orders'],\n        'suppliers': ['manage_suppliers'],\n        'users': ['manage_users'],\n        'analytics': ['view_analytics']\n    };\n\n    /**\n     * Check if user has a specific permission\n     */\n    const hasPermission = (permission) => {\n        if (!isAuthenticated || !user) return false;\n        \n        const userPermissions = rolePermissions[user.role] || [];\n        return userPermissions.includes(permission);\n    };\n\n    /**\n     * Check if user has any of the specified permissions\n     */\n    const hasAnyPermission = (permissions) => {\n        return permissions.some(permission => hasPermission(permission));\n    };\n\n    /**\n     * Check if user has all of the specified permissions\n     */\n    const hasAllPermissions = (permissions) => {\n        return permissions.every(permission => hasPermission(permission));\n    };\n\n    /**\n     * Check if user has a specific role\n     */\n    const hasRole = (role) => {\n        if (!isAuthenticated || !user) return false;\n        return user.role === role;\n    };\n\n    /**\n     * Check if user has role equal to or higher than specified role\n     */\n    const hasRoleOrHigher = (role) => {\n        if (!isAuthenticated || !user) return false;\n        \n        const userLevel = roleHierarchy[user.role] || 0;\n        const requiredLevel = roleHierarchy[role] || 0;\n        \n        return userLevel >= requiredLevel;\n    };\n\n    /**\n     * Check if user can access admin dashboard\n     */\n    const canAccessAdmin = () => {\n        return hasPermission('access_admin_dashboard');\n    };\n\n    /**\n     * Check if user can access specific admin section\n     */\n    const canAccessAdminSection = (section) => {\n        const requiredPermissions = adminSections[section] || [];\n        return hasAllPermissions(requiredPermissions);\n    };\n\n    /**\n     * Get all accessible admin sections for current user\n     */\n    const getAccessibleAdminSections = () => {\n        if (!canAccessAdmin()) return [];\n        \n        return Object.keys(adminSections).filter(section => \n            canAccessAdminSection(section)\n        );\n    };\n\n    /**\n     * Check if user is admin\n     */\n    const isAdmin = () => hasRole('Admin');\n\n    /**\n     * Check if user is employee or higher\n     */\n    const isEmployee = () => hasRoleOrHigher('Employee');\n\n    /**\n     * Check if user is customer\n     */\n    const isCustomer = () => hasRole('Customer');\n\n    /**\n     * Get user's permissions list\n     */\n    const getUserPermissions = () => {\n        if (!isAuthenticated || !user) return [];\n        return rolePermissions[user.role] || [];\n    };\n\n    return {\n        // Permission checks\n        hasPermission,\n        hasAnyPermission,\n        hasAllPermissions,\n        \n        // Role checks\n        hasRole,\n        hasRoleOrHigher,\n        isAdmin,\n        isEmployee,\n        isCustomer,\n        \n        // Admin specific\n        canAccessAdmin,\n        canAccessAdminSection,\n        getAccessibleAdminSections,\n        \n        // Utilities\n        getUserPermissions,\n        roleHierarchy,\n        rolePermissions,\n        adminSections\n    };\n};\n\nexport default usePermissions;\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,WAAW;;AAEnC;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGJ,OAAO,CAAC,CAAC;;EAE3C;EACA,MAAMK,aAAa,GAAG;IAClB,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,CAAC;IACb,OAAO,EAAE;EACb,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG;IACpB,UAAU,EAAE,CACR,eAAe,EACf,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,gBAAgB,CACnB;IACD,UAAU,EAAE,CACR,eAAe,EACf,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,gBAAgB,EAChB,wBAAwB,EACxB,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,CAClB;IACD,OAAO,EAAE,CACL,eAAe,EACf,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,gBAAgB,EAChB,wBAAwB,EACxB,kBAAkB,EAClB,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,kBAAkB,EAClB,cAAc,EACd,gBAAgB,EAChB,iBAAiB;EAEzB,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG;IAClB,UAAU,EAAE,CAAC,wBAAwB,CAAC;IACtC,WAAW,EAAE,CAAC,kBAAkB,CAAC;IACjC,UAAU,EAAE,CAAC,iBAAiB,CAAC;IAC/B,QAAQ,EAAE,CAAC,iBAAiB,CAAC;IAC7B,WAAW,EAAE,CAAC,kBAAkB,CAAC;IACjC,OAAO,EAAE,CAAC,cAAc,CAAC;IACzB,WAAW,EAAE,CAAC,gBAAgB;EAClC,CAAC;;EAED;AACJ;AACA;EACI,MAAMC,aAAa,GAAIC,UAAU,IAAK;IAClC,IAAI,CAACL,eAAe,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;IAE3C,MAAMO,eAAe,GAAGJ,eAAe,CAACH,IAAI,CAACQ,IAAI,CAAC,IAAI,EAAE;IACxD,OAAOD,eAAe,CAACE,QAAQ,CAACH,UAAU,CAAC;EAC/C,CAAC;;EAED;AACJ;AACA;EACI,MAAMI,gBAAgB,GAAIC,WAAW,IAAK;IACtC,OAAOA,WAAW,CAACC,IAAI,CAACN,UAAU,IAAID,aAAa,CAACC,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;AACJ;AACA;EACI,MAAMO,iBAAiB,GAAIF,WAAW,IAAK;IACvC,OAAOA,WAAW,CAACG,KAAK,CAACR,UAAU,IAAID,aAAa,CAACC,UAAU,CAAC,CAAC;EACrE,CAAC;;EAED;AACJ;AACA;EACI,MAAMS,OAAO,GAAIP,IAAI,IAAK;IACtB,IAAI,CAACP,eAAe,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;IAC3C,OAAOA,IAAI,CAACQ,IAAI,KAAKA,IAAI;EAC7B,CAAC;;EAED;AACJ;AACA;EACI,MAAMQ,eAAe,GAAIR,IAAI,IAAK;IAC9B,IAAI,CAACP,eAAe,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;IAE3C,MAAMiB,SAAS,GAAGf,aAAa,CAACF,IAAI,CAACQ,IAAI,CAAC,IAAI,CAAC;IAC/C,MAAMU,aAAa,GAAGhB,aAAa,CAACM,IAAI,CAAC,IAAI,CAAC;IAE9C,OAAOS,SAAS,IAAIC,aAAa;EACrC,CAAC;;EAED;AACJ;AACA;EACI,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,OAAOd,aAAa,CAAC,wBAAwB,CAAC;EAClD,CAAC;;EAED;AACJ;AACA;EACI,MAAMe,qBAAqB,GAAIC,OAAO,IAAK;IACvC,MAAMC,mBAAmB,GAAGlB,aAAa,CAACiB,OAAO,CAAC,IAAI,EAAE;IACxD,OAAOR,iBAAiB,CAACS,mBAAmB,CAAC;EACjD,CAAC;;EAED;AACJ;AACA;EACI,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;IACrC,IAAI,CAACJ,cAAc,CAAC,CAAC,EAAE,OAAO,EAAE;IAEhC,OAAOK,MAAM,CAACC,IAAI,CAACrB,aAAa,CAAC,CAACsB,MAAM,CAACL,OAAO,IAC5CD,qBAAqB,CAACC,OAAO,CACjC,CAAC;EACL,CAAC;;EAED;AACJ;AACA;EACI,MAAMM,OAAO,GAAGA,CAAA,KAAMZ,OAAO,CAAC,OAAO,CAAC;;EAEtC;AACJ;AACA;EACI,MAAMa,UAAU,GAAGA,CAAA,KAAMZ,eAAe,CAAC,UAAU,CAAC;;EAEpD;AACJ;AACA;EACI,MAAMa,UAAU,GAAGA,CAAA,KAAMd,OAAO,CAAC,UAAU,CAAC;;EAE5C;AACJ;AACA;EACI,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC7B,eAAe,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACxC,OAAOG,eAAe,CAACH,IAAI,CAACQ,IAAI,CAAC,IAAI,EAAE;EAC3C,CAAC;EAED,OAAO;IACH;IACAH,aAAa;IACbK,gBAAgB;IAChBG,iBAAiB;IAEjB;IACAE,OAAO;IACPC,eAAe;IACfW,OAAO;IACPC,UAAU;IACVC,UAAU;IAEV;IACAV,cAAc;IACdC,qBAAqB;IACrBG,0BAA0B;IAE1B;IACAO,kBAAkB;IAClB5B,aAAa;IACbC,eAAe;IACfC;EACJ,CAAC;AACL,CAAC;AAACL,EAAA,CAjLWD,cAAc;EAAA,QACWD,OAAO;AAAA;AAkL7C,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}