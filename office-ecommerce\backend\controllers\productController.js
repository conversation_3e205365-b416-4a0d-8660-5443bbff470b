const ProductService = require('../services/productService');
const logger = require('../utils/logger');

/**
 * Product Controller
 * Handles HTTP requests and responses for product-related operations
 */
class ProductController {

  constructor() {
    this.productService = new ProductService();
  }

  /**
   * Get all products with pagination and filters
   * @route GET /api/products
   * @access Public
   */
  async getProducts(req, res) {
    try {
      const filters = {
        page: req.query.page,
        limit: req.query.limit,
        category: req.query.category,
        search: req.query.search,
        minPrice: req.query.minPrice,
        maxPrice: req.query.maxPrice,
        inStock: req.query.inStock === 'true',
        featured: req.query.featured === 'true',
        sortBy: req.query.sortBy,
        sortOrder: req.query.sortOrder
      };

      const result = await this.productService.getProducts(filters);

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('ProductController.getProducts error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch products',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get a single product by ID
   * @route GET /api/products/:id
   * @access Public
   */
  async getProductById(req, res) {
    try {
      const { id } = req.params;

      const result = await this.productService.getProductById(id);

      if (!result.success) {
        const statusCode = result.code === 'INVALID_PRODUCT_ID' ? 400 : 404;
        return res.status(statusCode).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('ProductController.getProductById error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Create a new product
   * @route POST /api/products
   * @access Private (Admin/Employee)
   */
  async createProduct(req, res) {
    try {
      const productData = req.body;

      const result = await this.productService.createProduct(productData, req.user);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.status(201).json({
        success: true,
        message: 'Product created successfully',
        data: result.data
      });

    } catch (error) {
      logger.error('ProductController.createProduct error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create product',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Update an existing product
   * @route PUT /api/products/:id
   * @access Private (Admin/Employee)
   */
  async updateProduct(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const result = await this.productService.updateProduct(id, updateData, req.user);

      if (!result.success) {
        const statusCode = result.code === 'INVALID_PRODUCT_ID' ? 400 : 404;
        return res.status(statusCode).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.json({
        success: true,
        message: 'Product updated successfully',
        data: result.data
      });

    } catch (error) {
      logger.error('ProductController.updateProduct error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update product',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Delete a product
   * @route DELETE /api/products/:id
   * @access Private (Admin/Employee)
   */
  async deleteProduct(req, res) {
    try {
      const { id } = req.params;

      const result = await this.productService.deleteProduct(id, req.user);

      if (!result.success) {
        const statusCode = result.code === 'INVALID_PRODUCT_ID' ? 400 : 
                          result.code === 'PRODUCT_HAS_ACTIVE_ORDERS' ? 409 : 404;
        return res.status(statusCode).json({
          success: false,
          message: result.error,
          code: result.code
        });
      }

      res.json({
        success: true,
        message: result.message
      });

    } catch (error) {
      logger.error('ProductController.deleteProduct error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete product',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get product categories
   * @route GET /api/products/categories
   * @access Public
   */
  async getCategories(req, res) {
    try {
      const result = await this.productService.getProductCategories();

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('ProductController.getCategories error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch categories',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get featured products
   * @route GET /api/products/featured
   * @access Public
   */
  async getFeaturedProducts(req, res) {
    try {
      const limit = req.query.limit || 8;
      const result = await this.productService.getFeaturedProducts(limit);

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('ProductController.getFeaturedProducts error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch featured products',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Search products
   * @route GET /api/products/search
   * @access Public
   */
  async searchProducts(req, res) {
    try {
      const { q: query } = req.query;
      
      if (!query) {
        return res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const filters = {
        limit: req.query.limit,
        category: req.query.category,
        minPrice: req.query.minPrice,
        maxPrice: req.query.maxPrice
      };

      const result = await this.productService.searchProducts(query, filters);

      res.json({
        success: true,
        data: result.data
      });

    } catch (error) {
      logger.error('ProductController.searchProducts error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search products',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

module.exports = ProductController;
