{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MathUtils } from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { useSpring, a } from '@react-spring/three';\nimport { useGesture } from '@use-gesture/react';\nfunction PresentationControls({\n  enabled = true,\n  snap,\n  global,\n  domElement,\n  cursor = true,\n  children,\n  speed = 1,\n  rotation = [0, 0, 0],\n  zoom = 1,\n  polar = [0, Math.PI / 2],\n  azimuth = [-Infinity, Infinity],\n  config = {\n    mass: 1,\n    tension: 170,\n    friction: 26\n  }\n}) {\n  const events = useThree(state => state.events);\n  const gl = useThree(state => state.gl);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const {\n    size\n  } = useThree();\n  const rPolar = React.useMemo(() => [rotation[0] + polar[0], rotation[0] + polar[1]], [rotation[0], polar[0], polar[1]]);\n  const rAzimuth = React.useMemo(() => [rotation[1] + azimuth[0], rotation[1] + azimuth[1]], [rotation[1], azimuth[0], azimuth[1]]);\n  const rInitial = React.useMemo(() => [MathUtils.clamp(rotation[0], ...rPolar), MathUtils.clamp(rotation[1], ...rAzimuth), rotation[2]], [rotation[0], rotation[1], rotation[2], rPolar, rAzimuth]);\n  const [spring, api] = useSpring(() => ({\n    scale: 1,\n    rotation: rInitial,\n    config\n  }));\n  React.useEffect(() => void api.start({\n    scale: 1,\n    rotation: rInitial,\n    config\n  }), [rInitial]);\n  React.useEffect(() => {\n    if (global && cursor && enabled) {\n      explDomElement.style.cursor = 'grab';\n      gl.domElement.style.cursor = '';\n      return () => {\n        explDomElement.style.cursor = 'default';\n        gl.domElement.style.cursor = 'default';\n      };\n    }\n  }, [global, cursor, explDomElement, enabled]);\n  const bind = useGesture({\n    onHover: ({\n      last\n    }) => {\n      if (cursor && !global && enabled) explDomElement.style.cursor = last ? 'auto' : 'grab';\n    },\n    onDrag: ({\n      down,\n      delta: [x, y],\n      memo: [oldY, oldX] = spring.rotation.animation.to || rInitial\n    }) => {\n      if (!enabled) return [y, x];\n      if (cursor) explDomElement.style.cursor = down ? 'grabbing' : 'grab';\n      x = MathUtils.clamp(oldX + x / size.width * Math.PI * speed, ...rAzimuth);\n      y = MathUtils.clamp(oldY + y / size.height * Math.PI * speed, ...rPolar);\n      const sConfig = snap && !down && typeof snap !== 'boolean' ? snap : config;\n      api.start({\n        scale: down && y > rPolar[1] / 2 ? zoom : 1,\n        rotation: snap && !down ? rInitial : [y, x, 0],\n        config: n => n === 'scale' ? {\n          ...sConfig,\n          friction: sConfig.friction * 3\n        } : sConfig\n      });\n      return [y, x];\n    }\n  }, {\n    target: global ? explDomElement : undefined\n  });\n  return /*#__PURE__*/React.createElement(a.group, _extends({}, bind == null ? void 0 : bind(), spring), children);\n}\nexport { PresentationControls };", "map": {"version": 3, "names": ["_extends", "React", "MathUtils", "useThree", "useSpring", "a", "useGesture", "PresentationControls", "enabled", "snap", "global", "dom<PERSON>lement", "cursor", "children", "speed", "rotation", "zoom", "polar", "Math", "PI", "azimuth", "Infinity", "config", "mass", "tension", "friction", "events", "state", "gl", "explDomElement", "connected", "size", "rPolar", "useMemo", "rAzimuth", "rInitial", "clamp", "spring", "api", "scale", "useEffect", "start", "style", "bind", "onHover", "last", "onDrag", "down", "delta", "x", "y", "memo", "oldY", "oldX", "animation", "to", "width", "height", "sConfig", "n", "target", "undefined", "createElement", "group"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/PresentationControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MathUtils } from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { useSpring, a } from '@react-spring/three';\nimport { useGesture } from '@use-gesture/react';\n\nfunction PresentationControls({\n  enabled = true,\n  snap,\n  global,\n  domElement,\n  cursor = true,\n  children,\n  speed = 1,\n  rotation = [0, 0, 0],\n  zoom = 1,\n  polar = [0, Math.PI / 2],\n  azimuth = [-Infinity, Infinity],\n  config = {\n    mass: 1,\n    tension: 170,\n    friction: 26\n  }\n}) {\n  const events = useThree(state => state.events);\n  const gl = useThree(state => state.gl);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const {\n    size\n  } = useThree();\n  const rPolar = React.useMemo(() => [rotation[0] + polar[0], rotation[0] + polar[1]], [rotation[0], polar[0], polar[1]]);\n  const rAzimuth = React.useMemo(() => [rotation[1] + azimuth[0], rotation[1] + azimuth[1]], [rotation[1], azimuth[0], azimuth[1]]);\n  const rInitial = React.useMemo(() => [MathUtils.clamp(rotation[0], ...rPolar), MathUtils.clamp(rotation[1], ...rAzimuth), rotation[2]], [rotation[0], rotation[1], rotation[2], rPolar, rAzimuth]);\n  const [spring, api] = useSpring(() => ({\n    scale: 1,\n    rotation: rInitial,\n    config\n  }));\n  React.useEffect(() => void api.start({\n    scale: 1,\n    rotation: rInitial,\n    config\n  }), [rInitial]);\n  React.useEffect(() => {\n    if (global && cursor && enabled) {\n      explDomElement.style.cursor = 'grab';\n      gl.domElement.style.cursor = '';\n      return () => {\n        explDomElement.style.cursor = 'default';\n        gl.domElement.style.cursor = 'default';\n      };\n    }\n  }, [global, cursor, explDomElement, enabled]);\n  const bind = useGesture({\n    onHover: ({\n      last\n    }) => {\n      if (cursor && !global && enabled) explDomElement.style.cursor = last ? 'auto' : 'grab';\n    },\n    onDrag: ({\n      down,\n      delta: [x, y],\n      memo: [oldY, oldX] = spring.rotation.animation.to || rInitial\n    }) => {\n      if (!enabled) return [y, x];\n      if (cursor) explDomElement.style.cursor = down ? 'grabbing' : 'grab';\n      x = MathUtils.clamp(oldX + x / size.width * Math.PI * speed, ...rAzimuth);\n      y = MathUtils.clamp(oldY + y / size.height * Math.PI * speed, ...rPolar);\n      const sConfig = snap && !down && typeof snap !== 'boolean' ? snap : config;\n      api.start({\n        scale: down && y > rPolar[1] / 2 ? zoom : 1,\n        rotation: snap && !down ? rInitial : [y, x, 0],\n        config: n => n === 'scale' ? { ...sConfig,\n          friction: sConfig.friction * 3\n        } : sConfig\n      });\n      return [y, x];\n    }\n  }, {\n    target: global ? explDomElement : undefined\n  });\n  return /*#__PURE__*/React.createElement(a.group, _extends({}, bind == null ? void 0 : bind(), spring), children);\n}\n\nexport { PresentationControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,SAAS,EAAEC,CAAC,QAAQ,qBAAqB;AAClD,SAASC,UAAU,QAAQ,oBAAoB;AAE/C,SAASC,oBAAoBA,CAAC;EAC5BC,OAAO,GAAG,IAAI;EACdC,IAAI;EACJC,MAAM;EACNC,UAAU;EACVC,MAAM,GAAG,IAAI;EACbC,QAAQ;EACRC,KAAK,GAAG,CAAC;EACTC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,IAAI,GAAG,CAAC;EACRC,KAAK,GAAG,CAAC,CAAC,EAAEC,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;EACxBC,OAAO,GAAG,CAAC,CAACC,QAAQ,EAAEA,QAAQ,CAAC;EAC/BC,MAAM,GAAG;IACPC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE;EACZ;AACF,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGvB,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC;EAC9C,MAAME,EAAE,GAAGzB,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACC,EAAE,CAAC;EACtC,MAAMC,cAAc,GAAGlB,UAAU,IAAIe,MAAM,CAACI,SAAS,IAAIF,EAAE,CAACjB,UAAU;EACtE,MAAM;IACJoB;EACF,CAAC,GAAG5B,QAAQ,CAAC,CAAC;EACd,MAAM6B,MAAM,GAAG/B,KAAK,CAACgC,OAAO,CAAC,MAAM,CAAClB,QAAQ,CAAC,CAAC,CAAC,GAAGE,KAAK,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC,GAAGE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACvH,MAAMiB,QAAQ,GAAGjC,KAAK,CAACgC,OAAO,CAAC,MAAM,CAAClB,QAAQ,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC,CAAC,CAAC,EAAEL,QAAQ,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACjI,MAAMe,QAAQ,GAAGlC,KAAK,CAACgC,OAAO,CAAC,MAAM,CAAC/B,SAAS,CAACkC,KAAK,CAACrB,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAGiB,MAAM,CAAC,EAAE9B,SAAS,CAACkC,KAAK,CAACrB,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAGmB,QAAQ,CAAC,EAAEnB,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,EAAEiB,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAClM,MAAM,CAACG,MAAM,EAAEC,GAAG,CAAC,GAAGlC,SAAS,CAAC,OAAO;IACrCmC,KAAK,EAAE,CAAC;IACRxB,QAAQ,EAAEoB,QAAQ;IAClBb;EACF,CAAC,CAAC,CAAC;EACHrB,KAAK,CAACuC,SAAS,CAAC,MAAM,KAAKF,GAAG,CAACG,KAAK,CAAC;IACnCF,KAAK,EAAE,CAAC;IACRxB,QAAQ,EAAEoB,QAAQ;IAClBb;EACF,CAAC,CAAC,EAAE,CAACa,QAAQ,CAAC,CAAC;EACflC,KAAK,CAACuC,SAAS,CAAC,MAAM;IACpB,IAAI9B,MAAM,IAAIE,MAAM,IAAIJ,OAAO,EAAE;MAC/BqB,cAAc,CAACa,KAAK,CAAC9B,MAAM,GAAG,MAAM;MACpCgB,EAAE,CAACjB,UAAU,CAAC+B,KAAK,CAAC9B,MAAM,GAAG,EAAE;MAC/B,OAAO,MAAM;QACXiB,cAAc,CAACa,KAAK,CAAC9B,MAAM,GAAG,SAAS;QACvCgB,EAAE,CAACjB,UAAU,CAAC+B,KAAK,CAAC9B,MAAM,GAAG,SAAS;MACxC,CAAC;IACH;EACF,CAAC,EAAE,CAACF,MAAM,EAAEE,MAAM,EAAEiB,cAAc,EAAErB,OAAO,CAAC,CAAC;EAC7C,MAAMmC,IAAI,GAAGrC,UAAU,CAAC;IACtBsC,OAAO,EAAEA,CAAC;MACRC;IACF,CAAC,KAAK;MACJ,IAAIjC,MAAM,IAAI,CAACF,MAAM,IAAIF,OAAO,EAAEqB,cAAc,CAACa,KAAK,CAAC9B,MAAM,GAAGiC,IAAI,GAAG,MAAM,GAAG,MAAM;IACxF,CAAC;IACDC,MAAM,EAAEA,CAAC;MACPC,IAAI;MACJC,KAAK,EAAE,CAACC,CAAC,EAAEC,CAAC,CAAC;MACbC,IAAI,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC,GAAGhB,MAAM,CAACtB,QAAQ,CAACuC,SAAS,CAACC,EAAE,IAAIpB;IACvD,CAAC,KAAK;MACJ,IAAI,CAAC3B,OAAO,EAAE,OAAO,CAAC0C,CAAC,EAAED,CAAC,CAAC;MAC3B,IAAIrC,MAAM,EAAEiB,cAAc,CAACa,KAAK,CAAC9B,MAAM,GAAGmC,IAAI,GAAG,UAAU,GAAG,MAAM;MACpEE,CAAC,GAAG/C,SAAS,CAACkC,KAAK,CAACiB,IAAI,GAAGJ,CAAC,GAAGlB,IAAI,CAACyB,KAAK,GAAGtC,IAAI,CAACC,EAAE,GAAGL,KAAK,EAAE,GAAGoB,QAAQ,CAAC;MACzEgB,CAAC,GAAGhD,SAAS,CAACkC,KAAK,CAACgB,IAAI,GAAGF,CAAC,GAAGnB,IAAI,CAAC0B,MAAM,GAAGvC,IAAI,CAACC,EAAE,GAAGL,KAAK,EAAE,GAAGkB,MAAM,CAAC;MACxE,MAAM0B,OAAO,GAAGjD,IAAI,IAAI,CAACsC,IAAI,IAAI,OAAOtC,IAAI,KAAK,SAAS,GAAGA,IAAI,GAAGa,MAAM;MAC1EgB,GAAG,CAACG,KAAK,CAAC;QACRF,KAAK,EAAEQ,IAAI,IAAIG,CAAC,GAAGlB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGhB,IAAI,GAAG,CAAC;QAC3CD,QAAQ,EAAEN,IAAI,IAAI,CAACsC,IAAI,GAAGZ,QAAQ,GAAG,CAACe,CAAC,EAAED,CAAC,EAAE,CAAC,CAAC;QAC9C3B,MAAM,EAAEqC,CAAC,IAAIA,CAAC,KAAK,OAAO,GAAG;UAAE,GAAGD,OAAO;UACvCjC,QAAQ,EAAEiC,OAAO,CAACjC,QAAQ,GAAG;QAC/B,CAAC,GAAGiC;MACN,CAAC,CAAC;MACF,OAAO,CAACR,CAAC,EAAED,CAAC,CAAC;IACf;EACF,CAAC,EAAE;IACDW,MAAM,EAAElD,MAAM,GAAGmB,cAAc,GAAGgC;EACpC,CAAC,CAAC;EACF,OAAO,aAAa5D,KAAK,CAAC6D,aAAa,CAACzD,CAAC,CAAC0D,KAAK,EAAE/D,QAAQ,CAAC,CAAC,CAAC,EAAE2C,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC,CAAC,EAAEN,MAAM,CAAC,EAAExB,QAAQ,CAAC;AAClH;AAEA,SAASN,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}