/* Activity Logs Styles */
.activity-logs {
  padding: 2rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.activity-logs-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.header-title h1 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 600;
}

.header-title p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.mock-mode-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  color: #92400e;
  font-size: 0.875rem;
  font-weight: 500;
}

.mock-mode-indicator svg {
  width: 16px;
  height: 16px;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* Statistics Cards */
.activity-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.error {
  border-left-color: #ef4444;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Filters */
.activity-filters {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.filters-row {
  display: flex;
  gap: 1rem;
  align-items: end;
  margin-bottom: 1rem;
}

.filters-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  flex: 1;
  min-width: 150px;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
}

.search-input {
  position: relative;
}

.search-input svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

.search-input input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-group input:focus,
.filter-group select:focus,
.search-input input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  align-items: end;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.btn svg {
  width: 16px;
  height: 16px;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  margin-bottom: 1rem;
}

.error-message svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.fallback-notice {
  color: #f59e0b;
  font-style: italic;
}

/* Activity Table */
.activity-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.activity-table {
  width: 100%;
  border-collapse: collapse;
}

.activity-table th {
  background-color: #f9fafb;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  border-bottom: 1px solid #e5e7eb;
}

.activity-table td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  font-size: 0.875rem;
}

.activity-table tbody tr:hover {
  background-color: #f9fafb;
}

/* Severity-based row styling */
.activity-table tbody tr.severity-warning {
  border-left: 3px solid #f59e0b;
}

.activity-table tbody tr.severity-error {
  border-left: 3px solid #ef4444;
}

.activity-table tbody tr.severity-critical {
  border-left: 3px solid #dc2626;
  background-color: #fef2f2;
}

/* Table cell specific styles */
.timestamp {
  color: #6b7280;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  white-space: nowrap;
}

.user-info {
  min-width: 120px;
}

.user-name {
  font-weight: 500;
  color: #1f2937;
}

.user-role {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.action-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.action-badge.login {
  background-color: #dcfce7;
  color: #166534;
}

.action-badge.logout {
  background-color: #fef3c7;
  color: #92400e;
}

.action-badge.create {
  background-color: #dbeafe;
  color: #1e40af;
}

.action-badge.update {
  background-color: #e0e7ff;
  color: #3730a3;
}

.action-badge.delete {
  background-color: #fee2e2;
  color: #991b1b;
}

.action-badge.view {
  background-color: #f3f4f6;
  color: #374151;
}

.entity {
  color: #6b7280;
  font-weight: 500;
}

.description {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.severity-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.severity-indicator svg {
  width: 16px;
  height: 16px;
}

.ip-address {
  font-family: 'Courier New', monospace;
  color: #6b7280;
  font-size: 0.8rem;
}

.duration {
  color: #6b7280;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .filters-row {
    flex-wrap: wrap;
  }
  
  .filter-group {
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .activity-logs {
    padding: 1rem;
  }
  
  .activity-logs-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .activity-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .activity-table-container {
    overflow-x: auto;
  }
  
  .activity-table {
    min-width: 800px;
  }
  
  .filters-row {
    flex-direction: column;
  }
  
  .filter-group {
    min-width: auto;
  }
}
