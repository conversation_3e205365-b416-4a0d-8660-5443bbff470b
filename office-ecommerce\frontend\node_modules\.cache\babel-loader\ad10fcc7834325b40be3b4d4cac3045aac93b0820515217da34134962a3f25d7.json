{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\contexts\\\\CurrencyContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Currency context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CurrencyContext = /*#__PURE__*/createContext();\n\n// Currency action types\nconst CURRENCY_ACTIONS = {\n  SET_CURRENCY: 'SET_CURRENCY',\n  SET_EXCHANGE_RATES: 'SET_EXCHANGE_RATES',\n  LOAD_CURRENCY: 'LOAD_CURRENCY'\n};\n\n// Initial state\nconst initialState = {\n  currentCurrency: 'USD',\n  exchangeRates: {\n    USD: 1,\n    PHP: 56.50 // 1 USD = 56.50 PHP (approximate rate)\n  },\n  currencies: [{\n    code: 'USD',\n    name: 'US Dollar',\n    symbol: '$'\n  }, {\n    code: 'PHP',\n    name: 'Philippine Peso',\n    symbol: '₱'\n  }]\n};\n\n// Currency reducer\nconst currencyReducer = (state, action) => {\n  switch (action.type) {\n    case CURRENCY_ACTIONS.SET_CURRENCY:\n      return {\n        ...state,\n        currentCurrency: action.payload\n      };\n    case CURRENCY_ACTIONS.SET_EXCHANGE_RATES:\n      return {\n        ...state,\n        exchangeRates: action.payload\n      };\n    case CURRENCY_ACTIONS.LOAD_CURRENCY:\n      return {\n        ...state,\n        currentCurrency: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Currency provider component\nexport const CurrencyProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(currencyReducer, initialState);\n\n  // Load currency from localStorage on mount\n  useEffect(() => {\n    const savedCurrency = localStorage.getItem('selected-currency');\n    if (savedCurrency) {\n      dispatch({\n        type: CURRENCY_ACTIONS.LOAD_CURRENCY,\n        payload: savedCurrency\n      });\n    }\n  }, []);\n\n  // Save currency to localStorage whenever it changes\n  useEffect(() => {\n    localStorage.setItem('selected-currency', state.currentCurrency);\n  }, [state.currentCurrency]);\n\n  // Set currency\n  const setCurrency = currencyCode => {\n    dispatch({\n      type: CURRENCY_ACTIONS.SET_CURRENCY,\n      payload: currencyCode\n    });\n  };\n\n  // Convert price from USD to selected currency\n  const convertPrice = priceInUSD => {\n    const rate = state.exchangeRates[state.currentCurrency] || 1;\n    return priceInUSD * rate;\n  };\n\n  // Format price with currency symbol\n  const formatPrice = priceInUSD => {\n    const convertedPrice = convertPrice(priceInUSD);\n    const currency = state.currencies.find(c => c.code === state.currentCurrency);\n    const symbol = currency ? currency.symbol : '$';\n\n    // Format based on currency\n    if (state.currentCurrency === 'PHP') {\n      return `${symbol}${convertedPrice.toLocaleString('en-PH', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })}`;\n    } else {\n      return `${symbol}${convertedPrice.toLocaleString('en-US', {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n      })}`;\n    }\n  };\n\n  // Get current currency info\n  const getCurrentCurrency = () => {\n    return state.currencies.find(c => c.code === state.currentCurrency) || state.currencies[0];\n  };\n\n  // Update exchange rates (could be called from an API)\n  const updateExchangeRates = rates => {\n    dispatch({\n      type: CURRENCY_ACTIONS.SET_EXCHANGE_RATES,\n      payload: rates\n    });\n  };\n  const value = {\n    // State\n    currentCurrency: state.currentCurrency,\n    currencies: state.currencies,\n    exchangeRates: state.exchangeRates,\n    // Actions\n    setCurrency,\n    updateExchangeRates,\n    // Utilities\n    convertPrice,\n    formatPrice,\n    getCurrentCurrency\n  };\n  return /*#__PURE__*/_jsxDEV(CurrencyContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 9\n  }, this);\n};\n\n// Custom hook to use currency context\n_s(CurrencyProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = CurrencyProvider;\nexport const useCurrency = () => {\n  _s2();\n  const context = useContext(CurrencyContext);\n  if (!context) {\n    throw new Error('useCurrency must be used within a CurrencyProvider');\n  }\n  return context;\n};\n_s2(useCurrency, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default CurrencyContext;\nvar _c;\n$RefreshReg$(_c, \"CurrencyProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>cyContext", "CURRENCY_ACTIONS", "SET_CURRENCY", "SET_EXCHANGE_RATES", "LOAD_CURRENCY", "initialState", "currentCurrency", "exchangeRates", "USD", "PHP", "currencies", "code", "name", "symbol", "currencyReducer", "state", "action", "type", "payload", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "savedCurrency", "localStorage", "getItem", "setItem", "setCurrency", "currencyCode", "convertPrice", "priceInUSD", "rate", "formatPrice", "convertedPrice", "currency", "find", "c", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "getCurrentCurrency", "updateExchangeRates", "rates", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useCurrency", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/contexts/CurrencyContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\n\n// Currency context\nconst CurrencyContext = createContext();\n\n// Currency action types\nconst CURRENCY_ACTIONS = {\n    SET_CURRENCY: 'SET_CURRENCY',\n    SET_EXCHANGE_RATES: 'SET_EXCHANGE_RATES',\n    LOAD_CURRENCY: 'LOAD_CURRENCY'\n};\n\n// Initial state\nconst initialState = {\n    currentCurrency: 'USD',\n    exchangeRates: {\n        USD: 1,\n        PHP: 56.50 // 1 USD = 56.50 PHP (approximate rate)\n    },\n    currencies: [\n        { code: 'USD', name: 'US Dollar', symbol: '$' },\n        { code: 'PHP', name: 'Philippine Peso', symbol: '₱' }\n    ]\n};\n\n// Currency reducer\nconst currencyReducer = (state, action) => {\n    switch (action.type) {\n        case CURRENCY_ACTIONS.SET_CURRENCY:\n            return {\n                ...state,\n                currentCurrency: action.payload\n            };\n        case CURRENCY_ACTIONS.SET_EXCHANGE_RATES:\n            return {\n                ...state,\n                exchangeRates: action.payload\n            };\n        case CURRENCY_ACTIONS.LOAD_CURRENCY:\n            return {\n                ...state,\n                currentCurrency: action.payload\n            };\n        default:\n            return state;\n    }\n};\n\n// Currency provider component\nexport const CurrencyProvider = ({ children }) => {\n    const [state, dispatch] = useReducer(currencyReducer, initialState);\n\n    // Load currency from localStorage on mount\n    useEffect(() => {\n        const savedCurrency = localStorage.getItem('selected-currency');\n        if (savedCurrency) {\n            dispatch({ type: CURRENCY_ACTIONS.LOAD_CURRENCY, payload: savedCurrency });\n        }\n    }, []);\n\n    // Save currency to localStorage whenever it changes\n    useEffect(() => {\n        localStorage.setItem('selected-currency', state.currentCurrency);\n    }, [state.currentCurrency]);\n\n    // Set currency\n    const setCurrency = (currencyCode) => {\n        dispatch({ type: CURRENCY_ACTIONS.SET_CURRENCY, payload: currencyCode });\n    };\n\n    // Convert price from USD to selected currency\n    const convertPrice = (priceInUSD) => {\n        const rate = state.exchangeRates[state.currentCurrency] || 1;\n        return priceInUSD * rate;\n    };\n\n    // Format price with currency symbol\n    const formatPrice = (priceInUSD) => {\n        const convertedPrice = convertPrice(priceInUSD);\n        const currency = state.currencies.find(c => c.code === state.currentCurrency);\n        const symbol = currency ? currency.symbol : '$';\n        \n        // Format based on currency\n        if (state.currentCurrency === 'PHP') {\n            return `${symbol}${convertedPrice.toLocaleString('en-PH', { \n                minimumFractionDigits: 2, \n                maximumFractionDigits: 2 \n            })}`;\n        } else {\n            return `${symbol}${convertedPrice.toLocaleString('en-US', { \n                minimumFractionDigits: 2, \n                maximumFractionDigits: 2 \n            })}`;\n        }\n    };\n\n    // Get current currency info\n    const getCurrentCurrency = () => {\n        return state.currencies.find(c => c.code === state.currentCurrency) || state.currencies[0];\n    };\n\n    // Update exchange rates (could be called from an API)\n    const updateExchangeRates = (rates) => {\n        dispatch({ type: CURRENCY_ACTIONS.SET_EXCHANGE_RATES, payload: rates });\n    };\n\n    const value = {\n        // State\n        currentCurrency: state.currentCurrency,\n        currencies: state.currencies,\n        exchangeRates: state.exchangeRates,\n        \n        // Actions\n        setCurrency,\n        updateExchangeRates,\n        \n        // Utilities\n        convertPrice,\n        formatPrice,\n        getCurrentCurrency\n    };\n\n    return (\n        <CurrencyContext.Provider value={value}>\n            {children}\n        </CurrencyContext.Provider>\n    );\n};\n\n// Custom hook to use currency context\nexport const useCurrency = () => {\n    const context = useContext(CurrencyContext);\n    if (!context) {\n        throw new Error('useCurrency must be used within a CurrencyProvider');\n    }\n    return context;\n};\n\nexport default CurrencyContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;;AAE/E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,gBAAGN,aAAa,CAAC,CAAC;;AAEvC;AACA,MAAMO,gBAAgB,GAAG;EACrBC,YAAY,EAAE,cAAc;EAC5BC,kBAAkB,EAAE,oBAAoB;EACxCC,aAAa,EAAE;AACnB,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACjBC,eAAe,EAAE,KAAK;EACtBC,aAAa,EAAE;IACXC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,KAAK,CAAC;EACf,CAAC;EACDC,UAAU,EAAE,CACR;IAAEC,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,WAAW;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC/C;IAAEF,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,MAAM,EAAE;EAAI,CAAC;AAE7D,CAAC;;AAED;AACA,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACvC,QAAQA,MAAM,CAACC,IAAI;IACf,KAAKhB,gBAAgB,CAACC,YAAY;MAC9B,OAAO;QACH,GAAGa,KAAK;QACRT,eAAe,EAAEU,MAAM,CAACE;MAC5B,CAAC;IACL,KAAKjB,gBAAgB,CAACE,kBAAkB;MACpC,OAAO;QACH,GAAGY,KAAK;QACRR,aAAa,EAAES,MAAM,CAACE;MAC1B,CAAC;IACL,KAAKjB,gBAAgB,CAACG,aAAa;MAC/B,OAAO;QACH,GAAGW,KAAK;QACRT,eAAe,EAAEU,MAAM,CAACE;MAC5B,CAAC;IACL;MACI,OAAOH,KAAK;EACpB;AACJ,CAAC;;AAED;AACA,OAAO,MAAMI,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACN,KAAK,EAAEO,QAAQ,CAAC,GAAG1B,UAAU,CAACkB,eAAe,EAAET,YAAY,CAAC;;EAEnE;EACAR,SAAS,CAAC,MAAM;IACZ,MAAM0B,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC/D,IAAIF,aAAa,EAAE;MACfD,QAAQ,CAAC;QAAEL,IAAI,EAAEhB,gBAAgB,CAACG,aAAa;QAAEc,OAAO,EAAEK;MAAc,CAAC,CAAC;IAC9E;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1B,SAAS,CAAC,MAAM;IACZ2B,YAAY,CAACE,OAAO,CAAC,mBAAmB,EAAEX,KAAK,CAACT,eAAe,CAAC;EACpE,CAAC,EAAE,CAACS,KAAK,CAACT,eAAe,CAAC,CAAC;;EAE3B;EACA,MAAMqB,WAAW,GAAIC,YAAY,IAAK;IAClCN,QAAQ,CAAC;MAAEL,IAAI,EAAEhB,gBAAgB,CAACC,YAAY;MAAEgB,OAAO,EAAEU;IAAa,CAAC,CAAC;EAC5E,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAGhB,KAAK,CAACR,aAAa,CAACQ,KAAK,CAACT,eAAe,CAAC,IAAI,CAAC;IAC5D,OAAOwB,UAAU,GAAGC,IAAI;EAC5B,CAAC;;EAED;EACA,MAAMC,WAAW,GAAIF,UAAU,IAAK;IAChC,MAAMG,cAAc,GAAGJ,YAAY,CAACC,UAAU,CAAC;IAC/C,MAAMI,QAAQ,GAAGnB,KAAK,CAACL,UAAU,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzB,IAAI,KAAKI,KAAK,CAACT,eAAe,CAAC;IAC7E,MAAMO,MAAM,GAAGqB,QAAQ,GAAGA,QAAQ,CAACrB,MAAM,GAAG,GAAG;;IAE/C;IACA,IAAIE,KAAK,CAACT,eAAe,KAAK,KAAK,EAAE;MACjC,OAAO,GAAGO,MAAM,GAAGoB,cAAc,CAACI,cAAc,CAAC,OAAO,EAAE;QACtDC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MAC3B,CAAC,CAAC,EAAE;IACR,CAAC,MAAM;MACH,OAAO,GAAG1B,MAAM,GAAGoB,cAAc,CAACI,cAAc,CAAC,OAAO,EAAE;QACtDC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE;MAC3B,CAAC,CAAC,EAAE;IACR;EACJ,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,OAAOzB,KAAK,CAACL,UAAU,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzB,IAAI,KAAKI,KAAK,CAACT,eAAe,CAAC,IAAIS,KAAK,CAACL,UAAU,CAAC,CAAC,CAAC;EAC9F,CAAC;;EAED;EACA,MAAM+B,mBAAmB,GAAIC,KAAK,IAAK;IACnCpB,QAAQ,CAAC;MAAEL,IAAI,EAAEhB,gBAAgB,CAACE,kBAAkB;MAAEe,OAAO,EAAEwB;IAAM,CAAC,CAAC;EAC3E,CAAC;EAED,MAAMC,KAAK,GAAG;IACV;IACArC,eAAe,EAAES,KAAK,CAACT,eAAe;IACtCI,UAAU,EAAEK,KAAK,CAACL,UAAU;IAC5BH,aAAa,EAAEQ,KAAK,CAACR,aAAa;IAElC;IACAoB,WAAW;IACXc,mBAAmB;IAEnB;IACAZ,YAAY;IACZG,WAAW;IACXQ;EACJ,CAAC;EAED,oBACIzC,OAAA,CAACC,eAAe,CAAC4C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvB,QAAA,EAClCA;EAAQ;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAEnC,CAAC;;AAED;AAAA3B,EAAA,CAhFaF,gBAAgB;AAAA8B,EAAA,GAAhB9B,gBAAgB;AAiF7B,OAAO,MAAM+B,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAMC,OAAO,GAAGzD,UAAU,CAACK,eAAe,CAAC;EAC3C,IAAI,CAACoD,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACzE;EACA,OAAOD,OAAO;AAClB,CAAC;AAACD,GAAA,CANWD,WAAW;AAQxB,eAAelD,eAAe;AAAC,IAAAiD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}