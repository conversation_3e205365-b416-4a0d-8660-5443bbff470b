const axios = require('axios');

async function testBackendHealth() {
  console.log('🔍 Testing Backend Server Health...');
  
  try {
    // Test health endpoint
    const healthResponse = await axios.get('http://localhost:8000/health');
    console.log('✅ Health Check:', healthResponse.data);
    
    // Test login endpoint
    const loginResponse = await axios.post('http://localhost:8000/api/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Login Test: Successful');
      const token = loginResponse.data.data.token;
      
      // Test activity logs endpoint
      const logsResponse = await axios.get('http://localhost:8000/api/admin/activity-logs?limit=5', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (logsResponse.data.success) {
        console.log('✅ Activity Logs API: Working');
        console.log(`   Found ${logsResponse.data.data.logs.length} activity logs`);
      } else {
        console.log('❌ Activity Logs API: Failed');
      }
      
      // Test activity stats endpoint
      const statsResponse = await axios.get('http://localhost:8000/api/admin/activity-logs/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (statsResponse.data.success) {
        console.log('✅ Activity Stats API: Working');
        console.log(`   Total activities: ${statsResponse.data.data.overview.totalActivities}`);
      } else {
        console.log('❌ Activity Stats API: Failed');
      }
      
    } else {
      console.log('❌ Login Test: Failed');
    }
    
    console.log('\n🎉 Backend server is fully operational!');
    
  } catch (error) {
    console.error('❌ Backend test failed:', error.response?.data?.message || error.message);
  }
}

testBackendHealth();
