{"ast": null, "code": "import { ShaderMaterial, Vector3, Color, Vector2 } from 'three';\nclass SpotLightMaterial extends ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        depth: {\n          value: null\n        },\n        opacity: {\n          value: 1\n        },\n        attenuation: {\n          value: 2.5\n        },\n        anglePower: {\n          value: 12\n        },\n        spotPosition: {\n          value: new Vector3(0, 0, 0)\n        },\n        lightColor: {\n          value: new Color('white')\n        },\n        cameraNear: {\n          value: 0\n        },\n        cameraFar: {\n          value: 1\n        },\n        resolution: {\n          value: new Vector2(0, 0)\n        }\n      },\n      transparent: true,\n      depthWrite: false,\n      vertexShader: /* glsl */\n      `\n      varying vec3 vNormal;\n      varying vec3 vWorldPosition;\n      varying float vViewZ;\n      varying float vIntensity;\n      uniform vec3 spotPosition;\n      uniform float attenuation;      \n\n      void main() {\n        // compute intensity\n        vNormal = normalize( normalMatrix * normal );\n        vec4 worldPosition\t= modelMatrix * vec4( position, 1.0 );\n        vWorldPosition = worldPosition.xyz;\n        vec4 viewPosition = viewMatrix * worldPosition;\n        vViewZ = viewPosition.z;\n        float intensity\t= distance(worldPosition.xyz, spotPosition) / attenuation;\n        intensity\t= 1.0 - clamp(intensity, 0.0, 1.0);\n        vIntensity = intensity;        \n        // set gl_Position\n        gl_Position\t= projectionMatrix * viewPosition;\n\n      }`,\n      fragmentShader: /* glsl */\n      `\n      #include <packing>\n\n      varying vec3 vNormal;\n      varying vec3 vWorldPosition;\n      uniform vec3 lightColor;\n      uniform vec3 spotPosition;\n      uniform float attenuation;\n      uniform float anglePower;\n      uniform sampler2D depth;\n      uniform vec2 resolution;\n      uniform float cameraNear;\n      uniform float cameraFar;\n      varying float vViewZ;\n      varying float vIntensity;\n      uniform float opacity;\n\n      float readDepth( sampler2D depthSampler, vec2 coord ) {\n        float fragCoordZ = texture2D( depthSampler, coord ).x;\n        float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n        return viewZ;\n      }\n\n      void main() {\n        float d = 1.0;\n        bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n        if (isSoft) {\n          vec2 sUv = gl_FragCoord.xy / resolution;\n          d = readDepth(depth, sUv);\n        }\n        float intensity = vIntensity;\n        vec3 normal\t= vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n        float angleIntensity\t= pow( dot(normal, vec3(0.0, 0.0, 1.0)), anglePower );\n        intensity\t*= angleIntensity;\n        // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n        if (isSoft) {\n          intensity\t*= smoothstep(0., 1., vViewZ - d);\n        }\n        gl_FragColor = vec4(lightColor, intensity * opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <encodings_fragment>\n      }`\n    });\n  }\n}\nexport { SpotLightMaterial };", "map": {"version": 3, "names": ["ShaderMaterial", "Vector3", "Color", "Vector2", "SpotLightMaterial", "constructor", "uniforms", "depth", "value", "opacity", "attenuation", "anglePower", "spotPosition", "lightColor", "cameraNear", "cameraFar", "resolution", "transparent", "depthWrite", "vertexShader", "fragmentShader"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/materials/SpotLightMaterial.js"], "sourcesContent": ["import { ShaderMaterial, Vector3, Color, Vector2 } from 'three';\n\nclass SpotLightMaterial extends ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        depth: {\n          value: null\n        },\n        opacity: {\n          value: 1\n        },\n        attenuation: {\n          value: 2.5\n        },\n        anglePower: {\n          value: 12\n        },\n        spotPosition: {\n          value: new Vector3(0, 0, 0)\n        },\n        lightColor: {\n          value: new Color('white')\n        },\n        cameraNear: {\n          value: 0\n        },\n        cameraFar: {\n          value: 1\n        },\n        resolution: {\n          value: new Vector2(0, 0)\n        }\n      },\n      transparent: true,\n      depthWrite: false,\n      vertexShader:\n      /* glsl */\n      `\n      varying vec3 vNormal;\n      varying vec3 vWorldPosition;\n      varying float vViewZ;\n      varying float vIntensity;\n      uniform vec3 spotPosition;\n      uniform float attenuation;      \n\n      void main() {\n        // compute intensity\n        vNormal = normalize( normalMatrix * normal );\n        vec4 worldPosition\t= modelMatrix * vec4( position, 1.0 );\n        vWorldPosition = worldPosition.xyz;\n        vec4 viewPosition = viewMatrix * worldPosition;\n        vViewZ = viewPosition.z;\n        float intensity\t= distance(worldPosition.xyz, spotPosition) / attenuation;\n        intensity\t= 1.0 - clamp(intensity, 0.0, 1.0);\n        vIntensity = intensity;        \n        // set gl_Position\n        gl_Position\t= projectionMatrix * viewPosition;\n\n      }`,\n      fragmentShader:\n      /* glsl */\n      `\n      #include <packing>\n\n      varying vec3 vNormal;\n      varying vec3 vWorldPosition;\n      uniform vec3 lightColor;\n      uniform vec3 spotPosition;\n      uniform float attenuation;\n      uniform float anglePower;\n      uniform sampler2D depth;\n      uniform vec2 resolution;\n      uniform float cameraNear;\n      uniform float cameraFar;\n      varying float vViewZ;\n      varying float vIntensity;\n      uniform float opacity;\n\n      float readDepth( sampler2D depthSampler, vec2 coord ) {\n        float fragCoordZ = texture2D( depthSampler, coord ).x;\n        float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n        return viewZ;\n      }\n\n      void main() {\n        float d = 1.0;\n        bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n        if (isSoft) {\n          vec2 sUv = gl_FragCoord.xy / resolution;\n          d = readDepth(depth, sUv);\n        }\n        float intensity = vIntensity;\n        vec3 normal\t= vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n        float angleIntensity\t= pow( dot(normal, vec3(0.0, 0.0, 1.0)), anglePower );\n        intensity\t*= angleIntensity;\n        // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n        if (isSoft) {\n          intensity\t*= smoothstep(0., 1., vViewZ - d);\n        }\n        gl_FragColor = vec4(lightColor, intensity * opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <encodings_fragment>\n      }`\n    });\n  }\n\n}\n\nexport { SpotLightMaterial };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAQ,OAAO;AAE/D,MAAMC,iBAAiB,SAASJ,cAAc,CAAC;EAC7CK,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC;MACJC,QAAQ,EAAE;QACRC,KAAK,EAAE;UACLC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPD,KAAK,EAAE;QACT,CAAC;QACDE,WAAW,EAAE;UACXF,KAAK,EAAE;QACT,CAAC;QACDG,UAAU,EAAE;UACVH,KAAK,EAAE;QACT,CAAC;QACDI,YAAY,EAAE;UACZJ,KAAK,EAAE,IAAIP,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5B,CAAC;QACDY,UAAU,EAAE;UACVL,KAAK,EAAE,IAAIN,KAAK,CAAC,OAAO;QAC1B,CAAC;QACDY,UAAU,EAAE;UACVN,KAAK,EAAE;QACT,CAAC;QACDO,SAAS,EAAE;UACTP,KAAK,EAAE;QACT,CAAC;QACDQ,UAAU,EAAE;UACVR,KAAK,EAAE,IAAIL,OAAO,CAAC,CAAC,EAAE,CAAC;QACzB;MACF,CAAC;MACDc,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EACZ;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;MACFC,cAAc,EACd;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,CAAC,CAAC;EACJ;AAEF;AAEA,SAAShB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}