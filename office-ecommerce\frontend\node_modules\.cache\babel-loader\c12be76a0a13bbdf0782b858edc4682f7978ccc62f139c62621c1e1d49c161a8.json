{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass Timer {\n  constructor() {\n    __publicField(this, \"_previousTime\");\n    __publicField(this, \"_currentTime\");\n    __publicField(this, \"_delta\");\n    __publicField(this, \"_elapsed\");\n    __publicField(this, \"_timescale\");\n    __publicField(this, \"_useFixedDelta\");\n    __publicField(this, \"_fixedDelta\");\n    __publicField(this, \"_usePageVisibilityAPI\");\n    __publicField(this, \"_pageVisibilityHandler\");\n    this._previousTime = 0;\n    this._currentTime = 0;\n    this._delta = 0;\n    this._elapsed = 0;\n    this._timescale = 1;\n    this._useFixedDelta = false;\n    this._fixedDelta = 16.67;\n    this._usePageVisibilityAPI = typeof document !== \"undefined\" && document.hidden !== void 0;\n  }\n  // https://github.com/mrdoob/three.js/issues/20575\n  // use Page Visibility API to avoid large time delta values\n  connect() {\n    if (this._usePageVisibilityAPI) {\n      this._pageVisibilityHandler = handleVisibilityChange.bind(this);\n      document.addEventListener(\"visibilitychange\", this._pageVisibilityHandler, false);\n    }\n    return this;\n  }\n  dispose() {\n    if (this._usePageVisibilityAPI && this._pageVisibilityHandler) {\n      document.removeEventListener(\"visibilitychange\", this._pageVisibilityHandler);\n    }\n    return this;\n  }\n  disableFixedDelta() {\n    this._useFixedDelta = false;\n    return this;\n  }\n  enableFixedDelta() {\n    this._useFixedDelta = true;\n    return this;\n  }\n  getDelta() {\n    return this._delta / 1e3;\n  }\n  getElapsedTime() {\n    return this._elapsed / 1e3;\n  }\n  getFixedDelta() {\n    return this._fixedDelta / 1e3;\n  }\n  getTimescale() {\n    return this._timescale;\n  }\n  reset() {\n    this._currentTime = this._now();\n    return this;\n  }\n  setFixedDelta(fixedDelta) {\n    this._fixedDelta = fixedDelta * 1e3;\n    return this;\n  }\n  setTimescale(timescale) {\n    this._timescale = timescale;\n    return this;\n  }\n  update() {\n    if (this._useFixedDelta === true) {\n      this._delta = this._fixedDelta;\n    } else {\n      this._previousTime = this._currentTime;\n      this._currentTime = this._now();\n      this._delta = this._currentTime - this._previousTime;\n    }\n    this._delta *= this._timescale;\n    this._elapsed += this._delta;\n    return this;\n  }\n  // For THREE.Clock backward compatibility\n  get elapsedTime() {\n    return this.getElapsedTime();\n  }\n  // private\n  _now() {\n    return (typeof performance === \"undefined\" ? Date : performance).now();\n  }\n}\nfunction handleVisibilityChange() {\n  if (document.hidden === false) this.reset();\n}\nexport { Timer };", "map": {"version": 3, "names": ["Timer", "constructor", "__publicField", "_previousTime", "_currentTime", "_delta", "_elapsed", "_timescale", "_useFixedDelta", "_fixedDel<PERSON>", "_usePageVisibilityAPI", "document", "hidden", "connect", "_pageVisibilityHandler", "handleVisibilityChange", "bind", "addEventListener", "dispose", "removeEventListener", "disableFix<PERSON><PERSON><PERSON><PERSON>", "enableFixedDelta", "<PERSON><PERSON><PERSON><PERSON>", "getElapsedTime", "getFixedDelta", "getTimescale", "reset", "_now", "setFixedDel<PERSON>", "fixedDelta", "setTimescale", "timescale", "update", "elapsedTime", "performance", "Date", "now"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\misc\\Timer.ts"], "sourcesContent": ["class Timer {\n  private _previousTime: number\n  private _currentTime: number\n  private _delta: number\n  private _elapsed: number\n  private _timescale: number\n  private _useFixedDelta: boolean\n  private _fixedDelta: number\n  private _usePageVisibilityAPI: boolean\n  private _pageVisibilityHandler: ((...args: any[]) => void) | undefined\n\n  constructor() {\n    this._previousTime = 0\n    this._currentTime = 0\n    this._delta = 0\n    this._elapsed = 0\n    this._timescale = 1\n    this._useFixedDelta = false\n    this._fixedDelta = 16.67 // ms, corresponds to approx. 60 FPS\n    this._usePageVisibilityAPI = typeof document !== 'undefined' && document.hidden !== undefined\n  }\n\n  // https://github.com/mrdoob/three.js/issues/20575\n  // use Page Visibility API to avoid large time delta values\n  connect(): this {\n    if (this._usePageVisibilityAPI) {\n      this._pageVisibilityHandler = handleVisibilityChange.bind(this)\n      document.addEventListener('visibilitychange', this._pageVisibilityHandler, false)\n    }\n    return this\n  }\n\n  dispose(): this {\n    if (this._usePageVisibilityAPI && this._pageVisibilityHandler) {\n      document.removeEventListener('visibilitychange', this._pageVisibilityHandler)\n    }\n    return this\n  }\n\n  disableFixedDelta(): this {\n    this._useFixedDelta = false\n    return this\n  }\n\n  enableFixedDelta(): this {\n    this._useFixedDelta = true\n    return this\n  }\n\n  getDelta(): number {\n    return this._delta / 1000\n  }\n\n  getElapsedTime(): number {\n    return this._elapsed / 1000\n  }\n\n  getFixedDelta(): number {\n    return this._fixedDelta / 1000\n  }\n\n  getTimescale(): number {\n    return this._timescale\n  }\n\n  reset(): this {\n    this._currentTime = this._now()\n    return this\n  }\n\n  setFixedDelta(fixedDelta: number): this {\n    this._fixedDelta = fixedDelta * 1000\n    return this\n  }\n\n  setTimescale(timescale: number): this {\n    this._timescale = timescale\n    return this\n  }\n\n  update(): this {\n    if (this._useFixedDelta === true) {\n      this._delta = this._fixedDelta\n    } else {\n      this._previousTime = this._currentTime\n      this._currentTime = this._now()\n      this._delta = this._currentTime - this._previousTime\n    }\n    this._delta *= this._timescale\n    this._elapsed += this._delta // _elapsed is the accumulation of all previous deltas\n    return this\n  }\n\n  // For THREE.Clock backward compatibility\n  get elapsedTime(): number {\n    return this.getElapsedTime()\n  }\n\n  // private\n\n  private _now(): number {\n    return (typeof performance === 'undefined' ? Date : performance).now()\n  }\n}\n\nfunction handleVisibilityChange(this: Timer): void {\n  if (document.hidden === false) this.reset()\n}\n\nexport { Timer }\n"], "mappings": ";;;;;;;;;;;AAAA,MAAMA,KAAA,CAAM;EAWVC,YAAA,EAAc;IAVNC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAGN,KAAKC,aAAA,GAAgB;IACrB,KAAKC,YAAA,GAAe;IACpB,KAAKC,MAAA,GAAS;IACd,KAAKC,QAAA,GAAW;IAChB,KAAKC,UAAA,GAAa;IAClB,KAAKC,cAAA,GAAiB;IACtB,KAAKC,WAAA,GAAc;IACnB,KAAKC,qBAAA,GAAwB,OAAOC,QAAA,KAAa,eAAeA,QAAA,CAASC,MAAA,KAAW;EACtF;EAAA;EAAA;EAIAC,QAAA,EAAgB;IACd,IAAI,KAAKH,qBAAA,EAAuB;MACzB,KAAAI,sBAAA,GAAyBC,sBAAA,CAAuBC,IAAA,CAAK,IAAI;MAC9DL,QAAA,CAASM,gBAAA,CAAiB,oBAAoB,KAAKH,sBAAA,EAAwB,KAAK;IAClF;IACO;EACT;EAEAI,QAAA,EAAgB;IACV,SAAKR,qBAAA,IAAyB,KAAKI,sBAAA,EAAwB;MACpDH,QAAA,CAAAQ,mBAAA,CAAoB,oBAAoB,KAAKL,sBAAsB;IAC9E;IACO;EACT;EAEAM,kBAAA,EAA0B;IACxB,KAAKZ,cAAA,GAAiB;IACf;EACT;EAEAa,iBAAA,EAAyB;IACvB,KAAKb,cAAA,GAAiB;IACf;EACT;EAEAc,SAAA,EAAmB;IACjB,OAAO,KAAKjB,MAAA,GAAS;EACvB;EAEAkB,eAAA,EAAyB;IACvB,OAAO,KAAKjB,QAAA,GAAW;EACzB;EAEAkB,cAAA,EAAwB;IACtB,OAAO,KAAKf,WAAA,GAAc;EAC5B;EAEAgB,aAAA,EAAuB;IACrB,OAAO,KAAKlB,UAAA;EACd;EAEAmB,MAAA,EAAc;IACP,KAAAtB,YAAA,GAAe,KAAKuB,IAAA;IAClB;EACT;EAEAC,cAAcC,UAAA,EAA0B;IACtC,KAAKpB,WAAA,GAAcoB,UAAA,GAAa;IACzB;EACT;EAEAC,aAAaC,SAAA,EAAyB;IACpC,KAAKxB,UAAA,GAAawB,SAAA;IACX;EACT;EAEAC,OAAA,EAAe;IACT,SAAKxB,cAAA,KAAmB,MAAM;MAChC,KAAKH,MAAA,GAAS,KAAKI,WAAA;IAAA,OACd;MACL,KAAKN,aAAA,GAAgB,KAAKC,YAAA;MACrB,KAAAA,YAAA,GAAe,KAAKuB,IAAA;MACpB,KAAAtB,MAAA,GAAS,KAAKD,YAAA,GAAe,KAAKD,aAAA;IACzC;IACA,KAAKE,MAAA,IAAU,KAAKE,UAAA;IACpB,KAAKD,QAAA,IAAY,KAAKD,MAAA;IACf;EACT;EAAA;EAGA,IAAI4B,YAAA,EAAsB;IACxB,OAAO,KAAKV,cAAA;EACd;EAAA;EAIQI,KAAA,EAAe;IACrB,QAAQ,OAAOO,WAAA,KAAgB,cAAcC,IAAA,GAAOD,WAAA,EAAaE,GAAA;EACnE;AACF;AAEA,SAASrB,uBAAA,EAA0C;EACjD,IAAIJ,QAAA,CAASC,MAAA,KAAW,OAAO,KAAKc,KAAA,CAAM;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}