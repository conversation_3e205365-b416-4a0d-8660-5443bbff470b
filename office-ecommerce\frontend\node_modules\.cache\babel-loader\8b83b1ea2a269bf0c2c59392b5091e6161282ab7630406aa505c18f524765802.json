{"ast": null, "code": "// WebSocket service for real-time communication\nimport { io } from 'socket.io-client';\nimport apiConfig from './apiConfig';\nclass WebSocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.listeners = new Map();\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n  }\n  async connect(token) {\n    if (this.socket) {\n      this.disconnect();\n    }\n    try {\n      // Check if we're in mock mode\n      if (localStorage.getItem('mockMode') === 'true') {\n        console.log('🔄 Mock mode detected, skipping WebSocket connection');\n        return Promise.reject(new Error('WebSocket disabled in mock mode'));\n      }\n\n      // Check if real-time updates are enabled\n      if (!apiConfig.isFeatureEnabled('realTimeUpdates')) {\n        console.warn('⚠️ Real-time updates are disabled');\n        return Promise.reject(new Error('Real-time updates are disabled'));\n      }\n      const serverUrl = apiConfig.getWebSocketUrl();\n      if (apiConfig.debugMode) {\n        console.log('🔌 Connecting to WebSocket:', serverUrl);\n      }\n      this.socket = io(serverUrl, {\n        auth: {\n          token: token\n        },\n        transports: ['websocket', 'polling'],\n        timeout: 20000,\n        forceNew: true\n      });\n      this.setupEventListeners();\n      return new Promise((resolve, reject) => {\n        const timeout = setTimeout(() => {\n          reject(new Error('Connection timeout'));\n        }, 10000);\n        this.socket.on('connected', data => {\n          clearTimeout(timeout);\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n          console.log('WebSocket connected:', data);\n          resolve(data);\n        });\n        this.socket.on('connect_error', error => {\n          clearTimeout(timeout);\n          console.error('WebSocket connection error:', error);\n          reject(error);\n        });\n      });\n    } catch (error) {\n      console.error('Failed to connect WebSocket:', error);\n      throw error;\n    }\n  }\n  setupEventListeners() {\n    if (!this.socket) return;\n    this.socket.on('connect', () => {\n      console.log('WebSocket connected to server');\n      this.isConnected = true;\n      this.reconnectAttempts = 0;\n    });\n    this.socket.on('disconnect', reason => {\n      console.log('WebSocket disconnected:', reason);\n      this.isConnected = false;\n      if (reason === 'io server disconnect') {\n        this.attemptReconnect();\n      }\n    });\n    this.socket.on('connect_error', error => {\n      console.error('WebSocket connection error:', error);\n      this.isConnected = false;\n      this.attemptReconnect();\n    });\n\n    // Listen for real-time events\n    this.socket.on('inventoryUpdated', data => {\n      this.emit('inventoryUpdated', data);\n    });\n    this.socket.on('lowStockAlert', data => {\n      this.emit('lowStockAlert', data);\n    });\n    this.socket.on('orderUpdated', data => {\n      this.emit('orderUpdated', data);\n    });\n    this.socket.on('dashboardUpdated', data => {\n      this.emit('dashboardUpdated', data);\n    });\n    this.socket.on('notification', data => {\n      this.emit('notification', data);\n    });\n\n    // Product management events\n    this.socket.on('productCreated', data => {\n      this.emit('productCreated', data);\n    });\n    this.socket.on('productUpdated', data => {\n      this.emit('productUpdated', data);\n    });\n    this.socket.on('productDeleted', data => {\n      this.emit('productDeleted', data);\n    });\n    this.socket.on('productFileUploaded', data => {\n      this.emit('productFileUploaded', data);\n    });\n    this.socket.on('productStatusChanged', data => {\n      this.emit('productStatusChanged', data);\n    });\n  }\n  attemptReconnect() {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      const delay = Math.pow(2, this.reconnectAttempts) * 1000;\n      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      setTimeout(() => {\n        if (this.socket) {\n          this.socket.connect();\n        }\n      }, delay);\n    } else {\n      console.error('Max reconnection attempts reached');\n    }\n  }\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n    this.isConnected = false;\n    this.listeners.clear();\n  }\n  subscribeToInventory() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'inventory');\n    }\n  }\n  subscribeToOrders() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'orders');\n    }\n  }\n  subscribeToDashboard() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'dashboard');\n    }\n  }\n  subscribeToProducts() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'products');\n    }\n  }\n\n  // Product management specific methods\n  notifyProductCreated(productData) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productCreated', productData);\n    }\n    // Also emit locally for immediate UI updates\n    this.emit('productCreated', productData);\n  }\n  notifyProductUpdated(productData) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productUpdated', productData);\n    }\n    this.emit('productUpdated', productData);\n  }\n  notifyProductDeleted(productId) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productDeleted', {\n        productId\n      });\n    }\n    this.emit('productDeleted', {\n      productId\n    });\n  }\n  notifyProductFileUploaded(productId, fileData) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productFileUploaded', {\n        productId,\n        fileData\n      });\n    }\n    this.emit('productFileUploaded', {\n      productId,\n      fileData\n    });\n  }\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, []);\n    }\n    this.listeners.get(event).push(callback);\n  }\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      const callbacks = this.listeners.get(event);\n      const index = callbacks.indexOf(callback);\n      if (index > -1) {\n        callbacks.splice(index, 1);\n      }\n    }\n  }\n  emit(event, data) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error('Error in event callback:', error);\n        }\n      });\n    }\n  }\n  sendEvent(event, data) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit(event, data);\n    }\n  }\n  showNotification(title, message, type = 'info') {\n    this.emit('notification', {\n      id: Date.now(),\n      title,\n      message,\n      type,\n      timestamp: new Date().toISOString()\n    });\n  }\n  getConnectionStatus() {\n    if (!this.socket) return 'disconnected';\n    if (this.isConnected) return 'connected';\n    if (this.socket.connecting) return 'connecting';\n    return 'error';\n  }\n}\nconst websocketService = new WebSocketService();\nexport default websocketService;", "map": {"version": 3, "names": ["io", "apiConfig", "WebSocketService", "constructor", "socket", "isConnected", "listeners", "Map", "reconnectAttempts", "maxReconnectAttempts", "connect", "token", "disconnect", "localStorage", "getItem", "console", "log", "Promise", "reject", "Error", "isFeatureEnabled", "warn", "serverUrl", "getWebSocketUrl", "debugMode", "auth", "transports", "timeout", "forceNew", "setupEventListeners", "resolve", "setTimeout", "on", "data", "clearTimeout", "error", "reason", "attemptReconnect", "emit", "delay", "Math", "pow", "clear", "subscribeToInventory", "subscribeToOrders", "subscribeToDashboard", "subscribeToProducts", "notifyProductCreated", "productData", "notifyProductUpdated", "notifyProductDeleted", "productId", "notifyProductFileUploaded", "fileData", "event", "callback", "has", "set", "get", "push", "off", "callbacks", "index", "indexOf", "splice", "for<PERSON>ach", "sendEvent", "showNotification", "title", "message", "type", "id", "Date", "now", "timestamp", "toISOString", "getConnectionStatus", "connecting", "websocketService"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/websocketService.js"], "sourcesContent": ["// WebSocket service for real-time communication\nimport { io } from 'socket.io-client';\nimport apiConfig from './apiConfig';\n\nclass WebSocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.listeners = new Map();\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n  }\n\n  async connect(token) {\n    if (this.socket) {\n      this.disconnect();\n    }\n\n    try {\n      // Check if we're in mock mode\n      if (localStorage.getItem('mockMode') === 'true') {\n        console.log('🔄 Mock mode detected, skipping WebSocket connection');\n        return Promise.reject(new Error('WebSocket disabled in mock mode'));\n      }\n\n      // Check if real-time updates are enabled\n      if (!apiConfig.isFeatureEnabled('realTimeUpdates')) {\n        console.warn('⚠️ Real-time updates are disabled');\n        return Promise.reject(new Error('Real-time updates are disabled'));\n      }\n\n      const serverUrl = apiConfig.getWebSocketUrl();\n\n      if (apiConfig.debugMode) {\n        console.log('🔌 Connecting to WebSocket:', serverUrl);\n      }\n\n      this.socket = io(serverUrl, {\n        auth: {\n          token: token\n        },\n        transports: ['websocket', 'polling'],\n        timeout: 20000,\n        forceNew: true\n      });\n\n      this.setupEventListeners();\n      \n      return new Promise((resolve, reject) => {\n        const timeout = setTimeout(() => {\n          reject(new Error('Connection timeout'));\n        }, 10000);\n\n        this.socket.on('connected', (data) => {\n          clearTimeout(timeout);\n          this.isConnected = true;\n          this.reconnectAttempts = 0;\n          console.log('WebSocket connected:', data);\n          resolve(data);\n        });\n\n        this.socket.on('connect_error', (error) => {\n          clearTimeout(timeout);\n          console.error('WebSocket connection error:', error);\n          reject(error);\n        });\n      });\n    } catch (error) {\n      console.error('Failed to connect WebSocket:', error);\n      throw error;\n    }\n  }\n\n  setupEventListeners() {\n    if (!this.socket) return;\n\n    this.socket.on('connect', () => {\n      console.log('WebSocket connected to server');\n      this.isConnected = true;\n      this.reconnectAttempts = 0;\n    });\n\n    this.socket.on('disconnect', (reason) => {\n      console.log('WebSocket disconnected:', reason);\n      this.isConnected = false;\n      \n      if (reason === 'io server disconnect') {\n        this.attemptReconnect();\n      }\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('WebSocket connection error:', error);\n      this.isConnected = false;\n      this.attemptReconnect();\n    });\n\n    // Listen for real-time events\n    this.socket.on('inventoryUpdated', (data) => {\n      this.emit('inventoryUpdated', data);\n    });\n\n    this.socket.on('lowStockAlert', (data) => {\n      this.emit('lowStockAlert', data);\n    });\n\n    this.socket.on('orderUpdated', (data) => {\n      this.emit('orderUpdated', data);\n    });\n\n    this.socket.on('dashboardUpdated', (data) => {\n      this.emit('dashboardUpdated', data);\n    });\n\n    this.socket.on('notification', (data) => {\n      this.emit('notification', data);\n    });\n\n    // Product management events\n    this.socket.on('productCreated', (data) => {\n      this.emit('productCreated', data);\n    });\n\n    this.socket.on('productUpdated', (data) => {\n      this.emit('productUpdated', data);\n    });\n\n    this.socket.on('productDeleted', (data) => {\n      this.emit('productDeleted', data);\n    });\n\n    this.socket.on('productFileUploaded', (data) => {\n      this.emit('productFileUploaded', data);\n    });\n\n    this.socket.on('productStatusChanged', (data) => {\n      this.emit('productStatusChanged', data);\n    });\n  }\n\n  attemptReconnect() {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++;\n      const delay = Math.pow(2, this.reconnectAttempts) * 1000;\n      \n      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      \n      setTimeout(() => {\n        if (this.socket) {\n          this.socket.connect();\n        }\n      }, delay);\n    } else {\n      console.error('Max reconnection attempts reached');\n    }\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n    this.isConnected = false;\n    this.listeners.clear();\n  }\n\n  subscribeToInventory() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'inventory');\n    }\n  }\n\n  subscribeToOrders() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'orders');\n    }\n  }\n\n  subscribeToDashboard() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'dashboard');\n    }\n  }\n\n  subscribeToProducts() {\n    if (this.socket) {\n      this.socket.emit('subscribe', 'products');\n    }\n  }\n\n  // Product management specific methods\n  notifyProductCreated(productData) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productCreated', productData);\n    }\n    // Also emit locally for immediate UI updates\n    this.emit('productCreated', productData);\n  }\n\n  notifyProductUpdated(productData) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productUpdated', productData);\n    }\n    this.emit('productUpdated', productData);\n  }\n\n  notifyProductDeleted(productId) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productDeleted', { productId });\n    }\n    this.emit('productDeleted', { productId });\n  }\n\n  notifyProductFileUploaded(productId, fileData) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit('productFileUploaded', { productId, fileData });\n    }\n    this.emit('productFileUploaded', { productId, fileData });\n  }\n\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, []);\n    }\n    this.listeners.get(event).push(callback);\n  }\n\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      const callbacks = this.listeners.get(event);\n      const index = callbacks.indexOf(callback);\n      if (index > -1) {\n        callbacks.splice(index, 1);\n      }\n    }\n  }\n\n  emit(event, data) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error('Error in event callback:', error);\n        }\n      });\n    }\n  }\n\n  sendEvent(event, data) {\n    if (this.socket && this.isConnected) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  showNotification(title, message, type = 'info') {\n    this.emit('notification', {\n      id: Date.now(),\n      title,\n      message,\n      type,\n      timestamp: new Date().toISOString()\n    });\n  }\n\n  getConnectionStatus() {\n    if (!this.socket) return 'disconnected';\n    if (this.isConnected) return 'connected';\n    if (this.socket.connecting) return 'connecting';\n    return 'error';\n  }\n}\n\nconst websocketService = new WebSocketService();\nexport default websocketService;\n"], "mappings": "AAAA;AACA,SAASA,EAAE,QAAQ,kBAAkB;AACrC,OAAOC,SAAS,MAAM,aAAa;AAEnC,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;EAC/B;EAEA,MAAMC,OAAOA,CAACC,KAAK,EAAE;IACnB,IAAI,IAAI,CAACP,MAAM,EAAE;MACf,IAAI,CAACQ,UAAU,CAAC,CAAC;IACnB;IAEA,IAAI;MACF;MACA,IAAIC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,MAAM,EAAE;QAC/CC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnE,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,iCAAiC,CAAC,CAAC;MACrE;;MAEA;MACA,IAAI,CAAClB,SAAS,CAACmB,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;QAClDL,OAAO,CAACM,IAAI,CAAC,mCAAmC,CAAC;QACjD,OAAOJ,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,gCAAgC,CAAC,CAAC;MACpE;MAEA,MAAMG,SAAS,GAAGrB,SAAS,CAACsB,eAAe,CAAC,CAAC;MAE7C,IAAItB,SAAS,CAACuB,SAAS,EAAE;QACvBT,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEM,SAAS,CAAC;MACvD;MAEA,IAAI,CAAClB,MAAM,GAAGJ,EAAE,CAACsB,SAAS,EAAE;QAC1BG,IAAI,EAAE;UACJd,KAAK,EAAEA;QACT,CAAC;QACDe,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAE1B,OAAO,IAAIZ,OAAO,CAAC,CAACa,OAAO,EAAEZ,MAAM,KAAK;QACtC,MAAMS,OAAO,GAAGI,UAAU,CAAC,MAAM;UAC/Bb,MAAM,CAAC,IAAIC,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACzC,CAAC,EAAE,KAAK,CAAC;QAET,IAAI,CAACf,MAAM,CAAC4B,EAAE,CAAC,WAAW,EAAGC,IAAI,IAAK;UACpCC,YAAY,CAACP,OAAO,CAAC;UACrB,IAAI,CAACtB,WAAW,GAAG,IAAI;UACvB,IAAI,CAACG,iBAAiB,GAAG,CAAC;UAC1BO,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEiB,IAAI,CAAC;UACzCH,OAAO,CAACG,IAAI,CAAC;QACf,CAAC,CAAC;QAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,eAAe,EAAGG,KAAK,IAAK;UACzCD,YAAY,CAACP,OAAO,CAAC;UACrBZ,OAAO,CAACoB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnDjB,MAAM,CAACiB,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;EAEAN,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACzB,MAAM,EAAE;IAElB,IAAI,CAACA,MAAM,CAAC4B,EAAE,CAAC,SAAS,EAAE,MAAM;MAC9BjB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAACX,WAAW,GAAG,IAAI;MACvB,IAAI,CAACG,iBAAiB,GAAG,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACJ,MAAM,CAAC4B,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;MACvCrB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoB,MAAM,CAAC;MAC9C,IAAI,CAAC/B,WAAW,GAAG,KAAK;MAExB,IAAI+B,MAAM,KAAK,sBAAsB,EAAE;QACrC,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;IAEF,IAAI,CAACjC,MAAM,CAAC4B,EAAE,CAAC,eAAe,EAAGG,KAAK,IAAK;MACzCpB,OAAO,CAACoB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,CAAC9B,WAAW,GAAG,KAAK;MACxB,IAAI,CAACgC,gBAAgB,CAAC,CAAC;IACzB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACjC,MAAM,CAAC4B,EAAE,CAAC,kBAAkB,EAAGC,IAAI,IAAK;MAC3C,IAAI,CAACK,IAAI,CAAC,kBAAkB,EAAEL,IAAI,CAAC;IACrC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,eAAe,EAAGC,IAAI,IAAK;MACxC,IAAI,CAACK,IAAI,CAAC,eAAe,EAAEL,IAAI,CAAC;IAClC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,cAAc,EAAGC,IAAI,IAAK;MACvC,IAAI,CAACK,IAAI,CAAC,cAAc,EAAEL,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,kBAAkB,EAAGC,IAAI,IAAK;MAC3C,IAAI,CAACK,IAAI,CAAC,kBAAkB,EAAEL,IAAI,CAAC;IACrC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,cAAc,EAAGC,IAAI,IAAK;MACvC,IAAI,CAACK,IAAI,CAAC,cAAc,EAAEL,IAAI,CAAC;IACjC,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,gBAAgB,EAAGC,IAAI,IAAK;MACzC,IAAI,CAACK,IAAI,CAAC,gBAAgB,EAAEL,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,gBAAgB,EAAGC,IAAI,IAAK;MACzC,IAAI,CAACK,IAAI,CAAC,gBAAgB,EAAEL,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,gBAAgB,EAAGC,IAAI,IAAK;MACzC,IAAI,CAACK,IAAI,CAAC,gBAAgB,EAAEL,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,qBAAqB,EAAGC,IAAI,IAAK;MAC9C,IAAI,CAACK,IAAI,CAAC,qBAAqB,EAAEL,IAAI,CAAC;IACxC,CAAC,CAAC;IAEF,IAAI,CAAC7B,MAAM,CAAC4B,EAAE,CAAC,sBAAsB,EAAGC,IAAI,IAAK;MAC/C,IAAI,CAACK,IAAI,CAAC,sBAAsB,EAAEL,IAAI,CAAC;IACzC,CAAC,CAAC;EACJ;EAEAI,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC7B,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;MACtD,IAAI,CAACD,iBAAiB,EAAE;MACxB,MAAM+B,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjC,iBAAiB,CAAC,GAAG,IAAI;MAExDO,OAAO,CAACC,GAAG,CAAC,8BAA8BuB,KAAK,eAAe,IAAI,CAAC/B,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;MAErHsB,UAAU,CAAC,MAAM;QACf,IAAI,IAAI,CAAC3B,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACM,OAAO,CAAC,CAAC;QACvB;MACF,CAAC,EAAE6B,KAAK,CAAC;IACX,CAAC,MAAM;MACLxB,OAAO,CAACoB,KAAK,CAAC,mCAAmC,CAAC;IACpD;EACF;EAEAvB,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACR,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACQ,UAAU,CAAC,CAAC;MACxB,IAAI,CAACR,MAAM,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,CAACoC,KAAK,CAAC,CAAC;EACxB;EAEAC,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACvC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACkC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC;IAC5C;EACF;EAEAM,iBAAiBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACxC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACkC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC;IACzC;EACF;EAEAO,oBAAoBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACzC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACkC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC;IAC5C;EACF;EAEAQ,mBAAmBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC1C,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACkC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC;IAC3C;EACF;;EAEA;EACAS,oBAAoBA,CAACC,WAAW,EAAE;IAChC,IAAI,IAAI,CAAC5C,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAACkC,IAAI,CAAC,gBAAgB,EAAEU,WAAW,CAAC;IACjD;IACA;IACA,IAAI,CAACV,IAAI,CAAC,gBAAgB,EAAEU,WAAW,CAAC;EAC1C;EAEAC,oBAAoBA,CAACD,WAAW,EAAE;IAChC,IAAI,IAAI,CAAC5C,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAACkC,IAAI,CAAC,gBAAgB,EAAEU,WAAW,CAAC;IACjD;IACA,IAAI,CAACV,IAAI,CAAC,gBAAgB,EAAEU,WAAW,CAAC;EAC1C;EAEAE,oBAAoBA,CAACC,SAAS,EAAE;IAC9B,IAAI,IAAI,CAAC/C,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAACkC,IAAI,CAAC,gBAAgB,EAAE;QAAEa;MAAU,CAAC,CAAC;IACnD;IACA,IAAI,CAACb,IAAI,CAAC,gBAAgB,EAAE;MAAEa;IAAU,CAAC,CAAC;EAC5C;EAEAC,yBAAyBA,CAACD,SAAS,EAAEE,QAAQ,EAAE;IAC7C,IAAI,IAAI,CAACjD,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAACkC,IAAI,CAAC,qBAAqB,EAAE;QAAEa,SAAS;QAAEE;MAAS,CAAC,CAAC;IAClE;IACA,IAAI,CAACf,IAAI,CAAC,qBAAqB,EAAE;MAAEa,SAAS;MAAEE;IAAS,CAAC,CAAC;EAC3D;EAEArB,EAAEA,CAACsB,KAAK,EAAEC,QAAQ,EAAE;IAClB,IAAI,CAAC,IAAI,CAACjD,SAAS,CAACkD,GAAG,CAACF,KAAK,CAAC,EAAE;MAC9B,IAAI,CAAChD,SAAS,CAACmD,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;IAC/B;IACA,IAAI,CAAChD,SAAS,CAACoD,GAAG,CAACJ,KAAK,CAAC,CAACK,IAAI,CAACJ,QAAQ,CAAC;EAC1C;EAEAK,GAAGA,CAACN,KAAK,EAAEC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACjD,SAAS,CAACkD,GAAG,CAACF,KAAK,CAAC,EAAE;MAC7B,MAAMO,SAAS,GAAG,IAAI,CAACvD,SAAS,CAACoD,GAAG,CAACJ,KAAK,CAAC;MAC3C,MAAMQ,KAAK,GAAGD,SAAS,CAACE,OAAO,CAACR,QAAQ,CAAC;MACzC,IAAIO,KAAK,GAAG,CAAC,CAAC,EAAE;QACdD,SAAS,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC5B;IACF;EACF;EAEAxB,IAAIA,CAACgB,KAAK,EAAErB,IAAI,EAAE;IAChB,IAAI,IAAI,CAAC3B,SAAS,CAACkD,GAAG,CAACF,KAAK,CAAC,EAAE;MAC7B,IAAI,CAAChD,SAAS,CAACoD,GAAG,CAACJ,KAAK,CAAC,CAACW,OAAO,CAACV,QAAQ,IAAI;QAC5C,IAAI;UACFA,QAAQ,CAACtB,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdpB,OAAO,CAACoB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;EACF;EAEA+B,SAASA,CAACZ,KAAK,EAAErB,IAAI,EAAE;IACrB,IAAI,IAAI,CAAC7B,MAAM,IAAI,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAACD,MAAM,CAACkC,IAAI,CAACgB,KAAK,EAAErB,IAAI,CAAC;IAC/B;EACF;EAEAkC,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,IAAI,GAAG,MAAM,EAAE;IAC9C,IAAI,CAAChC,IAAI,CAAC,cAAc,EAAE;MACxBiC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdL,KAAK;MACLC,OAAO;MACPC,IAAI;MACJI,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;IACpC,CAAC,CAAC;EACJ;EAEAC,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACxE,MAAM,EAAE,OAAO,cAAc;IACvC,IAAI,IAAI,CAACC,WAAW,EAAE,OAAO,WAAW;IACxC,IAAI,IAAI,CAACD,MAAM,CAACyE,UAAU,EAAE,OAAO,YAAY;IAC/C,OAAO,OAAO;EAChB;AACF;AAEA,MAAMC,gBAAgB,GAAG,IAAI5E,gBAAgB,CAAC,CAAC;AAC/C,eAAe4E,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}