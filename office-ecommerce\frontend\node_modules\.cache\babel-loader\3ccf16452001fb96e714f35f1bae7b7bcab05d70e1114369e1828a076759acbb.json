{"ast": null, "code": "import { a as _toConsumableArray } from './triangle-b62b9067.esm.js';\nimport { Color, Vector3, Quaternion, Vector2, Vector4, Euler, Spherical, Matrix4 } from 'three';\nimport { d as deltaAngle } from './misc-7d870b3c.esm.js';\n\n/**\n * Rounded square wave easing\n */\n\nvar rsqw = function rsqw(t) {\n  var delta = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.01;\n  var a = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  var f = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1 / (2 * Math.PI);\n  return a / Math.atan(1 / delta) * Math.atan(Math.sin(2 * Math.PI * t * f) / delta);\n};\n/**\n * Exponential easing\n */\n\nvar exp = function exp(t) {\n  return 1 / (1 + t + 0.48 * t * t + 0.235 * t * t * t);\n};\n/**\n * Damp, based on Game Programming Gems 4 Chapter 1.10\n *   Return value indicates whether the animation is still running.\n */\n\nfunction damp(/** The object */\ncurrent, /** The key to animate */\nprop, /** To goal value */\ntarget) {\n  var smoothTime = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.25;\n  var delta = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0.01;\n  var maxSpeed = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : Infinity;\n  var easing = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : exp;\n  var eps = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 0.001;\n  var vel = \"velocity_\" + prop;\n  if (current.__damp === undefined) current.__damp = {};\n  if (current.__damp[vel] === undefined) current.__damp[vel] = 0;\n  if (Math.abs(current[prop] - target) <= eps) {\n    current[prop] = target;\n    return false;\n  }\n  smoothTime = Math.max(0.0001, smoothTime);\n  var omega = 2 / smoothTime;\n  var t = easing(omega * delta);\n  var change = current[prop] - target;\n  var originalTo = target; // Clamp maximum maxSpeed\n\n  var maxChange = maxSpeed * smoothTime;\n  change = Math.min(Math.max(change, -maxChange), maxChange);\n  target = current[prop] - change;\n  var temp = (current.__damp[vel] + omega * change) * delta;\n  current.__damp[vel] = (current.__damp[vel] - omega * temp) * t;\n  var output = target + (change + temp) * t; // Prevent overshooting\n\n  if (originalTo - current[prop] > 0.0 === output > originalTo) {\n    output = originalTo;\n    current.__damp[vel] = (output - originalTo) / delta;\n  }\n  current[prop] = output;\n  return true;\n}\n/**\n * DampAngle, based on Game Programming Gems 4 Chapter 1.10\n */\n\nfunction dampAngle(current, prop, target, smoothTime, delta, maxSpeed, easing, eps) {\n  return damp(current, prop, current[prop] + deltaAngle(current[prop], target), smoothTime, delta, maxSpeed, easing, eps);\n}\n/**\n * Vector2D Damp\n */\n\nvar v2d = /*@__PURE__*/new Vector2();\nvar a2, b2;\nfunction damp2(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v2d.setScalar(target);else if (Array.isArray(target)) v2d.set(target[0], target[1]);else v2d.copy(target);\n  a2 = damp(current, \"x\", v2d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b2 = damp(current, \"y\", v2d.y, smoothTime, delta, maxSpeed, easing, eps);\n  return a2 || b2;\n}\n/**\n * Vector3D Damp\n */\n\nvar v3d = /*@__PURE__*/new Vector3();\nvar a3, b3, c3;\nfunction damp3(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v3d.setScalar(target);else if (Array.isArray(target)) v3d.set(target[0], target[1], target[2]);else v3d.copy(target);\n  a3 = damp(current, \"x\", v3d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b3 = damp(current, \"y\", v3d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c3 = damp(current, \"z\", v3d.z, smoothTime, delta, maxSpeed, easing, eps);\n  return a3 || b3 || c3;\n}\n/**\n * Vector4D Damp\n */\n\nvar v4d = /*@__PURE__*/new Vector4();\nvar a4, b4, c4, d4;\nfunction damp4(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v4d.setScalar(target);else if (Array.isArray(target)) v4d.set(target[0], target[1], target[2], target[3]);else v4d.copy(target);\n  a4 = damp(current, \"x\", v4d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b4 = damp(current, \"y\", v4d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c4 = damp(current, \"z\", v4d.z, smoothTime, delta, maxSpeed, easing, eps);\n  d4 = damp(current, \"w\", v4d.w, smoothTime, delta, maxSpeed, easing, eps);\n  return a4 || b4 || c4 || d4;\n}\n/**\n * Euler Damp\n */\n\nvar rot = /*@__PURE__*/new Euler();\nvar aE, bE, cE;\nfunction dampE(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) rot.set(target[0], target[1], target[2], target[3]);else rot.copy(target);\n  aE = dampAngle(current, \"x\", rot.x, smoothTime, delta, maxSpeed, easing, eps);\n  bE = dampAngle(current, \"y\", rot.y, smoothTime, delta, maxSpeed, easing, eps);\n  cE = dampAngle(current, \"z\", rot.z, smoothTime, delta, maxSpeed, easing, eps);\n  return aE || bE || cE;\n}\n/**\n * Color Damp\n */\n\nvar col = /*@__PURE__*/new Color();\nvar aC, bC, cC;\nfunction dampC(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (target instanceof Color) col.copy(target);else if (Array.isArray(target)) col.setRGB(target[0], target[1], target[2]);else col.set(target);\n  aC = damp(current, \"r\", col.r, smoothTime, delta, maxSpeed, easing, eps);\n  bC = damp(current, \"g\", col.g, smoothTime, delta, maxSpeed, easing, eps);\n  cC = damp(current, \"b\", col.b, smoothTime, delta, maxSpeed, easing, eps);\n  return aC || bC || cC;\n}\n/**\n * Quaternion Damp\n * https://gist.github.com/maxattack/4c7b4de00f5c1b95a33b\n * Copyright 2016 Max Kaufmann (<EMAIL>)\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nvar qt = /*@__PURE__*/new Quaternion();\nvar v4result = /*@__PURE__*/new Vector4();\nvar v4velocity = /*@__PURE__*/new Vector4();\nvar v4error = /*@__PURE__*/new Vector4();\nvar aQ, bQ, cQ, dQ;\nfunction dampQ(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n  if (Array.isArray(target)) qt.set(target[0], target[1], target[2], target[3]);else qt.copy(target);\n  var multi = current.dot(qt) > 0 ? 1 : -1;\n  qt.x *= multi;\n  qt.y *= multi;\n  qt.z *= multi;\n  qt.w *= multi;\n  aQ = damp(current, \"x\", qt.x, smoothTime, delta, maxSpeed, easing, eps);\n  bQ = damp(current, \"y\", qt.y, smoothTime, delta, maxSpeed, easing, eps);\n  cQ = damp(current, \"z\", qt.z, smoothTime, delta, maxSpeed, easing, eps);\n  dQ = damp(current, \"w\", qt.w, smoothTime, delta, maxSpeed, easing, eps); // smooth damp (nlerp approx)\n\n  v4result.set(current.x, current.y, current.z, current.w).normalize();\n  v4velocity.set(cur.__damp.velocity_x, cur.__damp.velocity_y, cur.__damp.velocity_z, cur.__damp.velocity_w); // ensure deriv is tangent\n\n  v4error.copy(v4result).multiplyScalar(v4velocity.dot(v4result) / v4result.dot(v4result));\n  cur.__damp.velocity_x -= v4error.x;\n  cur.__damp.velocity_y -= v4error.y;\n  cur.__damp.velocity_z -= v4error.z;\n  cur.__damp.velocity_w -= v4error.w;\n  current.set(v4result.x, v4result.y, v4result.z, v4result.w);\n  return aQ || bQ || cQ || dQ;\n}\n/**\n * Spherical Damp\n */\n\nvar spherical = /*@__PURE__*/new Spherical();\nvar aS, bS, cS;\nfunction dampS(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) spherical.set(target[0], target[1], target[2]);else spherical.copy(target);\n  aS = damp(current, \"radius\", spherical.radius, smoothTime, delta, maxSpeed, easing, eps);\n  bS = dampAngle(current, \"phi\", spherical.phi, smoothTime, delta, maxSpeed, easing, eps);\n  cS = dampAngle(current, \"theta\", spherical.theta, smoothTime, delta, maxSpeed, easing, eps);\n  return aS || bS || cS;\n}\n/**\n * Matrix4 Damp\n */\n\nvar mat = /*@__PURE__*/new Matrix4();\nvar mPos = /*@__PURE__*/new Vector3();\nvar mRot = /*@__PURE__*/new Quaternion();\nvar mSca = /*@__PURE__*/new Vector3();\nvar aM, bM, cM;\nfunction dampM(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n  if (cur.__damp === undefined) {\n    cur.__damp = {\n      position: new Vector3(),\n      rotation: new Quaternion(),\n      scale: new Vector3()\n    };\n    current.decompose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  }\n  if (Array.isArray(target)) mat.set.apply(mat, _toConsumableArray(target));else mat.copy(target);\n  mat.decompose(mPos, mRot, mSca);\n  aM = damp3(cur.__damp.position, mPos, smoothTime, delta, maxSpeed, easing, eps);\n  bM = dampQ(cur.__damp.rotation, mRot, smoothTime, delta, maxSpeed, easing, eps);\n  cM = damp3(cur.__damp.scale, mSca, smoothTime, delta, maxSpeed, easing, eps);\n  current.compose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  return aM || bM || cM;\n}\nvar easing = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  rsqw: rsqw,\n  exp: exp,\n  damp: damp,\n  dampAngle: dampAngle,\n  damp2: damp2,\n  damp3: damp3,\n  damp4: damp4,\n  dampE: dampE,\n  dampC: dampC,\n  dampQ: dampQ,\n  dampS: dampS,\n  dampM: dampM\n});\nexport { exp as a, dampAngle as b, damp2 as c, damp as d, easing as e, damp3 as f, damp4 as g, dampE as h, dampC as i, dampQ as j, dampS as k, dampM as l, rsqw as r };", "map": {"version": 3, "names": ["a", "_toConsumableArray", "Color", "Vector3", "Quaternion", "Vector2", "Vector4", "<PERSON>uler", "Spherical", "Matrix4", "d", "deltaAngle", "rsqw", "t", "delta", "arguments", "length", "undefined", "f", "Math", "PI", "atan", "sin", "exp", "damp", "current", "prop", "target", "smoothTime", "maxSpeed", "Infinity", "easing", "eps", "vel", "__damp", "abs", "max", "omega", "change", "originalTo", "max<PERSON><PERSON><PERSON>", "min", "temp", "output", "dampAngle", "v2d", "a2", "b2", "damp2", "setScalar", "Array", "isArray", "set", "copy", "x", "y", "v3d", "a3", "b3", "c3", "damp3", "z", "v4d", "a4", "b4", "c4", "d4", "damp4", "w", "rot", "aE", "bE", "cE", "dampE", "col", "aC", "bC", "cC", "dampC", "setRGB", "r", "g", "b", "qt", "v4result", "v4velocity", "v4error", "aQ", "bQ", "cQ", "dQ", "dampQ", "cur", "multi", "dot", "normalize", "velocity_x", "velocity_y", "velocity_z", "velocity_w", "multiplyScalar", "spherical", "aS", "bS", "cS", "dampS", "radius", "phi", "theta", "mat", "mPos", "mRot", "mSca", "aM", "bM", "cM", "dampM", "position", "rotation", "scale", "decompose", "apply", "compose", "Object", "freeze", "__proto__", "c", "e", "h", "i", "j", "k", "l"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/maath/dist/easing-3be59c6d.esm.js"], "sourcesContent": ["import { a as _toConsumableArray } from './triangle-b62b9067.esm.js';\nimport { Color, Vector3, Quaternion, Vector2, Vector4, Euler, Spherical, Matrix4 } from 'three';\nimport { d as deltaAngle } from './misc-7d870b3c.esm.js';\n\n/**\n * Rounded square wave easing\n */\n\nvar rsqw = function rsqw(t) {\n  var delta = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.01;\n  var a = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  var f = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1 / (2 * Math.PI);\n  return a / Math.atan(1 / delta) * Math.atan(Math.sin(2 * Math.PI * t * f) / delta);\n};\n/**\n * Exponential easing\n */\n\nvar exp = function exp(t) {\n  return 1 / (1 + t + 0.48 * t * t + 0.235 * t * t * t);\n};\n/**\n * Damp, based on Game Programming Gems 4 Chapter 1.10\n *   Return value indicates whether the animation is still running.\n */\n\nfunction damp(\n/** The object */\ncurrent,\n/** The key to animate */\nprop,\n/** To goal value */\ntarget) {\n  var smoothTime = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.25;\n  var delta = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0.01;\n  var maxSpeed = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : Infinity;\n  var easing = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : exp;\n  var eps = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 0.001;\n  var vel = \"velocity_\" + prop;\n  if (current.__damp === undefined) current.__damp = {};\n  if (current.__damp[vel] === undefined) current.__damp[vel] = 0;\n\n  if (Math.abs(current[prop] - target) <= eps) {\n    current[prop] = target;\n    return false;\n  }\n\n  smoothTime = Math.max(0.0001, smoothTime);\n  var omega = 2 / smoothTime;\n  var t = easing(omega * delta);\n  var change = current[prop] - target;\n  var originalTo = target; // Clamp maximum maxSpeed\n\n  var maxChange = maxSpeed * smoothTime;\n  change = Math.min(Math.max(change, -maxChange), maxChange);\n  target = current[prop] - change;\n  var temp = (current.__damp[vel] + omega * change) * delta;\n  current.__damp[vel] = (current.__damp[vel] - omega * temp) * t;\n  var output = target + (change + temp) * t; // Prevent overshooting\n\n  if (originalTo - current[prop] > 0.0 === output > originalTo) {\n    output = originalTo;\n    current.__damp[vel] = (output - originalTo) / delta;\n  }\n\n  current[prop] = output;\n  return true;\n}\n/**\n * DampAngle, based on Game Programming Gems 4 Chapter 1.10\n */\n\nfunction dampAngle(current, prop, target, smoothTime, delta, maxSpeed, easing, eps) {\n  return damp(current, prop, current[prop] + deltaAngle(current[prop], target), smoothTime, delta, maxSpeed, easing, eps);\n}\n/**\n * Vector2D Damp\n */\n\nvar v2d = /*@__PURE__*/new Vector2();\nvar a2, b2;\nfunction damp2(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v2d.setScalar(target);else if (Array.isArray(target)) v2d.set(target[0], target[1]);else v2d.copy(target);\n  a2 = damp(current, \"x\", v2d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b2 = damp(current, \"y\", v2d.y, smoothTime, delta, maxSpeed, easing, eps);\n  return a2 || b2;\n}\n/**\n * Vector3D Damp\n */\n\nvar v3d = /*@__PURE__*/new Vector3();\nvar a3, b3, c3;\nfunction damp3(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v3d.setScalar(target);else if (Array.isArray(target)) v3d.set(target[0], target[1], target[2]);else v3d.copy(target);\n  a3 = damp(current, \"x\", v3d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b3 = damp(current, \"y\", v3d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c3 = damp(current, \"z\", v3d.z, smoothTime, delta, maxSpeed, easing, eps);\n  return a3 || b3 || c3;\n}\n/**\n * Vector4D Damp\n */\n\nvar v4d = /*@__PURE__*/new Vector4();\nvar a4, b4, c4, d4;\nfunction damp4(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v4d.setScalar(target);else if (Array.isArray(target)) v4d.set(target[0], target[1], target[2], target[3]);else v4d.copy(target);\n  a4 = damp(current, \"x\", v4d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b4 = damp(current, \"y\", v4d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c4 = damp(current, \"z\", v4d.z, smoothTime, delta, maxSpeed, easing, eps);\n  d4 = damp(current, \"w\", v4d.w, smoothTime, delta, maxSpeed, easing, eps);\n  return a4 || b4 || c4 || d4;\n}\n/**\n * Euler Damp\n */\n\nvar rot = /*@__PURE__*/new Euler();\nvar aE, bE, cE;\nfunction dampE(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) rot.set(target[0], target[1], target[2], target[3]);else rot.copy(target);\n  aE = dampAngle(current, \"x\", rot.x, smoothTime, delta, maxSpeed, easing, eps);\n  bE = dampAngle(current, \"y\", rot.y, smoothTime, delta, maxSpeed, easing, eps);\n  cE = dampAngle(current, \"z\", rot.z, smoothTime, delta, maxSpeed, easing, eps);\n  return aE || bE || cE;\n}\n/**\n * Color Damp\n */\n\nvar col = /*@__PURE__*/new Color();\nvar aC, bC, cC;\nfunction dampC(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (target instanceof Color) col.copy(target);else if (Array.isArray(target)) col.setRGB(target[0], target[1], target[2]);else col.set(target);\n  aC = damp(current, \"r\", col.r, smoothTime, delta, maxSpeed, easing, eps);\n  bC = damp(current, \"g\", col.g, smoothTime, delta, maxSpeed, easing, eps);\n  cC = damp(current, \"b\", col.b, smoothTime, delta, maxSpeed, easing, eps);\n  return aC || bC || cC;\n}\n/**\n * Quaternion Damp\n * https://gist.github.com/maxattack/4c7b4de00f5c1b95a33b\n * Copyright 2016 Max Kaufmann (<EMAIL>)\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nvar qt = /*@__PURE__*/new Quaternion();\nvar v4result = /*@__PURE__*/new Vector4();\nvar v4velocity = /*@__PURE__*/new Vector4();\nvar v4error = /*@__PURE__*/new Vector4();\nvar aQ, bQ, cQ, dQ;\nfunction dampQ(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n  if (Array.isArray(target)) qt.set(target[0], target[1], target[2], target[3]);else qt.copy(target);\n  var multi = current.dot(qt) > 0 ? 1 : -1;\n  qt.x *= multi;\n  qt.y *= multi;\n  qt.z *= multi;\n  qt.w *= multi;\n  aQ = damp(current, \"x\", qt.x, smoothTime, delta, maxSpeed, easing, eps);\n  bQ = damp(current, \"y\", qt.y, smoothTime, delta, maxSpeed, easing, eps);\n  cQ = damp(current, \"z\", qt.z, smoothTime, delta, maxSpeed, easing, eps);\n  dQ = damp(current, \"w\", qt.w, smoothTime, delta, maxSpeed, easing, eps); // smooth damp (nlerp approx)\n\n  v4result.set(current.x, current.y, current.z, current.w).normalize();\n  v4velocity.set(cur.__damp.velocity_x, cur.__damp.velocity_y, cur.__damp.velocity_z, cur.__damp.velocity_w); // ensure deriv is tangent\n\n  v4error.copy(v4result).multiplyScalar(v4velocity.dot(v4result) / v4result.dot(v4result));\n  cur.__damp.velocity_x -= v4error.x;\n  cur.__damp.velocity_y -= v4error.y;\n  cur.__damp.velocity_z -= v4error.z;\n  cur.__damp.velocity_w -= v4error.w;\n  current.set(v4result.x, v4result.y, v4result.z, v4result.w);\n  return aQ || bQ || cQ || dQ;\n}\n/**\n * Spherical Damp\n */\n\nvar spherical = /*@__PURE__*/new Spherical();\nvar aS, bS, cS;\nfunction dampS(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) spherical.set(target[0], target[1], target[2]);else spherical.copy(target);\n  aS = damp(current, \"radius\", spherical.radius, smoothTime, delta, maxSpeed, easing, eps);\n  bS = dampAngle(current, \"phi\", spherical.phi, smoothTime, delta, maxSpeed, easing, eps);\n  cS = dampAngle(current, \"theta\", spherical.theta, smoothTime, delta, maxSpeed, easing, eps);\n  return aS || bS || cS;\n}\n/**\n * Matrix4 Damp\n */\n\nvar mat = /*@__PURE__*/new Matrix4();\nvar mPos = /*@__PURE__*/new Vector3();\nvar mRot = /*@__PURE__*/new Quaternion();\nvar mSca = /*@__PURE__*/new Vector3();\nvar aM, bM, cM;\nfunction dampM(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n\n  if (cur.__damp === undefined) {\n    cur.__damp = {\n      position: new Vector3(),\n      rotation: new Quaternion(),\n      scale: new Vector3()\n    };\n    current.decompose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  }\n\n  if (Array.isArray(target)) mat.set.apply(mat, _toConsumableArray(target));else mat.copy(target);\n  mat.decompose(mPos, mRot, mSca);\n  aM = damp3(cur.__damp.position, mPos, smoothTime, delta, maxSpeed, easing, eps);\n  bM = dampQ(cur.__damp.rotation, mRot, smoothTime, delta, maxSpeed, easing, eps);\n  cM = damp3(cur.__damp.scale, mSca, smoothTime, delta, maxSpeed, easing, eps);\n  current.compose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  return aM || bM || cM;\n}\n\nvar easing = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  rsqw: rsqw,\n  exp: exp,\n  damp: damp,\n  dampAngle: dampAngle,\n  damp2: damp2,\n  damp3: damp3,\n  damp4: damp4,\n  dampE: dampE,\n  dampC: dampC,\n  dampQ: dampQ,\n  dampS: dampS,\n  dampM: dampM\n});\n\nexport { exp as a, dampAngle as b, damp2 as c, damp as d, easing as e, damp3 as f, damp4 as g, dampE as h, dampC as i, dampQ as j, dampS as k, dampM as l, rsqw as r };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,kBAAkB,QAAQ,4BAA4B;AACpE,SAASC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC/F,SAASC,CAAC,IAAIC,UAAU,QAAQ,wBAAwB;;AAExD;AACA;AACA;;AAEA,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,CAAC,EAAE;EAC1B,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACpF,IAAIf,CAAC,GAAGe,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7E,IAAIG,CAAC,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAGI,IAAI,CAACC,EAAE,CAAC;EAC7F,OAAOpB,CAAC,GAAGmB,IAAI,CAACE,IAAI,CAAC,CAAC,GAAGP,KAAK,CAAC,GAAGK,IAAI,CAACE,IAAI,CAACF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAGH,IAAI,CAACC,EAAE,GAAGP,CAAC,GAAGK,CAAC,CAAC,GAAGJ,KAAK,CAAC;AACpF,CAAC;AACD;AACA;AACA;;AAEA,IAAIS,GAAG,GAAG,SAASA,GAAGA,CAACV,CAAC,EAAE;EACxB,OAAO,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAGA,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAAC;AACvD,CAAC;AACD;AACA;AACA;AACA;;AAEA,SAASW,IAAIA,CACb;AACAC,OAAO,EACP;AACAC,IAAI,EACJ;AACAC,MAAM,EAAE;EACN,IAAIC,UAAU,GAAGb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACzF,IAAID,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACpF,IAAIc,QAAQ,GAAGd,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGe,QAAQ;EAC3F,IAAIC,MAAM,GAAGhB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGQ,GAAG;EACpF,IAAIS,GAAG,GAAGjB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACnF,IAAIkB,GAAG,GAAG,WAAW,GAAGP,IAAI;EAC5B,IAAID,OAAO,CAACS,MAAM,KAAKjB,SAAS,EAAEQ,OAAO,CAACS,MAAM,GAAG,CAAC,CAAC;EACrD,IAAIT,OAAO,CAACS,MAAM,CAACD,GAAG,CAAC,KAAKhB,SAAS,EAAEQ,OAAO,CAACS,MAAM,CAACD,GAAG,CAAC,GAAG,CAAC;EAE9D,IAAId,IAAI,CAACgB,GAAG,CAACV,OAAO,CAACC,IAAI,CAAC,GAAGC,MAAM,CAAC,IAAIK,GAAG,EAAE;IAC3CP,OAAO,CAACC,IAAI,CAAC,GAAGC,MAAM;IACtB,OAAO,KAAK;EACd;EAEAC,UAAU,GAAGT,IAAI,CAACiB,GAAG,CAAC,MAAM,EAAER,UAAU,CAAC;EACzC,IAAIS,KAAK,GAAG,CAAC,GAAGT,UAAU;EAC1B,IAAIf,CAAC,GAAGkB,MAAM,CAACM,KAAK,GAAGvB,KAAK,CAAC;EAC7B,IAAIwB,MAAM,GAAGb,OAAO,CAACC,IAAI,CAAC,GAAGC,MAAM;EACnC,IAAIY,UAAU,GAAGZ,MAAM,CAAC,CAAC;;EAEzB,IAAIa,SAAS,GAAGX,QAAQ,GAAGD,UAAU;EACrCU,MAAM,GAAGnB,IAAI,CAACsB,GAAG,CAACtB,IAAI,CAACiB,GAAG,CAACE,MAAM,EAAE,CAACE,SAAS,CAAC,EAAEA,SAAS,CAAC;EAC1Db,MAAM,GAAGF,OAAO,CAACC,IAAI,CAAC,GAAGY,MAAM;EAC/B,IAAII,IAAI,GAAG,CAACjB,OAAO,CAACS,MAAM,CAACD,GAAG,CAAC,GAAGI,KAAK,GAAGC,MAAM,IAAIxB,KAAK;EACzDW,OAAO,CAACS,MAAM,CAACD,GAAG,CAAC,GAAG,CAACR,OAAO,CAACS,MAAM,CAACD,GAAG,CAAC,GAAGI,KAAK,GAAGK,IAAI,IAAI7B,CAAC;EAC9D,IAAI8B,MAAM,GAAGhB,MAAM,GAAG,CAACW,MAAM,GAAGI,IAAI,IAAI7B,CAAC,CAAC,CAAC;;EAE3C,IAAI0B,UAAU,GAAGd,OAAO,CAACC,IAAI,CAAC,GAAG,GAAG,KAAKiB,MAAM,GAAGJ,UAAU,EAAE;IAC5DI,MAAM,GAAGJ,UAAU;IACnBd,OAAO,CAACS,MAAM,CAACD,GAAG,CAAC,GAAG,CAACU,MAAM,GAAGJ,UAAU,IAAIzB,KAAK;EACrD;EAEAW,OAAO,CAACC,IAAI,CAAC,GAAGiB,MAAM;EACtB,OAAO,IAAI;AACb;AACA;AACA;AACA;;AAEA,SAASC,SAASA,CAACnB,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EAClF,OAAOR,IAAI,CAACC,OAAO,EAAEC,IAAI,EAAED,OAAO,CAACC,IAAI,CAAC,GAAGf,UAAU,CAACc,OAAO,CAACC,IAAI,CAAC,EAAEC,MAAM,CAAC,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;AACzH;AACA;AACA;AACA;;AAEA,IAAIa,GAAG,GAAG,aAAa,IAAIxC,OAAO,CAAC,CAAC;AACpC,IAAIyC,EAAE,EAAEC,EAAE;AACV,SAASC,KAAKA,CAACvB,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAI,OAAOL,MAAM,KAAK,QAAQ,EAAEkB,GAAG,CAACI,SAAS,CAACtB,MAAM,CAAC,CAAC,KAAK,IAAIuB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAEkB,GAAG,CAACO,GAAG,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKkB,GAAG,CAACQ,IAAI,CAAC1B,MAAM,CAAC;EACzImB,EAAE,GAAGtB,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEoB,GAAG,CAACS,CAAC,EAAE1B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxEe,EAAE,GAAGvB,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEoB,GAAG,CAACU,CAAC,EAAE3B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE,OAAOc,EAAE,IAAIC,EAAE;AACjB;AACA;AACA;AACA;;AAEA,IAAIS,GAAG,GAAG,aAAa,IAAIrD,OAAO,CAAC,CAAC;AACpC,IAAIsD,EAAE,EAAEC,EAAE,EAAEC,EAAE;AACd,SAASC,KAAKA,CAACnC,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAI,OAAOL,MAAM,KAAK,QAAQ,EAAE6B,GAAG,CAACP,SAAS,CAACtB,MAAM,CAAC,CAAC,KAAK,IAAIuB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAE6B,GAAG,CAACJ,GAAG,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK6B,GAAG,CAACH,IAAI,CAAC1B,MAAM,CAAC;EACpJ8B,EAAE,GAAGjC,IAAI,CAACC,OAAO,EAAE,GAAG,EAAE+B,GAAG,CAACF,CAAC,EAAE1B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE0B,EAAE,GAAGlC,IAAI,CAACC,OAAO,EAAE,GAAG,EAAE+B,GAAG,CAACD,CAAC,EAAE3B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE2B,EAAE,GAAGnC,IAAI,CAACC,OAAO,EAAE,GAAG,EAAE+B,GAAG,CAACK,CAAC,EAAEjC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE,OAAOyB,EAAE,IAAIC,EAAE,IAAIC,EAAE;AACvB;AACA;AACA;AACA;;AAEA,IAAIG,GAAG,GAAG,aAAa,IAAIxD,OAAO,CAAC,CAAC;AACpC,IAAIyD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;AAClB,SAASC,KAAKA,CAAC1C,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAI,OAAOL,MAAM,KAAK,QAAQ,EAAEmC,GAAG,CAACb,SAAS,CAACtB,MAAM,CAAC,CAAC,KAAK,IAAIuB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAEmC,GAAG,CAACV,GAAG,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKmC,GAAG,CAACT,IAAI,CAAC1B,MAAM,CAAC;EAC/JoC,EAAE,GAAGvC,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEqC,GAAG,CAACR,CAAC,EAAE1B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxEgC,EAAE,GAAGxC,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEqC,GAAG,CAACP,CAAC,EAAE3B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxEiC,EAAE,GAAGzC,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEqC,GAAG,CAACD,CAAC,EAAEjC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxEkC,EAAE,GAAG1C,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEqC,GAAG,CAACM,CAAC,EAAExC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE,OAAO+B,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE;AAC7B;AACA;AACA;AACA;;AAEA,IAAIG,GAAG,GAAG,aAAa,IAAI9D,KAAK,CAAC,CAAC;AAClC,IAAI+D,EAAE,EAAEC,EAAE,EAAEC,EAAE;AACd,SAASC,KAAKA,CAAChD,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAIkB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAE0C,GAAG,CAACjB,GAAG,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK0C,GAAG,CAAChB,IAAI,CAAC1B,MAAM,CAAC;EACpG2C,EAAE,GAAG1B,SAAS,CAACnB,OAAO,EAAE,GAAG,EAAE4C,GAAG,CAACf,CAAC,EAAE1B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EAC7EuC,EAAE,GAAG3B,SAAS,CAACnB,OAAO,EAAE,GAAG,EAAE4C,GAAG,CAACd,CAAC,EAAE3B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EAC7EwC,EAAE,GAAG5B,SAAS,CAACnB,OAAO,EAAE,GAAG,EAAE4C,GAAG,CAACR,CAAC,EAAEjC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EAC7E,OAAOsC,EAAE,IAAIC,EAAE,IAAIC,EAAE;AACvB;AACA;AACA;AACA;;AAEA,IAAIE,GAAG,GAAG,aAAa,IAAIxE,KAAK,CAAC,CAAC;AAClC,IAAIyE,EAAE,EAAEC,EAAE,EAAEC,EAAE;AACd,SAASC,KAAKA,CAACrD,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAIL,MAAM,YAAYzB,KAAK,EAAEwE,GAAG,CAACrB,IAAI,CAAC1B,MAAM,CAAC,CAAC,KAAK,IAAIuB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAE+C,GAAG,CAACK,MAAM,CAACpD,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK+C,GAAG,CAACtB,GAAG,CAACzB,MAAM,CAAC;EAC9IgD,EAAE,GAAGnD,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEiD,GAAG,CAACM,CAAC,EAAEpD,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE4C,EAAE,GAAGpD,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEiD,GAAG,CAACO,CAAC,EAAErD,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE6C,EAAE,GAAGrD,IAAI,CAACC,OAAO,EAAE,GAAG,EAAEiD,GAAG,CAACQ,CAAC,EAAEtD,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxE,OAAO2C,EAAE,IAAIC,EAAE,IAAIC,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIM,EAAE,GAAG,aAAa,IAAI/E,UAAU,CAAC,CAAC;AACtC,IAAIgF,QAAQ,GAAG,aAAa,IAAI9E,OAAO,CAAC,CAAC;AACzC,IAAI+E,UAAU,GAAG,aAAa,IAAI/E,OAAO,CAAC,CAAC;AAC3C,IAAIgF,OAAO,GAAG,aAAa,IAAIhF,OAAO,CAAC,CAAC;AACxC,IAAIiF,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;AAClB,SAASC,KAAKA,CAAClE,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAI4D,GAAG,GAAGnE,OAAO;EACjB,IAAIyB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAEwD,EAAE,CAAC/B,GAAG,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKwD,EAAE,CAAC9B,IAAI,CAAC1B,MAAM,CAAC;EAClG,IAAIkE,KAAK,GAAGpE,OAAO,CAACqE,GAAG,CAACX,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxCA,EAAE,CAAC7B,CAAC,IAAIuC,KAAK;EACbV,EAAE,CAAC5B,CAAC,IAAIsC,KAAK;EACbV,EAAE,CAACtB,CAAC,IAAIgC,KAAK;EACbV,EAAE,CAACf,CAAC,IAAIyB,KAAK;EACbN,EAAE,GAAG/D,IAAI,CAACC,OAAO,EAAE,GAAG,EAAE0D,EAAE,CAAC7B,CAAC,EAAE1B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACvEwD,EAAE,GAAGhE,IAAI,CAACC,OAAO,EAAE,GAAG,EAAE0D,EAAE,CAAC5B,CAAC,EAAE3B,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACvEyD,EAAE,GAAGjE,IAAI,CAACC,OAAO,EAAE,GAAG,EAAE0D,EAAE,CAACtB,CAAC,EAAEjC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACvE0D,EAAE,GAAGlE,IAAI,CAACC,OAAO,EAAE,GAAG,EAAE0D,EAAE,CAACf,CAAC,EAAExC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC,CAAC,CAAC;;EAEzEoD,QAAQ,CAAChC,GAAG,CAAC3B,OAAO,CAAC6B,CAAC,EAAE7B,OAAO,CAAC8B,CAAC,EAAE9B,OAAO,CAACoC,CAAC,EAAEpC,OAAO,CAAC2C,CAAC,CAAC,CAAC2B,SAAS,CAAC,CAAC;EACpEV,UAAU,CAACjC,GAAG,CAACwC,GAAG,CAAC1D,MAAM,CAAC8D,UAAU,EAAEJ,GAAG,CAAC1D,MAAM,CAAC+D,UAAU,EAAEL,GAAG,CAAC1D,MAAM,CAACgE,UAAU,EAAEN,GAAG,CAAC1D,MAAM,CAACiE,UAAU,CAAC,CAAC,CAAC;;EAE5Gb,OAAO,CAACjC,IAAI,CAAC+B,QAAQ,CAAC,CAACgB,cAAc,CAACf,UAAU,CAACS,GAAG,CAACV,QAAQ,CAAC,GAAGA,QAAQ,CAACU,GAAG,CAACV,QAAQ,CAAC,CAAC;EACxFQ,GAAG,CAAC1D,MAAM,CAAC8D,UAAU,IAAIV,OAAO,CAAChC,CAAC;EAClCsC,GAAG,CAAC1D,MAAM,CAAC+D,UAAU,IAAIX,OAAO,CAAC/B,CAAC;EAClCqC,GAAG,CAAC1D,MAAM,CAACgE,UAAU,IAAIZ,OAAO,CAACzB,CAAC;EAClC+B,GAAG,CAAC1D,MAAM,CAACiE,UAAU,IAAIb,OAAO,CAAClB,CAAC;EAClC3C,OAAO,CAAC2B,GAAG,CAACgC,QAAQ,CAAC9B,CAAC,EAAE8B,QAAQ,CAAC7B,CAAC,EAAE6B,QAAQ,CAACvB,CAAC,EAAEuB,QAAQ,CAAChB,CAAC,CAAC;EAC3D,OAAOmB,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE;AAC7B;AACA;AACA;AACA;;AAEA,IAAIW,SAAS,GAAG,aAAa,IAAI7F,SAAS,CAAC,CAAC;AAC5C,IAAI8F,EAAE,EAAEC,EAAE,EAAEC,EAAE;AACd,SAASC,KAAKA,CAAChF,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAIkB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAE0E,SAAS,CAACjD,GAAG,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK0E,SAAS,CAAChD,IAAI,CAAC1B,MAAM,CAAC;EACrG2E,EAAE,GAAG9E,IAAI,CAACC,OAAO,EAAE,QAAQ,EAAE4E,SAAS,CAACK,MAAM,EAAE9E,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACxFuE,EAAE,GAAG3D,SAAS,CAACnB,OAAO,EAAE,KAAK,EAAE4E,SAAS,CAACM,GAAG,EAAE/E,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EACvFwE,EAAE,GAAG5D,SAAS,CAACnB,OAAO,EAAE,OAAO,EAAE4E,SAAS,CAACO,KAAK,EAAEhF,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EAC3F,OAAOsE,EAAE,IAAIC,EAAE,IAAIC,EAAE;AACvB;AACA;AACA;AACA;;AAEA,IAAIK,GAAG,GAAG,aAAa,IAAIpG,OAAO,CAAC,CAAC;AACpC,IAAIqG,IAAI,GAAG,aAAa,IAAI3G,OAAO,CAAC,CAAC;AACrC,IAAI4G,IAAI,GAAG,aAAa,IAAI3G,UAAU,CAAC,CAAC;AACxC,IAAI4G,IAAI,GAAG,aAAa,IAAI7G,OAAO,CAAC,CAAC;AACrC,IAAI8G,EAAE,EAAEC,EAAE,EAAEC,EAAE;AACd,SAASC,KAAKA,CAAC3F,OAAO,EAAEE,MAAM,EAAEC,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,EAAE;EACxE,IAAI4D,GAAG,GAAGnE,OAAO;EAEjB,IAAImE,GAAG,CAAC1D,MAAM,KAAKjB,SAAS,EAAE;IAC5B2E,GAAG,CAAC1D,MAAM,GAAG;MACXmF,QAAQ,EAAE,IAAIlH,OAAO,CAAC,CAAC;MACvBmH,QAAQ,EAAE,IAAIlH,UAAU,CAAC,CAAC;MAC1BmH,KAAK,EAAE,IAAIpH,OAAO,CAAC;IACrB,CAAC;IACDsB,OAAO,CAAC+F,SAAS,CAAC5B,GAAG,CAAC1D,MAAM,CAACmF,QAAQ,EAAEzB,GAAG,CAAC1D,MAAM,CAACoF,QAAQ,EAAE1B,GAAG,CAAC1D,MAAM,CAACqF,KAAK,CAAC;EAC/E;EAEA,IAAIrE,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,EAAEkF,GAAG,CAACzD,GAAG,CAACqE,KAAK,CAACZ,GAAG,EAAE5G,kBAAkB,CAAC0B,MAAM,CAAC,CAAC,CAAC,KAAKkF,GAAG,CAACxD,IAAI,CAAC1B,MAAM,CAAC;EAC/FkF,GAAG,CAACW,SAAS,CAACV,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;EAC/BC,EAAE,GAAGrD,KAAK,CAACgC,GAAG,CAAC1D,MAAM,CAACmF,QAAQ,EAAEP,IAAI,EAAElF,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EAC/EkF,EAAE,GAAGvB,KAAK,CAACC,GAAG,CAAC1D,MAAM,CAACoF,QAAQ,EAAEP,IAAI,EAAEnF,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EAC/EmF,EAAE,GAAGvD,KAAK,CAACgC,GAAG,CAAC1D,MAAM,CAACqF,KAAK,EAAEP,IAAI,EAAEpF,UAAU,EAAEd,KAAK,EAAEe,QAAQ,EAAEE,MAAM,EAAEC,GAAG,CAAC;EAC5EP,OAAO,CAACiG,OAAO,CAAC9B,GAAG,CAAC1D,MAAM,CAACmF,QAAQ,EAAEzB,GAAG,CAAC1D,MAAM,CAACoF,QAAQ,EAAE1B,GAAG,CAAC1D,MAAM,CAACqF,KAAK,CAAC;EAC3E,OAAON,EAAE,IAAIC,EAAE,IAAIC,EAAE;AACvB;AAEA,IAAIpF,MAAM,GAAG,aAAa4F,MAAM,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE,IAAI;EACfjH,IAAI,EAAEA,IAAI;EACVW,GAAG,EAAEA,GAAG;EACRC,IAAI,EAAEA,IAAI;EACVoB,SAAS,EAAEA,SAAS;EACpBI,KAAK,EAAEA,KAAK;EACZY,KAAK,EAAEA,KAAK;EACZO,KAAK,EAAEA,KAAK;EACZM,KAAK,EAAEA,KAAK;EACZK,KAAK,EAAEA,KAAK;EACZa,KAAK,EAAEA,KAAK;EACZc,KAAK,EAAEA,KAAK;EACZW,KAAK,EAAEA;AACT,CAAC,CAAC;AAEF,SAAS7F,GAAG,IAAIvB,CAAC,EAAE4C,SAAS,IAAIsC,CAAC,EAAElC,KAAK,IAAI8E,CAAC,EAAEtG,IAAI,IAAId,CAAC,EAAEqB,MAAM,IAAIgG,CAAC,EAAEnE,KAAK,IAAI1C,CAAC,EAAEiD,KAAK,IAAIc,CAAC,EAAER,KAAK,IAAIuD,CAAC,EAAElD,KAAK,IAAImD,CAAC,EAAEtC,KAAK,IAAIuC,CAAC,EAAEzB,KAAK,IAAI0B,CAAC,EAAEf,KAAK,IAAIgB,CAAC,EAAExH,IAAI,IAAIoE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}