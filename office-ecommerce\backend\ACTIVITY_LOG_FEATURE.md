# Activity Log Feature for Admin Dashboard

## Overview

The Activity Log feature provides comprehensive tracking and auditing capabilities for the admin dashboard. It logs all significant user actions, system events, and administrative operations to help administrators monitor system usage, track changes, and maintain security.

## Features

### 1. Comprehensive Activity Tracking
- User authentication events (login, logout)
- CRUD operations on all entities (products, orders, users, etc.)
- Admin dashboard access
- File uploads and downloads
- System configuration changes
- Bulk operations
- Error tracking

### 2. Rich Metadata Storage
- User information (ID, email, name, role)
- Request details (IP address, user agent, HTTP method, path)
- Performance metrics (request duration, status codes)
- Entity information (type, ID, name)
- Before/after values for updates
- Custom metadata for specific operations

### 3. Advanced Filtering and Search
- Filter by user, action type, entity type, severity
- Date range filtering
- Full-text search across descriptions and entity names
- Pagination support

### 4. Dashboard Integration
- Activity statistics on admin dashboard
- Recent activity feed
- Error count monitoring
- Activity trends and charts

## Database Schema

### ActivityLogs Table
```sql
CREATE TABLE ActivityLogs (
    LogID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserID UNIQUEIDENTIFIER NULL,
    UserEmail NVARCHAR(255) NULL,
    UserName NVARCHAR(255) NULL,
    UserRole NVARCHAR(50) NULL,
    Action NVARCHAR(100) NOT NULL,
    EntityType NVARCHAR(50) NOT NULL,
    EntityID UNIQUEIDENTIFIER NULL,
    EntityName NVARCHAR(255) NULL,
    Description NVARCHAR(500) NOT NULL,
    IPAddress NVARCHAR(45) NULL,
    UserAgent NVARCHAR(500) NULL,
    RequestMethod NVARCHAR(10) NULL,
    RequestPath NVARCHAR(500) NULL,
    StatusCode INT NULL,
    Duration INT NULL,
    OldValues NVARCHAR(MAX) NULL,
    NewValues NVARCHAR(MAX) NULL,
    Metadata NVARCHAR(MAX) NULL,
    Severity NVARCHAR(20) NOT NULL DEFAULT 'INFO',
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE SET NULL
);
```

## API Endpoints

### 1. Get Activity Logs
```
GET /api/admin/activity-logs
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 50, max: 100)
- `userID` (string): Filter by user ID
- `action` (string): Filter by action type
- `entityType` (string): Filter by entity type
- `severity` (string): Filter by severity (INFO, WARNING, ERROR, CRITICAL)
- `startDate` (ISO date): Filter from date
- `endDate` (ISO date): Filter to date
- `search` (string): Search in description, user name, entity name

**Response:**
```json
{
  "success": true,
  "data": {
    "logs": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 10,
      "totalItems": 500,
      "itemsPerPage": 50
    }
  }
}
```

### 2. Get Activity Statistics
```
GET /api/admin/activity-logs/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalActivities": 1250,
      "last24Hours": 45,
      "last7Days": 320,
      "last30Days": 1100,
      "errorCount": 8
    },
    "actionStats": [...],
    "entityStats": [...],
    "dailyStats": [...]
  }
}
```

### 3. Get Available Actions
```
GET /api/admin/activity-logs/actions
```

### 4. Get Available Entity Types
```
GET /api/admin/activity-logs/entity-types
```

## Usage Examples

### 1. Automatic Logging with Middleware

```javascript
// Add to routes for automatic logging
router.post('/products', 
  authenticateToken,
  requirePermission('products:create'),
  ActivityLoggerMiddleware.logProductCreate(),
  async (req, res) => {
    // Route handler
  }
);
```

### 2. Manual Logging

```javascript
const ActivityLogService = require('../services/activityLogService');

// Log custom activity
await ActivityLogService.logActivity({
  userID: req.user.id,
  userEmail: req.user.email,
  userName: `${req.user.firstName} ${req.user.lastName}`,
  userRole: req.user.role,
  action: 'EXPORT',
  entityType: 'Report',
  description: 'Exported sales report',
  ipAddress: req.ip,
  severity: 'INFO'
});
```

### 3. Query Activity Logs

```javascript
// Get recent activities for a specific user
const logs = await ActivityLogService.getActivityLogs({
  userID: 'user-uuid',
  page: 1,
  limit: 20,
  startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
});
```

## Security Considerations

1. **Access Control**: Activity log endpoints require admin authentication
2. **Data Retention**: Consider implementing log rotation/archival for large datasets
3. **Sensitive Data**: Avoid logging sensitive information like passwords or tokens
4. **Performance**: Logging is asynchronous to avoid impacting request performance

## Installation

1. Run the database migration:
```bash
sqlcmd -S "SERVER\INSTANCE" -U "username" -P "password" -i "database/create-activity-logs.sql"
```

2. The feature is automatically integrated into existing routes with middleware

## Monitoring and Maintenance

- Monitor log table size and implement archival strategy
- Review error logs regularly for system issues
- Use activity trends to identify usage patterns
- Set up alerts for critical activities or high error rates

## Future Enhancements

- Real-time activity notifications
- Activity log export functionality
- Advanced analytics and reporting
- Integration with external monitoring systems
- Automated anomaly detection
