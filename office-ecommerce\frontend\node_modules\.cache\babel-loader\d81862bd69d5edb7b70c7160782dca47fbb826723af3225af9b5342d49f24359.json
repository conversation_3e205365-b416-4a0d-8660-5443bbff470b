{"ast": null, "code": "var _jsxFileName = \"c:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\hooks\\\\useAuth.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const savedToken = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n      if (savedToken && savedUser) {\n        try {\n          const userData = JSON.parse(savedUser);\n          setUser(userData);\n          setToken(savedToken);\n        } catch (error) {\n          console.error('Failed to initialize auth:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    initializeAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await authService.login(email, password);\n      console.log('Login response:', response); // Debug log\n      console.log('Response structure:', JSON.stringify(response, null, 2)); // More detailed debug\n\n      // Check if this is a mock response\n      if (response.message && response.message.includes('mock')) {\n        console.warn('🔄 Using mock authentication - backend not accessible');\n      }\n\n      // The API client returns the response body directly, so response.data contains the actual data\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n\n      // Normalize user data to match frontend expectations (handle both backend and mock responses)\n      const normalizedUser = {\n        id: userData.UserID || userData.id,\n        email: userData.Email || userData.email,\n        name: userData.Name || `${userData.firstName || userData.FirstName} ${userData.lastName || userData.LastName}`,\n        firstName: userData.FirstName || userData.firstName,\n        lastName: userData.LastName || userData.lastName,\n        role: userData.Role || userData.role,\n        // This is the key property for RBAC\n        isActive: userData.IsActive !== undefined ? userData.IsActive : true,\n        emailVerified: userData.EmailVerified !== undefined ? userData.EmailVerified : true\n      };\n      localStorage.setItem('token', newToken);\n      localStorage.setItem('user', JSON.stringify(normalizedUser));\n      setToken(newToken);\n      setUser(normalizedUser);\n      return {\n        success: true,\n        user: normalizedUser\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login failed:', error);\n      return {\n        success: false,\n        error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || 'Login failed'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await authService.register(userData);\n      const {\n        token: newToken,\n        user: newUser\n      } = response;\n      localStorage.setItem('token', newToken);\n      setToken(newToken);\n      setUser(newUser);\n      return {\n        success: true,\n        user: newUser\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Registration failed:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setToken(null);\n    setUser(null);\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await authService.updateProfile(profileData);\n      setUser(response.user);\n      return {\n        success: true,\n        user: response.user\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Profile update failed:', error);\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Profile update failed'\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 9\n  }, this);\n};\n_s2(AuthProvider, \"/pbUqy0QsBvMqKPYubk3+KKKH8I=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "initializeAuth", "savedToken", "savedUser", "userData", "JSON", "parse", "error", "console", "removeItem", "login", "email", "password", "response", "log", "stringify", "message", "includes", "warn", "newToken", "data", "normalizedUser", "id", "UserID", "Email", "name", "Name", "firstName", "FirstName", "lastName", "LastName", "role", "Role", "isActive", "IsActive", "undefined", "emailVerified", "EmailVerified", "setItem", "success", "_error$response", "_error$response$data", "register", "newUser", "_error$response2", "_error$response2$data", "logout", "updateProfile", "profileData", "_error$response3", "_error$response3$data", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/src/hooks/useAuth.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/auth';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n    const context = useContext(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n    const [user, setUser] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [token, setToken] = useState(localStorage.getItem('token'));\n\n    useEffect(() => {\n        const initializeAuth = async () => {\n            const savedToken = localStorage.getItem('token');\n            const savedUser = localStorage.getItem('user');\n            if (savedToken && savedUser) {\n                try {\n                    const userData = JSON.parse(savedUser);\n                    setUser(userData);\n                    setToken(savedToken);\n                } catch (error) {\n                    console.error('Failed to initialize auth:', error);\n                    localStorage.removeItem('token');\n                    localStorage.removeItem('user');\n                    setToken(null);\n                }\n            }\n            setLoading(false);\n        };\n\n        initializeAuth();\n    }, []);\n\n    const login = async (email, password) => {\n        try {\n            const response = await authService.login(email, password);\n            console.log('Login response:', response); // Debug log\n            console.log('Response structure:', JSON.stringify(response, null, 2)); // More detailed debug\n\n            // Check if this is a mock response\n            if (response.message && response.message.includes('mock')) {\n                console.warn('🔄 Using mock authentication - backend not accessible');\n            }\n\n            // The API client returns the response body directly, so response.data contains the actual data\n            const { token: newToken, user: userData } = response.data;\n\n            // Normalize user data to match frontend expectations (handle both backend and mock responses)\n            const normalizedUser = {\n                id: userData.UserID || userData.id,\n                email: userData.Email || userData.email,\n                name: userData.Name || `${userData.firstName || userData.FirstName} ${userData.lastName || userData.LastName}`,\n                firstName: userData.FirstName || userData.firstName,\n                lastName: userData.LastName || userData.lastName,\n                role: userData.Role || userData.role, // This is the key property for RBAC\n                isActive: userData.IsActive !== undefined ? userData.IsActive : true,\n                emailVerified: userData.EmailVerified !== undefined ? userData.EmailVerified : true\n            };\n\n            localStorage.setItem('token', newToken);\n            localStorage.setItem('user', JSON.stringify(normalizedUser));\n            setToken(newToken);\n            setUser(normalizedUser);\n\n            return { success: true, user: normalizedUser };\n        } catch (error) {\n            console.error('Login failed:', error);\n            return {\n                success: false,\n                error: error.response?.data?.message || error.message || 'Login failed'\n            };\n        }\n    };\n\n    const register = async (userData) => {\n        try {\n            const response = await authService.register(userData);\n            const { token: newToken, user: newUser } = response;\n            \n            localStorage.setItem('token', newToken);\n            setToken(newToken);\n            setUser(newUser);\n            \n            return { success: true, user: newUser };\n        } catch (error) {\n            console.error('Registration failed:', error);\n            return { \n                success: false, \n                error: error.response?.data?.message || 'Registration failed' \n            };\n        }\n    };\n\n    const logout = () => {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        setToken(null);\n        setUser(null);\n    };\n\n    const updateProfile = async (profileData) => {\n        try {\n            const response = await authService.updateProfile(profileData);\n            setUser(response.user);\n            return { success: true, user: response.user };\n        } catch (error) {\n            console.error('Profile update failed:', error);\n            return { \n                success: false, \n                error: error.response?.data?.message || 'Profile update failed' \n            };\n        }\n    };\n\n    const value = {\n        user,\n        token,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        isAuthenticated: !!user\n    };\n\n    return (\n        <AuthContext.Provider value={value}>\n            {children}\n        </AuthContext.Provider>\n    );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAClE;EACA,OAAOD,OAAO;AAClB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC1C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EAEjEnB,SAAS,CAAC,MAAM;IACZ,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAMC,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAChD,MAAMG,SAAS,GAAGJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC9C,IAAIE,UAAU,IAAIC,SAAS,EAAE;QACzB,IAAI;UACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC;UACtCT,OAAO,CAACU,QAAQ,CAAC;UACjBN,QAAQ,CAACI,UAAU,CAAC;QACxB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClDR,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;UAChCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;UAC/BX,QAAQ,CAAC,IAAI,CAAC;QAClB;MACJ;MACAF,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACrC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM/B,WAAW,CAAC4B,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;MACzDJ,OAAO,CAACM,GAAG,CAAC,iBAAiB,EAAED,QAAQ,CAAC,CAAC,CAAC;MAC1CL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAET,IAAI,CAACU,SAAS,CAACF,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEvE;MACA,IAAIA,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACG,OAAO,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QACvDT,OAAO,CAACU,IAAI,CAAC,uDAAuD,CAAC;MACzE;;MAEA;MACA,MAAM;QAAErB,KAAK,EAAEsB,QAAQ;QAAE1B,IAAI,EAAEW;MAAS,CAAC,GAAGS,QAAQ,CAACO,IAAI;;MAEzD;MACA,MAAMC,cAAc,GAAG;QACnBC,EAAE,EAAElB,QAAQ,CAACmB,MAAM,IAAInB,QAAQ,CAACkB,EAAE;QAClCX,KAAK,EAAEP,QAAQ,CAACoB,KAAK,IAAIpB,QAAQ,CAACO,KAAK;QACvCc,IAAI,EAAErB,QAAQ,CAACsB,IAAI,IAAI,GAAGtB,QAAQ,CAACuB,SAAS,IAAIvB,QAAQ,CAACwB,SAAS,IAAIxB,QAAQ,CAACyB,QAAQ,IAAIzB,QAAQ,CAAC0B,QAAQ,EAAE;QAC9GH,SAAS,EAAEvB,QAAQ,CAACwB,SAAS,IAAIxB,QAAQ,CAACuB,SAAS;QACnDE,QAAQ,EAAEzB,QAAQ,CAAC0B,QAAQ,IAAI1B,QAAQ,CAACyB,QAAQ;QAChDE,IAAI,EAAE3B,QAAQ,CAAC4B,IAAI,IAAI5B,QAAQ,CAAC2B,IAAI;QAAE;QACtCE,QAAQ,EAAE7B,QAAQ,CAAC8B,QAAQ,KAAKC,SAAS,GAAG/B,QAAQ,CAAC8B,QAAQ,GAAG,IAAI;QACpEE,aAAa,EAAEhC,QAAQ,CAACiC,aAAa,KAAKF,SAAS,GAAG/B,QAAQ,CAACiC,aAAa,GAAG;MACnF,CAAC;MAEDtC,YAAY,CAACuC,OAAO,CAAC,OAAO,EAAEnB,QAAQ,CAAC;MACvCpB,YAAY,CAACuC,OAAO,CAAC,MAAM,EAAEjC,IAAI,CAACU,SAAS,CAACM,cAAc,CAAC,CAAC;MAC5DvB,QAAQ,CAACqB,QAAQ,CAAC;MAClBzB,OAAO,CAAC2B,cAAc,CAAC;MAEvB,OAAO;QAAEkB,OAAO,EAAE,IAAI;QAAE9C,IAAI,EAAE4B;MAAe,CAAC;IAClD,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAAiC,eAAA,EAAAC,oBAAA;MACZjC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QACHgC,OAAO,EAAE,KAAK;QACdhC,KAAK,EAAE,EAAAiC,eAAA,GAAAjC,KAAK,CAACM,QAAQ,cAAA2B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpB,IAAI,cAAAqB,oBAAA,uBAApBA,oBAAA,CAAsBzB,OAAO,KAAIT,KAAK,CAACS,OAAO,IAAI;MAC7D,CAAC;IACL;EACJ,CAAC;EAED,MAAM0B,QAAQ,GAAG,MAAOtC,QAAQ,IAAK;IACjC,IAAI;MACA,MAAMS,QAAQ,GAAG,MAAM/B,WAAW,CAAC4D,QAAQ,CAACtC,QAAQ,CAAC;MACrD,MAAM;QAAEP,KAAK,EAAEsB,QAAQ;QAAE1B,IAAI,EAAEkD;MAAQ,CAAC,GAAG9B,QAAQ;MAEnDd,YAAY,CAACuC,OAAO,CAAC,OAAO,EAAEnB,QAAQ,CAAC;MACvCrB,QAAQ,CAACqB,QAAQ,CAAC;MAClBzB,OAAO,CAACiD,OAAO,CAAC;MAEhB,OAAO;QAAEJ,OAAO,EAAE,IAAI;QAAE9C,IAAI,EAAEkD;MAAQ,CAAC;IAC3C,CAAC,CAAC,OAAOpC,KAAK,EAAE;MAAA,IAAAqC,gBAAA,EAAAC,qBAAA;MACZrC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO;QACHgC,OAAO,EAAE,KAAK;QACdhC,KAAK,EAAE,EAAAqC,gBAAA,GAAArC,KAAK,CAACM,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsB7B,OAAO,KAAI;MAC5C,CAAC;IACL;EACJ,CAAC;EAED,MAAM8B,MAAM,GAAGA,CAAA,KAAM;IACjB/C,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;IAC/BX,QAAQ,CAAC,IAAI,CAAC;IACdJ,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMqD,aAAa,GAAG,MAAOC,WAAW,IAAK;IACzC,IAAI;MACA,MAAMnC,QAAQ,GAAG,MAAM/B,WAAW,CAACiE,aAAa,CAACC,WAAW,CAAC;MAC7DtD,OAAO,CAACmB,QAAQ,CAACpB,IAAI,CAAC;MACtB,OAAO;QAAE8C,OAAO,EAAE,IAAI;QAAE9C,IAAI,EAAEoB,QAAQ,CAACpB;MAAK,CAAC;IACjD,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA;MACZ1C,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QACHgC,OAAO,EAAE,KAAK;QACdhC,KAAK,EAAE,EAAA0C,gBAAA,GAAA1C,KAAK,CAACM,QAAQ,cAAAoC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBlC,OAAO,KAAI;MAC5C,CAAC;IACL;EACJ,CAAC;EAED,MAAMmC,KAAK,GAAG;IACV1D,IAAI;IACJI,KAAK;IACLF,OAAO;IACPe,KAAK;IACLgC,QAAQ;IACRI,MAAM;IACNC,aAAa;IACbK,eAAe,EAAE,CAAC,CAAC3D;EACvB,CAAC;EAED,oBACIT,OAAA,CAACC,WAAW,CAACoE,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA5D,QAAA,EAC9BA;EAAQ;IAAA+D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAE/B,CAAC;AAACjE,GAAA,CA5HWF,YAAY;AAAAoE,EAAA,GAAZpE,YAAY;AAAA,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}