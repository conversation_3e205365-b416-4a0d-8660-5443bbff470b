{"ast": null, "code": "import { <PERSON><PERSON>, FileLoader, Group, MeshPhongMaterial, Float32BufferAttribute, Color, BufferGeometry, Mesh } from \"three\";\nimport { unzipSync } from \"fflate\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nclass AMFLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data) {\n    function loadDocument(data2) {\n      let view = new DataView(data2);\n      const magic = String.fromCharCode(view.getUint8(0), view.getUint8(1));\n      if (magic === \"PK\") {\n        let zip = null;\n        let file = null;\n        console.log(\"THREE.AMFLoader: Loading Zip\");\n        try {\n          zip = unzipSync(new Uint8Array(data2));\n        } catch (e) {\n          if (e instanceof ReferenceError) {\n            console.log(\"THREE.AMFLoader: fflate missing and file is compressed.\");\n            return null;\n          }\n        }\n        for (file in zip) {\n          if (file.toLowerCase().substr(-4) === \".amf\") {\n            break;\n          }\n        }\n        console.log(\"THREE.AMFLoader: Trying to load file asset: \" + file);\n        view = new DataView(zip[file].buffer);\n      }\n      const fileText = decodeText(view);\n      const xmlData2 = new DOMParser().parseFromString(fileText, \"application/xml\");\n      if (xmlData2.documentElement.nodeName.toLowerCase() !== \"amf\") {\n        console.log(\"THREE.AMFLoader: Error loading AMF - no AMF document found.\");\n        return null;\n      }\n      return xmlData2;\n    }\n    function loadDocumentScale(node) {\n      let scale = 1;\n      let unit = \"millimeter\";\n      if (node.documentElement.attributes.unit !== void 0) {\n        unit = node.documentElement.attributes.unit.value.toLowerCase();\n      }\n      const scaleUnits = {\n        millimeter: 1,\n        inch: 25.4,\n        feet: 304.8,\n        meter: 1e3,\n        micron: 1e-3\n      };\n      if (scaleUnits[unit] !== void 0) {\n        scale = scaleUnits[unit];\n      }\n      console.log(\"THREE.AMFLoader: Unit scale: \" + scale);\n      return scale;\n    }\n    function loadMaterials(node) {\n      let matName = \"AMF Material\";\n      const matId = node.attributes.id.textContent;\n      let color = {\n        r: 1,\n        g: 1,\n        b: 1,\n        a: 1\n      };\n      let loadedMaterial = null;\n      for (let i2 = 0; i2 < node.childNodes.length; i2++) {\n        const matChildEl = node.childNodes[i2];\n        if (matChildEl.nodeName === \"metadata\" && matChildEl.attributes.type !== void 0) {\n          if (matChildEl.attributes.type.value === \"name\") {\n            matName = matChildEl.textContent;\n          }\n        } else if (matChildEl.nodeName === \"color\") {\n          color = loadColor(matChildEl);\n        }\n      }\n      loadedMaterial = new MeshPhongMaterial({\n        flatShading: true,\n        color: new Color(color.r, color.g, color.b),\n        name: matName\n      });\n      if (color.a !== 1) {\n        loadedMaterial.transparent = true;\n        loadedMaterial.opacity = color.a;\n      }\n      return {\n        id: matId,\n        material: loadedMaterial\n      };\n    }\n    function loadColor(node) {\n      const color = {\n        r: 1,\n        g: 1,\n        b: 1,\n        a: 1\n      };\n      for (let i2 = 0; i2 < node.childNodes.length; i2++) {\n        const matColor = node.childNodes[i2];\n        if (matColor.nodeName === \"r\") {\n          color.r = matColor.textContent;\n        } else if (matColor.nodeName === \"g\") {\n          color.g = matColor.textContent;\n        } else if (matColor.nodeName === \"b\") {\n          color.b = matColor.textContent;\n        } else if (matColor.nodeName === \"a\") {\n          color.a = matColor.textContent;\n        }\n      }\n      return color;\n    }\n    function loadMeshVolume(node) {\n      const volume = {\n        name: \"\",\n        triangles: [],\n        materialid: null\n      };\n      let currVolumeNode = node.firstElementChild;\n      if (node.attributes.materialid !== void 0) {\n        volume.materialId = node.attributes.materialid.nodeValue;\n      }\n      while (currVolumeNode) {\n        if (currVolumeNode.nodeName === \"metadata\") {\n          if (currVolumeNode.attributes.type !== void 0) {\n            if (currVolumeNode.attributes.type.value === \"name\") {\n              volume.name = currVolumeNode.textContent;\n            }\n          }\n        } else if (currVolumeNode.nodeName === \"triangle\") {\n          const v1 = currVolumeNode.getElementsByTagName(\"v1\")[0].textContent;\n          const v2 = currVolumeNode.getElementsByTagName(\"v2\")[0].textContent;\n          const v3 = currVolumeNode.getElementsByTagName(\"v3\")[0].textContent;\n          volume.triangles.push(v1, v2, v3);\n        }\n        currVolumeNode = currVolumeNode.nextElementSibling;\n      }\n      return volume;\n    }\n    function loadMeshVertices(node) {\n      const vertArray = [];\n      const normalArray = [];\n      let currVerticesNode = node.firstElementChild;\n      while (currVerticesNode) {\n        if (currVerticesNode.nodeName === \"vertex\") {\n          let vNode = currVerticesNode.firstElementChild;\n          while (vNode) {\n            if (vNode.nodeName === \"coordinates\") {\n              const x = vNode.getElementsByTagName(\"x\")[0].textContent;\n              const y = vNode.getElementsByTagName(\"y\")[0].textContent;\n              const z = vNode.getElementsByTagName(\"z\")[0].textContent;\n              vertArray.push(x, y, z);\n            } else if (vNode.nodeName === \"normal\") {\n              const nx = vNode.getElementsByTagName(\"nx\")[0].textContent;\n              const ny = vNode.getElementsByTagName(\"ny\")[0].textContent;\n              const nz = vNode.getElementsByTagName(\"nz\")[0].textContent;\n              normalArray.push(nx, ny, nz);\n            }\n            vNode = vNode.nextElementSibling;\n          }\n        }\n        currVerticesNode = currVerticesNode.nextElementSibling;\n      }\n      return {\n        vertices: vertArray,\n        normals: normalArray\n      };\n    }\n    function loadObject(node) {\n      const objId = node.attributes.id.textContent;\n      const loadedObject = {\n        name: \"amfobject\",\n        meshes: []\n      };\n      let currColor = null;\n      let currObjNode = node.firstElementChild;\n      while (currObjNode) {\n        if (currObjNode.nodeName === \"metadata\") {\n          if (currObjNode.attributes.type !== void 0) {\n            if (currObjNode.attributes.type.value === \"name\") {\n              loadedObject.name = currObjNode.textContent;\n            }\n          }\n        } else if (currObjNode.nodeName === \"color\") {\n          currColor = loadColor(currObjNode);\n        } else if (currObjNode.nodeName === \"mesh\") {\n          let currMeshNode = currObjNode.firstElementChild;\n          const mesh = {\n            vertices: [],\n            normals: [],\n            volumes: [],\n            color: currColor\n          };\n          while (currMeshNode) {\n            if (currMeshNode.nodeName === \"vertices\") {\n              const loadedVertices = loadMeshVertices(currMeshNode);\n              mesh.normals = mesh.normals.concat(loadedVertices.normals);\n              mesh.vertices = mesh.vertices.concat(loadedVertices.vertices);\n            } else if (currMeshNode.nodeName === \"volume\") {\n              mesh.volumes.push(loadMeshVolume(currMeshNode));\n            }\n            currMeshNode = currMeshNode.nextElementSibling;\n          }\n          loadedObject.meshes.push(mesh);\n        }\n        currObjNode = currObjNode.nextElementSibling;\n      }\n      return {\n        id: objId,\n        obj: loadedObject\n      };\n    }\n    const xmlData = loadDocument(data);\n    let amfName = \"\";\n    let amfAuthor = \"\";\n    const amfScale = loadDocumentScale(xmlData);\n    const amfMaterials = {};\n    const amfObjects = {};\n    const childNodes = xmlData.documentElement.childNodes;\n    let i, j;\n    for (i = 0; i < childNodes.length; i++) {\n      const child = childNodes[i];\n      if (child.nodeName === \"metadata\") {\n        if (child.attributes.type !== void 0) {\n          if (child.attributes.type.value === \"name\") {\n            amfName = child.textContent;\n          } else if (child.attributes.type.value === \"author\") {\n            amfAuthor = child.textContent;\n          }\n        }\n      } else if (child.nodeName === \"material\") {\n        const loadedMaterial = loadMaterials(child);\n        amfMaterials[loadedMaterial.id] = loadedMaterial.material;\n      } else if (child.nodeName === \"object\") {\n        const loadedObject = loadObject(child);\n        amfObjects[loadedObject.id] = loadedObject.obj;\n      }\n    }\n    const sceneObject = new Group();\n    const defaultMaterial = new MeshPhongMaterial({\n      color: 11184895,\n      flatShading: true\n    });\n    sceneObject.name = amfName;\n    sceneObject.userData.author = amfAuthor;\n    sceneObject.userData.loader = \"AMF\";\n    for (const id in amfObjects) {\n      const part = amfObjects[id];\n      const meshes = part.meshes;\n      const newObject = new Group();\n      newObject.name = part.name || \"\";\n      for (i = 0; i < meshes.length; i++) {\n        let objDefaultMaterial = defaultMaterial;\n        const mesh = meshes[i];\n        const vertices = new Float32BufferAttribute(mesh.vertices, 3);\n        let normals = null;\n        if (mesh.normals.length) {\n          normals = new Float32BufferAttribute(mesh.normals, 3);\n        }\n        if (mesh.color) {\n          const color = mesh.color;\n          objDefaultMaterial = defaultMaterial.clone();\n          objDefaultMaterial.color = new Color(color.r, color.g, color.b);\n          if (color.a !== 1) {\n            objDefaultMaterial.transparent = true;\n            objDefaultMaterial.opacity = color.a;\n          }\n        }\n        const volumes = mesh.volumes;\n        for (j = 0; j < volumes.length; j++) {\n          const volume = volumes[j];\n          const newGeometry = new BufferGeometry();\n          let material = objDefaultMaterial;\n          newGeometry.setIndex(volume.triangles);\n          newGeometry.setAttribute(\"position\", vertices.clone());\n          if (normals) {\n            newGeometry.setAttribute(\"normal\", normals.clone());\n          }\n          if (amfMaterials[volume.materialId] !== void 0) {\n            material = amfMaterials[volume.materialId];\n          }\n          newGeometry.scale(amfScale, amfScale, amfScale);\n          newObject.add(new Mesh(newGeometry, material.clone()));\n        }\n      }\n      sceneObject.add(newObject);\n    }\n    return sceneObject;\n  }\n}\nexport { AMFLoader };", "map": {"version": 3, "names": ["AMFLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "data", "loadDocument", "data2", "view", "DataView", "magic", "String", "fromCharCode", "getUint8", "zip", "file", "log", "unzipSync", "Uint8Array", "ReferenceError", "toLowerCase", "substr", "buffer", "fileText", "decodeText", "xmlData2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "documentElement", "nodeName", "loadDocumentScale", "node", "scale", "unit", "attributes", "value", "scaleUnits", "millimeter", "inch", "feet", "meter", "micron", "loadMaterials", "<PERSON><PERSON><PERSON>", "matId", "id", "textContent", "color", "r", "g", "b", "a", "loadedMaterial", "i2", "childNodes", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "loadColor", "MeshPhongMaterial", "flatShading", "Color", "name", "transparent", "opacity", "material", "matColor", "loadMeshVolume", "volume", "triangles", "materialid", "currVolumeNode", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "materialId", "nodeValue", "v1", "getElementsByTagName", "v2", "v3", "push", "nextElement<PERSON><PERSON>ling", "loadMeshVertices", "vertArray", "normalArray", "currVerticesNode", "vNode", "x", "y", "z", "nx", "ny", "nz", "vertices", "normals", "loadObject", "objId", "loadedObject", "meshes", "currColor", "currObjNode", "currMeshNode", "mesh", "volumes", "loadedVertices", "concat", "obj", "xmlData", "amfName", "amfAuthor", "amfScale", "amfMaterials", "amfObjects", "i", "j", "child", "sceneObject", "Group", "defaultMaterial", "userData", "author", "part", "newObject", "objDefaultMaterial", "Float32BufferAttribute", "clone", "newGeometry", "BufferGeometry", "setIndex", "setAttribute", "add", "<PERSON><PERSON>"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\loaders\\AMFLoader.js"], "sourcesContent": ["import {\n  BufferGeometry,\n  Color,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  Loader,\n  LoaderUtils,\n  Mesh,\n  MeshPhongMaterial,\n} from 'three'\nimport { unzipSync } from 'fflate'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\n/**\n * Description: Early release of an AMF Loader following the pattern of the\n * example loaders in the three.js project.\n *\n * Usage:\n *\tconst loader = new AMFLoader();\n *\tloader.load('/path/to/project.amf', function(objecttree) {\n *\t\tscene.add(objecttree);\n *\t});\n *\n * Materials now supported, material colors supported\n * Zip support, requires fflate\n * No constellation support (yet)!\n *\n */\n\nclass AMFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    function loadDocument(data) {\n      let view = new DataView(data)\n      const magic = String.fromCharCode(view.getUint8(0), view.getUint8(1))\n\n      if (magic === 'PK') {\n        let zip = null\n        let file = null\n\n        console.log('THREE.AMFLoader: Loading Zip')\n\n        try {\n          zip = unzipSync(new Uint8Array(data))\n        } catch (e) {\n          if (e instanceof ReferenceError) {\n            console.log('THREE.AMFLoader: fflate missing and file is compressed.')\n            return null\n          }\n        }\n\n        for (file in zip) {\n          if (file.toLowerCase().substr(-4) === '.amf') {\n            break\n          }\n        }\n\n        console.log('THREE.AMFLoader: Trying to load file asset: ' + file)\n        view = new DataView(zip[file].buffer)\n      }\n\n      const fileText = decodeText(view)\n      const xmlData = new DOMParser().parseFromString(fileText, 'application/xml')\n\n      if (xmlData.documentElement.nodeName.toLowerCase() !== 'amf') {\n        console.log('THREE.AMFLoader: Error loading AMF - no AMF document found.')\n        return null\n      }\n\n      return xmlData\n    }\n\n    function loadDocumentScale(node) {\n      let scale = 1.0\n      let unit = 'millimeter'\n\n      if (node.documentElement.attributes.unit !== undefined) {\n        unit = node.documentElement.attributes.unit.value.toLowerCase()\n      }\n\n      const scaleUnits = {\n        millimeter: 1.0,\n        inch: 25.4,\n        feet: 304.8,\n        meter: 1000.0,\n        micron: 0.001,\n      }\n\n      if (scaleUnits[unit] !== undefined) {\n        scale = scaleUnits[unit]\n      }\n\n      console.log('THREE.AMFLoader: Unit scale: ' + scale)\n      return scale\n    }\n\n    function loadMaterials(node) {\n      let matName = 'AMF Material'\n      const matId = node.attributes.id.textContent\n      let color = { r: 1.0, g: 1.0, b: 1.0, a: 1.0 }\n\n      let loadedMaterial = null\n\n      for (let i = 0; i < node.childNodes.length; i++) {\n        const matChildEl = node.childNodes[i]\n\n        if (matChildEl.nodeName === 'metadata' && matChildEl.attributes.type !== undefined) {\n          if (matChildEl.attributes.type.value === 'name') {\n            matName = matChildEl.textContent\n          }\n        } else if (matChildEl.nodeName === 'color') {\n          color = loadColor(matChildEl)\n        }\n      }\n\n      loadedMaterial = new MeshPhongMaterial({\n        flatShading: true,\n        color: new Color(color.r, color.g, color.b),\n        name: matName,\n      })\n\n      if (color.a !== 1.0) {\n        loadedMaterial.transparent = true\n        loadedMaterial.opacity = color.a\n      }\n\n      return { id: matId, material: loadedMaterial }\n    }\n\n    function loadColor(node) {\n      const color = { r: 1.0, g: 1.0, b: 1.0, a: 1.0 }\n\n      for (let i = 0; i < node.childNodes.length; i++) {\n        const matColor = node.childNodes[i]\n\n        if (matColor.nodeName === 'r') {\n          color.r = matColor.textContent\n        } else if (matColor.nodeName === 'g') {\n          color.g = matColor.textContent\n        } else if (matColor.nodeName === 'b') {\n          color.b = matColor.textContent\n        } else if (matColor.nodeName === 'a') {\n          color.a = matColor.textContent\n        }\n      }\n\n      return color\n    }\n\n    function loadMeshVolume(node) {\n      const volume = { name: '', triangles: [], materialid: null }\n\n      let currVolumeNode = node.firstElementChild\n\n      if (node.attributes.materialid !== undefined) {\n        volume.materialId = node.attributes.materialid.nodeValue\n      }\n\n      while (currVolumeNode) {\n        if (currVolumeNode.nodeName === 'metadata') {\n          if (currVolumeNode.attributes.type !== undefined) {\n            if (currVolumeNode.attributes.type.value === 'name') {\n              volume.name = currVolumeNode.textContent\n            }\n          }\n        } else if (currVolumeNode.nodeName === 'triangle') {\n          const v1 = currVolumeNode.getElementsByTagName('v1')[0].textContent\n          const v2 = currVolumeNode.getElementsByTagName('v2')[0].textContent\n          const v3 = currVolumeNode.getElementsByTagName('v3')[0].textContent\n\n          volume.triangles.push(v1, v2, v3)\n        }\n\n        currVolumeNode = currVolumeNode.nextElementSibling\n      }\n\n      return volume\n    }\n\n    function loadMeshVertices(node) {\n      const vertArray = []\n      const normalArray = []\n      let currVerticesNode = node.firstElementChild\n\n      while (currVerticesNode) {\n        if (currVerticesNode.nodeName === 'vertex') {\n          let vNode = currVerticesNode.firstElementChild\n\n          while (vNode) {\n            if (vNode.nodeName === 'coordinates') {\n              const x = vNode.getElementsByTagName('x')[0].textContent\n              const y = vNode.getElementsByTagName('y')[0].textContent\n              const z = vNode.getElementsByTagName('z')[0].textContent\n\n              vertArray.push(x, y, z)\n            } else if (vNode.nodeName === 'normal') {\n              const nx = vNode.getElementsByTagName('nx')[0].textContent\n              const ny = vNode.getElementsByTagName('ny')[0].textContent\n              const nz = vNode.getElementsByTagName('nz')[0].textContent\n\n              normalArray.push(nx, ny, nz)\n            }\n\n            vNode = vNode.nextElementSibling\n          }\n        }\n\n        currVerticesNode = currVerticesNode.nextElementSibling\n      }\n\n      return { vertices: vertArray, normals: normalArray }\n    }\n\n    function loadObject(node) {\n      const objId = node.attributes.id.textContent\n      const loadedObject = { name: 'amfobject', meshes: [] }\n      let currColor = null\n      let currObjNode = node.firstElementChild\n\n      while (currObjNode) {\n        if (currObjNode.nodeName === 'metadata') {\n          if (currObjNode.attributes.type !== undefined) {\n            if (currObjNode.attributes.type.value === 'name') {\n              loadedObject.name = currObjNode.textContent\n            }\n          }\n        } else if (currObjNode.nodeName === 'color') {\n          currColor = loadColor(currObjNode)\n        } else if (currObjNode.nodeName === 'mesh') {\n          let currMeshNode = currObjNode.firstElementChild\n          const mesh = { vertices: [], normals: [], volumes: [], color: currColor }\n\n          while (currMeshNode) {\n            if (currMeshNode.nodeName === 'vertices') {\n              const loadedVertices = loadMeshVertices(currMeshNode)\n\n              mesh.normals = mesh.normals.concat(loadedVertices.normals)\n              mesh.vertices = mesh.vertices.concat(loadedVertices.vertices)\n            } else if (currMeshNode.nodeName === 'volume') {\n              mesh.volumes.push(loadMeshVolume(currMeshNode))\n            }\n\n            currMeshNode = currMeshNode.nextElementSibling\n          }\n\n          loadedObject.meshes.push(mesh)\n        }\n\n        currObjNode = currObjNode.nextElementSibling\n      }\n\n      return { id: objId, obj: loadedObject }\n    }\n\n    const xmlData = loadDocument(data)\n    let amfName = ''\n    let amfAuthor = ''\n    const amfScale = loadDocumentScale(xmlData)\n    const amfMaterials = {}\n    const amfObjects = {}\n    const childNodes = xmlData.documentElement.childNodes\n\n    let i, j\n\n    for (i = 0; i < childNodes.length; i++) {\n      const child = childNodes[i]\n\n      if (child.nodeName === 'metadata') {\n        if (child.attributes.type !== undefined) {\n          if (child.attributes.type.value === 'name') {\n            amfName = child.textContent\n          } else if (child.attributes.type.value === 'author') {\n            amfAuthor = child.textContent\n          }\n        }\n      } else if (child.nodeName === 'material') {\n        const loadedMaterial = loadMaterials(child)\n\n        amfMaterials[loadedMaterial.id] = loadedMaterial.material\n      } else if (child.nodeName === 'object') {\n        const loadedObject = loadObject(child)\n\n        amfObjects[loadedObject.id] = loadedObject.obj\n      }\n    }\n\n    const sceneObject = new Group()\n    const defaultMaterial = new MeshPhongMaterial({ color: 0xaaaaff, flatShading: true })\n\n    sceneObject.name = amfName\n    sceneObject.userData.author = amfAuthor\n    sceneObject.userData.loader = 'AMF'\n\n    for (const id in amfObjects) {\n      const part = amfObjects[id]\n      const meshes = part.meshes\n      const newObject = new Group()\n      newObject.name = part.name || ''\n\n      for (i = 0; i < meshes.length; i++) {\n        let objDefaultMaterial = defaultMaterial\n        const mesh = meshes[i]\n        const vertices = new Float32BufferAttribute(mesh.vertices, 3)\n        let normals = null\n\n        if (mesh.normals.length) {\n          normals = new Float32BufferAttribute(mesh.normals, 3)\n        }\n\n        if (mesh.color) {\n          const color = mesh.color\n\n          objDefaultMaterial = defaultMaterial.clone()\n          objDefaultMaterial.color = new Color(color.r, color.g, color.b)\n\n          if (color.a !== 1.0) {\n            objDefaultMaterial.transparent = true\n            objDefaultMaterial.opacity = color.a\n          }\n        }\n\n        const volumes = mesh.volumes\n\n        for (j = 0; j < volumes.length; j++) {\n          const volume = volumes[j]\n          const newGeometry = new BufferGeometry()\n          let material = objDefaultMaterial\n\n          newGeometry.setIndex(volume.triangles)\n          newGeometry.setAttribute('position', vertices.clone())\n\n          if (normals) {\n            newGeometry.setAttribute('normal', normals.clone())\n          }\n\n          if (amfMaterials[volume.materialId] !== undefined) {\n            material = amfMaterials[volume.materialId]\n          }\n\n          newGeometry.scale(amfScale, amfScale, amfScale)\n          newObject.add(new Mesh(newGeometry, material.clone()))\n        }\n      }\n\n      sceneObject.add(newObject)\n    }\n\n    return sceneObject\n  }\n}\n\nexport { AMFLoader }\n"], "mappings": ";;;AA8BA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMN,OAAO;IAC3CO,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;IAC/CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAI,CAAC;MACzB,SAAQE,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMN,OAAA,CAAQqB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMK,IAAA,EAAM;IACV,SAASC,aAAaC,KAAA,EAAM;MAC1B,IAAIC,IAAA,GAAO,IAAIC,QAAA,CAASF,KAAI;MAC5B,MAAMG,KAAA,GAAQC,MAAA,CAAOC,YAAA,CAAaJ,IAAA,CAAKK,QAAA,CAAS,CAAC,GAAGL,IAAA,CAAKK,QAAA,CAAS,CAAC,CAAC;MAEpE,IAAIH,KAAA,KAAU,MAAM;QAClB,IAAII,GAAA,GAAM;QACV,IAAIC,IAAA,GAAO;QAEXb,OAAA,CAAQc,GAAA,CAAI,8BAA8B;QAE1C,IAAI;UACFF,GAAA,GAAMG,SAAA,CAAU,IAAIC,UAAA,CAAWX,KAAI,CAAC;QACrC,SAAQN,CAAA,EAAP;UACA,IAAIA,CAAA,YAAakB,cAAA,EAAgB;YAC/BjB,OAAA,CAAQc,GAAA,CAAI,yDAAyD;YACrE,OAAO;UACR;QACF;QAED,KAAKD,IAAA,IAAQD,GAAA,EAAK;UAChB,IAAIC,IAAA,CAAKK,WAAA,CAAa,EAACC,MAAA,CAAO,EAAE,MAAM,QAAQ;YAC5C;UACD;QACF;QAEDnB,OAAA,CAAQc,GAAA,CAAI,iDAAiDD,IAAI;QACjEP,IAAA,GAAO,IAAIC,QAAA,CAASK,GAAA,CAAIC,IAAI,EAAEO,MAAM;MACrC;MAED,MAAMC,QAAA,GAAWC,UAAA,CAAWhB,IAAI;MAChC,MAAMiB,QAAA,GAAU,IAAIC,SAAA,CAAS,EAAGC,eAAA,CAAgBJ,QAAA,EAAU,iBAAiB;MAE3E,IAAIE,QAAA,CAAQG,eAAA,CAAgBC,QAAA,CAAST,WAAA,CAAW,MAAO,OAAO;QAC5DlB,OAAA,CAAQc,GAAA,CAAI,6DAA6D;QACzE,OAAO;MACR;MAED,OAAOS,QAAA;IACR;IAED,SAASK,kBAAkBC,IAAA,EAAM;MAC/B,IAAIC,KAAA,GAAQ;MACZ,IAAIC,IAAA,GAAO;MAEX,IAAIF,IAAA,CAAKH,eAAA,CAAgBM,UAAA,CAAWD,IAAA,KAAS,QAAW;QACtDA,IAAA,GAAOF,IAAA,CAAKH,eAAA,CAAgBM,UAAA,CAAWD,IAAA,CAAKE,KAAA,CAAMf,WAAA,CAAa;MAChE;MAED,MAAMgB,UAAA,GAAa;QACjBC,UAAA,EAAY;QACZC,IAAA,EAAM;QACNC,IAAA,EAAM;QACNC,KAAA,EAAO;QACPC,MAAA,EAAQ;MACT;MAED,IAAIL,UAAA,CAAWH,IAAI,MAAM,QAAW;QAClCD,KAAA,GAAQI,UAAA,CAAWH,IAAI;MACxB;MAED/B,OAAA,CAAQc,GAAA,CAAI,kCAAkCgB,KAAK;MACnD,OAAOA,KAAA;IACR;IAED,SAASU,cAAcX,IAAA,EAAM;MAC3B,IAAIY,OAAA,GAAU;MACd,MAAMC,KAAA,GAAQb,IAAA,CAAKG,UAAA,CAAWW,EAAA,CAAGC,WAAA;MACjC,IAAIC,KAAA,GAAQ;QAAEC,CAAA,EAAG;QAAKC,CAAA,EAAG;QAAKC,CAAA,EAAG;QAAKC,CAAA,EAAG;MAAK;MAE9C,IAAIC,cAAA,GAAiB;MAErB,SAASC,EAAA,GAAI,GAAGA,EAAA,GAAItB,IAAA,CAAKuB,UAAA,CAAWC,MAAA,EAAQF,EAAA,IAAK;QAC/C,MAAMG,UAAA,GAAazB,IAAA,CAAKuB,UAAA,CAAWD,EAAC;QAEpC,IAAIG,UAAA,CAAW3B,QAAA,KAAa,cAAc2B,UAAA,CAAWtB,UAAA,CAAWuB,IAAA,KAAS,QAAW;UAClF,IAAID,UAAA,CAAWtB,UAAA,CAAWuB,IAAA,CAAKtB,KAAA,KAAU,QAAQ;YAC/CQ,OAAA,GAAUa,UAAA,CAAWV,WAAA;UACtB;QACX,WAAmBU,UAAA,CAAW3B,QAAA,KAAa,SAAS;UAC1CkB,KAAA,GAAQW,SAAA,CAAUF,UAAU;QAC7B;MACF;MAEDJ,cAAA,GAAiB,IAAIO,iBAAA,CAAkB;QACrCC,WAAA,EAAa;QACbb,KAAA,EAAO,IAAIc,KAAA,CAAMd,KAAA,CAAMC,CAAA,EAAGD,KAAA,CAAME,CAAA,EAAGF,KAAA,CAAMG,CAAC;QAC1CY,IAAA,EAAMnB;MACd,CAAO;MAED,IAAII,KAAA,CAAMI,CAAA,KAAM,GAAK;QACnBC,cAAA,CAAeW,WAAA,GAAc;QAC7BX,cAAA,CAAeY,OAAA,GAAUjB,KAAA,CAAMI,CAAA;MAChC;MAED,OAAO;QAAEN,EAAA,EAAID,KAAA;QAAOqB,QAAA,EAAUb;MAAgB;IAC/C;IAED,SAASM,UAAU3B,IAAA,EAAM;MACvB,MAAMgB,KAAA,GAAQ;QAAEC,CAAA,EAAG;QAAKC,CAAA,EAAG;QAAKC,CAAA,EAAG;QAAKC,CAAA,EAAG;MAAK;MAEhD,SAASE,EAAA,GAAI,GAAGA,EAAA,GAAItB,IAAA,CAAKuB,UAAA,CAAWC,MAAA,EAAQF,EAAA,IAAK;QAC/C,MAAMa,QAAA,GAAWnC,IAAA,CAAKuB,UAAA,CAAWD,EAAC;QAElC,IAAIa,QAAA,CAASrC,QAAA,KAAa,KAAK;UAC7BkB,KAAA,CAAMC,CAAA,GAAIkB,QAAA,CAASpB,WAAA;QAC7B,WAAmBoB,QAAA,CAASrC,QAAA,KAAa,KAAK;UACpCkB,KAAA,CAAME,CAAA,GAAIiB,QAAA,CAASpB,WAAA;QAC7B,WAAmBoB,QAAA,CAASrC,QAAA,KAAa,KAAK;UACpCkB,KAAA,CAAMG,CAAA,GAAIgB,QAAA,CAASpB,WAAA;QAC7B,WAAmBoB,QAAA,CAASrC,QAAA,KAAa,KAAK;UACpCkB,KAAA,CAAMI,CAAA,GAAIe,QAAA,CAASpB,WAAA;QACpB;MACF;MAED,OAAOC,KAAA;IACR;IAED,SAASoB,eAAepC,IAAA,EAAM;MAC5B,MAAMqC,MAAA,GAAS;QAAEN,IAAA,EAAM;QAAIO,SAAA,EAAW,EAAE;QAAEC,UAAA,EAAY;MAAM;MAE5D,IAAIC,cAAA,GAAiBxC,IAAA,CAAKyC,iBAAA;MAE1B,IAAIzC,IAAA,CAAKG,UAAA,CAAWoC,UAAA,KAAe,QAAW;QAC5CF,MAAA,CAAOK,UAAA,GAAa1C,IAAA,CAAKG,UAAA,CAAWoC,UAAA,CAAWI,SAAA;MAChD;MAED,OAAOH,cAAA,EAAgB;QACrB,IAAIA,cAAA,CAAe1C,QAAA,KAAa,YAAY;UAC1C,IAAI0C,cAAA,CAAerC,UAAA,CAAWuB,IAAA,KAAS,QAAW;YAChD,IAAIc,cAAA,CAAerC,UAAA,CAAWuB,IAAA,CAAKtB,KAAA,KAAU,QAAQ;cACnDiC,MAAA,CAAON,IAAA,GAAOS,cAAA,CAAezB,WAAA;YAC9B;UACF;QACX,WAAmByB,cAAA,CAAe1C,QAAA,KAAa,YAAY;UACjD,MAAM8C,EAAA,GAAKJ,cAAA,CAAeK,oBAAA,CAAqB,IAAI,EAAE,CAAC,EAAE9B,WAAA;UACxD,MAAM+B,EAAA,GAAKN,cAAA,CAAeK,oBAAA,CAAqB,IAAI,EAAE,CAAC,EAAE9B,WAAA;UACxD,MAAMgC,EAAA,GAAKP,cAAA,CAAeK,oBAAA,CAAqB,IAAI,EAAE,CAAC,EAAE9B,WAAA;UAExDsB,MAAA,CAAOC,SAAA,CAAUU,IAAA,CAAKJ,EAAA,EAAIE,EAAA,EAAIC,EAAE;QACjC;QAEDP,cAAA,GAAiBA,cAAA,CAAeS,kBAAA;MACjC;MAED,OAAOZ,MAAA;IACR;IAED,SAASa,iBAAiBlD,IAAA,EAAM;MAC9B,MAAMmD,SAAA,GAAY,EAAE;MACpB,MAAMC,WAAA,GAAc,EAAE;MACtB,IAAIC,gBAAA,GAAmBrD,IAAA,CAAKyC,iBAAA;MAE5B,OAAOY,gBAAA,EAAkB;QACvB,IAAIA,gBAAA,CAAiBvD,QAAA,KAAa,UAAU;UAC1C,IAAIwD,KAAA,GAAQD,gBAAA,CAAiBZ,iBAAA;UAE7B,OAAOa,KAAA,EAAO;YACZ,IAAIA,KAAA,CAAMxD,QAAA,KAAa,eAAe;cACpC,MAAMyD,CAAA,GAAID,KAAA,CAAMT,oBAAA,CAAqB,GAAG,EAAE,CAAC,EAAE9B,WAAA;cAC7C,MAAMyC,CAAA,GAAIF,KAAA,CAAMT,oBAAA,CAAqB,GAAG,EAAE,CAAC,EAAE9B,WAAA;cAC7C,MAAM0C,CAAA,GAAIH,KAAA,CAAMT,oBAAA,CAAqB,GAAG,EAAE,CAAC,EAAE9B,WAAA;cAE7CoC,SAAA,CAAUH,IAAA,CAAKO,CAAA,EAAGC,CAAA,EAAGC,CAAC;YACpC,WAAuBH,KAAA,CAAMxD,QAAA,KAAa,UAAU;cACtC,MAAM4D,EAAA,GAAKJ,KAAA,CAAMT,oBAAA,CAAqB,IAAI,EAAE,CAAC,EAAE9B,WAAA;cAC/C,MAAM4C,EAAA,GAAKL,KAAA,CAAMT,oBAAA,CAAqB,IAAI,EAAE,CAAC,EAAE9B,WAAA;cAC/C,MAAM6C,EAAA,GAAKN,KAAA,CAAMT,oBAAA,CAAqB,IAAI,EAAE,CAAC,EAAE9B,WAAA;cAE/CqC,WAAA,CAAYJ,IAAA,CAAKU,EAAA,EAAIC,EAAA,EAAIC,EAAE;YAC5B;YAEDN,KAAA,GAAQA,KAAA,CAAML,kBAAA;UACf;QACF;QAEDI,gBAAA,GAAmBA,gBAAA,CAAiBJ,kBAAA;MACrC;MAED,OAAO;QAAEY,QAAA,EAAUV,SAAA;QAAWW,OAAA,EAASV;MAAa;IACrD;IAED,SAASW,WAAW/D,IAAA,EAAM;MACxB,MAAMgE,KAAA,GAAQhE,IAAA,CAAKG,UAAA,CAAWW,EAAA,CAAGC,WAAA;MACjC,MAAMkD,YAAA,GAAe;QAAElC,IAAA,EAAM;QAAamC,MAAA,EAAQ;MAAI;MACtD,IAAIC,SAAA,GAAY;MAChB,IAAIC,WAAA,GAAcpE,IAAA,CAAKyC,iBAAA;MAEvB,OAAO2B,WAAA,EAAa;QAClB,IAAIA,WAAA,CAAYtE,QAAA,KAAa,YAAY;UACvC,IAAIsE,WAAA,CAAYjE,UAAA,CAAWuB,IAAA,KAAS,QAAW;YAC7C,IAAI0C,WAAA,CAAYjE,UAAA,CAAWuB,IAAA,CAAKtB,KAAA,KAAU,QAAQ;cAChD6D,YAAA,CAAalC,IAAA,GAAOqC,WAAA,CAAYrD,WAAA;YACjC;UACF;QACX,WAAmBqD,WAAA,CAAYtE,QAAA,KAAa,SAAS;UAC3CqE,SAAA,GAAYxC,SAAA,CAAUyC,WAAW;QAC3C,WAAmBA,WAAA,CAAYtE,QAAA,KAAa,QAAQ;UAC1C,IAAIuE,YAAA,GAAeD,WAAA,CAAY3B,iBAAA;UAC/B,MAAM6B,IAAA,GAAO;YAAET,QAAA,EAAU;YAAIC,OAAA,EAAS,EAAE;YAAES,OAAA,EAAS;YAAIvD,KAAA,EAAOmD;UAAW;UAEzE,OAAOE,YAAA,EAAc;YACnB,IAAIA,YAAA,CAAavE,QAAA,KAAa,YAAY;cACxC,MAAM0E,cAAA,GAAiBtB,gBAAA,CAAiBmB,YAAY;cAEpDC,IAAA,CAAKR,OAAA,GAAUQ,IAAA,CAAKR,OAAA,CAAQW,MAAA,CAAOD,cAAA,CAAeV,OAAO;cACzDQ,IAAA,CAAKT,QAAA,GAAWS,IAAA,CAAKT,QAAA,CAASY,MAAA,CAAOD,cAAA,CAAeX,QAAQ;YAC1E,WAAuBQ,YAAA,CAAavE,QAAA,KAAa,UAAU;cAC7CwE,IAAA,CAAKC,OAAA,CAAQvB,IAAA,CAAKZ,cAAA,CAAeiC,YAAY,CAAC;YAC/C;YAEDA,YAAA,GAAeA,YAAA,CAAapB,kBAAA;UAC7B;UAEDgB,YAAA,CAAaC,MAAA,CAAOlB,IAAA,CAAKsB,IAAI;QAC9B;QAEDF,WAAA,GAAcA,WAAA,CAAYnB,kBAAA;MAC3B;MAED,OAAO;QAAEnC,EAAA,EAAIkD,KAAA;QAAOU,GAAA,EAAKT;MAAc;IACxC;IAED,MAAMU,OAAA,GAAUpG,YAAA,CAAaD,IAAI;IACjC,IAAIsG,OAAA,GAAU;IACd,IAAIC,SAAA,GAAY;IAChB,MAAMC,QAAA,GAAW/E,iBAAA,CAAkB4E,OAAO;IAC1C,MAAMI,YAAA,GAAe,CAAE;IACvB,MAAMC,UAAA,GAAa,CAAE;IACrB,MAAMzD,UAAA,GAAaoD,OAAA,CAAQ9E,eAAA,CAAgB0B,UAAA;IAE3C,IAAI0D,CAAA,EAAGC,CAAA;IAEP,KAAKD,CAAA,GAAI,GAAGA,CAAA,GAAI1D,UAAA,CAAWC,MAAA,EAAQyD,CAAA,IAAK;MACtC,MAAME,KAAA,GAAQ5D,UAAA,CAAW0D,CAAC;MAE1B,IAAIE,KAAA,CAAMrF,QAAA,KAAa,YAAY;QACjC,IAAIqF,KAAA,CAAMhF,UAAA,CAAWuB,IAAA,KAAS,QAAW;UACvC,IAAIyD,KAAA,CAAMhF,UAAA,CAAWuB,IAAA,CAAKtB,KAAA,KAAU,QAAQ;YAC1CwE,OAAA,GAAUO,KAAA,CAAMpE,WAAA;UACjB,WAAUoE,KAAA,CAAMhF,UAAA,CAAWuB,IAAA,CAAKtB,KAAA,KAAU,UAAU;YACnDyE,SAAA,GAAYM,KAAA,CAAMpE,WAAA;UACnB;QACF;MACT,WAAiBoE,KAAA,CAAMrF,QAAA,KAAa,YAAY;QACxC,MAAMuB,cAAA,GAAiBV,aAAA,CAAcwE,KAAK;QAE1CJ,YAAA,CAAa1D,cAAA,CAAeP,EAAE,IAAIO,cAAA,CAAea,QAAA;MACzD,WAAiBiD,KAAA,CAAMrF,QAAA,KAAa,UAAU;QACtC,MAAMmE,YAAA,GAAeF,UAAA,CAAWoB,KAAK;QAErCH,UAAA,CAAWf,YAAA,CAAanD,EAAE,IAAImD,YAAA,CAAaS,GAAA;MAC5C;IACF;IAED,MAAMU,WAAA,GAAc,IAAIC,KAAA,CAAO;IAC/B,MAAMC,eAAA,GAAkB,IAAI1D,iBAAA,CAAkB;MAAEZ,KAAA,EAAO;MAAUa,WAAA,EAAa;IAAA,CAAM;IAEpFuD,WAAA,CAAYrD,IAAA,GAAO6C,OAAA;IACnBQ,WAAA,CAAYG,QAAA,CAASC,MAAA,GAASX,SAAA;IAC9BO,WAAA,CAAYG,QAAA,CAAShI,MAAA,GAAS;IAE9B,WAAWuD,EAAA,IAAMkE,UAAA,EAAY;MAC3B,MAAMS,IAAA,GAAOT,UAAA,CAAWlE,EAAE;MAC1B,MAAMoD,MAAA,GAASuB,IAAA,CAAKvB,MAAA;MACpB,MAAMwB,SAAA,GAAY,IAAIL,KAAA,CAAO;MAC7BK,SAAA,CAAU3D,IAAA,GAAO0D,IAAA,CAAK1D,IAAA,IAAQ;MAE9B,KAAKkD,CAAA,GAAI,GAAGA,CAAA,GAAIf,MAAA,CAAO1C,MAAA,EAAQyD,CAAA,IAAK;QAClC,IAAIU,kBAAA,GAAqBL,eAAA;QACzB,MAAMhB,IAAA,GAAOJ,MAAA,CAAOe,CAAC;QACrB,MAAMpB,QAAA,GAAW,IAAI+B,sBAAA,CAAuBtB,IAAA,CAAKT,QAAA,EAAU,CAAC;QAC5D,IAAIC,OAAA,GAAU;QAEd,IAAIQ,IAAA,CAAKR,OAAA,CAAQtC,MAAA,EAAQ;UACvBsC,OAAA,GAAU,IAAI8B,sBAAA,CAAuBtB,IAAA,CAAKR,OAAA,EAAS,CAAC;QACrD;QAED,IAAIQ,IAAA,CAAKtD,KAAA,EAAO;UACd,MAAMA,KAAA,GAAQsD,IAAA,CAAKtD,KAAA;UAEnB2E,kBAAA,GAAqBL,eAAA,CAAgBO,KAAA,CAAO;UAC5CF,kBAAA,CAAmB3E,KAAA,GAAQ,IAAIc,KAAA,CAAMd,KAAA,CAAMC,CAAA,EAAGD,KAAA,CAAME,CAAA,EAAGF,KAAA,CAAMG,CAAC;UAE9D,IAAIH,KAAA,CAAMI,CAAA,KAAM,GAAK;YACnBuE,kBAAA,CAAmB3D,WAAA,GAAc;YACjC2D,kBAAA,CAAmB1D,OAAA,GAAUjB,KAAA,CAAMI,CAAA;UACpC;QACF;QAED,MAAMmD,OAAA,GAAUD,IAAA,CAAKC,OAAA;QAErB,KAAKW,CAAA,GAAI,GAAGA,CAAA,GAAIX,OAAA,CAAQ/C,MAAA,EAAQ0D,CAAA,IAAK;UACnC,MAAM7C,MAAA,GAASkC,OAAA,CAAQW,CAAC;UACxB,MAAMY,WAAA,GAAc,IAAIC,cAAA,CAAgB;UACxC,IAAI7D,QAAA,GAAWyD,kBAAA;UAEfG,WAAA,CAAYE,QAAA,CAAS3D,MAAA,CAAOC,SAAS;UACrCwD,WAAA,CAAYG,YAAA,CAAa,YAAYpC,QAAA,CAASgC,KAAA,CAAK,CAAE;UAErD,IAAI/B,OAAA,EAAS;YACXgC,WAAA,CAAYG,YAAA,CAAa,UAAUnC,OAAA,CAAQ+B,KAAA,CAAK,CAAE;UACnD;UAED,IAAId,YAAA,CAAa1C,MAAA,CAAOK,UAAU,MAAM,QAAW;YACjDR,QAAA,GAAW6C,YAAA,CAAa1C,MAAA,CAAOK,UAAU;UAC1C;UAEDoD,WAAA,CAAY7F,KAAA,CAAM6E,QAAA,EAAUA,QAAA,EAAUA,QAAQ;UAC9CY,SAAA,CAAUQ,GAAA,CAAI,IAAIC,IAAA,CAAKL,WAAA,EAAa5D,QAAA,CAAS2D,KAAA,CAAK,CAAE,CAAC;QACtD;MACF;MAEDT,WAAA,CAAYc,GAAA,CAAIR,SAAS;IAC1B;IAED,OAAON,WAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}