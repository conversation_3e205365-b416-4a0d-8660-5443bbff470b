const puppeteer = require('puppeteer');

class ActivityLogsFrontendTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
  }

  async initialize() {
    console.log('🚀 Initializing Activity Logs Frontend Test Suite...');
    
    this.browser = await puppeteer.launch({
      headless: false, // Set to true for headless testing
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Listen for console messages
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Console Error:', msg.text());
      }
    });
  }

  async runAllTests() {
    try {
      await this.initialize();
      
      console.log('🧪 Starting Frontend Activity Logs Tests...');
      console.log('=' .repeat(60));

      // Test 1: Login and Navigation
      await this.testLoginAndNavigation();
      
      // Test 2: Activity Logs Component Loading
      await this.testActivityLogsComponentLoading();
      
      // Test 3: Mock Data Display
      await this.testMockDataDisplay();
      
      // Test 4: Filtering Functionality
      await this.testFilteringFunctionality();
      
      // Test 5: Search Functionality
      await this.testSearchFunctionality();
      
      // Test 6: Statistics Cards
      await this.testStatisticsCards();
      
      // Test 7: Mock Mode Indicator
      await this.testMockModeIndicator();
      
      // Test 8: Error Handling
      await this.testErrorHandling();

      this.printTestSummary();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async testLoginAndNavigation() {
    console.log('\n🔐 Testing Login and Navigation...');
    
    try {
      // Navigate to the application
      await this.page.goto('http://localhost:3000');
      await this.page.waitForSelector('input[type="email"]', { timeout: 10000 });
      
      // Login
      await this.page.type('input[type="email"]', '<EMAIL>');
      await this.page.type('input[type="password"]', 'admin123');
      await this.page.click('button[type="submit"]');
      
      // Wait for admin dashboard
      await this.page.waitForSelector('.admin-dashboard', { timeout: 10000 });
      
      // Navigate to Activity Logs
      await this.page.click('button[title*="Activity Logs"]');
      await this.page.waitForSelector('.activity-logs-container', { timeout: 5000 });
      
      this.recordTest('Login and Navigation', true, 'Successfully logged in and navigated to Activity Logs');
      
    } catch (error) {
      this.recordTest('Login and Navigation', false, error.message);
    }
  }

  async testActivityLogsComponentLoading() {
    console.log('\n📋 Testing Activity Logs Component Loading...');
    
    try {
      // Check if main components are present
      const header = await this.page.$('.activity-logs-header');
      const filters = await this.page.$('.activity-logs-filters');
      const table = await this.page.$('.activity-logs-table');
      const pagination = await this.page.$('.activity-logs-pagination');
      
      const allComponentsPresent = header && filters && table && pagination;
      
      this.recordTest('Component Loading', allComponentsPresent, 
        allComponentsPresent ? 'All main components loaded successfully' : 'Some components missing');
      
    } catch (error) {
      this.recordTest('Component Loading', false, error.message);
    }
  }

  async testMockDataDisplay() {
    console.log('\n📊 Testing Mock Data Display...');
    
    try {
      // Wait for data to load
      await this.page.waitForSelector('.activity-log-row', { timeout: 5000 });
      
      // Check if mock data is displayed
      const rows = await this.page.$$('.activity-log-row');
      const hasData = rows.length > 0;
      
      if (hasData) {
        // Check if mock mode indicator is present
        const mockIndicator = await this.page.$('.mock-mode-indicator');
        const hasMockIndicator = !!mockIndicator;
        
        this.recordTest('Mock Data Display', true, 
          `${rows.length} activity logs displayed${hasMockIndicator ? ' with mock mode indicator' : ''}`);
      } else {
        this.recordTest('Mock Data Display', false, 'No activity logs displayed');
      }
      
    } catch (error) {
      this.recordTest('Mock Data Display', false, error.message);
    }
  }

  async testFilteringFunctionality() {
    console.log('\n🎯 Testing Filtering Functionality...');
    
    try {
      // Test action filter
      const actionFilter = await this.page.$('select[name="action"]');
      if (actionFilter) {
        await this.page.select('select[name="action"]', 'LOGIN');
        await this.page.waitForTimeout(1000); // Wait for filter to apply
        
        this.recordTest('Action Filter', true, 'Action filter applied successfully');
      } else {
        this.recordTest('Action Filter', false, 'Action filter not found');
      }
      
      // Test severity filter
      const severityFilter = await this.page.$('select[name="severity"]');
      if (severityFilter) {
        await this.page.select('select[name="severity"]', 'INFO');
        await this.page.waitForTimeout(1000);
        
        this.recordTest('Severity Filter', true, 'Severity filter applied successfully');
      } else {
        this.recordTest('Severity Filter', false, 'Severity filter not found');
      }
      
    } catch (error) {
      this.recordTest('Filtering Functionality', false, error.message);
    }
  }

  async testSearchFunctionality() {
    console.log('\n🔍 Testing Search Functionality...');
    
    try {
      const searchInput = await this.page.$('input[placeholder*="search"]');
      if (searchInput) {
        await this.page.type('input[placeholder*="search"]', 'login');
        await this.page.waitForTimeout(1000);
        
        this.recordTest('Search Functionality', true, 'Search input works correctly');
      } else {
        this.recordTest('Search Functionality', false, 'Search input not found');
      }
      
    } catch (error) {
      this.recordTest('Search Functionality', false, error.message);
    }
  }

  async testStatisticsCards() {
    console.log('\n📈 Testing Statistics Cards...');
    
    try {
      const statsCards = await this.page.$$('.stats-card');
      const hasStats = statsCards.length >= 3; // Should have at least 3 stats cards
      
      if (hasStats) {
        // Check if stats have values
        const statsValues = await this.page.$$eval('.stats-card .stats-value', 
          elements => elements.map(el => el.textContent.trim()));
        
        const hasValues = statsValues.some(value => value !== '0' && value !== '');
        
        this.recordTest('Statistics Cards', hasValues, 
          `${statsCards.length} stats cards with ${hasValues ? 'data' : 'no data'}`);
      } else {
        this.recordTest('Statistics Cards', false, 'Statistics cards not found');
      }
      
    } catch (error) {
      this.recordTest('Statistics Cards', false, error.message);
    }
  }

  async testMockModeIndicator() {
    console.log('\n⚠️ Testing Mock Mode Indicator...');
    
    try {
      // Check for mock mode indicator in header
      const mockIndicator = await this.page.$('.mock-mode-indicator');
      const hasMockIndicator = !!mockIndicator;
      
      // Check for demo mode status in admin header
      const demoModeStatus = await this.page.$eval('.status-text', 
        el => el.textContent.includes('Demo Mode')).catch(() => false);
      
      const mockModeWorking = hasMockIndicator || demoModeStatus;
      
      this.recordTest('Mock Mode Indicator', mockModeWorking, 
        mockModeWorking ? 'Mock mode properly indicated' : 'Mock mode indicator missing');
      
    } catch (error) {
      this.recordTest('Mock Mode Indicator', false, error.message);
    }
  }

  async testErrorHandling() {
    console.log('\n🛡️ Testing Error Handling...');
    
    try {
      // Check console for errors
      const errors = [];
      this.page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      // Wait a bit to collect any errors
      await this.page.waitForTimeout(2000);
      
      // Filter out expected WebSocket errors in mock mode
      const unexpectedErrors = errors.filter(error => 
        !error.includes('WebSocket') && 
        !error.includes('socket.io') &&
        !error.includes('Mock mode')
      );
      
      const hasUnexpectedErrors = unexpectedErrors.length > 0;
      
      this.recordTest('Error Handling', !hasUnexpectedErrors, 
        hasUnexpectedErrors ? `${unexpectedErrors.length} unexpected errors` : 'No unexpected errors');
      
    } catch (error) {
      this.recordTest('Error Handling', false, error.message);
    }
  }

  recordTest(testName, passed, message) {
    const result = { testName, passed, message };
    this.testResults.push(result);
    
    if (passed) {
      console.log(`  ✅ ${testName}: ${message}`);
    } else {
      console.log(`  ❌ ${testName}: ${message}`);
    }
  }

  printTestSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 FRONTEND TEST SUMMARY');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(r => r.passed).length;
    const failed = this.testResults.filter(r => !r.passed).length;
    const total = this.testResults.length;

    console.log(`\n✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📊 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
      console.log('\n🎉 ALL FRONTEND TESTS PASSED! Activity Logs UI is fully functional.');
    } else {
      console.log(`\n⚠️  ${failed} test(s) failed. Please review the issues above.`);
    }

    console.log('\n📋 Detailed Results:');
    this.testResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`  ${status} ${result.testName}: ${result.message}`);
    });
  }
}

// Check if puppeteer is available
async function checkPuppeteer() {
  try {
    require('puppeteer');
    return true;
  } catch (error) {
    console.log('⚠️  Puppeteer not available. Install with: npm install puppeteer');
    console.log('🔄 Running manual test instructions instead...');
    return false;
  }
}

// Manual testing instructions
function printManualTestInstructions() {
  console.log('\n🧪 MANUAL ACTIVITY LOGS TESTING INSTRUCTIONS');
  console.log('=' .repeat(60));
  
  console.log('\n1. 🔐 LOGIN TEST:');
  console.log('   - Go to http://localhost:3000');
  console.log('   - Login with: <EMAIL> / admin123');
  console.log('   - ✅ Should see admin dashboard');
  
  console.log('\n2. 📋 NAVIGATION TEST:');
  console.log('   - Click "Activity Logs" in the sidebar');
  console.log('   - ✅ Should see Activity Logs interface');
  
  console.log('\n3. 📊 DATA DISPLAY TEST:');
  console.log('   - ✅ Should see sample activity logs');
  console.log('   - ✅ Should see "Demo Mode" indicator');
  console.log('   - ✅ Should see statistics cards with data');
  
  console.log('\n4. 🎯 FILTERING TEST:');
  console.log('   - Try action filter dropdown');
  console.log('   - Try severity filter dropdown');
  console.log('   - Try entity type filter');
  console.log('   - ✅ Filters should work with mock data');
  
  console.log('\n5. 🔍 SEARCH TEST:');
  console.log('   - Type in search box');
  console.log('   - ✅ Should filter results');
  
  console.log('\n6. 📄 PAGINATION TEST:');
  console.log('   - Check pagination controls');
  console.log('   - ✅ Should show page information');
  
  console.log('\n7. ⚠️  ERROR HANDLING TEST:');
  console.log('   - Check browser console (F12)');
  console.log('   - ✅ Should not see unexpected errors');
  console.log('   - ✅ WebSocket errors are expected in demo mode');
}

// Run tests
async function runTests() {
  const hasPuppeteer = await checkPuppeteer();
  
  if (hasPuppeteer) {
    const tester = new ActivityLogsFrontendTester();
    await tester.runAllTests();
  } else {
    printManualTestInstructions();
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ActivityLogsFrontendTester, runTests };
} else {
  // Run if called directly
  runTests().catch(console.error);
}
