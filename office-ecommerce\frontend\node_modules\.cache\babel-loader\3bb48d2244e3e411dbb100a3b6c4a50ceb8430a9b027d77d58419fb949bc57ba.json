{"ast": null, "code": "import { <PERSON><PERSON>erGeo<PERSON>, Vector3, Float32BufferAttribute } from \"three\";\nclass ParametricGeometry extends BufferGeometry {\n  constructor(func = (u, v, target) => target.set(u, v, Math.cos(u) * Math.sin(v)), slices = 8, stacks = 8) {\n    super();\n    this.type = \"ParametricGeometry\";\n    this.parameters = {\n      func,\n      slices,\n      stacks\n    };\n    const indices = [];\n    const vertices = [];\n    const normals = [];\n    const uvs = [];\n    const EPS = 1e-5;\n    const normal = new Vector3();\n    const p0 = new Vector3(),\n      p1 = new Vector3();\n    const pu = new Vector3(),\n      pv = new Vector3();\n    const sliceCount = slices + 1;\n    for (let i = 0; i <= stacks; i++) {\n      const v = i / stacks;\n      for (let j = 0; j <= slices; j++) {\n        const u = j / slices;\n        func(u, v, p0);\n        vertices.push(p0.x, p0.y, p0.z);\n        if (u - EPS >= 0) {\n          func(u - EPS, v, p1);\n          pu.subVectors(p0, p1);\n        } else {\n          func(u + EPS, v, p1);\n          pu.subVectors(p1, p0);\n        }\n        if (v - EPS >= 0) {\n          func(u, v - EPS, p1);\n          pv.subVectors(p0, p1);\n        } else {\n          func(u, v + EPS, p1);\n          pv.subVectors(p1, p0);\n        }\n        normal.crossVectors(pu, pv).normalize();\n        normals.push(normal.x, normal.y, normal.z);\n        uvs.push(u, v);\n      }\n    }\n    for (let i = 0; i < stacks; i++) {\n      for (let j = 0; j < slices; j++) {\n        const a = i * sliceCount + j;\n        const b = i * sliceCount + j + 1;\n        const c = (i + 1) * sliceCount + j + 1;\n        const d = (i + 1) * sliceCount + j;\n        indices.push(a, b, d);\n        indices.push(b, c, d);\n      }\n    }\n    this.setIndex(indices);\n    this.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n    this.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n    this.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n  }\n}\nexport { ParametricGeometry };", "map": {"version": 3, "names": ["ParametricGeometry", "BufferGeometry", "constructor", "func", "u", "v", "target", "set", "Math", "cos", "sin", "slices", "stacks", "type", "parameters", "indices", "vertices", "normals", "uvs", "EPS", "normal", "Vector3", "p0", "p1", "pu", "pv", "sliceCount", "i", "j", "push", "x", "y", "z", "subVectors", "crossVectors", "normalize", "a", "b", "c", "d", "setIndex", "setAttribute", "Float32BufferAttribute"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\geometries\\ParametricGeometry.js"], "sourcesContent": ["import { <PERSON><PERSON>erGeo<PERSON>, Float32BufferAttribute, Vector3 } from 'three'\n\n/**\n * Parametric Surfaces Geometry\n * based on the brilliant article by @prideout https://prideout.net/blog/old/blog/index.html@p=44.html\n */\nclass ParametricGeometry extends BufferGeometry {\n  constructor(func = (u, v, target) => target.set(u, v, Math.cos(u) * Math.sin(v)), slices = 8, stacks = 8) {\n    super()\n\n    this.type = 'ParametricGeometry'\n\n    this.parameters = {\n      func: func,\n      slices: slices,\n      stacks: stacks,\n    }\n\n    // buffers\n\n    const indices = []\n    const vertices = []\n    const normals = []\n    const uvs = []\n\n    const EPS = 0.00001\n\n    const normal = new Vector3()\n\n    const p0 = new Vector3(),\n      p1 = new Vector3()\n    const pu = new Vector3(),\n      pv = new Vector3()\n\n    // generate vertices, normals and uvs\n\n    const sliceCount = slices + 1\n\n    for (let i = 0; i <= stacks; i++) {\n      const v = i / stacks\n\n      for (let j = 0; j <= slices; j++) {\n        const u = j / slices\n\n        // vertex\n\n        func(u, v, p0)\n        vertices.push(p0.x, p0.y, p0.z)\n\n        // normal\n\n        // approximate tangent vectors via finite differences\n\n        if (u - EPS >= 0) {\n          func(u - EPS, v, p1)\n          pu.subVectors(p0, p1)\n        } else {\n          func(u + EPS, v, p1)\n          pu.subVectors(p1, p0)\n        }\n\n        if (v - EPS >= 0) {\n          func(u, v - EPS, p1)\n          pv.subVectors(p0, p1)\n        } else {\n          func(u, v + EPS, p1)\n          pv.subVectors(p1, p0)\n        }\n\n        // cross product of tangent vectors returns surface normal\n\n        normal.crossVectors(pu, pv).normalize()\n        normals.push(normal.x, normal.y, normal.z)\n\n        // uv\n\n        uvs.push(u, v)\n      }\n    }\n\n    // generate indices\n\n    for (let i = 0; i < stacks; i++) {\n      for (let j = 0; j < slices; j++) {\n        const a = i * sliceCount + j\n        const b = i * sliceCount + j + 1\n        const c = (i + 1) * sliceCount + j + 1\n        const d = (i + 1) * sliceCount + j\n\n        // faces one and two\n\n        indices.push(a, b, d)\n        indices.push(b, c, d)\n      }\n    }\n\n    // build geometry\n\n    this.setIndex(indices)\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n  }\n}\n\nexport { ParametricGeometry }\n"], "mappings": ";AAMA,MAAMA,kBAAA,SAA2BC,cAAA,CAAe;EAC9CC,YAAYC,IAAA,GAAOA,CAACC,CAAA,EAAGC,CAAA,EAAGC,MAAA,KAAWA,MAAA,CAAOC,GAAA,CAAIH,CAAA,EAAGC,CAAA,EAAGG,IAAA,CAAKC,GAAA,CAAIL,CAAC,IAAII,IAAA,CAAKE,GAAA,CAAIL,CAAC,CAAC,GAAGM,MAAA,GAAS,GAAGC,MAAA,GAAS,GAAG;IACxG,MAAO;IAEP,KAAKC,IAAA,GAAO;IAEZ,KAAKC,UAAA,GAAa;MAChBX,IAAA;MACAQ,MAAA;MACAC;IACD;IAID,MAAMG,OAAA,GAAU,EAAE;IAClB,MAAMC,QAAA,GAAW,EAAE;IACnB,MAAMC,OAAA,GAAU,EAAE;IAClB,MAAMC,GAAA,GAAM,EAAE;IAEd,MAAMC,GAAA,GAAM;IAEZ,MAAMC,MAAA,GAAS,IAAIC,OAAA,CAAS;IAE5B,MAAMC,EAAA,GAAK,IAAID,OAAA,CAAS;MACtBE,EAAA,GAAK,IAAIF,OAAA,CAAS;IACpB,MAAMG,EAAA,GAAK,IAAIH,OAAA,CAAS;MACtBI,EAAA,GAAK,IAAIJ,OAAA,CAAS;IAIpB,MAAMK,UAAA,GAAaf,MAAA,GAAS;IAE5B,SAASgB,CAAA,GAAI,GAAGA,CAAA,IAAKf,MAAA,EAAQe,CAAA,IAAK;MAChC,MAAMtB,CAAA,GAAIsB,CAAA,GAAIf,MAAA;MAEd,SAASgB,CAAA,GAAI,GAAGA,CAAA,IAAKjB,MAAA,EAAQiB,CAAA,IAAK;QAChC,MAAMxB,CAAA,GAAIwB,CAAA,GAAIjB,MAAA;QAIdR,IAAA,CAAKC,CAAA,EAAGC,CAAA,EAAGiB,EAAE;QACbN,QAAA,CAASa,IAAA,CAAKP,EAAA,CAAGQ,CAAA,EAAGR,EAAA,CAAGS,CAAA,EAAGT,EAAA,CAAGU,CAAC;QAM9B,IAAI5B,CAAA,GAAIe,GAAA,IAAO,GAAG;UAChBhB,IAAA,CAAKC,CAAA,GAAIe,GAAA,EAAKd,CAAA,EAAGkB,EAAE;UACnBC,EAAA,CAAGS,UAAA,CAAWX,EAAA,EAAIC,EAAE;QAC9B,OAAe;UACLpB,IAAA,CAAKC,CAAA,GAAIe,GAAA,EAAKd,CAAA,EAAGkB,EAAE;UACnBC,EAAA,CAAGS,UAAA,CAAWV,EAAA,EAAID,EAAE;QACrB;QAED,IAAIjB,CAAA,GAAIc,GAAA,IAAO,GAAG;UAChBhB,IAAA,CAAKC,CAAA,EAAGC,CAAA,GAAIc,GAAA,EAAKI,EAAE;UACnBE,EAAA,CAAGQ,UAAA,CAAWX,EAAA,EAAIC,EAAE;QAC9B,OAAe;UACLpB,IAAA,CAAKC,CAAA,EAAGC,CAAA,GAAIc,GAAA,EAAKI,EAAE;UACnBE,EAAA,CAAGQ,UAAA,CAAWV,EAAA,EAAID,EAAE;QACrB;QAIDF,MAAA,CAAOc,YAAA,CAAaV,EAAA,EAAIC,EAAE,EAAEU,SAAA,CAAW;QACvClB,OAAA,CAAQY,IAAA,CAAKT,MAAA,CAAOU,CAAA,EAAGV,MAAA,CAAOW,CAAA,EAAGX,MAAA,CAAOY,CAAC;QAIzCd,GAAA,CAAIW,IAAA,CAAKzB,CAAA,EAAGC,CAAC;MACd;IACF;IAID,SAASsB,CAAA,GAAI,GAAGA,CAAA,GAAIf,MAAA,EAAQe,CAAA,IAAK;MAC/B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIjB,MAAA,EAAQiB,CAAA,IAAK;QAC/B,MAAMQ,CAAA,GAAIT,CAAA,GAAID,UAAA,GAAaE,CAAA;QAC3B,MAAMS,CAAA,GAAIV,CAAA,GAAID,UAAA,GAAaE,CAAA,GAAI;QAC/B,MAAMU,CAAA,IAAKX,CAAA,GAAI,KAAKD,UAAA,GAAaE,CAAA,GAAI;QACrC,MAAMW,CAAA,IAAKZ,CAAA,GAAI,KAAKD,UAAA,GAAaE,CAAA;QAIjCb,OAAA,CAAQc,IAAA,CAAKO,CAAA,EAAGC,CAAA,EAAGE,CAAC;QACpBxB,OAAA,CAAQc,IAAA,CAAKQ,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACrB;IACF;IAID,KAAKC,QAAA,CAASzB,OAAO;IACrB,KAAK0B,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuB1B,QAAA,EAAU,CAAC,CAAC;IACrE,KAAKyB,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBzB,OAAA,EAAS,CAAC,CAAC;IAClE,KAAKwB,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBxB,GAAA,EAAK,CAAC,CAAC;EAC3D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}