{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Pass, FullScreenQuad } from \"./Pass.js\";\nimport { Vector2, WebGLRenderTarget, UniformsUtils, ShaderMaterial, AdditiveBlending } from \"three\";\nimport { ConvolutionShader } from \"../shaders/ConvolutionShader.js\";\nclass BloomPass extends Pass {\n  constructor(strength = 1, kernelSize = 25, sigma = 4, resolution = 256) {\n    super();\n    __publicField(this, \"renderTargetX\");\n    __publicField(this, \"renderTargetY\");\n    __publicField(this, \"materialCombine\");\n    __publicField(this, \"materialConvolution\");\n    __publicField(this, \"fsQuad\");\n    __publicField(this, \"combineUniforms\");\n    __publicField(this, \"convolutionUniforms\");\n    __publicField(this, \"blurX\", new Vector2(1953125e-9, 0));\n    __publicField(this, \"blurY\", new Vector2(0, 1953125e-9));\n    this.renderTargetX = new WebGLRenderTarget(resolution, resolution);\n    this.renderTargetX.texture.name = \"BloomPass.x\";\n    this.renderTargetY = new WebGLRenderTarget(resolution, resolution);\n    this.renderTargetY.texture.name = \"BloomPass.y\";\n    this.combineUniforms = UniformsUtils.clone(CombineShader.uniforms);\n    this.combineUniforms[\"strength\"].value = strength;\n    this.materialCombine = new ShaderMaterial({\n      uniforms: this.combineUniforms,\n      vertexShader: CombineShader.vertexShader,\n      fragmentShader: CombineShader.fragmentShader,\n      blending: AdditiveBlending,\n      transparent: true\n    });\n    if (ConvolutionShader === void 0) console.error(\"BloomPass relies on ConvolutionShader\");\n    const convolutionShader = ConvolutionShader;\n    this.convolutionUniforms = UniformsUtils.clone(convolutionShader.uniforms);\n    this.convolutionUniforms[\"uImageIncrement\"].value = this.blurX;\n    this.convolutionUniforms[\"cKernel\"].value = ConvolutionShader.buildKernel(sigma);\n    this.materialConvolution = new ShaderMaterial({\n      uniforms: this.convolutionUniforms,\n      vertexShader: convolutionShader.vertexShader,\n      fragmentShader: convolutionShader.fragmentShader,\n      defines: {\n        KERNEL_SIZE_FLOAT: kernelSize.toFixed(1),\n        KERNEL_SIZE_INT: kernelSize.toFixed(0)\n      }\n    });\n    this.needsSwap = false;\n    this.fsQuad = new FullScreenQuad(this.materialConvolution);\n  }\n  render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n    if (maskActive) renderer.state.buffers.stencil.setTest(false);\n    this.fsQuad.material = this.materialConvolution;\n    this.convolutionUniforms[\"tDiffuse\"].value = readBuffer.texture;\n    this.convolutionUniforms[\"uImageIncrement\"].value = this.blurX;\n    renderer.setRenderTarget(this.renderTargetX);\n    renderer.clear();\n    this.fsQuad.render(renderer);\n    this.convolutionUniforms[\"tDiffuse\"].value = this.renderTargetX.texture;\n    this.convolutionUniforms[\"uImageIncrement\"].value = this.blurY;\n    renderer.setRenderTarget(this.renderTargetY);\n    renderer.clear();\n    this.fsQuad.render(renderer);\n    this.fsQuad.material = this.materialCombine;\n    this.combineUniforms[\"tDiffuse\"].value = this.renderTargetY.texture;\n    if (maskActive) renderer.state.buffers.stencil.setTest(true);\n    renderer.setRenderTarget(readBuffer);\n    if (this.clear) renderer.clear();\n    this.fsQuad.render(renderer);\n  }\n}\nconst CombineShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    strength: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n  varying vec2 vUv;\n  void main() {\n    vUv = uv;\n    gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n  }`),\n  fragmentShader: (/* glsl */\n  `\n  uniform float strength;\n  uniform sampler2D tDiffuse;\n  varying vec2 vUv;\n  void main() {\n    vec4 texel = texture2D( tDiffuse, vUv );\n    gl_FragColor = strength * texel;\n  }`)\n};\nexport { BloomPass };", "map": {"version": 3, "names": ["BloomPass", "Pass", "constructor", "strength", "kernelSize", "sigma", "resolution", "__publicField", "Vector2", "renderTargetX", "WebGLRenderTarget", "texture", "name", "renderTargetY", "combineUniforms", "UniformsUtils", "clone", "CombineShader", "uniforms", "value", "material<PERSON><PERSON><PERSON>", "ShaderMaterial", "vertexShader", "fragmentShader", "blending", "AdditiveBlending", "transparent", "ConvolutionShader", "console", "error", "convolutionShader", "convolutionUniforms", "blurX", "buildKernel", "materialConvolution", "defines", "KERNEL_SIZE_FLOAT", "toFixed", "KERNEL_SIZE_INT", "needsSwap", "fsQuad", "FullScreenQuad", "render", "renderer", "writeBuffer", "readBuffer", "deltaTime", "maskActive", "state", "buffers", "stencil", "setTest", "material", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "blurY", "tDiffuse"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\postprocessing\\BloomPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport {\n  AdditiveBlending,\n  IUniform,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector2,\n  WebGLRenderer,\n  WebGLRenderTarget,\n} from 'three'\nimport { ConvolutionShader } from '../shaders/ConvolutionShader'\n\nclass BloomPass extends Pass {\n  public renderTargetX: WebGLRenderTarget\n  public renderTargetY: WebGLRenderTarget\n  public materialCombine: ShaderMaterial\n  public materialConvolution: ShaderMaterial\n  public fsQuad: FullScreenQuad\n  public combineUniforms: Record<keyof typeof CombineShader['uniforms'], IUniform<any>>\n  public convolutionUniforms: Record<keyof typeof ConvolutionShader['uniforms'], IUniform<any>>\n\n  public blurX = new Vector2(0.001953125, 0.0)\n  public blurY = new Vector2(0.0, 0.001953125)\n\n  constructor(strength = 1, kernelSize = 25, sigma = 4, resolution = 256) {\n    super() // render targets\n\n    this.renderTargetX = new WebGLRenderTarget(resolution, resolution)\n    this.renderTargetX.texture.name = 'BloomPass.x'\n    this.renderTargetY = new WebGLRenderTarget(resolution, resolution)\n    this.renderTargetY.texture.name = 'BloomPass.y' // combine material\n\n    this.combineUniforms = UniformsUtils.clone(CombineShader.uniforms)\n    this.combineUniforms['strength'].value = strength\n    this.materialCombine = new ShaderMaterial({\n      uniforms: this.combineUniforms,\n      vertexShader: CombineShader.vertexShader,\n      fragmentShader: CombineShader.fragmentShader,\n      blending: AdditiveBlending,\n      transparent: true,\n    }) // convolution material\n\n    if (ConvolutionShader === undefined) console.error('BloomPass relies on ConvolutionShader')\n    const convolutionShader = ConvolutionShader\n    this.convolutionUniforms = UniformsUtils.clone(convolutionShader.uniforms)\n    this.convolutionUniforms['uImageIncrement'].value = this.blurX\n    this.convolutionUniforms['cKernel'].value = ConvolutionShader.buildKernel(sigma)\n    this.materialConvolution = new ShaderMaterial({\n      uniforms: this.convolutionUniforms,\n      vertexShader: convolutionShader.vertexShader,\n      fragmentShader: convolutionShader.fragmentShader,\n      defines: {\n        KERNEL_SIZE_FLOAT: kernelSize.toFixed(1),\n        KERNEL_SIZE_INT: kernelSize.toFixed(0),\n      },\n    })\n    this.needsSwap = false\n    this.fsQuad = new FullScreenQuad(this.materialConvolution)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n    maskActive: boolean,\n  ): void {\n    if (maskActive) renderer.state.buffers.stencil.setTest(false) // Render quad with blured scene into texture (convolution pass 1)\n\n    this.fsQuad.material = this.materialConvolution\n    this.convolutionUniforms['tDiffuse'].value = readBuffer.texture\n    this.convolutionUniforms['uImageIncrement'].value = this.blurX\n    renderer.setRenderTarget(this.renderTargetX)\n    renderer.clear()\n    this.fsQuad.render(renderer) // Render quad with blured scene into texture (convolution pass 2)\n\n    this.convolutionUniforms['tDiffuse'].value = this.renderTargetX.texture\n    this.convolutionUniforms['uImageIncrement'].value = this.blurY\n    renderer.setRenderTarget(this.renderTargetY)\n    renderer.clear()\n    this.fsQuad.render(renderer) // Render original scene with superimposed blur to texture\n\n    this.fsQuad.material = this.materialCombine\n    this.combineUniforms['tDiffuse'].value = this.renderTargetY.texture\n    if (maskActive) renderer.state.buffers.stencil.setTest(true)\n    renderer.setRenderTarget(readBuffer)\n    if (this.clear) renderer.clear()\n    this.fsQuad.render(renderer)\n  }\n}\n\nconst CombineShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null,\n    },\n    strength: {\n      value: 1.0,\n    },\n  },\n  vertexShader:\n    /* glsl */\n    `\n  varying vec2 vUv;\n  void main() {\n    vUv = uv;\n    gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n  }`,\n  fragmentShader:\n    /* glsl */\n    `\n  uniform float strength;\n  uniform sampler2D tDiffuse;\n  varying vec2 vUv;\n  void main() {\n    vec4 texel = texture2D( tDiffuse, vUv );\n    gl_FragColor = strength * texel;\n  }`,\n}\n\nexport { BloomPass }\n"], "mappings": ";;;;;;;;;;;;;;AAYA,MAAMA,SAAA,SAAkBC,IAAA,CAAK;EAY3BC,YAAYC,QAAA,GAAW,GAAGC,UAAA,GAAa,IAAIC,KAAA,GAAQ,GAAGC,UAAA,GAAa,KAAK;IAChE;IAZDC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAEAA,aAAA,gBAAQ,IAAIC,OAAA,CAAQ,YAAa,CAAG;IACpCD,aAAA,gBAAQ,IAAIC,OAAA,CAAQ,GAAK,UAAW;IAKzC,KAAKC,aAAA,GAAgB,IAAIC,iBAAA,CAAkBJ,UAAA,EAAYA,UAAU;IAC5D,KAAAG,aAAA,CAAcE,OAAA,CAAQC,IAAA,GAAO;IAClC,KAAKC,aAAA,GAAgB,IAAIH,iBAAA,CAAkBJ,UAAA,EAAYA,UAAU;IAC5D,KAAAO,aAAA,CAAcF,OAAA,CAAQC,IAAA,GAAO;IAElC,KAAKE,eAAA,GAAkBC,aAAA,CAAcC,KAAA,CAAMC,aAAA,CAAcC,QAAQ;IAC5D,KAAAJ,eAAA,CAAgB,UAAU,EAAEK,KAAA,GAAQhB,QAAA;IACpC,KAAAiB,eAAA,GAAkB,IAAIC,cAAA,CAAe;MACxCH,QAAA,EAAU,KAAKJ,eAAA;MACfQ,YAAA,EAAcL,aAAA,CAAcK,YAAA;MAC5BC,cAAA,EAAgBN,aAAA,CAAcM,cAAA;MAC9BC,QAAA,EAAUC,gBAAA;MACVC,WAAA,EAAa;IAAA,CACd;IAED,IAAIC,iBAAA,KAAsB,QAAWC,OAAA,CAAQC,KAAA,CAAM,uCAAuC;IAC1F,MAAMC,iBAAA,GAAoBH,iBAAA;IAC1B,KAAKI,mBAAA,GAAsBhB,aAAA,CAAcC,KAAA,CAAMc,iBAAA,CAAkBZ,QAAQ;IACzE,KAAKa,mBAAA,CAAoB,iBAAiB,EAAEZ,KAAA,GAAQ,KAAKa,KAAA;IACzD,KAAKD,mBAAA,CAAoB,SAAS,EAAEZ,KAAA,GAAQQ,iBAAA,CAAkBM,WAAA,CAAY5B,KAAK;IAC1E,KAAA6B,mBAAA,GAAsB,IAAIb,cAAA,CAAe;MAC5CH,QAAA,EAAU,KAAKa,mBAAA;MACfT,YAAA,EAAcQ,iBAAA,CAAkBR,YAAA;MAChCC,cAAA,EAAgBO,iBAAA,CAAkBP,cAAA;MAClCY,OAAA,EAAS;QACPC,iBAAA,EAAmBhC,UAAA,CAAWiC,OAAA,CAAQ,CAAC;QACvCC,eAAA,EAAiBlC,UAAA,CAAWiC,OAAA,CAAQ,CAAC;MACvC;IAAA,CACD;IACD,KAAKE,SAAA,GAAY;IACjB,KAAKC,MAAA,GAAS,IAAIC,cAAA,CAAe,KAAKP,mBAAmB;EAC3D;EAEOQ,OACLC,QAAA,EACAC,WAAA,EACAC,UAAA,EACAC,SAAA,EACAC,UAAA,EACM;IACF,IAAAA,UAAA,EAAYJ,QAAA,CAASK,KAAA,CAAMC,OAAA,CAAQC,OAAA,CAAQC,OAAA,CAAQ,KAAK;IAEvD,KAAAX,MAAA,CAAOY,QAAA,GAAW,KAAKlB,mBAAA;IAC5B,KAAKH,mBAAA,CAAoB,UAAU,EAAEZ,KAAA,GAAQ0B,UAAA,CAAWlC,OAAA;IACxD,KAAKoB,mBAAA,CAAoB,iBAAiB,EAAEZ,KAAA,GAAQ,KAAKa,KAAA;IAChDW,QAAA,CAAAU,eAAA,CAAgB,KAAK5C,aAAa;IAC3CkC,QAAA,CAASW,KAAA,CAAM;IACV,KAAAd,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAE3B,KAAKZ,mBAAA,CAAoB,UAAU,EAAEZ,KAAA,GAAQ,KAAKV,aAAA,CAAcE,OAAA;IAChE,KAAKoB,mBAAA,CAAoB,iBAAiB,EAAEZ,KAAA,GAAQ,KAAKoC,KAAA;IAChDZ,QAAA,CAAAU,eAAA,CAAgB,KAAKxC,aAAa;IAC3C8B,QAAA,CAASW,KAAA,CAAM;IACV,KAAAd,MAAA,CAAOE,MAAA,CAAOC,QAAQ;IAEtB,KAAAH,MAAA,CAAOY,QAAA,GAAW,KAAKhC,eAAA;IAC5B,KAAKN,eAAA,CAAgB,UAAU,EAAEK,KAAA,GAAQ,KAAKN,aAAA,CAAcF,OAAA;IACxD,IAAAoC,UAAA,EAAYJ,QAAA,CAASK,KAAA,CAAMC,OAAA,CAAQC,OAAA,CAAQC,OAAA,CAAQ,IAAI;IAC3DR,QAAA,CAASU,eAAA,CAAgBR,UAAU;IACnC,IAAI,KAAKS,KAAA,EAAOX,QAAA,CAASW,KAAA,CAAM;IAC1B,KAAAd,MAAA,CAAOE,MAAA,CAAOC,QAAQ;EAC7B;AACF;AAEA,MAAM1B,aAAA,GAAgB;EACpBC,QAAA,EAAU;IACRsC,QAAA,EAAU;MACRrC,KAAA,EAAO;IACT;IACAhB,QAAA,EAAU;MACRgB,KAAA,EAAO;IACT;EACF;EACAG,YAAA;EAEE;AAAA;AAAA;AAAA;AAAA;AAAA;EAMFC,cAAA;EAEE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}