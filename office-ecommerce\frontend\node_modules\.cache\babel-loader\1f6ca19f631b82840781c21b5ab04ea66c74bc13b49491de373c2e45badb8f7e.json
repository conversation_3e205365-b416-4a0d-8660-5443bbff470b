{"ast": null, "code": "import { Mesh, MeshBasicMaterial, EqualStencilFunc, IncrementStencilOp, Matrix4 } from \"three\";\nconst _shadowMatrix = /* @__PURE__ */new Matrix4();\nclass ShadowMesh extends Mesh {\n  constructor(mesh) {\n    const shadowMaterial = new MeshBasicMaterial({\n      color: 0,\n      transparent: true,\n      opacity: 0.6,\n      depthWrite: false,\n      stencilWrite: true,\n      stencilFunc: EqualStencilFunc,\n      stencilRef: 0,\n      stencilZPass: IncrementStencilOp\n    });\n    super(mesh.geometry, shadowMaterial);\n    this.isShadowMesh = true;\n    this.meshMatrix = mesh.matrixWorld;\n    this.frustumCulled = false;\n    this.matrixAutoUpdate = false;\n  }\n  update(plane, lightPosition4D) {\n    const dot = plane.normal.x * lightPosition4D.x + plane.normal.y * lightPosition4D.y + plane.normal.z * lightPosition4D.z + -plane.constant * lightPosition4D.w;\n    const sme = _shadowMatrix.elements;\n    sme[0] = dot - lightPosition4D.x * plane.normal.x;\n    sme[4] = -lightPosition4D.x * plane.normal.y;\n    sme[8] = -lightPosition4D.x * plane.normal.z;\n    sme[12] = -lightPosition4D.x * -plane.constant;\n    sme[1] = -lightPosition4D.y * plane.normal.x;\n    sme[5] = dot - lightPosition4D.y * plane.normal.y;\n    sme[9] = -lightPosition4D.y * plane.normal.z;\n    sme[13] = -lightPosition4D.y * -plane.constant;\n    sme[2] = -lightPosition4D.z * plane.normal.x;\n    sme[6] = -lightPosition4D.z * plane.normal.y;\n    sme[10] = dot - lightPosition4D.z * plane.normal.z;\n    sme[14] = -lightPosition4D.z * -plane.constant;\n    sme[3] = -lightPosition4D.w * plane.normal.x;\n    sme[7] = -lightPosition4D.w * plane.normal.y;\n    sme[11] = -lightPosition4D.w * plane.normal.z;\n    sme[15] = dot - lightPosition4D.w * -plane.constant;\n    this.matrix.multiplyMatrices(_shadowMatrix, this.meshMatrix);\n  }\n}\nexport { ShadowMesh };", "map": {"version": 3, "names": ["_shadowMatrix", "Matrix4", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "constructor", "mesh", "shadowMaterial", "MeshBasicMaterial", "color", "transparent", "opacity", "depthWrite", "stencilWrite", "stencil<PERSON>unc", "EqualStencilFunc", "stencilRef", "stencilZPass", "IncrementStencilOp", "geometry", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meshMatrix", "matrixWorld", "frustumCulled", "matrixAutoUpdate", "update", "plane", "lightPosition4D", "dot", "normal", "x", "y", "z", "constant", "w", "sme", "elements", "matrix", "multiplyMatrices"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\objects\\ShadowMesh.js"], "sourcesContent": ["import { Matrix4, <PERSON><PERSON>, MeshBasicMaterial, EqualStencilFunc, IncrementStencilOp } from 'three'\n\n/**\n * A shadow Mesh that follows a shadow-casting Mesh in the scene, but is confined to a single plane.\n */\n\nconst _shadowMatrix = /* @__PURE__ */ new Matrix4()\n\nclass ShadowMesh extends Mesh {\n  constructor(mesh) {\n    const shadowMaterial = new MeshBasicMaterial({\n      color: 0x000000,\n      transparent: true,\n      opacity: 0.6,\n      depthWrite: false,\n      stencilWrite: true,\n      stencilFunc: EqualStencilFunc,\n      stencilRef: 0,\n      stencilZPass: IncrementStencilOp,\n    })\n\n    super(mesh.geometry, shadowMaterial)\n\n    this.isShadowMesh = true\n\n    this.meshMatrix = mesh.matrixWorld\n\n    this.frustumCulled = false\n    this.matrixAutoUpdate = false\n  }\n\n  update(plane, lightPosition4D) {\n    // based on https://www.opengl.org/archives/resources/features/StencilTalk/tsld021.htm\n\n    const dot =\n      plane.normal.x * lightPosition4D.x +\n      plane.normal.y * lightPosition4D.y +\n      plane.normal.z * lightPosition4D.z +\n      -plane.constant * lightPosition4D.w\n\n    const sme = _shadowMatrix.elements\n\n    sme[0] = dot - lightPosition4D.x * plane.normal.x\n    sme[4] = -lightPosition4D.x * plane.normal.y\n    sme[8] = -lightPosition4D.x * plane.normal.z\n    sme[12] = -lightPosition4D.x * -plane.constant\n\n    sme[1] = -lightPosition4D.y * plane.normal.x\n    sme[5] = dot - lightPosition4D.y * plane.normal.y\n    sme[9] = -lightPosition4D.y * plane.normal.z\n    sme[13] = -lightPosition4D.y * -plane.constant\n\n    sme[2] = -lightPosition4D.z * plane.normal.x\n    sme[6] = -lightPosition4D.z * plane.normal.y\n    sme[10] = dot - lightPosition4D.z * plane.normal.z\n    sme[14] = -lightPosition4D.z * -plane.constant\n\n    sme[3] = -lightPosition4D.w * plane.normal.x\n    sme[7] = -lightPosition4D.w * plane.normal.y\n    sme[11] = -lightPosition4D.w * plane.normal.z\n    sme[15] = dot - lightPosition4D.w * -plane.constant\n\n    this.matrix.multiplyMatrices(_shadowMatrix, this.meshMatrix)\n  }\n}\n\nexport { ShadowMesh }\n"], "mappings": ";AAMA,MAAMA,aAAA,GAAgC,mBAAIC,OAAA,CAAS;AAEnD,MAAMC,UAAA,SAAmBC,IAAA,CAAK;EAC5BC,YAAYC,IAAA,EAAM;IAChB,MAAMC,cAAA,GAAiB,IAAIC,iBAAA,CAAkB;MAC3CC,KAAA,EAAO;MACPC,WAAA,EAAa;MACbC,OAAA,EAAS;MACTC,UAAA,EAAY;MACZC,YAAA,EAAc;MACdC,WAAA,EAAaC,gBAAA;MACbC,UAAA,EAAY;MACZC,YAAA,EAAcC;IACpB,CAAK;IAED,MAAMZ,IAAA,CAAKa,QAAA,EAAUZ,cAAc;IAEnC,KAAKa,YAAA,GAAe;IAEpB,KAAKC,UAAA,GAAaf,IAAA,CAAKgB,WAAA;IAEvB,KAAKC,aAAA,GAAgB;IACrB,KAAKC,gBAAA,GAAmB;EACzB;EAEDC,OAAOC,KAAA,EAAOC,eAAA,EAAiB;IAG7B,MAAMC,GAAA,GACJF,KAAA,CAAMG,MAAA,CAAOC,CAAA,GAAIH,eAAA,CAAgBG,CAAA,GACjCJ,KAAA,CAAMG,MAAA,CAAOE,CAAA,GAAIJ,eAAA,CAAgBI,CAAA,GACjCL,KAAA,CAAMG,MAAA,CAAOG,CAAA,GAAIL,eAAA,CAAgBK,CAAA,GACjC,CAACN,KAAA,CAAMO,QAAA,GAAWN,eAAA,CAAgBO,CAAA;IAEpC,MAAMC,GAAA,GAAMlC,aAAA,CAAcmC,QAAA;IAE1BD,GAAA,CAAI,CAAC,IAAIP,GAAA,GAAMD,eAAA,CAAgBG,CAAA,GAAIJ,KAAA,CAAMG,MAAA,CAAOC,CAAA;IAChDK,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBG,CAAA,GAAIJ,KAAA,CAAMG,MAAA,CAAOE,CAAA;IAC3CI,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBG,CAAA,GAAIJ,KAAA,CAAMG,MAAA,CAAOG,CAAA;IAC3CG,GAAA,CAAI,EAAE,IAAI,CAACR,eAAA,CAAgBG,CAAA,GAAI,CAACJ,KAAA,CAAMO,QAAA;IAEtCE,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBI,CAAA,GAAIL,KAAA,CAAMG,MAAA,CAAOC,CAAA;IAC3CK,GAAA,CAAI,CAAC,IAAIP,GAAA,GAAMD,eAAA,CAAgBI,CAAA,GAAIL,KAAA,CAAMG,MAAA,CAAOE,CAAA;IAChDI,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBI,CAAA,GAAIL,KAAA,CAAMG,MAAA,CAAOG,CAAA;IAC3CG,GAAA,CAAI,EAAE,IAAI,CAACR,eAAA,CAAgBI,CAAA,GAAI,CAACL,KAAA,CAAMO,QAAA;IAEtCE,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBK,CAAA,GAAIN,KAAA,CAAMG,MAAA,CAAOC,CAAA;IAC3CK,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBK,CAAA,GAAIN,KAAA,CAAMG,MAAA,CAAOE,CAAA;IAC3CI,GAAA,CAAI,EAAE,IAAIP,GAAA,GAAMD,eAAA,CAAgBK,CAAA,GAAIN,KAAA,CAAMG,MAAA,CAAOG,CAAA;IACjDG,GAAA,CAAI,EAAE,IAAI,CAACR,eAAA,CAAgBK,CAAA,GAAI,CAACN,KAAA,CAAMO,QAAA;IAEtCE,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBO,CAAA,GAAIR,KAAA,CAAMG,MAAA,CAAOC,CAAA;IAC3CK,GAAA,CAAI,CAAC,IAAI,CAACR,eAAA,CAAgBO,CAAA,GAAIR,KAAA,CAAMG,MAAA,CAAOE,CAAA;IAC3CI,GAAA,CAAI,EAAE,IAAI,CAACR,eAAA,CAAgBO,CAAA,GAAIR,KAAA,CAAMG,MAAA,CAAOG,CAAA;IAC5CG,GAAA,CAAI,EAAE,IAAIP,GAAA,GAAMD,eAAA,CAAgBO,CAAA,GAAI,CAACR,KAAA,CAAMO,QAAA;IAE3C,KAAKI,MAAA,CAAOC,gBAAA,CAAiBrC,aAAA,EAAe,KAAKoB,UAAU;EAC5D;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}