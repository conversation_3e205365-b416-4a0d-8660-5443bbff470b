const axios = require('axios');

async function testLoginAPI() {
  try {
    console.log('🧪 Testing Login API...');

    // Test with admin credentials
    console.log('\n👤 Testing admin login...');
    try {
      const adminResponse = await axios.post('http://localhost:5001/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      console.log('✅ Admin login successful');
      console.log('Response:', JSON.stringify(adminResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Admin login failed:', error.response?.status, error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log('Error details:', JSON.stringify(error.response.data, null, 2));
      }
    }

    // Test with manager credentials
    console.log('\n👤 Testing manager login...');
    try {
      const managerResponse = await axios.post('http://localhost:5001/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      console.log('✅ Manager login successful');
      console.log('Response:', JSON.stringify(managerResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Manager login failed:', error.response?.status, error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log('Error details:', JSON.stringify(error.response.data, null, 2));
      }
    }

    // Test with invalid credentials
    console.log('\n❌ Testing invalid login...');
    try {
      const invalidResponse = await axios.post('http://localhost:5001/api/auth/login', {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      
      console.log('⚠️ Invalid login unexpectedly succeeded:', invalidResponse.data);
    } catch (error) {
      console.log('✅ Invalid login correctly failed:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n✅ Login API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testLoginAPI();
