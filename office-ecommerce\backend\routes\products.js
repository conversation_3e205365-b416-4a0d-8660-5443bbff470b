const express = require('express');
const { body, query } = require('express-validator');
const router = express.Router();

// Import controllers and middleware
const { productController } = require('../controllers');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const ActivityLoggerMiddleware = require('../middleware/activityLogger');




// @route   GET /api/products
// @desc    Get all products
// @access  Public
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('category').optional().isLength({ min: 1, max: 50 }).withMessage('Category must be 1-50 characters'),
  query('search').optional().isLength({ min: 1, max: 100 }).withMessage('Search term must be 1-100 characters'),
  query('minPrice').optional().isFloat({ min: 0 }).withMessage('Min price must be positive'),
  query('maxPrice').optional().isFloat({ min: 0 }).withMessage('Max price must be positive'),
  query('inStock').optional().isBoolean().withMessage('In stock must be boolean'),
  query('featured').optional().isBoolean().withMessage('Featured must be boolean'),
  query('sortBy').optional().isIn(['name', 'price', 'createdAt', 'updatedAt']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc'),
  handleValidationErrors
], productController.getProducts);


// @route   GET /api/products/categories
// @desc    Get all product categories
// @access  Private
router.get('/categories', async (req, res) => {
  try {
    const categories = [...new Set(products.map(p => p.category))];

    res.json({
      success: true,
      data: categories
    });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/products/:id
// @desc    Get product by ID
// @access  Private
router.get('/:id', async (req, res) => {
  try {
    const product = products.find(p => p.id === req.params.id);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    res.json({
      success: true,
      data: product
    });
    
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/products
// @desc    Create new product
// @access  Private
router.post('/', [
  body('name').notEmpty().trim(),
  body('sku').notEmpty().trim(),
  body('category').notEmpty().trim(),
  body('basePrice').isFloat({ min: 0 }),
  body('costPrice').isFloat({ min: 0 }),
  body('description').optional().trim()
],
authenticateToken,
requirePermission('products:create'),
ActivityLoggerMiddleware.logProductCreate(),
async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { name, sku, category, description, basePrice, costPrice, weight, dimensions, materials, colors } = req.body;
    
    // Check if SKU already exists
    const existingProduct = products.find(p => p.sku === sku);
    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'Product with this SKU already exists'
      });
    }
    
    const newProduct = {
      id: `PROD${String(products.length + 1).padStart(3, '0')}`,
      name,
      sku,
      category,
      description: description || '',
      basePrice: parseFloat(basePrice),
      costPrice: parseFloat(costPrice),
      weight: weight || 0,
      dimensions: dimensions || { width: 0, depth: 0, height: 0 },
      materials: materials || [],
      colors: colors || [],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    products.push(newProduct);

    // Emit real-time update
    websocketService.emitProductCreated(newProduct);

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: newProduct
    });
    
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/products/:id
// @desc    Update product
// @access  Private
router.put('/:id', [
  body('name').optional().notEmpty().trim(),
  body('sku').optional().notEmpty().trim(),
  body('category').optional().notEmpty().trim(),
  body('basePrice').optional().isFloat({ min: 0 }),
  body('costPrice').optional().isFloat({ min: 0 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const productIndex = products.findIndex(p => p.id === req.params.id);
    if (productIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Update product
    const updatedProduct = {
      ...products[productIndex],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    products[productIndex] = updatedProduct;

    // Emit real-time update
    websocketService.emitProductUpdated(updatedProduct);

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
    
  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/products/:id
// @desc    Delete product
// @access  Private
router.delete('/:id', async (req, res) => {
  try {
    const productIndex = products.findIndex(p => p.id === req.params.id);
    if (productIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Soft delete - set isActive to false
    products[productIndex].isActive = false;
    products[productIndex].updatedAt = new Date().toISOString();

    // Emit real-time update
    websocketService.emitProductDeleted(req.params.id);

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/products/:id/models
// @desc    Upload 3D model for product
// @access  Private
router.post('/:id/models', async (req, res) => {
  try {
    // Mock file upload response
    res.status(201).json({
      success: true,
      message: '3D model uploaded successfully',
      data: {
        FileName: 'uploaded-model.glb',
        FileSize: 2048000,
        IsPrimary: true
      }
    });
  } catch (error) {
    console.error('Upload model error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/products/:id/images
// @desc    Upload images for product
// @access  Private
router.post('/:id/images', async (req, res) => {
  try {
    // Mock file upload response
    res.status(201).json({
      success: true,
      message: 'Images uploaded successfully',
      data: [
        {
          FileName: 'uploaded-image.jpg',
          FileSize: 512000,
          IsPrimary: true
        }
      ]
    });
  } catch (error) {
    console.error('Upload images error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
