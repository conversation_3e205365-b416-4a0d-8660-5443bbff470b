{"ast": null, "code": "import * as React from 'react';\nimport { useTexture } from './useTexture.js';\nimport { suspend } from 'suspend-react';\nfunction getFormatString(format) {\n  switch (format) {\n    case 64:\n      return '-64px';\n    case 128:\n      return '-128px';\n    case 256:\n      return '-256px';\n    case 512:\n      return '-512px';\n    default:\n      return '';\n  }\n}\nconst LIST_URL = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/matcaps.json';\nconst MATCAP_ROOT = 'https://rawcdn.githack.com/emmelleppi/matcaps/9b36ccaaf0a24881a39062d05566c9e92be4aa0d';\nfunction useMatcapTexture(id = 0, format = 1024, onLoad) {\n  const matcapList = suspend(() => fetch(LIST_URL).then(res => res.json()), ['matcapList']);\n  const DEFAULT_MATCAP = matcapList[0];\n  const numTot = React.useMemo(() => Object.keys(matcapList).length, []);\n  const fileHash = React.useMemo(() => {\n    if (typeof id === 'string') {\n      return id;\n    } else if (typeof id === 'number') {\n      return matcapList[id];\n    }\n    return null;\n  }, [id]);\n  const fileName = `${fileHash || DEFAULT_MATCAP}${getFormatString(format)}.png`;\n  const url = `${MATCAP_ROOT}/${format}/${fileName}`;\n  const matcapTexture = useTexture(url, onLoad);\n  return [matcapTexture, url, numTot];\n}\nexport { useMatcapTexture };", "map": {"version": 3, "names": ["React", "useTexture", "suspend", "getFormatString", "format", "LIST_URL", "MATCAP_ROOT", "useMatcapTexture", "id", "onLoad", "matcapList", "fetch", "then", "res", "json", "DEFAULT_MATCAP", "numTot", "useMemo", "Object", "keys", "length", "fileHash", "fileName", "url", "matcapTexture"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useMatcapTexture.js"], "sourcesContent": ["import * as React from 'react';\nimport { useTexture } from './useTexture.js';\nimport { suspend } from 'suspend-react';\n\nfunction getFormatString(format) {\n  switch (format) {\n    case 64:\n      return '-64px';\n\n    case 128:\n      return '-128px';\n\n    case 256:\n      return '-256px';\n\n    case 512:\n      return '-512px';\n\n    default:\n      return '';\n  }\n}\n\nconst LIST_URL = 'https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/matcaps.json';\nconst MATCAP_ROOT = 'https://rawcdn.githack.com/emmelleppi/matcaps/9b36ccaaf0a24881a39062d05566c9e92be4aa0d';\nfunction useMatcapTexture(id = 0, format = 1024, onLoad) {\n  const matcapList = suspend(() => fetch(LIST_URL).then(res => res.json()), ['matcapList']);\n  const DEFAULT_MATCAP = matcapList[0];\n  const numTot = React.useMemo(() => Object.keys(matcapList).length, []);\n  const fileHash = React.useMemo(() => {\n    if (typeof id === 'string') {\n      return id;\n    } else if (typeof id === 'number') {\n      return matcapList[id];\n    }\n\n    return null;\n  }, [id]);\n  const fileName = `${fileHash || DEFAULT_MATCAP}${getFormatString(format)}.png`;\n  const url = `${MATCAP_ROOT}/${format}/${fileName}`;\n  const matcapTexture = useTexture(url, onLoad);\n  return [matcapTexture, url, numTot];\n}\n\nexport { useMatcapTexture };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,OAAO,QAAQ,eAAe;AAEvC,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,QAAQA,MAAM;IACZ,KAAK,EAAE;MACL,OAAO,OAAO;IAEhB,KAAK,GAAG;MACN,OAAO,QAAQ;IAEjB,KAAK,GAAG;MACN,OAAO,QAAQ;IAEjB,KAAK,GAAG;MACN,OAAO,QAAQ;IAEjB;MACE,OAAO,EAAE;EACb;AACF;AAEA,MAAMC,QAAQ,GAAG,oEAAoE;AACrF,MAAMC,WAAW,GAAG,wFAAwF;AAC5G,SAASC,gBAAgBA,CAACC,EAAE,GAAG,CAAC,EAAEJ,MAAM,GAAG,IAAI,EAAEK,MAAM,EAAE;EACvD,MAAMC,UAAU,GAAGR,OAAO,CAAC,MAAMS,KAAK,CAACN,QAAQ,CAAC,CAACO,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;EACzF,MAAMC,cAAc,GAAGL,UAAU,CAAC,CAAC,CAAC;EACpC,MAAMM,MAAM,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAMC,MAAM,CAACC,IAAI,CAACT,UAAU,CAAC,CAACU,MAAM,EAAE,EAAE,CAAC;EACtE,MAAMC,QAAQ,GAAGrB,KAAK,CAACiB,OAAO,CAAC,MAAM;IACnC,IAAI,OAAOT,EAAE,KAAK,QAAQ,EAAE;MAC1B,OAAOA,EAAE;IACX,CAAC,MAAM,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MACjC,OAAOE,UAAU,CAACF,EAAE,CAAC;IACvB;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EACR,MAAMc,QAAQ,GAAG,GAAGD,QAAQ,IAAIN,cAAc,GAAGZ,eAAe,CAACC,MAAM,CAAC,MAAM;EAC9E,MAAMmB,GAAG,GAAG,GAAGjB,WAAW,IAAIF,MAAM,IAAIkB,QAAQ,EAAE;EAClD,MAAME,aAAa,GAAGvB,UAAU,CAACsB,GAAG,EAAEd,MAAM,CAAC;EAC7C,OAAO,CAACe,aAAa,EAAED,GAAG,EAAEP,MAAM,CAAC;AACrC;AAEA,SAAST,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}