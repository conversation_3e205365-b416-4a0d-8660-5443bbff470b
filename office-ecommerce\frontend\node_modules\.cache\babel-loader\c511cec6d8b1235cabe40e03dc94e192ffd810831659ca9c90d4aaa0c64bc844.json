{"ast": null, "code": "import { Vector3, Line3, <PERSON>e, <PERSON> } from \"three\";\nconst Visible = 0;\nconst Deleted = 1;\nconst _v1 = /* @__PURE__ */new Vector3();\nconst _line3 = /* @__PURE__ */new Line3();\nconst _plane = /* @__PURE__ */new Plane();\nconst _closestPoint = /* @__PURE__ */new Vector3();\nconst _triangle = /* @__PURE__ */new Triangle();\nclass ConvexHull {\n  constructor() {\n    this.tolerance = -1;\n    this.faces = [];\n    this.newFaces = [];\n    this.assigned = new VertexList();\n    this.unassigned = new VertexList();\n    this.vertices = [];\n  }\n  setFromPoints(points) {\n    if (points.length >= 4) {\n      this.makeEmpty();\n      for (let i = 0, l = points.length; i < l; i++) {\n        this.vertices.push(new VertexNode(points[i]));\n      }\n      this.compute();\n    }\n    return this;\n  }\n  setFromObject(object) {\n    const points = [];\n    object.updateMatrixWorld(true);\n    object.traverse(function (node) {\n      const geometry = node.geometry;\n      if (geometry !== void 0) {\n        const attribute = geometry.attributes.position;\n        if (attribute !== void 0) {\n          for (let i = 0, l = attribute.count; i < l; i++) {\n            const point = new Vector3();\n            point.fromBufferAttribute(attribute, i).applyMatrix4(node.matrixWorld);\n            points.push(point);\n          }\n        }\n      }\n    });\n    return this.setFromPoints(points);\n  }\n  containsPoint(point) {\n    const faces = this.faces;\n    for (let i = 0, l = faces.length; i < l; i++) {\n      const face = faces[i];\n      if (face.distanceToPoint(point) > this.tolerance) return false;\n    }\n    return true;\n  }\n  intersectRay(ray, target) {\n    const faces = this.faces;\n    let tNear = -Infinity;\n    let tFar = Infinity;\n    for (let i = 0, l = faces.length; i < l; i++) {\n      const face = faces[i];\n      const vN = face.distanceToPoint(ray.origin);\n      const vD = face.normal.dot(ray.direction);\n      if (vN > 0 && vD >= 0) return null;\n      const t = vD !== 0 ? -vN / vD : 0;\n      if (t <= 0) continue;\n      if (vD > 0) {\n        tFar = Math.min(t, tFar);\n      } else {\n        tNear = Math.max(t, tNear);\n      }\n      if (tNear > tFar) {\n        return null;\n      }\n    }\n    if (tNear !== -Infinity) {\n      ray.at(tNear, target);\n    } else {\n      ray.at(tFar, target);\n    }\n    return target;\n  }\n  intersectsRay(ray) {\n    return this.intersectRay(ray, _v1) !== null;\n  }\n  makeEmpty() {\n    this.faces = [];\n    this.vertices = [];\n    return this;\n  }\n  // Adds a vertex to the 'assigned' list of vertices and assigns it to the given face\n  addVertexToFace(vertex, face) {\n    vertex.face = face;\n    if (face.outside === null) {\n      this.assigned.append(vertex);\n    } else {\n      this.assigned.insertBefore(face.outside, vertex);\n    }\n    face.outside = vertex;\n    return this;\n  }\n  // Removes a vertex from the 'assigned' list of vertices and from the given face\n  removeVertexFromFace(vertex, face) {\n    if (vertex === face.outside) {\n      if (vertex.next !== null && vertex.next.face === face) {\n        face.outside = vertex.next;\n      } else {\n        face.outside = null;\n      }\n    }\n    this.assigned.remove(vertex);\n    return this;\n  }\n  // Removes all the visible vertices that a given face is able to see which are stored in the 'assigned' vertex list\n  removeAllVerticesFromFace(face) {\n    if (face.outside !== null) {\n      const start = face.outside;\n      let end = face.outside;\n      while (end.next !== null && end.next.face === face) {\n        end = end.next;\n      }\n      this.assigned.removeSubList(start, end);\n      start.prev = end.next = null;\n      face.outside = null;\n      return start;\n    }\n  }\n  // Removes all the visible vertices that 'face' is able to see\n  deleteFaceVertices(face, absorbingFace) {\n    const faceVertices = this.removeAllVerticesFromFace(face);\n    if (faceVertices !== void 0) {\n      if (absorbingFace === void 0) {\n        this.unassigned.appendChain(faceVertices);\n      } else {\n        let vertex = faceVertices;\n        do {\n          const nextVertex = vertex.next;\n          const distance = absorbingFace.distanceToPoint(vertex.point);\n          if (distance > this.tolerance) {\n            this.addVertexToFace(vertex, absorbingFace);\n          } else {\n            this.unassigned.append(vertex);\n          }\n          vertex = nextVertex;\n        } while (vertex !== null);\n      }\n    }\n    return this;\n  }\n  // Reassigns as many vertices as possible from the unassigned list to the new faces\n  resolveUnassignedPoints(newFaces) {\n    if (this.unassigned.isEmpty() === false) {\n      let vertex = this.unassigned.first();\n      do {\n        const nextVertex = vertex.next;\n        let maxDistance = this.tolerance;\n        let maxFace = null;\n        for (let i = 0; i < newFaces.length; i++) {\n          const face = newFaces[i];\n          if (face.mark === Visible) {\n            const distance = face.distanceToPoint(vertex.point);\n            if (distance > maxDistance) {\n              maxDistance = distance;\n              maxFace = face;\n            }\n            if (maxDistance > 1e3 * this.tolerance) break;\n          }\n        }\n        if (maxFace !== null) {\n          this.addVertexToFace(vertex, maxFace);\n        }\n        vertex = nextVertex;\n      } while (vertex !== null);\n    }\n    return this;\n  }\n  // Computes the extremes of a simplex which will be the initial hull\n  computeExtremes() {\n    const min = new Vector3();\n    const max = new Vector3();\n    const minVertices = [];\n    const maxVertices = [];\n    for (let i = 0; i < 3; i++) {\n      minVertices[i] = maxVertices[i] = this.vertices[0];\n    }\n    min.copy(this.vertices[0].point);\n    max.copy(this.vertices[0].point);\n    for (let i = 0, l = this.vertices.length; i < l; i++) {\n      const vertex = this.vertices[i];\n      const point = vertex.point;\n      for (let j = 0; j < 3; j++) {\n        if (point.getComponent(j) < min.getComponent(j)) {\n          min.setComponent(j, point.getComponent(j));\n          minVertices[j] = vertex;\n        }\n      }\n      for (let j = 0; j < 3; j++) {\n        if (point.getComponent(j) > max.getComponent(j)) {\n          max.setComponent(j, point.getComponent(j));\n          maxVertices[j] = vertex;\n        }\n      }\n    }\n    this.tolerance = 3 * Number.EPSILON * (Math.max(Math.abs(min.x), Math.abs(max.x)) + Math.max(Math.abs(min.y), Math.abs(max.y)) + Math.max(Math.abs(min.z), Math.abs(max.z)));\n    return {\n      min: minVertices,\n      max: maxVertices\n    };\n  }\n  // Computes the initial simplex assigning to its faces all the points\n  // that are candidates to form part of the hull\n  computeInitialHull() {\n    const vertices = this.vertices;\n    const extremes = this.computeExtremes();\n    const min = extremes.min;\n    const max = extremes.max;\n    let maxDistance = 0;\n    let index = 0;\n    for (let i = 0; i < 3; i++) {\n      const distance = max[i].point.getComponent(i) - min[i].point.getComponent(i);\n      if (distance > maxDistance) {\n        maxDistance = distance;\n        index = i;\n      }\n    }\n    const v0 = min[index];\n    const v1 = max[index];\n    let v2;\n    let v3;\n    maxDistance = 0;\n    _line3.set(v0.point, v1.point);\n    for (let i = 0, l = this.vertices.length; i < l; i++) {\n      const vertex = vertices[i];\n      if (vertex !== v0 && vertex !== v1) {\n        _line3.closestPointToPoint(vertex.point, true, _closestPoint);\n        const distance = _closestPoint.distanceToSquared(vertex.point);\n        if (distance > maxDistance) {\n          maxDistance = distance;\n          v2 = vertex;\n        }\n      }\n    }\n    maxDistance = -1;\n    _plane.setFromCoplanarPoints(v0.point, v1.point, v2.point);\n    for (let i = 0, l = this.vertices.length; i < l; i++) {\n      const vertex = vertices[i];\n      if (vertex !== v0 && vertex !== v1 && vertex !== v2) {\n        const distance = Math.abs(_plane.distanceToPoint(vertex.point));\n        if (distance > maxDistance) {\n          maxDistance = distance;\n          v3 = vertex;\n        }\n      }\n    }\n    const faces = [];\n    if (_plane.distanceToPoint(v3.point) < 0) {\n      faces.push(Face.create(v0, v1, v2), Face.create(v3, v1, v0), Face.create(v3, v2, v1), Face.create(v3, v0, v2));\n      for (let i = 0; i < 3; i++) {\n        const j = (i + 1) % 3;\n        faces[i + 1].getEdge(2).setTwin(faces[0].getEdge(j));\n        faces[i + 1].getEdge(1).setTwin(faces[j + 1].getEdge(0));\n      }\n    } else {\n      faces.push(Face.create(v0, v2, v1), Face.create(v3, v0, v1), Face.create(v3, v1, v2), Face.create(v3, v2, v0));\n      for (let i = 0; i < 3; i++) {\n        const j = (i + 1) % 3;\n        faces[i + 1].getEdge(2).setTwin(faces[0].getEdge((3 - i) % 3));\n        faces[i + 1].getEdge(0).setTwin(faces[j + 1].getEdge(1));\n      }\n    }\n    for (let i = 0; i < 4; i++) {\n      this.faces.push(faces[i]);\n    }\n    for (let i = 0, l = vertices.length; i < l; i++) {\n      const vertex = vertices[i];\n      if (vertex !== v0 && vertex !== v1 && vertex !== v2 && vertex !== v3) {\n        maxDistance = this.tolerance;\n        let maxFace = null;\n        for (let j = 0; j < 4; j++) {\n          const distance = this.faces[j].distanceToPoint(vertex.point);\n          if (distance > maxDistance) {\n            maxDistance = distance;\n            maxFace = this.faces[j];\n          }\n        }\n        if (maxFace !== null) {\n          this.addVertexToFace(vertex, maxFace);\n        }\n      }\n    }\n    return this;\n  }\n  // Removes inactive faces\n  reindexFaces() {\n    const activeFaces = [];\n    for (let i = 0; i < this.faces.length; i++) {\n      const face = this.faces[i];\n      if (face.mark === Visible) {\n        activeFaces.push(face);\n      }\n    }\n    this.faces = activeFaces;\n    return this;\n  }\n  // Finds the next vertex to create faces with the current hull\n  nextVertexToAdd() {\n    if (this.assigned.isEmpty() === false) {\n      let eyeVertex,\n        maxDistance = 0;\n      const eyeFace = this.assigned.first().face;\n      let vertex = eyeFace.outside;\n      do {\n        const distance = eyeFace.distanceToPoint(vertex.point);\n        if (distance > maxDistance) {\n          maxDistance = distance;\n          eyeVertex = vertex;\n        }\n        vertex = vertex.next;\n      } while (vertex !== null && vertex.face === eyeFace);\n      return eyeVertex;\n    }\n  }\n  // Computes a chain of half edges in CCW order called the 'horizon'.\n  // For an edge to be part of the horizon it must join a face that can see\n  // 'eyePoint' and a face that cannot see 'eyePoint'.\n  computeHorizon(eyePoint, crossEdge, face, horizon) {\n    this.deleteFaceVertices(face);\n    face.mark = Deleted;\n    let edge;\n    if (crossEdge === null) {\n      edge = crossEdge = face.getEdge(0);\n    } else {\n      edge = crossEdge.next;\n    }\n    do {\n      const twinEdge = edge.twin;\n      const oppositeFace = twinEdge.face;\n      if (oppositeFace.mark === Visible) {\n        if (oppositeFace.distanceToPoint(eyePoint) > this.tolerance) {\n          this.computeHorizon(eyePoint, twinEdge, oppositeFace, horizon);\n        } else {\n          horizon.push(edge);\n        }\n      }\n      edge = edge.next;\n    } while (edge !== crossEdge);\n    return this;\n  }\n  // Creates a face with the vertices 'eyeVertex.point', 'horizonEdge.tail' and 'horizonEdge.head' in CCW order\n  addAdjoiningFace(eyeVertex, horizonEdge) {\n    const face = Face.create(eyeVertex, horizonEdge.tail(), horizonEdge.head());\n    this.faces.push(face);\n    face.getEdge(-1).setTwin(horizonEdge.twin);\n    return face.getEdge(0);\n  }\n  //  Adds 'horizon.length' faces to the hull, each face will be linked with the\n  //  horizon opposite face and the face on the left/right\n  addNewFaces(eyeVertex, horizon) {\n    this.newFaces = [];\n    let firstSideEdge = null;\n    let previousSideEdge = null;\n    for (let i = 0; i < horizon.length; i++) {\n      const horizonEdge = horizon[i];\n      const sideEdge = this.addAdjoiningFace(eyeVertex, horizonEdge);\n      if (firstSideEdge === null) {\n        firstSideEdge = sideEdge;\n      } else {\n        sideEdge.next.setTwin(previousSideEdge);\n      }\n      this.newFaces.push(sideEdge.face);\n      previousSideEdge = sideEdge;\n    }\n    firstSideEdge.next.setTwin(previousSideEdge);\n    return this;\n  }\n  // Adds a vertex to the hull\n  addVertexToHull(eyeVertex) {\n    const horizon = [];\n    this.unassigned.clear();\n    this.removeVertexFromFace(eyeVertex, eyeVertex.face);\n    this.computeHorizon(eyeVertex.point, null, eyeVertex.face, horizon);\n    this.addNewFaces(eyeVertex, horizon);\n    this.resolveUnassignedPoints(this.newFaces);\n    return this;\n  }\n  cleanup() {\n    this.assigned.clear();\n    this.unassigned.clear();\n    this.newFaces = [];\n    return this;\n  }\n  compute() {\n    let vertex;\n    this.computeInitialHull();\n    while ((vertex = this.nextVertexToAdd()) !== void 0) {\n      this.addVertexToHull(vertex);\n    }\n    this.reindexFaces();\n    this.cleanup();\n    return this;\n  }\n}\nconst Face = /* @__PURE__ */(() => {\n  class Face2 {\n    constructor() {\n      this.normal = new Vector3();\n      this.midpoint = new Vector3();\n      this.area = 0;\n      this.constant = 0;\n      this.outside = null;\n      this.mark = Visible;\n      this.edge = null;\n    }\n    static create(a, b, c) {\n      const face = new Face2();\n      const e0 = new HalfEdge(a, face);\n      const e1 = new HalfEdge(b, face);\n      const e2 = new HalfEdge(c, face);\n      e0.next = e2.prev = e1;\n      e1.next = e0.prev = e2;\n      e2.next = e1.prev = e0;\n      face.edge = e0;\n      return face.compute();\n    }\n    getEdge(i) {\n      let edge = this.edge;\n      while (i > 0) {\n        edge = edge.next;\n        i--;\n      }\n      while (i < 0) {\n        edge = edge.prev;\n        i++;\n      }\n      return edge;\n    }\n    compute() {\n      const a = this.edge.tail();\n      const b = this.edge.head();\n      const c = this.edge.next.head();\n      _triangle.set(a.point, b.point, c.point);\n      _triangle.getNormal(this.normal);\n      _triangle.getMidpoint(this.midpoint);\n      this.area = _triangle.getArea();\n      this.constant = this.normal.dot(this.midpoint);\n      return this;\n    }\n    distanceToPoint(point) {\n      return this.normal.dot(point) - this.constant;\n    }\n  }\n  return Face2;\n})();\nclass HalfEdge {\n  constructor(vertex, face) {\n    this.vertex = vertex;\n    this.prev = null;\n    this.next = null;\n    this.twin = null;\n    this.face = face;\n  }\n  head() {\n    return this.vertex;\n  }\n  tail() {\n    return this.prev ? this.prev.vertex : null;\n  }\n  length() {\n    const head = this.head();\n    const tail = this.tail();\n    if (tail !== null) {\n      return tail.point.distanceTo(head.point);\n    }\n    return -1;\n  }\n  lengthSquared() {\n    const head = this.head();\n    const tail = this.tail();\n    if (tail !== null) {\n      return tail.point.distanceToSquared(head.point);\n    }\n    return -1;\n  }\n  setTwin(edge) {\n    this.twin = edge;\n    edge.twin = this;\n    return this;\n  }\n}\nclass VertexNode {\n  constructor(point) {\n    this.point = point;\n    this.prev = null;\n    this.next = null;\n    this.face = null;\n  }\n}\nclass VertexList {\n  constructor() {\n    this.head = null;\n    this.tail = null;\n  }\n  first() {\n    return this.head;\n  }\n  last() {\n    return this.tail;\n  }\n  clear() {\n    this.head = this.tail = null;\n    return this;\n  }\n  // Inserts a vertex before the target vertex\n  insertBefore(target, vertex) {\n    vertex.prev = target.prev;\n    vertex.next = target;\n    if (vertex.prev === null) {\n      this.head = vertex;\n    } else {\n      vertex.prev.next = vertex;\n    }\n    target.prev = vertex;\n    return this;\n  }\n  // Inserts a vertex after the target vertex\n  insertAfter(target, vertex) {\n    vertex.prev = target;\n    vertex.next = target.next;\n    if (vertex.next === null) {\n      this.tail = vertex;\n    } else {\n      vertex.next.prev = vertex;\n    }\n    target.next = vertex;\n    return this;\n  }\n  // Appends a vertex to the end of the linked list\n  append(vertex) {\n    if (this.head === null) {\n      this.head = vertex;\n    } else {\n      this.tail.next = vertex;\n    }\n    vertex.prev = this.tail;\n    vertex.next = null;\n    this.tail = vertex;\n    return this;\n  }\n  // Appends a chain of vertices where 'vertex' is the head.\n  appendChain(vertex) {\n    if (this.head === null) {\n      this.head = vertex;\n    } else {\n      this.tail.next = vertex;\n    }\n    vertex.prev = this.tail;\n    while (vertex.next !== null) {\n      vertex = vertex.next;\n    }\n    this.tail = vertex;\n    return this;\n  }\n  // Removes a vertex from the linked list\n  remove(vertex) {\n    if (vertex.prev === null) {\n      this.head = vertex.next;\n    } else {\n      vertex.prev.next = vertex.next;\n    }\n    if (vertex.next === null) {\n      this.tail = vertex.prev;\n    } else {\n      vertex.next.prev = vertex.prev;\n    }\n    return this;\n  }\n  // Removes a list of vertices whose 'head' is 'a' and whose 'tail' is b\n  removeSubList(a, b) {\n    if (a.prev === null) {\n      this.head = b.next;\n    } else {\n      a.prev.next = b.next;\n    }\n    if (b.next === null) {\n      this.tail = a.prev;\n    } else {\n      b.next.prev = a.prev;\n    }\n    return this;\n  }\n  isEmpty() {\n    return this.head === null;\n  }\n}\nexport { ConvexHull, Face, HalfEdge, VertexList, VertexNode };", "map": {"version": 3, "names": ["Visible", "Deleted", "_v1", "Vector3", "_line3", "Line3", "_plane", "Plane", "_closestPoint", "_triangle", "Triangle", "ConvexHull", "constructor", "tolerance", "faces", "newFaces", "assigned", "VertexList", "unassigned", "vertices", "setFromPoints", "points", "length", "makeEmpty", "i", "l", "push", "VertexNode", "compute", "setFromObject", "object", "updateMatrixWorld", "traverse", "node", "geometry", "attribute", "attributes", "position", "count", "point", "fromBufferAttribute", "applyMatrix4", "matrixWorld", "containsPoint", "face", "distanceToPoint", "intersectRay", "ray", "target", "tNear", "Infinity", "tFar", "vN", "origin", "vD", "normal", "dot", "direction", "t", "Math", "min", "max", "at", "intersectsRay", "addVertexToFace", "vertex", "outside", "append", "insertBefore", "removeVertexFromFace", "next", "remove", "removeAllVerticesFromFace", "start", "end", "removeSubList", "prev", "deleteFaceVertices", "absorbingFace", "faceVertices", "append<PERSON><PERSON><PERSON>", "nextVertex", "distance", "resolveUnassignedPoints", "isEmpty", "first", "maxDistance", "maxFace", "mark", "computeExtremes", "minVertices", "maxVertices", "copy", "j", "getComponent", "setComponent", "Number", "EPSILON", "abs", "x", "y", "z", "computeInitialHull", "extremes", "index", "v0", "v1", "v2", "v3", "set", "closestPointToPoint", "distanceToSquared", "setFromCoplanarPoints", "Face", "create", "getEdge", "setTwin", "reindexFaces", "activeFaces", "nextVertexToAdd", "eyeVertex", "eyeFace", "computeHorizon", "eyePoint", "crossEdge", "horizon", "edge", "twinEdge", "twin", "oppositeFace", "addAdjoiningFace", "horizonEdge", "tail", "head", "addNewFaces", "firstSideEdge", "previousSideEdge", "sideEdge", "addVertexToHull", "clear", "cleanup", "Face2", "midpoint", "area", "constant", "a", "b", "c", "e0", "Half<PERSON><PERSON>", "e1", "e2", "getNormal", "getMidpoint", "getArea", "distanceTo", "lengthSquared", "last", "insertAfter"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\math\\ConvexHull.js"], "sourcesContent": ["import { Line3, Plane, Triangle, Vector3 } from 'three'\n\n/**\n * Ported from: https://github.com/maurizzzio/quickhull3d/ by <PERSON><PERSON><PERSON> (https://github.com/ma<PERSON><PERSON><PERSON>)\n */\n\nconst Visible = 0\nconst Deleted = 1\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _line3 = /* @__PURE__ */ new Line3()\nconst _plane = /* @__PURE__ */ new Plane()\nconst _closestPoint = /* @__PURE__ */ new Vector3()\nconst _triangle = /* @__PURE__ */ new Triangle()\n\nclass ConvexHull {\n  constructor() {\n    this.tolerance = -1\n\n    this.faces = [] // the generated faces of the convex hull\n    this.newFaces = [] // this array holds the faces that are generated within a single iteration\n\n    // the vertex lists work as follows:\n    //\n    // let 'a' and 'b' be 'Face' instances\n    // let 'v' be points wrapped as instance of 'Vertex'\n    //\n    //     [v, v, ..., v, v, v, ...]\n    //      ^             ^\n    //      |             |\n    //  a.outside     b.outside\n    //\n    this.assigned = new VertexList()\n    this.unassigned = new VertexList()\n\n    this.vertices = [] // vertices of the hull (internal representation of given geometry data)\n  }\n\n  setFromPoints(points) {\n    // The algorithm needs at least four points.\n\n    if (points.length >= 4) {\n      this.makeEmpty()\n\n      for (let i = 0, l = points.length; i < l; i++) {\n        this.vertices.push(new VertexNode(points[i]))\n      }\n\n      this.compute()\n    }\n\n    return this\n  }\n\n  setFromObject(object) {\n    const points = []\n\n    object.updateMatrixWorld(true)\n\n    object.traverse(function (node) {\n      const geometry = node.geometry\n\n      if (geometry !== undefined) {\n        const attribute = geometry.attributes.position\n\n        if (attribute !== undefined) {\n          for (let i = 0, l = attribute.count; i < l; i++) {\n            const point = new Vector3()\n\n            point.fromBufferAttribute(attribute, i).applyMatrix4(node.matrixWorld)\n\n            points.push(point)\n          }\n        }\n      }\n    })\n\n    return this.setFromPoints(points)\n  }\n\n  containsPoint(point) {\n    const faces = this.faces\n\n    for (let i = 0, l = faces.length; i < l; i++) {\n      const face = faces[i]\n\n      // compute signed distance and check on what half space the point lies\n\n      if (face.distanceToPoint(point) > this.tolerance) return false\n    }\n\n    return true\n  }\n\n  intersectRay(ray, target) {\n    // based on \"Fast Ray-Convex Polyhedron Intersection\" by Eric Haines, GRAPHICS GEMS II\n\n    const faces = this.faces\n\n    let tNear = -Infinity\n    let tFar = Infinity\n\n    for (let i = 0, l = faces.length; i < l; i++) {\n      const face = faces[i]\n\n      // interpret faces as planes for the further computation\n\n      const vN = face.distanceToPoint(ray.origin)\n      const vD = face.normal.dot(ray.direction)\n\n      // if the origin is on the positive side of a plane (so the plane can \"see\" the origin) and\n      // the ray is turned away or parallel to the plane, there is no intersection\n\n      if (vN > 0 && vD >= 0) return null\n\n      // compute the distance from the ray’s origin to the intersection with the plane\n\n      const t = vD !== 0 ? -vN / vD : 0\n\n      // only proceed if the distance is positive. a negative distance means the intersection point\n      // lies \"behind\" the origin\n\n      if (t <= 0) continue\n\n      // now categorized plane as front-facing or back-facing\n\n      if (vD > 0) {\n        // plane faces away from the ray, so this plane is a back-face\n\n        tFar = Math.min(t, tFar)\n      } else {\n        // front-face\n\n        tNear = Math.max(t, tNear)\n      }\n\n      if (tNear > tFar) {\n        // if tNear ever is greater than tFar, the ray must miss the convex hull\n\n        return null\n      }\n    }\n\n    // evaluate intersection point\n\n    // always try tNear first since its the closer intersection point\n\n    if (tNear !== -Infinity) {\n      ray.at(tNear, target)\n    } else {\n      ray.at(tFar, target)\n    }\n\n    return target\n  }\n\n  intersectsRay(ray) {\n    return this.intersectRay(ray, _v1) !== null\n  }\n\n  makeEmpty() {\n    this.faces = []\n    this.vertices = []\n\n    return this\n  }\n\n  // Adds a vertex to the 'assigned' list of vertices and assigns it to the given face\n\n  addVertexToFace(vertex, face) {\n    vertex.face = face\n\n    if (face.outside === null) {\n      this.assigned.append(vertex)\n    } else {\n      this.assigned.insertBefore(face.outside, vertex)\n    }\n\n    face.outside = vertex\n\n    return this\n  }\n\n  // Removes a vertex from the 'assigned' list of vertices and from the given face\n\n  removeVertexFromFace(vertex, face) {\n    if (vertex === face.outside) {\n      // fix face.outside link\n\n      if (vertex.next !== null && vertex.next.face === face) {\n        // face has at least 2 outside vertices, move the 'outside' reference\n\n        face.outside = vertex.next\n      } else {\n        // vertex was the only outside vertex that face had\n\n        face.outside = null\n      }\n    }\n\n    this.assigned.remove(vertex)\n\n    return this\n  }\n\n  // Removes all the visible vertices that a given face is able to see which are stored in the 'assigned' vertex list\n\n  removeAllVerticesFromFace(face) {\n    if (face.outside !== null) {\n      // reference to the first and last vertex of this face\n\n      const start = face.outside\n      let end = face.outside\n\n      while (end.next !== null && end.next.face === face) {\n        end = end.next\n      }\n\n      this.assigned.removeSubList(start, end)\n\n      // fix references\n\n      start.prev = end.next = null\n      face.outside = null\n\n      return start\n    }\n  }\n\n  // Removes all the visible vertices that 'face' is able to see\n\n  deleteFaceVertices(face, absorbingFace) {\n    const faceVertices = this.removeAllVerticesFromFace(face)\n\n    if (faceVertices !== undefined) {\n      if (absorbingFace === undefined) {\n        // mark the vertices to be reassigned to some other face\n\n        this.unassigned.appendChain(faceVertices)\n      } else {\n        // if there's an absorbing face try to assign as many vertices as possible to it\n\n        let vertex = faceVertices\n\n        do {\n          // we need to buffer the subsequent vertex at this point because the 'vertex.next' reference\n          // will be changed by upcoming method calls\n\n          const nextVertex = vertex.next\n\n          const distance = absorbingFace.distanceToPoint(vertex.point)\n\n          // check if 'vertex' is able to see 'absorbingFace'\n\n          if (distance > this.tolerance) {\n            this.addVertexToFace(vertex, absorbingFace)\n          } else {\n            this.unassigned.append(vertex)\n          }\n\n          // now assign next vertex\n\n          vertex = nextVertex\n        } while (vertex !== null)\n      }\n    }\n\n    return this\n  }\n\n  // Reassigns as many vertices as possible from the unassigned list to the new faces\n\n  resolveUnassignedPoints(newFaces) {\n    if (this.unassigned.isEmpty() === false) {\n      let vertex = this.unassigned.first()\n\n      do {\n        // buffer 'next' reference, see .deleteFaceVertices()\n\n        const nextVertex = vertex.next\n\n        let maxDistance = this.tolerance\n\n        let maxFace = null\n\n        for (let i = 0; i < newFaces.length; i++) {\n          const face = newFaces[i]\n\n          if (face.mark === Visible) {\n            const distance = face.distanceToPoint(vertex.point)\n\n            if (distance > maxDistance) {\n              maxDistance = distance\n              maxFace = face\n            }\n\n            if (maxDistance > 1000 * this.tolerance) break\n          }\n        }\n\n        // 'maxFace' can be null e.g. if there are identical vertices\n\n        if (maxFace !== null) {\n          this.addVertexToFace(vertex, maxFace)\n        }\n\n        vertex = nextVertex\n      } while (vertex !== null)\n    }\n\n    return this\n  }\n\n  // Computes the extremes of a simplex which will be the initial hull\n\n  computeExtremes() {\n    const min = new Vector3()\n    const max = new Vector3()\n\n    const minVertices = []\n    const maxVertices = []\n\n    // initially assume that the first vertex is the min/max\n\n    for (let i = 0; i < 3; i++) {\n      minVertices[i] = maxVertices[i] = this.vertices[0]\n    }\n\n    min.copy(this.vertices[0].point)\n    max.copy(this.vertices[0].point)\n\n    // compute the min/max vertex on all six directions\n\n    for (let i = 0, l = this.vertices.length; i < l; i++) {\n      const vertex = this.vertices[i]\n      const point = vertex.point\n\n      // update the min coordinates\n\n      for (let j = 0; j < 3; j++) {\n        if (point.getComponent(j) < min.getComponent(j)) {\n          min.setComponent(j, point.getComponent(j))\n          minVertices[j] = vertex\n        }\n      }\n\n      // update the max coordinates\n\n      for (let j = 0; j < 3; j++) {\n        if (point.getComponent(j) > max.getComponent(j)) {\n          max.setComponent(j, point.getComponent(j))\n          maxVertices[j] = vertex\n        }\n      }\n    }\n\n    // use min/max vectors to compute an optimal epsilon\n\n    this.tolerance =\n      3 *\n      Number.EPSILON *\n      (Math.max(Math.abs(min.x), Math.abs(max.x)) +\n        Math.max(Math.abs(min.y), Math.abs(max.y)) +\n        Math.max(Math.abs(min.z), Math.abs(max.z)))\n\n    return { min: minVertices, max: maxVertices }\n  }\n\n  // Computes the initial simplex assigning to its faces all the points\n  // that are candidates to form part of the hull\n\n  computeInitialHull() {\n    const vertices = this.vertices\n    const extremes = this.computeExtremes()\n    const min = extremes.min\n    const max = extremes.max\n\n    // 1. Find the two vertices 'v0' and 'v1' with the greatest 1d separation\n    // (max.x - min.x)\n    // (max.y - min.y)\n    // (max.z - min.z)\n\n    let maxDistance = 0\n    let index = 0\n\n    for (let i = 0; i < 3; i++) {\n      const distance = max[i].point.getComponent(i) - min[i].point.getComponent(i)\n\n      if (distance > maxDistance) {\n        maxDistance = distance\n        index = i\n      }\n    }\n\n    const v0 = min[index]\n    const v1 = max[index]\n    let v2\n    let v3\n\n    // 2. The next vertex 'v2' is the one farthest to the line formed by 'v0' and 'v1'\n\n    maxDistance = 0\n    _line3.set(v0.point, v1.point)\n\n    for (let i = 0, l = this.vertices.length; i < l; i++) {\n      const vertex = vertices[i]\n\n      if (vertex !== v0 && vertex !== v1) {\n        _line3.closestPointToPoint(vertex.point, true, _closestPoint)\n\n        const distance = _closestPoint.distanceToSquared(vertex.point)\n\n        if (distance > maxDistance) {\n          maxDistance = distance\n          v2 = vertex\n        }\n      }\n    }\n\n    // 3. The next vertex 'v3' is the one farthest to the plane 'v0', 'v1', 'v2'\n\n    maxDistance = -1\n    _plane.setFromCoplanarPoints(v0.point, v1.point, v2.point)\n\n    for (let i = 0, l = this.vertices.length; i < l; i++) {\n      const vertex = vertices[i]\n\n      if (vertex !== v0 && vertex !== v1 && vertex !== v2) {\n        const distance = Math.abs(_plane.distanceToPoint(vertex.point))\n\n        if (distance > maxDistance) {\n          maxDistance = distance\n          v3 = vertex\n        }\n      }\n    }\n\n    const faces = []\n\n    if (_plane.distanceToPoint(v3.point) < 0) {\n      // the face is not able to see the point so 'plane.normal' is pointing outside the tetrahedron\n\n      faces.push(Face.create(v0, v1, v2), Face.create(v3, v1, v0), Face.create(v3, v2, v1), Face.create(v3, v0, v2))\n\n      // set the twin edge\n\n      for (let i = 0; i < 3; i++) {\n        const j = (i + 1) % 3\n\n        // join face[ i ] i > 0, with the first face\n\n        faces[i + 1].getEdge(2).setTwin(faces[0].getEdge(j))\n\n        // join face[ i ] with face[ i + 1 ], 1 <= i <= 3\n\n        faces[i + 1].getEdge(1).setTwin(faces[j + 1].getEdge(0))\n      }\n    } else {\n      // the face is able to see the point so 'plane.normal' is pointing inside the tetrahedron\n\n      faces.push(Face.create(v0, v2, v1), Face.create(v3, v0, v1), Face.create(v3, v1, v2), Face.create(v3, v2, v0))\n\n      // set the twin edge\n\n      for (let i = 0; i < 3; i++) {\n        const j = (i + 1) % 3\n\n        // join face[ i ] i > 0, with the first face\n\n        faces[i + 1].getEdge(2).setTwin(faces[0].getEdge((3 - i) % 3))\n\n        // join face[ i ] with face[ i + 1 ]\n\n        faces[i + 1].getEdge(0).setTwin(faces[j + 1].getEdge(1))\n      }\n    }\n\n    // the initial hull is the tetrahedron\n\n    for (let i = 0; i < 4; i++) {\n      this.faces.push(faces[i])\n    }\n\n    // initial assignment of vertices to the faces of the tetrahedron\n\n    for (let i = 0, l = vertices.length; i < l; i++) {\n      const vertex = vertices[i]\n\n      if (vertex !== v0 && vertex !== v1 && vertex !== v2 && vertex !== v3) {\n        maxDistance = this.tolerance\n        let maxFace = null\n\n        for (let j = 0; j < 4; j++) {\n          const distance = this.faces[j].distanceToPoint(vertex.point)\n\n          if (distance > maxDistance) {\n            maxDistance = distance\n            maxFace = this.faces[j]\n          }\n        }\n\n        if (maxFace !== null) {\n          this.addVertexToFace(vertex, maxFace)\n        }\n      }\n    }\n\n    return this\n  }\n\n  // Removes inactive faces\n\n  reindexFaces() {\n    const activeFaces = []\n\n    for (let i = 0; i < this.faces.length; i++) {\n      const face = this.faces[i]\n\n      if (face.mark === Visible) {\n        activeFaces.push(face)\n      }\n    }\n\n    this.faces = activeFaces\n\n    return this\n  }\n\n  // Finds the next vertex to create faces with the current hull\n\n  nextVertexToAdd() {\n    // if the 'assigned' list of vertices is empty, no vertices are left. return with 'undefined'\n\n    if (this.assigned.isEmpty() === false) {\n      let eyeVertex,\n        maxDistance = 0\n\n      // grap the first available face and start with the first visible vertex of that face\n\n      const eyeFace = this.assigned.first().face\n      let vertex = eyeFace.outside\n\n      // now calculate the farthest vertex that face can see\n\n      do {\n        const distance = eyeFace.distanceToPoint(vertex.point)\n\n        if (distance > maxDistance) {\n          maxDistance = distance\n          eyeVertex = vertex\n        }\n\n        vertex = vertex.next\n      } while (vertex !== null && vertex.face === eyeFace)\n\n      return eyeVertex\n    }\n  }\n\n  // Computes a chain of half edges in CCW order called the 'horizon'.\n  // For an edge to be part of the horizon it must join a face that can see\n  // 'eyePoint' and a face that cannot see 'eyePoint'.\n\n  computeHorizon(eyePoint, crossEdge, face, horizon) {\n    // moves face's vertices to the 'unassigned' vertex list\n\n    this.deleteFaceVertices(face)\n\n    face.mark = Deleted\n\n    let edge\n\n    if (crossEdge === null) {\n      edge = crossEdge = face.getEdge(0)\n    } else {\n      // start from the next edge since 'crossEdge' was already analyzed\n      // (actually 'crossEdge.twin' was the edge who called this method recursively)\n\n      edge = crossEdge.next\n    }\n\n    do {\n      const twinEdge = edge.twin\n      const oppositeFace = twinEdge.face\n\n      if (oppositeFace.mark === Visible) {\n        if (oppositeFace.distanceToPoint(eyePoint) > this.tolerance) {\n          // the opposite face can see the vertex, so proceed with next edge\n\n          this.computeHorizon(eyePoint, twinEdge, oppositeFace, horizon)\n        } else {\n          // the opposite face can't see the vertex, so this edge is part of the horizon\n\n          horizon.push(edge)\n        }\n      }\n\n      edge = edge.next\n    } while (edge !== crossEdge)\n\n    return this\n  }\n\n  // Creates a face with the vertices 'eyeVertex.point', 'horizonEdge.tail' and 'horizonEdge.head' in CCW order\n\n  addAdjoiningFace(eyeVertex, horizonEdge) {\n    // all the half edges are created in ccw order thus the face is always pointing outside the hull\n\n    const face = Face.create(eyeVertex, horizonEdge.tail(), horizonEdge.head())\n\n    this.faces.push(face)\n\n    // join face.getEdge( - 1 ) with the horizon's opposite edge face.getEdge( - 1 ) = face.getEdge( 2 )\n\n    face.getEdge(-1).setTwin(horizonEdge.twin)\n\n    return face.getEdge(0) // the half edge whose vertex is the eyeVertex\n  }\n\n  //  Adds 'horizon.length' faces to the hull, each face will be linked with the\n  //  horizon opposite face and the face on the left/right\n\n  addNewFaces(eyeVertex, horizon) {\n    this.newFaces = []\n\n    let firstSideEdge = null\n    let previousSideEdge = null\n\n    for (let i = 0; i < horizon.length; i++) {\n      const horizonEdge = horizon[i]\n\n      // returns the right side edge\n\n      const sideEdge = this.addAdjoiningFace(eyeVertex, horizonEdge)\n\n      if (firstSideEdge === null) {\n        firstSideEdge = sideEdge\n      } else {\n        // joins face.getEdge( 1 ) with previousFace.getEdge( 0 )\n\n        sideEdge.next.setTwin(previousSideEdge)\n      }\n\n      this.newFaces.push(sideEdge.face)\n      previousSideEdge = sideEdge\n    }\n\n    // perform final join of new faces\n\n    firstSideEdge.next.setTwin(previousSideEdge)\n\n    return this\n  }\n\n  // Adds a vertex to the hull\n\n  addVertexToHull(eyeVertex) {\n    const horizon = []\n\n    this.unassigned.clear()\n\n    // remove 'eyeVertex' from 'eyeVertex.face' so that it can't be added to the 'unassigned' vertex list\n\n    this.removeVertexFromFace(eyeVertex, eyeVertex.face)\n\n    this.computeHorizon(eyeVertex.point, null, eyeVertex.face, horizon)\n\n    this.addNewFaces(eyeVertex, horizon)\n\n    // reassign 'unassigned' vertices to the new faces\n\n    this.resolveUnassignedPoints(this.newFaces)\n\n    return this\n  }\n\n  cleanup() {\n    this.assigned.clear()\n    this.unassigned.clear()\n    this.newFaces = []\n\n    return this\n  }\n\n  compute() {\n    let vertex\n\n    this.computeInitialHull()\n\n    // add all available vertices gradually to the hull\n\n    while ((vertex = this.nextVertexToAdd()) !== undefined) {\n      this.addVertexToHull(vertex)\n    }\n\n    this.reindexFaces()\n\n    this.cleanup()\n\n    return this\n  }\n}\n\n//\n\nconst Face = /* @__PURE__ */ (() => {\n  class Face {\n    constructor() {\n      this.normal = new Vector3()\n      this.midpoint = new Vector3()\n      this.area = 0\n\n      this.constant = 0 // signed distance from face to the origin\n      this.outside = null // reference to a vertex in a vertex list this face can see\n      this.mark = Visible\n      this.edge = null\n    }\n\n    static create(a, b, c) {\n      const face = new Face()\n\n      const e0 = new HalfEdge(a, face)\n      const e1 = new HalfEdge(b, face)\n      const e2 = new HalfEdge(c, face)\n\n      // join edges\n\n      e0.next = e2.prev = e1\n      e1.next = e0.prev = e2\n      e2.next = e1.prev = e0\n\n      // main half edge reference\n\n      face.edge = e0\n\n      return face.compute()\n    }\n\n    getEdge(i) {\n      let edge = this.edge\n\n      while (i > 0) {\n        edge = edge.next\n        i--\n      }\n\n      while (i < 0) {\n        edge = edge.prev\n        i++\n      }\n\n      return edge\n    }\n\n    compute() {\n      const a = this.edge.tail()\n      const b = this.edge.head()\n      const c = this.edge.next.head()\n\n      _triangle.set(a.point, b.point, c.point)\n\n      _triangle.getNormal(this.normal)\n      _triangle.getMidpoint(this.midpoint)\n      this.area = _triangle.getArea()\n\n      this.constant = this.normal.dot(this.midpoint)\n\n      return this\n    }\n\n    distanceToPoint(point) {\n      return this.normal.dot(point) - this.constant\n    }\n  }\n\n  return Face\n})()\n\n// Entity for a Doubly-Connected Edge List (DCEL).\n\nclass HalfEdge {\n  constructor(vertex, face) {\n    this.vertex = vertex\n    this.prev = null\n    this.next = null\n    this.twin = null\n    this.face = face\n  }\n\n  head() {\n    return this.vertex\n  }\n\n  tail() {\n    return this.prev ? this.prev.vertex : null\n  }\n\n  length() {\n    const head = this.head()\n    const tail = this.tail()\n\n    if (tail !== null) {\n      return tail.point.distanceTo(head.point)\n    }\n\n    return -1\n  }\n\n  lengthSquared() {\n    const head = this.head()\n    const tail = this.tail()\n\n    if (tail !== null) {\n      return tail.point.distanceToSquared(head.point)\n    }\n\n    return -1\n  }\n\n  setTwin(edge) {\n    this.twin = edge\n    edge.twin = this\n\n    return this\n  }\n}\n\n// A vertex as a double linked list node.\n\nclass VertexNode {\n  constructor(point) {\n    this.point = point\n    this.prev = null\n    this.next = null\n    this.face = null // the face that is able to see this vertex\n  }\n}\n\n// A double linked list that contains vertex nodes.\n\nclass VertexList {\n  constructor() {\n    this.head = null\n    this.tail = null\n  }\n\n  first() {\n    return this.head\n  }\n\n  last() {\n    return this.tail\n  }\n\n  clear() {\n    this.head = this.tail = null\n\n    return this\n  }\n\n  // Inserts a vertex before the target vertex\n\n  insertBefore(target, vertex) {\n    vertex.prev = target.prev\n    vertex.next = target\n\n    if (vertex.prev === null) {\n      this.head = vertex\n    } else {\n      vertex.prev.next = vertex\n    }\n\n    target.prev = vertex\n\n    return this\n  }\n\n  // Inserts a vertex after the target vertex\n\n  insertAfter(target, vertex) {\n    vertex.prev = target\n    vertex.next = target.next\n\n    if (vertex.next === null) {\n      this.tail = vertex\n    } else {\n      vertex.next.prev = vertex\n    }\n\n    target.next = vertex\n\n    return this\n  }\n\n  // Appends a vertex to the end of the linked list\n\n  append(vertex) {\n    if (this.head === null) {\n      this.head = vertex\n    } else {\n      this.tail.next = vertex\n    }\n\n    vertex.prev = this.tail\n    vertex.next = null // the tail has no subsequent vertex\n\n    this.tail = vertex\n\n    return this\n  }\n\n  // Appends a chain of vertices where 'vertex' is the head.\n\n  appendChain(vertex) {\n    if (this.head === null) {\n      this.head = vertex\n    } else {\n      this.tail.next = vertex\n    }\n\n    vertex.prev = this.tail\n\n    // ensure that the 'tail' reference points to the last vertex of the chain\n\n    while (vertex.next !== null) {\n      vertex = vertex.next\n    }\n\n    this.tail = vertex\n\n    return this\n  }\n\n  // Removes a vertex from the linked list\n\n  remove(vertex) {\n    if (vertex.prev === null) {\n      this.head = vertex.next\n    } else {\n      vertex.prev.next = vertex.next\n    }\n\n    if (vertex.next === null) {\n      this.tail = vertex.prev\n    } else {\n      vertex.next.prev = vertex.prev\n    }\n\n    return this\n  }\n\n  // Removes a list of vertices whose 'head' is 'a' and whose 'tail' is b\n\n  removeSubList(a, b) {\n    if (a.prev === null) {\n      this.head = b.next\n    } else {\n      a.prev.next = b.next\n    }\n\n    if (b.next === null) {\n      this.tail = a.prev\n    } else {\n      b.next.prev = a.prev\n    }\n\n    return this\n  }\n\n  isEmpty() {\n    return this.head === null\n  }\n}\n\nexport { ConvexHull, Face, HalfEdge, VertexNode, VertexList }\n"], "mappings": ";AAMA,MAAMA,OAAA,GAAU;AAChB,MAAMC,OAAA,GAAU;AAEhB,MAAMC,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AACzC,MAAMC,MAAA,GAAyB,mBAAIC,KAAA,CAAO;AAC1C,MAAMC,MAAA,GAAyB,mBAAIC,KAAA,CAAO;AAC1C,MAAMC,aAAA,GAAgC,mBAAIL,OAAA,CAAS;AACnD,MAAMM,SAAA,GAA4B,mBAAIC,QAAA,CAAU;AAEhD,MAAMC,UAAA,CAAW;EACfC,YAAA,EAAc;IACZ,KAAKC,SAAA,GAAY;IAEjB,KAAKC,KAAA,GAAQ,EAAE;IACf,KAAKC,QAAA,GAAW,EAAE;IAYlB,KAAKC,QAAA,GAAW,IAAIC,UAAA,CAAY;IAChC,KAAKC,UAAA,GAAa,IAAID,UAAA,CAAY;IAElC,KAAKE,QAAA,GAAW,EAAE;EACnB;EAEDC,cAAcC,MAAA,EAAQ;IAGpB,IAAIA,MAAA,CAAOC,MAAA,IAAU,GAAG;MACtB,KAAKC,SAAA,CAAW;MAEhB,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIJ,MAAA,CAAOC,MAAA,EAAQE,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,KAAKL,QAAA,CAASO,IAAA,CAAK,IAAIC,UAAA,CAAWN,MAAA,CAAOG,CAAC,CAAC,CAAC;MAC7C;MAED,KAAKI,OAAA,CAAS;IACf;IAED,OAAO;EACR;EAEDC,cAAcC,MAAA,EAAQ;IACpB,MAAMT,MAAA,GAAS,EAAE;IAEjBS,MAAA,CAAOC,iBAAA,CAAkB,IAAI;IAE7BD,MAAA,CAAOE,QAAA,CAAS,UAAUC,IAAA,EAAM;MAC9B,MAAMC,QAAA,GAAWD,IAAA,CAAKC,QAAA;MAEtB,IAAIA,QAAA,KAAa,QAAW;QAC1B,MAAMC,SAAA,GAAYD,QAAA,CAASE,UAAA,CAAWC,QAAA;QAEtC,IAAIF,SAAA,KAAc,QAAW;UAC3B,SAASX,CAAA,GAAI,GAAGC,CAAA,GAAIU,SAAA,CAAUG,KAAA,EAAOd,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YAC/C,MAAMe,KAAA,GAAQ,IAAIpC,OAAA,CAAS;YAE3BoC,KAAA,CAAMC,mBAAA,CAAoBL,SAAA,EAAWX,CAAC,EAAEiB,YAAA,CAAaR,IAAA,CAAKS,WAAW;YAErErB,MAAA,CAAOK,IAAA,CAAKa,KAAK;UAClB;QACF;MACF;IACP,CAAK;IAED,OAAO,KAAKnB,aAAA,CAAcC,MAAM;EACjC;EAEDsB,cAAcJ,KAAA,EAAO;IACnB,MAAMzB,KAAA,GAAQ,KAAKA,KAAA;IAEnB,SAASU,CAAA,GAAI,GAAGC,CAAA,GAAIX,KAAA,CAAMQ,MAAA,EAAQE,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC5C,MAAMoB,IAAA,GAAO9B,KAAA,CAAMU,CAAC;MAIpB,IAAIoB,IAAA,CAAKC,eAAA,CAAgBN,KAAK,IAAI,KAAK1B,SAAA,EAAW,OAAO;IAC1D;IAED,OAAO;EACR;EAEDiC,aAAaC,GAAA,EAAKC,MAAA,EAAQ;IAGxB,MAAMlC,KAAA,GAAQ,KAAKA,KAAA;IAEnB,IAAImC,KAAA,GAAQ,CAAAC,QAAA;IACZ,IAAIC,IAAA,GAAOD,QAAA;IAEX,SAAS1B,CAAA,GAAI,GAAGC,CAAA,GAAIX,KAAA,CAAMQ,MAAA,EAAQE,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC5C,MAAMoB,IAAA,GAAO9B,KAAA,CAAMU,CAAC;MAIpB,MAAM4B,EAAA,GAAKR,IAAA,CAAKC,eAAA,CAAgBE,GAAA,CAAIM,MAAM;MAC1C,MAAMC,EAAA,GAAKV,IAAA,CAAKW,MAAA,CAAOC,GAAA,CAAIT,GAAA,CAAIU,SAAS;MAKxC,IAAIL,EAAA,GAAK,KAAKE,EAAA,IAAM,GAAG,OAAO;MAI9B,MAAMI,CAAA,GAAIJ,EAAA,KAAO,IAAI,CAACF,EAAA,GAAKE,EAAA,GAAK;MAKhC,IAAII,CAAA,IAAK,GAAG;MAIZ,IAAIJ,EAAA,GAAK,GAAG;QAGVH,IAAA,GAAOQ,IAAA,CAAKC,GAAA,CAAIF,CAAA,EAAGP,IAAI;MAC/B,OAAa;QAGLF,KAAA,GAAQU,IAAA,CAAKE,GAAA,CAAIH,CAAA,EAAGT,KAAK;MAC1B;MAED,IAAIA,KAAA,GAAQE,IAAA,EAAM;QAGhB,OAAO;MACR;IACF;IAMD,IAAIF,KAAA,KAAU,CAAAC,QAAA,EAAW;MACvBH,GAAA,CAAIe,EAAA,CAAGb,KAAA,EAAOD,MAAM;IAC1B,OAAW;MACLD,GAAA,CAAIe,EAAA,CAAGX,IAAA,EAAMH,MAAM;IACpB;IAED,OAAOA,MAAA;EACR;EAEDe,cAAchB,GAAA,EAAK;IACjB,OAAO,KAAKD,YAAA,CAAaC,GAAA,EAAK7C,GAAG,MAAM;EACxC;EAEDqB,UAAA,EAAY;IACV,KAAKT,KAAA,GAAQ,EAAE;IACf,KAAKK,QAAA,GAAW,EAAE;IAElB,OAAO;EACR;EAAA;EAID6C,gBAAgBC,MAAA,EAAQrB,IAAA,EAAM;IAC5BqB,MAAA,CAAOrB,IAAA,GAAOA,IAAA;IAEd,IAAIA,IAAA,CAAKsB,OAAA,KAAY,MAAM;MACzB,KAAKlD,QAAA,CAASmD,MAAA,CAAOF,MAAM;IACjC,OAAW;MACL,KAAKjD,QAAA,CAASoD,YAAA,CAAaxB,IAAA,CAAKsB,OAAA,EAASD,MAAM;IAChD;IAEDrB,IAAA,CAAKsB,OAAA,GAAUD,MAAA;IAEf,OAAO;EACR;EAAA;EAIDI,qBAAqBJ,MAAA,EAAQrB,IAAA,EAAM;IACjC,IAAIqB,MAAA,KAAWrB,IAAA,CAAKsB,OAAA,EAAS;MAG3B,IAAID,MAAA,CAAOK,IAAA,KAAS,QAAQL,MAAA,CAAOK,IAAA,CAAK1B,IAAA,KAASA,IAAA,EAAM;QAGrDA,IAAA,CAAKsB,OAAA,GAAUD,MAAA,CAAOK,IAAA;MAC9B,OAAa;QAGL1B,IAAA,CAAKsB,OAAA,GAAU;MAChB;IACF;IAED,KAAKlD,QAAA,CAASuD,MAAA,CAAON,MAAM;IAE3B,OAAO;EACR;EAAA;EAIDO,0BAA0B5B,IAAA,EAAM;IAC9B,IAAIA,IAAA,CAAKsB,OAAA,KAAY,MAAM;MAGzB,MAAMO,KAAA,GAAQ7B,IAAA,CAAKsB,OAAA;MACnB,IAAIQ,GAAA,GAAM9B,IAAA,CAAKsB,OAAA;MAEf,OAAOQ,GAAA,CAAIJ,IAAA,KAAS,QAAQI,GAAA,CAAIJ,IAAA,CAAK1B,IAAA,KAASA,IAAA,EAAM;QAClD8B,GAAA,GAAMA,GAAA,CAAIJ,IAAA;MACX;MAED,KAAKtD,QAAA,CAAS2D,aAAA,CAAcF,KAAA,EAAOC,GAAG;MAItCD,KAAA,CAAMG,IAAA,GAAOF,GAAA,CAAIJ,IAAA,GAAO;MACxB1B,IAAA,CAAKsB,OAAA,GAAU;MAEf,OAAOO,KAAA;IACR;EACF;EAAA;EAIDI,mBAAmBjC,IAAA,EAAMkC,aAAA,EAAe;IACtC,MAAMC,YAAA,GAAe,KAAKP,yBAAA,CAA0B5B,IAAI;IAExD,IAAImC,YAAA,KAAiB,QAAW;MAC9B,IAAID,aAAA,KAAkB,QAAW;QAG/B,KAAK5D,UAAA,CAAW8D,WAAA,CAAYD,YAAY;MAChD,OAAa;QAGL,IAAId,MAAA,GAASc,YAAA;QAEb,GAAG;UAID,MAAME,UAAA,GAAahB,MAAA,CAAOK,IAAA;UAE1B,MAAMY,QAAA,GAAWJ,aAAA,CAAcjC,eAAA,CAAgBoB,MAAA,CAAO1B,KAAK;UAI3D,IAAI2C,QAAA,GAAW,KAAKrE,SAAA,EAAW;YAC7B,KAAKmD,eAAA,CAAgBC,MAAA,EAAQa,aAAa;UACtD,OAAiB;YACL,KAAK5D,UAAA,CAAWiD,MAAA,CAAOF,MAAM;UAC9B;UAIDA,MAAA,GAASgB,UAAA;QACnB,SAAiBhB,MAAA,KAAW;MACrB;IACF;IAED,OAAO;EACR;EAAA;EAIDkB,wBAAwBpE,QAAA,EAAU;IAChC,IAAI,KAAKG,UAAA,CAAWkE,OAAA,CAAO,MAAO,OAAO;MACvC,IAAInB,MAAA,GAAS,KAAK/C,UAAA,CAAWmE,KAAA,CAAO;MAEpC,GAAG;QAGD,MAAMJ,UAAA,GAAahB,MAAA,CAAOK,IAAA;QAE1B,IAAIgB,WAAA,GAAc,KAAKzE,SAAA;QAEvB,IAAI0E,OAAA,GAAU;QAEd,SAAS/D,CAAA,GAAI,GAAGA,CAAA,GAAIT,QAAA,CAASO,MAAA,EAAQE,CAAA,IAAK;UACxC,MAAMoB,IAAA,GAAO7B,QAAA,CAASS,CAAC;UAEvB,IAAIoB,IAAA,CAAK4C,IAAA,KAASxF,OAAA,EAAS;YACzB,MAAMkF,QAAA,GAAWtC,IAAA,CAAKC,eAAA,CAAgBoB,MAAA,CAAO1B,KAAK;YAElD,IAAI2C,QAAA,GAAWI,WAAA,EAAa;cAC1BA,WAAA,GAAcJ,QAAA;cACdK,OAAA,GAAU3C,IAAA;YACX;YAED,IAAI0C,WAAA,GAAc,MAAO,KAAKzE,SAAA,EAAW;UAC1C;QACF;QAID,IAAI0E,OAAA,KAAY,MAAM;UACpB,KAAKvB,eAAA,CAAgBC,MAAA,EAAQsB,OAAO;QACrC;QAEDtB,MAAA,GAASgB,UAAA;MACjB,SAAehB,MAAA,KAAW;IACrB;IAED,OAAO;EACR;EAAA;EAIDwB,gBAAA,EAAkB;IAChB,MAAM7B,GAAA,GAAM,IAAIzD,OAAA,CAAS;IACzB,MAAM0D,GAAA,GAAM,IAAI1D,OAAA,CAAS;IAEzB,MAAMuF,WAAA,GAAc,EAAE;IACtB,MAAMC,WAAA,GAAc,EAAE;IAItB,SAASnE,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1BkE,WAAA,CAAYlE,CAAC,IAAImE,WAAA,CAAYnE,CAAC,IAAI,KAAKL,QAAA,CAAS,CAAC;IAClD;IAEDyC,GAAA,CAAIgC,IAAA,CAAK,KAAKzE,QAAA,CAAS,CAAC,EAAEoB,KAAK;IAC/BsB,GAAA,CAAI+B,IAAA,CAAK,KAAKzE,QAAA,CAAS,CAAC,EAAEoB,KAAK;IAI/B,SAASf,CAAA,GAAI,GAAGC,CAAA,GAAI,KAAKN,QAAA,CAASG,MAAA,EAAQE,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MACpD,MAAMyC,MAAA,GAAS,KAAK9C,QAAA,CAASK,CAAC;MAC9B,MAAMe,KAAA,GAAQ0B,MAAA,CAAO1B,KAAA;MAIrB,SAASsD,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1B,IAAItD,KAAA,CAAMuD,YAAA,CAAaD,CAAC,IAAIjC,GAAA,CAAIkC,YAAA,CAAaD,CAAC,GAAG;UAC/CjC,GAAA,CAAImC,YAAA,CAAaF,CAAA,EAAGtD,KAAA,CAAMuD,YAAA,CAAaD,CAAC,CAAC;UACzCH,WAAA,CAAYG,CAAC,IAAI5B,MAAA;QAClB;MACF;MAID,SAAS4B,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1B,IAAItD,KAAA,CAAMuD,YAAA,CAAaD,CAAC,IAAIhC,GAAA,CAAIiC,YAAA,CAAaD,CAAC,GAAG;UAC/ChC,GAAA,CAAIkC,YAAA,CAAaF,CAAA,EAAGtD,KAAA,CAAMuD,YAAA,CAAaD,CAAC,CAAC;UACzCF,WAAA,CAAYE,CAAC,IAAI5B,MAAA;QAClB;MACF;IACF;IAID,KAAKpD,SAAA,GACH,IACAmF,MAAA,CAAOC,OAAA,IACNtC,IAAA,CAAKE,GAAA,CAAIF,IAAA,CAAKuC,GAAA,CAAItC,GAAA,CAAIuC,CAAC,GAAGxC,IAAA,CAAKuC,GAAA,CAAIrC,GAAA,CAAIsC,CAAC,CAAC,IACxCxC,IAAA,CAAKE,GAAA,CAAIF,IAAA,CAAKuC,GAAA,CAAItC,GAAA,CAAIwC,CAAC,GAAGzC,IAAA,CAAKuC,GAAA,CAAIrC,GAAA,CAAIuC,CAAC,CAAC,IACzCzC,IAAA,CAAKE,GAAA,CAAIF,IAAA,CAAKuC,GAAA,CAAItC,GAAA,CAAIyC,CAAC,GAAG1C,IAAA,CAAKuC,GAAA,CAAIrC,GAAA,CAAIwC,CAAC,CAAC;IAE7C,OAAO;MAAEzC,GAAA,EAAK8B,WAAA;MAAa7B,GAAA,EAAK8B;IAAa;EAC9C;EAAA;EAAA;EAKDW,mBAAA,EAAqB;IACnB,MAAMnF,QAAA,GAAW,KAAKA,QAAA;IACtB,MAAMoF,QAAA,GAAW,KAAKd,eAAA,CAAiB;IACvC,MAAM7B,GAAA,GAAM2C,QAAA,CAAS3C,GAAA;IACrB,MAAMC,GAAA,GAAM0C,QAAA,CAAS1C,GAAA;IAOrB,IAAIyB,WAAA,GAAc;IAClB,IAAIkB,KAAA,GAAQ;IAEZ,SAAShF,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1B,MAAM0D,QAAA,GAAWrB,GAAA,CAAIrC,CAAC,EAAEe,KAAA,CAAMuD,YAAA,CAAatE,CAAC,IAAIoC,GAAA,CAAIpC,CAAC,EAAEe,KAAA,CAAMuD,YAAA,CAAatE,CAAC;MAE3E,IAAI0D,QAAA,GAAWI,WAAA,EAAa;QAC1BA,WAAA,GAAcJ,QAAA;QACdsB,KAAA,GAAQhF,CAAA;MACT;IACF;IAED,MAAMiF,EAAA,GAAK7C,GAAA,CAAI4C,KAAK;IACpB,MAAME,EAAA,GAAK7C,GAAA,CAAI2C,KAAK;IACpB,IAAIG,EAAA;IACJ,IAAIC,EAAA;IAIJtB,WAAA,GAAc;IACdlF,MAAA,CAAOyG,GAAA,CAAIJ,EAAA,CAAGlE,KAAA,EAAOmE,EAAA,CAAGnE,KAAK;IAE7B,SAASf,CAAA,GAAI,GAAGC,CAAA,GAAI,KAAKN,QAAA,CAASG,MAAA,EAAQE,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MACpD,MAAMyC,MAAA,GAAS9C,QAAA,CAASK,CAAC;MAEzB,IAAIyC,MAAA,KAAWwC,EAAA,IAAMxC,MAAA,KAAWyC,EAAA,EAAI;QAClCtG,MAAA,CAAO0G,mBAAA,CAAoB7C,MAAA,CAAO1B,KAAA,EAAO,MAAM/B,aAAa;QAE5D,MAAM0E,QAAA,GAAW1E,aAAA,CAAcuG,iBAAA,CAAkB9C,MAAA,CAAO1B,KAAK;QAE7D,IAAI2C,QAAA,GAAWI,WAAA,EAAa;UAC1BA,WAAA,GAAcJ,QAAA;UACdyB,EAAA,GAAK1C,MAAA;QACN;MACF;IACF;IAIDqB,WAAA,GAAc;IACdhF,MAAA,CAAO0G,qBAAA,CAAsBP,EAAA,CAAGlE,KAAA,EAAOmE,EAAA,CAAGnE,KAAA,EAAOoE,EAAA,CAAGpE,KAAK;IAEzD,SAASf,CAAA,GAAI,GAAGC,CAAA,GAAI,KAAKN,QAAA,CAASG,MAAA,EAAQE,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MACpD,MAAMyC,MAAA,GAAS9C,QAAA,CAASK,CAAC;MAEzB,IAAIyC,MAAA,KAAWwC,EAAA,IAAMxC,MAAA,KAAWyC,EAAA,IAAMzC,MAAA,KAAW0C,EAAA,EAAI;QACnD,MAAMzB,QAAA,GAAWvB,IAAA,CAAKuC,GAAA,CAAI5F,MAAA,CAAOuC,eAAA,CAAgBoB,MAAA,CAAO1B,KAAK,CAAC;QAE9D,IAAI2C,QAAA,GAAWI,WAAA,EAAa;UAC1BA,WAAA,GAAcJ,QAAA;UACd0B,EAAA,GAAK3C,MAAA;QACN;MACF;IACF;IAED,MAAMnD,KAAA,GAAQ,EAAE;IAEhB,IAAIR,MAAA,CAAOuC,eAAA,CAAgB+D,EAAA,CAAGrE,KAAK,IAAI,GAAG;MAGxCzB,KAAA,CAAMY,IAAA,CAAKuF,IAAA,CAAKC,MAAA,CAAOT,EAAA,EAAIC,EAAA,EAAIC,EAAE,GAAGM,IAAA,CAAKC,MAAA,CAAON,EAAA,EAAIF,EAAA,EAAID,EAAE,GAAGQ,IAAA,CAAKC,MAAA,CAAON,EAAA,EAAID,EAAA,EAAID,EAAE,GAAGO,IAAA,CAAKC,MAAA,CAAON,EAAA,EAAIH,EAAA,EAAIE,EAAE,CAAC;MAI7G,SAASnF,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1B,MAAMqE,CAAA,IAAKrE,CAAA,GAAI,KAAK;QAIpBV,KAAA,CAAMU,CAAA,GAAI,CAAC,EAAE2F,OAAA,CAAQ,CAAC,EAAEC,OAAA,CAAQtG,KAAA,CAAM,CAAC,EAAEqG,OAAA,CAAQtB,CAAC,CAAC;QAInD/E,KAAA,CAAMU,CAAA,GAAI,CAAC,EAAE2F,OAAA,CAAQ,CAAC,EAAEC,OAAA,CAAQtG,KAAA,CAAM+E,CAAA,GAAI,CAAC,EAAEsB,OAAA,CAAQ,CAAC,CAAC;MACxD;IACP,OAAW;MAGLrG,KAAA,CAAMY,IAAA,CAAKuF,IAAA,CAAKC,MAAA,CAAOT,EAAA,EAAIE,EAAA,EAAID,EAAE,GAAGO,IAAA,CAAKC,MAAA,CAAON,EAAA,EAAIH,EAAA,EAAIC,EAAE,GAAGO,IAAA,CAAKC,MAAA,CAAON,EAAA,EAAIF,EAAA,EAAIC,EAAE,GAAGM,IAAA,CAAKC,MAAA,CAAON,EAAA,EAAID,EAAA,EAAIF,EAAE,CAAC;MAI7G,SAASjF,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1B,MAAMqE,CAAA,IAAKrE,CAAA,GAAI,KAAK;QAIpBV,KAAA,CAAMU,CAAA,GAAI,CAAC,EAAE2F,OAAA,CAAQ,CAAC,EAAEC,OAAA,CAAQtG,KAAA,CAAM,CAAC,EAAEqG,OAAA,EAAS,IAAI3F,CAAA,IAAK,CAAC,CAAC;QAI7DV,KAAA,CAAMU,CAAA,GAAI,CAAC,EAAE2F,OAAA,CAAQ,CAAC,EAAEC,OAAA,CAAQtG,KAAA,CAAM+E,CAAA,GAAI,CAAC,EAAEsB,OAAA,CAAQ,CAAC,CAAC;MACxD;IACF;IAID,SAAS3F,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1B,KAAKV,KAAA,CAAMY,IAAA,CAAKZ,KAAA,CAAMU,CAAC,CAAC;IACzB;IAID,SAASA,CAAA,GAAI,GAAGC,CAAA,GAAIN,QAAA,CAASG,MAAA,EAAQE,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC/C,MAAMyC,MAAA,GAAS9C,QAAA,CAASK,CAAC;MAEzB,IAAIyC,MAAA,KAAWwC,EAAA,IAAMxC,MAAA,KAAWyC,EAAA,IAAMzC,MAAA,KAAW0C,EAAA,IAAM1C,MAAA,KAAW2C,EAAA,EAAI;QACpEtB,WAAA,GAAc,KAAKzE,SAAA;QACnB,IAAI0E,OAAA,GAAU;QAEd,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1B,MAAMX,QAAA,GAAW,KAAKpE,KAAA,CAAM+E,CAAC,EAAEhD,eAAA,CAAgBoB,MAAA,CAAO1B,KAAK;UAE3D,IAAI2C,QAAA,GAAWI,WAAA,EAAa;YAC1BA,WAAA,GAAcJ,QAAA;YACdK,OAAA,GAAU,KAAKzE,KAAA,CAAM+E,CAAC;UACvB;QACF;QAED,IAAIN,OAAA,KAAY,MAAM;UACpB,KAAKvB,eAAA,CAAgBC,MAAA,EAAQsB,OAAO;QACrC;MACF;IACF;IAED,OAAO;EACR;EAAA;EAID8B,aAAA,EAAe;IACb,MAAMC,WAAA,GAAc,EAAE;IAEtB,SAAS9F,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKV,KAAA,CAAMQ,MAAA,EAAQE,CAAA,IAAK;MAC1C,MAAMoB,IAAA,GAAO,KAAK9B,KAAA,CAAMU,CAAC;MAEzB,IAAIoB,IAAA,CAAK4C,IAAA,KAASxF,OAAA,EAAS;QACzBsH,WAAA,CAAY5F,IAAA,CAAKkB,IAAI;MACtB;IACF;IAED,KAAK9B,KAAA,GAAQwG,WAAA;IAEb,OAAO;EACR;EAAA;EAIDC,gBAAA,EAAkB;IAGhB,IAAI,KAAKvG,QAAA,CAASoE,OAAA,CAAO,MAAO,OAAO;MACrC,IAAIoC,SAAA;QACFlC,WAAA,GAAc;MAIhB,MAAMmC,OAAA,GAAU,KAAKzG,QAAA,CAASqE,KAAA,CAAO,EAACzC,IAAA;MACtC,IAAIqB,MAAA,GAASwD,OAAA,CAAQvD,OAAA;MAIrB,GAAG;QACD,MAAMgB,QAAA,GAAWuC,OAAA,CAAQ5E,eAAA,CAAgBoB,MAAA,CAAO1B,KAAK;QAErD,IAAI2C,QAAA,GAAWI,WAAA,EAAa;UAC1BA,WAAA,GAAcJ,QAAA;UACdsC,SAAA,GAAYvD,MAAA;QACb;QAEDA,MAAA,GAASA,MAAA,CAAOK,IAAA;MACjB,SAAQL,MAAA,KAAW,QAAQA,MAAA,CAAOrB,IAAA,KAAS6E,OAAA;MAE5C,OAAOD,SAAA;IACR;EACF;EAAA;EAAA;EAAA;EAMDE,eAAeC,QAAA,EAAUC,SAAA,EAAWhF,IAAA,EAAMiF,OAAA,EAAS;IAGjD,KAAKhD,kBAAA,CAAmBjC,IAAI;IAE5BA,IAAA,CAAK4C,IAAA,GAAOvF,OAAA;IAEZ,IAAI6H,IAAA;IAEJ,IAAIF,SAAA,KAAc,MAAM;MACtBE,IAAA,GAAOF,SAAA,GAAYhF,IAAA,CAAKuE,OAAA,CAAQ,CAAC;IACvC,OAAW;MAILW,IAAA,GAAOF,SAAA,CAAUtD,IAAA;IAClB;IAED,GAAG;MACD,MAAMyD,QAAA,GAAWD,IAAA,CAAKE,IAAA;MACtB,MAAMC,YAAA,GAAeF,QAAA,CAASnF,IAAA;MAE9B,IAAIqF,YAAA,CAAazC,IAAA,KAASxF,OAAA,EAAS;QACjC,IAAIiI,YAAA,CAAapF,eAAA,CAAgB8E,QAAQ,IAAI,KAAK9G,SAAA,EAAW;UAG3D,KAAK6G,cAAA,CAAeC,QAAA,EAAUI,QAAA,EAAUE,YAAA,EAAcJ,OAAO;QACvE,OAAe;UAGLA,OAAA,CAAQnG,IAAA,CAAKoG,IAAI;QAClB;MACF;MAEDA,IAAA,GAAOA,IAAA,CAAKxD,IAAA;IAClB,SAAawD,IAAA,KAASF,SAAA;IAElB,OAAO;EACR;EAAA;EAIDM,iBAAiBV,SAAA,EAAWW,WAAA,EAAa;IAGvC,MAAMvF,IAAA,GAAOqE,IAAA,CAAKC,MAAA,CAAOM,SAAA,EAAWW,WAAA,CAAYC,IAAA,CAAM,GAAED,WAAA,CAAYE,IAAA,EAAM;IAE1E,KAAKvH,KAAA,CAAMY,IAAA,CAAKkB,IAAI;IAIpBA,IAAA,CAAKuE,OAAA,CAAQ,EAAE,EAAEC,OAAA,CAAQe,WAAA,CAAYH,IAAI;IAEzC,OAAOpF,IAAA,CAAKuE,OAAA,CAAQ,CAAC;EACtB;EAAA;EAAA;EAKDmB,YAAYd,SAAA,EAAWK,OAAA,EAAS;IAC9B,KAAK9G,QAAA,GAAW,EAAE;IAElB,IAAIwH,aAAA,GAAgB;IACpB,IAAIC,gBAAA,GAAmB;IAEvB,SAAShH,CAAA,GAAI,GAAGA,CAAA,GAAIqG,OAAA,CAAQvG,MAAA,EAAQE,CAAA,IAAK;MACvC,MAAM2G,WAAA,GAAcN,OAAA,CAAQrG,CAAC;MAI7B,MAAMiH,QAAA,GAAW,KAAKP,gBAAA,CAAiBV,SAAA,EAAWW,WAAW;MAE7D,IAAII,aAAA,KAAkB,MAAM;QAC1BA,aAAA,GAAgBE,QAAA;MACxB,OAAa;QAGLA,QAAA,CAASnE,IAAA,CAAK8C,OAAA,CAAQoB,gBAAgB;MACvC;MAED,KAAKzH,QAAA,CAASW,IAAA,CAAK+G,QAAA,CAAS7F,IAAI;MAChC4F,gBAAA,GAAmBC,QAAA;IACpB;IAIDF,aAAA,CAAcjE,IAAA,CAAK8C,OAAA,CAAQoB,gBAAgB;IAE3C,OAAO;EACR;EAAA;EAIDE,gBAAgBlB,SAAA,EAAW;IACzB,MAAMK,OAAA,GAAU,EAAE;IAElB,KAAK3G,UAAA,CAAWyH,KAAA,CAAO;IAIvB,KAAKtE,oBAAA,CAAqBmD,SAAA,EAAWA,SAAA,CAAU5E,IAAI;IAEnD,KAAK8E,cAAA,CAAeF,SAAA,CAAUjF,KAAA,EAAO,MAAMiF,SAAA,CAAU5E,IAAA,EAAMiF,OAAO;IAElE,KAAKS,WAAA,CAAYd,SAAA,EAAWK,OAAO;IAInC,KAAK1C,uBAAA,CAAwB,KAAKpE,QAAQ;IAE1C,OAAO;EACR;EAED6H,QAAA,EAAU;IACR,KAAK5H,QAAA,CAAS2H,KAAA,CAAO;IACrB,KAAKzH,UAAA,CAAWyH,KAAA,CAAO;IACvB,KAAK5H,QAAA,GAAW,EAAE;IAElB,OAAO;EACR;EAEDa,QAAA,EAAU;IACR,IAAIqC,MAAA;IAEJ,KAAKqC,kBAAA,CAAoB;IAIzB,QAAQrC,MAAA,GAAS,KAAKsD,eAAA,CAAe,OAAQ,QAAW;MACtD,KAAKmB,eAAA,CAAgBzE,MAAM;IAC5B;IAED,KAAKoD,YAAA,CAAc;IAEnB,KAAKuB,OAAA,CAAS;IAEd,OAAO;EACR;AACH;AAIK,MAAC3B,IAAA,GAAwB,sBAAM;EAClC,MAAM4B,KAAA,CAAK;IACTjI,YAAA,EAAc;MACZ,KAAK2C,MAAA,GAAS,IAAIpD,OAAA,CAAS;MAC3B,KAAK2I,QAAA,GAAW,IAAI3I,OAAA,CAAS;MAC7B,KAAK4I,IAAA,GAAO;MAEZ,KAAKC,QAAA,GAAW;MAChB,KAAK9E,OAAA,GAAU;MACf,KAAKsB,IAAA,GAAOxF,OAAA;MACZ,KAAK8H,IAAA,GAAO;IACb;IAED,OAAOZ,OAAO+B,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MACrB,MAAMvG,IAAA,GAAO,IAAIiG,KAAA,CAAM;MAEvB,MAAMO,EAAA,GAAK,IAAIC,QAAA,CAASJ,CAAA,EAAGrG,IAAI;MAC/B,MAAM0G,EAAA,GAAK,IAAID,QAAA,CAASH,CAAA,EAAGtG,IAAI;MAC/B,MAAM2G,EAAA,GAAK,IAAIF,QAAA,CAASF,CAAA,EAAGvG,IAAI;MAI/BwG,EAAA,CAAG9E,IAAA,GAAOiF,EAAA,CAAG3E,IAAA,GAAO0E,EAAA;MACpBA,EAAA,CAAGhF,IAAA,GAAO8E,EAAA,CAAGxE,IAAA,GAAO2E,EAAA;MACpBA,EAAA,CAAGjF,IAAA,GAAOgF,EAAA,CAAG1E,IAAA,GAAOwE,EAAA;MAIpBxG,IAAA,CAAKkF,IAAA,GAAOsB,EAAA;MAEZ,OAAOxG,IAAA,CAAKhB,OAAA,CAAS;IACtB;IAEDuF,QAAQ3F,CAAA,EAAG;MACT,IAAIsG,IAAA,GAAO,KAAKA,IAAA;MAEhB,OAAOtG,CAAA,GAAI,GAAG;QACZsG,IAAA,GAAOA,IAAA,CAAKxD,IAAA;QACZ9C,CAAA;MACD;MAED,OAAOA,CAAA,GAAI,GAAG;QACZsG,IAAA,GAAOA,IAAA,CAAKlD,IAAA;QACZpD,CAAA;MACD;MAED,OAAOsG,IAAA;IACR;IAEDlG,QAAA,EAAU;MACR,MAAMqH,CAAA,GAAI,KAAKnB,IAAA,CAAKM,IAAA,CAAM;MAC1B,MAAMc,CAAA,GAAI,KAAKpB,IAAA,CAAKO,IAAA,CAAM;MAC1B,MAAMc,CAAA,GAAI,KAAKrB,IAAA,CAAKxD,IAAA,CAAK+D,IAAA,CAAM;MAE/B5H,SAAA,CAAUoG,GAAA,CAAIoC,CAAA,CAAE1G,KAAA,EAAO2G,CAAA,CAAE3G,KAAA,EAAO4G,CAAA,CAAE5G,KAAK;MAEvC9B,SAAA,CAAU+I,SAAA,CAAU,KAAKjG,MAAM;MAC/B9C,SAAA,CAAUgJ,WAAA,CAAY,KAAKX,QAAQ;MACnC,KAAKC,IAAA,GAAOtI,SAAA,CAAUiJ,OAAA,CAAS;MAE/B,KAAKV,QAAA,GAAW,KAAKzF,MAAA,CAAOC,GAAA,CAAI,KAAKsF,QAAQ;MAE7C,OAAO;IACR;IAEDjG,gBAAgBN,KAAA,EAAO;MACrB,OAAO,KAAKgB,MAAA,CAAOC,GAAA,CAAIjB,KAAK,IAAI,KAAKyG,QAAA;IACtC;EACF;EAED,OAAOH,KAAA;AACT,GAAI;AAIJ,MAAMQ,QAAA,CAAS;EACbzI,YAAYqD,MAAA,EAAQrB,IAAA,EAAM;IACxB,KAAKqB,MAAA,GAASA,MAAA;IACd,KAAKW,IAAA,GAAO;IACZ,KAAKN,IAAA,GAAO;IACZ,KAAK0D,IAAA,GAAO;IACZ,KAAKpF,IAAA,GAAOA,IAAA;EACb;EAEDyF,KAAA,EAAO;IACL,OAAO,KAAKpE,MAAA;EACb;EAEDmE,KAAA,EAAO;IACL,OAAO,KAAKxD,IAAA,GAAO,KAAKA,IAAA,CAAKX,MAAA,GAAS;EACvC;EAED3C,OAAA,EAAS;IACP,MAAM+G,IAAA,GAAO,KAAKA,IAAA,CAAM;IACxB,MAAMD,IAAA,GAAO,KAAKA,IAAA,CAAM;IAExB,IAAIA,IAAA,KAAS,MAAM;MACjB,OAAOA,IAAA,CAAK7F,KAAA,CAAMoH,UAAA,CAAWtB,IAAA,CAAK9F,KAAK;IACxC;IAED,OAAO;EACR;EAEDqH,cAAA,EAAgB;IACd,MAAMvB,IAAA,GAAO,KAAKA,IAAA,CAAM;IACxB,MAAMD,IAAA,GAAO,KAAKA,IAAA,CAAM;IAExB,IAAIA,IAAA,KAAS,MAAM;MACjB,OAAOA,IAAA,CAAK7F,KAAA,CAAMwE,iBAAA,CAAkBsB,IAAA,CAAK9F,KAAK;IAC/C;IAED,OAAO;EACR;EAED6E,QAAQU,IAAA,EAAM;IACZ,KAAKE,IAAA,GAAOF,IAAA;IACZA,IAAA,CAAKE,IAAA,GAAO;IAEZ,OAAO;EACR;AACH;AAIA,MAAMrG,UAAA,CAAW;EACff,YAAY2B,KAAA,EAAO;IACjB,KAAKA,KAAA,GAAQA,KAAA;IACb,KAAKqC,IAAA,GAAO;IACZ,KAAKN,IAAA,GAAO;IACZ,KAAK1B,IAAA,GAAO;EACb;AACH;AAIA,MAAM3B,UAAA,CAAW;EACfL,YAAA,EAAc;IACZ,KAAKyH,IAAA,GAAO;IACZ,KAAKD,IAAA,GAAO;EACb;EAED/C,MAAA,EAAQ;IACN,OAAO,KAAKgD,IAAA;EACb;EAEDwB,KAAA,EAAO;IACL,OAAO,KAAKzB,IAAA;EACb;EAEDO,MAAA,EAAQ;IACN,KAAKN,IAAA,GAAO,KAAKD,IAAA,GAAO;IAExB,OAAO;EACR;EAAA;EAIDhE,aAAapB,MAAA,EAAQiB,MAAA,EAAQ;IAC3BA,MAAA,CAAOW,IAAA,GAAO5B,MAAA,CAAO4B,IAAA;IACrBX,MAAA,CAAOK,IAAA,GAAOtB,MAAA;IAEd,IAAIiB,MAAA,CAAOW,IAAA,KAAS,MAAM;MACxB,KAAKyD,IAAA,GAAOpE,MAAA;IAClB,OAAW;MACLA,MAAA,CAAOW,IAAA,CAAKN,IAAA,GAAOL,MAAA;IACpB;IAEDjB,MAAA,CAAO4B,IAAA,GAAOX,MAAA;IAEd,OAAO;EACR;EAAA;EAID6F,YAAY9G,MAAA,EAAQiB,MAAA,EAAQ;IAC1BA,MAAA,CAAOW,IAAA,GAAO5B,MAAA;IACdiB,MAAA,CAAOK,IAAA,GAAOtB,MAAA,CAAOsB,IAAA;IAErB,IAAIL,MAAA,CAAOK,IAAA,KAAS,MAAM;MACxB,KAAK8D,IAAA,GAAOnE,MAAA;IAClB,OAAW;MACLA,MAAA,CAAOK,IAAA,CAAKM,IAAA,GAAOX,MAAA;IACpB;IAEDjB,MAAA,CAAOsB,IAAA,GAAOL,MAAA;IAEd,OAAO;EACR;EAAA;EAIDE,OAAOF,MAAA,EAAQ;IACb,IAAI,KAAKoE,IAAA,KAAS,MAAM;MACtB,KAAKA,IAAA,GAAOpE,MAAA;IAClB,OAAW;MACL,KAAKmE,IAAA,CAAK9D,IAAA,GAAOL,MAAA;IAClB;IAEDA,MAAA,CAAOW,IAAA,GAAO,KAAKwD,IAAA;IACnBnE,MAAA,CAAOK,IAAA,GAAO;IAEd,KAAK8D,IAAA,GAAOnE,MAAA;IAEZ,OAAO;EACR;EAAA;EAIDe,YAAYf,MAAA,EAAQ;IAClB,IAAI,KAAKoE,IAAA,KAAS,MAAM;MACtB,KAAKA,IAAA,GAAOpE,MAAA;IAClB,OAAW;MACL,KAAKmE,IAAA,CAAK9D,IAAA,GAAOL,MAAA;IAClB;IAEDA,MAAA,CAAOW,IAAA,GAAO,KAAKwD,IAAA;IAInB,OAAOnE,MAAA,CAAOK,IAAA,KAAS,MAAM;MAC3BL,MAAA,GAASA,MAAA,CAAOK,IAAA;IACjB;IAED,KAAK8D,IAAA,GAAOnE,MAAA;IAEZ,OAAO;EACR;EAAA;EAIDM,OAAON,MAAA,EAAQ;IACb,IAAIA,MAAA,CAAOW,IAAA,KAAS,MAAM;MACxB,KAAKyD,IAAA,GAAOpE,MAAA,CAAOK,IAAA;IACzB,OAAW;MACLL,MAAA,CAAOW,IAAA,CAAKN,IAAA,GAAOL,MAAA,CAAOK,IAAA;IAC3B;IAED,IAAIL,MAAA,CAAOK,IAAA,KAAS,MAAM;MACxB,KAAK8D,IAAA,GAAOnE,MAAA,CAAOW,IAAA;IACzB,OAAW;MACLX,MAAA,CAAOK,IAAA,CAAKM,IAAA,GAAOX,MAAA,CAAOW,IAAA;IAC3B;IAED,OAAO;EACR;EAAA;EAIDD,cAAcsE,CAAA,EAAGC,CAAA,EAAG;IAClB,IAAID,CAAA,CAAErE,IAAA,KAAS,MAAM;MACnB,KAAKyD,IAAA,GAAOa,CAAA,CAAE5E,IAAA;IACpB,OAAW;MACL2E,CAAA,CAAErE,IAAA,CAAKN,IAAA,GAAO4E,CAAA,CAAE5E,IAAA;IACjB;IAED,IAAI4E,CAAA,CAAE5E,IAAA,KAAS,MAAM;MACnB,KAAK8D,IAAA,GAAOa,CAAA,CAAErE,IAAA;IACpB,OAAW;MACLsE,CAAA,CAAE5E,IAAA,CAAKM,IAAA,GAAOqE,CAAA,CAAErE,IAAA;IACjB;IAED,OAAO;EACR;EAEDQ,QAAA,EAAU;IACR,OAAO,KAAKiD,IAAA,KAAS;EACtB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}