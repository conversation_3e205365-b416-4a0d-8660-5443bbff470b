{"ast": null, "code": "import { MD2CharacterComplex } from \"./misc/MD2CharacterComplex.js\";\nimport { ConvexObjectBreaker } from \"./misc/ConvexObjectBreaker.js\";\nimport { MorphBlendMesh } from \"./misc/MorphBlendMesh.js\";\nimport { GPUComputationRenderer } from \"./misc/GPUComputationRenderer.js\";\nimport { Gyroscope } from \"./misc/Gyroscope.js\";\nimport { MorphAnimMesh } from \"./misc/MorphAnimMesh.js\";\nimport { RollerCoasterGeometry, RollerCoasterLiftersGeometry, RollerCoasterShadowGeometry, SkyGeometry, TreesGeometry } from \"./misc/RollerCoaster.js\";\nimport { Timer } from \"./misc/Timer.js\";\nimport { getErrorMessage, getWebGL2ErrorMessage, getWebGLErrorMessage, isWebGL2Available, isWebGLAvailable } from \"./misc/WebGL.js\";\nimport { MD2Character } from \"./misc/MD2Character.js\";\nimport { VolumeSlice } from \"./misc/VolumeSlice.js\";\nimport { TubePainter } from \"./misc/TubePainter.js\";\nimport { Volume } from \"./misc/Volume.js\";\nimport { ProgressiveLightMap } from \"./misc/ProgressiveLightmap.js\";\nimport { CSS2DObject, CSS2DRenderer } from \"./renderers/CSS2DRenderer.js\";\nimport { CSS3DObject, CSS3DRenderer, CSS3DSprite } from \"./renderers/CSS3DRenderer.js\";\nimport { Projector, RenderableFace, RenderableLine, RenderableObject, RenderableSprite, RenderableVertex } from \"./renderers/Projector.js\";\nimport { SVGObject, SVGRenderer } from \"./renderers/SVGRenderer.js\";\nimport { FlakesTexture } from \"./textures/FlakesTexture.js\";\nimport { Flow, InstancedFlow, getUniforms, initSplineTexture, modifyShader, updateSplineTexture } from \"./modifiers/CurveModifier.js\";\nimport { SimplifyModifier } from \"./modifiers/SimplifyModifier.js\";\nimport { EdgeSplitModifier } from \"./modifiers/EdgeSplitModifier.js\";\nimport { TessellateModifier } from \"./modifiers/TessellateModifier.js\";\nimport { GLTFExporter } from \"./exporters/GLTFExporter.js\";\nimport { USDZExporter } from \"./exporters/USDZExporter.js\";\nimport { PLYExporter } from \"./exporters/PLYExporter.js\";\nimport { DRACOExporter } from \"./exporters/DRACOExporter.js\";\nimport { ColladaExporter } from \"./exporters/ColladaExporter.js\";\nimport { MMDExporter } from \"./exporters/MMDExporter.js\";\nimport { STLExporter } from \"./exporters/STLExporter.js\";\nimport { OBJExporter } from \"./exporters/OBJExporter.js\";\nimport { RoomEnvironment } from \"./environments/RoomEnvironment.js\";\nimport { AnimationClipCreator } from \"./animation/AnimationClipCreator.js\";\nimport { CCDIKHelper, CCDIKSolver } from \"./animation/CCDIKSolver.js\";\nimport { MMDPhysics } from \"./animation/MMDPhysics.js\";\nimport { MMDAnimationHelper } from \"./animation/MMDAnimationHelper.js\";\nimport { BatchedMesh } from \"./objects/BatchedMesh.js\";\nimport { Reflector } from \"./objects/Reflector.js\";\nimport { Refractor } from \"./objects/Refractor.js\";\nimport { ShadowMesh } from \"./objects/ShadowMesh.js\";\nimport { Lensflare, LensflareElement } from \"./objects/Lensflare.js\";\nimport { Water } from \"./objects/Water.js\";\nimport { MarchingCubes, edgeTable, triTable } from \"./objects/MarchingCubes.js\";\nimport { LightningStorm } from \"./objects/LightningStorm.js\";\nimport { ReflectorRTT } from \"./objects/ReflectorRTT.js\";\nimport { ReflectorForSSRPass } from \"./objects/ReflectorForSSRPass.js\";\nimport { Sky } from \"./objects/Sky.js\";\nimport { Water2 } from \"./objects/Water2.js\";\nimport { GroundProjectedEnv } from \"./objects/GroundProjectedEnv.js\";\nimport { SceneUtils } from \"./utils/SceneUtils.js\";\nimport { UVsDebug } from \"./utils/UVsDebug.js\";\nimport { GeometryUtils } from \"./utils/GeometryUtils.js\";\nimport { RoughnessMipmapper } from \"./utils/RoughnessMipmapper.js\";\nimport { SkeletonUtils } from \"./utils/SkeletonUtils.js\";\nimport { ShadowMapViewer } from \"./utils/ShadowMapViewer.js\";\nimport { computeMorphedAttributes, estimateBytesUsed, interleaveAttributes, mergeBufferAttributes, mergeBufferGeometries, mergeVertices, toCreasedNormals, toTrianglesDrawMode } from \"./utils/BufferGeometryUtils.js\";\nimport { GeometryCompressionUtils, PackedPhongMaterial } from \"./utils/GeometryCompressionUtils.js\";\nimport { CinematicCamera } from \"./cameras/CinematicCamera.js\";\nimport { ConvexHull, Face, HalfEdge, VertexList, VertexNode } from \"./math/ConvexHull.js\";\nimport { MeshSurfaceSampler } from \"./math/MeshSurfaceSampler.js\";\nimport { SimplexNoise } from \"./math/SimplexNoise.js\";\nimport { OBB } from \"./math/OBB.js\";\nimport { Capsule } from \"./math/Capsule.js\";\nimport { ColorConverter } from \"./math/ColorConverter.js\";\nimport { ImprovedNoise } from \"./math/ImprovedNoise.js\";\nimport { Octree } from \"./math/Octree.js\";\nimport { ColorMapKeywords, Lut } from \"./math/Lut.js\";\nimport { CameraControls, MapControlsExp, OrbitControlsExp, STATE, TrackballControlsExp } from \"./controls/experimental/CameraControls.js\";\nimport { FirstPersonControls } from \"./controls/FirstPersonControls.js\";\nimport { TransformControls, TransformControlsGizmo, TransformControlsPlane } from \"./controls/TransformControls.js\";\nimport { DragControls } from \"./controls/DragControls.js\";\nimport { PointerLockControls } from \"./controls/PointerLockControls.js\";\nimport { DeviceOrientationControls } from \"./controls/DeviceOrientationControls.js\";\nimport { TrackballControls } from \"./controls/TrackballControls.js\";\nimport { MapControls, OrbitControls } from \"./controls/OrbitControls.js\";\nimport { ArcballControls } from \"./controls/ArcballControls.js\";\nimport { FlyControls } from \"./controls/FlyControls.js\";\nimport { LUTPass } from \"./postprocessing/LUTPass.js\";\nimport { ClearPass } from \"./postprocessing/ClearPass.js\";\nimport { GlitchPass } from \"./postprocessing/GlitchPass.js\";\nimport { HalftonePass } from \"./postprocessing/HalftonePass.js\";\nimport { SMAAPass } from \"./postprocessing/SMAAPass.js\";\nimport { FilmPass } from \"./postprocessing/FilmPass.js\";\nimport { OutlinePass } from \"./postprocessing/OutlinePass.js\";\nimport { SSAOPass } from \"./postprocessing/SSAOPass.js\";\nimport { SavePass } from \"./postprocessing/SavePass.js\";\nimport { BokehPass } from \"./postprocessing/BokehPass.js\";\nimport { FullScreenQuad, Pass } from \"./postprocessing/Pass.js\";\nimport { TexturePass } from \"./postprocessing/TexturePass.js\";\nimport { AdaptiveToneMappingPass } from \"./postprocessing/AdaptiveToneMappingPass.js\";\nimport { UnrealBloomPass } from \"./postprocessing/UnrealBloomPass.js\";\nimport { CubeTexturePass } from \"./postprocessing/CubeTexturePass.js\";\nimport { SAOPass } from \"./postprocessing/SAOPass.js\";\nimport { AfterimagePass } from \"./postprocessing/AfterimagePass.js\";\nimport { ClearMaskPass, MaskPass } from \"./postprocessing/MaskPass.js\";\nimport { EffectComposer } from \"./postprocessing/EffectComposer.js\";\nimport { DotScreenPass } from \"./postprocessing/DotScreenPass.js\";\nimport { SSRPass } from \"./postprocessing/SSRPass.js\";\nimport { TAARenderPass } from \"./postprocessing/TAARenderPass.js\";\nimport { ShaderPass } from \"./postprocessing/ShaderPass.js\";\nimport { SSAARenderPass } from \"./postprocessing/SSAARenderPass.js\";\nimport { RenderPass } from \"./postprocessing/RenderPass.js\";\nimport { RenderPixelatedPass } from \"./postprocessing/RenderPixelatedPass.js\";\nimport { BloomPass } from \"./postprocessing/BloomPass.js\";\nimport { WaterPass } from \"./postprocessing/WaterPass.js\";\nimport { ARButton } from \"./webxr/ARButton.js\";\nimport { OculusHandModel } from \"./webxr/OculusHandModel.js\";\nimport { OculusHandPointerModel } from \"./webxr/OculusHandPointerModel.js\";\nimport { createText } from \"./webxr/Text2D.js\";\nimport { VRButton } from \"./webxr/VRButton.js\";\nimport { XRControllerModelFactory } from \"./webxr/XRControllerModelFactory.js\";\nimport { XREstimatedLight } from \"./webxr/XREstimatedLight.js\";\nimport { XRHandMeshModel } from \"./webxr/XRHandMeshModel.js\";\nimport { XRHandModelFactory } from \"./webxr/XRHandModelFactory.js\";\nimport { XRHandPrimitiveModel } from \"./webxr/XRHandPrimitiveModel.js\";\nimport { ParametricGeometries } from \"./geometries/ParametricGeometries.js\";\nimport { ParametricGeometry } from \"./geometries/ParametricGeometry.js\";\nimport { ConvexGeometry } from \"./geometries/ConvexGeometry.js\";\nimport { LightningStrike } from \"./geometries/LightningStrike.js\";\nimport { RoundedBoxGeometry } from \"./geometries/RoundedBoxGeometry.js\";\nimport { BoxLineGeometry } from \"./geometries/BoxLineGeometry.js\";\nimport { DecalGeometry, DecalVertex } from \"./geometries/DecalGeometry.js\";\nimport { TeapotGeometry } from \"./geometries/TeapotGeometry.js\";\nimport { TextGeometry, TextGeometry as TextGeometry2 } from \"./geometries/TextGeometry.js\";\nimport { CSM } from \"./csm/CSM.js\";\nimport { CSMFrustum } from \"./csm/CSMFrustum.js\";\nimport { CSMHelper } from \"./csm/CSMHelper.js\";\nimport { CSMShader } from \"./csm/CSMShader.js\";\nimport { ACESFilmicToneMappingShader } from \"./shaders/ACESFilmicToneMappingShader.js\";\nimport { AfterimageShader } from \"./shaders/AfterimageShader.js\";\nimport { BasicShader } from \"./shaders/BasicShader.js\";\nimport { BleachBypassShader } from \"./shaders/BleachBypassShader.js\";\nimport { BlendShader } from \"./shaders/BlendShader.js\";\nimport { BokehShader } from \"./shaders/BokehShader.js\";\nimport { BokehDepthShader, BokehShader2 } from \"./shaders/BokehShader2.js\";\nimport { BrightnessContrastShader } from \"./shaders/BrightnessContrastShader.js\";\nimport { ColorCorrectionShader } from \"./shaders/ColorCorrectionShader.js\";\nimport { ColorifyShader } from \"./shaders/ColorifyShader.js\";\nimport { ConvolutionShader } from \"./shaders/ConvolutionShader.js\";\nimport { CopyShader } from \"./shaders/CopyShader.js\";\nimport { DOFMipMapShader } from \"./shaders/DOFMipMapShader.js\";\nimport { BlurShaderUtils, DepthLimitedBlurShader } from \"./shaders/DepthLimitedBlurShader.js\";\nimport { DigitalGlitch } from \"./shaders/DigitalGlitch.js\";\nimport { DotScreenShader } from \"./shaders/DotScreenShader.js\";\nimport { FXAAShader } from \"./shaders/FXAAShader.js\";\nimport { FilmShader } from \"./shaders/FilmShader.js\";\nimport { FocusShader } from \"./shaders/FocusShader.js\";\nimport { FreiChenShader } from \"./shaders/FreiChenShader.js\";\nimport { FresnelShader } from \"./shaders/FresnelShader.js\";\nimport { GammaCorrectionShader } from \"./shaders/GammaCorrectionShader.js\";\nimport { GodRaysCombineShader, GodRaysDepthMaskShader, GodRaysFakeSunShader, GodRaysGenerateShader } from \"./shaders/GodRaysShader.js\";\nimport { HalftoneShader } from \"./shaders/HalftoneShader.js\";\nimport { HorizontalBlurShader } from \"./shaders/HorizontalBlurShader.js\";\nimport { HorizontalTiltShiftShader } from \"./shaders/HorizontalTiltShiftShader.js\";\nimport { HueSaturationShader } from \"./shaders/HueSaturationShader.js\";\nimport { KaleidoShader } from \"./shaders/KaleidoShader.js\";\nimport { LuminosityHighPassShader } from \"./shaders/LuminosityHighPassShader.js\";\nimport { LuminosityShader } from \"./shaders/LuminosityShader.js\";\nimport { MirrorShader } from \"./shaders/MirrorShader.js\";\nimport { NormalMapShader } from \"./shaders/NormalMapShader.js\";\nimport { ParallaxShader } from \"./shaders/ParallaxShader.js\";\nimport { PixelShader } from \"./shaders/PixelShader.js\";\nimport { RGBShiftShader } from \"./shaders/RGBShiftShader.js\";\nimport { SAOShader } from \"./shaders/SAOShader.js\";\nimport { SMAABlendShader, SMAAEdgesShader, SMAAWeightsShader } from \"./shaders/SMAAShader.js\";\nimport { SSAOBlurShader, SSAODepthShader, SSAOShader } from \"./shaders/SSAOShader.js\";\nimport { SSRBlurShader, SSRDepthShader, SSRShader } from \"./shaders/SSRShader.js\";\nimport { SepiaShader } from \"./shaders/SepiaShader.js\";\nimport { SobelOperatorShader } from \"./shaders/SobelOperatorShader.js\";\nimport { SubsurfaceScatteringShader } from \"./shaders/SubsurfaceScatteringShader.js\";\nimport { TechnicolorShader } from \"./shaders/TechnicolorShader.js\";\nimport { ToneMapShader } from \"./shaders/ToneMapShader.js\";\nimport { ToonShader1, ToonShader2, ToonShaderDotted, ToonShaderHatching } from \"./shaders/ToonShader.js\";\nimport { TriangleBlurShader } from \"./shaders/TriangleBlurShader.js\";\nimport { UnpackDepthRGBAShader } from \"./shaders/UnpackDepthRGBAShader.js\";\nimport { VerticalBlurShader } from \"./shaders/VerticalBlurShader.js\";\nimport { VerticalTiltShiftShader } from \"./shaders/VerticalTiltShiftShader.js\";\nimport { VignetteShader } from \"./shaders/VignetteShader.js\";\nimport { VolumeRenderShader1 } from \"./shaders/VolumeShader.js\";\nimport { WaterRefractionShader } from \"./shaders/WaterRefractionShader.js\";\nimport { HTMLMesh } from \"./interactive/HTMLMesh.js\";\nimport { InteractiveGroup } from \"./interactive/InteractiveGroup.js\";\nimport { SelectionHelper } from \"./interactive/SelectionHelper.js\";\nimport { SelectionBox } from \"./interactive/SelectionBox.js\";\nimport { AmmoPhysics } from \"./physics/AmmoPhysics.js\";\nimport { ParallaxBarrierEffect } from \"./effects/ParallaxBarrierEffect.js\";\nimport { PeppersGhostEffect } from \"./effects/PeppersGhostEffect.js\";\nimport { OutlineEffect } from \"./effects/OutlineEffect.js\";\nimport { AnaglyphEffect } from \"./effects/AnaglyphEffect.js\";\nimport { AsciiEffect } from \"./effects/AsciiEffect.js\";\nimport { StereoEffect } from \"./effects/StereoEffect.js\";\nimport { FBXLoader } from \"./loaders/FBXLoader.js\";\nimport { Font, FontLoader } from \"./loaders/FontLoader.js\";\nimport { TGALoader } from \"./loaders/TGALoader.js\";\nimport { LUTCubeLoader } from \"./loaders/LUTCubeLoader.js\";\nimport { NRRDLoader } from \"./loaders/NRRDLoader.js\";\nimport { STLLoader } from \"./loaders/STLLoader.js\";\nimport { MTLLoader } from \"./loaders/MTLLoader.js\";\nimport { XLoader } from \"./loaders/XLoader.js\";\nimport { BVHLoader } from \"./loaders/BVHLoader.js\";\nimport { KMZLoader } from \"./loaders/KMZLoader.js\";\nimport { VRMLoader } from \"./loaders/VRMLoader.js\";\nimport { VRMLLoader } from \"./loaders/VRMLLoader.js\";\nimport { KTX2Loader } from \"./loaders/KTX2Loader.js\";\nimport { LottieLoader } from \"./loaders/LottieLoader.js\";\nimport { TTFLoader } from \"./loaders/TTFLoader.js\";\nimport { RGBELoader } from \"./loaders/RGBELoader.js\";\nimport { AssimpLoader } from \"./loaders/AssimpLoader.js\";\nimport { ColladaLoader } from \"./loaders/ColladaLoader.js\";\nimport { MDDLoader } from \"./loaders/MDDLoader.js\";\nimport { EXRLoader } from \"./loaders/EXRLoader.js\";\nimport { ThreeMFLoader } from \"./loaders/3MFLoader.js\";\nimport { XYZLoader } from \"./loaders/XYZLoader.js\";\nimport { VTKLoader } from \"./loaders/VTKLoader.js\";\nimport { LUT3dlLoader } from \"./loaders/LUT3dlLoader.js\";\nimport { DDSLoader } from \"./loaders/DDSLoader.js\";\nimport { PVRLoader } from \"./loaders/PVRLoader.js\";\nimport { GCodeLoader } from \"./loaders/GCodeLoader.js\";\nimport { BasisTextureLoader } from \"./loaders/BasisTextureLoader.js\";\nimport { TDSLoader } from \"./loaders/TDSLoader.js\";\nimport { LDrawLoader } from \"./loaders/LDrawLoader.js\";\nimport { GLTFLoader } from \"./loaders/GLTFLoader.js\";\nimport { SVGLoader } from \"./loaders/SVGLoader.js\";\nimport { Rhino3dmLoader } from \"./loaders/3DMLoader.js\";\nimport { OBJLoader } from \"./loaders/OBJLoader.js\";\nimport { AMFLoader } from \"./loaders/AMFLoader.js\";\nimport { MMDLoader } from \"./loaders/MMDLoader.js\";\nimport { MD2Loader } from \"./loaders/MD2Loader.js\";\nimport { KTXLoader } from \"./loaders/KTXLoader.js\";\nimport { TiltLoader } from \"./loaders/TiltLoader.js\";\nimport { DRACOLoader } from \"./loaders/DRACOLoader.js\";\nimport { HDRCubeTextureLoader } from \"./loaders/HDRCubeTextureLoader.js\";\nimport { PDBLoader } from \"./loaders/PDBLoader.js\";\nimport { PRWMLoader } from \"./loaders/PRWMLoader.js\";\nimport { RGBMLoader } from \"./loaders/RGBMLoader.js\";\nimport { VOXData3DTexture, VOXLoader, VOXMesh } from \"./loaders/VOXLoader.js\";\nimport { PCDLoader } from \"./loaders/PCDLoader.js\";\nimport { LWOLoader } from \"./loaders/LWOLoader.js\";\nimport { PLYLoader } from \"./loaders/PLYLoader.js\";\nimport { LineSegmentsGeometry } from \"./lines/LineSegmentsGeometry.js\";\nimport { LineGeometry } from \"./lines/LineGeometry.js\";\nimport { Wireframe } from \"./lines/Wireframe.js\";\nimport { WireframeGeometry2 } from \"./lines/WireframeGeometry2.js\";\nimport { Line2 } from \"./lines/Line2.js\";\nimport { LineMaterial } from \"./lines/LineMaterial.js\";\nimport { LineSegments2 } from \"./lines/LineSegments2.js\";\nimport { LightProbeHelper } from \"./helpers/LightProbeHelper.js\";\nimport { RaycasterHelper } from \"./helpers/RaycasterHelper.js\";\nimport { VertexTangentsHelper } from \"./helpers/VertexTangentsHelper.js\";\nimport { PositionalAudioHelper } from \"./helpers/PositionalAudioHelper.js\";\nimport { VertexNormalsHelper } from \"./helpers/VertexNormalsHelper.js\";\nimport { RectAreaLightHelper } from \"./helpers/RectAreaLightHelper.js\";\nimport { RectAreaLightUniformsLib } from \"./lights/RectAreaLightUniformsLib.js\";\nimport { LightProbeGenerator } from \"./lights/LightProbeGenerator.js\";\nimport { calcBSplineDerivatives, calcBSplinePoint, calcBasisFunctionDerivatives, calcBasisFunctions, calcKoverI, calcNURBSDerivatives, calcRationalCurveDerivatives, calcSurfacePoint, findSpan } from \"./curves/NURBSUtils.js\";\nimport { NURBSCurve } from \"./curves/NURBSCurve.js\";\nimport { NURBSSurface } from \"./curves/NURBSSurface.js\";\nimport { CinquefoilKnot, DecoratedTorusKnot4a, DecoratedTorusKnot4b, DecoratedTorusKnot5a, DecoratedTorusKnot5c, FigureEightPolynomialKnot, GrannyKnot, HeartCurve, HelixCurve, KnotCurve, TorusKnot, TrefoilKnot, TrefoilPolynomialKnot, VivianiCurve } from \"./curves/CurveExtras.js\";\nimport { Face3, Geometry } from \"./deprecated/Geometry.js\";\nimport { MeshoptDecoder } from \"./libs/MeshoptDecoder.js\";\nimport { MotionController, MotionControllerConstants, fetchProfile, fetchProfilesList } from \"./libs/MotionControllers.js\";\nexport { ACESFilmicToneMappingShader, AMFLoader, ARButton, AdaptiveToneMappingPass, AfterimagePass, AfterimageShader, AmmoPhysics, AnaglyphEffect, AnimationClipCreator, ArcballControls, AsciiEffect, AssimpLoader, BVHLoader, BasicShader, BasisTextureLoader, BatchedMesh, BleachBypassShader, BlendShader, BloomPass, BlurShaderUtils, BokehDepthShader, BokehPass, BokehShader, BokehShader2, BoxLineGeometry, BrightnessContrastShader, CCDIKHelper, CCDIKSolver, CSM, CSMFrustum, CSMHelper, CSMShader, CSS2DObject, CSS2DRenderer, CSS3DObject, CSS3DRenderer, CSS3DSprite, CameraControls, Capsule, CinematicCamera, CinquefoilKnot, ClearMaskPass, ClearPass, ColladaExporter, ColladaLoader, ColorConverter, ColorCorrectionShader, ColorMapKeywords, ColorifyShader, ConvexGeometry, ConvexHull, ConvexObjectBreaker, ConvolutionShader, CopyShader, CubeTexturePass, DDSLoader, DOFMipMapShader, DRACOExporter, DRACOLoader, DecalGeometry, DecalVertex, DecoratedTorusKnot4a, DecoratedTorusKnot4b, DecoratedTorusKnot5a, DecoratedTorusKnot5c, DepthLimitedBlurShader, DeviceOrientationControls, DigitalGlitch, DotScreenPass, DotScreenShader, DragControls, EXRLoader, EdgeSplitModifier, EffectComposer, FBXLoader, FXAAShader, Face, Face3, FigureEightPolynomialKnot, FilmPass, FilmShader, FirstPersonControls, FlakesTexture, Flow, FlyControls, FocusShader, Font, FontLoader, FreiChenShader, FresnelShader, FullScreenQuad, GCodeLoader, GLTFExporter, GLTFLoader, GPUComputationRenderer, GammaCorrectionShader, Geometry, GeometryCompressionUtils, GeometryUtils, GlitchPass, GodRaysCombineShader, GodRaysDepthMaskShader, GodRaysFakeSunShader, GodRaysGenerateShader, GrannyKnot, GroundProjectedEnv, Gyroscope, HDRCubeTextureLoader, HTMLMesh, HalfEdge, HalftonePass, HalftoneShader, HeartCurve, HelixCurve, HorizontalBlurShader, HorizontalTiltShiftShader, HueSaturationShader, ImprovedNoise, InstancedFlow, InteractiveGroup, KMZLoader, KTX2Loader, KTXLoader, KaleidoShader, KnotCurve, LDrawLoader, LUT3dlLoader, LUTCubeLoader, LUTPass, LWOLoader, Lensflare, LensflareElement, LightProbeGenerator, LightProbeHelper, LightningStorm, LightningStrike, Line2, LineGeometry, LineMaterial, LineSegments2, LineSegmentsGeometry, LottieLoader, LuminosityHighPassShader, LuminosityShader, Lut, MD2Character, MD2CharacterComplex, MD2Loader, MDDLoader, MMDAnimationHelper, MMDExporter, MMDLoader, MMDPhysics, MTLLoader, MapControls, MapControlsExp, MarchingCubes, MaskPass, MeshSurfaceSampler, MeshoptDecoder, MirrorShader, MorphAnimMesh, MorphBlendMesh, MotionController, MotionControllerConstants, NRRDLoader, NURBSCurve, NURBSSurface, NormalMapShader, OBB, OBJExporter, OBJLoader, Octree, OculusHandModel, OculusHandPointerModel, OrbitControls, OrbitControlsExp, OutlineEffect, OutlinePass, PCDLoader, PDBLoader, PLYExporter, PLYLoader, PRWMLoader, PVRLoader, PackedPhongMaterial, ParallaxBarrierEffect, ParallaxShader, ParametricGeometries, ParametricGeometry, Pass, PeppersGhostEffect, PixelShader, PointerLockControls, PositionalAudioHelper, ProgressiveLightMap, Projector, RGBELoader, RGBMLoader, RGBShiftShader, RaycasterHelper, RectAreaLightHelper, RectAreaLightUniformsLib, Reflector, ReflectorForSSRPass, ReflectorRTT, Refractor, RenderPass, RenderPixelatedPass, RenderableFace, RenderableLine, RenderableObject, RenderableSprite, RenderableVertex, Rhino3dmLoader, RollerCoasterGeometry, RollerCoasterLiftersGeometry, RollerCoasterShadowGeometry, RoomEnvironment, RoughnessMipmapper, RoundedBoxGeometry, SAOPass, SAOShader, SMAABlendShader, SMAAEdgesShader, SMAAPass, SMAAWeightsShader, SSAARenderPass, SSAOBlurShader, SSAODepthShader, SSAOPass, SSAOShader, SSRBlurShader, SSRDepthShader, SSRPass, SSRShader, STATE, STLExporter, STLLoader, SVGLoader, SVGObject, SVGRenderer, SavePass, SceneUtils, SelectionBox, SelectionHelper, SepiaShader, ShaderPass, ShadowMapViewer, ShadowMesh, SimplexNoise, SimplifyModifier, SkeletonUtils, Sky, SkyGeometry, SobelOperatorShader, StereoEffect, SubsurfaceScatteringShader, TAARenderPass, TDSLoader, TGALoader, TTFLoader, TeapotGeometry, TechnicolorShader, TessellateModifier, TextGeometry as TextBufferGeometry, TextGeometry2 as TextGeometry, TexturePass, ThreeMFLoader, TiltLoader, Timer, ToneMapShader, ToonShader1, ToonShader2, ToonShaderDotted, ToonShaderHatching, TorusKnot, TrackballControls, TrackballControlsExp, TransformControls, TransformControlsGizmo, TransformControlsPlane, TreesGeometry, TrefoilKnot, TrefoilPolynomialKnot, TriangleBlurShader, TubePainter, USDZExporter, UVsDebug, UnpackDepthRGBAShader, UnrealBloomPass, VOXData3DTexture, VOXLoader, VOXMesh, VRButton, VRMLLoader, VRMLoader, VTKLoader, VertexList, VertexNode, VertexNormalsHelper, VertexTangentsHelper, VerticalBlurShader, VerticalTiltShiftShader, VignetteShader, VivianiCurve, Volume, VolumeRenderShader1, VolumeSlice, Water, Water2, WaterPass, WaterRefractionShader, Wireframe, WireframeGeometry2, XLoader, XRControllerModelFactory, XREstimatedLight, XRHandMeshModel, XRHandModelFactory, XRHandPrimitiveModel, XYZLoader, calcBSplineDerivatives, calcBSplinePoint, calcBasisFunctionDerivatives, calcBasisFunctions, calcKoverI, calcNURBSDerivatives, calcRationalCurveDerivatives, calcSurfacePoint, computeMorphedAttributes, createText, edgeTable, estimateBytesUsed, fetchProfile, fetchProfilesList, findSpan, getErrorMessage, getUniforms, getWebGL2ErrorMessage, getWebGLErrorMessage, initSplineTexture, interleaveAttributes, isWebGL2Available, isWebGLAvailable, mergeBufferAttributes, mergeBufferGeometries, mergeVertices, modifyShader, toCreasedNormals, toTrianglesDrawMode, triTable, updateSplineTexture };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { MD2CharacterComplex } from \"./misc/MD2CharacterComplex.js\";\nimport { ConvexObjectBreaker } from \"./misc/ConvexObjectBreaker.js\";\nimport { MorphBlendMesh } from \"./misc/MorphBlendMesh.js\";\nimport { GPUComputationRenderer } from \"./misc/GPUComputationRenderer.js\";\nimport { Gyroscope } from \"./misc/Gyroscope.js\";\nimport { MorphAnimMesh } from \"./misc/MorphAnimMesh.js\";\nimport { RollerCoasterGeometry, RollerCoasterLiftersGeometry, RollerCoasterShadowGeometry, SkyGeometry, TreesGeometry } from \"./misc/RollerCoaster.js\";\nimport { Timer } from \"./misc/Timer.js\";\nimport { getErrorMessage, getWebGL2ErrorMessage, getWebGLErrorMessage, isWebGL2Available, isWebGLAvailable } from \"./misc/WebGL.js\";\nimport { MD2Character } from \"./misc/MD2Character.js\";\nimport { VolumeSlice } from \"./misc/VolumeSlice.js\";\nimport { TubePainter } from \"./misc/TubePainter.js\";\nimport { Volume } from \"./misc/Volume.js\";\nimport { ProgressiveLightMap } from \"./misc/ProgressiveLightmap.js\";\nimport { CSS2DObject, CSS2DRenderer } from \"./renderers/CSS2DRenderer.js\";\nimport { CSS3DObject, CSS3DRenderer, CSS3DSprite } from \"./renderers/CSS3DRenderer.js\";\nimport { Projector, RenderableFace, RenderableLine, RenderableObject, RenderableSprite, RenderableVertex } from \"./renderers/Projector.js\";\nimport { SVGObject, SVGRenderer } from \"./renderers/SVGRenderer.js\";\nimport { FlakesTexture } from \"./textures/FlakesTexture.js\";\nimport { Flow, InstancedFlow, getUniforms, initSplineTexture, modifyShader, updateSplineTexture } from \"./modifiers/CurveModifier.js\";\nimport { SimplifyModifier } from \"./modifiers/SimplifyModifier.js\";\nimport { EdgeSplitModifier } from \"./modifiers/EdgeSplitModifier.js\";\nimport { TessellateModifier } from \"./modifiers/TessellateModifier.js\";\nimport { GLTFExporter } from \"./exporters/GLTFExporter.js\";\nimport { USDZExporter } from \"./exporters/USDZExporter.js\";\nimport { PLYExporter } from \"./exporters/PLYExporter.js\";\nimport { DRACOExporter } from \"./exporters/DRACOExporter.js\";\nimport { ColladaExporter } from \"./exporters/ColladaExporter.js\";\nimport { MMDExporter } from \"./exporters/MMDExporter.js\";\nimport { STLExporter } from \"./exporters/STLExporter.js\";\nimport { OBJExporter } from \"./exporters/OBJExporter.js\";\nimport { RoomEnvironment } from \"./environments/RoomEnvironment.js\";\nimport { AnimationClipCreator } from \"./animation/AnimationClipCreator.js\";\nimport { CCDIKHelper, CCDIKSolver } from \"./animation/CCDIKSolver.js\";\nimport { MMDPhysics } from \"./animation/MMDPhysics.js\";\nimport { MMDAnimationHelper } from \"./animation/MMDAnimationHelper.js\";\nimport { BatchedMesh } from \"./objects/BatchedMesh.js\";\nimport { Reflector } from \"./objects/Reflector.js\";\nimport { Refractor } from \"./objects/Refractor.js\";\nimport { ShadowMesh } from \"./objects/ShadowMesh.js\";\nimport { Lensflare, LensflareElement } from \"./objects/Lensflare.js\";\nimport { Water } from \"./objects/Water.js\";\nimport { MarchingCubes, edgeTable, triTable } from \"./objects/MarchingCubes.js\";\nimport { LightningStorm } from \"./objects/LightningStorm.js\";\nimport { ReflectorRTT } from \"./objects/ReflectorRTT.js\";\nimport { ReflectorForSSRPass } from \"./objects/ReflectorForSSRPass.js\";\nimport { Sky } from \"./objects/Sky.js\";\nimport { Water2 } from \"./objects/Water2.js\";\nimport { GroundProjectedEnv } from \"./objects/GroundProjectedEnv.js\";\nimport { SceneUtils } from \"./utils/SceneUtils.js\";\nimport { UVsDebug } from \"./utils/UVsDebug.js\";\nimport { GeometryUtils } from \"./utils/GeometryUtils.js\";\nimport { RoughnessMipmapper } from \"./utils/RoughnessMipmapper.js\";\nimport { SkeletonUtils } from \"./utils/SkeletonUtils.js\";\nimport { ShadowMapViewer } from \"./utils/ShadowMapViewer.js\";\nimport { computeMorphedAttributes, estimateBytesUsed, interleaveAttributes, mergeBufferAttributes, mergeBufferGeometries, mergeVertices, toCreasedNormals, toTrianglesDrawMode } from \"./utils/BufferGeometryUtils.js\";\nimport { GeometryCompressionUtils, PackedPhongMaterial } from \"./utils/GeometryCompressionUtils.js\";\nimport { CinematicCamera } from \"./cameras/CinematicCamera.js\";\nimport { ConvexHull, Face, HalfEdge, VertexList, VertexNode } from \"./math/ConvexHull.js\";\nimport { MeshSurfaceSampler } from \"./math/MeshSurfaceSampler.js\";\nimport { SimplexNoise } from \"./math/SimplexNoise.js\";\nimport { OBB } from \"./math/OBB.js\";\nimport { Capsule } from \"./math/Capsule.js\";\nimport { ColorConverter } from \"./math/ColorConverter.js\";\nimport { ImprovedNoise } from \"./math/ImprovedNoise.js\";\nimport { Octree } from \"./math/Octree.js\";\nimport { ColorMapKeywords, Lut } from \"./math/Lut.js\";\nimport { CameraControls, MapControlsExp, OrbitControlsExp, STATE, TrackballControlsExp } from \"./controls/experimental/CameraControls.js\";\nimport { FirstPersonControls } from \"./controls/FirstPersonControls.js\";\nimport { TransformControls, TransformControlsGizmo, TransformControlsPlane } from \"./controls/TransformControls.js\";\nimport { DragControls } from \"./controls/DragControls.js\";\nimport { PointerLockControls } from \"./controls/PointerLockControls.js\";\nimport { DeviceOrientationControls } from \"./controls/DeviceOrientationControls.js\";\nimport { TrackballControls } from \"./controls/TrackballControls.js\";\nimport { MapControls, OrbitControls } from \"./controls/OrbitControls.js\";\nimport { ArcballControls } from \"./controls/ArcballControls.js\";\nimport { FlyControls } from \"./controls/FlyControls.js\";\nimport { LUTPass } from \"./postprocessing/LUTPass.js\";\nimport { ClearPass } from \"./postprocessing/ClearPass.js\";\nimport { GlitchPass } from \"./postprocessing/GlitchPass.js\";\nimport { HalftonePass } from \"./postprocessing/HalftonePass.js\";\nimport { SMAAPass } from \"./postprocessing/SMAAPass.js\";\nimport { FilmPass } from \"./postprocessing/FilmPass.js\";\nimport { OutlinePass } from \"./postprocessing/OutlinePass.js\";\nimport { SSAOPass } from \"./postprocessing/SSAOPass.js\";\nimport { SavePass } from \"./postprocessing/SavePass.js\";\nimport { BokehPass } from \"./postprocessing/BokehPass.js\";\nimport { FullScreenQuad, Pass } from \"./postprocessing/Pass.js\";\nimport { TexturePass } from \"./postprocessing/TexturePass.js\";\nimport { AdaptiveToneMappingPass } from \"./postprocessing/AdaptiveToneMappingPass.js\";\nimport { UnrealBloomPass } from \"./postprocessing/UnrealBloomPass.js\";\nimport { CubeTexturePass } from \"./postprocessing/CubeTexturePass.js\";\nimport { SAOPass } from \"./postprocessing/SAOPass.js\";\nimport { AfterimagePass } from \"./postprocessing/AfterimagePass.js\";\nimport { ClearMaskPass, MaskPass } from \"./postprocessing/MaskPass.js\";\nimport { EffectComposer } from \"./postprocessing/EffectComposer.js\";\nimport { DotScreenPass } from \"./postprocessing/DotScreenPass.js\";\nimport { SSRPass } from \"./postprocessing/SSRPass.js\";\nimport { TAARenderPass } from \"./postprocessing/TAARenderPass.js\";\nimport { ShaderPass } from \"./postprocessing/ShaderPass.js\";\nimport { SSAARenderPass } from \"./postprocessing/SSAARenderPass.js\";\nimport { RenderPass } from \"./postprocessing/RenderPass.js\";\nimport { RenderPixelatedPass } from \"./postprocessing/RenderPixelatedPass.js\";\nimport { BloomPass } from \"./postprocessing/BloomPass.js\";\nimport { WaterPass } from \"./postprocessing/WaterPass.js\";\nimport { ARButton } from \"./webxr/ARButton.js\";\nimport { OculusHandModel } from \"./webxr/OculusHandModel.js\";\nimport { OculusHandPointerModel } from \"./webxr/OculusHandPointerModel.js\";\nimport { createText } from \"./webxr/Text2D.js\";\nimport { VRButton } from \"./webxr/VRButton.js\";\nimport { XRControllerModelFactory } from \"./webxr/XRControllerModelFactory.js\";\nimport { XREstimatedLight } from \"./webxr/XREstimatedLight.js\";\nimport { XRHandMeshModel } from \"./webxr/XRHandMeshModel.js\";\nimport { XRHandModelFactory } from \"./webxr/XRHandModelFactory.js\";\nimport { XRHandPrimitiveModel } from \"./webxr/XRHandPrimitiveModel.js\";\nimport { ParametricGeometries } from \"./geometries/ParametricGeometries.js\";\nimport { ParametricGeometry } from \"./geometries/ParametricGeometry.js\";\nimport { ConvexGeometry } from \"./geometries/ConvexGeometry.js\";\nimport { LightningStrike } from \"./geometries/LightningStrike.js\";\nimport { RoundedBoxGeometry } from \"./geometries/RoundedBoxGeometry.js\";\nimport { BoxLineGeometry } from \"./geometries/BoxLineGeometry.js\";\nimport { DecalGeometry, DecalVertex } from \"./geometries/DecalGeometry.js\";\nimport { TeapotGeometry } from \"./geometries/TeapotGeometry.js\";\nimport { TextGeometry, TextGeometry as TextGeometry2 } from \"./geometries/TextGeometry.js\";\nimport { CSM } from \"./csm/CSM.js\";\nimport { CSMFrustum } from \"./csm/CSMFrustum.js\";\nimport { CSMHelper } from \"./csm/CSMHelper.js\";\nimport { CSMShader } from \"./csm/CSMShader.js\";\nimport { ACESFilmicToneMappingShader } from \"./shaders/ACESFilmicToneMappingShader.js\";\nimport { AfterimageShader } from \"./shaders/AfterimageShader.js\";\nimport { BasicShader } from \"./shaders/BasicShader.js\";\nimport { BleachBypassShader } from \"./shaders/BleachBypassShader.js\";\nimport { BlendShader } from \"./shaders/BlendShader.js\";\nimport { BokehShader } from \"./shaders/BokehShader.js\";\nimport { BokehDepthShader, BokehShader2 } from \"./shaders/BokehShader2.js\";\nimport { BrightnessContrastShader } from \"./shaders/BrightnessContrastShader.js\";\nimport { ColorCorrectionShader } from \"./shaders/ColorCorrectionShader.js\";\nimport { ColorifyShader } from \"./shaders/ColorifyShader.js\";\nimport { ConvolutionShader } from \"./shaders/ConvolutionShader.js\";\nimport { CopyShader } from \"./shaders/CopyShader.js\";\nimport { DOFMipMapShader } from \"./shaders/DOFMipMapShader.js\";\nimport { BlurShaderUtils, DepthLimitedBlurShader } from \"./shaders/DepthLimitedBlurShader.js\";\nimport { DigitalGlitch } from \"./shaders/DigitalGlitch.js\";\nimport { DotScreenShader } from \"./shaders/DotScreenShader.js\";\nimport { FXAAShader } from \"./shaders/FXAAShader.js\";\nimport { FilmShader } from \"./shaders/FilmShader.js\";\nimport { FocusShader } from \"./shaders/FocusShader.js\";\nimport { FreiChenShader } from \"./shaders/FreiChenShader.js\";\nimport { FresnelShader } from \"./shaders/FresnelShader.js\";\nimport { GammaCorrectionShader } from \"./shaders/GammaCorrectionShader.js\";\nimport { GodRaysCombineShader, GodRaysDepthMaskShader, GodRaysFakeSunShader, GodRaysGenerateShader } from \"./shaders/GodRaysShader.js\";\nimport { HalftoneShader } from \"./shaders/HalftoneShader.js\";\nimport { HorizontalBlurShader } from \"./shaders/HorizontalBlurShader.js\";\nimport { HorizontalTiltShiftShader } from \"./shaders/HorizontalTiltShiftShader.js\";\nimport { HueSaturationShader } from \"./shaders/HueSaturationShader.js\";\nimport { KaleidoShader } from \"./shaders/KaleidoShader.js\";\nimport { LuminosityHighPassShader } from \"./shaders/LuminosityHighPassShader.js\";\nimport { LuminosityShader } from \"./shaders/LuminosityShader.js\";\nimport { MirrorShader } from \"./shaders/MirrorShader.js\";\nimport { NormalMapShader } from \"./shaders/NormalMapShader.js\";\nimport { ParallaxShader } from \"./shaders/ParallaxShader.js\";\nimport { PixelShader } from \"./shaders/PixelShader.js\";\nimport { RGBShiftShader } from \"./shaders/RGBShiftShader.js\";\nimport { SAOShader } from \"./shaders/SAOShader.js\";\nimport { SMAABlendShader, SMAAEdgesShader, SMAAWeightsShader } from \"./shaders/SMAAShader.js\";\nimport { SSAOBlurShader, SSAODepthShader, SSAOShader } from \"./shaders/SSAOShader.js\";\nimport { SSRBlurShader, SSRDepthShader, SSRShader } from \"./shaders/SSRShader.js\";\nimport { SepiaShader } from \"./shaders/SepiaShader.js\";\nimport { SobelOperatorShader } from \"./shaders/SobelOperatorShader.js\";\nimport { SubsurfaceScatteringShader } from \"./shaders/SubsurfaceScatteringShader.js\";\nimport { TechnicolorShader } from \"./shaders/TechnicolorShader.js\";\nimport { ToneMapShader } from \"./shaders/ToneMapShader.js\";\nimport { ToonShader1, ToonShader2, ToonShaderDotted, ToonShaderHatching } from \"./shaders/ToonShader.js\";\nimport { TriangleBlurShader } from \"./shaders/TriangleBlurShader.js\";\nimport { UnpackDepthRGBAShader } from \"./shaders/UnpackDepthRGBAShader.js\";\nimport { VerticalBlurShader } from \"./shaders/VerticalBlurShader.js\";\nimport { VerticalTiltShiftShader } from \"./shaders/VerticalTiltShiftShader.js\";\nimport { VignetteShader } from \"./shaders/VignetteShader.js\";\nimport { VolumeRenderShader1 } from \"./shaders/VolumeShader.js\";\nimport { WaterRefractionShader } from \"./shaders/WaterRefractionShader.js\";\nimport { HTMLMesh } from \"./interactive/HTMLMesh.js\";\nimport { InteractiveGroup } from \"./interactive/InteractiveGroup.js\";\nimport { SelectionHelper } from \"./interactive/SelectionHelper.js\";\nimport { SelectionBox } from \"./interactive/SelectionBox.js\";\nimport { AmmoPhysics } from \"./physics/AmmoPhysics.js\";\nimport { ParallaxBarrierEffect } from \"./effects/ParallaxBarrierEffect.js\";\nimport { PeppersGhostEffect } from \"./effects/PeppersGhostEffect.js\";\nimport { OutlineEffect } from \"./effects/OutlineEffect.js\";\nimport { AnaglyphEffect } from \"./effects/AnaglyphEffect.js\";\nimport { AsciiEffect } from \"./effects/AsciiEffect.js\";\nimport { StereoEffect } from \"./effects/StereoEffect.js\";\nimport { FBXLoader } from \"./loaders/FBXLoader.js\";\nimport { Font, FontLoader } from \"./loaders/FontLoader.js\";\nimport { TGALoader } from \"./loaders/TGALoader.js\";\nimport { LUTCubeLoader } from \"./loaders/LUTCubeLoader.js\";\nimport { NRRDLoader } from \"./loaders/NRRDLoader.js\";\nimport { STLLoader } from \"./loaders/STLLoader.js\";\nimport { MTLLoader } from \"./loaders/MTLLoader.js\";\nimport { XLoader } from \"./loaders/XLoader.js\";\nimport { BVHLoader } from \"./loaders/BVHLoader.js\";\nimport { KMZLoader } from \"./loaders/KMZLoader.js\";\nimport { VRMLoader } from \"./loaders/VRMLoader.js\";\nimport { VRMLLoader } from \"./loaders/VRMLLoader.js\";\nimport { KTX2Loader } from \"./loaders/KTX2Loader.js\";\nimport { LottieLoader } from \"./loaders/LottieLoader.js\";\nimport { TTFLoader } from \"./loaders/TTFLoader.js\";\nimport { RGBELoader } from \"./loaders/RGBELoader.js\";\nimport { AssimpLoader } from \"./loaders/AssimpLoader.js\";\nimport { ColladaLoader } from \"./loaders/ColladaLoader.js\";\nimport { MDDLoader } from \"./loaders/MDDLoader.js\";\nimport { EXRLoader } from \"./loaders/EXRLoader.js\";\nimport { ThreeMFLoader } from \"./loaders/3MFLoader.js\";\nimport { XYZLoader } from \"./loaders/XYZLoader.js\";\nimport { VTKLoader } from \"./loaders/VTKLoader.js\";\nimport { LUT3dlLoader } from \"./loaders/LUT3dlLoader.js\";\nimport { DDSLoader } from \"./loaders/DDSLoader.js\";\nimport { PVRLoader } from \"./loaders/PVRLoader.js\";\nimport { GCodeLoader } from \"./loaders/GCodeLoader.js\";\nimport { BasisTextureLoader } from \"./loaders/BasisTextureLoader.js\";\nimport { TDSLoader } from \"./loaders/TDSLoader.js\";\nimport { LDrawLoader } from \"./loaders/LDrawLoader.js\";\nimport { GLTFLoader } from \"./loaders/GLTFLoader.js\";\nimport { SVGLoader } from \"./loaders/SVGLoader.js\";\nimport { Rhino3dmLoader } from \"./loaders/3DMLoader.js\";\nimport { OBJLoader } from \"./loaders/OBJLoader.js\";\nimport { AMFLoader } from \"./loaders/AMFLoader.js\";\nimport { MMDLoader } from \"./loaders/MMDLoader.js\";\nimport { MD2Loader } from \"./loaders/MD2Loader.js\";\nimport { KTXLoader } from \"./loaders/KTXLoader.js\";\nimport { TiltLoader } from \"./loaders/TiltLoader.js\";\nimport { DRACOLoader } from \"./loaders/DRACOLoader.js\";\nimport { HDRCubeTextureLoader } from \"./loaders/HDRCubeTextureLoader.js\";\nimport { PDBLoader } from \"./loaders/PDBLoader.js\";\nimport { PRWMLoader } from \"./loaders/PRWMLoader.js\";\nimport { RGBMLoader } from \"./loaders/RGBMLoader.js\";\nimport { VOXData3DTexture, VOXLoader, VOXMesh } from \"./loaders/VOXLoader.js\";\nimport { PCDLoader } from \"./loaders/PCDLoader.js\";\nimport { LWOLoader } from \"./loaders/LWOLoader.js\";\nimport { PLYLoader } from \"./loaders/PLYLoader.js\";\nimport { LineSegmentsGeometry } from \"./lines/LineSegmentsGeometry.js\";\nimport { LineGeometry } from \"./lines/LineGeometry.js\";\nimport { Wireframe } from \"./lines/Wireframe.js\";\nimport { WireframeGeometry2 } from \"./lines/WireframeGeometry2.js\";\nimport { Line2 } from \"./lines/Line2.js\";\nimport { LineMaterial } from \"./lines/LineMaterial.js\";\nimport { LineSegments2 } from \"./lines/LineSegments2.js\";\nimport { LightProbeHelper } from \"./helpers/LightProbeHelper.js\";\nimport { RaycasterHelper } from \"./helpers/RaycasterHelper.js\";\nimport { VertexTangentsHelper } from \"./helpers/VertexTangentsHelper.js\";\nimport { PositionalAudioHelper } from \"./helpers/PositionalAudioHelper.js\";\nimport { VertexNormalsHelper } from \"./helpers/VertexNormalsHelper.js\";\nimport { RectAreaLightHelper } from \"./helpers/RectAreaLightHelper.js\";\nimport { RectAreaLightUniformsLib } from \"./lights/RectAreaLightUniformsLib.js\";\nimport { LightProbeGenerator } from \"./lights/LightProbeGenerator.js\";\nimport { calcBSplineDerivatives, calcBSplinePoint, calcBasisFunctionDerivatives, calcBasisFunctions, calcKoverI, calcNURBSDerivatives, calcRationalCurveDerivatives, calcSurfacePoint, findSpan } from \"./curves/NURBSUtils.js\";\nimport { NURBSCurve } from \"./curves/NURBSCurve.js\";\nimport { NURBSSurface } from \"./curves/NURBSSurface.js\";\nimport { CinquefoilKnot, DecoratedTorusKnot4a, DecoratedTorusKnot4b, DecoratedTorusKnot5a, DecoratedTorusKnot5c, FigureEightPolynomialKnot, GrannyKnot, HeartCurve, HelixCurve, KnotCurve, TorusKnot, TrefoilKnot, TrefoilPolynomialKnot, VivianiCurve } from \"./curves/CurveExtras.js\";\nimport { Face3, Geometry } from \"./deprecated/Geometry.js\";\nimport { MeshoptDecoder } from \"./libs/MeshoptDecoder.js\";\nimport { MotionController, MotionControllerConstants, fetchProfile, fetchProfilesList } from \"./libs/MotionControllers.js\";\nexport {\n  ACESFilmicToneMappingShader,\n  AMFLoader,\n  ARButton,\n  AdaptiveToneMappingPass,\n  AfterimagePass,\n  AfterimageShader,\n  AmmoPhysics,\n  AnaglyphEffect,\n  AnimationClipCreator,\n  ArcballControls,\n  AsciiEffect,\n  AssimpLoader,\n  BVHLoader,\n  BasicShader,\n  BasisTextureLoader,\n  BatchedMesh,\n  BleachBypassShader,\n  BlendShader,\n  BloomPass,\n  BlurShaderUtils,\n  BokehDepthShader,\n  BokehPass,\n  BokehShader,\n  BokehShader2,\n  BoxLineGeometry,\n  BrightnessContrastShader,\n  CCDIKHelper,\n  CCDIKSolver,\n  CSM,\n  CSMFrustum,\n  CSMHelper,\n  CSMShader,\n  CSS2DObject,\n  CSS2DRenderer,\n  CSS3DObject,\n  CSS3DRenderer,\n  CSS3DSprite,\n  CameraControls,\n  Capsule,\n  CinematicCamera,\n  CinquefoilKnot,\n  ClearMaskPass,\n  ClearPass,\n  ColladaExporter,\n  ColladaLoader,\n  ColorConverter,\n  ColorCorrectionShader,\n  ColorMapKeywords,\n  ColorifyShader,\n  ConvexGeometry,\n  ConvexHull,\n  ConvexObjectBreaker,\n  ConvolutionShader,\n  CopyShader,\n  CubeTexturePass,\n  DDSLoader,\n  DOFMipMapShader,\n  DRACOExporter,\n  DRACOLoader,\n  DecalGeometry,\n  DecalVertex,\n  DecoratedTorusKnot4a,\n  DecoratedTorusKnot4b,\n  DecoratedTorusKnot5a,\n  DecoratedTorusKnot5c,\n  DepthLimitedBlurShader,\n  DeviceOrientationControls,\n  DigitalGlitch,\n  DotScreenPass,\n  DotScreenShader,\n  DragControls,\n  EXRLoader,\n  EdgeSplitModifier,\n  EffectComposer,\n  FBXLoader,\n  FXAAShader,\n  Face,\n  Face3,\n  FigureEightPolynomialKnot,\n  FilmPass,\n  FilmShader,\n  FirstPersonControls,\n  FlakesTexture,\n  Flow,\n  FlyControls,\n  FocusShader,\n  Font,\n  FontLoader,\n  FreiChenShader,\n  FresnelShader,\n  FullScreenQuad,\n  GCodeLoader,\n  GLTFExporter,\n  GLTFLoader,\n  GPUComputationRenderer,\n  GammaCorrectionShader,\n  Geometry,\n  GeometryCompressionUtils,\n  GeometryUtils,\n  GlitchPass,\n  GodRaysCombineShader,\n  GodRaysDepthMaskShader,\n  GodRaysFakeSunShader,\n  GodRaysGenerateShader,\n  GrannyKnot,\n  GroundProjectedEnv,\n  Gyroscope,\n  HDRCubeTextureLoader,\n  HTMLMesh,\n  HalfEdge,\n  HalftonePass,\n  HalftoneShader,\n  HeartCurve,\n  HelixCurve,\n  HorizontalBlurShader,\n  HorizontalTiltShiftShader,\n  HueSaturationShader,\n  ImprovedNoise,\n  InstancedFlow,\n  InteractiveGroup,\n  KMZLoader,\n  KTX2Loader,\n  KTXLoader,\n  KaleidoShader,\n  KnotCurve,\n  LDrawLoader,\n  LUT3dlLoader,\n  LUTCubeLoader,\n  LUTPass,\n  LWOLoader,\n  Lensflare,\n  LensflareElement,\n  LightProbeGenerator,\n  LightProbeHelper,\n  LightningStorm,\n  LightningStrike,\n  Line2,\n  LineGeometry,\n  LineMaterial,\n  LineSegments2,\n  LineSegmentsGeometry,\n  LottieLoader,\n  LuminosityHighPassShader,\n  LuminosityShader,\n  Lut,\n  MD2Character,\n  MD2CharacterComplex,\n  MD2Loader,\n  MDDLoader,\n  MMDAnimationHelper,\n  MMDExporter,\n  MMDLoader,\n  MMDPhysics,\n  MTLLoader,\n  MapControls,\n  MapControlsExp,\n  MarchingCubes,\n  MaskPass,\n  MeshSurfaceSampler,\n  MeshoptDecoder,\n  MirrorShader,\n  MorphAnimMesh,\n  MorphBlendMesh,\n  MotionController,\n  MotionControllerConstants,\n  NRRDLoader,\n  NURBSCurve,\n  NURBSSurface,\n  NormalMapShader,\n  OBB,\n  OBJExporter,\n  OBJLoader,\n  Octree,\n  OculusHandModel,\n  OculusHandPointerModel,\n  OrbitControls,\n  OrbitControlsExp,\n  OutlineEffect,\n  OutlinePass,\n  PCDLoader,\n  PDBLoader,\n  PLYExporter,\n  PLYLoader,\n  PRWMLoader,\n  PVRLoader,\n  PackedPhongMaterial,\n  ParallaxBarrierEffect,\n  ParallaxShader,\n  ParametricGeometries,\n  ParametricGeometry,\n  Pass,\n  PeppersGhostEffect,\n  PixelShader,\n  PointerLockControls,\n  PositionalAudioHelper,\n  ProgressiveLightMap,\n  Projector,\n  RGBELoader,\n  RGBMLoader,\n  RGBShiftShader,\n  RaycasterHelper,\n  RectAreaLightHelper,\n  RectAreaLightUniformsLib,\n  Reflector,\n  ReflectorForSSRPass,\n  ReflectorRTT,\n  Refractor,\n  RenderPass,\n  RenderPixelatedPass,\n  RenderableFace,\n  RenderableLine,\n  RenderableObject,\n  RenderableSprite,\n  RenderableVertex,\n  Rhino3dmLoader,\n  RollerCoasterGeometry,\n  RollerCoasterLiftersGeometry,\n  RollerCoasterShadowGeometry,\n  RoomEnvironment,\n  RoughnessMipmapper,\n  RoundedBoxGeometry,\n  SAOPass,\n  SAOShader,\n  SMAABlendShader,\n  SMAAEdgesShader,\n  SMAAPass,\n  SMAAWeightsShader,\n  SSAARenderPass,\n  SSAOBlurShader,\n  SSAODepthShader,\n  SSAOPass,\n  SSAOShader,\n  SSRBlurShader,\n  SSRDepthShader,\n  SSRPass,\n  SSRShader,\n  STATE,\n  STLExporter,\n  STLLoader,\n  SVGLoader,\n  SVGObject,\n  SVGRenderer,\n  SavePass,\n  SceneUtils,\n  SelectionBox,\n  SelectionHelper,\n  SepiaShader,\n  ShaderPass,\n  ShadowMapViewer,\n  ShadowMesh,\n  SimplexNoise,\n  SimplifyModifier,\n  SkeletonUtils,\n  Sky,\n  SkyGeometry,\n  SobelOperatorShader,\n  StereoEffect,\n  SubsurfaceScatteringShader,\n  TAARenderPass,\n  TDSLoader,\n  TGALoader,\n  TTFLoader,\n  TeapotGeometry,\n  TechnicolorShader,\n  TessellateModifier,\n  TextGeometry as TextBufferGeometry,\n  TextGeometry2 as TextGeometry,\n  TexturePass,\n  ThreeMFLoader,\n  TiltLoader,\n  Timer,\n  ToneMapShader,\n  ToonShader1,\n  ToonShader2,\n  ToonShaderDotted,\n  ToonShaderHatching,\n  TorusKnot,\n  TrackballControls,\n  TrackballControlsExp,\n  TransformControls,\n  TransformControlsGizmo,\n  TransformControlsPlane,\n  TreesGeometry,\n  TrefoilKnot,\n  TrefoilPolynomialKnot,\n  TriangleBlurShader,\n  TubePainter,\n  USDZExporter,\n  UVsDebug,\n  UnpackDepthRGBAShader,\n  UnrealBloomPass,\n  VOXData3DTexture,\n  VOXLoader,\n  VOXMesh,\n  VRButton,\n  VRMLLoader,\n  VRMLoader,\n  VTKLoader,\n  VertexList,\n  VertexNode,\n  VertexNormalsHelper,\n  VertexTangentsHelper,\n  VerticalBlurShader,\n  VerticalTiltShiftShader,\n  VignetteShader,\n  VivianiCurve,\n  Volume,\n  VolumeRenderShader1,\n  VolumeSlice,\n  Water,\n  Water2,\n  WaterPass,\n  WaterRefractionShader,\n  Wireframe,\n  WireframeGeometry2,\n  XLoader,\n  XRControllerModelFactory,\n  XREstimatedLight,\n  XRHandMeshModel,\n  XRHandModelFactory,\n  XRHandPrimitiveModel,\n  XYZLoader,\n  calcBSplineDerivatives,\n  calcBSplinePoint,\n  calcBasisFunctionDerivatives,\n  calcBasisFunctions,\n  calcKoverI,\n  calcNURBSDerivatives,\n  calcRationalCurveDerivatives,\n  calcSurfacePoint,\n  computeMorphedAttributes,\n  createText,\n  edgeTable,\n  estimateBytesUsed,\n  fetchProfile,\n  fetchProfilesList,\n  findSpan,\n  getErrorMessage,\n  getUniforms,\n  getWebGL2ErrorMessage,\n  getWebGLErrorMessage,\n  initSplineTexture,\n  interleaveAttributes,\n  isWebGL2Available,\n  isWebGLAvailable,\n  mergeBufferAttributes,\n  mergeBufferGeometries,\n  mergeVertices,\n  modifyShader,\n  toCreasedNormals,\n  toTrianglesDrawMode,\n  triTable,\n  updateSplineTexture\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}