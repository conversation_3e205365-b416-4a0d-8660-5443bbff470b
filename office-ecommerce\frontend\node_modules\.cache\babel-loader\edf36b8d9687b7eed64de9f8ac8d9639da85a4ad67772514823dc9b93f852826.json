{"ast": null, "code": "import { Box3, Vector3, Triangle, Plane, Line3, Sphere } from \"three\";\nimport { Capsule } from \"./Capsule.js\";\nconst _v1 = /* @__PURE__ */new Vector3();\nconst _v2 = /* @__PURE__ */new Vector3();\nconst _plane = /* @__PURE__ */new Plane();\nconst _line1 = /* @__PURE__ */new Line3();\nconst _line2 = /* @__PURE__ */new Line3();\nconst _sphere = /* @__PURE__ */new Sphere();\nconst _capsule = /* @__PURE__ */new Capsule();\nclass Octree {\n  constructor(box) {\n    this.triangles = [];\n    this.box = box;\n    this.subTrees = [];\n  }\n  addTriangle(triangle) {\n    if (!this.bounds) this.bounds = new Box3();\n    this.bounds.min.x = Math.min(this.bounds.min.x, triangle.a.x, triangle.b.x, triangle.c.x);\n    this.bounds.min.y = Math.min(this.bounds.min.y, triangle.a.y, triangle.b.y, triangle.c.y);\n    this.bounds.min.z = Math.min(this.bounds.min.z, triangle.a.z, triangle.b.z, triangle.c.z);\n    this.bounds.max.x = Math.max(this.bounds.max.x, triangle.a.x, triangle.b.x, triangle.c.x);\n    this.bounds.max.y = Math.max(this.bounds.max.y, triangle.a.y, triangle.b.y, triangle.c.y);\n    this.bounds.max.z = Math.max(this.bounds.max.z, triangle.a.z, triangle.b.z, triangle.c.z);\n    this.triangles.push(triangle);\n    return this;\n  }\n  calcBox() {\n    this.box = this.bounds.clone();\n    this.box.min.x -= 0.01;\n    this.box.min.y -= 0.01;\n    this.box.min.z -= 0.01;\n    return this;\n  }\n  split(level) {\n    if (!this.box) return;\n    const subTrees = [];\n    const halfsize = _v2.copy(this.box.max).sub(this.box.min).multiplyScalar(0.5);\n    for (let x = 0; x < 2; x++) {\n      for (let y = 0; y < 2; y++) {\n        for (let z = 0; z < 2; z++) {\n          const box = new Box3();\n          const v = _v1.set(x, y, z);\n          box.min.copy(this.box.min).add(v.multiply(halfsize));\n          box.max.copy(box.min).add(halfsize);\n          subTrees.push(new Octree(box));\n        }\n      }\n    }\n    let triangle;\n    while (triangle = this.triangles.pop()) {\n      for (let i = 0; i < subTrees.length; i++) {\n        if (subTrees[i].box.intersectsTriangle(triangle)) {\n          subTrees[i].triangles.push(triangle);\n        }\n      }\n    }\n    for (let i = 0; i < subTrees.length; i++) {\n      const len = subTrees[i].triangles.length;\n      if (len > 8 && level < 16) {\n        subTrees[i].split(level + 1);\n      }\n      if (len !== 0) {\n        this.subTrees.push(subTrees[i]);\n      }\n    }\n    return this;\n  }\n  build() {\n    this.calcBox();\n    this.split(0);\n    return this;\n  }\n  getRayTriangles(ray, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i];\n      if (!ray.intersectsBox(subTree.box)) continue;\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j]);\n        }\n      } else {\n        subTree.getRayTriangles(ray, triangles);\n      }\n    }\n    return triangles;\n  }\n  triangleCapsuleIntersect(capsule, triangle) {\n    triangle.getPlane(_plane);\n    const d1 = _plane.distanceToPoint(capsule.start) - capsule.radius;\n    const d2 = _plane.distanceToPoint(capsule.end) - capsule.radius;\n    if (d1 > 0 && d2 > 0 || d1 < -capsule.radius && d2 < -capsule.radius) {\n      return false;\n    }\n    const delta = Math.abs(d1 / (Math.abs(d1) + Math.abs(d2)));\n    const intersectPoint = _v1.copy(capsule.start).lerp(capsule.end, delta);\n    if (triangle.containsPoint(intersectPoint)) {\n      return {\n        normal: _plane.normal.clone(),\n        point: intersectPoint.clone(),\n        depth: Math.abs(Math.min(d1, d2))\n      };\n    }\n    const r2 = capsule.radius * capsule.radius;\n    const line1 = _line1.set(capsule.start, capsule.end);\n    const lines = [[triangle.a, triangle.b], [triangle.b, triangle.c], [triangle.c, triangle.a]];\n    for (let i = 0; i < lines.length; i++) {\n      const line2 = _line2.set(lines[i][0], lines[i][1]);\n      const [point1, point2] = capsule.lineLineMinimumPoints(line1, line2);\n      if (point1.distanceToSquared(point2) < r2) {\n        return {\n          normal: point1.clone().sub(point2).normalize(),\n          point: point2.clone(),\n          depth: capsule.radius - point1.distanceTo(point2)\n        };\n      }\n    }\n    return false;\n  }\n  triangleSphereIntersect(sphere, triangle) {\n    triangle.getPlane(_plane);\n    if (!sphere.intersectsPlane(_plane)) return false;\n    const depth = Math.abs(_plane.distanceToSphere(sphere));\n    const r2 = sphere.radius * sphere.radius - depth * depth;\n    const plainPoint = _plane.projectPoint(sphere.center, _v1);\n    if (triangle.containsPoint(sphere.center)) {\n      return {\n        normal: _plane.normal.clone(),\n        point: plainPoint.clone(),\n        depth: Math.abs(_plane.distanceToSphere(sphere))\n      };\n    }\n    const lines = [[triangle.a, triangle.b], [triangle.b, triangle.c], [triangle.c, triangle.a]];\n    for (let i = 0; i < lines.length; i++) {\n      _line1.set(lines[i][0], lines[i][1]);\n      _line1.closestPointToPoint(plainPoint, true, _v2);\n      const d = _v2.distanceToSquared(sphere.center);\n      if (d < r2) {\n        return {\n          normal: sphere.center.clone().sub(_v2).normalize(),\n          point: _v2.clone(),\n          depth: sphere.radius - Math.sqrt(d)\n        };\n      }\n    }\n    return false;\n  }\n  getSphereTriangles(sphere, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i];\n      if (!sphere.intersectsBox(subTree.box)) continue;\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j]);\n        }\n      } else {\n        subTree.getSphereTriangles(sphere, triangles);\n      }\n    }\n  }\n  getCapsuleTriangles(capsule, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i];\n      if (!capsule.intersectsBox(subTree.box)) continue;\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j]);\n        }\n      } else {\n        subTree.getCapsuleTriangles(capsule, triangles);\n      }\n    }\n  }\n  sphereIntersect(sphere) {\n    _sphere.copy(sphere);\n    const triangles = [];\n    let result,\n      hit = false;\n    this.getSphereTriangles(sphere, triangles);\n    for (let i = 0; i < triangles.length; i++) {\n      if (result = this.triangleSphereIntersect(_sphere, triangles[i])) {\n        hit = true;\n        _sphere.center.add(result.normal.multiplyScalar(result.depth));\n      }\n    }\n    if (hit) {\n      const collisionVector = _sphere.center.clone().sub(sphere.center);\n      const depth = collisionVector.length();\n      return {\n        normal: collisionVector.normalize(),\n        depth\n      };\n    }\n    return false;\n  }\n  capsuleIntersect(capsule) {\n    _capsule.copy(capsule);\n    const triangles = [];\n    let result,\n      hit = false;\n    this.getCapsuleTriangles(_capsule, triangles);\n    for (let i = 0; i < triangles.length; i++) {\n      if (result = this.triangleCapsuleIntersect(_capsule, triangles[i])) {\n        hit = true;\n        _capsule.translate(result.normal.multiplyScalar(result.depth));\n      }\n    }\n    if (hit) {\n      const collisionVector = _capsule.getCenter(new Vector3()).sub(capsule.getCenter(_v1));\n      const depth = collisionVector.length();\n      return {\n        normal: collisionVector.normalize(),\n        depth\n      };\n    }\n    return false;\n  }\n  rayIntersect(ray) {\n    if (ray.direction.length() === 0) return;\n    const triangles = [];\n    let triangle,\n      position,\n      distance = 1e100;\n    this.getRayTriangles(ray, triangles);\n    for (let i = 0; i < triangles.length; i++) {\n      const result = ray.intersectTriangle(triangles[i].a, triangles[i].b, triangles[i].c, true, _v1);\n      if (result) {\n        const newdistance = result.sub(ray.origin).length();\n        if (distance > newdistance) {\n          position = result.clone().add(ray.origin);\n          distance = newdistance;\n          triangle = triangles[i];\n        }\n      }\n    }\n    return distance < 1e100 ? {\n      distance,\n      triangle,\n      position\n    } : false;\n  }\n  fromGraphNode(group) {\n    group.updateWorldMatrix(true, true);\n    group.traverse(obj => {\n      if (obj.isMesh === true) {\n        let geometry,\n          isTemp = false;\n        if (obj.geometry.index !== null) {\n          isTemp = true;\n          geometry = obj.geometry.toNonIndexed();\n        } else {\n          geometry = obj.geometry;\n        }\n        const positionAttribute = geometry.getAttribute(\"position\");\n        for (let i = 0; i < positionAttribute.count; i += 3) {\n          const v1 = new Vector3().fromBufferAttribute(positionAttribute, i);\n          const v2 = new Vector3().fromBufferAttribute(positionAttribute, i + 1);\n          const v3 = new Vector3().fromBufferAttribute(positionAttribute, i + 2);\n          v1.applyMatrix4(obj.matrixWorld);\n          v2.applyMatrix4(obj.matrixWorld);\n          v3.applyMatrix4(obj.matrixWorld);\n          this.addTriangle(new Triangle(v1, v2, v3));\n        }\n        if (isTemp) {\n          geometry.dispose();\n        }\n      }\n    });\n    this.build();\n    return this;\n  }\n}\nexport { Octree };", "map": {"version": 3, "names": ["_v1", "Vector3", "_v2", "_plane", "Plane", "_line1", "Line3", "_line2", "_sphere", "Sphere", "_capsule", "Capsule", "Octree", "constructor", "box", "triangles", "subTrees", "addTriangle", "triangle", "bounds", "Box3", "min", "x", "Math", "a", "b", "c", "y", "z", "max", "push", "calcBox", "clone", "split", "level", "halfsize", "copy", "sub", "multiplyScalar", "v", "set", "add", "multiply", "pop", "i", "length", "intersectsTriangle", "len", "build", "getRayTriangles", "ray", "subTree", "intersectsBox", "j", "indexOf", "triangleCapsuleIntersect", "capsule", "getPlane", "d1", "distanceToPoint", "start", "radius", "d2", "end", "delta", "abs", "intersectPoint", "lerp", "containsPoint", "normal", "point", "depth", "r2", "line1", "lines", "line2", "point1", "point2", "lineLineMinimumPoints", "distanceToSquared", "normalize", "distanceTo", "triangleSphereIntersect", "sphere", "intersects<PERSON><PERSON>", "distanceToSphere", "plainPoint", "projectPoint", "center", "closestPointToPoint", "d", "sqrt", "getSphereTriangles", "getCapsuleTriangles", "sphereIntersect", "result", "hit", "collisionVector", "capsuleIntersect", "translate", "getCenter", "rayIntersect", "direction", "position", "distance", "intersectTriangle", "newdistance", "origin", "fromGraphNode", "group", "updateWorldMatrix", "traverse", "obj", "<PERSON><PERSON><PERSON>", "geometry", "isTemp", "index", "toNonIndexed", "positionAttribute", "getAttribute", "count", "v1", "fromBufferAttribute", "v2", "v3", "applyMatrix4", "matrixWorld", "Triangle", "dispose"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\math\\Octree.js"], "sourcesContent": ["import { Box3, Line3, Plane, Sphere, Triangle, Vector3 } from 'three'\nimport { Capsule } from '../math/Capsule'\n\nconst _v1 = /* @__PURE__ */ new Vector3()\nconst _v2 = /* @__PURE__ */ new Vector3()\nconst _plane = /* @__PURE__ */ new Plane()\nconst _line1 = /* @__PURE__ */ new Line3()\nconst _line2 = /* @__PURE__ */ new Line3()\nconst _sphere = /* @__PURE__ */ new Sphere()\nconst _capsule = /* @__PURE__ */ new Capsule()\n\nclass Octree {\n  constructor(box) {\n    this.triangles = []\n    this.box = box\n    this.subTrees = []\n  }\n\n  addTriangle(triangle) {\n    if (!this.bounds) this.bounds = new Box3()\n\n    this.bounds.min.x = Math.min(this.bounds.min.x, triangle.a.x, triangle.b.x, triangle.c.x)\n    this.bounds.min.y = Math.min(this.bounds.min.y, triangle.a.y, triangle.b.y, triangle.c.y)\n    this.bounds.min.z = Math.min(this.bounds.min.z, triangle.a.z, triangle.b.z, triangle.c.z)\n    this.bounds.max.x = Math.max(this.bounds.max.x, triangle.a.x, triangle.b.x, triangle.c.x)\n    this.bounds.max.y = Math.max(this.bounds.max.y, triangle.a.y, triangle.b.y, triangle.c.y)\n    this.bounds.max.z = Math.max(this.bounds.max.z, triangle.a.z, triangle.b.z, triangle.c.z)\n\n    this.triangles.push(triangle)\n\n    return this\n  }\n\n  calcBox() {\n    this.box = this.bounds.clone()\n\n    // offset small amount to account for regular grid\n    this.box.min.x -= 0.01\n    this.box.min.y -= 0.01\n    this.box.min.z -= 0.01\n\n    return this\n  }\n\n  split(level) {\n    if (!this.box) return\n\n    const subTrees = []\n    const halfsize = _v2.copy(this.box.max).sub(this.box.min).multiplyScalar(0.5)\n\n    for (let x = 0; x < 2; x++) {\n      for (let y = 0; y < 2; y++) {\n        for (let z = 0; z < 2; z++) {\n          const box = new Box3()\n          const v = _v1.set(x, y, z)\n\n          box.min.copy(this.box.min).add(v.multiply(halfsize))\n          box.max.copy(box.min).add(halfsize)\n\n          subTrees.push(new Octree(box))\n        }\n      }\n    }\n\n    let triangle\n\n    while ((triangle = this.triangles.pop())) {\n      for (let i = 0; i < subTrees.length; i++) {\n        if (subTrees[i].box.intersectsTriangle(triangle)) {\n          subTrees[i].triangles.push(triangle)\n        }\n      }\n    }\n\n    for (let i = 0; i < subTrees.length; i++) {\n      const len = subTrees[i].triangles.length\n\n      if (len > 8 && level < 16) {\n        subTrees[i].split(level + 1)\n      }\n\n      if (len !== 0) {\n        this.subTrees.push(subTrees[i])\n      }\n    }\n\n    return this\n  }\n\n  build() {\n    this.calcBox()\n    this.split(0)\n\n    return this\n  }\n\n  getRayTriangles(ray, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i]\n      if (!ray.intersectsBox(subTree.box)) continue\n\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j])\n        }\n      } else {\n        subTree.getRayTriangles(ray, triangles)\n      }\n    }\n\n    return triangles\n  }\n\n  triangleCapsuleIntersect(capsule, triangle) {\n    triangle.getPlane(_plane)\n\n    const d1 = _plane.distanceToPoint(capsule.start) - capsule.radius\n    const d2 = _plane.distanceToPoint(capsule.end) - capsule.radius\n\n    if ((d1 > 0 && d2 > 0) || (d1 < -capsule.radius && d2 < -capsule.radius)) {\n      return false\n    }\n\n    const delta = Math.abs(d1 / (Math.abs(d1) + Math.abs(d2)))\n    const intersectPoint = _v1.copy(capsule.start).lerp(capsule.end, delta)\n\n    if (triangle.containsPoint(intersectPoint)) {\n      return { normal: _plane.normal.clone(), point: intersectPoint.clone(), depth: Math.abs(Math.min(d1, d2)) }\n    }\n\n    const r2 = capsule.radius * capsule.radius\n\n    const line1 = _line1.set(capsule.start, capsule.end)\n\n    const lines = [\n      [triangle.a, triangle.b],\n      [triangle.b, triangle.c],\n      [triangle.c, triangle.a],\n    ]\n\n    for (let i = 0; i < lines.length; i++) {\n      const line2 = _line2.set(lines[i][0], lines[i][1])\n\n      const [point1, point2] = capsule.lineLineMinimumPoints(line1, line2)\n\n      if (point1.distanceToSquared(point2) < r2) {\n        return {\n          normal: point1.clone().sub(point2).normalize(),\n          point: point2.clone(),\n          depth: capsule.radius - point1.distanceTo(point2),\n        }\n      }\n    }\n\n    return false\n  }\n\n  triangleSphereIntersect(sphere, triangle) {\n    triangle.getPlane(_plane)\n\n    if (!sphere.intersectsPlane(_plane)) return false\n\n    const depth = Math.abs(_plane.distanceToSphere(sphere))\n    const r2 = sphere.radius * sphere.radius - depth * depth\n\n    const plainPoint = _plane.projectPoint(sphere.center, _v1)\n\n    if (triangle.containsPoint(sphere.center)) {\n      return {\n        normal: _plane.normal.clone(),\n        point: plainPoint.clone(),\n        depth: Math.abs(_plane.distanceToSphere(sphere)),\n      }\n    }\n\n    const lines = [\n      [triangle.a, triangle.b],\n      [triangle.b, triangle.c],\n      [triangle.c, triangle.a],\n    ]\n\n    for (let i = 0; i < lines.length; i++) {\n      _line1.set(lines[i][0], lines[i][1])\n      _line1.closestPointToPoint(plainPoint, true, _v2)\n\n      const d = _v2.distanceToSquared(sphere.center)\n\n      if (d < r2) {\n        return {\n          normal: sphere.center.clone().sub(_v2).normalize(),\n          point: _v2.clone(),\n          depth: sphere.radius - Math.sqrt(d),\n        }\n      }\n    }\n\n    return false\n  }\n\n  getSphereTriangles(sphere, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i]\n\n      if (!sphere.intersectsBox(subTree.box)) continue\n\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j])\n        }\n      } else {\n        subTree.getSphereTriangles(sphere, triangles)\n      }\n    }\n  }\n\n  getCapsuleTriangles(capsule, triangles) {\n    for (let i = 0; i < this.subTrees.length; i++) {\n      const subTree = this.subTrees[i]\n\n      if (!capsule.intersectsBox(subTree.box)) continue\n\n      if (subTree.triangles.length > 0) {\n        for (let j = 0; j < subTree.triangles.length; j++) {\n          if (triangles.indexOf(subTree.triangles[j]) === -1) triangles.push(subTree.triangles[j])\n        }\n      } else {\n        subTree.getCapsuleTriangles(capsule, triangles)\n      }\n    }\n  }\n\n  sphereIntersect(sphere) {\n    _sphere.copy(sphere)\n\n    const triangles = []\n    let result,\n      hit = false\n\n    this.getSphereTriangles(sphere, triangles)\n\n    for (let i = 0; i < triangles.length; i++) {\n      if ((result = this.triangleSphereIntersect(_sphere, triangles[i]))) {\n        hit = true\n\n        _sphere.center.add(result.normal.multiplyScalar(result.depth))\n      }\n    }\n\n    if (hit) {\n      const collisionVector = _sphere.center.clone().sub(sphere.center)\n      const depth = collisionVector.length()\n\n      return { normal: collisionVector.normalize(), depth: depth }\n    }\n\n    return false\n  }\n\n  capsuleIntersect(capsule) {\n    _capsule.copy(capsule)\n\n    const triangles = []\n    let result,\n      hit = false\n\n    this.getCapsuleTriangles(_capsule, triangles)\n\n    for (let i = 0; i < triangles.length; i++) {\n      if ((result = this.triangleCapsuleIntersect(_capsule, triangles[i]))) {\n        hit = true\n\n        _capsule.translate(result.normal.multiplyScalar(result.depth))\n      }\n    }\n\n    if (hit) {\n      const collisionVector = _capsule.getCenter(new Vector3()).sub(capsule.getCenter(_v1))\n      const depth = collisionVector.length()\n\n      return { normal: collisionVector.normalize(), depth: depth }\n    }\n\n    return false\n  }\n\n  rayIntersect(ray) {\n    if (ray.direction.length() === 0) return\n\n    const triangles = []\n    let triangle,\n      position,\n      distance = 1e100\n\n    this.getRayTriangles(ray, triangles)\n\n    for (let i = 0; i < triangles.length; i++) {\n      const result = ray.intersectTriangle(triangles[i].a, triangles[i].b, triangles[i].c, true, _v1)\n\n      if (result) {\n        const newdistance = result.sub(ray.origin).length()\n\n        if (distance > newdistance) {\n          position = result.clone().add(ray.origin)\n          distance = newdistance\n          triangle = triangles[i]\n        }\n      }\n    }\n\n    return distance < 1e100 ? { distance: distance, triangle: triangle, position: position } : false\n  }\n\n  fromGraphNode(group) {\n    group.updateWorldMatrix(true, true)\n\n    group.traverse((obj) => {\n      if (obj.isMesh === true) {\n        let geometry,\n          isTemp = false\n\n        if (obj.geometry.index !== null) {\n          isTemp = true\n          geometry = obj.geometry.toNonIndexed()\n        } else {\n          geometry = obj.geometry\n        }\n\n        const positionAttribute = geometry.getAttribute('position')\n\n        for (let i = 0; i < positionAttribute.count; i += 3) {\n          const v1 = new Vector3().fromBufferAttribute(positionAttribute, i)\n          const v2 = new Vector3().fromBufferAttribute(positionAttribute, i + 1)\n          const v3 = new Vector3().fromBufferAttribute(positionAttribute, i + 2)\n\n          v1.applyMatrix4(obj.matrixWorld)\n          v2.applyMatrix4(obj.matrixWorld)\n          v3.applyMatrix4(obj.matrixWorld)\n\n          this.addTriangle(new Triangle(v1, v2, v3))\n        }\n\n        if (isTemp) {\n          geometry.dispose()\n        }\n      }\n    })\n\n    this.build()\n\n    return this\n  }\n}\n\nexport { Octree }\n"], "mappings": ";;AAGA,MAAMA,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AACzC,MAAMC,GAAA,GAAsB,mBAAID,OAAA,CAAS;AACzC,MAAME,MAAA,GAAyB,mBAAIC,KAAA,CAAO;AAC1C,MAAMC,MAAA,GAAyB,mBAAIC,KAAA,CAAO;AAC1C,MAAMC,MAAA,GAAyB,mBAAID,KAAA,CAAO;AAC1C,MAAME,OAAA,GAA0B,mBAAIC,MAAA,CAAQ;AAC5C,MAAMC,QAAA,GAA2B,mBAAIC,OAAA,CAAS;AAE9C,MAAMC,MAAA,CAAO;EACXC,YAAYC,GAAA,EAAK;IACf,KAAKC,SAAA,GAAY,EAAE;IACnB,KAAKD,GAAA,GAAMA,GAAA;IACX,KAAKE,QAAA,GAAW,EAAE;EACnB;EAEDC,YAAYC,QAAA,EAAU;IACpB,IAAI,CAAC,KAAKC,MAAA,EAAQ,KAAKA,MAAA,GAAS,IAAIC,IAAA,CAAM;IAE1C,KAAKD,MAAA,CAAOE,GAAA,CAAIC,CAAA,GAAIC,IAAA,CAAKF,GAAA,CAAI,KAAKF,MAAA,CAAOE,GAAA,CAAIC,CAAA,EAAGJ,QAAA,CAASM,CAAA,CAAEF,CAAA,EAAGJ,QAAA,CAASO,CAAA,CAAEH,CAAA,EAAGJ,QAAA,CAASQ,CAAA,CAAEJ,CAAC;IACxF,KAAKH,MAAA,CAAOE,GAAA,CAAIM,CAAA,GAAIJ,IAAA,CAAKF,GAAA,CAAI,KAAKF,MAAA,CAAOE,GAAA,CAAIM,CAAA,EAAGT,QAAA,CAASM,CAAA,CAAEG,CAAA,EAAGT,QAAA,CAASO,CAAA,CAAEE,CAAA,EAAGT,QAAA,CAASQ,CAAA,CAAEC,CAAC;IACxF,KAAKR,MAAA,CAAOE,GAAA,CAAIO,CAAA,GAAIL,IAAA,CAAKF,GAAA,CAAI,KAAKF,MAAA,CAAOE,GAAA,CAAIO,CAAA,EAAGV,QAAA,CAASM,CAAA,CAAEI,CAAA,EAAGV,QAAA,CAASO,CAAA,CAAEG,CAAA,EAAGV,QAAA,CAASQ,CAAA,CAAEE,CAAC;IACxF,KAAKT,MAAA,CAAOU,GAAA,CAAIP,CAAA,GAAIC,IAAA,CAAKM,GAAA,CAAI,KAAKV,MAAA,CAAOU,GAAA,CAAIP,CAAA,EAAGJ,QAAA,CAASM,CAAA,CAAEF,CAAA,EAAGJ,QAAA,CAASO,CAAA,CAAEH,CAAA,EAAGJ,QAAA,CAASQ,CAAA,CAAEJ,CAAC;IACxF,KAAKH,MAAA,CAAOU,GAAA,CAAIF,CAAA,GAAIJ,IAAA,CAAKM,GAAA,CAAI,KAAKV,MAAA,CAAOU,GAAA,CAAIF,CAAA,EAAGT,QAAA,CAASM,CAAA,CAAEG,CAAA,EAAGT,QAAA,CAASO,CAAA,CAAEE,CAAA,EAAGT,QAAA,CAASQ,CAAA,CAAEC,CAAC;IACxF,KAAKR,MAAA,CAAOU,GAAA,CAAID,CAAA,GAAIL,IAAA,CAAKM,GAAA,CAAI,KAAKV,MAAA,CAAOU,GAAA,CAAID,CAAA,EAAGV,QAAA,CAASM,CAAA,CAAEI,CAAA,EAAGV,QAAA,CAASO,CAAA,CAAEG,CAAA,EAAGV,QAAA,CAASQ,CAAA,CAAEE,CAAC;IAExF,KAAKb,SAAA,CAAUe,IAAA,CAAKZ,QAAQ;IAE5B,OAAO;EACR;EAEDa,QAAA,EAAU;IACR,KAAKjB,GAAA,GAAM,KAAKK,MAAA,CAAOa,KAAA,CAAO;IAG9B,KAAKlB,GAAA,CAAIO,GAAA,CAAIC,CAAA,IAAK;IAClB,KAAKR,GAAA,CAAIO,GAAA,CAAIM,CAAA,IAAK;IAClB,KAAKb,GAAA,CAAIO,GAAA,CAAIO,CAAA,IAAK;IAElB,OAAO;EACR;EAEDK,MAAMC,KAAA,EAAO;IACX,IAAI,CAAC,KAAKpB,GAAA,EAAK;IAEf,MAAME,QAAA,GAAW,EAAE;IACnB,MAAMmB,QAAA,GAAWjC,GAAA,CAAIkC,IAAA,CAAK,KAAKtB,GAAA,CAAIe,GAAG,EAAEQ,GAAA,CAAI,KAAKvB,GAAA,CAAIO,GAAG,EAAEiB,cAAA,CAAe,GAAG;IAE5E,SAAShB,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1B,SAASK,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1B,MAAMd,GAAA,GAAM,IAAIM,IAAA,CAAM;UACtB,MAAMmB,CAAA,GAAIvC,GAAA,CAAIwC,GAAA,CAAIlB,CAAA,EAAGK,CAAA,EAAGC,CAAC;UAEzBd,GAAA,CAAIO,GAAA,CAAIe,IAAA,CAAK,KAAKtB,GAAA,CAAIO,GAAG,EAAEoB,GAAA,CAAIF,CAAA,CAAEG,QAAA,CAASP,QAAQ,CAAC;UACnDrB,GAAA,CAAIe,GAAA,CAAIO,IAAA,CAAKtB,GAAA,CAAIO,GAAG,EAAEoB,GAAA,CAAIN,QAAQ;UAElCnB,QAAA,CAASc,IAAA,CAAK,IAAIlB,MAAA,CAAOE,GAAG,CAAC;QAC9B;MACF;IACF;IAED,IAAII,QAAA;IAEJ,OAAQA,QAAA,GAAW,KAAKH,SAAA,CAAU4B,GAAA,CAAG,GAAK;MACxC,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI5B,QAAA,CAAS6B,MAAA,EAAQD,CAAA,IAAK;QACxC,IAAI5B,QAAA,CAAS4B,CAAC,EAAE9B,GAAA,CAAIgC,kBAAA,CAAmB5B,QAAQ,GAAG;UAChDF,QAAA,CAAS4B,CAAC,EAAE7B,SAAA,CAAUe,IAAA,CAAKZ,QAAQ;QACpC;MACF;IACF;IAED,SAAS0B,CAAA,GAAI,GAAGA,CAAA,GAAI5B,QAAA,CAAS6B,MAAA,EAAQD,CAAA,IAAK;MACxC,MAAMG,GAAA,GAAM/B,QAAA,CAAS4B,CAAC,EAAE7B,SAAA,CAAU8B,MAAA;MAElC,IAAIE,GAAA,GAAM,KAAKb,KAAA,GAAQ,IAAI;QACzBlB,QAAA,CAAS4B,CAAC,EAAEX,KAAA,CAAMC,KAAA,GAAQ,CAAC;MAC5B;MAED,IAAIa,GAAA,KAAQ,GAAG;QACb,KAAK/B,QAAA,CAASc,IAAA,CAAKd,QAAA,CAAS4B,CAAC,CAAC;MAC/B;IACF;IAED,OAAO;EACR;EAEDI,MAAA,EAAQ;IACN,KAAKjB,OAAA,CAAS;IACd,KAAKE,KAAA,CAAM,CAAC;IAEZ,OAAO;EACR;EAEDgB,gBAAgBC,GAAA,EAAKnC,SAAA,EAAW;IAC9B,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK5B,QAAA,CAAS6B,MAAA,EAAQD,CAAA,IAAK;MAC7C,MAAMO,OAAA,GAAU,KAAKnC,QAAA,CAAS4B,CAAC;MAC/B,IAAI,CAACM,GAAA,CAAIE,aAAA,CAAcD,OAAA,CAAQrC,GAAG,GAAG;MAErC,IAAIqC,OAAA,CAAQpC,SAAA,CAAU8B,MAAA,GAAS,GAAG;QAChC,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAIF,OAAA,CAAQpC,SAAA,CAAU8B,MAAA,EAAQQ,CAAA,IAAK;UACjD,IAAItC,SAAA,CAAUuC,OAAA,CAAQH,OAAA,CAAQpC,SAAA,CAAUsC,CAAC,CAAC,MAAM,IAAItC,SAAA,CAAUe,IAAA,CAAKqB,OAAA,CAAQpC,SAAA,CAAUsC,CAAC,CAAC;QACxF;MACT,OAAa;QACLF,OAAA,CAAQF,eAAA,CAAgBC,GAAA,EAAKnC,SAAS;MACvC;IACF;IAED,OAAOA,SAAA;EACR;EAEDwC,yBAAyBC,OAAA,EAAStC,QAAA,EAAU;IAC1CA,QAAA,CAASuC,QAAA,CAAStD,MAAM;IAExB,MAAMuD,EAAA,GAAKvD,MAAA,CAAOwD,eAAA,CAAgBH,OAAA,CAAQI,KAAK,IAAIJ,OAAA,CAAQK,MAAA;IAC3D,MAAMC,EAAA,GAAK3D,MAAA,CAAOwD,eAAA,CAAgBH,OAAA,CAAQO,GAAG,IAAIP,OAAA,CAAQK,MAAA;IAEzD,IAAKH,EAAA,GAAK,KAAKI,EAAA,GAAK,KAAOJ,EAAA,GAAK,CAACF,OAAA,CAAQK,MAAA,IAAUC,EAAA,GAAK,CAACN,OAAA,CAAQK,MAAA,EAAS;MACxE,OAAO;IACR;IAED,MAAMG,KAAA,GAAQzC,IAAA,CAAK0C,GAAA,CAAIP,EAAA,IAAMnC,IAAA,CAAK0C,GAAA,CAAIP,EAAE,IAAInC,IAAA,CAAK0C,GAAA,CAAIH,EAAE,EAAE;IACzD,MAAMI,cAAA,GAAiBlE,GAAA,CAAIoC,IAAA,CAAKoB,OAAA,CAAQI,KAAK,EAAEO,IAAA,CAAKX,OAAA,CAAQO,GAAA,EAAKC,KAAK;IAEtE,IAAI9C,QAAA,CAASkD,aAAA,CAAcF,cAAc,GAAG;MAC1C,OAAO;QAAEG,MAAA,EAAQlE,MAAA,CAAOkE,MAAA,CAAOrC,KAAA,CAAO;QAAEsC,KAAA,EAAOJ,cAAA,CAAelC,KAAA,CAAO;QAAEuC,KAAA,EAAOhD,IAAA,CAAK0C,GAAA,CAAI1C,IAAA,CAAKF,GAAA,CAAIqC,EAAA,EAAII,EAAE,CAAC;MAAG;IAC3G;IAED,MAAMU,EAAA,GAAKhB,OAAA,CAAQK,MAAA,GAASL,OAAA,CAAQK,MAAA;IAEpC,MAAMY,KAAA,GAAQpE,MAAA,CAAOmC,GAAA,CAAIgB,OAAA,CAAQI,KAAA,EAAOJ,OAAA,CAAQO,GAAG;IAEnD,MAAMW,KAAA,GAAQ,CACZ,CAACxD,QAAA,CAASM,CAAA,EAAGN,QAAA,CAASO,CAAC,GACvB,CAACP,QAAA,CAASO,CAAA,EAAGP,QAAA,CAASQ,CAAC,GACvB,CAACR,QAAA,CAASQ,CAAA,EAAGR,QAAA,CAASM,CAAC,EACxB;IAED,SAASoB,CAAA,GAAI,GAAGA,CAAA,GAAI8B,KAAA,CAAM7B,MAAA,EAAQD,CAAA,IAAK;MACrC,MAAM+B,KAAA,GAAQpE,MAAA,CAAOiC,GAAA,CAAIkC,KAAA,CAAM9B,CAAC,EAAE,CAAC,GAAG8B,KAAA,CAAM9B,CAAC,EAAE,CAAC,CAAC;MAEjD,MAAM,CAACgC,MAAA,EAAQC,MAAM,IAAIrB,OAAA,CAAQsB,qBAAA,CAAsBL,KAAA,EAAOE,KAAK;MAEnE,IAAIC,MAAA,CAAOG,iBAAA,CAAkBF,MAAM,IAAIL,EAAA,EAAI;QACzC,OAAO;UACLH,MAAA,EAAQO,MAAA,CAAO5C,KAAA,CAAK,EAAGK,GAAA,CAAIwC,MAAM,EAAEG,SAAA,CAAW;UAC9CV,KAAA,EAAOO,MAAA,CAAO7C,KAAA,CAAO;UACrBuC,KAAA,EAAOf,OAAA,CAAQK,MAAA,GAASe,MAAA,CAAOK,UAAA,CAAWJ,MAAM;QACjD;MACF;IACF;IAED,OAAO;EACR;EAEDK,wBAAwBC,MAAA,EAAQjE,QAAA,EAAU;IACxCA,QAAA,CAASuC,QAAA,CAAStD,MAAM;IAExB,IAAI,CAACgF,MAAA,CAAOC,eAAA,CAAgBjF,MAAM,GAAG,OAAO;IAE5C,MAAMoE,KAAA,GAAQhD,IAAA,CAAK0C,GAAA,CAAI9D,MAAA,CAAOkF,gBAAA,CAAiBF,MAAM,CAAC;IACtD,MAAMX,EAAA,GAAKW,MAAA,CAAOtB,MAAA,GAASsB,MAAA,CAAOtB,MAAA,GAASU,KAAA,GAAQA,KAAA;IAEnD,MAAMe,UAAA,GAAanF,MAAA,CAAOoF,YAAA,CAAaJ,MAAA,CAAOK,MAAA,EAAQxF,GAAG;IAEzD,IAAIkB,QAAA,CAASkD,aAAA,CAAce,MAAA,CAAOK,MAAM,GAAG;MACzC,OAAO;QACLnB,MAAA,EAAQlE,MAAA,CAAOkE,MAAA,CAAOrC,KAAA,CAAO;QAC7BsC,KAAA,EAAOgB,UAAA,CAAWtD,KAAA,CAAO;QACzBuC,KAAA,EAAOhD,IAAA,CAAK0C,GAAA,CAAI9D,MAAA,CAAOkF,gBAAA,CAAiBF,MAAM,CAAC;MAChD;IACF;IAED,MAAMT,KAAA,GAAQ,CACZ,CAACxD,QAAA,CAASM,CAAA,EAAGN,QAAA,CAASO,CAAC,GACvB,CAACP,QAAA,CAASO,CAAA,EAAGP,QAAA,CAASQ,CAAC,GACvB,CAACR,QAAA,CAASQ,CAAA,EAAGR,QAAA,CAASM,CAAC,EACxB;IAED,SAASoB,CAAA,GAAI,GAAGA,CAAA,GAAI8B,KAAA,CAAM7B,MAAA,EAAQD,CAAA,IAAK;MACrCvC,MAAA,CAAOmC,GAAA,CAAIkC,KAAA,CAAM9B,CAAC,EAAE,CAAC,GAAG8B,KAAA,CAAM9B,CAAC,EAAE,CAAC,CAAC;MACnCvC,MAAA,CAAOoF,mBAAA,CAAoBH,UAAA,EAAY,MAAMpF,GAAG;MAEhD,MAAMwF,CAAA,GAAIxF,GAAA,CAAI6E,iBAAA,CAAkBI,MAAA,CAAOK,MAAM;MAE7C,IAAIE,CAAA,GAAIlB,EAAA,EAAI;QACV,OAAO;UACLH,MAAA,EAAQc,MAAA,CAAOK,MAAA,CAAOxD,KAAA,CAAO,EAACK,GAAA,CAAInC,GAAG,EAAE8E,SAAA,CAAW;UAClDV,KAAA,EAAOpE,GAAA,CAAI8B,KAAA,CAAO;UAClBuC,KAAA,EAAOY,MAAA,CAAOtB,MAAA,GAAStC,IAAA,CAAKoE,IAAA,CAAKD,CAAC;QACnC;MACF;IACF;IAED,OAAO;EACR;EAEDE,mBAAmBT,MAAA,EAAQpE,SAAA,EAAW;IACpC,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK5B,QAAA,CAAS6B,MAAA,EAAQD,CAAA,IAAK;MAC7C,MAAMO,OAAA,GAAU,KAAKnC,QAAA,CAAS4B,CAAC;MAE/B,IAAI,CAACuC,MAAA,CAAO/B,aAAA,CAAcD,OAAA,CAAQrC,GAAG,GAAG;MAExC,IAAIqC,OAAA,CAAQpC,SAAA,CAAU8B,MAAA,GAAS,GAAG;QAChC,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAIF,OAAA,CAAQpC,SAAA,CAAU8B,MAAA,EAAQQ,CAAA,IAAK;UACjD,IAAItC,SAAA,CAAUuC,OAAA,CAAQH,OAAA,CAAQpC,SAAA,CAAUsC,CAAC,CAAC,MAAM,IAAItC,SAAA,CAAUe,IAAA,CAAKqB,OAAA,CAAQpC,SAAA,CAAUsC,CAAC,CAAC;QACxF;MACT,OAAa;QACLF,OAAA,CAAQyC,kBAAA,CAAmBT,MAAA,EAAQpE,SAAS;MAC7C;IACF;EACF;EAED8E,oBAAoBrC,OAAA,EAASzC,SAAA,EAAW;IACtC,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK5B,QAAA,CAAS6B,MAAA,EAAQD,CAAA,IAAK;MAC7C,MAAMO,OAAA,GAAU,KAAKnC,QAAA,CAAS4B,CAAC;MAE/B,IAAI,CAACY,OAAA,CAAQJ,aAAA,CAAcD,OAAA,CAAQrC,GAAG,GAAG;MAEzC,IAAIqC,OAAA,CAAQpC,SAAA,CAAU8B,MAAA,GAAS,GAAG;QAChC,SAASQ,CAAA,GAAI,GAAGA,CAAA,GAAIF,OAAA,CAAQpC,SAAA,CAAU8B,MAAA,EAAQQ,CAAA,IAAK;UACjD,IAAItC,SAAA,CAAUuC,OAAA,CAAQH,OAAA,CAAQpC,SAAA,CAAUsC,CAAC,CAAC,MAAM,IAAItC,SAAA,CAAUe,IAAA,CAAKqB,OAAA,CAAQpC,SAAA,CAAUsC,CAAC,CAAC;QACxF;MACT,OAAa;QACLF,OAAA,CAAQ0C,mBAAA,CAAoBrC,OAAA,EAASzC,SAAS;MAC/C;IACF;EACF;EAED+E,gBAAgBX,MAAA,EAAQ;IACtB3E,OAAA,CAAQ4B,IAAA,CAAK+C,MAAM;IAEnB,MAAMpE,SAAA,GAAY,EAAE;IACpB,IAAIgF,MAAA;MACFC,GAAA,GAAM;IAER,KAAKJ,kBAAA,CAAmBT,MAAA,EAAQpE,SAAS;IAEzC,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI7B,SAAA,CAAU8B,MAAA,EAAQD,CAAA,IAAK;MACzC,IAAKmD,MAAA,GAAS,KAAKb,uBAAA,CAAwB1E,OAAA,EAASO,SAAA,CAAU6B,CAAC,CAAC,GAAI;QAClEoD,GAAA,GAAM;QAENxF,OAAA,CAAQgF,MAAA,CAAO/C,GAAA,CAAIsD,MAAA,CAAO1B,MAAA,CAAO/B,cAAA,CAAeyD,MAAA,CAAOxB,KAAK,CAAC;MAC9D;IACF;IAED,IAAIyB,GAAA,EAAK;MACP,MAAMC,eAAA,GAAkBzF,OAAA,CAAQgF,MAAA,CAAOxD,KAAA,CAAK,EAAGK,GAAA,CAAI8C,MAAA,CAAOK,MAAM;MAChE,MAAMjB,KAAA,GAAQ0B,eAAA,CAAgBpD,MAAA,CAAQ;MAEtC,OAAO;QAAEwB,MAAA,EAAQ4B,eAAA,CAAgBjB,SAAA,CAAS;QAAIT;MAAc;IAC7D;IAED,OAAO;EACR;EAED2B,iBAAiB1C,OAAA,EAAS;IACxB9C,QAAA,CAAS0B,IAAA,CAAKoB,OAAO;IAErB,MAAMzC,SAAA,GAAY,EAAE;IACpB,IAAIgF,MAAA;MACFC,GAAA,GAAM;IAER,KAAKH,mBAAA,CAAoBnF,QAAA,EAAUK,SAAS;IAE5C,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI7B,SAAA,CAAU8B,MAAA,EAAQD,CAAA,IAAK;MACzC,IAAKmD,MAAA,GAAS,KAAKxC,wBAAA,CAAyB7C,QAAA,EAAUK,SAAA,CAAU6B,CAAC,CAAC,GAAI;QACpEoD,GAAA,GAAM;QAENtF,QAAA,CAASyF,SAAA,CAAUJ,MAAA,CAAO1B,MAAA,CAAO/B,cAAA,CAAeyD,MAAA,CAAOxB,KAAK,CAAC;MAC9D;IACF;IAED,IAAIyB,GAAA,EAAK;MACP,MAAMC,eAAA,GAAkBvF,QAAA,CAAS0F,SAAA,CAAU,IAAInG,OAAA,CAAO,CAAE,EAAEoC,GAAA,CAAImB,OAAA,CAAQ4C,SAAA,CAAUpG,GAAG,CAAC;MACpF,MAAMuE,KAAA,GAAQ0B,eAAA,CAAgBpD,MAAA,CAAQ;MAEtC,OAAO;QAAEwB,MAAA,EAAQ4B,eAAA,CAAgBjB,SAAA,CAAS;QAAIT;MAAc;IAC7D;IAED,OAAO;EACR;EAED8B,aAAanD,GAAA,EAAK;IAChB,IAAIA,GAAA,CAAIoD,SAAA,CAAUzD,MAAA,CAAQ,MAAK,GAAG;IAElC,MAAM9B,SAAA,GAAY,EAAE;IACpB,IAAIG,QAAA;MACFqF,QAAA;MACAC,QAAA,GAAW;IAEb,KAAKvD,eAAA,CAAgBC,GAAA,EAAKnC,SAAS;IAEnC,SAAS6B,CAAA,GAAI,GAAGA,CAAA,GAAI7B,SAAA,CAAU8B,MAAA,EAAQD,CAAA,IAAK;MACzC,MAAMmD,MAAA,GAAS7C,GAAA,CAAIuD,iBAAA,CAAkB1F,SAAA,CAAU6B,CAAC,EAAEpB,CAAA,EAAGT,SAAA,CAAU6B,CAAC,EAAEnB,CAAA,EAAGV,SAAA,CAAU6B,CAAC,EAAElB,CAAA,EAAG,MAAM1B,GAAG;MAE9F,IAAI+F,MAAA,EAAQ;QACV,MAAMW,WAAA,GAAcX,MAAA,CAAO1D,GAAA,CAAIa,GAAA,CAAIyD,MAAM,EAAE9D,MAAA,CAAQ;QAEnD,IAAI2D,QAAA,GAAWE,WAAA,EAAa;UAC1BH,QAAA,GAAWR,MAAA,CAAO/D,KAAA,CAAK,EAAGS,GAAA,CAAIS,GAAA,CAAIyD,MAAM;UACxCH,QAAA,GAAWE,WAAA;UACXxF,QAAA,GAAWH,SAAA,CAAU6B,CAAC;QACvB;MACF;IACF;IAED,OAAO4D,QAAA,GAAW,QAAQ;MAAEA,QAAA;MAAoBtF,QAAA;MAAoBqF;IAAkB,IAAK;EAC5F;EAEDK,cAAcC,KAAA,EAAO;IACnBA,KAAA,CAAMC,iBAAA,CAAkB,MAAM,IAAI;IAElCD,KAAA,CAAME,QAAA,CAAUC,GAAA,IAAQ;MACtB,IAAIA,GAAA,CAAIC,MAAA,KAAW,MAAM;QACvB,IAAIC,QAAA;UACFC,MAAA,GAAS;QAEX,IAAIH,GAAA,CAAIE,QAAA,CAASE,KAAA,KAAU,MAAM;UAC/BD,MAAA,GAAS;UACTD,QAAA,GAAWF,GAAA,CAAIE,QAAA,CAASG,YAAA,CAAc;QAChD,OAAe;UACLH,QAAA,GAAWF,GAAA,CAAIE,QAAA;QAChB;QAED,MAAMI,iBAAA,GAAoBJ,QAAA,CAASK,YAAA,CAAa,UAAU;QAE1D,SAAS3E,CAAA,GAAI,GAAGA,CAAA,GAAI0E,iBAAA,CAAkBE,KAAA,EAAO5E,CAAA,IAAK,GAAG;UACnD,MAAM6E,EAAA,GAAK,IAAIxH,OAAA,CAAO,EAAGyH,mBAAA,CAAoBJ,iBAAA,EAAmB1E,CAAC;UACjE,MAAM+E,EAAA,GAAK,IAAI1H,OAAA,CAAS,EAACyH,mBAAA,CAAoBJ,iBAAA,EAAmB1E,CAAA,GAAI,CAAC;UACrE,MAAMgF,EAAA,GAAK,IAAI3H,OAAA,CAAS,EAACyH,mBAAA,CAAoBJ,iBAAA,EAAmB1E,CAAA,GAAI,CAAC;UAErE6E,EAAA,CAAGI,YAAA,CAAab,GAAA,CAAIc,WAAW;UAC/BH,EAAA,CAAGE,YAAA,CAAab,GAAA,CAAIc,WAAW;UAC/BF,EAAA,CAAGC,YAAA,CAAab,GAAA,CAAIc,WAAW;UAE/B,KAAK7G,WAAA,CAAY,IAAI8G,QAAA,CAASN,EAAA,EAAIE,EAAA,EAAIC,EAAE,CAAC;QAC1C;QAED,IAAIT,MAAA,EAAQ;UACVD,QAAA,CAASc,OAAA,CAAS;QACnB;MACF;IACP,CAAK;IAED,KAAKhF,KAAA,CAAO;IAEZ,OAAO;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}