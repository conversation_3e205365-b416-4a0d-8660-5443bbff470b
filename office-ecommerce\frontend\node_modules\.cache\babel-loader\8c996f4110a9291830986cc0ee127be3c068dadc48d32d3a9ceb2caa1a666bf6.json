{"ast": null, "code": "export const nextTick = (() => {\n  const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n  if (isPromiseAvailable) {\n    return cb => Promise.resolve().then(cb);\n  } else {\n    return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n  }\n})();\nexport const globalThisShim = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() {}", "map": {"version": 3, "names": ["nextTick", "isPromiseAvailable", "Promise", "resolve", "cb", "then", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "defaultBinaryType", "createCookieJar"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/engine.io-client/build/esm/globals.js"], "sourcesContent": ["export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,CAAC,MAAM;EAC3B,MAAMC,kBAAkB,GAAG,OAAOC,OAAO,KAAK,UAAU,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU;EACjG,IAAIF,kBAAkB,EAAE;IACpB,OAAQG,EAAE,IAAKF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACE,IAAI,CAACD,EAAE,CAAC;EAC7C,CAAC,MACI;IACD,OAAO,CAACA,EAAE,EAAEE,YAAY,KAAKA,YAAY,CAACF,EAAE,EAAE,CAAC,CAAC;EACpD;AACJ,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMG,cAAc,GAAG,CAAC,MAAM;EACjC,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOA,IAAI;EACf,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACpC,OAAOA,MAAM;EACjB,CAAC,MACI;IACD,OAAOC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;EACpC;AACJ,CAAC,EAAE,CAAC;AACJ,OAAO,MAAMC,iBAAiB,GAAG,aAAa;AAC9C,OAAO,SAASC,eAAeA,CAAA,EAAG,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}