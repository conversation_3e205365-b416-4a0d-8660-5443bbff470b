{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Object3D, Sphere, Box3 } from \"three\";\nimport { XRHandMeshModel } from \"./XRHandMeshModel.js\";\nconst TOUCH_RADIUS = 0.01;\nconst POINTING_JOINT = \"index-finger-tip\";\nclass OculusHandModel extends Object3D {\n  constructor(controller, leftModelPath, rightModelPath) {\n    super();\n    __publicField(this, \"controller\");\n    __publicField(this, \"motionController\");\n    __publicField(this, \"envMap\");\n    __publicField(this, \"mesh\");\n    __publicField(this, \"xrInputSource\");\n    this.controller = controller;\n    this.motionController = null;\n    this.envMap = null;\n    this.mesh = null;\n    this.xrInputSource = null;\n    controller.addEventListener(\"connected\", event => {\n      const xrInputSource = event.data;\n      if (xrInputSource.hand && !this.motionController) {\n        this.xrInputSource = xrInputSource;\n        this.motionController = new XRHandMeshModel(this, controller, void 0, xrInputSource.handedness, xrInputSource.handedness === \"left\" ? leftModelPath : rightModelPath);\n      }\n    });\n    controller.addEventListener(\"disconnected\", () => {\n      this.dispose();\n    });\n  }\n  updateMatrixWorld(force) {\n    super.updateMatrixWorld(force);\n    if (this.motionController) {\n      this.motionController.updateMesh();\n    }\n  }\n  getPointerPosition() {\n    const indexFingerTip = this.controller.joints[POINTING_JOINT];\n    if (indexFingerTip) {\n      return indexFingerTip.position;\n    } else {\n      return null;\n    }\n  }\n  intersectBoxObject(boxObject) {\n    const pointerPosition = this.getPointerPosition();\n    if (pointerPosition) {\n      const indexSphere = new Sphere(pointerPosition, TOUCH_RADIUS);\n      const box = new Box3().setFromObject(boxObject);\n      return indexSphere.intersectsBox(box);\n    } else {\n      return false;\n    }\n  }\n  checkButton(button) {\n    if (this.intersectBoxObject(button)) {\n      button.onPress();\n    } else {\n      button.onClear();\n    }\n    if (button.isPressed()) {\n      button.whilePressed();\n    }\n  }\n  dispose() {\n    this.clear();\n    this.motionController = null;\n  }\n}\nexport { OculusHandModel };", "map": {"version": 3, "names": ["TOUCH_RADIUS", "POINTING_JOINT", "OculusHandModel", "Object3D", "constructor", "controller", "leftModelPath", "rightModelPath", "__publicField", "motionController", "envMap", "mesh", "xrInputSource", "addEventListener", "event", "data", "hand", "XRHandMeshModel", "handedness", "dispose", "updateMatrixWorld", "force", "updateMesh", "getPointerPosition", "indexFingerTip", "joints", "position", "intersectBoxObject", "boxObject", "pointerPosition", "indexSphere", "Sphere", "box", "Box3", "setFromObject", "intersectsBox", "checkButton", "button", "onPress", "onClear", "isPressed", "whilePressed", "clear"], "sources": ["c:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\src\\webxr\\OculusHandModel.ts"], "sourcesContent": ["import { Object3D, Sphere, Box3, Mesh, Texture, Vector3 } from 'three'\nimport { XRHandMeshModel } from './XRHandMeshModel'\n\nconst TOUCH_RADIUS = 0.01\nconst POINTING_JOINT = 'index-finger-tip'\n\nexport interface XRButton extends Object3D {\n  onPress(): void\n  onClear(): void\n  isPressed(): boolean\n  whilePressed(): void\n}\n\nclass OculusHandModel extends Object3D {\n  controller: Object3D\n  motionController: XRHandMeshModel | null\n  envMap: Texture | null\n  mesh: Mesh | null\n  xrInputSource: XRInputSource | null\n\n  constructor(controller: Object3D, leftModelPath?: string, rightModelPath?: string) {\n    super()\n\n    this.controller = controller\n    this.motionController = null\n    this.envMap = null\n\n    this.mesh = null\n    this.xrInputSource = null\n\n    controller.addEventListener('connected', (event) => {\n      const xrInputSource = (event as any).data\n\n      if (xrInputSource.hand && !this.motionController) {\n        this.xrInputSource = xrInputSource\n\n        this.motionController = new XRHandMeshModel(\n          this,\n          controller,\n          undefined,\n          xrInputSource.handedness,\n          xrInputSource.handedness === 'left' ? leftModelPath : rightModelPath,\n        )\n      }\n    })\n\n    controller.addEventListener('disconnected', () => {\n      this.dispose()\n    })\n  }\n\n  updateMatrixWorld(force?: boolean): void {\n    super.updateMatrixWorld(force)\n\n    if (this.motionController) {\n      this.motionController.updateMesh()\n    }\n  }\n\n  getPointerPosition(): Vector3 | null {\n    // @ts-ignore XRController needs to extend Group\n    const indexFingerTip = this.controller.joints[POINTING_JOINT]\n    if (indexFingerTip) {\n      return indexFingerTip.position\n    } else {\n      return null\n    }\n  }\n\n  intersectBoxObject(boxObject: Object3D): boolean {\n    const pointerPosition = this.getPointerPosition()\n    if (pointerPosition) {\n      const indexSphere = new Sphere(pointerPosition, TOUCH_RADIUS)\n      const box = new Box3().setFromObject(boxObject)\n      return indexSphere.intersectsBox(box)\n    } else {\n      return false\n    }\n  }\n\n  checkButton(button: XRButton): void {\n    if (this.intersectBoxObject(button)) {\n      button.onPress()\n    } else {\n      button.onClear()\n    }\n\n    if (button.isPressed()) {\n      button.whilePressed()\n    }\n  }\n\n  dispose(): void {\n    this.clear()\n    this.motionController = null\n  }\n}\n\nexport { OculusHandModel }\n"], "mappings": ";;;;;;;;;;;;;AAGA,MAAMA,YAAA,GAAe;AACrB,MAAMC,cAAA,GAAiB;AASvB,MAAMC,eAAA,SAAwBC,QAAA,CAAS;EAOrCC,YAAYC,UAAA,EAAsBC,aAAA,EAAwBC,cAAA,EAAyB;IAC3E;IAPRC,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IACAA,aAAA;IAKE,KAAKH,UAAA,GAAaA,UAAA;IAClB,KAAKI,gBAAA,GAAmB;IACxB,KAAKC,MAAA,GAAS;IAEd,KAAKC,IAAA,GAAO;IACZ,KAAKC,aAAA,GAAgB;IAEVP,UAAA,CAAAQ,gBAAA,CAAiB,aAAcC,KAAA,IAAU;MAClD,MAAMF,aAAA,GAAiBE,KAAA,CAAcC,IAAA;MAErC,IAAIH,aAAA,CAAcI,IAAA,IAAQ,CAAC,KAAKP,gBAAA,EAAkB;QAChD,KAAKG,aAAA,GAAgBA,aAAA;QAErB,KAAKH,gBAAA,GAAmB,IAAIQ,eAAA,CAC1B,MACAZ,UAAA,EACA,QACAO,aAAA,CAAcM,UAAA,EACdN,aAAA,CAAcM,UAAA,KAAe,SAASZ,aAAA,GAAgBC,cAAA;MAE1D;IAAA,CACD;IAEUF,UAAA,CAAAQ,gBAAA,CAAiB,gBAAgB,MAAM;MAChD,KAAKM,OAAA,CAAQ;IAAA,CACd;EACH;EAEAC,kBAAkBC,KAAA,EAAuB;IACvC,MAAMD,iBAAA,CAAkBC,KAAK;IAE7B,IAAI,KAAKZ,gBAAA,EAAkB;MACzB,KAAKA,gBAAA,CAAiBa,UAAA;IACxB;EACF;EAEAC,mBAAA,EAAqC;IAEnC,MAAMC,cAAA,GAAiB,KAAKnB,UAAA,CAAWoB,MAAA,CAAOxB,cAAc;IAC5D,IAAIuB,cAAA,EAAgB;MAClB,OAAOA,cAAA,CAAeE,QAAA;IAAA,OACjB;MACE;IACT;EACF;EAEAC,mBAAmBC,SAAA,EAA8B;IACzC,MAAAC,eAAA,GAAkB,KAAKN,kBAAA;IAC7B,IAAIM,eAAA,EAAiB;MACnB,MAAMC,WAAA,GAAc,IAAIC,MAAA,CAAOF,eAAA,EAAiB7B,YAAY;MAC5D,MAAMgC,GAAA,GAAM,IAAIC,IAAA,CAAK,EAAEC,aAAA,CAAcN,SAAS;MACvC,OAAAE,WAAA,CAAYK,aAAA,CAAcH,GAAG;IAAA,OAC/B;MACE;IACT;EACF;EAEAI,YAAYC,MAAA,EAAwB;IAC9B,SAAKV,kBAAA,CAAmBU,MAAM,GAAG;MACnCA,MAAA,CAAOC,OAAA,CAAQ;IAAA,OACV;MACLD,MAAA,CAAOE,OAAA,CAAQ;IACjB;IAEI,IAAAF,MAAA,CAAOG,SAAA,IAAa;MACtBH,MAAA,CAAOI,YAAA,CAAa;IACtB;EACF;EAEAtB,QAAA,EAAgB;IACd,KAAKuB,KAAA,CAAM;IACX,KAAKjC,gBAAA,GAAmB;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}