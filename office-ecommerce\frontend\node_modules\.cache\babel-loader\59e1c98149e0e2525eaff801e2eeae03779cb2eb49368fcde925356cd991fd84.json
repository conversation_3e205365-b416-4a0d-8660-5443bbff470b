{"ast": null, "code": "import { shaderMaterial } from '../core/shaderMaterial.js';\nconst DiscardMaterial = shaderMaterial({}, 'void main() { }', 'void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }');\nexport { DiscardMaterial };", "map": {"version": 3, "names": ["shaderMaterial", "DiscardMaterial"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/materials/DiscardMaterial.js"], "sourcesContent": ["import { shaderMaterial } from '../core/shaderMaterial.js';\n\nconst DiscardMaterial = shaderMaterial({}, 'void main() { }', 'void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }');\n\nexport { DiscardMaterial };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,2BAA2B;AAE1D,MAAMC,eAAe,GAAGD,cAAc,CAAC,CAAC,CAAC,EAAE,iBAAiB,EAAE,oEAAoE,CAAC;AAEnI,SAASC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}