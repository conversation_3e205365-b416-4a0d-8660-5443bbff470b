{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect } from 'react';\nimport { ArcballControls as ArcballControls$1 } from 'three-stdlib';\nconst ArcballControls = /*#__PURE__*/forwardRef(({\n  camera,\n  makeDefault,\n  regress,\n  domElement,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = useMemo(() => new ArcballControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [explDomElement, regress, controls, invalidate]);\n  useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    controls.addEventListener('change', callback);\n    if (onStart) controls.addEventListener('start', onStart);\n    if (onEnd) controls.addEventListener('end', onEnd);\n    return () => {\n      controls.removeEventListener('change', callback);\n      if (onStart) controls.removeEventListener('start', onStart);\n      if (onEnd) controls.removeEventListener('end', onEnd);\n    };\n  }, [onChange, onStart, onEnd]);\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\nexport { ArcballControls };", "map": {"version": 3, "names": ["_extends", "useThree", "useFrame", "React", "forwardRef", "useMemo", "useEffect", "ArcballControls", "ArcballControls$1", "camera", "makeDefault", "regress", "dom<PERSON>lement", "onChange", "onStart", "onEnd", "restProps", "ref", "invalidate", "state", "defaultCamera", "gl", "events", "set", "get", "performance", "explCamera", "explDomElement", "connected", "controls", "enabled", "update", "connect", "dispose", "callback", "e", "addEventListener", "removeEventListener", "old", "createElement", "object"], "sources": ["c:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/ArcballControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect } from 'react';\nimport { ArcballControls as ArcballControls$1 } from 'three-stdlib';\n\nconst ArcballControls = /*#__PURE__*/forwardRef(({\n  camera,\n  makeDefault,\n  regress,\n  domElement,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = useMemo(() => new ArcballControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [explDomElement, regress, controls, invalidate]);\n  useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n\n    controls.addEventListener('change', callback);\n    if (onStart) controls.addEventListener('start', onStart);\n    if (onEnd) controls.addEventListener('end', onEnd);\n    return () => {\n      controls.removeEventListener('change', callback);\n      if (onStart) controls.removeEventListener('start', onStart);\n      if (onEnd) controls.removeEventListener('end', onEnd);\n    };\n  }, [onChange, onStart, onEnd]);\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\n\nexport { ArcballControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,EAAEC,SAAS,QAAQ,OAAO;AACtD,SAASC,eAAe,IAAIC,iBAAiB,QAAQ,cAAc;AAEnE,MAAMD,eAAe,GAAG,aAAaH,UAAU,CAAC,CAAC;EAC/CK,MAAM;EACNC,WAAW;EACXC,OAAO;EACPC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,UAAU,GAAGjB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACD,UAAU,CAAC;EACtD,MAAME,aAAa,GAAGnB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACV,MAAM,CAAC;EACrD,MAAMY,EAAE,GAAGpB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACE,EAAE,CAAC;EACtC,MAAMC,MAAM,GAAGrB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,GAAG,GAAGtB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACI,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGvB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAC;EACxC,MAAMC,WAAW,GAAGxB,QAAQ,CAACkB,KAAK,IAAIA,KAAK,CAACM,WAAW,CAAC;EACxD,MAAMC,UAAU,GAAGjB,MAAM,IAAIW,aAAa;EAC1C,MAAMO,cAAc,GAAGf,UAAU,IAAIU,MAAM,CAACM,SAAS,IAAIP,EAAE,CAACT,UAAU;EACtE,MAAMiB,QAAQ,GAAGxB,OAAO,CAAC,MAAM,IAAIG,iBAAiB,CAACkB,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAC/ExB,QAAQ,CAAC,MAAM;IACb,IAAI2B,QAAQ,CAACC,OAAO,EAAED,QAAQ,CAACE,MAAM,CAAC,CAAC;EACzC,CAAC,EAAE,CAAC,CAAC,CAAC;EACNzB,SAAS,CAAC,MAAM;IACduB,QAAQ,CAACG,OAAO,CAACL,cAAc,CAAC;IAChC,OAAO,MAAM,KAAKE,QAAQ,CAACI,OAAO,CAAC,CAAC;EACtC,CAAC,EAAE,CAACN,cAAc,EAAEhB,OAAO,EAAEkB,QAAQ,EAAEX,UAAU,CAAC,CAAC;EACnDZ,SAAS,CAAC,MAAM;IACd,MAAM4B,QAAQ,GAAGC,CAAC,IAAI;MACpBjB,UAAU,CAAC,CAAC;MACZ,IAAIP,OAAO,EAAEc,WAAW,CAACd,OAAO,CAAC,CAAC;MAClC,IAAIE,QAAQ,EAAEA,QAAQ,CAACsB,CAAC,CAAC;IAC3B,CAAC;IAEDN,QAAQ,CAACO,gBAAgB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC7C,IAAIpB,OAAO,EAAEe,QAAQ,CAACO,gBAAgB,CAAC,OAAO,EAAEtB,OAAO,CAAC;IACxD,IAAIC,KAAK,EAAEc,QAAQ,CAACO,gBAAgB,CAAC,KAAK,EAAErB,KAAK,CAAC;IAClD,OAAO,MAAM;MACXc,QAAQ,CAACQ,mBAAmB,CAAC,QAAQ,EAAEH,QAAQ,CAAC;MAChD,IAAIpB,OAAO,EAAEe,QAAQ,CAACQ,mBAAmB,CAAC,OAAO,EAAEvB,OAAO,CAAC;MAC3D,IAAIC,KAAK,EAAEc,QAAQ,CAACQ,mBAAmB,CAAC,KAAK,EAAEtB,KAAK,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAACF,QAAQ,EAAEC,OAAO,EAAEC,KAAK,CAAC,CAAC;EAC9BT,SAAS,CAAC,MAAM;IACd,IAAII,WAAW,EAAE;MACf,MAAM4B,GAAG,GAAGd,GAAG,CAAC,CAAC,CAACK,QAAQ;MAC1BN,GAAG,CAAC;QACFM;MACF,CAAC,CAAC;MACF,OAAO,MAAMN,GAAG,CAAC;QACfM,QAAQ,EAAES;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5B,WAAW,EAAEmB,QAAQ,CAAC,CAAC;EAC3B,OAAO,aAAa1B,KAAK,CAACoC,aAAa,CAAC,WAAW,EAAEvC,QAAQ,CAAC;IAC5DiB,GAAG,EAAEA,GAAG;IACRuB,MAAM,EAAEX;EACV,CAAC,EAAEb,SAAS,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,SAAST,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}